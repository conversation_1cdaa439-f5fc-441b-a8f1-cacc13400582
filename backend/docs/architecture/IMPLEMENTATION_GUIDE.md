# Implementation Guide for Clean Architecture Refactoring

## Overview

This guide provides step-by-step instructions for implementing the clean Domain-Driven Design architecture for the wheel generation system. It includes detailed file creation, refactoring steps, and validation procedures.

## Phase 1: Foundation - Domain Models & Enums (Week 1-2)

### Step 1.1: Create Domain Enums (Day 1)
**File**: `backend/apps/main/domain/enums/domain_enums.py`

```bash
# Create directory structure
mkdir -p backend/apps/main/domain/enums
touch backend/apps/main/domain/__init__.py
touch backend/apps/main/domain/enums/__init__.py

# Create domain enums file
```

**Implementation**:
1. Define `DomainCode` enum with all 12 domain types
2. Define `TrustPhase`, `EnergyLevel`, `ResourceType` enums
3. Add validation methods for enum consistency
4. Create unit tests for enum validation

**Validation**:
```bash
# Test enum creation
python manage.py shell -c "from apps.main.domain.enums.domain_enums import DomainCode; print(list(DomainCode))"
```

### Step 1.2: Create Core Domain Models (Day 2-3)
**Files**: 
- `backend/apps/main/domain/models/wheel_models.py`
- `backend/apps/main/domain/models/activity_models.py`
- `backend/apps/main/domain/models/user_models.py`

**Implementation Order**:
1. Create base models with essential fields
2. Add validation rules and business constraints
3. Implement model relationships and dependencies
4. Add comprehensive docstrings and examples

**Validation**:
```bash
# Test model validation
python manage.py shell -c "
from apps.main.domain.models.activity_models import ActivityData
from apps.main.domain.enums.domain_enums import DomainCode
activity = ActivityData(
    id='test-1',
    name='Test Activity',
    description='Test description for validation',
    instructions='Test instructions for validation',
    domain=DomainCode.WELLNESS,
    duration_minutes=30,
    challenge_rating=50
)
print('Model validation successful:', activity.dict())
"
```

### Step 1.3: Create Repository Interfaces (Day 4-5)
**Files**:
- `backend/apps/main/domain/repositories/repository_interfaces.py`
- `backend/apps/main/infrastructure/repositories/django_activity_repository.py`
- `backend/apps/main/infrastructure/repositories/django_wheel_repository.py`

**Implementation**:
1. Define abstract repository interfaces
2. Implement Django ORM repositories
3. Add async/await support for all repository methods
4. Implement caching layer for performance

**Validation**:
```bash
# Test repository implementation
python manage.py shell -c "
import asyncio
from apps.main.infrastructure.repositories.django_activity_repository import DjangoActivityRepository
from apps.main.domain.models.activity_models import ActivitySelectionCriteria

async def test_repo():
    repo = DjangoActivityRepository()
    criteria = ActivitySelectionCriteria(time_available=30, energy_level=70)
    activities = await repo.find_candidates(criteria)
    print(f'Found {len(activities)} activities')

asyncio.run(test_repo())
"
```

## Phase 2: Business Services Implementation (Week 3-4)

### Step 2.1: Create Core Business Services (Day 6-8)
**Files**:
- `backend/apps/main/domain/services/activity_selection_service.py`
- `backend/apps/main/domain/services/activity_tailoring_service.py`
- `backend/apps/main/domain/services/wheel_building_service.py`

**Implementation Strategy**:
1. Extract business logic from existing `ProgrammaticActivitySelector`
2. Refactor existing `ActivityTailoringService` to use domain models
3. Create new `WheelBuildingService` from agent logic
4. Implement dependency injection pattern

**Migration Steps**:
```python
# Step 1: Create new service alongside existing implementation
# Step 2: Add feature flag to switch between old and new
# Step 3: Gradually migrate functionality
# Step 4: Remove old implementation

# Example feature flag usage:
USE_NEW_ARCHITECTURE = getattr(settings, 'USE_NEW_ARCHITECTURE', False)

if USE_NEW_ARCHITECTURE:
    service = ActivitySelectionService(repository)
else:
    service = ProgrammaticActivitySelector(user_profile_id)
```

### Step 2.2: Create Central Orchestration Service (Day 9-10)
**File**: `backend/apps/main/domain/services/wheel_generation_service.py`

**Implementation**:
1. Create main `WheelGenerationService` class
2. Implement dependency injection for all sub-services
3. Add comprehensive error handling and logging
4. Implement performance metrics collection

**Integration Test**:
```bash
# Test complete wheel generation
python manage.py shell -c "
import asyncio
from apps.main.domain.services.wheel_generation_service import WheelGenerationService
from apps.main.domain.models.wheel_models import WheelGenerationRequest

async def test_generation():
    service = WheelGenerationService.create_with_dependencies()
    request = WheelGenerationRequest(
        user_profile_id='test-user',
        selection_criteria=...,
        user_context=...,
        wheel_config=...
    )
    result = await service.generate_wheel(request)
    print(f'Generated wheel with {len(result.wheel.items)} items')

asyncio.run(test_generation())
"
```

## Phase 3: Agent Layer Refactoring (Week 5-6)

### Step 3.1: Refactor WheelAndActivityAgent (Day 11-13)
**File**: `backend/apps/main/agents/wheel_activity_agent.py`

**Refactoring Strategy**:
1. **Before**: 1200+ lines with business logic
2. **After**: ~200 lines with coordination only

**Implementation Steps**:
```python
# Step 1: Add service injection
class WheelAndActivityAgent(LangGraphAgent):
    def __init__(self, user_profile_id: str, wheel_service: WheelGenerationService = None):
        super().__init__(user_profile_id=user_profile_id, agent_role="activity")
        self.wheel_service = wheel_service or WheelGenerationService.create_with_dependencies()

# Step 2: Replace business logic methods with service calls
async def process(self, state: WheelGenerationState) -> Tuple[Dict[str, Any], Dict[str, Any]]:
    # OLD: 400+ lines of business logic
    # NEW: Delegate to service
    request = self._build_request_from_state(state)
    result = await self.wheel_service.generate_wheel(request)
    return self._build_response_from_result(result)

# Step 3: Remove business logic methods
# DELETE: _query_activity_catalog(), _create_tailored_activities(), _assign_probability_weights()
# KEEP: _build_request_from_state(), _build_response_from_result()
```

### Step 3.2: Update Tools Layer (Day 14-15)
**File**: `backend/apps/main/agents/tools/tools.py`

**Refactoring Strategy**:
1. Simplify `generate_wheel` tool to delegate to service
2. Remove business logic from tool implementations
3. Focus tools on data marshalling only

```python
@register_tool('generate_wheel')
async def generate_wheel(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """Simplified tool that delegates to business service."""
    # OLD: 400+ lines of business logic
    # NEW: Data marshalling + service delegation
    
    # 1. Convert input to domain model
    request = WheelGenerationRequest.from_tool_input(input_data)
    
    # 2. Delegate to business service
    service = WheelGenerationService.create_with_dependencies()
    result = await service.generate_wheel(request)
    
    # 3. Convert result to tool output format
    return result.to_tool_output()
```

## Phase 4: Integration & Testing (Week 7-8)

### Step 4.1: Create Comprehensive Test Suite (Day 16-18)
**Test Files**:
- `backend/apps/main/tests/domain/test_wheel_generation_service.py`
- `backend/apps/main/tests/domain/test_activity_selection_service.py`
- `backend/apps/main/tests/integration/test_wheel_generation_integration.py`

**Test Strategy**:
```python
# Unit Tests (80% coverage target)
class TestWheelGenerationService:
    def test_generate_wheel_success(self):
        # Test successful wheel generation
        pass
    
    def test_generate_wheel_validation_error(self):
        # Test validation error handling
        pass
    
    def test_generate_wheel_business_rule_validation(self):
        # Test business rule enforcement
        pass

# Integration Tests
class TestWheelGenerationIntegration:
    async def test_end_to_end_wheel_generation(self):
        # Test complete workflow from request to response
        pass
    
    async def test_performance_benchmarks(self):
        # Test performance meets targets (<30 seconds)
        pass
```

### Step 4.2: Performance Validation (Day 19-20)
**Performance Tests**:
```bash
# Create performance test script
cat > backend/test_architecture_performance.py << 'EOF'
import asyncio
import time
from apps.main.domain.services.wheel_generation_service import WheelGenerationService

async def benchmark_wheel_generation():
    service = WheelGenerationService.create_with_dependencies()
    
    start_time = time.time()
    # Run 10 wheel generations
    for i in range(10):
        request = create_test_request(f"user-{i}")
        result = await service.generate_wheel(request)
        print(f"Generation {i+1}: {len(result.wheel.items)} items")
    
    end_time = time.time()
    avg_time = (end_time - start_time) / 10
    print(f"Average generation time: {avg_time:.2f} seconds")
    
    # Validate performance target
    assert avg_time < 30, f"Performance target missed: {avg_time}s > 30s"

if __name__ == "__main__":
    asyncio.run(benchmark_wheel_generation())
EOF

# Run performance test
python backend/test_architecture_performance.py
```

## Deployment Strategy

### Feature Flag Rollout
```python
# settings.py
WHEEL_GENERATION_ARCHITECTURE = {
    'USE_NEW_DOMAIN_MODELS': True,
    'USE_NEW_BUSINESS_SERVICES': True,
    'USE_NEW_AGENT_ARCHITECTURE': True,
    'PERFORMANCE_MONITORING': True,
}

# Gradual rollout
if settings.WHEEL_GENERATION_ARCHITECTURE['USE_NEW_BUSINESS_SERVICES']:
    # Use new architecture
else:
    # Use legacy architecture
```

### Monitoring & Validation
```python
# Add performance monitoring
class WheelGenerationMetrics:
    @staticmethod
    def track_generation_time(func):
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            result = await func(*args, **kwargs)
            end_time = time.time()
            
            # Log metrics
            logger.info(f"Wheel generation took {end_time - start_time:.2f}s")
            
            return result
        return wrapper
```

### Rollback Plan
1. **Immediate Rollback**: Feature flags can disable new architecture instantly
2. **Data Consistency**: All data remains compatible between architectures
3. **Performance Monitoring**: Automatic rollback if performance degrades
4. **Error Handling**: Graceful fallback to legacy system on errors

## Success Validation

### Performance Metrics
- **Generation Time**: <30 seconds (vs current 3-4 minutes)
- **Memory Usage**: 50% reduction through efficient models
- **Database Queries**: 70% reduction through optimized repositories

### Quality Metrics
- **Code Coverage**: 90%+ for domain layer
- **Business Logic Centralization**: 100% in domain services
- **Agent Complexity**: <200 lines per agent (vs current 1200+)
- **Type Safety**: 100% type hint coverage

### Business Metrics
- **Domain Diversity**: Maintained or improved
- **Activity Quality**: Maintained or improved
- **User Experience**: No degradation in wheel quality
- **System Reliability**: Improved error handling and recovery
