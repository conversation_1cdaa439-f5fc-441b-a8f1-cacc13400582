# Business Services Specification

## Overview

This document specifies the business services layer that will centralize all business logic, replacing the current fragmented implementation across agents, tools, and services. These services contain pure business logic with no infrastructure dependencies.

## Core Business Services

### 1. WheelGenerationService
**File**: `backend/apps/main/domain/services/wheel_generation_service.py`
**Purpose**: Central orchestrator for complete wheel generation business logic

```python
class WheelGenerationService:
    """Central business service for wheel generation logic."""
    
    def __init__(self, 
                 activity_selector: ActivitySelectionService,
                 activity_tailorer: ActivityTailoringService,
                 wheel_builder: WheelBuildingService,
                 domain_service: DomainService):
        self.activity_selector = activity_selector
        self.activity_tailorer = activity_tailorer
        self.wheel_builder = wheel_builder
        self.domain_service = domain_service
    
    async def generate_wheel(self, request: WheelGenerationRequest) -> WheelGenerationResult:
        """Main business method - pure business logic."""
        # 1. Validate request using business rules
        self._validate_generation_request(request)
        
        # 2. Select activities using domain rules
        selected_activities = await self.activity_selector.select_activities(
            request.selection_criteria
        )
        
        # 3. Tailor activities to user context
        tailored_activities = await self.activity_tailorer.tailor_activities(
            selected_activities, request.user_context
        )
        
        # 4. Build wheel structure
        wheel = await self.wheel_builder.build_wheel(
            tailored_activities, request.wheel_config
        )
        
        # 5. Generate metadata and metrics
        metadata = self._generate_metadata(request, wheel)
        performance_metrics = self._calculate_performance_metrics(request, wheel)
        validation_results = self._validate_result(wheel)
        
        return WheelGenerationResult(
            wheel=wheel,
            metadata=metadata,
            performance_metrics=performance_metrics,
            validation_results=validation_results
        )
```

**Key Methods**:
- `generate_wheel()`: Main business orchestration
- `_validate_generation_request()`: Business rule validation
- `_generate_metadata()`: Metadata creation with business context
- `_calculate_performance_metrics()`: Quality and performance scoring
- `_validate_result()`: Final business rule validation

### 2. ActivitySelectionService
**File**: `backend/apps/main/domain/services/activity_selection_service.py`
**Purpose**: Pure business logic for activity selection

```python
class ActivitySelectionService:
    """Pure business logic for activity selection."""
    
    def __init__(self, activity_repository: ActivityRepository):
        self.repository = activity_repository
    
    async def select_activities(self, criteria: ActivitySelectionCriteria) -> List[ActivityData]:
        """Select activities based on business rules."""
        # 1. Get candidate activities from repository
        candidates = await self.repository.find_candidates(criteria)
        
        # 2. Apply business scoring algorithm
        scored_activities = self._score_activities(candidates, criteria)
        
        # 3. Apply selection strategy based on trust phase
        selected_activities = self._apply_selection_strategy(scored_activities, criteria)
        
        # 4. Validate selection meets business requirements
        self._validate_selection(selected_activities, criteria)
        
        return selected_activities
    
    def _score_activities(self, activities: List[ActivityData], criteria: ActivitySelectionCriteria) -> List[ScoredActivity]:
        """Business logic for activity scoring."""
        scored = []
        for activity in activities:
            score = self._calculate_activity_score(activity, criteria)
            scored.append(ScoredActivity(activity=activity, score=score))
        return sorted(scored, key=lambda x: x.score, reverse=True)
    
    def _calculate_activity_score(self, activity: ActivityData, criteria: ActivitySelectionCriteria) -> float:
        """Calculate activity score based on business rules."""
        score = 0.0
        
        # Time compatibility (0-1)
        time_score = self._calculate_time_compatibility(activity.duration_minutes, criteria.time_available)
        score += time_score * 0.3
        
        # Energy compatibility (0-1)
        energy_score = self._calculate_energy_compatibility(activity.energy_requirement, criteria.energy_level)
        score += energy_score * 0.3
        
        # Domain preference (0-1)
        domain_score = criteria.domain_preferences.get(activity.domain, 0.5)
        score += domain_score * 0.2
        
        # Challenge compatibility (0-1)
        challenge_score = self._calculate_challenge_compatibility(activity.challenge_rating, criteria.challenge_range)
        score += challenge_score * 0.2
        
        return score
```

**Key Methods**:
- `select_activities()`: Main selection orchestration
- `_score_activities()`: Business scoring algorithm
- `_apply_selection_strategy()`: Trust-phase-based selection
- `_validate_selection()`: Business rule validation

### 3. ActivityTailoringService
**File**: `backend/apps/main/domain/services/activity_tailoring_service.py`
**Purpose**: Business logic for activity personalization

```python
class ActivityTailoringService:
    """Business logic for activity tailoring and personalization."""
    
    def __init__(self, llm_service: LLMService, domain_service: DomainService):
        self.llm_service = llm_service
        self.domain_service = domain_service
    
    async def tailor_activities(self, activities: List[ActivityData], user_context: UserContext) -> List[ActivityData]:
        """Tailor activities to user context using business rules."""
        tailored_activities = []
        
        for i, activity in enumerate(activities):
            tailored_activity = await self._tailor_single_activity(activity, user_context, i)
            tailored_activities.append(tailored_activity)
        
        # Validate tailoring results
        self._validate_tailoring_results(tailored_activities, user_context)
        
        return tailored_activities
    
    async def _tailor_single_activity(self, activity: ActivityData, user_context: UserContext, index: int) -> ActivityData:
        """Tailor a single activity using business rules and LLM."""
        # 1. Prepare tailoring context
        tailoring_context = self._build_tailoring_context(activity, user_context)
        
        # 2. Apply business rule adjustments
        adjusted_activity = self._apply_business_adjustments(activity, user_context)
        
        # 3. Use LLM for creative personalization
        personalized_activity = await self._apply_llm_personalization(adjusted_activity, tailoring_context)
        
        # 4. Validate tailored activity
        self._validate_tailored_activity(personalized_activity, user_context)
        
        return personalized_activity
    
    def _apply_business_adjustments(self, activity: ActivityData, user_context: UserContext) -> ActivityData:
        """Apply business rule adjustments before LLM personalization."""
        # Adjust duration based on available time
        adjusted_duration = self._adjust_duration_for_context(activity.duration_minutes, user_context.time_available)
        
        # Adjust challenge based on trust phase
        adjusted_challenge = self._adjust_challenge_for_trust_phase(activity.challenge_rating, user_context.trust_phase)
        
        # Adjust resources based on availability
        adjusted_resources = self._adjust_resources_for_availability(activity.required_resources, user_context.available_resources)
        
        return activity.copy(update={
            'duration_minutes': adjusted_duration,
            'challenge_rating': adjusted_challenge,
            'required_resources': adjusted_resources
        })
```

**Key Methods**:
- `tailor_activities()`: Main tailoring orchestration
- `_tailor_single_activity()`: Individual activity tailoring
- `_apply_business_adjustments()`: Business rule adjustments
- `_apply_llm_personalization()`: LLM-based personalization

### 4. WheelBuildingService
**File**: `backend/apps/main/domain/services/wheel_building_service.py`
**Purpose**: Business logic for wheel construction and probability assignment

```python
class WheelBuildingService:
    """Business logic for wheel construction and probability assignment."""
    
    def __init__(self, domain_service: DomainService):
        self.domain_service = domain_service
    
    async def build_wheel(self, activities: List[ActivityData], config: WheelConfiguration) -> WheelData:
        """Build wheel structure using business rules."""
        # 1. Create wheel items from activities
        wheel_items = self._create_wheel_items(activities)
        
        # 2. Assign probability weights using business logic
        weighted_items = self._assign_probability_weights(wheel_items, config)
        
        # 3. Create wheel metadata
        metadata = self._create_wheel_metadata(activities, config)
        
        # 4. Validate wheel structure
        wheel = WheelData(
            name=self._generate_wheel_name(config),
            items=weighted_items,
            metadata=metadata
        )
        
        self._validate_wheel_structure(wheel)
        
        return wheel
    
    def _assign_probability_weights(self, items: List[WheelItemData], config: WheelConfiguration) -> List[WheelItemData]:
        """Assign probability weights based on business rules."""
        if config.probability_strategy == ProbabilityStrategy.EQUAL:
            return self._assign_equal_weights(items)
        elif config.probability_strategy == ProbabilityStrategy.DOMAIN_WEIGHTED:
            return self._assign_domain_weighted(items, config.domain_preferences)
        elif config.probability_strategy == ProbabilityStrategy.CHALLENGE_WEIGHTED:
            return self._assign_challenge_weighted(items, config.trust_phase)
        else:
            return self._assign_adaptive_weights(items, config)
    
    def _assign_adaptive_weights(self, items: List[WheelItemData], config: WheelConfiguration) -> List[WheelItemData]:
        """Assign adaptive weights based on multiple business factors."""
        total_weight = 0.0
        
        for item in items:
            # Base weight
            weight = 1.0
            
            # Domain preference adjustment
            domain_preference = config.domain_preferences.get(item.domain, 0.5)
            weight *= (0.5 + domain_preference)
            
            # Trust phase adjustment
            if config.trust_phase == TrustPhase.FOUNDATION:
                # Favor lower challenge activities
                challenge_factor = 1.0 - (item.challenge_rating / 100.0) * 0.3
                weight *= challenge_factor
            
            item.weight = weight
            total_weight += weight
        
        # Normalize to percentages
        for item in items:
            item.percentage = (item.weight / total_weight) * 100.0
            item.probability = item.percentage / 100.0
        
        return items
```

**Key Methods**:
- `build_wheel()`: Main wheel construction
- `_assign_probability_weights()`: Business logic for weight assignment
- `_create_wheel_metadata()`: Metadata generation
- `_validate_wheel_structure()`: Business rule validation

## Service Integration

### Dependency Injection Pattern
All services use dependency injection for testability and flexibility:

```python
# Service composition in application layer
class WheelGenerationServiceFactory:
    @staticmethod
    def create(repositories: RepositoryContainer, external_services: ExternalServiceContainer) -> WheelGenerationService:
        domain_service = DomainService()
        
        activity_selector = ActivitySelectionService(repositories.activity_repository)
        activity_tailorer = ActivityTailoringService(external_services.llm_service, domain_service)
        wheel_builder = WheelBuildingService(domain_service)
        
        return WheelGenerationService(
            activity_selector=activity_selector,
            activity_tailorer=activity_tailorer,
            wheel_builder=wheel_builder,
            domain_service=domain_service
        )
```

### Error Handling Strategy
All services implement consistent error handling:

```python
class BusinessLogicError(Exception):
    """Base exception for business logic errors."""
    pass

class ActivitySelectionError(BusinessLogicError):
    """Raised when activity selection fails business rules."""
    pass

class ActivityTailoringError(BusinessLogicError):
    """Raised when activity tailoring fails business rules."""
    pass

class WheelBuildingError(BusinessLogicError):
    """Raised when wheel building fails business rules."""
    pass
```

## Testing Strategy

### Unit Testing
Each service method is unit tested in isolation:
- Mock all dependencies (repositories, external services)
- Test business logic with various input scenarios
- Validate error handling and edge cases
- Ensure deterministic behavior

### Integration Testing
Test service interactions:
- Test complete wheel generation flow
- Validate data flow between services
- Test error propagation and handling
- Performance testing under load

### Business Rule Testing
Specific tests for business rules:
- Challenge calibration based on trust phase
- Domain diversity requirements
- Time and energy compatibility
- Resource availability validation
