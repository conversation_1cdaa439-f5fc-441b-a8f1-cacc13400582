# Domain Models Specification

## Overview

This document specifies the rich Pydantic domain models that will serve as the foundation for the clean architecture. These models encapsulate business rules, validation logic, and provide type safety across all layers.

## Core Domain Models

### 1. Wheel Generation Models

#### WheelGenerationRequest
**File**: `backend/apps/main/domain/models/wheel_models.py`
**Purpose**: Complete request for wheel generation with validation

```python
class WheelGenerationRequest(BaseModel):
    """Complete request for wheel generation with business rule validation."""
    user_profile_id: str = Field(..., min_length=1)
    selection_criteria: ActivitySelectionCriteria
    user_context: UserContext
    wheel_config: WheelConfiguration
    
    # Metadata
    workflow_id: Optional[str] = None
    request_timestamp: datetime = Field(default_factory=datetime.utcnow)
    
    class Config:
        validate_assignment = True
        use_enum_values = True
```

#### WheelGenerationResult
**File**: `backend/apps/main/domain/models/wheel_models.py`
**Purpose**: Complete result with metadata and performance metrics

```python
class WheelGenerationResult(BaseModel):
    """Complete result of wheel generation with comprehensive metadata."""
    wheel: WheelData
    metadata: WheelMetadata
    performance_metrics: PerformanceMetrics
    validation_results: ValidationResults
    
    # Business metrics
    selection_quality_score: float = Field(..., ge=0.0, le=1.0)
    tailoring_confidence: float = Field(..., ge=0.0, le=1.0)
    domain_diversity_score: float = Field(..., ge=0.0, le=1.0)
```

#### WheelData
**File**: `backend/apps/main/domain/models/wheel_models.py`
**Purpose**: Core wheel structure with business validation

```python
class WheelData(BaseModel):
    """Complete wheel data structure with business rule validation."""
    id: Optional[str] = None
    name: str = Field(..., min_length=1, max_length=200)
    items: List[WheelItemData] = Field(..., min_items=1, max_items=12)
    metadata: WheelMetadata
    
    @validator('items')
    def validate_percentages_sum_to_100(cls, v):
        total = sum(item.percentage for item in v)
        if not 99.0 <= total <= 101.0:  # Allow small rounding errors
            raise ValueError(f'Wheel percentages must sum to 100, got {total}')
        return v
    
    @validator('items')
    def validate_domain_diversity(cls, v):
        domains = [item.domain for item in v]
        unique_domains = set(domains)
        if len(unique_domains) < 2:
            raise ValueError('Wheel must have at least 2 different domains')
        return v
```

### 2. Activity Models

#### ActivityData
**File**: `backend/apps/main/domain/models/activity_models.py`
**Purpose**: Unified activity representation across all layers

```python
class ActivityData(BaseModel):
    """Unified activity representation with comprehensive validation."""
    id: str = Field(..., min_length=1)
    name: str = Field(..., min_length=1, max_length=200)
    description: str = Field(..., min_length=10, max_length=1000)
    instructions: str = Field(..., min_length=10, max_length=2000)
    
    # Domain and categorization
    domain: DomainCode  # Enum for type safety
    sub_domains: List[DomainCode] = Field(default_factory=list)
    
    # Time and difficulty
    duration_minutes: int = Field(..., ge=5, le=480)  # 5 min to 8 hours
    challenge_rating: int = Field(..., ge=0, le=100)
    energy_requirement: EnergyLevel  # Enum: LOW, MEDIUM, HIGH
    
    # Resources and requirements
    required_resources: List[ResourceType] = Field(default_factory=list)
    environment_requirements: EnvironmentRequirements
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    version: int = Field(default=1, ge=1)
    confidence: float = Field(default=1.0, ge=0.0, le=1.0)
    
    class Config:
        use_enum_values = True
        validate_assignment = True
```

#### ActivitySelectionCriteria
**File**: `backend/apps/main/domain/models/activity_models.py`
**Purpose**: Selection parameters with business rules

```python
class ActivitySelectionCriteria(BaseModel):
    """Criteria for activity selection with business rule validation."""
    time_available: int = Field(..., ge=5, le=480)  # 5 min to 8 hours
    energy_level: int = Field(..., ge=0, le=100)
    
    # Domain preferences (sum should be <= 1.0)
    domain_preferences: Dict[DomainCode, float] = Field(default_factory=dict)
    
    # Challenge calibration
    challenge_range: Tuple[int, int] = Field(default=(30, 70))
    trust_phase: TrustPhase = Field(default=TrustPhase.FOUNDATION)
    
    # Selection parameters
    min_activities: int = Field(default=5, ge=1, le=12)
    max_activities: int = Field(default=8, ge=1, le=12)
    
    @validator('challenge_range')
    def validate_challenge_range(cls, v):
        if v[0] >= v[1]:
            raise ValueError('Min challenge must be less than max challenge')
        if v[0] < 0 or v[1] > 100:
            raise ValueError('Challenge range must be between 0 and 100')
        return v
    
    @validator('domain_preferences')
    def validate_domain_preferences(cls, v):
        total = sum(v.values())
        if total > 1.0:
            raise ValueError('Domain preferences cannot sum to more than 1.0')
        return v
    
    @validator('max_activities')
    def validate_activity_counts(cls, v, values):
        min_activities = values.get('min_activities', 1)
        if v < min_activities:
            raise ValueError('Max activities must be >= min activities')
        return v
```

### 3. User Context Models

#### UserContext
**File**: `backend/apps/main/domain/models/user_models.py`
**Purpose**: Complete user context for personalization

```python
class UserContext(BaseModel):
    """Complete user context for activity personalization."""
    user_profile_id: str = Field(..., min_length=1)
    
    # Current state
    current_mood: MoodState
    energy_level: int = Field(..., ge=0, le=100)
    time_available: int = Field(..., ge=5, le=480)
    
    # Environment
    environment: EnvironmentContext
    available_resources: List[ResourceType] = Field(default_factory=list)
    
    # Psychological profile
    trust_phase: TrustPhase
    personality_traits: PersonalityTraits
    growth_goals: List[GrowthGoal] = Field(default_factory=list)
    
    # Preferences and history
    domain_preferences: Dict[DomainCode, float] = Field(default_factory=dict)
    recent_activities: List[str] = Field(default_factory=list)  # Activity IDs
    
    class Config:
        use_enum_values = True
```

## Domain Enums

### Core Enums
**File**: `backend/apps/main/domain/enums/domain_enums.py`

```python
class DomainCode(str, Enum):
    """Type-safe domain codes for activities."""
    WELLNESS = "wellness"
    CREATIVITY = "creativity"
    PHYSICAL = "physical"
    SOCIAL = "social"
    LEARNING = "learning"
    EMOTIONAL = "emotional"
    REFLECTIVE = "reflective"
    SPIRITUAL = "spiritual_existential"
    PRODUCTIVE = "productive_practical"
    LEISURE = "leisure_recreational"
    EXPLORATORY = "exploratory_adventurous"
    GENERAL = "general"

class TrustPhase(str, Enum):
    """Trust phases for challenge calibration."""
    FOUNDATION = "foundation"
    EXPANSION = "expansion"

class EnergyLevel(str, Enum):
    """Energy level categories for activity matching."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"

class ResourceType(str, Enum):
    """Types of resources required for activities."""
    DIGITAL_DEVICE = "digital_device"
    PHYSICAL_SPACE = "physical_space"
    CRAFT_MATERIALS = "craft_materials"
    EXERCISE_EQUIPMENT = "exercise_equipment"
    WRITING_MATERIALS = "writing_materials"
    MUSICAL_INSTRUMENT = "musical_instrument"
    OUTDOOR_ACCESS = "outdoor_access"
    SOCIAL_CONNECTION = "social_connection"
```

## Validation Rules

### Business Rule Validation
1. **Time Constraints**: All durations must be realistic (5 min to 8 hours)
2. **Challenge Calibration**: Challenge levels must respect trust phase limits
3. **Domain Diversity**: Wheels must have minimum domain variety
4. **Resource Compatibility**: Activities must match available resources
5. **Energy Matching**: Activity energy requirements must align with user energy

### Data Integrity Rules
1. **Required Fields**: All critical business data must be present
2. **Range Validation**: Numeric values must be within business-defined ranges
3. **Relationship Consistency**: Related entities must maintain referential integrity
4. **Enum Validation**: All categorical data must use defined enums

## Migration Strategy

### Phase 1: Model Creation
1. Create domain model files with comprehensive validation
2. Define all enums and type-safe constants
3. Implement validation rules and business constraints
4. Add comprehensive unit tests for all models

### Phase 2: Integration
1. Update existing services to use domain models
2. Replace raw dictionaries with typed models
3. Add validation at all layer boundaries
4. Ensure backward compatibility during transition

### Phase 3: Validation
1. Run comprehensive test suite
2. Validate performance impact of additional validation
3. Monitor for validation errors in production
4. Optimize validation rules based on real usage patterns
