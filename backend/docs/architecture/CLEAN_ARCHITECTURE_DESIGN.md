# Clean Architecture Design for Wheel Generation System

## Overview

This document outlines the complete Domain-Driven Design architecture for the wheel generation system, following clean architecture principles with proper separation of concerns and business logic centralization.

## Architecture Layers

### 1. Domain Layer (Core Business Logic)
**Location**: `backend/apps/main/domain/`
**Responsibility**: Pure business logic, domain models, and business rules
**Dependencies**: None (innermost layer)

#### Domain Models
```
backend/apps/main/domain/models/
├── wheel_models.py          # Wheel-related domain models
├── activity_models.py       # Activity-related domain models  
├── user_models.py          # User context domain models
└── common_models.py        # Shared domain models
```

#### Domain Services
```
backend/apps/main/domain/services/
├── wheel_generation_service.py     # Central wheel generation business logic
├── activity_selection_service.py   # Activity selection business rules
├── activity_tailoring_service.py   # Activity tailoring business logic
├── wheel_building_service.py       # Wheel construction business rules
└── domain_service.py              # Domain validation and rules
```

#### Domain Enums
```
backend/apps/main/domain/enums/
├── domain_enums.py         # Domain codes, trust phases, energy levels
├── activity_enums.py       # Activity types, difficulty levels, resources
└── wheel_enums.py         # Wheel configurations, probability strategies
```

### 2. Application Layer (Use Cases & Orchestration)
**Location**: `backend/apps/main/application/`
**Responsibility**: Use case orchestration, application services
**Dependencies**: Domain layer only

#### Use Cases
```
backend/apps/main/application/use_cases/
├── generate_wheel_use_case.py      # Complete wheel generation use case
├── select_activities_use_case.py   # Activity selection use case
└── tailor_activity_use_case.py     # Activity tailoring use case
```

#### Application Services
```
backend/apps/main/application/services/
├── wheel_application_service.py    # Wheel-related application logic
└── activity_application_service.py # Activity-related application logic
```

### 3. Infrastructure Layer (Data Access & External Services)
**Location**: `backend/apps/main/infrastructure/`
**Responsibility**: Database access, external APIs, caching
**Dependencies**: Domain and application layers

#### Repositories
```
backend/apps/main/infrastructure/repositories/
├── django_activity_repository.py   # Django ORM activity data access
├── django_wheel_repository.py      # Django ORM wheel data access
├── django_user_repository.py       # Django ORM user data access
└── repository_interfaces.py        # Repository interface definitions
```

#### External Services
```
backend/apps/main/infrastructure/external/
├── llm_service.py                  # LLM API integration
├── caching_service.py              # Redis/cache integration
└── notification_service.py         # WebSocket/notification integration
```

### 4. Presentation Layer (APIs & Agents)
**Location**: `backend/apps/main/presentation/`
**Responsibility**: API endpoints, agent coordination, data serialization
**Dependencies**: Application layer

#### Agent Coordinators
```
backend/apps/main/agents/
├── wheel_activity_agent.py         # Thin coordinator (200 lines max)
├── orchestrator_agent.py           # Workflow orchestration
└── base_agent.py                   # Base agent functionality
```

#### API Controllers
```
backend/apps/main/api/
├── wheel_api.py                    # Wheel-related API endpoints
├── activity_api.py                 # Activity-related API endpoints
└── serializers.py                  # API data serialization
```

## Data Flow Architecture

### Request Flow
```
User Request → Agent Coordinator → Application Service → Domain Service → Repository → Database
```

### Response Flow  
```
Database → Repository → Domain Model → Application Service → Agent Coordinator → User Response
```

### Key Principles
1. **Dependency Inversion**: Outer layers depend on inner layers, never the reverse
2. **Single Responsibility**: Each layer has one clear responsibility
3. **Interface Segregation**: Small, focused interfaces between layers
4. **Dependency Injection**: Services injected rather than instantiated

## Business Logic Centralization

### Current State (Fragmented)
- Activity selection: 3 different implementations across agents, services, tools
- Domain extraction: 4 different methods with inconsistent results
- Wheel building: Logic scattered across agents and tools
- Data validation: Mixed with conversion and persistence logic

### Target State (Centralized)
- **Single Source of Truth**: All business logic in domain services
- **Pure Functions**: Business logic independent of infrastructure
- **Testable Units**: Each business rule can be unit tested in isolation
- **Consistent Results**: Same input always produces same output

## Integration Strategy

### Phase 1: Domain Models
1. Create rich Pydantic domain models with validation
2. Define clear interfaces between layers
3. Establish type safety with enums and constraints

### Phase 2: Business Services
1. Extract business logic from agents and tools
2. Create pure domain services with dependency injection
3. Implement comprehensive unit tests

### Phase 3: Infrastructure Abstraction
1. Implement repository pattern for data access
2. Abstract external service dependencies
3. Add caching and performance optimizations

### Phase 4: Agent Simplification
1. Refactor agents to thin coordinators
2. Remove business logic from agent implementations
3. Focus agents on workflow orchestration only

## Quality Assurance

### Testing Strategy
- **Unit Tests**: Domain services and models (80%+ coverage)
- **Integration Tests**: Repository implementations and external services
- **End-to-End Tests**: Complete workflow validation
- **Performance Tests**: Load testing and benchmarking

### Validation Framework
- **Schema Validation**: Pydantic models at all boundaries
- **Business Rule Validation**: Domain service validation
- **Data Integrity**: Repository-level constraints
- **Type Safety**: Comprehensive type hints and enum usage

## Success Metrics

### Performance Targets
- Wheel generation time: <30 seconds (vs current 3-4 minutes)
- Memory usage: 50% reduction through efficient data models
- Database queries: 70% reduction through optimized repositories

### Quality Targets
- Code coverage: 90%+ for domain layer
- Cyclomatic complexity: <10 for all business methods
- Dependency violations: 0 (enforced by architecture tests)
- Type safety: 100% type hint coverage

### Maintainability Targets
- Business logic centralization: 100% in domain services
- Agent line count: <200 lines per agent (vs current 1200+)
- Service responsibilities: Single responsibility per service
- Interface clarity: Clear contracts between all layers
