# Enhanced Agent Benchmarking System

## Overview

The Enhanced Agent Benchmarking System provides comprehensive debugging and analysis capabilities for agent performance evaluation. This system captures detailed execution data including LLM interactions, tool calls, memory operations, and provides an intuitive UI for analysis.

## Key Features

### 1. LLM Call Interception and Recording
- **Real-time LLM call capture** during agent execution
- **Token usage tracking** with input/output counts
- **Response time monitoring** for performance analysis
- **Model and temperature tracking** for configuration analysis

### 2. Tool Call Monitoring
- **Complete tool execution tracking** with inputs and outputs
- **Execution mode detection** (real vs mock)
- **Success/failure status** with error details
- **Duration tracking** for performance optimization

### 3. Enhanced Agent Evaluation Modal
- **Rich LLM interaction display** with prompts, responses, and token usage
- **Tool call visualization** with detailed input/output analysis
- **Copy run data functionality** for exporting complete benchmark data as JSON
- **Refresh capability** for real-time data updates

### 4. Memory Operation Tracking
- **Database operation monitoring** during agent execution
- **Memory usage analysis** for optimization insights
- **State transition tracking** for workflow analysis

## Architecture

### Core Components

#### 1. LLM Interaction Interceptor (`apps/main/services/llm_interaction_interceptor.py`)
```python
class LLMInteractionInterceptor:
    """Intercepts and records LLM calls during agent execution."""
    
    def intercept_llm_call(self, agent_name: str, model_name: str):
        """Decorator to intercept LLM calls and record detailed metrics."""
```

#### 2. Agent Communication Tracker (`apps/main/services/agent_communication_tracker.py`)
```python
class AgentCommunicationTracker:
    """Tracks comprehensive agent execution data."""
    
    def record_llm_interaction(self, agent: str, model: str, prompt: str, response: str, ...):
        """Records detailed LLM interaction data."""
    
    def record_tool_call(self, tool_name: str, tool_input: dict, tool_output: dict, ...):
        """Records tool execution details."""
```

#### 3. Enhanced Benchmarking (`apps/main/agents/benchmarking.py`)
```python
class AgentBenchmark:
    def _setup_tool_call_interception(self, agent, comm_tracker):
        """Sets up tool call interception for comprehensive monitoring."""
```

### UI Components

#### 1. Agent Evaluation Modal (`backend/templates/admin_tools/modals/agent_evaluation_modal.html`)
- **Enhanced LLM Interactions Section** - Displays detailed LLM call information
- **Tool Usage Analysis** - Shows tool execution details and statistics
- **Copy Run Data Button** - Exports complete benchmark data as JSON
- **Refresh Modal Button** - Reloads modal with latest data

#### 2. Quick Benchmark Integration (`backend/static/admin/js/quick_benchmark.js`)
```javascript
function openAgentEvaluationModal(benchmarkRunId) {
    // Fetches benchmark data and opens enhanced modal
}

function loadAgentEvaluationModal() {
    // Dynamically loads modal HTML and JavaScript functions
}
```

## Usage

### Running Enhanced Benchmarks

1. **Via Admin Interface:**
   ```
   http://localhost:8000/admin/benchmarks/manage/
   → Quick Benchmark tab
   → Select agent, user profile, evaluation template
   → Click "Run Benchmark"
   → Click "View Detailed Results"
   ```

2. **Via API:**
   ```python
   from apps.main.services.quick_benchmark_service import QuickBenchmarkService
   
   service = QuickBenchmarkService()
   benchmark_run = service.run_quick_benchmark_sync(
       agent_name='mentor',
       user_profile_id='1',
       evaluation_template='mentor_helpfulness',
       use_real_tools=True,
       use_real_db=True
   )
   ```

### Viewing Enhanced Results

The enhanced agent evaluation modal provides:

- **LLM Interactions Summary** with token counts and timing
- **Individual LLM Call Details** with prompts, responses, and metadata
- **Tool Call Analysis** with execution modes and success rates
- **Performance Metrics** including duration and cost estimates
- **Export Functionality** for complete data analysis

### Testing the System

#### Comprehensive Test Suite

1. **Enhanced Agent Benchmarking Test:**
   ```bash
   docker exec -it backend-web-1 python /usr/src/app/real_condition_tests/test_enhanced_agent_benchmarking.py
   ```

2. **Agent Modal Integration Test:**
   ```bash
   docker exec -it backend-web-1 python /usr/src/app/real_condition_tests/test_agent_modal_integration.py
   ```

3. **Complete Benchmarking Experience Test:**
   ```bash
   docker exec -it backend-web-1 python /usr/src/app/real_condition_tests/test_complete_benchmarking_experience.py
   ```

## Data Structure

### Enhanced Debugging Data Format

```json
{
  "enhanced_debugging_data": {
    "enabled": true,
    "data_model_version": "2.0.0",
    "llm_interactions": [
      {
        "agent": "mentor",
        "model": "mistral-small-latest",
        "prompt": "...",
        "response": "...",
        "token_usage": {"input": 597, "output": 29},
        "temperature": 0.7,
        "duration_ms": 350.5,
        "success": true,
        "timestamp": "2025-06-15T09:54:35Z"
      }
    ],
    "tool_calls": [
      {
        "tool_name": "get_user_profile",
        "tool_input": {"user_id": "1"},
        "tool_output": {"profile": "..."},
        "execution_mode": "real",
        "duration_ms": 45.2,
        "success": true,
        "timestamp": "2025-06-15T09:54:35Z"
      }
    ],
    "agents": [
      {
        "agent": "MentorAgent",
        "input": {"user_input": "Hello, I need help"},
        "output": {"user_response": "Hello! I'm glad you're here..."},
        "success": true,
        "duration_ms": 668.8,
        "timestamp": "2025-06-15T09:54:35Z"
      }
    ]
  }
}
```

## Performance Considerations

### Optimization Features

1. **Selective Interception** - Only intercepts calls during benchmarking
2. **Efficient Data Storage** - Optimized JSON structure for large datasets
3. **Lazy Loading** - Modal content loaded on demand
4. **Token Estimation Fallbacks** - Multiple methods for accurate token counting

### Monitoring Capabilities

- **Real-time Performance Tracking** during execution
- **Cost Estimation** based on token usage and model pricing
- **Success Rate Analysis** across multiple benchmark runs
- **Bottleneck Identification** through timing analysis

## Troubleshooting

### Common Issues

1. **"renderAgentDetails is not defined"**
   - Ensure agent evaluation modal is included in the page
   - Check that JavaScript functions are properly loaded

2. **"Unexpected token '<'"**
   - Verify API endpoint returns JSON, not HTML
   - Check authentication and permissions

3. **Missing LLM interactions**
   - Ensure LLM call interception is properly set up
   - Verify enhanced debugging is enabled

### Debugging Tools

All testing tools are documented in `backend/real_condition_tests/AI-ENTRYPOINT.md` and provide comprehensive validation of the entire system from backend data capture to frontend display.

## Future Enhancements

- **Real-time Streaming** of benchmark progress
- **Advanced Analytics** with trend analysis
- **Custom Evaluation Metrics** for specific use cases
- **Automated Performance Optimization** suggestions
- **Integration with External Monitoring** systems
