# Django Static File Organization Guidelines

## Overview

This document defines comprehensive guidelines for organizing static files (CSS, JavaScript) in Django admin pages following Django best practices. These guidelines aim to improve maintainability, reduce code duplication, and provide a clear hierarchical structure for static assets.

## Core Principles

### 1. Separation of Concerns
- **No inline CSS or JavaScript** in templates
- Separate files for different functionalities
- Clear separation between layout, component, and page-specific styles

### 2. Hierarchical Organization
- Logical directory structure reflecting application architecture
- Consistent naming conventions
- Clear dependency relationships

### 3. Django Media Class Integration
- Use Django's Media class for dependency management
- Automatic asset inclusion and ordering
- Proper inheritance and extension patterns

## Directory Structure

```
backend/static/
├── admin_tools/
│   ├── css/
│   │   ├── base/
│   │   │   ├── admin_base.css          # Base admin styles
│   │   │   ├── layout.css              # Layout components
│   │   │   └── typography.css          # Typography styles
│   │   ├── components/
│   │   │   ├── modals.css              # Modal components
│   │   │   ├── tables.css              # Table components
│   │   │   ├── forms.css               # Form components
│   │   │   ├── buttons.css             # Button components
│   │   │   └── cards.css               # Card components
│   │   ├── pages/
│   │   │   ├── user_profile_management.css
│   │   │   ├── benchmark_history.css
│   │   │   └── command_management.css
│   │   └── utilities/
│   │       ├── animations.css          # Animation utilities
│   │       ├── responsive.css          # Responsive utilities
│   │       └── themes.css              # Theme utilities
│   ├── js/
│   │   ├── base/
│   │   │   ├── admin_base.js           # Base admin functionality
│   │   │   ├── utils.js                # Utility functions
│   │   │   └── api_client.js           # API client utilities
│   │   ├── components/
│   │   │   ├── modal_manager.js        # Modal management
│   │   │   ├── table_manager.js        # Table functionality
│   │   │   ├── form_validator.js       # Form validation
│   │   │   └── batch_actions.js        # Batch action handling
│   │   ├── pages/
│   │   │   ├── user_profile_management.js
│   │   │   ├── benchmark_history.js
│   │   │   └── command_management.js
│   │   └── modules/
│   │       ├── import_manager.js       # Import functionality
│   │       ├── export_manager.js       # Export functionality
│   │       └── validation_engine.js    # Validation engine
│   └── images/
│       ├── icons/
│       └── backgrounds/
```

## File Naming Conventions

### CSS Files
- **Base styles**: `admin_base.css`, `layout.css`
- **Components**: `component_name.css` (e.g., `modals.css`, `tables.css`)
- **Pages**: `page_name.css` (e.g., `user_profile_management.css`)
- **Utilities**: `utility_purpose.css` (e.g., `animations.css`)

### JavaScript Files
- **Base functionality**: `admin_base.js`, `utils.js`
- **Components**: `component_manager.js` (e.g., `modal_manager.js`)
- **Pages**: `page_name.js` (e.g., `user_profile_management.js`)
- **Modules**: `module_name.js` (e.g., `import_manager.js`)

## Django Media Class Implementation

### 1. Base Admin Media Class

```python
# backend/apps/admin_tools/media.py
from django import forms

class AdminToolsBaseMedia:
    """Base media class for admin tools with common dependencies."""
    
    class Media:
        css = {
            'all': [
                'admin_tools/css/base/admin_base.css',
                'admin_tools/css/base/layout.css',
                'admin_tools/css/base/typography.css',
            ]
        }
        js = [
            'admin_tools/js/base/admin_base.js',
            'admin_tools/js/base/utils.js',
            'admin_tools/js/base/api_client.js',
        ]
```

### 2. Component-Specific Media Classes

```python
class ModalComponentMedia(AdminToolsBaseMedia):
    """Media class for modal components."""
    
    class Media:
        css = {
            'all': [
                'admin_tools/css/components/modals.css',
            ]
        }
        js = [
            'admin_tools/js/components/modal_manager.js',
        ]

class TableComponentMedia(AdminToolsBaseMedia):
    """Media class for table components."""
    
    class Media:
        css = {
            'all': [
                'admin_tools/css/components/tables.css',
            ]
        }
        js = [
            'admin_tools/js/components/table_manager.js',
        ]
```

### 3. Page-Specific Media Classes

```python
class UserProfileManagementMedia(AdminToolsBaseMedia):
    """Media class for user profile management page."""
    
    class Media:
        css = {
            'all': [
                'admin_tools/css/components/modals.css',
                'admin_tools/css/components/tables.css',
                'admin_tools/css/components/forms.css',
                'admin_tools/css/pages/user_profile_management.css',
            ]
        }
        js = [
            'admin_tools/js/components/modal_manager.js',
            'admin_tools/js/components/table_manager.js',
            'admin_tools/js/components/batch_actions.js',
            'admin_tools/js/modules/import_manager.js',
            'admin_tools/js/pages/user_profile_management.js',
        ]
```

## View Integration

### 1. Class-Based Views

```python
from django.views.generic import TemplateView
from .media import UserProfileManagementMedia

class UserProfileManagementView(TemplateView, UserProfileManagementMedia):
    template_name = 'admin_tools/user_profile_management.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['media'] = self.media
        return context
```

### 2. Function-Based Views

```python
from .media import UserProfileManagementMedia

@staff_member_required
def user_profile_management_view(request):
    media_instance = UserProfileManagementMedia()
    context = {
        'media': media_instance.media,
        # ... other context
    }
    return render(request, 'admin_tools/user_profile_management.html', context)
```

## Template Integration

### 1. Media Block Usage

```html
{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}User Profile Management{% endblock %}

{% block extrahead %}
{{ block.super }}
{{ media }}
{% endblock %}

{% block content %}
<!-- Page content -->
{% endblock %}
```

### 2. Conditional Asset Loading

```html
{% block extrahead %}
{{ block.super }}
{% if media %}
    {{ media }}
{% endif %}
<!-- Additional conditional assets -->
{% if user.is_staff %}
    <link rel="stylesheet" type="text/css" href="{% static 'admin_tools/css/utilities/debug.css' %}">
{% endif %}
{% endblock %}
```

## Asset Dependencies and Ordering

### 1. CSS Loading Order
1. **Base styles** (admin_base.css, layout.css, typography.css)
2. **Component styles** (modals.css, tables.css, forms.css)
3. **Page-specific styles** (user_profile_management.css)
4. **Utility styles** (animations.css, responsive.css)

### 2. JavaScript Loading Order
1. **Base functionality** (admin_base.js, utils.js, api_client.js)
2. **Component managers** (modal_manager.js, table_manager.js)
3. **Modules** (import_manager.js, export_manager.js)
4. **Page-specific scripts** (user_profile_management.js)

## Code Organization Patterns

### 1. CSS Structure

```css
/* admin_tools/css/components/modals.css */

/* ==========================================================================
   Modal Components
   ========================================================================== */

/* Base modal styles */
.modal-base {
    /* Base modal styling */
}

/* Modal variants */
.modal-large {
    /* Large modal styling */
}

.modal-small {
    /* Small modal styling */
}

/* Modal states */
.modal-loading {
    /* Loading state styling */
}

/* Responsive design */
@media (max-width: 768px) {
    .modal-base {
        /* Mobile modal styling */
    }
}
```

### 2. JavaScript Structure

```javascript
// admin_tools/js/components/modal_manager.js

/**
 * Modal Manager Component
 * Handles modal creation, display, and interaction
 */
class ModalManager {
    constructor(options = {}) {
        this.options = {
            backdrop: true,
            keyboard: true,
            ...options
        };
        this.init();
    }

    init() {
        // Initialize modal functionality
    }

    show(modalId, data = {}) {
        // Show modal implementation
    }

    hide(modalId) {
        // Hide modal implementation
    }
}

// Export for use in other modules
window.ModalManager = ModalManager;
```

## Maintenance Guidelines

### 1. Regular Cleanup
- Remove unused CSS rules and JavaScript functions
- Consolidate duplicate styles and scripts
- Update dependencies and remove deprecated code

### 2. Performance Optimization
- Minify CSS and JavaScript files for production
- Use CSS and JavaScript bundling where appropriate
- Implement lazy loading for non-critical assets

### 3. Documentation
- Document component APIs and usage patterns
- Maintain changelog for asset modifications
- Provide examples for common use cases

## Migration Strategy

### 1. Gradual Migration
- Start with new pages using the new structure
- Migrate existing pages one at a time
- Maintain backward compatibility during transition

### 2. Testing
- Test asset loading and functionality after migration
- Verify responsive design and cross-browser compatibility
- Validate performance impact of changes

### 3. Rollback Plan
- Keep backup of original inline styles/scripts
- Document changes for easy rollback if needed
- Test rollback procedures before deployment

## Benefits

1. **Maintainability**: Easier to locate and modify specific styles/scripts
2. **Reusability**: Components can be reused across different pages
3. **Performance**: Better caching and loading optimization
4. **Collaboration**: Clear structure for team development
5. **Testing**: Easier to test individual components
6. **Scalability**: Structure supports application growth

## Implementation Checklist

- [ ] Create directory structure
- [ ] Define base media classes
- [ ] Extract inline CSS to separate files
- [ ] Extract inline JavaScript to separate files
- [ ] Implement Media classes for views
- [ ] Update templates to use external assets
- [ ] Test functionality and performance
- [ ] Document changes and usage patterns
