You are provided with a questionnaire ("onboarding_questionnaire.md"), and the answers a human has given to that questionnaire (answers.txt). From that, your goal is to create a structured user profile. The answers come from the transcription of an audio file, so it might contain some transcription error : when a word does not make sense in the context, try to guess what word what confused by another.

# User Profile Generation Instructions

Generate comprehensive, realistic user profile for a personalized life coaching system. Create complete JSON objects that follow the provided schema structure exactly.

## Core Requirements

**Output Format**: Return a single, valid JSON object that matches this schema structure precisely. Every required field must be included with appropriate values.
master schema : "user_profile.schema.json"

**Quality Standards**: The success of your mission will be measured with how good you have "translated" the answers in the very structured json, preserving the quality in an inteligent way.

**⚠️ CRITICAL CODE VALIDATION**: All trait_code, skill_code, generic_resource, limitation_code, belief_code, and environment_code values MUST use exact codes from the authoritative catalog. Using invalid codes will cause import failure.

**� AUTHORITATIVE CODE REFERENCE**:
- **PRIMARY SOURCE**: `comprehensive_codes_catalog.md` - **AI-optimized catalogsystem codes**
  - Contains ALL valid codes across 6 categories: Beliefs, Domains, Environments, Limitations, Resources, Traits
  - Quick reference tables with code counts and purposes
  - AI usage instructions and validation guidelines
  - **Use this as your single source of truth for all codes**

## 🎯 Translation Strategy

### From Questionnaire to Profile
When translating user's answers :

**Question 1 (Current Chapter)** → Demographics, Environment, User Account
**Question 2 (Daily Rhythm)** → Temporal preferences, Energy patterns, Environment details
**Question 3 (Past Projects)** → Conscientiousness traits, Skills, Aspirations
**Question 4 (Social Energy)** → Extraversion traits, Social context, Limitations
**Question 5 (Creative Flow)** → Openness traits, Skills, Preferences
**Questions 6-15** → Deeper psychological traits, Beliefs, Motivations

### Future-Proof Data Handling
For data not explicitly covered in the current questionnaire, use these guidelines:

**Missing Trait Data**: Include 15-24 HEXACO traits based on conversation patterns and explicit mentions
**Inferred Skills**: Extract skills from activities, hobbies, and work descriptions
**Environmental Context**: Build comprehensive environment from living situation descriptions
**Resource Identification**: Identify tools, spaces, and assets mentioned in responses (using valid codes)
**Limitation Recognition**: Extract constraints from challenges and obstacles (using valid codes)

## 📋 Catalog Usage Guidelines

### Code Validation Process
1. **Always Reference Catalogs**: Before using any code, verify it exists in the appropriate catalog file
2. **Use Exact Codes**: Codes are case-sensitive and must match exactly as defined in the catalogs
3. **Check Categories**: Ensure you're using codes from the correct category (e.g., PHYSICAL vs SOCIAL domains)
4. **Validate Resources**: Resource codes are organized by type (technology, equipment, stationery, etc.)

### Catalog Structure Understanding
- **Domains**: Organized by primary categories (PHYSICAL, SOCIAL, CREATIVE, REFLECTIVE, INTELLECTUAL, LEISURE_RECREATIONAL, PRACTICAL, PROFESSIONAL)
- **Environments**: Organized by context (healthcare, cultural, transportation, natural, residential, professional, educational, commercial)
- **Resources**: Organized by type (technology, equipment, stationery, creative, leisure, food, garden, outdoor, media, finance, access, home, education, specialized)
- **Traits**: Organized by HEXACO dimensions (HONESTYHUMILITY, EMOTIONALITY, EXTRAVERSION, AGREEABLENESS, CONSCIENTIOUSNESS, OPENNESS)
- **Limitations**: Organized by type (PHYSICAL, COGNITIVE, PSYCHOLOGICAL, SOCIAL, ENVIRONMENTAL, TEMPORAL, RESOURCE)
- **Beliefs**: Organized by category (SELF_WORTH, CONTROL, GROWTH, RELATIONSHIPS, ACHIEVEMENT, MEANING, RESILIENCE)

### Common Code Patterns
- **Domain codes**: Often descriptive (e.g., `phys_cardio`, `social_networking`, `creative_visual`)
- **Environment codes**: Include context prefix (e.g., `ind_healthcare_hospital_ward`, `outdoor_nature_park`)
- **Resource codes**: Include type prefix (e.g., `tech_smartphone`, `equip_yoga_mat`, `finance_small`)
- **Trait codes**: Follow HEXACO pattern (e.g., `honesty_sincerity`, `extra_sociability`)

## Profile Components Guide

### User Account & Identity
- **username**: Unique identifier (letters, numbers, underscore only)
- **email**: Valid email format
- **profile_name**: How the person wants to be known (can differ from legal name)
- **is_real**: Set to `true` for actual people, `false` for test profiles

### Demographics
Complete the required demographic information:
- **full_name**: Complete legal or preferred name
- **age**: Realistic age (13-120 years)
- **gender**: Gender identity (traditional or non-binary expressions welcome)
- **location**: Current residence (city, region, country)
- **language**: Primary language(s) spoken
- **occupation**: Current role, including student, retired, unemployed, etc.

### Environments (1-5 environments recommended)
Create an array of user environments. Include primary living/working spaces and any significant secondary environments:
- **environment_name**: User's personal name for their space ("My apartment", "The farm", "Office")
- **environment_description**: Rich, specific description of the physical and social environment
- **is_current**: Set to `true` for current primary environment, `false` for secondary or past environments
- **effective_start**: Date when user started using this environment (YYYY-MM-DD format, required)
- **effective_end**: Date when user will stop using this environment (YYYY-MM-DD format, optional)
- **generic_environment_code**: Optional code from authoritative catalog if applicable

#### Physical Properties (All 0-100 scales except where noted - ALL FIELDS REQUIRED)
- **rurality**: 0=urban center, 50=suburban, 100=rural/remote (REQUIRED)
- **noise_level**: 0=library quiet, 50=residential, 100=construction site (REQUIRED)
- **light_quality**: 0=basement/windowless, 50=average, 100=floor-to-ceiling windows (REQUIRED)
- **accessibility**: 0=many barriers, 50=some accommodations, 100=fully accessible (REQUIRED)
- **air_quality**: 0=polluted/stuffy, 50=average, 100=fresh/clean (REQUIRED)
- **temperature_range**: "cold", "cool", "moderate", "warm", "hot", or "variable" (REQUIRED)
- **has_natural_elements**: Whether space includes plants, water features, natural materials (REQUIRED)
- **surface_type**: "hard", "soft", "mixed", "natural", or "artificial" (REQUIRED)
- **water_proximity**: 0=no water access, 100=immediate water access (REQUIRED)
- **space_size**: "tiny", "small", "medium", "large", or "very_large" (REQUIRED)

#### Social Context (EXACT field names required - All 0-100 scales except supervision_level)
- **privacy_level**: 0=completely public, 50=semi-private, 100=totally private
- **typical_occupancy**: 0=empty, 50=moderate occupancy, 100=extremely crowded
- **social_interaction_level**: 0=no interaction, 50=moderate interaction, 100=constant interaction
- **formality_level**: 0=very casual, 50=mixed, 100=very formal
- **safety_level**: 0=unsafe, 50=reasonably safe, 100=very safe
- **supervision_level**: "none", "minimal", "moderate", "high", or "constant"
- **cultural_diversity**: 0=homogeneous, 50=some diversity, 100=highly diverse

#### Activity Support (EXACT structure required)
- **digital_connectivity**: 0-100 (0=no connectivity, 100=excellent connectivity)
- **resource_availability**: 0-100 (0=no resources, 100=abundant resources)
- **time_availability**: JSON object like `{"weekdays": ["morning", "afternoon"], "weekends": ["all_day"]}`
- **domain_specific_support**: JSON object with domain support levels like `{"creative": 85, "physical": 60, "intellectual": 70, "social": 40, "reflective": 90, "productive": 80}`

#### Psychological Qualities (EXACT field names required - ALL FIELDS REQUIRED)
- **restorative_quality**: 0-100 (0=draining, 100=deeply rejuvenating) (REQUIRED)
- **stimulation_level**: 0-100 (0=understimulating, 100=overstimulating) (REQUIRED)
- **aesthetic_appeal**: 0-100 (0=unappealing, 100=beautiful) (REQUIRED)
- **novelty_level**: 0-100 (0=very familiar, 100=completely novel) (REQUIRED)
- **comfort_level**: 0-100 (0=uncomfortable, 100=very comfortable) (REQUIRED)
- **personal_significance**: 0-100 (0=no attachment, 100=deeply meaningful) (REQUIRED)
- **emotional_associations**: JSON object like `{"calm": 80, "focus": 70, "happiness": 65}` (REQUIRED)

### Personality Traits (HEXACO Model)
Create 15-24 trait inclinations using these **EXACT** codes from the database:

**Honesty-Humility**: honesty_sincerity, honesty_fairness, honesty_greed_avoidance, honesty_modesty
**Emotionality**: emotion_fearfulness, emotion_anxiety, emotion_dependence, emotion_sentimentality
**Extraversion**: extra_self_esteem, extra_social_boldness, extra_sociability, extra_liveliness
**Agreeableness**: agree_forgiveness, agree_gentleness, agree_flexibility, agree_patience
**Conscientiousness**: consc_organization, consc_diligence, consc_perfectionism, consc_prudence
**Openness**: open_aesthetic, open_inquisitive, open_creativity, open_unconventional

⚠️ **CRITICAL**: Use ONLY these exact codes. Any other codes will cause import failure.

For each trait:
- **strength**: 0-100 rating of how strongly this trait manifests
- **awareness**: 0-100 rating of user's self-awareness of this trait
- **development_notes**: Optional notes about how this trait shows up in behavior

### Beliefs and Worldview (3-8 beliefs)
Core beliefs that shape decisions:
- **belief_code**: Optional code from authoritative catalog that best matches this belief (e.g., `growth_mindset`, `control_external`, `meaning_purpose`)
- **belief_statement**: Clear, specific statement of what they believe
- **strength**: 0-100 how strongly held
- **certainty**: 0-100 how confident they are in this belief
- **evidence_sources**: Array of experiences or information supporting this belief
- **life_impact**: 0-100 how much this belief influences daily decisions
- **emotionality**: -100 to 100 emotional charge (-100=very negative, 0=neutral, 100=very positive)
- **user_confidence**: 0-100 user's personal conviction in this belief
- **stability**: 0-100 resistance to change (0=very changeable, 100=deeply ingrained)
- **user_awareness**: 0-100 user's awareness of holding this belief (0=unconscious, 100=highly conscious)

⚠️ **BELIEF CODE USAGE**: When possible, reference the belief codes from the comprehensive catalog to link user beliefs to the system's belief taxonomy. This enables better matching and personalization.

### Goals and Aspirations

#### Aspirations (2-5 long-term goals, 2+ years)
- **title**: Concise aspiration name
- **description**: What they want to achieve or become
- **importance**: 0-100 how important this is to them
- **time_horizon**: Expected timeframe ("5 years", "lifetime", etc.)
- **current_progress**: 0-100 how far along they are

#### Intentions (2-8 short-term goals, days to 2 years)
- **title**: Specific goal or intention
- **description**: Concrete actions or outcomes intended
- **target_date**: Specific completion date (YYYY-MM-DD)
- **commitment_level**: 0-100 how committed they are
- **success_criteria**: How they'll know they've succeeded

### Inspirations (2-6 sources)
- **source**: Person, book, experience, philosophy, etc.
- **description**: How this inspires them and why it matters
- **strength**: 0-100 inspirational power
- **category**: Type (person, media, experience, philosophy, nature, etc.)
- **reference_url**: If applicable (books, articles, videos)

### Skills (5-15 capabilities)
Use these **EXACT** skill codes from the database:
- communication, soft_communication, soft_empathy, soft_introspection, soft_philosophical
- soft_service, tech_ai_concepts, tech_coding_python, tech_graphic_design, tech_sailing, tech_writing

For each skill:
- **skill_code**: Must be one of the exact codes above
- **description**: How this skill manifests personally
- **level**: 0-100 current competency (0=beginner, 50=intermediate, 100=expert)
- **user_awareness**: 0-100 self-awareness of skill level
- **user_enjoyment**: 0-100 how much they enjoy using this skill
- **practice_frequency**: How often practiced (daily, weekly, monthly, rarely)
- **acquisition_context**: How/where learned (school, work, self-taught, etc.)

⚠️ **CRITICAL**: Use ONLY the exact skill codes listed above. Any other codes will cause import failure.

### Resources (UserResource → GenericResource Relationship)

**IMPORTANT**: Resources create **UserResource objects** that link to **GenericResource codes**. Each user resource is a specific item that relates to a generic category.

#### Resource Relationship Model
- **specific_name**: User's personal name for their resource (e.g., "My MacBook Pro", "Dad's workshop")
- **generic_resource**: Generic category code this resource relates to (e.g., "tech_laptop", "space_workshop")
- **location_details**: Where this specific resource is located or accessed
- **ownership_details**: How the user owns, rents, borrows, or accesses this specific resource
- **availability**: When this specific resource is available for use
- **condition**: Current state or quality of this specific resource
- **notes**: Additional relevant information about this specific resource

#### Gap Coverage with "Other" Categories
Use these codes when specific resources don't fit standard categories:
- **tech_other**: Any technology not covered by specific tech codes
- **equip_other**: Any equipment/tools not covered by specific equipment codes
- **creative_other**: Any creative materials not covered by specific creative codes
- **leisure_other**: Any leisure/recreational resources not covered
- **home_other**: Any home/household resources not covered
- **access_other**: Any access/services/memberships not covered
- **specialized_other**: Any specialized tools not covered
- **resource_other**: Any resource that doesn't fit any category

#### Examples
```json
{
  "specific_name": "My MacBook Pro 2021",
  "generic_resource": "tech_laptop",
  "location_details": "Home office desk",
  "ownership_details": "Personal laptop, fully owned"
}
```

```json
{
  "specific_name": "Grandmother's sewing machine",
  "generic_resource": "equip_other",
  "location_details": "Spare bedroom closet",
  "ownership_details": "Inherited, occasionally used"
}
```

⚠️ **CRITICAL**: Every user resource MUST use a valid generic_resource from the comprehensive catalog. Use "other" codes to ensure no import failures.

### Limitations (2-10 constraints)
Use **EXACT** limitation codes from the database (50 available codes including):
- cog_executive, cog_processing, env_crowds, env_outdoor, phys_dexterity
- time_availability, financial_constraints, social_anxiety, etc.

For each limitation:
- **limitation_code**: Must be one of the exact codes from the database
- **description**: How this limitation affects them personally
- **severity**: 0-100 impact level (0=minor inconvenience, 100=major barrier)
- **frequency**: How often encountered (constantly, daily, weekly, situational)
- **coping_strategies**: How they currently manage this limitation
- **is_temporary**: Whether expected to be temporary or permanent

⚠️ **CRITICAL**: Use ONLY valid limitation codes from the database. See VALID_REFERENCE_CODES.md for complete list.

### Preferences (8-20 patterns)
- **pref_name**: Name of the preference
- **pref_description**: Detailed explanation of the preference
- **pref_strength**: -100 to +100 (-100=strong dislike, 0=neutral, +100=strong preference)
- **user_awareness**: 0-100 how conscious they are of this preference
- **context**: Where this preference applies (work, social, personal, etc.)

### Current Mood State
- **energy_level**: 0-100 (0=exhausted, 50=normal, 100=highly energetic)
- **stress_level**: 0-100 (0=completely relaxed, 100=extremely stressed)
- **optimism**: 0-100 (0=pessimistic, 100=very optimistic)
- **social_engagement**: 0-100 (0=want to be alone, 100=want lots of social contact)
- **mood_description**: Qualitative description of overall current emotional state

### Trust Level
- **value**: 0-100 overall trust in guidance systems
- **domain_scores**: Trust in specific areas (goal_setting, activity_recommendation, personal_growth, lifestyle_guidance), each 0-100
- **progression_notes**: Notes about trust development over time

## Quality Guidelines

### Consistency Requirements
- Personality traits should align with described behaviors and preferences
- Goals should be realistic given resources and limitations
- Environment should support stated lifestyle and activities
- Beliefs should be consistent with aspirations and decision patterns

### Realism Standards
- Include both strengths and growth areas
- Reflect normal human complexity and contradictions
- Use specific, concrete details rather than generalizations
- Consider how life circumstances affect priorities and capabilities

### Diversity Considerations
- Respect varied backgrounds, values, and life situations
- Avoid stereotypes while acknowledging cultural influences
- Include diverse family structures, career paths, and life choices
- Consider how different life stages affect goals and limitations

## Generation Process

1. **Establish Identity**: Start with demographics and basic life context
2. **Build Environment**: Create realistic living situation that fits the person
3. **Develop Personality**: Generate consistent HEXACO traits
4. **Create Worldview**: Establish beliefs that align with personality and experiences
5. **Set Goals**: Generate aspirations and intentions that fit values and situation
6. **Add Details**: Fill in skills, resources, limitations, and preferences
7. **Capture Current State**: Set realistic mood and trust levels
8. **Review Consistency**: Ensure all elements work together coherently

Remember: You're creating a complete, believable person with authentic complexity. The profile should enable personalized coaching that feels truly relevant to this individual's unique situation, personality, and goals.