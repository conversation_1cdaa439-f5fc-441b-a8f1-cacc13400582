"""
Test suite for database field fixes in agent tools.

This test suite verifies that the fixes for database field mismatches,
user ID format handling, and model attribute errors are working correctly.

Tests cover:
1. Database field name corrections (created_at vs created_on, etc.)
2. User ID format handling (string vs integer conversion)
3. Model attribute access fixes (event_data vs details)
4. UserResource relationship corrections
5. Schema validation improvements
"""

import pytest
import asyncio
from unittest.mock import patch, MagicMock
from django.test import TestCase
from django.contrib.auth.models import User
from apps.user.models import UserProfile
from apps.main.agents.tools.engagement_tools import (
    get_domain_preferences, get_completion_patterns, get_temporal_patterns
)
from apps.main.agents.tools.psychological_tools import (
    analyze_psychological_state, get_trust_metrics, get_trait_analysis
)
from apps.main.agents.tools.activity_tools import (
    query_activity_catalog, tailor_activity
)


class DatabaseFieldFixesTestCase(TestCase):
    """Test case for database field fixes."""
    
    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.user_profile = UserProfile.objects.create(
            user=self.user,
            profile_name='Test User'
        )
        
    @pytest.mark.asyncio
    async def test_engagement_tools_with_string_user_id(self):
        """Test engagement tools handle string user IDs correctly."""
        string_user_id = "test-user-123"
        
        # Test get_domain_preferences
        result = await get_domain_preferences(string_user_id, time_period_days=30)
        self.assertIsInstance(result, dict)
        self.assertIn('confidence', result)
        self.assertNotIn('error', result)
        
        # Test get_completion_patterns
        result = await get_completion_patterns(string_user_id, time_period_days=30)
        self.assertIsInstance(result, dict)
        self.assertIn('confidence', result)
        self.assertNotIn('error', result)
        
        # Test get_temporal_patterns
        result = await get_temporal_patterns(string_user_id)
        self.assertIsInstance(result, dict)
        self.assertNotIn('error', result)
        
    @pytest.mark.asyncio
    async def test_engagement_tools_with_integer_user_id(self):
        """Test engagement tools work with integer user IDs."""
        user_id = self.user_profile.id
        
        # Test get_domain_preferences
        result = await get_domain_preferences(user_id, time_period_days=30)
        self.assertIsInstance(result, dict)
        self.assertNotIn('error', result)
        
        # Test get_completion_patterns
        result = await get_completion_patterns(user_id, time_period_days=30)
        self.assertIsInstance(result, dict)
        self.assertNotIn('error', result)
        
    @pytest.mark.asyncio
    async def test_psychological_tools_with_string_user_id(self):
        """Test psychological tools handle string user IDs correctly."""
        string_user_id = "benchmark-user-456"
        
        # Test analyze_psychological_state
        result = await analyze_psychological_state(string_user_id, reported_mood="neutral")
        self.assertIsInstance(result, dict)
        self.assertNotIn('error', result)
        
        # Test get_trust_metrics
        result = await get_trust_metrics(string_user_id)
        self.assertIsInstance(result, dict)
        self.assertIn('confidence', result)
        self.assertNotIn('error', result)
        
        # Test get_trait_analysis
        result = await get_trait_analysis(string_user_id)
        self.assertIsInstance(result, dict)
        self.assertIn('confidence', result)
        self.assertNotIn('error', result)
        
    @pytest.mark.asyncio
    async def test_activity_tools_with_string_user_id(self):
        """Test activity tools handle string user IDs correctly."""
        string_user_id = "test-user-789"
        
        # Test query_activity_catalog
        result = await query_activity_catalog(string_user_id, domains=["creativity", "wellness"])
        self.assertIsInstance(result, dict)
        self.assertNotIn('error', result)
        self.assertIn('activities', result)
        
        # Test tailor_activity
        result = await tailor_activity(string_user_id, generic_activity_id=1)
        self.assertIsInstance(result, dict)
        self.assertNotIn('error', result)
        
    @pytest.mark.asyncio
    async def test_activity_tools_with_integer_user_id(self):
        """Test activity tools work with integer user IDs."""
        user_id = self.user_profile.id
        
        # Test query_activity_catalog
        result = await query_activity_catalog(user_id, domains=["creativity"])
        self.assertIsInstance(result, dict)
        self.assertNotIn('error', result)
        
        # Test tailor_activity
        result = await tailor_activity(user_id, generic_activity_id=1)
        self.assertIsInstance(result, dict)
        self.assertNotIn('error', result)
        
    def test_user_id_conversion_handling(self):
        """Test that user ID conversion is handled gracefully."""
        from apps.main.agents.tools.activity_tools import _query_activities_from_db
        
        # Test with string that can't be converted to int
        async def test_conversion():
            result = await _query_activities_from_db("test-user-123", domains=["creativity"])
            self.assertIsInstance(result, list)
            
        asyncio.run(test_conversion())
        
    @pytest.mark.asyncio
    async def test_confidence_scores_present(self):
        """Test that tools return appropriate confidence scores."""
        user_id = "test-user-confidence"
        
        # Test engagement tools confidence
        result = await get_domain_preferences(user_id)
        self.assertIn('confidence', result)
        self.assertGreater(result['confidence'], 0)
        
        # Test psychological tools confidence
        result = await get_trust_metrics(user_id)
        self.assertIn('confidence', result)
        self.assertGreater(result['confidence'], 0)
        
        result = await get_trait_analysis(user_id)
        self.assertIn('confidence', result)
        self.assertGreater(result['confidence'], 0)
        
    @pytest.mark.asyncio
    async def test_no_database_errors(self):
        """Test that tools don't raise database field errors."""
        user_ids = ["test-user-123", "1", "benchmark-user-456", str(self.user_profile.id)]
        
        for user_id in user_ids:
            # Test engagement tools
            try:
                await get_domain_preferences(user_id)
                await get_completion_patterns(user_id)
                await get_temporal_patterns(user_id)
            except Exception as e:
                self.fail(f"Engagement tools failed for user {user_id}: {str(e)}")
                
            # Test psychological tools
            try:
                await analyze_psychological_state(user_id, reported_mood="neutral")
                await get_trust_metrics(user_id)
                await get_trait_analysis(user_id)
            except Exception as e:
                self.fail(f"Psychological tools failed for user {user_id}: {str(e)}")
                
            # Test activity tools
            try:
                await query_activity_catalog(user_id, domains=["creativity"])
                await tailor_activity(user_id, generic_activity_id=1)
            except Exception as e:
                self.fail(f"Activity tools failed for user {user_id}: {str(e)}")


class DatabaseFieldIntegrationTest(TestCase):
    """Integration tests for database field fixes."""
    
    @pytest.mark.asyncio
    async def test_full_workflow_simulation(self):
        """Test a full workflow simulation with various user ID formats."""
        user_ids = ["test-user-workflow", "benchmark-user-workflow", "1", "2"]
        
        for user_id in user_ids:
            # Simulate a full workflow using multiple tools
            domain_prefs = await get_domain_preferences(user_id)
            self.assertNotIn('error', domain_prefs)
            
            psych_state = await analyze_psychological_state(user_id, reported_mood="neutral")
            self.assertNotIn('error', psych_state)
            
            activities = await query_activity_catalog(user_id, domains=["wellness"])
            self.assertNotIn('error', activities)
            
            if activities.get('activities'):
                activity_id = activities['activities'][0].get('id', 1)
                tailored = await tailor_activity(user_id, generic_activity_id=activity_id)
                self.assertNotIn('error', tailored)
                
    def test_benchmark_user_handling(self):
        """Test that benchmark users are handled correctly."""
        benchmark_user_ids = [
            "test-user-123",
            "benchmark-user-456", 
            "evaluation-user-789"
        ]
        
        async def test_benchmark_users():
            for user_id in benchmark_user_ids:
                # These should not raise exceptions
                result = await get_domain_preferences(user_id)
                self.assertIsInstance(result, dict)
                self.assertIn('confidence', result)
                
        asyncio.run(test_benchmark_users())
