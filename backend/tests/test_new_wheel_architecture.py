"""
Comprehensive pytest tests for the new wheel activity agent architecture.

Tests cover:
1. Individual LLM calls per activity
2. Mistral structured output with ActivityTailoredSchema
3. No reuse of existing tailored activities
4. Quality of selection and tailoring process
"""

import pytest
import asyncio
import json
from unittest.mock import AsyncMock, MagicMock, patch
from apps.main.agents.wheel_activity_agent import WheelAndActivityAgent
from apps.main.services.programmatic_activity_selector import ProgrammaticActivitySelector, SelectionCriteria
from apps.main.schemas.activity_schemas import ActivityTailoredSchema


class TestNewWheelArchitecture:
    """Test suite for the new wheel activity agent architecture."""

    @pytest.fixture
    def agent(self):
        """Create a wheel activity agent for testing."""
        return WheelAndActivityAgent(user_profile_id="test_user_1")

    @pytest.fixture
    def mock_llm_response(self):
        """Create a mock LLM response with structured output."""
        return type('MockResponse', (), {
            'is_text': True,
            'content': json.dumps({
                "id": "test_tailored_activity",
                "name": "Test Tailored Activity",
                "description": "A test activity tailored for the user",
                "instructions": "Follow these test instructions",
                "domain": "wellness",
                "duration_minutes": 25,
                "difficulty_level": 3,
                "required_resources": ["time", "space"],
                "adaptations": ["Indoor friendly", "Adjustable intensity"],
                "resource_intensity": "low",
                "estimated_completion_time": 30,
                "base_challenge_rating": 50,
                "tailorization_level": 80,
                "version": 1,
                "customization_notes": "Test tailoring with structured output",
                "confidence": 0.85,
                "method": "test_llm_tailoring"
            })
        })()

    @pytest.fixture
    def test_activities(self):
        """Create test activities for tailoring."""
        return [
            {
                "id": "test_activity_1",
                "name": "Test Activity 1",
                "description": "First test activity",
                "domain": "wellness",
                "challenge_level": 50,
                "duration_range": "20-30 minutes"
            },
            {
                "id": "test_activity_2", 
                "name": "Test Activity 2",
                "description": "Second test activity",
                "domain": "creativity",
                "challenge_level": 60,
                "duration_range": "15-25 minutes"
            },
            {
                "id": "test_activity_3",
                "name": "Test Activity 3", 
                "description": "Third test activity",
                "domain": "physical",
                "challenge_level": 40,
                "duration_range": "25-35 minutes"
            }
        ]

    @pytest.fixture
    def test_context(self):
        """Create test context for activity tailoring."""
        return {
            "resource_context": {
                "time": {"reported_duration_minutes": 30}
            },
            "context_packet": {
                "reported_mood": "focused",
                "reported_energy_level": "medium",
                "reported_environment": "home",
                "user_name": "TestUser"
            }
        }

    @pytest.mark.asyncio
    async def test_individual_llm_calls_per_activity(self, agent, test_activities, test_context, mock_llm_response):
        """Test that each activity gets its own individual LLM call."""
        # Mock the LLM client to count calls
        call_count = 0
        
        async def mock_llm_call(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            return mock_llm_response
        
        agent.llm_client.chat_completion_with_structured_output = mock_llm_call
        
        # Test individual tailoring
        tailored_activities = await agent._create_tailored_activities(
            test_activities, 
            test_context["resource_context"], 
            test_context["context_packet"]
        )
        
        # Verify each activity got its own LLM call
        assert call_count == len(test_activities), f"Expected {len(test_activities)} LLM calls, got {call_count}"
        assert len(tailored_activities) == len(test_activities), f"Expected {len(test_activities)} tailored activities"
        
        # Verify each activity was processed
        for i, activity in enumerate(tailored_activities):
            assert activity is not None, f"Activity {i} should not be None"
            assert "id" in activity, f"Activity {i} should have an ID"
            assert "name" in activity, f"Activity {i} should have a name"

    @pytest.mark.asyncio
    async def test_mistral_structured_output_integration(self, agent, mock_llm_response):
        """Test integration with Mistral structured output and ActivityTailoredSchema."""
        # Mock the LLM client
        agent.llm_client.chat_completion_with_structured_output = AsyncMock(return_value=mock_llm_response)
        
        # Test single activity tailoring
        test_activity = {
            "id": "test_structured",
            "name": "Test Structured Activity",
            "domain": "wellness",
            "challenge_level": 50
        }
        
        resource_context = {"time": {"reported_duration_minutes": 30}}
        context_packet = {
            "reported_mood": "focused",
            "user_name": "TestUser"
        }
        
        result = await agent._tailor_single_activity_with_llm(
            test_activity, resource_context, context_packet, 0
        )
        
        # Verify structured output was used
        agent.llm_client.chat_completion_with_structured_output.assert_called_once()
        
        # Verify the call included the schema
        call_args = agent.llm_client.chat_completion_with_structured_output.call_args
        assert 'schema' in call_args.kwargs
        assert call_args.kwargs['schema'] == ActivityTailoredSchema
        
        # Verify result structure
        assert result is not None
        assert "id" in result
        assert "name" in result
        assert "domain" in result
        assert "duration_minutes" in result

    @pytest.mark.asyncio
    async def test_no_reuse_of_existing_activities(self, agent):
        """Test that existing tailored activities are not reused."""
        # Verify the agent doesn't use the old tailoring service
        import inspect
        source = inspect.getsource(agent._create_tailored_activities)
        
        # Should not import or use the old activity_tailoring_service
        assert 'activity_tailoring_service' not in source, "Should not use old tailoring service"
        
        # Should use the new individual LLM tailoring method
        assert '_tailor_single_activity_with_llm' in source, "Should use new individual LLM method"
        
        # Verify the agent has the new architecture methods
        assert hasattr(agent, '_tailor_single_activity_with_llm'), "Should have new tailoring method"
        assert hasattr(agent, 'set_custom_tailoring_template'), "Should have custom template method"
        assert hasattr(agent, 'clear_custom_tailoring_template'), "Should have template clearing method"

    @pytest.mark.asyncio
    @pytest.mark.django_db
    async def test_programmatic_selection_quality(self):
        """Test the quality of programmatic activity selection."""
        selector = ProgrammaticActivitySelector("test_user_1")
        
        # Test high-quality selection criteria
        criteria = SelectionCriteria(
            time_available=30,
            energy_level=75,
            available_resources=['time', 'space'],
            target_challenge_range=(40, 60),
            min_activities=3,
            max_activities=6
        )
        
        activities = await selector.select_activities(criteria)
        
        # Verify selection quality
        assert len(activities) >= criteria.min_activities, "Should meet minimum activity count"
        assert len(activities) <= criteria.max_activities, "Should not exceed maximum activity count"
        
        # Verify time matching
        time_matches = sum(1 for a in activities if a.get('estimated_duration', 30) <= criteria.time_available)
        time_ratio = time_matches / len(activities) if activities else 0
        assert time_ratio >= 0.8, f"At least 80% of activities should match time criteria, got {time_ratio:.2f}"
        
        # Verify domain diversity
        domains = set(a.get('domain', 'general') for a in activities)
        assert len(domains) >= 2, f"Should have at least 2 different domains, got {len(domains)}"
        
        # Verify scoring
        for activity in activities:
            assert 'score' in activity, "Each activity should have a score"
            assert activity['score'] > 0, "Activity scores should be positive"

    def test_activity_tailored_schema_validation(self):
        """Test ActivityTailoredSchema validation with various inputs."""
        # Test valid data
        valid_data = {
            "id": "test_schema_validation",
            "name": "Test Schema Activity",
            "description": "Testing schema validation",
            "instructions": "Follow these validation instructions",
            "domain": "wellness",
            "duration_minutes": 25,
            "difficulty_level": 3,
            "required_resources": ["time", "space"],
            "adaptations": ["Indoor friendly"],
            "resource_intensity": "low",
            "estimated_completion_time": 30,
            "base_challenge_rating": 50,
            "tailorization_level": 80,
            "version": 1,
            "customization_notes": "Test validation",
            "confidence": 0.85,
            "method": "test_validation"
        }
        
        # Should validate successfully
        validated = ActivityTailoredSchema(**valid_data)
        assert validated.id == "test_schema_validation"
        assert validated.duration_minutes == 25
        assert validated.difficulty_level == 3
        assert isinstance(validated.required_resources, list)
        assert isinstance(validated.confidence, float)
        
        # Test invalid data - should raise validation error
        invalid_data = valid_data.copy()
        invalid_data["duration_minutes"] = "invalid"  # Should be int
        
        with pytest.raises(ValueError):
            ActivityTailoredSchema(**invalid_data)

    @pytest.mark.asyncio
    async def test_fallback_activity_creation(self, agent):
        """Test fallback activity creation when LLM tailoring fails."""
        test_activity = {
            "id": "test_fallback",
            "name": "Test Fallback Activity",
            "domain": "wellness",
            "challenge_level": 50
        }
        
        resource_context = {"time": {"reported_duration_minutes": 25}}
        context_packet = {"reported_mood": "neutral"}
        
        fallback = agent._create_fallback_activity(test_activity, 0, resource_context, context_packet)
        
        # Verify fallback structure
        assert fallback is not None
        assert "id" in fallback
        assert "name" in fallback
        assert "domain" in fallback
        assert fallback["domain"] == "wellness"  # Should preserve original domain
        assert fallback["duration_minutes"] == 25  # Should match available time
        assert fallback["method"] == "fallback_creation"
        assert fallback["confidence"] < 0.5  # Should have low confidence

    @pytest.mark.asyncio
    async def test_diverse_fallback_activities(self, agent):
        """Test creation of diverse fallback activities."""
        resource_context = {"time": {"reported_duration_minutes": 30}}
        context_packet = {
            "reported_mood": "energetic",
            "reported_environment": "home",
            "reported_energy_level": "high"
        }
        
        activities = agent._create_diverse_fallback_activities(resource_context, context_packet)
        
        # Verify diversity
        assert len(activities) >= 4, "Should create at least 4 diverse activities"
        
        domains = set(activity["domain"] for activity in activities)
        assert len(domains) >= 4, f"Should have at least 4 different domains, got {len(domains)}"
        
        # Verify each activity has required fields
        for activity in activities:
            assert "id" in activity
            assert "name" in activity
            assert "domain" in activity
            assert "duration_minutes" in activity
            assert activity["duration_minutes"] == 30  # Should match available time
            assert "energetic" in activity["name"].lower() or "energetic" in activity["description"].lower()

    @pytest.mark.asyncio
    @pytest.mark.django_db
    async def test_custom_template_integration(self, agent):
        """Test custom template integration with the new architecture."""
        # Test setting custom template
        custom_template = "Custom template for {{USER_NAME}} with {{ENERGY_LEVEL}} energy."
        agent.set_custom_tailoring_template(custom_template)

        assert agent.custom_tailoring_template == custom_template

        # Test contextualization with simplified context (no database access needed)
        context_packet = {
            "user_input_context": {
                "energy_level": 75,
                "user_name": "TestUser"
            }
        }

        contextualized = await agent._contextualize_instructions(context_packet=context_packet)

        # Should use custom template with basic placeholder replacement
        assert "Custom template for" in contextualized
        assert "energy" in contextualized.lower()

        # Test clearing template
        agent.clear_custom_tailoring_template()
        assert agent.custom_tailoring_template is None

    @pytest.mark.asyncio
    @pytest.mark.django_db
    async def test_end_to_end_architecture_flow(self, agent, mock_llm_response):
        """Test the complete end-to-end flow of the new architecture."""
        # Mock LLM client
        agent.llm_client.chat_completion_with_structured_output = AsyncMock(return_value=mock_llm_response)

        # Test complete flow
        strategy_framework = {
            "domain_distribution": {
                "domains": {
                    "physical": 25,
                    "mental": 25,
                    "creative": 25,
                    "wellness": 25
                }
            }
        }

        context_packet = {
            "user_input_context": {
                "energy_level": 75,
                "time_available": 30
            }
        }

        # Test programmatic selection
        activities = await agent._query_activity_catalog(strategy_framework, context_packet)
        assert isinstance(activities, list)

        # If no activities from database, create mock activities for testing
        if len(activities) == 0:
            activities = [
                {"id": "mock_1", "name": "Mock Activity 1", "domain": "physical"},
                {"id": "mock_2", "name": "Mock Activity 2", "domain": "mental"},
                {"id": "mock_3", "name": "Mock Activity 3", "domain": "creative"}
            ]

        # Test tailoring with new architecture
        resource_context = {"time": {"reported_duration_minutes": 30}}
        full_context = {
            "reported_mood": "focused",
            "reported_energy_level": "medium",
            "user_name": "TestUser"
        }

        tailored = await agent._create_tailored_activities(
            activities[:3], resource_context, full_context, strategy_framework
        )

        # Verify end-to-end results
        assert len(tailored) == 3
        assert all("id" in activity for activity in tailored)
        assert all("domain" in activity for activity in tailored)

        # Verify LLM was called for each activity
        assert agent.llm_client.chat_completion_with_structured_output.call_count == 3
