"""
Comprehensive tests for HistoryEvent tracking system and custom activity creation.
"""

import json
import time
from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.contrib.contenttypes.models import ContentType
from django.urls import reverse
from apps.main.models import HistoryEvent, Wheel, WheelItem
from apps.user.models import UserProfile, UserEnvironment, GenericEnvironment
from apps.activity.models import ActivityTailored


class HistoryEventTrackingTestCase(TestCase):
    """Test cases for HistoryEvent tracking system"""

    def setUp(self):
        """Set up test data"""
        self.client = Client()

        # Create test user and profile
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        # Create user profile first
        self.user_profile = UserProfile.objects.create(
            user=self.user,
            profile_name='Test User'
        )

        # Use existing generic environment or create a simple one
        try:
            self.generic_env = GenericEnvironment.objects.first()
            if not self.generic_env:
                # Create a minimal generic environment without domain relationships
                from apps.activity.models import ActivityDomain
                domain = ActivityDomain.objects.first()
                if not domain:
                    domain = ActivityDomain.objects.create(name='general', description='General activities')

                self.generic_env = GenericEnvironment.objects.create(
                    name='Test Environment',
                    description='Test environment for testing'
                )
                # Save first to get primary key
                self.generic_env.save()
        except Exception:
            # Fallback: use existing environment
            self.generic_env = GenericEnvironment.objects.first()

        # Create user environment
        from datetime import date
        if self.generic_env:
            self.user_env = UserEnvironment.objects.create(
                user_profile=self.user_profile,
                generic_environment=self.generic_env,
                environment_name='Test User Environment',
                environment_description='Test environment for testing',
                environment_details={'test': True},
                effective_start=date.today(),
                is_current=True
            )

            # Update user profile with environment
            self.user_profile.current_environment = self.user_env
            self.user_profile.save()
        else:
            # Create a minimal user environment without generic environment
            self.user_env = UserEnvironment.objects.create(
                user_profile=self.user_profile,
                environment_name='Test User Environment',
                environment_description='Test environment for testing',
                environment_details={'test': True},
                effective_start=date.today(),
                is_current=True
            )

            # Update user profile with environment
            self.user_profile.current_environment = self.user_env
            self.user_profile.save()

        # Create test wheel
        from datetime import date
        self.wheel = Wheel.objects.create(
            name='Test Wheel for Test User',
            created_by='test_agent',
            created_at=date.today()
        )

        # Login user
        self.client.login(username='testuser', password='testpass123')

    def test_event_tracking_api_endpoint(self):
        """Test the event tracking API endpoint"""
        event_data = {
            'event_type': 'app_launched',
            'content_type': 'UserProfile',
            'object_id': str(self.user_profile.id),
            'details': {
                'device_info': {
                    'user_agent': 'Test Browser',
                    'platform': 'test',
                    'language': 'en-US'
                },
                'session_id': 'test_session_123'
            }
        }
        
        response = self.client.post(
            '/api/track-event/',
            data=json.dumps(event_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        response_data = response.json()
        self.assertTrue(response_data['success'])
        self.assertIn('event_id', response_data)
        
        # Verify event was created
        event = HistoryEvent.objects.get(id=response_data['event_id'])
        self.assertEqual(event.event_type, 'app_launched')
        self.assertEqual(event.user_profile, self.user_profile)
        self.assertEqual(event.details['session_id'], 'test_session_123')

    def test_custom_activity_creation_api(self):
        """Test custom activity creation API endpoint"""
        activity_data = {
            'title': 'Custom Morning Meditation',
            'description': 'A personalized meditation practice in my garden',
            'duration_range': '15-30 minutes',
            'challengingness': 30,
            'comfort_level': 80
        }
        
        response = self.client.post(
            '/api/custom-activity/',
            data=json.dumps(activity_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        response_data = response.json()
        self.assertTrue(response_data['success'])
        self.assertIn('custom_activity', response_data)
        self.assertIn('history_event_id', response_data)
        
        # Verify HistoryEvent was created
        history_event = HistoryEvent.objects.get(id=response_data['history_event_id'])
        self.assertEqual(history_event.event_type, 'custom_activity_created')
        self.assertEqual(history_event.user_profile, self.user_profile)
        self.assertEqual(history_event.details['title'], 'Custom Morning Meditation')
        self.assertEqual(history_event.details['challengingness'], 30)

    def test_custom_activity_addition_to_wheel(self):
        """Test adding custom activity to wheel via WheelItemManagementView"""
        # First create a custom activity
        activity_data = {
            'title': 'Custom Yoga Session',
            'description': 'Personalized yoga routine',
            'duration_range': '20-45 minutes',
            'challengingness': 60,
            'comfort_level': 70
        }
        
        create_response = self.client.post(
            '/api/custom-activity/',
            data=json.dumps(activity_data),
            content_type='application/json'
        )
        
        self.assertEqual(create_response.status_code, 200)
        create_data = create_response.json()
        custom_activity_id = create_data['custom_activity']['id']
        
        # Now add it to the wheel
        wheel_item_data = {
            'activity_id': custom_activity_id,
            'activity_type': 'custom'
        }
        
        add_response = self.client.post(
            '/api/wheel-items/',
            data=json.dumps(wheel_item_data),
            content_type='application/json'
        )

        # Debug: Print response details if test fails
        if add_response.status_code != 200:
            print(f"Response status: {add_response.status_code}")
            print(f"Response content: {add_response.content.decode()}")

        self.assertEqual(add_response.status_code, 200)
        add_data = add_response.json()
        self.assertTrue(add_data['success'])
        
        # Verify ActivityTailored was created from HistoryEvent
        activity_tailored = ActivityTailored.objects.filter(
            name='Custom Yoga Session',
            user_profile=self.user_profile
        ).first()
        
        self.assertIsNotNone(activity_tailored)
        self.assertEqual(activity_tailored.base_challenge_rating, 60)
        self.assertEqual(activity_tailored.duration_range, '20-45 minutes')
        self.assertEqual(activity_tailored.tailorization_level, 100)  # Fully custom

    def test_wheel_spin_event_tracking(self):
        """Test wheel spin event tracking"""
        # Test contract display event
        contract_event_data = {
            'event_type': 'contract_displayed',
            'content_type': 'Wheel',
            'object_id': str(self.wheel.id),
            'details': {
                'wheel_items_count': 4,
                'user_energy_level': 75,
                'time_available': 30
            }
        }
        
        response = self.client.post(
            '/api/track-event/',
            data=json.dumps(contract_event_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        
        # Test contract signing event
        signing_event_data = {
            'event_type': 'contract_signed',
            'content_type': 'Wheel',
            'object_id': str(self.wheel.id),
            'details': {
                'signature_data': 'test_signature_data',
                'acceptance_timestamp': time.time(),
                'wheel_items_count': 4
            }
        }
        
        response = self.client.post(
            '/api/track-event/',
            data=json.dumps(signing_event_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        
        # Test wheel spin initiation
        spin_event_data = {
            'event_type': 'wheel_spin_initiated',
            'content_type': 'Wheel',
            'object_id': str(self.wheel.id),
            'details': {
                'user_energy_level': 75,
                'time_available': 30,
                'wheel_items_count': 4
            }
        }
        
        response = self.client.post(
            '/api/track-event/',
            data=json.dumps(spin_event_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        
        # Verify all events were created
        contract_events = HistoryEvent.objects.filter(
            event_type__in=['contract_displayed', 'contract_signed', 'wheel_spin_initiated'],
            user_profile=self.user_profile
        )
        
        self.assertEqual(contract_events.count(), 3)

    def test_activity_search_event_tracking(self):
        """Test activity search event tracking"""
        search_event_data = {
            'event_type': 'activity_search_performed',
            'content_type': 'UserProfile',
            'object_id': str(self.user_profile.id),
            'details': {
                'search_query': 'meditation',
                'results_count': 15,
                'filters_applied': {
                    'energy_level': 60,
                    'time_available': 30
                }
            }
        }
        
        response = self.client.post(
            '/api/track-event/',
            data=json.dumps(search_event_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        
        # Verify event was created
        search_event = HistoryEvent.objects.filter(
            event_type='activity_search_performed',
            user_profile=self.user_profile
        ).first()
        
        self.assertIsNotNone(search_event)
        self.assertEqual(search_event.details['search_query'], 'meditation')
        self.assertEqual(search_event.details['results_count'], 15)

    def test_modal_interaction_tracking(self):
        """Test modal interaction event tracking"""
        modal_event_data = {
            'event_type': 'modal_opened',
            'content_type': 'UserProfile',
            'object_id': str(self.user_profile.id),
            'details': {
                'modal_type': 'add_activity',
                'trigger_action': 'add_button_click',
                'context': {
                    'current_wheel_items': 3,
                    'user_energy_level': 80
                }
            }
        }
        
        response = self.client.post(
            '/api/track-event/',
            data=json.dumps(modal_event_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        
        # Verify event was created
        modal_event = HistoryEvent.objects.filter(
            event_type='modal_opened',
            user_profile=self.user_profile
        ).first()
        
        self.assertIsNotNone(modal_event)
        self.assertEqual(modal_event.details['modal_type'], 'add_activity')

    def test_event_tracking_authentication_required(self):
        """Test that event tracking requires authentication"""
        # Logout user
        self.client.logout()
        
        event_data = {
            'event_type': 'app_launched',
            'content_type': 'UserProfile',
            'object_id': str(self.user_profile.id),
            'details': {}
        }
        
        response = self.client.post(
            '/api/track-event/',
            data=json.dumps(event_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 401)
        response_data = response.json()
        self.assertFalse(response_data['success'])
        self.assertEqual(response_data['error'], 'Authentication required')

    def test_event_tracking_validation(self):
        """Test event tracking input validation"""
        # Test missing required fields
        invalid_data = {
            'event_type': 'app_launched',
            # Missing content_type and object_id
            'details': {}
        }
        
        response = self.client.post(
            '/api/track-event/',
            data=json.dumps(invalid_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 400)
        response_data = response.json()
        self.assertFalse(response_data['success'])
        self.assertIn('required', response_data['error'])

    def test_custom_activity_validation(self):
        """Test custom activity creation validation"""
        # Test missing required fields
        invalid_data = {
            'title': '',  # Empty title
            'description': '',  # Empty description
            'challengingness': 50
        }
        
        response = self.client.post(
            '/api/custom-activity/',
            data=json.dumps(invalid_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 400)
        response_data = response.json()
        self.assertFalse(response_data['success'])
        self.assertIn('required', response_data['error'])
