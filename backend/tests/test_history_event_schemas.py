"""
Tests for HistoryEvent schema system
"""

import pytest
from django.test import TestCase
from django.contrib.contenttypes.models import ContentType

from apps.main.models import HistoryEvent, Wheel
from apps.user.models import UserProfile
from django.contrib.auth.models import User
from apps.main.services.history_event_service import HistoryEventService
from apps.main.schemas.history_event_schemas import (
    validate_event_details,
    get_event_schema,
    get_event_schema_info,
    EVENT_SCHEMAS
)


class HistoryEventSchemaTestCase(TestCase):
    """Test cases for HistoryEvent schema system"""

    def setUp(self):
        """Set up test data"""
        # Create test user and profile
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.user_profile = UserProfile.objects.create(
            user=self.user,
            profile_name='Test User'
        )
        
        # Create test wheel
        self.wheel = Wheel.objects.create(
            name='Test Wheel',
            created_by='test_agent',
            created_at='2024-01-01'
        )

    def test_wheel_generated_schema_validation(self):
        """Test wheel_generated event schema validation"""
        valid_details = {
            "workflow_id": "workflow_123",
            "agent_flow": "wheel_generation",
            "performance_metrics": {
                "generation_time_ms": 2500,
                "item_count": 5,
                "domain_count": 3
            },
            "wheel_summary": {
                "domains": ["physical", "creative", "social"],
                "total_duration_minutes": 120
            }
        }
        
        # Should validate successfully
        validated = validate_event_details("wheel_generated", valid_details)
        self.assertIn("workflow_id", validated)
        self.assertIn("schema_version", validated)
        self.assertEqual(validated["workflow_id"], "workflow_123")

    def test_wheel_generated_schema_validation_missing_required(self):
        """Test wheel_generated event schema validation with missing required fields"""
        invalid_details = {
            "agent_flow": "wheel_generation",
            # Missing required workflow_id
        }
        
        # Should raise validation error
        with self.assertRaises(ValueError):
            validate_event_details("wheel_generated", invalid_details)

    def test_frontend_error_schema_validation(self):
        """Test frontend error event schema validation"""
        valid_details = {
            "error_type": "CONNECTION_ERROR",
            "error_message": "WebSocket connection failed",
            "error_level": "temporary",
            "component": "websocket-manager",
            "user_agent": "Mozilla/5.0...",
            "url": "https://example.com/app",
            "retry_count": 3,
            "auto_resolved": True
        }
        
        # Should validate successfully
        validated = validate_event_details("frontend_error_temporary", valid_details)
        self.assertIn("error_type", validated)
        self.assertIn("schema_version", validated)
        self.assertEqual(validated["error_level"], "temporary")

    def test_unknown_event_type_fallback(self):
        """Test handling of unknown event types"""
        unknown_details = {
            "custom_field": "custom_value",
            "timestamp": "2023-01-01T00:00:00Z"
        }
        
        # Should fall back to base schema validation
        validated = validate_event_details("unknown_event_type", unknown_details)
        self.assertIn("custom_field", validated)
        self.assertIn("schema_version", validated)

    def test_history_event_service_wheel_generated(self):
        """Test HistoryEventService for wheel_generated events"""
        performance_metrics = {
            "generation_time_ms": 2500,
            "item_count": 5,
            "domain_count": 3
        }
        wheel_summary = {
            "domains": ["physical", "creative", "social"],
            "total_duration_minutes": 120
        }
        
        event = HistoryEventService.create_wheel_generated_event(
            user_profile=self.user_profile,
            wheel=self.wheel,
            workflow_id="workflow_123",
            performance_metrics=performance_metrics,
            wheel_summary=wheel_summary
        )
        
        self.assertEqual(event.event_type, "wheel_generated")
        self.assertEqual(event.user_profile, self.user_profile)
        self.assertIn("workflow_id", event.details)
        self.assertIn("schema_version", event.details)
        self.assertEqual(event.details["workflow_id"], "workflow_123")

    def test_history_event_service_activity_removed(self):
        """Test HistoryEventService for activity_removed_from_wheel events"""
        event = HistoryEventService.create_activity_removed_event(
            wheel=self.wheel,
            user_profile=self.user_profile,
            activity_name="Morning Run",
            activity_domain="physical",
            wheel_items_before=5,
            wheel_items_after=4,
            removal_method="api_delete",
            wheel_item_id="item_123"
        )
        
        self.assertEqual(event.event_type, "activity_removed_from_wheel")
        self.assertEqual(event.user_profile, self.user_profile)
        self.assertIn("activity_name", event.details)
        self.assertIn("schema_version", event.details)
        self.assertEqual(event.details["activity_name"], "Morning Run")
        self.assertEqual(event.details["wheel_items_before"], 5)
        self.assertEqual(event.details["wheel_items_after"], 4)

    def test_history_event_service_frontend_error(self):
        """Test HistoryEventService for frontend error events"""
        event = HistoryEventService.create_frontend_error_event(
            user_profile=self.user_profile,
            error_type="CONNECTION_ERROR",
            error_message="WebSocket connection failed",
            error_level="temporary",
            component="websocket-manager",
            user_agent="Mozilla/5.0...",
            url="https://example.com/app",
            retry_count=3,
            auto_resolved=True
        )
        
        self.assertEqual(event.event_type, "frontend_error_temporary")
        self.assertEqual(event.user_profile, self.user_profile)
        self.assertIn("error_type", event.details)
        self.assertIn("schema_version", event.details)
        self.assertEqual(event.details["error_type"], "CONNECTION_ERROR")
        self.assertEqual(event.details["retry_count"], 3)
        self.assertTrue(event.details["auto_resolved"])

    def test_get_event_schema_info(self):
        """Test getting schema information"""
        schema_info = get_event_schema_info("wheel_generated")
        
        self.assertTrue(schema_info["schema_available"])
        self.assertEqual(schema_info["event_type"], "wheel_generated")
        self.assertIn("workflow_id", schema_info["fields"])
        self.assertIn("workflow_id", schema_info["required_fields"])

    def test_get_event_schema_info_unknown(self):
        """Test getting schema information for unknown event type"""
        schema_info = get_event_schema_info("unknown_event")
        
        self.assertFalse(schema_info["schema_available"])
        self.assertEqual(schema_info["event_type"], "unknown_event")
        self.assertEqual(schema_info["fields"], [])

    def test_list_all_schemas(self):
        """Test listing all available schemas"""
        schemas_info = HistoryEventService.list_available_schemas()
        
        self.assertIn("schemas", schemas_info)
        self.assertIn("total_count", schemas_info)
        self.assertGreater(schemas_info["total_count"], 0)
        
        # Check that wheel_generated is in the list
        schema_names = [schema["event_type"] for schema in schemas_info["schemas"]]
        self.assertIn("wheel_generated", schema_names)
        self.assertIn("frontend_error_temporary", schema_names)

    def test_schema_versioning(self):
        """Test that schema versioning is properly included"""
        details = {
            "workflow_id": "workflow_123",
            "performance_metrics": {"generation_time_ms": 1000},
            "wheel_summary": {"domains": ["test"]}
        }
        
        validated = validate_event_details("wheel_generated", details)
        self.assertIn("schema_version", validated)
        self.assertEqual(validated["schema_version"], "1.0")

    def test_schema_extra_fields_allowed(self):
        """Test that extra fields are allowed in schemas"""
        details = {
            "workflow_id": "workflow_123",
            "performance_metrics": {"generation_time_ms": 1000},
            "wheel_summary": {"domains": ["test"]},
            "extra_field": "extra_value",  # This should be allowed
            "custom_metadata": {"key": "value"}
        }
        
        validated = validate_event_details("wheel_generated", details)
        self.assertIn("extra_field", validated)
        self.assertIn("custom_metadata", validated)
        self.assertEqual(validated["extra_field"], "extra_value")

    def test_performance_metrics_validation(self):
        """Test that performance metrics are properly validated and defaulted"""
        details = {
            "workflow_id": "workflow_123",
            "performance_metrics": {},  # Empty metrics should get defaults
            "wheel_summary": {"domains": ["test"]}
        }
        
        validated = validate_event_details("wheel_generated", details)
        metrics = validated["performance_metrics"]
        
        # Should have default values
        self.assertIn("generation_time_ms", metrics)
        self.assertIn("item_count", metrics)
        self.assertIn("domain_count", metrics)
        self.assertEqual(metrics["generation_time_ms"], 0)
        self.assertEqual(metrics["item_count"], 0)
        self.assertEqual(metrics["domain_count"], 0)
