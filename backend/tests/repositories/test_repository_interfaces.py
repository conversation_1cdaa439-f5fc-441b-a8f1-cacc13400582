"""
Tests for Repository Interfaces and Implementations

This test suite validates that repository interfaces work correctly with
mock implementations and that business services integrate properly.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock
from typing import List, Optional

from apps.main.domain.repositories.repository_interfaces import (
    ActivityRepositoryInterface,
    WheelRepositoryInterface,
    UserRepositoryInterface,
    CacheRepositoryInterface
)
from apps.main.domain.models.activity_models import ActivityData, ActivitySelectionCriteria
from apps.main.domain.models.wheel_models import WheelData, WheelItemData
from apps.main.domain.models.user_models import UserContext
from apps.main.domain.enums.domain_enums import DomainCode, EnergyLevel, TrustPhase


class MockActivityRepository(ActivityRepositoryInterface):
    """Mock implementation of ActivityRepositoryInterface for testing."""

    def __init__(self):
        self.activities = []
        self.call_log = []

    async def find_by_criteria(self, criteria: ActivitySelectionCriteria) -> List[ActivityData]:
        self.call_log.append(('find_by_criteria', criteria))
        # Return mock activities that match criteria
        return [
            ActivityData(
                id="mock-activity-1",
                name="Mock Mindfulness",
                description="A mock mindfulness activity for testing",
                instructions="Mock instructions for mindfulness practice",
                domain=DomainCode.WELLNESS,
                duration_minutes=15,
                challenge_rating=30,
                energy_requirement=EnergyLevel.LOW
            ),
            ActivityData(
                id="mock-activity-2",
                name="Mock Creative Writing",
                description="A mock creative writing activity for testing",
                instructions="Mock instructions for creative writing",
                domain=DomainCode.CREATIVITY,
                duration_minutes=25,
                challenge_rating=40,
                energy_requirement=EnergyLevel.MEDIUM
            )
        ]

    async def find_by_id(self, activity_id: str) -> Optional[ActivityData]:
        self.call_log.append(('find_by_id', activity_id))
        if activity_id == "mock-activity-1":
            return ActivityData(
                id="mock-activity-1",
                name="Mock Mindfulness",
                description="A mock mindfulness activity for testing",
                instructions="Mock instructions for mindfulness practice",
                domain=DomainCode.WELLNESS,
                duration_minutes=15,
                challenge_rating=30,
                energy_requirement=EnergyLevel.LOW
            )
        return None

    async def find_by_ids(self, activity_ids: List[str]) -> List[ActivityData]:
        self.call_log.append(('find_by_ids', activity_ids))
        return [await self.find_by_id(aid) for aid in activity_ids if await self.find_by_id(aid)]

    async def find_by_domain(self, domain: DomainCode, limit: Optional[int] = None) -> List[ActivityData]:
        self.call_log.append(('find_by_domain', domain, limit))
        return []

    async def find_by_energy_level(self, energy_level: EnergyLevel, limit: Optional[int] = None) -> List[ActivityData]:
        self.call_log.append(('find_by_energy_level', energy_level, limit))
        return []

    async def find_by_duration_range(self, min_duration: int, max_duration: int) -> List[ActivityData]:
        self.call_log.append(('find_by_duration_range', min_duration, max_duration))
        return []

    async def find_by_challenge_range(self, min_challenge: int, max_challenge: int) -> List[ActivityData]:
        self.call_log.append(('find_by_challenge_range', min_challenge, max_challenge))
        return []

    async def find_by_resources(self, required_resources) -> List[ActivityData]:
        self.call_log.append(('find_by_resources', required_resources))
        return []

    async def save_activity(self, activity: ActivityData) -> str:
        self.call_log.append(('save_activity', activity))
        return "mock-saved-id"

    async def update_activity(self, activity: ActivityData) -> bool:
        self.call_log.append(('update_activity', activity))
        return True

    async def delete_activity(self, activity_id: str) -> bool:
        self.call_log.append(('delete_activity', activity_id))
        return True

    async def count_by_criteria(self, criteria: ActivitySelectionCriteria) -> int:
        self.call_log.append(('count_by_criteria', criteria))
        return 2


class MockWheelRepository(WheelRepositoryInterface):
    """Mock implementation of WheelRepositoryInterface for testing."""

    def __init__(self):
        self.wheels = {}
        self.call_log = []

    async def save_wheel(self, wheel: WheelData) -> str:
        self.call_log.append(('save_wheel', wheel))
        wheel_id = "mock-wheel-id"
        self.wheels[wheel_id] = wheel
        return wheel_id

    async def find_by_id(self, wheel_id: str) -> Optional[WheelData]:
        self.call_log.append(('find_by_id', wheel_id))
        return self.wheels.get(wheel_id)

    async def find_by_user_id(self, user_id: str, limit: Optional[int] = None) -> List[WheelData]:
        self.call_log.append(('find_by_user_id', user_id, limit))
        return list(self.wheels.values())[:limit] if limit else list(self.wheels.values())

    async def find_recent_wheels(self, limit: int = 10) -> List[WheelData]:
        self.call_log.append(('find_recent_wheels', limit))
        return list(self.wheels.values())[:limit]

    async def update_wheel(self, wheel: WheelData) -> bool:
        self.call_log.append(('update_wheel', wheel))
        if wheel.id and wheel.id in self.wheels:
            self.wheels[wheel.id] = wheel
            return True
        return False

    async def delete_wheel(self, wheel_id: str) -> bool:
        self.call_log.append(('delete_wheel', wheel_id))
        if wheel_id in self.wheels:
            del self.wheels[wheel_id]
            return True
        return False

    async def count_by_user(self, user_id: str) -> int:
        self.call_log.append(('count_by_user', user_id))
        return len(self.wheels)


class MockUserRepository(UserRepositoryInterface):
    """Mock implementation of UserRepositoryInterface for testing."""

    def __init__(self):
        self.users = {}
        self.call_log = []

    async def get_user_context(self, user_profile_id: str) -> Optional[UserContext]:
        self.call_log.append(('get_user_context', user_profile_id))
        if user_profile_id == "test-user-123":
            return UserContext(
                user_profile_id=user_profile_id,
                energy_level=75,
                time_available=60,
                trust_phase=TrustPhase.FOUNDATION
            )
        return None

    async def save_user_context(self, context: UserContext) -> bool:
        self.call_log.append(('save_user_context', context))
        self.users[context.user_profile_id] = context
        return True

    async def update_user_preferences(self, user_id: str, domain_preferences) -> bool:
        self.call_log.append(('update_user_preferences', user_id, domain_preferences))
        return True

    async def add_recent_activity(self, user_id: str, activity_id: str) -> bool:
        self.call_log.append(('add_recent_activity', user_id, activity_id))
        return True

    async def get_recent_activities(self, user_id: str, limit: int = 10) -> List[str]:
        self.call_log.append(('get_recent_activities', user_id, limit))
        return ["activity-1", "activity-2"]

    async def update_trust_phase(self, user_id: str, trust_phase: str) -> bool:
        self.call_log.append(('update_trust_phase', user_id, trust_phase))
        return True

    async def user_exists(self, user_profile_id: str) -> bool:
        self.call_log.append(('user_exists', user_profile_id))
        return user_profile_id == "test-user-123"


@pytest.mark.asyncio
class TestRepositoryInterfaces:
    """Test repository interfaces with mock implementations."""

    async def test_activity_repository_interface(self):
        """Test ActivityRepositoryInterface with mock implementation."""
        repo = MockActivityRepository()
        
        # Test find_by_criteria
        criteria = ActivitySelectionCriteria(
            time_available=30,
            energy_level=70,
            trust_phase=TrustPhase.FOUNDATION
        )
        activities = await repo.find_by_criteria(criteria)
        
        assert len(activities) == 2
        assert activities[0].name == "Mock Mindfulness"
        assert activities[1].name == "Mock Creative Writing"
        assert ('find_by_criteria', criteria) in repo.call_log

        # Test find_by_id
        activity = await repo.find_by_id("mock-activity-1")
        assert activity is not None
        assert activity.name == "Mock Mindfulness"
        assert ('find_by_id', "mock-activity-1") in repo.call_log

        # Test save_activity
        new_activity = ActivityData(
            id="new-activity",
            name="New Activity",
            description="A new test activity",
            instructions="Test instructions",
            domain=DomainCode.PHYSICAL,
            duration_minutes=20,
            challenge_rating=50,
            energy_requirement=EnergyLevel.MEDIUM
        )
        saved_id = await repo.save_activity(new_activity)
        assert saved_id == "mock-saved-id"
        assert ('save_activity', new_activity) in repo.call_log

    async def test_wheel_repository_interface(self):
        """Test WheelRepositoryInterface with mock implementation."""
        repo = MockWheelRepository()
        
        # Create test wheel
        wheel_items = [
            WheelItemData(
                id="item-1",
                activity_id="activity-1",
                name="Test Activity",
                percentage=100.0,
                probability=1.0,
                position=0,
                domain=DomainCode.WELLNESS,
                challenge_rating=30,
                duration_minutes=15
            )
        ]
        
        wheel = WheelData(
            name="Test Wheel",
            items=wheel_items,
            trust_phase=TrustPhase.FOUNDATION
        )
        
        # Test save_wheel
        wheel_id = await repo.save_wheel(wheel)
        assert wheel_id == "mock-wheel-id"
        assert ('save_wheel', wheel) in repo.call_log
        
        # Test find_by_id
        found_wheel = await repo.find_by_id(wheel_id)
        assert found_wheel is not None
        assert found_wheel.name == "Test Wheel"
        assert ('find_by_id', wheel_id) in repo.call_log

    async def test_user_repository_interface(self):
        """Test UserRepositoryInterface with mock implementation."""
        repo = MockUserRepository()
        
        # Test get_user_context
        context = await repo.get_user_context("test-user-123")
        assert context is not None
        assert context.user_profile_id == "test-user-123"
        assert context.energy_level == 75
        assert ('get_user_context', "test-user-123") in repo.call_log
        
        # Test user_exists
        exists = await repo.user_exists("test-user-123")
        assert exists is True
        assert ('user_exists', "test-user-123") in repo.call_log
        
        # Test non-existent user
        context = await repo.get_user_context("non-existent")
        assert context is None
