"""
Tests for Business Service Integration with Repositories

This test suite validates that business services work correctly with
repository implementations and maintain clean separation of concerns.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock

from apps.main.domain.services.activity_selection_service import ActivitySelectionService
from apps.main.domain.services.wheel_generation_service import WheelGenerationService
from apps.main.domain.models.activity_models import ActivitySelectionCriteria
from apps.main.domain.models.wheel_models import WheelGenerationRequest, WheelConfiguration
from apps.main.domain.enums.domain_enums import TrustPhase, ProbabilityStrategy
from apps.main.infrastructure.repositories.django_repository_factory import DjangoRepositoryFactory

from .test_repository_interfaces import MockActivityRepository, MockWheelRepository, MockUserRepository


class MockRepositoryFactory:
    """Mock repository factory for testing."""

    def __init__(self):
        self.activity_repository = MockActivityRepository()
        self.wheel_repository = MockWheelRepository()
        self.user_repository = MockUserRepository()

    def create_activity_repository(self):
        return self.activity_repository

    def create_wheel_repository(self):
        return self.wheel_repository

    def create_user_repository(self):
        return self.user_repository

    def create_cache_repository(self):
        return Mock()


@pytest.mark.asyncio
class TestBusinessServiceIntegration:
    """Test business services with repository integration."""

    async def test_activity_selection_service_with_repository(self):
        """Test ActivitySelectionService uses repository correctly."""
        # Setup
        mock_repo = MockActivityRepository()
        service = ActivitySelectionService(activity_repository=mock_repo)
        
        criteria = ActivitySelectionCriteria(
            time_available=30,
            energy_level=70,
            trust_phase=TrustPhase.FOUNDATION
        )
        
        # Execute
        activities = await service.select_activities(criteria)
        
        # Verify
        assert len(activities) == 2
        assert activities[0].name == "Mock Mindfulness"
        assert activities[1].name == "Mock Creative Writing"
        
        # Verify repository was called
        assert ('find_by_criteria', criteria) in mock_repo.call_log
        
        # Verify business logic was applied (activities should be scored and filtered)
        assert all(activity.confidence > 0 for activity in activities)

    async def test_activity_selection_service_without_repository(self):
        """Test ActivitySelectionService fallback when no repository provided."""
        # Setup
        service = ActivitySelectionService(activity_repository=None)
        
        criteria = ActivitySelectionCriteria(
            time_available=30,
            energy_level=70,
            trust_phase=TrustPhase.FOUNDATION
        )
        
        # Execute
        activities = await service.select_activities(criteria)
        
        # Verify fallback mock data is used
        assert len(activities) == 3  # Mock implementation returns 3 activities
        assert any("Mindfulness" in activity.name for activity in activities)

    async def test_wheel_generation_service_with_repositories(self):
        """Test WheelGenerationService with full repository integration."""
        # Setup
        factory = MockRepositoryFactory()
        service = WheelGenerationService.create_with_dependencies(factory)
        
        # Create generation request
        request = WheelGenerationRequest(
            user_profile_id="test-user-123",
            selection_criteria=ActivitySelectionCriteria(
                time_available=60,
                energy_level=75,
                trust_phase=TrustPhase.FOUNDATION
            ),
            wheel_config=WheelConfiguration(
                item_count=5,
                probability_strategy=ProbabilityStrategy.ADAPTIVE,
                trust_phase=TrustPhase.FOUNDATION
            )
        )
        
        # Execute
        result = await service.generate_wheel(request)
        
        # Verify result structure
        assert result is not None
        assert result.wheel is not None
        assert len(result.wheel.items) >= 2  # Should have at least the mock activities
        assert result.selected_activities is not None
        assert result.generation_time_seconds > 0
        assert 0 <= result.selection_quality_score <= 1
        assert 0 <= result.domain_diversity_score <= 1
        
        # Verify repositories were called
        activity_repo = factory.activity_repository
        wheel_repo = factory.wheel_repository
        user_repo = factory.user_repository
        
        # Activity repository should have been called for candidate activities
        assert any('find_by_criteria' in call[0] for call in activity_repo.call_log)
        
        # User repository should have been called for user context
        assert any('get_user_context' in call[0] for call in user_repo.call_log)
        
        # Wheel repository should have been called to save the wheel
        assert any('save_wheel' in call[0] for call in wheel_repo.call_log)

    async def test_wheel_generation_service_without_repositories(self):
        """Test WheelGenerationService fallback when no repositories provided."""
        # Setup
        service = WheelGenerationService.create_with_dependencies(None)
        
        # Create generation request
        request = WheelGenerationRequest(
            user_profile_id="test-user-123",
            selection_criteria=ActivitySelectionCriteria(
                time_available=60,
                energy_level=75,
                trust_phase=TrustPhase.FOUNDATION
            ),
            wheel_config=WheelConfiguration(
                item_count=5,
                probability_strategy=ProbabilityStrategy.ADAPTIVE,
                trust_phase=TrustPhase.FOUNDATION
            )
        )
        
        # Execute
        result = await service.generate_wheel(request)
        
        # Verify result structure (should work with fallback mock data)
        assert result is not None
        assert result.wheel is not None
        assert len(result.wheel.items) >= 2
        assert result.selected_activities is not None
        
        # Verify wheel was not persisted (no repository)
        assert result.wheel.id is None

    async def test_repository_error_handling(self):
        """Test business services handle repository errors gracefully."""
        # Setup mock repository that raises errors
        mock_repo = Mock()
        mock_repo.find_by_criteria = AsyncMock(side_effect=Exception("Database error"))
        
        service = ActivitySelectionService(activity_repository=mock_repo)
        
        criteria = ActivitySelectionCriteria(
            time_available=30,
            energy_level=70,
            trust_phase=TrustPhase.FOUNDATION
        )
        
        # Execute - should not raise exception but fall back gracefully
        activities = await service.select_activities(criteria)
        
        # Verify fallback behavior
        assert len(activities) == 3  # Should fall back to mock data

    async def test_repository_dependency_injection(self):
        """Test that repository dependencies are properly injected."""
        # Setup
        activity_repo = MockActivityRepository()
        wheel_repo = MockWheelRepository()
        user_repo = MockUserRepository()
        
        service = WheelGenerationService(
            activity_repository=activity_repo,
            wheel_repository=wheel_repo,
            user_repository=user_repo
        )
        
        # Verify dependencies are set
        assert service.activity_repository is activity_repo
        assert service.wheel_repository is wheel_repo
        assert service.user_repository is user_repo
        
        # Verify activity selector has repository dependency
        assert service.activity_selector.activity_repository is activity_repo

    async def test_business_logic_preservation(self):
        """Test that business logic is preserved when using repositories."""
        # Setup
        mock_repo = MockActivityRepository()
        service = ActivitySelectionService(activity_repository=mock_repo)
        
        # Test with high energy criteria
        high_energy_criteria = ActivitySelectionCriteria(
            time_available=60,
            energy_level=90,  # High energy
            trust_phase=TrustPhase.EXPANSION
        )
        
        activities = await service.select_activities(high_energy_criteria)
        
        # Verify business logic is applied
        assert len(activities) >= 1
        
        # Test with low energy criteria
        low_energy_criteria = ActivitySelectionCriteria(
            time_available=30,
            energy_level=20,  # Low energy
            trust_phase=TrustPhase.FOUNDATION
        )
        
        activities = await service.select_activities(low_energy_criteria)
        
        # Verify business logic is applied
        assert len(activities) >= 1
        
        # Verify repository was called for both scenarios
        assert len(mock_repo.call_log) >= 2

    async def test_caching_integration(self):
        """Test that caching works correctly with repositories."""
        # This would test caching behavior, but since we're using mock repositories
        # we can verify that the caching interface is properly used
        
        factory = MockRepositoryFactory()
        cache_repo = factory.create_cache_repository()
        
        # Verify cache repository is available
        assert cache_repo is not None
        
        # In a real implementation, we would test:
        # - Cache hits and misses
        # - Cache invalidation
        # - Performance improvements from caching
