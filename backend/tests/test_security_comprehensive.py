"""
Comprehensive Security Test Suite for Goali Application

This test suite validates all security aspects of the application with ambitious
coverage and excellence in testing methodology.
"""

import pytest
import json
from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from apps.user.models import UserProfile, Demographics, Preference
from apps.main.models import UserFeedback
from unittest.mock import patch, MagicMock


class SecurityTestCase(TestCase):
    """Base class for security tests with common setup"""
    
    def setUp(self):
        """Set up test data for security testing"""
        self.client = Client()
        
        # Create test users with different privilege levels
        self.regular_user = User.objects.create_user(
            username='regular_user',
            email='<EMAIL>',
            password='secure_password_123'
        )
        self.regular_profile = UserProfile.objects.create(
            user=self.regular_user,
            profile_name='Regular User'
        )
        
        self.staff_user = User.objects.create_user(
            username='staff_user',
            email='<EMAIL>',
            password='staff_password_123',
            is_staff=True
        )
        self.staff_profile = UserProfile.objects.create(
            user=self.staff_user,
            profile_name='Staff User'
        )
        
        self.admin_user = User.objects.create_superuser(
            username='admin_user',
            email='<EMAIL>',
            password='admin_password_123'
        )
        self.admin_profile = UserProfile.objects.create(
            user=self.admin_user,
            profile_name='Admin User'
        )
        
        # Create another regular user for cross-user access testing
        self.other_user = User.objects.create_user(
            username='other_user',
            email='<EMAIL>',
            password='other_password_123'
        )
        self.other_profile = UserProfile.objects.create(
            user=self.other_user,
            profile_name='Other User'
        )


class AuthenticationSecurityTests(SecurityTestCase):
    """Test authentication security measures"""
    
    def test_unauthenticated_api_access_denied(self):
        """Test that all protected API endpoints deny unauthenticated access"""
        protected_endpoints = [
            '/api/user/profile/',
            f'/api/user/profile/{self.regular_profile.id}/',
            '/api/activities/catalog/',
            '/api/auth/verify/',
        ]
        
        for endpoint in protected_endpoints:
            with self.subTest(endpoint=endpoint):
                response = self.client.get(endpoint)
                self.assertIn(response.status_code, [401, 403], 
                            f"Endpoint {endpoint} should deny unauthenticated access")
    
    def test_login_with_valid_credentials(self):
        """Test successful login with valid credentials"""
        response = self.client.post('/api/auth/login/', {
            'username': 'regular_user',
            'password': 'secure_password_123'
        }, content_type='application/json')
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data['success'])
        self.assertEqual(data['user']['username'], 'regular_user')
        self.assertEqual(data['user']['id'], str(self.regular_profile.id))
        self.assertFalse(data['user']['is_staff'])
    
    def test_login_with_invalid_credentials(self):
        """Test login rejection with invalid credentials"""
        invalid_credentials = [
            {'username': 'regular_user', 'password': 'wrong_password'},
            {'username': 'nonexistent_user', 'password': 'any_password'},
            {'username': '', 'password': 'secure_password_123'},
            {'username': 'regular_user', 'password': ''},
        ]
        
        for credentials in invalid_credentials:
            with self.subTest(credentials=credentials):
                response = self.client.post('/api/auth/login/', 
                                          credentials, content_type='application/json')
                self.assertIn(response.status_code, [400, 401])
    
    def test_session_management(self):
        """Test session creation, verification, and logout"""
        # Login
        login_response = self.client.post('/api/auth/login/', {
            'username': 'regular_user',
            'password': 'secure_password_123'
        }, content_type='application/json')
        self.assertEqual(login_response.status_code, 200)
        
        # Verify session
        verify_response = self.client.post('/api/auth/verify/')
        self.assertEqual(verify_response.status_code, 200)
        verify_data = verify_response.json()
        self.assertTrue(verify_data['valid'])
        
        # Logout
        logout_response = self.client.post('/api/auth/logout/')
        self.assertEqual(logout_response.status_code, 200)
        
        # Verify session is invalid after logout
        verify_after_logout = self.client.post('/api/auth/verify/')
        self.assertEqual(verify_after_logout.status_code, 401)
    
    def test_brute_force_protection(self):
        """Test protection against brute force attacks"""
        # Attempt multiple failed logins
        for i in range(10):
            response = self.client.post('/api/auth/login/', {
                'username': 'regular_user',
                'password': f'wrong_password_{i}'
            }, content_type='application/json')
            self.assertIn(response.status_code, [401, 429])  # 429 for rate limiting


class AuthorizationSecurityTests(SecurityTestCase):
    """Test authorization and access control"""
    
    def test_user_can_access_own_profile(self):
        """Test that users can access their own profile"""
        self.client.login(username='regular_user', password='secure_password_123')
        
        response = self.client.get('/api/user/profile/')
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data['success'])
        self.assertEqual(data['profile']['id'], str(self.regular_profile.id))
    
    def test_user_cannot_access_other_profiles(self):
        """Test that regular users cannot access other users' profiles"""
        self.client.login(username='regular_user', password='secure_password_123')
        
        # Try to access other user's profile
        response = self.client.get(f'/api/user/profile/{self.other_profile.id}/')
        self.assertEqual(response.status_code, 403)
        data = response.json()
        self.assertFalse(data['success'])
        self.assertEqual(data['error'], 'Access denied')
    
    def test_staff_can_access_other_profiles(self):
        """Test that staff users can access other users' profiles"""
        self.client.login(username='staff_user', password='staff_password_123')
        
        # Staff should be able to access regular user's profile
        response = self.client.get(f'/api/user/profile/{self.regular_profile.id}/')
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data['success'])
        self.assertEqual(data['profile']['id'], str(self.regular_profile.id))
    
    def test_admin_has_full_access(self):
        """Test that admin users have full access to all profiles"""
        self.client.login(username='admin_user', password='admin_password_123')
        
        profiles_to_test = [self.regular_profile, self.staff_profile, self.other_profile]
        
        for profile in profiles_to_test:
            with self.subTest(profile=profile):
                response = self.client.get(f'/api/user/profile/{profile.id}/')
                self.assertEqual(response.status_code, 200)
                data = response.json()
                self.assertTrue(data['success'])
    
    def test_privilege_escalation_prevention(self):
        """Test that users cannot escalate their privileges"""
        self.client.login(username='regular_user', password='secure_password_123')
        
        # Try to access admin endpoints
        admin_endpoints = [
            '/admin/',
            '/api/debug/users/',
            '/api/debug/llm-configs/',
        ]
        
        for endpoint in admin_endpoints:
            with self.subTest(endpoint=endpoint):
                response = self.client.get(endpoint)
                self.assertIn(response.status_code, [302, 403, 404])  # Redirect to login or forbidden


class DataLeakagePreventionTests(SecurityTestCase):
    """Test prevention of data leakage between users"""
    
    def test_user_profile_data_isolation(self):
        """Test that user profile data is properly isolated"""
        # Create demographics for both users
        Demographics.objects.create(
            user_profile=self.regular_profile,
            full_name='Regular User Full Name',
            age=25,
            location='Regular City'
        )
        
        Demographics.objects.create(
            user_profile=self.other_profile,
            full_name='Other User Full Name',
            age=30,
            location='Other City'
        )
        
        # Login as regular user
        self.client.login(username='regular_user', password='secure_password_123')
        
        # Get own profile
        response = self.client.get('/api/user/profile/')
        self.assertEqual(response.status_code, 200)
        data = response.json()
        
        # Verify only own data is returned
        demographics = data['profile']['demographics']
        self.assertEqual(demographics['full_name'], 'Regular User Full Name')
        self.assertEqual(demographics['age'], 25)
        self.assertEqual(demographics['location'], 'Regular City')
        
        # Verify other user's data is not leaked
        self.assertNotEqual(demographics['full_name'], 'Other User Full Name')
    
    def test_activity_catalog_user_isolation(self):
        """Test that activity catalog respects user boundaries"""
        self.client.login(username='regular_user', password='secure_password_123')
        
        response = self.client.get('/api/activities/catalog/')
        self.assertEqual(response.status_code, 200)
        data = response.json()
        
        # Verify response structure
        self.assertTrue(data['success'])
        self.assertIn('activities', data)
        self.assertIn('tailored_count', data)
        self.assertIn('generic_count', data)
    
    def test_websocket_user_isolation(self):
        """Test that WebSocket connections are properly isolated per user"""
        # This would require WebSocket testing framework
        # For now, we'll test the session management aspect
        
        # Login as regular user
        self.client.login(username='regular_user', password='secure_password_123')
        
        # Verify session is created
        verify_response = self.client.post('/api/auth/verify/')
        self.assertEqual(verify_response.status_code, 200)
        user_data = verify_response.json()['user']
        
        # Verify user ID matches the logged-in user
        self.assertEqual(user_data['id'], str(self.regular_profile.id))
        self.assertEqual(user_data['username'], 'regular_user')


class InputValidationSecurityTests(SecurityTestCase):
    """Test input validation and sanitization"""
    
    def test_sql_injection_prevention(self):
        """Test protection against SQL injection attacks"""
        self.client.login(username='regular_user', password='secure_password_123')
        
        # Try SQL injection in search parameters
        malicious_inputs = [
            "'; DROP TABLE auth_user; --",
            "1' OR '1'='1",
            "UNION SELECT * FROM auth_user",
            "<script>alert('xss')</script>",
        ]
        
        for malicious_input in malicious_inputs:
            with self.subTest(input=malicious_input):
                response = self.client.get('/api/activities/catalog/', {
                    'search': malicious_input
                })
                # Should not cause server error
                self.assertNotEqual(response.status_code, 500)
    
    def test_xss_prevention(self):
        """Test protection against XSS attacks"""
        self.client.login(username='regular_user', password='secure_password_123')
        
        xss_payloads = [
            "<script>alert('xss')</script>",
            "javascript:alert('xss')",
            "<img src=x onerror=alert('xss')>",
            "';alert('xss');//",
        ]
        
        for payload in xss_payloads:
            with self.subTest(payload=payload):
                # Test in various input fields
                response = self.client.post('/api/activities/create/', {
                    'name': payload,
                    'description': f'Test activity with {payload}',
                }, content_type='application/json')
                
                # Should either reject or sanitize the input
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success'):
                        # If accepted, verify it's sanitized
                        activity_name = data['activity']['name']
                        self.assertNotIn('<script>', activity_name)
                        self.assertNotIn('javascript:', activity_name)


class SessionSecurityTests(SecurityTestCase):
    """Test session security measures"""
    
    def test_session_timeout(self):
        """Test that sessions timeout appropriately"""
        # Login
        self.client.login(username='regular_user', password='secure_password_123')
        
        # Verify session is active
        response = self.client.post('/api/auth/verify/')
        self.assertEqual(response.status_code, 200)
        
        # In a real test, we would manipulate session expiry
        # For now, verify the session management structure
        self.assertTrue(response.json()['valid'])
    
    def test_concurrent_session_handling(self):
        """Test handling of concurrent sessions"""
        # Create multiple clients to simulate concurrent sessions
        client1 = Client()
        client2 = Client()
        
        # Login with same user from different clients
        login_data = {
            'username': 'regular_user',
            'password': 'secure_password_123'
        }
        
        response1 = client1.post('/api/auth/login/', login_data, content_type='application/json')
        response2 = client2.post('/api/auth/login/', login_data, content_type='application/json')
        
        # Both should succeed (or implement session limiting if required)
        self.assertEqual(response1.status_code, 200)
        self.assertEqual(response2.status_code, 200)


class SecurityHeadersTests(SecurityTestCase):
    """Test security headers and CSRF protection"""
    
    def test_security_headers_present(self):
        """Test that appropriate security headers are present"""
        response = self.client.get('/api/health/')
        
        # Check for security headers (these would be set by middleware/nginx)
        # In development, some headers might not be present
        self.assertEqual(response.status_code, 200)
    
    def test_csrf_protection(self):
        """Test CSRF protection on state-changing operations"""
        # Test without CSRF token
        response = self.client.post('/api/auth/login/', {
            'username': 'regular_user',
            'password': 'secure_password_123'
        })
        
        # CSRF is disabled for API endpoints with @csrf_exempt
        # This is acceptable for API endpoints using other auth methods
        self.assertIn(response.status_code, [200, 403])


class ComprehensiveSecurityIntegrationTests(SecurityTestCase):
    """Integration tests covering complete security scenarios"""
    
    def test_complete_user_journey_security(self):
        """Test security throughout a complete user journey"""
        # 1. Unauthenticated user tries to access protected resource
        response = self.client.get('/api/user/profile/')
        self.assertEqual(response.status_code, 401)
        
        # 2. User logs in
        login_response = self.client.post('/api/auth/login/', {
            'username': 'regular_user',
            'password': 'secure_password_123'
        }, content_type='application/json')
        self.assertEqual(login_response.status_code, 200)
        
        # 3. User accesses their own data
        profile_response = self.client.get('/api/user/profile/')
        self.assertEqual(profile_response.status_code, 200)
        
        # 4. User tries to access other user's data
        other_profile_response = self.client.get(f'/api/user/profile/{self.other_profile.id}/')
        self.assertEqual(other_profile_response.status_code, 403)
        
        # 5. User logs out
        logout_response = self.client.post('/api/auth/logout/')
        self.assertEqual(logout_response.status_code, 200)
        
        # 6. User tries to access protected resource after logout
        post_logout_response = self.client.get('/api/user/profile/')
        self.assertEqual(post_logout_response.status_code, 401)
    
    def test_staff_privilege_boundaries(self):
        """Test that staff privileges are properly bounded"""
        self.client.login(username='staff_user', password='staff_password_123')
        
        # Staff can access user profiles
        response = self.client.get(f'/api/user/profile/{self.regular_profile.id}/')
        self.assertEqual(response.status_code, 200)
        
        # But certain admin functions might still be restricted
        # (This depends on your specific admin interface implementation)
    
    def test_security_under_load(self):
        """Test security measures under simulated load"""
        # Simulate multiple concurrent authentication attempts
        clients = [Client() for _ in range(5)]
        
        for i, client in enumerate(clients):
            response = client.post('/api/auth/login/', {
                'username': 'regular_user',
                'password': 'secure_password_123'
            }, content_type='application/json')
            
            # All should succeed or be rate limited appropriately
            self.assertIn(response.status_code, [200, 429])


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
