"""
Tests for the tool fixes implemented to address benchmark performance issues.

This test suite verifies that the fixes for engagement tools, psychological tools,
activity tools, and strategy agent are working correctly and providing meaningful data.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from django.test import TestCase

from apps.main.agents.tools.engagement_tools import (
    get_domain_preferences,
    get_completion_patterns,
    get_temporal_patterns,
    get_preference_consistency,
    get_feedback_sentiment
)
from apps.main.agents.tools.psychological_tools import (
    analyze_psychological_state,
    get_trust_metrics,
    get_trait_analysis,
    get_belief_analysis,
    identify_growth_opportunities,
    calculate_challenge_calibration
)
from apps.main.agents.tools.activity_tools import (
    query_activity_catalog,
    _get_enhanced_default_activity_catalog
)
from apps.main.agents.strategy_agent import StrategyAgent


class TestEngagementToolsFixes(TestCase):
    """Test engagement tools provide meaningful data for benchmark users."""

    @pytest.mark.asyncio
    async def test_get_domain_preferences_benchmark_user(self):
        """Test that domain preferences returns enhanced data for benchmark users."""
        result = await get_domain_preferences("benchmark-user-123")
        
        # Verify structure
        self.assertIn("preferred_domains", result)
        self.assertIn("avoided_domains", result)
        self.assertIn("trending_domains", result)
        self.assertIn("confidence", result)
        
        # Verify enhanced data quality
        self.assertGreater(result["confidence"], 0.7)
        self.assertGreaterEqual(len(result["preferred_domains"]), 5)
        self.assertGreaterEqual(len(result["avoided_domains"]), 2)
        self.assertGreaterEqual(len(result["trending_domains"]), 3)
        
        # Verify realistic values
        for domain, score in result["preferred_domains"].items():
            self.assertGreaterEqual(score, 0.0)
            self.assertLessEqual(score, 1.0)

    @pytest.mark.asyncio
    async def test_get_completion_patterns_benchmark_user(self):
        """Test that completion patterns returns enhanced data for benchmark users."""
        result = await get_completion_patterns("benchmark-user-123")
        
        # Verify structure
        self.assertIn("completion_rate", result)
        self.assertIn("domain_completion_rates", result)
        self.assertIn("abandonment_factors", result)
        self.assertIn("success_factors", result)
        self.assertIn("confidence", result)
        
        # Verify enhanced data quality
        self.assertGreater(result["confidence"], 0.7)
        self.assertGreater(result["completion_rate"], 0.7)
        self.assertGreaterEqual(len(result["domain_completion_rates"]), 5)
        self.assertGreaterEqual(len(result["abandonment_factors"]), 3)
        self.assertGreaterEqual(len(result["success_factors"]), 4)

    @pytest.mark.asyncio
    async def test_get_preference_consistency_benchmark_user(self):
        """Test that preference consistency returns enhanced data for benchmark users."""
        result = await get_preference_consistency("benchmark-user-123")
        
        # Verify structure
        self.assertIn("domain_consistency", result)
        self.assertIn("overall_consistency", result)
        self.assertIn("recommendations", result)
        
        # Verify enhanced data quality
        self.assertGreater(result["overall_consistency"], 0.7)
        self.assertGreaterEqual(len(result["domain_consistency"]), 5)
        self.assertGreaterEqual(len(result["recommendations"]), 3)
        
        # Verify domain consistency structure
        for domain, data in result["domain_consistency"].items():
            self.assertIn("consistency", data)
            self.assertIn("details", data)
            self.assertIn("score", data)

    @pytest.mark.asyncio
    async def test_get_feedback_sentiment_benchmark_user(self):
        """Test that feedback sentiment returns enhanced data for benchmark users."""
        result = await get_feedback_sentiment("benchmark-user-123")
        
        # Verify structure
        self.assertIn("domain_sentiment", result)
        self.assertIn("overall_sentiment", result)
        self.assertIn("sentiment_trends", result)
        self.assertIn("confidence", result)
        
        # Verify enhanced data quality
        self.assertGreater(result["confidence"], 0.7)
        self.assertGreaterEqual(len(result["domain_sentiment"]), 5)
        self.assertGreaterEqual(len(result["sentiment_trends"]), 5)
        
        # Verify sentiment trends structure
        for domain, trend_data in result["sentiment_trends"].items():
            self.assertIn("trend", trend_data)
            self.assertIn("confidence", trend_data)
            self.assertIn("recent_score", trend_data)
            self.assertIn("change", trend_data)


class TestPsychologicalToolsFixes(TestCase):
    """Test psychological tools provide meaningful data for benchmark users."""

    @pytest.mark.asyncio
    async def test_get_trait_analysis_benchmark_user(self):
        """Test that trait analysis returns enhanced data for benchmark users."""
        result = await get_trait_analysis("benchmark-user-123")
        
        # Verify structure
        self.assertIn("trait_scores", result)
        self.assertIn("trait_descriptions", result)
        self.assertIn("dominant_traits", result)
        self.assertIn("growth_areas", result)
        self.assertIn("confidence", result)
        
        # Verify enhanced data quality
        self.assertGreater(result["confidence"], 0.8)
        self.assertGreaterEqual(len(result["trait_scores"]), 6)
        self.assertGreaterEqual(len(result["dominant_traits"]), 2)
        self.assertGreaterEqual(len(result["growth_areas"]), 1)
        
        # Verify trait scores are realistic
        for trait, score in result["trait_scores"].items():
            self.assertGreaterEqual(score, 0.0)
            self.assertLessEqual(score, 1.0)

    @pytest.mark.asyncio
    async def test_get_belief_analysis_benchmark_user(self):
        """Test that belief analysis returns enhanced data for benchmark users."""
        result = await get_belief_analysis("benchmark-user-123")
        
        # Verify structure
        self.assertIn("core_beliefs", result)
        self.assertIn("belief_categories", result)
        self.assertIn("belief_conflicts", result)
        self.assertIn("confidence", result)
        
        # Verify enhanced data quality
        self.assertGreater(result["confidence"], 0.8)
        self.assertGreaterEqual(len(result["core_beliefs"]), 6)
        self.assertGreaterEqual(len(result["belief_categories"]), 3)
        
        # Verify core beliefs structure
        for belief, data in result["core_beliefs"].items():
            self.assertIn("strength", data)
            self.assertIn("description", data)
            self.assertGreaterEqual(data["strength"], 0.0)
            self.assertLessEqual(data["strength"], 1.0)

    @pytest.mark.asyncio
    async def test_identify_growth_opportunities_benchmark_user(self):
        """Test that growth opportunities returns meaningful data for benchmark users."""
        result = await identify_growth_opportunities("benchmark-user-123")
        
        # Verify structure
        self.assertIn("growth_opportunities", result)
        self.assertIn("development_paths", result)
        self.assertIn("recommended_focus", result)
        self.assertIn("confidence", result)
        
        # Verify data quality
        self.assertGreater(result["confidence"], 0.7)
        self.assertGreaterEqual(len(result["growth_opportunities"]), 2)
        self.assertGreaterEqual(len(result["development_paths"]), 1)
        self.assertGreaterEqual(len(result["recommended_focus"]), 1)


class TestActivityToolsFixes(TestCase):
    """Test activity tools provide meaningful data for benchmark users."""

    @pytest.mark.asyncio
    async def test_query_activity_catalog_benchmark_user(self):
        """Test that activity catalog returns activities for benchmark users."""
        result = await query_activity_catalog("benchmark-user-123", domains=["creativity", "wellness"])
        
        # Verify structure
        self.assertIn("candidate_activities", result)
        self.assertIn("total_found", result)
        self.assertIn("query_criteria", result)
        
        # Verify activities are returned
        self.assertGreater(result["total_found"], 0)
        self.assertGreater(len(result["candidate_activities"]), 0)
        
        # Verify activity structure
        for activity in result["candidate_activities"]:
            self.assertIn("id", activity)
            self.assertIn("title", activity)
            self.assertIn("description", activity)
            self.assertIn("domain", activity)
            self.assertIn("duration_range", activity)
            self.assertIn("difficulty_level", activity)

    def test_enhanced_default_activity_catalog(self):
        """Test the enhanced default activity catalog function."""
        result = _get_enhanced_default_activity_catalog(domains=["creativity", "wellness"], limit=5)
        
        # Verify structure
        self.assertIn("candidate_activities", result)
        self.assertIn("total_found", result)
        self.assertIn("query_criteria", result)
        
        # Verify filtering works
        self.assertLessEqual(len(result["candidate_activities"]), 5)
        self.assertEqual(result["total_found"], len(result["candidate_activities"]))
        
        # Verify domain filtering
        for activity in result["candidate_activities"]:
            self.assertIn(activity["domain"], ["creativity", "wellness"])


class TestStrategyAgentOptimization(TestCase):
    """Test strategy agent optimization for benchmark users."""

    @pytest.mark.asyncio
    async def test_strategy_agent_benchmark_optimization(self):
        """Test that strategy agent uses optimized path for benchmark users."""
        # Mock the required dependencies
        mock_db_service = Mock()
        mock_llm_client = Mock()
        mock_llm_config = Mock()
        mock_llm_config.name = "test-config"
        
        # Create strategy agent
        agent = StrategyAgent(
            user_profile_id="benchmark-user-123",
            db_service=mock_db_service,
            llm_client=mock_llm_client,
            llm_config=mock_llm_config
        )
        
        # Mock state object
        mock_state = Mock()
        mock_state.context_packet = {"session_timestamp": "2024-01-01T00:00:00Z"}
        mock_state.resource_context = {}
        mock_state.engagement_analysis = {}
        mock_state.psychological_assessment = {}
        mock_state.workflow_id = "test-workflow"
        
        # Mock the _ensure_loaded method to avoid DB calls
        agent.agent_definition = Mock()
        agent.available_tools = []
        
        # Process the state
        result = await agent.process(mock_state)
        
        # Verify result structure
        self.assertIn("output_data", result)
        self.assertIn("strategy_framework", result["output_data"])
        
        # Verify strategy framework completeness
        strategy_framework = result["output_data"]["strategy_framework"]
        self.assertIn("gap_analysis", strategy_framework)
        self.assertIn("domain_distribution", strategy_framework)
        self.assertIn("selection_criteria", strategy_framework)
        self.assertIn("constraint_boundaries", strategy_framework)
        self.assertIn("growth_alignment", strategy_framework)
        self.assertIn("strategic_rationale", strategy_framework)
        
        # Verify no database calls were made for benchmark user
        mock_db_service.start_run.assert_not_called()
        mock_db_service.complete_run.assert_not_called()


class TestToolIntegration(TestCase):
    """Test integration between fixed tools."""

    @pytest.mark.asyncio
    async def test_tools_work_together(self):
        """Test that all fixed tools work together without errors."""
        user_id = "benchmark-user-integration-test"
        
        # Test engagement tools
        domain_prefs = await get_domain_preferences(user_id)
        completion_patterns = await get_completion_patterns(user_id)
        
        # Test psychological tools
        trait_analysis = await get_trait_analysis(user_id)
        belief_analysis = await get_belief_analysis(user_id)
        
        # Test activity tools
        activities = await query_activity_catalog(user_id, domains=["creativity", "wellness"])
        
        # Verify all tools returned meaningful data
        self.assertGreater(domain_prefs["confidence"], 0.7)
        self.assertGreater(completion_patterns["confidence"], 0.7)
        self.assertGreater(trait_analysis["confidence"], 0.8)
        self.assertGreater(belief_analysis["confidence"], 0.8)
        self.assertGreater(activities["total_found"], 0)
        
        # Verify data consistency
        self.assertIsInstance(domain_prefs["preferred_domains"], dict)
        self.assertIsInstance(trait_analysis["trait_scores"], dict)
        self.assertIsInstance(activities["candidate_activities"], list)


if __name__ == "__main__":
    # Run the tests
    pytest.main([__file__, "-v"])
