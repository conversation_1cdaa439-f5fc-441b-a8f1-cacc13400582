#!/usr/bin/env python3
"""
<PERSON>ript to properly restart containers and clear Python cache to ensure code changes take effect.
This addresses the critical issue where containers run old cached bytecode.
"""

import subprocess
import time
import sys
import os

def run_command(command, description, check=True):
    """Run a shell command and handle errors."""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, check=check)
        if result.stdout:
            print(f"   Output: {result.stdout.strip()}")
        if result.stderr and check:
            print(f"   Warning: {result.stderr.strip()}")
        print(f"✅ {description} completed")
        return result
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        if e.stdout:
            print(f"   stdout: {e.stdout}")
        if e.stderr:
            print(f"   stderr: {e.stderr}")
        if check:
            raise
        return e

def clear_python_cache():
    """Clear Python bytecode cache from containers."""
    print("\n" + "=" * 60)
    print("CLEARING PYTHON CACHE")
    print("=" * 60)
    
    # Clear cache from web container
    run_command(
        'docker exec backend-web-1 find /usr/src/app -name "*.pyc" -delete',
        "Clearing .pyc files from web container",
        check=False
    )
    
    run_command(
        'docker exec backend-web-1 find /usr/src/app -name "__pycache__" -type d -exec rm -rf {} +',
        "Clearing __pycache__ directories from web container",
        check=False
    )
    
    # Clear cache from celery container
    run_command(
        'docker exec backend-celery-1 find /usr/src/app -name "*.pyc" -delete',
        "Clearing .pyc files from celery container",
        check=False
    )
    
    run_command(
        'docker exec backend-celery-1 find /usr/src/app -name "__pycache__" -type d -exec rm -rf {} +',
        "Clearing __pycache__ directories from celery container",
        check=False
    )

def restart_containers():
    """Restart the backend containers."""
    print("\n" + "=" * 60)
    print("RESTARTING CONTAINERS")
    print("=" * 60)
    
    # Restart containers
    run_command(
        "cd backend && docker compose up web celery -d --build",
        "Restarting backend containers"
    )

def main():
    """Main function to restart containers and verify fixes."""
    print("🚀 CONTAINER RESTART AND CACHE CLEAR PROCEDURE")
    print("=" * 60)
    print("This script will:")
    print("1. Clear Python bytecode cache from containers")
    print("2. Restart backend containers")
    print("3. Wait for containers to fully initialize")
    print("4. Verify containers are running and responsive")
    print("5. Verify code changes are present in containers")
    print("6. Run comprehensive tests to verify fixes")
    print("=" * 60)
    
    try:
        # Step 1: Clear Python cache
        clear_python_cache()
        
        # Step 2: Restart containers
        restart_containers()
        
        
        print("\n🎉 CONTAINER RESTART PROCEDURE COMPLETED SUCCESSFULLY!")
        
    except Exception as e:
        print(f"\n❌ CONTAINER RESTART PROCEDURE FAILED: {e}")
        print("Please check the error messages above and try again.")
        sys.exit(1)

if __name__ == "__main__":
    main()
