#!/bin/bash

# Production startup script for Django + Celery
# This script starts both <PERSON><PERSON> (Django) and Celery worker in the same container

set -e

echo "🚀 Starting production services..."

# Start Celery worker in the background
echo "📋 Starting Celery worker..."
celery -A config worker --loglevel=info --concurrency=2 --detach --pidfile=/tmp/celery.pid --logfile=/tmp/celery.log

# Give <PERSON><PERSON>y a moment to start
sleep 3

# Check if Celery started successfully
if [ -f /tmp/celery.pid ]; then
    echo "✅ Celery worker started successfully (PID: $(cat /tmp/celery.pid))"
else
    echo "❌ Failed to start Celery worker"
    exit 1
fi

# Start Gunicorn (Django) in the foreground
echo "🌐 Starting Django with <PERSON><PERSON>..."
exec gunicorn --worker-tmp-dir /dev/shm --bind 0.0.0.0:8080 config.wsgi:application
