<!-- Profile Repair Modal -->
<div class="modal fade" id="profileRepairModal" tabindex="-1" aria-labelledby="profileRepairModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="profileRepairModalLabel">
                    <i class="fas fa-tools me-2"></i>Profile Repair Assistant
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            
            <div class="modal-body">
                <!-- Error Summary -->
                <div id="errorSummary" class="alert alert-info mb-4">
                    <h6><i class="fas fa-info-circle me-2"></i>Analysis Summary</h6>
                    <div id="summaryText">Analyzing profile data...</div>
                    <div class="mt-2">
                        <span class="badge bg-danger me-2" id="criticalCount">0 Critical</span>
                        <span class="badge bg-warning me-2" id="highCount">0 High</span>
                        <span class="badge bg-info me-2" id="mediumCount">0 Medium</span>
                        <span class="badge bg-success me-2" id="autoFixableCount">0 Auto-fixable</span>
                    </div>
                </div>

                <!-- Architectural Issues Section -->
                <div id="architecturalIssues" class="card mb-4" style="display: none;">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>Architectural Issues Detected
                        </h6>
                        <small>These issues require system-level fixes and may need developer attention</small>
                    </div>
                    <div class="card-body">
                        <div id="architecturalIssuesList">
                            <!-- Architectural issues will be populated here -->
                        </div>
                        <div class="alert alert-warning mt-3">
                            <strong>⚠️ Important:</strong> Architectural issues indicate mismatches between the business object definitions
                            and database models. These typically require code changes to fix properly.
                        </div>
                    </div>
                </div>

                <!-- Auto-Repair Section -->
                <div id="autoRepairSection" class="card mb-4" style="display: none;">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0"><i class="fas fa-magic me-2"></i>Automatic Repairs Available</h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-3">The following issues can be automatically repaired:</p>
                        <ul id="autoRepairList" class="list-group list-group-flush mb-3"></ul>
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-success" id="applyAutoRepairsBtn">
                                <i class="fas fa-magic me-2"></i>Apply Auto-Repairs
                            </button>
                            <button type="button" class="btn btn-outline-secondary" id="previewRepairsBtn">
                                <i class="fas fa-eye me-2"></i>Preview Changes
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Manual Repair Section -->
                <div id="manualRepairSection" class="card mb-4">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="mb-0"><i class="fas fa-edit me-2"></i>Manual Repairs Required</h6>
                    </div>
                    <div class="card-body">
                        <div id="manualRepairList">
                            <!-- Manual repair items will be populated here -->
                        </div>
                    </div>
                </div>

                <!-- Profile Editor -->
                <div id="profileEditor" class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-code me-2"></i>Profile Data Editor</h6>
                        <small class="text-muted">Edit the profile data directly (JSON format)</small>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="btn-toolbar mb-2" role="toolbar">
                                <div class="btn-group me-2" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-primary" id="formatJsonBtn">
                                        <i class="fas fa-indent me-1"></i>Format
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" id="validateJsonBtn">
                                        <i class="fas fa-check me-1"></i>Validate
                                    </button>
                                </div>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-info" id="resetJsonBtn">
                                        <i class="fas fa-undo me-1"></i>Reset
                                    </button>
                                </div>
                            </div>
                            <textarea id="profileDataEditor" class="form-control font-monospace" rows="20" 
                                      placeholder="Profile JSON data will appear here..."></textarea>
                        </div>
                        <div id="editorValidation" class="alert" style="display: none;"></div>
                    </div>
                </div>

                <!-- Next Steps -->
                <div id="nextStepsSection" class="card mt-4">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0"><i class="fas fa-list-ol me-2"></i>Next Steps</h6>
                    </div>
                    <div class="card-body">
                        <ol id="nextStepsList" class="mb-0">
                            <!-- Next steps will be populated here -->
                        </ol>
                    </div>
                </div>
            </div>
            
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="revalidateBtn">
                    <i class="fas fa-sync me-2"></i>Re-validate
                </button>
                <button type="button" class="btn btn-success" id="applyRepairsBtn">
                    <i class="fas fa-save me-2"></i>Apply Repairs & Import
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Repair Preview Modal -->
<div class="modal fade" id="repairPreviewModal" tabindex="-1" aria-labelledby="repairPreviewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="repairPreviewModalLabel">
                    <i class="fas fa-eye me-2"></i>Repair Preview
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="repairPreviewContent">
                    <!-- Preview content will be populated here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" id="confirmRepairsBtn">
                    <i class="fas fa-check me-2"></i>Confirm Repairs
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.error-item {
    border-left: 4px solid #dc3545;
    padding: 12px;
    margin-bottom: 12px;
    background-color: #f8f9fa;
    border-radius: 0 4px 4px 0;
}

.error-item.warning {
    border-left-color: #ffc107;
}

.error-item.info {
    border-left-color: #17a2b8;
}

.error-item.success {
    border-left-color: #28a745;
}

.error-field-path {
    font-family: 'Courier New', monospace;
    background-color: #e9ecef;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.9em;
}

.suggestion-item {
    background-color: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 4px;
    padding: 8px 12px;
    margin: 4px 0;
}

.suggestion-item.auto-fix {
    background-color: #e8f5e8;
    border-color: #b3e5b3;
}

.confidence-indicator {
    display: inline-block;
    width: 60px;
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.confidence-bar {
    height: 100%;
    transition: width 0.3s ease;
}

.confidence-high { background-color: #28a745; }
.confidence-medium { background-color: #ffc107; }
.confidence-low { background-color: #dc3545; }

.repair-diff {
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
}

.diff-removed {
    background-color: #ffeaea;
    color: #d73a49;
    text-decoration: line-through;
}

.diff-added {
    background-color: #e6ffed;
    color: #28a745;
}

#profileDataEditor {
    font-size: 0.9em;
    line-height: 1.4;
}

.json-error {
    background-color: #fff5f5;
    border-color: #fed7d7;
    color: #c53030;
}

.json-valid {
    background-color: #f0fff4;
    border-color: #9ae6b4;
    color: #2f855a;
}
</style>
