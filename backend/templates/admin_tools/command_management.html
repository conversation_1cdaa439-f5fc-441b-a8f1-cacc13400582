{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}{{ page_title }} - {{ block.super }}{% endblock %}

{% block extrahead %}
{{ block.super }}
{{ media }}

{% endblock %}

{% block content %}
<div class="catalog-management-container">
    <!-- Header Section -->
    <div class="catalog-header">
        <h1>{{ page_title }}</h1>
        <p>Comprehensive catalog management with rich visualization and enhanced seeding capabilities</p>

        <div class="catalog-stats">
            <div class="stat-item">
                <div class="stat-number">{{ total_commands }}</div>
                <div class="stat-label">Commands</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{{ valid_catalogs }}</div>
                <div class="stat-label">Valid Catalogs</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{{ total_catalogs }}</div>
                <div class="stat-label">Total Catalogs</div>
            </div>
        </div>
    </div>

    <!-- Catalog Status Overview -->
    <div class="catalog-status-overview">
        <h2>📊 Catalog Status Overview</h2>
        <p>{{ valid_catalogs }}/{{ total_catalogs }} catalogs are valid and ready for use</p>

        <div class="catalog-grid">
            {% for catalog_name, status in catalog_status.items %}
            <div class="catalog-card {% if status.valid %}valid{% else %}invalid{% endif %}" data-catalog="{{ catalog_name }}">
                <div class="catalog-card-header">
                    <div class="catalog-title">{{ catalog_name|title }}</div>
                    <span class="catalog-status {% if status.valid %}valid{% else %}invalid{% endif %}" data-catalog-status="{{ catalog_name }}">
                        {% if status.valid %}✓ Valid{% else %}✗ Invalid{% endif %}
                    </span>
                </div>

                <div class="catalog-metadata">
                    <div class="metadata-item">
                        <span class="metadata-label">Last Checked:</span>
                        <span class="metadata-value">{{ status.last_checked|date:"M d, H:i" }}</span>
                    </div>
                </div>

                {% if not status.valid and status.errors %}
                <div class="catalog-errors">
                    <strong>Validation Errors:</strong>
                    <ul>
                        {% for error in status.errors %}
                        <li>{{ error }}</li>
                        {% endfor %}
                    </ul>
                </div>
                {% endif %}

                <div class="catalog-actions">
                    <button class="btn btn-sm btn-outline-primary validate-catalog-btn" data-catalog="{{ catalog_name }}">
                        🔄 Revalidate
                    </button>
                    <button class="btn btn-sm btn-primary view-catalog-btn" data-catalog="{{ catalog_name }}">
                        👁️ View Catalog
                    </button>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Available Commands -->
    <div class="commands-section">
        <h2>🚀 Available Commands</h2>
        <p>Execute catalog management and seeding commands with enhanced capabilities</p>

        <div class="commands-grid">
            {% for command_id, command_info in available_commands.items %}
            <div class="command-card {{ command_info.risk_level }}-risk" data-command="{{ command_id }}">
                <div class="command-header">
                    <div class="command-info">
                        <h3>{{ command_info.name }}</h3>
                        <div class="command-category">{{ command_info.category }}</div>
                        <div class="command-description">{{ command_info.description }}</div>

                        <div class="command-meta">
                            <span><strong>⏱️ Time:</strong> {{ command_info.estimated_time }}</span>
                            <span><strong>⚠️ Risk:</strong>
                                <span class="risk-badge {{ command_info.risk_level }}">{{ command_info.risk_level|title }}</span>
                            </span>
                            {% if command_info.supports_bypass %}
                            <span><strong>🔓 Bypass:</strong> Supported</span>
                            {% endif %}
                            {% if command_info.supports_external_json %}
                            <span><strong>📁 External JSON:</strong> Supported</span>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Enhanced Parameters Section -->
                {% if command_info.parameters %}
                <div class="parameters-section">
                    <h4>⚙️ Parameters</h4>
                    <div class="parameter-group">
                        {% for param in command_info.parameters %}
                        <div class="parameter-input {% if param.type == 'checkbox' %}checkbox{% elif param.type == 'file' %}file{% endif %}">
                            <label for="{{ command_id }}_{{ param.name }}">
                                {{ param.name|title }}
                                {% if param.required %}<span style="color: #dc3545;">*</span>{% endif %}
                            </label>

                            {% if param.type == 'checkbox' %}
                                <input type="checkbox"
                                       id="{{ command_id }}_{{ param.name }}"
                                       data-command-param="{{ command_id }}"
                                       data-param-name="{{ param.name }}"
                                       class="bypass-checkbox"
                                       {% if param.default %}checked{% endif %}>
                                <span class="help-text">{{ param.description }}</span>
                            {% elif param.type == 'file' %}
                                <div class="file-upload-area">
                                    <input type="file"
                                           id="{{ command_id }}_{{ param.name }}"
                                           data-command-param="{{ command_id }}"
                                           data-param-name="{{ param.name }}"
                                           class="external-json-file"
                                           accept="{{ param.accept|default:'.json' }}"
                                           style="display: none;">
                                    <div class="upload-text">Click or drag JSON file here</div>
                                    <div class="help-text">{{ param.description }}</div>
                                </div>
                            {% else %}
                                <input type="{{ param.type }}"
                                       id="{{ command_id }}_{{ param.name }}"
                                       data-command-param="{{ command_id }}"
                                       data-param-name="{{ param.name }}"
                                       value="{{ param.default }}"
                                       {% if param.required %}required{% endif %}
                                       placeholder="{{ param.description }}">
                                <div class="help-text">{{ param.description }}</div>
                            {% endif %}
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

                <!-- Command Actions -->
                <div class="command-actions">
                    <button class="execute-btn"
                            data-command="{{ command_id }}"
                            id="btn_{{ command_id }}">
                        🚀 Execute Command
                    </button>
                </div>

                <!-- Command Output -->
                <div id="output_{{ command_id }}" class="command-output" style="display: none;"></div>
                <div id="info_{{ command_id }}" class="execution-info" style="display: none;"></div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<!-- The JavaScript functionality is now handled by the external catalog_management.js file -->
<script>
// Initialize catalog management when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // The CatalogManagement class is automatically initialized by the external JS file
    console.log('Enhanced Catalog Management interface loaded');
});
</script>
{% endblock %}
