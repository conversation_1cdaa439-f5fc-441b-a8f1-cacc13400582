<!-- Inventory Detail Modal -->
<div class="modal fade" id="inventory-detail-modal" tabindex="-1" aria-labelledby="inventory-modal-title" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="inventory-modal-title">📦 Inventory Details</h5>
                <div class="modal-actions">
                    <button id="refresh-inventory-btn" class="btn btn-dark btn-sm me-2" title="Refresh inventory data">
                        🔄 Refresh
                    </button>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
            </div>
            <div class="modal-body p-0">
                <div id="inventory-modal-body" class="inventory-modal-body">
                    <!-- Inventory content will be loaded here -->
                    <div class="modal-loading text-center p-5">
                        <div class="spinner-border text-warning" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-3">Loading inventory details...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Inventory Modal Custom Styles */
.inventory-modal-body {
    max-height: 75vh;
    overflow-y: auto;
    padding: 0;
}

#inventory-detail-modal {
    z-index: 9999 !important;
}

.inventory-modal-body .modal-header {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
}

.inventory-section {
    border-bottom: 1px solid #e9ecef;
    padding: 25px;
    position: relative;
}

.inventory-section:last-child {
    border-bottom: none;
}

.inventory-section::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    border-radius: 2px;
}

.inventory-section h3 {
    margin: 0 0 20px 0;
    color: #495057;
    font-size: 1.4em;
    display: flex;
    align-items: center;
    gap: 10px;
}

.inventory-section-icon {
    font-size: 1.2em;
}

.inventory-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

/* Resource cards */
.resource-card {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.resource-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.resource-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.resource-name {
    font-size: 1.1em;
    font-weight: 600;
    color: #495057;
    margin: 0;
}

.resource-type {
    font-size: 0.9em;
    color: #6c757d;
    margin: 5px 0 0 0;
}

.resource-status {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 5px;
}

.resource-badge {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
}

.resource-badge.available {
    background: #d4edda;
    color: #155724;
}

.resource-badge.unavailable {
    background: #f8d7da;
    color: #721c24;
}

.resource-badge.condition-excellent {
    background: #d4edda;
    color: #155724;
}

.resource-badge.condition-good {
    background: #d1ecf1;
    color: #0c5460;
}

.resource-badge.condition-fair {
    background: #fff3cd;
    color: #856404;
}

.resource-badge.condition-poor {
    background: #f8d7da;
    color: #721c24;
}

.resource-badge.condition-broken {
    background: #f5c6cb;
    color: #721c24;
}

.resource-badge.transport-pocket {
    background: #e7f3ff;
    color: #0066cc;
}

.resource-badge.transport-portable {
    background: #d1ecf1;
    color: #0c5460;
}

.resource-badge.transport-heavy {
    background: #fff3cd;
    color: #856404;
}

.resource-badge.transport-fixed {
    background: #f8d7da;
    color: #721c24;
}

.resource-details {
    margin-top: 15px;
}

.resource-detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f8f9fa;
}

.resource-detail-row:last-child {
    border-bottom: none;
}

.resource-detail-label {
    font-weight: 600;
    color: #6c757d;
    font-size: 0.9em;
}

.resource-detail-value {
    color: #495057;
    font-size: 0.9em;
    text-align: right;
    max-width: 60%;
}

/* Inventory stats */
.inventory-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.inventory-stat-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
}

.inventory-stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #ffc107;
    display: block;
}

.inventory-stat-label {
    font-size: 12px;
    color: #6c757d;
    margin-top: 5px;
}

/* Filter controls */
.inventory-filters {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.filter-row {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filter-group label {
    font-size: 12px;
    font-weight: 600;
    color: #6c757d;
}

.filter-group select {
    padding: 5px 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 12px;
}

/* Empty state */
.inventory-empty {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.inventory-empty-icon {
    font-size: 4em;
    margin-bottom: 20px;
    opacity: 0.5;
}

/* Responsive design */
@media (max-width: 768px) {
    .inventory-grid {
        grid-template-columns: 1fr;
    }
    
    .resource-card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .resource-status {
        align-items: flex-start;
        flex-direction: row;
        flex-wrap: wrap;
    }
    
    .filter-row {
        flex-direction: column;
        align-items: stretch;
    }
    
    .inventory-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>

<script>
/**
 * Inventory Detail Modal Management
 */
let inventoryModalInstance = null;

window.openInventoryDetailModal = function() {
    const modalElement = document.getElementById('inventory-detail-modal');
    const modalBody = document.getElementById('inventory-modal-body');
    const title = document.getElementById('inventory-modal-title');

    // Initialize Bootstrap modal instance if it doesn't exist
    if (!inventoryModalInstance) {
        inventoryModalInstance = new bootstrap.Modal(modalElement);
    }

    // Show modal
    inventoryModalInstance.show();

    // Set title
    title.textContent = '📦 Inventory & Resources';

    // Show loading state
    modalBody.innerHTML = `
        <div class="modal-loading text-center p-5">
            <div class="spinner-border text-warning" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3">Loading inventory details...</p>
        </div>
    `;

    // Load inventory data
    loadInventoryData();
};

async function loadInventoryData() {
    try {
        // Get the profile ID from the current context
        const profileId = getCurrentProfileId();
        if (!profileId) {
            throw new Error('Profile ID not available');
        }

        // Build API URL
        const apiUrl = `/admin/user-profiles/api/${profileId}/inventory/`;

        // Fetch inventory data from API
        const response = await fetch(apiUrl, {
            method: 'GET',
            headers: {
                'X-CSRFToken': getCsrfToken(),
                'Content-Type': 'application/json',
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const inventoryData = await response.json();
        renderInventoryData(inventoryData);

    } catch (error) {
        console.error('Error loading inventory data:', error);
        document.getElementById('inventory-modal-body').innerHTML = `
            <div class="profile-error">
                <h4>⚠️ Error Loading Inventory</h4>
                <p>${error.message}</p>
                <button onclick="loadInventoryData()" class="btn btn-warning">
                    🔄 Retry
                </button>
            </div>
        `;
    }
}

function getCurrentProfileId() {
    // Try to get profile ID from various sources
    const modal = document.getElementById('user-profile-detail-modal');
    if (modal && modal.getAttribute('data-profile-id')) {
        return modal.getAttribute('data-profile-id');
    }

    // Try to get from URL or other context
    const urlParams = new URLSearchParams(window.location.search);
    const profileId = urlParams.get('profile_id');
    if (profileId) {
        return profileId;
    }

    // Try to get from the currently opened profile modal
    const profileModalBody = document.getElementById('profile-modal-body');
    if (profileModalBody && profileModalBody.dataset.profileId) {
        return profileModalBody.dataset.profileId;
    }

    return null;
}

function getCsrfToken() {
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
    return csrfToken ? csrfToken.value : '';
}

function renderInventoryData(data) {
    const modalBody = document.getElementById('inventory-modal-body');

    if (!data.inventories || data.inventories.length === 0) {
        modalBody.innerHTML = `
            <div class="inventory-section">
                <h3><span class="inventory-section-icon">📦</span>No Inventories Found</h3>
                <p>This user profile has no inventories configured.</p>
            </div>
        `;
        return;
    }

    modalBody.innerHTML = `
        <!-- Inventory Overview Section -->
        <div class="inventory-section">
            <h3><span class="inventory-section-icon">📦</span>Inventory System Overview</h3>
            <div class="inventory-stats">
                <div class="inventory-stat-card">
                    <span class="inventory-stat-number">${data.statistics.total_inventories}</span>
                    <div class="inventory-stat-label">Total Inventories</div>
                </div>
                <div class="inventory-stat-card">
                    <span class="inventory-stat-number">${data.statistics.total_resources}</span>
                    <div class="inventory-stat-label">Total Resources</div>
                </div>
                <div class="inventory-stat-card">
                    <span class="inventory-stat-number">${data.statistics.mobile_inventories}</span>
                    <div class="inventory-stat-label">Mobile</div>
                </div>
                <div class="inventory-stat-card">
                    <span class="inventory-stat-number">${data.statistics.resource_categories}</span>
                    <div class="inventory-stat-label">Categories</div>
                </div>
            </div>
        </div>

        <!-- Inventories List -->
        <div class="inventory-section">
            <h3><span class="inventory-section-icon">📋</span>Inventories</h3>
            <div class="inventories-grid">
                ${data.inventories.map(inventory => `
                    <div class="inventory-card ${inventory.is_mobile ? 'mobile-inventory' : ''} ${inventory.is_temporary ? 'temporary-inventory' : ''}">
                        <div class="inventory-card-header">
                            <h4>${inventory.inventory_name}</h4>
                            <div class="inventory-badges">
                                ${inventory.is_mobile ? '<span class="inventory-badge mobile">📱 Mobile</span>' : ''}
                                ${inventory.is_temporary ? '<span class="inventory-badge temporary">⏰ Temporary</span>' : ''}
                                <span class="inventory-badge type">${inventory.inventory_type}</span>
                            </div>
                        </div>
                        <div class="inventory-card-body">
                            <p>${inventory.inventory_description || 'No description'}</p>
                            ${inventory.location_details ? `<small><strong>Location:</strong> ${inventory.location_details}</small>` : ''}
                            <div class="inventory-stats">
                                <span class="stat-badge">📦 ${inventory.resources.length} resources</span>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>

        <!-- Resources by Category -->
        <div class="inventory-section">
            <h3><span class="inventory-section-icon">🏷️</span>Resources by Category</h3>
            <div class="resources-categories">
                ${Object.entries(data.resources_by_category).map(([category, resources]) => `
                    <div class="resource-category">
                        <h4 class="category-header">
                            <span class="category-icon">${getCategoryIcon(category)}</span>
                            ${category} (${resources.length})
                        </h4>
                        <div class="resources-list">
                            ${resources.map(resource => `
                                <div class="resource-item">
                                    <div class="resource-info">
                                        <strong>${resource.resource_name}</strong>
                                        <p>${resource.resource_description || 'No description'}</p>
                                        ${resource.generic_resource ? `<small>Type: ${resource.generic_resource.name}</small>` : ''}
                                    </div>
                                    <div class="resource-status">
                                        <span class="status-badge availability-${resource.availability.toLowerCase()}">${resource.availability}</span>
                                        <span class="status-badge condition-${resource.condition.toLowerCase()}">${resource.condition}</span>
                                        ${resource.transportability ? `<span class="status-badge transportable">🚚 ${resource.transportability}</span>` : ''}
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>

        <!-- Inventory Filters -->
        <div class="inventory-section">
            <h3><span class="inventory-section-icon">🔍</span>Resource Filters</h3>
            <div class="inventory-filters">
                <div class="filter-row">
                    <div class="filter-group">
                        <label>Inventory Type</label>
                        <select id="inventory-type-filter">
                            <option value="">All Inventories</option>
                            <option value="mobile">Mobile Inventory</option>
                            <option value="environment">Environment-Specific</option>
                            <option value="temporary">Temporary</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>Availability</label>
                        <select id="availability-filter">
                            <option value="">All Resources</option>
                            <option value="available">Available</option>
                            <option value="unavailable">Unavailable</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>Condition</label>
                        <select id="condition-filter">
                            <option value="">All Conditions</option>
                            <option value="excellent">Excellent</option>
                            <option value="good">Good</option>
                            <option value="fair">Fair</option>
                            <option value="poor">Poor</option>
                            <option value="broken">Broken</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>Transportability</label>
                        <select id="transportability-filter">
                            <option value="">All Types</option>
                            <option value="pocket">Pocket</option>
                            <option value="portable">Portable</option>
                            <option value="heavy">Heavy</option>
                            <option value="fixed">Fixed</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mobile Inventory Section -->
        <div class="inventory-section">
            <h3><span class="inventory-section-icon">🎒</span>Mobile Inventory</h3>
            <p class="text-muted">Resources you can take anywhere - portable and pocket-sized items</p>

            <div class="resource-card">
                <div class="resource-card-header">
                    <div>
                        <h5 class="resource-name">Smartphone</h5>
                        <p class="resource-type">Electronics / Communication</p>
                    </div>
                    <div class="resource-status">
                        <span class="resource-badge available">Available</span>
                        <span class="resource-badge condition-excellent">Excellent</span>
                        <span class="resource-badge transport-pocket">Pocket</span>
                    </div>
                </div>
                <div class="resource-details">
                    <div class="resource-detail-row">
                        <span class="resource-detail-label">Location</span>
                        <span class="resource-detail-value">Always with me</span>
                    </div>
                    <div class="resource-detail-row">
                        <span class="resource-detail-label">Ownership</span>
                        <span class="resource-detail-value">Personal device</span>
                    </div>
                    <div class="resource-detail-row">
                        <span class="resource-detail-label">Notes</span>
                        <span class="resource-detail-value">Primary communication device</span>
                    </div>
                </div>
            </div>

            <div class="resource-card">
                <div class="resource-card-header">
                    <div>
                        <h5 class="resource-name">Laptop</h5>
                        <p class="resource-type">Electronics / Computing</p>
                    </div>
                    <div class="resource-status">
                        <span class="resource-badge available">Available</span>
                        <span class="resource-badge condition-good">Good</span>
                        <span class="resource-badge transport-portable">Portable</span>
                    </div>
                </div>
                <div class="resource-details">
                    <div class="resource-detail-row">
                        <span class="resource-detail-label">Location</span>
                        <span class="resource-detail-value">Laptop bag</span>
                    </div>
                    <div class="resource-detail-row">
                        <span class="resource-detail-label">Ownership</span>
                        <span class="resource-detail-value">Work laptop</span>
                    </div>
                    <div class="resource-detail-row">
                        <span class="resource-detail-label">Notes</span>
                        <span class="resource-detail-value">For work and personal projects</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Environment-Specific Inventories -->
        <div class="inventory-section">
            <h3><span class="inventory-section-icon">🏠</span>Home Workshop Inventory</h3>
            <p class="text-muted">Resources available at home workshop environment</p>

            <div class="resource-card">
                <div class="resource-card-header">
                    <div>
                        <h5 class="resource-name">3D Printer</h5>
                        <p class="resource-type">Tools / Manufacturing</p>
                    </div>
                    <div class="resource-status">
                        <span class="resource-badge available">Available</span>
                        <span class="resource-badge condition-good">Good</span>
                        <span class="resource-badge transport-fixed">Fixed</span>
                    </div>
                </div>
                <div class="resource-details">
                    <div class="resource-detail-row">
                        <span class="resource-detail-label">Location</span>
                        <span class="resource-detail-value">Workshop desk, corner</span>
                    </div>
                    <div class="resource-detail-row">
                        <span class="resource-detail-label">Ownership</span>
                        <span class="resource-detail-value">Personal equipment</span>
                    </div>
                    <div class="resource-detail-row">
                        <span class="resource-detail-label">Notes</span>
                        <span class="resource-detail-value">Requires filament and maintenance</span>
                    </div>
                </div>
            </div>

            <div class="resource-card">
                <div class="resource-card-header">
                    <div>
                        <h5 class="resource-name">Drill Set</h5>
                        <p class="resource-type">Tools / Hand Tools</p>
                    </div>
                    <div class="resource-status">
                        <span class="resource-badge unavailable">Unavailable</span>
                        <span class="resource-badge condition-fair">Fair</span>
                        <span class="resource-badge transport-portable">Portable</span>
                    </div>
                </div>
                <div class="resource-details">
                    <div class="resource-detail-row">
                        <span class="resource-detail-label">Location</span>
                        <span class="resource-detail-value">Lent to neighbor</span>
                    </div>
                    <div class="resource-detail-row">
                        <span class="resource-detail-label">Ownership</span>
                        <span class="resource-detail-value">Personal tools</span>
                    </div>
                    <div class="resource-detail-row">
                        <span class="resource-detail-label">Notes</span>
                        <span class="resource-detail-value">Should be returned next week</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Temporary Inventory -->
        <div class="inventory-section">
            <h3><span class="inventory-section-icon">⏰</span>Temporary Resources</h3>
            <p class="text-muted">Time-limited access resources (borrowed, rented, or temporary)</p>

            <div class="resource-card">
                <div class="resource-card-header">
                    <div>
                        <h5 class="resource-name">Library Books</h5>
                        <p class="resource-type">Educational / Books</p>
                    </div>
                    <div class="resource-status">
                        <span class="resource-badge available">Available</span>
                        <span class="resource-badge condition-good">Good</span>
                        <span class="resource-badge transport-portable">Portable</span>
                    </div>
                </div>
                <div class="resource-details">
                    <div class="resource-detail-row">
                        <span class="resource-detail-label">Location</span>
                        <span class="resource-detail-value">Home office shelf</span>
                    </div>
                    <div class="resource-detail-row">
                        <span class="resource-detail-label">Ownership</span>
                        <span class="resource-detail-value">Borrowed from library</span>
                    </div>
                    <div class="resource-detail-row">
                        <span class="resource-detail-label">Valid Until</span>
                        <span class="resource-detail-value">July 15, 2025</span>
                    </div>
                    <div class="resource-detail-row">
                        <span class="resource-detail-label">Notes</span>
                        <span class="resource-detail-value">Due back in 2 weeks</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Resource Management Actions -->
        <div class="inventory-section">
            <h3><span class="inventory-section-icon">⚙️</span>Inventory Management</h3>
            <div class="inventory-grid">
                <div class="inventory-field">
                    <button class="btn btn-primary">📝 Add New Resource</button>
                </div>
                <div class="inventory-field">
                    <button class="btn btn-secondary">🔄 Update Availability</button>
                </div>
                <div class="inventory-field">
                    <button class="btn btn-warning">🔧 Mark for Maintenance</button>
                </div>
                <div class="inventory-field">
                    <button class="btn btn-info">📊 Generate Report</button>
                </div>
            </div>
        </div>
    `;
}

// Modal initialization
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('inventory-detail-modal');

    modal.addEventListener('hidden.bs.modal', function () {
        // Clear modal content when closed
        const modalBody = document.getElementById('inventory-modal-body');
        modalBody.innerHTML = `
            <div class="modal-loading text-center p-5">
                <div class="spinner-border text-warning" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-3">Loading inventory details...</p>
            </div>
        `;
    });
});

// Helper function for category icons
function getCategoryIcon(category) {
    const icons = {
        'Tools': '🔧',
        'Equipment': '⚙️',
        'Materials': '📦',
        'Electronics': '💻',
        'Books': '📚',
        'Furniture': '🪑',
        'Clothing': '👕',
        'Food': '🍎',
        'Transportation': '🚗',
        'Sports': '⚽',
        'Art': '🎨',
        'Music': '🎵',
        'Other': '📋'
    };
    return icons[category] || '📋';
}
</script>
