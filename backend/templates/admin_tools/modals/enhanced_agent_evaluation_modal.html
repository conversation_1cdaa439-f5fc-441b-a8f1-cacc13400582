<!-- Enhanced Agent Evaluation Modal with Modular Components -->
<div id="enhanced-agent-details-modal" class="modal">
    <div class="modal-content enhanced-agent-evaluation-modal">
        <span class="close">&times;</span>
        <div class="modal-header">
            <h2>🤖 Enhanced Agent Evaluation</h2>
            <p class="modal-description">
                Modular, component-based analysis with specialized views for each agent type.
            </p>
            <div class="modal-actions">
                <button id="copy-enhanced-run-data-btn" class="btn btn-secondary" title="Copy full run data as JSON">
                    📋 Copy Data
                </button>
                <button id="refresh-enhanced-modal-btn" class="btn btn-secondary" title="Refresh modal data">
                    🔄 Refresh
                </button>
                <button id="switch-to-legacy-btn" class="btn btn-secondary" title="Switch to legacy modal">
                    🔄 Legacy View
                </button>
            </div>
        </div>
        <div id="enhanced-agent-modal-body" class="enhanced-agent-modal-body">
            <!-- Modular components will be loaded here -->
            <div class="modal-loading">
                <div class="loader"></div>
                <p>Loading enhanced agent evaluation...</p>
            </div>
        </div>
    </div>
</div>

<style>
/* Enhanced Agent Evaluation Modal Styles */
.enhanced-agent-evaluation-modal .modal-content {
    max-width: 1400px;
    width: 95%;
    max-height: 90vh;
}

.enhanced-agent-modal-body {
    max-height: 75vh;
    overflow-y: auto;
    padding: 0;
}

/* Component container styles */
.components-container {
    display: flex;
    flex-direction: column;
    gap: 0;
}

.component-section {
    border-bottom: 1px solid #e9ecef;
}

.component-section:last-child {
    border-bottom: none;
}

/* Agent type indicator */
.agent-type-indicator {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 25px;
    margin: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.agent-type-badge {
    background: rgba(255,255,255,0.2);
    padding: 6px 16px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9em;
    backdrop-filter: blur(10px);
}

.agent-metadata {
    display: flex;
    gap: 20px;
    font-size: 0.9em;
    opacity: 0.9;
}

/* Component loading states */
.component-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #6c757d;
    background: #f8f9fa;
}

.component-error {
    padding: 20px;
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    margin: 10px;
    border-radius: 6px;
}

/* Responsive design */
@media (max-width: 1200px) {
    .enhanced-agent-evaluation-modal .modal-content {
        width: 98%;
        max-width: none;
    }
}

@media (max-width: 768px) {
    .enhanced-agent-modal-body {
        max-height: 70vh;
    }
    
    .agent-type-indicator {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .agent-metadata {
        flex-direction: column;
        gap: 5px;
    }
}

/* Enhanced scrollbar */
.enhanced-agent-modal-body::-webkit-scrollbar {
    width: 8px;
}

.enhanced-agent-modal-body::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.enhanced-agent-modal-body::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.enhanced-agent-modal-body::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Component transition animations */
.component-section {
    transition: all 0.3s ease;
}

.component-section.collapsed {
    max-height: 60px;
    overflow: hidden;
}

.component-section.expanded {
    max-height: none;
}

/* Loading animation */
.loader {
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Component registry status indicator */
.registry-status {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: #28a745;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.8em;
    font-weight: 600;
    z-index: 10000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.registry-status.show {
    opacity: 1;
}

.registry-status.error {
    background: #dc3545;
}

.registry-status.warning {
    background: #ffc107;
    color: #212529;
}
</style>

<script>
/**
 * Enhanced Agent Details Rendering with Modular Components
 */
window.renderEnhancedAgentDetails = async function(modalBody, data, runId) {
    console.log('Enhanced Agent Details: Starting render with modular components');
    
    if (!modalBody) {
        console.error('Enhanced modal body element not provided');
        return;
    }
    
    try {
        // Show loading state
        modalBody.innerHTML = `
            <div class="component-loading">
                <div class="loader"></div>
                <p>Loading modular components...</p>
            </div>
        `;
        
        // Check if component registry is available
        if (!window.componentRegistry) {
            throw new Error('Component registry not available');
        }
        
        // Show registry status
        showRegistryStatus('Components loading...', 'info');
        
        // Determine agent type
        const agentRole = data.agent_role || 'unknown';
        const agentType = agentRole.toLowerCase();
        
        // Create container
        modalBody.innerHTML = `
            <div class="components-container">
                <div class="agent-type-indicator">
                    <div class="agent-info">
                        <h3>🤖 ${agentRole.charAt(0).toUpperCase() + agentRole.slice(1)} Agent Analysis</h3>
                        <p>${data.scenario || 'N/A'}</p>
                    </div>
                    <div class="agent-metadata">
                        <span class="agent-type-badge">${agentType.toUpperCase()}</span>
                        <span>Run: ${runId.substring(0, 8)}...</span>
                        <span>${data.execution_date ? new Date(data.execution_date).toLocaleString() : 'N/A'}</span>
                    </div>
                </div>
                <div id="agent-components-container" class="agent-components-container">
                    <!-- Components will be loaded here -->
                </div>
            </div>
        `;
        
        const componentsContainer = modalBody.querySelector('#agent-components-container');
        
        // Load agent-specific components
        await loadAgentComponents(agentType, componentsContainer, data, runId);
        
        // Load shared components
        await loadSharedComponents(componentsContainer, data, runId);
        
        showRegistryStatus('Components loaded successfully!', 'success');
        
        // Hide status after delay
        setTimeout(() => {
            hideRegistryStatus();
        }, 2000);
        
    } catch (error) {
        console.error('Enhanced Agent Details: Failed to render:', error);
        modalBody.innerHTML = `
            <div class="component-error">
                <h4>⚠️ Component Loading Failed</h4>
                <p>${error.message}</p>
                <p>Falling back to legacy modal view...</p>
                <button onclick="switchToLegacyModal('${runId}')" class="btn btn-primary">
                    Switch to Legacy Modal
                </button>
            </div>
        `;
        
        showRegistryStatus('Component loading failed', 'error');
    }
};

/**
 * Load agent-specific components
 */
async function loadAgentComponents(agentType, container, data, runId) {
    console.log(`Loading components for agent type: ${agentType}`);
    
    switch (agentType) {
        case 'resource':
            await loadResourceAgentComponents(container, data, runId);
            break;
        case 'mentor':
            await loadMentorAgentComponents(container, data, runId);
            break;
        case 'engagement':
            await loadEngagementAgentComponents(container, data, runId);
            break;
        default:
            await loadGenericAgentComponents(container, data, runId);
    }
}

/**
 * Load Resource Agent specific components
 */
async function loadResourceAgentComponents(container, data, runId) {
    try {
        // Create Resource Agent component
        const resourceComponent = window.componentRegistry.createComponent(
            'ResourceAgent',
            container,
            data,
            {
                attributes: {
                    title: 'Resource Analysis',
                    expanded: 'true'
                }
            }
        );
        
        if (!resourceComponent) {
            throw new Error('Failed to create ResourceAgent component');
        }
        
        console.log('Resource Agent component created successfully');
        
    } catch (error) {
        console.error('Failed to load Resource Agent components:', error);
        container.innerHTML += `
            <div class="component-error">
                <h4>Resource Agent Component Error</h4>
                <p>${error.message}</p>
            </div>
        `;
    }
}

/**
 * Load Mentor Agent specific components
 */
async function loadMentorAgentComponents(container, data, runId) {
    try {
        // Create Mentor Agent component
        const mentorComponent = window.componentRegistry.createComponent(
            'MentorAgent',
            container,
            data,
            {
                attributes: {
                    title: 'Mentor Communication Analysis',
                    expanded: 'true'
                }
            }
        );

        if (!mentorComponent) {
            throw new Error('Failed to create MentorAgent component');
        }

        console.log('Mentor Agent component created successfully');

    } catch (error) {
        console.error('Failed to load Mentor Agent components:', error);
        container.innerHTML += `
            <div class="component-error">
                <h4>Mentor Agent Component Error</h4>
                <p>${error.message}</p>
            </div>
        `;
    }
}

/**
 * Load Engagement Agent specific components
 */
async function loadEngagementAgentComponents(container, data, runId) {
    try {
        // Create Engagement Agent component
        const engagementComponent = window.componentRegistry.createComponent(
            'EngagementAgent',
            container,
            data,
            {
                attributes: {
                    title: 'Engagement & Pattern Analysis',
                    expanded: 'true'
                }
            }
        );

        if (!engagementComponent) {
            throw new Error('Failed to create EngagementAgent component');
        }

        console.log('Engagement Agent component created successfully');

    } catch (error) {
        console.error('Failed to load Engagement Agent components:', error);
        container.innerHTML += `
            <div class="component-error">
                <h4>Engagement Agent Component Error</h4>
                <p>${error.message}</p>
            </div>
        `;
    }
}

/**
 * Load generic agent components
 */
async function loadGenericAgentComponents(container, data, runId) {
    try {
        // Create Generic Agent component
        const genericComponent = window.componentRegistry.createComponent(
            'GenericAgent',
            container,
            data,
            {
                attributes: {
                    title: `${data.agent_role || 'Unknown'} Agent Analysis`,
                    expanded: 'true',
                    'view-mode': 'summary'
                }
            }
        );

        if (!genericComponent) {
            throw new Error('Failed to create GenericAgent component');
        }

        console.log('Generic Agent component created successfully');

    } catch (error) {
        console.error('Failed to load Generic Agent components:', error);
        container.innerHTML += `
            <div class="component-error">
                <h4>Generic Agent Component Error</h4>
                <p>${error.message}</p>
                <p>Agent Role: ${data.agent_role || 'Unknown'}</p>
                <p>Fallback: Basic performance metrics and output analysis.</p>
            </div>
        `;
    }
}

/**
 * Load shared components
 */
async function loadSharedComponents(container, data, runId) {
    try {
        // Performance visualization
        if (data.mean_duration || data.token_usage) {
            const performanceData = {
                labels: ['Response Time', 'Token Usage', 'Success Rate', 'Cost'],
                datasets: [{
                    label: 'Performance Metrics',
                    data: [
                        data.mean_duration || 0,
                        data.token_usage || 0,
                        (data.success_rate || 0) * 100,
                        (data.estimated_cost || 0) * 10000
                    ]
                }]
            };
            
            const perfComponent = window.componentRegistry.createComponent(
                'DataVisualization',
                container,
                performanceData,
                {
                    attributes: {
                        'chart-type': 'bar',
                        title: '📊 Performance Metrics',
                        height: '300px'
                    }
                }
            );
            
            if (perfComponent) {
                console.log('Performance visualization component created');
            }
        }
        
        // Tool usage visualization
        if (data.tool_breakdown && Object.keys(data.tool_breakdown).length > 0) {
            const toolData = {
                labels: Object.keys(data.tool_breakdown),
                datasets: [{
                    label: 'Tool Usage',
                    data: Object.values(data.tool_breakdown)
                }]
            };
            
            const toolComponent = window.componentRegistry.createComponent(
                'DataVisualization',
                container,
                toolData,
                {
                    attributes: {
                        'chart-type': 'pie',
                        title: '🛠️ Tool Usage Distribution',
                        height: '300px'
                    }
                }
            );
            
            if (toolComponent) {
                console.log('Tool usage visualization component created');
            }
        }
        
    } catch (error) {
        console.error('Failed to load shared components:', error);
    }
}

/**
 * Show registry status
 */
function showRegistryStatus(message, type = 'info') {
    let statusEl = document.querySelector('.registry-status');
    if (!statusEl) {
        statusEl = document.createElement('div');
        statusEl.className = 'registry-status';
        document.body.appendChild(statusEl);
    }
    
    statusEl.textContent = message;
    statusEl.className = `registry-status ${type} show`;
}

/**
 * Hide registry status
 */
function hideRegistryStatus() {
    const statusEl = document.querySelector('.registry-status');
    if (statusEl) {
        statusEl.classList.remove('show');
    }
}

/**
 * Switch to legacy modal
 */
function switchToLegacyModal(runId) {
    // Close enhanced modal
    const enhancedModal = document.getElementById('enhanced-agent-details-modal');
    if (enhancedModal) {
        enhancedModal.style.display = 'none';
    }
    
    // Open legacy modal
    if (typeof openAgentEvaluationModal === 'function') {
        openAgentEvaluationModal(runId);
    }
}

/**
 * Enhanced modal event handlers
 */
document.addEventListener('DOMContentLoaded', function() {
    // Close button
    const enhancedModal = document.getElementById('enhanced-agent-details-modal');
    if (enhancedModal) {
        const closeBtn = enhancedModal.querySelector('.close');
        if (closeBtn) {
            closeBtn.addEventListener('click', function() {
                enhancedModal.style.display = 'none';
            });
        }
        
        // Click outside to close
        enhancedModal.addEventListener('click', function(event) {
            if (event.target === enhancedModal) {
                enhancedModal.style.display = 'none';
            }
        });
    }
    
    // Switch to legacy button
    const switchBtn = document.getElementById('switch-to-legacy-btn');
    if (switchBtn) {
        switchBtn.addEventListener('click', function() {
            const runId = this.getAttribute('data-run-id');
            if (runId) {
                switchToLegacyModal(runId);
            }
        });
    }
});

/**
 * Open enhanced agent evaluation modal
 */
window.openEnhancedAgentEvaluationModal = function(benchmarkRunId) {
    console.log('Opening enhanced agent evaluation modal for run:', benchmarkRunId);
    
    // Check if modal elements exist
    const modal = document.getElementById('enhanced-agent-details-modal');
    const modalBody = document.getElementById('enhanced-agent-modal-body');
    
    if (!modal || !modalBody) {
        console.error('Enhanced agent modal elements not found');
        // Fallback to legacy modal
        if (typeof openAgentEvaluationModal === 'function') {
            openAgentEvaluationModal(benchmarkRunId);
        }
        return;
    }
    
    // Show modal
    modal.style.display = 'block';
    
    // Set run ID on switch button
    const switchBtn = document.getElementById('switch-to-legacy-btn');
    if (switchBtn) {
        switchBtn.setAttribute('data-run-id', benchmarkRunId);
    }
    
    // Load benchmark data and render
    loadBenchmarkData(benchmarkRunId)
        .then(data => {
            if (data) {
                window.renderEnhancedAgentDetails(modalBody, data, benchmarkRunId);
            } else {
                throw new Error('No benchmark data received');
            }
        })
        .catch(error => {
            console.error('Failed to load benchmark data:', error);
            modalBody.innerHTML = `
                <div class="component-error">
                    <h4>⚠️ Data Loading Failed</h4>
                    <p>${error.message}</p>
                    <button onclick="switchToLegacyModal('${benchmarkRunId}')" class="btn btn-primary">
                        Switch to Legacy Modal
                    </button>
                </div>
            `;
        });
};

/**
 * Load benchmark data (reuse existing function)
 */
async function loadBenchmarkData(benchmarkRunId) {
    try {
        const response = await fetch(`/admin/benchmarks/api/run/${benchmarkRunId}/`);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return await response.json();
    } catch (error) {
        console.error('Failed to fetch benchmark data:', error);
        throw error;
    }
}
</script>
