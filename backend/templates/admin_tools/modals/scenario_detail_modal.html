<!-- Scenario Detail Modal -->
<div id="scenario-detail-modal" class="modal" style="display: none;">
    <div class="modal-content" style="max-width: 1200px; max-height: 90vh; overflow-y: auto;">
        <div class="modal-header">
            <h2 id="scenario-detail-modal-title">Scenario Details</h2>
            <span class="close" onclick="hideScenarioDetailModal()">&times;</span>
        </div>
        
        <div class="modal-body">
            <!-- Scenario Overview -->
            <div class="scenario-overview">
                <div class="row">
                    <div class="col-md-6">
                        <h3>Basic Information</h3>
                        <div class="info-group">
                            <label>Name:</label>
                            <span id="scenario-detail-name" class="info-value"></span>
                        </div>
                        <div class="info-group">
                            <label>Agent Role:</label>
                            <span id="scenario-detail-agent-role" class="info-value"></span>
                        </div>
                        <div class="info-group">
                            <label>Workflow Type:</label>
                            <span id="scenario-detail-workflow-type" class="info-value"></span>
                        </div>
                        <div class="info-group">
                            <label>Status:</label>
                            <span id="scenario-detail-status" class="info-value"></span>
                        </div>
                        <div class="info-group">
                            <label>Version:</label>
                            <span id="scenario-detail-version" class="info-value"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h3>Metadata Summary</h3>
                        <div class="info-group">
                            <label>Warmup Runs:</label>
                            <span id="scenario-detail-warmup-runs" class="info-value"></span>
                        </div>
                        <div class="info-group">
                            <label>Benchmark Runs:</label>
                            <span id="scenario-detail-benchmark-runs" class="info-value"></span>
                        </div>
                        <div class="info-group">
                            <label>Timeout (seconds):</label>
                            <span id="scenario-detail-timeout" class="info-value"></span>
                        </div>
                        <div class="info-group">
                            <label>Evaluation Template:</label>
                            <span id="scenario-detail-eval-template" class="info-value"></span>
                        </div>
                    </div>
                </div>
                
                <div class="info-group">
                    <label>Description:</label>
                    <div id="scenario-detail-description" class="info-value description-text"></div>
                </div>
                
                <div class="info-group">
                    <label>Tags:</label>
                    <div id="scenario-detail-tags" class="tags-container"></div>
                </div>
            </div>

            <!-- Tabs for detailed information -->
            <div class="scenario-detail-tabs">
                <ul class="nav nav-tabs" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" id="input-data-tab" data-toggle="tab" href="#input-data-content" role="tab">Input Data</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="metadata-tab" data-toggle="tab" href="#metadata-content" role="tab">Metadata</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="quality-criteria-tab" data-toggle="tab" href="#quality-criteria-content" role="tab">Quality Criteria</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="mock-responses-tab" data-toggle="tab" href="#mock-responses-content" role="tab">Mock Responses</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="context-tab" data-toggle="tab" href="#context-content" role="tab">Context</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="history-tab" data-toggle="tab" href="#history-content" role="tab">Run History</a>
                    </li>
                </ul>

                <div class="tab-content">
                    <!-- Input Data Tab -->
                    <div class="tab-pane active" id="input-data-content" role="tabpanel">
                        <h4>Input Data Structure</h4>
                        <div class="json-viewer">
                            <pre id="scenario-detail-input-data" class="json-content"></pre>
                        </div>
                    </div>

                    <!-- Metadata Tab -->
                    <div class="tab-pane" id="metadata-content" role="tabpanel">
                        <h4>Complete Metadata</h4>
                        <div class="json-viewer">
                            <pre id="scenario-detail-metadata" class="json-content"></pre>
                        </div>
                    </div>

                    <!-- Quality Criteria Tab -->
                    <div class="tab-pane" id="quality-criteria-content" role="tabpanel">
                        <h4>Expected Quality Criteria</h4>
                        <div id="scenario-detail-quality-criteria" class="criteria-content">
                            <!-- Will be populated dynamically -->
                        </div>
                    </div>

                    <!-- Mock Responses Tab -->
                    <div class="tab-pane" id="mock-responses-content" role="tabpanel">
                        <h4>Mock Tool Responses</h4>
                        <div id="scenario-detail-mock-responses" class="mock-responses-content">
                            <!-- Will be populated dynamically -->
                        </div>
                    </div>

                    <!-- Context Tab -->
                    <div class="tab-pane" id="context-content" role="tabpanel">
                        <h4>User Profile Context</h4>
                        <div id="scenario-detail-context" class="context-content">
                            <!-- Will be populated dynamically -->
                        </div>
                    </div>

                    <!-- History Tab -->
                    <div class="tab-pane" id="history-content" role="tabpanel">
                        <h4>Recent Benchmark Runs</h4>
                        <div id="scenario-detail-history" class="history-content">
                            <div class="loading">Loading run history...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal-footer">
            <button type="button" class="btn btn-primary" onclick="editScenarioFromDetail()">
                <i class="fas fa-edit"></i> Edit Scenario
            </button>
            <button type="button" class="btn btn-info" onclick="duplicateScenario()">
                <i class="fas fa-copy"></i> Duplicate
            </button>
            <button type="button" class="btn btn-success" onclick="validateScenarioFromDetail()">
                <i class="fas fa-check"></i> Validate
            </button>
            <button type="button" class="btn btn-warning" onclick="exportScenario()">
                <i class="fas fa-download"></i> Export
            </button>
            <button type="button" class="btn btn-secondary" onclick="hideScenarioDetailModal()">
                Close
            </button>
        </div>
    </div>
</div>

<style>
.scenario-overview {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.info-group {
    margin-bottom: 15px;
}

.info-group label {
    font-weight: bold;
    color: #495057;
    display: inline-block;
    min-width: 120px;
}

.info-value {
    color: #212529;
}

.description-text {
    background: white;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #dee2e6;
    margin-top: 5px;
}

.tags-container {
    margin-top: 5px;
}

.tag-badge {
    display: inline-block;
    background: #007bff;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    margin-right: 5px;
    margin-bottom: 5px;
}

.json-viewer {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    max-height: 400px;
    overflow-y: auto;
}

.json-content {
    margin: 0;
    padding: 15px;
    background: transparent;
    border: none;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    line-height: 1.4;
}

.scenario-detail-tabs {
    margin-top: 20px;
}

.criteria-content, .mock-responses-content, .context-content, .history-content {
    padding: 15px;
    min-height: 200px;
}

.loading {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 40px;
}
</style>
