<!-- Phase 2 Advanced Monitoring Dashboard Modal -->
<div id="phase2-monitoring-modal" class="modal">
    <div class="modal-content phase2-monitoring-modal">
        <span class="close">&times;</span>
        <div class="modal-header phase2-monitoring-header">
            <h2>🚀 Phase 2 Advanced Monitoring Dashboard</h2>
            <p class="modal-description">
                Real-time state tracking, tool call sequence analysis, and performance correlation insights for enhanced debugging and optimization.
            </p>
            <div class="monitoring-badges">
                <span class="monitoring-badge real-time" title="Real-Time State Tracking">
                    ⚡ Real-Time Tracking
                </span>
                <span class="monitoring-badge correlation" title="Performance Correlation Analysis">
                    📊 Correlation Analysis
                </span>
                <span class="monitoring-badge insights" title="Automated Insights">
                    💡 AI Insights
                </span>
                <span class="monitoring-badge recommendations" title="Debugging Recommendations">
                    🔍 Recommendations
                </span>
            </div>
        </div>
        <div id="phase2-monitoring-body" class="phase2-monitoring-body">
            <!-- Content will be loaded here by JS -->
            <div class="modal-loading">
                <div class="loader"></div>
                <p>Loading Phase 2 monitoring data...</p>
            </div>
        </div>
    </div>
</div>

<style>
/* Phase 2 Monitoring Modal Specific Styles */
.phase2-monitoring-modal .modal-content {
    max-width: 1400px;
    width: 95%;
    max-height: 90vh;
    overflow-y: auto;
}

.phase2-monitoring-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    border-radius: 8px;
    margin-bottom: 25px;
    text-align: center;
}

.phase2-monitoring-header h2 {
    margin: 0 0 10px 0;
    font-size: 2em;
}

.monitoring-badges {
    display: flex;
    gap: 12px;
    margin-top: 20px;
    flex-wrap: wrap;
    justify-content: center;
}

.monitoring-badge {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 13px;
    font-weight: 600;
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    cursor: help;
}

.monitoring-badge:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.2);
}

.monitoring-badge.real-time {
    background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
}

.monitoring-badge.correlation {
    background: linear-gradient(135deg, #48dbfb 0%, #0abde3 100%);
}

.monitoring-badge.insights {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.monitoring-badge.recommendations {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

/* Dashboard Grid Layout */
.monitoring-dashboard-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 25px;
    margin-bottom: 30px;
}

.monitoring-panel {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 25px;
    border: 1px solid #e9ecef;
    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.monitoring-panel:hover {
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.monitoring-panel h3 {
    margin: 0 0 20px 0;
    color: #495057;
    font-size: 1.4em;
    display: flex;
    align-items: center;
    gap: 10px;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
}

/* State Consistency Panel */
.state-consistency-panel {
    border-left: 4px solid #28a745;
}

.state-consistency-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.consistency-metric {
    background: white;
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    border: 1px solid #e9ecef;
    transition: all 0.2s ease;
}

.consistency-metric:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.consistency-metric-value {
    font-size: 24px;
    font-weight: bold;
    color: #495057;
    display: block;
    margin-bottom: 5px;
}

.consistency-metric-value.good { color: #28a745; }
.consistency-metric-value.warning { color: #ffc107; }
.consistency-metric-value.error { color: #dc3545; }

.consistency-metric-label {
    font-size: 12px;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Tool Sequence Panel */
.tool-sequence-panel {
    border-left: 4px solid #6f42c1;
}

.sequence-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.sequence-metric {
    background: white;
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    border: 1px solid #e9ecef;
    transition: all 0.2s ease;
}

.sequence-metric:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.sequence-metric-value {
    font-size: 24px;
    font-weight: bold;
    color: #6f42c1;
    display: block;
    margin-bottom: 5px;
}

.sequence-metric-label {
    font-size: 12px;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Performance Correlation Panel */
.performance-correlation-panel {
    border-left: 4px solid #17a2b8;
}

.correlation-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.correlation-metric {
    background: white;
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    border: 1px solid #e9ecef;
    transition: all 0.2s ease;
}

.correlation-metric:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.correlation-metric-value {
    font-size: 24px;
    font-weight: bold;
    color: #17a2b8;
    display: block;
    margin-bottom: 5px;
}

.correlation-metric-label {
    font-size: 12px;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Insights Panel */
.insights-panel {
    border-left: 4px solid #fd7e14;
}

.insights-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.insight-metric {
    background: white;
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    border: 1px solid #e9ecef;
    transition: all 0.2s ease;
}

.insight-metric:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.insight-metric-value {
    font-size: 24px;
    font-weight: bold;
    color: #fd7e14;
    display: block;
    margin-bottom: 5px;
}

.insight-metric-label {
    font-size: 12px;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Full Width Panels */
.monitoring-full-panel {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 25px;
    border: 1px solid #e9ecef;
    margin-bottom: 25px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
}

.monitoring-full-panel h3 {
    margin: 0 0 20px 0;
    color: #495057;
    font-size: 1.4em;
    display: flex;
    align-items: center;
    gap: 10px;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
}

/* Detailed Section Styles */
.state-timeline {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 15px;
}

.state-timeline-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 12px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    transition: all 0.2s ease;
}

.state-timeline-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.state-timeline-item.valid {
    border-left: 4px solid #28a745;
}

.state-timeline-item.warning {
    border-left: 4px solid #ffc107;
}

.state-timeline-item.inconsistent {
    border-left: 4px solid #dc3545;
}

.state-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    flex-shrink: 0;
}

.state-indicator.valid { background: #28a745; }
.state-indicator.warning { background: #ffc107; }
.state-indicator.inconsistent { background: #dc3545; }

.state-info {
    flex: 1;
}

.state-agent {
    font-weight: 600;
    color: #495057;
    margin-bottom: 2px;
}

.state-stage {
    font-size: 0.9em;
    color: #6c757d;
}

.state-timestamp {
    font-size: 0.8em;
    color: #6c757d;
    font-family: monospace;
}

.state-issues {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.issue-badge {
    background: #f8d7da;
    color: #721c24;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
}

.sequence-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 15px;
}

.sequence-item {
    background: white;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e9ecef;
    transition: all 0.2s ease;
}

.sequence-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.sequence-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
}

.sequence-number {
    background: #6f42c1;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}

.sequence-type-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.sequence-type-badge.sequential {
    background: #d4edda;
    color: #155724;
}

.sequence-type-badge.parallel {
    background: #fff3cd;
    color: #856404;
}

.sequence-type-badge.dependent {
    background: #f8d7da;
    color: #721c24;
}

.sequence-duration {
    background: #e9ecef;
    color: #6c757d;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    margin-left: auto;
}

.sequence-info {
    display: flex;
    gap: 15px;
    font-size: 0.9em;
    color: #6c757d;
}

.sequence-dependencies {
    margin-top: 8px;
    font-size: 0.9em;
    color: #6c757d;
    font-style: italic;
}

.correlation-matrix {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 15px;
}

.correlation-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.correlation-components {
    font-weight: 600;
    color: #495057;
}

.correlation-strength {
    display: flex;
    align-items: center;
    gap: 10px;
}

.correlation-coefficient {
    font-weight: 600;
    font-family: monospace;
}

.correlation-coefficient.positive { color: #28a745; }
.correlation-coefficient.negative { color: #dc3545; }
.correlation-coefficient.none { color: #6c757d; }

.correlation-bar {
    width: 60px;
    height: 6px;
    background: #e9ecef;
    border-radius: 3px;
    overflow: hidden;
}

.correlation-fill {
    height: 100%;
    transition: width 0.3s ease;
}

.correlation-fill.positive {
    background: linear-gradient(90deg, #28a745, #20c997);
}

.correlation-fill.negative {
    background: linear-gradient(90deg, #dc3545, #e74c3c);
}

.correlation-significance {
    font-size: 0.8em;
    color: #6c757d;
}

.insights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.insight-card {
    background: white;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e9ecef;
}

.insight-card h6 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 1em;
    display: flex;
    align-items: center;
    gap: 8px;
}

.insight-metrics {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.insight-metric-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 0;
    border-bottom: 1px solid #f8f9fa;
}

.insight-metric-row:last-child {
    border-bottom: none;
}

.recommendations-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 15px;
}

.recommendation-item {
    background: white;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e9ecef;
    border-left: 4px solid transparent;
}

.recommendation-item.high {
    border-left-color: #dc3545;
}

.recommendation-item.medium {
    border-left-color: #ffc107;
}

.recommendation-item.low {
    border-left-color: #28a745;
}

.recommendation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.recommendation-title {
    font-weight: 600;
    color: #495057;
}

.recommendation-priority {
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.recommendation-priority.high {
    background: #f8d7da;
    color: #721c24;
}

.recommendation-priority.medium {
    background: #fff3cd;
    color: #856404;
}

.recommendation-priority.low {
    background: #d4edda;
    color: #155724;
}

.recommendation-description {
    color: #6c757d;
    font-size: 0.9em;
    margin-bottom: 8px;
    line-height: 1.4;
}

.recommendation-suggestion {
    color: #495057;
    font-size: 0.9em;
    font-style: italic;
    background: #f8f9fa;
    padding: 8px;
    border-radius: 4px;
    border-left: 3px solid #007bff;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .monitoring-dashboard-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .phase2-monitoring-modal .modal-content {
        width: 98%;
        margin: 1% auto;
    }

    .monitoring-badges {
        flex-direction: column;
        align-items: center;
    }

    .consistency-overview,
    .sequence-overview,
    .correlation-overview,
    .insights-overview {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    }

    .insights-grid {
        grid-template-columns: 1fr;
    }

    .correlation-row {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
}
</style>

<script>
// Phase 2 Monitoring Dashboard Functions
window.showPhase2MonitoringDashboard = function(benchmarkData) {
    const modal = document.getElementById('phase2-monitoring-modal');
    const modalBody = document.getElementById('phase2-monitoring-body');
    
    if (!modal || !modalBody) {
        console.error('Phase 2 monitoring modal elements not found');
        return;
    }
    
    // Show modal
    modal.style.display = 'block';
    
    // Load monitoring dashboard
    renderPhase2MonitoringDashboard(modalBody, benchmarkData);
}

function renderPhase2MonitoringDashboard(modalBody, benchmarkData) {
    const agentCommunications = benchmarkData.agent_communications || {};
    
    // Phase 2 data
    const stateSnapshots = agentCommunications.state_snapshots || [];
    const toolCallSequences = agentCommunications.tool_call_sequences || [];
    const performanceCorrelations = agentCommunications.performance_correlations || [];
    const performanceInsights = agentCommunications.performance_insights || {};
    const debuggingRecommendations = agentCommunications.debugging_recommendations || [];
    const advancedMonitoring = agentCommunications.advanced_monitoring || false;
    
    if (!advancedMonitoring) {
        modalBody.innerHTML = `
            <div class="no-data">
                <h3>⚠️ Phase 2 Advanced Monitoring Not Available</h3>
                <p>This benchmark run was executed without Phase 2 advanced monitoring enabled.</p>
                <p>To access these features:</p>
                <ul>
                    <li>Enable advanced monitoring in benchmark configuration</li>
                    <li>Run benchmarks with enhanced tracking enabled</li>
                    <li>Ensure data model version 2.0.0 or higher</li>
                </ul>
            </div>
        `;
        return;
    }
    
    modalBody.innerHTML = `
        <div class="monitoring-dashboard-grid">
            <!-- State Consistency Panel -->
            <div class="monitoring-panel state-consistency-panel">
                <h3>🔄 State Consistency Tracking</h3>
                <div class="state-consistency-overview">
                    <div class="consistency-metric">
                        <span class="consistency-metric-value">${stateSnapshots.length}</span>
                        <span class="consistency-metric-label">Total Snapshots</span>
                    </div>
                    <div class="consistency-metric">
                        <span class="consistency-metric-value good">${stateSnapshots.filter(s => s.validation_status === 'valid').length}</span>
                        <span class="consistency-metric-label">Valid States</span>
                    </div>
                    <div class="consistency-metric">
                        <span class="consistency-metric-value warning">${stateSnapshots.filter(s => s.validation_status === 'warning').length}</span>
                        <span class="consistency-metric-label">Warnings</span>
                    </div>
                    <div class="consistency-metric">
                        <span class="consistency-metric-value error">${stateSnapshots.filter(s => s.validation_status === 'inconsistent').length}</span>
                        <span class="consistency-metric-label">Inconsistencies</span>
                    </div>
                </div>
                ${renderStateConsistencyDetails(stateSnapshots)}
            </div>
            
            <!-- Tool Sequence Analysis Panel -->
            <div class="monitoring-panel tool-sequence-panel">
                <h3>🔧 Tool Call Sequence Analysis</h3>
                <div class="sequence-overview">
                    <div class="sequence-metric">
                        <span class="sequence-metric-value">${toolCallSequences.length}</span>
                        <span class="sequence-metric-label">Sequences</span>
                    </div>
                    <div class="sequence-metric">
                        <span class="sequence-metric-value">${toolCallSequences.filter(s => s.sequence_type === 'parallel').length}</span>
                        <span class="sequence-metric-label">Parallel</span>
                    </div>
                    <div class="sequence-metric">
                        <span class="sequence-metric-value">${toolCallSequences.filter(s => s.sequence_type === 'sequential').length}</span>
                        <span class="sequence-metric-label">Sequential</span>
                    </div>
                    <div class="sequence-metric">
                        <span class="sequence-metric-value">${toolCallSequences.reduce((sum, s) => sum + s.dependencies.length, 0)}</span>
                        <span class="sequence-metric-label">Dependencies</span>
                    </div>
                </div>
                ${renderToolSequenceDetails(toolCallSequences)}
            </div>
        </div>
        
        <!-- Performance Correlation Analysis -->
        <div class="monitoring-full-panel performance-correlation-panel">
            <h3>📊 Performance Correlation Analysis</h3>
            <div class="correlation-overview">
                <div class="correlation-metric">
                    <span class="correlation-metric-value">${performanceCorrelations.length}</span>
                    <span class="correlation-metric-label">Correlations</span>
                </div>
                <div class="correlation-metric">
                    <span class="correlation-metric-value">${performanceCorrelations.filter(c => c.correlation_type === 'positive' && Math.abs(c.correlation_coefficient) > 0.7).length}</span>
                    <span class="correlation-metric-label">Strong Positive</span>
                </div>
                <div class="correlation-metric">
                    <span class="correlation-metric-value">${performanceCorrelations.filter(c => c.correlation_type === 'negative' && Math.abs(c.correlation_coefficient) > 0.7).length}</span>
                    <span class="correlation-metric-label">Strong Negative</span>
                </div>
                <div class="correlation-metric">
                    <span class="correlation-metric-value">${performanceCorrelations.filter(c => Math.abs(c.correlation_coefficient) > 0.5).length}</span>
                    <span class="correlation-metric-label">Significant</span>
                </div>
            </div>
            ${renderPerformanceCorrelationDetails(performanceCorrelations)}
        </div>
        
        <!-- Performance Insights -->
        <div class="monitoring-full-panel insights-panel">
            <h3>💡 Automated Performance Insights</h3>
            <div class="insights-overview">
                <div class="insight-metric">
                    <span class="insight-metric-value">${performanceInsights.workflow_efficiency?.efficiency_score ? (performanceInsights.workflow_efficiency.efficiency_score * 100).toFixed(1) : 'N/A'}</span>
                    <span class="insight-metric-label">Efficiency %</span>
                </div>
                <div class="insight-metric">
                    <span class="insight-metric-value">${performanceInsights.bottleneck_analysis?.bottleneck_percentage?.toFixed(1) || 'N/A'}</span>
                    <span class="insight-metric-label">Bottleneck %</span>
                </div>
                <div class="insight-metric">
                    <span class="insight-metric-value">${performanceInsights.quality_metrics?.error_count || 0}</span>
                    <span class="insight-metric-label">Errors</span>
                </div>
                <div class="insight-metric">
                    <span class="insight-metric-value">${debuggingRecommendations.length}</span>
                    <span class="insight-metric-label">Recommendations</span>
                </div>
            </div>
            ${renderPerformanceInsightsDetails(performanceInsights, debuggingRecommendations)}
        </div>
    `;
}

// Helper functions for rendering detailed sections

function renderStateConsistencyDetails(stateSnapshots) {
    if (stateSnapshots.length === 0) {
        return '<p>No state snapshots captured during execution.</p>';
    }

    return `
        <div class="state-consistency-details">
            <h5>State Snapshot Timeline</h5>
            <div class="state-timeline">
                ${stateSnapshots.map(snapshot => `
                    <div class="state-timeline-item ${snapshot.validation_status}">
                        <div class="state-indicator ${snapshot.validation_status}"></div>
                        <div class="state-info">
                            <div class="state-agent">${snapshot.agent}</div>
                            <div class="state-stage">${snapshot.stage}</div>
                            <div class="state-timestamp">${new Date(snapshot.timestamp).toLocaleTimeString()}</div>
                        </div>
                        ${snapshot.validation_details?.inconsistencies_found?.length > 0 ? `
                            <div class="state-issues">
                                ${snapshot.validation_details.inconsistencies_found.map(issue => `
                                    <span class="issue-badge">${issue}</span>
                                `).join('')}
                            </div>
                        ` : ''}
                    </div>
                `).join('')}
            </div>
        </div>
    `;
}

function renderToolSequenceDetails(toolCallSequences) {
    if (toolCallSequences.length === 0) {
        return '<p>No tool call sequences analyzed during execution.</p>';
    }

    return `
        <div class="tool-sequence-details">
            <h5>Sequence Performance Analysis</h5>
            <div class="sequence-list">
                ${toolCallSequences.map((sequence, index) => `
                    <div class="sequence-item">
                        <div class="sequence-header">
                            <span class="sequence-number">${index + 1}</span>
                            <span class="sequence-type-badge ${sequence.sequence_type}">${sequence.sequence_type}</span>
                            <span class="sequence-duration">${sequence.total_duration_ms.toFixed(2)}ms</span>
                        </div>
                        <div class="sequence-info">
                            <div class="sequence-tools">${sequence.tool_calls.length} tools</div>
                            <div class="sequence-efficiency">Efficiency: ${sequence.performance_impact.efficiency_score?.toFixed(2) || 'N/A'}</div>
                        </div>
                        ${sequence.dependencies.length > 0 ? `
                            <div class="sequence-dependencies">
                                Dependencies: ${sequence.dependencies.join(', ')}
                            </div>
                        ` : ''}
                    </div>
                `).join('')}
            </div>
        </div>
    `;
}

function renderPerformanceCorrelationDetails(performanceCorrelations) {
    if (performanceCorrelations.length === 0) {
        return '<p>No performance correlations found during execution.</p>';
    }

    return `
        <div class="correlation-details">
            <h5>Correlation Matrix</h5>
            <div class="correlation-matrix">
                ${performanceCorrelations.map(correlation => `
                    <div class="correlation-row">
                        <div class="correlation-components">
                            ${correlation.component_a} ↔ ${correlation.component_b}
                        </div>
                        <div class="correlation-strength">
                            <span class="correlation-coefficient ${correlation.correlation_type}">
                                ${correlation.correlation_coefficient.toFixed(3)}
                            </span>
                            <div class="correlation-bar">
                                <div class="correlation-fill ${correlation.correlation_type}"
                                     style="width: ${Math.abs(correlation.correlation_coefficient) * 100}%"></div>
                            </div>
                            <span class="correlation-significance">
                                ${(correlation.significance_level * 100).toFixed(1)}% sig.
                            </span>
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
}

function renderPerformanceInsightsDetails(performanceInsights, debuggingRecommendations) {
    return `
        <div class="insights-details">
            ${Object.keys(performanceInsights).length > 0 ? `
                <div class="insights-breakdown">
                    <h5>Performance Breakdown</h5>
                    <div class="insights-grid">
                        ${performanceInsights.workflow_efficiency ? `
                            <div class="insight-card">
                                <h6>⚡ Workflow Efficiency</h6>
                                <div class="insight-metrics">
                                    <div class="insight-metric-row">
                                        <span>Total Execution:</span>
                                        <span>${performanceInsights.workflow_efficiency.total_execution_time_ms?.toFixed(2) || 'N/A'}ms</span>
                                    </div>
                                    <div class="insight-metric-row">
                                        <span>Average Duration:</span>
                                        <span>${performanceInsights.workflow_efficiency.average_agent_duration_ms?.toFixed(2) || 'N/A'}ms</span>
                                    </div>
                                    <div class="insight-metric-row">
                                        <span>Agent Count:</span>
                                        <span>${performanceInsights.workflow_efficiency.agent_count || 'N/A'}</span>
                                    </div>
                                </div>
                            </div>
                        ` : ''}

                        ${performanceInsights.bottleneck_analysis ? `
                            <div class="insight-card">
                                <h6>🚧 Bottleneck Analysis</h6>
                                <div class="insight-metrics">
                                    <div class="insight-metric-row">
                                        <span>Slowest Agent:</span>
                                        <span>${performanceInsights.bottleneck_analysis.slowest_agent || 'N/A'}</span>
                                    </div>
                                    <div class="insight-metric-row">
                                        <span>Duration:</span>
                                        <span>${performanceInsights.bottleneck_analysis.slowest_duration_ms?.toFixed(2) || 'N/A'}ms</span>
                                    </div>
                                    <div class="insight-metric-row">
                                        <span>Impact:</span>
                                        <span>${performanceInsights.bottleneck_analysis.bottleneck_percentage?.toFixed(1) || 'N/A'}%</span>
                                    </div>
                                </div>
                            </div>
                        ` : ''}

                        ${performanceInsights.resource_utilization ? `
                            <div class="insight-card">
                                <h6>🧠 Resource Utilization</h6>
                                <div class="insight-metrics">
                                    <div class="insight-metric-row">
                                        <span>Total LLM Calls:</span>
                                        <span>${performanceInsights.resource_utilization.total_llm_calls || 'N/A'}</span>
                                    </div>
                                    <div class="insight-metric-row">
                                        <span>Total Tokens:</span>
                                        <span>${performanceInsights.resource_utilization.total_tokens?.toLocaleString() || 'N/A'}</span>
                                    </div>
                                    <div class="insight-metric-row">
                                        <span>Avg Tokens/Call:</span>
                                        <span>${performanceInsights.resource_utilization.avg_tokens_per_call?.toFixed(0) || 'N/A'}</span>
                                    </div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            ` : ''}

            ${debuggingRecommendations.length > 0 ? `
                <div class="recommendations-section">
                    <h5>🔍 Debugging Recommendations</h5>
                    <div class="recommendations-list">
                        ${debuggingRecommendations.map(rec => `
                            <div class="recommendation-item ${rec.priority}">
                                <div class="recommendation-header">
                                    <span class="recommendation-title">${rec.title}</span>
                                    <span class="recommendation-priority ${rec.priority}">${rec.priority}</span>
                                </div>
                                <div class="recommendation-description">${rec.description}</div>
                                <div class="recommendation-suggestion">${rec.suggestion}</div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            ` : ''}
        </div>
    `;
}

// Close modal functionality
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('phase2-monitoring-modal');
    const closeBtn = modal?.querySelector('.close');

    if (closeBtn) {
        closeBtn.onclick = function() {
            modal.style.display = 'none';
        }
    }

    if (modal) {
        window.onclick = function(event) {
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }
    }
});
</script>
