<!-- Agent Execution Detail Modal -->
<div class="modal fade" id="agentExecutionDetailModal" tabindex="-1" aria-labelledby="agentExecutionDetailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content agent-execution-modal">
            <div class="modal-header">
                <h5 class="modal-title" id="agentExecutionDetailModalLabel">
                    <span class="agent-icon">🤖</span>
                    <span id="agent-detail-title">Agent Execution Details</span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="agent-detail-body">
                <!-- Content will be loaded here by JS -->
                <div class="modal-loading">
                    <div class="loader"></div>
                    <p>Loading agent execution details...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Agent Execution Detail Modal Styles */
.agent-execution-modal .modal-content {
    max-width: 1400px;
    width: 95%;
}

.agent-execution-modal .modal-body {
    max-height: 80vh;
    overflow-y: auto;
    padding: 1.5rem;
}

.agent-detail-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
}

.agent-detail-header h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.agent-detail-meta {
    display: flex;
    gap: 1rem;
    margin-top: 0.5rem;
    flex-wrap: wrap;
}

.agent-detail-meta span {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
}

.execution-sections {
    display: grid;
    gap: 1.5rem;
}

.execution-section {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
}

.section-header {
    background: #e9ecef;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.section-content {
    padding: 1.5rem;
}

.execution-timeline {
    position: relative;
    padding-left: 2rem;
}

.timeline-item {
    position: relative;
    padding-bottom: 1.5rem;
    border-left: 2px solid #dee2e6;
    padding-left: 1.5rem;
    margin-left: 0.5rem;
}

.timeline-item:last-child {
    border-left: none;
}

.timeline-marker {
    position: absolute;
    left: -0.5rem;
    top: 0.25rem;
    width: 1rem;
    height: 1rem;
    background: #007bff;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-marker.success {
    background: #28a745;
}

.timeline-marker.error {
    background: #dc3545;
}

.timeline-marker.warning {
    background: #ffc107;
}

.timeline-content h6 {
    margin: 0 0 0.5rem 0;
    color: #495057;
    font-weight: 600;
}

.timeline-content p {
    margin: 0;
    color: #6c757d;
    font-size: 0.875rem;
}

.timeline-timestamp {
    font-size: 0.75rem;
    color: #adb5bd;
    margin-top: 0.25rem;
}

.data-display {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 1rem;
    margin-top: 1rem;
}

.data-display pre {
    margin: 0;
    font-size: 0.875rem;
    color: #495057;
    white-space: pre-wrap;
    word-wrap: break-word;
}

/* Rich output display styles */
.rich-output-display {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    max-height: 400px;
    overflow-y: auto;
}

.rich-output-display h6 {
    margin: 0 0 1rem 0;
    color: #495057;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
    padding-bottom: 0.5rem;
}

.output-sections {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.output-section {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 0.75rem;
}

.output-section h7 {
    display: block;
    font-weight: 600;
    color: #6c757d;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.output-section ul {
    margin: 0;
    padding-left: 1rem;
    list-style-type: disc;
}

.output-section li {
    margin-bottom: 0.25rem;
    font-size: 0.85rem;
}

.activities-list {
    max-height: 200px;
    overflow-y: auto;
}

.activity-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 0.5rem;
    margin-bottom: 0.5rem;
}

.activity-item strong {
    color: #495057;
    display: block;
    margin-bottom: 0.25rem;
}

.activity-item p {
    margin: 0 0 0.25rem 0;
    font-size: 0.85rem;
    color: #6c757d;
}

.activity-item small {
    color: #868e96;
    font-size: 0.75rem;
}

.json-fallback {
    margin-top: 1rem;
    border-top: 1px solid #dee2e6;
    padding-top: 1rem;
}

.json-fallback details {
    cursor: pointer;
}

.json-fallback summary {
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
}

.json-fallback pre {
    background: #f1f3f4;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 0.75rem;
    font-size: 0.75rem;
    max-height: 200px;
    overflow-y: auto;
}

.tool-calls-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.tool-call-item {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 1rem;
}

.tool-call-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.tool-call-name {
    font-weight: 600;
    color: #495057;
}

.tool-call-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
}

.tool-call-status.success {
    background: #d4edda;
    color: #155724;
}

.tool-call-status.error {
    background: #f8d7da;
    color: #721c24;
}

.tool-call-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.tool-call-input,
.tool-call-output {
    background: #f8f9fa;
    border-radius: 4px;
    padding: 0.75rem;
}

.tool-call-input h6,
.tool-call-output h6 {
    margin: 0 0 0.5rem 0;
    font-size: 0.875rem;
    font-weight: 600;
    color: #495057;
}

.performance-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.metric-card {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 1rem;
    text-align: center;
}

.metric-value {
    font-size: 1.5rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.25rem;
}

.metric-label {
    font-size: 0.875rem;
    color: #6c757d;
}

.error-details {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 6px;
    padding: 1rem;
    color: #721c24;
}

.error-details h6 {
    margin: 0 0 0.5rem 0;
    color: #721c24;
}

.execution-mode-indicator {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.mode-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
}

.mode-badge.real {
    background: #d4edda;
    color: #155724;
}

.mode-badge.mock {
    background: #fff3cd;
    color: #856404;
}

.expandable-section {
    cursor: pointer;
    user-select: none;
}

.expandable-section:hover {
    background: #f1f3f4;
}

.expandable-content {
    display: none;
    margin-top: 1rem;
}

.expandable-content.expanded {
    display: block;
}

.expand-icon {
    transition: transform 0.2s ease;
}

.expand-icon.expanded {
    transform: rotate(90deg);
}
</style>

<script>
// Global function to show agent execution details
function showAgentExecutionDetails(agentName, agentIndex, workflowData) {
    console.log('Showing agent execution details for:', agentName, 'at index:', agentIndex);
    
    // Get the agent communication data
    const agentCommunications = workflowData?.agent_communications?.agents || [];
    const agentData = agentCommunications[agentIndex];
    
    if (!agentData) {
        console.error('Agent data not found for index:', agentIndex);
        alert('Agent execution data not available');
        return;
    }
    
    // Update modal title
    document.getElementById('agent-detail-title').textContent = `${agentName} Execution Details`;
    
    // Render the agent details
    const modalBody = document.getElementById('agent-detail-body');
    modalBody.innerHTML = renderAgentExecutionDetails(agentData, agentName, workflowData);
    
    // Show the modal
    const modalElement = document.getElementById('agentExecutionDetailModal');
    if (modalElement) {
        // Try Bootstrap 5 first
        if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
            try {
                const modal = new bootstrap.Modal(modalElement);
                modal.show();
            } catch (e) {
                console.warn('Bootstrap modal failed, using fallback:', e);
                // Fallback: show modal manually
                modalElement.style.display = 'block';
                modalElement.classList.add('show');
                // Add backdrop
                const backdrop = document.createElement('div');
                backdrop.className = 'modal-backdrop fade show';
                backdrop.id = 'agent-detail-backdrop';
                document.body.appendChild(backdrop);
            }
        } else {
            // Fallback: show modal manually
            modalElement.style.display = 'block';
            modalElement.classList.add('show');
            // Add backdrop
            const backdrop = document.createElement('div');
            backdrop.className = 'modal-backdrop fade show';
            backdrop.id = 'agent-detail-backdrop';
            document.body.appendChild(backdrop);
        }
    }
}

// Add close handler for manual modal
function closeAgentDetailModal() {
    const modalElement = document.getElementById('agentExecutionDetailModal');
    const backdrop = document.getElementById('agent-detail-backdrop');

    if (modalElement) {
        modalElement.style.display = 'none';
        modalElement.classList.remove('show');
    }

    if (backdrop) {
        backdrop.remove();
    }
}

// Attach close event listeners
document.addEventListener('DOMContentLoaded', function() {
    const closeBtn = document.querySelector('#agentExecutionDetailModal .close');
    if (closeBtn) {
        closeBtn.addEventListener('click', closeAgentDetailModal);
    }

    // Close on backdrop click
    document.addEventListener('click', function(e) {
        if (e.target && e.target.id === 'agent-detail-backdrop') {
            closeAgentDetailModal();
        }
    });
});

function renderAgentExecutionDetails(agentData, agentName, workflowData) {
    const duration = agentData.duration_ms ? `${agentData.duration_ms.toFixed(2)}ms` : 'N/A';
    const timestamp = agentData.timestamp ? new Date(agentData.timestamp).toLocaleString() : 'N/A';
    const success = agentData.success !== false;
    
    return `
        <div class="agent-detail-header">
            <h3>🤖 ${agentName} Agent</h3>
            <div class="agent-detail-meta">
                <span>Stage: ${agentData.stage || 'Unknown'}</span>
                <span>Duration: ${duration}</span>
                <span>Status: ${success ? '✅ Success' : '❌ Failed'}</span>
                <span>Timestamp: ${timestamp}</span>
            </div>
        </div>
        
        <div class="execution-sections">
            ${renderExecutionOverview(agentData)}
            ${renderInputOutput(agentData)}
            ${renderToolCalls(agentData)}
            ${renderPerformanceMetrics(agentData)}
            ${renderExecutionMode(agentData)}
            ${agentData.error ? renderErrorDetails(agentData) : ''}
        </div>
    `;
}

function renderExecutionOverview(agentData) {
    return `
        <div class="execution-section">
            <div class="section-header">
                <span>📊</span> Execution Overview
            </div>
            <div class="section-content">
                <div class="execution-timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker ${agentData.success !== false ? 'success' : 'error'}"></div>
                        <div class="timeline-content">
                            <h6>Agent Execution</h6>
                            <p>${agentData.success !== false ? 'Completed successfully' : 'Failed with error'}</p>
                            <div class="timeline-timestamp">${agentData.timestamp ? new Date(agentData.timestamp).toLocaleString() : 'N/A'}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function renderInputOutput(agentData) {
    // Use comprehensive data extraction functions
    const inputData = extractAgentInputData(agentData);
    const outputData = extractAgentOutputData(agentData);

    return `
        <div class="execution-section">
            <div class="section-header">
                <span>📥📤</span> Input & Output Data
            </div>
            <div class="section-content">
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1.5rem;">
                    <div>
                        <h6>Input Data</h6>
                        <div class="data-display">
                            <pre>${JSON.stringify(inputData, null, 2)}</pre>
                        </div>
                    </div>
                    <div>
                        <h6>Output Data</h6>
                        <div class="data-display">
                            ${renderRichOutputData(outputData, agentData)}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function renderToolCalls(agentData) {
    const toolCalls = extractToolCallsFromAgent(agentData);
    
    if (!toolCalls || toolCalls.length === 0) {
        return `
            <div class="execution-section">
                <div class="section-header">
                    <span>🛠️</span> Tool Calls
                </div>
                <div class="section-content">
                    <p class="text-muted">No tool calls detected for this agent execution.</p>
                </div>
            </div>
        `;
    }
    
    return `
        <div class="execution-section">
            <div class="section-header">
                <span>🛠️</span> Tool Calls (${toolCalls.length})
            </div>
            <div class="section-content">
                <div class="tool-calls-list">
                    ${toolCalls.map((toolCall, index) => `
                        <div class="tool-call-item">
                            <div class="tool-call-header">
                                <span class="tool-call-name">🔧 ${toolCall.name || `Tool Call ${index + 1}`}</span>
                                <span class="tool-call-status ${toolCall.success ? 'success' : 'error'}">
                                    ${toolCall.success ? 'Success' : 'Failed'}
                                </span>
                            </div>
                            <div class="tool-call-details">
                                <div class="tool-call-input">
                                    <h6>Input Parameters</h6>
                                    <pre>${JSON.stringify(toolCall.input || {}, null, 2)}</pre>
                                </div>
                                <div class="tool-call-output">
                                    <h6>Output Result</h6>
                                    <pre>${JSON.stringify(toolCall.output || {}, null, 2)}</pre>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        </div>
    `;
}

function renderPerformanceMetrics(agentData) {
    const metrics = [
        {
            label: 'Execution Time',
            value: agentData.duration_ms ? `${agentData.duration_ms.toFixed(2)}ms` : 'N/A'
        },
        {
            label: 'Success Rate',
            value: agentData.success !== false ? '100%' : '0%'
        },
        {
            label: 'Tool Calls',
            value: extractToolCallsFromAgent(agentData)?.length || 0
        },
        {
            label: 'Data Size',
            value: calculateDataSize(agentData)
        }
    ];
    
    return `
        <div class="execution-section">
            <div class="section-header">
                <span>📈</span> Performance Metrics
            </div>
            <div class="section-content">
                <div class="performance-metrics">
                    ${metrics.map(metric => `
                        <div class="metric-card">
                            <div class="metric-value">${metric.value}</div>
                            <div class="metric-label">${metric.label}</div>
                        </div>
                    `).join('')}
                </div>
            </div>
        </div>
    `;
}

function renderExecutionMode(agentData) {
    // Try to extract execution mode information using enhanced detection
    let executionMode = agentData.execution_mode ||
                       agentData.output?.execution_mode_used ||
                       agentData.output_data?.execution_mode_used || {};

    // Check execution_context for execution mode
    if (agentData?.execution_context?.execution_mode) {
        executionMode = agentData.execution_context.execution_mode;
    }

    // Check if this is from enhanced_debugging_data with execution mode in processing steps
    if (agentData?.execution_context?.processing_steps) {
        const enhancedTrackingStep = agentData.execution_context.processing_steps.find(
            step => step.description === 'enhanced_tracking_setup'
        );
        if (enhancedTrackingStep?.data?.execution_mode) {
            executionMode = enhancedTrackingStep.data.execution_mode;
        }
    }

    // Check raw_results for execution mode
    if (agentData?.raw_results?.last_output?.execution_mode) {
        const mode = agentData.raw_results.last_output.execution_mode;
        if (mode === 'real') {
            executionMode = { real_llm: true, real_tools: true, real_db: true };
        } else if (mode === 'mock') {
            executionMode = { real_llm: false, real_tools: false, real_db: false };
        }
    }

    // Determine overall mode for display
    const isRealMode = executionMode.real_llm || executionMode.real_tools || executionMode.real_db;
    const overallMode = isRealMode ? 'Real' : 'Mock';

    return `
        <div class="execution-section">
            <div class="section-header">
                <span>⚙️</span> Execution Mode: ${overallMode} Mode
            </div>
            <div class="section-content">
                <div class="execution-mode-indicator">
                    <span class="mode-badge ${executionMode.real_llm ? 'real' : 'mock'}">
                        LLM: ${executionMode.real_llm ? 'Real' : 'Mock'}
                    </span>
                    <span class="mode-badge ${executionMode.real_tools ? 'real' : 'mock'}">
                        Tools: ${executionMode.real_tools ? 'Real' : 'Mock'}
                    </span>
                    <span class="mode-badge ${executionMode.real_db ? 'real' : 'mock'}">
                        Database: ${executionMode.real_db ? 'Real' : 'Mock'}
                    </span>
                </div>
            </div>
        </div>
    `;
}

function renderErrorDetails(agentData) {
    return `
        <div class="execution-section">
            <div class="section-header">
                <span>❌</span> Error Details
            </div>
            <div class="section-content">
                <div class="error-details">
                    <h6>Error Message</h6>
                    <p>${agentData.error || 'Unknown error occurred'}</p>
                    ${agentData.error_context ? `
                        <h6 style="margin-top: 1rem;">Error Context</h6>
                        <pre>${JSON.stringify(agentData.error_context, null, 2)}</pre>
                    ` : ''}
                </div>
            </div>
        </div>
    `;
}

function extractToolCallsFromAgent(agentData) {
    // Try multiple ways to extract tool calls
    if (agentData.tool_calls) return agentData.tool_calls;
    if (agentData.execution_context?.tool_calls) return agentData.execution_context.tool_calls;
    if (agentData.output?.tool_calls) return agentData.output.tool_calls;
    if (agentData.output_data?.tool_calls) return agentData.output_data.tool_calls;
    
    // Look for tool call patterns in the data
    const toolCalls = [];
    const searchData = JSON.stringify(agentData);
    
    // This is a simplified extraction - in a real implementation,
    // you'd have more sophisticated tool call detection
    if (searchData.includes('tool_call') || searchData.includes('_call_tool')) {
        toolCalls.push({
            name: 'Detected Tool Call',
            success: agentData.success !== false,
            input: {},
            output: {}
        });
    }
    
    return toolCalls;
}

function calculateDataSize(agentData) {
    try {
        const dataString = JSON.stringify(agentData);
        const sizeInBytes = new Blob([dataString]).size;

        if (sizeInBytes < 1024) return `${sizeInBytes}B`;
        if (sizeInBytes < 1024 * 1024) return `${(sizeInBytes / 1024).toFixed(1)}KB`;
        return `${(sizeInBytes / (1024 * 1024)).toFixed(1)}MB`;
    } catch (e) {
        return 'N/A';
    }
}

// Comprehensive data extraction functions (from wheel_generation_evaluation_modal.html)
function extractAgentInputData(agentData) {
    // 1. Direct input_data field (new format from AgentCommunication.to_dict())
    if (agentData?.input_data && Object.keys(agentData.input_data).length > 0) {
        return agentData.input_data;
    }

    // 2. Legacy 'input' field (some older formats)
    if (agentData?.input && Object.keys(agentData.input).length > 0) {
        return agentData.input;
    }

    // 3. Execution context input data
    if (agentData?.execution_context?.input_data) {
        return agentData.execution_context.input_data;
    }

    // 4. Check for nested data structures from BenchmarkRun
    if (agentData?.raw_results?.last_output?.agent_communications?.agents) {
        const agents = agentData.raw_results.last_output.agent_communications.agents;
        const currentAgent = agents.find(agent => agent.agent === agentData.agent);
        if (currentAgent?.input_data) {
            return currentAgent.input_data;
        }
        if (currentAgent?.input) {
            return currentAgent.input;
        }
    }

    return {
        message: "No input data available",
        _debug: {
            available_keys: Object.keys(agentData || {}),
            agent_name: agentData?.agent,
            data_structure_type: typeof agentData
        }
    };
}

function extractAgentOutputData(agentData) {
    // 1. Direct output_data field (new format from AgentCommunication.to_dict())
    if (agentData?.output_data && Object.keys(agentData.output_data).length > 0) {
        return agentData.output_data;
    }

    // 2. Legacy 'output' field (some older formats)
    if (agentData?.output && Object.keys(agentData.output).length > 0) {
        return agentData.output;
    }

    // 3. Check if this is wrapped in agent communications format
    if (agentData?.agent_communications?.agents) {
        const currentAgent = agentData.agent_communications.agents.find(
            agent => agent.agent === agentData.agent
        );
        if (currentAgent?.output_data) {
            return currentAgent.output_data;
        }
        if (currentAgent?.output) {
            return currentAgent.output;
        }
    }

    // 4. Check for nested data structures from BenchmarkRun
    if (agentData?.raw_results?.last_output?.agent_communications?.agents) {
        const agents = agentData.raw_results.last_output.agent_communications.agents;
        const currentAgent = agents.find(agent => agent.agent === agentData.agent);
        if (currentAgent?.output_data) {
            return currentAgent.output_data;
        }
        if (currentAgent?.output) {
            return currentAgent.output;
        }
    }

    // 5. NEW: Check enhanced_debugging_data structure (where rich agent data is actually stored)
    if (agentData?.raw_results?.last_output?.enhanced_debugging_data?.agents) {
        const agents = agentData.raw_results.last_output.enhanced_debugging_data.agents;
        const currentAgent = agents.find(agent => agent.agent === agentData.agent);
        if (currentAgent?.output) {
            return currentAgent.output;
        }
        if (currentAgent?.output_data) {
            return currentAgent.output_data;
        }
    }

    // 6. Check if agentData itself is from enhanced_debugging_data structure
    if (agentData?.output && typeof agentData.output === 'object' &&
        (agentData.output.combined_resource_context ||
         agentData.output.engagement_analysis ||
         agentData.output.psychological_assessment ||
         agentData.output.strategy_framework ||
         agentData.output.wheel ||
         agentData.output.ethical_validation)) {
        return agentData.output;
    }

    // 7. If no rich output data found, construct from available metadata
    if (agentData?.performance_metrics || agentData?.data_summary) {
        return {
            data_summary: agentData.data_summary || "Processing completed",
            performance_metrics: agentData.performance_metrics || {},
            success: agentData.success,
            duration_ms: agentData.duration_ms,
            stage: agentData.stage,
            _note: "Fallback data - rich output not found"
        };
    }

    return {
        message: "No output data available",
        _debug: {
            available_keys: Object.keys(agentData || {}),
            agent_name: agentData?.agent,
            data_structure_type: typeof agentData
        }
    };
}

function renderRichOutputData(outputData, agentData) {
    // Check if this is rich agent output data with meaningful content
    const agentName = agentData?.agent || 'unknown';

    // Handle specific agent types with rich data structures
    if (outputData && typeof outputData === 'object') {
        // Resource Agent - combined_resource_context
        if (outputData.combined_resource_context) {
            return renderResourceAgentOutput(outputData.combined_resource_context);
        }

        // Engagement Agent - engagement_analysis
        if (outputData.engagement_analysis) {
            return renderEngagementAgentOutput(outputData.engagement_analysis);
        }

        // Psychological Agent - psychological_assessment
        if (outputData.psychological_assessment) {
            return renderPsychologicalAgentOutput(outputData.psychological_assessment);
        }

        // Strategy Agent - strategy_framework
        if (outputData.strategy_framework) {
            return renderStrategyAgentOutput(outputData.strategy_framework);
        }

        // Activity Agent - wheel
        if (outputData.wheel) {
            return renderActivityAgentOutput(outputData.wheel);
        }

        // Ethical Agent - ethical_validation
        if (outputData.ethical_validation) {
            return renderEthicalAgentOutput(outputData.ethical_validation);
        }
    }

    // Fallback to JSON display for other data
    return `<pre>${JSON.stringify(outputData, null, 2)}</pre>`;
}

function renderResourceAgentOutput(resourceContext) {
    const environment = resourceContext.environment || {};
    const time = resourceContext.time || {};
    const resources = resourceContext.resources || {};
    const analysis = resourceContext.analysis_summary || {};

    return `
        <div class="rich-output-display">
            <h6>🏠 Resource & Environment Analysis</h6>
            <div class="output-sections">
                <div class="output-section">
                    <h7>Environment Context</h7>
                    <ul>
                        <li><strong>Location:</strong> ${environment.current_environment?.name || 'Unknown'}</li>
                        <li><strong>Privacy Level:</strong> ${environment.privacy_level || 'N/A'}/100</li>
                        <li><strong>Space Size:</strong> ${environment.space_size || 'Unknown'}</li>
                        <li><strong>Noise Level:</strong> ${environment.noise_level || 'Unknown'}</li>
                    </ul>
                </div>
                <div class="output-section">
                    <h7>Time Analysis</h7>
                    <ul>
                        <li><strong>Duration:</strong> ${time.duration_minutes || 'N/A'} minutes</li>
                        <li><strong>Flexibility:</strong> ${time.flexibility || 'Unknown'}</li>
                        <li><strong>Urgency:</strong> ${time.urgency || 'Unknown'}</li>
                    </ul>
                </div>
                <div class="output-section">
                    <h7>Resource Inventory</h7>
                    <ul>
                        <li><strong>Available Items:</strong> ${resources.inventory_count || 0}</li>
                        <li><strong>Limitations:</strong> ${resources.limitations_count || 0}</li>
                        <li><strong>Feasibility Score:</strong> ${analysis.overall_feasibility_score || 'N/A'}</li>
                    </ul>
                </div>
            </div>
            <div class="json-fallback">
                <details>
                    <summary>View Raw Data</summary>
                    <pre>${JSON.stringify(resourceContext, null, 2)}</pre>
                </details>
            </div>
        </div>
    `;
}

function renderEngagementAgentOutput(engagementAnalysis) {
    const domainPrefs = engagementAnalysis.domain_preferences || {};
    const completionPatterns = engagementAnalysis.completion_patterns || {};
    const recommendations = engagementAnalysis.recommendations || {};

    return `
        <div class="rich-output-display">
            <h6>📊 Engagement & Pattern Analysis</h6>
            <div class="output-sections">
                <div class="output-section">
                    <h7>Domain Preferences</h7>
                    <ul>
                        ${Object.entries(domainPrefs.preferred_domains || {}).map(([domain, score]) =>
                            `<li><strong>${domain}:</strong> ${(score * 100).toFixed(0)}%</li>`
                        ).join('')}
                    </ul>
                </div>
                <div class="output-section">
                    <h7>Completion Patterns</h7>
                    <ul>
                        <li><strong>Overall Rate:</strong> ${(completionPatterns.completion_rate * 100).toFixed(0)}%</li>
                        <li><strong>Success Factors:</strong> ${completionPatterns.success_factors?.length || 0} identified</li>
                        <li><strong>Abandonment Factors:</strong> ${completionPatterns.abandonment_factors?.length || 0} identified</li>
                    </ul>
                </div>
                <div class="output-section">
                    <h7>Recommendations</h7>
                    <ul>
                        <li><strong>Focus Areas:</strong> ${recommendations.focus_areas?.join(', ') || 'None'}</li>
                        <li><strong>Optimal Timing:</strong> ${recommendations.optimal_timing || 'Any time'}</li>
                    </ul>
                </div>
            </div>
            <div class="json-fallback">
                <details>
                    <summary>View Raw Data</summary>
                    <pre>${JSON.stringify(engagementAnalysis, null, 2)}</pre>
                </details>
            </div>
        </div>
    `;
}

function renderPsychologicalAgentOutput(psychAssessment) {
    const personalityProfile = psychAssessment.personality_profile || {};
    const motivationalFactors = psychAssessment.motivational_factors || {};
    const stressFactors = psychAssessment.stress_factors || {};

    return `
        <div class="rich-output-display">
            <h6>🧠 Psychological Assessment</h6>
            <div class="output-sections">
                <div class="output-section">
                    <h7>Personality Profile</h7>
                    <ul>
                        <li><strong>Primary Traits:</strong> ${personalityProfile.primary_traits?.join(', ') || 'None identified'}</li>
                        <li><strong>Motivation Style:</strong> ${personalityProfile.motivation_style || 'Unknown'}</li>
                        <li><strong>Decision Making:</strong> ${personalityProfile.decision_making_style || 'Unknown'}</li>
                    </ul>
                </div>
                <div class="output-section">
                    <h7>Motivational Factors</h7>
                    <ul>
                        <li><strong>Intrinsic:</strong> ${motivationalFactors.intrinsic?.join(', ') || 'None'}</li>
                        <li><strong>Extrinsic:</strong> ${motivationalFactors.extrinsic?.join(', ') || 'None'}</li>
                        <li><strong>Social:</strong> ${motivationalFactors.social?.join(', ') || 'None'}</li>
                    </ul>
                </div>
                <div class="output-section">
                    <h7>Stress & Barriers</h7>
                    <ul>
                        <li><strong>Stress Triggers:</strong> ${stressFactors.triggers?.join(', ') || 'None identified'}</li>
                        <li><strong>Coping Strategies:</strong> ${stressFactors.coping_strategies?.join(', ') || 'None'}</li>
                    </ul>
                </div>
            </div>
            <div class="json-fallback">
                <details>
                    <summary>View Raw Data</summary>
                    <pre>${JSON.stringify(psychAssessment, null, 2)}</pre>
                </details>
            </div>
        </div>
    `;
}

function renderStrategyAgentOutput(strategyFramework) {
    const approach = strategyFramework.approach || {};
    const timeline = strategyFramework.timeline || {};
    const riskAssessment = strategyFramework.risk_assessment || {};

    return `
        <div class="rich-output-display">
            <h6>🎯 Strategy Framework</h6>
            <div class="output-sections">
                <div class="output-section">
                    <h7>Strategic Approach</h7>
                    <ul>
                        <li><strong>Primary Strategy:</strong> ${approach.primary_strategy || 'Not defined'}</li>
                        <li><strong>Success Metrics:</strong> ${approach.success_metrics?.join(', ') || 'None defined'}</li>
                        <li><strong>Key Milestones:</strong> ${approach.key_milestones?.length || 0} identified</li>
                    </ul>
                </div>
                <div class="output-section">
                    <h7>Timeline & Pacing</h7>
                    <ul>
                        <li><strong>Duration:</strong> ${timeline.total_duration || 'Flexible'}</li>
                        <li><strong>Phases:</strong> ${timeline.phases?.length || 0} planned</li>
                        <li><strong>Critical Path:</strong> ${timeline.critical_path || 'Not defined'}</li>
                    </ul>
                </div>
                <div class="output-section">
                    <h7>Risk Assessment</h7>
                    <ul>
                        <li><strong>Risk Level:</strong> ${riskAssessment.overall_risk_level || 'Unknown'}</li>
                        <li><strong>Mitigation Plans:</strong> ${riskAssessment.mitigation_strategies?.length || 0} strategies</li>
                        <li><strong>Contingencies:</strong> ${riskAssessment.contingency_plans?.length || 0} plans</li>
                    </ul>
                </div>
            </div>
            <div class="json-fallback">
                <details>
                    <summary>View Raw Data</summary>
                    <pre>${JSON.stringify(strategyFramework, null, 2)}</pre>
                </details>
            </div>
        </div>
    `;
}

function renderActivityAgentOutput(wheelData) {
    const activities = wheelData.activities || [];
    const metadata = wheelData.metadata || {};

    return `
        <div class="rich-output-display">
            <h6>🎡 Wheel Activities</h6>
            <div class="output-sections">
                <div class="output-section">
                    <h7>Generated Activities (${activities.length})</h7>
                    <div class="activities-list">
                        ${activities.map((activity, index) => `
                            <div class="activity-item">
                                <strong>${index + 1}. ${activity.title || activity.name || 'Untitled'}</strong>
                                <p>${activity.description || 'No description'}</p>
                                <small>Duration: ${activity.duration || 'Unknown'} | Difficulty: ${activity.difficulty || 'Unknown'}</small>
                            </div>
                        `).join('')}
                    </div>
                </div>
                <div class="output-section">
                    <h7>Wheel Metadata</h7>
                    <ul>
                        <li><strong>Theme:</strong> ${metadata.theme || 'General'}</li>
                        <li><strong>Total Duration:</strong> ${metadata.total_duration || 'Variable'}</li>
                        <li><strong>Difficulty Range:</strong> ${metadata.difficulty_range || 'Mixed'}</li>
                        <li><strong>Categories:</strong> ${metadata.categories?.join(', ') || 'Various'}</li>
                    </ul>
                </div>
            </div>
            <div class="json-fallback">
                <details>
                    <summary>View Raw Data</summary>
                    <pre>${JSON.stringify(wheelData, null, 2)}</pre>
                </details>
            </div>
        </div>
    `;
}

function renderEthicalAgentOutput(ethicalValidation) {
    const validation = ethicalValidation.validation_results || {};
    const concerns = ethicalValidation.ethical_concerns || [];
    const recommendations = ethicalValidation.recommendations || [];

    return `
        <div class="rich-output-display">
            <h6>⚖️ Ethical Validation</h6>
            <div class="output-sections">
                <div class="output-section">
                    <h7>Validation Results</h7>
                    <ul>
                        <li><strong>Overall Score:</strong> ${validation.overall_score || 'N/A'}/100</li>
                        <li><strong>Safety Level:</strong> ${validation.safety_level || 'Unknown'}</li>
                        <li><strong>Appropriateness:</strong> ${validation.appropriateness_score || 'N/A'}/100</li>
                    </ul>
                </div>
                <div class="output-section">
                    <h7>Ethical Concerns (${concerns.length})</h7>
                    <ul>
                        ${concerns.map(concern => `<li>${concern.description || concern}</li>`).join('')}
                    </ul>
                </div>
                <div class="output-section">
                    <h7>Recommendations (${recommendations.length})</h7>
                    <ul>
                        ${recommendations.map(rec => `<li>${rec.description || rec}</li>`).join('')}
                    </ul>
                </div>
            </div>
            <div class="json-fallback">
                <details>
                    <summary>View Raw Data</summary>
                    <pre>${JSON.stringify(ethicalValidation, null, 2)}</pre>
                </details>
            </div>
        </div>
    `;
}
</script>
