<!-- User Environment Detail Modal -->
<div class="modal fade" id="user-environment-detail-modal" tabindex="-1" aria-labelledby="environment-modal-title" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="environment-modal-title">🏠 Environment Details</h5>
                <div class="modal-actions">
                    <button id="refresh-environment-btn" class="btn btn-light btn-sm me-2" title="Refresh environment data">
                        🔄 Refresh
                    </button>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
            </div>
            <div class="modal-body p-0">
                <div id="environment-modal-body" class="environment-modal-body">
                    <!-- Environment content will be loaded here -->
                    <div class="modal-loading text-center p-5">
                        <div class="spinner-border text-success" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-3">Loading environment details...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Environment Modal Custom Styles */
.environment-modal-body {
    max-height: 75vh;
    overflow-y: auto;
    padding: 0;
}

#user-environment-detail-modal {
    z-index: 9999 !important;
}

.environment-modal-body .modal-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.environment-section {
    border-bottom: 1px solid #e9ecef;
    padding: 25px;
    position: relative;
}

.environment-section:last-child {
    border-bottom: none;
}

.environment-section::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border-radius: 2px;
}

.environment-section h3 {
    margin: 0 0 20px 0;
    color: #495057;
    font-size: 1.4em;
    display: flex;
    align-items: center;
    gap: 10px;
}

.environment-section-icon {
    font-size: 1.2em;
}

.environment-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.environment-field {
    margin-bottom: 15px;
}

.environment-field label {
    display: block;
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
    font-size: 14px;
}

.environment-field-value {
    padding: 8px 12px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    min-height: 20px;
    font-size: 14px;
}

.environment-field-value.empty {
    color: #6c757d;
    font-style: italic;
}

/* Property bars for numeric values */
.property-bar {
    position: relative;
    width: 120px;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    display: inline-block;
    margin-left: 10px;
}

.property-fill {
    height: 100%;
    background: linear-gradient(90deg, #dc3545 0%, #ffc107 50%, #28a745 100%);
    transition: width 0.3s ease;
    border-radius: 10px;
}

.property-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 10px;
    font-weight: bold;
    color: #333;
    z-index: 1;
}

/* Badge styles */
.env-property-badge {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    margin: 2px;
}

.env-property-badge.physical {
    background: #e7f3ff;
    color: #0066cc;
}

.env-property-badge.social {
    background: #fff3cd;
    color: #856404;
}

.env-property-badge.activity {
    background: #d4edda;
    color: #155724;
}

.env-property-badge.psychological {
    background: #f8d7da;
    color: #721c24;
}

/* Resource cards */
.resource-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
}

.resource-card h5 {
    margin: 0 0 10px 0;
    color: #495057;
}

.resource-card .resource-meta {
    font-size: 12px;
    color: #6c757d;
}

/* Emotional associations */
.emotion-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
    margin-top: 10px;
}

.emotion-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.emotion-name {
    font-weight: 600;
    text-transform: capitalize;
}

.emotion-value {
    font-size: 12px;
    font-weight: bold;
    color: #007bff;
}

/* Domain support visualization */
.domain-support-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.domain-support-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 12px;
    text-align: center;
}

.domain-name {
    font-weight: 600;
    margin-bottom: 8px;
    color: #495057;
}

.domain-support-bar {
    width: 100%;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.domain-support-fill {
    height: 100%;
    background: linear-gradient(90deg, #dc3545 0%, #ffc107 50%, #28a745 100%);
    transition: width 0.3s ease;
}

.domain-support-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 11px;
    font-weight: bold;
    color: #333;
    z-index: 1;
}

/* Time availability visualization */
.time-availability {
    margin-top: 15px;
}

.time-slot {
    display: inline-block;
    padding: 4px 8px;
    margin: 2px;
    background: #e7f3ff;
    color: #0066cc;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
}

/* Responsive design */
@media (max-width: 768px) {
    .environment-grid {
        grid-template-columns: 1fr;
    }
    
    .domain-support-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .emotion-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
/**
 * User Environment Detail Modal Management
 */
let userEnvironmentModalInstance = null;

window.openEnvironmentDetailModal = function(environmentId = null) {
    const modalElement = document.getElementById('user-environment-detail-modal');
    const modalBody = document.getElementById('environment-modal-body');
    const title = document.getElementById('environment-modal-title');

    // Initialize Bootstrap modal instance if it doesn't exist
    if (!userEnvironmentModalInstance) {
        userEnvironmentModalInstance = new bootstrap.Modal(modalElement);
    }

    // Show modal
    userEnvironmentModalInstance.show();

    // Set title
    title.textContent = environmentId ? '🏠 Environment Details' : '🏠 All Environments';

    // Show loading state
    modalBody.innerHTML = `
        <div class="modal-loading text-center p-5">
            <div class="spinner-border text-success" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3">Loading environment details...</p>
        </div>
    `;

    // Load environment data
    loadEnvironmentData(environmentId);
};

async function loadEnvironmentData(environmentId) {
    try {
        // Get the profile ID from the current context
        const profileId = getCurrentProfileId();
        if (!profileId) {
            throw new Error('Profile ID not available');
        }

        // Build API URL
        let apiUrl;
        if (environmentId) {
            apiUrl = `/admin/user-profiles/api/${profileId}/environments/${environmentId}/`;
        } else {
            apiUrl = `/admin/user-profiles/api/${profileId}/environments/`;
        }

        // Fetch environment data from API
        const response = await fetch(apiUrl, {
            method: 'GET',
            headers: {
                'X-CSRFToken': getCsrfToken(),
                'Content-Type': 'application/json',
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const environmentData = await response.json();
        renderEnvironmentData(environmentData, environmentId);

    } catch (error) {
        console.error('Error loading environment data:', error);
        document.getElementById('environment-modal-body').innerHTML = `
            <div class="profile-error">
                <h4>⚠️ Error Loading Environment</h4>
                <p>${error.message}</p>
                <button onclick="loadEnvironmentData(${environmentId})" class="btn btn-success">
                    🔄 Retry
                </button>
            </div>
        `;
    }
}

function getCurrentProfileId() {
    // Try to get profile ID from various sources
    const modal = document.getElementById('user-profile-detail-modal');
    if (modal && modal.getAttribute('data-profile-id')) {
        return modal.getAttribute('data-profile-id');
    }

    // Try to get from URL or other context
    const urlParams = new URLSearchParams(window.location.search);
    const profileId = urlParams.get('profile_id');
    if (profileId) {
        return profileId;
    }

    // Try to get from the currently opened profile modal
    const profileModalBody = document.getElementById('profile-modal-body');
    if (profileModalBody && profileModalBody.dataset.profileId) {
        return profileModalBody.dataset.profileId;
    }

    return null;
}

function getCsrfToken() {
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
    return csrfToken ? csrfToken.value : '';
}

function renderEnvironmentData(data, environmentId) {
    const modalBody = document.getElementById('environment-modal-body');

    if (environmentId) {
        // Render single environment details
        renderSingleEnvironment(data);
    } else {
        // Render all environments
        renderAllEnvironments(data);
    }
}

function renderSingleEnvironment(environment) {
    const modalBody = document.getElementById('environment-modal-body');

    modalBody.innerHTML = `
        <!-- Environment Overview -->
        <div class="environment-section">
            <h3><span class="environment-section-icon">🏠</span>Environment Overview</h3>
            <div class="environment-grid">
                <div class="environment-field">
                    <label>Environment Name</label>
                    <div class="environment-field-value">${environment.environment_name || 'Not set'}</div>
                </div>
                <div class="environment-field">
                    <label>Description</label>
                    <div class="environment-field-value">${environment.environment_description || 'No description available'}</div>
                </div>
                <div class="environment-field">
                    <label>Generic Type</label>
                    <div class="environment-field-value">
                        ${environment.generic_environment ?
                            `<strong>${environment.generic_environment.name}</strong><br><small>${environment.generic_environment.description}</small>` :
                            'Not specified'
                        }
                    </div>
                </div>
                <div class="environment-field">
                    <label>Current Status</label>
                    <div class="environment-field-value">
                        <span class="env-badge ${environment.is_current ? 'env-current' : 'env-inactive'}">
                            ${environment.is_current ? '✅ Active Environment' : '⭕ Inactive Environment'}
                        </span>
                    </div>
                </div>
                ${environment.effective_start ? `
                    <div class="environment-field">
                        <label>Available From</label>
                        <div class="environment-field-value">${new Date(environment.effective_start).toLocaleDateString()}</div>
                    </div>
                ` : ''}
                ${environment.effective_end ? `
                    <div class="environment-field">
                        <label>Available Until</label>
                        <div class="environment-field-value">${new Date(environment.effective_end).toLocaleDateString()}</div>
                    </div>
                ` : ''}
            </div>
        </div>

        ${environment.physical_properties ? `
        <!-- Physical Properties -->
        <div class="environment-section">
            <h3><span class="environment-section-icon">🏗️</span>Physical Properties</h3>
            <div class="environment-grid">
                <div class="environment-field">
                    <label>Rurality Level</label>
                    <div class="environment-field-value">
                        ${getRuralityDescription(environment.physical_properties.rurality)} (${environment.physical_properties.rurality}/100)
                        <div class="property-bar">
                            <div class="property-fill" style="width: ${environment.physical_properties.rurality}%"></div>
                            <span class="property-text">${environment.physical_properties.rurality}%</span>
                        </div>
                    </div>
                </div>
                <div class="environment-field">
                    <label>Noise Level</label>
                    <div class="environment-field-value">
                        ${getNoiseDescription(environment.physical_properties.noise_level)} (${environment.physical_properties.noise_level}/100)
                        <div class="property-bar">
                            <div class="property-fill" style="width: ${environment.physical_properties.noise_level}%"></div>
                            <span class="property-text">${environment.physical_properties.noise_level}%</span>
                        </div>
                    </div>
                </div>
                <div class="environment-field">
                    <label>Light Quality</label>
                    <div class="environment-field-value">
                        ${getLightDescription(environment.physical_properties.light_quality)} (${environment.physical_properties.light_quality}/100)
                        <div class="property-bar">
                            <div class="property-fill" style="width: ${environment.physical_properties.light_quality}%"></div>
                            <span class="property-text">${environment.physical_properties.light_quality}%</span>
                        </div>
                    </div>
                </div>
                <div class="environment-field">
                    <label>Temperature</label>
                    <div class="environment-field-value">
                        <span class="env-property-badge physical">Moderate</span>
                    </div>
                </div>
                <div class="environment-field">
                    <label>Accessibility</label>
                    <div class="environment-field-value">
                        Excellent (95/100)
                        <div class="property-bar">
                            <div class="property-fill" style="width: 95%"></div>
                            <span class="property-text">95%</span>
                        </div>
                    </div>
                </div>
                <div class="environment-field">
                    <label>Air Quality</label>
                    <div class="environment-field-value">
                        Good (80/100)
                        <div class="property-bar">
                            <div class="property-fill" style="width: 80%"></div>
                            <span class="property-text">80%</span>
                        </div>
                    </div>
                </div>
                <div class="environment-field">
                    <label>Natural Elements</label>
                    <div class="environment-field-value">
                        <span class="env-property-badge physical">✅ Present</span>
                        <small>Plants and natural light</small>
                    </div>
                </div>
                <div class="environment-field">
                    <label>Surface Type</label>
                    <div class="environment-field-value">
                        <span class="env-property-badge physical">Mixed</span>
                        <small>Hardwood floors, work surfaces</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Social Context -->
        <div class="environment-section">
            <h3><span class="environment-section-icon">👥</span>Social Context</h3>
            <div class="environment-grid">
                <div class="environment-field">
                    <label>Privacy Level</label>
                    <div class="environment-field-value">
                        High Privacy (85/100)
                        <div class="property-bar">
                            <div class="property-fill" style="width: 85%"></div>
                            <span class="property-text">85%</span>
                        </div>
                    </div>
                </div>
                <div class="environment-field">
                    <label>Typical Occupancy</label>
                    <div class="environment-field-value">
                        Low (15/100)
                        <div class="property-bar">
                            <div class="property-fill" style="width: 15%"></div>
                            <span class="property-text">15%</span>
                        </div>
                    </div>
                </div>
                <div class="environment-field">
                    <label>Social Interaction</label>
                    <div class="environment-field-value">
                        Minimal (10/100)
                        <div class="property-bar">
                            <div class="property-fill" style="width: 10%"></div>
                            <span class="property-text">10%</span>
                        </div>
                    </div>
                </div>
                <div class="environment-field">
                    <label>Formality Level</label>
                    <div class="environment-field-value">
                        Casual (20/100)
                        <div class="property-bar">
                            <div class="property-fill" style="width: 20%"></div>
                            <span class="property-text">20%</span>
                        </div>
                    </div>
                </div>
                <div class="environment-field">
                    <label>Safety Level</label>
                    <div class="environment-field-value">
                        Very Safe (95/100)
                        <div class="property-bar">
                            <div class="property-fill" style="width: 95%"></div>
                            <span class="property-text">95%</span>
                        </div>
                    </div>
                </div>
                <div class="environment-field">
                    <label>Supervision</label>
                    <div class="environment-field-value">
                        <span class="env-property-badge social">None</span>
                        <small>Complete autonomy</small>
                    </div>
                </div>
                <div class="environment-field">
                    <label>Cultural Diversity</label>
                    <div class="environment-field-value">
                        Moderate (50/100)
                        <div class="property-bar">
                            <div class="property-fill" style="width: 50%"></div>
                            <span class="property-text">50%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Activity Support -->
        <div class="environment-section">
            <h3><span class="environment-section-icon">⚡</span>Activity Support</h3>
            <div class="environment-grid">
                <div class="environment-field">
                    <label>Digital Connectivity</label>
                    <div class="environment-field-value">
                        Excellent (95/100)
                        <div class="property-bar">
                            <div class="property-fill" style="width: 95%"></div>
                            <span class="property-text">95%</span>
                        </div>
                    </div>
                </div>
                <div class="environment-field">
                    <label>Resource Availability</label>
                    <div class="environment-field-value">
                        High (80/100)
                        <div class="property-bar">
                            <div class="property-fill" style="width: 80%"></div>
                            <span class="property-text">80%</span>
                        </div>
                    </div>
                </div>
                <div class="environment-field">
                    <label>Time Availability</label>
                    <div class="environment-field-value time-availability">
                        <span class="time-slot">Weekday Evenings</span>
                        <span class="time-slot">Weekend Mornings</span>
                        <span class="time-slot">Weekend Afternoons</span>
                    </div>
                </div>
            </div>

            <!-- Domain Support -->
            <h4>Domain-Specific Support</h4>
            <div class="domain-support-grid">
                <div class="domain-support-item">
                    <div class="domain-name">Creative</div>
                    <div class="domain-support-bar">
                        <div class="domain-support-fill" style="width: 90%"></div>
                        <span class="domain-support-text">90%</span>
                    </div>
                </div>
                <div class="domain-support-item">
                    <div class="domain-name">Technical</div>
                    <div class="domain-support-bar">
                        <div class="domain-support-fill" style="width: 95%"></div>
                        <span class="domain-support-text">95%</span>
                    </div>
                </div>
                <div class="domain-support-item">
                    <div class="domain-name">Physical</div>
                    <div class="domain-support-bar">
                        <div class="domain-support-fill" style="width: 40%"></div>
                        <span class="domain-support-text">40%</span>
                    </div>
                </div>
                <div class="domain-support-item">
                    <div class="domain-name">Social</div>
                    <div class="domain-support-bar">
                        <div class="domain-support-fill" style="width: 20%"></div>
                        <span class="domain-support-text">20%</span>
                    </div>
                </div>
                <div class="domain-support-item">
                    <div class="domain-name">Learning</div>
                    <div class="domain-support-bar">
                        <div class="domain-support-fill" style="width: 85%"></div>
                        <span class="domain-support-text">85%</span>
                    </div>
                </div>
                <div class="domain-support-item">
                    <div class="domain-name">Wellness</div>
                    <div class="domain-support-bar">
                        <div class="domain-support-fill" style="width: 60%"></div>
                        <span class="domain-support-text">60%</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Psychological Qualities -->
        <div class="environment-section">
            <h3><span class="environment-section-icon">🧠</span>Psychological Qualities</h3>
            <div class="environment-grid">
                <div class="environment-field">
                    <label>Restorative Quality</label>
                    <div class="environment-field-value">
                        High (80/100)
                        <div class="property-bar">
                            <div class="property-fill" style="width: 80%"></div>
                            <span class="property-text">80%</span>
                        </div>
                    </div>
                </div>
                <div class="environment-field">
                    <label>Stimulation Level</label>
                    <div class="environment-field-value">
                        Moderate (55/100)
                        <div class="property-bar">
                            <div class="property-fill" style="width: 55%"></div>
                            <span class="property-text">55%</span>
                        </div>
                    </div>
                </div>
                <div class="environment-field">
                    <label>Aesthetic Appeal</label>
                    <div class="environment-field-value">
                        High (85/100)
                        <div class="property-bar">
                            <div class="property-fill" style="width: 85%"></div>
                            <span class="property-text">85%</span>
                        </div>
                    </div>
                </div>
                <div class="environment-field">
                    <label>Novelty Level</label>
                    <div class="environment-field-value">
                        Low (15/100)
                        <div class="property-bar">
                            <div class="property-fill" style="width: 15%"></div>
                            <span class="property-text">15%</span>
                        </div>
                    </div>
                </div>
                <div class="environment-field">
                    <label>Comfort Level</label>
                    <div class="environment-field-value">
                        Very High (90/100)
                        <div class="property-bar">
                            <div class="property-fill" style="width: 90%"></div>
                            <span class="property-text">90%</span>
                        </div>
                    </div>
                </div>
                <div class="environment-field">
                    <label>Personal Significance</label>
                    <div class="environment-field-value">
                        Very High (95/100)
                        <div class="property-bar">
                            <div class="property-fill" style="width: 95%"></div>
                            <span class="property-text">95%</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Emotional Associations -->
            <h4>Emotional Associations</h4>
            <div class="emotion-grid">
                <div class="emotion-item">
                    <span class="emotion-name">Focus</span>
                    <span class="emotion-value">85%</span>
                </div>
                <div class="emotion-item">
                    <span class="emotion-name">Creativity</span>
                    <span class="emotion-value">90%</span>
                </div>
                <div class="emotion-item">
                    <span class="emotion-name">Calm</span>
                    <span class="emotion-value">80%</span>
                </div>
                <div class="emotion-item">
                    <span class="emotion-name">Productivity</span>
                    <span class="emotion-value">88%</span>
                </div>
                <div class="emotion-item">
                    <span class="emotion-name">Inspiration</span>
                    <span class="emotion-value">85%</span>
                </div>
                <div class="emotion-item">
                    <span class="emotion-name">Satisfaction</span>
                    <span class="emotion-value">92%</span>
                </div>
            </div>
        </div>

        <!-- Available Resources -->
        <div class="environment-section">
            <h3><span class="environment-section-icon">🛠️</span>Available Resources</h3>
            <p class="text-muted">Resources specifically available in this environment</p>

            <div class="resource-card">
                <h5>3D Printer</h5>
                <div class="resource-meta">
                    <span class="resource-badge available">Available</span>
                    <span class="resource-badge condition-good">Good Condition</span>
                    <span class="resource-badge transport-fixed">Fixed Location</span>
                </div>
            </div>

            <div class="resource-card">
                <h5>Electronics Workbench</h5>
                <div class="resource-meta">
                    <span class="resource-badge available">Available</span>
                    <span class="resource-badge condition-excellent">Excellent</span>
                    <span class="resource-badge transport-fixed">Fixed Location</span>
                </div>
            </div>

            <div class="resource-card">
                <h5>Hand Tools Set</h5>
                <div class="resource-meta">
                    <span class="resource-badge unavailable">Unavailable</span>
                    <span class="resource-badge condition-fair">Fair Condition</span>
                    <span class="resource-badge transport-portable">Portable</span>
                </div>
            </div>

            <button class="btn btn-primary mt-3" onclick="openInventoryDetailModal()">
                📦 View Complete Inventory
            </button>
        </div>

        <!-- Environment Management -->
        <div class="environment-section">
            <h3><span class="environment-section-icon">⚙️</span>Environment Management</h3>
            <div class="environment-grid">
                <div class="environment-field">
                    <button class="btn btn-primary">✏️ Edit Properties</button>
                </div>
                <div class="environment-field">
                    <button class="btn btn-secondary">📊 Generate Report</button>
                </div>
                <div class="environment-field">
                    <button class="btn btn-info">🔄 Update Resources</button>
                </div>
                <div class="environment-field">
                    <button class="btn btn-warning">⚠️ Report Issues</button>
                </div>
            </div>
        </div>
    `;
}

// Modal initialization
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('user-environment-detail-modal');

    modal.addEventListener('hidden.bs.modal', function () {
        // Clear modal content when closed
        const modalBody = document.getElementById('environment-modal-body');
        modalBody.innerHTML = `
            <div class="modal-loading text-center p-5">
                <div class="spinner-border text-success" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-3">Loading environment details...</p>
            </div>
        `;
    });
});

// Helper functions for environment data descriptions
function getRuralityDescription(value) {
    if (value <= 20) return 'Urban';
    if (value <= 40) return 'Suburban';
    if (value <= 60) return 'Semi-rural';
    if (value <= 80) return 'Rural';
    return 'Remote';
}

function getNoiseDescription(value) {
    if (value <= 20) return 'Very Quiet';
    if (value <= 40) return 'Quiet';
    if (value <= 60) return 'Moderate';
    if (value <= 80) return 'Noisy';
    return 'Very Noisy';
}

function getLightDescription(value) {
    if (value <= 20) return 'Poor';
    if (value <= 40) return 'Dim';
    if (value <= 60) return 'Adequate';
    if (value <= 80) return 'Good';
    return 'Excellent';
}

function getTemperatureDescription(value) {
    if (value <= 20) return 'Cold';
    if (value <= 40) return 'Cool';
    if (value <= 60) return 'Comfortable';
    if (value <= 80) return 'Warm';
    return 'Hot';
}

function getAccessibilityDescription(value) {
    if (value <= 20) return 'Poor Access';
    if (value <= 40) return 'Limited Access';
    if (value <= 60) return 'Moderate Access';
    if (value <= 80) return 'Good Access';
    return 'Excellent Access';
}

function getAirQualityDescription(value) {
    if (value <= 20) return 'Poor';
    if (value <= 40) return 'Fair';
    if (value <= 60) return 'Good';
    if (value <= 80) return 'Very Good';
    return 'Excellent';
}

function renderAllEnvironments(data) {
    const modalBody = document.getElementById('environment-modal-body');

    if (!data.environments || data.environments.length === 0) {
        modalBody.innerHTML = `
            <div class="environment-section">
                <h3><span class="environment-section-icon">🏠</span>No Environments Found</h3>
                <p>This user profile has no environments configured.</p>
            </div>
        `;
        return;
    }

    let environmentsHtml = `
        <div class="environment-section">
            <h3><span class="environment-section-icon">🏠</span>All Environments (${data.environments.length})</h3>
            <div class="environments-grid">
    `;

    data.environments.forEach(env => {
        environmentsHtml += `
            <div class="environment-card ${env.is_current ? 'current-environment' : ''}">
                <div class="environment-card-header">
                    <h4>${env.environment_name}</h4>
                    ${env.is_current ? '<span class="env-badge env-current">CURRENT</span>' : ''}
                </div>
                <div class="environment-card-body">
                    <p>${env.environment_description || 'No description'}</p>
                    ${env.generic_environment ? `<small><strong>Type:</strong> ${env.generic_environment.name}</small>` : ''}
                    <div class="environment-stats">
                        <span class="stat-badge">📦 ${env.resources ? env.resources.length : 0} resources</span>
                        <span class="stat-badge">🏷️ ${env.domains ? env.domains.length : 0} domains</span>
                    </div>
                </div>
                <div class="environment-card-actions">
                    <button class="btn btn-sm btn-primary" onclick="openEnvironmentDetailModal(${env.id})">
                        👁️ View Details
                    </button>
                </div>
            </div>
        `;
    });

    environmentsHtml += `
            </div>
        </div>
    `;

    modalBody.innerHTML = environmentsHtml;
}
</script>
