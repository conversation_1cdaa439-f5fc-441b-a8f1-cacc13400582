<!-- backend/templates/admin_tools/user_profile_management.html -->
{% extends "admin/base_site.html" %}
{% load static i18n %}

{% block title %}User Profile Management | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block extrahead %}
{{ block.super }}
<!-- Bootstrap CSS and JS for modal functionality -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<!-- Font Awesome for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<!-- Admin Tools Static Files -->
{{ media }}
{% endblock %}

{% block extrastyle %}
{{ block.super }}
<!-- User Profile Management Styles -->
<link rel="stylesheet" type="text/css" href="{% static 'admin_tools/css/user_profile_management.css' %}">
{% endblock %}

{% block content %}
<div class="user-profile-management profile-container">
    <div class="card">
        <h1>👤 User Profile Management</h1>
        <p>Comprehensive user profile management with detailed information display and editing capabilities.</p>

        <!-- Schema Management Section -->
        <div class="mt-4">
            <h3>🔧 Schema Management & Validation</h3>
            <div class="row">
                <div class="col-md-4">
                    <button type="button" class="btn btn-info btn-sm" onclick="validateSchema()">
                        <i class="fas fa-check-circle"></i> Validate Schema Pipeline
                    </button>
                </div>
                <div class="col-md-4">
                    <button type="button" class="btn btn-warning btn-sm" onclick="viewSchema()">
                        <i class="fas fa-eye"></i> View Current Schema
                    </button>
                </div>
                <div class="col-md-4">
                    <button type="button" class="btn btn-secondary btn-sm" onclick="generateMismatchReport()">
                        <i class="fas fa-file-alt"></i> Generate Mismatch Report
                    </button>
                </div>
            </div>
            <div id="schemaValidationResults" class="mt-3" style="display: none;">
                <!-- Validation results will be displayed here -->
            </div>
        </div>
        <hr>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <span class="stat-number">{{ total_profiles }}</span>
                <div class="stat-label">Total Profiles</div>
            </div>
            <div class="stat-card">
                <span class="stat-number">{{ real_profiles }}</span>
                <div class="stat-label">Real Users</div>
            </div>
            <div class="stat-card">
                <span class="stat-number">{{ test_profiles }}</span>
                <div class="stat-label">Test Profiles</div>
            </div>
            <div class="stat-card">
                <span class="stat-number">{{ profiles_with_demographics }}</span>
                <div class="stat-label">With Demographics</div>
            </div>
            <div class="stat-card">
                <span class="stat-number">{{ profiles_with_environment }}</span>
                <div class="stat-label">With Environment</div>
            </div>
        </div>
    </div>

    <!-- Catalog Validation Section -->
    <div class="card">
        <h2>📋 Catalog Validation</h2>
        <p>Validate catalog files before importing user profiles to ensure data integrity.</p>

        <div class="row">
            <div class="col-md-6">
                <button type="button" class="btn btn-primary" onclick="validateAllCatalogs()">
                    Validate All Catalogs
                </button>
                <button type="button" class="btn btn-outline-secondary" onclick="refreshCatalogStatus()">
                    Refresh Status
                </button>
                <a href="/admin/commands/" class="btn btn-outline-info">
                    Command Management
                </a>
            </div>
            <div class="col-md-6">
                <div id="catalog-validation-status" class="mt-2">
                    <em>Click "Validate All Catalogs" to check status</em>
                </div>
            </div>
        </div>

        <div id="catalog-validation-results" class="mt-3" style="display: none;">
            <!-- Validation results will be displayed here -->
        </div>
    </div>

    <!-- Filters -->
    <div class="card">
        <div class="filters">
            <form method="get" class="filter-form">
                <div class="form-group">
                    <label for="search">Search:</label>
                    <input type="text" id="search" name="search" value="{{ search_query }}" 
                           placeholder="Name, username, email..." class="search-box">
                </div>
                <div class="form-group">
                    <label for="profile_type">Profile Type:</label>
                    <select id="profile_type" name="profile_type">
                        <option value="">All Types</option>
                        <option value="real" {% if profile_type == 'real' %}selected{% endif %}>Real Users</option>
                        <option value="test" {% if profile_type == 'test' %}selected{% endif %}>Test Profiles</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="has_demographics">Demographics:</label>
                    <select id="has_demographics" name="has_demographics">
                        <option value="">All</option>
                        <option value="yes" {% if has_demographics == 'yes' %}selected{% endif %}>Has Demographics</option>
                        <option value="no" {% if has_demographics == 'no' %}selected{% endif %}>No Demographics</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="has_environment">Environment:</label>
                    <select id="has_environment" name="has_environment">
                        <option value="">All</option>
                        <option value="yes" {% if has_environment == 'yes' %}selected{% endif %}>Has Environment</option>
                        <option value="no" {% if has_environment == 'no' %}selected{% endif %}>No Environment</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="completeness">Completeness:</label>
                    <select id="completeness" name="completeness">
                        <option value="">All Levels</option>
                        <option value="low" {% if completeness == 'low' %}selected{% endif %}>Low (&lt; 30%)</option>
                        <option value="medium" {% if completeness == 'medium' %}selected{% endif %}>Medium (30-70%)</option>
                        <option value="high" {% if completeness == 'high' %}selected{% endif %}>High (&gt; 70%)</option>
                    </select>
                </div>
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">Filter</button>
                    <a href="{% url 'admin:user_profile_management' %}" class="btn btn-secondary">Clear</a>
                </div>
            </form>
        </div>
    </div>

    <!-- Import User Profile -->    
    <div class="card import-profile-card">
        <h3>📤 Import User Profile</h3>
        <p>Import a complete user profile from JSON data (supports AI-generated profiles and manual exports).</p>
        
        <div class="import-section">
            <div class="import-method-tabs">
                <button class="tab-btn active" data-tab="json-upload">📄 JSON Upload</button>
                <button class="tab-btn" data-tab="json-paste">📝 JSON Paste</button>
                <button class="tab-btn" data-tab="ai-generate">🤖 AI Generate</button>
            </div>
            
            <!-- JSON Upload Tab -->
            <div class="tab-content active" data-tab="json-upload">
                <div class="upload-area" id="json-upload-area">
                    <div class="upload-placeholder">
                        <div class="upload-icon">📁</div>
                        <div class="upload-text">
                            <strong>Drop JSON file here or click to browse</strong>
                            <br><small>Supports .json files with complete user profile data</small>
                        </div>
                    </div>
                    <input type="file" id="json-file-input" accept=".json" style="display: none;">
                </div>
            </div>
            
            <!-- JSON Paste Tab -->
            <div class="tab-content" data-tab="json-paste">
                <div class="json-input-section">
                    <label for="json-textarea">Paste JSON Profile Data:</label>
                    <textarea id="json-textarea" placeholder="Paste your JSON user profile data here..."></textarea>
                    <div class="json-validation" id="json-validation"></div>
                </div>
            </div>
            
            <!-- AI Generate Tab -->
            <div class="tab-content" data-tab="ai-generate">
                <div class="ai-generate-section">
                    <label for="questionnaire-data">Questionnaire Responses:</label>
                    <textarea id="questionnaire-data" placeholder="Paste questionnaire responses or interview transcript here..."></textarea>
                    <div class="ai-options">
                        <label>
                            <input type="checkbox" id="include-archetype-analysis" checked>
                            Include Archetype Analysis
                        </label>
                        <label>
                            <input type="checkbox" id="include-environment-inference" checked>
                            Infer Environment Details
                        </label>
                        <label>
                            <input type="checkbox" id="include-goals-extraction" checked>
                            Extract Goals and Aspirations
                        </label>
                    </div>
                    <button class="btn btn-primary" id="generate-profile-btn">🤖 Generate Profile</button>
                </div>
            </div>
        </div>
        
        <!-- Profile Preview -->
        <div class="profile-preview" id="profile-preview" style="display: none;">
            <h4>📋 Profile Preview</h4>
            <div class="preview-summary" id="preview-summary"></div>
            <div class="preview-details" id="preview-details"></div>
        </div>
        
        <!-- Import Controls -->
        <div class="import-controls" id="import-controls" style="display: none;">
            <div class="import-options">
                <label>
                    <input type="checkbox" id="overwrite-existing" checked>
                    Overwrite existing profile if username/email exists
                </label>
                <label>
                    <input type="checkbox" id="validate-before-import" checked>
                    Validate data before importing
                </label>
                <label>
                    <input type="checkbox" id="create-backup" checked>
                    Create backup of existing data
                </label>
            </div>
            
            <div class="import-buttons">
                <button class="btn btn-warning" id="validate-only-btn">🔍 Validate Only</button>
                <button class="btn btn-success" id="import-profile-btn">✅ Import Profile</button>
                <button class="btn btn-secondary" id="clear-import-btn">🗑️ Clear</button>
                <button class="btn btn-info" id="download-schema-btn">📊 Download Schema</button>
                <button class="btn btn-outline-info" id="validate-schema-btn">🔧 Validate Schema</button>
            </div>
        </div>
        
        <!-- Import Status -->
        <div class="import-status" id="import-status" style="display: none;">
            <div class="status-message" id="status-message"></div>
            <div class="status-details" id="status-details"></div>
        </div>
    </div>

    <!-- Import History -->
    <div class="card">
        <h3>📋 Import History</h3>
        <p>Track and manage profile import operations with detailed status and performance metrics.</p>

        <div class="import-history-controls">
            <div class="history-filters">
                <select id="history-status-filter">
                    <option value="">All Statuses</option>
                    <option value="completed">Completed</option>
                    <option value="failed">Failed</option>
                    <option value="partial">Partial</option>
                    <option value="in_progress">In Progress</option>
                </select>

                <select id="history-type-filter">
                    <option value="">All Types</option>
                    <option value="single">Single Import</option>
                    <option value="batch">Batch Import</option>
                    <option value="validation_only">Validation Only</option>
                </select>

                <input type="text" id="history-user-filter" placeholder="Filter by user...">

                <button class="btn btn-primary" id="refresh-history-btn">🔄 Refresh</button>
                <button class="btn btn-secondary" id="clear-history-btn">🗑️ Clear Old Records</button>
            </div>
        </div>

        <div class="import-history-table-container">
            <table class="import-history-table" id="import-history-table">
                <thead>
                    <tr>
                        <th>Import ID</th>
                        <th>Type</th>
                        <th>Status</th>
                        <th>Profile Name</th>
                        <th>Initiated By</th>
                        <th>Started</th>
                        <th>Duration</th>
                        <th>Success Rate</th>
                        <th>Quality Score</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="import-history-tbody">
                    <tr>
                        <td colspan="10" class="loading">
                            <div class="loader"></div>
                            Loading import history...
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="import-history-pagination">
            <button class="btn btn-outline-secondary" id="history-prev-btn" disabled>← Previous</button>
            <span id="history-page-info">Page 1 of 1</span>
            <button class="btn btn-outline-secondary" id="history-next-btn" disabled>Next →</button>
        </div>
    </div>

    <!-- Batch Actions -->
    <div class="card batch-actions-card" style="display: none;">
        <div class="batch-actions">
            <div class="batch-info">
                <span id="selected-count">0 profiles selected</span>
            </div>
            <div class="batch-buttons">
                <button id="select-all-btn" class="btn btn-outline-primary">Select All</button>
                <button id="deselect-all-btn" class="btn btn-outline-secondary">Deselect All</button>
                <button id="batch-delete-btn" class="btn btn-danger" disabled>🗑️ Delete Selected</button>
                <button id="batch-export-btn" class="btn btn-success" disabled>📊 Export Selected</button>
            </div>
        </div>
    </div>

    <!-- Profile Table -->
    <div class="card">
        <div class="table-container">
            {% if profiles %}
            <table class="profile-table">
                <thead>
                    <tr>
                        <th>
                            <input type="checkbox" id="select-all-checkbox" title="Select all profiles">
                        </th>
                        <th>Profile</th>
                        <th>Type</th>
                        <th>User Account</th>
                        <th>Demographics</th>
                        <th>Environment</th>
                        <th>Data Counts</th>
                        <th>Completeness</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for profile in profiles %}
                    <tr data-profile-id="{{ profile.id }}">
                        <td>
                            <input type="checkbox" class="profile-checkbox" value="{{ profile.id }}" title="Select this profile">
                        </td>
                        <td>
                            <strong>{{ profile.profile_name }}</strong>
                            <br>
                            <small>ID: {{ profile.id }}</small>
                        </td>
                        <td>
                            <span class="profile-type-badge {% if profile.is_real %}profile-type-real{% else %}profile-type-test{% endif %}">
                                {{ profile.profile_type }}
                            </span>
                        </td>
                        <td>
                            <strong>{{ profile.user.username }}</strong>
                            {% if profile.user.email %}
                            <br><small>{{ profile.user.email }}</small>
                            {% endif %}
                            <br><small>Joined: {{ profile.user.date_joined|date:"Y-m-d" }}</small>
                        </td>
                        <td>
                            {% if profile.demographics %}
                            <strong>{{ profile.demographics.full_name }}</strong>
                            <br><small>{{ profile.demographics.age }} years, {{ profile.demographics.gender }}</small>
                            <br><small>{{ profile.demographics.location }}</small>
                            {% else %}
                            <em>No demographics</em>
                            {% endif %}
                        </td>
                        <td>
                            {% if profile.current_environment %}
                            <strong>{{ profile.current_environment.environment_name }}</strong>
                            <br><small>{{ profile.current_environment.environment_description|truncatechars:50 }}</small>
                            {% else %}
                            <em>No environment</em>
                            {% endif %}
                        </td>
                        <td>
                            <small>
                                Envs: {{ profile.environments.count }}<br>
                                Skills: {{ profile.skills.count }}<br>
                                Resources: {{ profile.resources.count }}<br>
                                Prefs: {{ profile.preferences.count }}
                            </small>
                        </td>
                        <td>
                            <div class="completeness-bar">
                                <div class="completeness-fill" style="width: {{ profile.completeness_percentage }}%"></div>
                                <div class="completeness-text">{{ profile.completeness_percentage }}%</div>
                            </div>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-primary view-profile-btn" data-profile-id="{{ profile.id }}">
                                    👁️ View
                                </button>
                                <button class="btn btn-secondary edit-profile-btn" data-profile-id="{{ profile.id }}">
                                    ✏️ Edit
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% else %}
            <div class="no-data">
                <h3>No profiles found</h3>
                <p>Try adjusting your search criteria or filters.</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Include the user profile detail modal -->
{% include 'admin_tools/modals/user_profile_detail_modal.html' %}

<!-- Include the new dedicated modals -->
{% include 'admin_tools/modals/user_environment_detail_modal.html' %}
{% include 'admin_tools/modals/inventory_detail_modal.html' %}

<!-- Include the profile repair modal -->
{% include 'admin_tools/profile_repair_modal.html' %}

<!-- User Account Selection Modal -->
<div class="modal fade" id="userAccountSelectionModal" tabindex="-1" aria-labelledby="userAccountSelectionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="userAccountSelectionModalLabel">
                    <i class="fas fa-user-plus me-2"></i>User Account Selection
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="fw-bold mb-3">Profile Information</h6>
                        <div class="card bg-light">
                            <div class="card-body">
                                <p><strong>Profile Name:</strong> <span id="modal-profile-name"></span></p>
                                <p><strong>Username:</strong> <span id="modal-username"></span></p>
                                <p><strong>Email:</strong> <span id="modal-email"></span></p>
                                <p><strong>Full Name:</strong> <span id="modal-full-name"></span></p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6 class="fw-bold mb-3">Account Options</h6>
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="radio" name="accountOption" id="createNewAccount" value="create" checked>
                            <label class="form-check-label" for="createNewAccount">
                                <strong>Create New User Account</strong>
                                <br><small class="text-muted">Create a new user account with the provided details</small>
                            </label>
                        </div>
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="radio" name="accountOption" id="connectExistingAccount" value="connect">
                            <label class="form-check-label" for="connectExistingAccount">
                                <strong>Connect to Existing Account</strong>
                                <br><small class="text-muted">Attach this profile to an existing user account</small>
                            </label>
                        </div>

                        <div id="existingAccountSelection" style="display: none;">
                            <label for="existingUserSelect" class="form-label">Select Existing User:</label>
                            <select class="form-select" id="existingUserSelect">
                                <option value="">Loading users...</option>
                            </select>
                            <small class="text-muted">The profile will be attached to the selected user account</small>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <h6 class="fw-bold">Import Options</h6>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="modalOverwriteExisting" checked>
                        <label class="form-check-label" for="modalOverwriteExisting">
                            Overwrite existing profile data if user already has a profile
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="modalCreateBackup" checked>
                        <label class="form-check-label" for="modalCreateBackup">
                            Create backup of existing data before import
                        </label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="proceedWithImportBtn">
                    <i class="fas fa-upload me-2"></i>Proceed with Import
                </button>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript functionality has been moved to external files -->










































<!-- Include the Profile Repair Modal -->
{% include 'admin_tools/profile_repair_modal.html' %}

<!-- Include JavaScript Files -->
<script src="{% static 'admin_tools/js/profile_repair.js' %}"></script>
<script src="{% static 'admin_tools/js/modules/import_manager.js' %}"></script>
<script src="{% static 'admin_tools/js/user_profile_management.js' %}"></script>

{% endblock %}
