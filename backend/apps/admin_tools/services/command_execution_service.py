"""
Centralized Command Execution Service with Real-time Progress and Error Reporting

This service provides:
- Real-time progress reporting via WebSocket
- Comprehensive error handling and broadcasting
- Centralized command execution logic
- Integration with existing EventService for WebSocket communication
"""

import asyncio
import json
import logging
import traceback
from datetime import datetime
from io import <PERSON><PERSON>
from typing import Dict, Any, Optional, List

from django.core.management import call_command
from django.core.management.base import CommandError
from django.http import JsonResponse

from apps.main.services.event_service import EventService

logger = logging.getLogger(__name__)


class CommandExecutionService:
    """
    Centralized service for executing Django management commands with real-time feedback.
    
    Features:
    - Real-time progress reporting via WebSocket
    - Comprehensive error handling and broadcasting
    - Detailed execution logging and metrics
    - Integration with existing admin error broadcasting system
    """
    
    def __init__(self, user_id: Optional[str] = None, session_id: Optional[str] = None):
        """
        Initialize the command execution service.
        
        Args:
            user_id: Optional user ID for targeted WebSocket messages
            session_id: Optional session ID for targeted WebSocket messages
        """
        self.user_id = user_id
        self.session_id = session_id
        self.execution_id = f"cmd_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
        
    async def execute_command_with_progress(
        self,
        command_name: str,
        parameters: Dict[str, Any],
        external_json_path: Optional[str] = None,
        bypass_idempotent: bool = False
    ) -> JsonResponse:
        """
        Execute a Django management command with real-time progress reporting.
        
        Args:
            command_name: Name of the Django management command
            parameters: Command parameters dictionary
            external_json_path: Optional path to external JSON file
            bypass_idempotent: Whether to bypass idempotency checks
            
        Returns:
            JsonResponse with execution results
        """
        start_time = datetime.now()
        
        # Send initial progress update
        await self._emit_progress_update(
            stage="initialization",
            progress=0,
            message=f"Starting command: {command_name}",
            details={"command": command_name, "parameters": parameters}
        )
        
        try:
            # Prepare command arguments
            await self._emit_progress_update(
                stage="preparation",
                progress=10,
                message="Preparing command arguments..."
            )
            
            args = self._prepare_command_arguments(parameters, external_json_path)
            
            # Set up output capture
            output_buffer = StringIO()
            error_buffer = StringIO()
            
            # Handle bypass idempotent if needed
            original_env = self._handle_bypass_idempotent(bypass_idempotent)
            
            await self._emit_progress_update(
                stage="execution",
                progress=25,
                message=f"Executing command with arguments: {args}"
            )
            
            try:
                # Execute the command
                call_command(command_name, *args, stdout=output_buffer, stderr=error_buffer)
                
                end_time = datetime.now()
                execution_time = (end_time - start_time).total_seconds()
                
                output = output_buffer.getvalue()
                error_output = error_buffer.getvalue()
                
                # Check if there were any errors in stderr
                if error_output.strip():
                    await self._emit_error(
                        error_type="command_stderr",
                        message="Command completed but produced error output",
                        details={
                            "command": command_name,
                            "stderr": error_output,
                            "stdout": output
                        }
                    )
                
                await self._emit_progress_update(
                    stage="completion",
                    progress=100,
                    message=f"Command completed successfully in {execution_time:.2f}s",
                    details={
                        "execution_time": execution_time,
                        "output_lines": len(output.splitlines()) if output else 0
                    }
                )
                
                return JsonResponse({
                    'success': True,
                    'output': output,
                    'error_output': error_output,
                    'execution_time': execution_time,
                    'started_at': start_time.isoformat(),
                    'completed_at': end_time.isoformat(),
                    'execution_id': self.execution_id
                })
                
            except CommandError as e:
                end_time = datetime.now()
                execution_time = (end_time - start_time).total_seconds()
                
                error_details = {
                    "command": command_name,
                    "arguments": args,
                    "error_message": str(e),
                    "execution_time": execution_time,
                    "stdout": output_buffer.getvalue(),
                    "stderr": error_buffer.getvalue()
                }
                
                await self._emit_error(
                    error_type="command_error",
                    message=f"Command '{command_name}' failed: {str(e)}",
                    details=error_details
                )
                
                return JsonResponse({
                    'success': False,
                    'error': str(e),
                    'error_type': 'CommandError',
                    'output': output_buffer.getvalue(),
                    'error_output': error_buffer.getvalue(),
                    'execution_time': execution_time,
                    'started_at': start_time.isoformat(),
                    'completed_at': end_time.isoformat(),
                    'execution_id': self.execution_id,
                    'details': error_details
                })
                
            finally:
                # Restore environment if needed
                self._restore_environment(bypass_idempotent, original_env)
                
        except Exception as e:
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            error_details = {
                "command": command_name,
                "parameters": parameters,
                "error_message": str(e),
                "error_type": type(e).__name__,
                "traceback": traceback.format_exc(),
                "execution_time": execution_time
            }
            
            await self._emit_error(
                error_type="unexpected_error",
                message=f"Unexpected error executing command '{command_name}': {str(e)}",
                details=error_details
            )
            
            return JsonResponse({
                'success': False,
                'error': f'Unexpected error: {str(e)}',
                'error_type': type(e).__name__,
                'execution_time': execution_time,
                'started_at': start_time.isoformat(),
                'completed_at': end_time.isoformat(),
                'execution_id': self.execution_id,
                'details': error_details,
                'traceback': traceback.format_exc()
            })
    
    def _prepare_command_arguments(self, parameters: Dict[str, Any], external_json_path: Optional[str] = None) -> List[str]:
        """Prepare command arguments from parameters dictionary."""
        args = []
        
        for param_name, param_value in parameters.items():
            # Skip special parameters handled separately
            if param_name in ['bypass_idempotent', 'external_json']:
                continue
                
            if param_value and param_value != 'false':  # Handle checkbox values
                if param_name == 'update_schemas' and param_value == 'true':
                    args.append('--update-schemas')
                elif param_name != 'update_schemas' and param_value != 'true':  # Don't add flag-only parameters
                    args.extend([f'--{param_name}', str(param_value)])
                elif param_name != 'update_schemas' and param_value == 'true':  # Handle other boolean flags
                    args.append(f'--{param_name}')
        
        # Add external JSON path if provided
        if external_json_path:
            args.extend(['--external-json', str(external_json_path)])
            
        return args
    
    def _handle_bypass_idempotent(self, bypass_idempotent: bool) -> Optional[str]:
        """Handle bypass idempotent environment variable."""
        import os
        
        if bypass_idempotent:
            original_env = os.environ.get('SKIP_SEEDER_IDEMPOTENCY_CHECK')
            os.environ['SKIP_SEEDER_IDEMPOTENCY_CHECK'] = 'true'
            return original_env
        return None
    
    def _restore_environment(self, bypass_idempotent: bool, original_env: Optional[str]):
        """Restore environment variables after command execution."""
        import os
        
        if bypass_idempotent:
            if original_env is not None:
                os.environ['SKIP_SEEDER_IDEMPOTENCY_CHECK'] = original_env
            else:
                os.environ.pop('SKIP_SEEDER_IDEMPOTENCY_CHECK', None)
    
    async def _emit_progress_update(self, stage: str, progress: int, message: str, details: Optional[Dict] = None):
        """Emit progress update via WebSocket."""
        try:
            event_data = {
                "execution_id": self.execution_id,
                "stage": stage,
                "progress": progress,
                "message": message,
                "timestamp": datetime.now().isoformat(),
                "details": details or {}
            }
            
            # Emit to admin dashboard
            await EventService.emit_event(
                event_type="command_progress",
                data=event_data,
                target_groups=["admin_tester", "benchmark_dashboard"]
            )
            
            logger.info(f"Command progress: {stage} - {progress}% - {message}")
            
        except Exception as e:
            logger.error(f"Failed to emit progress update: {e}")
    
    async def _emit_error(self, error_type: str, message: str, details: Optional[Dict] = None):
        """Emit error event via WebSocket."""
        try:
            event_data = {
                "execution_id": self.execution_id,
                "error_type": error_type,
                "message": message,
                "timestamp": datetime.now().isoformat(),
                "details": details or {}
            }
            
            # Emit to admin dashboard
            await EventService.emit_event(
                event_type="error",
                data=event_data,
                target_groups=["admin_tester", "benchmark_dashboard"]
            )
            
            # Also emit as debug_info for detailed error tracking
            await EventService.emit_event(
                event_type="debug_info",
                data={
                    "type": "command_execution_error",
                    "execution_id": self.execution_id,
                    "error_details": event_data
                },
                target_groups=["admin_tester", "benchmark_dashboard"]
            )
            
            logger.error(f"Command error: {error_type} - {message}")
            
        except Exception as e:
            logger.error(f"Failed to emit error event: {e}")

    def _execute_command_sync_fallback(
        self,
        command_name: str,
        parameters: Dict[str, Any],
        external_json_path: Optional[str] = None,
        bypass_idempotent: bool = False
    ) -> JsonResponse:
        """
        Synchronous fallback command execution without WebSocket progress reporting.

        This method provides basic command execution when async WebSocket
        communication is not available.
        """
        start_time = datetime.now()

        try:
            # Prepare command arguments
            args = self._prepare_command_arguments(parameters, external_json_path)

            # Set up output capture
            output_buffer = StringIO()
            error_buffer = StringIO()

            # Handle bypass idempotent if needed
            original_env = self._handle_bypass_idempotent(bypass_idempotent)

            try:
                # Execute the command
                call_command(command_name, *args, stdout=output_buffer, stderr=error_buffer)

                end_time = datetime.now()
                execution_time = (end_time - start_time).total_seconds()

                output = output_buffer.getvalue()
                error_output = error_buffer.getvalue()

                return JsonResponse({
                    'success': True,
                    'output': output,
                    'error_output': error_output,
                    'execution_time': execution_time,
                    'started_at': start_time.isoformat(),
                    'completed_at': end_time.isoformat(),
                    'execution_id': self.execution_id,
                    'fallback_mode': True
                })

            except CommandError as e:
                end_time = datetime.now()
                execution_time = (end_time - start_time).total_seconds()

                error_details = {
                    "command": command_name,
                    "arguments": args,
                    "error_message": str(e),
                    "execution_time": execution_time,
                    "stdout": output_buffer.getvalue(),
                    "stderr": error_buffer.getvalue()
                }

                return JsonResponse({
                    'success': False,
                    'error': str(e),
                    'error_type': 'CommandError',
                    'output': output_buffer.getvalue(),
                    'error_output': error_buffer.getvalue(),
                    'execution_time': execution_time,
                    'started_at': start_time.isoformat(),
                    'completed_at': end_time.isoformat(),
                    'execution_id': self.execution_id,
                    'details': error_details,
                    'fallback_mode': True
                })

            finally:
                # Restore environment if needed
                self._restore_environment(bypass_idempotent, original_env)

        except Exception as e:
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()

            error_details = {
                "command": command_name,
                "parameters": parameters,
                "error_message": str(e),
                "error_type": type(e).__name__,
                "traceback": traceback.format_exc(),
                "execution_time": execution_time
            }

            return JsonResponse({
                'success': False,
                'error': f'Unexpected error: {str(e)}',
                'error_type': type(e).__name__,
                'execution_time': execution_time,
                'started_at': start_time.isoformat(),
                'completed_at': end_time.isoformat(),
                'execution_id': self.execution_id,
                'details': error_details,
                'traceback': traceback.format_exc(),
                'fallback_mode': True
            })


def execute_command_sync(
    command_name: str,
    parameters: Dict[str, Any],
    external_json_path: Optional[str] = None,
    bypass_idempotent: bool = False,
    user_id: Optional[str] = None,
    session_id: Optional[str] = None
) -> JsonResponse:
    """
    Synchronous wrapper for command execution with progress reporting.

    This function handles the async context properly for Django views
    and provides real-time progress and error reporting via WebSocket.
    """
    from asgiref.sync import async_to_sync

    service = CommandExecutionService(user_id=user_id, session_id=session_id)

    try:
        # Use Django's async_to_sync to properly handle the async context
        return async_to_sync(service.execute_command_with_progress)(
            command_name, parameters, external_json_path, bypass_idempotent
        )
    except Exception as e:
        # Fallback error handling if async execution fails
        logger.error(f"Failed to execute command with async progress: {e}")

        # Fall back to synchronous execution without WebSocket progress
        return service._execute_command_sync_fallback(
            command_name, parameters, external_json_path, bypass_idempotent
        )
