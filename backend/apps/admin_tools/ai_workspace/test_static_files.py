#!/usr/bin/env python3
"""
Test static file loading for catalog management.
This script tests if the static files are accessible and properly configured.

Usage:
    docker exec -it backend-web-1 python /usr/src/app/apps/admin_tools/ai_workspace/test_static_files.py
"""

import os
import sys
import requests
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, '/usr/src/app')

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')

import django
django.setup()

from django.conf import settings
from django.contrib.staticfiles.finders import find


class StaticFilesTester:
    """Test static files for catalog management."""
    
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.errors_found = []
        self.tests_passed = 0
        self.tests_failed = 0
    
    def test_static_file_paths(self):
        """Test if static files exist in the filesystem."""
        print("🔍 Testing static file paths...")
        
        required_files = [
            'admin_tools/js/modules/catalog_viewer.js',
            'admin_tools/js/pages/catalog_management.js',
            'admin_tools/css/pages/catalog_management.css',
            'admin_tools/css/components/catalog_viewer.css',
        ]
        
        for file_path in required_files:
            found_path = find(file_path)
            if found_path:
                print(f"✅ Found: {file_path} -> {found_path}")
                self.tests_passed += 1
            else:
                print(f"❌ Missing: {file_path}")
                self.tests_failed += 1
                self.errors_found.append(f"Static file not found: {file_path}")
    
    def test_static_file_urls(self):
        """Test if static files are accessible via HTTP."""
        print("\n🌐 Testing static file URLs...")
        
        static_urls = [
            '/static/admin_tools/js/modules/catalog_viewer.js',
            '/static/admin_tools/js/pages/catalog_management.js',
            '/static/admin_tools/css/pages/catalog_management.css',
            '/static/admin_tools/css/components/catalog_viewer.css',
        ]
        
        for url in static_urls:
            try:
                response = requests.get(f"{self.base_url}{url}", timeout=5)
                if response.status_code == 200:
                    print(f"✅ Accessible: {url} (Status: {response.status_code})")
                    self.tests_passed += 1
                else:
                    print(f"❌ Failed: {url} (Status: {response.status_code})")
                    self.tests_failed += 1
                    self.errors_found.append(f"HTTP error for {url}: {response.status_code}")
            except Exception as e:
                print(f"❌ Error accessing {url}: {e}")
                self.tests_failed += 1
                self.errors_found.append(f"Network error for {url}: {e}")
    
    def test_javascript_syntax(self):
        """Test if JavaScript files have valid syntax."""
        print("\n📝 Testing JavaScript syntax...")
        
        js_files = [
            'admin_tools/js/modules/catalog_viewer.js',
            'admin_tools/js/pages/catalog_management.js',
        ]
        
        for file_path in js_files:
            found_path = find(file_path)
            if found_path:
                try:
                    with open(found_path, 'r') as f:
                        content = f.read()
                    
                    # Basic syntax checks
                    if 'class CatalogViewer' in content:
                        print(f"✅ CatalogViewer class found in {file_path}")
                        self.tests_passed += 1
                    elif 'CatalogViewer' in file_path:
                        print(f"❌ CatalogViewer class not found in {file_path}")
                        self.tests_failed += 1
                        self.errors_found.append(f"CatalogViewer class missing in {file_path}")
                    
                    if 'class CatalogManagement' in content:
                        print(f"✅ CatalogManagement class found in {file_path}")
                        self.tests_passed += 1
                    elif 'CatalogManagement' in file_path:
                        print(f"❌ CatalogManagement class not found in {file_path}")
                        self.tests_failed += 1
                        self.errors_found.append(f"CatalogManagement class missing in {file_path}")
                        
                except Exception as e:
                    print(f"❌ Error reading {file_path}: {e}")
                    self.tests_failed += 1
                    self.errors_found.append(f"File read error for {file_path}: {e}")
    
    def test_media_class_configuration(self):
        """Test if Media class is properly configured."""
        print("\n⚙️ Testing Media class configuration...")
        
        try:
            from apps.admin_tools.media import CatalogManagementMedia
            media_instance = CatalogManagementMedia()
            
            # Check CSS files
            css_files = media_instance.media.render_css()
            if 'catalog_management.css' in css_files:
                print("✅ catalog_management.css included in Media class")
                self.tests_passed += 1
            else:
                print("❌ catalog_management.css not included in Media class")
                self.tests_failed += 1
                self.errors_found.append("catalog_management.css not in Media class")
            
            # Check JS files
            js_files = media_instance.media.render_js()
            if 'catalog_viewer.js' in js_files:
                print("✅ catalog_viewer.js included in Media class")
                self.tests_passed += 1
            else:
                print("❌ catalog_viewer.js not included in Media class")
                self.tests_failed += 1
                self.errors_found.append("catalog_viewer.js not in Media class")
            
            if 'catalog_management.js' in js_files:
                print("✅ catalog_management.js included in Media class")
                self.tests_passed += 1
            else:
                print("❌ catalog_management.js not included in Media class")
                self.tests_failed += 1
                self.errors_found.append("catalog_management.js not in Media class")
                
        except Exception as e:
            print(f"❌ Error testing Media class: {e}")
            self.tests_failed += 1
            self.errors_found.append(f"Media class error: {e}")
    
    def generate_report(self):
        """Generate test report with findings."""
        print("\n" + "=" * 60)
        print("📊 STATIC FILES TESTING REPORT")
        print("=" * 60)
        
        total_tests = self.tests_passed + self.tests_failed
        success_rate = (self.tests_passed / total_tests * 100) if total_tests > 0 else 0
        
        print(f"✅ Tests Passed: {self.tests_passed}")
        print(f"❌ Tests Failed: {self.tests_failed}")
        print(f"📈 Success Rate: {success_rate:.1f}%")
        
        if self.errors_found:
            print("\n🔍 ERRORS FOUND:")
            for i, error in enumerate(self.errors_found, 1):
                print(f"  {i}. {error}")
        
        print("\n🎯 NEXT ACTIONS:")
        if success_rate < 50:
            print("  🚨 CRITICAL: Major static file issues found.")
            print("     - Check Django static file configuration")
            print("     - Verify file paths and permissions")
            print("     - Test static file serving")
        elif success_rate < 80:
            print("  🔧 MODERATE: Some static files not working.")
            print("     - Fix missing files")
            print("     - Check Media class configuration")
            print("     - Test JavaScript loading order")
        else:
            print("  ✅ GOOD: Most static files working correctly.")
    
    def run_all_tests(self):
        """Run all static file tests."""
        print("🧪 STARTING STATIC FILES TESTING")
        print("=" * 60)
        
        try:
            self.test_static_file_paths()
            self.test_static_file_urls()
            self.test_javascript_syntax()
            self.test_media_class_configuration()
            
            self.generate_report()
            
        except Exception as e:
            print(f"❌ Critical error during testing: {e}")
            self.errors_found.append(f"Critical error: {e}")


def main():
    """Main test execution."""
    tester = StaticFilesTester()
    tester.run_all_tests()


if __name__ == '__main__':
    main()
