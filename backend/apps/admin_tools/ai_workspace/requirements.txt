# AI Workspace Requirements
# Core dependencies for the Goali AI Workspace

# Playwright for admin page validation and UI testing
playwright>=1.40.0

# Additional testing and validation tools
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-django>=4.5.0

# Performance monitoring and profiling
psutil>=5.9.0

# Data analysis and reporting
pandas>=2.0.0
matplotlib>=3.7.0

# Documentation generation
markdown>=3.5.0
jinja2>=3.1.0

# Async utilities
aiofiles>=23.0.0
asyncio-throttle>=1.0.0

# JSON schema validation
jsonschema>=4.19.0

# HTTP client for API testing
httpx>=0.25.0
requests>=2.31.0

# Development and debugging tools
ipython>=8.15.0
rich>=13.5.0  # For beautiful console output

# Optional: AI/ML libraries for advanced analysis
# scikit-learn>=1.3.0
# numpy>=1.24.0

# Note: Django and other core Goali dependencies are assumed to be
# already installed as part of the main project requirements
