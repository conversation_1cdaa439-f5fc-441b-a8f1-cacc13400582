#!/usr/bin/env python3
"""
Test script for enhanced user profile API endpoints.

This script tests the new API endpoints for user profiles, environments, and inventory
to ensure they work correctly with real data.
"""

import os
import sys
import django
import json
import requests
from datetime import datetime

# Setup Django environment
sys.path.append('/usr/src/app')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.contrib.auth.models import User
from django.test import Client
from apps.user.models import UserProfile, Demographics, UserEnvironment, Inventory, UserResource


class EnhancedAPITester:
    def __init__(self):
        self.client = Client()
        self.base_url = 'http://localhost:8000'
        self.admin_user = None
        self.test_profile = None
        
    def setup_test_data(self):
        """Create test data for API testing."""
        print("🔧 Setting up test data...")
        
        # Create admin user
        self.admin_user, created = User.objects.get_or_create(
            username='test_admin',
            defaults={
                'email': '<EMAIL>',
                'is_staff': True,
                'is_superuser': True
            }
        )
        if created:
            self.admin_user.set_password('testpass123')
            self.admin_user.save()
        
        # Create test profile with comprehensive data
        self.test_profile, created = UserProfile.objects.get_or_create(
            profile_name='Enhanced API Test Profile',
            defaults={
                'user': self.admin_user,
                'is_real': False
            }
        )
        
        # Create demographics
        Demographics.objects.get_or_create(
            user_profile=self.test_profile,
            defaults={
                'full_name': 'Test User Enhanced',
                'age': 30,
                'gender': 'Test',
                'location': 'Test City',
                'language': 'English',
                'occupation': 'API Tester'
            }
        )
        
        # Create test environment
        test_env, created = UserEnvironment.objects.get_or_create(
            user_profile=self.test_profile,
            environment_name='Test Environment',
            defaults={
                'environment_description': 'Test environment for API testing',
                'is_current': True
            }
        )
        
        # Create test inventory
        test_inventory, created = Inventory.objects.get_or_create(
            user_profile=self.test_profile,
            name='Test Inventory',
            defaults={
                'notes': 'Test inventory for API testing',
                'user_environment': test_env
            }
        )
        
        print(f"✅ Test data setup complete. Profile ID: {self.test_profile.id}")
        return self.test_profile.id
    
    def login_admin(self):
        """Login as admin user."""
        # Force login for testing
        self.client.force_login(self.admin_user)
        print("✅ Admin login successful")
    
    def test_enhanced_profile_api(self, profile_id):
        """Test the enhanced user profile API endpoint."""
        print(f"\n🧪 Testing enhanced profile API for profile {profile_id}...")
        
        response = self.client.get(f'/admin/user-profiles/api/{profile_id}/')
        
        if response.status_code != 200:
            print(f"❌ Profile API failed with status {response.status_code}")
            print(f"Response: {response.content.decode()}")
            return False
        
        data = response.json()
        
        # Check for new fields
        required_fields = ['beliefs', 'traits', 'trust_level', 'statistics']
        missing_fields = []
        
        for field in required_fields:
            if field not in data:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"❌ Missing fields in profile API: {missing_fields}")
            return False
        
        print("✅ Enhanced profile API working correctly")
        print(f"   - Beliefs: {len(data.get('beliefs', []))} items")
        print(f"   - Traits: {len(data.get('traits', []))} items")
        print(f"   - Trust Level: {'Present' if data.get('trust_level') else 'None'}")
        print(f"   - Statistics: {len(data.get('statistics', {}))} metrics")
        
        return True
    
    def test_environment_api(self, profile_id):
        """Test the environment API endpoint."""
        print(f"\n🏠 Testing environment API for profile {profile_id}...")
        
        # Test all environments endpoint
        response = self.client.get(f'/admin/user-profiles/api/{profile_id}/environments/')
        
        if response.status_code != 200:
            print(f"❌ Environment API failed with status {response.status_code}")
            print(f"Response: {response.content.decode()}")
            return False
        
        data = response.json()
        
        # Check response structure
        required_fields = ['profile_id', 'environments', 'total_environments']
        missing_fields = []
        
        for field in required_fields:
            if field not in data:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"❌ Missing fields in environment API: {missing_fields}")
            return False
        
        print("✅ Environment API working correctly")
        print(f"   - Total environments: {data.get('total_environments', 0)}")
        print(f"   - Environments data: {len(data.get('environments', []))} items")
        
        # Test specific environment if available
        if data.get('environments'):
            env_id = data['environments'][0]['id']
            response = self.client.get(f'/admin/user-profiles/api/{profile_id}/environments/{env_id}/')
            
            if response.status_code == 200:
                env_data = response.json()
                print(f"   - Specific environment API: ✅ Working")
                print(f"   - Environment name: {env_data.get('environment_name', 'N/A')}")
            else:
                print(f"   - Specific environment API: ❌ Failed ({response.status_code})")
        
        return True
    
    def test_inventory_api(self, profile_id):
        """Test the inventory API endpoint."""
        print(f"\n📦 Testing inventory API for profile {profile_id}...")
        
        response = self.client.get(f'/admin/user-profiles/api/{profile_id}/inventory/')
        
        if response.status_code != 200:
            print(f"❌ Inventory API failed with status {response.status_code}")
            print(f"Response: {response.content.decode()}")
            return False
        
        data = response.json()
        
        # Check response structure
        required_fields = ['profile_id', 'inventories', 'resources_by_category', 'statistics']
        missing_fields = []
        
        for field in required_fields:
            if field not in data:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"❌ Missing fields in inventory API: {missing_fields}")
            return False
        
        print("✅ Inventory API working correctly")
        print(f"   - Total inventories: {data.get('statistics', {}).get('total_inventories', 0)}")
        print(f"   - Total resources: {data.get('statistics', {}).get('total_resources', 0)}")
        print(f"   - Resource categories: {len(data.get('resources_by_category', {}))}")
        
        return True
    
    def test_error_handling(self):
        """Test error handling for invalid requests."""
        print(f"\n⚠️ Testing error handling...")
        
        # Test invalid profile ID
        response = self.client.get('/admin/user-profiles/api/99999/')
        if response.status_code == 404:
            print("✅ Invalid profile ID handled correctly (404)")
        else:
            print(f"❌ Invalid profile ID not handled correctly ({response.status_code})")
        
        # Test missing profile ID for environment API
        response = self.client.get('/admin/user-profiles/api//environments/')
        if response.status_code in [400, 404]:
            print("✅ Missing profile ID handled correctly")
        else:
            print(f"❌ Missing profile ID not handled correctly ({response.status_code})")
        
        return True
    
    def run_all_tests(self):
        """Run all API tests."""
        print("🚀 Starting Enhanced API Tests")
        print("=" * 50)
        
        try:
            # Setup
            profile_id = self.setup_test_data()
            self.login_admin()
            
            # Run tests
            tests = [
                self.test_enhanced_profile_api,
                self.test_environment_api,
                self.test_inventory_api,
            ]
            
            results = []
            for test in tests:
                try:
                    result = test(profile_id)
                    results.append(result)
                except Exception as e:
                    print(f"❌ Test {test.__name__} failed with exception: {e}")
                    results.append(False)
            
            # Test error handling
            self.test_error_handling()
            
            # Summary
            print("\n" + "=" * 50)
            print("📊 Test Summary")
            print("=" * 50)
            
            passed = sum(results)
            total = len(results)
            
            print(f"Tests passed: {passed}/{total}")
            
            if passed == total:
                print("🎉 All tests passed! Enhanced API is working correctly.")
            else:
                print("⚠️ Some tests failed. Check the output above for details.")
            
            return passed == total
            
        except Exception as e:
            print(f"❌ Test suite failed with exception: {e}")
            import traceback
            traceback.print_exc()
            return False


if __name__ == '__main__':
    tester = EnhancedAPITester()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)
