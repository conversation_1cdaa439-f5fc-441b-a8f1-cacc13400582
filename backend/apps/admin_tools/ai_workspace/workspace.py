"""
AI Workspace Main Interface

Central interface for AI agents to access all workspace tools and capabilities.
Provides a unified entry point for agent management, benchmark analysis, admin validation, and knowledge management.
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from pathlib import Path

from .tools.agent_manager import Agent<PERSON>anager
from .tools.benchmark_analyzer import Benchmark<PERSON>nalyzer
from .tools.admin_validator import AdminValidator
from .tools.knowledge_manager import KnowledgeManager
from .tools.benchmark_tools import BenchmarkTools
from .tools.agent_debugging_system import AgentDebuggingSystem

logger = logging.getLogger(__name__)


class AIWorkspace:
    """
    Central AI Workspace interface for Goali admin tools improvement.
    
    This class provides AI agents with a unified interface to access all workspace
    capabilities including agent management, benchmark analysis, admin validation,
    and knowledge management.
    """
    
    def __init__(self, workspace_path: str = None):
        self.logger = logger
        self.workspace_path = Path(workspace_path) if workspace_path else Path(__file__).parent
        
        # Initialize tools
        self.agent_manager = AgentManager()
        self.benchmark_analyzer = BenchmarkAnalyzer()
        self.admin_validator = AdminValidator()
        self.knowledge_manager = KnowledgeManager(str(self.workspace_path))
        self.benchmark_tools = BenchmarkTools()
        self.debugging_system = AgentDebuggingSystem(str(self.workspace_path))
        
        # Workspace state
        self.initialized = False
        self.session_id = None
        self.start_time = None
    
    async def initialize(self) -> Dict[str, Any]:
        """
        Initialize the AI workspace and all tools.
        
        Returns:
            Initialization status and capabilities summary
        """
        try:
            self.start_time = datetime.now()
            self.session_id = f"ai_workspace_{self.start_time.strftime('%Y%m%d_%H%M%S')}"
            
            # Initialize tools that require async setup
            await self.benchmark_tools.initialize()
            
            # Test basic functionality
            capabilities = await self._test_capabilities()
            
            self.initialized = True
            
            # Document workspace initialization
            await self._document_initialization(capabilities)
            
            self.logger.info(f"AI Workspace initialized successfully: {self.session_id}")
            
            return {
                'status': 'initialized',
                'session_id': self.session_id,
                'start_time': self.start_time.isoformat(),
                'capabilities': capabilities,
                'workspace_path': str(self.workspace_path)
            }
            
        except Exception as e:
            self.logger.error(f"Error initializing AI workspace: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'session_id': self.session_id
            }
    
    # =========================================================================
    # High-Level Operations
    # =========================================================================
    
    async def analyze_system_health(self) -> Dict[str, Any]:
        """
        Perform comprehensive system health analysis.
        
        Returns:
            Complete system health report
        """
        try:
            health_report = {
                'timestamp': datetime.now().isoformat(),
                'session_id': self.session_id,
                'status': 'running',
                'components': {}
            }
            
            # Agent system health
            self.logger.info("Analyzing agent system health...")
            agent_summary = self.agent_manager.get_agent_summary()
            health_report['components']['agents'] = {
                'status': 'healthy' if agent_summary['active_agents'] > 0 else 'warning',
                'summary': agent_summary
            }
            
            # Benchmark system health
            self.logger.info("Analyzing benchmark system health...")
            benchmark_validation = await self.benchmark_tools.validate_benchmark_system()
            health_report['components']['benchmarks'] = {
                'status': benchmark_validation.get('overall_health', 'unknown'),
                'validation': benchmark_validation
            }
            
            # Admin interface health (if Playwright available)
            self.logger.info("Analyzing admin interface health...")
            try:
                admin_test = await self.admin_validator.test_admin_dashboard()
                health_report['components']['admin_interface'] = {
                    'status': admin_test.get('status', 'unknown'),
                    'test_results': admin_test
                }
            except Exception as e:
                health_report['components']['admin_interface'] = {
                    'status': 'error',
                    'error': str(e)
                }
            
            # Overall health assessment
            component_statuses = [comp.get('status') for comp in health_report['components'].values()]
            if all(status in ['healthy', 'excellent', 'good'] for status in component_statuses):
                health_report['overall_status'] = 'healthy'
            elif any(status in ['error', 'poor'] for status in component_statuses):
                health_report['overall_status'] = 'critical'
            else:
                health_report['overall_status'] = 'warning'
            
            health_report['status'] = 'completed'
            
            # Document findings
            await self._document_health_analysis(health_report)
            
            self.logger.info(f"System health analysis completed: {health_report['overall_status']}")
            return health_report
            
        except Exception as e:
            self.logger.error(f"Error analyzing system health: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def improve_agent_performance(self, agent_role: str) -> Dict[str, Any]:
        """
        Analyze and improve performance for a specific agent.
        
        Args:
            agent_role: Agent role to improve
            
        Returns:
            Improvement analysis and recommendations
        """
        try:
            improvement_report = {
                'agent_role': agent_role,
                'timestamp': datetime.now().isoformat(),
                'session_id': self.session_id,
                'status': 'running',
                'analysis': {},
                'improvements': [],
                'validation': {}
            }
            
            # Get current agent configuration
            agent_config = self.agent_manager.get_agent_by_role(agent_role)
            if not agent_config:
                return {'error': f'Agent role {agent_role} not found'}
            
            # Analyze performance trends
            performance_analysis = await self.benchmark_analyzer.analyze_agent_performance_trends(agent_role)
            improvement_report['analysis']['performance_trends'] = performance_analysis
            
            # Get recent benchmark runs
            recent_runs = await self.benchmark_analyzer.get_benchmark_runs_by_agent(agent_role)
            improvement_report['analysis']['recent_runs'] = recent_runs
            
            # Generate improvement recommendations
            recommendations = await self._generate_improvement_recommendations(agent_config, performance_analysis)
            improvement_report['improvements'] = recommendations
            
            # Test current configuration
            if agent_role in ['mentor', 'orchestrator']:  # Test workflow agents
                test_result = await self.benchmark_tools.test_activity_tailoring_workflow()
                improvement_report['validation']['workflow_test'] = test_result
            
            improvement_report['status'] = 'completed'
            
            # Document improvement analysis
            await self._document_improvement_analysis(improvement_report)
            
            self.logger.info(f"Agent performance improvement analysis completed for: {agent_role}")
            return improvement_report
            
        except Exception as e:
            self.logger.error(f"Error improving agent performance for {agent_role}: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'agent_role': agent_role
            }
    
    async def validate_admin_interfaces(self) -> Dict[str, Any]:
        """
        Comprehensive validation of all admin interfaces.
        
        Returns:
            Complete admin interface validation report
        """
        try:
            validation_report = {
                'timestamp': datetime.now().isoformat(),
                'session_id': self.session_id,
                'status': 'running',
                'interfaces': {},
                'accessibility': {},
                'performance': {},
                'recommendations': []
            }
            
            # Initialize admin validator
            if not await self.admin_validator.initialize():
                return {'error': 'Failed to initialize admin validator (Playwright required)'}
            
            try:
                # Test main admin dashboard
                dashboard_test = await self.admin_validator.test_admin_dashboard()
                validation_report['interfaces']['dashboard'] = dashboard_test
                
                # Test benchmark management page
                benchmark_test = await self.admin_validator.test_benchmark_management_page()
                validation_report['interfaces']['benchmark_management'] = benchmark_test
                
                # Test WebSocket dashboard
                websocket_test = await self.admin_validator.test_websocket_dashboard()
                validation_report['interfaces']['websocket_dashboard'] = websocket_test
                
                # Accessibility checks
                admin_urls = [
                    'http://localhost:8000/admin/',
                    'http://localhost:8000/admin/benchmarks/',
                    'http://localhost:8000/admin/connection-dashboard/'
                ]
                
                for url in admin_urls:
                    accessibility_result = await self.admin_validator.check_accessibility(url)
                    validation_report['accessibility'][url] = accessibility_result
                
                # Performance tests
                for url in admin_urls:
                    performance_result = await self.admin_validator.test_page_performance(url)
                    validation_report['performance'][url] = performance_result
                
                # Generate recommendations
                validation_report['recommendations'] = await self._generate_ui_recommendations(validation_report)
                
            finally:
                # Clean up Playwright resources
                await self.admin_validator.cleanup()
            
            validation_report['status'] = 'completed'
            
            # Document validation results
            await self._document_ui_validation(validation_report)
            
            self.logger.info("Admin interface validation completed")
            return validation_report
            
        except Exception as e:
            self.logger.error(f"Error validating admin interfaces: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    # =========================================================================
    # Agent Debugging and Improvement Operations
    # =========================================================================

    async def debug_agent_with_user_stories(self, agent_role: str,
                                           situation_ids: List[str] = None,
                                           archetype_ids: List[str] = None) -> Dict[str, Any]:
        """
        Debug an agent using comprehensive user story simulation.

        Args:
            agent_role: Agent role to debug
            situation_ids: List of situation IDs to test (optional)
            archetype_ids: List of archetype IDs to test (optional)

        Returns:
            Comprehensive debugging report
        """
        try:
            debug_report = {
                'agent_role': agent_role,
                'timestamp': datetime.now().isoformat(),
                'session_id': self.session_id,
                'status': 'running',
                'user_stories': {},
                'performance_analysis': {},
                'improvement_recommendations': []
            }

            # Use default situations and archetypes if none provided
            if not situation_ids:
                situation_ids = ["wheel_gen_basic", "wheel_gen_stressed", "wheel_gen_creative"]
            if not archetype_ids:
                archetype_ids = ["curious_beginner", "stressed_professional", "creative_explorer"]

            self.logger.info(f"Starting agent debugging for {agent_role} with {len(situation_ids)} situations and {len(archetype_ids)} archetypes")

            # Run user story simulations
            story_results = await self.debugging_system.simulate_multiple_stories(
                situation_ids, archetype_ids, f"debug_{agent_role}_{self.session_id}"
            )
            debug_report['user_stories'] = story_results

            # Analyze agent performance
            performance_analysis = await self.benchmark_analyzer.analyze_agent_performance_trends(agent_role)
            debug_report['performance_analysis'] = performance_analysis

            # Generate improvement recommendations
            recommendations = await self._generate_debugging_recommendations(
                agent_role, story_results, performance_analysis
            )
            debug_report['improvement_recommendations'] = recommendations

            debug_report['status'] = 'completed'

            # Document debugging session
            await self._document_debugging_session(debug_report)

            self.logger.info(f"Agent debugging completed for {agent_role}")
            return debug_report

        except Exception as e:
            self.logger.error(f"Error debugging agent {agent_role}: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'agent_role': agent_role,
                'timestamp': datetime.now().isoformat()
            }

    async def test_agent_changes(self, agent_role: str, change_type: str,
                               changes: Dict[str, Any], description: str) -> Dict[str, Any]:
        """
        Test the impact of specific agent changes.

        Args:
            agent_role: Agent role to modify
            change_type: Type of change ('instructions', 'llm_config', 'tools', 'schema')
            changes: Dictionary of changes to apply
            description: Description of the changes

        Returns:
            Impact measurement results
        """
        try:
            # Create change set
            change_set = await self.debugging_system.create_agent_change_set(
                agent_role=agent_role,
                change_type=change_type,
                changes=changes,
                description=description
            )

            self.logger.info(f"Testing agent changes: {description}")

            # Measure impact
            impact_measurement = await self.debugging_system.measure_agent_change_impact(change_set)

            # Document results
            await self._document_change_impact(change_set, impact_measurement)

            return {
                'status': 'completed',
                'change_set': change_set.__dict__,
                'impact_measurement': impact_measurement.__dict__,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Error testing agent changes: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'agent_role': agent_role,
                'timestamp': datetime.now().isoformat()
            }

    async def manage_temporary_profiles(self, action: str, **kwargs) -> Dict[str, Any]:
        """
        Manage temporary user profiles for testing.

        Args:
            action: Action to perform ('create', 'list', 'prune')
            **kwargs: Additional arguments based on action

        Returns:
            Results of the profile management operation
        """
        try:
            if action == 'create':
                archetype_id = kwargs.get('archetype_id')
                session_id = kwargs.get('session_id', f"manual_{self.session_id}")

                profile = await self.debugging_system.create_temporary_profile(
                    archetype_id, session_id
                )

                return {
                    'status': 'created',
                    'profile_id': str(profile.id),
                    'profile_name': profile.profile_name,
                    'archetype_id': archetype_id,
                    'session_id': session_id
                }

            elif action == 'list':
                session_id = kwargs.get('session_id')
                profiles = await self.debugging_system.get_temporary_profiles(session_id)

                return {
                    'status': 'listed',
                    'profiles': [
                        {
                            'id': str(p.id),
                            'name': p.profile_name,
                            'created_at': p.created_at.isoformat() if hasattr(p, 'created_at') else None
                        }
                        for p in profiles
                    ],
                    'total_count': len(profiles)
                }

            elif action == 'prune':
                session_id = kwargs.get('session_id')
                older_than_hours = kwargs.get('older_than_hours', 24)

                deleted_count = await self.debugging_system.prune_temporary_profiles(
                    session_id, older_than_hours
                )

                return {
                    'status': 'pruned',
                    'deleted_count': deleted_count,
                    'session_id': session_id,
                    'older_than_hours': older_than_hours
                }

            else:
                return {
                    'status': 'error',
                    'error': f"Unknown action: {action}",
                    'valid_actions': ['create', 'list', 'prune']
                }

        except Exception as e:
            self.logger.error(f"Error managing temporary profiles: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'action': action
            }
