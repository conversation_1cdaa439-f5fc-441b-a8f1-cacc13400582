#!/usr/bin/env python3
"""
ADHD User Experience Debugging Script

This script systematically tests and fixes ADHD user experience issues:
1. Immediate wheel generation instead of mood assessment
2. Duplicated wheel items with same IDs

Usage:
    python debug_adhd_experience.py [--test-only] [--fix-issues] [--validate-fixes]
"""

import os
import sys
import asyncio
import json
import logging
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
import django
django.setup()

from tools.adhd_user_debugging import ADHDUserDebuggingTool

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def main():
    """Main debugging workflow."""
    logger.info("=" * 80)
    logger.info("🧠 ADHD USER EXPERIENCE DEBUGGING TOOL")
    logger.info("=" * 80)
    
    # Initialize debugging tool
    debug_tool = ADHDUserDebuggingTool()
    
    # Phase 1: Test current behavior
    logger.info("📊 Phase 1: Testing current ADHD user experience...")
    current_results = await debug_tool.test_current_adhd_experience()
    
    # Display results
    logger.info("\n" + "=" * 60)
    logger.info("🔍 CURRENT BEHAVIOR ANALYSIS")
    logger.info("=" * 60)
    
    issues_found = current_results.get("issues_found", [])
    if issues_found:
        logger.info(f"❌ Found {len(issues_found)} issues:")
        for i, issue in enumerate(issues_found, 1):
            logger.info(f"  {i}. {issue['issue']}: {issue['description']} (Severity: {issue['severity']})")
    else:
        logger.info("✅ No issues detected in current behavior!")
    
    # Display recommendations
    recommendations = current_results.get("recommendations", [])
    if recommendations:
        logger.info("\n📋 RECOMMENDATIONS:")
        for i, rec in enumerate(recommendations, 1):
            logger.info(f"  {i}. {rec['recommendation']}")
            logger.info(f"     Priority: {rec['priority']}")
            logger.info(f"     Components: {', '.join(rec['components_affected'])}")
    
    # Save detailed results
    results_file = Path(__file__).parent / "debug_results.json"
    with open(results_file, 'w') as f:
        json.dump(current_results, f, indent=2, default=str)
    logger.info(f"\n💾 Detailed results saved to: {results_file}")
    
    # Phase 2: Provide implementation guidance
    logger.info("\n" + "=" * 60)
    logger.info("🛠️  IMPLEMENTATION GUIDANCE")
    logger.info("=" * 60)
    
    if any(issue["issue"] == "immediate_wheel_generation" for issue in issues_found):
        logger.info("\n🔧 FIX 1: Immediate Wheel Generation")
        logger.info("   Problem: System generates wheel immediately for first-time users")
        logger.info("   Solution: Modify ConversationDispatcher message classification")
        logger.info("   Files to modify:")
        logger.info("     - apps/main/services/conversation_dispatcher.py")
        logger.info("     - Add first-time user detection logic")
        logger.info("     - Route wellness requests to 'discussion' workflow for new users")
    
    if any(issue["issue"] == "duplicate_activity_ids" for issue in issues_found):
        logger.info("\n🔧 FIX 2: Duplicate Activity IDs")
        logger.info("   Problem: All wheel items have the same ID")
        logger.info("   Solution: Fix Activity Agent ID generation")
        logger.info("   Files to modify:")
        logger.info("     - apps/main/agents/activity_agent.py")
        logger.info("     - Update wheel item creation to use unique IDs")
    
    # Phase 3: Next steps
    logger.info("\n" + "=" * 60)
    logger.info("📝 NEXT STEPS")
    logger.info("=" * 60)
    logger.info("1. Review the detailed results in debug_results.json")
    logger.info("2. Implement the recommended fixes")
    logger.info("3. Run this script again to validate improvements")
    logger.info("4. Use the AI workspace tools for ongoing monitoring")
    
    logger.info("\n✅ ADHD user experience debugging completed!")
    return current_results


if __name__ == "__main__":
    try:
        results = asyncio.run(main())
        sys.exit(0)
    except Exception as e:
        logger.error(f"❌ Error during debugging: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
