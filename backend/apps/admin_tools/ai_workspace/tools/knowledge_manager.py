"""
Knowledge Manager Tool

AI-intuitive tool for managing documentation and findings from AI agent work.
Provides comprehensive knowledge management capabilities for tracking improvements and maintaining documentation.
"""

import json
import logging
import os
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from pathlib import Path

logger = logging.getLogger(__name__)


class KnowledgeManager:
    """
    AI-intuitive tool for managing knowledge and documentation.
    
    This tool provides comprehensive capabilities for AI agents to document findings,
    track improvements, maintain knowledge bases, and generate reports.
    """
    
    def __init__(self, workspace_path: str = None):
        self.logger = logger
        self.workspace_path = Path(workspace_path) if workspace_path else Path(__file__).parent.parent
        self.docs_path = self.workspace_path / "docs"
        self.logs_path = self.workspace_path / "logs"
        self.reports_path = self.workspace_path / "reports"
        
        # Ensure directories exist
        self._ensure_directories()
    
    def _ensure_directories(self):
        """Ensure required directories exist."""
        for path in [self.docs_path, self.logs_path, self.reports_path]:
            path.mkdir(parents=True, exist_ok=True)
    
    # =========================================================================
    # Documentation Management
    # =========================================================================
    
    def document_finding(self, finding: Dict[str, Any]) -> str:
        """
        Document a new finding or discovery.
        
        Args:
            finding: Dictionary containing finding details
            
        Returns:
            Path to the created documentation file
        """
        try:
            # Validate finding structure
            required_fields = ['title', 'description', 'category']
            for field in required_fields:
                if field not in finding:
                    raise ValueError(f"Missing required field: {field}")
            
            # Add metadata
            finding['timestamp'] = datetime.now().isoformat()
            finding['id'] = self._generate_finding_id(finding['title'])
            
            # Create filename
            filename = f"finding_{finding['id']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
            file_path = self.docs_path / filename
            
            # Generate markdown content
            markdown_content = self._generate_finding_markdown(finding)
            
            # Write to file
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(markdown_content)
            
            # Update index
            self._update_findings_index(finding, filename)
            
            self.logger.info(f"Documented finding: {finding['title']} -> {filename}")
            return str(file_path)
            
        except Exception as e:
            self.logger.error(f"Error documenting finding: {e}")
            raise
    
    def document_improvement(self, improvement: Dict[str, Any]) -> str:
        """
        Document a system improvement.
        
        Args:
            improvement: Dictionary containing improvement details
            
        Returns:
            Path to the created documentation file
        """
        try:
            # Validate improvement structure
            required_fields = ['title', 'description', 'component', 'impact']
            for field in required_fields:
                if field not in improvement:
                    raise ValueError(f"Missing required field: {field}")
            
            # Add metadata
            improvement['timestamp'] = datetime.now().isoformat()
            improvement['id'] = self._generate_improvement_id(improvement['title'])
            
            # Create filename
            filename = f"improvement_{improvement['id']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
            file_path = self.docs_path / filename
            
            # Generate markdown content
            markdown_content = self._generate_improvement_markdown(improvement)
            
            # Write to file
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(markdown_content)
            
            # Update index
            self._update_improvements_index(improvement, filename)
            
            self.logger.info(f"Documented improvement: {improvement['title']} -> {filename}")
            return str(file_path)
            
        except Exception as e:
            self.logger.error(f"Error documenting improvement: {e}")
            raise
    
    def update_knowledge_base(self, topic: str, content: str, category: str = "general") -> str:
        """
        Update or create knowledge base entry.
        
        Args:
            topic: Topic name
            content: Content to add or update
            category: Category for organization
            
        Returns:
            Path to the updated knowledge base file
        """
        try:
            # Create category directory if needed
            category_path = self.docs_path / "knowledge_base" / category
            category_path.mkdir(parents=True, exist_ok=True)
            
            # Create filename
            topic_id = self._sanitize_filename(topic)
            filename = f"{topic_id}.md"
            file_path = category_path / filename
            
            # Check if file exists
            if file_path.exists():
                # Update existing file
                with open(file_path, 'r', encoding='utf-8') as f:
                    existing_content = f.read()
                
                # Add update section
                updated_content = existing_content + f"\n\n## Update - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n{content}\n"
            else:
                # Create new file
                updated_content = f"# {topic}\n\n**Category:** {category}\n**Created:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n{content}\n"
            
            # Write to file
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(updated_content)
            
            self.logger.info(f"Updated knowledge base: {topic} -> {filename}")
            return str(file_path)
            
        except Exception as e:
            self.logger.error(f"Error updating knowledge base for topic {topic}: {e}")
            raise
    
    # =========================================================================
    # Report Generation
    # =========================================================================
    
    def generate_findings_report(self, days: int = 30) -> str:
        """
        Generate a report of recent findings.
        
        Args:
            days: Number of days to include in report
            
        Returns:
            Path to the generated report
        """
        try:
            # Load findings index
            findings_index = self._load_findings_index()
            
            # Filter recent findings
            cutoff_date = datetime.now().timestamp() - (days * 24 * 60 * 60)
            recent_findings = [
                f for f in findings_index 
                if datetime.fromisoformat(f['timestamp']).timestamp() > cutoff_date
            ]
            
            # Generate report
            report_content = self._generate_findings_report_content(recent_findings, days)
            
            # Save report
            report_filename = f"findings_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
            report_path = self.reports_path / report_filename
            
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            self.logger.info(f"Generated findings report: {report_filename}")
            return str(report_path)
            
        except Exception as e:
            self.logger.error(f"Error generating findings report: {e}")
            raise
    
    def generate_improvements_report(self, days: int = 30) -> str:
        """
        Generate a report of recent improvements.
        
        Args:
            days: Number of days to include in report
            
        Returns:
            Path to the generated report
        """
        try:
            # Load improvements index
            improvements_index = self._load_improvements_index()
            
            # Filter recent improvements
            cutoff_date = datetime.now().timestamp() - (days * 24 * 60 * 60)
            recent_improvements = [
                i for i in improvements_index 
                if datetime.fromisoformat(i['timestamp']).timestamp() > cutoff_date
            ]
            
            # Generate report
            report_content = self._generate_improvements_report_content(recent_improvements, days)
            
            # Save report
            report_filename = f"improvements_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
            report_path = self.reports_path / report_filename
            
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            self.logger.info(f"Generated improvements report: {report_filename}")
            return str(report_path)
            
        except Exception as e:
            self.logger.error(f"Error generating improvements report: {e}")
            raise
    
    def generate_comprehensive_report(self) -> str:
        """
        Generate a comprehensive report of all AI workspace activities.
        
        Returns:
            Path to the generated comprehensive report
        """
        try:
            # Collect all data
            findings_index = self._load_findings_index()
            improvements_index = self._load_improvements_index()
            
            # Generate comprehensive report
            report_content = self._generate_comprehensive_report_content(findings_index, improvements_index)
            
            # Save report
            report_filename = f"comprehensive_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
            report_path = self.reports_path / report_filename
            
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            self.logger.info(f"Generated comprehensive report: {report_filename}")
            return str(report_path)
            
        except Exception as e:
            self.logger.error(f"Error generating comprehensive report: {e}")
            raise
    
    # =========================================================================
    # Search and Retrieval
    # =========================================================================
    
    def search_findings(self, query: str, category: str = None) -> List[Dict[str, Any]]:
        """
        Search through documented findings.
        
        Args:
            query: Search query
            category: Optional category filter
            
        Returns:
            List of matching findings
        """
        try:
            findings_index = self._load_findings_index()
            
            # Filter by category if specified
            if category:
                findings_index = [f for f in findings_index if f.get('category') == category]
            
            # Search in title and description
            query_lower = query.lower()
            matching_findings = []
            
            for finding in findings_index:
                if (query_lower in finding.get('title', '').lower() or 
                    query_lower in finding.get('description', '').lower()):
                    matching_findings.append(finding)
            
            self.logger.info(f"Found {len(matching_findings)} findings matching query: {query}")
            return matching_findings
            
        except Exception as e:
            self.logger.error(f"Error searching findings: {e}")
            return []

    def search_improvements(self, query: str, component: str = None) -> List[Dict[str, Any]]:
        """
        Search through documented improvements.

        Args:
            query: Search query
            component: Optional component filter

        Returns:
            List of matching improvements
        """
        try:
            improvements_index = self._load_improvements_index()

            # Filter by component if specified
            if component:
                improvements_index = [i for i in improvements_index if i.get('component') == component]

            # Search in title and description
            query_lower = query.lower()
            matching_improvements = []

            for improvement in improvements_index:
                if (query_lower in improvement.get('title', '').lower() or
                    query_lower in improvement.get('description', '').lower()):
                    matching_improvements.append(improvement)

            self.logger.info(f"Found {len(matching_improvements)} improvements matching query: {query}")
            return matching_improvements

        except Exception as e:
            self.logger.error(f"Error searching improvements: {e}")
            return []

    # =========================================================================
    # Utility Methods
    # =========================================================================

    def _generate_finding_id(self, title: str) -> str:
        """Generate unique ID for finding."""
        return self._sanitize_filename(title)[:20]

    def _generate_improvement_id(self, title: str) -> str:
        """Generate unique ID for improvement."""
        return self._sanitize_filename(title)[:20]

    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for filesystem compatibility."""
        import re
        # Remove special characters and replace spaces with underscores
        sanitized = re.sub(r'[^\w\s-]', '', filename)
        sanitized = re.sub(r'[-\s]+', '_', sanitized)
        return sanitized.lower()

    def _generate_finding_markdown(self, finding: Dict[str, Any]) -> str:
        """Generate markdown content for finding."""
        content = f"""# {finding['title']}

**ID:** {finding['id']}
**Category:** {finding['category']}
**Timestamp:** {finding['timestamp']}

## Description

{finding['description']}
"""

        if 'technical_details' in finding:
            content += f"\n## Technical Details\n\n{finding['technical_details']}\n"

        if 'impact' in finding:
            content += f"\n## Impact\n\n{finding['impact']}\n"

        if 'recommendations' in finding:
            content += f"\n## Recommendations\n\n{finding['recommendations']}\n"

        if 'related_components' in finding:
            content += f"\n## Related Components\n\n{', '.join(finding['related_components'])}\n"

        return content

    def _generate_improvement_markdown(self, improvement: Dict[str, Any]) -> str:
        """Generate markdown content for improvement."""
        content = f"""# {improvement['title']}

**ID:** {improvement['id']}
**Component:** {improvement['component']}
**Impact:** {improvement['impact']}
**Timestamp:** {improvement['timestamp']}

## Description

{improvement['description']}
"""

        if 'before_state' in improvement:
            content += f"\n## Before State\n\n{improvement['before_state']}\n"

        if 'after_state' in improvement:
            content += f"\n## After State\n\n{improvement['after_state']}\n"

        if 'implementation_details' in improvement:
            content += f"\n## Implementation Details\n\n{improvement['implementation_details']}\n"

        if 'metrics' in improvement:
            content += f"\n## Metrics\n\n{improvement['metrics']}\n"

        if 'validation' in improvement:
            content += f"\n## Validation\n\n{improvement['validation']}\n"

        return content

    def _update_findings_index(self, finding: Dict[str, Any], filename: str):
        """Update the findings index."""
        try:
            index_path = self.docs_path / "findings_index.json"

            # Load existing index
            if index_path.exists():
                with open(index_path, 'r', encoding='utf-8') as f:
                    index = json.load(f)
            else:
                index = []

            # Add new finding
            index_entry = {
                'id': finding['id'],
                'title': finding['title'],
                'category': finding['category'],
                'timestamp': finding['timestamp'],
                'filename': filename
            }
            index.append(index_entry)

            # Save updated index
            with open(index_path, 'w', encoding='utf-8') as f:
                json.dump(index, f, indent=2)

        except Exception as e:
            self.logger.error(f"Error updating findings index: {e}")

    def _update_improvements_index(self, improvement: Dict[str, Any], filename: str):
        """Update the improvements index."""
        try:
            index_path = self.docs_path / "improvements_index.json"

            # Load existing index
            if index_path.exists():
                with open(index_path, 'r', encoding='utf-8') as f:
                    index = json.load(f)
            else:
                index = []

            # Add new improvement
            index_entry = {
                'id': improvement['id'],
                'title': improvement['title'],
                'component': improvement['component'],
                'impact': improvement['impact'],
                'timestamp': improvement['timestamp'],
                'filename': filename
            }
            index.append(index_entry)

            # Save updated index
            with open(index_path, 'w', encoding='utf-8') as f:
                json.dump(index, f, indent=2)

        except Exception as e:
            self.logger.error(f"Error updating improvements index: {e}")

    def _load_findings_index(self) -> List[Dict[str, Any]]:
        """Load the findings index."""
        try:
            index_path = self.docs_path / "findings_index.json"
            if index_path.exists():
                with open(index_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return []
        except Exception as e:
            self.logger.error(f"Error loading findings index: {e}")
            return []

    def _load_improvements_index(self) -> List[Dict[str, Any]]:
        """Load the improvements index."""
        try:
            index_path = self.docs_path / "improvements_index.json"
            if index_path.exists():
                with open(index_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return []
        except Exception as e:
            self.logger.error(f"Error loading improvements index: {e}")
            return []
