"""
Benchmark Tools Collection

Intelligent selection of existing benchmark-oriented tools from real_condition_tests
for AI agents working on system improvements.
"""

import asyncio
import json
import logging
import os
import sys
import django
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from pathlib import Path

# Setup Django if not already configured
if not django.conf.settings.configured:
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')
    django.setup()

from apps.main.models import BenchmarkScenario, EvaluationCriteriaTemplate, BenchmarkRun
from apps.user.models import UserProfile
from django.test import Client
from django.contrib.auth.models import User
from asgiref.sync import sync_to_async

logger = logging.getLogger(__name__)


class BenchmarkTools:
    """
    Collection of intelligent benchmark tools curated from real_condition_tests.
    
    This class provides AI agents with access to proven benchmark testing tools
    that have been validated in real conditions.
    """
    
    def __init__(self):
        self.logger = logger
        self.client = None
        self.admin_user = None
    
    async def initialize(self):
        """Initialize Django test client and admin user."""
        try:
            # Create or get admin user
            self.admin_user = await sync_to_async(User.objects.get_or_create)(
                username='ai_workspace_admin',
                defaults={'is_superuser': True, 'is_staff': True}
            )
            self.admin_user = self.admin_user[0]
            
            # Create test client
            self.client = Client()
            await sync_to_async(self.client.force_login)(self.admin_user)
            
            self.logger.info("Initialized benchmark tools with admin access")
            
        except Exception as e:
            self.logger.error(f"Error initializing benchmark tools: {e}")
            raise
    
    # =========================================================================
    # Quick Test Execution (from test_quick_test_exact_path.py)
    # =========================================================================
    
    async def run_quick_benchmark_test(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Run a quick benchmark test using the exact path from test_quick_test_exact_path.py
        
        Args:
            config: Configuration dictionary with test parameters
            
        Returns:
            Test results dictionary
        """
        try:
            if not self.client:
                await self.initialize()
            
            self.logger.info("Starting quick benchmark test")
            
            # Default configuration
            default_config = {
                "scenario": "Test Wheel Generation with Agent Communications (workflow)",
                "runs": "1",
                "semanticEval": True,
                "executionMode": "full-real",
                "userProfile": "PhiPhi (Trust: 65 | 22y, Male)",
                "template": "Contextual Wheel Generation Evaluation (contextual)",
                "detailedLogging": True,
                "saveArtifacts": False,
                "timeout": 300,
                "retryAttempts": 1
            }
            
            # Merge with provided config
            test_config = {**default_config, **config}
            
            # Step 1: Find scenario
            scenario = await self._find_scenario(test_config["scenario"])
            if not scenario:
                return {'error': 'Scenario not found', 'config': test_config}
            
            # Step 2: Find evaluation template
            template = await self._find_evaluation_template(test_config["template"])
            if not template:
                return {'error': 'Evaluation template not found', 'config': test_config}
            
            # Step 3: Find user profile
            user_profile = await self._find_user_profile(test_config["userProfile"])
            if not user_profile:
                return {'error': 'User profile not found', 'config': test_config}
            
            # Step 4: Prepare test parameters
            test_params = await self._prepare_test_parameters(scenario, template, user_profile, test_config)
            
            # Step 5: Execute benchmark
            result = await self._execute_benchmark(test_params)
            
            # Step 6: Analyze results
            analysis = await self._analyze_benchmark_results(result)
            
            return {
                'status': 'completed',
                'config': test_config,
                'scenario_id': scenario.id,
                'template_id': template.id,
                'user_profile_id': user_profile.id,
                'result': result,
                'analysis': analysis
            }
            
        except Exception as e:
            self.logger.error(f"Error running quick benchmark test: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'config': config
            }
    
    async def validate_benchmark_system(self) -> Dict[str, Any]:
        """
        Validate the benchmark system integrity using real condition tests.
        
        Returns:
            Validation results dictionary
        """
        try:
            validation_results = {
                'timestamp': datetime.now().isoformat(),
                'status': 'running',
                'checks': [],
                'overall_health': 'unknown'
            }
            
            # Check 1: Database connectivity and models
            db_check = await self._check_database_health()
            validation_results['checks'].append(db_check)
            
            # Check 2: Scenario availability
            scenario_check = await self._check_scenario_availability()
            validation_results['checks'].append(scenario_check)
            
            # Check 3: User profiles
            user_check = await self._check_user_profiles()
            validation_results['checks'].append(user_check)
            
            # Check 4: LLM configurations
            llm_check = await self._check_llm_configurations()
            validation_results['checks'].append(llm_check)
            
            # Check 5: Celery task system
            celery_check = await self._check_celery_system()
            validation_results['checks'].append(celery_check)
            
            # Determine overall health
            passed_checks = sum(1 for check in validation_results['checks'] if check['passed'])
            total_checks = len(validation_results['checks'])
            
            if passed_checks == total_checks:
                validation_results['overall_health'] = 'excellent'
            elif passed_checks >= total_checks * 0.8:
                validation_results['overall_health'] = 'good'
            elif passed_checks >= total_checks * 0.6:
                validation_results['overall_health'] = 'fair'
            else:
                validation_results['overall_health'] = 'poor'
            
            validation_results['status'] = 'completed'
            validation_results['health_score'] = (passed_checks / total_checks) * 100
            
            self.logger.info(f"Benchmark system validation completed: {validation_results['overall_health']}")
            return validation_results
            
        except Exception as e:
            self.logger.error(f"Error validating benchmark system: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    # =========================================================================
    # User ID Consistency Testing (from test_user_id_consistency.py)
    # =========================================================================
    
    async def test_user_id_consistency(self, user_profile_name: str = "PhiPhi") -> Dict[str, Any]:
        """
        Test user ID consistency in benchmark runs to detect test-user-123 issues.
        
        Args:
            user_profile_name: Name of user profile to test
            
        Returns:
            Consistency test results
        """
        try:
            # Find user profile
            user_profile_qs = UserProfile.objects.filter(
                profile_name=user_profile_name,
                is_real=False
            )
            user_profile = await sync_to_async(user_profile_qs.first)()

            if not user_profile:
                return {'error': f'User profile {user_profile_name} not found'}
            
            # Run a test benchmark
            test_config = {
                "scenario": "Test Wheel Generation with Agent Communications (workflow)",
                "runs": "1",
                "executionMode": "full-real",
                "userProfile": user_profile_name
            }
            
            result = await self.run_quick_benchmark_test(test_config)
            
            if result.get('status') != 'completed':
                return {
                    'status': 'failed',
                    'error': 'Benchmark test failed',
                    'details': result
                }
            
            # Check for user ID consistency
            consistency_check = await self._check_user_id_in_results(result, user_profile.id)
            
            return {
                'status': 'completed',
                'user_profile': user_profile_name,
                'user_id': user_profile.id,
                'consistency_check': consistency_check,
                'benchmark_result': result
            }
            
        except Exception as e:
            self.logger.error(f"Error testing user ID consistency: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    # =========================================================================
    # Workflow Testing (from test_tailor_activity_workflow.py)
    # =========================================================================
    
    async def test_activity_tailoring_workflow(self) -> Dict[str, Any]:
        """
        Test the activity tailoring workflow quality.
        
        Returns:
            Workflow test results
        """
        try:
            # Run workflow-specific benchmark
            test_config = {
                "scenario": "Test Wheel Generation with Agent Communications (workflow)",
                "runs": "1",
                "executionMode": "full-real",
                "semanticEval": True,
                "template": "Contextual Wheel Generation Evaluation (contextual)"
            }
            
            result = await self.run_quick_benchmark_test(test_config)
            
            if result.get('status') != 'completed':
                return {
                    'status': 'failed',
                    'error': 'Workflow benchmark failed',
                    'details': result
                }
            
            # Analyze workflow quality
            workflow_analysis = await self._analyze_workflow_quality(result)
            
            return {
                'status': 'completed',
                'workflow_analysis': workflow_analysis,
                'benchmark_result': result
            }
            
        except Exception as e:
            self.logger.error(f"Error testing activity tailoring workflow: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }

    # =========================================================================
    # Utility Methods
    # =========================================================================

    async def _find_scenario(self, scenario_name: str):
        """Find benchmark scenario by name."""
        scenario_qs = BenchmarkScenario.objects.filter(
            name__icontains=scenario_name.split('(')[0].strip()
        )
        return await sync_to_async(scenario_qs.first)()

    async def _find_evaluation_template(self, template_name: str):
        """Find evaluation template by name."""
        template_qs = EvaluationCriteriaTemplate.objects.filter(
            name__icontains=template_name.split('(')[0].strip()
        )
        return await sync_to_async(template_qs.first)()

    async def _find_user_profile(self, user_profile_desc: str):
        """Find user profile by description."""
        # Extract profile name from description like "PhiPhi (Trust: 65 | 22y, Male)"
        profile_name = user_profile_desc.split('(')[0].strip()
        profile_qs = UserProfile.objects.filter(
            profile_name=profile_name,
            is_real=False
        )
        return await sync_to_async(profile_qs.first)()

    async def _prepare_test_parameters(self, scenario, template, user_profile, config):
        """Prepare test parameters for benchmark execution."""
        execution_mode_params = {
            'use_real_llm': True,
            'use_real_tools': True,
            'use_real_db': True
        }

        return {
            'scenario_id': scenario.id,
            'evaluation_template_id': template.id,
            'params': {
                'runs': int(config['runs']),
                'semantic_evaluation': config['semanticEval'],
                'context_variables': {
                    'trust_level': 35  # Default trust level
                },
                **execution_mode_params,
                'user_profile_id': str(user_profile.id)
            }
        }

    async def _execute_benchmark(self, test_params):
        """Execute benchmark using Django test client."""
        def make_api_call():
            response = self.client.post(
                '/admin/benchmarks/api/run/',
                data=json.dumps(test_params),
                content_type='application/json'
            )
            return response

        response = await sync_to_async(make_api_call)()

        if response.status_code != 200:
            raise Exception(f"API call failed with status {response.status_code}: {response.content}")

        result = json.loads(response.content)
        task_id = result.get('task_id')

        if not task_id:
            raise Exception("No task ID returned from benchmark API")

        # Poll for completion
        return await self._poll_task_completion(task_id)

    async def _poll_task_completion(self, task_id, max_attempts=60):
        """Poll task until completion."""
        for attempt in range(max_attempts):
            await asyncio.sleep(10)  # Wait 10 seconds between checks

            def check_status():
                response = self.client.get(f'/admin/benchmarks/api/task/{task_id}/status/')
                return response

            status_response = await sync_to_async(check_status)()

            if status_response.status_code == 200:
                status_result = json.loads(status_response.content)
                status = status_result.get('status', 'unknown')

                if status in ['SUCCESS', 'completed']:
                    return status_result.get('result', {})
                elif status in ['FAILURE', 'REVOKED', 'failed']:
                    raise Exception(f"Task failed with status: {status}")

        raise Exception("Task did not complete within timeout")

    async def _analyze_benchmark_results(self, result):
        """Analyze benchmark results for insights."""
        analysis = {
            'summary': {},
            'quality_indicators': {},
            'issues_found': [],
            'recommendations': []
        }

        if 'runs' in result and result['runs']:
            first_run = result['runs'][0]

            # Check for test-user-123 occurrences
            result_str = json.dumps(result)
            test_user_count = result_str.count('test-user-123')

            analysis['summary'] = {
                'total_runs': len(result['runs']),
                'test_user_123_occurrences': test_user_count,
                'has_agent_communications': 'agent_communications' in first_run,
                'has_semantic_evaluation': 'semantic_score' in first_run
            }

            if test_user_count > 0:
                analysis['issues_found'].append({
                    'type': 'user_id_consistency',
                    'severity': 'high',
                    'description': f'Found {test_user_count} occurrences of test-user-123',
                    'recommendation': 'Check user ID handling in workflow execution'
                })

        return analysis

    async def _check_database_health(self):
        """Check database connectivity and basic model access."""
        try:
            # Test basic model queries
            scenario_count = await sync_to_async(BenchmarkScenario.objects.count)()
            user_count = await sync_to_async(UserProfile.objects.count)()

            return {
                'check': 'database_health',
                'passed': True,
                'details': {
                    'scenarios_available': scenario_count,
                    'user_profiles_available': user_count
                }
            }
        except Exception as e:
            return {
                'check': 'database_health',
                'passed': False,
                'error': str(e)
            }

    async def _check_scenario_availability(self):
        """Check if required scenarios are available."""
        try:
            required_scenarios = [
                "Test Wheel Generation with Agent Communications",
                "wheel_generation"
            ]

            available_scenarios = []
            for scenario_name in required_scenarios:
                scenario_qs = BenchmarkScenario.objects.filter(
                    name__icontains=scenario_name
                )
                scenario = await sync_to_async(scenario_qs.first)()
                if scenario:
                    available_scenarios.append(scenario.name)

            return {
                'check': 'scenario_availability',
                'passed': len(available_scenarios) > 0,
                'details': {
                    'required_scenarios': required_scenarios,
                    'available_scenarios': available_scenarios
                }
            }
        except Exception as e:
            return {
                'check': 'scenario_availability',
                'passed': False,
                'error': str(e)
            }
