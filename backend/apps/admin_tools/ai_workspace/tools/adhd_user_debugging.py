"""
ADHD User Experience Debugging Tool

Specialized debugging tool for addressing ADHD user experience issues:
1. Immediate wheel generation instead of mood assessment
2. Duplicated wheel items with same IDs
"""

import asyncio
import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path

from .agent_debugging_system import AgentDebuggingSystem, GenericSituation, GenericUser
from .benchmark_tools import BenchmarkTools

logger = logging.getLogger(__name__)


class ADHDUserDebuggingTool:
    """
    Specialized debugging tool for ADHD user experience issues.
    
    Focuses on:
    - First-time user interaction patterns
    - Mood assessment before activity generation
    - Unique activity ID generation
    - ADHD-friendly communication patterns
    """
    
    def __init__(self, workspace_path: str = None):
        self.logger = logger
        self.workspace_path = Path(workspace_path) if workspace_path else Path(__file__).parent.parent
        
        # Initialize core debugging system
        self.debugging_system = AgentDebuggingSystem(workspace_path)
        self.benchmark_tools = BenchmarkTools()
        
        # Load ADHD-specific archetypes and situations
        self.adhd_archetypes = self._load_adhd_archetypes()
        self.first_time_scenarios = self._load_first_time_scenarios()
    
    def _load_adhd_archetypes(self) -> Dict[str, GenericUser]:
        """Load ADHD-specific user archetypes."""
        archetypes = {}
        adhd_file = self.workspace_path / "generic_users" / "adhd_archetypes.json"
        
        if adhd_file.exists():
            try:
                with open(adhd_file, 'r') as f:
                    data = json.load(f)
                    for item in data:
                        archetype = GenericUser(**item)
                        archetypes[archetype.id] = archetype
            except Exception as e:
                self.logger.error(f"Error loading ADHD archetypes: {e}")
        
        return archetypes
    
    def _load_first_time_scenarios(self) -> Dict[str, GenericSituation]:
        """Load first-time user scenarios."""
        scenarios = {}
        scenarios_file = self.workspace_path / "generic_situations" / "first_time_user_scenarios.json"
        
        if scenarios_file.exists():
            try:
                with open(scenarios_file, 'r') as f:
                    data = json.load(f)
                    for item in data:
                        scenario = GenericSituation(**item)
                        scenarios[scenario.id] = scenario
            except Exception as e:
                self.logger.error(f"Error loading first-time scenarios: {e}")
        
        return scenarios
    
    async def test_current_adhd_experience(self) -> Dict[str, Any]:
        """
        Test the current ADHD user experience to identify issues.
        
        Returns comprehensive analysis of current behavior.
        """
        self.logger.info("🧪 Testing current ADHD user experience...")
        
        results = {
            "test_timestamp": datetime.now().isoformat(),
            "issues_found": [],
            "test_results": {},
            "recommendations": []
        }
        
        # Test 1: First-time user with wellness request
        self.logger.info("Testing first-time user wellness request...")
        test1_result = await self._test_first_time_wellness_request()
        results["test_results"]["first_time_wellness"] = test1_result
        
        # Analyze for immediate wheel generation issue
        if self._detects_immediate_wheel_generation(test1_result):
            results["issues_found"].append({
                "issue": "immediate_wheel_generation",
                "description": "System generates wheel immediately instead of asking about mood",
                "severity": "high",
                "evidence": test1_result.get("evidence", {})
            })
        
        # Test 2: Check for duplicate activity IDs
        if "wheel" in test1_result.get("output_data", {}):
            duplicate_ids = self._check_duplicate_activity_ids(test1_result["output_data"]["wheel"])
            if duplicate_ids:
                results["issues_found"].append({
                    "issue": "duplicate_activity_ids",
                    "description": "Multiple activities have the same ID",
                    "severity": "medium",
                    "evidence": {"duplicate_ids": duplicate_ids}
                })
        
        # Generate recommendations
        results["recommendations"] = self._generate_recommendations(results["issues_found"])
        
        return results
    
    async def _test_first_time_wellness_request(self) -> Dict[str, Any]:
        """Test how system handles first-time user wellness request."""
        try:
            # Use the benchmark system to test
            result = await self.benchmark_tools.run_quick_benchmark_test({
                "scenario": "Test ADHD First-Time User Experience",
                "executionMode": "full-real",
                "userProfile": "PhiPhi",  # Using existing user for now
                "customCriteria": {
                    "name": "ADHD First-Time User Mood Assessment",
                    "description": "Test if system asks about mood before generating activities",
                    "criteria": [
                        {
                            "name": "Mood Inquiry",
                            "description": "Should ask about user's current mood",
                            "weight": 0.4
                        },
                        {
                            "name": "No Immediate Wheel",
                            "description": "Should not immediately generate activity wheel",
                            "weight": 0.6
                        }
                    ]
                }
            })
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error testing first-time wellness request: {e}")
            return {"error": str(e)}
    
    def _detects_immediate_wheel_generation(self, test_result: Dict[str, Any]) -> bool:
        """Check if the test result shows immediate wheel generation."""
        if "output_data" not in test_result:
            return False
        
        output_data = test_result["output_data"]
        
        # Check if wheel was generated
        has_wheel = "wheel" in output_data
        
        # Check if user_response asks about mood
        user_response = output_data.get("user_response", "")
        asks_about_mood = any(keyword in user_response.lower() for keyword in [
            "how are you feeling", "what's your mood", "how's your energy",
            "tell me about your mood", "how are you doing today"
        ])
        
        # Issue detected if wheel generated without mood inquiry
        return has_wheel and not asks_about_mood
    
    def _check_duplicate_activity_ids(self, wheel_data: Dict[str, Any]) -> List[int]:
        """Check for duplicate activity IDs in wheel data."""
        if "items" not in wheel_data:
            return []
        
        ids = [item.get("id") for item in wheel_data["items"]]
        id_counts = {}
        
        for id_val in ids:
            if id_val is not None:
                id_counts[id_val] = id_counts.get(id_val, 0) + 1
        
        # Return IDs that appear more than once
        return [id_val for id_val, count in id_counts.items() if count > 1]
    
    def _generate_recommendations(self, issues_found: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Generate recommendations based on issues found."""
        recommendations = []
        
        for issue in issues_found:
            if issue["issue"] == "immediate_wheel_generation":
                recommendations.append({
                    "issue": "immediate_wheel_generation",
                    "recommendation": "Modify ConversationDispatcher to classify first-time wellness requests as 'discussion' workflow",
                    "implementation": "Update message classification logic to detect first-time users and route to discussion instead of wheel_generation",
                    "priority": "high",
                    "components_affected": ["ConversationDispatcher", "Mentor Agent"]
                })
            
            elif issue["issue"] == "duplicate_activity_ids":
                recommendations.append({
                    "issue": "duplicate_activity_ids",
                    "recommendation": "Fix Activity Agent to generate unique IDs for each wheel item",
                    "implementation": "Update wheel item creation to use incremental or UUID-based ID generation",
                    "priority": "medium",
                    "components_affected": ["Activity Agent", "Wheel generation logic"]
                })
        
        return recommendations
    
    async def test_proposed_fixes(self) -> Dict[str, Any]:
        """Test proposed fixes for ADHD user experience issues."""
        self.logger.info("🔧 Testing proposed fixes...")
        
        # This would test the fixes after they're implemented
        # For now, return a placeholder structure
        return {
            "test_timestamp": datetime.now().isoformat(),
            "fixes_tested": [],
            "improvement_metrics": {},
            "validation_status": "pending_implementation"
        }
