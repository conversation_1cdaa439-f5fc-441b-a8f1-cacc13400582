"""
Agent Manager Tool

AI-intuitive tool for managing Goali agent characteristics, instructions, and LLM configurations.
Provides comprehensive agent management capabilities for AI agents working on system improvements.
"""

import json
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from django.db import transaction
from django.core.exceptions import ValidationError

from apps.main.models import GenericAgent, LLMConfig, AgentTool, BenchmarkMetric
from apps.main.models import AgentRole

logger = logging.getLogger(__name__)


class AgentManager:
    """
    AI-intuitive tool for managing agent characteristics and configurations.
    
    This tool provides comprehensive capabilities for AI agents to read and update
    agent definitions, LLM configurations, and related settings in the Goali system.
    """
    
    def __init__(self):
        self.logger = logger
        self._backup_enabled = True
        self._validation_enabled = True
    
    # =========================================================================
    # Agent Reading Operations
    # =========================================================================
    
    def get_all_agents(self) -> List[Dict[str, Any]]:
        """
        Get all agent definitions with their complete configurations.
        
        Returns:
            List of agent dictionaries with full configuration data
        """
        try:
            agents = GenericAgent.objects.select_related('llm_config').prefetch_related(
                'available_tools', 'benchmark_metrics'
            ).all()
            
            agent_list = []
            for agent in agents:
                agent_data = self._serialize_agent(agent)
                agent_list.append(agent_data)
            
            self.logger.info(f"Retrieved {len(agent_list)} agent definitions")
            return agent_list
            
        except Exception as e:
            self.logger.error(f"Error retrieving agents: {e}")
            raise
    
    def get_agent_by_role(self, role: str) -> Optional[Dict[str, Any]]:
        """
        Get agent definition by role.
        
        Args:
            role: Agent role (e.g., 'mentor', 'orchestrator', 'debugger')
            
        Returns:
            Agent dictionary or None if not found
        """
        try:
            agent = GenericAgent.objects.select_related('llm_config').prefetch_related(
                'available_tools', 'benchmark_metrics'
            ).filter(role=role, is_active=True).first()
            
            if agent:
                agent_data = self._serialize_agent(agent)
                self.logger.info(f"Retrieved agent definition for role: {role}")
                return agent_data
            else:
                self.logger.warning(f"No active agent found for role: {role}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error retrieving agent by role {role}: {e}")
            raise
    
    def get_agent_instructions(self, role: str) -> Optional[str]:
        """
        Get agent system instructions by role.
        
        Args:
            role: Agent role
            
        Returns:
            System instructions string or None if not found
        """
        try:
            agent = GenericAgent.objects.filter(role=role, is_active=True).first()
            if agent:
                return agent.system_instructions
            return None
            
        except Exception as e:
            self.logger.error(f"Error retrieving instructions for role {role}: {e}")
            raise
    
    def get_agent_schemas(self, role: str) -> Dict[str, Any]:
        """
        Get agent input/output/state/memory schemas by role.
        
        Args:
            role: Agent role
            
        Returns:
            Dictionary containing all schemas
        """
        try:
            agent = GenericAgent.objects.filter(role=role, is_active=True).first()
            if agent:
                return {
                    'input_schema': agent.input_schema,
                    'output_schema': agent.output_schema,
                    'state_schema': agent.state_schema,
                    'memory_schema': agent.memory_schema
                }
            return {}
            
        except Exception as e:
            self.logger.error(f"Error retrieving schemas for role {role}: {e}")
            raise
    
    # =========================================================================
    # Agent Update Operations
    # =========================================================================
    
    def update_agent_instructions(self, role: str, instructions: str, backup: bool = True) -> bool:
        """
        Update agent system instructions.
        
        Args:
            role: Agent role
            instructions: New system instructions
            backup: Whether to create backup before update
            
        Returns:
            True if successful, False otherwise
        """
        try:
            with transaction.atomic():
                agent = GenericAgent.objects.filter(role=role, is_active=True).first()
                if not agent:
                    self.logger.error(f"No active agent found for role: {role}")
                    return False
                
                # Create backup if requested
                if backup and self._backup_enabled:
                    self._create_agent_backup(agent, 'instructions_update')
                
                # Validate instructions if validation is enabled
                if self._validation_enabled:
                    if not self._validate_instructions(instructions):
                        self.logger.error("Instructions validation failed")
                        return False
                
                # Update instructions
                old_instructions = agent.system_instructions
                agent.system_instructions = instructions
                agent.save()
                
                self.logger.info(f"Updated instructions for agent role: {role}")
                self.logger.debug(f"Instructions changed from {len(old_instructions)} to {len(instructions)} characters")
                
                return True
                
        except Exception as e:
            self.logger.error(f"Error updating instructions for role {role}: {e}")
            return False
    
    def update_agent_schema(self, role: str, schema_type: str, schema_data: Dict[str, Any], 
                           backup: bool = True) -> bool:
        """
        Update agent schema (input, output, state, or memory).
        
        Args:
            role: Agent role
            schema_type: Type of schema ('input', 'output', 'state', 'memory')
            schema_data: New schema data
            backup: Whether to create backup before update
            
        Returns:
            True if successful, False otherwise
        """
        try:
            with transaction.atomic():
                agent = GenericAgent.objects.filter(role=role, is_active=True).first()
                if not agent:
                    self.logger.error(f"No active agent found for role: {role}")
                    return False
                
                # Create backup if requested
                if backup and self._backup_enabled:
                    self._create_agent_backup(agent, f'{schema_type}_schema_update')
                
                # Validate schema if validation is enabled
                if self._validation_enabled:
                    if not self._validate_json_schema(schema_data):
                        self.logger.error(f"Schema validation failed for {schema_type}")
                        return False
                
                # Update the appropriate schema
                if schema_type == 'input':
                    agent.input_schema = schema_data
                elif schema_type == 'output':
                    agent.output_schema = schema_data
                elif schema_type == 'state':
                    agent.state_schema = schema_data
                elif schema_type == 'memory':
                    agent.memory_schema = schema_data
                else:
                    self.logger.error(f"Invalid schema type: {schema_type}")
                    return False
                
                agent.save()
                
                self.logger.info(f"Updated {schema_type} schema for agent role: {role}")
                return True
                
        except Exception as e:
            self.logger.error(f"Error updating {schema_type} schema for role {role}: {e}")
            return False

    # =========================================================================
    # LLM Configuration Operations
    # =========================================================================

    def get_all_llm_configs(self) -> List[Dict[str, Any]]:
        """Get all LLM configurations."""
        try:
            configs = LLMConfig.objects.all()
            config_list = []

            for config in configs:
                config_data = {
                    'id': config.id,
                    'name': config.name,
                    'model_name': config.model_name,
                    'temperature': config.temperature,
                    'max_tokens': config.max_tokens,
                    'top_p': config.top_p,
                    'frequency_penalty': config.frequency_penalty,
                    'presence_penalty': config.presence_penalty,
                    'is_default': config.is_default,
                    'is_evaluation': config.is_evaluation,
                    'provider': config.provider,
                    'api_base': config.api_base,
                    'description': config.description
                }
                config_list.append(config_data)

            self.logger.info(f"Retrieved {len(config_list)} LLM configurations")
            return config_list

        except Exception as e:
            self.logger.error(f"Error retrieving LLM configurations: {e}")
            raise

    def get_llm_config_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        """Get LLM configuration by name."""
        try:
            config = LLMConfig.objects.filter(name=name).first()
            if config:
                return self._serialize_llm_config(config)
            return None

        except Exception as e:
            self.logger.error(f"Error retrieving LLM config {name}: {e}")
            raise

    def update_agent_llm_config(self, role: str, llm_config_name: str, backup: bool = True) -> bool:
        """
        Update agent's LLM configuration.

        Args:
            role: Agent role
            llm_config_name: Name of the LLM configuration to assign
            backup: Whether to create backup before update

        Returns:
            True if successful, False otherwise
        """
        try:
            with transaction.atomic():
                agent = GenericAgent.objects.filter(role=role, is_active=True).first()
                if not agent:
                    self.logger.error(f"No active agent found for role: {role}")
                    return False

                llm_config = LLMConfig.objects.filter(name=llm_config_name).first()
                if not llm_config:
                    self.logger.error(f"No LLM config found with name: {llm_config_name}")
                    return False

                # Create backup if requested
                if backup and self._backup_enabled:
                    self._create_agent_backup(agent, 'llm_config_update')

                # Update LLM config
                old_config = agent.llm_config
                agent.llm_config = llm_config
                agent.save()

                old_name = old_config.name if old_config else 'None'
                self.logger.info(f"Updated LLM config for agent {role}: {old_name} -> {llm_config_name}")

                return True

        except Exception as e:
            self.logger.error(f"Error updating LLM config for role {role}: {e}")
            return False

    def create_llm_config(self, config_data: Dict[str, Any]) -> Optional[int]:
        """
        Create new LLM configuration.

        Args:
            config_data: Dictionary containing LLM configuration parameters

        Returns:
            ID of created configuration or None if failed
        """
        try:
            with transaction.atomic():
                # Validate required fields
                required_fields = ['name', 'model_name']
                for field in required_fields:
                    if field not in config_data:
                        self.logger.error(f"Missing required field: {field}")
                        return None

                # Create new LLM config
                llm_config = LLMConfig.objects.create(**config_data)

                self.logger.info(f"Created new LLM configuration: {llm_config.name} (ID: {llm_config.id})")
                return llm_config.id

        except Exception as e:
            self.logger.error(f"Error creating LLM configuration: {e}")
            return None

    # =========================================================================
    # Agent Tools Operations
    # =========================================================================

    def get_available_tools(self) -> List[Dict[str, Any]]:
        """Get all available agent tools."""
        try:
            tools = AgentTool.objects.all()
            tool_list = []

            for tool in tools:
                tool_data = {
                    'id': tool.id,
                    'name': tool.name,
                    'description': tool.description,
                    'function_path': tool.function_path,
                    'input_schema': tool.input_schema,
                    'output_schema': tool.output_schema,
                    'is_active': tool.is_active,
                    'category': tool.category,
                    'version': tool.version
                }
                tool_list.append(tool_data)

            self.logger.info(f"Retrieved {len(tool_list)} available tools")
            return tool_list

        except Exception as e:
            self.logger.error(f"Error retrieving available tools: {e}")
            raise

    def get_agent_tools(self, role: str) -> List[Dict[str, Any]]:
        """Get tools assigned to a specific agent."""
        try:
            agent = GenericAgent.objects.prefetch_related('available_tools').filter(
                role=role, is_active=True
            ).first()

            if not agent:
                self.logger.warning(f"No active agent found for role: {role}")
                return []

            tools = []
            for tool in agent.available_tools.all():
                tool_data = {
                    'id': tool.id,
                    'name': tool.name,
                    'description': tool.description,
                    'function_path': tool.function_path,
                    'category': tool.category
                }
                tools.append(tool_data)

            self.logger.info(f"Retrieved {len(tools)} tools for agent role: {role}")
            return tools

        except Exception as e:
            self.logger.error(f"Error retrieving tools for role {role}: {e}")
            raise

    # =========================================================================
    # Utility Methods
    # =========================================================================

    def _serialize_agent(self, agent: GenericAgent) -> Dict[str, Any]:
        """Serialize agent object to dictionary."""
        return {
            'id': agent.id,
            'role': agent.role,
            'version': agent.version,
            'is_active': agent.is_active,
            'description': agent.description,
            'system_instructions': agent.system_instructions,
            'input_schema': agent.input_schema,
            'output_schema': agent.output_schema,
            'state_schema': agent.state_schema,
            'memory_schema': agent.memory_schema,
            'read_models': agent.read_models,
            'write_models': agent.write_models,
            'recommend_models': agent.recommend_models,
            'langgraph_node_class': agent.langgraph_node_class,
            'processing_timeout': agent.processing_timeout,
            'llm_config': self._serialize_llm_config(agent.llm_config) if agent.llm_config else None,
            'available_tools': [
                {'id': tool.id, 'name': tool.name, 'description': tool.description}
                for tool in agent.available_tools.all()
            ],
            'benchmark_metrics': [
                {'id': metric.id, 'name': metric.name, 'description': metric.description}
                for metric in agent.benchmark_metrics.all()
            ]
        }

    def _serialize_llm_config(self, config: LLMConfig) -> Dict[str, Any]:
        """Serialize LLM config object to dictionary."""
        return {
            'id': config.id,
            'name': config.name,
            'model_name': config.model_name,
            'temperature': config.temperature,
            'max_tokens': config.max_tokens,
            'top_p': config.top_p,
            'frequency_penalty': config.frequency_penalty,
            'presence_penalty': config.presence_penalty,
            'is_default': config.is_default,
            'is_evaluation': config.is_evaluation,
            'provider': config.provider,
            'api_base': config.api_base,
            'description': config.description
        }

    def _create_agent_backup(self, agent: GenericAgent, operation: str) -> str:
        """Create backup of agent configuration before modification."""
        try:
            backup_data = {
                'timestamp': datetime.now().isoformat(),
                'operation': operation,
                'agent_data': self._serialize_agent(agent)
            }

            # Create backup filename
            backup_filename = f"agent_{agent.role}_{operation}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

            # In a real implementation, you would save this to a backup directory
            # For now, we'll just log it
            self.logger.info(f"Created backup for agent {agent.role}: {backup_filename}")

            return backup_filename

        except Exception as e:
            self.logger.error(f"Error creating backup for agent {agent.role}: {e}")
            return ""

    def _validate_instructions(self, instructions: str) -> bool:
        """Validate agent instructions."""
        if not instructions or not instructions.strip():
            return False

        # Basic validation - instructions should be reasonable length
        if len(instructions) < 50:
            self.logger.warning("Instructions seem too short")
            return False

        if len(instructions) > 10000:
            self.logger.warning("Instructions seem too long")
            return False

        return True

    def _validate_json_schema(self, schema_data: Dict[str, Any]) -> bool:
        """Validate JSON schema structure."""
        try:
            # Basic validation - should be a valid dictionary
            if not isinstance(schema_data, dict):
                return False

            # Should have some basic schema properties
            if 'type' not in schema_data and 'properties' not in schema_data:
                self.logger.warning("Schema missing basic structure")
                return False

            return True

        except Exception as e:
            self.logger.error(f"Error validating schema: {e}")
            return False

    def get_agent_summary(self) -> Dict[str, Any]:
        """Get summary of all agents and their configurations."""
        try:
            agents = GenericAgent.objects.select_related('llm_config').all()

            summary = {
                'total_agents': agents.count(),
                'active_agents': agents.filter(is_active=True).count(),
                'agents_by_role': {},
                'llm_configs_in_use': set(),
                'tools_in_use': set()
            }

            for agent in agents:
                summary['agents_by_role'][agent.role] = {
                    'active': agent.is_active,
                    'version': agent.version,
                    'llm_config': agent.llm_config.name if agent.llm_config else None,
                    'tool_count': agent.available_tools.count()
                }

                if agent.llm_config:
                    summary['llm_configs_in_use'].add(agent.llm_config.name)

                for tool in agent.available_tools.all():
                    summary['tools_in_use'].add(tool.name)

            # Convert sets to lists for JSON serialization
            summary['llm_configs_in_use'] = list(summary['llm_configs_in_use'])
            summary['tools_in_use'] = list(summary['tools_in_use'])

            return summary

        except Exception as e:
            self.logger.error(f"Error generating agent summary: {e}")
            raise
