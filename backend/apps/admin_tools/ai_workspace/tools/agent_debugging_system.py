"""
Agent Debugging System

Comprehensive debugging and improvement system for early-stage agent development.
Focuses on simulating user stories, managing temporary profiles, and measuring agent improvements.
"""

import asyncio
import json
import logging
import uuid
from typing import Dict, List, Optional, Any, Union, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from pathlib import Path

from django.db import transaction
from django.utils import timezone
from django.test import Client
from django.contrib.auth.models import User
from asgiref.sync import sync_to_async

from apps.main.models import (
    BenchmarkScenario, BenchmarkRun, GenericAgent, LLMConfig,
    EvaluationCriteriaTemplate
)
from apps.user.models import UserProfile, Demographics, TrustLevel
from .benchmark_tools import BenchmarkTools

logger = logging.getLogger(__name__)


@dataclass
class GenericSituation:
    """Normalized generic situation (scenario) definition."""
    id: str
    name: str
    description: str
    workflow_type: str
    user_message: str
    context_variables: Dict[str, Any]
    expected_outcomes: List[str]
    evaluation_criteria: Dict[str, Any]
    difficulty_level: int = 1  # 1-5 scale
    tags: List[str] = None
    # New schema fields (optional for backward compatibility)
    version: str = "1.0"
    created_date: str = None
    last_updated: str = None
    scenario_category: str = None
    priority: str = "medium"
    estimated_duration: str = None

    def __post_init__(self):
        if self.tags is None:
            self.tags = []
        if self.created_date is None:
            self.created_date = datetime.now().isoformat()
        if self.last_updated is None:
            self.last_updated = self.created_date


@dataclass
class GenericUser:
    """Normalized generic user archetype definition."""
    id: str
    archetype_name: str
    description: str
    demographics: Dict[str, Any]
    personality_traits: Dict[str, float]
    trust_level: int
    communication_preferences: Dict[str, Any]
    behavioral_patterns: Dict[str, Any]
    limitations: List[str]
    capabilities: List[str]
    is_temporary: bool = True
    # New schema fields (optional for backward compatibility)
    version: str = "1.0"
    created_date: str = None
    last_updated: str = None
    context_variables: Dict[str, Any] = None
    is_real: bool = False
    archetype_category: str = None

    def __post_init__(self):
        if self.context_variables is None:
            self.context_variables = {}
        if self.created_date is None:
            self.created_date = datetime.now().isoformat()
        if self.last_updated is None:
            self.last_updated = self.created_date

    def to_user_profile_data(self) -> Dict[str, Any]:
        """Convert to UserProfile creation data."""
        return {
            'profile_name': self.archetype_name,
            'profile_type': 'Archetype Template',
            'is_real': self.is_real,
            'demographics': self.demographics,
            'personality_traits': self.personality_traits,
            'trust_level': self.trust_level,
            'communication_preferences': self.communication_preferences,
            'behavioral_patterns': self.behavioral_patterns
        }


@dataclass
class AgentChangeSet:
    """Represents a set of changes to test on an agent."""
    change_id: str
    agent_role: str
    change_type: str  # 'instructions', 'llm_config', 'tools', 'schema'
    description: str
    changes: Dict[str, Any]
    expected_impact: str
    created_at: datetime
    
    def __post_init__(self):
        if isinstance(self.created_at, str):
            self.created_at = datetime.fromisoformat(self.created_at)


@dataclass
class ImpactMeasurement:
    """Measures the impact of agent changes."""
    change_id: str
    baseline_metrics: Dict[str, float]
    modified_metrics: Dict[str, float]
    improvement_score: float
    semantic_score_delta: float
    execution_time_delta: float
    success_rate_delta: float
    detailed_analysis: Dict[str, Any]
    measurement_timestamp: datetime
    
    def __post_init__(self):
        if isinstance(self.measurement_timestamp, str):
            self.measurement_timestamp = datetime.fromisoformat(self.measurement_timestamp)


class AgentDebuggingSystem:
    """
    Comprehensive agent debugging and improvement system for early-stage development.
    
    This system provides:
    - User story simulation with generic situations and users
    - Temporary profile management with archetype library
    - Agent change impact measurement
    - Systematic agent improvement workflows
    """
    
    def __init__(self, workspace_path: str = None):
        self.logger = logger
        self.workspace_path = Path(workspace_path) if workspace_path else Path(__file__).parent.parent
        self.profiles_path = self.workspace_path / "profiles"
        self.situations_path = self.workspace_path / "situations"
        self.experiments_path = self.workspace_path / "experiments"
        
        # Initialize benchmark tools
        self.benchmark_tools = BenchmarkTools()
        
        # Ensure directories exist
        self._ensure_directories()
        
        # Load archetype library
        self.archetype_library = self._load_archetype_library()
        self.situation_library = self._load_situation_library()
    
    def _ensure_directories(self):
        """Ensure required directories exist."""
        for path in [self.profiles_path, self.situations_path, self.experiments_path]:
            path.mkdir(parents=True, exist_ok=True)
    
    # =========================================================================
    # Generic Situation Management
    # =========================================================================
    
    def _load_situation_library(self) -> Dict[str, GenericSituation]:
        """Load the library of generic situations."""
        library = {}
        
        # Define core situations for early-stage testing
        core_situations = [
            GenericSituation(
                id="wheel_gen_basic",
                name="Basic Wheel Generation",
                description="User requests activity suggestions for their day",
                workflow_type="wheel_generation",
                user_message="I want to try something new today",
                context_variables={"trust_level": 35, "mood": "curious", "time_available": "2 hours"},
                expected_outcomes=["6-8 diverse activities", "personalized recommendations", "clear instructions"],
                evaluation_criteria={
                    "activity_variety": "Should include 3+ different domains",
                    "personalization": "Should reflect user's trust level and mood",
                    "clarity": "Instructions should be clear and actionable"
                },
                difficulty_level=1,
                tags=["wheel_generation", "basic", "new_user"]
            ),
            GenericSituation(
                id="wheel_gen_stressed",
                name="Stressed User Wheel Generation",
                description="User is overwhelmed and needs calming activities",
                workflow_type="wheel_generation",
                user_message="I'm feeling really overwhelmed and need something to help me relax",
                context_variables={"trust_level": 45, "mood": "stressed", "time_available": "30 minutes"},
                expected_outcomes=["calming activities", "stress-relief focus", "short duration options"],
                evaluation_criteria={
                    "stress_awareness": "Should recognize stressed state",
                    "activity_appropriateness": "Should suggest calming, not energizing activities",
                    "time_sensitivity": "Should respect limited time availability"
                },
                difficulty_level=2,
                tags=["wheel_generation", "stress", "emotional_support"]
            ),
            GenericSituation(
                id="wheel_gen_creative",
                name="Creative Exploration Request",
                description="User wants to explore their creative side",
                workflow_type="wheel_generation",
                user_message="I've been feeling really creative lately and want to try some artistic activities",
                context_variables={"trust_level": 65, "mood": "inspired", "time_available": "3 hours"},
                expected_outcomes=["creative activities", "artistic exploration", "skill building"],
                evaluation_criteria={
                    "creativity_focus": "Should prioritize creative and artistic activities",
                    "skill_progression": "Should offer activities of varying difficulty",
                    "inspiration_support": "Should build on user's inspired mood"
                },
                difficulty_level=2,
                tags=["wheel_generation", "creativity", "artistic", "skill_building"]
            ),
            GenericSituation(
                id="discussion_basic",
                name="Basic Discussion Flow",
                description="User wants to have a conversation about their goals",
                workflow_type="discussion",
                user_message="I'd like to talk about what I want to achieve this month",
                context_variables={"trust_level": 50, "conversation_depth": "medium"},
                expected_outcomes=["engaging conversation", "goal clarification", "supportive responses"],
                evaluation_criteria={
                    "engagement_quality": "Should ask thoughtful follow-up questions",
                    "goal_exploration": "Should help user clarify their objectives",
                    "supportive_tone": "Should maintain encouraging and supportive tone"
                },
                difficulty_level=1,
                tags=["discussion", "goals", "conversation"]
            ),
            GenericSituation(
                id="onboarding_new",
                name="New User Onboarding",
                description="First-time user going through initial setup",
                workflow_type="onboarding",
                user_message="Hi, I'm new here and not sure how this works",
                context_variables={"trust_level": 25, "experience_level": "beginner"},
                expected_outcomes=["clear guidance", "trust building", "system explanation"],
                evaluation_criteria={
                    "clarity": "Should explain system clearly for beginners",
                    "trust_building": "Should establish rapport and confidence",
                    "guidance_quality": "Should provide step-by-step guidance"
                },
                difficulty_level=3,
                tags=["onboarding", "new_user", "trust_building"]
            )
        ]
        
        for situation in core_situations:
            library[situation.id] = situation
        
        # Load additional situations from files if they exist
        situations_file = self.situations_path / "situations.json"
        if situations_file.exists():
            try:
                with open(situations_file, 'r') as f:
                    data = json.load(f)
                    for item in data:
                        situation = GenericSituation(**item)
                        library[situation.id] = situation
            except Exception as e:
                self.logger.warning(f"Error loading situations from file: {e}")
        
        return library

    def get_situations_by_workflow(self, workflow_type: str) -> List[GenericSituation]:
        """Get all situations for a specific workflow type."""
        return [s for s in self.situation_library.values() if s.workflow_type == workflow_type]

    def get_situations_by_difficulty(self, difficulty_level: int) -> List[GenericSituation]:
        """Get all situations of a specific difficulty level."""
        return [s for s in self.situation_library.values() if s.difficulty_level == difficulty_level]

    def create_custom_situation(self, situation_data: Dict[str, Any]) -> GenericSituation:
        """Create a custom situation for testing."""
        situation = GenericSituation(
            id=situation_data.get('id', f"custom_{uuid.uuid4().hex[:8]}"),
            name=situation_data['name'],
            description=situation_data['description'],
            workflow_type=situation_data['workflow_type'],
            user_message=situation_data['user_message'],
            context_variables=situation_data.get('context_variables', {}),
            expected_outcomes=situation_data.get('expected_outcomes', []),
            evaluation_criteria=situation_data.get('evaluation_criteria', {}),
            difficulty_level=situation_data.get('difficulty_level', 1),
            tags=situation_data.get('tags', [])
        )

        self.situation_library[situation.id] = situation
        self._save_situation_library()

        return situation

    def _save_situation_library(self):
        """Save situation library to file."""
        try:
            situations_file = self.situations_path / "situations.json"
            with open(situations_file, 'w') as f:
                data = [asdict(s) for s in self.situation_library.values()]
                json.dump(data, f, indent=2, default=str)
        except Exception as e:
            self.logger.error(f"Error saving situation library: {e}")

    # =========================================================================
    # Generic User Archetype Management
    # =========================================================================

    def _load_archetype_library(self) -> Dict[str, GenericUser]:
        """Load the library of user archetypes."""
        library = {}

        # Define core archetypes for early-stage testing
        core_archetypes = [
            GenericUser(
                id="curious_beginner",
                archetype_name="Curious Beginner",
                description="New user who is eager to explore but needs guidance",
                demographics={
                    "age": 28,
                    "gender": "Non-binary",
                    "location": "Urban area",
                    "occupation": "Office worker",
                    "education": "College graduate"
                },
                personality_traits={
                    "openness": 0.8,
                    "conscientiousness": 0.6,
                    "extraversion": 0.5,
                    "agreeableness": 0.7,
                    "neuroticism": 0.4
                },
                trust_level=35,
                communication_preferences={
                    "tone": "encouraging",
                    "detail_level": "medium",
                    "formality": "casual",
                    "response_length": "moderate"
                },
                behavioral_patterns={
                    "exploration_tendency": "high",
                    "risk_tolerance": "medium",
                    "feedback_seeking": "high",
                    "completion_rate": 0.7
                },
                limitations=["limited experience", "needs clear instructions", "may abandon difficult tasks"],
                capabilities=["eager to learn", "asks good questions", "follows guidance well"]
            ),
            GenericUser(
                id="stressed_professional",
                archetype_name="Stressed Professional",
                description="Busy professional dealing with work stress and time constraints",
                demographics={
                    "age": 35,
                    "gender": "Female",
                    "location": "Suburban area",
                    "occupation": "Manager",
                    "education": "MBA"
                },
                personality_traits={
                    "openness": 0.6,
                    "conscientiousness": 0.9,
                    "extraversion": 0.4,
                    "agreeableness": 0.6,
                    "neuroticism": 0.7
                },
                trust_level=45,
                communication_preferences={
                    "tone": "supportive",
                    "detail_level": "low",
                    "formality": "professional",
                    "response_length": "brief"
                },
                behavioral_patterns={
                    "exploration_tendency": "low",
                    "risk_tolerance": "low",
                    "time_availability": "limited",
                    "stress_level": "high"
                },
                limitations=["time constraints", "high stress", "skeptical of new approaches"],
                capabilities=["efficient execution", "clear communication", "goal-oriented"]
            ),
            GenericUser(
                id="creative_explorer",
                archetype_name="Creative Explorer",
                description="Artistic individual seeking creative inspiration and skill development",
                demographics={
                    "age": 24,
                    "gender": "Male",
                    "location": "Urban area",
                    "occupation": "Freelance artist",
                    "education": "Art school"
                },
                personality_traits={
                    "openness": 0.95,
                    "conscientiousness": 0.5,
                    "extraversion": 0.7,
                    "agreeableness": 0.8,
                    "neuroticism": 0.3
                },
                trust_level=65,
                communication_preferences={
                    "tone": "inspiring",
                    "detail_level": "high",
                    "formality": "casual",
                    "response_length": "detailed"
                },
                behavioral_patterns={
                    "exploration_tendency": "very_high",
                    "risk_tolerance": "high",
                    "creativity_focus": "primary",
                    "skill_building": "active"
                },
                limitations=["inconsistent schedule", "may get distracted", "perfectionist tendencies"],
                capabilities=["high creativity", "artistic skills", "innovative thinking"]
            ),
            GenericUser(
                id="cautious_senior",
                archetype_name="Cautious Senior",
                description="Older adult who is careful about new experiences but wants to stay active",
                demographics={
                    "age": 68,
                    "gender": "Male",
                    "location": "Rural area",
                    "occupation": "Retired",
                    "education": "High school"
                },
                personality_traits={
                    "openness": 0.4,
                    "conscientiousness": 0.8,
                    "extraversion": 0.3,
                    "agreeableness": 0.9,
                    "neuroticism": 0.5
                },
                trust_level=25,
                communication_preferences={
                    "tone": "respectful",
                    "detail_level": "high",
                    "formality": "formal",
                    "response_length": "detailed"
                },
                behavioral_patterns={
                    "exploration_tendency": "low",
                    "risk_tolerance": "very_low",
                    "safety_focus": "high",
                    "routine_preference": "strong"
                },
                limitations=["physical limitations", "technology hesitancy", "prefers familiar activities"],
                capabilities=["life experience", "patience", "careful planning"]
            ),
            GenericUser(
                id="ambitious_student",
                archetype_name="Ambitious Student",
                description="Young student focused on personal development and achievement",
                demographics={
                    "age": 20,
                    "gender": "Female",
                    "location": "College town",
                    "occupation": "Student",
                    "education": "Undergraduate"
                },
                personality_traits={
                    "openness": 0.7,
                    "conscientiousness": 0.8,
                    "extraversion": 0.6,
                    "agreeableness": 0.6,
                    "neuroticism": 0.6
                },
                trust_level=55,
                communication_preferences={
                    "tone": "motivational",
                    "detail_level": "medium",
                    "formality": "casual",
                    "response_length": "moderate"
                },
                behavioral_patterns={
                    "exploration_tendency": "high",
                    "risk_tolerance": "medium",
                    "achievement_focus": "high",
                    "learning_orientation": "strong"
                },
                limitations=["budget constraints", "academic pressure", "limited life experience"],
                capabilities=["quick learning", "high motivation", "adaptability"]
            )
        ]

        for archetype in core_archetypes:
            library[archetype.id] = archetype

        # Load additional archetypes from files if they exist
        profiles_file = self.profiles_path / "archetypes.json"
        if profiles_file.exists():
            try:
                with open(profiles_file, 'r') as f:
                    data = json.load(f)
                    for item in data:
                        archetype = GenericUser(**item)
                        library[archetype.id] = archetype
            except Exception as e:
                self.logger.warning(f"Error loading archetypes from file: {e}")

        return library

    def get_archetypes_by_trust_level(self, min_trust: int, max_trust: int) -> List[GenericUser]:
        """Get archetypes within a specific trust level range."""
        return [a for a in self.archetype_library.values()
                if min_trust <= a.trust_level <= max_trust]

    def get_archetypes_by_trait(self, trait_name: str, min_value: float) -> List[GenericUser]:
        """Get archetypes with a specific personality trait above threshold."""
        return [a for a in self.archetype_library.values()
                if a.personality_traits.get(trait_name, 0) >= min_value]

    def create_custom_archetype(self, archetype_data: Dict[str, Any]) -> GenericUser:
        """Create a custom user archetype for testing."""
        archetype = GenericUser(
            id=archetype_data.get('id', f"custom_{uuid.uuid4().hex[:8]}"),
            archetype_name=archetype_data['archetype_name'],
            description=archetype_data['description'],
            demographics=archetype_data.get('demographics', {}),
            personality_traits=archetype_data.get('personality_traits', {}),
            trust_level=archetype_data.get('trust_level', 50),
            communication_preferences=archetype_data.get('communication_preferences', {}),
            behavioral_patterns=archetype_data.get('behavioral_patterns', {}),
            limitations=archetype_data.get('limitations', []),
            capabilities=archetype_data.get('capabilities', []),
            is_temporary=archetype_data.get('is_temporary', True)
        )

        self.archetype_library[archetype.id] = archetype
        self._save_archetype_library()

        return archetype

    def _save_archetype_library(self):
        """Save archetype library to file."""
        try:
            profiles_file = self.profiles_path / "archetypes.json"
            with open(profiles_file, 'w') as f:
                data = [asdict(a) for a in self.archetype_library.values()]
                json.dump(data, f, indent=2, default=str)
        except Exception as e:
            self.logger.error(f"Error saving archetype library: {e}")

    # =========================================================================
    # Temporary Profile Management
    # =========================================================================

    async def create_temporary_profile(self, archetype_id: str, session_id: str = None) -> UserProfile:
        """Create a temporary UserProfile from an archetype."""
        if archetype_id not in self.archetype_library:
            raise ValueError(f"Archetype {archetype_id} not found in library")

        archetype = self.archetype_library[archetype_id]
        session_id = session_id or f"debug_{uuid.uuid4().hex[:8]}"

        try:
            with transaction.atomic():
                # Create unique profile name for this session
                profile_name = f"{archetype.archetype_name}_{session_id}"

                # Create UserProfile
                profile_data = archetype.to_user_profile_data()
                profile_data['profile_name'] = profile_name

                create_profile = sync_to_async(UserProfile.objects.create)
                user_profile = await create_profile(**{
                    'profile_name': profile_name,
                    'profile_type': 'Debug Archetype',
                    'is_real': False
                })

                # Create Demographics if provided
                if archetype.demographics:
                    create_demographics = sync_to_async(Demographics.objects.create)
                    await create_demographics(
                        user_profile=user_profile,
                        **archetype.demographics
                    )

                # Create TrustLevel
                create_trust = sync_to_async(TrustLevel.objects.create)
                await create_trust(
                    user_profile=user_profile,
                    value=archetype.trust_level,
                    aggregate_type="Overall",
                    aggregate_id="system",
                    notes=f"Initial trust level for {archetype.archetype_name} archetype"
                )

                self.logger.info(f"Created temporary profile: {profile_name} (ID: {user_profile.id})")
                return user_profile

        except Exception as e:
            self.logger.error(f"Error creating temporary profile: {e}")
            raise

    async def prune_temporary_profiles(self, session_id: str = None, older_than_hours: int = 24) -> int:
        """Remove temporary profiles to reset them."""
        try:
            cutoff_time = timezone.now() - timedelta(hours=older_than_hours)

            # Build filter criteria
            filter_kwargs = {
                'is_real': False,
                'profile_type': 'Debug Archetype',
                'created_at__lt': cutoff_time
            }

            if session_id:
                filter_kwargs['profile_name__contains'] = session_id

            # Get profiles to delete
            get_profiles = sync_to_async(lambda: list(
                UserProfile.objects.filter(**filter_kwargs)
            ))
            profiles_to_delete = await get_profiles()

            # Delete profiles
            delete_count = 0
            for profile in profiles_to_delete:
                delete_profile = sync_to_async(profile.delete)
                await delete_profile()
                delete_count += 1
                self.logger.info(f"Pruned temporary profile: {profile.profile_name}")

            self.logger.info(f"Pruned {delete_count} temporary profiles")
            return delete_count

        except Exception as e:
            self.logger.error(f"Error pruning temporary profiles: {e}")
            return 0

    async def get_temporary_profiles(self, session_id: str = None) -> List[UserProfile]:
        """Get all temporary profiles, optionally filtered by session."""
        try:
            filter_kwargs = {
                'is_real': False,
                'profile_type': 'Debug Archetype'
            }

            if session_id:
                filter_kwargs['profile_name__contains'] = session_id

            get_profiles = sync_to_async(lambda: list(
                UserProfile.objects.filter(**filter_kwargs).order_by('-created_at')
            ))
            profiles = await get_profiles()

            return profiles

        except Exception as e:
            self.logger.error(f"Error getting temporary profiles: {e}")
            return []

    # =========================================================================
    # User Story Simulation
    # =========================================================================

    async def simulate_user_story(self, situation_id: str, archetype_id: str,
                                session_id: str = None) -> Dict[str, Any]:
        """Simulate a complete user story with a specific situation and archetype."""
        try:
            # Validate inputs
            if situation_id not in self.situation_library:
                raise ValueError(f"Situation {situation_id} not found")
            if archetype_id not in self.archetype_library:
                raise ValueError(f"Archetype {archetype_id} not found")

            situation = self.situation_library[situation_id]
            archetype = self.archetype_library[archetype_id]
            session_id = session_id or f"story_{uuid.uuid4().hex[:8]}"

            self.logger.info(f"Simulating user story: {situation.name} with {archetype.archetype_name}")

            # Create temporary profile
            user_profile = await self.create_temporary_profile(archetype_id, session_id)

            # Prepare context variables
            context_variables = {
                **situation.context_variables,
                'user_profile_id': str(user_profile.id),
                'archetype_id': archetype_id,
                'session_id': session_id
            }

            # Create or find matching scenario
            scenario = await self._get_or_create_scenario(situation)

            # Execute benchmark
            benchmark_result = await self._execute_user_story_benchmark(
                scenario, user_profile, context_variables, situation
            )

            # Analyze results
            analysis = await self._analyze_user_story_results(
                benchmark_result, situation, archetype
            )

            return {
                'session_id': session_id,
                'situation': asdict(situation),
                'archetype': asdict(archetype),
                'user_profile_id': str(user_profile.id),
                'benchmark_result': benchmark_result,
                'analysis': analysis,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Error simulating user story: {e}")
            raise

    async def simulate_multiple_stories(self, situation_ids: List[str], archetype_ids: List[str],
                                      session_id: str = None) -> Dict[str, Any]:
        """Simulate multiple user stories for comprehensive testing."""
        session_id = session_id or f"multi_{uuid.uuid4().hex[:8]}"
        results = []

        for situation_id in situation_ids:
            for archetype_id in archetype_ids:
                try:
                    result = await self.simulate_user_story(situation_id, archetype_id, session_id)
                    results.append(result)
                except Exception as e:
                    self.logger.error(f"Error in story {situation_id} + {archetype_id}: {e}")
                    results.append({
                        'situation_id': situation_id,
                        'archetype_id': archetype_id,
                        'error': str(e),
                        'timestamp': datetime.now().isoformat()
                    })

        # Aggregate analysis
        aggregate_analysis = self._aggregate_story_analysis(results)

        return {
            'session_id': session_id,
            'total_stories': len(results),
            'successful_stories': len([r for r in results if 'error' not in r]),
            'results': results,
            'aggregate_analysis': aggregate_analysis,
            'timestamp': datetime.now().isoformat()
        }

    # =========================================================================
    # Agent Change Impact Measurement
    # =========================================================================

    async def measure_agent_change_impact(self, change_set: AgentChangeSet,
                                        test_situations: List[str] = None,
                                        test_archetypes: List[str] = None) -> ImpactMeasurement:
        """Measure the impact of agent changes using before/after comparison."""
        try:
            self.logger.info(f"Measuring impact of change: {change_set.description}")

            # Use default test scenarios if none provided
            if not test_situations:
                test_situations = ["wheel_gen_basic", "wheel_gen_stressed"]
            if not test_archetypes:
                test_archetypes = ["curious_beginner", "stressed_professional"]

            # Get baseline metrics (before changes)
            baseline_metrics = await self._get_baseline_metrics(
                change_set.agent_role, test_situations, test_archetypes
            )

            # Apply changes temporarily
            original_config = await self._apply_agent_changes(change_set)

            try:
                # Get modified metrics (after changes)
                modified_metrics = await self._get_modified_metrics(
                    change_set.agent_role, test_situations, test_archetypes
                )

                # Calculate impact
                impact_measurement = self._calculate_impact(
                    change_set, baseline_metrics, modified_metrics
                )

                return impact_measurement

            finally:
                # Restore original configuration
                await self._restore_agent_config(change_set.agent_role, original_config)

        except Exception as e:
            self.logger.error(f"Error measuring agent change impact: {e}")
            raise

    async def _get_baseline_metrics(self, agent_role: str, situations: List[str],
                                  archetypes: List[str]) -> Dict[str, float]:
        """Get baseline performance metrics for an agent."""
        metrics = {
            'semantic_scores': [],
            'execution_times': [],
            'success_rates': [],
            'tool_call_counts': [],
            'error_counts': []
        }

        for situation_id in situations:
            for archetype_id in archetypes:
                try:
                    result = await self.simulate_user_story(situation_id, archetype_id)

                    # Extract metrics from benchmark result
                    if 'benchmark_result' in result and result['benchmark_result']:
                        br = result['benchmark_result']
                        if 'runs' in br and br['runs']:
                            run = br['runs'][0]
                            metrics['semantic_scores'].append(run.get('semantic_score', 0))
                            metrics['execution_times'].append(run.get('mean_duration', 0))
                            metrics['success_rates'].append(run.get('success_rate', 0))
                            metrics['tool_call_counts'].append(len(run.get('tool_calls', [])))
                            metrics['error_counts'].append(len(run.get('errors', [])))

                except Exception as e:
                    self.logger.warning(f"Error getting baseline for {situation_id}+{archetype_id}: {e}")
                    metrics['error_counts'].append(1)

        # Calculate averages
        return {
            'avg_semantic_score': sum(metrics['semantic_scores']) / max(len(metrics['semantic_scores']), 1),
            'avg_execution_time': sum(metrics['execution_times']) / max(len(metrics['execution_times']), 1),
            'avg_success_rate': sum(metrics['success_rates']) / max(len(metrics['success_rates']), 1),
            'avg_tool_calls': sum(metrics['tool_call_counts']) / max(len(metrics['tool_call_counts']), 1),
            'total_errors': sum(metrics['error_counts'])
        }

    async def _get_modified_metrics(self, agent_role: str, situations: List[str],
                                  archetypes: List[str]) -> Dict[str, float]:
        """Get performance metrics after applying changes."""
        # Same logic as baseline but with modified agent
        return await self._get_baseline_metrics(agent_role, situations, archetypes)

    def _calculate_impact(self, change_set: AgentChangeSet, baseline: Dict[str, float],
                         modified: Dict[str, float]) -> ImpactMeasurement:
        """Calculate the impact of changes."""
        semantic_delta = modified['avg_semantic_score'] - baseline['avg_semantic_score']
        time_delta = modified['avg_execution_time'] - baseline['avg_execution_time']
        success_delta = modified['avg_success_rate'] - baseline['avg_success_rate']

        # Calculate overall improvement score (weighted)
        improvement_score = (
            semantic_delta * 0.4 +  # Semantic quality is most important
            success_delta * 0.3 +   # Success rate is important
            (-time_delta / 10) * 0.2 +  # Faster is better (normalized)
            (-modified['total_errors'] + baseline['total_errors']) * 0.1  # Fewer errors is better
        )

        detailed_analysis = {
            'semantic_improvement': semantic_delta,
            'time_change': time_delta,
            'success_improvement': success_delta,
            'error_change': modified['total_errors'] - baseline['total_errors'],
            'tool_call_change': modified['avg_tool_calls'] - baseline['avg_tool_calls'],
            'overall_assessment': self._assess_improvement(improvement_score)
        }

        return ImpactMeasurement(
            change_id=change_set.change_id,
            baseline_metrics=baseline,
            modified_metrics=modified,
            improvement_score=improvement_score,
            semantic_score_delta=semantic_delta,
            execution_time_delta=time_delta,
            success_rate_delta=success_delta,
            detailed_analysis=detailed_analysis,
            measurement_timestamp=datetime.now()
        )

    def _assess_improvement(self, score: float) -> str:
        """Assess the improvement based on score."""
        if score > 0.1:
            return "significant_improvement"
        elif score > 0.05:
            return "moderate_improvement"
        elif score > 0.01:
            return "slight_improvement"
        elif score > -0.01:
            return "no_change"
        elif score > -0.05:
            return "slight_degradation"
        elif score > -0.1:
            return "moderate_degradation"
        else:
            return "significant_degradation"

    # =========================================================================
    # Agent Improvement Workflow
    # =========================================================================

    async def create_agent_change_set(self, agent_role: str, change_type: str,
                                    changes: Dict[str, Any], description: str,
                                    expected_impact: str = "improvement") -> AgentChangeSet:
        """Create a new agent change set for testing."""
        change_set = AgentChangeSet(
            change_id=f"{agent_role}_{change_type}_{uuid.uuid4().hex[:8]}",
            agent_role=agent_role,
            change_type=change_type,
            description=description,
            changes=changes,
            expected_impact=expected_impact,
            created_at=datetime.now()
        )

        # Save change set
        self._save_change_set(change_set)

        return change_set

    def _save_change_set(self, change_set: AgentChangeSet):
        """Save change set to file."""
        try:
            experiments_file = self.experiments_path / f"{change_set.change_id}.json"
            with open(experiments_file, 'w') as f:
                json.dump(asdict(change_set), f, indent=2, default=str)
        except Exception as e:
            self.logger.error(f"Error saving change set: {e}")

    async def _apply_agent_changes(self, change_set: AgentChangeSet) -> Dict[str, Any]:
        """Apply changes to an agent and return original configuration for restoration."""
        from .agent_manager import AgentManager

        agent_manager = AgentManager()

        # Get current configuration
        original_config = agent_manager.get_agent_by_role(change_set.agent_role)
        if not original_config:
            raise ValueError(f"Agent {change_set.agent_role} not found")

        # Apply changes based on type
        if change_set.change_type == 'instructions':
            success = agent_manager.update_agent_instructions(
                role=change_set.agent_role,
                instructions=change_set.changes['instructions'],
                backup=True
            )
            if not success:
                raise Exception("Failed to update agent instructions")

        elif change_set.change_type == 'llm_config':
            success = agent_manager.update_agent_llm_config(
                role=change_set.agent_role,
                llm_config_name=change_set.changes['llm_config_name'],
                backup=True
            )
            if not success:
                raise Exception("Failed to update LLM configuration")

        elif change_set.change_type == 'schema':
            for schema_type, schema_data in change_set.changes.items():
                success = agent_manager.update_agent_schema(
                    role=change_set.agent_role,
                    schema_type=schema_type,
                    schema_data=schema_data,
                    backup=True
                )
                if not success:
                    raise Exception(f"Failed to update {schema_type} schema")

        else:
            raise ValueError(f"Unsupported change type: {change_set.change_type}")

        return original_config

    async def _restore_agent_config(self, agent_role: str, original_config: Dict[str, Any]):
        """Restore agent to original configuration."""
        from .agent_manager import AgentManager

        agent_manager = AgentManager()

        # Restore instructions
        if 'system_instructions' in original_config:
            agent_manager.update_agent_instructions(
                role=agent_role,
                instructions=original_config['system_instructions'],
                backup=False
            )

        # Restore LLM config
        if 'llm_config' in original_config and original_config['llm_config']:
            agent_manager.update_agent_llm_config(
                role=agent_role,
                llm_config_name=original_config['llm_config']['name'],
                backup=False
            )

        # Restore schemas
        for schema_type in ['input_schema', 'output_schema', 'state_schema', 'memory_schema']:
            if schema_type in original_config:
                agent_manager.update_agent_schema(
                    role=agent_role,
                    schema_type=schema_type.replace('_schema', ''),
                    schema_data=original_config[schema_type],
                    backup=False
                )

    # =========================================================================
    # Utility Methods
    # =========================================================================

    async def _get_or_create_scenario(self, situation: GenericSituation) -> BenchmarkScenario:
        """Get or create a BenchmarkScenario from a GenericSituation."""
        try:
            # Try to find existing scenario
            get_scenario = sync_to_async(lambda: BenchmarkScenario.objects.filter(
                name=situation.name
            ).first())
            scenario = await get_scenario()

            if scenario:
                return scenario

            # Create new scenario
            create_scenario = sync_to_async(BenchmarkScenario.objects.create)
            scenario = await create_scenario(
                name=situation.name,
                description=situation.description,
                agent_role="mentor",  # Default for most workflows
                workflow_type=situation.workflow_type,
                input_data={
                    "user_message": situation.user_message,
                    "context_packet": situation.context_variables
                },
                warmup_runs=1,
                benchmark_runs=1
            )

            return scenario

        except Exception as e:
            self.logger.error(f"Error getting/creating scenario: {e}")
            raise

    async def _execute_user_story_benchmark(self, scenario: BenchmarkScenario,
                                          user_profile: UserProfile,
                                          context_variables: Dict[str, Any],
                                          situation: GenericSituation) -> Dict[str, Any]:
        """Execute a benchmark for a user story."""
        try:
            # Initialize benchmark tools if needed
            if not self.benchmark_tools.client:
                await self.benchmark_tools.initialize()

            # Prepare test configuration
            test_config = {
                "scenario": scenario.name,
                "runs": "1",
                "semanticEval": True,
                "executionMode": "full-real",
                "userProfile": user_profile.profile_name,
                "detailedLogging": True,
                "context_variables": context_variables
            }

            # Execute benchmark
            result = await self.benchmark_tools.run_quick_benchmark_test(test_config)

            return result

        except Exception as e:
            self.logger.error(f"Error executing user story benchmark: {e}")
            return {'error': str(e)}

    async def _analyze_user_story_results(self, benchmark_result: Dict[str, Any],
                                        situation: GenericSituation,
                                        archetype: GenericUser) -> Dict[str, Any]:
        """Analyze the results of a user story simulation."""
        analysis = {
            'situation_match': {},
            'archetype_alignment': {},
            'quality_assessment': {},
            'improvement_suggestions': []
        }

        if 'error' in benchmark_result:
            analysis['error'] = benchmark_result['error']
            return analysis

        # Analyze situation match
        if 'result' in benchmark_result and benchmark_result['result']:
            result = benchmark_result['result']

            # Check if expected outcomes were met
            analysis['situation_match'] = self._check_expected_outcomes(
                result, situation.expected_outcomes
            )

            # Check archetype alignment
            analysis['archetype_alignment'] = self._check_archetype_alignment(
                result, archetype
            )

            # Overall quality assessment
            analysis['quality_assessment'] = self._assess_story_quality(
                result, situation, archetype
            )

        return analysis

    def _check_expected_outcomes(self, result: Dict[str, Any],
                               expected_outcomes: List[str]) -> Dict[str, Any]:
        """Check if expected outcomes were achieved."""
        # This would analyze the benchmark result against expected outcomes
        # Implementation depends on the specific structure of benchmark results
        return {
            'outcomes_met': 0,
            'total_outcomes': len(expected_outcomes),
            'details': "Outcome analysis not yet implemented"
        }

    def _check_archetype_alignment(self, result: Dict[str, Any],
                                 archetype: GenericUser) -> Dict[str, Any]:
        """Check if the result aligns with the archetype characteristics."""
        # This would analyze if the agent's response matches the archetype's needs
        return {
            'alignment_score': 0.5,
            'details': "Archetype alignment analysis not yet implemented"
        }

    def _assess_story_quality(self, result: Dict[str, Any],
                            situation: GenericSituation,
                            archetype: GenericUser) -> Dict[str, Any]:
        """Assess overall quality of the user story execution."""
        quality_score = 0.0

        # Extract semantic score if available
        if 'runs' in result and result['runs']:
            run = result['runs'][0]
            quality_score = run.get('semantic_score', 0.0)

        return {
            'overall_score': quality_score,
            'semantic_score': quality_score,
            'execution_success': 'runs' in result and len(result['runs']) > 0,
            'details': "Quality assessment based on semantic score"
        }

    def _aggregate_story_analysis(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Aggregate analysis across multiple user stories."""
        successful_results = [r for r in results if 'error' not in r]

        if not successful_results:
            return {'error': 'No successful results to analyze'}

        # Calculate aggregate metrics
        semantic_scores = []
        for result in successful_results:
            if 'analysis' in result and 'quality_assessment' in result['analysis']:
                score = result['analysis']['quality_assessment'].get('semantic_score', 0)
                semantic_scores.append(score)

        return {
            'total_stories': len(results),
            'successful_stories': len(successful_results),
            'average_semantic_score': sum(semantic_scores) / max(len(semantic_scores), 1),
            'success_rate': len(successful_results) / len(results),
            'recommendations': self._generate_aggregate_recommendations(successful_results)
        }

    def _generate_aggregate_recommendations(self, results: List[Dict[str, Any]]) -> List[str]:
        """Generate recommendations based on aggregate analysis."""
        recommendations = []

        # Analyze patterns in results
        low_score_count = 0
        for result in results:
            if 'analysis' in result and 'quality_assessment' in result['analysis']:
                score = result['analysis']['quality_assessment'].get('semantic_score', 0)
                if score < 0.7:
                    low_score_count += 1

        if low_score_count > len(results) * 0.5:
            recommendations.append("Consider improving agent instructions for better semantic quality")

        if len(results) < 5:
            recommendations.append("Run more test scenarios for comprehensive analysis")

        return recommendations
