"""
Admin Validator Tool

AI-intuitive tool for validating admin page quality using Playwright automation.
Provides comprehensive UI testing and validation capabilities for AI agents working on admin interfaces.
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from pathlib import Path

try:
    from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>, BrowserContext
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False
    Browser = Page = BrowserContext = None

from django.conf import settings
from django.test import Client
from django.contrib.auth.models import User

logger = logging.getLogger(__name__)


class AdminValidator:
    """
    AI-intuitive tool for validating admin page quality using Playwright.
    
    This tool provides comprehensive capabilities for AI agents to test admin pages,
    validate UI components, check accessibility, and perform automated testing.
    """
    
    def __init__(self):
        self.logger = logger
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.base_url = "http://localhost:8000"  # Default Django dev server
        
        if not PLAYWRIGHT_AVAILABLE:
            self.logger.warning("Playwright not available. Install with: pip install playwright")
    
    async def initialize(self, headless: bool = True, browser_type: str = "chromium") -> bool:
        """
        Initialize Playwright browser for testing.
        
        Args:
            headless: Whether to run browser in headless mode
            browser_type: Browser type ('chromium', 'firefox', 'webkit')
            
        Returns:
            True if initialization successful, False otherwise
        """
        if not PLAYWRIGHT_AVAILABLE:
            self.logger.error("Playwright not available")
            return False
        
        try:
            playwright = await async_playwright().start()
            
            if browser_type == "chromium":
                self.browser = await playwright.chromium.launch(headless=headless)
            elif browser_type == "firefox":
                self.browser = await playwright.firefox.launch(headless=headless)
            elif browser_type == "webkit":
                self.browser = await playwright.webkit.launch(headless=headless)
            else:
                self.logger.error(f"Unsupported browser type: {browser_type}")
                return False
            
            # Create browser context with useful settings
            self.context = await self.browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (compatible; GoaliAdminValidator/1.0)'
            )
            
            # Create page
            self.page = await self.context.new_page()
            
            self.logger.info(f"Initialized Playwright with {browser_type} browser")
            return True
            
        except Exception as e:
            self.logger.error(f"Error initializing Playwright: {e}")
            return False
    
    async def cleanup(self):
        """Clean up Playwright resources."""
        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            
            self.logger.info("Cleaned up Playwright resources")
            
        except Exception as e:
            self.logger.error(f"Error cleaning up Playwright: {e}")
    
    # =========================================================================
    # Admin Page Testing Operations
    # =========================================================================
    
    async def test_admin_dashboard(self) -> Dict[str, Any]:
        """
        Test the admin dashboard functionality.
        
        Returns:
            Test results dictionary
        """
        if not await self._ensure_initialized():
            return {'error': 'Playwright not initialized'}
        
        try:
            test_results = {
                'test_name': 'admin_dashboard',
                'timestamp': datetime.now().isoformat(),
                'status': 'running',
                'checks': [],
                'performance': {},
                'errors': []
            }
            
            # Navigate to admin dashboard
            dashboard_url = f"{self.base_url}/admin/"
            await self.page.goto(dashboard_url)
            
            # Check if login is required
            if "login" in self.page.url.lower():
                login_result = await self._perform_admin_login()
                if not login_result['success']:
                    test_results['status'] = 'failed'
                    test_results['errors'].append('Failed to login to admin')
                    return test_results
                
                # Navigate to dashboard again after login
                await self.page.goto(dashboard_url)
            
            # Test dashboard elements
            checks = [
                await self._check_page_title("Django administration"),
                await self._check_element_exists("h1", "Django administration"),
                await self._check_navigation_links(),
                await self._check_responsive_design(),
                await self._check_page_load_time()
            ]
            
            test_results['checks'] = checks
            test_results['status'] = 'passed' if all(c['passed'] for c in checks) else 'failed'
            
            # Performance metrics
            test_results['performance'] = await self._get_performance_metrics()
            
            self.logger.info("Completed admin dashboard test")
            return test_results
            
        except Exception as e:
            self.logger.error(f"Error testing admin dashboard: {e}")
            return {
                'test_name': 'admin_dashboard',
                'status': 'error',
                'error': str(e)
            }
    
    async def test_benchmark_management_page(self) -> Dict[str, Any]:
        """
        Test the benchmark management page functionality.
        
        Returns:
            Test results dictionary
        """
        if not await self._ensure_initialized():
            return {'error': 'Playwright not initialized'}
        
        try:
            test_results = {
                'test_name': 'benchmark_management',
                'timestamp': datetime.now().isoformat(),
                'status': 'running',
                'checks': [],
                'ui_elements': {},
                'errors': []
            }
            
            # Navigate to benchmark management page
            benchmark_url = f"{self.base_url}/admin/benchmarks/"
            await self.page.goto(benchmark_url)
            
            # Test benchmark page elements
            checks = [
                await self._check_page_title("Benchmark Management"),
                await self._check_element_exists("h1", "Benchmark Management"),
                await self._check_benchmark_buttons(),
                await self._check_benchmark_table(),
                await self._check_modal_functionality(),
                await self._check_javascript_errors()
            ]
            
            test_results['checks'] = checks
            test_results['status'] = 'passed' if all(c['passed'] for c in checks) else 'failed'
            
            # Test UI elements
            test_results['ui_elements'] = await self._analyze_ui_elements()
            
            self.logger.info("Completed benchmark management page test")
            return test_results
            
        except Exception as e:
            self.logger.error(f"Error testing benchmark management page: {e}")
            return {
                'test_name': 'benchmark_management',
                'status': 'error',
                'error': str(e)
            }
    
    async def test_websocket_dashboard(self) -> Dict[str, Any]:
        """
        Test the WebSocket connection dashboard.
        
        Returns:
            Test results dictionary
        """
        if not await self._ensure_initialized():
            return {'error': 'Playwright not initialized'}
        
        try:
            test_results = {
                'test_name': 'websocket_dashboard',
                'timestamp': datetime.now().isoformat(),
                'status': 'running',
                'checks': [],
                'websocket_features': {},
                'errors': []
            }
            
            # Navigate to WebSocket dashboard
            ws_url = f"{self.base_url}/admin/connection-dashboard/"
            await self.page.goto(ws_url)
            
            # Test WebSocket dashboard elements
            checks = [
                await self._check_page_title("WebSocket Connection Dashboard"),
                await self._check_connection_status_display(),
                await self._check_message_monitoring_features(),
                await self._check_real_time_updates(),
                await self._check_dashboard_responsiveness()
            ]
            
            test_results['checks'] = checks
            test_results['status'] = 'passed' if all(c['passed'] for c in checks) else 'failed'
            
            # Test WebSocket-specific features
            test_results['websocket_features'] = await self._test_websocket_features()
            
            self.logger.info("Completed WebSocket dashboard test")
            return test_results
            
        except Exception as e:
            self.logger.error(f"Error testing WebSocket dashboard: {e}")
            return {
                'test_name': 'websocket_dashboard',
                'status': 'error',
                'error': str(e)
            }
    
    # =========================================================================
    # Accessibility Testing
    # =========================================================================
    
    async def check_accessibility(self, url: str) -> Dict[str, Any]:
        """
        Check accessibility compliance for a given page.
        
        Args:
            url: URL to check for accessibility
            
        Returns:
            Accessibility check results
        """
        if not await self._ensure_initialized():
            return {'error': 'Playwright not initialized'}
        
        try:
            await self.page.goto(url)
            
            accessibility_results = {
                'url': url,
                'timestamp': datetime.now().isoformat(),
                'checks': [],
                'score': 0,
                'issues': []
            }
            
            # Basic accessibility checks
            checks = [
                await self._check_alt_text_on_images(),
                await self._check_heading_structure(),
                await self._check_color_contrast(),
                await self._check_keyboard_navigation(),
                await self._check_aria_labels(),
                await self._check_form_labels()
            ]
            
            accessibility_results['checks'] = checks
            
            # Calculate accessibility score
            passed_checks = sum(1 for check in checks if check['passed'])
            accessibility_results['score'] = (passed_checks / len(checks)) * 100
            
            self.logger.info(f"Completed accessibility check for {url}")
            return accessibility_results
            
        except Exception as e:
            self.logger.error(f"Error checking accessibility for {url}: {e}")
            return {
                'url': url,
                'status': 'error',
                'error': str(e)
            }

    # =========================================================================
    # Performance Testing
    # =========================================================================

    async def test_page_performance(self, url: str) -> Dict[str, Any]:
        """
        Test page performance metrics.

        Args:
            url: URL to test performance

        Returns:
            Performance test results
        """
        if not await self._ensure_initialized():
            return {'error': 'Playwright not initialized'}

        try:
            # Start performance monitoring
            await self.page.goto(url, wait_until='networkidle')

            # Get performance metrics
            performance_metrics = await self.page.evaluate("""
                () => {
                    const navigation = performance.getEntriesByType('navigation')[0];
                    const paint = performance.getEntriesByType('paint');

                    return {
                        loadTime: navigation.loadEventEnd - navigation.loadEventStart,
                        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
                        firstPaint: paint.find(p => p.name === 'first-paint')?.startTime || 0,
                        firstContentfulPaint: paint.find(p => p.name === 'first-contentful-paint')?.startTime || 0,
                        transferSize: navigation.transferSize,
                        encodedBodySize: navigation.encodedBodySize
                    };
                }
            """)

            # Analyze performance
            performance_results = {
                'url': url,
                'timestamp': datetime.now().isoformat(),
                'metrics': performance_metrics,
                'analysis': self._analyze_performance_metrics(performance_metrics),
                'recommendations': []
            }

            # Generate recommendations
            performance_results['recommendations'] = self._generate_performance_recommendations(performance_metrics)

            self.logger.info(f"Completed performance test for {url}")
            return performance_results

        except Exception as e:
            self.logger.error(f"Error testing performance for {url}: {e}")
            return {
                'url': url,
                'status': 'error',
                'error': str(e)
            }

    # =========================================================================
    # Utility Methods
    # =========================================================================

    async def _ensure_initialized(self) -> bool:
        """Ensure Playwright is initialized."""
        if not PLAYWRIGHT_AVAILABLE:
            return False

        if not self.browser or not self.page:
            return await self.initialize()

        return True

    async def _perform_admin_login(self) -> Dict[str, Any]:
        """Perform admin login if required."""
        try:
            # Check if we're on a login page
            if not await self.page.locator('input[name="username"]').is_visible():
                return {'success': False, 'error': 'Not on login page'}

            # Try to login with test credentials
            await self.page.fill('input[name="username"]', 'admin')
            await self.page.fill('input[name="password"]', 'admin')
            await self.page.click('input[type="submit"]')

            # Wait for navigation
            await self.page.wait_for_load_state('networkidle')

            # Check if login was successful
            if "login" not in self.page.url.lower():
                return {'success': True}
            else:
                return {'success': False, 'error': 'Login failed'}

        except Exception as e:
            return {'success': False, 'error': str(e)}

    async def _check_page_title(self, expected_title: str) -> Dict[str, Any]:
        """Check if page title contains expected text."""
        try:
            title = await self.page.title()
            passed = expected_title.lower() in title.lower()

            return {
                'check': 'page_title',
                'passed': passed,
                'expected': expected_title,
                'actual': title,
                'message': f"Page title {'contains' if passed else 'does not contain'} expected text"
            }

        except Exception as e:
            return {
                'check': 'page_title',
                'passed': False,
                'error': str(e)
            }

    async def _check_element_exists(self, selector: str, expected_text: str = None) -> Dict[str, Any]:
        """Check if element exists and optionally contains expected text."""
        try:
            element = self.page.locator(selector)
            exists = await element.count() > 0

            if exists and expected_text:
                text = await element.first.text_content()
                text_match = expected_text.lower() in text.lower() if text else False
                passed = exists and text_match
                message = f"Element {selector} exists and {'contains' if text_match else 'does not contain'} expected text"
            else:
                passed = exists
                message = f"Element {selector} {'exists' if exists else 'does not exist'}"

            return {
                'check': f'element_exists_{selector}',
                'passed': passed,
                'message': message
            }

        except Exception as e:
            return {
                'check': f'element_exists_{selector}',
                'passed': False,
                'error': str(e)
            }

    async def _check_navigation_links(self) -> Dict[str, Any]:
        """Check navigation links functionality."""
        try:
            # Look for common navigation elements
            nav_selectors = ['nav', '.navbar', '.navigation', 'header nav']
            nav_found = False

            for selector in nav_selectors:
                if await self.page.locator(selector).count() > 0:
                    nav_found = True
                    break

            return {
                'check': 'navigation_links',
                'passed': nav_found,
                'message': f"Navigation elements {'found' if nav_found else 'not found'}"
            }

        except Exception as e:
            return {
                'check': 'navigation_links',
                'passed': False,
                'error': str(e)
            }

    async def _check_responsive_design(self) -> Dict[str, Any]:
        """Check responsive design by testing different viewport sizes."""
        try:
            # Test mobile viewport
            await self.page.set_viewport_size({'width': 375, 'height': 667})
            await self.page.wait_for_timeout(1000)  # Wait for layout adjustment

            # Check if content is still accessible
            body_width = await self.page.evaluate("document.body.scrollWidth")
            viewport_width = 375

            # Reset to desktop viewport
            await self.page.set_viewport_size({'width': 1920, 'height': 1080})

            responsive = body_width <= viewport_width * 1.1  # Allow 10% tolerance

            return {
                'check': 'responsive_design',
                'passed': responsive,
                'message': f"Page {'is' if responsive else 'is not'} responsive on mobile"
            }

        except Exception as e:
            return {
                'check': 'responsive_design',
                'passed': False,
                'error': str(e)
            }
