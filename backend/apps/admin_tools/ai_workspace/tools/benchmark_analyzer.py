"""
Benchmark Analyzer Tool

AI-intuitive tool for accessing and analyzing benchmark results with intelligent insights.
Provides comprehensive benchmark analysis capabilities for AI agents working on system improvements.
"""

import json
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
from django.db.models import Q, Avg, Count, Max, Min
from django.utils import timezone

from apps.main.models import BenchmarkRun, BenchmarkScenario, GenericAgent, LLMConfig
from apps.user.models import UserProfile

logger = logging.getLogger(__name__)


class BenchmarkAnalyzer:
    """
    AI-intuitive tool for analyzing benchmark results and generating insights.
    
    This tool provides comprehensive capabilities for AI agents to access benchmark data,
    analyze performance trends, and generate actionable recommendations for improvements.
    """
    
    def __init__(self):
        self.logger = logger
    
    # =========================================================================
    # Benchmark Query Operations
    # =========================================================================
    
    def get_recent_benchmark_runs(self, days: int = 7, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Get recent benchmark runs with comprehensive data.
        
        Args:
            days: Number of days to look back
            limit: Maximum number of runs to return
            
        Returns:
            List of benchmark run dictionaries
        """
        try:
            since_date = timezone.now() - timedelta(days=days)
            
            runs = BenchmarkRun.objects.select_related(
                'scenario', 'agent_definition', 'llm_config'
            ).filter(
                execution_date__gte=since_date
            ).order_by('-execution_date')[:limit]
            
            run_list = []
            for run in runs:
                run_data = self._serialize_benchmark_run(run)
                run_list.append(run_data)
            
            self.logger.info(f"Retrieved {len(run_list)} recent benchmark runs from last {days} days")
            return run_list
            
        except Exception as e:
            self.logger.error(f"Error retrieving recent benchmark runs: {e}")
            raise
    
    def get_benchmark_runs_by_agent(self, agent_role: str, limit: int = 20) -> List[Dict[str, Any]]:
        """
        Get benchmark runs for a specific agent role.
        
        Args:
            agent_role: Agent role to filter by
            limit: Maximum number of runs to return
            
        Returns:
            List of benchmark run dictionaries
        """
        try:
            runs = BenchmarkRun.objects.select_related(
                'scenario', 'agent_definition', 'llm_config'
            ).filter(
                agent_definition__role=agent_role
            ).order_by('-execution_date')[:limit]
            
            run_list = []
            for run in runs:
                run_data = self._serialize_benchmark_run(run)
                run_list.append(run_data)
            
            self.logger.info(f"Retrieved {len(run_list)} benchmark runs for agent role: {agent_role}")
            return run_list
            
        except Exception as e:
            self.logger.error(f"Error retrieving benchmark runs for agent {agent_role}: {e}")
            raise
    
    def get_benchmark_runs_by_scenario(self, scenario_name: str, limit: int = 20) -> List[Dict[str, Any]]:
        """
        Get benchmark runs for a specific scenario.
        
        Args:
            scenario_name: Scenario name to filter by
            limit: Maximum number of runs to return
            
        Returns:
            List of benchmark run dictionaries
        """
        try:
            runs = BenchmarkRun.objects.select_related(
                'scenario', 'agent_definition', 'llm_config'
            ).filter(
                scenario__name__icontains=scenario_name
            ).order_by('-execution_date')[:limit]
            
            run_list = []
            for run in runs:
                run_data = self._serialize_benchmark_run(run)
                run_list.append(run_data)
            
            self.logger.info(f"Retrieved {len(run_list)} benchmark runs for scenario: {scenario_name}")
            return run_list
            
        except Exception as e:
            self.logger.error(f"Error retrieving benchmark runs for scenario {scenario_name}: {e}")
            raise
    
    def get_benchmark_run_details(self, run_id: str) -> Optional[Dict[str, Any]]:
        """
        Get detailed information for a specific benchmark run.
        
        Args:
            run_id: Benchmark run ID
            
        Returns:
            Detailed benchmark run dictionary or None if not found
        """
        try:
            run = BenchmarkRun.objects.select_related(
                'scenario', 'agent_definition', 'llm_config'
            ).filter(id=run_id).first()
            
            if not run:
                self.logger.warning(f"Benchmark run not found: {run_id}")
                return None
            
            # Get detailed data including agent communications and context
            run_data = self._serialize_benchmark_run(run, include_details=True)
            
            self.logger.info(f"Retrieved detailed data for benchmark run: {run_id}")
            return run_data
            
        except Exception as e:
            self.logger.error(f"Error retrieving benchmark run details {run_id}: {e}")
            raise
    
    # =========================================================================
    # Performance Analysis Operations
    # =========================================================================
    
    def analyze_agent_performance_trends(self, agent_role: str, days: int = 30) -> Dict[str, Any]:
        """
        Analyze performance trends for a specific agent over time.
        
        Args:
            agent_role: Agent role to analyze
            days: Number of days to analyze
            
        Returns:
            Performance trend analysis dictionary
        """
        try:
            since_date = timezone.now() - timedelta(days=days)
            
            runs = BenchmarkRun.objects.filter(
                agent_definition__role=agent_role,
                execution_date__gte=since_date
            ).order_by('execution_date')
            
            if not runs.exists():
                return {'error': f'No benchmark runs found for agent {agent_role} in last {days} days'}
            
            # Calculate trends
            analysis = {
                'agent_role': agent_role,
                'analysis_period_days': days,
                'total_runs': runs.count(),
                'performance_metrics': self._calculate_performance_metrics(runs),
                'trend_analysis': self._calculate_trends(runs),
                'quality_indicators': self._analyze_quality_indicators(runs),
                'recommendations': self._generate_performance_recommendations(runs, agent_role)
            }
            
            self.logger.info(f"Generated performance trend analysis for agent: {agent_role}")
            return analysis
            
        except Exception as e:
            self.logger.error(f"Error analyzing performance trends for agent {agent_role}: {e}")
            raise
    
    def compare_agent_performance(self, agent_roles: List[str], days: int = 30) -> Dict[str, Any]:
        """
        Compare performance between multiple agents.
        
        Args:
            agent_roles: List of agent roles to compare
            days: Number of days to analyze
            
        Returns:
            Comparative performance analysis dictionary
        """
        try:
            since_date = timezone.now() - timedelta(days=days)
            
            comparison = {
                'comparison_period_days': days,
                'agents_compared': agent_roles,
                'agent_performance': {},
                'comparative_analysis': {},
                'recommendations': []
            }
            
            # Analyze each agent
            for role in agent_roles:
                runs = BenchmarkRun.objects.filter(
                    agent_definition__role=role,
                    execution_date__gte=since_date
                )
                
                if runs.exists():
                    comparison['agent_performance'][role] = {
                        'total_runs': runs.count(),
                        'avg_semantic_score': runs.aggregate(Avg('semantic_score'))['semantic_score__avg'],
                        'avg_success_rate': runs.aggregate(Avg('success_rate'))['success_rate__avg'],
                        'avg_duration': runs.aggregate(Avg('mean_duration'))['mean_duration__avg'],
                        'avg_cost': runs.aggregate(Avg('estimated_cost'))['estimated_cost__avg']
                    }
                else:
                    comparison['agent_performance'][role] = {'error': 'No runs found'}
            
            # Generate comparative insights
            comparison['comparative_analysis'] = self._generate_comparative_analysis(comparison['agent_performance'])
            comparison['recommendations'] = self._generate_comparative_recommendations(comparison['agent_performance'])
            
            self.logger.info(f"Generated comparative analysis for agents: {agent_roles}")
            return comparison
            
        except Exception as e:
            self.logger.error(f"Error comparing agent performance: {e}")
            raise

    # =========================================================================
    # Context Package Analysis
    # =========================================================================

    def extract_context_packages(self, run_ids: List[str]) -> Dict[str, Any]:
        """
        Extract and analyze context packages from multiple benchmark runs.

        Args:
            run_ids: List of benchmark run IDs

        Returns:
            Context package analysis dictionary
        """
        try:
            runs = BenchmarkRun.objects.filter(id__in=run_ids)

            context_analysis = {
                'total_runs_analyzed': runs.count(),
                'context_patterns': {},
                'agent_output_patterns': {},
                'quality_correlations': {},
                'insights': []
            }

            for run in runs:
                # Extract context variables
                params = run.parameters or {}
                context_vars = params.get('context_variables', {})

                # Analyze context patterns
                self._analyze_context_patterns(context_vars, run.semantic_score, context_analysis)

                # Extract agent output patterns
                if run.raw_results:
                    self._analyze_agent_output_patterns(run.raw_results, run.semantic_score, context_analysis)

            # Generate insights
            context_analysis['insights'] = self._generate_context_insights(context_analysis)

            self.logger.info(f"Extracted context packages from {runs.count()} benchmark runs")
            return context_analysis

        except Exception as e:
            self.logger.error(f"Error extracting context packages: {e}")
            raise

    def analyze_wheel_generation_quality(self, days: int = 7) -> Dict[str, Any]:
        """
        Analyze wheel generation quality from recent workflow benchmarks.

        Args:
            days: Number of days to analyze

        Returns:
            Wheel generation quality analysis
        """
        try:
            since_date = timezone.now() - timedelta(days=days)

            # Get workflow benchmark runs (wheel generation)
            runs = BenchmarkRun.objects.filter(
                execution_date__gte=since_date,
                scenario__workflow_type='wheel_generation'
            ).order_by('-execution_date')

            analysis = {
                'analysis_period_days': days,
                'total_wheel_generations': runs.count(),
                'quality_metrics': {},
                'wheel_characteristics': {},
                'improvement_areas': [],
                'recommendations': []
            }

            if runs.exists():
                # Analyze wheel quality
                analysis['quality_metrics'] = self._analyze_wheel_quality_metrics(runs)
                analysis['wheel_characteristics'] = self._analyze_wheel_characteristics(runs)
                analysis['improvement_areas'] = self._identify_wheel_improvement_areas(runs)
                analysis['recommendations'] = self._generate_wheel_recommendations(runs)

            self.logger.info(f"Analyzed wheel generation quality from {runs.count()} runs")
            return analysis

        except Exception as e:
            self.logger.error(f"Error analyzing wheel generation quality: {e}")
            raise

    # =========================================================================
    # Utility Methods
    # =========================================================================

    def _serialize_benchmark_run(self, run: BenchmarkRun, include_details: bool = False) -> Dict[str, Any]:
        """Serialize benchmark run to dictionary."""
        data = {
            'id': str(run.id),
            'scenario_name': run.scenario.name if run.scenario else 'N/A',
            'agent_role': run.agent_definition.role if run.agent_definition else 'N/A',
            'execution_date': run.execution_date.isoformat(),
            'success_rate': run.success_rate,
            'semantic_score': run.semantic_score,
            'mean_duration': run.mean_duration,
            'total_tokens': run.total_tokens,
            'estimated_cost': float(run.estimated_cost) if run.estimated_cost else 0.0,
            'llm_model': run.agent_llm_model_name,
            'llm_temperature': run.llm_temperature
        }

        if include_details:
            data.update({
                'parameters': run.parameters,
                'raw_results': run.raw_results,
                'agent_communications': run.agent_communications,
                'semantic_evaluation_details': run.semantic_evaluation_details,
                'semantic_evaluations': run.semantic_evaluations,
                'tool_calls': run.tool_calls,
                'tool_breakdown': run.tool_breakdown
            })

        return data

    def _calculate_performance_metrics(self, runs) -> Dict[str, Any]:
        """Calculate performance metrics from benchmark runs."""
        metrics = {
            'avg_semantic_score': 0,
            'avg_success_rate': 0,
            'avg_duration': 0,
            'avg_cost': 0,
            'total_tokens': 0,
            'success_rate_trend': 'stable'
        }

        if runs.exists():
            aggregates = runs.aggregate(
                avg_semantic=Avg('semantic_score'),
                avg_success=Avg('success_rate'),
                avg_duration=Avg('mean_duration'),
                avg_cost=Avg('estimated_cost'),
                total_tokens=Avg('total_tokens')
            )

            metrics.update({
                'avg_semantic_score': aggregates['avg_semantic'] or 0,
                'avg_success_rate': aggregates['avg_success'] or 0,
                'avg_duration': aggregates['avg_duration'] or 0,
                'avg_cost': aggregates['avg_cost'] or 0,
                'total_tokens': aggregates['total_tokens'] or 0
            })

        return metrics

    def _calculate_trends(self, runs) -> Dict[str, Any]:
        """Calculate performance trends."""
        trends = {
            'semantic_score_trend': 'stable',
            'duration_trend': 'stable',
            'cost_trend': 'stable',
            'recent_performance': 'average'
        }

        if runs.count() >= 5:
            # Simple trend calculation - compare first half vs second half
            run_list = list(runs)
            mid_point = len(run_list) // 2

            first_half = run_list[:mid_point]
            second_half = run_list[mid_point:]

            # Calculate averages for each half
            first_avg_semantic = sum(r.semantic_score or 0 for r in first_half) / len(first_half)
            second_avg_semantic = sum(r.semantic_score or 0 for r in second_half) / len(second_half)

            if second_avg_semantic > first_avg_semantic * 1.05:
                trends['semantic_score_trend'] = 'improving'
            elif second_avg_semantic < first_avg_semantic * 0.95:
                trends['semantic_score_trend'] = 'declining'

        return trends
