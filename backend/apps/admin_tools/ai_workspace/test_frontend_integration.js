/**
 * Frontend Integration Test for User Profile Import Functionality
 * 
 * This script tests the JavaScript integration after the refactor to ensure:
 * 1. ImportManager is properly initialized
 * 2. User account selection modal works
 * 3. Profile repair modal integration works
 * 4. All event handlers are properly connected
 */

// Test configuration
const TEST_CONFIG = {
    baseUrl: 'http://localhost:8000',
    testTimeout: 30000,
    adminCredentials: {
        username: 'admin',
        password: 'admin'
    }
};

// Test profile data
const TEST_PROFILE = {
    "profile_name": "Test Profile Integration",
    "user_account": {
        "username": "testintegration123",
        "email": "<EMAIL>",
        "first_name": "Test",
        "last_name": "Integration"
    },
    "demographics": {
        "full_name": "Test Integration",
        "age": 25,
        "gender": "Other",
        "location": "Test City"
    }
};

class FrontendIntegrationTest {
    constructor(page) {
        this.page = page;
        this.results = {
            passed: 0,
            failed: 0,
            tests: []
        };
    }

    async runTest(testName, testFunction) {
        console.log(`🧪 Running test: ${testName}`);
        try {
            await testFunction();
            this.results.passed++;
            this.results.tests.push({ name: testName, status: 'PASS' });
            console.log(`✅ ${testName} - PASSED`);
        } catch (error) {
            this.results.failed++;
            this.results.tests.push({ name: testName, status: 'FAIL', error: error.message });
            console.log(`❌ ${testName} - FAILED: ${error.message}`);
        }
    }

    async testPageLoad() {
        // Check if the page loads and contains required elements
        await this.page.waitForSelector('.user-profile-management', { timeout: 10000 });
        
        // Check for import section
        const importSection = await this.page.$('.import-profile-card');
        if (!importSection) {
            throw new Error('Import profile section not found');
        }

        // Check for required modals
        const modals = [
            '#userAccountSelectionModal',
            '#profileRepairModal'
        ];

        for (const modal of modals) {
            const element = await this.page.$(modal);
            if (!element) {
                throw new Error(`Modal not found: ${modal}`);
            }
        }
    }

    async testJavaScriptLoading() {
        // Check if JavaScript files are loaded and classes are available
        const jsChecks = await this.page.evaluate(() => {
            const results = {};
            
            // Check if ImportManager is available
            results.importManager = typeof window.ImportManager === 'function';
            results.importManagerInstance = typeof window.importManager === 'object';
            
            // Check if ProfileRepairManager is available
            results.profileRepairManager = typeof window.ProfileRepairManager === 'function';
            results.profileRepairManagerInstance = typeof window.profileRepairManager === 'object';
            
            // Check if main functions are available
            results.showUserAccountSelectionModal = typeof showUserAccountSelectionModal === 'function';
            results.showProfileRepairModal = typeof showProfileRepairModal === 'function';
            
            return results;
        });

        if (!jsChecks.importManager) {
            throw new Error('ImportManager class not loaded');
        }
        
        if (!jsChecks.importManagerInstance) {
            throw new Error('ImportManager instance not created');
        }
        
        if (!jsChecks.profileRepairManager) {
            throw new Error('ProfileRepairManager class not loaded');
        }
        
        if (!jsChecks.profileRepairManagerInstance) {
            throw new Error('ProfileRepairManager instance not created');
        }
    }

    async testImportTabSwitching() {
        // Test tab switching functionality
        await this.page.click('[data-tab="json-paste"]');
        
        // Wait for tab to become active
        await this.page.waitForSelector('.tab-content[data-tab="json-paste"].active', { timeout: 5000 });
        
        // Switch back to upload tab
        await this.page.click('[data-tab="json-upload"]');
        await this.page.waitForSelector('.tab-content[data-tab="json-upload"].active', { timeout: 5000 });
    }

    async testJSONValidation() {
        // Switch to JSON paste tab
        await this.page.click('[data-tab="json-paste"]');
        await this.page.waitForSelector('.tab-content[data-tab="json-paste"].active');
        
        // Enter invalid JSON
        await this.page.fill('#json-textarea', '{ invalid json }');
        
        // Wait for validation to appear
        await this.page.waitForSelector('#json-validation', { timeout: 5000 });
        
        const validationText = await this.page.textContent('#json-validation');
        if (!validationText.includes('Invalid JSON')) {
            throw new Error('JSON validation not working for invalid JSON');
        }
        
        // Enter valid JSON
        await this.page.fill('#json-textarea', JSON.stringify(TEST_PROFILE, null, 2));
        
        // Wait for validation to update
        await this.page.waitForTimeout(1000);
        
        const validValidationText = await this.page.textContent('#json-validation');
        if (!validValidationText.includes('Valid JSON') && !validValidationText.includes('✅')) {
            throw new Error('JSON validation not working for valid JSON');
        }
    }

    async testImportControls() {
        // Ensure we have valid JSON in the textarea
        await this.page.click('[data-tab="json-paste"]');
        await this.page.fill('#json-textarea', JSON.stringify(TEST_PROFILE, null, 2));
        
        // Wait for import controls to appear
        await this.page.waitForSelector('#import-controls', { timeout: 5000 });
        
        // Check if import controls are visible
        const controlsVisible = await this.page.isVisible('#import-controls');
        if (!controlsVisible) {
            throw new Error('Import controls not visible after entering valid JSON');
        }
        
        // Check if buttons are present
        const buttons = ['#validate-only-btn', '#import-profile-btn', '#clear-import-btn'];
        for (const button of buttons) {
            const buttonExists = await this.page.$(button);
            if (!buttonExists) {
                throw new Error(`Import button not found: ${button}`);
            }
        }
    }

    async testValidationButton() {
        // Ensure we have valid JSON
        await this.page.click('[data-tab="json-paste"]');
        await this.page.fill('#json-textarea', JSON.stringify(TEST_PROFILE, null, 2));
        await this.page.waitForSelector('#import-controls');
        
        // Click validate button
        await this.page.click('#validate-only-btn');
        
        // Wait for status to appear
        await this.page.waitForSelector('#import-status', { timeout: 10000 });
        
        // Check if status is visible
        const statusVisible = await this.page.isVisible('#import-status');
        if (!statusVisible) {
            throw new Error('Import status not shown after validation');
        }
    }

    async testClearButton() {
        // Add some data first
        await this.page.click('[data-tab="json-paste"]');
        await this.page.fill('#json-textarea', JSON.stringify(TEST_PROFILE, null, 2));
        await this.page.waitForSelector('#import-controls');
        
        // Click clear button
        await this.page.click('#clear-import-btn');
        
        // Check if textarea is cleared
        const textareaValue = await this.page.inputValue('#json-textarea');
        if (textareaValue.trim() !== '') {
            throw new Error('Clear button did not clear the textarea');
        }
        
        // Check if controls are hidden
        const controlsVisible = await this.page.isVisible('#import-controls');
        if (controlsVisible) {
            throw new Error('Import controls still visible after clear');
        }
    }

    async runAllTests() {
        console.log('🚀 Starting Frontend Integration Tests');
        console.log('=' * 60);
        
        await this.runTest('Page Load', () => this.testPageLoad());
        await this.runTest('JavaScript Loading', () => this.testJavaScriptLoading());
        await this.runTest('Import Tab Switching', () => this.testImportTabSwitching());
        await this.runTest('JSON Validation', () => this.testJSONValidation());
        await this.runTest('Import Controls', () => this.testImportControls());
        await this.runTest('Validation Button', () => this.testValidationButton());
        await this.runTest('Clear Button', () => this.testClearButton());
        
        console.log('\n' + '=' * 60);
        console.log('📊 Test Results Summary');
        console.log(`✅ Passed: ${this.results.passed}`);
        console.log(`❌ Failed: ${this.results.failed}`);
        console.log(`📈 Success Rate: ${(this.results.passed / (this.results.passed + this.results.failed) * 100).toFixed(1)}%`);
        
        if (this.results.failed > 0) {
            console.log('\n❌ Failed Tests:');
            this.results.tests.filter(t => t.status === 'FAIL').forEach(test => {
                console.log(`   - ${test.name}: ${test.error}`);
            });
        }
        
        return this.results.failed === 0;
    }
}

// Export for use with Playwright
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { FrontendIntegrationTest, TEST_CONFIG, TEST_PROFILE };
}
