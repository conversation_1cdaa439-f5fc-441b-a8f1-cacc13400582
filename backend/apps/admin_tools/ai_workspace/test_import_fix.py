#!/usr/bin/env python3
"""
Test script to validate the user profile import fix.
This script tests the import functionality that was just implemented.
"""

import json
import requests
import os
import sys
import django

# Add the project root to the Python path
sys.path.insert(0, '/usr/src/app')

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client
from django.urls import reverse

User = get_user_model()

def test_profile_import():
    """Test the profile import functionality"""
    print("🧪 Testing User Profile Import Fix")
    print("=" * 50)
    
    # Load test profile data
    test_profile_path = '/usr/src/app/apps/admin_tools/ai_workspace/test_profile.json'
    
    try:
        with open(test_profile_path, 'r') as f:
            profile_data = json.load(f)
        print("✅ Test profile data loaded successfully")
    except Exception as e:
        print(f"❌ Failed to load test profile: {e}")
        return False
    
    # Create a test admin user
    try:
        admin_user, created = User.objects.get_or_create(
            username='test_admin',
            defaults={
                'email': '<EMAIL>',
                'is_staff': True,
                'is_superuser': True
            }
        )
        if created:
            admin_user.set_password('testpass123')
            admin_user.save()
        print("✅ Test admin user ready")
    except Exception as e:
        print(f"❌ Failed to create admin user: {e}")
        return False
    
    # Create Django test client
    client = Client()
    
    # Login as admin
    login_success = client.login(username='test_admin', password='testpass123')
    if not login_success:
        print("❌ Failed to login as admin")
        return False
    print("✅ Logged in as admin")
    
    # Test the import endpoint
    import_url = '/admin/user-profiles/import/'
    
    try:
        # First, test validation only
        print("\n📋 Testing validation endpoint...")
        validation_response = client.post(
            '/admin/user-profiles/validate/',
            data=json.dumps({
                'profile_data': profile_data,
                'options': {'validate_only': True}
            }),
            content_type='application/json'
        )
        
        print(f"Validation response status: {validation_response.status_code}")
        if validation_response.status_code == 200:
            validation_result = validation_response.json()
            print(f"✅ Validation successful: {validation_result.get('success', False)}")
            if validation_result.get('warnings'):
                print(f"⚠️  Warnings: {len(validation_result['warnings'])}")
        else:
            print(f"❌ Validation failed with status {validation_response.status_code}")
            try:
                error_data = validation_response.json()
                print(f"Error: {error_data.get('error', 'Unknown error')}")
            except:
                print(f"Response content: {validation_response.content}")
        
        # Now test the actual import
        print("\n📤 Testing import endpoint...")
        import_response = client.post(
            import_url,
            data=json.dumps({
                'profile_data': profile_data,
                'options': {
                    'overwrite_existing': True,
                    'validate_before_import': True,
                    'create_backup': False
                }
            }),
            content_type='application/json'
        )
        
        print(f"Import response status: {import_response.status_code}")
        
        if import_response.status_code == 200:
            import_result = import_response.json()
            print(f"✅ Import successful: {import_result.get('success', False)}")
            print(f"Profile ID: {import_result.get('profile_id')}")
            print(f"User ID: {import_result.get('user_id')}")
            print(f"Created records: {import_result.get('created_records', 0)}")
            print(f"Updated records: {import_result.get('updated_records', 0)}")
            
            if import_result.get('warnings'):
                print(f"⚠️  Warnings: {len(import_result['warnings'])}")
                for warning in import_result['warnings'][:3]:  # Show first 3 warnings
                    print(f"   - {warning}")
            
            return True
        else:
            print(f"❌ Import failed with status {import_response.status_code}")
            try:
                error_data = import_response.json()
                print(f"Error: {error_data.get('error', 'Unknown error')}")
                if error_data.get('details'):
                    print(f"Details: {error_data['details']}")
            except:
                print(f"Response content: {import_response.content}")
            return False
            
    except Exception as e:
        print(f"❌ Exception during import test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_frontend_endpoints():
    """Test that the frontend can access the necessary endpoints"""
    print("\n🌐 Testing Frontend Endpoint Access")
    print("=" * 40)
    
    # Test with requests to simulate browser behavior
    base_url = 'http://localhost:8000'
    
    # Create session
    session = requests.Session()
    
    # Get CSRF token from login page
    try:
        login_page = session.get(f'{base_url}/admin/login/')
        if login_page.status_code != 200:
            print(f"❌ Cannot access admin login page: {login_page.status_code}")
            return False
        print("✅ Admin login page accessible")
        
        # Extract CSRF token (simplified)
        csrf_token = None
        for line in login_page.text.split('\n'):
            if 'csrfmiddlewaretoken' in line and 'value=' in line:
                csrf_token = line.split('value="')[1].split('"')[0]
                break
        
        if not csrf_token:
            print("❌ Could not extract CSRF token")
            return False
        print("✅ CSRF token extracted")
        
        # Login
        login_data = {
            'username': 'test_admin',
            'password': 'testpass123',
            'csrfmiddlewaretoken': csrf_token
        }
        
        login_response = session.post(f'{base_url}/admin/login/', data=login_data)
        if login_response.status_code != 200 or 'admin/login' in login_response.url:
            print(f"❌ Login failed: {login_response.status_code}")
            return False
        print("✅ Login successful")
        
        # Test user profile management page
        profile_page = session.get(f'{base_url}/admin/user-profiles/')
        if profile_page.status_code != 200:
            print(f"❌ Cannot access user profile management page: {profile_page.status_code}")
            return False
        print("✅ User profile management page accessible")
        
        # Check if the import button functionality is present
        if 'import-profile-btn' in profile_page.text:
            print("✅ Import button found in page")
        else:
            print("⚠️  Import button not found in page")
        
        return True
        
    except Exception as e:
        print(f"❌ Exception during frontend test: {e}")
        return False

if __name__ == '__main__':
    print("🚀 Starting User Profile Import Tests")
    print("=" * 60)
    
    # Test the import functionality
    import_success = test_profile_import()
    
    # Test frontend access
    frontend_success = test_frontend_endpoints()
    
    print("\n📊 Test Results Summary")
    print("=" * 30)
    print(f"Import functionality: {'✅ PASS' if import_success else '❌ FAIL'}")
    print(f"Frontend access: {'✅ PASS' if frontend_success else '❌ FAIL'}")
    
    if import_success and frontend_success:
        print("\n🎉 All tests passed! The import fix is working correctly.")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed. Please check the output above.")
        sys.exit(1)
