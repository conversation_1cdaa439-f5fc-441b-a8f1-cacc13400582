#!/usr/bin/env python3
"""
Test All ADHD Direct Activity Request Scenarios

This script tests all the scenarios that were previously failing
to ensure the fix is comprehensive.
"""

import asyncio
import json
import logging
from datetime import datetime

# Setup Django
import os
import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from apps.main.services.conversation_dispatcher import ConversationDispatcher
from apps.user.models import UserProfile
from asgiref.sync import sync_to_async

logging.basicConfig(level=logging.WARNING)  # Reduce noise
logger = logging.getLogger(__name__)


async def test_scenario(message, expected_workflow, description):
    """Test a single scenario."""
    
    print(f"\n📝 Test: {description}")
    print(f"   Message: '{message}'")
    print(f"   Expected: {expected_workflow}")
    
    # Get a test user profile
    user_profiles_qs = UserProfile.objects.filter(is_real=False)
    user_profile = await sync_to_async(user_profiles_qs.first)()
    
    if not user_profile:
        print("❌ No test user profiles found")
        return {'error': 'No test user profiles found'}
    
    try:
        # Initialize ConversationDispatcher
        dispatcher = ConversationDispatcher(
            user_profile_id=str(user_profile.id),
            fail_fast_on_errors=True
        )
        
        # Process the message
        result = await dispatcher.process_message({
            'text': message,
            'timestamp': datetime.now().isoformat(),
            'metadata': {}
        })
        
        workflow_type = result.get('workflow_type')
        confidence = result.get('confidence', 0.0)
        
        # Check if the result matches expectation
        success = workflow_type == expected_workflow
        
        if success:
            print(f"   ✅ SUCCESS: Got {workflow_type} (confidence: {confidence:.2f})")
        else:
            print(f"   ❌ FAILED: Got {workflow_type}, expected {expected_workflow}")
        
        return {
            'message': message,
            'expected': expected_workflow,
            'actual': workflow_type,
            'confidence': confidence,
            'success': success,
            'description': description
        }
        
    except Exception as e:
        print(f"   ❌ ERROR: {e}")
        return {
            'message': message,
            'expected': expected_workflow,
            'error': str(e),
            'success': False,
            'description': description
        }


async def main():
    """Main test function."""
    print("🧪 COMPREHENSIVE ADHD DIRECT ACTIVITY REQUEST TEST")
    print("=" * 60)
    print(f"🕒 Test started at: {datetime.now().isoformat()}")
    
    # Test scenarios based on the original failing cases
    test_scenarios = [
        {
            'message': "I want to improve my wellness and productivity",
            'expected': 'user_onboarding',  # Should not go directly to wheel_generation
            'description': 'General wellness request'
        },
        {
            'message': "I need some activity suggestions",
            'expected': 'discussion',  # Low trust users should get mood assessment first
            'description': 'Direct activity request'
        },
        {
            'message': "I'm ready for some activities",
            'expected': 'discussion',  # Low trust users should get mood assessment first
            'description': 'Ready for activities'
        },
        {
            'message': "What should I do today?",
            'expected': 'discussion',  # Should assess mood first for low trust users
            'description': 'Open-ended activity request'
        },
        {
            'message': "Can you recommend some activities?",
            'expected': 'discussion',  # Should assess mood first for low trust users
            'description': 'Activity recommendation request'
        },
        {
            'message': "Hello, how are you?",
            'expected': 'discussion',  # General greeting should go to discussion
            'description': 'General greeting'
        }
    ]
    
    results = []
    success_count = 0
    
    for scenario in test_scenarios:
        result = await test_scenario(
            scenario['message'],
            scenario['expected'],
            scenario['description']
        )
        results.append(result)
        
        if result.get('success'):
            success_count += 1
    
    # Summary
    total_tests = len(test_scenarios)
    success_rate = (success_count / total_tests) * 100
    
    print(f"\n📊 TEST SUMMARY")
    print("=" * 30)
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {success_count}")
    print(f"Failed: {total_tests - success_count}")
    print(f"Success Rate: {success_rate:.1f}%")
    
    if success_rate == 100:
        print("🎉 ALL TESTS PASSED! The fix is working perfectly!")
    elif success_rate >= 80:
        print("✅ Most tests passed. Minor issues may remain.")
    else:
        print("❌ Significant issues remain. More work needed.")
    
    # Save detailed results
    with open('/tmp/all_scenarios_test_results.json', 'w') as f:
        json.dump({
            'summary': {
                'total_tests': total_tests,
                'passed': success_count,
                'failed': total_tests - success_count,
                'success_rate': success_rate
            },
            'results': results,
            'timestamp': datetime.now().isoformat()
        }, f, indent=2, default=str)
    
    print(f"\n💾 Detailed results saved to /tmp/all_scenarios_test_results.json")
    print(f"🕒 Test completed at: {datetime.now().isoformat()}")
    
    return results


if __name__ == '__main__':
    asyncio.run(main())
