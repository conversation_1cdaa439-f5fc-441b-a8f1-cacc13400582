{"test_timestamp": "2025-06-12T10:22:44.444884", "issues_found": [], "test_results": {"first_time_wellness": {"status": "error", "error": "'coroutine' object has no attribute 'afirst'", "config": {"scenario": "Test ADHD First-Time User Experience", "executionMode": "full-real", "userProfile": "PhiPhi", "customCriteria": {"name": "ADHD First-Time User Mood Assessment", "description": "Test if system asks about mood before generating activities", "criteria": [{"name": "<PERSON><PERSON>", "description": "Should ask about user's current mood", "weight": 0.4}, {"name": "No Immediate Wheel", "description": "Should not immediately generate activity wheel", "weight": 0.6}]}}}}, "recommendations": []}