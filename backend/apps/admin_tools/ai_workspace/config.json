{"workspace_info": {"name": "Goali AI Workspace", "version": "1.0.0", "description": "AI-intuitive tools for improving Goali admin interfaces and tools", "created": "2025-01-27", "last_updated": "2025-01-27"}, "tools": {"agent_manager": {"enabled": true, "description": "Manage agent characteristics, instructions, and LLM configurations", "capabilities": ["read_agent_definitions", "update_agent_instructions", "modify_llm_configs", "validate_agent_schemas", "backup_agent_configs"]}, "benchmark_analyzer": {"enabled": true, "description": "Access and analyze benchmark results with intelligent insights", "capabilities": ["query_benchmark_runs", "analyze_performance_trends", "extract_context_packages", "generate_improvement_recommendations", "compare_agent_performance"]}, "admin_validator": {"enabled": true, "description": "Validate admin page quality using Playwright automation", "capabilities": ["test_page_functionality", "validate_ui_components", "check_accessibility", "performance_testing", "cross_browser_validation"]}, "knowledge_manager": {"enabled": true, "description": "Manage documentation and findings from AI agent work", "capabilities": ["document_findings", "track_improvements", "maintain_knowledge_base", "generate_reports", "version_control_docs"]}}, "settings": {"default_timeout": 300, "max_concurrent_operations": 5, "logging_level": "INFO", "backup_enabled": true, "validation_strict_mode": true}, "paths": {"docs": "./docs/", "tests": "./tests/", "tools": "./tools/", "logs": "./logs/", "backups": "./backups/"}, "integrations": {"playwright": {"browsers": ["chromium", "firefox", "webkit"], "headless": true, "timeout": 30000}, "django": {"settings_module": "config.settings.dev", "database_access": true}, "celery": {"task_monitoring": true, "result_backend_access": true}}}