"""
Test suite for Agent Debugging System

Tests for the comprehensive debugging and improvement system.
"""

import asyncio
import pytest
import os
import sys
import django
from pathlib import Path

# Setup Django for testing
if not django.conf.settings.configured:
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')
    django.setup()

# Add the workspace to Python path
workspace_path = Path(__file__).parent.parent
sys.path.insert(0, str(workspace_path))

from tools.agent_debugging_system import (
    AgentDebuggingSystem, GenericSituation, GenericUser, 
    AgentChangeSet, ImpactMeasurement
)
from workspace import AIWorkspace


class TestGenericSituation:
    """Test the GenericSituation dataclass."""
    
    def test_generic_situation_creation(self):
        """Test creating a generic situation."""
        situation = GenericSituation(
            id="test_situation",
            name="Test Situation",
            description="A test situation",
            workflow_type="wheel_generation",
            user_message="Test message",
            context_variables={"trust_level": 50},
            expected_outcomes=["outcome1", "outcome2"],
            evaluation_criteria={"quality": "high"}
        )
        
        assert situation.id == "test_situation"
        assert situation.workflow_type == "wheel_generation"
        assert situation.difficulty_level == 1  # Default value
        assert situation.tags == []  # Default empty list


class TestGenericUser:
    """Test the GenericUser dataclass."""
    
    def test_generic_user_creation(self):
        """Test creating a generic user archetype."""
        user = GenericUser(
            id="test_user",
            archetype_name="Test User",
            description="A test user archetype",
            demographics={"age": 30},
            personality_traits={"openness": 0.8},
            trust_level=50,
            communication_preferences={"tone": "friendly"},
            behavioral_patterns={"exploration": "high"},
            limitations=["time constraints"],
            capabilities=["quick learning"]
        )
        
        assert user.id == "test_user"
        assert user.trust_level == 50
        assert user.is_temporary is True  # Default value
    
    def test_to_user_profile_data(self):
        """Test conversion to UserProfile data."""
        user = GenericUser(
            id="test_user",
            archetype_name="Test User",
            description="A test user",
            demographics={"age": 30},
            personality_traits={"openness": 0.8},
            trust_level=50,
            communication_preferences={"tone": "friendly"},
            behavioral_patterns={"exploration": "high"},
            limitations=[],
            capabilities=[]
        )
        
        profile_data = user.to_user_profile_data()
        
        assert profile_data['profile_name'] == "Test User"
        assert profile_data['is_real'] is False
        assert profile_data['trust_level'] == 50


class TestAgentDebuggingSystem:
    """Test the main AgentDebuggingSystem class."""
    
    def test_debugging_system_initialization(self):
        """Test debugging system initialization."""
        system = AgentDebuggingSystem()
        
        assert system is not None
        assert system.archetype_library is not None
        assert system.situation_library is not None
        assert len(system.archetype_library) > 0
        assert len(system.situation_library) > 0
    
    def test_predefined_situations(self):
        """Test that predefined situations are loaded."""
        system = AgentDebuggingSystem()
        
        # Check for core situations
        assert "wheel_gen_basic" in system.situation_library
        assert "wheel_gen_stressed" in system.situation_library
        assert "wheel_gen_creative" in system.situation_library
        assert "discussion_basic" in system.situation_library
        assert "onboarding_new" in system.situation_library
        
        # Check situation properties
        basic_wheel = system.situation_library["wheel_gen_basic"]
        assert basic_wheel.workflow_type == "wheel_generation"
        assert basic_wheel.difficulty_level == 1
        assert "wheel_generation" in basic_wheel.tags
    
    def test_predefined_archetypes(self):
        """Test that predefined archetypes are loaded."""
        system = AgentDebuggingSystem()
        
        # Check for core archetypes
        assert "curious_beginner" in system.archetype_library
        assert "stressed_professional" in system.archetype_library
        assert "creative_explorer" in system.archetype_library
        assert "cautious_senior" in system.archetype_library
        assert "ambitious_student" in system.archetype_library
        
        # Check archetype properties
        beginner = system.archetype_library["curious_beginner"]
        assert beginner.trust_level == 35
        assert beginner.personality_traits["openness"] == 0.8
        assert beginner.is_temporary is True
    
    def test_get_situations_by_workflow(self):
        """Test filtering situations by workflow type."""
        system = AgentDebuggingSystem()
        
        wheel_situations = system.get_situations_by_workflow("wheel_generation")
        discussion_situations = system.get_situations_by_workflow("discussion")
        
        assert len(wheel_situations) >= 3  # At least 3 wheel generation situations
        assert len(discussion_situations) >= 1  # At least 1 discussion situation
        
        # All returned situations should match the workflow type
        for situation in wheel_situations:
            assert situation.workflow_type == "wheel_generation"
    
    def test_get_situations_by_difficulty(self):
        """Test filtering situations by difficulty level."""
        system = AgentDebuggingSystem()
        
        easy_situations = system.get_situations_by_difficulty(1)
        hard_situations = system.get_situations_by_difficulty(3)
        
        assert len(easy_situations) >= 2  # At least 2 easy situations
        
        # All returned situations should match the difficulty level
        for situation in easy_situations:
            assert situation.difficulty_level == 1
    
    def test_get_archetypes_by_trust_level(self):
        """Test filtering archetypes by trust level."""
        system = AgentDebuggingSystem()
        
        low_trust = system.get_archetypes_by_trust_level(0, 40)
        high_trust = system.get_archetypes_by_trust_level(60, 100)
        
        assert len(low_trust) >= 1  # At least 1 low trust archetype
        assert len(high_trust) >= 1  # At least 1 high trust archetype
        
        # Check trust level ranges
        for archetype in low_trust:
            assert 0 <= archetype.trust_level <= 40
        for archetype in high_trust:
            assert 60 <= archetype.trust_level <= 100
    
    def test_get_archetypes_by_trait(self):
        """Test filtering archetypes by personality trait."""
        system = AgentDebuggingSystem()
        
        open_archetypes = system.get_archetypes_by_trait("openness", 0.7)
        
        assert len(open_archetypes) >= 1  # At least 1 highly open archetype
        
        # Check trait values
        for archetype in open_archetypes:
            assert archetype.personality_traits.get("openness", 0) >= 0.7
    
    def test_create_custom_situation(self):
        """Test creating a custom situation."""
        system = AgentDebuggingSystem()
        
        situation_data = {
            "name": "Custom Test Situation",
            "description": "A custom situation for testing",
            "workflow_type": "test_workflow",
            "user_message": "Test message",
            "context_variables": {"test": True},
            "expected_outcomes": ["test outcome"],
            "evaluation_criteria": {"test": "criteria"}
        }
        
        situation = system.create_custom_situation(situation_data)
        
        assert situation.name == "Custom Test Situation"
        assert situation.workflow_type == "test_workflow"
        assert situation.id in system.situation_library
    
    def test_create_custom_archetype(self):
        """Test creating a custom archetype."""
        system = AgentDebuggingSystem()
        
        archetype_data = {
            "archetype_name": "Custom Test Archetype",
            "description": "A custom archetype for testing",
            "demographics": {"age": 25},
            "personality_traits": {"openness": 0.5},
            "trust_level": 40,
            "communication_preferences": {"tone": "casual"},
            "behavioral_patterns": {"test": "pattern"},
            "limitations": ["test limitation"],
            "capabilities": ["test capability"]
        }
        
        archetype = system.create_custom_archetype(archetype_data)
        
        assert archetype.archetype_name == "Custom Test Archetype"
        assert archetype.trust_level == 40
        assert archetype.id in system.archetype_library


class TestWorkspaceIntegration:
    """Test integration with the main AIWorkspace."""
    
    @pytest.mark.asyncio
    async def test_workspace_debugging_system_integration(self):
        """Test that debugging system is properly integrated with workspace."""
        workspace = AIWorkspace()
        await workspace.initialize()
        
        assert workspace.debugging_system is not None
        assert hasattr(workspace, 'debug_agent_with_user_stories')
        assert hasattr(workspace, 'test_agent_changes')
        assert hasattr(workspace, 'manage_temporary_profiles')
    
    @pytest.mark.asyncio
    async def test_temporary_profile_management(self):
        """Test temporary profile management through workspace."""
        workspace = AIWorkspace()
        await workspace.initialize()
        
        # Test profile creation
        result = await workspace.manage_temporary_profiles(
            action='create',
            archetype_id='curious_beginner',
            session_id='test_session'
        )
        
        if result['status'] == 'created':
            assert 'profile_id' in result
            assert 'profile_name' in result
            
            # Test profile listing
            list_result = await workspace.manage_temporary_profiles(
                action='list',
                session_id='test_session'
            )
            
            assert list_result['status'] == 'listed'
            assert list_result['total_count'] >= 1
            
            # Test profile cleanup
            cleanup_result = await workspace.manage_temporary_profiles(
                action='prune',
                session_id='test_session',
                older_than_hours=0
            )
            
            assert cleanup_result['status'] == 'pruned'
            assert cleanup_result['deleted_count'] >= 1


class TestAgentChangeSet:
    """Test the AgentChangeSet dataclass."""
    
    def test_agent_change_set_creation(self):
        """Test creating an agent change set."""
        from datetime import datetime
        
        change_set = AgentChangeSet(
            change_id="test_change",
            agent_role="mentor",
            change_type="instructions",
            description="Test change",
            changes={"instructions": "New instructions"},
            expected_impact="improvement",
            created_at=datetime.now()
        )
        
        assert change_set.change_id == "test_change"
        assert change_set.agent_role == "mentor"
        assert change_set.change_type == "instructions"


class TestImpactMeasurement:
    """Test the ImpactMeasurement dataclass."""
    
    def test_impact_measurement_creation(self):
        """Test creating an impact measurement."""
        from datetime import datetime
        
        measurement = ImpactMeasurement(
            change_id="test_change",
            baseline_metrics={"score": 0.5},
            modified_metrics={"score": 0.7},
            improvement_score=0.2,
            semantic_score_delta=0.2,
            execution_time_delta=-1.0,
            success_rate_delta=0.1,
            detailed_analysis={"assessment": "improvement"},
            measurement_timestamp=datetime.now()
        )
        
        assert measurement.change_id == "test_change"
        assert measurement.improvement_score == 0.2
        assert measurement.semantic_score_delta == 0.2


if __name__ == '__main__':
    # Run basic tests
    print("Running Agent Debugging System Tests...")
    
    # Test basic initialization
    system = AgentDebuggingSystem()
    print(f"✅ System initialized with {len(system.situation_library)} situations and {len(system.archetype_library)} archetypes")
    
    # Test situation filtering
    wheel_situations = system.get_situations_by_workflow("wheel_generation")
    print(f"✅ Found {len(wheel_situations)} wheel generation situations")
    
    # Test archetype filtering
    low_trust = system.get_archetypes_by_trust_level(0, 40)
    print(f"✅ Found {len(low_trust)} low trust archetypes")
    
    print("\nTo run full test suite:")
    print("pytest backend/apps/admin_tools/ai_workspace/tests/test_debugging_system.py -v")
