#!/usr/bin/env python3
"""
Test script for <PERSON>'s profile import with enhanced trait fields and inventory creation.

This script tests the complete import process including:
- Trait manifestation and context fields
- Inventory creation from resources
- API response validation
"""

import os
import sys
import django
import json
import traceback
from pathlib import Path

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from apps.user.services.profile_import_service import ProfileImportService
from apps.user.models import UserProfile, UserTraitInclination, Inventory, UserResource
from django.contrib.auth.models import User
from django.test import Client


def load_guillaume_profile():
    """Lo<PERSON>'s profile from the JSON file."""
    # Try multiple possible paths
    possible_paths = [
        Path("/tmp/guigui.json"),  # Copied file
        Path(__file__).parent.parent.parent.parent.parent / "docs" / "users" / "guigui.json",
        Path("/usr/src/app/docs/users/guigui.json"),
        Path("docs/users/guigui.json"),
    ]

    for json_path in possible_paths:
        if json_path.exists():
            with open(json_path, 'r', encoding='utf-8') as f:
                return json.load(f)

    raise FileNotFoundError(f"Guillaume's profile not found at any of: {[str(p) for p in possible_paths]}")


def cleanup_existing_guillaume():
    """Clean up any existing Guillaume profile."""
    try:
        # Try to find by username
        user = User.objects.get(username='guillaume_dev_fr')
        user.delete()
        print("✅ Cleaned up existing Guillaume profile")
        return True
    except User.DoesNotExist:
        print("ℹ️  No existing Guillaume profile found")
        return False


def test_guillaume_import():
    """Test Guillaume's profile import."""
    print("🧪 Testing Guillaume's Profile Import")
    print("=" * 50)
    
    # Step 1: Load profile data
    print("📄 Loading Guillaume's profile data...")
    try:
        profile_data = load_guillaume_profile()
        print(f"✅ Loaded profile for: {profile_data['profile_name']}")
        print(f"   - Traits: {len(profile_data.get('traits', []))}")
        print(f"   - Resources: {len(profile_data.get('resources', []))}")
        print(f"   - Environments: {len(profile_data.get('environments', []))}")
        
        # Check if traits have new fields
        sample_trait = profile_data.get('traits', [{}])[0]
        has_manifestation = 'manifestation' in sample_trait
        has_context = 'context' in sample_trait
        print(f"   - Traits have manifestation: {has_manifestation}")
        print(f"   - Traits have context: {has_context}")
        
    except Exception as e:
        print(f"❌ Failed to load profile: {e}")
        return False
    
    # Step 2: Clean up existing data
    print("\n🧹 Cleaning up existing data...")
    cleanup_existing_guillaume()
    
    # Step 3: Import profile
    print("\n📥 Importing profile...")
    try:
        admin_user = User.objects.filter(is_staff=True).first()
        if not admin_user:
            print("❌ No admin user found for import")
            return False
            
        import_service = ProfileImportService(initiated_by=admin_user)
        result = import_service.import_profile(profile_data)
        
        if result['success']:
            print("✅ Profile import successful!")
            print(f"   - Created records: {result.get('created_records', 0)}")
            print(f"   - Updated records: {result.get('updated_records', 0)}")
            if result.get('warnings'):
                print(f"   - Warnings: {len(result['warnings'])}")
        else:
            print(f"❌ Profile import failed: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Import exception: {e}")
        traceback.print_exc()
        return False
    
    # Step 4: Validate imported data
    print("\n🔍 Validating imported data...")
    try:
        # Find Guillaume's profile
        user = User.objects.get(username='guillaume_dev_fr')
        profile = UserProfile.objects.get(user=user)
        print(f"✅ Found profile: {profile.profile_name} (ID: {profile.id})")
        
        # Check traits with new fields
        traits = UserTraitInclination.objects.filter(user_profile=profile)
        print(f"✅ Traits imported: {traits.count()}")
        
        traits_with_manifestation = traits.exclude(manifestation__isnull=True).exclude(manifestation='')
        traits_with_context = traits.exclude(context__isnull=True).exclude(context='')
        
        print(f"   - With manifestation: {traits_with_manifestation.count()}")
        print(f"   - With context: {traits_with_context.count()}")
        
        # Show sample trait
        sample_trait = traits.first()
        if sample_trait:
            print(f"   - Sample: {sample_trait.generic_trait.code}")
            print(f"     * Strength: {sample_trait.strength}")
            print(f"     * Awareness: {sample_trait.awareness}")
            print(f"     * Manifestation: {sample_trait.manifestation[:50]}..." if sample_trait.manifestation else "     * Manifestation: None")
            print(f"     * Context: {sample_trait.context}" if sample_trait.context else "     * Context: None")
        
        # Check inventories
        inventories = Inventory.objects.filter(user_profile=profile)
        print(f"✅ Inventories created: {inventories.count()}")
        
        total_resources_in_inventories = 0
        for inv in inventories:
            resource_count = inv.resources.count()
            total_resources_in_inventories += resource_count
            print(f"   - {inv.name}: {resource_count} resources")
            
        # Check resources
        resources = UserResource.objects.filter(user_environment__user_profile=profile)
        print(f"✅ Resources imported: {resources.count()}")
        print(f"   - Resources in inventories: {total_resources_in_inventories}")
        
        # Show sample resources
        for resource in resources[:3]:
            print(f"   - {resource.specific_name} ({resource.generic_resource.code if resource.generic_resource else 'No generic'})")
        
    except Exception as e:
        print(f"❌ Validation failed: {e}")
        traceback.print_exc()
        return False
    
    # Step 5: Test API response
    print("\n🌐 Testing API response...")
    try:
        client = Client()
        admin_user = User.objects.filter(is_staff=True).first()
        client.force_login(admin_user)
        
        # Test profile API
        response = client.get(f'/admin/user-profiles/api/{profile.id}/')
        if response.status_code == 200:
            data = response.json()
            api_traits = data.get('traits', [])
            print(f"✅ Profile API working: {len(api_traits)} traits returned")
            
            # Check if new fields are in API response
            traits_with_manifestation_api = [t for t in api_traits if t.get('manifestation')]
            traits_with_context_api = [t for t in api_traits if t.get('context')]
            
            print(f"   - API traits with manifestation: {len(traits_with_manifestation_api)}")
            print(f"   - API traits with context: {len(traits_with_context_api)}")
            
        else:
            print(f"❌ Profile API failed: {response.status_code}")
            return False
        
        # Test inventory API
        inv_response = client.get(f'/admin/user-profiles/api/{profile.id}/inventory/')
        if inv_response.status_code == 200:
            inv_data = inv_response.json()
            api_inventories = inv_data.get('inventories', [])
            api_total_resources = inv_data.get('statistics', {}).get('total_resources', 0)
            
            print(f"✅ Inventory API working: {len(api_inventories)} inventories, {api_total_resources} total resources")
            
            if len(api_inventories) == 0:
                print("⚠️  Warning: No inventories returned by API")
                return False
                
        else:
            print(f"❌ Inventory API failed: {inv_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API test failed: {e}")
        traceback.print_exc()
        return False
    
    print("\n🎉 All tests passed!")
    print("=" * 50)
    print("✅ Guillaume's profile import is working correctly")
    print("✅ Trait manifestation and context fields are populated")
    print("✅ Inventories are created from resources")
    print("✅ API endpoints return correct data")
    
    return True


if __name__ == "__main__":
    success = test_guillaume_import()
    sys.exit(0 if success else 1)
