#!/usr/bin/env python
"""
Test tool to investigate guigui.json import issues
Reproduces the exact same import process as the admin interface
"""
import os
import sys
import django
import json
import traceback

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from apps.user.services.profile_import_service import ProfileImportService
from apps.user.services.user_profile_business_objects import UserProfileImportRequestBO
from pydantic import ValidationError

def test_guigui_import():
    """Test importing guigui.json and capture detailed error information"""
    print("🧪 Testing guigui.json Import")
    print("=" * 60)
    
    # Try to load guigui.json from the docs directory
    guigui_path = '/usr/src/app/docs/users/guigui.json'
    
    try:
        with open(guigui_path, 'r') as f:
            profile_data = json.load(f)
        print(f"✅ Loaded guigui.json from {guigui_path}")
        print(f"📊 Profile data keys: {list(profile_data.keys())}")
        
        # Check if resources exist
        if 'resources' in profile_data:
            print(f"📦 Found {len(profile_data['resources'])} resources")
            for i, resource in enumerate(profile_data['resources'][:3]):  # Show first 3
                print(f"  Resource {i+1}: {resource.get('specific_name', 'No name')}")
        else:
            print("⚠️ No resources found in profile data")
        
    except FileNotFoundError:
        print(f"❌ guigui.json not found at {guigui_path}")
        print("🔍 Searching for guigui.json...")
        
        # Search for the file
        import subprocess
        try:
            result = subprocess.run(['find', '/usr/src/app', '-name', 'guigui.json', '-type', 'f'], 
                                  capture_output=True, text=True)
            if result.stdout.strip():
                print(f"📍 Found guigui.json at: {result.stdout.strip()}")
                guigui_path = result.stdout.strip()
                with open(guigui_path, 'r') as f:
                    profile_data = json.load(f)
                print("✅ Successfully loaded guigui.json")
            else:
                print("❌ guigui.json not found anywhere in the container")
                return False
        except Exception as e:
            print(f"❌ Error searching for file: {e}")
            return False
    
    except json.JSONDecodeError as e:
        print(f"❌ JSON parsing error: {e}")
        return False
    
    except Exception as e:
        print(f"❌ Error loading guigui.json: {e}")
        return False
    
    # Now test the import process step by step
    print("\n🔍 Testing Import Process Step by Step")
    print("=" * 60)
    
    try:
        # Step 1: Test Pydantic validation
        print("Step 1: Testing Pydantic Business Object Validation...")
        try:
            user_profile_bo = UserProfileImportRequestBO(**profile_data)
            print("✅ Pydantic validation passed")
        except ValidationError as e:
            print("❌ Pydantic validation failed:")
            print(f"Error count: {e.error_count()}")
            for error in e.errors():
                print(f"  Field: {error['loc']}")
                print(f"  Error: {error['msg']}")
                print(f"  Input: {error.get('input', 'N/A')}")
                print("  ---")
            print("\n🔧 This explains why you get generic suggestions instead of specific errors!")
            return False
        except Exception as e:
            print(f"❌ Unexpected validation error: {e}")
            traceback.print_exc()
            return False
        
        # Step 2: Test ProfileImportService
        print("\nStep 2: Testing ProfileImportService...")
        service = ProfileImportService()
        result = service.import_profile(profile_data)
        
        if result.get('success'):
            print("✅ Profile import successful!")
            profile_id = result.get('profile_id')
            print(f"📝 Created profile ID: {profile_id}")
            return True
        else:
            print("❌ Profile import failed:")
            print(f"Error: {result.get('error', 'Unknown error')}")

            if 'field_errors' in result:
                print("Field-specific errors:")
                for field, errors in result['field_errors'].items():
                    print(f"  {field}: {errors}")

            if 'validation_errors' in result:
                print("Validation errors:")
                for error in result['validation_errors']:
                    print(f"  {error}")

            # Check for analysis data with detailed diagnostics
            if 'analysis' in result:
                analysis = result['analysis']
                print(f"\n🔍 Detailed Analysis:")
                print(f"Total errors: {analysis.get('total_errors', 0)}")

                if 'diagnostics' in analysis:
                    print("Diagnostics:")
                    for diagnostic in analysis['diagnostics'][:5]:  # Show first 5
                        print(f"  - {diagnostic.get('message', 'No message')}")
                        print(f"    Field: {diagnostic.get('field_path', 'Unknown')}")
                        print(f"    Current Value: {diagnostic.get('current_value', 'N/A')}")
                        if diagnostic.get('suggestions'):
                            print(f"    Suggestion: {diagnostic['suggestions'][0]}")
                        print()

            return False
    
    except Exception as e:
        print(f"❌ Import process failed with exception: {e}")
        traceback.print_exc()
        return False

def analyze_guigui_structure():
    """Analyze the structure of guigui.json to identify potential issues"""
    print("\n🔍 Analyzing guigui.json Structure")
    print("=" * 60)
    
    try:
        # Load the file
        guigui_path = '/usr/src/app/docs/users/guigui.json'
        try:
            with open(guigui_path, 'r') as f:
                profile_data = json.load(f)
        except FileNotFoundError:
            # Try to find it
            import subprocess
            result = subprocess.run(['find', '/usr/src/app', '-name', 'guigui.json', '-type', 'f'], 
                                  capture_output=True, text=True)
            if result.stdout.strip():
                guigui_path = result.stdout.strip()
                with open(guigui_path, 'r') as f:
                    profile_data = json.load(f)
            else:
                print("❌ Cannot find guigui.json for analysis")
                return False
        
        # Analyze structure
        print("📊 Top-level structure:")
        for key, value in profile_data.items():
            if isinstance(value, list):
                print(f"  {key}: list with {len(value)} items")
                if value and isinstance(value[0], dict):
                    print(f"    First item keys: {list(value[0].keys())}")
            elif isinstance(value, dict):
                print(f"  {key}: dict with keys: {list(value.keys())}")
            else:
                print(f"  {key}: {type(value).__name__} = {str(value)[:50]}...")
        
        # Check resources specifically
        if 'resources' in profile_data:
            print(f"\n📦 Resources Analysis ({len(profile_data['resources'])} items):")
            for i, resource in enumerate(profile_data['resources']):
                print(f"  Resource {i+1}:")
                for key, value in resource.items():
                    print(f"    {key}: {str(value)[:50]}...")
        
        # Check for common issues
        print("\n🔍 Checking for Common Issues:")
        
        # Check required fields
        required_fields = ['user_account', 'profile_name', 'demographics', 'environments']
        missing_required = [field for field in required_fields if field not in profile_data]
        if missing_required:
            print(f"❌ Missing required fields: {missing_required}")
        else:
            print("✅ All required top-level fields present")
        
        # Check user_account structure
        if 'user_account' in profile_data:
            user_account = profile_data['user_account']
            required_user_fields = ['username', 'email', 'first_name', 'last_name']
            missing_user_fields = [field for field in required_user_fields if field not in user_account]
            if missing_user_fields:
                print(f"❌ Missing user_account fields: {missing_user_fields}")
            else:
                print("✅ user_account structure looks good")
        
        return True
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting guigui.json Import Investigation")
    print("=" * 80)
    
    # Run analysis first
    analysis_success = analyze_guigui_structure()
    
    # Then test import
    import_success = test_guigui_import()
    
    print("\n" + "=" * 80)
    print("📊 Investigation Results")
    print("=" * 80)
    print(f"Structure Analysis: {'✅ PASSED' if analysis_success else '❌ FAILED'}")
    print(f"Import Test: {'✅ PASSED' if import_success else '❌ FAILED'}")
    
    if not import_success:
        print("\n🔧 DIAGNOSIS:")
        print("The import is failing at the Pydantic validation stage.")
        print("This explains why you see generic suggestions instead of specific field errors.")
        print("The admin interface should show detailed validation errors, not generic suggestions.")
        print("\n💡 NEXT STEPS:")
        print("1. Fix the Pydantic validation errors shown above")
        print("2. Improve error handling in the admin interface")
        print("3. Ensure specific validation errors are displayed to users")
    else:
        print("\n🎉 Import working correctly!")
