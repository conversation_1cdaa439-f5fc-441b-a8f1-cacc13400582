"""
Test suite for AI Workspace

Basic tests to validate workspace functionality and tool integration.
"""

import asyncio
import pytest
import os
import sys
import django
from pathlib import Path

# Setup Django for testing
if not django.conf.settings.configured:
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')
    django.setup()

# Add the workspace to Python path
workspace_path = Path(__file__).parent.parent
sys.path.insert(0, str(workspace_path))

from workspace import AIWorkspace
from tools.agent_manager import Agent<PERSON>anager
from tools.benchmark_analyzer import BenchmarkAnalyzer
from tools.knowledge_manager import KnowledgeManager
from tools.benchmark_tools import BenchmarkTools


class TestAIWorkspace:
    """Test the main AI Workspace functionality."""
    
    @pytest.mark.asyncio
    async def test_workspace_initialization(self):
        """Test workspace initialization."""
        workspace = AIWorkspace()
        result = await workspace.initialize()
        
        assert result['status'] == 'initialized'
        assert 'session_id' in result
        assert 'capabilities' in result
        assert workspace.initialized is True
    
    @pytest.mark.asyncio
    async def test_workspace_tools_available(self):
        """Test that all workspace tools are available."""
        workspace = AIWorkspace()
        await workspace.initialize()
        
        # Check that all tools are instantiated
        assert workspace.agent_manager is not None
        assert workspace.benchmark_analyzer is not None
        assert workspace.admin_validator is not None
        assert workspace.knowledge_manager is not None
        assert workspace.benchmark_tools is not None


class TestAgentManager:
    """Test the Agent Manager tool."""
    
    def test_agent_manager_initialization(self):
        """Test agent manager initialization."""
        manager = AgentManager()
        assert manager is not None
    
    def test_get_all_agents(self):
        """Test getting all agents."""
        manager = AgentManager()
        agents = manager.get_all_agents()
        
        assert isinstance(agents, list)
        # Should have at least some agents in the system
        if agents:
            agent = agents[0]
            assert 'role' in agent
            assert 'is_active' in agent
            assert 'system_instructions' in agent
    
    def test_get_agent_by_role(self):
        """Test getting agent by role."""
        manager = AgentManager()
        
        # Try to get a common agent role
        agent = manager.get_agent_by_role('mentor')
        
        if agent:  # Only test if mentor agent exists
            assert agent['role'] == 'mentor'
            assert 'system_instructions' in agent
            assert 'llm_config' in agent
    
    def test_get_agent_summary(self):
        """Test getting agent summary."""
        manager = AgentManager()
        summary = manager.get_agent_summary()
        
        assert isinstance(summary, dict)
        assert 'total_agents' in summary
        assert 'active_agents' in summary
        assert 'agents_by_role' in summary


class TestBenchmarkAnalyzer:
    """Test the Benchmark Analyzer tool."""
    
    def test_benchmark_analyzer_initialization(self):
        """Test benchmark analyzer initialization."""
        analyzer = BenchmarkAnalyzer()
        assert analyzer is not None
    
    def test_get_recent_benchmark_runs(self):
        """Test getting recent benchmark runs."""
        analyzer = BenchmarkAnalyzer()
        runs = analyzer.get_recent_benchmark_runs(days=30, limit=10)
        
        assert isinstance(runs, list)
        # Test structure if runs exist
        if runs:
            run = runs[0]
            assert 'id' in run
            assert 'scenario_name' in run
            assert 'agent_role' in run
            assert 'execution_date' in run
    
    @pytest.mark.asyncio
    async def test_analyze_agent_performance_trends(self):
        """Test agent performance trend analysis."""
        analyzer = BenchmarkAnalyzer()
        
        # Try to analyze mentor agent if it exists
        analysis = await analyzer.analyze_agent_performance_trends('mentor', days=30)
        
        assert isinstance(analysis, dict)
        if 'error' not in analysis:
            assert 'agent_role' in analysis
            assert 'performance_metrics' in analysis
            assert 'trend_analysis' in analysis


class TestKnowledgeManager:
    """Test the Knowledge Manager tool."""
    
    def test_knowledge_manager_initialization(self):
        """Test knowledge manager initialization."""
        manager = KnowledgeManager()
        assert manager is not None
        assert manager.docs_path.exists()
        assert manager.logs_path.exists()
        assert manager.reports_path.exists()
    
    def test_document_finding(self):
        """Test documenting a finding."""
        manager = KnowledgeManager()
        
        finding = {
            'title': 'Test Finding',
            'description': 'This is a test finding for validation',
            'category': 'test'
        }
        
        result_path = manager.document_finding(finding)
        assert result_path is not None
        assert Path(result_path).exists()
    
    def test_document_improvement(self):
        """Test documenting an improvement."""
        manager = KnowledgeManager()
        
        improvement = {
            'title': 'Test Improvement',
            'description': 'This is a test improvement for validation',
            'component': 'test_component',
            'impact': 'low'
        }
        
        result_path = manager.document_improvement(improvement)
        assert result_path is not None
        assert Path(result_path).exists()
    
    def test_search_findings(self):
        """Test searching findings."""
        manager = KnowledgeManager()
        
        # Search should return a list (empty or with results)
        results = manager.search_findings('test')
        assert isinstance(results, list)


class TestBenchmarkTools:
    """Test the Benchmark Tools collection."""
    
    @pytest.mark.asyncio
    async def test_benchmark_tools_initialization(self):
        """Test benchmark tools initialization."""
        tools = BenchmarkTools()
        await tools.initialize()
        
        assert tools.client is not None
        assert tools.admin_user is not None
    
    @pytest.mark.asyncio
    async def test_validate_benchmark_system(self):
        """Test benchmark system validation."""
        tools = BenchmarkTools()
        await tools.initialize()
        
        validation = await tools.validate_benchmark_system()
        
        assert isinstance(validation, dict)
        assert 'status' in validation
        assert 'checks' in validation
        assert 'overall_health' in validation


# Integration tests
class TestWorkspaceIntegration:
    """Test workspace integration scenarios."""
    
    @pytest.mark.asyncio
    async def test_system_health_analysis(self):
        """Test complete system health analysis."""
        workspace = AIWorkspace()
        await workspace.initialize()
        
        # This is a comprehensive test that exercises multiple tools
        health_report = await workspace.analyze_system_health()
        
        assert isinstance(health_report, dict)
        assert 'status' in health_report
        assert 'components' in health_report
        
        # Should have analyzed multiple components
        components = health_report['components']
        assert 'agents' in components
        assert 'benchmarks' in components
    
    @pytest.mark.asyncio
    async def test_agent_improvement_workflow(self):
        """Test agent improvement workflow."""
        workspace = AIWorkspace()
        await workspace.initialize()
        
        # Get available agents
        agents = workspace.agent_manager.get_all_agents()
        
        if agents:
            # Test improvement analysis for first active agent
            active_agents = [a for a in agents if a['is_active']]
            if active_agents:
                agent_role = active_agents[0]['role']
                
                improvement_report = await workspace.improve_agent_performance(agent_role)
                
                assert isinstance(improvement_report, dict)
                if 'error' not in improvement_report:
                    assert 'agent_role' in improvement_report
                    assert 'analysis' in improvement_report
                    assert 'improvements' in improvement_report


if __name__ == '__main__':
    # Run basic tests
    print("Running AI Workspace Tests...")
    
    # Test basic initialization
    workspace = AIWorkspace()
    
    async def run_basic_tests():
        init_result = await workspace.initialize()
        print(f"Workspace initialization: {init_result['status']}")
        
        if init_result['status'] == 'initialized':
            print("✅ AI Workspace initialized successfully")
            print(f"Session ID: {init_result['session_id']}")
            print(f"Capabilities: {list(init_result['capabilities'].keys())}")
        else:
            print("❌ AI Workspace initialization failed")
            print(f"Error: {init_result.get('error', 'Unknown error')}")
    
    # Run the basic test
    asyncio.run(run_basic_tests())
    
    print("\nTo run full test suite:")
    print("pytest backend/apps/admin_tools/ai_workspace/tests/test_workspace.py -v")
