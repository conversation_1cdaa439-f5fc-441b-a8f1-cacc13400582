#!/usr/bin/env python3
"""
Test Media class rendering for catalog management.

Usage:
    docker exec -it backend-web-1 python /usr/src/app/apps/admin_tools/ai_workspace/test_media_class.py
"""

import os
import sys

# Add the project root to Python path
sys.path.insert(0, '/usr/src/app')

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')

import django
django.setup()

from apps.admin_tools.media import CatalogManagementMedia


def test_media_class():
    """Test the Media class rendering."""
    print("🧪 Testing CatalogManagementMedia class...")
    
    try:
        media = CatalogManagementMedia()
        
        print("\n📄 CSS Files:")
        css_output = media.media.render_css()
        print("CSS output type:", type(css_output))
        if hasattr(css_output, '__iter__'):
            css_list = list(css_output)
            print("CSS list:", css_list)
            css_output = '\n'.join(css_list)
        print(css_output)
        
        print("\n📜 JavaScript Files:")
        js_output = media.media.render_js()
        print(js_output)
        
        print("\n🔍 Analysis:")
        if 'catalog_management.css' in css_output:
            print("✅ catalog_management.css found in CSS output")
        else:
            print("❌ catalog_management.css NOT found in CSS output")
            
        if 'catalog_viewer.css' in css_output:
            print("✅ catalog_viewer.css found in CSS output")
        else:
            print("❌ catalog_viewer.css NOT found in CSS output")
            
        if 'catalog_viewer.js' in js_output:
            print("✅ catalog_viewer.js found in JS output")
        else:
            print("❌ catalog_viewer.js NOT found in JS output")
            
        if 'catalog_management.js' in js_output:
            print("✅ catalog_management.js found in JS output")
        else:
            print("❌ catalog_management.js NOT found in JS output")
            
    except Exception as e:
        print(f"❌ Error testing Media class: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    test_media_class()
