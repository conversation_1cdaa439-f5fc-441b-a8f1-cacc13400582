/**
 * Playwright test runner for frontend integration tests
 */

const { chromium } = require('playwright');
const { FrontendIntegrationTest, TEST_CONFIG } = require('./test_frontend_integration.js');

async function runFrontendTests() {
    console.log('🎭 Starting Playwright Frontend Integration Tests');
    console.log('=' * 60);
    
    const browser = await chromium.launch({ 
        headless: false,  // Set to true for headless mode
        slowMo: 500       // Slow down for better visibility
    });
    
    const context = await browser.newContext();
    const page = await context.newPage();
    
    try {
        // Navigate to the admin login page first
        console.log('🔐 Logging in to admin interface...');
        await page.goto(`${TEST_CONFIG.baseUrl}/admin/login/`);
        
        // Login as admin
        await page.fill('#id_username', TEST_CONFIG.adminCredentials.username);
        await page.fill('#id_password', TEST_CONFIG.adminCredentials.password);
        await page.click('input[type="submit"]');
        
        // Wait for login to complete
        await page.waitForURL('**/admin/', { timeout: 10000 });
        console.log('✅ Successfully logged in');
        
        // Navigate to user profile management page
        console.log('🔄 Navigating to user profile management...');
        await page.goto(`${TEST_CONFIG.baseUrl}/admin/user-profiles/`);
        
        // Wait for page to load
        await page.waitForLoadState('networkidle');
        console.log('✅ Page loaded successfully');
        
        // Check for JavaScript errors
        const jsErrors = [];
        page.on('pageerror', error => {
            jsErrors.push(error.message);
        });
        
        // Run the integration tests
        const tester = new FrontendIntegrationTest(page);
        const success = await tester.runAllTests();
        
        // Report JavaScript errors if any
        if (jsErrors.length > 0) {
            console.log('\n⚠️  JavaScript Errors Detected:');
            jsErrors.forEach(error => {
                console.log(`   - ${error}`);
            });
        }
        
        if (success && jsErrors.length === 0) {
            console.log('\n🎉 All frontend integration tests passed!');
            console.log('✅ Import functionality is working correctly');
            console.log('✅ JavaScript integration is successful');
            console.log('✅ Modal functionality is operational');
        } else {
            console.log('\n❌ Some tests failed or JavaScript errors were detected');
            console.log('🔧 Please check the output above for details');
        }
        
        return success && jsErrors.length === 0;
        
    } catch (error) {
        console.error('❌ Test execution failed:', error.message);
        return false;
    } finally {
        await browser.close();
    }
}

// Run the tests
if (require.main === module) {
    runFrontendTests()
        .then(success => {
            process.exit(success ? 0 : 1);
        })
        .catch(error => {
            console.error('Fatal error:', error);
            process.exit(1);
        });
}

module.exports = { runFrontendTests };
