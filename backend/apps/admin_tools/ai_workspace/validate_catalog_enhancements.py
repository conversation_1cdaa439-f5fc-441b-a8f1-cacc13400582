#!/usr/bin/env python3
"""
Quick validation script for enhanced catalog management features.

This script performs basic validation checks to ensure the enhanced catalog
management system is properly installed and configured.

Usage:
    docker exec -it backend-web-1 python /usr/src/app/apps/admin_tools/ai_workspace/validate_catalog_enhancements.py
"""

import os
import sys
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, '/usr/src/app')

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')

import django
django.setup()

from django.conf import settings


def check_file_exists(file_path, description):
    """Check if a file exists and report result."""
    full_path = Path(settings.BASE_DIR) / file_path
    if full_path.exists():
        print(f"✅ {description}: {file_path}")
        return True
    else:
        print(f"❌ {description} MISSING: {file_path}")
        return False


def check_import(module_path, description):
    """Check if a module can be imported."""
    try:
        __import__(module_path)
        print(f"✅ {description}: {module_path}")
        return True
    except ImportError as e:
        print(f"❌ {description} IMPORT ERROR: {module_path} - {e}")
        return False


def validate_enhancements():
    """Validate all enhanced catalog management features."""
    print("🔍 VALIDATING ENHANCED CATALOG MANAGEMENT SYSTEM")
    print("=" * 60)
    
    checks_passed = 0
    total_checks = 0
    
    # 1. Check static files
    print("\n📁 Checking Static Files...")
    static_files = [
        ('static/admin_tools/css/pages/catalog_management.css', 'Catalog Management CSS'),
        ('static/admin_tools/js/pages/catalog_management.js', 'Catalog Management JS'),
        ('static/admin_tools/css/components/catalog_viewer.css', 'Catalog Viewer CSS'),
        ('static/admin_tools/js/modules/catalog_viewer.js', 'Catalog Viewer JS'),
    ]
    
    for file_path, description in static_files:
        if check_file_exists(file_path, description):
            checks_passed += 1
        total_checks += 1
    
    # 2. Check Python modules
    print("\n🐍 Checking Python Modules...")
    modules = [
        ('apps.admin_tools.media', 'Media Classes'),
        ('apps.admin_tools.views.command_management', 'Enhanced Command Management Views'),
        ('apps.main.services.catalog_validation_service', 'Catalog Validation Service'),
    ]
    
    for module_path, description in modules:
        if check_import(module_path, description):
            checks_passed += 1
        total_checks += 1
    
    # 3. Check enhanced command files
    print("\n⚙️ Checking Enhanced Commands...")
    command_files = [
        ('apps/main/management/commands/seed_db_45_resources.py', 'Enhanced Resources Seeding'),
        ('apps/main/management/commands/generate_codes_catalog.py', 'Enhanced Catalog Generator'),
    ]
    
    for file_path, description in command_files:
        if check_file_exists(file_path, description):
            checks_passed += 1
        total_checks += 1
    
    # 4. Check template files
    print("\n📄 Checking Template Files...")
    template_files = [
        ('templates/admin_tools/command_management.html', 'Enhanced Command Management Template'),
    ]
    
    for file_path, description in template_files:
        if check_file_exists(file_path, description):
            checks_passed += 1
        total_checks += 1
    
    # 5. Check documentation
    print("\n📚 Checking Documentation...")
    doc_files = [
        ('docs/backend/CATALOG_ARCHITECTURE.md', 'Enhanced Catalog Architecture Documentation'),
        ('backend/apps/admin_tools/ai_workspace/AI-ENTRYPOINT.md', 'Updated AI Entrypoint Documentation'),
    ]
    
    for file_path, description in doc_files:
        if check_file_exists(file_path, description):
            checks_passed += 1
        total_checks += 1
    
    # 6. Test basic functionality
    print("\n🧪 Testing Basic Functionality...")
    
    # Test media class
    try:
        from apps.admin_tools.media import CatalogManagementMedia, get_page_media
        media_instance = CatalogManagementMedia()
        page_media = get_page_media('catalog_management')
        
        if hasattr(media_instance, 'media') and page_media:
            print("✅ Media Classes Working")
            checks_passed += 1
        else:
            print("❌ Media Classes NOT WORKING")
    except Exception as e:
        print(f"❌ Media Classes ERROR: {e}")
    total_checks += 1
    
    # Test enhanced command parameters
    try:
        from django.core.management import get_commands
        commands = get_commands()
        
        if 'seed_db_45_resources' in commands:
            print("✅ Enhanced Seeding Commands Available")
            checks_passed += 1
        else:
            print("❌ Enhanced Seeding Commands NOT AVAILABLE")
    except Exception as e:
        print(f"❌ Enhanced Commands ERROR: {e}")
    total_checks += 1
    
    # Generate final report
    print("\n" + "=" * 60)
    print("📊 VALIDATION REPORT")
    print("=" * 60)
    
    success_rate = (checks_passed / total_checks * 100) if total_checks > 0 else 0
    
    print(f"✅ Passed: {checks_passed}/{total_checks}")
    print(f"📈 Success Rate: {success_rate:.1f}%")
    
    if success_rate >= 90:
        print("\n🎉 EXCELLENT! Enhanced catalog management system is properly installed.")
        print("   Ready to use the new features:")
        print("   • Navigate to /admin/commands/ for the enhanced interface")
        print("   • Use bypass and external JSON features in seeding commands")
        print("   • Generate schemas with --update-schemas flag")
    elif success_rate >= 75:
        print("\n⚠️  GOOD! Most features are working. Address missing components.")
    elif success_rate >= 50:
        print("\n🔧 PARTIAL! Some features are missing. Check installation.")
    else:
        print("\n🚨 CRITICAL! Major components are missing. Reinstallation needed.")
    
    print("\n🚀 NEXT STEPS:")
    print("   1. Run comprehensive tests:")
    print("      docker exec -it backend-web-1 python /usr/src/app/apps/admin_tools/ai_workspace/test_enhanced_catalog_management.py")
    print("   2. Access enhanced interface:")
    print("      http://localhost:8000/admin/commands/")
    print("   3. Test seeding with external JSON:")
    print("      docker exec -it backend-web-1 python manage.py seed_db_45_resources --external-json /path/to/file.json")
    
    return success_rate >= 75


if __name__ == '__main__':
    validate_enhancements()
