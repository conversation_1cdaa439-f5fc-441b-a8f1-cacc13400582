"""
AI Workspace for Goali Admin Tools

This package provides AI-intuitive tools for AI agents to work on improving
tools and admin interfaces in the Goali system.

Core capabilities:
- Agent characteristics management (instructions, LLM config, etc.)
- Benchmark results access and analysis
- Admin page quality validation using Playwright
- Intelligent tool selection from existing benchmark-oriented tools
"""

__version__ = "1.0.0"
__author__ = "Goali AI Workspace"

# Import core modules for easy access
from .tools.agent_manager import AgentManager
from .tools.benchmark_analyzer import BenchmarkAnalyzer
from .tools.admin_validator import AdminValidator
from .tools.knowledge_manager import KnowledgeManager
from .tools.agent_debugging_system import AgentDebuggingSystem
from .workspace import AIWorkspace

__all__ = [
    'AgentManager',
    'BenchmarkAnalyzer',
    'AdminValidator',
    'KnowledgeManager',
    'AgentDebuggingSystem',
    'AIWorkspace'
]
