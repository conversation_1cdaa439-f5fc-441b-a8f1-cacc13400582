#!/usr/bin/env python3
"""
Direct ADHD User Experience Test

This script directly tests the current system behavior to identify the issues:
1. Immediate wheel generation instead of mood assessment
2. Duplicated wheel items
"""

import asyncio
import json
import logging
from datetime import datetime

# Setup Django
import os
import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from apps.main.services.conversation_dispatcher import ConversationDispatcher
from apps.user.models import UserProfile
from asgiref.sync import sync_to_async

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_adhd_first_time_user():
    """Test how the system handles a first-time ADHD user wellness request."""
    
    print("🧪 DIRECT ADHD USER EXPERIENCE TEST")
    print("=" * 50)
    
    # Find a test user profile (preferably ADHD-related)
    try:
        # Look for PhiPhi or any test user
        user_profile_qs = UserProfile.objects.filter(is_real=False)
        user_profile = await sync_to_async(user_profile_qs.first)()
        if not user_profile:
            print("❌ No test user profiles found")
            return
        
        print(f"👤 Using test user: {user_profile.profile_name}")
        print(f"🔢 User ID: {user_profile.id}")
        # Skip trust level to avoid async issues
        print(f"🤝 Trust Level: [Will be checked in workflow]")
        
        # Initialize ConversationDispatcher with user profile ID
        dispatcher = ConversationDispatcher(
            user_profile_id=str(user_profile.id),
            fail_fast_on_errors=True  # For testing
        )

        # Test message that should trigger discussion, not immediate wheel generation
        test_message = "I want to improve my wellness and productivity"

        print(f"\n📝 Test Message: '{test_message}'")
        print("🎯 Expected: Should ask about mood, NOT generate wheel immediately")

        # Process the message
        print("\n🔄 Processing message...")
        result = await dispatcher.process_message({
            'text': test_message,
            'timestamp': datetime.now().isoformat(),
            'metadata': {}
        })
        
        print("\n📊 RESULTS:")
        print("-" * 30)
        
        # Analyze the result
        if isinstance(result, dict):
            print(f"✅ Result type: {type(result)}")
            print(f"📋 Keys: {list(result.keys())}")
            
            # Check for immediate wheel generation
            has_wheel = False
            user_response = ""
            
            if 'user_response' in result:
                user_response = result['user_response']
                print(f"💬 User Response Length: {len(user_response)} characters")
                
                # Check if response asks about mood
                mood_keywords = ['mood', 'feeling', 'energy', 'how are you', 'emotional']
                asks_about_mood = any(keyword in user_response.lower() for keyword in mood_keywords)
                print(f"❓ Asks about mood: {'✅ YES' if asks_about_mood else '❌ NO'}")
                
                # Print first 200 characters of response
                print(f"📝 Response preview: {user_response[:200]}...")
            
            if 'wheel' in result:
                has_wheel = True
                wheel = result['wheel']
                print(f"🎡 Wheel generated: ❌ YES (This is the problem!)")
                
                if isinstance(wheel, dict) and 'items' in wheel:
                    items = wheel['items']
                    print(f"🎯 Activity count: {len(items)}")
                    
                    # Check for duplicate IDs
                    ids = [item.get('id') for item in items if 'id' in item]
                    unique_ids = len(set(ids))
                    print(f"🆔 Unique IDs: {unique_ids}/{len(ids)}")
                    
                    if unique_ids < len(ids):
                        print("❌ DUPLICATE IDs FOUND!")
                        id_counts = {}
                        for id_val in ids:
                            id_counts[id_val] = id_counts.get(id_val, 0) + 1
                        duplicates = [id_val for id_val, count in id_counts.items() if count > 1]
                        print(f"🔄 Duplicate IDs: {duplicates}")
                    else:
                        print("✅ All IDs are unique")
            else:
                print(f"🎡 Wheel generated: ✅ NO (This is correct for first-time users)")
            
            # Check workflow type
            if 'workflow_type' in result:
                workflow_type = result['workflow_type']
                print(f"🔄 Workflow type: {workflow_type}")
                if workflow_type == 'wheel_generation':
                    print("❌ ISSUE: Should be 'discussion' for first-time users")
                elif workflow_type == 'discussion':
                    print("✅ CORRECT: Discussion workflow for first-time users")
        
        else:
            print(f"❌ Unexpected result type: {type(result)}")
            print(f"📄 Result: {result}")
        
        print("\n🎯 ISSUE SUMMARY:")
        print("-" * 30)
        
        issues_found = []
        
        # Issue 1: Immediate wheel generation
        if has_wheel:
            issues_found.append("❌ IMMEDIATE WHEEL GENERATION: System generated wheel instead of asking about mood first")
        
        # Issue 2: Check if mood was assessed
        if user_response and not any(keyword in user_response.lower() for keyword in ['mood', 'feeling', 'energy']):
            issues_found.append("❌ NO MOOD ASSESSMENT: System didn't ask about user's current mood/energy")
        
        if not issues_found:
            print("✅ No issues found - system behaved correctly!")
        else:
            for issue in issues_found:
                print(issue)
        
        return {
            'has_wheel': has_wheel,
            'asks_about_mood': 'mood' in user_response.lower() if user_response else False,
            'user_response': user_response,
            'issues_found': issues_found,
            'result': result
        }
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
        return {'error': str(e)}


async def main():
    """Main test function."""
    print(f"🕒 Test started at: {datetime.now().isoformat()}")
    
    result = await test_adhd_first_time_user()
    
    print(f"\n🕒 Test completed at: {datetime.now().isoformat()}")
    
    # Save results for analysis
    with open('/tmp/adhd_test_results.json', 'w') as f:
        json.dump(result, f, indent=2, default=str)
    
    print("💾 Results saved to /tmp/adhd_test_results.json")


if __name__ == '__main__':
    asyncio.run(main())
