#!/usr/bin/env python3
"""
Test script for LLM tailoring in wheel generation workflow.

This script triggers a wheel generation workflow and monitors the results
to verify that LLM tailoring is working correctly.
"""

import asyncio
import json
import logging
import sys
import time
from datetime import datetime

# Django setup
import os
import django
sys.path.append('/usr/src/app')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')
django.setup()

from apps.main.services.conversation_dispatcher import ConversationDispatcher
from apps.user.models import UserProfile
from asgiref.sync import sync_to_async

logger = logging.getLogger(__name__)

async def test_wheel_generation():
    """Test wheel generation with the new energetic prompt."""
    print("🎡 Testing Wheel Generation with LLM Tailoring")
    print("=" * 60)
    
    # Get a test user (using sync_to_async)
    get_user = sync_to_async(lambda: UserProfile.objects.filter(id=2).first())
    user_profile = await get_user()
    if not user_profile:
        print("❌ User profile 2 not found")
        return
    
    print(f"✅ Using user profile: {user_profile.id}")
    
    # Create conversation dispatcher
    dispatcher = ConversationDispatcher(
        user_profile_id=str(user_profile.id),
        fail_fast_on_errors=True
    )
    
    # Test message with energetic context (using process_message format)
    test_message = {
        "text": "hey, I'm feeling energetic and I have 2h free ahead ! give me a wheel !",
        "timestamp": datetime.now().isoformat(),
        "metadata": {
            "requested_workflow": "wheel_generation"
        }
    }

    print(f"📤 Sending message: {test_message['text']}")
    print("⏳ Processing workflow...")

    start_time = time.time()

    try:
        # Process the message
        result = await dispatcher.process_message(test_message)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"✅ Workflow completed in {duration:.2f} seconds")
        print(f"📊 Result type: {type(result)}")
        
        if isinstance(result, dict):
            print(f"📋 Result keys: {list(result.keys())}")
            
            # Check for wheel data
            if 'wheel' in result:
                wheel_data = result['wheel']
                print(f"🎡 Wheel data found!")
                print(f"   Items count: {len(wheel_data.get('items', []))}")
                print(f"   Activities count: {len(wheel_data.get('activities', []))}")
                
                # Check first few activities for LLM tailoring
                activities = wheel_data.get('activities', [])
                if activities:
                    print("\n🔍 Activity Analysis:")
                    for i, activity in enumerate(activities[:3]):  # Check first 3
                        print(f"   Activity {i+1}:")
                        print(f"     Title: {activity.get('title', 'N/A')}")
                        print(f"     Duration: {activity.get('duration_minutes', 'N/A')} minutes")
                        print(f"     ID: {activity.get('id', 'N/A')}")
                        
                        # Check if it looks like LLM-tailored (unique ID, personalized content)
                        activity_id = activity.get('id', '')
                        if 'llm_tailored' in str(activity_id):
                            print(f"     ✅ LLM-tailored activity detected!")
                        else:
                            print(f"     ❌ Generic activity (not LLM-tailored)")
            else:
                print("❌ No wheel data in result")
        
        return result
        
    except Exception as e:
        print(f"❌ Error during workflow execution: {e}")
        import traceback
        traceback.print_exc()
        return None

async def main():
    """Main test function."""
    print("🚀 Starting LLM Tailoring Test")
    print(f"📅 Test started at: {datetime.now().isoformat()}")
    
    result = await test_wheel_generation()
    
    if result:
        print("\n✅ Test completed successfully!")
    else:
        print("\n❌ Test failed!")
    
    print(f"📅 Test ended at: {datetime.now().isoformat()}")

if __name__ == "__main__":
    asyncio.run(main())
