#!/usr/bin/env python3
"""
Enhanced Observability Tools for Goali System Maintenance

This module provides comprehensive monitoring and debugging tools for the Goali system,
including WebSocket monitoring, workflow tracking, and system health checks.

Usage:
    python observability_tools.py --monitor-websockets
    python observability_tools.py --check-system-health
    python observability_tools.py --track-workflow <workflow_id>
    python observability_tools.py --analyze-celery-logs
"""

import asyncio
import json
import logging
import sys
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import argparse
import subprocess
import re

# Django setup
import os
import django
sys.path.append('/usr/src/app')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')
django.setup()

from django.core.management.base import BaseCommand
from apps.main.models import BenchmarkRun, LLMConfig
from apps.user.models import UserProfile
from apps.main.services.database_service import RealDatabaseService
import redis

logger = logging.getLogger(__name__)

class SystemObservabilityTool:
    """Comprehensive system observability and debugging tool."""
    
    def __init__(self):
        self.redis_client = redis.Redis(host='redis', port=6379, db=0)
        self.db_service = RealDatabaseService()
        
    def check_system_health(self) -> Dict[str, Any]:
        """Perform comprehensive system health check."""
        health_report = {
            "timestamp": datetime.now().isoformat(),
            "components": {},
            "overall_status": "healthy"
        }
        
        # Check database connectivity
        try:
            user_count = UserProfile.objects.count()
            health_report["components"]["database"] = {
                "status": "healthy",
                "user_count": user_count,
                "connection": "active"
            }
        except Exception as e:
            health_report["components"]["database"] = {
                "status": "unhealthy",
                "error": str(e)
            }
            health_report["overall_status"] = "degraded"
        
        # Check Redis connectivity
        try:
            redis_info = self.redis_client.info()
            health_report["components"]["redis"] = {
                "status": "healthy",
                "connected_clients": redis_info.get("connected_clients", 0),
                "memory_usage": redis_info.get("used_memory_human", "unknown")
            }
        except Exception as e:
            health_report["components"]["redis"] = {
                "status": "unhealthy",
                "error": str(e)
            }
            health_report["overall_status"] = "degraded"
        
        # Check LLM configuration
        try:
            llm_configs = LLMConfig.objects.all()
            health_report["components"]["llm"] = {
                "status": "healthy",
                "total_configs": llm_configs.count(),
                "models": [config.model_name for config in llm_configs]
            }
        except Exception as e:
            health_report["components"]["llm"] = {
                "status": "unhealthy",
                "error": str(e)
            }
            health_report["overall_status"] = "degraded"
        
        # Check recent workflow activity
        try:
            recent_workflows = BenchmarkRun.objects.filter(
                execution_date__gte=datetime.now() - timedelta(hours=1)
            ).count()
            health_report["components"]["workflows"] = {
                "status": "healthy",
                "recent_runs": recent_workflows
            }
        except Exception as e:
            health_report["components"]["workflows"] = {
                "status": "unhealthy",
                "error": str(e)
            }
            health_report["overall_status"] = "degraded"
        
        return health_report
    
    def analyze_celery_logs(self, lines: int = 100) -> Dict[str, Any]:
        """Analyze recent Celery logs for issues and patterns."""
        try:
            # Get Celery logs
            result = subprocess.run(
                ["docker", "logs", "backend-celery-1", "--tail", str(lines)],
                capture_output=True, text=True, timeout=30
            )
            
            if result.returncode != 0:
                return {"error": f"Failed to get logs: {result.stderr}"}
            
            logs = result.stdout
            analysis = {
                "timestamp": datetime.now().isoformat(),
                "total_lines": len(logs.split('\n')),
                "patterns": {},
                "errors": [],
                "warnings": [],
                "workflow_activity": [],
                "llm_activity": []
            }
            
            # Analyze patterns
            for line in logs.split('\n'):
                if not line.strip():
                    continue
                    
                # Count error patterns
                if "ERROR" in line:
                    analysis["errors"].append(line.strip())
                elif "WARNING" in line:
                    analysis["warnings"].append(line.strip())
                elif "wheel_generation" in line:
                    analysis["workflow_activity"].append(line.strip())
                elif "LLM" in line or "chat_completion" in line:
                    analysis["llm_activity"].append(line.strip())
            
            # Pattern analysis
            analysis["patterns"] = {
                "error_count": len(analysis["errors"]),
                "warning_count": len(analysis["warnings"]),
                "workflow_runs": len(analysis["workflow_activity"]),
                "llm_calls": len(analysis["llm_activity"])
            }
            
            return analysis
            
        except Exception as e:
            return {"error": f"Failed to analyze logs: {str(e)}"}
    
    def track_workflow_performance(self, hours: int = 24) -> Dict[str, Any]:
        """Track workflow performance over specified time period."""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            workflows = BenchmarkRun.objects.filter(execution_date__gte=cutoff_time)
            
            performance_data = {
                "timestamp": datetime.now().isoformat(),
                "time_period_hours": hours,
                "total_workflows": workflows.count(),
                "by_type": {},
                "by_status": {},
                "average_duration": None,
                "recent_failures": []
            }
            
            # Group by workflow type (using scenario type as proxy)
            for workflow in workflows:
                workflow_type = workflow.scenario.scenario_type if workflow.scenario else "unknown"
                if workflow_type not in performance_data["by_type"]:
                    performance_data["by_type"][workflow_type] = 0
                performance_data["by_type"][workflow_type] += 1

                # Group by status (benchmark runs are always completed)
                status = "completed"
                if status not in performance_data["by_status"]:
                    performance_data["by_status"][status] = 0
                performance_data["by_status"][status] += 1
            
            return performance_data
            
        except Exception as e:
            return {"error": f"Failed to track performance: {str(e)}"}
    
    def generate_system_report(self) -> str:
        """Generate comprehensive system status report."""
        print("🔍 Generating Comprehensive System Report...")
        print("=" * 60)
        
        # System health
        health = self.check_system_health()
        print(f"\n📊 SYSTEM HEALTH - {health['overall_status'].upper()}")
        print("-" * 30)
        for component, status in health["components"].items():
            status_icon = "✅" if status["status"] == "healthy" else "❌"
            print(f"{status_icon} {component.title()}: {status['status']}")
            if "error" in status:
                print(f"   Error: {status['error']}")
        
        # Workflow performance
        performance = self.track_workflow_performance(24)
        print(f"\n🔄 WORKFLOW PERFORMANCE (24h)")
        print("-" * 30)
        if "error" not in performance:
            print(f"Total workflows: {performance['total_workflows']}")
            print(f"By type: {performance['by_type']}")
            print(f"By status: {performance['by_status']}")
            if performance['recent_failures']:
                print(f"Recent failures: {len(performance['recent_failures'])}")
        
        # Celery log analysis
        log_analysis = self.analyze_celery_logs(200)
        print(f"\n📋 CELERY LOG ANALYSIS")
        print("-" * 30)
        if "error" not in log_analysis:
            patterns = log_analysis["patterns"]
            print(f"Errors: {patterns['error_count']}")
            print(f"Warnings: {patterns['warning_count']}")
            print(f"Workflow activity: {patterns['workflow_runs']}")
            print(f"LLM activity: {patterns['llm_calls']}")
            
            if log_analysis["errors"]:
                print("\nRecent errors:")
                for error in log_analysis["errors"][-3:]:  # Show last 3 errors
                    print(f"  • {error}")
        
        print(f"\n📅 Report generated at: {datetime.now().isoformat()}")
        print("=" * 60)
        
        return "Report generated successfully"

def main():
    """Main CLI interface for observability tools."""
    parser = argparse.ArgumentParser(description="Goali System Observability Tools")
    parser.add_argument("--system-report", action="store_true", 
                       help="Generate comprehensive system report")
    parser.add_argument("--health-check", action="store_true",
                       help="Perform system health check")
    parser.add_argument("--analyze-logs", type=int, default=100,
                       help="Analyze Celery logs (specify number of lines)")
    parser.add_argument("--track-workflows", type=int, default=24,
                       help="Track workflow performance (specify hours)")
    
    args = parser.parse_args()
    
    tool = SystemObservabilityTool()
    
    if args.system_report:
        tool.generate_system_report()
    elif args.health_check:
        health = tool.check_system_health()
        print(json.dumps(health, indent=2))
    elif args.analyze_logs:
        analysis = tool.analyze_celery_logs(args.analyze_logs)
        print(json.dumps(analysis, indent=2))
    elif args.track_workflows:
        performance = tool.track_workflow_performance(args.track_workflows)
        print(json.dumps(performance, indent=2))
    else:
        # Default: generate system report
        tool.generate_system_report()

if __name__ == "__main__":
    main()
