#!/usr/bin/env python3
"""
Complete end-to-end testing of catalog management using MCP browser tools.
This script tests the entire flow from page loading to command execution and database verification.

Usage:
    docker exec -it backend-web-1 python /usr/src/app/apps/admin_tools/ai_workspace/test_catalog_management_complete.py
"""

import os
import sys
import time
import asyncio
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, '/usr/src/app')

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')

import django
django.setup()

from django.contrib.auth.models import User
from apps.main.models import BenchmarkRun


class CatalogManagementTester:
    """Complete end-to-end testing of catalog management functionality."""
    
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.admin_username = "admin"
        self.admin_password = "admin123"
        self.test_results = []
        self.errors_found = []
    
    def log_test(self, test_name, success, message=""):
        """Log test result."""
        status = "✅ PASS" if success else "❌ FAIL"
        self.test_results.append({
            'name': test_name,
            'success': success,
            'message': message
        })
        print(f"{status}: {test_name}")
        if message:
            print(f"    {message}")
        if not success:
            self.errors_found.append(f"{test_name}: {message}")
    
    def test_database_state_before(self):
        """Test database state before operations."""
        print("\n🔍 Testing database state BEFORE operations...")
        
        try:
            # Count existing benchmark runs (as a test of database connectivity)
            benchmark_count = BenchmarkRun.objects.count()
            self.log_test(
                "Database accessibility",
                True,
                f"Found {benchmark_count} existing benchmark runs"
            )

            # Store initial count for comparison
            self.initial_benchmark_count = benchmark_count
            
            return True
            
        except Exception as e:
            self.log_test("Database accessibility", False, str(e))
            return False
    
    def test_admin_user_exists(self):
        """Test if admin user exists and can be used for login."""
        print("\n👤 Testing admin user availability...")
        
        try:
            admin_user = User.objects.get(username=self.admin_username)
            self.log_test(
                "Admin user exists", 
                True, 
                f"Admin user found: {admin_user.username} (ID: {admin_user.id})"
            )
            
            # Test if user is staff
            if admin_user.is_staff:
                self.log_test("Admin user has staff privileges", True)
            else:
                self.log_test("Admin user has staff privileges", False, "User is not staff")
                
            return admin_user.is_staff
            
        except User.DoesNotExist:
            self.log_test("Admin user exists", False, "Admin user not found")
            return False
        except Exception as e:
            self.log_test("Admin user exists", False, str(e))
            return False
    
    def test_static_files_availability(self):
        """Test if required static files are available."""
        print("\n📁 Testing static files availability...")
        
        from django.contrib.staticfiles.finders import find
        
        required_files = [
            'admin_tools/js/modules/catalog_viewer.js',
            'admin_tools/js/pages/catalog_management.js',
            'admin_tools/css/pages/catalog_management.css',
            'admin_tools/css/components/catalog_viewer.css',
        ]
        
        all_found = True
        for file_path in required_files:
            found_path = find(file_path)
            if found_path:
                self.log_test(f"Static file: {file_path}", True, f"Found at {found_path}")
            else:
                self.log_test(f"Static file: {file_path}", False, "File not found")
                all_found = False
        
        return all_found
    
    def test_media_class_configuration(self):
        """Test Media class configuration."""
        print("\n⚙️ Testing Media class configuration...")
        
        try:
            from apps.admin_tools.media import CatalogManagementMedia
            media_instance = CatalogManagementMedia()
            
            # Test CSS rendering
            css_output = list(media_instance.media.render_css())
            css_found = any('catalog_management.css' in css for css in css_output)
            self.log_test("CSS files in Media class", css_found, f"Found {len(css_output)} CSS files")
            
            # Test JS rendering
            js_output = media_instance.media.render_js()
            js_found = any('catalog_viewer.js' in js for js in js_output)
            self.log_test("JS files in Media class", js_found, f"Found {len(js_output)} JS files")
            
            return css_found and js_found
            
        except Exception as e:
            self.log_test("Media class configuration", False, str(e))
            return False
    
    def test_view_functionality(self):
        """Test the view functionality without browser."""
        print("\n🎯 Testing view functionality...")
        
        try:
            from apps.admin_tools.views.command_management import command_management_page
            from django.test import RequestFactory
            from django.contrib.auth.models import AnonymousUser
            
            # Create a mock request
            factory = RequestFactory()
            request = factory.get('/admin/commands/')
            request.user = User.objects.get(username=self.admin_username)
            
            # Test the view
            response = command_management_page(request)
            
            if response.status_code == 200:
                self.log_test("View returns 200 status", True)
                
                # Check if response contains expected content
                content = response.content.decode('utf-8')
                if 'catalog-management-container' in content:
                    self.log_test("View contains catalog management container", True)
                else:
                    self.log_test("View contains catalog management container", False)
                
                if 'CatalogViewer' in content:
                    self.log_test("View includes CatalogViewer references", True)
                else:
                    self.log_test("View includes CatalogViewer references", False)
                
                return True
            else:
                self.log_test("View returns 200 status", False, f"Status: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("View functionality", False, str(e))
            return False
    
    def simulate_command_execution(self):
        """Simulate command execution to test database integration."""
        print("\n🚀 Simulating command execution...")
        
        try:
            # Test the seed_db_45_resources command logic
            from apps.main.management.commands.seed_db_45_resources import Command
            
            # Create command instance
            command = Command()
            
            # Test if we can access the command's handle method
            if hasattr(command, 'handle'):
                self.log_test("Command class accessible", True, "seed_db_45_resources command found")
                
                # Test loading JSON data (without actually executing)
                try:
                    # This tests the JSON loading mechanism
                    json_path = Path('/usr/src/app/backend/data/catalogs/resources.json')
                    if json_path.exists():
                        self.log_test("Resources JSON file exists", True, str(json_path))
                        
                        import json
                        with open(json_path, 'r') as f:
                            data = json.load(f)
                        
                        if 'resources' in data:
                            resource_count = len(data['resources'])
                            self.log_test(
                                "Resources JSON structure valid", 
                                True, 
                                f"Found {resource_count} resources in JSON"
                            )
                        else:
                            self.log_test("Resources JSON structure valid", False, "No 'resources' key found")
                    else:
                        self.log_test("Resources JSON file exists", False, "File not found")
                        
                except Exception as e:
                    self.log_test("JSON loading test", False, str(e))
                
                return True
            else:
                self.log_test("Command class accessible", False, "No handle method found")
                return False
                
        except Exception as e:
            self.log_test("Command execution simulation", False, str(e))
            return False
    
    def test_database_state_after(self):
        """Test database state after operations."""
        print("\n🔍 Testing database state AFTER operations...")
        
        try:
            # Count benchmark runs after operations
            final_benchmark_count = BenchmarkRun.objects.count()
            self.log_test(
                "Database state after operations",
                True,
                f"Final count: {final_benchmark_count} (was {self.initial_benchmark_count})"
            )

            # Test if we can query specific benchmark runs
            sample_benchmarks = BenchmarkRun.objects.all()[:5]
            if sample_benchmarks:
                self.log_test(
                    "Sample benchmark runs accessible",
                    True,
                    f"Retrieved {len(sample_benchmarks)} sample benchmark runs"
                )

                for benchmark in sample_benchmarks:
                    print(f"    - {benchmark.id} (Created: {benchmark.created_at})")
            else:
                self.log_test("Sample benchmark runs accessible", False, "No benchmark runs found")
            
            return True
            
        except Exception as e:
            self.log_test("Database state after operations", False, str(e))
            return False
    
    def generate_comprehensive_report(self):
        """Generate comprehensive test report."""
        print("\n" + "=" * 80)
        print("📊 COMPREHENSIVE CATALOG MANAGEMENT TEST REPORT")
        print("=" * 80)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        print(f"📈 Overall Results:")
        print(f"   ✅ Tests Passed: {passed_tests}")
        print(f"   ❌ Tests Failed: {failed_tests}")
        print(f"   📊 Success Rate: {success_rate:.1f}%")
        
        print(f"\n📋 Detailed Results:")
        for result in self.test_results:
            status = "✅" if result['success'] else "❌"
            print(f"   {status} {result['name']}")
            if result['message']:
                print(f"      {result['message']}")
        
        if self.errors_found:
            print(f"\n🔍 Errors Found:")
            for i, error in enumerate(self.errors_found, 1):
                print(f"   {i}. {error}")
        
        print(f"\n🎯 Recommendations:")
        if success_rate >= 90:
            print("   ✅ EXCELLENT: System is ready for browser testing")
            print("   ➡️  Next: Test with MCP browser tools")
        elif success_rate >= 70:
            print("   🔧 GOOD: Minor issues to fix before browser testing")
            print("   ➡️  Fix identified issues, then test with browser")
        else:
            print("   🚨 CRITICAL: Major issues found")
            print("   ➡️  Fix fundamental issues before proceeding")
        
        return success_rate >= 70
    
    def run_complete_test_suite(self):
        """Run the complete test suite."""
        print("🧪 STARTING COMPREHENSIVE CATALOG MANAGEMENT TESTING")
        print("=" * 80)
        
        try:
            # Run all tests in sequence
            self.test_database_state_before()
            self.test_admin_user_exists()
            self.test_static_files_availability()
            self.test_media_class_configuration()
            self.test_view_functionality()
            self.simulate_command_execution()
            self.test_database_state_after()
            
            # Generate comprehensive report
            ready_for_browser = self.generate_comprehensive_report()
            
            if ready_for_browser:
                print(f"\n🎉 SYSTEM READY FOR BROWSER TESTING!")
                print(f"   Use MCP browser tools to test: http://localhost:8000/admin/commands/")
                print(f"   Login: {self.admin_username} / {self.admin_password}")
            
            return ready_for_browser
            
        except Exception as e:
            print(f"❌ Critical error during testing: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """Main test execution."""
    tester = CatalogManagementTester()
    success = tester.run_complete_test_suite()
    
    if success:
        print(f"\n🚀 Ready to proceed with MCP browser testing!")
    else:
        print(f"\n⚠️  Fix issues before proceeding with browser testing.")
    
    return success


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
