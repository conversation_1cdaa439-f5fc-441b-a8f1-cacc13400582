#!/usr/bin/env python3
"""
Enhanced Catalog Management System Test Suite

This script comprehensively tests the new catalog management features including:
- Rich GUI interface functionality
- Enhanced seeding commands with bypass and external JSON
- Catalog visualization and validation
- Schema generation and validation
- Django static file organization

Usage:
    docker exec -it backend-web-1 python /usr/src/app/apps/admin_tools/ai_workspace/test_enhanced_catalog_management.py
"""

import os
import sys
import json
import tempfile
from pathlib import Path
from datetime import datetime

# Add the project root to Python path
sys.path.insert(0, '/usr/src/app')

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')

import django
django.setup()

from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.core.management import call_command
from django.conf import settings
from io import StringIO


class EnhancedCatalogManagementTest:
    """Comprehensive test suite for enhanced catalog management system."""
    
    def __init__(self):
        self.client = Client()
        self.admin_user = None
        self.test_results = {
            'passed': 0,
            'failed': 0,
            'errors': []
        }
        
    def setup(self):
        """Set up test environment."""
        print("🔧 Setting up test environment...")
        
        # Create admin user for testing
        try:
            self.admin_user = User.objects.get(username='admin')
        except User.DoesNotExist:
            self.admin_user = User.objects.create_superuser(
                username='admin',
                email='<EMAIL>',
                password='admin123'
            )
        
        # Login as admin
        self.client.force_login(self.admin_user)
        print("✅ Admin user created and logged in")
        
    def test_catalog_management_page_access(self):
        """Test access to enhanced catalog management page."""
        print("\n📋 Testing catalog management page access...")
        
        try:
            response = self.client.get('/admin/commands/')
            
            if response.status_code == 200:
                print("✅ Catalog management page accessible")
                
                # Check for enhanced features in response
                content = response.content.decode()
                
                checks = [
                    ('catalog-management-container', 'Enhanced container class'),
                    ('catalog-status-overview', 'Status overview section'),
                    ('commands-section', 'Commands section'),
                    ('execute-btn', 'Enhanced execute buttons'),
                    ('parameter-input', 'Parameter input fields'),
                    ('file-upload-area', 'File upload areas'),
                ]
                
                for check_class, description in checks:
                    if check_class in content:
                        print(f"✅ {description} found")
                        self.test_results['passed'] += 1
                    else:
                        print(f"❌ {description} missing")
                        self.test_results['failed'] += 1
                        self.test_results['errors'].append(f"Missing {description}")
                        
            else:
                print(f"❌ Page access failed with status {response.status_code}")
                self.test_results['failed'] += 1
                self.test_results['errors'].append(f"Page access failed: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error accessing page: {e}")
            self.test_results['failed'] += 1
            self.test_results['errors'].append(f"Page access error: {e}")
    
    def test_catalog_validation_endpoints(self):
        """Test catalog validation API endpoints."""
        print("\n🔍 Testing catalog validation endpoints...")
        
        endpoints = [
            ('/admin/commands/status/', 'Status endpoint'),
            ('/admin/commands/catalogs/', 'Catalogs list endpoint'),
        ]
        
        for endpoint, description in endpoints:
            try:
                response = self.client.get(endpoint)
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success'):
                        print(f"✅ {description} working")
                        self.test_results['passed'] += 1
                    else:
                        print(f"❌ {description} returned error: {data.get('error', 'Unknown')}")
                        self.test_results['failed'] += 1
                        self.test_results['errors'].append(f"{description} error: {data.get('error')}")
                else:
                    print(f"❌ {description} failed with status {response.status_code}")
                    self.test_results['failed'] += 1
                    self.test_results['errors'].append(f"{description} failed: {response.status_code}")
                    
            except Exception as e:
                print(f"❌ Error testing {description}: {e}")
                self.test_results['failed'] += 1
                self.test_results['errors'].append(f"{description} error: {e}")
    
    def test_enhanced_seeding_commands(self):
        """Test enhanced seeding commands with new parameters."""
        print("\n🌱 Testing enhanced seeding commands...")
        
        # Test bypass idempotent mechanism
        try:
            os.environ['SKIP_SEEDER_IDEMPOTENCY_CHECK'] = 'true'
            
            output = StringIO()
            call_command('seed_db_45_resources', stdout=output)
            
            output_content = output.getvalue()
            if 'Skipping AppliedSeedingCommand check' in output_content:
                print("✅ Bypass idempotent mechanism working")
                self.test_results['passed'] += 1
            else:
                print("❌ Bypass idempotent mechanism not working")
                self.test_results['failed'] += 1
                self.test_results['errors'].append("Bypass mechanism not working")
                
        except Exception as e:
            print(f"❌ Error testing bypass mechanism: {e}")
            self.test_results['failed'] += 1
            self.test_results['errors'].append(f"Bypass mechanism error: {e}")
        finally:
            os.environ.pop('SKIP_SEEDER_IDEMPOTENCY_CHECK', None)
    
    def test_external_json_support(self):
        """Test external JSON file support in seeding commands."""
        print("\n📁 Testing external JSON support...")
        
        # Create test external JSON file
        test_data = {
            "resources": {
                "test_category": [
                    {
                        "code": "TEST_RESOURCE_001",
                        "name": "Test Resource",
                        "description": "A test resource for validation",
                        "category": "test"
                    }
                ]
            }
        }
        
        try:
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                json.dump(test_data, f)
                temp_file_path = f.name
            
            # Test command with external JSON
            output = StringIO()
            call_command('seed_db_45_resources', external_json=temp_file_path, stdout=output)
            
            output_content = output.getvalue()
            if 'Merged' in output_content and 'additional resources' in output_content:
                print("✅ External JSON support working")
                self.test_results['passed'] += 1
            else:
                print("❌ External JSON support not working")
                self.test_results['failed'] += 1
                self.test_results['errors'].append("External JSON support not working")
                
        except Exception as e:
            print(f"❌ Error testing external JSON: {e}")
            self.test_results['failed'] += 1
            self.test_results['errors'].append(f"External JSON error: {e}")
        finally:
            # Clean up temp file
            try:
                os.unlink(temp_file_path)
            except:
                pass
    
    def test_schema_generation(self):
        """Test JSON schema generation functionality."""
        print("\n📋 Testing schema generation...")
        
        try:
            output = StringIO()
            call_command('generate_codes_catalog', update_schemas=True, stdout=output)
            
            output_content = output.getvalue()
            
            # Check if schemas were generated
            schemas_path = Path(settings.BASE_DIR) / 'data' / 'schemas'
            
            if schemas_path.exists():
                print("✅ Schemas directory created")
                self.test_results['passed'] += 1
                
                # Check for master schema file
                master_schema = schemas_path / 'catalog_schemas.json'
                if master_schema.exists():
                    print("✅ Master schema file created")
                    self.test_results['passed'] += 1
                    
                    # Validate schema content
                    with open(master_schema, 'r') as f:
                        schema_data = json.load(f)
                        
                    if 'schemas' in schema_data and len(schema_data['schemas']) > 0:
                        print(f"✅ Schema contains {len(schema_data['schemas'])} catalog schemas")
                        self.test_results['passed'] += 1
                    else:
                        print("❌ Schema file is empty or invalid")
                        self.test_results['failed'] += 1
                        self.test_results['errors'].append("Invalid schema content")
                else:
                    print("❌ Master schema file not created")
                    self.test_results['failed'] += 1
                    self.test_results['errors'].append("Master schema file missing")
            else:
                print("❌ Schemas directory not created")
                self.test_results['failed'] += 1
                self.test_results['errors'].append("Schemas directory missing")
                
        except Exception as e:
            print(f"❌ Error testing schema generation: {e}")
            self.test_results['failed'] += 1
            self.test_results['errors'].append(f"Schema generation error: {e}")
    
    def test_static_file_organization(self):
        """Test Django static file organization."""
        print("\n🎨 Testing static file organization...")
        
        static_files = [
            ('static/admin_tools/css/pages/catalog_management.css', 'Catalog management CSS'),
            ('static/admin_tools/js/pages/catalog_management.js', 'Catalog management JS'),
            ('static/admin_tools/css/components/catalog_viewer.css', 'Catalog viewer CSS'),
            ('static/admin_tools/js/modules/catalog_viewer.js', 'Catalog viewer JS'),
        ]
        
        for file_path, description in static_files:
            full_path = Path(settings.BASE_DIR) / file_path
            
            if full_path.exists():
                print(f"✅ {description} exists")
                self.test_results['passed'] += 1
            else:
                print(f"❌ {description} missing")
                self.test_results['failed'] += 1
                self.test_results['errors'].append(f"Missing {description}")
    
    def test_media_class_integration(self):
        """Test Django Media class integration."""
        print("\n📦 Testing Media class integration...")
        
        try:
            from apps.admin_tools.media import CatalogManagementMedia, get_page_media
            
            # Test media class instantiation
            media_instance = CatalogManagementMedia()
            if hasattr(media_instance, 'media'):
                print("✅ CatalogManagementMedia class working")
                self.test_results['passed'] += 1
                
                # Test CSS and JS files are defined
                media = media_instance.media
                if hasattr(media, 'css') and hasattr(media, 'js'):
                    print("✅ Media CSS and JS defined")
                    self.test_results['passed'] += 1
                else:
                    print("❌ Media CSS/JS not properly defined")
                    self.test_results['failed'] += 1
                    self.test_results['errors'].append("Media CSS/JS not defined")
            else:
                print("❌ CatalogManagementMedia class not working")
                self.test_results['failed'] += 1
                self.test_results['errors'].append("Media class not working")
                
            # Test utility function
            page_media = get_page_media('catalog_management')
            if page_media:
                print("✅ get_page_media utility working")
                self.test_results['passed'] += 1
            else:
                print("❌ get_page_media utility not working")
                self.test_results['failed'] += 1
                self.test_results['errors'].append("get_page_media not working")
                
        except Exception as e:
            print(f"❌ Error testing Media class: {e}")
            self.test_results['failed'] += 1
            self.test_results['errors'].append(f"Media class error: {e}")
    
    def run_all_tests(self):
        """Run all tests and generate report."""
        print("🚀 Starting Enhanced Catalog Management System Tests")
        print("=" * 60)
        
        self.setup()
        
        # Run all test methods
        test_methods = [
            self.test_catalog_management_page_access,
            self.test_catalog_validation_endpoints,
            self.test_enhanced_seeding_commands,
            self.test_external_json_support,
            self.test_schema_generation,
            self.test_static_file_organization,
            self.test_media_class_integration,
        ]
        
        for test_method in test_methods:
            try:
                test_method()
            except Exception as e:
                print(f"❌ Test method {test_method.__name__} failed: {e}")
                self.test_results['failed'] += 1
                self.test_results['errors'].append(f"{test_method.__name__}: {e}")
        
        # Generate final report
        self.generate_report()
    
    def generate_report(self):
        """Generate comprehensive test report."""
        print("\n" + "=" * 60)
        print("📊 ENHANCED CATALOG MANAGEMENT TEST REPORT")
        print("=" * 60)
        
        total_tests = self.test_results['passed'] + self.test_results['failed']
        success_rate = (self.test_results['passed'] / total_tests * 100) if total_tests > 0 else 0
        
        print(f"✅ Passed: {self.test_results['passed']}")
        print(f"❌ Failed: {self.test_results['failed']}")
        print(f"📈 Success Rate: {success_rate:.1f}%")
        
        if self.test_results['errors']:
            print("\n🔍 ERRORS FOUND:")
            for i, error in enumerate(self.test_results['errors'], 1):
                print(f"  {i}. {error}")
        
        print("\n🎯 RECOMMENDATIONS:")
        if success_rate >= 90:
            print("  ✅ System is working excellently! Ready for production use.")
        elif success_rate >= 75:
            print("  ⚠️  System is mostly working. Address minor issues before production.")
        elif success_rate >= 50:
            print("  🔧 System needs significant fixes before production use.")
        else:
            print("  🚨 System has major issues. Extensive debugging required.")
        
        print(f"\n📅 Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)


if __name__ == '__main__':
    test_suite = EnhancedCatalogManagementTest()
    test_suite.run_all_tests()
