/**
 * Comprehensive Test for Profile Management Fixes
 * Tests: 1) Completeness determinism, 2) Profile 47 API fix, 3) Modal z-index
 */

const { chromium } = require('playwright');

async function testProfileFixes() {
    console.log('🧪 Testing Profile Management Fixes...');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 500 
    });
    
    try {
        const context = await browser.newContext();
        const page = await context.newPage();
        
        // Test 1: Navigate to user profiles and check completeness consistency
        console.log('\n📍 Test 1: Completeness Consistency');
        await page.goto('http://localhost:8000/admin/user-profiles/');
        await page.waitForSelector('.profile-table', { timeout: 10000 });
        
        // Get completeness values for first few profiles
        const completenessValues1 = await page.$$eval('.completeness-text', elements => 
            elements.slice(0, 5).map(el => el.textContent)
        );
        console.log('First load completeness:', completenessValues1);
        
        // Reload page and check again
        await page.reload();
        await page.waitForSelector('.profile-table', { timeout: 10000 });
        
        const completenessValues2 = await page.$$eval('.completeness-text', elements => 
            elements.slice(0, 5).map(el => el.textContent)
        );
        console.log('Second load completeness:', completenessValues2);
        
        // Compare values
        const isConsistent = JSON.stringify(completenessValues1) === JSON.stringify(completenessValues2);
        console.log(isConsistent ? '✅ Completeness is consistent across reloads' : '❌ Completeness is inconsistent');
        
        // Test 2: Profile 47 API Fix
        console.log('\n📍 Test 2: Profile 47 API Fix');
        
        // Find Guillaume's profile (ID 47)
        const guillaumeRow = await page.locator('tr:has-text("Guillaume")').first();
        
        if (await guillaumeRow.count() === 0) {
            console.log('❌ Guillaume profile not found');
            return;
        }
        
        console.log('✅ Found Guillaume profile');
        
        // Set up network monitoring
        const apiErrors = [];
        page.on('response', response => {
            if (response.url().includes('/admin/user-profiles/api/') && response.status() >= 400) {
                apiErrors.push(`${response.status()}: ${response.url()}`);
            }
        });
        
        // Click view button
        const viewButton = guillaumeRow.locator('.view-profile-btn');
        await viewButton.click();
        
        // Wait for modal to appear
        try {
            await page.waitForSelector('#user-profile-detail-modal.show', { timeout: 5000 });
            console.log('✅ Profile modal opened successfully');
            
            // Check if modal content loaded without errors
            const modalContent = await page.locator('#user-profile-detail-modal .modal-body').textContent();
            if (modalContent.includes('Error') || modalContent.includes('error')) {
                console.log('❌ Modal shows error content');
            } else {
                console.log('✅ Modal content loaded successfully');
            }
            
        } catch (error) {
            console.log('❌ Modal failed to open:', error.message);
        }
        
        // Check for API errors
        if (apiErrors.length > 0) {
            console.log('❌ API Errors detected:');
            apiErrors.forEach(error => console.log(`  ${error}`));
        } else {
            console.log('✅ No API errors detected');
        }
        
        // Test 3: Modal Z-Index (check if modal is clickable)
        console.log('\n📍 Test 3: Modal Z-Index and Clickability');
        
        try {
            // Try to click on modal content to ensure it's not behind backdrop
            const modalHeader = page.locator('#user-profile-detail-modal .modal-header');
            await modalHeader.click();
            console.log('✅ Modal header is clickable (proper z-index)');
            
            // Check computed z-index
            const zIndex = await page.locator('#user-profile-detail-modal').evaluate(el => 
                window.getComputedStyle(el).zIndex
            );
            console.log(`Modal z-index: ${zIndex}`);
            
            if (parseInt(zIndex) >= 9999) {
                console.log('✅ Modal has proper z-index');
            } else {
                console.log('❌ Modal z-index may be too low');
            }
            
        } catch (error) {
            console.log('❌ Modal clickability test failed:', error.message);
        }
        
        // Test 4: Environment and Inventory Modal Buttons
        console.log('\n📍 Test 4: Dedicated Modal Buttons');
        
        try {
            // Look for environment modal button
            const envButton = page.locator('button:has-text("View Details")').first();
            if (await envButton.count() > 0) {
                console.log('✅ Environment detail button found');
                
                // Click it to test
                await envButton.click();
                await page.waitForTimeout(1000);
                
                // Check if environment modal appears
                const envModal = page.locator('#user-environment-detail-modal');
                if (await envModal.isVisible()) {
                    console.log('✅ Environment modal opens successfully');
                    
                    // Close it
                    await page.locator('#user-environment-detail-modal .btn-close').click();
                    await page.waitForTimeout(500);
                } else {
                    console.log('❌ Environment modal did not open');
                }
            }
            
            // Look for inventory modal button
            const invButton = page.locator('button:has-text("View Inventory")').first();
            if (await invButton.count() > 0) {
                console.log('✅ Inventory detail button found');
                
                // Click it to test
                await invButton.click();
                await page.waitForTimeout(1000);
                
                // Check if inventory modal appears
                const invModal = page.locator('#inventory-detail-modal');
                if (await invModal.isVisible()) {
                    console.log('✅ Inventory modal opens successfully');
                    
                    // Close it
                    await page.locator('#inventory-detail-modal .btn-close').click();
                    await page.waitForTimeout(500);
                } else {
                    console.log('❌ Inventory modal did not open');
                }
            }
            
        } catch (error) {
            console.log('❌ Dedicated modal test failed:', error.message);
        }
        
        // Final summary
        console.log('\n📋 Test Summary:');
        console.log('1. Completeness Consistency:', isConsistent ? '✅ PASS' : '❌ FAIL');
        console.log('2. Profile 47 API Fix:', apiErrors.length === 0 ? '✅ PASS' : '❌ FAIL');
        console.log('3. Modal Z-Index: ✅ PASS (if no errors above)');
        console.log('4. Dedicated Modals: ✅ PASS (if no errors above)');
        
        console.log('\n⏸️ Test complete. Browser will stay open for manual inspection.');
        console.log('Press Ctrl+C to close when done.');
        
        // Keep browser open for inspection
        await new Promise(() => {});
        
    } catch (error) {
        console.error('❌ Test suite failed:', error);
    }
}

// Run the test
testProfileFixes().catch(console.error);
