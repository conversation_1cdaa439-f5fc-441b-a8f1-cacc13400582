#!/usr/bin/env python3
"""
Test "I need some activity suggestions" Fix

This script specifically tests the failing scenario to verify the fix.
"""

import asyncio
import json
import logging
from datetime import datetime

# Setup Django
import os
import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from apps.main.services.conversation_dispatcher import ConversationDispatcher
from apps.user.models import UserProfile
from asgiref.sync import sync_to_async

logging.basicConfig(level=logging.WARNING)  # Reduce noise
logger = logging.getLogger(__name__)


async def test_suggestions_fix():
    """Test the specific failing scenario."""
    
    print("🎯 TESTING: 'I need some activity suggestions' Fix")
    print("=" * 50)
    
    # Get a test user profile
    user_profiles_qs = UserProfile.objects.filter(is_real=False)
    user_profile = await sync_to_async(user_profiles_qs.first)()
    
    if not user_profile:
        print("❌ No test user profiles found")
        return
    
    print(f"👤 Using test user: {user_profile.profile_name} (ID: {user_profile.id})")
    
    try:
        # Initialize ConversationDispatcher
        dispatcher = ConversationDispatcher(
            user_profile_id=str(user_profile.id),
            fail_fast_on_errors=True
        )
        
        # Test the specific message that was failing
        test_message = "I need some activity suggestions"
        
        print(f"📝 Test Message: '{test_message}'")
        print("🎯 Expected: Should route to 'discussion' for low trust user")
        print("❌ Previous: Was routing to 'wheel_generation'")
        
        # Process the message
        print("\n🔄 Processing message...")
        result = await dispatcher.process_message({
            'text': test_message,
            'timestamp': datetime.now().isoformat(),
            'metadata': {}
        })
        
        print(f"\n📊 RESULTS:")
        print(f"  🔄 Workflow: {result.get('workflow_type', 'unknown')}")
        print(f"  📊 Confidence: {result.get('confidence', 0.0):.2f}")
        
        # Check if the fix worked
        workflow_type = result.get('workflow_type')
        if workflow_type == 'discussion':
            print(f"  ✅ SUCCESS: Correctly routed to discussion workflow!")
            print(f"  🎯 This means low trust users will get mood assessment before activities")
        elif workflow_type == 'wheel_generation':
            print(f"  ❌ ISSUE: Still routing to wheel_generation for low trust user")
            print(f"  🔧 Need to investigate why the fix didn't work")
        else:
            print(f"  ❓ UNEXPECTED: Got {workflow_type} workflow")
        
        # Extract trust level from context if available
        context_packet = result.get('context_packet', {})
        mentor_context = context_packet.get('mentor_context', {})
        trust_level = mentor_context.get('trust_level', 'unknown')
        
        print(f"  🤝 Trust Level: {trust_level}")
        
        if isinstance(trust_level, float):
            trust_percentage = trust_level * 100
            print(f"  📊 Trust Percentage: {trust_percentage:.0f}%")
            
            if trust_percentage < 80:
                if workflow_type == 'discussion':
                    print(f"  ✅ CORRECT: Low trust ({trust_percentage:.0f}%) → discussion workflow")
                else:
                    print(f"  ❌ INCORRECT: Low trust ({trust_percentage:.0f}%) → {workflow_type} workflow")
        
        return {
            'test_message': test_message,
            'workflow_type': workflow_type,
            'confidence': result.get('confidence', 0.0),
            'trust_level': trust_level,
            'success': workflow_type == 'discussion',
            'result': result
        }
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
        return {'error': str(e)}


async def main():
    """Main test function."""
    print(f"🕒 Test started at: {datetime.now().isoformat()}")
    
    result = await test_suggestions_fix()
    
    print(f"\n🕒 Test completed at: {datetime.now().isoformat()}")
    
    # Save results for analysis
    with open('/tmp/suggestions_fix_test_results.json', 'w') as f:
        json.dump(result, f, indent=2, default=str)
    
    print("💾 Results saved to /tmp/suggestions_fix_test_results.json")
    
    # Summary
    if result.get('success'):
        print("\n🎉 TEST PASSED: Fix is working correctly!")
    elif 'error' in result:
        print(f"\n❌ TEST ERROR: {result['error']}")
    else:
        print("\n❌ TEST FAILED: Fix needs more work")


if __name__ == '__main__':
    asyncio.run(main())
