#!/usr/bin/env python3
"""
Final Verification Test

Quick test to verify all the key scenarios are working correctly.
"""

import asyncio
import json
import logging
from datetime import datetime

# Setup Django
import os
import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from apps.main.services.conversation_dispatcher import ConversationDispatcher
from apps.user.models import UserProfile
from asgiref.sync import sync_to_async

logging.basicConfig(level=logging.WARNING)  # Reduce noise
logger = logging.getLogger(__name__)


async def quick_test(message, expected_workflow):
    """Quick test of a single scenario."""
    
    # Get a test user profile
    user_profiles_qs = UserProfile.objects.filter(is_real=False)
    user_profile = await sync_to_async(user_profiles_qs.first)()
    
    if not user_profile:
        return {'error': 'No test user profiles found'}
    
    try:
        # Initialize ConversationDispatcher
        dispatcher = ConversationDispatcher(
            user_profile_id=str(user_profile.id),
            fail_fast_on_errors=True
        )
        
        # Process the message
        result = await dispatcher.process_message({
            'text': message,
            'timestamp': datetime.now().isoformat(),
            'metadata': {}
        })
        
        workflow_type = result.get('workflow_type')
        success = workflow_type == expected_workflow
        
        return {
            'message': message,
            'expected': expected_workflow,
            'actual': workflow_type,
            'success': success
        }
        
    except Exception as e:
        return {
            'message': message,
            'expected': expected_workflow,
            'error': str(e),
            'success': False
        }


async def main():
    """Main test function."""
    print("🧪 FINAL VERIFICATION TEST")
    print("=" * 40)
    print(f"🕒 Test started at: {datetime.now().isoformat()}")
    
    # Test the key scenarios that were previously failing
    test_scenarios = [
        ("I need some activity suggestions", "discussion"),
        ("I'm ready for some activities", "discussion"),
        ("Can you recommend some activities?", "discussion"),
    ]
    
    results = []
    success_count = 0
    
    for message, expected in test_scenarios:
        print(f"\n📝 Testing: '{message}'")
        print(f"   Expected: {expected}")
        
        result = await quick_test(message, expected)
        results.append(result)
        
        if result.get('success'):
            print(f"   ✅ SUCCESS: Got {result['actual']}")
            success_count += 1
        elif 'error' in result:
            print(f"   ❌ ERROR: {result['error']}")
        else:
            print(f"   ❌ FAILED: Got {result['actual']}, expected {expected}")
    
    # Summary
    total_tests = len(test_scenarios)
    success_rate = (success_count / total_tests) * 100
    
    print(f"\n📊 FINAL RESULTS")
    print("=" * 20)
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {success_count}")
    print(f"Failed: {total_tests - success_count}")
    print(f"Success Rate: {success_rate:.1f}%")
    
    if success_rate == 100:
        print("🎉 ALL TESTS PASSED! The fix is complete!")
    elif success_rate >= 80:
        print("✅ Most tests passed. Minor issues may remain.")
    else:
        print("❌ Significant issues remain. More work needed.")
    
    print(f"\n🕒 Test completed at: {datetime.now().isoformat()}")
    
    return results


if __name__ == '__main__':
    asyncio.run(main())
