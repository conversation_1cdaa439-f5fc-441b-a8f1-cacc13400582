# Authoritative Schemas - Single Source of Truth

## Overview

This document defines the authoritative schemas for all data structures used in the AI Workspace. These schemas serve as the single source of truth for data normalization and consistency across all tools and processes.

---

## Generic User Archetype Schema

### **Core Structure**
```json
{
  "id": "string",                    // Unique identifier (snake_case)
  "archetype_name": "string",        // Human-readable name
  "description": "string",           // Detailed description of the archetype
  "version": "1.0",                  // Schema version for evolution tracking
  "created_date": "ISO-8601",        // Creation timestamp
  "last_updated": "ISO-8601",        // Last modification timestamp
  
  "demographics": {
    "age": "integer",                // Age in years
    "gender": "string",              // Gender identity
    "location": "string",            // Geographic context
    "occupation": "string",          // Professional context
    "education": "string",           // Educational background
    "neurodivergent": "string|null"  // Neurodivergent status (ADHD, autism, etc.)
  },
  
  "personality_traits": {
    "openness": "float (0.0-1.0)",           // HEXACO: Openness to Experience
    "conscientiousness": "float (0.0-1.0)",  // HEXACO: Conscientiousness
    "extraversion": "float (0.0-1.0)",       // HEXACO: Extraversion
    "agreeableness": "float (0.0-1.0)",      // HEXACO: Agreeableness
    "neuroticism": "float (0.0-1.0)",        // HEXACO: Neuroticism
    "honesty_humility": "float (0.0-1.0)"    // HEXACO: Honesty-Humility (optional)
  },
  
  "trust_level": "integer (0-100)",  // Initial trust level
  
  "communication_preferences": {
    "tone": "string",                // preferred, supportive, encouraging, etc.
    "detail_level": "string",        // low, medium, high
    "formality": "string",           // casual, professional, formal
    "response_length": "string",     // brief, moderate, detailed
    "needs_reassurance": "boolean",  // Requires frequent reassurance
    "prefers_options": "boolean",    // Prefers choices over directives
    "appreciates_consistency": "boolean" // Values consistent interaction patterns
  },
  
  "behavioral_patterns": {
    "exploration_tendency": "string",     // low, medium, high, very_high
    "risk_tolerance": "string",           // very_low, low, medium, high
    "attention_span": "string",           // short, variable, long
    "overwhelm_threshold": "string",      // low, medium, high
    "needs_structure": "boolean",         // Requires structured guidance
    "prefers_choice": "boolean",          // Prefers having options
    "completion_rate": "float (0.0-1.0)", // Historical completion rate
    "feedback_seeking": "string"          // low, medium, high
  },
  
  "limitations": [
    "string"  // Array of limitation descriptions
  ],
  
  "capabilities": [
    "string"  // Array of capability descriptions
  ],
  
  "context_variables": {
    "session_context": "string",     // first_launch, returning, etc.
    "user_type": "string",           // adhd, neurotypical, etc.
    "experience_level": "string",    // beginner, intermediate, advanced
    "primary_goals": ["string"],     // Array of primary user goals
    "trigger_words": ["string"]      // Words that might trigger negative responses
  },
  
  "is_temporary": "boolean",         // Whether this is a test profile
  "is_real": "boolean",             // Whether based on real user data
  "archetype_category": "string"    // Category: neurodivergent, professional, creative, etc.
}
```

### **Validation Rules**
1. **ID Format**: Must be snake_case, unique across all archetypes
2. **Personality Traits**: All values must be between 0.0 and 1.0
3. **Trust Level**: Must be integer between 0 and 100
4. **Required Fields**: id, archetype_name, description, demographics, personality_traits, trust_level
5. **Enum Values**: 
   - tone: [gentle, supportive, encouraging, inspiring, motivational, respectful]
   - detail_level: [low, medium, high]
   - formality: [casual, professional, formal]
   - response_length: [brief, moderate, detailed]

---

## Generic Situation Schema

### **Core Structure**
```json
{
  "id": "string",                    // Unique identifier (snake_case)
  "name": "string",                  // Human-readable name
  "description": "string",           // Detailed description
  "version": "1.0",                  // Schema version
  "created_date": "ISO-8601",        // Creation timestamp
  "last_updated": "ISO-8601",        // Last modification timestamp
  
  "workflow_type": "string",         // discussion, wheel_generation, onboarding, etc.
  "user_message": "string",          // What the user says
  
  "context_variables": {
    "trust_level": "integer",        // User's trust level
    "is_first_time": "boolean",      // First time using the app
    "mood": "string",                // User's current mood
    "energy": "string",              // User's energy level
    "time_available": "string",      // Available time
    "environment": "string",         // User's environment
    "user_type": "string",           // adhd, neurotypical, etc.
    "session_context": "string",     // first_launch, returning, etc.
    "mood_assessed": "boolean"       // Whether mood has been assessed
  },
  
  "expected_outcomes": [
    "string"  // Array of expected outcomes
  ],
  
  "evaluation_criteria": {
    "criterion_name": "string"       // Key-value pairs of evaluation criteria
  },
  
  "difficulty_level": "integer (1-5)", // Complexity level
  "tags": ["string"],               // Categorization tags
  
  "scenario_category": "string",    // first_time, returning_user, crisis, etc.
  "priority": "string",             // low, medium, high, critical
  "estimated_duration": "string"    // Expected interaction duration
}
```

### **Validation Rules**
1. **ID Format**: Must be snake_case, unique across all situations
2. **Workflow Type**: Must be valid workflow type (discussion, wheel_generation, onboarding)
3. **Difficulty Level**: Must be integer between 1 and 5
4. **Required Fields**: id, name, description, workflow_type, user_message, expected_outcomes
5. **Enum Values**:
   - workflow_type: [discussion, wheel_generation, onboarding, feedback, crisis_support]
   - difficulty_level: [1, 2, 3, 4, 5]
   - priority: [low, medium, high, critical]

---

## Schema Evolution Guidelines

### **Version Management**
- All schemas include version field for tracking evolution
- Breaking changes require version increment
- Backward compatibility maintained for at least 2 versions

### **Validation Process**
1. All new archetypes/situations must validate against current schema
2. Automated validation in loading processes
3. Schema violations logged and reported
4. Graceful degradation for missing optional fields

### **Documentation Standards**
- All schema changes documented with rationale
- Examples provided for each schema version
- Migration guides for schema updates
- Regular schema review and optimization

---

## Implementation Notes

### **File Organization**
- Generic users: `generic_users/[category]/[archetype_id].json`
- Generic situations: `generic_situations/[workflow_type]/[situation_id].json`
- Schema validation: `schemas/validators.py`

### **Naming Conventions**
- IDs: snake_case (e.g., `adhd_first_timer`, `wheel_gen_basic`)
- Files: snake_case matching ID
- Categories: snake_case (e.g., `neurodivergent`, `first_time_scenarios`)

### **Quality Assurance**
- All schemas validated on load
- Regular audits for consistency
- Automated testing of schema compliance
- Documentation kept in sync with implementation
