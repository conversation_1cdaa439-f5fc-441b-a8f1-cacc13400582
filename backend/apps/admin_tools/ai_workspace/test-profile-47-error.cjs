/**
 * Test Profile 47 Error - Witness the GenericResource 'name' attribute error
 */

const { chromium } = require('playwright');

async function testProfile47Error() {
    console.log('🔍 Testing Profile 47 Error - Witnessing GenericResource name attribute error...');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000 
    });
    
    try {
        const context = await browser.newContext();
        const page = await context.newPage();
        
        // Navigate to admin user profiles page
        console.log('📍 Navigating to user profiles management...');
        await page.goto('http://localhost:8000/admin/user-profiles/');
        
        // Wait for page to load
        await page.waitForSelector('.profile-table', { timeout: 10000 });
        console.log('✅ User profiles page loaded');
        
        // Look for profile 47 (Guillaume)
        console.log('🔍 Looking for Profile 47 (Guillaume)...');
        
        // Find the row with profile ID 47
        const profile47Row = await page.locator('tr[data-profile-id="47"]').first();
        
        if (await profile47Row.count() === 0) {
            console.log('❌ Profile 47 not found on current page');
            // Try to find it by searching for <PERSON>
            const guillaumeRows = await page.locator('tr:has-text("Guillaume")').count();
            console.log(`Found ${guillaumeRows} rows containing "Guillaume"`);
            
            if (guillaumeRows > 0) {
                console.log('📋 Guillaume profiles found:');
                const rows = await page.locator('tr:has-text("Guillaume")').all();
                for (let i = 0; i < rows.length; i++) {
                    const rowText = await rows[i].textContent();
                    console.log(`  Row ${i + 1}: ${rowText.substring(0, 100)}...`);
                }
            }
            return;
        }
        
        console.log('✅ Found Profile 47 (Guillaume)');
        
        // Click the "View" button for profile 47
        console.log('🖱️ Clicking View button for Profile 47...');
        const viewButton = profile47Row.locator('.view-profile-btn');
        await viewButton.click();
        
        // Wait a moment for the API call
        await page.waitForTimeout(2000);
        
        // Check for error messages in console
        const consoleLogs = [];
        page.on('console', msg => {
            consoleLogs.push(`${msg.type()}: ${msg.text()}`);
        });
        
        // Check for network errors
        const networkErrors = [];
        page.on('response', response => {
            if (response.status() >= 400) {
                networkErrors.push(`${response.status()}: ${response.url()}`);
            }
        });
        
        // Wait for modal or error
        try {
            await page.waitForSelector('#user-profile-detail-modal', { timeout: 5000 });
            console.log('✅ Modal appeared - checking for content...');
            
            // Check if modal shows error or loads properly
            const modalContent = await page.locator('#user-profile-detail-modal .modal-body').textContent();
            if (modalContent.includes('Error') || modalContent.includes('error')) {
                console.log('❌ Modal shows error content');
                console.log('Modal content:', modalContent.substring(0, 200));
            } else {
                console.log('✅ Modal loaded successfully');
            }
            
        } catch (error) {
            console.log('❌ Modal did not appear within timeout');
        }
        
        // Report any console errors
        if (consoleLogs.length > 0) {
            console.log('\n📋 Console logs:');
            consoleLogs.forEach(log => console.log(`  ${log}`));
        }
        
        // Report any network errors
        if (networkErrors.length > 0) {
            console.log('\n🚨 Network errors:');
            networkErrors.forEach(error => console.log(`  ${error}`));
        }
        
        // Check browser network tab for the specific API call
        console.log('\n🔍 Checking for API call to profile 47...');
        
        // Make direct API call to witness the error
        console.log('🔗 Making direct API call to witness error...');
        try {
            const apiResponse = await page.request.get('http://localhost:8000/admin/user-profiles/api/47/');
            console.log(`API Response Status: ${apiResponse.status()}`);
            
            if (apiResponse.status() === 500) {
                const responseText = await apiResponse.text();
                console.log('🚨 API Error Response:', responseText.substring(0, 500));
            } else {
                console.log('✅ API call successful');
            }
        } catch (apiError) {
            console.log('❌ API call failed:', apiError.message);
        }
        
        // Keep browser open for manual inspection
        console.log('\n⏸️ Keeping browser open for manual inspection...');
        console.log('Press Ctrl+C to close when done inspecting');
        
        // Wait indefinitely
        await new Promise(() => {});
        
    } catch (error) {
        console.error('❌ Test failed:', error);
    } finally {
        // Don't close browser automatically for inspection
        // await browser.close();
    }
}

// Run the test
testProfile47Error().catch(console.error);
