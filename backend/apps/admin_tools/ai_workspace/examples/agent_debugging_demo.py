#!/usr/bin/env python3
"""
Agent Debugging System Demo

Demonstrates the comprehensive agent debugging and improvement system
for early-stage development.
"""

import asyncio
import json
import os
import sys
import django
from pathlib import Path

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')
django.setup()

# Add workspace to path
workspace_path = Path(__file__).parent.parent
sys.path.insert(0, str(workspace_path))

from workspace import AIWorkspace


async def demonstrate_user_story_simulation():
    """Demonstrate user story simulation with generic situations and archetypes."""
    
    print("🎭 User Story Simulation Demo")
    print("=" * 50)
    
    workspace = AIWorkspace()
    await workspace.initialize()
    
    # Show available situations and archetypes
    situations = workspace.debugging_system.situation_library
    archetypes = workspace.debugging_system.archetype_library
    
    print(f"Available situations: {len(situations)}")
    for situation_id, situation in list(situations.items())[:3]:
        print(f"  - {situation_id}: {situation.name}")
        print(f"    Workflow: {situation.workflow_type}, Difficulty: {situation.difficulty_level}")
    
    print(f"\nAvailable archetypes: {len(archetypes)}")
    for archetype_id, archetype in list(archetypes.items())[:3]:
        print(f"  - {archetype_id}: {archetype.archetype_name}")
        print(f"    Trust Level: {archetype.trust_level}, Traits: {archetype.personality_traits}")
    
    # Simulate a user story
    print("\n🎬 Simulating User Story...")
    try:
        result = await workspace.debugging_system.simulate_user_story(
            situation_id="wheel_gen_basic",
            archetype_id="curious_beginner"
        )
        
        print(f"✅ Story simulation completed!")
        print(f"   Session ID: {result['session_id']}")
        print(f"   User Profile ID: {result['user_profile_id']}")
        
        if 'analysis' in result:
            analysis = result['analysis']
            print(f"   Quality Score: {analysis.get('quality_assessment', {}).get('overall_score', 'N/A')}")
        
        # Show benchmark result summary
        if 'benchmark_result' in result and result['benchmark_result'].get('status') == 'completed':
            br = result['benchmark_result']['result']
            if 'runs' in br and br['runs']:
                run = br['runs'][0]
                print(f"   Semantic Score: {run.get('semantic_score', 'N/A')}")
                print(f"   Execution Time: {run.get('mean_duration', 'N/A')}s")
        
    except Exception as e:
        print(f"❌ Error simulating user story: {e}")


async def demonstrate_agent_debugging():
    """Demonstrate comprehensive agent debugging workflow."""
    
    print("\n🔧 Agent Debugging Workflow Demo")
    print("=" * 50)
    
    workspace = AIWorkspace()
    await workspace.initialize()
    
    # Debug an agent with multiple user stories
    print("Running comprehensive agent debugging...")
    try:
        debug_report = await workspace.debug_agent_with_user_stories(
            agent_role="mentor",
            situation_ids=["wheel_gen_basic", "wheel_gen_stressed"],
            archetype_ids=["curious_beginner", "stressed_professional"]
        )
        
        if debug_report['status'] == 'completed':
            print("✅ Agent debugging completed!")
            
            # Show user story results
            story_results = debug_report['user_stories']
            print(f"   Total stories tested: {story_results['total_stories']}")
            print(f"   Successful stories: {story_results['successful_stories']}")
            print(f"   Success rate: {story_results.get('success_rate', 0):.2%}")
            
            if 'aggregate_analysis' in story_results:
                agg = story_results['aggregate_analysis']
                print(f"   Average semantic score: {agg.get('average_semantic_score', 0):.3f}")
            
            # Show improvement recommendations
            recommendations = debug_report.get('improvement_recommendations', [])
            print(f"   Improvement recommendations: {len(recommendations)}")
            for i, rec in enumerate(recommendations[:3], 1):
                print(f"     {i}. {rec}")
        
        else:
            print(f"❌ Debugging failed: {debug_report.get('error', 'Unknown error')}")
    
    except Exception as e:
        print(f"❌ Error in agent debugging: {e}")


async def demonstrate_change_impact_testing():
    """Demonstrate testing the impact of agent changes."""
    
    print("\n📊 Change Impact Testing Demo")
    print("=" * 50)
    
    workspace = AIWorkspace()
    await workspace.initialize()
    
    # Test impact of instruction changes
    print("Testing impact of agent instruction changes...")
    try:
        # Example: Test improved instructions for stress handling
        improved_instructions = """
        You are a supportive mentor focused on helping users discover meaningful activities.
        
        STRESS AWARENESS: Pay special attention to signs of stress or overwhelm in user messages.
        When users express stress, prioritize calming and restorative activities.
        
        PERSONALIZATION: Always consider the user's trust level, mood, and available time.
        Adapt your suggestions to match their current emotional state and capabilities.
        
        ACTIVITY VARIETY: Provide diverse activity suggestions across different domains:
        - Physical activities (appropriate to stress level)
        - Creative pursuits (therapeutic when stressed)
        - Social connections (if user is open to it)
        - Mindfulness practices (especially for stressed users)
        - Learning opportunities (light and engaging)
        - Self-care activities (prioritize for stressed users)
        
        COMMUNICATION: Use an encouraging, supportive tone. For stressed users, be extra gentle
        and reassuring. Acknowledge their feelings and provide hope.
        """
        
        impact_result = await workspace.test_agent_changes(
            agent_role="mentor",
            change_type="instructions",
            changes={"instructions": improved_instructions},
            description="Enhanced stress awareness and personalized activity suggestions"
        )
        
        if impact_result['status'] == 'completed':
            print("✅ Change impact testing completed!")
            
            impact = impact_result['impact_measurement']
            print(f"   Overall improvement score: {impact['improvement_score']:.3f}")
            print(f"   Semantic score change: {impact['semantic_score_delta']:+.3f}")
            print(f"   Execution time change: {impact['execution_time_delta']:+.1f}s")
            print(f"   Success rate change: {impact['success_rate_delta']:+.3f}")
            
            assessment = impact['detailed_analysis']['overall_assessment']
            print(f"   Assessment: {assessment}")
            
            # Recommendation based on results
            if impact['improvement_score'] > 0.05:
                print("   🎉 Recommendation: APPLY CHANGES (significant improvement)")
            elif impact['improvement_score'] > 0.01:
                print("   ✅ Recommendation: CONSIDER APPLYING (moderate improvement)")
            elif impact['improvement_score'] > -0.01:
                print("   ⚠️  Recommendation: NO SIGNIFICANT CHANGE")
            else:
                print("   ❌ Recommendation: REJECT CHANGES (performance degradation)")
        
        else:
            print(f"❌ Change testing failed: {impact_result.get('error', 'Unknown error')}")
    
    except Exception as e:
        print(f"❌ Error in change impact testing: {e}")


async def demonstrate_profile_management():
    """Demonstrate temporary profile management."""
    
    print("\n👥 Temporary Profile Management Demo")
    print("=" * 50)
    
    workspace = AIWorkspace()
    await workspace.initialize()
    
    session_id = "demo_session_001"
    
    # Create temporary profiles
    print("Creating temporary profiles...")
    created_profiles = []
    
    for archetype_id in ["curious_beginner", "stressed_professional"]:
        try:
            result = await workspace.manage_temporary_profiles(
                action='create',
                archetype_id=archetype_id,
                session_id=session_id
            )
            
            if result['status'] == 'created':
                created_profiles.append(result)
                print(f"   ✅ Created: {result['profile_name']} (ID: {result['profile_id']})")
            else:
                print(f"   ❌ Failed to create profile for {archetype_id}")
        
        except Exception as e:
            print(f"   ❌ Error creating profile for {archetype_id}: {e}")
    
    # List profiles
    print(f"\nListing profiles for session {session_id}...")
    try:
        result = await workspace.manage_temporary_profiles(
            action='list',
            session_id=session_id
        )
        
        if result['status'] == 'listed':
            print(f"   Found {result['total_count']} profiles:")
            for profile in result['profiles']:
                print(f"     - {profile['name']} (ID: {profile['id']})")
        
    except Exception as e:
        print(f"   ❌ Error listing profiles: {e}")
    
    # Clean up profiles
    print(f"\nCleaning up profiles for session {session_id}...")
    try:
        result = await workspace.manage_temporary_profiles(
            action='prune',
            session_id=session_id,
            older_than_hours=0  # Delete all profiles for this session
        )
        
        if result['status'] == 'pruned':
            print(f"   ✅ Cleaned up {result['deleted_count']} profiles")
        
    except Exception as e:
        print(f"   ❌ Error cleaning up profiles: {e}")


async def demonstrate_systematic_improvement():
    """Demonstrate a complete systematic improvement workflow."""
    
    print("\n🚀 Systematic Agent Improvement Demo")
    print("=" * 50)
    
    workspace = AIWorkspace()
    await workspace.initialize()
    
    agent_role = "mentor"
    
    print(f"Starting systematic improvement for {agent_role} agent...")
    
    # Step 1: Baseline assessment
    print("\n1. Baseline Assessment...")
    try:
        baseline_report = await workspace.debug_agent_with_user_stories(
            agent_role=agent_role,
            situation_ids=["wheel_gen_basic", "wheel_gen_stressed"],
            archetype_ids=["curious_beginner", "stressed_professional"]
        )
        
        if baseline_report['status'] == 'completed':
            baseline_score = baseline_report['user_stories']['aggregate_analysis'].get('average_semantic_score', 0)
            print(f"   Baseline semantic score: {baseline_score:.3f}")
        else:
            print(f"   ❌ Baseline assessment failed")
            return
    
    except Exception as e:
        print(f"   ❌ Error in baseline assessment: {e}")
        return
    
    # Step 2: Test improvement
    print("\n2. Testing Improvement...")
    try:
        # Test a simple improvement
        impact_result = await workspace.test_agent_changes(
            agent_role=agent_role,
            change_type="instructions",
            changes={"instructions": "Enhanced instructions with better empathy and stress recognition..."},
            description="Improved empathy and stress handling"
        )
        
        if impact_result['status'] == 'completed':
            improvement_score = impact_result['impact_measurement']['improvement_score']
            print(f"   Improvement score: {improvement_score:.3f}")
            
            # Step 3: Decision making
            print("\n3. Decision Making...")
            if improvement_score > 0.05:
                print("   🎉 Decision: APPLY CHANGES (significant improvement)")
                # In a real scenario, we would apply the changes here
                print("   📝 Changes would be applied to the agent configuration")
            elif improvement_score > 0.01:
                print("   ✅ Decision: CONSIDER APPLYING (moderate improvement)")
                print("   📝 Changes might be applied after further testing")
            else:
                print("   ❌ Decision: REJECT CHANGES (insufficient improvement)")
                print("   📝 Changes would be discarded, try different approach")
        
        else:
            print(f"   ❌ Improvement testing failed")
    
    except Exception as e:
        print(f"   ❌ Error in improvement testing: {e}")
    
    print("\n4. Documentation...")
    print("   📚 All results would be documented in the knowledge base")
    print("   📊 Performance trends would be tracked over time")
    print("   🔄 Process would repeat with new improvement hypotheses")


async def main():
    """Main demonstration function."""
    print("🤖 Agent Debugging System - Comprehensive Demo")
    print("=" * 60)
    print("This demo shows how to systematically debug and improve AI agents")
    print("using normalized situations and user archetypes.")
    print()
    
    try:
        await demonstrate_user_story_simulation()
        await demonstrate_agent_debugging()
        await demonstrate_change_impact_testing()
        await demonstrate_profile_management()
        await demonstrate_systematic_improvement()
        
        print("\n" + "=" * 60)
        print("🎉 Agent Debugging System Demo Completed!")
        print("\nKey Benefits Demonstrated:")
        print("✅ Systematic user story simulation")
        print("✅ Quantitative change impact measurement")
        print("✅ Automated temporary profile management")
        print("✅ Data-driven improvement decisions")
        print("✅ Comprehensive debugging workflows")
        
    except KeyboardInterrupt:
        print("\n\nDemo interrupted by user")
    except Exception as e:
        print(f"\n\nDemo failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("Starting Agent Debugging System Demo...")
    print("Press Ctrl+C to interrupt at any time")
    print()
    
    asyncio.run(main())
