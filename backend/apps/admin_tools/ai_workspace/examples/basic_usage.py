#!/usr/bin/env python3
"""
Basic Usage Example for Goali AI Workspace

This script demonstrates how to use the AI Workspace for improving
Goali admin tools and interfaces.
"""

import asyncio
import json
import os
import sys
import django
from pathlib import Path

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')
django.setup()

# Add workspace to path
workspace_path = Path(__file__).parent.parent
sys.path.insert(0, str(workspace_path))

from workspace import AIWorkspace


async def demonstrate_workspace_capabilities():
    """Demonstrate the key capabilities of the AI Workspace."""
    
    print("🚀 Goali AI Workspace - Basic Usage Example")
    print("=" * 60)
    
    # Initialize the workspace
    print("\n1. Initializing AI Workspace...")
    workspace = AIWorkspace()
    init_result = await workspace.initialize()
    
    if init_result['status'] != 'initialized':
        print(f"❌ Failed to initialize workspace: {init_result.get('error')}")
        return
    
    print(f"✅ Workspace initialized successfully!")
    print(f"   Session ID: {init_result['session_id']}")
    print(f"   Capabilities: {', '.join(init_result['capabilities'].keys())}")
    
    # Demonstrate Agent Management
    print("\n2. Agent Management Capabilities...")
    try:
        agents = workspace.agent_manager.get_all_agents()
        print(f"   Found {len(agents)} agents in the system")
        
        active_agents = [a for a in agents if a['is_active']]
        print(f"   Active agents: {len(active_agents)}")
        
        for agent in active_agents[:3]:  # Show first 3 active agents
            print(f"   - {agent['role']}: {agent['description'][:50]}...")
        
        # Get agent summary
        summary = workspace.agent_manager.get_agent_summary()
        print(f"   LLM configs in use: {len(summary['llm_configs_in_use'])}")
        print(f"   Tools in use: {len(summary['tools_in_use'])}")
        
    except Exception as e:
        print(f"   ⚠️  Error accessing agents: {e}")
    
    # Demonstrate Benchmark Analysis
    print("\n3. Benchmark Analysis Capabilities...")
    try:
        recent_runs = workspace.benchmark_analyzer.get_recent_benchmark_runs(days=7, limit=5)
        print(f"   Found {len(recent_runs)} recent benchmark runs")
        
        if recent_runs:
            latest_run = recent_runs[0]
            print(f"   Latest run: {latest_run['scenario_name']} ({latest_run['agent_role']})")
            print(f"   Semantic score: {latest_run.get('semantic_score', 'N/A')}")
            print(f"   Success rate: {latest_run.get('success_rate', 'N/A')}")
        
        # Analyze wheel generation quality
        wheel_analysis = await workspace.benchmark_analyzer.analyze_wheel_generation_quality(days=7)
        print(f"   Wheel generation analysis: {wheel_analysis['total_wheel_generations']} runs analyzed")
        
    except Exception as e:
        print(f"   ⚠️  Error analyzing benchmarks: {e}")
    
    # Demonstrate Knowledge Management
    print("\n4. Knowledge Management Capabilities...")
    try:
        # Document a sample finding
        finding = {
            'title': 'AI Workspace Demo Finding',
            'description': 'This is a demonstration of the knowledge management system',
            'category': 'demo',
            'impact': 'This shows how AI agents can document their discoveries',
            'technical_details': 'The workspace successfully initialized and demonstrated all core capabilities'
        }
        
        finding_path = workspace.knowledge_manager.document_finding(finding)
        print(f"   ✅ Documented finding: {Path(finding_path).name}")
        
        # Search for findings
        search_results = workspace.knowledge_manager.search_findings('demo')
        print(f"   Found {len(search_results)} findings matching 'demo'")
        
        # Generate a report
        report_path = workspace.knowledge_manager.generate_findings_report(days=1)
        print(f"   ✅ Generated findings report: {Path(report_path).name}")
        
    except Exception as e:
        print(f"   ⚠️  Error with knowledge management: {e}")
    
    # Demonstrate Benchmark Tools
    print("\n5. Benchmark Tools Capabilities...")
    try:
        # Validate benchmark system
        validation = await workspace.benchmark_tools.validate_benchmark_system()
        print(f"   Benchmark system health: {validation.get('overall_health', 'unknown')}")
        print(f"   Health score: {validation.get('health_score', 0):.1f}%")
        
        # Show validation details
        if 'checks' in validation:
            passed_checks = sum(1 for check in validation['checks'] if check['passed'])
            total_checks = len(validation['checks'])
            print(f"   Validation checks: {passed_checks}/{total_checks} passed")
        
    except Exception as e:
        print(f"   ⚠️  Error with benchmark tools: {e}")
    
    # Demonstrate System Health Analysis
    print("\n6. Comprehensive System Health Analysis...")
    try:
        health_report = await workspace.analyze_system_health()
        print(f"   Overall system status: {health_report.get('overall_status', 'unknown')}")
        
        components = health_report.get('components', {})
        for component, status in components.items():
            print(f"   - {component}: {status.get('status', 'unknown')}")
        
    except Exception as e:
        print(f"   ⚠️  Error analyzing system health: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 AI Workspace demonstration completed!")
    print("\nNext steps:")
    print("- Explore individual tools in the tools/ directory")
    print("- Check generated documentation in docs/")
    print("- Review reports in reports/")
    print("- Run the full test suite: pytest tests/test_workspace.py")


async def demonstrate_agent_improvement():
    """Demonstrate agent improvement workflow."""
    
    print("\n🔧 Agent Improvement Workflow Demo")
    print("=" * 50)
    
    workspace = AIWorkspace()
    await workspace.initialize()
    
    # Get available agents
    agents = workspace.agent_manager.get_all_agents()
    active_agents = [a for a in agents if a['is_active']]
    
    if not active_agents:
        print("No active agents found for improvement demo")
        return
    
    # Pick the first active agent for demonstration
    target_agent = active_agents[0]
    agent_role = target_agent['role']
    
    print(f"Analyzing agent: {agent_role}")
    print(f"Description: {target_agent['description'][:100]}...")
    
    try:
        # Analyze agent performance
        improvement_report = await workspace.improve_agent_performance(agent_role)
        
        if 'error' in improvement_report:
            print(f"Error analyzing agent: {improvement_report['error']}")
            return
        
        print(f"✅ Analysis completed for {agent_role}")
        
        # Show analysis results
        analysis = improvement_report.get('analysis', {})
        if 'performance_trends' in analysis:
            trends = analysis['performance_trends']
            print(f"Performance trends: {trends.get('total_runs', 0)} runs analyzed")
        
        # Show improvements
        improvements = improvement_report.get('improvements', [])
        print(f"Found {len(improvements)} improvement recommendations")
        
        for i, improvement in enumerate(improvements[:3], 1):
            print(f"  {i}. {improvement.get('title', 'Improvement')}")
            print(f"     {improvement.get('description', 'No description')[:80]}...")
        
    except Exception as e:
        print(f"Error in agent improvement workflow: {e}")


async def main():
    """Main demonstration function."""
    try:
        await demonstrate_workspace_capabilities()
        await demonstrate_agent_improvement()
        
    except KeyboardInterrupt:
        print("\n\nDemo interrupted by user")
    except Exception as e:
        print(f"\n\nDemo failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("Starting Goali AI Workspace Demo...")
    print("Press Ctrl+C to interrupt at any time")
    print()
    
    asyncio.run(main())
