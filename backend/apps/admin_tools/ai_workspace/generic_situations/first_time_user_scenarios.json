[{"id": "adhd_first_launch_basic", "name": "ADHD User First App Launch - Basic", "description": "ADHD user launches the app for the first time with a general wellness request - should trigger discussion workflow, not immediate wheel generation", "version": "1.0", "created_date": "2025-01-11T10:00:00Z", "last_updated": "2025-01-11T10:00:00Z", "workflow_type": "discussion", "user_message": "I want to improve my wellness and productivity", "context_variables": {"trust_level": 25, "is_first_time": true, "mood": null, "energy": null, "time_available": null, "environment": "unknown", "user_type": "adhd", "session_context": "first_launch", "mood_assessed": false}, "expected_outcomes": ["<PERSON><PERSON> asks about current mood and emotional state", "<PERSON><PERSON> asks about energy level and capacity", "<PERSON><PERSON> provides gentle, supportive guidance", "NO immediate wheel generation or activity suggestions", "Supportive, non-overwhelming response that builds trust", "Clear explanation of next steps without pressure"], "evaluation_criteria": {"mood_inquiry": "Should ask about user's current mood before proceeding with any suggestions", "energy_assessment": "Should inquire about energy level and current capacity", "gentle_approach": "Should use supportive, non-pressuring language appropriate for ADHD users", "no_immediate_wheel": "Should NOT immediately generate a wheel or suggest activities", "choice_offering": "Should offer options rather than making assumptions about user needs", "trust_building": "Should focus on building rapport and understanding user state"}, "difficulty_level": 2, "tags": ["adhd", "first_time", "wellness", "discussion", "mood_assessment", "trust_building"], "scenario_category": "first_time", "priority": "high", "estimated_duration": "2-3 minutes"}, {"id": "adhd_first_launch_overwhelmed", "name": "ADHD User First Launch - Overwhelmed", "description": "ADHD user launches the app feeling overwhelmed and needing emotional support - requires careful, gentle approach", "version": "1.0", "created_date": "2025-01-11T10:00:00Z", "last_updated": "2025-01-11T10:00:00Z", "workflow_type": "discussion", "user_message": "I'm feeling really overwhelmed and don't know where to start", "context_variables": {"trust_level": 25, "is_first_time": true, "mood": "overwhelmed", "energy": "low", "time_available": null, "environment": "unknown", "user_type": "adhd", "session_context": "first_launch", "mood_assessed": true}, "expected_outcomes": ["<PERSON><PERSON> acknowledges and validates overwhelmed feelings", "<PERSON><PERSON> provides immediate reassurance and emotional support", "<PERSON><PERSON> asks gentle, simple clarifying questions", "<PERSON><PERSON> offers clear, simple next steps without pressure", "NO complex options or immediate wheel generation", "Focus on emotional regulation before any activity suggestions"], "evaluation_criteria": {"overwhelm_recognition": "Should immediately acknowledge and validate overwhelmed feelings", "reassurance_provided": "Should offer genuine comfort and emotional support", "gentle_questioning": "Should ask simple, non-overwhelming questions that don't add pressure", "simple_guidance": "Should provide clear, simple next steps focused on emotional regulation", "no_complexity": "Should avoid complex choices or immediate activity generation", "emotional_priority": "Should prioritize emotional support over productivity suggestions"}, "difficulty_level": 3, "tags": ["adhd", "first_time", "overwhelmed", "emotional_support", "gentle_guidance", "crisis_support"], "scenario_category": "first_time", "priority": "critical", "estimated_duration": "3-5 minutes"}, {"id": "adhd_ready_for_activities", "name": "ADHD User Ready for Activities", "description": "ADHD user who has been properly assessed and is ready for activity suggestions - should generate wheel with unique IDs", "version": "1.0", "created_date": "2025-01-11T10:00:00Z", "last_updated": "2025-01-11T10:00:00Z", "workflow_type": "wheel_generation", "user_message": "I'm feeling good and ready to try some activities", "context_variables": {"trust_level": 35, "is_first_time": false, "mood": "positive", "energy": "medium", "time_available": "2 hours", "environment": "home", "user_type": "adhd", "session_context": "returning", "mood_assessed": true}, "expected_outcomes": ["Generate diverse activities across multiple domains", "Ensure all activities have unique IDs (critical requirement)", "Provide ADHD-friendly options with appropriate challenge levels", "Include choice and flexibility in activity selection", "Clear, simple instructions that avoid overwhelm", "Activities tailored to positive mood and medium energy"], "evaluation_criteria": {"activity_diversity": "Should include 5+ different domains (wellness, creativity, physical, etc.)", "unique_ids": "All activities must have unique IDs - NO DUPLICATES ALLOWED", "adhd_friendly": "Activities should be ADHD-appropriate with clear structure", "choice_emphasis": "Should emphasize user choice and autonomy", "clear_instructions": "Instructions should be simple, clear, and actionable", "mood_alignment": "Activities should match positive mood and medium energy level"}, "difficulty_level": 2, "tags": ["adhd", "wheel_generation", "activities", "unique_ids", "choice", "mood_assessed"], "scenario_category": "returning_user", "priority": "medium", "estimated_duration": "1-2 minutes"}]