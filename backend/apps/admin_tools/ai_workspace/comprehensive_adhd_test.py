#!/usr/bin/env python3
"""
Comprehensive ADHD User Experience Test

This script tests multiple scenarios to identify when the immediate wheel generation issue occurs:
1. First-time user with low trust level
2. Returning user with medium trust level  
3. User with high trust level
4. Different message types and contexts
"""

import asyncio
import json
import logging
from datetime import datetime

# Setup Django
import os
import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from apps.main.services.conversation_dispatcher import ConversationDispatcher
from apps.user.models import UserProfile
from asgiref.sync import sync_to_async

logging.basicConfig(level=logging.WARNING)  # Reduce noise
logger = logging.getLogger(__name__)


async def test_scenario(user_profile, test_message, scenario_name, expected_workflow=None):
    """Test a specific scenario with a user profile and message."""
    
    print(f"\n🧪 TESTING SCENARIO: {scenario_name}")
    print("=" * 60)
    print(f"👤 User: {user_profile.profile_name} (ID: {user_profile.id})")
    print(f"📝 Message: '{test_message}'")
    if expected_workflow:
        print(f"🎯 Expected Workflow: {expected_workflow}")
    
    try:
        # Initialize ConversationDispatcher
        dispatcher = ConversationDispatcher(
            user_profile_id=str(user_profile.id),
            fail_fast_on_errors=True
        )
        
        # Process the message
        result = await dispatcher.process_message({
            'text': test_message,
            'timestamp': datetime.now().isoformat(),
            'metadata': {}
        })
        
        # Analyze results
        workflow_type = result.get('workflow_type', 'unknown')
        confidence = result.get('confidence', 0.0)
        
        print(f"📊 RESULTS:")
        print(f"  🔄 Workflow: {workflow_type} (confidence: {confidence:.2f})")
        
        # Check for issues
        issues = []
        
        # Issue 1: Immediate wheel generation for first-time users
        if workflow_type == 'wheel_generation':
            # Check if this is appropriate
            context = result.get('context_packet', {})
            mentor_context = context.get('mentor_context', {})
            trust_level = mentor_context.get('trust_level', 0)
            
            if trust_level < 0.6:  # Low trust level
                issues.append("❌ IMMEDIATE WHEEL GENERATION: Low trust user got wheel_generation workflow")
            else:
                print("  ✅ Wheel generation appropriate for high-trust user")
        
        # Issue 2: Check if mood assessment is missing for wellness requests
        if 'wellness' in test_message.lower() or 'improve' in test_message.lower():
            if workflow_type != 'discussion' and workflow_type != 'user_onboarding':
                issues.append("❌ NO MOOD ASSESSMENT: Wellness request should trigger discussion first")
        
        # Issue 3: Check expected workflow
        if expected_workflow and workflow_type != expected_workflow:
            issues.append(f"❌ WRONG WORKFLOW: Expected {expected_workflow}, got {workflow_type}")
        
        if issues:
            print(f"  ❌ Issues Found: {len(issues)}")
            for issue in issues:
                print(f"    {issue}")
        else:
            print(f"  ✅ No issues found")
        
        return {
            'scenario': scenario_name,
            'user_id': user_profile.id,
            'user_name': user_profile.profile_name,
            'message': test_message,
            'workflow_type': workflow_type,
            'confidence': confidence,
            'issues': issues,
            'result': result
        }
        
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return {
            'scenario': scenario_name,
            'error': str(e)
        }


async def main():
    """Run comprehensive ADHD user experience tests."""
    
    print("🔍 COMPREHENSIVE ADHD USER EXPERIENCE TEST")
    print("=" * 80)
    print(f"🕒 Started at: {datetime.now().isoformat()}")
    
    # Get test user profiles
    user_profiles_qs = UserProfile.objects.filter(is_real=False)
    user_profiles = await sync_to_async(list)(user_profiles_qs)
    
    if not user_profiles:
        print("❌ No test user profiles found")
        return
    
    print(f"👥 Found {len(user_profiles)} test user profiles")
    
    # Test scenarios
    test_scenarios = [
        {
            'message': 'I want to improve my wellness and productivity',
            'name': 'First-time wellness request',
            'expected_workflow': 'discussion'  # Should ask about mood first
        },
        {
            'message': 'I need some activity suggestions',
            'name': 'Direct activity request',
            'expected_workflow': None  # Could be wheel_generation or discussion depending on trust
        },
        {
            'message': 'I\'m feeling overwhelmed and don\'t know where to start',
            'name': 'Overwhelmed user',
            'expected_workflow': 'discussion'  # Should provide support first
        },
        {
            'message': 'I\'m ready for some activities',
            'name': 'Ready for activities',
            'expected_workflow': None  # Could be wheel_generation if trust is high
        },
        {
            'message': 'Hi, I\'m new here',
            'name': 'New user greeting',
            'expected_workflow': 'user_onboarding'
        }
    ]
    
    all_results = []
    
    # Test each scenario with each user profile
    for user_profile in user_profiles[:3]:  # Limit to first 3 users to avoid too much output
        for scenario in test_scenarios:
            result = await test_scenario(
                user_profile=user_profile,
                test_message=scenario['message'],
                scenario_name=scenario['name'],
                expected_workflow=scenario['expected_workflow']
            )
            all_results.append(result)
    
    # Summary analysis
    print(f"\n📊 COMPREHENSIVE ANALYSIS")
    print("=" * 80)
    
    total_tests = len(all_results)
    tests_with_issues = len([r for r in all_results if r.get('issues')])
    
    print(f"🧪 Total Tests: {total_tests}")
    print(f"❌ Tests with Issues: {tests_with_issues}")
    print(f"✅ Success Rate: {((total_tests - tests_with_issues) / total_tests * 100):.1f}%")
    
    # Group issues by type
    issue_types = {}
    for result in all_results:
        for issue in result.get('issues', []):
            issue_type = issue.split(':')[0].strip()
            if issue_type not in issue_types:
                issue_types[issue_type] = []
            issue_types[issue_type].append({
                'scenario': result['scenario'],
                'user': result.get('user_name', 'unknown'),
                'issue': issue
            })
    
    if issue_types:
        print(f"\n🔍 ISSUE BREAKDOWN:")
        for issue_type, occurrences in issue_types.items():
            print(f"\n  {issue_type}: {len(occurrences)} occurrences")
            for occ in occurrences[:3]:  # Show first 3 examples
                print(f"    - {occ['scenario']} ({occ['user']})")
    
    # Save detailed results
    with open('/tmp/comprehensive_adhd_test_results.json', 'w') as f:
        json.dump(all_results, f, indent=2, default=str)
    
    print(f"\n💾 Detailed results saved to /tmp/comprehensive_adhd_test_results.json")
    print(f"🕒 Completed at: {datetime.now().isoformat()}")


if __name__ == '__main__':
    asyncio.run(main())
