
# MCP Browser Testing Guide for Catalog Management

## 🎯 Purpose
This guide demonstrates how to leverage MCP server tools with <PERSON><PERSON> 
for comfortable, efficient testing of admin interfaces.

## 🔧 Setup Requirements
1. Django server running on localhost:8000
2. Admin user credentials: admin/admin123
3. MCP browser tools available
4. <PERSON><PERSON> properly installed

## 🚀 Testing Pattern

### Step 1: Initial Navigation
```python
browser_navigate_Playwright(url='http://localhost:8000/admin/login/')
browser_snapshot_Playwright()  # Verify login page loaded
```

### Step 2: Authentication
```python
browser_type_Playwright(element='username field', ref='id_username', text='admin')
browser_type_Playwright(element='password field', ref='id_password', text='admin123')
browser_click_Playwright(element='login button', ref='submit')
```

### Step 3: Navigate to Target Page
```python
browser_navigate_Playwright(url='http://localhost:8000/admin/commands/')
browser_snapshot_Playwright()  # Verify catalog page loaded
```

### Step 4: Test Interactive Elements
```python
# Test catalog viewing
browser_click_Playwright(element='View Catalog button', ref='view-catalog-btn')
browser_snapshot_Playwright()  # Verify modal appeared

# Test command execution
browser_click_Playwright(element='Execute Command button', ref='execute-btn')
browser_snapshot_Playwright()  # Verify command executed
```

### Step 5: Verify Database Changes
```python
# After command execution, verify database state
# Use Django ORM to check if changes were applied
```

## 🎯 Key Benefits of MCP Browser Tools

1. **Comfortable Layer**: No need to handle low-level Playwright details
2. **Visual Feedback**: Snapshots show exactly what's happening
3. **Error Handling**: Built-in error handling and reporting
4. **Interactive Testing**: Can test complex user interactions
5. **Database Integration**: Can verify backend changes

## 🔍 Troubleshooting

### JavaScript Errors
- Use browser_console_messages_Playwright() to see console output
- Check browser_snapshot_Playwright() for visual errors

### Modal Issues
- Use browser_handle_dialog_Playwright() for alert dialogs
- Check element references with browser_snapshot_Playwright()

### Static File Issues
- Verify files load with browser_network_requests_Playwright()
- Check console for 404 errors

## ✅ Success Criteria

1. Page loads without errors
2. JavaScript functions work correctly
3. Modals appear and function properly
4. Commands execute and affect database
5. User experience is smooth and intuitive

