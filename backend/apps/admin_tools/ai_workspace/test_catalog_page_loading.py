#!/usr/bin/env python3
"""
Direct Playwright testing for catalog management page loading and functionality.
This script tests the actual page loading, identifies errors, and validates features.

Usage:
    docker exec -it backend-web-1 python /usr/src/app/apps/admin_tools/ai_workspace/test_catalog_page_loading.py
"""

import os
import sys
import asyncio
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, '/usr/src/app')

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')

import django
django.setup()

try:
    from playwright.async_api import async_playwright
except ImportError:
    print("❌ Playwright not installed. Installing...")
    os.system("pip install playwright")
    os.system("playwright install chromium")
    from playwright.async_api import async_playwright


class CatalogPageTester:
    """Direct testing of catalog management page with <PERSON><PERSON>."""
    
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.admin_username = "admin"
        self.admin_password = "admin123"
        self.errors_found = []
        self.tests_passed = 0
        self.tests_failed = 0
    
    async def setup_browser(self):
        """Set up browser and login."""
        self.playwright = await async_playwright().start()
        self.browser = await self.playwright.chromium.launch(headless=True)
        self.context = await self.browser.new_context()
        self.page = await self.context.new_page()
        
        # Enable console logging to catch JavaScript errors
        self.page.on("console", lambda msg: print(f"🖥️  Console: {msg.text}"))
        self.page.on("pageerror", lambda error: self.errors_found.append(f"Page Error: {error}"))
        
        print("🔧 Setting up browser and logging in...")
        
        # Login to admin
        await self.page.goto(f"{self.base_url}/admin/login/")
        await self.page.fill('input[name="username"]', self.admin_username)
        await self.page.fill('input[name="password"]', self.admin_password)
        await self.page.click('input[type="submit"]')
        
        # Wait for login to complete
        await self.page.wait_for_url(f"{self.base_url}/admin/")
        print("✅ Successfully logged in to admin")
    
    async def test_catalog_page_loading(self):
        """Test if the catalog management page loads without errors."""
        print("\n📋 Testing catalog management page loading...")
        
        try:
            # Navigate to catalog management page
            await self.page.goto(f"{self.base_url}/admin/commands/")
            
            # Wait for page to load and check for errors
            await self.page.wait_for_load_state('networkidle')
            
            # Check if page loaded successfully
            title = await self.page.title()
            print(f"📄 Page title: {title}")
            
            # Check for Django error pages
            error_indicators = [
                "TemplateDoesNotExist",
                "NoReverseMatch", 
                "AttributeError",
                "ImportError",
                "500 Internal Server Error",
                "404 Not Found"
            ]
            
            page_content = await self.page.content()
            
            for error in error_indicators:
                if error in page_content:
                    self.errors_found.append(f"Django Error: {error} found in page")
                    self.tests_failed += 1
                    print(f"❌ Found Django error: {error}")
                    return False
            
            # Check if our enhanced elements are present
            enhanced_elements = [
                ".catalog-management-container",
                ".catalog-header", 
                ".catalog-stats",
                ".catalog-status-overview",
                ".commands-section"
            ]
            
            for selector in enhanced_elements:
                element = await self.page.query_selector(selector)
                if element:
                    print(f"✅ Found element: {selector}")
                    self.tests_passed += 1
                else:
                    print(f"❌ Missing element: {selector}")
                    self.tests_failed += 1
                    self.errors_found.append(f"Missing element: {selector}")
            
            return len(self.errors_found) == 0
            
        except Exception as e:
            print(f"❌ Error loading page: {e}")
            self.errors_found.append(f"Page loading error: {e}")
            self.tests_failed += 1
            return False
    
    async def test_static_files_loading(self):
        """Test if CSS and JS files are loading correctly."""
        print("\n🎨 Testing static files loading...")
        
        try:
            # Check for CSS files
            css_files = [
                "/static/admin_tools/css/pages/catalog_management.css",
                "/static/admin_tools/css/components/catalog_viewer.css"
            ]
            
            for css_file in css_files:
                response = await self.page.goto(f"{self.base_url}{css_file}")
                if response.status == 200:
                    print(f"✅ CSS file loaded: {css_file}")
                    self.tests_passed += 1
                else:
                    print(f"❌ CSS file failed to load: {css_file} (Status: {response.status})")
                    self.tests_failed += 1
                    self.errors_found.append(f"CSS file not found: {css_file}")
            
            # Check for JS files
            js_files = [
                "/static/admin_tools/js/pages/catalog_management.js",
                "/static/admin_tools/js/modules/catalog_viewer.js"
            ]
            
            for js_file in js_files:
                response = await self.page.goto(f"{self.base_url}{js_file}")
                if response.status == 200:
                    print(f"✅ JS file loaded: {js_file}")
                    self.tests_passed += 1
                else:
                    print(f"❌ JS file failed to load: {js_file} (Status: {response.status})")
                    self.tests_failed += 1
                    self.errors_found.append(f"JS file not found: {js_file}")
            
            # Return to catalog page
            await self.page.goto(f"{self.base_url}/admin/commands/")
            
        except Exception as e:
            print(f"❌ Error testing static files: {e}")
            self.errors_found.append(f"Static files error: {e}")
            self.tests_failed += 1
    
    async def test_command_buttons(self):
        """Test if command execution buttons are clickable."""
        print("\n🚀 Testing command buttons...")
        
        try:
            # Look for execute buttons
            execute_buttons = await self.page.query_selector_all(".execute-btn")
            
            if execute_buttons:
                print(f"✅ Found {len(execute_buttons)} execute buttons")
                self.tests_passed += 1
                
                # Test clicking the first button (without actually executing)
                first_button = execute_buttons[0]
                button_text = await first_button.text_content()
                print(f"🔘 Testing button: {button_text}")
                
                # Check if button is clickable
                is_enabled = await first_button.is_enabled()
                if is_enabled:
                    print("✅ Button is enabled and clickable")
                    self.tests_passed += 1
                else:
                    print("❌ Button is disabled")
                    self.tests_failed += 1
                    self.errors_found.append("Execute button is disabled")
            else:
                print("❌ No execute buttons found")
                self.tests_failed += 1
                self.errors_found.append("No execute buttons found")
                
        except Exception as e:
            print(f"❌ Error testing command buttons: {e}")
            self.errors_found.append(f"Command buttons error: {e}")
            self.tests_failed += 1
    
    async def test_catalog_view_buttons(self):
        """Test if catalog view buttons work."""
        print("\n👁️ Testing catalog view buttons...")
        
        try:
            # Look for view catalog buttons
            view_buttons = await self.page.query_selector_all(".view-catalog-btn")
            
            if view_buttons:
                print(f"✅ Found {len(view_buttons)} view catalog buttons")
                self.tests_passed += 1
                
                # Test clicking the first view button
                first_button = view_buttons[0]
                button_text = await first_button.text_content()
                print(f"🔘 Testing view button: {button_text}")
                
                # Click the button and check for modal
                await first_button.click()
                
                # Wait a moment for modal to appear
                await self.page.wait_for_timeout(1000)
                
                # Check if modal appeared
                modal = await self.page.query_selector(".catalog-modal")
                if modal:
                    print("✅ Catalog modal appeared")
                    self.tests_passed += 1
                    
                    # Check if modal is visible
                    is_visible = await modal.is_visible()
                    if is_visible:
                        print("✅ Modal is visible")
                        self.tests_passed += 1
                    else:
                        print("❌ Modal is not visible")
                        self.tests_failed += 1
                        self.errors_found.append("Modal not visible")
                else:
                    print("❌ Catalog modal did not appear")
                    self.tests_failed += 1
                    self.errors_found.append("Catalog modal not appearing")
            else:
                print("❌ No view catalog buttons found")
                self.tests_failed += 1
                self.errors_found.append("No view catalog buttons found")
                
        except Exception as e:
            print(f"❌ Error testing catalog view buttons: {e}")
            self.errors_found.append(f"Catalog view buttons error: {e}")
            self.tests_failed += 1
    
    async def cleanup(self):
        """Clean up browser resources."""
        try:
            if hasattr(self, 'browser') and self.browser:
                await self.browser.close()
            if hasattr(self, 'playwright') and self.playwright:
                await self.playwright.stop()
        except Exception as e:
            print(f"⚠️ Cleanup error (non-critical): {e}")
    
    async def run_all_tests(self):
        """Run all tests and generate report."""
        print("🧪 STARTING DIRECT CATALOG PAGE TESTING")
        print("=" * 60)
        
        try:
            await self.setup_browser()
            
            # Run tests in sequence
            page_loaded = await self.test_catalog_page_loading()
            
            if page_loaded:
                await self.test_static_files_loading()
                await self.test_command_buttons()
                await self.test_catalog_view_buttons()
            else:
                print("⚠️ Skipping further tests due to page loading failure")
            
            # Generate report
            self.generate_report()
            
        except Exception as e:
            print(f"❌ Critical error during testing: {e}")
            self.errors_found.append(f"Critical error: {e}")
        finally:
            await self.cleanup()
    
    def generate_report(self):
        """Generate test report with findings."""
        print("\n" + "=" * 60)
        print("📊 CATALOG PAGE TESTING REPORT")
        print("=" * 60)
        
        total_tests = self.tests_passed + self.tests_failed
        success_rate = (self.tests_passed / total_tests * 100) if total_tests > 0 else 0
        
        print(f"✅ Tests Passed: {self.tests_passed}")
        print(f"❌ Tests Failed: {self.tests_failed}")
        print(f"📈 Success Rate: {success_rate:.1f}%")
        
        if self.errors_found:
            print("\n🔍 ERRORS FOUND:")
            for i, error in enumerate(self.errors_found, 1):
                print(f"  {i}. {error}")
        
        print("\n🎯 NEXT ACTIONS NEEDED:")
        if success_rate < 50:
            print("  🚨 CRITICAL: Major issues found. Page likely not loading.")
            print("     - Check Django template syntax")
            print("     - Verify static files exist")
            print("     - Check URL routing")
        elif success_rate < 80:
            print("  🔧 MODERATE: Some features not working.")
            print("     - Fix missing static files")
            print("     - Debug JavaScript functionality")
            print("     - Test modal interactions")
        else:
            print("  ✅ GOOD: Most features working. Minor fixes needed.")


async def main():
    """Main test execution."""
    tester = CatalogPageTester()
    await tester.run_all_tests()


if __name__ == '__main__':
    asyncio.run(main())
