# Generated by Django 5.2.1 on 2025-06-28 17:02

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ImportHistory',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('import_type', models.CharField(choices=[('single', 'Single Profile Import'), ('batch', 'Batch Profile Import'), ('validation_only', 'Validation Only')], default='single', help_text='Type of import operation', max_length=50)),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('failed', 'Failed'), ('partial', 'Partially Completed')], default='pending', help_text='Current status of the import operation', max_length=20)),
                ('total_profiles', models.IntegerField(default=1, help_text='Total number of profiles to import')),
                ('successful_imports', models.IntegerField(default=0, help_text='Number of successfully imported profiles')),
                ('failed_imports', models.IntegerField(default=0, help_text='Number of failed profile imports')),
                ('errors', models.JSONField(default=list, help_text='List of errors encountered during import')),
                ('warnings', models.JSONField(default=list, help_text='List of warnings generated during import')),
                ('processing_time_seconds', models.FloatField(blank=True, help_text='Total processing time in seconds', null=True)),
                ('validation_time_seconds', models.FloatField(blank=True, help_text='Time spent on validation in seconds', null=True)),
                ('database_time_seconds', models.FloatField(blank=True, help_text='Time spent on database operations in seconds', null=True)),
                ('import_options', models.JSONField(default=dict, help_text='Options used for the import operation')),
                ('source_metadata', models.JSONField(default=dict, help_text='Metadata about the import source (file info, etc.)')),
                ('created_profiles', models.JSONField(default=list, help_text='List of profile IDs that were created')),
                ('updated_profiles', models.JSONField(default=list, help_text='List of profile IDs that were updated')),
                ('data_quality_score', models.FloatField(blank=True, help_text='Overall data quality score (0.0-1.0)', null=True)),
                ('completeness_score', models.FloatField(blank=True, help_text='Profile completeness score (0.0-1.0)', null=True)),
                ('notes', models.TextField(blank=True, help_text='Additional notes about the import operation')),
                ('initiated_by', models.ForeignKey(blank=True, help_text='Admin user who initiated the import', null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Import History',
                'verbose_name_plural': 'Import Histories',
                'ordering': ['-started_at'],
                'indexes': [models.Index(fields=['status'], name='admin_tools_status_7acbdd_idx'), models.Index(fields=['import_type'], name='admin_tools_import__38a491_idx'), models.Index(fields=['started_at'], name='admin_tools_started_eaecfe_idx'), models.Index(fields=['initiated_by'], name='admin_tools_initiat_14b4a4_idx')],
            },
        ),
    ]
