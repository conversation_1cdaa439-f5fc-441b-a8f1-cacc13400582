import json
import logging
import os
from datetime import datetime
from pathlib import Path
from typing import Dict, Any
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from rest_framework.response import Response
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils.decorators import method_decorator
from django.views import View
from django.conf import settings

from apps.user.serializers.import_serializers import (
    UserProfileImportRequestSerializer, AIGenerateRequestSerializer,
    ImportResponseSerializer, AIGenerateResponseSerializer, ErrorResponseSerializer
)
from apps.user.services.profile_import_service import ProfileImportService, ProfileImportError
from apps.user.services.ai_profile_generator import AIProfileGenerator, AIProfileGenerationError
from apps.user.services.profile_validation_service import validate_profile_import

logger = logging.getLogger(__name__)


@method_decorator(csrf_exempt, name='dispatch')
class ProfileImportView(View):
    """Handle user profile import requests"""
    
    def post(self, request):
        """Import a complete user profile from JSON data"""
        if not request.user.is_authenticated or not request.user.is_staff:
            return JsonResponse({'error': 'Authentication required'}, status=401)

        try:
            # Parse request data
            data = json.loads(request.body)

            # Enhanced validation with detailed feedback
            validation_result = validate_profile_import(data)

            if not validation_result.is_valid:
                return JsonResponse({
                    'success': False,
                    'error': 'Validation failed',
                    'validation_errors': validation_result.errors,
                    'field_errors': validation_result.field_errors,
                    'warnings': validation_result.warnings,
                    'summary': validation_result.summary
                }, status=400)

            # If there are warnings but validation passed, include them in response
            if validation_result.warnings:
                logger.info(f"Profile import has {len(validation_result.warnings)} warnings")

            # Validate with serializer (redundant but ensures compatibility)
            serializer = UserProfileImportRequestSerializer(data=data)
            if not serializer.is_valid():
                return JsonResponse({
                    'success': False,
                    'error': 'Serializer validation failed',
                    'validation_errors': serializer.errors
                }, status=400)

            # Extract validated data
            profile_data = serializer.validated_data['profile_data']
            options = serializer.validated_data.get('options', {})

            # Import the profile
            import_service = ProfileImportService()
            result = import_service.import_profile(profile_data, options)

            # Include validation warnings in successful response
            if validation_result.warnings:
                result['validation_warnings'] = validation_result.warnings
                result['validation_summary'] = validation_result.summary

            return JsonResponse(result)

        except json.JSONDecodeError:
            return JsonResponse({
                'success': False,
                'error': 'Invalid JSON data',
                'details': 'The request body must contain valid JSON'
            }, status=400)
        except ProfileImportError as e:
            return JsonResponse({
                'success': False,
                'error': str(e),
                'error_type': 'profile_import_error'
            }, status=400)
        except Exception as e:
            logger.error(f"Unexpected error in profile import: {e}")
            return JsonResponse({
                'success': False,
                'error': 'Internal server error',
                'error_type': 'system_error'
            }, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class AIProfileGenerateView(View):
    """Handle AI-powered profile generation from questionnaire data"""
    
    def post(self, request):
        """Generate a user profile from questionnaire responses using AI"""
        if not request.user.is_authenticated or not request.user.is_staff:
            return JsonResponse({'error': 'Authentication required'}, status=401)
        
        try:
            # Parse request data
            data = json.loads(request.body)
            
            # Validate request
            serializer = AIGenerateRequestSerializer(data=data)
            if not serializer.is_valid():
                return JsonResponse({
                    'success': False,
                    'error': 'Validation failed',
                    'validation_errors': serializer.errors
                }, status=400)
            
            # Extract validated data
            questionnaire_data = serializer.validated_data['questionnaire_data']
            options = serializer.validated_data.get('options', {})
            
            # Generate the profile
            ai_generator = AIProfileGenerator()
            result = ai_generator.generate_profile(questionnaire_data, options)
            
            return JsonResponse(result)
            
        except json.JSONDecodeError:
            return JsonResponse({
                'success': False,
                'error': 'Invalid JSON data'
            }, status=400)
        except AIProfileGenerationError as e:
            return JsonResponse({
                'success': False,
                'error': str(e)
            }, status=400)
        except Exception as e:
            logger.error(f"Unexpected error in AI profile generation: {e}")
            return JsonResponse({
                'success': False,
                'error': 'Internal server error'
            }, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class ProfileValidationView(View):
    """Handle profile validation without importing"""

    def post(self, request):
        """Validate profile data without importing"""
        if not request.user.is_authenticated or not request.user.is_staff:
            return JsonResponse({'error': 'Authentication required'}, status=401)

        try:
            # Parse request data
            data = json.loads(request.body)

            # Enhanced validation with detailed feedback
            validation_result = validate_profile_import(data)

            return JsonResponse({
                'success': True,
                'validation_result': validation_result.to_dict()
            })

        except json.JSONDecodeError:
            return JsonResponse({
                'success': False,
                'error': 'Invalid JSON data',
                'details': 'The request body must contain valid JSON'
            }, status=400)
        except Exception as e:
            logger.error(f"Unexpected error in profile validation: {e}")
            return JsonResponse({
                'success': False,
                'error': 'Internal server error',
                'error_type': 'system_error'
            }, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class ProfileSchemaValidationView(View):
    """Handle schema validation and coverage analysis"""

    def post(self, request):
        """Run schema coverage analysis"""
        if not request.user.is_authenticated or not request.user.is_staff:
            return JsonResponse({'error': 'Authentication required'}, status=401)

        try:
            # Parse request data
            data = json.loads(request.body)
            action = data.get('action', 'analyze_coverage')

            if action == 'analyze_coverage':
                # Run the schema coverage analysis
                import sys
                import os
                sys.path.append(os.path.join(settings.BASE_DIR, 'scripts'))
                from analyze_schema_coverage import SchemaCoverageAnalyzer

                analyzer = SchemaCoverageAnalyzer()
                analyzer.analyze_coverage()

                # Convert results to JSON-serializable format
                total_fields = len(analyzer.covered_fields) + len(analyzer.missing_fields)
                coverage_percentage = (len(analyzer.covered_fields) / total_fields * 100) if total_fields > 0 else 0

                return JsonResponse({
                    'success': True,
                    'coverage_analysis': {
                        'coverage_percentage': coverage_percentage,
                        'covered_fields': len(analyzer.covered_fields),
                        'missing_fields': len(analyzer.missing_fields),
                        'total_fields': total_fields
                    },
                    'missing_fields': analyzer.missing_fields,
                    'recommendations': analyzer.recommendations
                })
            else:
                return JsonResponse({
                    'error': f'Unknown action: {action}'
                }, status=400)

        except json.JSONDecodeError:
            return JsonResponse({
                'error': 'Invalid JSON data'
            }, status=400)
        except Exception as e:
            logger.error(f"Error in schema validation: {e}")
            return JsonResponse({
                'error': 'Schema validation failed',
                'details': str(e)
            }, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class ProfileSchemaView(View):
    """Provide the user profile import schema"""
    
    def get(self, request):
        """Return the OpenAPI schema for user profile import"""
        try:
            # Get the schema file path relative to the project root
            schema_path = Path(settings.BASE_DIR) / 'schemas' / 'user_profile_import_schema.json'

            # Load the schema file
            with open(schema_path, 'r') as f:
                schema = json.load(f)

            return JsonResponse(schema)

        except FileNotFoundError:
            return JsonResponse({
                'error': 'Schema file not found'
            }, status=404)
        except Exception as e:
            logger.error(f"Error serving schema: {e}")
            return JsonResponse({
                'error': 'Error loading schema'
            }, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class BatchProfileExportView(View):
    """Handle batch export of user profiles"""
    
    def post(self, request):
        """Export selected user profiles as JSON"""
        if not request.user.is_authenticated or not request.user.is_staff:
            return JsonResponse({'error': 'Authentication required'}, status=401)
        
        try:
            # Parse request data
            data = json.loads(request.body)
            profile_ids = data.get('profile_ids', [])
            
            if not profile_ids:
                return JsonResponse({
                    'error': 'No profile IDs provided'
                }, status=400)
            
            # Export profiles (this would be implemented based on your needs)
            # For now, return a placeholder response
            exported_data = self._export_profiles(profile_ids)
            
            response = HttpResponse(
                json.dumps(exported_data, indent=2),
                content_type='application/json'
            )
            response['Content-Disposition'] = f'attachment; filename=\"user_profiles_export.json\"'
            
            return response
            
        except json.JSONDecodeError:
            return JsonResponse({
                'error': 'Invalid JSON data'
            }, status=400)
        except Exception as e:
            logger.error(f"Error in batch export: {e}")
            return JsonResponse({
                'error': 'Export failed'
            }, status=500)
    
    def _export_profiles(self, profile_ids):
        """Export profiles to structured data using comprehensive export service"""
        from apps.user.services.profile_export_service import export_profiles_to_json

        try:
            # Use the comprehensive export service
            export_data = export_profiles_to_json(profile_ids)

            # Add export format information
            export_data['export_format'] = 'complete_user_profile_json'
            export_data['schema_compliance'] = 'user_profile_import_schema_v1.0.0'

            return export_data

        except Exception as e:
            logger.error(f"Error in comprehensive export: {e}")
            # Fallback to basic export if comprehensive export fails
            from apps.user.models import UserProfile

            profiles = UserProfile.objects.filter(id__in=profile_ids)
            exported_profiles = []

            for profile in profiles:
                exported_profiles.append({
                    'profile_id': profile.id,
                    'profile_name': profile.profile_name,
                    'username': profile.user.username,
                    'is_real': profile.is_real,
                    'export_error': 'Comprehensive export failed, using basic export'
                })

            return {
                'export_metadata': {
                    'exported_at': datetime.now().isoformat(),
                    'export_format': 'basic_profile_json',
                    'total_profiles': len(profile_ids),
                    'successfully_exported': len(exported_profiles),
                    'warnings': [f'Comprehensive export failed: {str(e)}']
                },
                'profiles': exported_profiles
            }


@method_decorator(csrf_exempt, name='dispatch')
class BatchProfileDeleteView(View):
    """Handle batch deletion of user profiles"""
    
    def post(self, request):
        """Delete selected user profiles"""
        if not request.user.is_authenticated or not request.user.is_staff:
            return JsonResponse({'error': 'Authentication required'}, status=401)
        
        try:
            # Parse request data
            data = json.loads(request.body)
            profile_ids = data.get('profile_ids', [])
            
            if not profile_ids:
                return JsonResponse({
                    'error': 'No profile IDs provided'
                }, status=400)
            
            # Delete profiles
            from apps.user.models import UserProfile
            deleted_count = UserProfile.objects.filter(id__in=profile_ids).delete()[0]
            
            return JsonResponse({
                'success': True,
                'deleted_count': deleted_count
            })
            
        except json.JSONDecodeError:
            return JsonResponse({
                'error': 'Invalid JSON data'
            }, status=400)
        except Exception as e:
            logger.error(f"Error in batch delete: {e}")
            return JsonResponse({
                'error': 'Delete failed'
            }, status=500)


# Function-based views for compatibility
@api_view(['POST'])
@permission_classes([IsAdminUser])
def import_user_profile(request):
    """Function-based view wrapper for profile import"""
    view = ProfileImportView()
    return view.post(request)


@api_view(['POST'])
@permission_classes([IsAdminUser])
def ai_generate_profile(request):
    """Function-based view wrapper for AI profile generation"""
    view = AIProfileGenerateView()
    return view.post(request)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_profile_schema(request):
    """Function-based view wrapper for schema retrieval"""
    view = ProfileSchemaView()
    return view.get(request)


@api_view(['POST'])
@permission_classes([IsAdminUser])
def batch_export_profiles(request):
    """Function-based view wrapper for batch export"""
    view = BatchProfileExportView()
    return view.post(request)


@api_view(['POST'])
@permission_classes([IsAdminUser])
def batch_delete_profiles(request):
    """Function-based view wrapper for batch delete"""
    view = BatchProfileDeleteView()
    return view.post(request)
