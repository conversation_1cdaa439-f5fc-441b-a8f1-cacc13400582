"""
Comprehensive tests for user profile import system

Tests cover business objects, schema validation, import service, and integration scenarios.
"""

import json
import pytest
from datetime import date, timedelta
from django.test import TestCase
from django.contrib.auth.models import User
from django.db import transaction
from unittest.mock import patch, mock_open

from apps.user.services.profile_import_service import (
    ProfileImportService,
    SchemaValidationError,
    ReferenceValidationError,
    ConsistencyValidationError,
    ProfileImportError
)
from apps.user.services.user_profile_business_objects import (
    UserProfileImportRequestBO,
    UserAccountBO,
    DemographicsBO,
    UserEnvironmentBO,
    TraitInclinationBO,
    BeliefBO,
    AspirationBO,
    IntentionBO,
    InspirationBO,
    SkillBO,
    ResourceBO,
    LimitationBO,
    PreferenceBO,
    CurrentMoodBO,
    TrustLevelBO
)
from apps.user.models import (
    UserProfile, Demographics, UserEnvironment, UserTraitInclination,
    Belief, Aspiration, Intention, Inspiration, Skill, UserResource,
    UserLimitation, Preference, CurrentMood, TrustLevel,
    GenericTrait, GenericSkill, GenericResource, GenericUserLimitation,
    GenericEnvironment
)


class TestBusinessObjects(TestCase):
    """Test Pydantic business object validation"""
    
    def test_user_account_validation(self):
        """Test UserAccountBO validation"""
        # Valid data
        valid_data = {
            'username': 'testuser123',
            'email': '<EMAIL>',
            'password': 'securepassword123'
        }
        user_account = UserAccountBO(**valid_data)
        self.assertEqual(user_account.username, 'testuser123')
        self.assertEqual(user_account.email, '<EMAIL>')
        
        # Invalid username (special characters)
        with self.assertRaises(ValueError):
            UserAccountBO(username='test@user', email='<EMAIL>')
        
        # Invalid email
        with self.assertRaises(ValueError):
            UserAccountBO(username='testuser', email='invalid-email')
        
        # Password too short
        with self.assertRaises(ValueError):
            UserAccountBO(username='testuser', email='<EMAIL>', password='short')
    
    def test_demographics_validation(self):
        """Test DemographicsBO validation"""
        # Valid data
        valid_data = {
            'full_name': 'John Doe',
            'age': 30,
            'gender': 'Male',
            'location': 'New York, NY',
            'language': 'English',
            'occupation': 'Software Engineer'
        }
        demographics = DemographicsBO(**valid_data)
        self.assertEqual(demographics.age, 30)
        
        # Age too young
        with self.assertRaises(ValueError):
            DemographicsBO(**{**valid_data, 'age': 12})
        
        # Age too old
        with self.assertRaises(ValueError):
            DemographicsBO(**{**valid_data, 'age': 150})
    
    def test_trait_inclination_validation(self):
        """Test TraitInclinationBO validation"""
        # Valid data
        valid_data = {
            'trait_code': 'honesty_sincerity',
            'strength': 75,
            'awareness': 80
        }
        trait = TraitInclinationBO(**valid_data)
        self.assertEqual(trait.strength, 75)
        
        # Strength out of range
        with self.assertRaises(ValueError):
            TraitInclinationBO(**{**valid_data, 'strength': 150})
        
        # Awareness out of range
        with self.assertRaises(ValueError):
            TraitInclinationBO(**{**valid_data, 'awareness': -10})
    
    def test_environment_date_validation(self):
        """Test UserEnvironmentBO date validation"""
        today = date.today()
        future_date = today + timedelta(days=30)
        
        # Valid date range
        valid_data = {
            'environment_name': 'Home Office',
            'environment_description': 'My home workspace',
            'is_current': True,
            'effective_start': today.isoformat(),
            'effective_end': future_date.isoformat()
        }
        environment = UserEnvironmentBO(**valid_data)
        self.assertEqual(environment.effective_start, today.isoformat())
        
        # Invalid date range (end before start)
        with self.assertRaises(ValueError):
            UserEnvironmentBO(**{
                **valid_data,
                'effective_start': future_date.isoformat(),
                'effective_end': today.isoformat()
            })
    
    def test_trust_level_domain_validation(self):
        """Test TrustLevelBO domain score validation"""
        # Valid trust level
        valid_data = {
            'value': 80.0,
            'domain_scores': {
                'career': 75.0,
                'health': 80.0,
                'relationships': 70.0
            }
        }
        trust = TrustLevelBO(**valid_data)
        self.assertEqual(trust.value, 80.0)
        
        # Invalid domain score (exceeds overall)
        with self.assertRaises(ValueError):
            TrustLevelBO(**{
                **valid_data,
                'domain_scores': {'career': 90.0}  # Exceeds overall value of 80
            })
    
    def test_complete_profile_validation(self):
        """Test complete UserProfileImportRequestBO validation"""
        # Create minimal valid profile
        minimal_profile = {
            'user_account': {
                'username': 'testuser',
                'email': '<EMAIL>'
            },
            'profile_name': 'Test Profile',
            'demographics': {
                'full_name': 'Test User',
                'age': 25,
                'gender': 'Non-binary',
                'location': 'Test City',
                'language': 'English',
                'occupation': 'Tester'
            }
        }
        
        profile = UserProfileImportRequestBO(**minimal_profile)
        self.assertEqual(profile.profile_name, 'Test Profile')
        self.assertEqual(profile.demographics.age, 25)
        
        # Test with too many traits (should raise validation error)
        many_traits = [
            {'trait_code': f'trait_{i}', 'strength': 50, 'awareness': 50}
            for i in range(30)  # More than 24 recommended
        ]
        
        with self.assertRaises(ValueError):
            UserProfileImportRequestBO(**{
                **minimal_profile,
                'traits': many_traits
            })


class TestSchemaValidation(TestCase):
    """Test JSON schema validation"""
    
    def setUp(self):
        self.import_service = ProfileImportService()
        
        # Mock schema file
        self.mock_schema = {
            "type": "object",
            "required": ["user_account", "profile_name", "demographics"],
            "properties": {
                "user_account": {"type": "object"},
                "profile_name": {"type": "string"},
                "demographics": {"type": "object"}
            }
        }
    
    @patch('builtins.open', mock_open(read_data='{"type": "object"}'))
    @patch('json.load')
    def test_schema_loading(self, mock_json_load):
        """Test schema file loading"""
        mock_json_load.return_value = self.mock_schema
        
        schema = self.import_service._load_json_schema()
        self.assertEqual(schema['type'], 'object')
    
    @patch.object(ProfileImportService, '_load_json_schema')
    def test_valid_schema_validation(self, mock_load_schema):
        """Test validation with valid data"""
        mock_load_schema.return_value = self.mock_schema
        
        valid_data = {
            'user_account': {'username': 'test'},
            'profile_name': 'Test Profile',
            'demographics': {'age': 25}
        }
        
        is_valid, errors = self.import_service.validate_json_schema(valid_data)
        self.assertTrue(is_valid)
        self.assertEqual(len(errors), 0)
    
    @patch.object(ProfileImportService, '_load_json_schema')
    def test_invalid_schema_validation(self, mock_load_schema):
        """Test validation with invalid data"""
        mock_load_schema.return_value = self.mock_schema
        
        invalid_data = {
            'user_account': {'username': 'test'},
            # Missing required 'profile_name' and 'demographics'
        }
        
        is_valid, errors = self.import_service.validate_json_schema(invalid_data)
        self.assertFalse(is_valid)
        self.assertGreater(len(errors), 0)


class TestImportService(TestCase):
    """Test import service methods"""
    
    def setUp(self):
        self.import_service = ProfileImportService()
        
        # Create test generic entities
        self.generic_trait = GenericTrait.objects.create(
            code='test_trait',
            description='Test trait'
        )
        self.generic_skill = GenericSkill.objects.create(
            code='test_skill',
            description='Test skill'
        )
        self.generic_resource = GenericResource.objects.create(
            code='test_resource',
            description='Test resource'
        )
        self.generic_limitation = GenericUserLimitation.objects.create(
            code='test_limitation',
            description='Test limitation',
            limitation_type='GENERAL'
        )
        self.generic_environment = GenericEnvironment.objects.create(
            code='test_environment',
            description='Test environment'
        )
    
    def test_reference_code_validation_valid(self):
        """Test reference code validation with valid codes"""
        from apps.user.services.user_profile_business_objects import UserProfileImportRequestBO
        
        # Create business object with valid reference codes
        bo_data = {
            'user_account': {
                'username': 'testuser',
                'email': '<EMAIL>'
            },
            'profile_name': 'Test Profile',
            'demographics': {
                'full_name': 'Test User',
                'age': 25,
                'gender': 'Test',
                'location': 'Test City',
                'language': 'English',
                'occupation': 'Tester'
            },
            'traits': [{'trait_code': 'test_trait', 'strength': 50, 'awareness': 50}],
            'skills': [{'skill_code': 'test_skill', 'level': 50}],
            'resources': [{'specific_name': 'My Resource', 'generic_resource': 'test_resource'}],
            'limitations': [{'limitation_code': 'test_limitation', 'severity': 50}],
            'environment': {
                'environment_name': 'Test Env',
                'environment_description': 'Test environment',
                'is_current': True,
                'generic_environment_code': 'test_environment'
            }
        }
        
        bo = UserProfileImportRequestBO(**bo_data)
        errors = self.import_service.validate_reference_codes(bo)
        self.assertEqual(len(errors), 0)
    
    def test_reference_code_validation_invalid(self):
        """Test reference code validation with invalid codes"""
        from apps.user.services.user_profile_business_objects import UserProfileImportRequestBO
        
        # Create business object with invalid reference codes
        bo_data = {
            'user_account': {
                'username': 'testuser',
                'email': '<EMAIL>'
            },
            'profile_name': 'Test Profile',
            'demographics': {
                'full_name': 'Test User',
                'age': 25,
                'gender': 'Test',
                'location': 'Test City',
                'language': 'English',
                'occupation': 'Tester'
            },
            'traits': [{'trait_code': 'invalid_trait', 'strength': 50, 'awareness': 50}],
            'skills': [{'skill_code': 'invalid_skill', 'level': 50}]
        }
        
        bo = UserProfileImportRequestBO(**bo_data)
        errors = self.import_service.validate_reference_codes(bo)
        self.assertGreater(len(errors), 0)
        self.assertTrue(any('invalid_trait' in error for error in errors))
        self.assertTrue(any('invalid_skill' in error for error in errors))


class TestIntegration(TestCase):
    """Test end-to-end import scenarios"""

    def setUp(self):
        self.import_service = ProfileImportService()

        # Create test generic entities
        GenericTrait.objects.create(code='honesty_sincerity', description='Honesty-Humility: Sincerity')
        GenericTrait.objects.create(code='extra_sociability', description='Extraversion: Sociability')
        GenericSkill.objects.create(code='communication', description='Communication skills')
        GenericSkill.objects.create(code='coding', description='Programming and coding')
        GenericResource.objects.create(code='laptop', description='Personal laptop computer')
        GenericUserLimitation.objects.create(
            code='time_constraints',
            description='Limited time availability',
            limitation_type='TEMPORAL'
        )
        GenericEnvironment.objects.create(code='home_office', description='Home office environment')

    def test_minimal_profile_import(self):
        """Test importing a minimal valid profile"""
        minimal_profile_data = {
            'user_account': {
                'username': 'minimal_user',
                'email': '<EMAIL>',
                'password': 'securepassword123'
            },
            'profile_name': 'Minimal Test Profile',
            'demographics': {
                'full_name': 'Minimal Test User',
                'age': 28,
                'gender': 'Non-binary',
                'location': 'Test City, TC',
                'language': 'English',
                'occupation': 'Software Tester'
            }
        }

        # Mock schema validation to pass
        with patch.object(self.import_service, 'validate_json_schema', return_value=(True, [])):
            result = self.import_service.import_profile(minimal_profile_data)

        self.assertTrue(result['success'])
        self.assertIn('user_id', result)
        self.assertIn('profile_id', result)

        # Verify user was created
        user = User.objects.get(username='minimal_user')
        self.assertEqual(user.email, '<EMAIL>')

        # Verify profile was created
        profile = UserProfile.objects.get(user=user)
        self.assertEqual(profile.profile_name, 'Minimal Test Profile')

        # Verify demographics were created
        demographics = Demographics.objects.get(user_profile=profile)
        self.assertEqual(demographics.age, 28)

    def test_complete_profile_import(self):
        """Test importing a complete profile with all components"""
        complete_profile_data = {
            'user_account': {
                'username': 'complete_user',
                'email': '<EMAIL>',
                'password': 'securepassword123'
            },
            'profile_name': 'Complete Test Profile',
            'is_real': True,
            'demographics': {
                'full_name': 'Complete Test User',
                'age': 32,
                'gender': 'Female',
                'location': 'Complete City, CC',
                'language': 'English',
                'occupation': 'Product Manager'
            },
            'environment': {
                'environment_name': 'My Home Office',
                'environment_description': 'A dedicated workspace in my home',
                'is_current': True,
                'generic_environment_code': 'home_office',
                'physical_properties': {
                    'rurality': 20,
                    'noise_level': 30,
                    'light_quality': 80
                }
            },
            'traits': [
                {
                    'trait_code': 'honesty_sincerity',
                    'strength': 85,
                    'awareness': 90,
                    'development_notes': 'Strong value for honesty'
                },
                {
                    'trait_code': 'extra_sociability',
                    'strength': 60,
                    'awareness': 75
                }
            ],
            'beliefs': [
                {
                    'belief_statement': 'Technology should serve humanity',
                    'strength': 90,
                    'certainty': 85,
                    'evidence_sources': ['Personal experience', 'Research studies']
                }
            ],
            'aspirations': [
                {
                    'title': 'Lead a tech team',
                    'description': 'Become a technical team leader in a innovative company',
                    'importance': 85,
                    'time_horizon': '3 years'
                }
            ],
            'intentions': [
                {
                    'title': 'Learn Python',
                    'description': 'Complete a comprehensive Python programming course',
                    'target_date': (date.today() + timedelta(days=180)).isoformat(),
                    'commitment_level': 80
                }
            ],
            'skills': [
                {
                    'skill_code': 'communication',
                    'description': 'Strong verbal and written communication',
                    'level': 80,
                    'user_awareness': 85,
                    'user_enjoyment': 90
                }
            ],
            'resources': [
                {
                    'specific_name': 'MacBook Pro',
                    'generic_resource': 'laptop',
                    'condition': 'Excellent',
                    'availability': 'Always available'
                }
            ],
            'limitations': [
                {
                    'limitation_code': 'time_constraints',
                    'description': 'Limited time due to work and family commitments',
                    'severity': 60,
                    'frequency': 'daily'
                }
            ],
            'current_mood': {
                'energy_level': 70,
                'stress_level': 40,
                'optimism': 80,
                'social_engagement': 60
            },
            'trust_level': {
                'value': 75.0,
                'domain_scores': {
                    'career': 70.0,
                    'health': 75.0
                }
            }
        }

        # Mock schema validation to pass
        with patch.object(self.import_service, 'validate_json_schema', return_value=(True, [])):
            result = self.import_service.import_profile(complete_profile_data)

        self.assertTrue(result['success'])

        # Verify all components were created
        user = User.objects.get(username='complete_user')
        profile = UserProfile.objects.get(user=user)

        # Check demographics
        demographics = Demographics.objects.get(user_profile=profile)
        self.assertEqual(demographics.age, 32)

        # Check environment
        environment = UserEnvironment.objects.get(user_profile=profile)
        self.assertEqual(environment.environment_name, 'My Home Office')
        self.assertEqual(profile.current_environment, environment)

        # Check traits
        traits = UserTraitInclination.objects.filter(user_profile=profile)
        self.assertEqual(traits.count(), 2)

        # Check beliefs
        beliefs = Belief.objects.filter(user_profile=profile)
        self.assertEqual(beliefs.count(), 1)

        # Check skills
        skills = Skill.objects.filter(user_profile=profile)
        self.assertEqual(skills.count(), 1)

        # Check current mood
        mood = CurrentMood.objects.get(user_profile=profile)
        self.assertEqual(mood.energy_level, 70)

        # Check trust level
        trust = TrustLevel.objects.get(user_profile=profile)
        self.assertEqual(trust.value, 75.0)

    def test_guigui_profile_compatibility(self):
        """Test importing the guigui.json profile for compatibility"""
        # This would load the actual guigui.json file and test import
        # For now, we'll create a representative sample
        guigui_sample = {
            'user_account': {
                'username': 'guigui_test',
                'email': '<EMAIL>',
                'password': 'testpassword123'
            },
            'profile_name': 'Guillaume Test Profile',
            'demographics': {
                'full_name': 'Guillaume Test',
                'age': 35,
                'gender': 'Male',
                'location': 'Montreal, QC',
                'language': 'French',
                'occupation': 'Software Developer'
            },
            'traits': [
                {
                    'trait_code': 'honesty_sincerity',
                    'strength': 80,
                    'awareness': 85
                }
            ]
        }

        # Mock schema validation to pass
        with patch.object(self.import_service, 'validate_json_schema', return_value=(True, [])):
            result = self.import_service.import_profile(guigui_sample)

        self.assertTrue(result['success'])

        # Verify profile was created successfully
        user = User.objects.get(username='guigui_test')
        profile = UserProfile.objects.get(user=user)
        self.assertEqual(profile.profile_name, 'Guillaume Test Profile')

    def test_error_handling_schema_validation(self):
        """Test error handling for schema validation failures"""
        invalid_data = {
            'user_account': {'username': 'test'},
            # Missing required fields
        }

        # Mock schema validation to fail
        with patch.object(self.import_service, 'validate_json_schema',
                         return_value=(False, ['Missing required field: profile_name'])):
            with self.assertRaises(SchemaValidationError):
                self.import_service.import_profile(invalid_data)

    def test_error_handling_reference_validation(self):
        """Test error handling for reference validation failures"""
        invalid_ref_data = {
            'user_account': {
                'username': 'test_user',
                'email': '<EMAIL>'
            },
            'profile_name': 'Test Profile',
            'demographics': {
                'full_name': 'Test User',
                'age': 25,
                'gender': 'Test',
                'location': 'Test City',
                'language': 'English',
                'occupation': 'Tester'
            },
            'traits': [
                {
                    'trait_code': 'nonexistent_trait',
                    'strength': 50,
                    'awareness': 50
                }
            ]
        }

        # Mock schema validation to pass
        with patch.object(self.import_service, 'validate_json_schema', return_value=(True, [])):
            with self.assertRaises(ReferenceValidationError):
                self.import_service.import_profile(invalid_ref_data)

    def test_transaction_rollback_on_error(self):
        """Test that database transaction rolls back on errors"""
        # Create a profile that will fail during import
        failing_data = {
            'user_account': {
                'username': 'failing_user',
                'email': '<EMAIL>'
            },
            'profile_name': 'Failing Profile',
            'demographics': {
                'full_name': 'Failing User',
                'age': 25,
                'gender': 'Test',
                'location': 'Test City',
                'language': 'English',
                'occupation': 'Tester'
            }
        }

        # Mock schema validation to pass but force an error during import
        with patch.object(self.import_service, 'validate_json_schema', return_value=(True, [])):
            with patch.object(self.import_service, '_import_demographics', side_effect=Exception('Forced error')):
                with self.assertRaises(Exception):
                    self.import_service.import_profile(failing_data)

        # Verify no user was created (transaction rolled back)
        self.assertFalse(User.objects.filter(username='failing_user').exists())
