from django.core.management.base import BaseCommand
from django.db import connection

class Command(BaseCommand):
    help = 'Drops all tables from the database, then runs migrate.'

    def handle(self, *args, **options):
        with connection.cursor() as cursor:
            self.stdout.write(self.style.WARNING('Dropping all tables...'))
            cursor.execute("""
                DO $$ DECLARE
                    r RECORD;
                BEGIN
                    FOR r IN (SELECT tablename FROM pg_tables WHERE schemaname = 'public') LOOP
                        EXECUTE 'DROP TABLE IF EXISTS ' || quote_ident(r.tablename) || ' CASCADE';
                    END LOOP;
                END $$;
            """)
            self.stdout.write(self.style.SUCCESS('All tables dropped.'))

        self.stdout.write(self.style.WARNING('Running migrations...'))
        from django.core.management import call_command
        call_command('migrate')
        self.stdout.write(self.style.SUCCESS('Migrations complete.'))
