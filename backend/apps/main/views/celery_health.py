# backend/apps/main/views/celery_health.py

import json
import time
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from celery.result import AsyncResult
from apps.main.tasks.test_tasks import test_worker_health, test_api_call


@require_http_methods(["GET"])
def celery_health_check(request):
    """
    Simple health check endpoint for Celery workers.
    
    Returns:
        JsonResponse: Status of Celery workers
    """
    try:
        # Submit a simple health check task
        result = test_worker_health.delay()
        
        # Wait a short time for the result
        try:
            task_result = result.get(timeout=10)
            return JsonResponse({
                "status": "healthy",
                "celery_worker": "available",
                "task_id": result.id,
                "worker_response": task_result,
                "timestamp": time.time()
            })
        except Exception as e:
            return JsonResponse({
                "status": "unhealthy",
                "celery_worker": "unavailable",
                "task_id": result.id,
                "error": str(e),
                "timestamp": time.time()
            }, status=503)
            
    except Exception as e:
        return JsonResponse({
            "status": "error",
            "celery_worker": "error",
            "error": str(e),
            "timestamp": time.time()
        }, status=500)


@require_http_methods(["POST"])
@csrf_exempt
def test_celery_task(request):
    """
    Endpoint to test different types of Celery tasks.
    
    POST body should contain:
    {
        "task_type": "health|api|ml|combined",
        "wait": true|false,
        "timeout": 30
    }
    
    Returns:
        JsonResponse: Task submission result and optionally the task result
    """
    try:
        # Parse request body
        try:
            data = json.loads(request.body)
        except json.JSONDecodeError:
            data = {}
        
        task_type = data.get('task_type', 'health')
        wait_for_result = data.get('wait', False)
        timeout = data.get('timeout', 30)
        
        # Submit the appropriate task
        if task_type == 'health':
            result = test_worker_health.delay()
        elif task_type == 'api':
            url = data.get('url', 'https://httpbin.org/json')
            result = test_api_call.delay(url)
        elif task_type == 'ml':
            from apps.main.tasks.test_tasks import test_ml_simulation
            complexity = data.get('complexity', 'simple')
            result = test_ml_simulation.delay(complexity)
        elif task_type == 'combined':
            from apps.main.tasks.test_tasks import test_combined_workload
            url = data.get('url', 'https://httpbin.org/delay/1')
            result = test_combined_workload.delay(url)
        else:
            return JsonResponse({
                "status": "error",
                "error": f"Unknown task type: {task_type}",
                "valid_types": ["health", "api", "ml", "combined"]
            }, status=400)
        
        response_data = {
            "status": "submitted",
            "task_id": result.id,
            "task_type": task_type,
            "timestamp": time.time()
        }
        
        # If wait is requested, wait for the result
        if wait_for_result:
            try:
                task_result = result.get(timeout=timeout)
                response_data.update({
                    "status": "completed",
                    "result": task_result
                })
            except Exception as e:
                response_data.update({
                    "status": "timeout_or_error",
                    "error": str(e),
                    "task_state": result.state
                })
                return JsonResponse(response_data, status=408)
        
        return JsonResponse(response_data)
        
    except Exception as e:
        return JsonResponse({
            "status": "error",
            "error": str(e),
            "timestamp": time.time()
        }, status=500)


@require_http_methods(["GET"])
def check_task_status(request, task_id):
    """
    Check the status of a specific Celery task.
    
    Args:
        task_id: The Celery task ID to check
        
    Returns:
        JsonResponse: Task status and result if available
    """
    try:
        result = AsyncResult(task_id)
        
        response_data = {
            "task_id": task_id,
            "state": result.state,
            "timestamp": time.time()
        }
        
        if result.state == 'SUCCESS':
            response_data["result"] = result.result
        elif result.state == 'FAILURE':
            response_data["error"] = str(result.info)
        elif result.state in ['PENDING', 'STARTED', 'RETRY']:
            response_data["info"] = str(result.info) if result.info else "Task in progress"
        
        return JsonResponse(response_data)
        
    except Exception as e:
        return JsonResponse({
            "task_id": task_id,
            "error": str(e),
            "timestamp": time.time()
        }, status=500)


@require_http_methods(["GET"])
def celery_worker_stats(request):
    """
    Get basic statistics about Celery workers.
    
    Returns:
        JsonResponse: Worker statistics
    """
    try:
        from celery import current_app
        
        # Get active workers
        inspect = current_app.control.inspect()
        
        stats = {
            "timestamp": time.time(),
            "workers": {},
            "queues": {},
            "active_tasks": 0,
            "scheduled_tasks": 0
        }
        
        try:
            # Get worker stats
            worker_stats = inspect.stats()
            if worker_stats:
                stats["workers"] = worker_stats
                
            # Get active tasks
            active_tasks = inspect.active()
            if active_tasks:
                stats["active_tasks"] = sum(len(tasks) for tasks in active_tasks.values())
                
            # Get scheduled tasks
            scheduled_tasks = inspect.scheduled()
            if scheduled_tasks:
                stats["scheduled_tasks"] = sum(len(tasks) for tasks in scheduled_tasks.values())
                
        except Exception as e:
            stats["inspection_error"] = str(e)
        
        return JsonResponse(stats)
        
    except Exception as e:
        return JsonResponse({
            "error": str(e),
            "timestamp": time.time()
        }, status=500)
