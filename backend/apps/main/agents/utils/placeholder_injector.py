"""
Placeholder injection system for agent instructions.

This module provides functionality to inject context variables into agent instruction
templates using the placeholder system defined in AGENT_INSTRUCTION_PLACEHOLDERS.md.
"""

import logging
import re
from datetime import datetime
from typing import Dict, Any, Optional
from django.utils import timezone
from asgiref.sync import sync_to_async
import pytz

logger = logging.getLogger(__name__)


class PlaceholderInjector:
    """
    Handles injection of context variables into instruction templates.
    """
    
    def __init__(self):
        self.placeholder_pattern = re.compile(r'\{\{([A-Z_][A-Z0-9_]*)\}\}')
    
    def inject_placeholders(self, instruction_template: str, context: Dict[str, Any]) -> str:
        """
        Inject context variables into instruction template.
        
        Args:
            instruction_template: Template string with placeholders
            context: Dictionary containing placeholder values
            
        Returns:
            Instruction string with placeholders replaced
        """
        if not instruction_template:
            return instruction_template
            
        def replace_placeholder(match):
            placeholder_name = match.group(1)
            if placeholder_name in context:
                value = context[placeholder_name]
                # Convert to string, handling None values
                if value is None:
                    return "not specified"
                return str(value)
            else:
                logger.warning(f"Undefined placeholder: {placeholder_name}")
                return match.group(0)  # Return original placeholder
        
        result = self.placeholder_pattern.sub(replace_placeholder, instruction_template)
        
        # Log the injection process for debugging
        placeholders_found = self.placeholder_pattern.findall(instruction_template)
        logger.debug(f"Injected {len(placeholders_found)} placeholders: {placeholders_found}")
        
        return result
    
    def build_context(self, user_profile_id: int, context_packet: Dict[str, Any] = None, 
                     resource_context: Dict[str, Any] = None, 
                     strategy_framework: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Build comprehensive context dictionary for placeholder injection.
        
        Args:
            user_profile_id: User profile ID
            context_packet: Context packet from workflow
            resource_context: Resource context from resource agent
            strategy_framework: Strategy framework from strategy agent
            
        Returns:
            Dictionary containing all placeholder values
        """
        context = {}
        
        try:
            # 🔥 CRITICAL DEBUG: Log all inputs to trace user_input_context (SYNC VERSION)
            logger.warning(f"🔥 PLACEHOLDER INJECTOR SYNC DEBUG: user_profile_id={user_profile_id}")
            logger.warning(f"🔥 PLACEHOLDER INJECTOR SYNC DEBUG: context_packet keys: {list(context_packet.keys()) if context_packet else 'None'}")
            if context_packet and 'user_input_context' in context_packet:
                logger.warning(f"🔥 PLACEHOLDER INJECTOR SYNC DEBUG: user_input_context found: {context_packet['user_input_context']}")
            else:
                logger.warning(f"🔥 PLACEHOLDER INJECTOR SYNC DEBUG: NO user_input_context in context_packet")

            # Import here to avoid circular imports
            from apps.user.models import UserProfile, CurrentMood, UserEnvironment, UserTraitInclination
            from apps.main.models import HistoryEvent
            from django.db.models import Q
            
            # Get user profile
            try:
                user_profile = UserProfile.objects.get(id=user_profile_id)
            except UserProfile.DoesNotExist:
                logger.warning(f"User profile {user_profile_id} not found")
                user_profile = None
            
            # Build temporal context
            now = timezone.now()
            user_timezone = pytz.timezone(getattr(user_profile, 'timezone', 'UTC') if user_profile else 'UTC')
            local_now = now.astimezone(user_timezone)
            
            context.update({
                'LOCAL_DATE': local_now.strftime('%Y-%m-%d'),
                'LOCAL_TIME': local_now.strftime('%H:%M'),
                'LOCAL_DATETIME': local_now.strftime('%Y-%m-%d %H:%M'),
                'DAY_OF_WEEK': local_now.strftime('%A'),
                'TIME_OF_DAY': self._get_time_of_day(local_now.hour),
            })
            
            # Build user profile context
            if user_profile:
                context.update({
                    'USER_NAME': user_profile.profile_name or "User",
                    'USER_AGE': getattr(user_profile, 'age', 'not specified'),
                    'USER_LOCATION': getattr(user_profile, 'location', 'not specified'),
                    'PROFILE_COMPLETION': f"{getattr(user_profile, 'completion_percentage', 0)}%",
                })
            
            # Build psychological context
            if user_profile:
                try:
                    current_mood = CurrentMood.objects.filter(user_profile=user_profile).first()
                    if current_mood:
                        # Use the actual field names from the model
                        mood_description = current_mood.description or 'neutral'
                        # Extract mood keywords from description
                        mood_keywords = ['excited', 'anxious', 'creative', 'inspired', 'impatient']
                        detected_mood = 'neutral'
                        for keyword in mood_keywords:
                            if keyword.lower() in mood_description.lower():
                                detected_mood = keyword
                                break

                        context.update({
                            'CURRENT_MOOD': detected_mood,
                            'ENERGY_LEVEL': f"height {current_mood.height}/100" if current_mood.height else 'moderate',
                            'STRESS_LEVEL': 'moderate',  # Default since not in model
                        })
                except Exception as e:
                    logger.warning(f"Could not load current mood: {e}")

                # Get trait inclinations
                try:
                    traits = UserTraitInclination.objects.filter(user_profile=user_profile)
                    trait_dict = {}
                    trait_names = []

                    for trait in traits:
                        trait_name = trait.generic_trait.name if trait.generic_trait else 'unknown'
                        trait_code = trait.generic_trait.code if trait.generic_trait else 'unknown'
                        trait_strength = trait.strength or 0
                        trait_dict[trait_name.lower()] = trait_strength
                        trait_dict[trait_code.lower()] = trait_strength  # Also store by code
                        if trait_strength > 60:  # High strength traits
                            trait_names.append(trait_name)

                    # Calculate Big Five/HEXACO domain averages from facets
                    openness_facets = [trait_dict.get(f'open_{facet}', 0) for facet in ['aesthetic', 'inquisitive', 'creativity', 'unconventional']]
                    conscientiousness_facets = [trait_dict.get(f'consc_{facet}', 0) for facet in ['organization', 'diligence', 'perfectionism', 'prudence']]
                    extraversion_facets = [trait_dict.get(f'extra_{facet}', 0) for facet in ['self_esteem', 'social_boldness', 'sociability', 'liveliness']]
                    agreeableness_facets = [trait_dict.get(f'agree_{facet}', 0) for facet in ['forgiveness', 'gentleness', 'flexibility', 'patience']]
                    emotionality_facets = [trait_dict.get(f'emotion_{facet}', 0) for facet in ['fearfulness', 'anxiety', 'dependence', 'sentimentality']]
                    honesty_facets = [trait_dict.get(f'honesty_{facet}', 0) for facet in ['sincerity', 'fairness', 'greed_avoidance', 'modesty']]

                    # Calculate domain averages (only if we have facet data)
                    openness_avg = sum(f for f in openness_facets if f > 0) / len([f for f in openness_facets if f > 0]) if any(f > 0 for f in openness_facets) else None
                    conscientiousness_avg = sum(f for f in conscientiousness_facets if f > 0) / len([f for f in conscientiousness_facets if f > 0]) if any(f > 0 for f in conscientiousness_facets) else None
                    extraversion_avg = sum(f for f in extraversion_facets if f > 0) / len([f for f in extraversion_facets if f > 0]) if any(f > 0 for f in extraversion_facets) else None
                    agreeableness_avg = sum(f for f in agreeableness_facets if f > 0) / len([f for f in agreeableness_facets if f > 0]) if any(f > 0 for f in agreeableness_facets) else None
                    emotionality_avg = sum(f for f in emotionality_facets if f > 0) / len([f for f in emotionality_facets if f > 0]) if any(f > 0 for f in emotionality_facets) else None
                    honesty_avg = sum(f for f in honesty_facets if f > 0) / len([f for f in honesty_facets if f > 0]) if any(f > 0 for f in honesty_facets) else None

                    context.update({
                        'DOMINANT_TRAITS': ', '.join(trait_names) if trait_names else 'balanced',
                        'TRAIT_OPENNESS': self._format_trait(openness_avg),
                        'TRAIT_CONSCIENTIOUSNESS': self._format_trait(conscientiousness_avg),
                        'TRAIT_EXTRAVERSION': self._format_trait(extraversion_avg),
                        'TRAIT_AGREEABLENESS': self._format_trait(agreeableness_avg),
                        'TRAIT_NEUROTICISM': self._format_trait(emotionality_avg),  # HEXACO Emotionality ≈ Big Five Neuroticism
                        'TRAIT_HONESTY_HUMILITY': self._format_trait(honesty_avg),
                    })
                except Exception as e:
                    logger.warning(f"Could not load trait inclinations: {e}")
            
            # Build environmental context from resource_context AND direct database access
            environment_context = self._build_comprehensive_environment_context_sync(user_profile, resource_context)
            context.update(environment_context)

            # Build resource and time context from resource_context
            if resource_context:
                time_analysis = resource_context.get('time_analysis', {})
                resource_analysis = resource_context.get('resource_analysis', {})

                context.update({
                    'TIME_FLEXIBILITY': time_analysis.get('flexibility_level', 'not specified'),
                    'AVAILABLE_RESOURCES': ', '.join(resource_analysis.get('inventory_items', [])),
                    'PHYSICAL_LIMITATIONS': ', '.join(resource_analysis.get('physical_limitations', [])),
                    'COGNITIVE_LIMITATIONS': ', '.join(resource_analysis.get('cognitive_limitations', [])),
                    'CAPABILITIES': ', '.join(resource_analysis.get('capabilities', [])),
                })

                # Only override TIME_AVAILABLE if not already set from user input
                if 'TIME_AVAILABLE' not in context or context['TIME_AVAILABLE'] == 'not specified':
                    context['TIME_AVAILABLE'] = str(time_analysis.get('duration_minutes', 'not specified'))

            # CRITICAL FIX: Check context_packet for user_input_context (energy_level, time_available)
            if context_packet:
                user_input_context = context_packet.get('user_input_context', {})
                if user_input_context:
                    # Override TIME_AVAILABLE and ENERGY_LEVEL with direct user input if available
                    if user_input_context.get('time_available') is not None:
                        context['TIME_AVAILABLE'] = str(user_input_context['time_available'])  # Just the number, template adds "minutes"
                        logger.debug(f"🎯 PLACEHOLDER INJECTOR: Using user input TIME_AVAILABLE: {context['TIME_AVAILABLE']}")

                    if user_input_context.get('energy_level') is not None:
                        context['ENERGY_LEVEL'] = f"{user_input_context['energy_level']}% energy"
                        logger.debug(f"🎯 PLACEHOLDER INJECTOR: Using user input ENERGY_LEVEL: {context['ENERGY_LEVEL']}")

                    # Add additional user input context
                    if user_input_context.get('direct_input'):
                        context['USER_INPUT_MODE'] = 'direct'
                    else:
                        context['USER_INPUT_MODE'] = 'inferred'
            
            # Build goal and aspiration context from context_packet
            if context_packet:
                context.update({
                    'PRIMARY_GOALS': context_packet.get('primary_goals', 'not specified'),
                    'CURRENT_ASPIRATIONS': context_packet.get('aspirations', 'not specified'),
                    'FOCUS_AREAS': context_packet.get('focus_areas', 'not specified'),
                    'GROWTH_PRIORITIES': context_packet.get('growth_priorities', 'not specified'),
                })
            
            # Build trust and relationship context
            if user_profile:
                try:
                    from apps.user.models import TrustLevel
                    trust_level = TrustLevel.objects.filter(user_profile=user_profile).first()
                    if trust_level:
                        context.update({
                            'TRUST_LEVEL': f"{trust_level.value}/100" if trust_level.value else 'building',
                            'TRUST_PHASE': trust_level.aggregate_type or 'foundation',
                        })
                except Exception as e:
                    logger.warning(f"Could not load trust level: {e}")
            
            # Add fallback values for any missing critical placeholders
            fallbacks = {
                'USER_NAME': 'User',
                'CURRENT_MOOD': 'neutral',
                'ENERGY_LEVEL': 'moderate',
                'LOCAL_DATE': now.strftime('%Y-%m-%d'),
                'LOCAL_TIME': now.strftime('%H:%M'),
                'TIME_AVAILABLE': 'flexible',
                'CURRENT_ENVIRONMENT': 'comfortable space',
                'TRUST_PHASE': 'foundation',
            }
            
            for key, fallback_value in fallbacks.items():
                if key not in context or not context[key] or context[key] == 'not specified':
                    context[key] = fallback_value
            
        except Exception as e:
            logger.error(f"Error building context: {e}", exc_info=True)
            # Provide minimal context to prevent complete failure
            context = {
                'USER_NAME': 'User',
                'LOCAL_DATE': timezone.now().strftime('%Y-%m-%d'),
                'LOCAL_TIME': timezone.now().strftime('%H:%M'),
                'CURRENT_MOOD': 'neutral',
                'ENERGY_LEVEL': 'moderate',
            }
        
        logger.debug(f"Built context with {len(context)} placeholders")
        return context

    async def build_context_async(self, user_profile_id: int, context_packet: Dict[str, Any] = None,
                                 resource_context: Dict[str, Any] = None,
                                 strategy_framework: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Async version of build_context for use in async contexts.

        Args:
            user_profile_id: User profile ID
            context_packet: Context packet from workflow
            resource_context: Resource context from resource agent
            strategy_framework: Strategy framework from strategy agent

        Returns:
            Dictionary containing all placeholder values
        """
        # 🔥 CRITICAL DEBUG: Log all inputs to trace user_input_context
        logger.warning(f"🔥 PLACEHOLDER INJECTOR ASYNC DEBUG: user_profile_id={user_profile_id}")
        logger.warning(f"🔥 PLACEHOLDER INJECTOR ASYNC DEBUG: context_packet keys: {list(context_packet.keys()) if context_packet else 'None'}")
        if context_packet and 'user_input_context' in context_packet:
            logger.warning(f"🔥 PLACEHOLDER INJECTOR ASYNC DEBUG: user_input_context found: {context_packet['user_input_context']}")
        else:
            logger.warning(f"🔥 PLACEHOLDER INJECTOR ASYNC DEBUG: NO user_input_context in context_packet")

        context = {}

        try:
            # Import here to avoid circular imports
            from apps.user.models import UserProfile, CurrentMood, UserEnvironment, UserTraitInclination
            from apps.main.models import HistoryEvent
            from django.db.models import Q

            # Get user profile (async)
            try:
                user_profile = await sync_to_async(UserProfile.objects.get)(id=user_profile_id)
            except UserProfile.DoesNotExist:
                logger.warning(f"User profile {user_profile_id} not found")
                user_profile = None

            # Build temporal context
            now = timezone.now()
            user_timezone = pytz.timezone(getattr(user_profile, 'timezone', 'UTC') if user_profile else 'UTC')
            local_now = now.astimezone(user_timezone)

            context.update({
                'LOCAL_DATE': local_now.strftime('%Y-%m-%d'),
                'LOCAL_TIME': local_now.strftime('%H:%M'),
                'LOCAL_DATETIME': local_now.strftime('%Y-%m-%d %H:%M'),
                'DAY_OF_WEEK': local_now.strftime('%A'),
                'TIME_OF_DAY': self._get_time_of_day(local_now.hour),
            })

            # Build user profile context
            if user_profile:
                context.update({
                    'USER_NAME': user_profile.profile_name or "User",
                    'USER_AGE': getattr(user_profile, 'age', 'not specified'),
                    'USER_LOCATION': getattr(user_profile, 'location', 'not specified'),
                    'PROFILE_COMPLETION': f"{getattr(user_profile, 'completion_percentage', 0)}%",
                })

            # Build psychological context (async)
            if user_profile:
                try:
                    current_mood = await sync_to_async(CurrentMood.objects.filter(user_profile=user_profile).first)()
                    if current_mood:
                        # Use the actual field names from the model
                        mood_description = current_mood.description or 'neutral'
                        # Extract mood keywords from description
                        mood_keywords = ['excited', 'anxious', 'creative', 'inspired', 'impatient']
                        detected_mood = 'neutral'
                        for keyword in mood_keywords:
                            if keyword.lower() in mood_description.lower():
                                detected_mood = keyword
                                break

                        context.update({
                            'CURRENT_MOOD': detected_mood,
                            'ENERGY_LEVEL': f"height {current_mood.height}/100" if current_mood.height else 'moderate',
                            'STRESS_LEVEL': 'moderate',  # Default since not in model
                        })
                except Exception as e:
                    logger.warning(f"Could not load current mood: {e}")

                # Get trait inclinations (async)
                try:
                    traits = await sync_to_async(list)(UserTraitInclination.objects.filter(user_profile=user_profile).select_related('generic_trait'))
                    trait_dict = {}
                    trait_names = []

                    for trait in traits:
                        trait_name = trait.generic_trait.name if trait.generic_trait else 'unknown'
                        trait_code = trait.generic_trait.code if trait.generic_trait else 'unknown'
                        trait_strength = trait.strength or 0
                        trait_dict[trait_name.lower()] = trait_strength
                        trait_dict[trait_code.lower()] = trait_strength  # Also store by code
                        if trait_strength > 60:  # High strength traits
                            trait_names.append(trait_name)

                    # Calculate Big Five/HEXACO domain averages from facets
                    openness_facets = [trait_dict.get(f'open_{facet}', 0) for facet in ['aesthetic', 'inquisitive', 'creativity', 'unconventional']]
                    conscientiousness_facets = [trait_dict.get(f'consc_{facet}', 0) for facet in ['organization', 'diligence', 'perfectionism', 'prudence']]
                    extraversion_facets = [trait_dict.get(f'extra_{facet}', 0) for facet in ['self_esteem', 'social_boldness', 'sociability', 'liveliness']]
                    agreeableness_facets = [trait_dict.get(f'agree_{facet}', 0) for facet in ['forgiveness', 'gentleness', 'flexibility', 'patience']]
                    emotionality_facets = [trait_dict.get(f'emotion_{facet}', 0) for facet in ['fearfulness', 'anxiety', 'dependence', 'sentimentality']]
                    honesty_facets = [trait_dict.get(f'honesty_{facet}', 0) for facet in ['sincerity', 'fairness', 'greed_avoidance', 'modesty']]

                    # Calculate domain averages (only if we have facet data)
                    openness_avg = sum(f for f in openness_facets if f > 0) / len([f for f in openness_facets if f > 0]) if any(f > 0 for f in openness_facets) else None
                    conscientiousness_avg = sum(f for f in conscientiousness_facets if f > 0) / len([f for f in conscientiousness_facets if f > 0]) if any(f > 0 for f in conscientiousness_facets) else None
                    extraversion_avg = sum(f for f in extraversion_facets if f > 0) / len([f for f in extraversion_facets if f > 0]) if any(f > 0 for f in extraversion_facets) else None
                    agreeableness_avg = sum(f for f in agreeableness_facets if f > 0) / len([f for f in agreeableness_facets if f > 0]) if any(f > 0 for f in agreeableness_facets) else None
                    emotionality_avg = sum(f for f in emotionality_facets if f > 0) / len([f for f in emotionality_facets if f > 0]) if any(f > 0 for f in emotionality_facets) else None
                    honesty_avg = sum(f for f in honesty_facets if f > 0) / len([f for f in honesty_facets if f > 0]) if any(f > 0 for f in honesty_facets) else None

                    context.update({
                        'DOMINANT_TRAITS': ', '.join(trait_names) if trait_names else 'balanced',
                        'TRAIT_OPENNESS': self._format_trait(openness_avg),
                        'TRAIT_CONSCIENTIOUSNESS': self._format_trait(conscientiousness_avg),
                        'TRAIT_EXTRAVERSION': self._format_trait(extraversion_avg),
                        'TRAIT_AGREEABLENESS': self._format_trait(agreeableness_avg),
                        'TRAIT_NEUROTICISM': self._format_trait(emotionality_avg),  # HEXACO Emotionality ≈ Big Five Neuroticism
                        'TRAIT_HONESTY_HUMILITY': self._format_trait(honesty_avg),
                    })
                except Exception as e:
                    logger.warning(f"Could not load trait inclinations: {e}")

            # Build environmental context from resource_context AND direct database access
            environment_context = await self._build_comprehensive_environment_context_async(user_profile, resource_context)
            context.update(environment_context)

            # Build resource and time context from resource_context
            if resource_context:
                time_analysis = resource_context.get('time_analysis', {})
                resource_analysis = resource_context.get('resource_analysis', {})

                context.update({
                    'TIME_FLEXIBILITY': time_analysis.get('flexibility_level', 'not specified'),
                    'AVAILABLE_RESOURCES': ', '.join(resource_analysis.get('inventory_items', [])),
                    'PHYSICAL_LIMITATIONS': ', '.join(resource_analysis.get('physical_limitations', [])),
                    'COGNITIVE_LIMITATIONS': ', '.join(resource_analysis.get('cognitive_limitations', [])),
                    'CAPABILITIES': ', '.join(resource_analysis.get('capabilities', [])),
                })

                # Only override TIME_AVAILABLE if not already set from user input
                if 'TIME_AVAILABLE' not in context or context['TIME_AVAILABLE'] == 'not specified':
                    context['TIME_AVAILABLE'] = str(time_analysis.get('duration_minutes', 'not specified'))

            # CRITICAL FIX: Check context_packet for user_input_context (energy_level, time_available)
            if context_packet:
                user_input_context = context_packet.get('user_input_context', {})
                if user_input_context:
                    # Override TIME_AVAILABLE and ENERGY_LEVEL with direct user input if available
                    if user_input_context.get('time_available') is not None:
                        context['TIME_AVAILABLE'] = str(user_input_context['time_available'])  # Just the number, template adds "minutes"
                        logger.debug(f"🎯 PLACEHOLDER INJECTOR (ASYNC): Using user input TIME_AVAILABLE: {context['TIME_AVAILABLE']}")

                    if user_input_context.get('energy_level') is not None:
                        context['ENERGY_LEVEL'] = f"{user_input_context['energy_level']}% energy"
                        logger.debug(f"🎯 PLACEHOLDER INJECTOR (ASYNC): Using user input ENERGY_LEVEL: {context['ENERGY_LEVEL']}")

                    # Add additional user input context
                    if user_input_context.get('direct_input'):
                        context['USER_INPUT_MODE'] = 'direct'
                    else:
                        context['USER_INPUT_MODE'] = 'inferred'

            # Build goal and aspiration context from context_packet
            if context_packet:
                context.update({
                    'PRIMARY_GOALS': context_packet.get('primary_goals', 'not specified'),
                    'CURRENT_ASPIRATIONS': context_packet.get('aspirations', 'not specified'),
                    'FOCUS_AREAS': context_packet.get('focus_areas', 'not specified'),
                    'GROWTH_PRIORITIES': context_packet.get('growth_priorities', 'not specified'),
                })

            # Build trust and relationship context (async)
            if user_profile:
                try:
                    from apps.user.models import TrustLevel
                    trust_level = await sync_to_async(TrustLevel.objects.filter(user_profile=user_profile).first)()
                    if trust_level:
                        context.update({
                            'TRUST_LEVEL': f"{trust_level.value}/100" if trust_level.value else 'building',
                            'TRUST_PHASE': trust_level.aggregate_type or 'foundation',
                        })
                except Exception as e:
                    logger.warning(f"Could not load trust level: {e}")

            # Add fallback values for any missing critical placeholders
            fallbacks = {
                'USER_NAME': 'User',
                'CURRENT_MOOD': 'neutral',
                'ENERGY_LEVEL': 'moderate',
                'LOCAL_DATE': now.strftime('%Y-%m-%d'),
                'LOCAL_TIME': now.strftime('%H:%M'),
                'TIME_AVAILABLE': 'flexible',
                'CURRENT_ENVIRONMENT': 'comfortable space',
                'TRUST_PHASE': 'foundation',
            }

            for key, fallback_value in fallbacks.items():
                if key not in context or not context[key] or context[key] == 'not specified':
                    context[key] = fallback_value

        except Exception as e:
            logger.error(f"Error building context: {e}", exc_info=True)
            # Provide minimal context to prevent complete failure
            context = {
                'USER_NAME': 'User',
                'LOCAL_DATE': timezone.now().strftime('%Y-%m-%d'),
                'LOCAL_TIME': timezone.now().strftime('%H:%M'),
                'CURRENT_MOOD': 'neutral',
                'ENERGY_LEVEL': 'moderate',
            }

        logger.debug(f"Built context with {len(context)} placeholders")
        return context

    async def _build_comprehensive_environment_context_async(self, user_profile, resource_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Build comprehensive environment context from database models and resource context.

        This method retrieves rich environment data directly from UserEnvironment models
        and combines it with resource_context data for complete environment information.
        """
        env_context = {}

        try:
            # Check if user_profile has current_environment using async-safe access
            current_env = None
            if user_profile:
                try:
                    current_env = await sync_to_async(lambda: user_profile.current_environment)()
                except Exception as e:
                    logger.debug(f"Could not access current_environment: {e}")

            if current_env:
                env = current_env

                # Basic environment information
                env_context.update({
                    'CURRENT_ENVIRONMENT': env.environment_description or env.environment_name or 'not specified',
                    'ENVIRONMENT_NAME': env.environment_name or 'not specified',
                })

                # Try to get physical properties
                try:
                    # Use async-safe access to related models
                    physical_props = None
                    try:
                        physical_props = await sync_to_async(lambda: getattr(env, 'physical_properties', None))()
                    except Exception:
                        # Try alternative access method
                        try:
                            from apps.user.models import PhysicalProperties
                            physical_props = await sync_to_async(PhysicalProperties.objects.filter(user_environment=env).first)()
                        except Exception as e:
                            logger.debug(f"Could not access physical properties via alternative method: {e}")

                    if physical_props:
                        # Map noise level to descriptive terms
                        noise_level = physical_props.noise_level
                        if noise_level <= 20:
                            noise_desc = "very quiet"
                        elif noise_level <= 40:
                            noise_desc = "quiet"
                        elif noise_level <= 60:
                            noise_desc = "moderate"
                        elif noise_level <= 80:
                            noise_desc = "noisy"
                        else:
                            noise_desc = "very noisy"

                        # Map light quality to descriptive terms
                        light_quality = physical_props.light_quality
                        if light_quality >= 80:
                            light_desc = "excellent natural light"
                        elif light_quality >= 60:
                            light_desc = "good lighting"
                        elif light_quality >= 40:
                            light_desc = "moderate lighting"
                        else:
                            light_desc = "dim lighting"

                        env_context.update({
                            'NOISE_LEVEL': noise_desc,
                            'LIGHT_QUALITY': light_desc,
                            'ACCESSIBILITY': f"{physical_props.accessibility}% accessible" if physical_props.accessibility else 'not specified',
                            'HAS_NATURAL_ELEMENTS': 'yes' if physical_props.has_natural_elements else 'no',
                            'AIR_QUALITY': f"{physical_props.air_quality}% air quality" if physical_props.air_quality else 'not specified',
                        })
                except Exception as e:
                    logger.debug(f"Could not access physical properties: {e}")

                # Try to get social context
                try:
                    # Use async-safe access to related models
                    social_ctx = None
                    try:
                        social_ctx = await sync_to_async(lambda: getattr(env, 'social_context', None))()
                    except Exception:
                        # Try alternative access method
                        try:
                            from apps.user.models import SocialContext
                            social_ctx = await sync_to_async(SocialContext.objects.filter(user_environment=env).first)()
                        except Exception as e:
                            logger.debug(f"Could not access social context via alternative method: {e}")

                    if social_ctx:
                        # Map privacy level to descriptive terms
                        privacy_level = social_ctx.privacy_level
                        if privacy_level >= 80:
                            privacy_desc = "very private"
                        elif privacy_level >= 60:
                            privacy_desc = "private"
                        elif privacy_level >= 40:
                            privacy_desc = "semi-private"
                        elif privacy_level >= 20:
                            privacy_desc = "public"
                        else:
                            privacy_desc = "very public"

                        # Map social interaction level
                        social_interaction = social_ctx.social_interaction_level
                        if social_interaction <= 20:
                            social_desc = "solitary"
                        elif social_interaction <= 40:
                            social_desc = "minimal interaction"
                        elif social_interaction <= 60:
                            social_desc = "moderate interaction"
                        elif social_interaction <= 80:
                            social_desc = "social"
                        else:
                            social_desc = "highly social"

                        env_context.update({
                            'PRIVACY_LEVEL': privacy_desc,
                            'SOCIAL_CONTEXT': social_desc,
                            'SAFETY_LEVEL': f"{social_ctx.safety_level}% safe" if social_ctx.safety_level else 'not specified',
                        })
                except Exception as e:
                    logger.debug(f"Could not access social context: {e}")

                # Try to get activity support
                try:
                    # Use async-safe access to related models
                    activity_sup = None
                    try:
                        activity_sup = await sync_to_async(lambda: getattr(env, 'activity_support', None))()
                    except Exception:
                        # Try alternative access method
                        try:
                            from apps.user.models import ActivitySupport
                            activity_sup = await sync_to_async(ActivitySupport.objects.filter(user_environment=env).first)()
                        except Exception as e:
                            logger.debug(f"Could not access activity support via alternative method: {e}")

                    if activity_sup:
                        # Map digital connectivity
                        connectivity = activity_sup.digital_connectivity
                        if connectivity >= 90:
                            conn_desc = "excellent internet"
                        elif connectivity >= 70:
                            conn_desc = "good internet"
                        elif connectivity >= 50:
                            conn_desc = "moderate internet"
                        elif connectivity >= 30:
                            conn_desc = "limited internet"
                        else:
                            conn_desc = "poor internet"

                        # Map resource availability
                        resources = activity_sup.resource_availability
                        if resources >= 80:
                            resource_desc = "abundant resources"
                        elif resources >= 60:
                            resource_desc = "good resources"
                        elif resources >= 40:
                            resource_desc = "moderate resources"
                        elif resources >= 20:
                            resource_desc = "limited resources"
                        else:
                            resource_desc = "minimal resources"

                        env_context.update({
                            'DIGITAL_CONNECTIVITY': conn_desc,
                            'RESOURCE_AVAILABILITY': resource_desc,
                        })

                        # Add domain-specific support if available
                        if activity_sup.domain_specific_support:
                            domain_support = []
                            for domain, support_level in activity_sup.domain_specific_support.items():
                                if support_level >= 70:
                                    domain_support.append(f"excellent {domain} support")
                                elif support_level >= 50:
                                    domain_support.append(f"good {domain} support")

                            if domain_support:
                                env_context['DOMAIN_SUPPORT'] = ', '.join(domain_support)

                except Exception as e:
                    logger.debug(f"Could not access activity support: {e}")

                # Try to get psychological context
                try:
                    # Use async-safe access to related models
                    psych_ctx = None
                    try:
                        psych_ctx = await sync_to_async(lambda: getattr(env, 'psychological_context', None))()
                    except Exception:
                        # Try alternative access method
                        try:
                            from apps.user.models import PsychologicalContext
                            psych_ctx = await sync_to_async(PsychologicalContext.objects.filter(user_environment=env).first)()
                        except Exception as e:
                            logger.debug(f"Could not access psychological context via alternative method: {e}")

                    if psych_ctx:
                        # Map comfort level
                        comfort = psych_ctx.comfort_level
                        if comfort >= 80:
                            comfort_desc = "very comfortable"
                        elif comfort >= 60:
                            comfort_desc = "comfortable"
                        elif comfort >= 40:
                            comfort_desc = "moderately comfortable"
                        else:
                            comfort_desc = "uncomfortable"

                        env_context.update({
                            'COMFORT_LEVEL': comfort_desc,
                            'RESTORATIVE_QUALITY': f"{psych_ctx.restorative_quality}% restorative" if psych_ctx.restorative_quality else 'not specified',
                        })
                except Exception as e:
                    logger.debug(f"Could not access psychological context: {e}")

            # Override with resource_context data if available (resource agent analysis takes precedence)
            if resource_context:
                env_analysis = resource_context.get('environment_analysis', {})
                if env_analysis:
                    # Only override if resource_context has more specific data
                    for key, value in env_analysis.items():
                        if value and value != 'not specified':
                            if key == 'environment_description':
                                env_context['CURRENT_ENVIRONMENT'] = value
                            elif key == 'environment_type':
                                env_context['ENVIRONMENT_TYPE'] = value
                            elif key == 'privacy_level':
                                env_context['PRIVACY_LEVEL'] = value
                            elif key == 'space_size':
                                env_context['SPACE_SIZE'] = value
                            elif key == 'noise_level':
                                env_context['NOISE_LEVEL'] = value
                            elif key == 'social_context':
                                env_context['SOCIAL_CONTEXT'] = value

            # Add fallback values for missing critical environment placeholders
            fallbacks = {
                'CURRENT_ENVIRONMENT': 'comfortable space',
                'ENVIRONMENT_TYPE': 'indoor space',
                'PRIVACY_LEVEL': 'private',
                'SPACE_SIZE': 'medium',
                'NOISE_LEVEL': 'quiet',
                'SOCIAL_CONTEXT': 'alone',
            }

            for key, fallback_value in fallbacks.items():
                if key not in env_context or not env_context[key] or env_context[key] == 'not specified':
                    env_context[key] = fallback_value

        except Exception as e:
            logger.error(f"Error building comprehensive environment context: {e}", exc_info=True)
            # Provide minimal fallback context
            env_context = {
                'CURRENT_ENVIRONMENT': 'comfortable space',
                'ENVIRONMENT_TYPE': 'indoor space',
                'PRIVACY_LEVEL': 'private',
                'SPACE_SIZE': 'medium',
                'NOISE_LEVEL': 'quiet',
                'SOCIAL_CONTEXT': 'alone',
            }

        logger.debug(f"Built comprehensive environment context with {len(env_context)} environment placeholders")
        return env_context

    def _build_comprehensive_environment_context_sync(self, user_profile, resource_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Build comprehensive environment context from database models and resource context (sync version).

        This method retrieves rich environment data directly from UserEnvironment models
        and combines it with resource_context data for complete environment information.
        """
        env_context = {}

        try:
            if user_profile and user_profile.current_environment:
                env = user_profile.current_environment

                # Basic environment information
                env_context.update({
                    'CURRENT_ENVIRONMENT': env.environment_description or env.environment_name or 'not specified',
                    'ENVIRONMENT_NAME': env.environment_name or 'not specified',
                })

                # Try to get physical properties
                try:
                    physical_props = getattr(env, 'physical_properties', None)
                    if physical_props:
                        # Map noise level to descriptive terms
                        noise_level = physical_props.noise_level
                        if noise_level <= 20:
                            noise_desc = "very quiet"
                        elif noise_level <= 40:
                            noise_desc = "quiet"
                        elif noise_level <= 60:
                            noise_desc = "moderate"
                        elif noise_level <= 80:
                            noise_desc = "noisy"
                        else:
                            noise_desc = "very noisy"

                        # Map light quality to descriptive terms
                        light_quality = physical_props.light_quality
                        if light_quality >= 80:
                            light_desc = "excellent natural light"
                        elif light_quality >= 60:
                            light_desc = "good lighting"
                        elif light_quality >= 40:
                            light_desc = "moderate lighting"
                        else:
                            light_desc = "dim lighting"

                        env_context.update({
                            'NOISE_LEVEL': noise_desc,
                            'LIGHT_QUALITY': light_desc,
                            'ACCESSIBILITY': f"{physical_props.accessibility}% accessible" if physical_props.accessibility else 'not specified',
                            'HAS_NATURAL_ELEMENTS': 'yes' if physical_props.has_natural_elements else 'no',
                            'AIR_QUALITY': f"{physical_props.air_quality}% air quality" if physical_props.air_quality else 'not specified',
                        })
                except Exception as e:
                    logger.debug(f"Could not access physical properties: {e}")

                # Try to get social context
                try:
                    social_ctx = getattr(env, 'social_context', None)
                    if social_ctx:
                        # Map privacy level to descriptive terms
                        privacy_level = social_ctx.privacy_level
                        if privacy_level >= 80:
                            privacy_desc = "very private"
                        elif privacy_level >= 60:
                            privacy_desc = "private"
                        elif privacy_level >= 40:
                            privacy_desc = "semi-private"
                        elif privacy_level >= 20:
                            privacy_desc = "public"
                        else:
                            privacy_desc = "very public"

                        # Map social interaction level
                        social_interaction = social_ctx.social_interaction_level
                        if social_interaction <= 20:
                            social_desc = "solitary"
                        elif social_interaction <= 40:
                            social_desc = "minimal interaction"
                        elif social_interaction <= 60:
                            social_desc = "moderate interaction"
                        elif social_interaction <= 80:
                            social_desc = "social"
                        else:
                            social_desc = "highly social"

                        env_context.update({
                            'PRIVACY_LEVEL': privacy_desc,
                            'SOCIAL_CONTEXT': social_desc,
                            'SAFETY_LEVEL': f"{social_ctx.safety_level}% safe" if social_ctx.safety_level else 'not specified',
                        })
                except Exception as e:
                    logger.debug(f"Could not access social context: {e}")

                # Try to get activity support
                try:
                    activity_sup = getattr(env, 'activity_support', None)
                    if activity_sup:
                        # Map digital connectivity
                        connectivity = activity_sup.digital_connectivity
                        if connectivity >= 90:
                            conn_desc = "excellent internet"
                        elif connectivity >= 70:
                            conn_desc = "good internet"
                        elif connectivity >= 50:
                            conn_desc = "moderate internet"
                        elif connectivity >= 30:
                            conn_desc = "limited internet"
                        else:
                            conn_desc = "poor internet"

                        # Map resource availability
                        resources = activity_sup.resource_availability
                        if resources >= 80:
                            resource_desc = "abundant resources"
                        elif resources >= 60:
                            resource_desc = "good resources"
                        elif resources >= 40:
                            resource_desc = "moderate resources"
                        elif resources >= 20:
                            resource_desc = "limited resources"
                        else:
                            resource_desc = "minimal resources"

                        env_context.update({
                            'DIGITAL_CONNECTIVITY': conn_desc,
                            'RESOURCE_AVAILABILITY': resource_desc,
                        })

                        # Add domain-specific support if available
                        if activity_sup.domain_specific_support:
                            domain_support = []
                            for domain, support_level in activity_sup.domain_specific_support.items():
                                if support_level >= 70:
                                    domain_support.append(f"excellent {domain} support")
                                elif support_level >= 50:
                                    domain_support.append(f"good {domain} support")

                            if domain_support:
                                env_context['DOMAIN_SUPPORT'] = ', '.join(domain_support)

                except Exception as e:
                    logger.debug(f"Could not access activity support: {e}")

                # Try to get psychological context
                try:
                    psych_ctx = getattr(env, 'psychological_context', None)
                    if psych_ctx:
                        # Map comfort level
                        comfort = psych_ctx.comfort_level
                        if comfort >= 80:
                            comfort_desc = "very comfortable"
                        elif comfort >= 60:
                            comfort_desc = "comfortable"
                        elif comfort >= 40:
                            comfort_desc = "moderately comfortable"
                        else:
                            comfort_desc = "uncomfortable"

                        env_context.update({
                            'COMFORT_LEVEL': comfort_desc,
                            'RESTORATIVE_QUALITY': f"{psych_ctx.restorative_quality}% restorative" if psych_ctx.restorative_quality else 'not specified',
                        })
                except Exception as e:
                    logger.debug(f"Could not access psychological context: {e}")

            # Override with resource_context data if available (resource agent analysis takes precedence)
            if resource_context:
                env_analysis = resource_context.get('environment_analysis', {})
                if env_analysis:
                    # Only override if resource_context has more specific data
                    for key, value in env_analysis.items():
                        if value and value != 'not specified':
                            if key == 'environment_description':
                                env_context['CURRENT_ENVIRONMENT'] = value
                            elif key == 'environment_type':
                                env_context['ENVIRONMENT_TYPE'] = value
                            elif key == 'privacy_level':
                                env_context['PRIVACY_LEVEL'] = value
                            elif key == 'space_size':
                                env_context['SPACE_SIZE'] = value
                            elif key == 'noise_level':
                                env_context['NOISE_LEVEL'] = value
                            elif key == 'social_context':
                                env_context['SOCIAL_CONTEXT'] = value

            # Add fallback values for missing critical environment placeholders
            fallbacks = {
                'CURRENT_ENVIRONMENT': 'comfortable space',
                'ENVIRONMENT_TYPE': 'indoor space',
                'PRIVACY_LEVEL': 'private',
                'SPACE_SIZE': 'medium',
                'NOISE_LEVEL': 'quiet',
                'SOCIAL_CONTEXT': 'alone',
            }

            for key, fallback_value in fallbacks.items():
                if key not in env_context or not env_context[key] or env_context[key] == 'not specified':
                    env_context[key] = fallback_value

        except Exception as e:
            logger.error(f"Error building comprehensive environment context (sync): {e}", exc_info=True)
            # Provide minimal fallback context
            env_context = {
                'CURRENT_ENVIRONMENT': 'comfortable space',
                'ENVIRONMENT_TYPE': 'indoor space',
                'PRIVACY_LEVEL': 'private',
                'SPACE_SIZE': 'medium',
                'NOISE_LEVEL': 'quiet',
                'SOCIAL_CONTEXT': 'alone',
            }

        logger.debug(f"Built comprehensive environment context (sync) with {len(env_context)} environment placeholders")
        return env_context

    def _get_time_of_day(self, hour: int) -> str:
        """Get time of day description from hour."""
        if 5 <= hour < 12:
            return 'morning'
        elif 12 <= hour < 17:
            return 'afternoon'
        elif 17 <= hour < 21:
            return 'evening'
        else:
            return 'night'
    
    def _format_trait(self, score: Optional[float]) -> str:
        """Format trait score for display."""
        if score is None:
            return 'not assessed'
        
        if score < 0.3:
            return f'low ({score:.1f})'
        elif score < 0.7:
            return f'moderate ({score:.1f})'
        else:
            return f'high ({score:.1f})'


# Global instance for easy access
placeholder_injector = PlaceholderInjector()
