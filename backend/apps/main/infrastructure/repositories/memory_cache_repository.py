"""
In-Memory Cache Repository Implementation

This module provides a simple in-memory cache implementation for development
and testing. In production, this could be replaced with Redis or Memcached.
"""

import logging
import time
import re
from typing import Optional, Any, Dict
from datetime import datetime, timedelta

from ...domain.repositories.repository_interfaces import CacheRepositoryInterface

logger = logging.getLogger(__name__)


class MemoryCacheRepository(CacheRepositoryInterface):
    """Simple in-memory cache implementation."""

    def __init__(self):
        """Initialize the cache."""
        self._cache: Dict[str, Dict[str, Any]] = {}
        self._default_ttl = 300  # 5 minutes

    async def get(self, key: str) -> Optional[Any]:
        """Get a value from cache by key."""
        try:
            if key not in self._cache:
                return None
            
            entry = self._cache[key]
            
            # Check if expired
            if entry['expires_at'] and datetime.utcnow() > entry['expires_at']:
                del self._cache[key]
                return None
            
            logger.debug(f"Cache hit for key: {key}")
            return entry['value']
            
        except Exception as e:
            logger.error(f"Error getting cache key {key}: {e}")
            return None

    async def set(self, key: str, value: Any, ttl_seconds: Optional[int] = None) -> bool:
        """Set a value in cache with optional TTL."""
        try:
            ttl = ttl_seconds or self._default_ttl
            expires_at = datetime.utcnow() + timedelta(seconds=ttl) if ttl > 0 else None
            
            self._cache[key] = {
                'value': value,
                'expires_at': expires_at,
                'created_at': datetime.utcnow()
            }
            
            logger.debug(f"Cache set for key: {key} (TTL: {ttl}s)")
            return True
            
        except Exception as e:
            logger.error(f"Error setting cache key {key}: {e}")
            return False

    async def delete(self, key: str) -> bool:
        """Delete a value from cache."""
        try:
            if key in self._cache:
                del self._cache[key]
                logger.debug(f"Cache deleted for key: {key}")
                return True
            return False
            
        except Exception as e:
            logger.error(f"Error deleting cache key {key}: {e}")
            return False

    async def clear_pattern(self, pattern: str) -> int:
        """Clear all cache entries matching a pattern. Returns count of deleted entries."""
        try:
            # Convert glob pattern to regex
            regex_pattern = pattern.replace('*', '.*').replace('?', '.')
            regex = re.compile(regex_pattern)
            
            keys_to_delete = [key for key in self._cache.keys() if regex.match(key)]
            
            for key in keys_to_delete:
                del self._cache[key]
            
            logger.debug(f"Cache cleared {len(keys_to_delete)} entries matching pattern: {pattern}")
            return len(keys_to_delete)
            
        except Exception as e:
            logger.error(f"Error clearing cache pattern {pattern}: {e}")
            return 0

    async def exists(self, key: str) -> bool:
        """Check if a key exists in cache."""
        try:
            if key not in self._cache:
                return False
            
            entry = self._cache[key]
            
            # Check if expired
            if entry['expires_at'] and datetime.utcnow() > entry['expires_at']:
                del self._cache[key]
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking cache key existence {key}: {e}")
            return False

    def clear_all(self) -> int:
        """Clear all cache entries. Returns count of deleted entries."""
        try:
            count = len(self._cache)
            self._cache.clear()
            logger.debug(f"Cache cleared all {count} entries")
            return count
            
        except Exception as e:
            logger.error(f"Error clearing all cache: {e}")
            return 0

    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        try:
            now = datetime.utcnow()
            total_entries = len(self._cache)
            expired_entries = 0
            
            for entry in self._cache.values():
                if entry['expires_at'] and now > entry['expires_at']:
                    expired_entries += 1
            
            return {
                'total_entries': total_entries,
                'expired_entries': expired_entries,
                'active_entries': total_entries - expired_entries,
                'memory_usage_estimate': len(str(self._cache))  # Rough estimate
            }
            
        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
            return {}

    async def cleanup_expired(self) -> int:
        """Remove expired entries from cache. Returns count of removed entries."""
        try:
            now = datetime.utcnow()
            keys_to_delete = []
            
            for key, entry in self._cache.items():
                if entry['expires_at'] and now > entry['expires_at']:
                    keys_to_delete.append(key)
            
            for key in keys_to_delete:
                del self._cache[key]
            
            if keys_to_delete:
                logger.debug(f"Cache cleanup removed {len(keys_to_delete)} expired entries")
            
            return len(keys_to_delete)
            
        except Exception as e:
            logger.error(f"Error during cache cleanup: {e}")
            return 0
