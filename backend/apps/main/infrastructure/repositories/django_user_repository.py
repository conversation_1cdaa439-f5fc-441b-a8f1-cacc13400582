"""
Django ORM Implementation of User Repository Interface

This module provides the concrete implementation of UserRepositoryInterface
using Django ORM for user context and profile data access.
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime
from django.db import models
from django.core.exceptions import ObjectDoesNotExist
from asgiref.sync import sync_to_async

from apps.user.models import UserProfile, Demographics, UserEnvironment
from ...domain.repositories.repository_interfaces import UserRepositoryInterface
from ...domain.models.user_models import UserContext
from ...domain.enums.domain_enums import DomainCode, TrustPhase, EnergyLevel, ResourceType

logger = logging.getLogger(__name__)


class DjangoUserRepository(UserRepositoryInterface):
    """Django ORM implementation of user repository."""

    def __init__(self, cache_repository=None):
        """Initialize with optional cache repository."""
        self.cache_repository = cache_repository
        self.cache_ttl = 300  # 5 minutes default TTL

    async def get_user_context(self, user_profile_id: str) -> Optional[UserContext]:
        """Get complete user context for wheel generation."""
        logger.debug(f"Getting user context for: {user_profile_id}")
        
        # Check cache first
        cache_key = f"user_context:{user_profile_id}"
        if self.cache_repository:
            cached_result = await self.cache_repository.get(cache_key)
            if cached_result:
                return UserContext(**cached_result)

        try:
            # Get user profile with related data
            user_profile = await sync_to_async(
                UserProfile.objects.select_related(
                    'demographics', 'current_environment'
                ).get
            )(id=user_profile_id)
            
            # Build user context
            user_context = await self._build_user_context(user_profile)
            
            # Cache the result
            if self.cache_repository and user_context:
                await self.cache_repository.set(cache_key, user_context.dict(), self.cache_ttl)
            
            return user_context
            
        except ObjectDoesNotExist:
            logger.warning(f"User profile not found: {user_profile_id}")
            return None
        except Exception as e:
            logger.error(f"Error getting user context for {user_profile_id}: {e}")
            return None

    async def save_user_context(self, context: UserContext) -> bool:
        """Save user context. Returns True if successful."""
        logger.debug(f"Saving user context for: {context.user_profile_id}")
        
        try:
            # Get user profile
            user_profile = await sync_to_async(UserProfile.objects.get)(id=context.user_profile_id)
            
            # Update user profile fields if needed
            # Note: Current UserProfile model doesn't have energy_level, time_available fields
            # This would need to be extended or stored in a separate model
            
            # For now, just clear cache to force refresh
            if self.cache_repository:
                await self.cache_repository.delete(f"user_context:{context.user_profile_id}")
            
            logger.info(f"Saved user context for: {context.user_profile_id}")
            return True
            
        except ObjectDoesNotExist:
            logger.warning(f"User profile not found: {context.user_profile_id}")
            return False
        except Exception as e:
            logger.error(f"Error saving user context: {e}")
            return False

    async def update_user_preferences(self, user_id: str, domain_preferences: Dict[DomainCode, float]) -> bool:
        """Update user domain preferences."""
        logger.debug(f"Updating domain preferences for user: {user_id}")
        
        try:
            # Get user profile
            user_profile = await sync_to_async(UserProfile.objects.get)(id=user_id)
            
            # Note: Current model doesn't have domain_preferences field
            # This would need to be added or stored in a separate preferences model
            
            # Clear cache
            if self.cache_repository:
                await self.cache_repository.delete(f"user_context:{user_id}")
            
            logger.info(f"Updated domain preferences for user: {user_id}")
            return True
            
        except ObjectDoesNotExist:
            logger.warning(f"User profile not found: {user_id}")
            return False
        except Exception as e:
            logger.error(f"Error updating user preferences: {e}")
            return False

    async def add_recent_activity(self, user_id: str, activity_id: str) -> bool:
        """Add an activity to user's recent activities list."""
        logger.debug(f"Adding recent activity {activity_id} for user: {user_id}")
        
        try:
            # Note: Current model doesn't have recent_activities field
            # This would need to be implemented as a separate model or JSON field
            
            # Clear cache to force refresh
            if self.cache_repository:
                await self.cache_repository.delete(f"user_context:{user_id}")
                await self.cache_repository.delete(f"recent_activities:{user_id}")
            
            logger.info(f"Added recent activity {activity_id} for user: {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error adding recent activity: {e}")
            return False

    async def get_recent_activities(self, user_id: str, limit: int = 10) -> List[str]:
        """Get user's recent activity IDs."""
        logger.debug(f"Getting recent activities for user: {user_id}")
        
        # Check cache first
        cache_key = f"recent_activities:{user_id}"
        if self.cache_repository:
            cached_result = await self.cache_repository.get(cache_key)
            if cached_result:
                return cached_result[:limit]

        try:
            # Note: Current model doesn't have recent_activities tracking
            # This would need to be implemented as a separate model
            # For now, return empty list
            
            recent_activities = []  # Placeholder
            
            # Cache the result
            if self.cache_repository:
                await self.cache_repository.set(cache_key, recent_activities, self.cache_ttl)
            
            return recent_activities
            
        except Exception as e:
            logger.error(f"Error getting recent activities for user {user_id}: {e}")
            return []

    async def update_trust_phase(self, user_id: str, trust_phase: str) -> bool:
        """Update user's trust phase."""
        logger.debug(f"Updating trust phase for user {user_id}: {trust_phase}")
        
        try:
            # Get user profile
            user_profile = await sync_to_async(UserProfile.objects.get)(id=user_id)
            
            # Note: Current model doesn't have trust_phase field
            # This would need to be added to UserProfile or stored separately
            
            # Clear cache
            if self.cache_repository:
                await self.cache_repository.delete(f"user_context:{user_id}")
            
            logger.info(f"Updated trust phase for user {user_id}: {trust_phase}")
            return True
            
        except ObjectDoesNotExist:
            logger.warning(f"User profile not found: {user_id}")
            return False
        except Exception as e:
            logger.error(f"Error updating trust phase: {e}")
            return False

    async def user_exists(self, user_profile_id: str) -> bool:
        """Check if a user profile exists."""
        logger.debug(f"Checking if user exists: {user_profile_id}")
        
        try:
            exists = await sync_to_async(UserProfile.objects.filter(id=user_profile_id).exists)()
            return exists
            
        except Exception as e:
            logger.error(f"Error checking if user exists: {e}")
            return False

    # Helper methods
    
    async def _build_user_context(self, user_profile: UserProfile) -> UserContext:
        """Build UserContext from Django UserProfile."""
        try:
            # Get demographics if available
            demographics = None
            try:
                demographics = await sync_to_async(lambda: user_profile.demographics)()
            except:
                pass
            
            # Get current environment if available
            environment = None
            try:
                environment = await sync_to_async(lambda: user_profile.current_environment)()
            except:
                pass
            
            # Build context with available data
            # NOTE: This repository method returns default values
            # The actual user context should be created from request data in the domain service
            user_context = UserContext(
                user_profile_id=str(user_profile.id),
                energy_level=70,  # Default - would need to be stored or calculated
                time_available=30,  # Default - reduced from 60 to more reasonable default
                available_resources=self._get_available_resources(environment),
                trust_phase=TrustPhase.FOUNDATION,  # Default - would need to be stored
                domain_preferences={},  # Default - would need to be stored
                recent_activities=[],  # Default - would need to be tracked
                context_timestamp=datetime.utcnow()
            )
            
            return user_context
            
        except Exception as e:
            logger.error(f"Error building user context: {e}")
            raise

    def _get_available_resources(self, environment: Optional[UserEnvironment]) -> List[ResourceType]:
        """Extract available resources from user environment."""
        try:
            if not environment:
                return [ResourceType.DIGITAL_DEVICE]  # Default minimal resources
            
            # Note: Current UserEnvironment model doesn't have resource tracking
            # This would need to be implemented based on environment type
            
            # Default resources based on environment
            default_resources = [
                ResourceType.DIGITAL_DEVICE,
                ResourceType.PHYSICAL_SPACE
            ]
            
            return default_resources
            
        except Exception as e:
            logger.warning(f"Error extracting resources from environment: {e}")
            return [ResourceType.DIGITAL_DEVICE]
