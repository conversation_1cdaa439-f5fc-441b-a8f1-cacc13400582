"""
Django ORM Implementation of Activity Repository Interface

This module provides the concrete implementation of ActivityRepositoryInterface
using Django ORM for data access, with proper error handling and caching.
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime
from django.db import models
from django.core.exceptions import ObjectDoesNotExist
from asgiref.sync import sync_to_async

from apps.activity.models import GenericActivity, ActivityTailored
from apps.user.models import UserProfile
from ...domain.repositories.repository_interfaces import ActivityRepositoryInterface
from ...domain.models.activity_models import ActivityData, ActivitySelectionCriteria
from ...domain.enums.domain_enums import DomainCode, EnergyLevel, ResourceType
from ...services.domain_management_service import CleanDomainMapper

logger = logging.getLogger(__name__)


class DjangoActivityRepository(ActivityRepositoryInterface):
    """Django ORM implementation of activity repository."""

    def __init__(self, cache_repository=None):
        """Initialize with optional cache repository."""
        self.cache_repository = cache_repository
        self.cache_ttl = 300  # 5 minutes default TTL

    async def find_by_criteria(self, criteria: ActivitySelectionCriteria) -> List[ActivityData]:
        """Find activities matching the given selection criteria using metadata-based filtering."""
        logger.debug(f"Finding activities by criteria: time={criteria.time_available}, energy={criteria.energy_level}")

        # Check cache first
        cache_key = self._generate_criteria_cache_key(criteria)
        if self.cache_repository:
            cached_result = await self.cache_repository.get(cache_key)
            if cached_result:
                logger.debug("Returning cached activity results")
                return [ActivityData(**activity_dict) for activity_dict in cached_result]

        try:
            from django.db import models

            # Build Django query with metadata-based filtering
            queryset = GenericActivity.objects.prefetch_related('domain_relationships__domain')

            # Apply metadata-based filtering using JSON field queries
            filters = models.Q()

            # Filter by energy level if specified
            if criteria.energy_level is not None:
                # Activities where energy_level_min <= user_energy <= energy_level_max
                # OR activities without metadata (will use fallback logic)
                energy_filter = (
                    models.Q(metadata__energy_level_min__lte=criteria.energy_level) &
                    models.Q(metadata__energy_level_max__gte=criteria.energy_level)
                ) | models.Q(metadata__isnull=True) | models.Q(metadata={})
                filters &= energy_filter

            # Filter by duration if specified
            if criteria.time_available is not None:
                # Activities where duration_min <= available_time
                # OR activities without metadata (will use fallback logic)
                duration_filter = (
                    models.Q(metadata__duration_min__lte=criteria.time_available)
                ) | models.Q(metadata__isnull=True) | models.Q(metadata={})
                filters &= duration_filter

            # Apply filters if any were specified
            if filters:
                queryset = queryset.filter(filters)

            # Apply challenge range filter if available (using metadata)
            if hasattr(criteria, 'challenge_range') and criteria.challenge_range:
                min_challenge, max_challenge = criteria.challenge_range
                challenge_filter = (
                    models.Q(metadata__challenge_level__gte=min_challenge) &
                    models.Q(metadata__challenge_level__lte=max_challenge)
                ) | models.Q(metadata__challenge_level__isnull=True) | models.Q(metadata={})
                queryset = queryset.filter(challenge_filter)
            
            # Prefetch related data for efficiency
            queryset = queryset.prefetch_related('domain_relationships__domain')
            
            # Execute query asynchronously
            activities = await sync_to_async(list)(queryset[:50])  # Limit to 50 for performance
            
            # Convert to domain models
            activity_data_list = []
            for activity in activities:
                try:
                    activity_data = await self._convert_to_domain_model(activity)
                    if activity_data:
                        activity_data_list.append(activity_data)
                except Exception as e:
                    logger.warning(f"Failed to convert activity {activity.id}: {e}")
                    continue
            
            # Cache the results
            if self.cache_repository and activity_data_list:
                cache_data = [activity.dict() for activity in activity_data_list]
                await self.cache_repository.set(cache_key, cache_data, self.cache_ttl)
            
            logger.info(f"Found {len(activity_data_list)} activities matching criteria")
            return activity_data_list
            
        except Exception as e:
            logger.error(f"Error finding activities by criteria: {e}")
            return []

    async def find_by_id(self, activity_id: str) -> Optional[ActivityData]:
        """Find a single activity by its ID."""
        logger.debug(f"Finding activity by ID: {activity_id}")
        
        # Check cache first
        cache_key = f"activity:{activity_id}"
        if self.cache_repository:
            cached_result = await self.cache_repository.get(cache_key)
            if cached_result:
                return ActivityData(**cached_result)

        try:
            # Try GenericActivity first
            try:
                activity = await sync_to_async(GenericActivity.objects.prefetch_related(
                    'domain_relationships__domain'
                ).get)(id=activity_id)
                activity_data = await self._convert_to_domain_model(activity)
            except ObjectDoesNotExist:
                # Try ActivityTailored
                try:
                    activity = await sync_to_async(ActivityTailored.objects.prefetch_related(
                        'domain_relationships__domain'
                    ).get)(id=activity_id)
                    activity_data = await self._convert_tailored_to_domain_model(activity)
                except ObjectDoesNotExist:
                    logger.warning(f"Activity not found: {activity_id}")
                    return None
            
            # Cache the result
            if self.cache_repository and activity_data:
                await self.cache_repository.set(cache_key, activity_data.dict(), self.cache_ttl)
            
            return activity_data
            
        except Exception as e:
            logger.error(f"Error finding activity by ID {activity_id}: {e}")
            return None

    async def find_by_ids(self, activity_ids: List[str]) -> List[ActivityData]:
        """Find multiple activities by their IDs."""
        logger.debug(f"Finding activities by IDs: {len(activity_ids)} activities")
        
        activities = []
        for activity_id in activity_ids:
            activity = await self.find_by_id(activity_id)
            if activity:
                activities.append(activity)
        
        return activities

    async def find_by_domain(self, domain: DomainCode, limit: Optional[int] = None) -> List[ActivityData]:
        """Find activities by domain with optional limit."""
        logger.debug(f"Finding activities by domain: {domain.value}")
        
        try:
            # Query activities with domain relationships
            queryset = GenericActivity.objects.filter(
                domain_relationships__domain__code=domain.value
            ).prefetch_related('domain_relationships__domain')
            
            if limit:
                queryset = queryset[:limit]
            
            activities = await sync_to_async(list)(queryset)
            
            # Convert to domain models
            activity_data_list = []
            for activity in activities:
                try:
                    activity_data = await self._convert_to_domain_model(activity)
                    if activity_data:
                        activity_data_list.append(activity_data)
                except Exception as e:
                    logger.warning(f"Failed to convert activity {activity.id}: {e}")
                    continue
            
            return activity_data_list
            
        except Exception as e:
            logger.error(f"Error finding activities by domain {domain.value}: {e}")
            return []

    async def find_by_energy_level(self, energy_level: EnergyLevel, limit: Optional[int] = None) -> List[ActivityData]:
        """Find activities by energy level requirement."""
        logger.debug(f"Finding activities by energy level: {energy_level.value}")
        
        # This would need to be implemented based on how energy levels are stored
        # For now, return empty list as GenericActivity doesn't have direct energy_level field
        logger.warning("Energy level filtering not yet implemented for GenericActivity model")
        return []

    async def find_by_duration_range(self, min_duration: int, max_duration: int) -> List[ActivityData]:
        """Find activities within a duration range (in minutes)."""
        logger.debug(f"Finding activities by duration range: {min_duration}-{max_duration} minutes")
        
        try:
            # This would need custom logic to parse duration_range field
            # For now, return all activities (simplified implementation)
            queryset = GenericActivity.objects.prefetch_related('domain_relationships__domain')[:20]
            activities = await sync_to_async(list)(queryset)
            
            # Convert and filter by duration
            activity_data_list = []
            for activity in activities:
                try:
                    activity_data = await self._convert_to_domain_model(activity)
                    if (activity_data and 
                        min_duration <= activity_data.duration_minutes <= max_duration):
                        activity_data_list.append(activity_data)
                except Exception as e:
                    logger.warning(f"Failed to convert activity {activity.id}: {e}")
                    continue
            
            return activity_data_list
            
        except Exception as e:
            logger.error(f"Error finding activities by duration range: {e}")
            return []

    async def find_by_challenge_range(self, min_challenge: int, max_challenge: int) -> List[ActivityData]:
        """Find activities within a challenge rating range."""
        logger.debug(f"Finding activities by challenge range: {min_challenge}-{max_challenge}")
        
        # Challenge rating would need to be calculated or stored separately
        logger.warning("Challenge range filtering not yet implemented for GenericActivity model")
        return []

    async def find_by_resources(self, required_resources: List[ResourceType]) -> List[ActivityData]:
        """Find activities that require specific resources."""
        logger.debug(f"Finding activities by resources: {[r.value for r in required_resources]}")
        
        # Resource filtering would need to be implemented based on activity requirements
        logger.warning("Resource filtering not yet implemented for GenericActivity model")
        return []

    async def save_activity(self, activity: ActivityData) -> str:
        """Save an activity and return its ID."""
        logger.debug(f"Saving activity: {activity.name}")
        
        try:
            # Create new ActivityTailored instance
            tailored_activity = ActivityTailored(
                name=activity.name,
                description=activity.description,
                instructions=activity.instructions,
                duration_range=self._minutes_to_duration_range(activity.duration_minutes),
                created_on=activity.created_at.date() if activity.created_at else datetime.now().date(),
                social_requirements={}  # Default empty
            )
            
            await sync_to_async(tailored_activity.save)()
            
            # Clear cache for this activity
            if self.cache_repository:
                await self.cache_repository.delete(f"activity:{tailored_activity.id}")
            
            logger.info(f"Saved activity with ID: {tailored_activity.id}")
            return str(tailored_activity.id)
            
        except Exception as e:
            logger.error(f"Error saving activity: {e}")
            raise

    async def update_activity(self, activity: ActivityData) -> bool:
        """Update an existing activity. Returns True if successful."""
        logger.debug(f"Updating activity: {activity.id}")
        
        try:
            # Try to update ActivityTailored first
            try:
                tailored_activity = await sync_to_async(ActivityTailored.objects.get)(id=activity.id)
                tailored_activity.name = activity.name
                tailored_activity.description = activity.description
                tailored_activity.instructions = activity.instructions
                tailored_activity.duration_range = self._minutes_to_duration_range(activity.duration_minutes)
                
                await sync_to_async(tailored_activity.save)()
                
                # Clear cache
                if self.cache_repository:
                    await self.cache_repository.delete(f"activity:{activity.id}")
                
                return True
                
            except ObjectDoesNotExist:
                logger.warning(f"Activity {activity.id} not found for update")
                return False
            
        except Exception as e:
            logger.error(f"Error updating activity {activity.id}: {e}")
            return False

    async def delete_activity(self, activity_id: str) -> bool:
        """Delete an activity by ID. Returns True if successful."""
        logger.debug(f"Deleting activity: {activity_id}")
        
        try:
            # Try to delete ActivityTailored
            try:
                tailored_activity = await sync_to_async(ActivityTailored.objects.get)(id=activity_id)
                await sync_to_async(tailored_activity.delete)()
                
                # Clear cache
                if self.cache_repository:
                    await self.cache_repository.delete(f"activity:{activity_id}")
                
                return True
                
            except ObjectDoesNotExist:
                logger.warning(f"Activity {activity_id} not found for deletion")
                return False
            
        except Exception as e:
            logger.error(f"Error deleting activity {activity_id}: {e}")
            return False

    async def count_by_criteria(self, criteria: ActivitySelectionCriteria) -> int:
        """Count activities matching the given criteria using metadata-based filtering."""
        logger.debug("Counting activities by criteria")

        try:
            from django.db import models

            # Build query similar to find_by_criteria but just count
            queryset = GenericActivity.objects.all()

            # Apply metadata-based filtering
            filters = models.Q()

            # Filter by energy level if specified
            if criteria.energy_level is not None:
                energy_filter = (
                    models.Q(metadata__energy_level_min__lte=criteria.energy_level) &
                    models.Q(metadata__energy_level_max__gte=criteria.energy_level)
                ) | models.Q(metadata__isnull=True) | models.Q(metadata={})
                filters &= energy_filter

            # Filter by duration if specified
            if criteria.time_available is not None:
                duration_filter = (
                    models.Q(metadata__duration_min__lte=criteria.time_available)
                ) | models.Q(metadata__isnull=True) | models.Q(metadata={})
                filters &= duration_filter

            # Apply filters if any were specified
            if filters:
                queryset = queryset.filter(filters)

            count = await sync_to_async(queryset.count)()
            return count

        except Exception as e:
            logger.error(f"Error counting activities by criteria: {e}")
            return 0

    # ============================================================================
    # Metadata-based filtering methods implementation
    # ============================================================================

    async def find_by_energy_level_range(self, energy_level: int, limit: Optional[int] = None) -> List[ActivityData]:
        """Find activities that match the given energy level using metadata constraints."""
        logger.debug(f"Finding activities by energy level: {energy_level}")

        try:
            from django.db import models

            # Query activities where energy_level_min <= energy_level <= energy_level_max
            queryset = GenericActivity.objects.filter(
                models.Q(metadata__energy_level_min__lte=energy_level) &
                models.Q(metadata__energy_level_max__gte=energy_level)
            ).prefetch_related('domain_relationships__domain')

            if limit:
                queryset = queryset[:limit]

            activities = await sync_to_async(list)(queryset)

            # Convert to domain models
            activity_data_list = []
            for activity in activities:
                try:
                    activity_data = await self._convert_to_domain_model(activity)
                    if activity_data:
                        activity_data_list.append(activity_data)
                except Exception as e:
                    logger.warning(f"Failed to convert activity {activity.id}: {e}")
                    continue

            logger.info(f"Found {len(activity_data_list)} activities for energy level {energy_level}")
            return activity_data_list

        except Exception as e:
            logger.error(f"Error finding activities by energy level: {e}")
            return []

    async def find_by_duration_constraint(self, duration_minutes: int, limit: Optional[int] = None) -> List[ActivityData]:
        """Find activities that match the given duration using metadata constraints."""
        logger.debug(f"Finding activities by duration constraint: {duration_minutes} minutes")

        try:
            from django.db import models

            # Query activities where duration_min <= duration_minutes <= duration_max
            queryset = GenericActivity.objects.filter(
                models.Q(metadata__duration_min__lte=duration_minutes) &
                models.Q(metadata__duration_max__gte=duration_minutes)
            ).prefetch_related('domain_relationships__domain')

            if limit:
                queryset = queryset[:limit]

            activities = await sync_to_async(list)(queryset)

            # Convert to domain models
            activity_data_list = []
            for activity in activities:
                try:
                    activity_data = await self._convert_to_domain_model(activity)
                    if activity_data:
                        activity_data_list.append(activity_data)
                except Exception as e:
                    logger.warning(f"Failed to convert activity {activity.id}: {e}")
                    continue

            logger.info(f"Found {len(activity_data_list)} activities for duration {duration_minutes} minutes")
            return activity_data_list

        except Exception as e:
            logger.error(f"Error finding activities by duration constraint: {e}")
            return []

    async def find_by_energy_and_duration(self, energy_level: int, duration_minutes: int,
                                        limit: Optional[int] = None) -> List[ActivityData]:
        """Find activities that match both energy level and duration constraints."""
        logger.debug(f"Finding activities by energy {energy_level} and duration {duration_minutes}")

        try:
            from django.db import models

            # Query activities matching both constraints
            queryset = GenericActivity.objects.filter(
                models.Q(metadata__energy_level_min__lte=energy_level) &
                models.Q(metadata__energy_level_max__gte=energy_level) &
                models.Q(metadata__duration_min__lte=duration_minutes) &
                models.Q(metadata__duration_max__gte=duration_minutes)
            ).prefetch_related('domain_relationships__domain')

            if limit:
                queryset = queryset[:limit]

            activities = await sync_to_async(list)(queryset)

            # Convert to domain models
            activity_data_list = []
            for activity in activities:
                try:
                    activity_data = await self._convert_to_domain_model(activity)
                    if activity_data:
                        activity_data_list.append(activity_data)
                except Exception as e:
                    logger.warning(f"Failed to convert activity {activity.id}: {e}")
                    continue

            logger.info(f"Found {len(activity_data_list)} activities for energy {energy_level} and duration {duration_minutes}")
            return activity_data_list

        except Exception as e:
            logger.error(f"Error finding activities by energy and duration: {e}")
            return []

    async def find_by_metadata_criteria(self,
                                      energy_level: Optional[int] = None,
                                      duration_minutes: Optional[int] = None,
                                      min_challenge: Optional[int] = None,
                                      max_challenge: Optional[int] = None,
                                      physical_intensity_min: Optional[int] = None,
                                      physical_intensity_max: Optional[int] = None,
                                      social_requirement: Optional[str] = None,
                                      limit: Optional[int] = None) -> List[ActivityData]:
        """Find activities using comprehensive metadata-based filtering."""
        logger.debug(f"Finding activities by comprehensive metadata criteria")

        try:
            from django.db import models

            # Start with all activities
            queryset = GenericActivity.objects.prefetch_related('domain_relationships__domain')
            filters = models.Q()

            # Apply energy level filter
            if energy_level is not None:
                energy_filter = (
                    models.Q(metadata__energy_level_min__lte=energy_level) &
                    models.Q(metadata__energy_level_max__gte=energy_level)
                )
                filters &= energy_filter

            # Apply duration filter
            if duration_minutes is not None:
                duration_filter = (
                    models.Q(metadata__duration_min__lte=duration_minutes) &
                    models.Q(metadata__duration_max__gte=duration_minutes)
                )
                filters &= duration_filter

            # Apply challenge level filters
            if min_challenge is not None:
                filters &= models.Q(metadata__challenge_level__gte=min_challenge)
            if max_challenge is not None:
                filters &= models.Q(metadata__challenge_level__lte=max_challenge)

            # Apply physical intensity filters
            if physical_intensity_min is not None:
                filters &= models.Q(metadata__physical_intensity__gte=physical_intensity_min)
            if physical_intensity_max is not None:
                filters &= models.Q(metadata__physical_intensity__lte=physical_intensity_max)

            # Apply social requirement filter
            if social_requirement is not None:
                filters &= models.Q(metadata__social_requirement=social_requirement)

            # Apply all filters
            if filters:
                queryset = queryset.filter(filters)

            if limit:
                queryset = queryset[:limit]

            activities = await sync_to_async(list)(queryset)

            # Convert to domain models
            activity_data_list = []
            for activity in activities:
                try:
                    activity_data = await self._convert_to_domain_model(activity)
                    if activity_data:
                        activity_data_list.append(activity_data)
                except Exception as e:
                    logger.warning(f"Failed to convert activity {activity.id}: {e}")
                    continue

            logger.info(f"Found {len(activity_data_list)} activities matching comprehensive criteria")
            return activity_data_list

        except Exception as e:
            logger.error(f"Error finding activities by comprehensive metadata criteria: {e}")
            return []

    # Helper methods
    
    def _generate_criteria_cache_key(self, criteria: ActivitySelectionCriteria) -> str:
        """Generate cache key for criteria-based queries."""
        return f"activities:criteria:{hash(str(criteria.dict()))}"

    def _duration_to_range_filter(self, time_available: int) -> str:
        """Convert time available to duration range filter."""
        # Simplified mapping - would need more sophisticated logic
        if time_available <= 15:
            return "10-15"
        elif time_available <= 30:
            return "15-30"
        elif time_available <= 60:
            return "30-60"
        else:
            return "60+"

    def _minutes_to_duration_range(self, minutes: int) -> str:
        """Convert minutes to duration range string."""
        if minutes <= 15:
            return "10-15 minutes"
        elif minutes <= 30:
            return "15-30 minutes"
        elif minutes <= 60:
            return "30-60 minutes"
        else:
            return "60+ minutes"

    async def _convert_to_domain_model(self, activity: GenericActivity) -> Optional[ActivityData]:
        """Convert Django GenericActivity to domain ActivityData."""
        try:
            # Get primary domain
            domain_relationships = await sync_to_async(list)(
                activity.domain_relationships.select_related('domain')
            )
            
            if not domain_relationships:
                logger.warning(f"Activity {activity.id} has no domain relationships")
                return None
            
            # Get primary domain (highest strength or first one)
            primary_domain_rel = domain_relationships[0]
            primary_domain_code = primary_domain_rel.domain.code

            # Get duration from metadata if available, otherwise parse duration_range
            duration_minutes = self._parse_duration_range(activity.duration_range)
            challenge_rating = 50  # Default
            energy_requirement = EnergyLevel.MEDIUM  # Default

            # Use metadata if available for more accurate values
            if activity.metadata and activity.metadata != {}:
                metadata = activity.metadata

                # Use optimal duration from metadata (midpoint of range)
                if metadata.get('duration_min') and metadata.get('duration_max'):
                    duration_min = metadata['duration_min']
                    duration_max = metadata['duration_max']
                    duration_minutes = (duration_min + duration_max) // 2

                # Use challenge level from metadata
                if metadata.get('challenge_level'):
                    challenge_rating = metadata['challenge_level']

                # Map energy level to EnergyLevel enum
                energy_min = metadata.get('energy_level_min', 50)
                energy_max = metadata.get('energy_level_max', 50)
                avg_energy = (energy_min + energy_max) // 2

                if avg_energy <= 30:
                    energy_requirement = EnergyLevel.LOW
                elif avg_energy <= 70:
                    energy_requirement = EnergyLevel.MEDIUM
                else:
                    energy_requirement = EnergyLevel.HIGH

            # Map old domain codes to new DomainCode enum values
            mapped_primary_domain = CleanDomainMapper.map_old_to_new(primary_domain_code)
            mapped_sub_domains = [
                CleanDomainMapper.map_old_to_new(rel.domain.code)
                for rel in domain_relationships[1:]
            ]

            return ActivityData(
                id=str(activity.id),
                name=activity.name,
                description=activity.description,
                instructions=activity.instructions or "",
                domain=mapped_primary_domain,
                sub_domains=mapped_sub_domains,
                duration_minutes=duration_minutes,
                challenge_rating=challenge_rating,
                energy_requirement=energy_requirement,
                required_resources=[],  # Would need parsing from social_requirements
                created_at=datetime.combine(activity.created_on, datetime.min.time()),
                version=1,
                confidence=1.0
            )
            
        except Exception as e:
            logger.error(f"Error converting GenericActivity {activity.id} to domain model: {e}")
            return None

    async def _convert_tailored_to_domain_model(self, activity: ActivityTailored) -> Optional[ActivityData]:
        """Convert Django ActivityTailored to domain ActivityData."""
        try:
            # Similar conversion logic for ActivityTailored
            domain_relationships = await sync_to_async(list)(
                activity.domain_relationships.select_related('domain')
            )
            
            if not domain_relationships:
                logger.warning(f"ActivityTailored {activity.id} has no domain relationships")
                return None
            
            primary_domain_rel = domain_relationships[0]
            primary_domain_code = primary_domain_rel.domain.code
            duration_minutes = self._parse_duration_range(activity.duration_range)

            # Map old domain codes to new DomainCode enum values
            mapped_primary_domain = CleanDomainMapper.map_old_to_new(primary_domain_code)
            mapped_sub_domains = [
                CleanDomainMapper.map_old_to_new(rel.domain.code)
                for rel in domain_relationships[1:]
            ]

            return ActivityData(
                id=str(activity.id),
                name=activity.name,
                description=activity.description,
                instructions=activity.instructions or "",
                domain=mapped_primary_domain,
                sub_domains=mapped_sub_domains,
                duration_minutes=duration_minutes,
                challenge_rating=50,  # Default
                energy_requirement=EnergyLevel.MEDIUM,  # Default
                required_resources=[],
                created_at=datetime.combine(activity.created_on, datetime.min.time()),
                version=2,  # Tailored activities are version 2
                confidence=0.8  # Slightly lower confidence for tailored
            )
            
        except Exception as e:
            logger.error(f"Error converting ActivityTailored {activity.id} to domain model: {e}")
            return None

    def _parse_duration_range(self, duration_range: str) -> int:
        """Parse duration range string to minutes."""
        try:
            # Simple parsing - would need more sophisticated logic
            if "10-15" in duration_range:
                return 12
            elif "15-30" in duration_range:
                return 22
            elif "30-60" in duration_range:
                return 45
            elif "60+" in duration_range:
                return 90
            else:
                return 30  # Default
        except:
            return 30  # Default fallback
