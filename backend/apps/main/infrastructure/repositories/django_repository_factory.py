"""
Django Repository Factory Implementation

This module provides a concrete factory for creating Django-based repository
instances with proper dependency injection and configuration.
"""

import logging
from typing import Optional

from ...domain.repositories.repository_interfaces import (
    RepositoryFactory,
    ActivityRepositoryInterface,
    WheelRepositoryInterface,
    UserRepositoryInterface,
    CacheRepositoryInterface
)
from .django_activity_repository import DjangoActivityRepository
from .django_wheel_repository import DjangoWheelRepository
from .django_user_repository import DjangoUserRepository
from .memory_cache_repository import MemoryCacheRepository

logger = logging.getLogger(__name__)


class DjangoRepositoryFactory(RepositoryFactory):
    """Factory for creating Django-based repository instances."""

    def __init__(self, use_cache: bool = True):
        """Initialize factory with configuration options."""
        self.use_cache = use_cache
        self._cache_repository: Optional[CacheRepositoryInterface] = None
        
        if self.use_cache:
            self._cache_repository = MemoryCacheRepository()
            logger.info("Repository factory initialized with caching enabled")
        else:
            logger.info("Repository factory initialized with caching disabled")

    def create_activity_repository(self) -> ActivityRepositoryInterface:
        """Create an activity repository instance."""
        logger.debug("Creating Django activity repository")
        return DjangoActivityRepository(cache_repository=self._cache_repository)

    def create_wheel_repository(self) -> WheelRepositoryInterface:
        """Create a wheel repository instance."""
        logger.debug("Creating Django wheel repository")
        return DjangoWheelRepository(cache_repository=self._cache_repository)

    def create_user_repository(self) -> UserRepositoryInterface:
        """Create a user repository instance."""
        logger.debug("Creating Django user repository")
        return DjangoUserRepository(cache_repository=self._cache_repository)

    def create_cache_repository(self) -> CacheRepositoryInterface:
        """Create a cache repository instance."""
        logger.debug("Creating memory cache repository")
        if not self._cache_repository:
            self._cache_repository = MemoryCacheRepository()
        return self._cache_repository

    def get_cache_stats(self) -> dict:
        """Get cache statistics if caching is enabled."""
        if self._cache_repository and hasattr(self._cache_repository, 'get_stats'):
            return self._cache_repository.get_stats()
        return {}

    async def cleanup_cache(self) -> int:
        """Cleanup expired cache entries if caching is enabled."""
        if self._cache_repository and hasattr(self._cache_repository, 'cleanup_expired'):
            return await self._cache_repository.cleanup_expired()
        return 0


# Global factory instance for dependency injection
_default_factory: Optional[DjangoRepositoryFactory] = None


def get_repository_factory() -> DjangoRepositoryFactory:
    """Get the default repository factory instance."""
    global _default_factory
    if _default_factory is None:
        _default_factory = DjangoRepositoryFactory(use_cache=True)
    return _default_factory


def set_repository_factory(factory: DjangoRepositoryFactory) -> None:
    """Set a custom repository factory instance."""
    global _default_factory
    _default_factory = factory
    logger.info("Custom repository factory set")


# Convenience functions for getting repositories
def get_activity_repository() -> ActivityRepositoryInterface:
    """Get an activity repository instance."""
    return get_repository_factory().create_activity_repository()


def get_wheel_repository() -> WheelRepositoryInterface:
    """Get a wheel repository instance."""
    return get_repository_factory().create_wheel_repository()


def get_user_repository() -> UserRepositoryInterface:
    """Get a user repository instance."""
    return get_repository_factory().create_user_repository()


def get_cache_repository() -> CacheRepositoryInterface:
    """Get a cache repository instance."""
    return get_repository_factory().create_cache_repository()
