"""
Django ORM Implementation of Wheel Repository Interface

This module provides the concrete implementation of WheelRepositoryInterface
using Django ORM for wheel data persistence and retrieval.
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime
from django.db import models, transaction
from django.core.exceptions import ObjectDoesNotExist
from asgiref.sync import sync_to_async

from apps.main.models import Wheel, WheelItem
from apps.activity.models import ActivityTailored
from ...domain.repositories.repository_interfaces import WheelRepositoryInterface
from ...domain.models.wheel_models import WheelData, WheelItemData
from ...domain.enums.domain_enums import DomainCode, TrustPhase
from ...services.domain_management_service import CleanDomainMapper

logger = logging.getLogger(__name__)


class DjangoWheelRepository(WheelRepositoryInterface):
    """Django ORM implementation of wheel repository."""

    def __init__(self, cache_repository=None):
        """Initialize with optional cache repository."""
        self.cache_repository = cache_repository
        self.cache_ttl = 600  # 10 minutes default TTL for wheels

    async def save_wheel(self, wheel: WheelData, user_profile_id: str = None) -> str:
        """Save a wheel and return its ID."""
        logger.debug(f"Saving wheel: {wheel.name} for user: {user_profile_id}")
        
        try:
            # Use sync transaction.atomic with sync_to_async wrapper
            @sync_to_async
            def create_wheel_and_items():
                with transaction.atomic():
                    # Create Wheel instance
                    django_wheel = Wheel(
                        name=wheel.name,
                        created_by="domain_service",  # Could be parameterized
                        created_at=wheel.created_at.date() if wheel.created_at else datetime.now().date()
                    )
                    django_wheel.save()

                    # Create WheelItem instances and track ID mappings
                    created_items = []

                    for i, item in enumerate(wheel.items):
                        # First, we need to create or find the ActivityTailored object
                        # For now, we'll create a minimal ActivityTailored since the domain service
                        # should have already handled activity tailoring
                        from apps.activity.models import ActivityTailored
                        from apps.user.models import UserProfile

                        # Try to find existing ActivityTailored by activity_id
                        activity_tailored = None
                        try:
                            # If activity_id is numeric, it might be an existing ActivityTailored ID
                            if item.activity_id.isdigit():
                                activity_tailored = ActivityTailored.objects.get(id=int(item.activity_id))
                        except (ActivityTailored.DoesNotExist, ValueError):
                            pass

                        # If not found, create a new ActivityTailored
                        if not activity_tailored:
                            # Get user profile from the provided user_profile_id
                            try:
                                if user_profile_id:
                                    user_profile = UserProfile.objects.get(id=user_profile_id)
                                else:
                                    user_profile = UserProfile.objects.first()  # Fallback
                                if not user_profile:
                                    logger.error("No user profile available for ActivityTailored creation")
                                    continue

                                # Use original name - cleaning is handled by domain service during tailoring
                                cleaned_name = item.name

                                # CRITICAL FIX: Convert duration_minutes to duration_range format using proper mapping
                                # ActivityTailored model uses duration_range (CharField) with ranges like "10-15 minutes"
                                try:
                                    duration_range = self._minutes_to_duration_range(item.duration_minutes)
                                except Exception as e:
                                    logger.error(f"🚨 ERROR: Failed to convert duration {item.duration_minutes}, using default: {e}")
                                    duration_range = "15-30 minutes"  # Safe default

                                # Get required related objects
                                from apps.activity.models import GenericActivity
                                from apps.user.models import UserEnvironment
                                from datetime import date

                                # Generate unique code for the activity
                                import uuid
                                activity_code = f"wheel_gen_{str(uuid.uuid4())[:8]}"

                                # Get or create a generic activity (fallback) with cleaned name
                                generic_activity, _ = GenericActivity.objects.get_or_create(
                                    name=cleaned_name,  # Use cleaned name
                                    defaults={
                                        'code': activity_code,  # CRITICAL FIX: Provide unique code
                                        'description': item.description or "",
                                        'created_on': date.today(),
                                        'duration_range': duration_range,
                                        'instructions': f"Complete this {cleaned_name.lower()} activity",
                                        'social_requirements': {"min_participants": 1, "max_participants": 1}
                                    }
                                )

                                # Get user environment (fallback to first available)
                                user_environment = UserEnvironment.objects.filter(user_profile=user_profile).first()
                                if not user_environment:
                                    logger.warning(f"No user environment found for user {user_profile.id}, skipping ActivityTailored creation")
                                    continue

                                # CRITICAL FIX: Only use fields that are in the unique constraint for get_or_create
                                # The unique constraint is: user_profile, generic_activity, user_environment, version
                                # duration_range is NOT in the constraint, so it should only be in defaults
                                activity_tailored, created = ActivityTailored.objects.get_or_create(
                                    user_profile=user_profile,
                                    generic_activity=generic_activity,
                                    user_environment=user_environment,
                                    version=1,
                                    # REMOVED: duration_range from filter - it's not in unique constraint!
                                    defaults={
                                        'name': cleaned_name,
                                        'description': item.description or "",
                                        'instructions': f"Complete this {cleaned_name.lower()} activity",
                                        'created_on': date.today(),
                                        'social_requirements': {"min_participants": 1, "max_participants": 1},
                                        'base_challenge_rating': item.challenge_rating,
                                        'challengingness': {"openness": 50, "conscientiousness": 50, "extraversion": 50, "agreeableness": 50, "neuroticism": 50},
                                        'tailorization_level': 75,
                                        'duration_range': duration_range,  # MOVED: duration_range to defaults only
                                    }
                                )

                                if created:
                                    logger.info(f"✅ Created new ActivityTailored for {item.name} (ID: {activity_tailored.id})")
                                else:
                                    logger.info(f"♻️ Reused existing ActivityTailored for {item.name} (ID: {activity_tailored.id})")
                            except Exception as e:
                                logger.error(f"Failed to create ActivityTailored for item {item.name}: {e}")
                                continue

                        # Generate unique wheel item ID
                        wheel_item_id = f"item_{django_wheel.id}_{i+1}_{item.activity_id[:8] if len(item.activity_id) > 8 else item.activity_id}"

                        # Create WheelItem with only the fields that exist in the Django model
                        wheel_item = WheelItem(
                            id=wheel_item_id,
                            wheel=django_wheel,
                            percentage=item.percentage,
                            activity_tailored=activity_tailored
                        )
                        wheel_item.save()

                        # Track the created item with its database ID for domain model update
                        created_items.append((item, wheel_item_id))

                    return django_wheel, created_items

            django_wheel, created_items = await create_wheel_and_items()

            # CRITICAL FIX: Update the domain model with real database IDs
            logger.debug(f"Updating domain model with {len(created_items)} database IDs")

            try:
                for domain_item, database_id in created_items:
                    old_id = domain_item.id
                    domain_item.id = database_id
                    logger.debug(f"Updated item ID: {old_id} → {database_id}")

                # Update the wheel ID in the domain model
                wheel.id = str(django_wheel.id)
                logger.debug(f"Updated wheel ID in domain model: {wheel.id}")

            except Exception as e:
                logger.error(f"Failed to update domain model IDs: {e}")
                # Continue execution - this is not a fatal error

            # Clear cache for user wheels
            if self.cache_repository:
                # We don't have user_id in WheelData, so clear general cache
                await self.cache_repository.clear_pattern("wheels:*")

            logger.info(f"Saved wheel with ID: {django_wheel.id}")
            return str(django_wheel.id)
                
        except Exception as e:
            logger.error(f"Error saving wheel: {e}")
            raise

    async def find_by_id(self, wheel_id: str) -> Optional[WheelData]:
        """Find a wheel by its ID."""
        logger.debug(f"Finding wheel by ID: {wheel_id}")
        
        # Check cache first
        cache_key = f"wheel:{wheel_id}"
        if self.cache_repository:
            cached_result = await self.cache_repository.get(cache_key)
            if cached_result:
                return WheelData(**cached_result)

        try:
            # Get wheel with related items
            wheel = await sync_to_async(Wheel.objects.prefetch_related('items').get)(id=wheel_id)
            wheel_data = await self._convert_to_domain_model(wheel)
            
            # Cache the result
            if self.cache_repository and wheel_data:
                await self.cache_repository.set(cache_key, wheel_data.dict(), self.cache_ttl)
            
            return wheel_data
            
        except ObjectDoesNotExist:
            logger.warning(f"Wheel not found: {wheel_id}")
            return None
        except Exception as e:
            logger.error(f"Error finding wheel by ID {wheel_id}: {e}")
            return None

    async def find_by_user_id(self, user_id: str, limit: Optional[int] = None) -> List[WheelData]:
        """Find wheels created by a specific user."""
        logger.debug(f"Finding wheels by user ID: {user_id}")
        
        # Check cache first
        cache_key = f"wheels:user:{user_id}:limit:{limit}"
        if self.cache_repository:
            cached_result = await self.cache_repository.get(cache_key)
            if cached_result:
                return [WheelData(**wheel_dict) for wheel_dict in cached_result]

        try:
            # Note: Current Wheel model doesn't have user_id field
            # This would need to be added or inferred from created_by field
            queryset = Wheel.objects.prefetch_related('items').order_by('-created_at')
            
            if limit:
                queryset = queryset[:limit]
            
            wheels = await sync_to_async(list)(queryset)
            
            # Convert to domain models
            wheel_data_list = []
            for wheel in wheels:
                try:
                    wheel_data = await self._convert_to_domain_model(wheel)
                    if wheel_data:
                        wheel_data_list.append(wheel_data)
                except Exception as e:
                    logger.warning(f"Failed to convert wheel {wheel.id}: {e}")
                    continue
            
            # Cache the results
            if self.cache_repository and wheel_data_list:
                cache_data = [wheel.dict() for wheel in wheel_data_list]
                await self.cache_repository.set(cache_key, cache_data, self.cache_ttl)
            
            logger.info(f"Found {len(wheel_data_list)} wheels for user {user_id}")
            return wheel_data_list
            
        except Exception as e:
            logger.error(f"Error finding wheels by user ID {user_id}: {e}")
            return []

    async def find_recent_wheels(self, limit: int = 10) -> List[WheelData]:
        """Find the most recently created wheels."""
        logger.debug(f"Finding {limit} recent wheels")
        
        # Check cache first
        cache_key = f"wheels:recent:{limit}"
        if self.cache_repository:
            cached_result = await self.cache_repository.get(cache_key)
            if cached_result:
                return [WheelData(**wheel_dict) for wheel_dict in cached_result]

        try:
            wheels = await sync_to_async(list)(
                Wheel.objects.prefetch_related('items').order_by('-created_at')[:limit]
            )
            
            # Convert to domain models
            wheel_data_list = []
            for wheel in wheels:
                try:
                    wheel_data = await self._convert_to_domain_model(wheel)
                    if wheel_data:
                        wheel_data_list.append(wheel_data)
                except Exception as e:
                    logger.warning(f"Failed to convert wheel {wheel.id}: {e}")
                    continue
            
            # Cache the results
            if self.cache_repository and wheel_data_list:
                cache_data = [wheel.dict() for wheel in wheel_data_list]
                await self.cache_repository.set(cache_key, cache_data, self.cache_ttl)
            
            return wheel_data_list
            
        except Exception as e:
            logger.error(f"Error finding recent wheels: {e}")
            return []

    async def update_wheel(self, wheel: WheelData, user_profile_id: str = None) -> bool:
        """Update an existing wheel. Returns True if successful."""
        logger.debug(f"Updating wheel: {wheel.id} for user: {user_profile_id}")
        
        if not wheel.id:
            logger.error("Cannot update wheel without ID")
            return False

        try:
            # Use sync transaction.atomic with sync_to_async wrapper
            @sync_to_async
            def update_wheel_and_items():
                with transaction.atomic():
                    # Get existing wheel
                    django_wheel = Wheel.objects.get(id=wheel.id)

                    # Update wheel fields
                    django_wheel.name = wheel.name
                    django_wheel.save()

                    # Delete existing items
                    WheelItem.objects.filter(wheel=django_wheel).delete()

                    # Create new items
                    for i, item in enumerate(wheel.items):
                        # First, we need to create or find the ActivityTailored object
                        from apps.activity.models import ActivityTailored
                        from apps.user.models import UserProfile

                        # Try to find existing ActivityTailored by activity_id
                        activity_tailored = None
                        try:
                            # If activity_id is numeric, it might be an existing ActivityTailored ID
                            if item.activity_id.isdigit():
                                activity_tailored = ActivityTailored.objects.get(id=int(item.activity_id))
                        except (ActivityTailored.DoesNotExist, ValueError):
                            pass

                        # If not found, create a new ActivityTailored
                        if not activity_tailored:
                            try:
                                if user_profile_id:
                                    user_profile = UserProfile.objects.get(id=user_profile_id)
                                else:
                                    user_profile = UserProfile.objects.first()  # Fallback
                                if not user_profile:
                                    logger.error("No user profile available for ActivityTailored creation")
                                    continue

                                # CRITICAL FIX: Clean activity name to prevent duplicate suffixes
                                try:
                                    cleaned_name = self._clean_activity_name(item.name)
                                except Exception as e:
                                    logger.error(f"🚨 ERROR: Failed to clean activity name, using original: {e}")
                                    cleaned_name = item.name

                                # CRITICAL FIX: Convert duration_minutes to duration_range format using proper mapping
                                # ActivityTailored model uses duration_range (CharField) with ranges like "10-15 minutes"
                                try:
                                    duration_range = self._minutes_to_duration_range(item.duration_minutes)
                                except Exception as e:
                                    logger.error(f"🚨 ERROR: Failed to convert duration {item.duration_minutes}, using default: {e}")
                                    duration_range = "15-30 minutes"  # Safe default

                                # Get required related objects
                                from apps.activity.models import GenericActivity
                                from apps.user.models import UserEnvironment
                                from datetime import date

                                # Generate unique code for the activity
                                import uuid
                                activity_code = f"wheel_gen_{str(uuid.uuid4())[:8]}"

                                # Get or create a generic activity (fallback) with cleaned name
                                generic_activity, _ = GenericActivity.objects.get_or_create(
                                    name=cleaned_name,  # Use cleaned name
                                    defaults={
                                        'code': activity_code,  # CRITICAL FIX: Provide unique code
                                        'description': item.description or "",
                                        'created_on': date.today(),
                                        'duration_range': duration_range,
                                        'instructions': f"Complete this {cleaned_name.lower()} activity",
                                        'social_requirements': {"min_participants": 1, "max_participants": 1}
                                    }
                                )

                                # Get user environment (fallback to first available)
                                user_environment = UserEnvironment.objects.filter(user_profile=user_profile).first()
                                if not user_environment:
                                    logger.warning(f"No user environment found for user {user_profile.id}, skipping ActivityTailored creation")
                                    continue

                                # CRITICAL FIX: Only use fields that are in the unique constraint for get_or_create
                                # The unique constraint is: user_profile, generic_activity, user_environment, version
                                # duration_range is NOT in the constraint, so it should only be in defaults
                                activity_tailored, created = ActivityTailored.objects.get_or_create(
                                    user_profile=user_profile,
                                    generic_activity=generic_activity,
                                    user_environment=user_environment,
                                    version=1,
                                    # REMOVED: duration_range from filter - it's not in unique constraint!
                                    defaults={
                                        'name': cleaned_name,  # Use cleaned name to prevent duplicate suffixes
                                        'description': item.description or "",
                                        'instructions': f"Complete this {cleaned_name.lower()} activity",
                                        'created_on': date.today(),
                                        'social_requirements': {"min_participants": 1, "max_participants": 1},
                                        'base_challenge_rating': item.challenge_rating,
                                        'challengingness': {"openness": 50, "conscientiousness": 50, "extraversion": 50, "agreeableness": 50, "neuroticism": 50},
                                        'tailorization_level': 75,
                                        'duration_range': duration_range,  # MOVED: duration_range to defaults only
                                    }
                                )

                                if created:
                                    logger.info(f"✅ Created new ActivityTailored for {cleaned_name} (ID: {activity_tailored.id})")
                                else:
                                    logger.info(f"♻️ Reused existing ActivityTailored for {cleaned_name} (ID: {activity_tailored.id})")
                                    # CRITICAL FIX: Update existing activity name if it has duplicate suffixes
                                    if activity_tailored.name != cleaned_name:
                                        logger.info(f"🧹 Updating existing activity name: '{activity_tailored.name}' → '{cleaned_name}'")
                                        activity_tailored.name = cleaned_name
                                        activity_tailored.save()
                            except Exception as e:
                                logger.error(f"Failed to create ActivityTailored for item {item.name}: {e}")
                                continue

                        # Generate unique wheel item ID
                        wheel_item_id = f"item_{django_wheel.id}_{i+1}_{item.activity_id[:8] if len(item.activity_id) > 8 else item.activity_id}"

                        # Create WheelItem with only the fields that exist in the Django model
                        wheel_item = WheelItem(
                            id=wheel_item_id,
                            wheel=django_wheel,
                            percentage=item.percentage,
                            activity_tailored=activity_tailored
                        )
                        wheel_item.save()

                    return True

            await update_wheel_and_items()

            # Clear cache
            if self.cache_repository:
                await self.cache_repository.delete(f"wheel:{wheel.id}")
                await self.cache_repository.clear_pattern("wheels:*")
                
                return True
                
        except ObjectDoesNotExist:
            logger.warning(f"Wheel {wheel.id} not found for update")
            return False
        except Exception as e:
            logger.error(f"Error updating wheel {wheel.id}: {e}")
            return False

    async def delete_wheel(self, wheel_id: str) -> bool:
        """Delete a wheel by ID. Returns True if successful."""
        logger.debug(f"Deleting wheel: {wheel_id}")
        
        try:
            # Use sync transaction.atomic with sync_to_async wrapper
            @sync_to_async
            def delete_wheel_and_items():
                with transaction.atomic():
                    wheel = Wheel.objects.get(id=wheel_id)

                    # Delete related items first (cascade should handle this, but being explicit)
                    WheelItem.objects.filter(wheel=wheel).delete()

                    # Delete wheel
                    wheel.delete()
                    return True

            await delete_wheel_and_items()

            # Clear cache
            if self.cache_repository:
                await self.cache_repository.delete(f"wheel:{wheel_id}")
                await self.cache_repository.clear_pattern("wheels:*")
                
                return True
                
        except ObjectDoesNotExist:
            logger.warning(f"Wheel {wheel_id} not found for deletion")
            return False
        except Exception as e:
            logger.error(f"Error deleting wheel {wheel_id}: {e}")
            return False

    async def count_by_user(self, user_id: str) -> int:
        """Count wheels created by a specific user."""
        logger.debug(f"Counting wheels for user: {user_id}")
        
        try:
            # Note: Current model doesn't have user_id, so return total count
            count = await sync_to_async(Wheel.objects.count)()
            return count
            
        except Exception as e:
            logger.error(f"Error counting wheels for user {user_id}: {e}")
            return 0

    # Helper methods
    
    async def _convert_to_domain_model(self, wheel: Wheel) -> Optional[WheelData]:
        """Convert Django Wheel to domain WheelData."""
        try:
            # Get wheel items
            items = await sync_to_async(list)(wheel.items.all())
            
            # Convert items to domain models
            wheel_items = []
            for item in items:
                try:
                    # Map old domain code to new DomainCode enum value
                    mapped_domain = CleanDomainMapper.map_old_to_new(item.domain)

                    wheel_item_data = WheelItemData(
                        id=str(item.id),
                        activity_id=str(item.activity_id),
                        name=item.name,
                        description=item.description,
                        percentage=float(item.percentage),
                        probability=float(item.percentage) / 100.0,
                        position=item.position,
                        domain=mapped_domain,
                        challenge_rating=item.challenge_rating,
                        duration_minutes=item.duration_minutes
                    )
                    wheel_items.append(wheel_item_data)
                except Exception as e:
                    logger.warning(f"Failed to convert wheel item {item.id}: {e}")
                    continue
            
            if not wheel_items:
                logger.warning(f"Wheel {wheel.id} has no valid items")
                return None
            
            # Create wheel data
            wheel_data = WheelData(
                id=str(wheel.id),
                name=wheel.name,
                items=wheel_items,
                created_at=datetime.combine(wheel.created_at, datetime.min.time()),
                trust_phase=TrustPhase.FOUNDATION  # Default - would need to be stored
            )
            
            return wheel_data
            
        except Exception as e:
            logger.error(f"Error converting Wheel {wheel.id} to domain model: {e}")
            return None



    def _minutes_to_duration_range(self, minutes: int) -> str:
        """Convert minutes to duration range string matching ActivityTailored model format."""
        if minutes <= 15:
            return "10-15 minutes"
        elif minutes <= 30:
            return "15-30 minutes"
        elif minutes <= 60:
            return "30-60 minutes"
        else:
            return "60+ minutes"
