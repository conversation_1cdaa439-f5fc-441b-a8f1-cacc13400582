"""
Management command to create an anonymous user for tracking non-sensitive events
without requiring authentication.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from apps.user.models import UserProfile


class Command(BaseCommand):
    help = 'Create an anonymous user for tracking non-sensitive events'

    def handle(self, *args, **options):
        try:
            # Check if anonymous user already exists
            anonymous_user = User.objects.filter(username='anonymous').first()
            if anonymous_user:
                self.stdout.write(
                    self.style.WARNING('Anonymous user already exists')
                )
                return

            # Create anonymous user
            anonymous_user = User.objects.create_user(
                username='anonymous',
                email='<EMAIL>',
                password='anonymous_password_not_for_login',
                first_name='Anonymous',
                last_name='User'
            )
            anonymous_user.is_active = False  # Prevent login
            anonymous_user.save()

            # Create anonymous user profile
            anonymous_profile = UserProfile.objects.create(
                user=anonymous_user,
                profile_name='Anonymous',
                is_real=False
            )

            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully created anonymous user and profile: {anonymous_profile.id}'
                )
            )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to create anonymous user: {e}')
            )
