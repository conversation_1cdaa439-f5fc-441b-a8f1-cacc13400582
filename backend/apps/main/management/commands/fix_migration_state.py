from django.core.management.base import BaseCommand
from django.core.management import call_command
from django.db import connection
from django.db.migrations.recorder import MigrationRecorder
from django.db.migrations.recorder import MigrationRecorder


class Command(BaseCommand):
    help = 'Fix inconsistent migration state by fake-applying missing migrations'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without actually doing it',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be made'))
        
        # Check current migration state
        recorder = MigrationRecorder(connection)
        applied_migrations = recorder.applied_migrations()
        
        self.stdout.write("Current applied migrations for 'main' app:")
        main_migrations = [m for m in applied_migrations if m[0] == 'main']
        for app, migration in sorted(main_migrations):
            self.stdout.write(f"  {app}.{migration}")
        
        # Check if the problematic merge migration is applied
        merge_migration = ('main', '0003_merge_20250426_1126')
        if merge_migration in applied_migrations:
            self.stdout.write(self.style.WARNING(
                f"Found applied merge migration: {merge_migration[1]}"
            ))
            
            # Check for missing dependencies
            missing_migrations = []
            required_migrations = [
                ('main', '0002_initial'),
                ('main', '0002_add_tool_call_details'),
            ]
            
            for migration in required_migrations:
                if migration not in applied_migrations:
                    missing_migrations.append(migration)
                    self.stdout.write(self.style.ERROR(
                        f"Missing dependency: {migration[1]}"
                    ))
            
            if missing_migrations:
                if not dry_run:
                    self.stdout.write("Fake-applying missing migrations...")
                    # Directly insert into django_migrations table to bypass consistency checks
                    recorder = MigrationRecorder(connection)

                    for app, migration in missing_migrations:
                        self.stdout.write(f"Fake-applying {app}.{migration}")
                        recorder.record_applied(app, migration)
                else:
                    self.stdout.write("Would fake-apply these missing migrations:")
                    for app, migration in missing_migrations:
                        self.stdout.write(f"  {app}.{migration}")
            else:
                self.stdout.write(self.style.SUCCESS("No missing dependencies found"))
        else:
            self.stdout.write("Merge migration not found in applied migrations")
        
        if not dry_run:
            self.stdout.write(self.style.SUCCESS("Migration state fix completed"))
        else:
            self.stdout.write(self.style.WARNING("DRY RUN completed - no changes made"))
