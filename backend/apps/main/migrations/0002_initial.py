# Generated by Django 4.2.8 on 2025-03-23 12:52

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0001_initial'),
        ('contenttypes', '0002_remove_content_type_name'),
        ('main', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='userfeedback',
            name='user_profile',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='user.userprofile'),
        ),
        migrations.AddField(
            model_name='historyevent',
            name='content_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype'),
        ),
        migrations.AddField(
            model_name='historyevent',
            name='secondary_content_type',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='secondary_events', to='contenttypes.contenttype'),
        ),
        migrations.AddField(
            model_name='historyevent',
            name='user_profile',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='user.userprofile'),
        ),
        migrations.AddField(
            model_name='genericagent',
            name='available_tools',
            field=models.ManyToManyField(help_text='Tools this agent can utilize', related_name='available_to_agents', to='main.agenttool'),
        ),
        migrations.AddField(
            model_name='genericagent',
            name='benchmark_metrics',
            field=models.ManyToManyField(help_text="Metrics used to benchmark this agent's performance", related_name='measured_on_agents', to='main.benchmarkmetric'),
        ),
        migrations.AddField(
            model_name='customagent',
            name='generic_agent',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='custom_agents', to='main.genericagent'),
        ),
        migrations.AddField(
            model_name='customagent',
            name='user_profile',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='custom_agents', to='user.userprofile'),
        ),
        migrations.AddIndex(
            model_name='benchmarkmetric',
            index=models.Index(fields=['code'], name='main_benchm_code_dd52ca_idx'),
        ),
        migrations.AddIndex(
            model_name='agenttool',
            index=models.Index(fields=['code'], name='main_agentt_code_0b6631_idx'),
        ),
        migrations.AddIndex(
            model_name='agenttool',
            index=models.Index(fields=['is_active'], name='main_agentt_is_acti_179edc_idx'),
        ),
        migrations.AddField(
            model_name='agentrun',
            name='agent',
            field=models.ForeignKey(help_text='The agent that was executed', on_delete=django.db.models.deletion.PROTECT, related_name='runs', to='main.genericagent'),
        ),
        migrations.AddField(
            model_name='agentrun',
            name='user_profile',
            field=models.ForeignKey(help_text='The user this agent run was performed for', on_delete=django.db.models.deletion.CASCADE, related_name='agent_runs', to='user.userprofile'),
        ),
        migrations.AddField(
            model_name='agentrecommendation',
            name='created_by_run',
            field=models.ForeignKey(help_text='The agent run that created this recommendation', on_delete=django.db.models.deletion.CASCADE, related_name='recommendations', to='main.agentrun'),
        ),
        migrations.AddField(
            model_name='agentrecommendation',
            name='reviewed_by',
            field=models.ForeignKey(blank=True, help_text='The agent run that reviewed this recommendation', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reviewed_recommendations', to='main.agentrun'),
        ),
        migrations.AddField(
            model_name='agentmetric',
            name='agent_run',
            field=models.ForeignKey(help_text='The agent run this metric is for', on_delete=django.db.models.deletion.CASCADE, related_name='metrics', to='main.agentrun'),
        ),
        migrations.AddField(
            model_name='agentmetric',
            name='metric',
            field=models.ForeignKey(help_text='The metric being measured', on_delete=django.db.models.deletion.PROTECT, related_name='measurements', to='main.benchmarkmetric'),
        ),
        migrations.AddField(
            model_name='agentmemory',
            name='custom_agent',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='memories', to='main.customagent'),
        ),
        migrations.AddField(
            model_name='agentgoal',
            name='custom_agent',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='agent_goals', to='main.customagent'),
        ),
        migrations.AddIndex(
            model_name='historyevent',
            index=models.Index(fields=['content_type', 'object_id'], name='main_histor_content_17e564_idx'),
        ),
        migrations.AddIndex(
            model_name='historyevent',
            index=models.Index(fields=['secondary_content_type', 'secondary_object_id'], name='main_histor_seconda_1d95ff_idx'),
        ),
        migrations.AddIndex(
            model_name='historyevent',
            index=models.Index(fields=['timestamp'], name='main_histor_timesta_aec51d_idx'),
        ),
        migrations.AddIndex(
            model_name='historyevent',
            index=models.Index(fields=['event_type'], name='main_histor_event_t_015083_idx'),
        ),
        migrations.AddIndex(
            model_name='genericagent',
            index=models.Index(fields=['role'], name='main_generi_role_f109cf_idx'),
        ),
        migrations.AddIndex(
            model_name='genericagent',
            index=models.Index(fields=['is_active'], name='main_generi_is_acti_cd58c1_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='customagent',
            unique_together={('user_profile', 'generic_agent')},
        ),
        migrations.AddIndex(
            model_name='agentrun',
            index=models.Index(fields=['workflow_id'], name='main_agentr_workflo_d7697f_idx'),
        ),
        migrations.AddIndex(
            model_name='agentrun',
            index=models.Index(fields=['agent'], name='main_agentr_agent_i_7bd18f_idx'),
        ),
        migrations.AddIndex(
            model_name='agentrun',
            index=models.Index(fields=['user_profile'], name='main_agentr_user_pr_c66229_idx'),
        ),
        migrations.AddIndex(
            model_name='agentrun',
            index=models.Index(fields=['status'], name='main_agentr_status_36e159_idx'),
        ),
        migrations.AddIndex(
            model_name='agentrun',
            index=models.Index(fields=['started_at'], name='main_agentr_started_442aa8_idx'),
        ),
        migrations.AddIndex(
            model_name='agentrecommendation',
            index=models.Index(fields=['status'], name='main_agentr_status_d534df_idx'),
        ),
        migrations.AddIndex(
            model_name='agentrecommendation',
            index=models.Index(fields=['created_by_run'], name='main_agentr_created_9aa0ab_idx'),
        ),
        migrations.AddIndex(
            model_name='agentrecommendation',
            index=models.Index(fields=['target_model'], name='main_agentr_target__9891d6_idx'),
        ),
        migrations.AddIndex(
            model_name='agentrecommendation',
            index=models.Index(fields=['priority'], name='main_agentr_priorit_083788_idx'),
        ),
        migrations.AddIndex(
            model_name='agentrecommendation',
            index=models.Index(fields=['created_at'], name='main_agentr_created_d8919f_idx'),
        ),
        migrations.AddIndex(
            model_name='agentmetric',
            index=models.Index(fields=['agent_run'], name='main_agentm_agent_r_7faf1d_idx'),
        ),
        migrations.AddIndex(
            model_name='agentmetric',
            index=models.Index(fields=['metric'], name='main_agentm_metric__e1752c_idx'),
        ),
        migrations.AddIndex(
            model_name='agentmetric',
            index=models.Index(fields=['timestamp'], name='main_agentm_timesta_99a419_idx'),
        ),
    ]
