"""
Wheel Generation Service

Central orchestrator for complete wheel generation business logic.
This service coordinates all other business services to generate wheels.
"""

import logging
import time
from datetime import datetime, timezone
from typing import List

from ..models.wheel_models import WheelGenerationRequest, WheelGenerationResult
from ..models.activity_models import ActivityData
from .activity_selection_service import ActivitySelectionService
from .activity_tailoring_service import ActivityTailoringService
from .wheel_building_service import WheelBuildingService

logger = logging.getLogger(__name__)


class WheelGenerationService:
    """Central business service for wheel generation logic."""

    def __init__(self,
                 activity_selector: ActivitySelectionService = None,
                 activity_tailorer: ActivityTailoringService = None,
                 wheel_builder: WheelBuildingService = None,
                 activity_repository=None,
                 wheel_repository=None,
                 user_repository=None):
        """Initialize with dependency injection."""
        from ..repositories.repository_interfaces import (
            ActivityRepositoryInterface, WheelRepositoryInterface, UserRepositoryInterface
        )

        # Initialize services with repository dependencies
        self.activity_selector = activity_selector or ActivitySelectionService(activity_repository)
        self.activity_tailorer = activity_tailorer or ActivityTailoringService()
        self.wheel_builder = wheel_builder or WheelBuildingService()

        # Store repository references
        self.activity_repository: ActivityRepositoryInterface = activity_repository
        self.wheel_repository: WheelRepositoryInterface = wheel_repository
        self.user_repository: UserRepositoryInterface = user_repository

    async def generate_wheel(self, request: WheelGenerationRequest) -> WheelGenerationResult:
        """Main business method - pure business logic."""
        logger.info(f"🎡 Starting wheel generation for user {request.user_profile_id}")
        start_time = time.time()

        # 1. Validate request using business rules
        self._validate_generation_request(request)

        # 2. Select activities using domain rules
        selected_activities = await self.activity_selector.select_activities(
            request.selection_criteria
        )
        logger.info(f"📋 Selected {len(selected_activities)} activities")

        # 3. Create user context from request data (ALWAYS use request data for time/energy)
        # The repository context is outdated and doesn't have current user input
        from ..models.user_models import UserContext
        from ..enums.domain_enums import TrustPhase

        # Ensure trust_phase is a TrustPhase enum, not a string
        trust_phase = request.selection_criteria.trust_phase
        if isinstance(trust_phase, str):
            trust_phase = TrustPhase(trust_phase)

        # CRITICAL FIX: Always use request data for time_available and energy_level
        # These are user inputs that change with each request
        user_context = UserContext(
            user_profile_id=request.user_profile_id,
            energy_level=request.selection_criteria.energy_level,
            time_available=request.selection_criteria.time_available,
            trust_phase=trust_phase
        )

        logger.info(f"🎯 Created UserContext with time_available={user_context.time_available}min, energy_level={user_context.energy_level}%")

        tailored_activities = await self.activity_tailorer.tailor_activities(
            selected_activities, user_context
        )
        logger.info(f"🎨 Tailored {len(tailored_activities)} activities")

        # 4. Build wheel structure
        wheel = await self.wheel_builder.build_wheel(
            tailored_activities, request.wheel_config
        )
        logger.info(f"🎡 Built wheel with {len(wheel.items)} items")

        # 5. Persist wheel if repository is available
        if self.wheel_repository:
            try:
                # Pass user_profile_id to repository for proper user context
                wheel_id = await self.wheel_repository.save_wheel(wheel, request.user_profile_id)
                wheel.id = wheel_id
                logger.info(f"💾 Persisted wheel with ID: {wheel_id}")
            except Exception as e:
                logger.warning(f"Failed to persist wheel: {e}")

        # 6. Generate result with metadata
        end_time = time.time()
        generation_time = end_time - start_time

        result = WheelGenerationResult(
            wheel=wheel,
            selected_activities=tailored_activities,
            selection_quality_score=self._calculate_selection_quality(selected_activities),
            domain_diversity_score=self._calculate_domain_diversity(tailored_activities),
            generation_time_seconds=generation_time,
            activities_considered=len(selected_activities) * 2,  # Mock value
            generated_at=datetime.now(timezone.utc)
        )

        logger.info(f"✅ Wheel generation completed in {generation_time:.2f}s")
        return result

    def _validate_generation_request(self, request: WheelGenerationRequest) -> None:
        """Validate generation request using business rules."""
        if not request.user_profile_id:
            raise ValueError("User profile ID is required")

        if request.selection_criteria.time_available < 5:
            raise ValueError("Minimum time available is 5 minutes")

        if request.selection_criteria.energy_level < 0 or request.selection_criteria.energy_level > 100:
            raise ValueError("Energy level must be between 0 and 100")

        if request.wheel_config.item_count < 1 or request.wheel_config.item_count > 12:
            raise ValueError("Wheel item count must be between 1 and 12")

    def _calculate_selection_quality(self, activities: List[ActivityData]) -> float:
        """Calculate quality score for selected activities."""
        if not activities:
            return 0.0

        # Mock quality calculation - in real implementation would be more sophisticated
        avg_confidence = sum(activity.confidence for activity in activities) / len(activities)
        return avg_confidence

    def _calculate_domain_diversity(self, activities: List[ActivityData]) -> float:
        """Calculate domain diversity score."""
        if not activities:
            return 0.0

        unique_domains = set(activity.domain for activity in activities)
        max_possible_domains = min(len(activities), 12)  # Max 12 domains

        return len(unique_domains) / max_possible_domains

    @classmethod
    def create_with_dependencies(cls, repository_factory=None) -> 'WheelGenerationService':
        """Factory method to create service with all dependencies."""
        logger.info("🔍 DEBUG: WheelGenerationService.create_with_dependencies() called")

        # Get repositories if factory is provided
        activity_repository = None
        wheel_repository = None
        user_repository = None

        if repository_factory:
            logger.info("🔍 DEBUG: Repository factory provided, creating repositories")
            activity_repository = repository_factory.create_activity_repository()
            wheel_repository = repository_factory.create_wheel_repository()
            user_repository = repository_factory.create_user_repository()
            logger.info("🔍 DEBUG: Repositories created successfully")
        else:
            logger.info("🔍 DEBUG: No repository factory provided")

        # Create services with repository dependencies
        logger.info("🔍 DEBUG: Creating ActivitySelectionService")
        activity_selector = ActivitySelectionService(activity_repository)
        logger.info(f"🔍 DEBUG: Created ActivitySelectionService: {activity_selector.__class__.__module__}.{activity_selector.__class__.__name__}")

        activity_tailorer = ActivityTailoringService()
        wheel_builder = WheelBuildingService()

        logger.info("🔍 DEBUG: Creating WheelGenerationService instance")
        service = cls(
            activity_selector=activity_selector,
            activity_tailorer=activity_tailorer,
            wheel_builder=wheel_builder,
            activity_repository=activity_repository,
            wheel_repository=wheel_repository,
            user_repository=user_repository
        )
        logger.info("🔍 DEBUG: WheelGenerationService instance created successfully")
        return service
