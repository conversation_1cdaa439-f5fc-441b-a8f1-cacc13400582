"""
Activity Selection Service

This service contains pure business logic for intelligent activity selection,
extracted from the ProgrammaticActivitySelector with enhanced domain-driven design.
"""

import logging
from typing import List, Optional, Tuple
from dataclasses import dataclass

from ..models.activity_models import ActivityData, ActivitySelectionCriteria
from ..enums.domain_enums import DomainCode, EnergyLevel, TrustPhase

logger = logging.getLogger(__name__)


@dataclass
class ScoredActivity:
    """Activity with its selection score and metadata."""
    activity: ActivityData
    score: float
    time_score: float
    energy_score: float
    challenge_score: float
    environment_score: float
    selection_reason: str


class EnergyBasedStrategy:
    """Strategy pattern for energy-based activity selection."""

    @staticmethod
    def get_strategy(energy_level: int):
        """Factory method to get appropriate strategy based on energy level."""
        if energy_level >= 70:
            return HighEnergyStrategy()
        elif energy_level <= 30:
            return LowEnergyStrategy()
        else:
            return MediumEnergyStrategy()

    def adjust_activity_score(self, activity: ActivityData, base_score: float) -> float:
        """Adjust activity score based on energy strategy."""
        return base_score

    def get_preferred_domains(self) -> List[DomainCode]:
        """Get domains preferred by this energy strategy."""
        return []


class HighEnergyStrategy(EnergyBasedStrategy):
    """Strategy for high energy levels - prioritize physical and challenging activities while maintaining domain diversity."""

    def adjust_activity_score(self, activity: ActivityData, base_score: float) -> float:
        # Strongly boost physical activities (primary preference)
        if activity.domain in [DomainCode.PHYSICAL, DomainCode.EXPLORATORY]:
            base_score *= 1.3
        # Moderately boost creative and social activities (secondary preference)
        elif activity.domain in [DomainCode.CREATIVITY, DomainCode.SOCIAL]:
            base_score *= 1.1
        # Slightly boost learning activities for high energy learning
        elif activity.domain == DomainCode.LEARNING and activity.duration_minutes <= 20:
            base_score *= 1.05
        # Reduce but don't eliminate reflective activities (for diversity)
        elif activity.domain in [DomainCode.REFLECTIVE, DomainCode.WELLNESS]:
            base_score *= 0.8

        return min(1.0, base_score)

    def get_preferred_domains(self) -> List[DomainCode]:
        return [DomainCode.PHYSICAL, DomainCode.EXPLORATORY, DomainCode.CREATIVITY, DomainCode.SOCIAL, DomainCode.LEARNING]


class LowEnergyStrategy(EnergyBasedStrategy):
    """Strategy for low energy levels - prioritize introspective and restorative activities."""

    def adjust_activity_score(self, activity: ActivityData, base_score: float) -> float:
        # Boost introspective and restorative activities
        if activity.domain in [DomainCode.WELLNESS, DomainCode.REFLECTIVE, DomainCode.EMOTIONAL]:
            base_score *= 1.3
        elif activity.domain == DomainCode.CREATIVITY and activity.duration_minutes <= 30:
            base_score *= 1.1
        elif activity.domain == DomainCode.PHYSICAL and activity.challenge_rating > 60:
            base_score *= 0.5

        return min(1.0, base_score)

    def get_preferred_domains(self) -> List[DomainCode]:
        return [DomainCode.WELLNESS, DomainCode.REFLECTIVE, DomainCode.EMOTIONAL, DomainCode.CREATIVITY]


class MediumEnergyStrategy(EnergyBasedStrategy):
    """Strategy for medium energy levels - balanced selection across domains."""

    def adjust_activity_score(self, activity: ActivityData, base_score: float) -> float:
        # Slight preference for balanced activities
        if activity.domain in [DomainCode.CREATIVITY, DomainCode.LEARNING, DomainCode.SOCIAL]:
            base_score *= 1.1
        return min(1.0, base_score)

    def get_preferred_domains(self) -> List[DomainCode]:
        return [DomainCode.CREATIVITY, DomainCode.LEARNING, DomainCode.SOCIAL, DomainCode.WELLNESS]


class ActivitySelectionService:
    """Pure business logic for activity selection."""

    def __init__(self, activity_repository=None):
        """Initialize with repository dependency injection."""
        from ..repositories.repository_interfaces import ActivityRepositoryInterface
        self.activity_repository: ActivityRepositoryInterface = activity_repository

    async def select_activities(self, criteria: ActivitySelectionCriteria) -> List[ActivityData]:
        """
        Select activities using intelligent business logic.

        ARCHITECTURAL EXCELLENCE: This method delegates to the proven programmatic
        selector which implements sophisticated energy-based distribution logic
        and intelligent domain selection strategies.
        """
        logger.info(f"🎯 Starting intelligent activity selection")
        logger.info(f"📊 Criteria: Time={criteria.time_available}min, Energy={criteria.energy_level}%")

        # Use the proven programmatic selector for intelligent selection
        try:
            logger.info("🔍 ARCHITECTURAL EXCELLENCE: Using programmatic selector for energy-based distribution")
            from apps.main.services.programmatic_activity_selector import (
                IntelligentActivitySelector, SelectionCriteria
            )
            logger.info("✅ Successfully imported programmatic selector")

            # Convert domain criteria to programmatic selector format
            programmatic_criteria = SelectionCriteria(
                time_available=criteria.time_available,
                energy_level=criteria.energy_level,
                environment=None,  # Will be enhanced later
                available_resources=['time', 'space', 'motivation'],  # Default resources
                target_challenge_range=criteria.challenge_range,
                min_activities=criteria.min_activities,
                max_activities=criteria.max_activities
            )

            # Use intelligent selector with energy-based distribution
            selector = IntelligentActivitySelector(str(criteria.user_profile_id))
            selected_activity_dicts = await selector.select_activities(programmatic_criteria)

            logger.info(f"✅ Programmatic selector returned {len(selected_activity_dicts)} activities")

            # Convert dict results back to ActivityData objects
            selected_activities = []
            for activity_dict in selected_activity_dicts:
                try:
                    # Convert dict to ActivityData using direct conversion
                    activity_data = self._convert_dict_to_activity_data(activity_dict)
                    if activity_data:
                        selected_activities.append(activity_data)
                except Exception as e:
                    logger.warning(f"Failed to convert activity dict to ActivityData: {e}")
                    continue

            logger.info(f"✅ Converted {len(selected_activities)} activities to domain models")
            return selected_activities

        except Exception as e:
            logger.error(f"❌ Programmatic selector failed, falling back to simple selection: {e}")
            # Fallback to simple selection if programmatic selector fails
            return await self._fallback_simple_selection(criteria)

    async def _fallback_simple_selection(self, criteria: ActivitySelectionCriteria) -> List[ActivityData]:
        """Fallback to simple selection if programmatic selector fails."""
        logger.info("🔄 Using fallback simple selection")

        # 1. Get candidate activities (would use repository in real implementation)
        candidates = await self._get_candidate_activities(criteria)

        # 2. Apply business scoring algorithm
        scored_activities = self._score_activities(candidates, criteria)

        # 3. Apply selection strategy based on trust phase
        selected_activities = self._apply_selection_strategy(scored_activities, criteria)

        # 4. Validate selection meets business requirements
        self._validate_selection(selected_activities, criteria)

        logger.info(f"✅ Fallback selected {len(selected_activities)} activities")
        return selected_activities

    def _convert_dict_to_activity_data(self, activity_dict: dict) -> Optional[ActivityData]:
        """Convert activity dictionary to ActivityData object."""
        try:
            from datetime import datetime
            from ...domain.enums.domain_enums import DomainCode, EnergyLevel

            # Map domain string to enum with sub-domain mapping
            domain_str = activity_dict.get('domain', 'general')
            domain = self._map_subdomain_to_main_domain(domain_str)

            return ActivityData(
                id=str(activity_dict.get('id', '')),
                name=activity_dict.get('name', ''),
                description=activity_dict.get('description', ''),
                instructions=activity_dict.get('instructions', ''),
                domain=domain,
                sub_domains=[],  # Simplified for now
                duration_minutes=activity_dict.get('duration_minutes', 10),
                challenge_rating=activity_dict.get('challenge_rating', 50),
                energy_requirement=EnergyLevel.MEDIUM,  # Default
                required_resources=[],  # Simplified for now
                created_at=datetime.now(),
                version=1,
                confidence=0.9
            )
        except Exception as e:
            logger.error(f"Error converting activity dict to ActivityData: {e}")
            return None

    def _map_subdomain_to_main_domain(self, subdomain_str: str) -> DomainCode:
        """
        Map programmatic selector sub-domain codes to main domain enums.

        ARCHITECTURAL EXCELLENCE: This method provides clean mapping between
        the programmatic selector's granular sub-domains and the domain service's
        main domain categories, ensuring proper domain diversity in wheels.
        """
        from ...domain.enums.domain_enums import DomainCode

        # Sub-domain to main domain mapping
        subdomain_mapping = {
            # Physical sub-domains
            'phys_strength': DomainCode.PHYSICAL,
            'phys_dance': DomainCode.PHYSICAL,
            'phys_sports': DomainCode.PHYSICAL,
            'phys_flexibility': DomainCode.PHYSICAL,
            'phys_outdoor': DomainCode.PHYSICAL,
            'phys_balance': DomainCode.PHYSICAL,
            'phys_chill': DomainCode.PHYSICAL,

            # Creative sub-domains
            'creative_visual': DomainCode.CREATIVITY,
            'creative_writing': DomainCode.CREATIVITY,
            'creative_music': DomainCode.CREATIVITY,
            'creative_culinary': DomainCode.CREATIVITY,
            'creative_craft': DomainCode.CREATIVITY,

            # Social sub-domains
            'soc_empathy': DomainCode.SOCIAL,
            'soc_comm': DomainCode.SOCIAL,
            'soc_connecting': DomainCode.SOCIAL,
            'soc_network': DomainCode.SOCIAL,
            'soc_family': DomainCode.SOCIAL,
            'soc_group': DomainCode.SOCIAL,
            'soc_leadership': DomainCode.SOCIAL,
            'leisure_social': DomainCode.SOCIAL,

            # Intellectual/Learning sub-domains
            'intel_learn': DomainCode.LEARNING,
            'intel_debate': DomainCode.LEARNING,
            'intel_language': DomainCode.LEARNING,
            'intel_strategic': DomainCode.LEARNING,
            'intel_science': DomainCode.LEARNING,

            # Reflective sub-domains
            'refl_meditate': DomainCode.REFLECTIVE,
            'refl_mindful': DomainCode.REFLECTIVE,
            'refl_grat': DomainCode.REFLECTIVE,
            'refl_journal': DomainCode.REFLECTIVE,
            'refl_persp': DomainCode.REFLECTIVE,
            'refl_comfort': DomainCode.REFLECTIVE,
            'leisure_relax': DomainCode.REFLECTIVE,

            # Emotional sub-domains
            'emot_aware': DomainCode.EMOTIONAL,
            'emot_regulation': DomainCode.EMOTIONAL,
            'emot_expression': DomainCode.EMOTIONAL,

            # Productive sub-domains
            'prod_health': DomainCode.PRODUCTIVE,
            'prod_transition': DomainCode.PRODUCTIVE,
            'prod_time': DomainCode.PRODUCTIVE,
            'prod_organization': DomainCode.PRODUCTIVE,

            # Exploratory sub-domains
            'explor_cultural': DomainCode.EXPLORATORY,
            'explor_sensory': DomainCode.EXPLORATORY,
            'explor_travel': DomainCode.EXPLORATORY,

            # Wellness sub-domains
            'wellness_mental': DomainCode.WELLNESS,
            'wellness_physical': DomainCode.WELLNESS,
            'wellness_nutrition': DomainCode.WELLNESS,

            # Spiritual sub-domains
            'spiritual_practice': DomainCode.SPIRITUAL,
            'spiritual_reflection': DomainCode.SPIRITUAL,
        }

        # Try direct mapping first
        if subdomain_str in subdomain_mapping:
            logger.debug(f"✅ Mapped sub-domain '{subdomain_str}' to {subdomain_mapping[subdomain_str].value}")
            return subdomain_mapping[subdomain_str]

        # Try direct enum match for main domains
        try:
            domain = DomainCode(subdomain_str)
            logger.debug(f"✅ Direct domain match for '{subdomain_str}'")
            return domain
        except ValueError:
            pass

        # Fallback with intelligent guessing based on prefix
        if subdomain_str.startswith('phys'):
            logger.info(f"🔍 Intelligent mapping: '{subdomain_str}' → PHYSICAL (prefix-based)")
            return DomainCode.PHYSICAL
        elif subdomain_str.startswith('creative'):
            logger.info(f"🔍 Intelligent mapping: '{subdomain_str}' → CREATIVITY (prefix-based)")
            return DomainCode.CREATIVITY
        elif subdomain_str.startswith('soc'):
            logger.info(f"🔍 Intelligent mapping: '{subdomain_str}' → SOCIAL (prefix-based)")
            return DomainCode.SOCIAL
        elif subdomain_str.startswith('intel'):
            logger.info(f"🔍 Intelligent mapping: '{subdomain_str}' → LEARNING (prefix-based)")
            return DomainCode.LEARNING
        elif subdomain_str.startswith('refl'):
            logger.info(f"🔍 Intelligent mapping: '{subdomain_str}' → REFLECTIVE (prefix-based)")
            return DomainCode.REFLECTIVE
        elif subdomain_str.startswith('emot'):
            logger.info(f"🔍 Intelligent mapping: '{subdomain_str}' → EMOTIONAL (prefix-based)")
            return DomainCode.EMOTIONAL
        elif subdomain_str.startswith('prod'):
            logger.info(f"🔍 Intelligent mapping: '{subdomain_str}' → PRODUCTIVE (prefix-based)")
            return DomainCode.PRODUCTIVE
        elif subdomain_str.startswith('explor'):
            logger.info(f"🔍 Intelligent mapping: '{subdomain_str}' → EXPLORATORY (prefix-based)")
            return DomainCode.EXPLORATORY
        elif subdomain_str.startswith('wellness'):
            logger.info(f"🔍 Intelligent mapping: '{subdomain_str}' → WELLNESS (prefix-based)")
            return DomainCode.WELLNESS
        elif subdomain_str.startswith('spiritual'):
            logger.info(f"🔍 Intelligent mapping: '{subdomain_str}' → SPIRITUAL (prefix-based)")
            return DomainCode.SPIRITUAL

        # Final fallback
        logger.warning(f"⚠️ Unknown domain '{subdomain_str}', using GENERAL as fallback")
        return DomainCode.GENERAL

    async def _get_candidate_activities(self, criteria: ActivitySelectionCriteria) -> List[ActivityData]:
        """Get candidate activities from repository."""
        if self.activity_repository:
            # Use repository to get real activities
            logger.debug("Using repository to get candidate activities")
            return await self.activity_repository.find_by_criteria(criteria)
        else:
            # Fallback to mock implementation for testing
            logger.warning("No repository provided, using mock activities")
            return [
                ActivityData(
                    id="activity-1",
                    name="Mindfulness Meditation",
                    description="A calming meditation practice to center your mind and reduce stress",
                    instructions="Find a quiet space, sit comfortably, and focus on your breathing",
                    domain=DomainCode.WELLNESS,
                    duration_minutes=15,
                    challenge_rating=20,
                    energy_requirement=EnergyLevel.LOW
                ),
                ActivityData(
                    id="activity-2",
                    name="Creative Writing",
                    description="Express yourself through creative writing and storytelling",
                    instructions="Choose a prompt and write freely for the specified duration",
                    domain=DomainCode.CREATIVITY,
                    duration_minutes=30,
                    challenge_rating=40,
                    energy_requirement=EnergyLevel.MEDIUM
                ),
                ActivityData(
                    id="activity-3",
                    name="High-Intensity Workout",
                    description="Energizing workout to boost your physical fitness",
                    instructions="Follow a structured workout routine with cardio and strength training",
                    domain=DomainCode.PHYSICAL,
                    duration_minutes=45,
                    challenge_rating=80,
                    energy_requirement=EnergyLevel.HIGH
                )
            ]

    def _score_activities(self, activities: List[ActivityData], criteria: ActivitySelectionCriteria) -> List[ScoredActivity]:
        """Score activities against selection criteria."""
        scored_activities = []
        energy_strategy = EnergyBasedStrategy.get_strategy(criteria.energy_level)

        for activity in activities:
            # Calculate individual scores
            time_score = self._calculate_time_score(activity, criteria.time_available)

            # Hard time filter
            if time_score == 0.0:
                continue

            energy_score = self._calculate_energy_score(activity, criteria.energy_level, energy_strategy)
            challenge_score = self._calculate_challenge_score(activity, criteria.challenge_range)
            environment_score = 0.8  # Mock environment score

            # Calculate base score
            base_score = (time_score * 0.3 + energy_score * 0.3 +
                         challenge_score * 0.2 + environment_score * 0.2)

            # Apply energy strategy intelligence
            final_score = energy_strategy.adjust_activity_score(activity, base_score)

            # Generate selection reason
            selection_reason = self._generate_selection_reason(activity, criteria, energy_strategy)

            scored_activities.append(ScoredActivity(
                activity=activity,
                score=final_score,
                time_score=time_score,
                energy_score=energy_score,
                challenge_score=challenge_score,
                environment_score=environment_score,
                selection_reason=selection_reason
            ))

        return sorted(scored_activities, key=lambda x: x.score, reverse=True)

    def _calculate_time_score(self, activity: ActivityData, time_available: int) -> float:
        """Calculate time compatibility score."""
        if activity.duration_minutes > time_available:
            return 0.0  # Hard filter - activity doesn't fit

        # Perfect match gets 1.0, decreasing as duration gets further from optimal
        optimal_ratio = activity.duration_minutes / time_available
        if optimal_ratio >= 0.8:
            return 1.0
        elif optimal_ratio >= 0.6:
            return 0.9
        elif optimal_ratio >= 0.4:
            return 0.7
        else:
            return 0.5

    def _calculate_energy_score(self, activity: ActivityData, energy_level: int, strategy: EnergyBasedStrategy) -> float:
        """Calculate energy compatibility score."""
        # Convert energy level to enum
        energy_enum = EnergyLevel.from_percentage(energy_level)

        # Perfect match
        if activity.energy_requirement == energy_enum:
            return 1.0

        # Partial matches
        if energy_level >= 70 and activity.energy_requirement == EnergyLevel.MEDIUM:
            return 0.7
        elif energy_level <= 30 and activity.energy_requirement == EnergyLevel.MEDIUM:
            return 0.7
        else:
            return 0.3

    def _calculate_challenge_score(self, activity: ActivityData, challenge_range: Tuple[int, int]) -> float:
        """Calculate challenge compatibility score."""
        min_challenge, max_challenge = challenge_range

        if min_challenge <= activity.challenge_rating <= max_challenge:
            return 1.0
        elif activity.challenge_rating < min_challenge:
            # Too easy
            gap = min_challenge - activity.challenge_rating
            return max(0.0, 1.0 - gap / 50.0)
        else:
            # Too hard
            gap = activity.challenge_rating - max_challenge
            return max(0.0, 1.0 - gap / 50.0)

    def _generate_selection_reason(self, activity: ActivityData, criteria: ActivitySelectionCriteria, strategy: EnergyBasedStrategy) -> str:
        """Generate human-readable reason for activity selection."""
        reasons = []

        # Energy matching
        if criteria.energy_level >= 70 and activity.domain in strategy.get_preferred_domains():
            reasons.append("high energy match")
        elif criteria.energy_level <= 30 and activity.domain in strategy.get_preferred_domains():
            reasons.append("low energy friendly")

        # Time matching
        time_ratio = activity.duration_minutes / criteria.time_available
        if time_ratio >= 0.8:
            reasons.append("perfect time fit")
        elif time_ratio >= 0.6:
            reasons.append("good time match")

        return ", ".join(reasons) if reasons else "general compatibility"

    def _apply_selection_strategy(self, scored_activities: List[ScoredActivity], criteria: ActivitySelectionCriteria) -> List[ActivityData]:
        """Apply selection strategy based on trust phase and ensure domain diversity."""
        # For foundation phase, prefer lower challenge activities
        if criteria.trust_phase == TrustPhase.FOUNDATION:
            # Filter out very high challenge activities
            filtered = [sa for sa in scored_activities if sa.activity.challenge_rating <= 70]
            scored_activities = filtered if filtered else scored_activities

        # Calculate target selection count
        target_count = min(criteria.max_activities, len(scored_activities))
        target_count = max(criteria.min_activities, target_count)

        # Apply domain diversity selection algorithm
        selected_activities = self._select_with_domain_diversity(scored_activities, target_count)

        return selected_activities

    def _select_with_domain_diversity(self, scored_activities: List[ScoredActivity], target_count: int) -> List[ActivityData]:
        """Select activities ensuring domain diversity while respecting scores."""
        if target_count <= 1 or len(scored_activities) <= 1:
            # No diversity needed for single activity
            return [scored_activities[0].activity] if scored_activities else []

        selected = []
        used_domains = set()
        remaining_activities = scored_activities.copy()

        # Phase 1: Ensure minimum domain diversity (at least 2 domains for 4+ activities)
        min_domains_needed = min(2, target_count, len(set(sa.activity.domain for sa in scored_activities)))

        # Select top activity from each unique domain first
        domains_covered = 0
        for scored_activity in remaining_activities[:]:
            if domains_covered >= min_domains_needed:
                break

            domain = scored_activity.activity.domain
            if domain not in used_domains:
                selected.append(scored_activity.activity)
                used_domains.add(domain)
                remaining_activities.remove(scored_activity)
                domains_covered += 1

        # Phase 2: Fill remaining slots with highest scoring activities (allowing domain repeats but preferring diversity)
        while len(selected) < target_count and remaining_activities:
            # Prefer activities from unused domains if available
            unused_domain_activities = [sa for sa in remaining_activities if sa.activity.domain not in used_domains]

            if unused_domain_activities and len(used_domains) < 4:  # Continue diversifying up to 4 domains
                best_activity = unused_domain_activities[0]
                used_domains.add(best_activity.activity.domain)
            else:
                best_activity = remaining_activities[0]  # Take highest scoring regardless of domain

            selected.append(best_activity.activity)
            remaining_activities.remove(best_activity)

        return selected

    def _validate_selection(self, selected_activities: List[ActivityData], criteria: ActivitySelectionCriteria) -> None:
        """Validate selection meets business requirements."""
        if len(selected_activities) < criteria.min_activities:
            raise ValueError(f"Selection has {len(selected_activities)} activities, minimum is {criteria.min_activities}")

        if len(selected_activities) > criteria.max_activities:
            raise ValueError(f"Selection has {len(selected_activities)} activities, maximum is {criteria.max_activities}")

        # Validate domain diversity
        domains = set(activity.domain for activity in selected_activities)
        if len(domains) < 2 and len(selected_activities) >= 2:
            logger.warning("Selection lacks domain diversity")
