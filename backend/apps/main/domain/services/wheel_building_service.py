"""
Wheel Building Service

This service contains pure business logic for wheel construction and probability assignment.
"""

import logging
from typing import List, Dict, Any
from datetime import datetime

from ..models.activity_models import ActivityData
from ..models.wheel_models import WheelData, WheelItemData, WheelConfiguration
from ..enums.domain_enums import DomainCode, ProbabilityStrategy, TrustPhase

logger = logging.getLogger(__name__)


class WheelBuildingService:
    """Business logic for wheel construction and probability assignment."""

    def __init__(self, domain_service=None):
        """Initialize with dependency injection."""
        self.domain_service = domain_service

    async def build_wheel(self, activities: List[ActivityData], config: WheelConfiguration) -> WheelData:
        """Build wheel structure using business rules."""
        logger.info(f"🎡 Building wheel with {len(activities)} activities")

        # 1. Create wheel items from activities
        wheel_items = self._create_wheel_items(activities)

        # 2. Assign probability weights using business logic
        weighted_items = self._assign_probability_weights(wheel_items, config)

        # 3. Create wheel with validation
        wheel = WheelData(
            name=self._generate_wheel_name(config),
            items=weighted_items,
            trust_phase=config.trust_phase
        )

        # 4. Validate wheel structure
        self._validate_wheel_structure(wheel)

        logger.info(f"✅ Built wheel '{wheel.name}' with {len(wheel.items)} items")
        return wheel

    def _create_wheel_items(self, activities: List[ActivityData]) -> List[WheelItemData]:
        """Create wheel items from activities."""
        wheel_items = []

        for i, activity in enumerate(activities):
            item = WheelItemData(
                id=f"wheel-item-{i+1}",
                activity_id=activity.id,
                name=activity.name,
                description=activity.description,
                percentage=0.0,  # Will be set by probability assignment
                probability=0.0,  # Will be set by probability assignment
                position=i,
                domain=activity.domain,
                challenge_rating=activity.challenge_rating,
                duration_minutes=activity.duration_minutes
            )
            wheel_items.append(item)

        return wheel_items

    def _assign_probability_weights(self, items: List[WheelItemData], config: WheelConfiguration) -> List[WheelItemData]:
        """Assign probability weights based on business rules."""
        if config.probability_strategy == ProbabilityStrategy.EQUAL:
            return self._assign_equal_weights(items)
        elif config.probability_strategy == ProbabilityStrategy.DOMAIN_WEIGHTED:
            return self._assign_domain_weighted(items, config.domain_preferences)
        elif config.probability_strategy == ProbabilityStrategy.CHALLENGE_WEIGHTED:
            return self._assign_challenge_weighted(items, config.trust_phase)
        else:
            return self._assign_adaptive_weights(items, config)

    def _assign_equal_weights(self, items: List[WheelItemData]) -> List[WheelItemData]:
        """Assign equal weights to all items."""
        if not items:
            return items

        percentage_per_item = 100.0 / len(items)
        probability_per_item = 1.0 / len(items)

        for item in items:
            item.percentage = percentage_per_item
            item.probability = probability_per_item

        return items

    def _assign_domain_weighted(self, items: List[WheelItemData], domain_preferences: Dict[DomainCode, float]) -> List[WheelItemData]:
        """Assign weights based on domain preferences."""
        # Calculate weights without modifying the items
        weights = []
        total_weight = 0.0

        for item in items:
            weight = domain_preferences.get(item.domain, 0.5)  # Default weight
            weights.append(weight)
            total_weight += weight

        # Normalize to percentages
        if total_weight > 0:
            for i, item in enumerate(items):
                percentage = (weights[i] / total_weight) * 100.0
                item.percentage = percentage
                item.probability = percentage / 100.0
        else:
            # Fallback to equal weights
            return self._assign_equal_weights(items)

        return items

    def _assign_challenge_weighted(self, items: List[WheelItemData], trust_phase: TrustPhase) -> List[WheelItemData]:
        """Assign weights based on challenge level and trust phase."""
        # Calculate weights without modifying the items
        weights = []
        total_weight = 0.0

        for item in items:
            if trust_phase == TrustPhase.FOUNDATION:
                # Favor lower challenge activities
                weight = 1.0 - (item.challenge_rating / 100.0) * 0.5
            else:
                # Balanced challenge weighting
                weight = 1.0

            weights.append(weight)
            total_weight += weight

        # Normalize to percentages
        for i, item in enumerate(items):
            percentage = (weights[i] / total_weight) * 100.0
            item.percentage = percentage
            item.probability = percentage / 100.0

        return items

    def _assign_adaptive_weights(self, items: List[WheelItemData], config: WheelConfiguration) -> List[WheelItemData]:
        """Assign adaptive weights based on multiple business factors."""
        # Calculate weights without modifying the items
        weights = []
        total_weight = 0.0

        for item in items:
            # Base weight
            weight = 1.0

            # Domain preference adjustment
            domain_preference = config.domain_preferences.get(item.domain, 0.5)
            weight *= (0.5 + domain_preference)

            # Trust phase adjustment
            if config.trust_phase == TrustPhase.FOUNDATION:
                # Favor lower challenge activities
                challenge_factor = 1.0 - (item.challenge_rating / 100.0) * 0.3
                weight *= challenge_factor

            weights.append(weight)
            total_weight += weight

        # Normalize to percentages
        for i, item in enumerate(items):
            percentage = (weights[i] / total_weight) * 100.0
            item.percentage = percentage
            item.probability = percentage / 100.0

        return items

    def _generate_wheel_name(self, config: WheelConfiguration) -> str:
        """Generate a descriptive name for the wheel."""
        timestamp = datetime.now().strftime("%H:%M")
        phase = config.trust_phase.value.title()
        return f"{phase} Wheel - {timestamp}"

    def _validate_wheel_structure(self, wheel: WheelData) -> None:
        """Validate wheel structure meets business requirements."""
        # Validation is handled by Pydantic model, but we can add business-specific checks
        total_percentage = sum(item.percentage for item in wheel.items)
        if not 99.0 <= total_percentage <= 101.0:
            raise ValueError(f"Wheel percentages sum to {total_percentage}, should be 100")

        # Validate domain diversity (context-aware validation now handled by WheelData model)
        domains = set(item.domain for item in wheel.items)
        if len(domains) < 2 and len(wheel.items) >= 2:
            # Check if this is a high-energy physical scenario
            physical_domains = {'physical', 'phys_strength', 'phys_cardio', 'phys_sports',
                              'phys_dance', 'phys_flexibility', 'phys_balance', 'phys_outdoor'}

            physical_count = sum(1 for item in wheel.items
                               if str(item.domain).lower() in physical_domains or
                                  str(item.domain).lower().startswith('phys'))
            physical_percentage = physical_count / len(wheel.items)

            if physical_percentage >= 0.75:
                logger.info(f"🔥 HIGH-ENERGY WHEEL: {physical_percentage*100:.1f}% physical activities - domain diversity relaxed")
            else:
                logger.warning("Wheel lacks domain diversity")
