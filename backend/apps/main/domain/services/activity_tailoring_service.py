"""
Activity Tailoring Service

This service contains pure business logic for activity tailoring and personalization,
refactored from the existing ActivityTailoringService with domain-driven design.
"""

import logging
from typing import List, Dict, Any, Optional

from ..models.activity_models import ActivityData
from ..models.user_models import UserContext
from ..enums.domain_enums import DomainCode, TrustPhase
from asgiref.sync import sync_to_async

logger = logging.getLogger(__name__)


class ActivityTailoringService:
    """Business logic for activity tailoring and personalization."""

    def __init__(self, llm_service=None, domain_service=None):
        """Initialize with dependency injection."""
        self.llm_service = llm_service
        self.domain_service = domain_service

    async def tailor_activities(self, activities: List[ActivityData], user_context: UserContext) -> List[ActivityData]:
        """Tailor activities to user context using business rules."""
        logger.info(f"🎨 Starting activity tailoring for {len(activities)} activities")

        tailored_activities = []

        for i, activity in enumerate(activities):
            tailored_activity = await self._tailor_single_activity(activity, user_context, i)
            tailored_activities.append(tailored_activity)

        # Validate tailoring results
        self._validate_tailoring_results(tailored_activities, user_context)

        logger.info(f"✅ Tailored {len(tailored_activities)} activities successfully")
        return tailored_activities

    async def _tailor_single_activity(self, activity: ActivityData, user_context: UserContext, index: int) -> ActivityData:
        """Tailor a single activity using business rules and LLM."""
        logger.debug(f"🎨 Tailoring activity {index + 1}: {activity.name}")

        # 1. Apply business rule adjustments
        adjusted_activity = self._apply_business_adjustments(activity, user_context)

        # 2. Apply personalization (would use LLM in real implementation)
        personalized_activity = await self._apply_personalization(adjusted_activity, user_context, index)

        # 3. Validate tailored activity
        self._validate_tailored_activity(personalized_activity, user_context)

        return personalized_activity

    def _apply_business_adjustments(self, activity: ActivityData, user_context: UserContext) -> ActivityData:
        """Apply business rule adjustments before personalization."""
        # Adjust duration based on available time
        adjusted_duration = self._adjust_duration_for_context(activity.duration_minutes, user_context.time_available)

        # Adjust challenge based on trust phase
        adjusted_challenge = self._adjust_challenge_for_trust_phase(activity.challenge_rating, user_context.trust_phase)

        # Create adjusted activity
        return activity.copy(update={
            'duration_minutes': adjusted_duration,
            'challenge_rating': adjusted_challenge,
            'confidence': 0.9  # High confidence for business rule adjustments
        })

    def _clean_activity_name(self, name: str) -> str:
        """
        Remove duplicate '(Tailored for Xmin)' suffixes from activity names.

        This is a critical fix to prevent duplicate suffixes that occur when activities
        are processed multiple times through the tailoring pipeline.

        Args:
            name: The activity name that may contain duplicate suffixes

        Returns:
            Cleaned activity name with only one suffix (if any)

        Raises:
            ValueError: If name is None or empty
        """
        if not name:
            raise ValueError("Activity name cannot be None or empty")

        try:
            import re
            # Pattern to match duplicate suffixes: ") (Tailored for Xmin)"
            # This removes the second and subsequent occurrences
            cleaned_name = re.sub(r'\) \(Tailored for \d+min\)', '', name)

            if cleaned_name != name:
                logger.info(f"🧹 DOMAIN: Cleaned duplicate suffix: '{name}' → '{cleaned_name}'")

            return cleaned_name

        except Exception as e:
            logger.error(f"🚨 ERROR: Failed to clean activity name '{name}': {e}")
            # Return original name if cleaning fails - better than crashing
            return name

    def _adjust_duration_for_context(self, original_duration: int, time_available: int) -> int:
        """Adjust activity duration based on available time with strict user control respect."""
        logger.debug(f"🕐 Duration adjustment: original={original_duration}min, available={time_available}min")

        # STRICT USER CONTROL: Never exceed user's time request
        if original_duration > time_available:
            # Scale down to fit exactly within user's time
            adjusted = max(5, min(time_available - 2, int(time_available * 0.9)))  # Leave 2min buffer
            logger.info(f"🔧 Duration scaled down: {original_duration}min → {adjusted}min (user requested {time_available}min)")
            return adjusted

        # If activity is much shorter, keep it short (user chose short time for a reason)
        if original_duration < time_available * 0.5:
            # Keep short activities short, just minor adjustment
            adjusted = min(original_duration + 2, time_available - 3)
            logger.debug(f"🔧 Duration slightly adjusted: {original_duration}min → {adjusted}min")
            return adjusted

        return original_duration

    def _adjust_challenge_for_trust_phase(self, original_challenge: int, trust_phase: TrustPhase) -> int:
        """Adjust challenge rating based on trust phase."""
        # DEBUG: Check what type trust_phase actually is
        logger.debug(f"🔍 TRUST_PHASE DEBUG: type={type(trust_phase)}, value={trust_phase}")

        # Ensure trust_phase is a TrustPhase enum, not a string
        if isinstance(trust_phase, str):
            logger.warning(f"🔧 Converting string trust_phase '{trust_phase}' to TrustPhase enum")
            trust_phase = TrustPhase(trust_phase)

        if trust_phase == TrustPhase.FOUNDATION:
            # Reduce challenge for foundation phase
            modifier = trust_phase.get_challenge_modifier()
            adjusted = int(original_challenge * modifier)
            return max(10, min(adjusted, 60))  # Cap at 60 for foundation

        return original_challenge

    async def _apply_personalization(self, activity: ActivityData, user_context: UserContext, index: int) -> ActivityData:
        """Apply LLM-based personalization using agent prompt template."""
        logger.info(f"🎨 LLM-based tailoring for activity {index + 1}: {activity.name}")

        # ARCHITECTURAL FIX: Clean activity name to prevent duplicate suffixes
        cleaned_name = self._clean_activity_name(activity.name)
        activity = activity.copy(update={'name': cleaned_name}) if cleaned_name != activity.name else activity

        # ARCHITECTURAL FIX: Check if activity is already tailored to prevent duplicate processing
        logger.info(f"🔍 DEBUGGING: Checking if activity is already tailored: '{activity.name}'")
        logger.info(f"🔍 DEBUGGING: Contains '(Tailored for ': {'(Tailored for ' in activity.name}")

        if "(Tailored for " in activity.name:
            logger.info(f"✅ Activity {index + 1} already tailored, skipping LLM processing: {activity.name}")
            # Just update the duration to match current context
            adjusted_duration = min(activity.duration_minutes or 20, user_context.time_available)
            return activity.copy(update={
                'duration_minutes': adjusted_duration,
                'confidence': 0.9,
                'version': activity.version + 1
            })

        logger.info(f"🔍 DEBUGGING: Activity not tailored, proceeding with LLM: {activity.name}")

        # Use LLM-based tailoring with agent prompt template
        tailored_activity = await self._tailor_activity_with_llm(activity, user_context, index)

        if tailored_activity:
            logger.info(f"✅ LLM tailoring successful for activity {index + 1}")
            return tailored_activity
        else:
            logger.warning(f"⚠️ LLM tailoring failed, using rule-based fallback for activity {index + 1}")
            # Fallback to rule-based personalization
            return await self._apply_rule_based_personalization(activity, user_context, index)

    async def _tailor_activity_with_llm(self, activity: ActivityData, user_context: UserContext, index: int) -> Optional[ActivityData]:
        """Tailor activity using LLM with agent prompt template."""
        try:
            # Get agent prompt template
            agent_prompt = await self._get_agent_prompt_template()
            if not agent_prompt:
                logger.warning("⚠️ No agent prompt template found, using fallback")
                return None

            # Build context for prompt placeholders
            prompt_context = await self._build_prompt_context(user_context, activity)

            # Fill prompt template with context
            filled_prompt = self._fill_prompt_template(agent_prompt, prompt_context)

            # Log the final prompt for debugging
            logger.info(f"🔍 LLM PROMPT for activity {index + 1}:")
            logger.info(f"📝 {filled_prompt[:500]}..." if len(filled_prompt) > 500 else filled_prompt)

            # Call LLM for tailoring
            tailored_result = await self._call_llm_for_tailoring(filled_prompt, activity, user_context)

            if tailored_result:
                logger.info(f"✅ LLM tailoring completed for activity {index + 1}")
                return tailored_result
            else:
                logger.warning(f"⚠️ LLM tailoring returned no result for activity {index + 1}")
                return None

        except Exception as e:
            logger.error(f"❌ LLM tailoring failed for activity {index + 1}: {e}")
            return None

    async def _apply_rule_based_personalization(self, activity: ActivityData, user_context: UserContext, index: int) -> ActivityData:
        """Apply rule-based personalization as fallback."""
        logger.debug(f"🔧 Rule-based personalization for activity {index + 1}")

        # Build contextual personalization based on user's specific situation
        personalized_name = self._personalize_activity_name(activity, user_context)
        personalized_instructions = self._personalize_activity_instructions(activity, user_context)
        personalized_description = self._personalize_activity_description(activity, user_context)

        return activity.copy(update={
            'name': personalized_name,
            'instructions': personalized_instructions,
            'description': personalized_description,
            'confidence': 0.75,  # Lower confidence for rule-based fallback
            'version': activity.version + 1
        })

    def _personalize_activity_name(self, activity: ActivityData, user_context: UserContext) -> str:
        """Personalize activity name based on context."""
        base_name = activity.name

        # Add contextual modifier based on environment and energy
        if user_context.energy_level >= 80:
            if "farm" in str(user_context).lower():
                return f"{base_name} (Farm-Energized)"
            else:
                return f"{base_name} (High-Energy)"
        elif user_context.time_available <= 15:
            return f"{base_name} (Quick)"
        else:
            return f"{base_name} (Personalized)"

    def _personalize_activity_instructions(self, activity: ActivityData, user_context: UserContext) -> str:
        """Create highly personalized instructions based on user's context."""
        base_instructions = activity.instructions

        # Build contextual adaptations
        adaptations = []

        # 1. Time-specific adaptations
        if user_context.time_available <= 10:
            adaptations.append(f"Quick {user_context.time_available}-minute version")

        # 2. Energy-specific adaptations
        if user_context.energy_level >= 80:
            adaptations.append("Take advantage of your high energy with dynamic movement")
            if "physical" not in base_instructions.lower():
                adaptations.append("Add physical elements like standing, walking, or stretching")

        # 3. Environment-specific adaptations (Rural Farm context)
        if "farm" in str(user_context).lower():
            adaptations.append("Use your farm environment - consider outdoor spaces, natural lighting, or farm sounds")
            if "outdoor" not in base_instructions.lower():
                adaptations.append("If weather permits, try this outside in your farm setting")

        # 4. Trust phase adaptations
        if user_context.trust_phase == TrustPhase.FOUNDATION:
            adaptations.append("Start gently and build confidence as you go")

        # Combine base instructions with contextual adaptations
        if adaptations:
            adaptation_text = " | ".join(adaptations)
            return f"🎯 PERSONALIZED: {adaptation_text}. {base_instructions}"
        else:
            return f"Personalized for your context: {base_instructions}"

    def _personalize_activity_description(self, activity: ActivityData, user_context: UserContext) -> str:
        """Enhance description with contextual relevance."""
        base_description = activity.description

        # Add contextual relevance note
        context_notes = []

        if user_context.energy_level >= 80:
            context_notes.append("perfect for your current high energy")

        if user_context.time_available <= 15:
            context_notes.append(f"designed for your {user_context.time_available}-minute window")

        if context_notes:
            context_text = " and ".join(context_notes)
            return f"{base_description} This activity is {context_text}."

        return base_description

    def _validate_tailored_activity(self, activity: ActivityData, user_context: UserContext) -> None:
        """Validate tailored activity meets business requirements."""
        # Validate duration doesn't exceed available time
        if activity.duration_minutes > user_context.time_available:
            raise ValueError(f"Tailored activity duration {activity.duration_minutes} exceeds available time {user_context.time_available}")

        # Validate challenge is appropriate for trust phase
        if user_context.trust_phase == TrustPhase.FOUNDATION and activity.challenge_rating > 70:
            raise ValueError(f"Challenge rating {activity.challenge_rating} too high for foundation phase")

        # Validate required fields
        if not activity.name or not activity.instructions:
            raise ValueError("Tailored activity missing required fields")

    def _validate_tailoring_results(self, activities: List[ActivityData], user_context: UserContext) -> None:
        """Validate overall tailoring results."""
        if not activities:
            raise ValueError("No activities were successfully tailored")

        # Validate domain diversity is maintained
        domains = set(activity.domain for activity in activities)
        if len(domains) < 2 and len(activities) >= 2:
            logger.warning("Tailoring reduced domain diversity")

        # Validate total time doesn't exceed available time
        total_duration = sum(activity.duration_minutes for activity in activities)
        if total_duration > user_context.time_available * len(activities):
            logger.warning(f"Total tailored duration {total_duration} may exceed practical limits")

    # LLM-based tailoring methods
    async def _get_agent_prompt_template(self) -> Optional[str]:
        """Get the agent prompt template for activity tailoring."""
        try:
            # Import here to avoid circular imports
            from apps.main.models import GenericAgent

            # Use string value instead of enum to avoid import issues
            agent = await sync_to_async(GenericAgent.objects.filter(
                role='activity',  # Use string instead of AgentRole.ACTIVITY
                is_active=True
            ).first)()

            if agent and agent.system_instructions:
                return agent.system_instructions
            else:
                logger.warning("⚠️ No active ACTIVITY agent found with system instructions")
                return None

        except Exception as e:
            logger.error(f"❌ Error getting agent prompt template: {e}")
            return None

    async def _build_prompt_context(self, user_context: UserContext, activity: ActivityData) -> Dict[str, Any]:
        """Build context dictionary for prompt template placeholders."""
        # This would be populated with actual user data in a real implementation
        # For now, using placeholder values
        return {
            'USER_NAME': f'User {user_context.user_profile_id}',
            'LOCAL_DATE': '2025-01-24',
            'LOCAL_TIME': '14:30',
            'LOCAL_DATETIME': '2025-01-24 14:30',
            'DAY_OF_WEEK': 'Friday',
            'TIME_OF_DAY': 'afternoon',
            'CURRENT_MOOD': 'focused',
            'ENERGY_LEVEL': user_context.energy_level,
            'TIME_AVAILABLE': user_context.time_available,
            'TIME_FLEXIBILITY': 'moderate',
            'CURRENT_ENVIRONMENT': 'rural farm',
            'ENVIRONMENT_TYPE': 'outdoor',
            'PRIVACY_LEVEL': 'high',
            'SPACE_SIZE': 'large',
            'NOISE_LEVEL': 'quiet',
            'SOCIAL_CONTEXT': 'alone',
            'AVAILABLE_RESOURCES': 'farm tools, open space',
            'PHYSICAL_LIMITATIONS': 'none',
            'COGNITIVE_LIMITATIONS': 'none',
            'DOMINANT_TRAITS': 'conscientious, open',
            'TRAIT_OPENNESS': 'high',
            'TRAIT_CONSCIENTIOUSNESS': 'high',
            'TRAIT_EXTRAVERSION': 'medium',
            'TRAIT_AGREEABLENESS': 'high',
            'TRAIT_NEUROTICISM': 'low',
            'TRAIT_HONESTY_HUMILITY': 'high',
            'PRIMARY_GOALS': 'personal growth, wellness',
            'CURRENT_ASPIRATIONS': 'improve fitness, learn new skills',
            'FOCUS_AREAS': 'physical health, mental clarity',
            'GROWTH_PRIORITIES': 'consistency, challenge',
            'TRUST_LEVEL': 'building',
            'TRUST_PHASE': str(user_context.trust_phase) if user_context.trust_phase else 'foundation',
            # Activity-specific context
            'ACTIVITY_NAME': activity.name,
            'ACTIVITY_DESCRIPTION': activity.description,
            'ACTIVITY_INSTRUCTIONS': activity.instructions,
            'ACTIVITY_DOMAIN': str(activity.domain) if activity.domain else 'general',
            'ACTIVITY_DURATION': activity.duration_minutes,
            'ACTIVITY_CHALLENGE': activity.challenge_rating
        }

    def _fill_prompt_template(self, template_str: str, context: Dict[str, Any]) -> str:
        """Fill prompt template with context values."""
        try:
            # Replace Django-style placeholders with actual values
            filled_template = template_str
            for key, value in context.items():
                placeholder = f'{{{{{key}}}}}'
                filled_template = filled_template.replace(placeholder, str(value))

            return filled_template

        except Exception as e:
            logger.error(f"❌ Error filling prompt template: {e}")
            return template_str

    async def _call_llm_for_tailoring(self, prompt: str, activity: ActivityData, user_context: UserContext) -> Optional[ActivityData]:
        """Call LLM for activity tailoring."""
        try:
            # For now, return a mock tailored activity
            # In a real implementation, this would call the LLM service
            logger.info(f"🤖 LLM call for tailoring activity: {activity.name}")
            logger.info(f"📝 Prompt length: {len(prompt)} characters")

            # Mock tailored result with adjusted duration
            adjusted_duration = self._adjust_duration_for_context(activity.duration_minutes, user_context.time_available)

            # ARCHITECTURAL FIX: Clean activity name first to prevent duplicate suffixes
            cleaned_name = self._clean_activity_name(activity.name)
            base_name = cleaned_name

            # ARCHITECTURAL FIX: Prevent duplicate "(Tailored for Xmin)" suffixes
            if "(Tailored for " in base_name:
                # Activity is already tailored - don't add suffix again
                # Just update the duration and return
                tailored_activity = activity.copy(update={
                    'name': base_name,  # Use cleaned name
                    'duration_minutes': adjusted_duration,
                    'confidence': 0.9,
                    'version': activity.version + 1
                })
                logger.info(f"✅ Activity already tailored, updated duration: {base_name} ({adjusted_duration}min)")
            else:
                # Activity is not tailored yet - add suffix
                tailored_activity = activity.copy(update={
                    'name': f"{base_name} (Tailored for {user_context.time_available}min)",
                    'instructions': f"Personalized instructions for {user_context.time_available} minutes: {activity.instructions}",
                    'description': f"Tailored for your current context: {activity.description}",
                    'duration_minutes': adjusted_duration,
                    'confidence': 0.9,
                    'version': activity.version + 1
                })
                logger.info(f"✅ Added tailoring suffix: {base_name} -> {tailored_activity.name}")

            logger.info(f"✅ LLM tailoring result: {tailored_activity.name} ({tailored_activity.duration_minutes}min)")
            return tailored_activity

        except Exception as e:
            logger.error(f"❌ LLM tailoring failed: {e}")
            return None
