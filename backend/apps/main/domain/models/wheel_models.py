"""
Wheel Domain Models

This module defines the core domain models for wheel generation,
including requests, results, and wheel data structures.
"""

from datetime import datetime
from typing import List, Dict, Optional, Any
from pydantic import BaseModel, Field, validator

from .activity_models import ActivitySelectionCriteria, ActivityData
from ..enums.domain_enums import DomainCode, TrustPhase, ProbabilityStrategy


class WheelConfiguration(BaseModel):
    """Configuration for wheel generation."""
    item_count: int = Field(default=5, ge=1, le=12)
    probability_strategy: ProbabilityStrategy = Field(default=ProbabilityStrategy.ADAPTIVE)
    trust_phase: TrustPhase = Field(default=TrustPhase.FOUNDATION)
    domain_preferences: Dict[DomainCode, float] = Field(default_factory=dict)


class WheelItemData(BaseModel):
    """Individual wheel item with validation."""
    id: str = Field(..., min_length=1)
    activity_id: str = Field(..., min_length=1)
    name: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = None
    
    # Wheel positioning
    percentage: float = Field(..., ge=0.0, le=100.0)
    probability: float = Field(..., ge=0.0, le=1.0)
    position: int = Field(..., ge=0)
    
    # Activity properties
    domain: DomainCode
    challenge_rating: int = Field(..., ge=0, le=100)
    duration_minutes: int = Field(..., ge=5, le=480)
    
    @validator('probability')
    def validate_probability_percentage_consistency(cls, v, values):
        percentage = values.get('percentage')
        if percentage is not None:
            expected_probability = percentage / 100.0
            if abs(v - expected_probability) > 0.01:  # Allow small rounding errors
                raise ValueError(f'Probability {v} inconsistent with percentage {percentage}')
        return v

    class Config:
        use_enum_values = True
        validate_assignment = True


class WheelData(BaseModel):
    """Complete wheel data structure with business rule validation."""
    id: Optional[str] = None
    name: str = Field(..., min_length=1, max_length=200)
    items: List[WheelItemData] = Field(..., min_items=1, max_items=12)
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    trust_phase: TrustPhase = Field(default=TrustPhase.FOUNDATION)
    
    @validator('items')
    def validate_percentages_sum_to_100(cls, v):
        total = sum(item.percentage for item in v)
        if not 99.0 <= total <= 101.0:  # Allow small rounding errors
            raise ValueError(f'Wheel percentages must sum to 100, got {total}')
        return v
    
    @validator('items')
    def validate_domain_diversity(cls, v, values):
        """
        Validate domain diversity with context-aware rules for high-energy scenarios.

        For high-energy scenarios (90%+ energy), allow single-domain wheels to support
        the enhanced energy distribution logic that prioritizes physical activities.
        """
        domains = [item.domain for item in v]
        unique_domains = set(domains)

        # Allow single-domain wheels for high-energy physical scenarios
        if len(unique_domains) < 2:
            # Check if this is a high-energy physical scenario
            physical_domains = {'physical', 'phys_strength', 'phys_cardio', 'phys_sports',
                              'phys_dance', 'phys_flexibility', 'phys_balance', 'phys_outdoor'}

            # Count physical activities
            physical_count = sum(1 for domain in domains
                               if str(domain).lower() in physical_domains or
                                  str(domain).lower().startswith('phys'))

            # If 75%+ activities are physical, this is likely a high-energy scenario
            physical_percentage = physical_count / len(v) if v else 0

            if physical_percentage >= 0.75:
                # Log this as an expected high-energy scenario
                import logging
                logger = logging.getLogger(__name__)
                logger.info(f"🔥 HIGH-ENERGY SCENARIO: Allowing single-domain wheel with {physical_percentage*100:.1f}% physical activities")
                return v
            else:
                # Standard diversity requirement for non-high-energy scenarios
                raise ValueError('Wheel must have at least 2 different domains')

        return v


class WheelGenerationRequest(BaseModel):
    """Complete request for wheel generation with validation."""
    user_profile_id: str = Field(..., min_length=1)
    selection_criteria: ActivitySelectionCriteria
    wheel_config: WheelConfiguration
    
    # Metadata
    workflow_id: Optional[str] = None
    request_timestamp: datetime = Field(default_factory=datetime.utcnow)
    
    class Config:
        validate_assignment = True
        use_enum_values = True


class WheelGenerationResult(BaseModel):
    """Complete result of wheel generation with comprehensive metadata."""
    wheel: WheelData
    selected_activities: List[ActivityData]
    
    # Quality metrics
    selection_quality_score: float = Field(..., ge=0.0, le=1.0)
    domain_diversity_score: float = Field(..., ge=0.0, le=1.0)
    
    # Performance metrics
    generation_time_seconds: float = Field(..., ge=0.0)
    activities_considered: int = Field(..., ge=0)
    
    # Metadata
    generated_at: datetime = Field(default_factory=datetime.utcnow)
    method: str = Field(default="domain_driven_architecture")
