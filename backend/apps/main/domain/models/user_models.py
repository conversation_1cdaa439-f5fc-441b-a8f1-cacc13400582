"""
User Context Domain Models

This module defines domain models for user context, environment,
and personalization data used in wheel generation.
"""

from datetime import datetime
from typing import List, Dict, Optional
from pydantic import BaseModel, Field

from ..enums.domain_enums import DomainCode, TrustPhase, EnergyLevel, ResourceType


class UserContext(BaseModel):
    """Complete user context for activity personalization."""
    user_profile_id: str = Field(..., min_length=1)
    
    # Current state
    energy_level: int = Field(..., ge=0, le=100)
    time_available: int = Field(..., ge=5, le=480)
    
    # Environment and resources
    available_resources: List[ResourceType] = Field(default_factory=list)
    
    # Psychological profile
    trust_phase: TrustPhase = Field(default=TrustPhase.FOUNDATION)
    
    # Preferences and history
    domain_preferences: Dict[DomainCode, float] = Field(default_factory=dict)
    recent_activities: List[str] = Field(default_factory=list)  # Activity IDs
    
    # Context timestamp
    context_timestamp: datetime = Field(default_factory=datetime.utcnow)
    
    class Config:
        use_enum_values = True
