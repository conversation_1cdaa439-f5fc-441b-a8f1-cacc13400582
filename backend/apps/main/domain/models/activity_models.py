"""
Activity Domain Models

This module defines the core domain models for activities, including
selection criteria, tailoring requests, and activity data structures.
"""

from datetime import datetime
from typing import List, Dict, Optional, Tuple
from pydantic import BaseModel, Field, validator

from ..enums.domain_enums import DomainCode, EnergyLevel, ResourceType, TrustPhase


class ActivityData(BaseModel):
    """Unified activity representation across all layers."""
    id: str = Field(..., min_length=1)
    name: str = Field(..., min_length=1, max_length=200)
    description: str = Field(..., min_length=10, max_length=1000)
    instructions: str = Field(..., min_length=10, max_length=2000)
    
    # Domain and categorization
    domain: DomainCode
    sub_domains: List[DomainCode] = Field(default_factory=list)
    
    # Time and difficulty
    duration_minutes: int = Field(..., ge=5, le=480)  # 5 min to 8 hours
    challenge_rating: int = Field(..., ge=0, le=100)
    energy_requirement: EnergyLevel = Field(default=EnergyLevel.MEDIUM)
    
    # Resources and requirements
    required_resources: List[ResourceType] = Field(default_factory=list)
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    version: int = Field(default=1, ge=1)
    confidence: float = Field(default=1.0, ge=0.0, le=1.0)
    
    class Config:
        use_enum_values = True
        validate_assignment = True


class ActivitySelectionCriteria(BaseModel):
    """Criteria for activity selection with business rule validation."""
    user_profile_id: str = Field(..., description="User profile ID for personalized selection")
    time_available: int = Field(..., ge=5, le=480)  # 5 min to 8 hours
    energy_level: int = Field(..., ge=0, le=100)
    
    # Domain preferences (sum should be <= 1.0)
    domain_preferences: Dict[DomainCode, float] = Field(default_factory=dict)
    
    # Challenge calibration
    challenge_range: Tuple[int, int] = Field(default=(30, 70))
    trust_phase: TrustPhase = Field(default=TrustPhase.FOUNDATION)
    
    # Selection parameters
    min_activities: int = Field(default=5, ge=1, le=12)
    max_activities: int = Field(default=8, ge=1, le=12)
    
    @validator('challenge_range')
    def validate_challenge_range(cls, v):
        if v[0] >= v[1]:
            raise ValueError('Min challenge must be less than max challenge')
        if v[0] < 0 or v[1] > 100:
            raise ValueError('Challenge range must be between 0 and 100')
        return v
    
    @validator('domain_preferences')
    def validate_domain_preferences(cls, v):
        total = sum(v.values())
        if total > 1.0:
            raise ValueError('Domain preferences cannot sum to more than 1.0')
        return v
    
    @validator('max_activities')
    def validate_activity_counts(cls, v, values):
        min_activities = values.get('min_activities', 1)
        if v < min_activities:
            raise ValueError('Max activities must be >= min activities')
        return v
