"""
Domain Enums for Type Safety and Business Rule Enforcement

This module defines all enums used throughout the domain layer to ensure
type safety and consistent business rule enforcement.
"""

from enum import Enum
from typing import List


class DomainCode(str, Enum):
    """Type-safe domain codes for activities."""
    WELLNESS = "wellness"
    CREATIVITY = "creativity"
    PHYSICAL = "physical"
    SOCIAL = "social"
    LEARNING = "learning"
    EMOTIONAL = "emotional"
    REFLECTIVE = "reflective"
    SPIRITUAL = "spiritual_existential"
    PRODUCTIVE = "productive_practical"
    LEISURE = "leisure_recreational"
    EXPLORATORY = "exploratory_adventurous"
    GENERAL = "general"
    
    @classmethod
    def get_main_domains(cls) -> List[str]:
        """Get list of main domain codes."""
        return [domain.value for domain in cls]
    
    @classmethod
    def is_valid_domain(cls, domain: str) -> bool:
        """Check if domain code is valid."""
        return domain in cls.get_main_domains()


class TrustPhase(str, Enum):
    """Trust phases for challenge calibration."""
    FOUNDATION = "foundation"
    EXPANSION = "expansion"
    
    def get_challenge_modifier(self) -> float:
        """Get challenge modifier for this trust phase."""
        return 0.7 if self == TrustPhase.FOUNDATION else 1.0


class EnergyLevel(str, Enum):
    """Energy level categories for activity matching."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    
    @classmethod
    def from_percentage(cls, percentage: int) -> 'EnergyLevel':
        """Convert percentage to energy level."""
        if percentage <= 30:
            return cls.LOW
        elif percentage >= 70:
            return cls.HIGH
        else:
            return cls.MEDIUM


class ResourceType(str, Enum):
    """Types of resources required for activities."""
    DIGITAL_DEVICE = "digital_device"
    PHYSICAL_SPACE = "physical_space"
    CRAFT_MATERIALS = "craft_materials"
    EXERCISE_EQUIPMENT = "exercise_equipment"
    WRITING_MATERIALS = "writing_materials"
    MUSICAL_INSTRUMENT = "musical_instrument"
    OUTDOOR_ACCESS = "outdoor_access"
    SOCIAL_CONNECTION = "social_connection"


class ProbabilityStrategy(str, Enum):
    """Strategies for assigning wheel probabilities."""
    EQUAL = "equal"
    DOMAIN_WEIGHTED = "domain_weighted"
    CHALLENGE_WEIGHTED = "challenge_weighted"
    ADAPTIVE = "adaptive"
