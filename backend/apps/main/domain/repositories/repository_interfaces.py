"""
Repository Interfaces for Clean Data Access Abstraction

This module defines abstract repository interfaces that separate business logic
from infrastructure concerns. These interfaces enable dependency injection,
testing with mocks, and switching between different data storage implementations.
"""

from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
from datetime import datetime

from ..models.activity_models import ActivityData, ActivitySelectionCriteria
from ..models.wheel_models import WheelData, WheelItemData
from ..models.user_models import UserContext
from ..enums.domain_enums import DomainCode, EnergyLevel, ResourceType


class ActivityRepositoryInterface(ABC):
    """Abstract interface for activity data access operations."""

    @abstractmethod
    async def find_by_criteria(self, criteria: ActivitySelectionCriteria) -> List[ActivityData]:
        """Find activities matching the given selection criteria."""
        pass

    @abstractmethod
    async def find_by_id(self, activity_id: str) -> Optional[ActivityData]:
        """Find a single activity by its ID."""
        pass

    @abstractmethod
    async def find_by_ids(self, activity_ids: List[str]) -> List[ActivityData]:
        """Find multiple activities by their IDs."""
        pass

    @abstractmethod
    async def find_by_domain(self, domain: DomainCode, limit: Optional[int] = None) -> List[ActivityData]:
        """Find activities by domain with optional limit."""
        pass

    @abstractmethod
    async def find_by_energy_level(self, energy_level: EnergyLevel, limit: Optional[int] = None) -> List[ActivityData]:
        """Find activities by energy level requirement."""
        pass

    @abstractmethod
    async def find_by_duration_range(self, min_duration: int, max_duration: int) -> List[ActivityData]:
        """Find activities within a duration range (in minutes)."""
        pass

    @abstractmethod
    async def find_by_challenge_range(self, min_challenge: int, max_challenge: int) -> List[ActivityData]:
        """Find activities within a challenge rating range."""
        pass

    @abstractmethod
    async def find_by_resources(self, required_resources: List[ResourceType]) -> List[ActivityData]:
        """Find activities that require specific resources."""
        pass

    @abstractmethod
    async def save_activity(self, activity: ActivityData) -> str:
        """Save an activity and return its ID."""
        pass

    @abstractmethod
    async def update_activity(self, activity: ActivityData) -> bool:
        """Update an existing activity. Returns True if successful."""
        pass

    @abstractmethod
    async def delete_activity(self, activity_id: str) -> bool:
        """Delete an activity by ID. Returns True if successful."""
        pass

    @abstractmethod
    async def count_by_criteria(self, criteria: ActivitySelectionCriteria) -> int:
        """Count activities matching the given criteria."""
        pass

    # ============================================================================
    # Metadata-based filtering methods
    # ============================================================================

    @abstractmethod
    async def find_by_energy_level_range(self, energy_level: int, limit: Optional[int] = None) -> List[ActivityData]:
        """
        Find activities that match the given energy level using metadata constraints.

        Args:
            energy_level: Energy level (0-100)
            limit: Optional limit on number of results

        Returns:
            List of activities where energy_level_min <= energy_level <= energy_level_max
        """
        pass

    @abstractmethod
    async def find_by_duration_constraint(self, duration_minutes: int, limit: Optional[int] = None) -> List[ActivityData]:
        """
        Find activities that match the given duration using metadata constraints.

        Args:
            duration_minutes: Duration in minutes
            limit: Optional limit on number of results

        Returns:
            List of activities where duration_min <= duration_minutes <= duration_max
        """
        pass

    @abstractmethod
    async def find_by_energy_and_duration(self, energy_level: int, duration_minutes: int,
                                        limit: Optional[int] = None) -> List[ActivityData]:
        """
        Find activities that match both energy level and duration constraints.

        Args:
            energy_level: Energy level (0-100)
            duration_minutes: Duration in minutes
            limit: Optional limit on number of results

        Returns:
            List of activities matching both constraints
        """
        pass

    @abstractmethod
    async def find_by_metadata_criteria(self,
                                      energy_level: Optional[int] = None,
                                      duration_minutes: Optional[int] = None,
                                      min_challenge: Optional[int] = None,
                                      max_challenge: Optional[int] = None,
                                      physical_intensity_min: Optional[int] = None,
                                      physical_intensity_max: Optional[int] = None,
                                      social_requirement: Optional[str] = None,
                                      limit: Optional[int] = None) -> List[ActivityData]:
        """
        Find activities using comprehensive metadata-based filtering.

        Args:
            energy_level: Energy level (0-100) - filters by energy_level_min/max
            duration_minutes: Duration in minutes - filters by duration_min/max
            min_challenge: Minimum challenge level
            max_challenge: Maximum challenge level
            physical_intensity_min: Minimum physical intensity
            physical_intensity_max: Maximum physical intensity
            social_requirement: Social requirement ('solo', 'pair', 'group')
            limit: Optional limit on number of results

        Returns:
            List of activities matching all specified criteria
        """
        pass


class WheelRepositoryInterface(ABC):
    """Abstract interface for wheel data persistence operations."""

    @abstractmethod
    async def save_wheel(self, wheel: WheelData) -> str:
        """Save a wheel and return its ID."""
        pass

    @abstractmethod
    async def find_by_id(self, wheel_id: str) -> Optional[WheelData]:
        """Find a wheel by its ID."""
        pass

    @abstractmethod
    async def find_by_user_id(self, user_id: str, limit: Optional[int] = None) -> List[WheelData]:
        """Find wheels created by a specific user."""
        pass

    @abstractmethod
    async def find_recent_wheels(self, limit: int = 10) -> List[WheelData]:
        """Find the most recently created wheels."""
        pass

    @abstractmethod
    async def update_wheel(self, wheel: WheelData) -> bool:
        """Update an existing wheel. Returns True if successful."""
        pass

    @abstractmethod
    async def delete_wheel(self, wheel_id: str) -> bool:
        """Delete a wheel by ID. Returns True if successful."""
        pass

    @abstractmethod
    async def count_by_user(self, user_id: str) -> int:
        """Count wheels created by a specific user."""
        pass


class UserRepositoryInterface(ABC):
    """Abstract interface for user context and profile data access."""

    @abstractmethod
    async def get_user_context(self, user_profile_id: str) -> Optional[UserContext]:
        """Get complete user context for wheel generation."""
        pass

    @abstractmethod
    async def save_user_context(self, context: UserContext) -> bool:
        """Save user context. Returns True if successful."""
        pass

    @abstractmethod
    async def update_user_preferences(self, user_id: str, domain_preferences: Dict[DomainCode, float]) -> bool:
        """Update user domain preferences."""
        pass

    @abstractmethod
    async def add_recent_activity(self, user_id: str, activity_id: str) -> bool:
        """Add an activity to user's recent activities list."""
        pass

    @abstractmethod
    async def get_recent_activities(self, user_id: str, limit: int = 10) -> List[str]:
        """Get user's recent activity IDs."""
        pass

    @abstractmethod
    async def update_trust_phase(self, user_id: str, trust_phase: str) -> bool:
        """Update user's trust phase."""
        pass

    @abstractmethod
    async def user_exists(self, user_profile_id: str) -> bool:
        """Check if a user profile exists."""
        pass


class CacheRepositoryInterface(ABC):
    """Abstract interface for caching operations to improve performance."""

    @abstractmethod
    async def get(self, key: str) -> Optional[Any]:
        """Get a value from cache by key."""
        pass

    @abstractmethod
    async def set(self, key: str, value: Any, ttl_seconds: Optional[int] = None) -> bool:
        """Set a value in cache with optional TTL."""
        pass

    @abstractmethod
    async def delete(self, key: str) -> bool:
        """Delete a value from cache."""
        pass

    @abstractmethod
    async def clear_pattern(self, pattern: str) -> int:
        """Clear all cache entries matching a pattern. Returns count of deleted entries."""
        pass

    @abstractmethod
    async def exists(self, key: str) -> bool:
        """Check if a key exists in cache."""
        pass


class RepositoryFactory(ABC):
    """Abstract factory for creating repository instances."""

    @abstractmethod
    def create_activity_repository(self) -> ActivityRepositoryInterface:
        """Create an activity repository instance."""
        pass

    @abstractmethod
    def create_wheel_repository(self) -> WheelRepositoryInterface:
        """Create a wheel repository instance."""
        pass

    @abstractmethod
    def create_user_repository(self) -> UserRepositoryInterface:
        """Create a user repository instance."""
        pass

    @abstractmethod
    def create_cache_repository(self) -> CacheRepositoryInterface:
        """Create a cache repository instance."""
        pass
