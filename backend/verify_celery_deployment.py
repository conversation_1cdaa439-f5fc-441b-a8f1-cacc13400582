#!/usr/bin/env python3
"""
Deployment verification script for Celery worker in DigitalOcean App Platform.
This script should be run after deployment to verify Celery functionality.
"""

import os
import sys
import django
import time
import json
import logging

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.prod')
django.setup()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_environment_variables():
    """Check that required environment variables are set"""
    logger.info("🔧 Checking Environment Variables...")
    
    required_vars = ['REDIS_URL', 'DATABASE_URL', 'DJANGO_SECRET_KEY']
    missing_vars = []
    
    for var in required_vars:
        value = os.environ.get(var)
        if value:
            # Don't log the actual values for security
            logger.info(f"✅ {var}: Set")
        else:
            logger.error(f"❌ {var}: Missing")
            missing_vars.append(var)
    
    return len(missing_vars) == 0


def check_celery_configuration():
    """Check Celery configuration"""
    logger.info("🔧 Checking Celery Configuration...")
    
    try:
        from celery import current_app
        from django.conf import settings
        
        logger.info(f"✅ Celery app: {current_app.main}")
        logger.info(f"✅ Broker URL configured: {bool(current_app.conf.broker_url)}")
        logger.info(f"✅ Result backend configured: {bool(current_app.conf.result_backend)}")
        logger.info(f"✅ Task time limit: {current_app.conf.task_time_limit}s")
        logger.info(f"✅ Worker max tasks per child: {current_app.conf.worker_max_tasks_per_child}")
        
        return True
    except Exception as e:
        logger.error(f"❌ Celery configuration error: {e}")
        return False


def check_redis_connection():
    """Check Redis connection"""
    logger.info("🔧 Checking Redis Connection...")
    
    try:
        import redis
        from django.conf import settings
        
        # Get Redis URL from settings
        redis_url = getattr(settings, 'REDIS_URL', None)
        if not redis_url:
            logger.error("❌ REDIS_URL not found in settings")
            return False
        
        # Try to connect to Redis
        r = redis.from_url(redis_url)
        r.ping()
        logger.info("✅ Redis connection successful")
        
        # Test basic operations
        r.set('test_key', 'test_value', ex=10)
        value = r.get('test_key')
        if value == b'test_value':
            logger.info("✅ Redis read/write operations working")
            r.delete('test_key')
            return True
        else:
            logger.error("❌ Redis read/write operations failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Redis connection failed: {e}")
        return False


def check_task_imports():
    """Check that Celery tasks can be imported"""
    logger.info("🔧 Checking Task Imports...")
    
    try:
        from apps.main.tasks.test_tasks import (
            test_worker_health,
            test_api_call,
            test_ml_simulation,
            test_combined_workload
        )
        logger.info("✅ Test tasks imported successfully")
        
        # Check existing tasks
        from apps.main.tasks.agent_tasks import run_all_benchmarks_task
        from apps.main.tasks.benchmark_tasks import run_workflow_benchmark
        logger.info("✅ Existing tasks imported successfully")
        
        return True
    except Exception as e:
        logger.error(f"❌ Task import failed: {e}")
        return False


def test_task_submission():
    """Test task submission (without waiting for results)"""
    logger.info("📤 Testing Task Submission...")
    
    try:
        from apps.main.tasks.test_tasks import test_worker_health
        
        # Submit a simple health check task
        result = test_worker_health.delay()
        logger.info(f"✅ Task submitted successfully: {result.id}")
        logger.info(f"✅ Task state: {result.state}")
        
        return result
    except Exception as e:
        logger.error(f"❌ Task submission failed: {e}")
        return None


def check_worker_availability():
    """Check if Celery workers are available"""
    logger.info("👷 Checking Worker Availability...")
    
    try:
        from celery import current_app
        inspect = current_app.control.inspect()
        
        # Check active workers
        active_workers = inspect.active()
        if active_workers:
            worker_names = list(active_workers.keys())
            logger.info(f"✅ Active workers found: {worker_names}")
            
            # Get worker stats
            stats = inspect.stats()
            if stats:
                for worker, worker_stats in stats.items():
                    logger.info(f"   Worker {worker}: {worker_stats.get('total', 'N/A')} tasks processed")
            
            return True
        else:
            logger.warning("⚠️  No active workers found")
            return False
            
    except Exception as e:
        logger.error(f"❌ Worker inspection failed: {e}")
        return False


def test_task_execution(task_result, timeout=30):
    """Test task execution by waiting for a result"""
    if not task_result:
        logger.warning("⚠️  No task to test execution")
        return False
    
    logger.info(f"⏳ Testing Task Execution (timeout: {timeout}s)...")
    
    try:
        result = task_result.get(timeout=timeout)
        logger.info("✅ Task executed successfully")
        logger.info(f"   Result: {json.dumps(result, indent=2)}")
        return True
    except Exception as e:
        logger.error(f"❌ Task execution failed or timed out: {e}")
        logger.info(f"   Task state: {task_result.state}")
        return False


def main():
    """Main verification function"""
    logger.info("🚀 Starting Celery Deployment Verification")
    logger.info("=" * 60)
    
    checks = []
    
    # Check 1: Environment variables
    env_ok = check_environment_variables()
    checks.append(("Environment Variables", env_ok))
    
    # Check 2: Celery configuration
    config_ok = check_celery_configuration()
    checks.append(("Celery Configuration", config_ok))
    
    # Check 3: Redis connection
    redis_ok = check_redis_connection()
    checks.append(("Redis Connection", redis_ok))
    
    # Check 4: Task imports
    imports_ok = check_task_imports()
    checks.append(("Task Imports", imports_ok))
    
    # Check 5: Worker availability
    workers_ok = check_worker_availability()
    checks.append(("Worker Availability", workers_ok))
    
    # Check 6: Task submission
    task_result = None
    if config_ok and redis_ok and imports_ok:
        task_result = test_task_submission()
        submission_ok = task_result is not None
        checks.append(("Task Submission", submission_ok))
        
        # Check 7: Task execution (only if workers are available)
        if workers_ok and task_result:
            execution_ok = test_task_execution(task_result)
            checks.append(("Task Execution", execution_ok))
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📋 Verification Summary:")
    
    all_passed = True
    for check_name, passed in checks:
        status = "✅ PASS" if passed else "❌ FAIL"
        logger.info(f"   {check_name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        logger.info("\n🎉 All checks passed! Celery deployment is working correctly.")
    else:
        logger.info("\n🔧 Some checks failed. Please review the logs above.")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
