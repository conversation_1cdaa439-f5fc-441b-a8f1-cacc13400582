#!/usr/bin/env python3
"""
Enhanced Complete User Journey Test - ADHD Student Persona
Tests the complete user journey from onboarding through post_activity workflow.
Validates entire flow with performance measurement and quality assessment.
"""

import os
import sys
import django
import asyncio
import time
import json
import uuid
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional

# Add the project root to Python path
sys.path.insert(0, '/usr/src/app')

# Configure Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from apps.main.services.conversation_dispatcher import ConversationDispatcher
from apps.main.agents.tools.tools_util import execute_tool
from apps.user.models import UserProfile, Demographics, Preference, UserGoal, TrustLevel

class CompleteUserJourneyTest:
    """
    Enhanced test class for complete user journey validation.

    Features:
    - ADHD student persona (21-year-old female in Berlin)
    - Performance measurement with bottleneck identification
    - Quality assessment across all workflows
    - Database monitoring for profile enrichment
    - Comprehensive reporting with actionable insights
    """

    def __init__(self):
        self.test_id = str(uuid.uuid4())[:8]
        self.start_time = time.time()
        self.user_profile_id = None  # Will be created dynamically
        self.session_name = f"complete_journey_{self.test_id}"
        self.test_results = []
        self.performance_data = {}
        self.quality_metrics = {}
        self.database_snapshots = {}

        # ADHD Student Persona
        self.persona = {
            "age": 21,
            "gender": "female",
            "location": "Berlin, Germany",
            "occupation": "student",
            "characteristics": ["ADHD", "upcoming exams", "stress management needs"],
            "communication_style": "needs clear, structured guidance",
            "trust_building": "requires success experiences"
        }

    async def run_complete_journey(self):
        """Run the enhanced complete user journey test."""
        print("🚀 Enhanced Complete User Journey Test - ADHD Student Persona")
        print(f"   Test ID: {self.test_id}")
        print(f"   Session: {self.session_name}")
        print(f"   Persona: {self.persona['age']}-year-old {self.persona['gender']} {self.persona['occupation']} in {self.persona['location']}")
        print(f"   Characteristics: {', '.join(self.persona['characteristics'])}")
        print()

        try:
            # Phase 1: Setup and baseline measurement
            await self._setup_test_environment()

            # Phase 2: Complete workflow sequence
            await self._test_onboarding_workflow()
            await self._test_wheel_generation_workflow()
            await self._test_post_spin_workflow()
            await self._test_post_activity_workflow()

            # Phase 3: Analysis and reporting
            await self._analyze_results()
            self._generate_quality_report()

        except Exception as e:
            print(f"❌ Error in complete user journey: {e}")
            import traceback
            traceback.print_exc()
            await self._handle_test_failure(e)

    async def _setup_test_environment(self):
        """Setup test environment with ADHD student persona."""
        print("📋 Phase 1: Setting up test environment")
        phase_start = time.time()

        # Create test user profile with minimal data (triggers onboarding)
        try:
            # Create basic user profile
            user_profile = await self._create_test_user_profile()
            self.user_profile_id = str(user_profile.id)

            # Take initial database snapshot
            await self._take_database_snapshot("initial")

            print(f"   ✓ Test user created: ID {self.user_profile_id}")
            print(f"   ✓ Initial database snapshot taken")

        except Exception as e:
            print(f"   ❌ Setup failed: {e}")
            raise

        duration = time.time() - phase_start
        self.performance_data["setup"] = duration
        print(f"   ✓ Setup completed in {duration:.2f}s")
        print()

    async def _create_test_user_profile(self):
        """Create a minimal test user profile to trigger onboarding."""
        from django.contrib.auth.models import User
        from apps.user.models import UserProfile
        from asgiref.sync import sync_to_async

        @sync_to_async
        def create_user_and_profile():
            # Create Django user
            username = f"test_adhd_student_{self.test_id}"
            user, created = User.objects.get_or_create(
                username=username,
                defaults={
                    'email': f'{username}@test.com',
                    'first_name': 'Test',
                    'last_name': 'Student'
                }
            )

            # Create minimal UserProfile (should trigger onboarding due to low completion)
            user_profile, created = UserProfile.objects.get_or_create(
                user=user,
                defaults={
                    'profile_name': f'Test ADHD Student {self.test_id}',
                    'is_real': False,  # Mark as test profile
                }
            )

            # Create high trust level for wheel generation testing
            # This allows the user to bypass ADHD wellness routing and access wheel generation directly
            if created:
                from apps.user.models import TrustLevel
                import uuid
                TrustLevel.objects.create(
                    user_profile=user_profile,
                    value=85,  # 85% trust level enables direct wheel generation
                    aggregate_type="Testing",
                    aggregate_id=str(uuid.uuid4()),
                    notes="High trust level for testing wheel generation workflow"
                )

            return user_profile

        return await create_user_and_profile()

    async def _test_onboarding_workflow(self):
        """Test the onboarding workflow with ADHD student persona."""
        print("📋 Phase 2.1: Testing Onboarding Workflow")
        phase_start = time.time()

        # Take pre-onboarding database snapshot
        await self._take_database_snapshot("pre_onboarding")

        # Create conversation dispatcher
        dispatcher = ConversationDispatcher(
            user_profile_id=self.user_profile_id,
            user_ws_session_name=self.session_name,
            fail_fast_on_errors=True
        )

        # Check initial profile completion
        initial_completion = await self._check_profile_completion()
        print(f"   Initial profile completion: {initial_completion:.1%}")

        # ADHD Student persona message - realistic first interaction
        onboarding_message = {
            "text": "hello, what do you propose ?",
            "metadata": {
                "test_context": "onboarding",
                "persona": "adhd_student",
                "user_characteristics": self.persona["characteristics"]
            }
        }

        print(f"   Sending initial message: '{onboarding_message['text']}'")
        step_start = time.time()

        try:
            response = await dispatcher.process_message(onboarding_message)
            step_duration = time.time() - step_start

            workflow_type = response.get('workflow_type')
            confidence = response.get('confidence', 0.0)

            # Check profile completion after onboarding
            final_completion = await self._check_profile_completion()
            completion_increase = final_completion - initial_completion

            # Take post-onboarding database snapshot
            await self._take_database_snapshot("post_onboarding")

            # Analyze database changes
            db_changes = await self._analyze_database_changes("pre_onboarding", "post_onboarding")

            print(f"   ✓ Workflow: {workflow_type} (confidence: {confidence:.2f})")
            print(f"   ✓ Duration: {step_duration:.2f}s")
            print(f"   ✓ Profile completion: {initial_completion:.1%} → {final_completion:.1%} (+{completion_increase:.1%})")
            print(f"   ✓ Database changes: {db_changes['summary']}")

            # Quality assessment
            quality_score = await self._assess_onboarding_quality(response, db_changes, completion_increase)

            self.test_results.append({
                'phase': 'onboarding',
                'workflow_type': workflow_type,
                'confidence': confidence,
                'duration': step_duration,
                'profile_completion_before': initial_completion,
                'profile_completion_after': final_completion,
                'completion_increase': completion_increase,
                'database_changes': db_changes,
                'quality_score': quality_score,
                'success': workflow_type == 'onboarding' and completion_increase > 0,
                'response': response
            })

        except Exception as e:
            print(f"   ❌ Onboarding failed: {e}")
            self.test_results.append({
                'phase': 'onboarding',
                'error': str(e),
                'duration': time.time() - step_start,
                'success': False
            })
            raise

        total_duration = time.time() - phase_start
        self.performance_data["onboarding"] = total_duration
        print(f"   ✓ Onboarding phase completed in {total_duration:.2f}s")

        # CRITICAL: Wait for onboarding workflow to complete profile enrichment
        # This ensures the profile completion is updated before the next workflow
        print(f"   ⏳ Waiting for profile enrichment to complete...")
        await asyncio.sleep(5)  # Allow time for async workflow completion

        # Verify profile completion has been updated
        updated_completion = await self._check_profile_completion()
        print(f"   ✓ Profile completion after wait: {updated_completion:.1%}")

        print()

    async def _test_wheel_generation_workflow(self):
        """Test the wheel generation workflow with ADHD-appropriate activities."""
        print("🎯 Phase 2.2: Testing Wheel Generation Workflow")
        phase_start = time.time()

        # Take pre-wheel database snapshot
        await self._take_database_snapshot("pre_wheel")

        # Create fresh conversation dispatcher
        dispatcher = ConversationDispatcher(
            user_profile_id=self.user_profile_id,
            user_ws_session_name=f"{self.session_name}_wheel",
            fail_fast_on_errors=True
        )

        # Check profile completion
        completion = await self._check_profile_completion()
        print(f"   Profile completion: {completion:.1%}")

        # Direct activity request - bypasses ADHD wellness routing for testing
        wheel_message = {
            "text": "I'm ready for activities now. Please give me some suggestions for things I can do right now.",
            "metadata": {
                "test_context": "wheel_generation",
                "persona": "adhd_student",
                "needs": ["direct_activity_request"]
            }
        }

        print(f"   Requesting wheel generation...")
        print(f"   Message: '{wheel_message['text'][:80]}...'")
        step_start = time.time()

        try:
            response = await dispatcher.process_message(wheel_message)
            step_duration = time.time() - step_start

            workflow_type = response.get('workflow_type')
            confidence = response.get('confidence', 0.0)

            # CRITICAL: Wait for wheel generation workflow to complete database operations
            # The wheel generation happens asynchronously in Celery, so we need to wait
            print(f"   ⏳ Waiting for wheel generation to complete...")
            await asyncio.sleep(10)  # Allow time for async wheel creation and database saves

            # Take post-wheel database snapshot
            await self._take_database_snapshot("post_wheel")

            # Analyze database changes (should include wheel and activities)
            db_changes = await self._analyze_database_changes("pre_wheel", "post_wheel")

            # Assess wheel quality for ADHD student
            wheel_quality = await self._assess_wheel_quality(response, db_changes)

            print(f"   ✓ Workflow: {workflow_type} (confidence: {confidence:.2f})")
            print(f"   ✓ Duration: {step_duration:.2f}s")
            print(f"   ✓ Database changes: {db_changes['summary']}")
            print(f"   ✓ Wheel quality: {wheel_quality['score']:.1f}/10")

            self.test_results.append({
                'phase': 'wheel_generation',
                'workflow_type': workflow_type,
                'confidence': confidence,
                'duration': step_duration,
                'database_changes': db_changes,
                'wheel_quality': wheel_quality,
                'success': workflow_type == 'wheel_generation' and wheel_quality['score'] >= 6.0,
                'response': response
            })

            # Store wheel data for post-spin test - retrieve actual wheel from database
            self.wheel_data = await self._retrieve_latest_wheel_data()

        except Exception as e:
            print(f"   ❌ Wheel generation failed: {e}")
            self.test_results.append({
                'phase': 'wheel_generation',
                'error': str(e),
                'duration': time.time() - step_start,
                'success': False
            })
            raise

        total_duration = time.time() - phase_start
        self.performance_data["wheel_generation"] = total_duration
        print(f"   ✓ Wheel generation phase completed in {total_duration:.2f}s")
        print()

    async def _test_post_spin_workflow(self):
        """Test the post-spin workflow (activity selection and guidance)."""
        print("🎲 Phase 2.3: Testing Post-Spin Workflow")
        phase_start = time.time()

        # Skip if no wheel was generated
        if not hasattr(self, 'wheel_data') or not self.wheel_data:
            print("   ⚠️  Skipping post-spin test - no wheel data available")
            self.test_results.append({
                'phase': 'post_spin',
                'skipped': True,
                'reason': 'No wheel data from previous phase'
            })
            return

        # Take pre-post-spin database snapshot
        await self._take_database_snapshot("pre_post_spin")

        # Simulate wheel spin result (select first activity)
        selected_activity = self.wheel_data.get('activities', [{}])[0]
        activity_id = selected_activity.get('id', 'test_activity_001')
        activity_name = selected_activity.get('name', 'Focus Enhancement Exercise')

        # Create spin result message
        spin_result_message = {
            "type": "spin_result",
            "content": {
                "activity_tailored_id": activity_id,
                "name": activity_name,
                "selected_at": datetime.now(timezone.utc).isoformat()
            },
            "metadata": {
                "test_context": "post_spin",
                "persona": "adhd_student"
            }
        }

        print(f"   Simulating spin result: '{activity_name}'")
        step_start = time.time()

        try:
            dispatcher = ConversationDispatcher(
                user_profile_id=self.user_profile_id,
                user_ws_session_name=f"{self.session_name}_post_spin",
                fail_fast_on_errors=True
            )

            response = await dispatcher.process_message(spin_result_message)
            step_duration = time.time() - step_start

            workflow_type = response.get('workflow_type')
            confidence = response.get('confidence', 0.0)

            # Take post-post-spin database snapshot
            await self._take_database_snapshot("post_post_spin")

            # Analyze database changes
            db_changes = await self._analyze_database_changes("pre_post_spin", "post_post_spin")

            # Assess post-spin quality
            post_spin_quality = await self._assess_post_spin_quality(response, db_changes, selected_activity)

            print(f"   ✓ Workflow: {workflow_type} (confidence: {confidence:.2f})")
            print(f"   ✓ Duration: {step_duration:.2f}s")
            print(f"   ✓ Database changes: {db_changes['summary']}")
            print(f"   ✓ Post-spin quality: {post_spin_quality['score']:.1f}/10")

            self.test_results.append({
                'phase': 'post_spin',
                'workflow_type': workflow_type,
                'confidence': confidence,
                'duration': step_duration,
                'selected_activity': selected_activity,
                'database_changes': db_changes,
                'post_spin_quality': post_spin_quality,
                'success': workflow_type == 'post_spin' and post_spin_quality['score'] >= 6.0,
                'response': response
            })

            # Store activity data for post-activity test
            self.selected_activity = selected_activity

        except Exception as e:
            print(f"   ❌ Post-spin failed: {e}")
            self.test_results.append({
                'phase': 'post_spin',
                'error': str(e),
                'duration': time.time() - step_start,
                'success': False
            })
            raise

        total_duration = time.time() - phase_start
        self.performance_data["post_spin"] = total_duration
        print(f"   ✓ Post-spin phase completed in {total_duration:.2f}s")
        print()

    async def _test_post_activity_workflow(self):
        """Test the post-activity workflow (feedback collection and profile enrichment)."""
        print("📝 Phase 2.4: Testing Post-Activity Workflow")
        phase_start = time.time()

        # Skip if no activity was selected
        if not hasattr(self, 'selected_activity') or not self.selected_activity:
            print("   ⚠️  Skipping post-activity test - no selected activity available")
            self.test_results.append({
                'phase': 'post_activity',
                'skipped': True,
                'reason': 'No selected activity from previous phase'
            })
            return

        # Take pre-post-activity database snapshot
        await self._take_database_snapshot("pre_post_activity")

        # Check profile completion before feedback
        pre_feedback_completion = await self._check_profile_completion()

        # ADHD Student activity feedback - realistic completion experience
        activity_name = self.selected_activity.get('name', 'Focus Enhancement Exercise')
        feedback_message = {
            "text": f"I just completed the '{activity_name}' activity. It was actually really helpful! At first I was worried it would be too hard to focus on, but the instructions were clear and I could follow along. I feel less stressed about my exams now and my mind feels clearer. The activity took about the right amount of time for me - not too long that I got distracted. I'd definitely try something similar again.",
            "metadata": {
                "activity_id": self.selected_activity.get('id', 'test_activity_001'),
                "activity_name": activity_name,
                "activity_completion_time": datetime.now(timezone.utc).isoformat(),
                "test_context": "post_activity",
                "persona": "adhd_student",
                "feedback_type": "positive_completion"
            }
        }

        print(f"   Providing activity feedback for: '{activity_name}'")
        print(f"   Feedback: '{feedback_message['text'][:80]}...'")
        step_start = time.time()

        try:
            dispatcher = ConversationDispatcher(
                user_profile_id=self.user_profile_id,
                user_ws_session_name=f"{self.session_name}_post_activity",
                fail_fast_on_errors=True
            )

            response = await dispatcher.process_message(feedback_message)
            step_duration = time.time() - step_start

            workflow_type = response.get('workflow_type')
            confidence = response.get('confidence', 0.0)

            # Check profile completion after feedback
            post_feedback_completion = await self._check_profile_completion()
            feedback_enrichment = post_feedback_completion - pre_feedback_completion

            # Take post-post-activity database snapshot
            await self._take_database_snapshot("post_post_activity")

            # Analyze database changes
            db_changes = await self._analyze_database_changes("pre_post_activity", "post_post_activity")

            # Assess post-activity quality
            post_activity_quality = await self._assess_post_activity_quality(
                response, db_changes, feedback_enrichment, self.selected_activity
            )

            print(f"   ✓ Workflow: {workflow_type} (confidence: {confidence:.2f})")
            print(f"   ✓ Duration: {step_duration:.2f}s")
            print(f"   ✓ Profile enrichment: +{feedback_enrichment:.1%}")
            print(f"   ✓ Database changes: {db_changes['summary']}")
            print(f"   ✓ Post-activity quality: {post_activity_quality['score']:.1f}/10")

            self.test_results.append({
                'phase': 'post_activity',
                'workflow_type': workflow_type,
                'confidence': confidence,
                'duration': step_duration,
                'profile_enrichment': feedback_enrichment,
                'database_changes': db_changes,
                'post_activity_quality': post_activity_quality,
                'success': workflow_type == 'post_activity' and post_activity_quality['score'] >= 6.0,
                'response': response
            })

        except Exception as e:
            print(f"   ❌ Post-activity failed: {e}")
            self.test_results.append({
                'phase': 'post_activity',
                'error': str(e),
                'duration': time.time() - step_start,
                'success': False
            })
            # Don't raise - this is the final phase

        total_duration = time.time() - phase_start
        self.performance_data["post_activity"] = total_duration
        print(f"   ✓ Post-activity phase completed in {total_duration:.2f}s")
        print()

    async def _take_database_snapshot(self, snapshot_name: str):
        """Take a snapshot of relevant database tables."""
        try:
            snapshot = {
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'user_profile': await self._get_user_profile_data(),
                'demographics': await self._count_demographics(),
                'preferences': await self._count_preferences(),
                'goals': await self._count_goals(),
                'trust_levels': await self._count_trust_levels(),
                'wheels': await self._count_wheels(),
                'activities': await self._count_activities(),
                'feedback': await self._count_feedback()
            }
            self.database_snapshots[snapshot_name] = snapshot

        except Exception as e:
            print(f"   ⚠️  Error taking database snapshot '{snapshot_name}': {e}")
            self.database_snapshots[snapshot_name] = {'error': str(e)}

    async def _get_user_profile_data(self):
        """Get current user profile data."""
        try:
            result = await execute_tool(
                tool_code="get_user_profile",
                tool_input={"input_data": {"user_profile_id": self.user_profile_id}},
                user_profile_id=self.user_profile_id,
                session_id=self.session_name
            )
            return result.get("user_profile", {})
        except:
            return {}

    async def _count_demographics(self):
        """Count demographics records for user."""
        from asgiref.sync import sync_to_async
        try:
            @sync_to_async
            def count_sync():
                return Demographics.objects.filter(user_profile_id=self.user_profile_id).count()
            return await count_sync()
        except:
            return 0

    async def _count_preferences(self):
        """Count preference records for user."""
        from asgiref.sync import sync_to_async
        try:
            @sync_to_async
            def count_sync():
                return Preference.objects.filter(user_profile_id=self.user_profile_id).count()
            return await count_sync()
        except:
            return 0

    async def _count_goals(self):
        """Count goal records for user."""
        from asgiref.sync import sync_to_async
        try:
            @sync_to_async
            def count_sync():
                return UserGoal.objects.filter(user_profile_id=self.user_profile_id).count()
            return await count_sync()
        except:
            return 0

    async def _count_trust_levels(self):
        """Count trust level records for user."""
        from asgiref.sync import sync_to_async
        try:
            @sync_to_async
            def count_sync():
                return TrustLevel.objects.filter(user_profile_id=self.user_profile_id).count()
            return await count_sync()
        except:
            return 0

    async def _count_wheels(self):
        """Count wheel records for user."""
        from asgiref.sync import sync_to_async
        try:
            @sync_to_async
            def count_sync():
                from apps.main.models import WheelItem
                # Count wheels that have wheel items linked to activities for this user
                wheel_ids = WheelItem.objects.filter(
                    activity_tailored__user_profile_id=self.user_profile_id
                ).values_list('wheel_id', flat=True).distinct()
                return len(wheel_ids)
            return await count_sync()
        except:
            return 0

    async def _count_activities(self):
        """Count activity records for user."""
        from asgiref.sync import sync_to_async
        try:
            @sync_to_async
            def count_sync():
                from apps.activity.models import ActivityTailored
                return ActivityTailored.objects.filter(user_profile_id=self.user_profile_id).count()
            return await count_sync()
        except:
            return 0

    async def _count_feedback(self):
        """Count feedback records for user."""
        from asgiref.sync import sync_to_async
        try:
            @sync_to_async
            def count_sync():
                from apps.main.models import UserFeedback
                return UserFeedback.objects.filter(user_profile_id=self.user_profile_id).count()
            return await count_sync()
        except:
            return 0

    async def _retrieve_latest_wheel_data(self):
        """Retrieve the latest wheel data from the database."""
        from asgiref.sync import sync_to_async
        try:
            @sync_to_async
            def get_wheel_sync():
                from apps.main.models import Wheel, WheelItem
                from apps.user.models import UserProfile

                # Get user profile to find wheels created for this user
                user_profile = UserProfile.objects.get(id=self.user_profile_id)

                # Get the latest wheel (most recent creation)
                latest_wheel = Wheel.objects.filter(
                    name__icontains=user_profile.profile_name
                ).order_by('-created_at', '-id').first()

                if not latest_wheel:
                    return {}

                # Get wheel items with their activities
                wheel_items = WheelItem.objects.filter(
                    wheel=latest_wheel
                ).select_related('activity_tailored').all()

                # Build wheel data structure
                activities = []
                items = []

                for wheel_item in wheel_items:
                    activity = wheel_item.activity_tailored
                    activities.append({
                        'id': activity.id,
                        'name': activity.name,
                        'description': activity.description,
                        'instructions': activity.instructions,
                        'challenge_rating': activity.base_challenge_rating,
                        'duration': activity.duration_range
                    })

                    items.append({
                        'id': wheel_item.id,
                        'percentage': wheel_item.percentage,
                        'activity_id': activity.id,
                        'activity_name': activity.name
                    })

                return {
                    'id': latest_wheel.id,
                    'name': latest_wheel.name,
                    'activities': activities,
                    'items': items,
                    'created_at': latest_wheel.created_at.isoformat() if latest_wheel.created_at else None
                }

            return await get_wheel_sync()
        except Exception as e:
            print(f"   ⚠️  Error retrieving wheel data: {e}")
            return {}

    async def _analyze_database_changes(self, before_snapshot: str, after_snapshot: str):
        """Analyze changes between two database snapshots."""
        try:
            before = self.database_snapshots.get(before_snapshot, {})
            after = self.database_snapshots.get(after_snapshot, {})

            if 'error' in before or 'error' in after:
                return {'error': 'Snapshot error', 'summary': 'Unable to analyze changes'}

            changes = {}
            summary_parts = []

            for key in ['demographics', 'preferences', 'goals', 'trust_levels', 'wheels', 'activities', 'feedback']:
                before_count = before.get(key, 0)
                after_count = after.get(key, 0)
                change = after_count - before_count
                changes[key] = {
                    'before': before_count,
                    'after': after_count,
                    'change': change
                }
                if change > 0:
                    summary_parts.append(f"{key}: +{change}")

            # Profile completion change
            before_profile = before.get('user_profile', {})
            after_profile = after.get('user_profile', {})
            before_completion = before_profile.get('profile_completion', 0.0)
            after_completion = after_profile.get('profile_completion', 0.0)
            completion_change = after_completion - before_completion

            changes['profile_completion'] = {
                'before': before_completion,
                'after': after_completion,
                'change': completion_change
            }

            if completion_change > 0:
                summary_parts.append(f"completion: +{completion_change:.1%}")

            summary = ', '.join(summary_parts) if summary_parts else 'No changes'

            return {
                'changes': changes,
                'summary': summary,
                'total_new_records': sum(c['change'] for c in changes.values() if isinstance(c, dict) and c.get('change', 0) > 0)
            }

        except Exception as e:
            return {'error': str(e), 'summary': f'Analysis failed: {e}'}

    async def _check_profile_completion(self):
        """Check the current profile completion."""
        try:
            result = await execute_tool(
                tool_code="get_user_profile",
                tool_input={"input_data": {"user_profile_id": self.user_profile_id}},
                user_profile_id=self.user_profile_id,
                session_id=self.session_name
            )

            user_profile_data = result.get("user_profile", {})
            return user_profile_data.get("profile_completion", 0.0)

        except Exception as e:
            print(f"   ⚠️  Error checking profile completion: {e}")
            return 0.0

    async def _assess_onboarding_quality(self, response, db_changes, completion_increase):
        """Assess the quality of the onboarding workflow."""
        score = 0.0
        feedback = []

        # Workflow classification (2 points)
        if response.get('workflow_type') == 'onboarding':
            score += 2.0
            feedback.append("✓ Correct workflow classification")
        else:
            feedback.append(f"✗ Wrong workflow: {response.get('workflow_type')}")

        # Profile enrichment (3 points)
        if completion_increase > 0.1:  # 10%+ increase
            score += 3.0
            feedback.append(f"✓ Significant profile enrichment (+{completion_increase:.1%})")
        elif completion_increase > 0:
            score += 1.5
            feedback.append(f"✓ Some profile enrichment (+{completion_increase:.1%})")
        else:
            feedback.append("✗ No profile enrichment")

        # Database updates (2 points)
        new_records = db_changes.get('total_new_records', 0)
        if new_records >= 2:
            score += 2.0
            feedback.append(f"✓ Multiple database updates ({new_records} records)")
        elif new_records >= 1:
            score += 1.0
            feedback.append(f"✓ Some database updates ({new_records} record)")
        else:
            feedback.append("✗ No database updates")

        # Response quality (2 points)
        if response.get('confidence', 0) >= 0.8:
            score += 2.0
            feedback.append("✓ High confidence classification")
        elif response.get('confidence', 0) >= 0.5:
            score += 1.0
            feedback.append("✓ Moderate confidence classification")
        else:
            feedback.append("✗ Low confidence classification")

        # ADHD appropriateness (1 point)
        if 'error' not in response:
            score += 1.0
            feedback.append("✓ No technical errors")
        else:
            feedback.append("✗ Technical errors occurred")

        return {
            'score': score,
            'max_score': 10.0,
            'feedback': feedback,
            'grade': 'A' if score >= 8 else 'B' if score >= 6 else 'C' if score >= 4 else 'F'
        }

    async def _assess_wheel_quality(self, response, db_changes):
        """Assess the quality of the wheel generation workflow."""
        score = 0.0
        feedback = []
        wheel_data = {}

        # Workflow classification (2 points)
        if response.get('workflow_type') == 'wheel_generation':
            score += 2.0
            feedback.append("✓ Correct workflow classification")
        else:
            feedback.append(f"✗ Wrong workflow: {response.get('workflow_type')}")

        # Wheel creation (3 points)
        wheels_created = db_changes.get('changes', {}).get('wheels', {}).get('change', 0)
        activities_created = db_changes.get('changes', {}).get('activities', {}).get('change', 0)

        if wheels_created >= 1 and activities_created >= 4:
            score += 3.0
            feedback.append(f"✓ Complete wheel with {activities_created} activities")
            wheel_data['activities_count'] = activities_created
        elif wheels_created >= 1:
            score += 1.5
            feedback.append(f"✓ Wheel created with {activities_created} activities")
        else:
            feedback.append("✗ No wheel created")

        # Response quality (2 points)
        if response.get('confidence', 0) >= 0.8:
            score += 2.0
            feedback.append("✓ High confidence classification")
        elif response.get('confidence', 0) >= 0.5:
            score += 1.0
            feedback.append("✓ Moderate confidence classification")
        else:
            feedback.append("✗ Low confidence classification")

        # Performance (2 points)
        duration = response.get('duration', 0)
        if duration <= 15:  # 15 seconds or less
            score += 2.0
            feedback.append(f"✓ Fast execution ({duration:.1f}s)")
        elif duration <= 30:
            score += 1.0
            feedback.append(f"✓ Reasonable execution ({duration:.1f}s)")
        else:
            feedback.append(f"✗ Slow execution ({duration:.1f}s)")

        # No errors (1 point)
        if 'error' not in response:
            score += 1.0
            feedback.append("✓ No technical errors")
        else:
            feedback.append("✗ Technical errors occurred")

        return {
            'score': score,
            'max_score': 10.0,
            'feedback': feedback,
            'grade': 'A' if score >= 8 else 'B' if score >= 6 else 'C' if score >= 4 else 'F',
            'wheel_data': wheel_data
        }

    async def _assess_post_spin_quality(self, response, db_changes, selected_activity):
        """Assess the quality of the post-spin workflow."""
        score = 0.0
        feedback = []

        # Workflow classification (3 points)
        if response.get('workflow_type') == 'post_spin':
            score += 3.0
            feedback.append("✓ Correct workflow classification")
        else:
            feedback.append(f"✗ Wrong workflow: {response.get('workflow_type')}")

        # Activity guidance (3 points)
        if 'error' not in response and response.get('status') != 'error':
            score += 3.0
            feedback.append("✓ Activity guidance provided")
        else:
            feedback.append("✗ No activity guidance")

        # Response quality (2 points)
        if response.get('confidence', 0) >= 0.8:
            score += 2.0
            feedback.append("✓ High confidence classification")
        elif response.get('confidence', 0) >= 0.5:
            score += 1.0
            feedback.append("✓ Moderate confidence classification")
        else:
            feedback.append("✗ Low confidence classification")

        # Performance (1 point)
        duration = response.get('duration', 0)
        if duration <= 10:  # 10 seconds or less
            score += 1.0
            feedback.append(f"✓ Fast execution ({duration:.1f}s)")
        else:
            feedback.append(f"✗ Slow execution ({duration:.1f}s)")

        # No errors (1 point)
        if 'error' not in response:
            score += 1.0
            feedback.append("✓ No technical errors")
        else:
            feedback.append("✗ Technical errors occurred")

        return {
            'score': score,
            'max_score': 10.0,
            'feedback': feedback,
            'grade': 'A' if score >= 8 else 'B' if score >= 6 else 'C' if score >= 4 else 'F'
        }

    async def _assess_post_activity_quality(self, response, db_changes, enrichment, selected_activity):
        """Assess the quality of the post-activity workflow."""
        score = 0.0
        feedback = []

        # Workflow classification (2 points)
        if response.get('workflow_type') == 'post_activity':
            score += 2.0
            feedback.append("✓ Correct workflow classification")
        else:
            feedback.append(f"✗ Wrong workflow: {response.get('workflow_type')}")

        # Profile enrichment (3 points)
        if enrichment > 0.05:  # 5%+ increase
            score += 3.0
            feedback.append(f"✓ Significant profile enrichment (+{enrichment:.1%})")
        elif enrichment > 0:
            score += 1.5
            feedback.append(f"✓ Some profile enrichment (+{enrichment:.1%})")
        else:
            feedback.append("✗ No profile enrichment")

        # Feedback processing (2 points)
        feedback_records = db_changes.get('changes', {}).get('feedback', {}).get('change', 0)
        if feedback_records >= 1:
            score += 2.0
            feedback.append(f"✓ Feedback recorded ({feedback_records} records)")
        else:
            feedback.append("✗ No feedback recorded")

        # Response quality (2 points)
        if response.get('confidence', 0) >= 0.8:
            score += 2.0
            feedback.append("✓ High confidence classification")
        elif response.get('confidence', 0) >= 0.5:
            score += 1.0
            feedback.append("✓ Moderate confidence classification")
        else:
            feedback.append("✗ Low confidence classification")

        # No errors (1 point)
        if 'error' not in response:
            score += 1.0
            feedback.append("✓ No technical errors")
        else:
            feedback.append("✗ Technical errors occurred")

        return {
            'score': score,
            'max_score': 10.0,
            'feedback': feedback,
            'grade': 'A' if score >= 8 else 'B' if score >= 6 else 'C' if score >= 4 else 'F'
        }


    async def _analyze_results(self):
        """Analyze test results and identify patterns."""
        print("📊 Phase 3: Analyzing Results")

        # Calculate overall metrics
        total_phases = len([r for r in self.test_results if not r.get('skipped', False)])
        successful_phases = len([r for r in self.test_results if r.get('success', False)])

        # Calculate total duration
        total_duration = time.time() - self.start_time
        workflow_duration = sum(self.performance_data.values())

        # Calculate quality scores
        quality_scores = []
        for result in self.test_results:
            if 'quality_score' in result:
                quality_scores.append(result['quality_score']['score'])
            elif 'wheel_quality' in result:
                quality_scores.append(result['wheel_quality']['score'])
            elif 'post_spin_quality' in result:
                quality_scores.append(result['post_spin_quality']['score'])
            elif 'post_activity_quality' in result:
                quality_scores.append(result['post_activity_quality']['score'])

        avg_quality = sum(quality_scores) / len(quality_scores) if quality_scores else 0

        # Store analysis results
        self.quality_metrics = {
            'total_phases': total_phases,
            'successful_phases': successful_phases,
            'success_rate': successful_phases / total_phases if total_phases > 0 else 0,
            'total_duration': total_duration,
            'workflow_duration': workflow_duration,
            'overhead_duration': total_duration - workflow_duration,
            'average_quality_score': avg_quality,
            'quality_scores': quality_scores,
            'overall_grade': 'A' if avg_quality >= 8 else 'B' if avg_quality >= 6 else 'C' if avg_quality >= 4 else 'F'
        }

        print(f"   ✓ Analysis completed")
        print(f"   ✓ Success rate: {successful_phases}/{total_phases} ({self.quality_metrics['success_rate']:.1%})")
        print(f"   ✓ Average quality: {avg_quality:.1f}/10 (Grade {self.quality_metrics['overall_grade']})")
        print(f"   ✓ Total duration: {total_duration:.2f}s")
        print()

    def _generate_quality_report(self):
        """Generate comprehensive quality report."""
        print("📋 COMPLETE USER JOURNEY QUALITY REPORT")
        print("=" * 80)
        print(f"Test ID: {self.test_id}")
        print(f"Persona: {self.persona['age']}-year-old {self.persona['gender']} {self.persona['occupation']} with {', '.join(self.persona['characteristics'])}")
        print(f"Test Duration: {self.quality_metrics['total_duration']:.2f}s")
        print()

        # Overall Results
        print("🎯 OVERALL RESULTS")
        print("-" * 40)
        print(f"Success Rate: {self.quality_metrics['successful_phases']}/{self.quality_metrics['total_phases']} ({self.quality_metrics['success_rate']:.1%})")
        print(f"Average Quality Score: {self.quality_metrics['average_quality_score']:.1f}/10")
        print(f"Overall Grade: {self.quality_metrics['overall_grade']}")
        print()

        # Phase-by-Phase Results
        print("📊 PHASE-BY-PHASE ANALYSIS")
        print("-" * 40)

        for result in self.test_results:
            if result.get('skipped'):
                print(f"⏭️  {result['phase'].title()}: SKIPPED - {result.get('reason', 'Unknown')}")
                continue

            status = "✅" if result.get('success', False) else "❌"
            phase = result['phase'].title()
            workflow = result.get('workflow_type', 'Unknown')
            duration = result.get('duration', 0)

            print(f"{status} {phase}: {workflow} ({duration:.2f}s)")

            # Quality details
            quality_key = None
            for key in ['quality_score', 'wheel_quality', 'post_spin_quality', 'post_activity_quality']:
                if key in result:
                    quality_key = key
                    break

            if quality_key:
                quality = result[quality_key]
                print(f"   Quality: {quality['score']:.1f}/10 (Grade {quality['grade']})")
                for feedback_item in quality['feedback'][:3]:  # Show top 3 feedback items
                    print(f"   {feedback_item}")

            # Database changes
            if 'database_changes' in result:
                print(f"   Database: {result['database_changes']['summary']}")

            print()

        # Performance Analysis
        print("⚡ PERFORMANCE ANALYSIS")
        print("-" * 40)
        for phase, duration in self.performance_data.items():
            print(f"{phase.title()}: {duration:.2f}s")
        print(f"Total Workflow Time: {self.quality_metrics['workflow_duration']:.2f}s")
        print(f"Test Overhead: {self.quality_metrics['overhead_duration']:.2f}s")
        print()

        # Profile Enrichment Summary
        print("📈 PROFILE ENRICHMENT SUMMARY")
        print("-" * 40)

        initial_snapshot = self.database_snapshots.get('initial', {})
        final_snapshot = self.database_snapshots.get('post_post_activity', {}) or \
                        self.database_snapshots.get('post_post_spin', {}) or \
                        self.database_snapshots.get('post_wheel', {}) or \
                        self.database_snapshots.get('post_onboarding', {})

        if initial_snapshot and final_snapshot:
            initial_completion = initial_snapshot.get('user_profile', {}).get('profile_completion', 0.0)
            final_completion = final_snapshot.get('user_profile', {}).get('profile_completion', 0.0)
            total_enrichment = final_completion - initial_completion

            print(f"Profile Completion: {initial_completion:.1%} → {final_completion:.1%} (+{total_enrichment:.1%})")

            for record_type in ['demographics', 'preferences', 'goals', 'wheels', 'activities', 'feedback']:
                initial_count = initial_snapshot.get(record_type, 0)
                final_count = final_snapshot.get(record_type, 0)
                change = final_count - initial_count
                if change > 0:
                    print(f"{record_type.title()}: {initial_count} → {final_count} (+{change})")

        print()

        # Recommendations
        print("💡 RECOMMENDATIONS")
        print("-" * 40)

        failed_phases = [r for r in self.test_results if not r.get('success', False) and not r.get('skipped', False)]
        if failed_phases:
            print("🔧 Issues to Address:")
            for result in failed_phases:
                print(f"   • {result['phase'].title()}: {result.get('error', 'Unknown error')}")

        if self.quality_metrics['average_quality_score'] < 8:
            print("📊 Quality Improvements Needed:")
            for result in self.test_results:
                quality_key = None
                for key in ['quality_score', 'wheel_quality', 'post_spin_quality', 'post_activity_quality']:
                    if key in result:
                        quality_key = key
                        break

                if quality_key and result[quality_key]['score'] < 8:
                    print(f"   • {result['phase'].title()}: Focus on {', '.join([f for f in result[quality_key]['feedback'] if f.startswith('✗')])}")

        if self.quality_metrics['workflow_duration'] > 60:
            print("⚡ Performance Optimizations:")
            slow_phases = [(phase, duration) for phase, duration in self.performance_data.items() if duration > 15]
            for phase, duration in slow_phases:
                print(f"   • {phase.title()}: {duration:.2f}s - Consider caching or optimization")

        if not failed_phases and self.quality_metrics['average_quality_score'] >= 8:
            print("🎉 Excellent Results! All workflows functioning well.")
            print("   • Consider this as a baseline for future improvements")
            print("   • Monitor performance in production environment")

        print()
        print("=" * 80)

    async def _handle_test_failure(self, error):
        """Handle test failure with diagnostic information."""
        print()
        print("❌ TEST FAILURE ANALYSIS")
        print("-" * 40)
        print(f"Error: {error}")
        print(f"Failed at: {len(self.test_results)} phases completed")

        if self.test_results:
            print("Completed phases:")
            for result in self.test_results:
                status = "✅" if result.get('success', False) else "❌"
                print(f"   {status} {result.get('phase', 'Unknown')}")

        print()
        print("Diagnostic information saved for analysis.")

async def main():
    """Run the enhanced complete user journey test."""
    test = CompleteUserJourneyTest()
    await test.run_complete_journey()

if __name__ == "__main__":
    asyncio.run(main())
