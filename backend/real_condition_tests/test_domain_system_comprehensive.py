#!/usr/bin/env python3
"""
Comprehensive Domain System Test

This test validates the entire domain management system from database to frontend,
ensuring that the domain refactoring hasn't broken any functionality.

Test Coverage:
1. Domain Management Service functionality
2. Wheel generation with proper domain assignment
3. Color mapping consistency
4. Domain validation and normalization
5. Frontend data contract compliance
"""

import os
import sys
import django
import logging
import asyncio

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
sys.path.append('/usr/src/app')
django.setup()

from apps.user.models import UserProfile
from apps.main.agents.tools.tools import generate_wheel
from apps.main.services.domain_management_service import domain_management_service
from apps.activity.models import GenericDomain
from asgiref.sync import sync_to_async

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_domain_management_service():
    """Test the domain management service functionality."""
    logger.info("🔧 Testing Domain Management Service...")
    
    try:
        # Test 1: Valid domain count
        valid_domains = domain_management_service._get_valid_domains()
        logger.info(f"  ✅ Loaded {len(valid_domains)} valid domain codes")
        
        if len(valid_domains) < 50:  # Should have many domains
            logger.error(f"  ❌ Too few domains loaded: {len(valid_domains)}")
            return False
        
        # Test 2: Color mapping for common domains
        test_domains = ['creative', 'physical', 'emotional', 'intellectual', 'social', 'general']
        for domain in test_domains:
            color = domain_management_service.get_domain_color(domain)
            if not color or color == 'missing':
                logger.error(f"  ❌ No color for domain: {domain}")
                return False
            logger.info(f"  ✅ {domain}: {color}")
        
        # Test 3: Domain normalization
        test_cases = [
            ('wellness', 'emotional'),  # Legacy mapping
            ('creativity', 'creative'),
            ('physical', 'physical'),
            ('invalid_domain', 'general'),  # Fallback
            (None, 'general'),  # None handling
        ]
        
        for input_domain, expected in test_cases:
            normalized = domain_management_service._normalize_domain(input_domain)
            if normalized != expected:
                logger.warning(f"  ⚠️ Domain normalization: {input_domain} -> {normalized} (expected {expected})")
            else:
                logger.info(f"  ✅ Domain normalization: {input_domain} -> {normalized}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Domain management service test failed: {e}")
        return False

async def test_wheel_generation_domains():
    """Test wheel generation with domain validation."""
    logger.info("🎡 Testing Wheel Generation with Domain Validation...")
    
    try:
        # Get test user
        user_profile = await sync_to_async(UserProfile.objects.filter(id=1).first)()
        if not user_profile:
            logger.error("❌ Test user not found")
            return False
        
        # Generate wheel
        strategy_framework = {
            "domains": {
                "mindfulness": {"name": "Mindfulness", "weight": 0.3},
                "physical": {"name": "Physical", "weight": 0.3},
                "creative": {"name": "Creative", "weight": 0.4}
            },
            "challenge_calibration": {
                "base_level": 40,
                "adjustment_factors": {"trust_phase": "foundation"}
            }
        }
        
        result = await generate_wheel({
            "user_profile_id": user_profile.id,
            "strategy_framework": strategy_framework,
            "activity_count": 6  # Test with more items
        })
        
        if "error" in result:
            logger.error(f"❌ Wheel generation failed: {result['error']}")
            return False
        
        wheel_data = result["wheel"]
        items = wheel_data.get("items", [])
        
        if len(items) == 0:
            logger.error("❌ No wheel items generated")
            return False
        
        logger.info(f"✅ Generated wheel with {len(items)} items")
        
        # Validate each item
        domain_counts = {}
        color_counts = {}
        
        for i, item in enumerate(items, 1):
            # Required fields
            required_fields = ['id', 'percentage', 'activity_name', 'domain', 'color']
            for field in required_fields:
                if field not in item:
                    logger.error(f"❌ Item {i} missing required field: {field}")
                    return False
            
            domain = item['domain']
            color = item['color']
            
            # Track domain distribution
            domain_counts[domain] = domain_counts.get(domain, 0) + 1
            color_counts[color] = color_counts.get(color, 0) + 1
            
            logger.info(f"  {i}. {item['activity_name']} - {domain} ({color})")
        
        # Validate domain diversity
        if len(domain_counts) < 2:
            logger.warning(f"⚠️ Low domain diversity: {len(domain_counts)} unique domains")
        else:
            logger.info(f"✅ Good domain diversity: {len(domain_counts)} unique domains")
        
        # Validate color diversity (should match domain diversity)
        if len(color_counts) != len(domain_counts):
            logger.error(f"❌ Color/domain mismatch: {len(color_counts)} colors vs {len(domain_counts)} domains")
            return False
        
        logger.info(f"✅ Color diversity matches domain diversity: {len(color_counts)} unique colors")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Wheel generation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_database_domain_consistency():
    """Test that database domains are consistent with the domain management service."""
    logger.info("🗄️ Testing Database Domain Consistency...")
    
    try:
        # Get all domains from database
        db_domains = await sync_to_async(list)(
            GenericDomain.objects.values_list('code', flat=True)
        )
        
        # Get domains from service
        service_domains = domain_management_service._get_valid_domains()
        
        logger.info(f"  Database domains: {len(db_domains)}")
        logger.info(f"  Service domains: {len(service_domains)}")
        
        # Check consistency
        db_set = set(db_domains)
        service_set = set(service_domains)
        
        if db_set != service_set:
            missing_in_service = db_set - service_set
            extra_in_service = service_set - db_set
            
            if missing_in_service:
                logger.error(f"❌ Domains in DB but not in service: {missing_in_service}")
            if extra_in_service:
                logger.error(f"❌ Domains in service but not in DB: {extra_in_service}")
            return False
        
        logger.info("✅ Database and service domains are consistent")
        
        # Test color mapping coverage
        domains_without_colors = []
        for domain in db_domains[:20]:  # Test first 20 domains
            color = domain_management_service.get_domain_color(domain)
            if color == '#9E9E9E':  # Default grey color
                domains_without_colors.append(domain)
        
        if domains_without_colors:
            logger.warning(f"⚠️ Domains using default color: {domains_without_colors}")
        else:
            logger.info("✅ All tested domains have specific colors")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Database consistency test failed: {e}")
        return False

async def main():
    """Run all comprehensive domain system tests."""
    logger.info("=" * 80)
    logger.info("COMPREHENSIVE DOMAIN SYSTEM TEST")
    logger.info("=" * 80)
    
    tests = [
        ("Domain Management Service", test_domain_management_service),
        ("Wheel Generation Domains", test_wheel_generation_domains),
        ("Database Domain Consistency", test_database_domain_consistency),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Running: {test_name}")
        try:
            result = await test_func()
            results.append((test_name, result))
            if result:
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
        except Exception as e:
            logger.error(f"💥 {test_name}: CRASHED - {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "=" * 80)
    logger.info("TEST SUMMARY")
    logger.info("=" * 80)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"  {test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 ALL TESTS PASSED! Domain system is working correctly.")
        sys.exit(0)
    else:
        logger.error("💥 SOME TESTS FAILED! Check the logs above for details.")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
