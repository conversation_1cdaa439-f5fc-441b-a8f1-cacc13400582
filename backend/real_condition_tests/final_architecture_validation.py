#!/usr/bin/env python3
"""
Final Architecture Validation

This script provides a comprehensive validation of the new wheel activity agent architecture
to confirm that all components are working correctly and the instructions are properly applied.

Usage:
    docker exec -it backend-web-1 python /usr/src/app/real_condition_tests/final_architecture_validation.py
"""

import os
import sys
import asyncio

# Add the project root to Python path
sys.path.insert(0, '/usr/src/app')

# Django setup
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
import django
django.setup()

from apps.main.models import GenericAgent, AgentRole
from apps.main.agents.wheel_activity_agent import WheelAndActivityAgent
from apps.main.services.programmatic_activity_selector import ProgrammaticActivitySelector, SelectionCriteria


async def validate_database_instructions():
    """Validate that the database has the new tailoring-focused instructions"""
    print("🔍 Validating Database Instructions")
    print("-" * 40)
    
    try:
        agent = GenericAgent.objects.get(role=AgentRole.ACTIVITY)
        
        # Check for new architecture indicators
        new_indicators = [
            "Activity Tailoring Specialist",
            "NEW ARCHITECTURE", 
            "SOLE responsibility",
            "programmatic"
        ]
        
        old_indicators = [
            "Wheel/Activity Agent, responsible for selecting",
            "Select appropriate activities from the GenericActivity catalog"
        ]
        
        new_count = sum(1 for indicator in new_indicators if indicator in agent.system_instructions)
        old_count = sum(1 for indicator in old_indicators if indicator in agent.system_instructions)
        
        print(f"   📊 New Architecture Indicators: {new_count}/{len(new_indicators)}")
        print(f"   📊 Old Architecture Indicators: {old_count}/{len(old_indicators)}")
        print(f"   📊 Agent Version: {agent.version}")
        print(f"   📊 Description: {agent.description[:80]}...")
        
        success = new_count >= 3 and old_count == 0
        print(f"   {'✅' if success else '❌'} Database Instructions: {'UPDATED' if success else 'NEEDS UPDATE'}")
        
        return success
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False


async def validate_programmatic_selection():
    """Validate that programmatic selection is working correctly"""
    print("\n🎯 Validating Programmatic Selection")
    print("-" * 40)
    
    try:
        selector = ProgrammaticActivitySelector("1")
        
        criteria = SelectionCriteria(
            time_available=45,
            energy_level=75,
            available_resources=['time', 'space', 'motivation'],
            target_challenge_range=(40, 60),
            min_activities=5,
            max_activities=8
        )
        
        activities = await selector.select_activities(criteria)
        
        print(f"   📊 Activities Selected: {len(activities)}")
        print(f"   📊 Target Range: {criteria.min_activities}-{criteria.max_activities}")
        
        # Check time matching
        time_matches = sum(1 for a in activities if a.get('estimated_duration', 30) <= 45)
        time_ratio = time_matches / len(activities) if activities else 0
        
        print(f"   ⏰ Time Matching: {time_matches}/{len(activities)} ({time_ratio:.2f})")
        
        # Check domain diversity
        domains = set(a.get('domain', 'general') for a in activities)
        print(f"   🎭 Domain Diversity: {len(domains)} unique domains")
        
        success = (
            criteria.min_activities <= len(activities) <= criteria.max_activities and
            time_ratio >= 0.8 and
            len(domains) >= 1
        )
        
        print(f"   {'✅' if success else '❌'} Programmatic Selection: {'WORKING' if success else 'NEEDS FIX'}")
        
        return success
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False


async def validate_agent_integration():
    """Validate that the agent properly integrates programmatic selection with LLM tailoring"""
    print("\n🤖 Validating Agent Integration")
    print("-" * 40)
    
    try:
        agent = WheelAndActivityAgent(user_profile_id="1")
        
        # Test custom template injection
        custom_template = "Test template for {{USER_NAME}} with {{ENERGY_LEVEL}} energy."
        agent.set_custom_tailoring_template(custom_template)
        
        context_packet = {
            "user_input_context": {
                "energy_level": 75,
                "time_available": 45
            }
        }
        
        # Test contextualization
        contextualized = await agent._contextualize_instructions(context_packet=context_packet)
        
        template_working = "Test template for" in contextualized and "{{USER_NAME}}" not in contextualized
        print(f"   🧪 Custom Template Injection: {'✅ WORKING' if template_working else '❌ FAILED'}")
        
        # Clear template and test database instructions
        agent.clear_custom_tailoring_template()
        
        db_contextualized = await agent._contextualize_instructions(context_packet=context_packet)
        db_working = "Activity Tailoring Specialist" in db_contextualized
        print(f"   📋 Database Instructions: {'✅ WORKING' if db_working else '❌ FAILED'}")
        
        # Test programmatic selection integration
        strategy_framework = {
            "domain_distribution": {
                "domains": {
                    "physical": 25,
                    "mental": 25,
                    "creative": 25,
                    "wellness": 25
                }
            }
        }
        
        activities = await agent._query_activity_catalog(strategy_framework, context_packet)
        selection_working = isinstance(activities, list) and len(activities) > 0
        print(f"   🎯 Selection Integration: {'✅ WORKING' if selection_working else '❌ FAILED'}")
        
        success = template_working and db_working and selection_working
        print(f"   {'✅' if success else '❌'} Agent Integration: {'COMPLETE' if success else 'NEEDS WORK'}")
        
        return success
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False


async def validate_architecture_separation():
    """Validate that the architecture properly separates concerns"""
    print("\n🏗️ Validating Architecture Separation")
    print("-" * 40)
    
    try:
        # Check that agent has new methods
        agent = WheelAndActivityAgent(user_profile_id="1")
        
        has_custom_methods = (
            hasattr(agent, 'set_custom_tailoring_template') and
            hasattr(agent, 'clear_custom_tailoring_template') and
            hasattr(agent, 'custom_tailoring_template')
        )
        
        print(f"   🔧 Custom Template Methods: {'✅ PRESENT' if has_custom_methods else '❌ MISSING'}")
        
        # Check that programmatic selector exists
        try:
            from apps.main.services.programmatic_activity_selector import ProgrammaticActivitySelector
            selector_exists = True
        except ImportError:
            selector_exists = False
        
        print(f"   🎯 Programmatic Selector: {'✅ AVAILABLE' if selector_exists else '❌ MISSING'}")
        
        # Check that agent uses programmatic selection
        await agent._ensure_loaded()
        
        # Mock test to see if _query_activity_catalog uses programmatic logic
        strategy_framework = {"domain_distribution": {"domains": {"general": 100}}}
        context_packet = {"user_input_context": {"energy_level": 75, "time_available": 45}}
        
        activities = await agent._query_activity_catalog(strategy_framework, context_packet)
        uses_programmatic = isinstance(activities, list)
        
        print(f"   🔄 Programmatic Integration: {'✅ ACTIVE' if uses_programmatic else '❌ INACTIVE'}")
        
        success = has_custom_methods and selector_exists and uses_programmatic
        print(f"   {'✅' if success else '❌'} Architecture Separation: {'COMPLETE' if success else 'INCOMPLETE'}")
        
        return success
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False


async def main():
    """Main validation function"""
    print("🏗️ FINAL WHEEL ACTIVITY AGENT ARCHITECTURE VALIDATION")
    print("=" * 60)
    print("Validating the complete new architecture implementation...")
    print()
    
    # Run all validations
    db_valid = await validate_database_instructions()
    selection_valid = await validate_programmatic_selection()
    integration_valid = await validate_agent_integration()
    separation_valid = await validate_architecture_separation()
    
    # Overall assessment
    print("\n" + "=" * 60)
    print("📊 FINAL VALIDATION SUMMARY")
    print("=" * 60)
    
    validations = [
        ("Database Instructions", db_valid),
        ("Programmatic Selection", selection_valid),
        ("Agent Integration", integration_valid),
        ("Architecture Separation", separation_valid)
    ]
    
    passed = sum(1 for _, valid in validations if valid)
    total = len(validations)
    
    for name, valid in validations:
        status = "✅ PASSED" if valid else "❌ FAILED"
        print(f"   {name}: {status}")
    
    overall_success = passed == total
    
    print(f"\n🏆 OVERALL VALIDATION: {passed}/{total} components passed")
    
    if overall_success:
        print("\n🎉 SUCCESS: NEW ARCHITECTURE FULLY VALIDATED!")
        print("🎯 Key Achievements:")
        print("   • Database updated with tailoring-focused instructions")
        print("   • Programmatic selection working with proper scoring")
        print("   • Agent integration complete with custom template support")
        print("   • Architecture separation implemented correctly")
        print("   • LLM usage optimized for creative tailoring tasks")
        print("   • Deterministic selection ensures consistent quality")
        print("\n✅ The wheel activity agent is ready for production use!")
    else:
        print(f"\n⚠️ PARTIAL SUCCESS: {passed}/{total} components working")
        print("🔧 Some components need attention before full deployment")
    
    return overall_success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
