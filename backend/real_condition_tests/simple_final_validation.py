#!/usr/bin/env python3
"""
Simple Final Validation

Quick validation of the key working components of the new architecture.

Usage:
    docker exec -it backend-web-1 python /usr/src/app/real_condition_tests/simple_final_validation.py
"""

import os
import sys
import asyncio

# Add the project root to Python path
sys.path.insert(0, '/usr/src/app')

# Django setup
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
import django
django.setup()

from apps.main.agents.wheel_activity_agent import WheelAndActivityAgent
from apps.main.services.programmatic_activity_selector import ProgrammaticActivitySelector, SelectionCriteria


async def main():
    """Simple validation of key components"""
    print("🎯 SIMPLE FINAL VALIDATION")
    print("=" * 40)
    print("Testing the core working components...")
    print()
    
    # Test 1: Programmatic Selection
    print("1. Testing Programmatic Selection...")
    try:
        selector = ProgrammaticActivitySelector("1")
        criteria = SelectionCriteria(
            time_available=45,
            energy_level=75,
            available_resources=['time', 'space'],
            target_challenge_range=(40, 60),
            min_activities=5,
            max_activities=8
        )
        
        activities = await selector.select_activities(criteria)
        print(f"   ✅ Selected {len(activities)} activities")
        print(f"   📊 Score range: {min(a.get('score', 0) for a in activities):.2f} - {max(a.get('score', 0) for a in activities):.2f}")
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False
    
    # Test 2: Agent Integration
    print("\n2. Testing Agent Integration...")
    try:
        agent = WheelAndActivityAgent(user_profile_id="1")
        
        # Test custom template
        agent.set_custom_tailoring_template("Test for {{USER_NAME}} with {{ENERGY_LEVEL}} energy")
        
        context_packet = {
            "user_input_context": {
                "energy_level": 75,
                "time_available": 45
            }
        }
        
        contextualized = await agent._contextualize_instructions(context_packet=context_packet)
        template_works = "Test for PhiPhi with" in contextualized
        print(f"   ✅ Custom template injection: {'WORKING' if template_works else 'FAILED'}")
        
        # Test programmatic selection
        strategy_framework = {
            "domain_distribution": {
                "domains": {"general": 100}
            }
        }
        
        activities = await agent._query_activity_catalog(strategy_framework, context_packet)
        selection_works = isinstance(activities, list) and len(activities) > 0
        print(f"   ✅ Programmatic selection: {'WORKING' if selection_works else 'FAILED'}")
        
        if not (template_works and selection_works):
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False
    
    # Test 3: Architecture Methods
    print("\n3. Testing Architecture Methods...")
    try:
        agent = WheelAndActivityAgent(user_profile_id="1")
        
        methods_exist = (
            hasattr(agent, 'set_custom_tailoring_template') and
            hasattr(agent, 'clear_custom_tailoring_template') and
            hasattr(agent, 'custom_tailoring_template')
        )
        
        print(f"   ✅ New methods present: {'YES' if methods_exist else 'NO'}")
        
        if not methods_exist:
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False
    
    print("\n" + "=" * 40)
    print("🎉 SUCCESS: Core Architecture Working!")
    print("=" * 40)
    print("✅ Key Components Validated:")
    print("   • Programmatic activity selection")
    print("   • Agent integration with selection")
    print("   • Custom template injection")
    print("   • Architecture separation methods")
    print()
    print("🚀 The new wheel activity agent architecture is ready!")
    print("🎯 Benefits achieved:")
    print("   • Deterministic activity selection")
    print("   • LLM focused on creative tailoring")
    print("   • Improved quality and consistency")
    print("   • Better testability and debugging")
    
    return True


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
