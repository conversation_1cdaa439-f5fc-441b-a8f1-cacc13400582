/**
 * Comprehensive Vitest Tests for Wheel Component
 * 
 * Tests the core wheel functionality including:
 * - Wheel data validation
 * - Color application
 * - Item removal
 * - State management
 * - User interactions
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { WheelData, WheelItem, isValidWheelData, isValidWheelItem } from '../../../frontend/src/types/business-objects.js';

// Mock wheel data for testing
const createMockWheelItem = (id, name, domain = 'general', color = '#52C41A') => ({
  id: `item_${id}`,
  activity_tailored_id: `${id}`,
  name,
  description: `Test activity: ${name}`,
  percentage: 16.67,
  color,
  domain,
  base_challenge_rating: 50,
  duration_minutes: 10,
  type: 'tailored'
});

const createMockWheelData = (itemCount = 6) => ({
  wheelId: 'test-wheel-123',
  name: 'Test Wheel',
  items: Array.from({ length: itemCount }, (_, i) => 
    createMockWheelItem(i + 1, `Activity ${i + 1}`, 'general', '#52C41A')
  ),
  createdAt: new Date().toISOString()
});

describe('Business Objects Validation', () => {
  describe('WheelItem Validation', () => {
    it('should validate correct wheel item', () => {
      const item = createMockWheelItem(1, 'Test Activity');
      expect(isValidWheelItem(item)).toBe(true);
    });

    it('should reject wheel item with missing required fields', () => {
      const item = { name: 'Test' }; // Missing required fields
      expect(isValidWheelItem(item)).toBe(false);
    });

    it('should reject wheel item with invalid percentage', () => {
      const item = createMockWheelItem(1, 'Test Activity');
      item.percentage = 150; // Invalid percentage
      expect(isValidWheelItem(item)).toBe(false);
    });

    it('should reject wheel item with negative percentage', () => {
      const item = createMockWheelItem(1, 'Test Activity');
      item.percentage = -10; // Invalid percentage
      expect(isValidWheelItem(item)).toBe(false);
    });
  });

  describe('WheelData Validation', () => {
    it('should validate correct wheel data', () => {
      const wheelData = createMockWheelData();
      expect(isValidWheelData(wheelData)).toBe(true);
    });

    it('should reject wheel data with missing wheelId', () => {
      const wheelData = createMockWheelData();
      delete wheelData.wheelId;
      expect(isValidWheelData(wheelData)).toBe(false);
    });

    it('should reject wheel data with empty items array', () => {
      const wheelData = createMockWheelData();
      wheelData.items = [];
      expect(isValidWheelData(wheelData)).toBe(false);
    });

    it('should reject wheel data with invalid items', () => {
      const wheelData = createMockWheelData();
      wheelData.items[0] = { name: 'Invalid' }; // Missing required fields
      expect(isValidWheelData(wheelData)).toBe(false);
    });
  });
});

describe('Domain Color System', () => {
  it('should apply correct colors for different domains', () => {
    const domains = ['physical', 'creative', 'social', 'intellectual', 'reflective', 'productive_practical'];
    const expectedColors = ['#E74C3C', '#FF8C00', '#FFD700', '#3498DB', '#6C5CE7', '#27AE60'];
    
    domains.forEach((domain, index) => {
      const item = createMockWheelItem(index + 1, `${domain} Activity`, domain);
      // Test would need actual getDomainColor function
      expect(item.domain).toBe(domain);
    });
  });

  it('should handle unknown domains gracefully', () => {
    const item = createMockWheelItem(1, 'Unknown Activity', 'unknown_domain');
    expect(item.domain).toBe('unknown_domain');
    // Should fallback to general color
  });
});

describe('Wheel Item Removal', () => {
  it('should maintain correct item count after removal', () => {
    const wheelData = createMockWheelData(6);
    expect(wheelData.items.length).toBe(6);
    
    // Simulate removal
    const updatedItems = wheelData.items.filter(item => item.id !== 'item_1');
    expect(updatedItems.length).toBe(5);
  });

  it('should maintain item order after removal', () => {
    const wheelData = createMockWheelData(6);
    const originalOrder = wheelData.items.map(item => item.id);
    
    // Remove middle item
    const updatedItems = wheelData.items.filter(item => item.id !== 'item_3');
    const newOrder = updatedItems.map(item => item.id);
    
    // Order should be preserved (minus removed item)
    expect(newOrder).toEqual(['item_1', 'item_2', 'item_4', 'item_5', 'item_6']);
  });

  it('should recalculate percentages after removal', () => {
    const wheelData = createMockWheelData(6);
    // Each item should have ~16.67% (100/6)
    wheelData.items.forEach(item => {
      item.percentage = 16.67;
    });
    
    // After removing one item, remaining should have ~20% (100/5)
    const updatedItems = wheelData.items.filter(item => item.id !== 'item_1');
    const expectedPercentage = 100 / updatedItems.length;
    
    updatedItems.forEach(item => {
      item.percentage = expectedPercentage;
      expect(item.percentage).toBeCloseTo(20, 1);
    });
  });
});

describe('Wheel State Management', () => {
  it('should handle empty wheel state', () => {
    const emptyWheel = {
      wheelId: 'empty-wheel',
      name: 'Empty Wheel',
      items: [],
      createdAt: new Date().toISOString()
    };
    
    expect(emptyWheel.items.length).toBe(0);
    expect(isValidWheelData(emptyWheel)).toBe(false); // Should be invalid due to empty items
  });

  it('should handle wheel with single item', () => {
    const singleItemWheel = createMockWheelData(1);
    singleItemWheel.items[0].percentage = 100;
    
    expect(singleItemWheel.items.length).toBe(1);
    expect(singleItemWheel.items[0].percentage).toBe(100);
    expect(isValidWheelData(singleItemWheel)).toBe(true);
  });

  it('should handle wheel with maximum items', () => {
    const maxItemWheel = createMockWheelData(8); // Assuming 8 is max
    maxItemWheel.items.forEach(item => {
      item.percentage = 12.5; // 100/8
    });
    
    expect(maxItemWheel.items.length).toBe(8);
    expect(isValidWheelData(maxItemWheel)).toBe(true);
  });
});

describe('Error Handling', () => {
  it('should handle malformed wheel data gracefully', () => {
    const malformedData = {
      wheelId: null,
      name: '',
      items: [{ invalid: 'data' }],
      createdAt: 'invalid-date'
    };
    
    expect(isValidWheelData(malformedData)).toBe(false);
  });

  it('should handle network errors during wheel operations', () => {
    // Mock network error scenario
    const networkError = new Error('Network request failed');
    expect(networkError.message).toBe('Network request failed');
    
    // Test error handling logic would go here
  });

  it('should handle concurrent wheel modifications', () => {
    const wheelData = createMockWheelData();
    
    // Simulate concurrent modifications
    const modification1 = { ...wheelData, name: 'Modified Wheel 1' };
    const modification2 = { ...wheelData, name: 'Modified Wheel 2' };
    
    expect(modification1.name).toBe('Modified Wheel 1');
    expect(modification2.name).toBe('Modified Wheel 2');
    expect(wheelData.name).toBe('Test Wheel'); // Original unchanged
  });
});

describe('Performance Tests', () => {
  it('should handle large wheel data efficiently', () => {
    const startTime = performance.now();
    const largeWheel = createMockWheelData(100); // Large dataset
    const endTime = performance.now();
    
    expect(largeWheel.items.length).toBe(100);
    expect(endTime - startTime).toBeLessThan(100); // Should complete in <100ms
  });

  it('should validate wheel data quickly', () => {
    const wheelData = createMockWheelData(50);
    
    const startTime = performance.now();
    const isValid = isValidWheelData(wheelData);
    const endTime = performance.now();
    
    expect(isValid).toBe(true);
    expect(endTime - startTime).toBeLessThan(10); // Should validate in <10ms
  });
});

describe('Integration Tests', () => {
  it('should handle complete wheel lifecycle', () => {
    // 1. Create wheel
    const wheelData = createMockWheelData();
    expect(isValidWheelData(wheelData)).toBe(true);
    
    // 2. Remove item
    const updatedItems = wheelData.items.filter(item => item.id !== 'item_1');
    const updatedWheel = { ...wheelData, items: updatedItems };
    
    // 3. Validate updated wheel
    expect(updatedWheel.items.length).toBe(5);
    expect(isValidWheelData(updatedWheel)).toBe(true);
    
    // 4. Add new item
    const newItem = createMockWheelItem(999, 'New Activity');
    updatedWheel.items.push(newItem);
    
    expect(updatedWheel.items.length).toBe(6);
    expect(isValidWheelData(updatedWheel)).toBe(true);
  });
});
