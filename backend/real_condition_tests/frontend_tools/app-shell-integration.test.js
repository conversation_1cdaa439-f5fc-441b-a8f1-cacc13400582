/**
 * Comprehensive Integration Tests for App Shell
 * 
 * Tests the complete user flow from wheel generation request
 * to wheel display and interaction.
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';

// Mock dependencies
const mockWebSocket = {
  send: vi.fn(),
  close: vi.fn(),
  readyState: 1,
  addEventListener: vi.fn(),
  removeEventListener: vi.fn()
};

const mockWheelStateMachine = {
  setWheelData: vi.fn(),
  getState: vi.fn(() => 'POPULATED'),
  shouldShowPopulatedWheel: true,
  shouldShowUnpopulatedWheel: false,
  shouldShowProgressBar: false
};

// Test data factories
const createTestWheelData = (id = 'test-wheel', itemCount = 6) => ({
  wheelId: id,
  name: 'Test Wheel',
  items: Array.from({ length: itemCount }, (_, i) => ({
    id: `item_${id}_${i + 1}_${100 + i}`,
    name: `Activity ${i + 1}`,
    description: `Test activity ${i + 1}`,
    percentage: 100 / itemCount,
    color: '#52C41A',
    domain: 'general',
    activity_tailored_id: `${100 + i}`,
    base_challenge_rating: 50,
    duration_minutes: 10
  })),
  createdAt: new Date().toISOString()
});

const createTestMessage = (content = 'Test message') => ({
  id: `msg_${Date.now()}`,
  type: 'ai',
  content,
  timestamp: new Date().toISOString()
});

describe('App Shell Integration Tests', () => {
  let appShell;

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock app shell instance
    appShell = {
      wheelStateMachine: mockWheelStateMachine,
      webSocket: mockWebSocket,
      messages: [],
      isLoading: false,
      isConnected: false,
      
      // Methods
      sendWheelGenerationRequest: vi.fn(),
      handleWheelData: vi.fn(),
      handleChatMessage: vi.fn(),
      removeWheelItem: vi.fn(),
      requestUpdate: vi.fn()
    };
  });

  describe('Wheel Generation Flow', () => {
    it('should handle complete wheel generation flow', async () => {
      // 1. User initiates wheel generation
      const request = {
        time_available: 10,
        energy_level: 'high',
        mood: 'focused'
      };
      
      appShell.sendWheelGenerationRequest(request);
      expect(appShell.sendWheelGenerationRequest).toHaveBeenCalledWith(request);
      
      // 2. Receive processing status
      const processingMessage = {
        type: 'processing_status',
        status: 'processing',
        stage: 'activity_selection',
        progress: 50
      };
      
      appShell.isLoading = true;
      expect(appShell.isLoading).toBe(true);
      
      // 3. Receive wheel data
      const wheelData = createTestWheelData();
      appShell.handleWheelData(wheelData);
      
      expect(appShell.handleWheelData).toHaveBeenCalledWith(wheelData);
      expect(mockWheelStateMachine.setWheelData).toHaveBeenCalledWith(wheelData);
      
      // 4. Update UI state
      appShell.isLoading = false;
      appShell.requestUpdate();
      
      expect(appShell.isLoading).toBe(false);
      expect(appShell.requestUpdate).toHaveBeenCalled();
    });

    it('should handle wheel generation errors gracefully', () => {
      const errorMessage = {
        type: 'error',
        error: 'Wheel generation failed',
        context: 'activity_selection'
      };
      
      // Should not crash the app
      expect(() => {
        appShell.isLoading = false;
        appShell.messages.push({
          type: 'error',
          content: errorMessage.error,
          timestamp: new Date().toISOString()
        });
      }).not.toThrow();
      
      expect(appShell.isLoading).toBe(false);
      expect(appShell.messages.length).toBe(1);
      expect(appShell.messages[0].type).toBe('error');
    });

    it('should validate wheel data before setting state', () => {
      const invalidWheelData = {
        wheelId: null,
        items: []
      };
      
      // Should validate before setting
      const isValid = (
        invalidWheelData.wheelId &&
        invalidWheelData.items &&
        invalidWheelData.items.length > 0
      );
      
      expect(isValid).toBe(false);
      
      if (!isValid) {
        console.warn('Invalid wheel data received');
      }
    });
  });

  describe('Wheel Item Removal', () => {
    it('should handle wheel item removal correctly', async () => {
      const wheelData = createTestWheelData();
      const itemToRemove = wheelData.items[0];
      
      // Mock removal API call
      const mockFetch = vi.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ success: true })
      });
      global.fetch = mockFetch;
      
      // Remove item
      appShell.removeWheelItem(itemToRemove.id);
      
      expect(appShell.removeWheelItem).toHaveBeenCalledWith(itemToRemove.id);
      
      // Verify API call
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/wheel-items/'),
        expect.objectContaining({
          method: 'DELETE'
        })
      );
    });

    it('should maintain wheel integrity after item removal', () => {
      const wheelData = createTestWheelData('test-wheel', 6);
      const originalItemCount = wheelData.items.length;
      
      // Simulate item removal
      const updatedItems = wheelData.items.filter(item => item.id !== wheelData.items[0].id);
      const updatedWheelData = {
        ...wheelData,
        items: updatedItems
      };
      
      expect(updatedWheelData.items.length).toBe(originalItemCount - 1);
      expect(updatedWheelData.wheelId).toBe(wheelData.wheelId);
      
      // Verify item IDs are still valid
      updatedWheelData.items.forEach(item => {
        expect(item.id).toMatch(/^item_test-wheel_\d+_\d+$/);
        expect(item.activity_tailored_id).toBeDefined();
      });
    });

    it('should handle removal errors gracefully', async () => {
      const mockFetch = vi.fn().mockRejectedValue(new Error('Network error'));
      global.fetch = mockFetch;
      
      // Should not crash on removal error
      expect(async () => {
        try {
          await appShell.removeWheelItem('item_1');
        } catch (error) {
          console.error('Removal failed:', error);
        }
      }).not.toThrow();
    });
  });

  describe('Message Handling', () => {
    it('should handle chat messages correctly', () => {
      const chatMessage = createTestMessage('Hello from AI');
      
      appShell.handleChatMessage(chatMessage);
      appShell.messages.push(chatMessage);
      
      expect(appShell.handleChatMessage).toHaveBeenCalledWith(chatMessage);
      expect(appShell.messages.length).toBe(1);
      expect(appShell.messages[0].content).toBe('Hello from AI');
    });

    it('should maintain message order', () => {
      const messages = [
        createTestMessage('Message 1'),
        createTestMessage('Message 2'),
        createTestMessage('Message 3')
      ];
      
      messages.forEach(msg => {
        appShell.messages.push(msg);
      });
      
      expect(appShell.messages.length).toBe(3);
      expect(appShell.messages[0].content).toBe('Message 1');
      expect(appShell.messages[2].content).toBe('Message 3');
    });

    it('should handle message overflow gracefully', () => {
      const maxMessages = 100;
      
      // Add many messages
      for (let i = 0; i < maxMessages + 10; i++) {
        appShell.messages.push(createTestMessage(`Message ${i}`));
      }
      
      // Should handle gracefully (might implement truncation)
      expect(appShell.messages.length).toBeGreaterThan(maxMessages);
    });
  });

  describe('State Management', () => {
    it('should maintain consistent state across operations', () => {
      // Initial state
      expect(appShell.isLoading).toBe(false);
      expect(appShell.isConnected).toBe(false);
      expect(appShell.messages.length).toBe(0);
      
      // Connection established
      appShell.isConnected = true;
      expect(appShell.isConnected).toBe(true);
      
      // Loading state
      appShell.isLoading = true;
      expect(appShell.isLoading).toBe(true);
      
      // Wheel received
      const wheelData = createTestWheelData();
      mockWheelStateMachine.setWheelData(wheelData);
      appShell.isLoading = false;
      
      expect(appShell.isLoading).toBe(false);
      expect(mockWheelStateMachine.setWheelData).toHaveBeenCalledWith(wheelData);
    });

    it('should handle state transitions correctly', () => {
      // Empty -> Loading
      appShell.isLoading = true;
      expect(appShell.isLoading).toBe(true);
      
      // Loading -> Populated
      const wheelData = createTestWheelData();
      mockWheelStateMachine.setWheelData(wheelData);
      appShell.isLoading = false;
      
      expect(mockWheelStateMachine.setWheelData).toHaveBeenCalledWith(wheelData);
      expect(appShell.isLoading).toBe(false);
      
      // Populated -> Empty (after removal)
      mockWheelStateMachine.setWheelData(null);
      expect(mockWheelStateMachine.setWheelData).toHaveBeenCalledWith(null);
    });
  });

  describe('Error Recovery', () => {
    it('should recover from WebSocket disconnection', () => {
      // Simulate disconnection
      appShell.isConnected = false;
      
      // Should attempt reconnection
      expect(appShell.isConnected).toBe(false);
      
      // Simulate reconnection
      appShell.isConnected = true;
      expect(appShell.isConnected).toBe(true);
    });

    it('should handle partial data gracefully', () => {
      const partialWheelData = {
        wheelId: 'partial-wheel',
        items: [
          {
            id: 'item_1',
            name: 'Partial Activity'
            // Missing other required fields
          }
        ]
      };
      
      // Should validate and handle gracefully
      const isValid = partialWheelData.items.every(item => 
        item.id && item.name && item.percentage !== undefined
      );
      
      expect(isValid).toBe(false);
    });

    it('should provide meaningful error messages to users', () => {
      const error = new Error('Network timeout');
      
      const userFriendlyMessage = (() => {
        if (error.message.includes('timeout')) {
          return 'Connection timed out. Please try again.';
        } else if (error.message.includes('network')) {
          return 'Network error. Please check your connection.';
        } else {
          return 'Something went wrong. Please try again.';
        }
      })();
      
      expect(userFriendlyMessage).toBe('Connection timed out. Please try again.');
    });
  });

  describe('Performance', () => {
    it('should handle rapid state updates efficiently', () => {
      const startTime = performance.now();
      
      // Simulate rapid updates
      for (let i = 0; i < 100; i++) {
        appShell.isLoading = i % 2 === 0;
        appShell.requestUpdate();
      }
      
      const endTime = performance.now();
      
      expect(endTime - startTime).toBeLessThan(100); // Should complete in <100ms
      expect(appShell.requestUpdate).toHaveBeenCalledTimes(100);
    });

    it('should handle large message volumes efficiently', () => {
      const startTime = performance.now();
      
      // Add many messages
      for (let i = 0; i < 1000; i++) {
        appShell.messages.push(createTestMessage(`Message ${i}`));
      }
      
      const endTime = performance.now();
      
      expect(endTime - startTime).toBeLessThan(200); // Should complete in <200ms
      expect(appShell.messages.length).toBe(1000);
    });
  });
});
