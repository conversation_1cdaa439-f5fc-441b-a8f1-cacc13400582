/**
 * Comprehensive Vitest Tests for Message Handler
 * 
 * Tests WebSocket message handling, state management, and error handling
 * for the frontend message processing system.
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';

// Mock WebSocket and related dependencies
const mockWebSocket = {
  send: vi.fn(),
  close: vi.fn(),
  readyState: 1, // OPEN
  addEventListener: vi.fn(),
  removeEventListener: vi.fn()
};

const mockStateManager = {
  setCurrentWheel: vi.fn(),
  updateChatMessages: vi.fn(),
  setConnectionStatus: vi.fn(),
  setLoadingState: vi.fn(),
  getState: vi.fn(() => ({
    isConnected: true,
    currentWheel: null,
    chatMessages: []
  }))
};

// Mock message types
const createWheelDataMessage = (wheelId = 'test-wheel-123', itemCount = 6) => ({
  type: 'wheel_data',
  wheel: {
    id: wheelId,
    name: 'Test Wheel',
    items: Array.from({ length: itemCount }, (_, i) => ({
      id: `item_${wheelId}_${i + 1}_${100 + i}`,
      name: `Activity ${i + 1}`,
      description: `Test activity ${i + 1}`,
      percentage: 100 / itemCount,
      color: '#52C41A',
      domain: 'general',
      activity_tailored_id: `${100 + i}`,
      base_challenge_rating: 50
    })),
    metadata: {
      created_at: new Date().toISOString(),
      trust_phase: 'foundation'
    }
  }
});

const createChatMessage = (content = 'Test message', type = 'ai') => ({
  type: 'chat_message',
  message: {
    id: `msg_${Date.now()}`,
    type,
    content,
    timestamp: new Date().toISOString()
  }
});

const createProcessingStatusMessage = (status = 'processing', stage = 'activity_selection') => ({
  type: 'processing_status',
  status,
  stage,
  progress: 50,
  message: 'Processing wheel generation...'
});

describe('Message Handler Core Functionality', () => {
  let messageHandler;

  beforeEach(() => {
    vi.clearAllMocks();
    // Mock MessageHandler class would be imported here
    messageHandler = {
      handleMessage: vi.fn(),
      handleWheelData: vi.fn(),
      handleChatMessage: vi.fn(),
      handleProcessingStatus: vi.fn(),
      handleError: vi.fn()
    };
  });

  describe('Message Routing', () => {
    it('should route wheel_data messages correctly', () => {
      const message = createWheelDataMessage();
      
      // Simulate message routing
      if (message.type === 'wheel_data') {
        messageHandler.handleWheelData(message);
      }
      
      expect(messageHandler.handleWheelData).toHaveBeenCalledWith(message);
    });

    it('should route chat_message messages correctly', () => {
      const message = createChatMessage();
      
      if (message.type === 'chat_message') {
        messageHandler.handleChatMessage(message);
      }
      
      expect(messageHandler.handleChatMessage).toHaveBeenCalledWith(message);
    });

    it('should route processing_status messages correctly', () => {
      const message = createProcessingStatusMessage();
      
      if (message.type === 'processing_status') {
        messageHandler.handleProcessingStatus(message);
      }
      
      expect(messageHandler.handleProcessingStatus).toHaveBeenCalledWith(message);
    });

    it('should handle unknown message types gracefully', () => {
      const unknownMessage = { type: 'unknown_type', data: 'test' };
      
      // Should not throw error
      expect(() => {
        if (!['wheel_data', 'chat_message', 'processing_status'].includes(unknownMessage.type)) {
          console.warn('Unknown message type:', unknownMessage.type);
        }
      }).not.toThrow();
    });
  });

  describe('Wheel Data Processing', () => {
    it('should process valid wheel data correctly', () => {
      const message = createWheelDataMessage();
      
      // Simulate wheel data processing
      const wheelData = {
        wheelId: message.wheel.id,
        name: message.wheel.name,
        items: message.wheel.items,
        createdAt: message.wheel.metadata.created_at
      };
      
      expect(wheelData.items.length).toBe(6);
      expect(wheelData.wheelId).toBe('test-wheel-123');
      expect(wheelData.items[0].id).toMatch(/^item_test-wheel-123_1_/);
    });

    it('should validate wheel data structure', () => {
      const message = createWheelDataMessage();
      
      // Validate required fields
      expect(message.wheel).toBeDefined();
      expect(message.wheel.id).toBeDefined();
      expect(message.wheel.items).toBeDefined();
      expect(Array.isArray(message.wheel.items)).toBe(true);
      expect(message.wheel.items.length).toBeGreaterThan(0);
    });

    it('should handle malformed wheel data', () => {
      const malformedMessage = {
        type: 'wheel_data',
        wheel: {
          // Missing required fields
          items: null
        }
      };
      
      // Should handle gracefully
      expect(() => {
        if (!malformedMessage.wheel?.items || !Array.isArray(malformedMessage.wheel.items)) {
          throw new Error('Invalid wheel data structure');
        }
      }).toThrow('Invalid wheel data structure');
    });

    it('should apply domain colors to wheel items', () => {
      const message = createWheelDataMessage();
      
      // Simulate color application
      const processedItems = message.wheel.items.map(item => ({
        ...item,
        color: item.color || '#52C41A' // Fallback color
      }));
      
      processedItems.forEach(item => {
        expect(item.color).toBeDefined();
        expect(item.color).toMatch(/^#[0-9A-Fa-f]{6}$/);
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle WebSocket connection errors', () => {
      const connectionError = new Error('WebSocket connection failed');
      
      expect(() => {
        messageHandler.handleError(connectionError);
      }).not.toThrow();
    });

    it('should handle message parsing errors', () => {
      const invalidJson = '{ invalid json }';
      
      expect(() => {
        try {
          JSON.parse(invalidJson);
        } catch (error) {
          messageHandler.handleError(error);
        }
      }).not.toThrow();
    });

    it('should handle state update errors', () => {
      const stateError = new Error('State update failed');
      
      expect(() => {
        try {
          mockStateManager.setCurrentWheel(null);
          throw stateError;
        } catch (error) {
          messageHandler.handleError(error);
        }
      }).not.toThrow();
    });

    it('should create beta error context for user reporting', () => {
      const error = new Error('Test error');
      const errorContext = {
        timestamp: new Date().toISOString(),
        session_id: 'test-session',
        error_type: 'network',
        error_message: error.message,
        context_data: {
          current_state: 'wheel_generation',
          last_action: 'send_wheel_request',
          browser_info: 'test-browser'
        }
      };
      
      expect(errorContext.error_message).toBe('Test error');
      expect(errorContext.error_type).toBe('network');
      expect(errorContext.context_data.current_state).toBe('wheel_generation');
    });
  });

  describe('State Management Integration', () => {
    it('should update state when receiving wheel data', () => {
      const message = createWheelDataMessage();
      
      // Simulate state update
      const wheelData = {
        wheelId: message.wheel.id,
        name: message.wheel.name,
        items: message.wheel.items,
        createdAt: message.wheel.metadata.created_at
      };
      
      mockStateManager.setCurrentWheel(wheelData);
      
      expect(mockStateManager.setCurrentWheel).toHaveBeenCalledWith(wheelData);
    });

    it('should update chat messages when receiving chat data', () => {
      const message = createChatMessage();
      
      mockStateManager.updateChatMessages(message.message);
      
      expect(mockStateManager.updateChatMessages).toHaveBeenCalledWith(message.message);
    });

    it('should update connection status on WebSocket events', () => {
      mockStateManager.setConnectionStatus(true);
      expect(mockStateManager.setConnectionStatus).toHaveBeenCalledWith(true);
      
      mockStateManager.setConnectionStatus(false);
      expect(mockStateManager.setConnectionStatus).toHaveBeenCalledWith(false);
    });
  });

  describe('Performance Tests', () => {
    it('should handle rapid message processing', () => {
      const messages = Array.from({ length: 100 }, (_, i) => 
        createChatMessage(`Message ${i}`, 'ai')
      );
      
      const startTime = performance.now();
      
      messages.forEach(message => {
        messageHandler.handleChatMessage(message);
      });
      
      const endTime = performance.now();
      
      expect(endTime - startTime).toBeLessThan(100); // Should process in <100ms
      expect(messageHandler.handleChatMessage).toHaveBeenCalledTimes(100);
    });

    it('should handle large wheel data efficiently', () => {
      const largeWheelMessage = createWheelDataMessage('large-wheel', 50);
      
      const startTime = performance.now();
      messageHandler.handleWheelData(largeWheelMessage);
      const endTime = performance.now();
      
      expect(endTime - startTime).toBeLessThan(50); // Should process in <50ms
      expect(messageHandler.handleWheelData).toHaveBeenCalledWith(largeWheelMessage);
    });
  });

  describe('Message Validation', () => {
    it('should validate message structure before processing', () => {
      const validMessage = createWheelDataMessage();
      
      // Validate message structure
      const isValid = (
        validMessage.type &&
        typeof validMessage.type === 'string' &&
        validMessage.wheel &&
        typeof validMessage.wheel === 'object'
      );
      
      expect(isValid).toBe(true);
    });

    it('should reject messages with missing required fields', () => {
      const invalidMessage = {
        type: 'wheel_data'
        // Missing wheel data
      };
      
      const isValid = (
        invalidMessage.type &&
        invalidMessage.wheel &&
        invalidMessage.wheel.items
      );
      
      expect(isValid).toBe(false);
    });

    it('should validate wheel item structure', () => {
      const message = createWheelDataMessage();
      const item = message.wheel.items[0];
      
      const isValidItem = (
        item.id &&
        item.name &&
        typeof item.percentage === 'number' &&
        item.percentage >= 0 &&
        item.percentage <= 100
      );
      
      expect(isValidItem).toBe(true);
    });
  });
});
