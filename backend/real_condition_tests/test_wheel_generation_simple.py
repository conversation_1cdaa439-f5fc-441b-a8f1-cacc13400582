#!/usr/bin/env python3
"""
Simple Wheel Generation Test

This test validates that wheel generation works with the current database structure,
before applying the ActivityTailored-UserEnvironment relationship changes.

Usage:
    docker exec -it backend-web-1 python /usr/src/app/real_condition_tests/test_wheel_generation_simple.py
"""

import os
import sys
import django
import logging
import asyncio

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from apps.user.models import UserProfile
from apps.main.agents.tools.tools import generate_wheel
from asgiref.sync import sync_to_async

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_wheel_generation():
    """Test wheel generation with PhiPhi user."""
    logger.info("🚀 Starting simple wheel generation test")

    try:
        # Get PhiPhi user (ID: 1)
        user_profile = await sync_to_async(UserProfile.objects.filter(id=1).first)()
        if not user_profile:
            logger.error("❌ PhiPhi user (ID: 1) not found")
            return False

        logger.info(f"✅ Found user: {user_profile.profile_name} (ID: {user_profile.id})")

        # Test wheel generation
        strategy_framework = {
            "domains": {
                "mindfulness": {"name": "Mindfulness", "weight": 0.4},
                "physical": {"name": "Physical", "weight": 0.3},
                "creative": {"name": "Creative", "weight": 0.3}
            },
            "challenge_calibration": {
                "base_level": 40,
                "adjustment_factors": {"trust_phase": "foundation"}
            }
        }

        logger.info("🎡 Generating wheel...")
        result = await generate_wheel({
            "user_profile_id": user_profile.id,
            "strategy_framework": strategy_framework,
            "activity_count": 4
        })
        
        # Check result
        if "error" in result:
            logger.error(f"❌ Wheel generation failed: {result['error']}")
            return False
        
        if "wheel" not in result:
            logger.error("❌ No wheel data in result")
            return False
        
        wheel_data = result["wheel"]
        if "items" not in wheel_data or len(wheel_data["items"]) == 0:
            logger.error("❌ No wheel items generated")
            return False
        
        logger.info(f"✅ Wheel generated successfully with {len(wheel_data['items'])} items")
        
        # Log wheel items and validate domain information (clean architecture)
        domain_validation_passed = True
        backend_colors_found = False

        for i, item in enumerate(wheel_data["items"], 1):
            activity_name = item.get('activity_name', 'Unknown')
            percentage = item.get('percentage', 0)
            domain = item.get('domain', 'missing')
            color = item.get('color', 'missing')

            logger.info(f"  {i}. {activity_name} ({percentage:.1f}%) - Domain: {domain}, Color: {color}")

            # Validate domain information (clean architecture expectations)
            if domain == 'missing' or domain == 'general':
                logger.error(f"❌ Item {i} missing or generic domain information")
                domain_validation_passed = False

            # Check for backend color assignment (should NOT happen in clean architecture)
            if color != 'missing' and color != '#95A5A6':
                logger.warning(f"⚠️ Item {i} has backend-assigned color {color} - violates clean architecture")
                backend_colors_found = True

        if not domain_validation_passed:
            logger.error("❌ Domain validation failed - wheel items missing proper domain information")
            return False

        if backend_colors_found:
            logger.warning("⚠️ Backend is assigning colors - should be handled by frontend")

        logger.info("✅ Clean architecture validation passed - domains present, colors handled by frontend")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed with exception: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main function to run the test."""
    logger.info("=" * 60)
    logger.info("SIMPLE WHEEL GENERATION TEST")
    logger.info("=" * 60)

    success = await test_wheel_generation()

    logger.info("=" * 60)
    if success:
        logger.info("🎉 Test PASSED! Wheel generation is working correctly.")
        sys.exit(0)
    else:
        logger.error("💥 Test FAILED! Check the logs above for details.")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
