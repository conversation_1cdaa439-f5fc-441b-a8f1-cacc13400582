#!/usr/bin/env python3
"""
Comprehensive Agent Quality Mission - Multi-Workflow Excellence

This test validates the complete user journey across all major workflows:
- Onboarding workflow
- Wheel generation workflow  
- Post spin workflow
- Post activity workflow (to be implemented)

Focus areas:
- Agent quality across all workflows
- Conversation dispatcher routing
- Performance measurement and optimization
- ADHD-specific user experience
- Database enrichment throughout journey
"""

import os
import sys
import django
import asyncio
import json
import time
from datetime import datetime
from typing import Dict, Any, List, Optional

# Add the project root to Python path
sys.path.insert(0, '/usr/src/app')

# Configure Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from django.db import transaction
from django.contrib.auth import get_user_model
from apps.user.models import UserProfile, UserEnvironment, GenericEnvironment, Demographics, Preference
from apps.main.services.conversation_dispatcher import ConversationDispatcher
from apps.main.agents.tools.tools_util import execute_tool

class ComprehensiveAgentQualityTester:
    """
    Comprehensive tester for agent quality across all workflows.
    
    Simulates a complete user journey from onboarding to activity completion,
    measuring performance and validating agent quality at each step.
    """
    
    def __init__(self):
        self.test_user_id = None
        self.test_results = {
            'start_time': datetime.now().isoformat(),
            'workflows_tested': [],
            'performance_metrics': {},
            'agent_quality_scores': {},
            'database_changes': {},
            'errors': [],
            'bottlenecks': [],
            'recommendations': []
        }
        
    async def run_comprehensive_test(self) -> Dict[str, Any]:
        """
        Execute the complete comprehensive agent quality test.
        
        Returns:
            dict: Complete test results with performance metrics and quality scores
        """
        print("🚀 Starting Comprehensive Agent Quality Mission")
        print("=" * 60)
        
        try:
            # Phase 1: Setup test environment
            await self._setup_test_environment()
            
            # Phase 2: Execute complete user journey
            await self._execute_user_journey()
            
            # Phase 3: Analyze performance and quality
            await self._analyze_results()
            
            # Phase 4: Generate recommendations
            await self._generate_recommendations()
            
            self.test_results['end_time'] = datetime.now().isoformat()
            self.test_results['total_duration'] = self._calculate_total_duration()
            self.test_results['status'] = 'completed'
            
            print("\n✅ Comprehensive Agent Quality Mission Completed")
            self._print_summary()
            
            return self.test_results
            
        except Exception as e:
            self.test_results['status'] = 'failed'
            self.test_results['error'] = str(e)
            self.test_results['end_time'] = datetime.now().isoformat()
            
            print(f"\n❌ Mission Failed: {str(e)}")
            import traceback
            traceback.print_exc()
            
            return self.test_results
    
    async def _setup_test_environment(self):
        """Setup test user profile and environment."""
        print("\n📋 Phase 1: Setting up test environment")
        
        start_time = time.time()
        
        # Create test user profile (21-year-old ADHD student in Berlin)
        self.test_user_id = await self._create_test_user()
        print(f"   ✓ Created test user: {self.test_user_id}")
        
        # Validate initial profile state
        initial_profile = await self._get_profile_state()
        self.test_results['database_changes']['initial_profile'] = initial_profile
        print(f"   ✓ Initial profile completion: {initial_profile.get('completion', 0):.1%}")
        
        setup_duration = time.time() - start_time
        self.test_results['performance_metrics']['setup_duration'] = setup_duration
        print(f"   ⏱️  Setup completed in {setup_duration:.2f}s")
    
    async def _execute_user_journey(self):
        """Execute the complete user journey across all workflows."""
        print("\n🎯 Phase 2: Executing complete user journey")
        
        # Step 1: Initial message and onboarding
        await self._test_onboarding_workflow()
        
        # Step 2: Wheel generation
        await self._test_wheel_generation_workflow()
        
        # Step 3: Post spin workflow
        await self._test_post_spin_workflow()
        
        # Step 4: Post activity workflow (to be implemented)
        await self._test_post_activity_workflow()
    
    async def _test_onboarding_workflow(self):
        """Test the onboarding workflow."""
        print("\n   🔄 Testing Onboarding Workflow")
        
        start_time = time.time()
        
        # Create conversation dispatcher
        dispatcher = ConversationDispatcher(
            user_profile_id=str(self.test_user_id),
            user_ws_session_name=f"test_session_{self.test_user_id}",
            fail_fast_on_errors=True
        )
        
        # Send initial message
        user_message = {
            "text": "hello, what do you propose ? I'm a 21-year-old student in Berlin and I have ADHD. I'm stressed about my upcoming exams and need help focusing.",
            "metadata": {
                "user_characteristics": "ADHD student with upcoming exams",
                "test_context": "comprehensive_agent_quality"
            }
        }
        
        try:
            response = await dispatcher.process_message(user_message)
            
            duration = time.time() - start_time
            self.test_results['performance_metrics']['onboarding_duration'] = duration
            
            # Validate response
            workflow_type = response.get('workflow_type')
            confidence = response.get('confidence', 0)
            
            print(f"      ✓ Workflow classified as: {workflow_type} (confidence: {confidence:.2f})")
            print(f"      ⏱️  Onboarding completed in {duration:.2f}s")
            
            # Record workflow test
            self.test_results['workflows_tested'].append({
                'workflow': 'onboarding',
                'duration': duration,
                'classification': workflow_type,
                'confidence': confidence,
                'status': 'completed'
            })
            
            # Check profile enrichment
            profile_after = await self._get_profile_state()
            self.test_results['database_changes']['after_onboarding'] = profile_after
            
            completion_increase = profile_after.get('completion', 0) - \
                                self.test_results['database_changes']['initial_profile'].get('completion', 0)
            
            print(f"      📈 Profile completion increased by {completion_increase:.1%}")
            
        except Exception as e:
            print(f"      ❌ Onboarding workflow failed: {str(e)}")
            self.test_results['errors'].append({
                'workflow': 'onboarding',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            })
    
    async def _test_wheel_generation_workflow(self):
        """Test the wheel generation workflow."""
        print("\n   🎡 Testing Wheel Generation Workflow")
        
        start_time = time.time()
        
        # Create new dispatcher for wheel generation
        dispatcher = ConversationDispatcher(
            user_profile_id=str(self.test_user_id),
            user_ws_session_name=f"test_session_{self.test_user_id}",
            fail_fast_on_errors=True
        )
        
        # Send wheel generation request
        user_message = {
            "text": "Thanks for the introduction! Now I really need some activities to help me focus and manage my stress. Can you suggest some activities for me? I want to spin the wheel and get some recommendations.",
            "metadata": {
                "user_state": "stressed",
                "context": "exam preparation",
                "test_context": "wheel_generation"
            }
        }
        
        try:
            response = await dispatcher.process_message(user_message)
            
            duration = time.time() - start_time
            self.test_results['performance_metrics']['wheel_generation_duration'] = duration
            
            workflow_type = response.get('workflow_type')
            confidence = response.get('confidence', 0)
            
            print(f"      ✓ Workflow classified as: {workflow_type} (confidence: {confidence:.2f})")
            print(f"      ⏱️  Wheel generation completed in {duration:.2f}s")
            
            # Record workflow test
            self.test_results['workflows_tested'].append({
                'workflow': 'wheel_generation',
                'duration': duration,
                'classification': workflow_type,
                'confidence': confidence,
                'status': 'completed'
            })
            
            # Analyze wheel quality (if generated)
            await self._analyze_wheel_quality()
            
        except Exception as e:
            print(f"      ❌ Wheel generation workflow failed: {str(e)}")
            self.test_results['errors'].append({
                'workflow': 'wheel_generation',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            })
    
    async def _test_post_spin_workflow(self):
        """Test the post spin workflow."""
        print("\n   🎯 Testing Post Spin Workflow")
        
        # This would test the post-spin workflow
        # For now, we'll simulate it
        print("      ⚠️  Post spin workflow test - simulated (requires wheel spin)")
        
        self.test_results['workflows_tested'].append({
            'workflow': 'post_spin',
            'duration': 0,
            'status': 'simulated',
            'note': 'Requires actual wheel spin to test'
        })
    
    async def _test_post_activity_workflow(self):
        """Test the post activity workflow following post_activity_FLOW.md specification."""
        print("\n   📝 Testing Post Activity Workflow")

        start_time = time.time()

        try:
            # Create conversation dispatcher for post activity feedback
            dispatcher = ConversationDispatcher(
                user_profile_id=str(self.test_user_id),
                user_ws_session_name=f"test_session_{self.test_user_id}",
                fail_fast_on_errors=True
            )

            # Simulate user providing feedback about a completed activity
            feedback_message = {
                "text": "I just finished the meditation activity you recommended. It was really helpful for managing my stress. I found it a bit challenging at first but got the hang of it. I feel much more relaxed now.",
                "metadata": {
                    "activity_id": "meditation_001",
                    "activity_name": "Mindful Breathing Meditation",
                    "activity_completion_time": "2025-06-16T16:00:00Z"
                }
            }

            print(f"      📝 Providing activity feedback: {feedback_message['text'][:50]}...")

            # Process the feedback message
            response = await dispatcher.process_message(feedback_message)

            workflow_type = response.get('workflow_type')
            duration = time.time() - start_time

            print(f"      ✓ Workflow classified as: {workflow_type}")
            print(f"      ⏱️  Post activity completed in {duration:.2f}s")

            # Record workflow test
            self.test_results['workflows_tested'].append({
                'workflow': 'post_activity',
                'duration': duration,
                'classification': workflow_type,
                'status': 'completed' if workflow_type == 'post_activity' else 'misclassified'
            })

            # Evaluate quality based on workflow classification and response
            if workflow_type == "post_activity":
                print("      ✅ Successfully classified as post_activity workflow")
            else:
                print(f"      ⚠️  Misclassified as {workflow_type} instead of post_activity")

        except Exception as e:
            duration = time.time() - start_time
            print(f"      ❌ Error in post activity workflow: {e}")

            self.test_results['workflows_tested'].append({
                'workflow': 'post_activity',
                'duration': duration,
                'status': 'error',
                'error': str(e)
            })

    async def _analyze_results(self):
        """Analyze performance and quality results."""
        print("\n📊 Phase 3: Analyzing results")

        # Analyze performance bottlenecks
        await self._identify_bottlenecks()

        # Analyze agent quality
        await self._assess_agent_quality()

        # Analyze database changes
        await self._analyze_database_changes()

    async def _identify_bottlenecks(self):
        """Identify performance bottlenecks."""
        print("   🔍 Identifying performance bottlenecks")

        metrics = self.test_results['performance_metrics']
        bottlenecks = []

        # Check setup duration
        if metrics.get('setup_duration', 0) > 2.0:
            bottlenecks.append({
                'area': 'setup',
                'duration': metrics['setup_duration'],
                'threshold': 2.0,
                'recommendation': 'Optimize user profile creation and database queries'
            })

        # Check onboarding duration
        if metrics.get('onboarding_duration', 0) > 30.0:
            bottlenecks.append({
                'area': 'onboarding',
                'duration': metrics['onboarding_duration'],
                'threshold': 30.0,
                'recommendation': 'Implement caching for profile completion checks and LLM responses'
            })

        # Check wheel generation duration
        if metrics.get('wheel_generation_duration', 0) > 60.0:
            bottlenecks.append({
                'area': 'wheel_generation',
                'duration': metrics['wheel_generation_duration'],
                'threshold': 60.0,
                'recommendation': 'Cache activity queries and implement activity pre-generation'
            })

        self.test_results['bottlenecks'] = bottlenecks

        if bottlenecks:
            print(f"      ⚠️  Found {len(bottlenecks)} performance bottlenecks")
            for bottleneck in bottlenecks:
                print(f"         - {bottleneck['area']}: {bottleneck['duration']:.2f}s (threshold: {bottleneck['threshold']}s)")
        else:
            print("      ✓ No significant performance bottlenecks detected")

    async def _assess_agent_quality(self):
        """Assess agent quality across workflows."""
        print("   🎭 Assessing agent quality")

        quality_scores = {}

        # Assess onboarding quality
        onboarding_workflow = next((w for w in self.test_results['workflows_tested'] if w['workflow'] == 'onboarding'), None)
        if onboarding_workflow:
            quality_scores['onboarding'] = {
                'classification_confidence': onboarding_workflow.get('confidence', 0),
                'completion_status': onboarding_workflow.get('status'),
                'profile_enrichment': self._calculate_profile_enrichment(),
                'overall_score': 0.0  # To be calculated
            }

        # Assess wheel generation quality
        wheel_workflow = next((w for w in self.test_results['workflows_tested'] if w['workflow'] == 'wheel_generation'), None)
        if wheel_workflow:
            quality_scores['wheel_generation'] = {
                'classification_confidence': wheel_workflow.get('confidence', 0),
                'completion_status': wheel_workflow.get('status'),
                'wheel_quality': await self._assess_wheel_quality(),
                'overall_score': 0.0  # To be calculated
            }

        # Calculate overall scores
        for workflow, scores in quality_scores.items():
            scores['overall_score'] = self._calculate_overall_quality_score(scores)

        self.test_results['agent_quality_scores'] = quality_scores

        # Print quality assessment
        for workflow, scores in quality_scores.items():
            print(f"      📊 {workflow.title()} Quality: {scores['overall_score']:.1%}")

    async def _analyze_database_changes(self):
        """Analyze database changes throughout the journey."""
        print("   🗄️  Analyzing database changes")

        initial = self.test_results['database_changes'].get('initial_profile', {})
        after_onboarding = self.test_results['database_changes'].get('after_onboarding', {})

        changes = {
            'profile_completion_change': after_onboarding.get('completion', 0) - initial.get('completion', 0),
            'new_records_created': after_onboarding.get('record_count', 0) - initial.get('record_count', 0),
            'enrichment_areas': []
        }

        # Identify enrichment areas
        if changes['profile_completion_change'] > 0:
            changes['enrichment_areas'].append('profile_completion')
        if changes['new_records_created'] > 0:
            changes['enrichment_areas'].append('preference_records')

        self.test_results['database_changes']['summary'] = changes

        print(f"      📈 Profile completion change: {changes['profile_completion_change']:.1%}")
        print(f"      📝 New records created: {changes['new_records_created']}")

    async def _generate_recommendations(self):
        """Generate recommendations for improvements."""
        print("\n💡 Phase 4: Generating recommendations")

        recommendations = self.test_results['recommendations']  # Already has post_activity implementation

        # Performance recommendations
        for bottleneck in self.test_results['bottlenecks']:
            recommendations.append({
                'type': 'performance',
                'priority': 'medium',
                'area': bottleneck['area'],
                'description': bottleneck['recommendation']
            })

        # Quality recommendations
        quality_scores = self.test_results['agent_quality_scores']
        for workflow, scores in quality_scores.items():
            if scores['overall_score'] < 0.8:
                recommendations.append({
                    'type': 'quality',
                    'priority': 'high',
                    'workflow': workflow,
                    'description': f'Improve {workflow} agent quality (current: {scores["overall_score"]:.1%})'
                })

        # ADHD-specific recommendations
        recommendations.append({
            'type': 'ux',
            'priority': 'high',
            'description': 'Implement ADHD-specific communication patterns and trust building',
            'areas': ['attention_management', 'trust_building', 'clear_instructions']
        })

        print(f"   📋 Generated {len(recommendations)} recommendations")
        for i, rec in enumerate(recommendations, 1):
            print(f"      {i}. [{rec['priority'].upper()}] {rec['description']}")

    async def _create_test_user(self) -> int:
        """Create a test user profile for the comprehensive test."""
        try:
            # Get or create a generic environment for Berlin
            berlin_env, created = await asyncio.to_thread(
                GenericEnvironment.objects.get_or_create,
                name="Berlin Student Apartment",
                defaults={
                    'code': 'ind_res_berlin_apt',  # Provide explicit code
                    'description': 'Small student apartment in Berlin',
                    'typical_privacy_level': 80,
                    'typical_space_size': 'small',
                    'primary_category': 'residential',
                    'is_indoor': True
                }
            )

            # Create Django user first
            User = get_user_model()
            django_user = await asyncio.to_thread(
                User.objects.create_user,
                username=f'test_user_{int(time.time())}',
                email='<EMAIL>'
            )

            # Create user profile
            user_profile = await asyncio.to_thread(
                UserProfile.objects.create,
                user=django_user,
                profile_name=f'ADHD Student {django_user.id}',
                is_real=False  # Test profile
            )

            # Create demographics
            demographics = await asyncio.to_thread(
                Demographics.objects.create,
                user_profile=user_profile,
                full_name='Anna Schmidt',
                age=21,
                gender='female',
                location='Berlin, Germany',
                language='German',
                occupation='Student'
            )

            # Create preferences as separate objects (replacing personal_prefs_json)
            from datetime import date, timedelta
            preferences_data = [
                ("ADHD Management", "Managing ADHD while studying", 80),
                ("Exam Preparation", "Preparing for upcoming exams", 90)
            ]

            for pref_name, description, strength in preferences_data:
                await asyncio.to_thread(
                    Preference.objects.create,
                    user_profile=user_profile,
                    pref_name=pref_name,
                    pref_description=description,
                    pref_strength=strength,
                    user_awareness=75,
                    effective_start=date.today(),
                    duration_estimate="6 months",
                    effective_end=date.today() + timedelta(days=180)
                )

            # Create user environment
            from datetime import date
            user_environment = await asyncio.to_thread(
                UserEnvironment.objects.create,
                user_profile=user_profile,
                environment_name='My Berlin Apartment',
                environment_description='Small student apartment in Berlin with basic amenities',
                generic_environment=berlin_env,
                effective_start=date.today(),
                is_current=True
            )

            return user_profile.id

        except Exception as e:
            print(f"Error creating test user: {str(e)}")
            raise

    async def _get_profile_state(self) -> Dict[str, Any]:
        """Get current profile state for analysis."""
        try:
            result = await execute_tool(
                tool_code="get_user_profile",
                tool_input={"input_data": {"user_profile_id": str(self.test_user_id)}},
                user_profile_id=str(self.test_user_id),
                session_id=f"test_session_{self.test_user_id}"
            )

            user_profile_data = result.get("user_profile", {})

            return {
                'completion': user_profile_data.get("profile_completion", 0.0),
                'record_count': user_profile_data.get("total_records", 0),
                'onboarding_stage': user_profile_data.get("onboarding_stage", "unknown"),
                'raw_data': user_profile_data
            }

        except Exception as e:
            print(f"Error getting profile state: {str(e)}")
            return {'completion': 0.0, 'record_count': 0, 'error': str(e)}

    async def _analyze_wheel_quality(self):
        """Analyze the quality of generated wheel."""
        print("      🔍 Analyzing wheel quality")

        # This would analyze the generated wheel
        # For now, we'll simulate the analysis
        wheel_quality = {
            'activities_count': 0,
            'domain_distribution': {},
            'adhd_appropriateness': 0.0,
            'personalization_score': 0.0
        }

        self.test_results['wheel_quality'] = wheel_quality
        print("      ⚠️  Wheel quality analysis - simulated (requires actual wheel)")

    async def _assess_wheel_quality(self) -> float:
        """Assess wheel quality score."""
        # This would assess actual wheel quality
        # For now, return a placeholder score
        return 0.75

    def _calculate_profile_enrichment(self) -> float:
        """Calculate profile enrichment score."""
        initial = self.test_results['database_changes'].get('initial_profile', {})
        after = self.test_results['database_changes'].get('after_onboarding', {})

        completion_change = after.get('completion', 0) - initial.get('completion', 0)
        return min(1.0, completion_change * 4)  # Scale to 0-1

    def _calculate_overall_quality_score(self, scores: Dict[str, Any]) -> float:
        """Calculate overall quality score for a workflow."""
        confidence = scores.get('classification_confidence', 0)
        status_score = 1.0 if scores.get('completion_status') == 'completed' else 0.0

        # Additional scoring based on workflow type
        additional_score = 0.0
        if 'profile_enrichment' in scores:
            additional_score = scores['profile_enrichment']
        elif 'wheel_quality' in scores:
            additional_score = scores['wheel_quality']

        return (confidence + status_score + additional_score) / 3.0

    def _calculate_total_duration(self) -> float:
        """Calculate total test duration."""
        start = datetime.fromisoformat(self.test_results['start_time'])
        end = datetime.fromisoformat(self.test_results['end_time'])
        return (end - start).total_seconds()

    def _print_summary(self):
        """Print test summary."""
        print("\n📊 COMPREHENSIVE AGENT QUALITY MISSION SUMMARY")
        print("=" * 60)

        # Overall status
        status = self.test_results['status']
        duration = self.test_results.get('total_duration', 0)
        print(f"Status: {status.upper()}")
        print(f"Total Duration: {duration:.2f}s")

        # Workflows tested
        workflows = self.test_results['workflows_tested']
        print(f"\nWorkflows Tested: {len(workflows)}")
        for workflow in workflows:
            status_icon = "✅" if workflow['status'] == 'completed' else "⚠️" if workflow['status'] == 'simulated' else "❌"
            print(f"  {status_icon} {workflow['workflow']}: {workflow['status']}")

        # Performance metrics
        print(f"\nPerformance Metrics:")
        metrics = self.test_results['performance_metrics']
        for metric, value in metrics.items():
            print(f"  - {metric}: {value:.2f}s")

        # Bottlenecks
        bottlenecks = self.test_results['bottlenecks']
        if bottlenecks:
            print(f"\nBottlenecks Found: {len(bottlenecks)}")
            for bottleneck in bottlenecks:
                print(f"  ⚠️  {bottleneck['area']}: {bottleneck['duration']:.2f}s")

        # Quality scores
        quality_scores = self.test_results['agent_quality_scores']
        if quality_scores:
            print(f"\nAgent Quality Scores:")
            for workflow, scores in quality_scores.items():
                print(f"  - {workflow}: {scores['overall_score']:.1%}")

        # Recommendations
        recommendations = self.test_results['recommendations']
        print(f"\nRecommendations: {len(recommendations)}")
        high_priority = [r for r in recommendations if r['priority'] == 'high']
        print(f"  - High Priority: {len(high_priority)}")

        # Database changes
        db_changes = self.test_results['database_changes'].get('summary', {})
        if db_changes:
            print(f"\nDatabase Changes:")
            print(f"  - Profile completion change: {db_changes.get('profile_completion_change', 0):.1%}")
            print(f"  - New records created: {db_changes.get('new_records_created', 0)}")

        print("\n" + "=" * 60)


async def main():
    """Main function to run the comprehensive agent quality test."""
    tester = ComprehensiveAgentQualityTester()
    results = await tester.run_comprehensive_test()

    # Save results to file
    results_file = f"/usr/src/app/real_condition_tests/results/comprehensive_agent_quality_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    os.makedirs(os.path.dirname(results_file), exist_ok=True)

    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)

    print(f"\n💾 Results saved to: {results_file}")

    return results


if __name__ == "__main__":
    asyncio.run(main())
