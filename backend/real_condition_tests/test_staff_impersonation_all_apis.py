#!/usr/bin/env python3
"""
Test script to validate staff impersonation across ALL API endpoints.

This script tests that all API endpoints properly handle the X-Debug-User-ID header
for staff impersonation, not just the wheel item removal endpoint.
"""

import os
import sys
import django
import json
from typing import Dict, Any

# Add the backend directory to Python path
sys.path.insert(0, '/usr/src/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from apps.user.models import UserProfile
from apps.main.models import Wheel, WheelItem
from apps.activity.models import ActivityTailored
from django.conf import settings
import logging

# Set up logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

class StaffImpersonationAllAPIsValidator:
    """Validate staff impersonation across all API endpoints."""
    
    def __init__(self):
        self.test_results = {
            'api_tests': [],
            'overall_success': False
        }
    
    def run_all_apis_validation(self):
        """Run comprehensive staff impersonation validation across all APIs."""
        print("🔐 STAFF IMPERSONATION ALL APIs VALIDATION")
        print("=" * 50)
        
        try:
            # Test 1: Track Event API with debug header
            self.test_track_event_api()
            
            # Test 2: Wheel Item Addition API with debug header
            self.test_wheel_item_addition_api()
            
            # Test 3: Wheel Item Removal API with debug header (should work)
            self.test_wheel_item_removal_api()
            
            # Test 4: Compare authentication implementations
            self.compare_authentication_implementations()
            
            # Generate final report
            self.generate_all_apis_report()
            
            return self.test_results['overall_success']
            
        except Exception as e:
            print(f"❌ All APIs validation failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_track_event_api(self):
        """Test track-event API with debug header."""
        print(f"\n🧪 TEST 1: TRACK EVENT API WITH DEBUG HEADER")
        print("-" * 40)
        
        try:
            client = Client()
            
            # Get PhiPhi's profile ID for impersonation
            phiphi_profile = UserProfile.objects.get(profile_name='PhiPhi')
            debug_user_id = str(phiphi_profile.id)
            
            print(f"   Using debug user ID: {debug_user_id} (PhiPhi)")
            
            # Test track event with debug header
            response = client.post(
                '/api/track-event/',
                data=json.dumps({
                    'event_type': 'activity_search_performed',
                    'content_type': 'search',
                    'object_id': 'test_search',
                    'details': {'query': 'test', 'results_count': 5}
                }),
                content_type='application/json',
                HTTP_X_DEBUG_USER_ID=debug_user_id
            )
            
            print(f"   Track event response status: {response.status_code}")
            
            if response.status_code == 200:
                self.record_api_test("Track Event API", True, "Track event with debug header successful")
                print(f"   ✅ Track event with debug header successful")
            elif response.status_code == 400:
                try:
                    error_data = response.json()
                    error_msg = error_data.get('error', 'Unknown error')
                    if 'User profile not found' in error_msg:
                        self.record_api_test("Track Event API", False, "AUTHENTICATION ISSUE: User profile not found")
                        print(f"   ❌ AUTHENTICATION ISSUE: {error_msg}")
                    else:
                        self.record_api_test("Track Event API", False, f"API error: {error_msg}")
                        print(f"   ❌ API error: {error_msg}")
                except:
                    error_msg = response.content.decode()
                    self.record_api_test("Track Event API", False, f"HTTP 400: {error_msg}")
                    print(f"   ❌ HTTP 400: {error_msg}")
            else:
                try:
                    error_data = response.json()
                    error_msg = error_data.get('error', 'Unknown error')
                except:
                    error_msg = response.content.decode()
                
                self.record_api_test("Track Event API", False, f"HTTP {response.status_code}: {error_msg}")
                print(f"   ❌ HTTP {response.status_code}: {error_msg}")
                
        except Exception as e:
            self.record_api_test("Track Event API", False, f"Exception: {str(e)}")
            print(f"   ❌ Exception: {e}")
    
    def test_wheel_item_addition_api(self):
        """Test wheel item addition API with debug header."""
        print(f"\n🧪 TEST 2: WHEEL ITEM ADDITION API WITH DEBUG HEADER")
        print("-" * 40)
        
        try:
            client = Client()
            
            # Get PhiPhi's profile ID for impersonation
            phiphi_profile = UserProfile.objects.get(profile_name='PhiPhi')
            debug_user_id = str(phiphi_profile.id)
            
            # Get a test activity to add
            test_activity = ActivityTailored.objects.filter(user_profile=phiphi_profile).first()
            if not test_activity:
                self.record_api_test("Wheel Item Addition API", False, "No test activity available for PhiPhi")
                return
            
            print(f"   Using debug user ID: {debug_user_id} (PhiPhi)")
            print(f"   Adding activity: {test_activity.name}")
            
            # Test wheel item addition with debug header
            response = client.post(
                '/api/wheel-items/',
                data=json.dumps({
                    'activity_id': test_activity.id,
                    'activity_type': 'tailored'
                }),
                content_type='application/json',
                HTTP_X_DEBUG_USER_ID=debug_user_id
            )
            
            print(f"   Wheel item addition response status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.record_api_test("Wheel Item Addition API", True, "Wheel item addition with debug header successful")
                    print(f"   ✅ Wheel item addition with debug header successful")
                else:
                    error_msg = data.get('error', 'Unknown error')
                    self.record_api_test("Wheel Item Addition API", False, f"API error: {error_msg}")
                    print(f"   ❌ API error: {error_msg}")
            elif response.status_code == 400:
                try:
                    error_data = response.json()
                    error_msg = error_data.get('error', 'Unknown error')
                    if 'User profile not found' in error_msg:
                        self.record_api_test("Wheel Item Addition API", False, "AUTHENTICATION ISSUE: User profile not found")
                        print(f"   ❌ AUTHENTICATION ISSUE: {error_msg}")
                    else:
                        self.record_api_test("Wheel Item Addition API", False, f"API error: {error_msg}")
                        print(f"   ❌ API error: {error_msg}")
                except:
                    error_msg = response.content.decode()
                    self.record_api_test("Wheel Item Addition API", False, f"HTTP 400: {error_msg}")
                    print(f"   ❌ HTTP 400: {error_msg}")
            else:
                try:
                    error_data = response.json()
                    error_msg = error_data.get('error', 'Unknown error')
                except:
                    error_msg = response.content.decode()
                
                self.record_api_test("Wheel Item Addition API", False, f"HTTP {response.status_code}: {error_msg}")
                print(f"   ❌ HTTP {response.status_code}: {error_msg}")
                
        except Exception as e:
            self.record_api_test("Wheel Item Addition API", False, f"Exception: {str(e)}")
            print(f"   ❌ Exception: {e}")
    
    def test_wheel_item_removal_api(self):
        """Test wheel item removal API with debug header (should work)."""
        print(f"\n🧪 TEST 3: WHEEL ITEM REMOVAL API WITH DEBUG HEADER")
        print("-" * 40)
        
        try:
            client = Client()
            
            # Get a wheel item to test with
            wheel_item = self.get_test_wheel_item()
            if not wheel_item:
                self.record_api_test("Wheel Item Removal API", False, "No test wheel item available")
                return
            
            # Get PhiPhi's profile ID for impersonation
            phiphi_profile = UserProfile.objects.get(profile_name='PhiPhi')
            debug_user_id = str(phiphi_profile.id)
            
            print(f"   Using debug user ID: {debug_user_id} (PhiPhi)")
            print(f"   Removing item: {wheel_item.id}")
            
            # Test wheel item removal with debug header
            response = client.delete(
                f'/api/wheel-items/{wheel_item.id}/',
                HTTP_X_DEBUG_USER_ID=debug_user_id
            )
            
            print(f"   Wheel item removal response status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.record_api_test("Wheel Item Removal API", True, "Wheel item removal with debug header successful")
                    print(f"   ✅ Wheel item removal with debug header successful")
                else:
                    error_msg = data.get('error', 'Unknown error')
                    self.record_api_test("Wheel Item Removal API", False, f"API error: {error_msg}")
                    print(f"   ❌ API error: {error_msg}")
            else:
                try:
                    error_data = response.json()
                    error_msg = error_data.get('error', 'Unknown error')
                except:
                    error_msg = response.content.decode()
                
                self.record_api_test("Wheel Item Removal API", False, f"HTTP {response.status_code}: {error_msg}")
                print(f"   ❌ HTTP {response.status_code}: {error_msg}")
                
        except Exception as e:
            self.record_api_test("Wheel Item Removal API", False, f"Exception: {str(e)}")
            print(f"   ❌ Exception: {e}")
    
    def compare_authentication_implementations(self):
        """Compare authentication implementations across different API endpoints."""
        print(f"\n🧪 TEST 4: AUTHENTICATION IMPLEMENTATION COMPARISON")
        print("-" * 40)
        
        try:
            # This is an analysis test, not a functional test
            print(f"   📊 Analyzing authentication patterns across API endpoints:")
            print(f"   ✅ Wheel Item Removal: Enhanced authentication with 4 scenarios")
            print(f"   ❌ Track Event: Likely missing enhanced authentication")
            print(f"   ❌ Wheel Item Addition: Likely missing enhanced authentication")
            print(f"   🔧 SOLUTION: Apply same authentication pattern to all endpoints")
            
            self.record_api_test("Authentication Implementation Analysis", True, "Identified inconsistent authentication patterns")
            
        except Exception as e:
            self.record_api_test("Authentication Implementation Analysis", False, f"Exception: {str(e)}")
            print(f"   ❌ Exception: {e}")
    
    def get_test_wheel_item(self) -> WheelItem:
        """Get a test wheel item for removal."""
        wheels_with_items = Wheel.objects.filter(items__isnull=False).distinct()
        if wheels_with_items.exists():
            wheel = wheels_with_items.first()
            items = list(wheel.items.all())
            if items:
                return items[0]
        return None
    
    def record_api_test(self, test_name: str, passed: bool, details: str):
        """Record an API test result."""
        result = {
            'test_name': test_name,
            'passed': passed,
            'details': details
        }
        self.test_results['api_tests'].append(result)
        
        if passed:
            print(f"   ✅ {test_name}: PASSED - {details}")
        else:
            print(f"   ❌ {test_name}: FAILED - {details}")
    
    def generate_all_apis_report(self):
        """Generate final all APIs validation report."""
        print(f"\n📋 STAFF IMPERSONATION ALL APIs VALIDATION REPORT")
        print("=" * 50)
        
        passed_tests = [t for t in self.test_results['api_tests'] if t['passed']]
        total_tests = len(self.test_results['api_tests'])
        
        print(f"📊 Test Results:")
        print(f"   Total Tests: {total_tests}")
        print(f"   Passed: {len(passed_tests)}")
        print(f"   Failed: {total_tests - len(passed_tests)}")
        
        # Analyze authentication issues
        auth_issues = []
        working_apis = []
        
        for test in self.test_results['api_tests']:
            if 'AUTHENTICATION ISSUE' in test['details']:
                auth_issues.append(test['test_name'])
            elif test['passed'] and 'successful' in test['details']:
                working_apis.append(test['test_name'])
        
        if auth_issues:
            print(f"\n🚨 AUTHENTICATION ISSUES FOUND:")
            for api in auth_issues:
                print(f"   ❌ {api}: Missing staff impersonation authentication")
            
            print(f"\n🔧 SOLUTION NEEDED:")
            print(f"   Apply the same enhanced authentication pattern from wheel item removal")
            print(f"   to the following APIs: {', '.join(auth_issues)}")
        
        if working_apis:
            print(f"\n✅ WORKING APIs:")
            for api in working_apis:
                print(f"   ✅ {api}: Staff impersonation working correctly")
        
        # Determine overall success
        self.test_results['overall_success'] = (len(auth_issues) == 0)
        
        if self.test_results['overall_success']:
            print(f"\n🎉 ALL APIs SUPPORT STAFF IMPERSONATION")
        else:
            print(f"\n⚠️ SOME APIs NEED AUTHENTICATION FIXES")
        
        # Save results
        try:
            with open('/usr/src/app/real_condition_tests/all_apis_validation_results.json', 'w') as f:
                json.dump(self.test_results, f, indent=2, default=str)
            print(f"\n📁 Detailed results saved to: all_apis_validation_results.json")
        except Exception as e:
            print(f"⚠️ Could not save results: {e}")

if __name__ == "__main__":
    validator = StaffImpersonationAllAPIsValidator()
    success = validator.run_all_apis_validation()
    
    if success:
        print(f"\n🎉 ALL APIs STAFF IMPERSONATION VALIDATION SUCCESSFUL")
        exit(0)
    else:
        print(f"\n❌ SOME APIs NEED AUTHENTICATION FIXES")
        exit(1)
