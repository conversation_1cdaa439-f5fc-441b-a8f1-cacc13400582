#!/usr/bin/env python3
"""
REAL User Experience Test - Wheel Disappearance Bug

This test reproduces the ACTUAL user experience by:
1. Generating a wheel via WebSocket (like the user does)
2. Checking what the frontend actually receives
3. Testing wheel item removal via the frontend flow
4. Verifying the wheel doesn't disappear

This test was created because the previous test was fundamentally flawed -
it only tested backend API endpoints, not the real user experience.
"""

import os
import sys
import django
import asyncio
import websockets
import json
import requests
from datetime import datetime

# Setup Django
sys.path.append('/usr/src/app')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()


async def test_real_user_wheel_generation():
    """Test the actual user wheel generation flow via WebSocket"""
    print("🔍 TESTING REAL USER WHEEL GENERATION FLOW")
    print("=" * 50)
    
    uri = "ws://localhost:8000/ws/game/"
    
    try:
        async with websockets.connect(uri) as websocket:
            print("✅ Connected to WebSocket")
            
            # Send wheel generation request (exactly like the frontend does)
            request_message = {
                "type": "chat_message",
                "content": {
                    "message": "Generate a personalized activity wheel (Energy: 100%, Time: 10 minutes)",
                    "user_profile_id": "1",
                    "timestamp": datetime.now().isoformat(),
                    "energy_level": 100,
                    "time_available_minutes": 10,
                    "metadata": {
                        "forced_wheel_generation": True,
                        "energy_level": 100,
                        "time_available_minutes": 10
                    }
                }
            }
            
            await websocket.send(json.dumps(request_message))
            print("📤 Sent wheel generation request")
            
            # Listen for wheel_data message
            wheel_data = None
            message_count = 0
            
            while not wheel_data and message_count < 50:
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=30.0)
                    message_count += 1
                    
                    try:
                        data = json.loads(message)
                        message_type = data.get('type', 'unknown')
                        
                        if message_type == 'wheel_data':
                            wheel_data = data
                            print(f"🎡 Received wheel_data message")
                            break
                            
                    except json.JSONDecodeError:
                        continue
                        
                except asyncio.TimeoutError:
                    print("⏰ Timeout waiting for wheel_data")
                    break
            
            if not wheel_data:
                print("❌ No wheel_data received")
                return None
            
            return wheel_data
            
    except Exception as e:
        print(f"❌ WebSocket error: {e}")
        return None


def analyze_wheel_data_issues(wheel_data):
    """Analyze the wheel data for the issues seen in console logs"""
    print(f"\n🔍 ANALYZING WHEEL DATA FOR REAL ISSUES")
    print("=" * 50)
    
    if not wheel_data or 'wheel' not in wheel_data:
        print("❌ No valid wheel data to analyze")
        return False
    
    wheel = wheel_data['wheel']
    items = wheel.get('items', [])
    
    print(f"📊 Wheel Analysis:")
    print(f"  - Wheel ID: {wheel.get('id', 'MISSING')}")
    print(f"  - Items count: {len(items)}")
    
    # Check for mixed ID patterns (the real issue from console logs)
    correct_ids = 0
    temporary_ids = 0
    
    print(f"\n🔍 Item ID Analysis:")
    for i, item in enumerate(items, 1):
        item_id = item.get('id', 'MISSING')
        
        if item_id.startswith('item_'):
            print(f"  ✅ Item {i}: {item_id} (correct database ID)")
            correct_ids += 1
        elif item_id.startswith('wheel-item-'):
            print(f"  ❌ Item {i}: {item_id} (temporary ID - BUG!)")
            temporary_ids += 1
        else:
            print(f"  ⚠️ Item {i}: {item_id} (unknown format)")
    
    print(f"\n📊 ID Consistency Analysis:")
    print(f"  - Correct database IDs: {correct_ids}/{len(items)}")
    print(f"  - Temporary IDs: {temporary_ids}/{len(items)}")
    
    # This is the REAL test - if we have temporary IDs, the bug still exists
    if temporary_ids > 0:
        print(f"❌ CRITICAL BUG DETECTED: {temporary_ids} items have temporary IDs")
        print(f"   This means the frontend will say 'wheel data unchanged' for these items")
        print(f"   and wheel removal will fail for these items")
        return False
    else:
        print(f"✅ All items have correct database IDs")
        return True


def test_wheel_item_removal_real_flow(wheel_data):
    """Test wheel item removal using the real frontend flow"""
    print(f"\n🔍 TESTING REAL WHEEL ITEM REMOVAL FLOW")
    print("=" * 50)
    
    if not wheel_data or 'wheel' not in wheel_data:
        print("❌ No wheel data to test removal")
        return False
    
    items = wheel_data['wheel'].get('items', [])
    
    # Find an item with correct database ID to test removal
    test_item = None
    for item in items:
        if item.get('id', '').startswith('item_'):
            test_item = item
            break
    
    if not test_item:
        print("⚠️ No items with correct database IDs to test removal")
        return True  # Not a failure, just no testable items
    
    item_id = test_item['id']
    item_name = test_item.get('name', 'Unknown')
    
    print(f"🗑️ Testing removal of: {item_name}")
    print(f"   Item ID: {item_id}")
    
    try:
        # Test the actual API endpoint the frontend uses
        response = requests.delete(
            f'http://localhost:8000/api/wheel-items/{item_id}/',
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"📡 API Response: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            
            if response_data.get('success'):
                print("✅ Item removal API successful")
                
                # Check if wheel still exists (the real test)
                # Use thread-safe database access to avoid async context issues
                import threading
                import queue

                def check_wheel_exists():
                    """Check wheel existence in a separate thread to avoid async context issues"""
                    try:
                        from apps.main.models import Wheel, WheelItem
                        from apps.user.models import UserProfile
                        from apps.main.services.wheel_service import WheelService

                        user_profile = UserProfile.objects.get(id=1)
                        current_wheel = WheelService.get_current_wheel(user_profile)

                        if current_wheel:
                            remaining_items = WheelItem.objects.filter(wheel=current_wheel).count()
                            return True, remaining_items
                        else:
                            return False, 0
                    except Exception as e:
                        print(f"🚨 ERROR checking wheel existence: {e}")
                        return False, 0

                # Execute in thread to avoid async context issues
                result_queue = queue.Queue()

                def thread_worker():
                    result = check_wheel_exists()
                    result_queue.put(result)

                thread = threading.Thread(target=thread_worker)
                thread.start()
                thread.join(timeout=10)  # 10 second timeout

                if not result_queue.empty():
                    wheel_exists, remaining_items = result_queue.get()
                    if wheel_exists:
                        print(f"✅ Wheel still exists with {remaining_items} items")
                        return True
                    else:
                        print("❌ WHEEL DISAPPEARED - BUG CONFIRMED!")
                        return False
                else:
                    print("⚠️ Timeout checking wheel existence")
                    return False
            else:
                print(f"❌ API returned success=false: {response_data}")
                return False
        else:
            print(f"❌ API call failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Exception during removal test: {e}")
        return False


async def main():
    """Run the real user experience test"""
    print("🎯 REAL USER EXPERIENCE WHEEL BUG TEST")
    print("=" * 60)
    print("This test reproduces the ACTUAL user experience, not just backend APIs")
    print("=" * 60)
    
    # Step 1: Generate wheel via WebSocket (real user flow)
    wheel_data = await test_real_user_wheel_generation()
    
    if not wheel_data:
        print("\n❌ FAILED: Could not generate wheel via WebSocket")
        return False
    
    # Step 2: Analyze wheel data for the real issues seen in console logs
    id_consistency_ok = analyze_wheel_data_issues(wheel_data)
    
    # Step 3: Test wheel item removal (real frontend flow)
    removal_ok = test_wheel_item_removal_real_flow(wheel_data)
    
    # Final assessment
    print(f"\n{'='*60}")
    print("🏆 REAL USER EXPERIENCE TEST RESULTS:")
    print(f"  - Wheel Generation: {'✅ PASS' if wheel_data else '❌ FAIL'}")
    print(f"  - ID Consistency: {'✅ PASS' if id_consistency_ok else '❌ FAIL'}")
    print(f"  - Item Removal: {'✅ PASS' if removal_ok else '❌ FAIL'}")
    
    overall_success = wheel_data and id_consistency_ok and removal_ok
    
    if overall_success:
        print(f"\n🎉 REAL USER EXPERIENCE: SUCCESS!")
        print("✅ The wheel disappearance bug is actually fixed")
    else:
        print(f"\n❌ REAL USER EXPERIENCE: FAILURE!")
        print("🚨 The wheel disappearance bug still exists")
        print("🔧 The previous test was wrong - it didn't test the real user flow")
    
    print(f"\n🕐 Test completed at: {datetime.now().isoformat()}")
    return overall_success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
