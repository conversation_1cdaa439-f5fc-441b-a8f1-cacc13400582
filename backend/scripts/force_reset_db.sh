#!/bin/bash

# This script will completely reset the database by removing the Docker volume.
# It will then restart the containers and run the migrations.

set -e

echo "Stopping Docker containers..."
docker-compose -f backend/docker-compose.yml down

echo "Removing postgres_data volume..."
docker volume rm backend_postgres_data

echo "Starting Docker containers in detached mode..."
docker-compose -f backend/docker-compose.yml up -d

echo "Waiting for the database to be ready..."
sleep 10

echo "Running migrations..."
docker-compose -f backend/docker-compose.yml exec web python manage.py migrate

echo "Database reset complete."
