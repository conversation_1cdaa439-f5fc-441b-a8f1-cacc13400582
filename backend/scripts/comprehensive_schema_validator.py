#!/usr/bin/env python3
"""
Comprehensive OpenAPI Schema Validator

This script provides detailed validation of the user_profile_import_schema.json file
for OpenAPI 3.0.3 compliance and best practices.
"""

import json
import sys
from pathlib import Path
from typing import Dict, List, Any, Set, Optional
import re


class ComprehensiveSchemaValidator:
    """Advanced validator for OpenAPI 3.0.3 schemas"""
    
    def __init__(self):
        self.errors: List[str] = []
        self.warnings: List[str] = []
        self.info: List[str] = []
        self.visited_refs: Set[str] = set()
        self.schema_definitions: Dict[str, Any] = {}
    
    def validate_schema_file(self, file_path: str) -> bool:
        """Validate an OpenAPI schema file comprehensively"""
        try:
            with open(file_path, 'r') as f:
                schema = json.load(f)
            
            print(f"🔍 Comprehensive validation of: {file_path}")
            print("=" * 80)
            
            # Store schema definitions for reference validation
            if 'components' in schema and 'schemas' in schema['components']:
                self.schema_definitions = schema['components']['schemas']
            
            # Validate different aspects
            self._validate_openapi_structure(schema)
            self._validate_info_section(schema)
            self._validate_paths_section(schema)
            self._validate_components_section(schema)
            self._validate_schema_consistency(schema)
            self._validate_examples_and_documentation(schema)
            
            # Report results
            self._report_comprehensive_results()
            
            return len(self.errors) == 0
            
        except json.JSONDecodeError as e:
            print(f"❌ CRITICAL: Invalid JSON in {file_path}: {e}")
            return False
        except FileNotFoundError:
            print(f"❌ CRITICAL: File not found: {file_path}")
            return False
        except Exception as e:
            print(f"❌ CRITICAL: Unexpected error validating {file_path}: {e}")
            return False
    
    def _validate_openapi_structure(self, schema: Dict[str, Any]):
        """Validate the root OpenAPI structure"""
        required_fields = ['openapi', 'info', 'paths']
        
        for field in required_fields:
            if field not in schema:
                self.errors.append(f"Missing required root field: {field}")
        
        # Check OpenAPI version
        if 'openapi' in schema:
            version = schema['openapi']
            if not version.startswith('3.0'):
                self.warnings.append(f"OpenAPI version {version} may not be fully supported")
            elif version == '3.0.3':
                self.info.append(f"✅ Using recommended OpenAPI version: {version}")
    
    def _validate_info_section(self, schema: Dict[str, Any]):
        """Validate the info section"""
        if 'info' not in schema:
            return
        
        info = schema['info']
        required_info_fields = ['title', 'version']
        
        for field in required_info_fields:
            if field not in info:
                self.errors.append(f"Missing required info field: {field}")
        
        # Check for recommended fields
        recommended_fields = ['description', 'contact', 'license']
        for field in recommended_fields:
            if field not in info:
                self.info.append(f"Consider adding info.{field} for better documentation")
    
    def _validate_paths_section(self, schema: Dict[str, Any]):
        """Validate the paths section"""
        if 'paths' not in schema:
            return
        
        paths = schema['paths']
        
        if not paths:
            self.warnings.append("Paths section is empty")
            return
        
        for path, path_obj in paths.items():
            if not isinstance(path_obj, dict):
                self.errors.append(f"Path {path} must be an object")
                continue
            
            # Validate HTTP methods
            valid_methods = ['get', 'post', 'put', 'patch', 'delete', 'head', 'options', 'trace']
            for method, operation in path_obj.items():
                if method.lower() in valid_methods:
                    self._validate_operation(operation, f"{path}.{method}")
                elif method not in ['parameters', 'summary', 'description']:
                    self.warnings.append(f"Unknown field in path {path}: {method}")
    
    def _validate_operation(self, operation: Dict[str, Any], path: str):
        """Validate an operation object"""
        if not isinstance(operation, dict):
            self.errors.append(f"{path}: Operation must be an object")
            return
        
        # Check for required/recommended fields
        if 'responses' not in operation:
            self.errors.append(f"{path}: Missing required 'responses' field")
        
        if 'summary' not in operation and 'description' not in operation:
            self.warnings.append(f"{path}: Consider adding summary or description")
        
        # Validate responses
        if 'responses' in operation:
            self._validate_responses(operation['responses'], f"{path}.responses")
        
        # Validate request body
        if 'requestBody' in operation:
            self._validate_request_body(operation['requestBody'], f"{path}.requestBody")
    
    def _validate_responses(self, responses: Dict[str, Any], path: str):
        """Validate responses object"""
        if not responses:
            self.errors.append(f"{path}: Responses cannot be empty")
            return
        
        # Check for default response or specific status codes
        has_success_response = any(
            code.startswith('2') or code == 'default' 
            for code in responses.keys()
        )
        
        if not has_success_response:
            self.warnings.append(f"{path}: Consider adding a success response (2xx)")
    
    def _validate_request_body(self, request_body: Dict[str, Any], path: str):
        """Validate request body object"""
        if 'content' not in request_body:
            self.errors.append(f"{path}: Missing required 'content' field")
    
    def _validate_components_section(self, schema: Dict[str, Any]):
        """Validate the components section"""
        if 'components' not in schema:
            self.info.append("No components section found")
            return
        
        components = schema['components']
        
        if 'schemas' in components:
            self._validate_schema_definitions(components['schemas'])
        
        if 'securitySchemes' in components:
            self._validate_security_schemes(components['securitySchemes'])
    
    def _validate_schema_definitions(self, schemas: Dict[str, Any]):
        """Validate all schema definitions"""
        for schema_name, schema_def in schemas.items():
            self._validate_single_schema(schema_def, f"#/components/schemas/{schema_name}")
    
    def _validate_single_schema(self, schema_def: Dict[str, Any], path: str):
        """Validate a single schema definition with comprehensive checks"""
        if not isinstance(schema_def, dict):
            self.errors.append(f"{path}: Schema definition must be an object")
            return
        
        # Check for $ref
        if '$ref' in schema_def:
            self._validate_reference(schema_def['$ref'], path)
            return
        
        # Validate type specification
        self._validate_schema_type(schema_def, path)
        
        # Validate type-specific requirements
        schema_type = schema_def.get('type')
        
        if schema_type == 'object':
            self._validate_object_schema_comprehensive(schema_def, path)
        elif schema_type == 'array':
            self._validate_array_schema_comprehensive(schema_def, path)
        elif schema_type == 'string':
            self._validate_string_schema_comprehensive(schema_def, path)
        elif schema_type in ['integer', 'number']:
            self._validate_numeric_schema_comprehensive(schema_def, path)
        
        # Recursively validate nested schemas
        self._validate_nested_schemas(schema_def, path)
    
    def _validate_schema_type(self, schema_def: Dict[str, Any], path: str):
        """Validate type specification with detailed analysis"""
        if 'type' not in schema_def:
            # Analyze what type it should be
            if 'properties' in schema_def:
                self.errors.append(f"{path}: Schema with 'properties' must specify 'type': 'object'")
            elif 'items' in schema_def:
                self.errors.append(f"{path}: Schema with 'items' must specify 'type': 'array'")
            elif 'enum' in schema_def:
                self.warnings.append(f"{path}: Schema with 'enum' should specify 'type'")
            elif any(key in schema_def for key in ['minimum', 'maximum', 'multipleOf']):
                self.warnings.append(f"{path}: Numeric constraints suggest missing 'type': 'number' or 'integer'")
            elif any(key in schema_def for key in ['minLength', 'maxLength', 'pattern', 'format']):
                self.warnings.append(f"{path}: String constraints suggest missing 'type': 'string'")
            else:
                self.warnings.append(f"{path}: Schema should specify 'type'")
        else:
            # Validate type value
            valid_types = ['null', 'boolean', 'object', 'array', 'number', 'string', 'integer']
            schema_type = schema_def['type']
            if schema_type not in valid_types:
                self.errors.append(f"{path}: Invalid type '{schema_type}'. Must be one of: {', '.join(valid_types)}")
    
    def _validate_object_schema_comprehensive(self, schema_def: Dict[str, Any], path: str):
        """Comprehensive validation of object-type schema"""
        # Check for properties or additionalProperties
        if 'properties' not in schema_def and 'additionalProperties' not in schema_def:
            self.warnings.append(f"{path}: Object schema should have 'properties' or 'additionalProperties'")
        
        # Validate required fields exist in properties
        if 'required' in schema_def and 'properties' in schema_def:
            required_fields = schema_def['required']
            properties = schema_def['properties']
            
            for field in required_fields:
                if field not in properties:
                    self.errors.append(f"{path}: Required field '{field}' not found in properties")
        
        # Check for empty properties
        if 'properties' in schema_def and not schema_def['properties']:
            self.warnings.append(f"{path}: Empty properties object")
        
        # Validate property naming conventions
        if 'properties' in schema_def:
            for prop_name in schema_def['properties'].keys():
                if not re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', prop_name):
                    self.warnings.append(f"{path}: Property name '{prop_name}' doesn't follow naming conventions")
    
    def _validate_array_schema_comprehensive(self, schema_def: Dict[str, Any], path: str):
        """Comprehensive validation of array-type schema"""
        if 'items' not in schema_def:
            self.errors.append(f"{path}: Array schema must have 'items' property")
        
        # Check for array constraints
        if 'minItems' in schema_def and 'maxItems' in schema_def:
            min_items = schema_def['minItems']
            max_items = schema_def['maxItems']
            if min_items > max_items:
                self.errors.append(f"{path}: minItems ({min_items}) cannot be greater than maxItems ({max_items})")
    
    def _validate_string_schema_comprehensive(self, schema_def: Dict[str, Any], path: str):
        """Comprehensive validation of string-type schema"""
        # Check for conflicting constraints
        if 'minLength' in schema_def and 'maxLength' in schema_def:
            min_len = schema_def['minLength']
            max_len = schema_def['maxLength']
            if min_len > max_len:
                self.errors.append(f"{path}: minLength ({min_len}) cannot be greater than maxLength ({max_len})")
        
        # Validate format
        if 'format' in schema_def:
            valid_formats = [
                'date', 'date-time', 'password', 'byte', 'binary', 'email', 'uuid', 
                'uri', 'uri-reference', 'hostname', 'ipv4', 'ipv6'
            ]
            format_value = schema_def['format']
            if format_value not in valid_formats:
                self.warnings.append(f"{path}: Unknown string format '{format_value}'")
        
        # Validate pattern
        if 'pattern' in schema_def:
            try:
                re.compile(schema_def['pattern'])
            except re.error as e:
                self.errors.append(f"{path}: Invalid regex pattern: {e}")
    
    def _validate_numeric_schema_comprehensive(self, schema_def: Dict[str, Any], path: str):
        """Comprehensive validation of numeric-type schema"""
        # Check for conflicting constraints
        if 'minimum' in schema_def and 'maximum' in schema_def:
            minimum = schema_def['minimum']
            maximum = schema_def['maximum']
            if minimum > maximum:
                self.errors.append(f"{path}: minimum ({minimum}) cannot be greater than maximum ({maximum})")
        
        # Check exclusive bounds
        if 'exclusiveMinimum' in schema_def and 'exclusiveMaximum' in schema_def:
            exc_min = schema_def['exclusiveMinimum']
            exc_max = schema_def['exclusiveMaximum']
            if exc_min >= exc_max:
                self.errors.append(f"{path}: exclusiveMinimum ({exc_min}) must be less than exclusiveMaximum ({exc_max})")
    
    def _validate_nested_schemas(self, schema_def: Dict[str, Any], path: str):
        """Validate nested schema definitions"""
        # Validate properties
        if 'properties' in schema_def:
            for prop_name, prop_def in schema_def['properties'].items():
                self._validate_single_schema(prop_def, f"{path}/properties/{prop_name}")
        
        # Validate items
        if 'items' in schema_def:
            self._validate_single_schema(schema_def['items'], f"{path}/items")
        
        # Validate additionalProperties
        if 'additionalProperties' in schema_def and isinstance(schema_def['additionalProperties'], dict):
            self._validate_single_schema(schema_def['additionalProperties'], f"{path}/additionalProperties")
        
        # Validate allOf, anyOf, oneOf
        for keyword in ['allOf', 'anyOf', 'oneOf']:
            if keyword in schema_def:
                if not isinstance(schema_def[keyword], list):
                    self.errors.append(f"{path}: {keyword} must be an array")
                else:
                    for i, sub_schema in enumerate(schema_def[keyword]):
                        self._validate_single_schema(sub_schema, f"{path}/{keyword}[{i}]")
    
    def _validate_reference(self, ref: str, path: str):
        """Validate a $ref reference"""
        if ref in self.visited_refs:
            return  # Avoid infinite recursion
        
        self.visited_refs.add(ref)
        
        # Check reference format
        if not ref.startswith('#/'):
            self.warnings.append(f"{path}: External references not validated: {ref}")
            return
        
        # Parse internal reference
        ref_parts = ref[2:].split('/')  # Remove '#/' prefix
        
        if len(ref_parts) >= 3 and ref_parts[0] == 'components' and ref_parts[1] == 'schemas':
            schema_name = ref_parts[2]
            if schema_name not in self.schema_definitions:
                self.errors.append(f"{path}: Reference to undefined schema: {ref}")
    
    def _validate_security_schemes(self, security_schemes: Dict[str, Any]):
        """Validate security schemes"""
        for scheme_name, scheme_def in security_schemes.items():
            if 'type' not in scheme_def:
                self.errors.append(f"Security scheme '{scheme_name}': Missing required 'type' field")
    
    def _validate_schema_consistency(self, schema: Dict[str, Any]):
        """Validate consistency across the schema"""
        # Check for unused schema definitions
        if 'components' in schema and 'schemas' in schema['components']:
            defined_schemas = set(schema['components']['schemas'].keys())
            referenced_schemas = set()
            
            # Collect all references (simplified - would need recursive search in real implementation)
            schema_str = json.dumps(schema)
            import re
            refs = re.findall(r'"#/components/schemas/([^"]+)"', schema_str)
            referenced_schemas.update(refs)
            
            unused_schemas = defined_schemas - referenced_schemas
            if unused_schemas:
                self.info.append(f"Unused schema definitions: {', '.join(unused_schemas)}")
    
    def _validate_examples_and_documentation(self, schema: Dict[str, Any]):
        """Validate examples and documentation quality"""
        # Check for missing descriptions in schema definitions
        if 'components' in schema and 'schemas' in schema['components']:
            for schema_name, schema_def in schema['components']['schemas'].items():
                if isinstance(schema_def, dict) and 'description' not in schema_def:
                    self.info.append(f"Consider adding description to schema: {schema_name}")
    
    def _report_comprehensive_results(self):
        """Report comprehensive validation results"""
        print("\n" + "=" * 80)
        print("📊 VALIDATION RESULTS")
        print("=" * 80)
        
        if self.errors:
            print(f"\n❌ ERRORS ({len(self.errors)}):")
            for i, error in enumerate(self.errors, 1):
                print(f"  {i:2d}. {error}")
        
        if self.warnings:
            print(f"\n⚠️  WARNINGS ({len(self.warnings)}):")
            for i, warning in enumerate(self.warnings, 1):
                print(f"  {i:2d}. {warning}")
        
        if self.info:
            print(f"\nℹ️  INFORMATION ({len(self.info)}):")
            for i, info in enumerate(self.info, 1):
                print(f"  {i:2d}. {info}")
        
        print("\n" + "=" * 80)
        if not self.errors and not self.warnings:
            print("🎉 EXCELLENT! Schema validation passed with no issues!")
        elif not self.errors:
            print(f"✅ GOOD! Schema validation passed with {len(self.warnings)} warnings")
        else:
            print(f"❌ ISSUES FOUND! {len(self.errors)} errors and {len(self.warnings)} warnings need attention")
        print("=" * 80)


def main():
    """Main function"""
    if len(sys.argv) > 1:
        schema_file = sys.argv[1]
    else:
        # Default to the user profile import schema
        schema_file = Path(__file__).parent.parent / 'schemas' / 'user_profile_import_schema.json'
    
    validator = ComprehensiveSchemaValidator()
    success = validator.validate_schema_file(str(schema_file))
    
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
