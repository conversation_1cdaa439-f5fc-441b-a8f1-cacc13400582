#!/usr/bin/env python3
"""
Schema Coverage Analysis

This script analyzes the user_profile_import_schema.json to ensure it covers
all the necessary fields from the Django models for complete user profile import.
"""

import json
import os
import django
from pathlib import Path
from typing import Dict, List, Set, Any

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from django.apps import apps
from django.db import models


class SchemaCoverageAnalyzer:
    """Analyzes schema coverage against Django models"""
    
    def __init__(self):
        self.schema_file = Path(__file__).parent.parent / 'schemas' / 'user_profile_import_schema.json'
        self.schema = {}
        self.missing_fields = []
        self.covered_fields = []
        self.recommendations = []
    
    def load_schema(self):
        """Load the OpenAPI schema"""
        with open(self.schema_file, 'r') as f:
            self.schema = json.load(f)
    
    def analyze_coverage(self):
        """Analyze schema coverage against Django models"""
        print("🔍 Analyzing Schema Coverage Against Django Models")
        print("=" * 80)
        
        # Load schema
        self.load_schema()
        
        # Get schema components
        schema_components = self.schema.get('components', {}).get('schemas', {})
        
        # Analyze each relevant model
        models_to_analyze = [
            # User models
            ('user', 'UserProfile'),
            ('user', 'UserAccount'),
            ('user', 'Demographics'),
            ('user', 'UserEnvironment'),
            ('user', 'GenericEnvironment'),
            ('user', 'ActivitySupport'),
            ('user', 'PsychologicalQualities'),
            ('user', 'UserTraitInclination'),
            ('user', 'GenericTrait'),
            ('user', 'UserBelief'),
            ('user', 'UserAspiration'),
            ('user', 'UserIntention'),
            ('user', 'UserInspiration'),
            ('user', 'Skill'),
            ('user', 'GenericSkill'),
            ('user', 'UserResource'),
            ('user', 'GenericResource'),
            ('user', 'UserLimitation'),
            ('user', 'GenericUserLimitation'),
            ('user', 'UserPreference'),
            ('user', 'UserMood'),
            # Activity models (relevant to user profile)
            ('activity', 'ActivityTailored'),
            ('activity', 'GenericActivity'),
        ]
        
        for app_label, model_name in models_to_analyze:
            self._analyze_model_coverage(app_label, model_name, schema_components)
        
        # Generate report
        self._generate_report()
    
    def _analyze_model_coverage(self, app_label: str, model_name: str, schema_components: Dict[str, Any]):
        """Analyze coverage for a specific model"""
        try:
            model_class = apps.get_model(app_label, model_name)
        except LookupError:
            print(f"⚠️  Model {app_label}.{model_name} not found")
            return
        
        print(f"\n📋 Analyzing {app_label}.{model_name}")
        
        # Get model fields
        model_fields = self._get_model_fields(model_class)
        
        # Find corresponding schema component
        schema_component = self._find_schema_component(model_name, schema_components)
        
        if not schema_component:
            print(f"   ❌ No schema component found for {model_name}")
            self.missing_fields.append({
                'model': f"{app_label}.{model_name}",
                'issue': 'No schema component found',
                'fields': list(model_fields.keys())
            })
            return
        
        # Check field coverage
        schema_properties = schema_component.get('properties', {})
        
        covered = []
        missing = []
        
        for field_name, field_info in model_fields.items():
            if field_name in schema_properties:
                covered.append(field_name)
                self.covered_fields.append({
                    'model': f"{app_label}.{model_name}",
                    'field': field_name,
                    'type': field_info['type']
                })
            else:
                missing.append(field_name)
                self.missing_fields.append({
                    'model': f"{app_label}.{model_name}",
                    'field': field_name,
                    'type': field_info['type'],
                    'required': field_info['required']
                })
        
        print(f"   ✅ Covered fields: {len(covered)}")
        print(f"   ❌ Missing fields: {len(missing)}")
        
        if missing:
            print(f"   Missing: {', '.join(missing)}")
    
    def _get_model_fields(self, model_class) -> Dict[str, Dict[str, Any]]:
        """Extract field information from a Django model"""
        fields = {}
        
        for field in model_class._meta.get_fields():
            if field.name.startswith('_'):
                continue
            
            # Skip reverse relations
            if hasattr(field, 'related_model') and field.related_model and hasattr(field, 'remote_field'):
                if field.remote_field:
                    continue
            
            field_info = {
                'type': field.__class__.__name__,
                'required': not getattr(field, 'null', True) and not getattr(field, 'blank', True),
                'max_length': getattr(field, 'max_length', None),
                'choices': getattr(field, 'choices', None),
                'default': getattr(field, 'default', None),
                'help_text': getattr(field, 'help_text', ''),
            }
            
            # Handle special field types
            if isinstance(field, models.ForeignKey):
                field_info['related_model'] = field.related_model.__name__
            elif isinstance(field, models.ManyToManyField):
                field_info['related_model'] = field.related_model.__name__
                field_info['many_to_many'] = True
            
            fields[field.name] = field_info
        
        return fields
    
    def _find_schema_component(self, model_name: str, schema_components: Dict[str, Any]) -> Dict[str, Any]:
        """Find the schema component that corresponds to a model"""
        # Direct match
        if model_name in schema_components:
            return schema_components[model_name]
        
        # Try common variations
        variations = [
            model_name.lower(),
            f"User{model_name}",
            f"Generic{model_name}",
            f"{model_name}Data",
            f"{model_name}Info",
        ]
        
        for variation in variations:
            if variation in schema_components:
                return schema_components[variation]
        
        # Try partial matches
        for component_name, component in schema_components.items():
            if model_name.lower() in component_name.lower():
                return component
        
        return None
    
    def _generate_report(self):
        """Generate a comprehensive coverage report"""
        print("\n" + "=" * 80)
        print("📊 SCHEMA COVERAGE ANALYSIS REPORT")
        print("=" * 80)
        
        total_fields = len(self.covered_fields) + len(self.missing_fields)
        coverage_percentage = (len(self.covered_fields) / total_fields * 100) if total_fields > 0 else 0
        
        print(f"\n📈 Overall Coverage: {coverage_percentage:.1f}%")
        print(f"   ✅ Covered fields: {len(self.covered_fields)}")
        print(f"   ❌ Missing fields: {len(self.missing_fields)}")
        
        # Group missing fields by model
        missing_by_model = {}
        for item in self.missing_fields:
            model = item.get('model', 'Unknown')
            if model not in missing_by_model:
                missing_by_model[model] = []
            missing_by_model[model].append(item)
        
        if missing_by_model:
            print(f"\n❌ MISSING FIELDS BY MODEL:")
            for model, fields in missing_by_model.items():
                print(f"\n   {model}:")
                for field in fields:
                    field_name = field.get('field', 'Unknown')
                    field_type = field.get('type', 'Unknown')
                    required = field.get('required', False)
                    req_str = " (REQUIRED)" if required else ""
                    print(f"     • {field_name}: {field_type}{req_str}")
        
        # Generate recommendations
        self._generate_recommendations()
        
        if self.recommendations:
            print(f"\n💡 RECOMMENDATIONS:")
            for i, rec in enumerate(self.recommendations, 1):
                print(f"   {i}. {rec}")
        
        print("\n" + "=" * 80)
    
    def _generate_recommendations(self):
        """Generate recommendations for improving schema coverage"""
        # Count missing fields by type
        missing_by_type = {}
        for item in self.missing_fields:
            field_type = item.get('type', 'Unknown')
            if field_type not in missing_by_type:
                missing_by_type[field_type] = 0
            missing_by_type[field_type] += 1
        
        # Generate recommendations based on missing field types
        if missing_by_type.get('ForeignKey', 0) > 0:
            self.recommendations.append(
                f"Add {missing_by_type['ForeignKey']} missing ForeignKey relationships to schema"
            )
        
        if missing_by_type.get('JSONField', 0) > 0:
            self.recommendations.append(
                f"Add {missing_by_type['JSONField']} missing JSONField definitions with proper schemas"
            )
        
        if missing_by_type.get('DateTimeField', 0) > 0:
            self.recommendations.append(
                f"Add {missing_by_type['DateTimeField']} missing timestamp fields for audit trails"
            )
        
        # Check for critical missing models
        missing_models = set()
        for item in self.missing_fields:
            if item.get('issue') == 'No schema component found':
                missing_models.add(item['model'])
        
        if missing_models:
            self.recommendations.append(
                f"Create schema components for missing models: {', '.join(missing_models)}"
            )
        
        # General recommendations
        if len(self.missing_fields) > 20:
            self.recommendations.append(
                "Consider creating a comprehensive schema update to cover all missing fields"
            )
        
        self.recommendations.append(
            "Update export functionality to include all covered fields"
        )
        
        self.recommendations.append(
            "Add validation for all required fields in import process"
        )


def main():
    """Main function"""
    analyzer = SchemaCoverageAnalyzer()
    analyzer.analyze_coverage()


if __name__ == '__main__':
    main()
