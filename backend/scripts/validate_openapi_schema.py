#!/usr/bin/env python3
"""
OpenAPI Schema Validation Script

This script validates the user_profile_import_schema.json file for OpenAPI 3.0.3 compliance.
It checks for common issues like missing "type" specifications and other schema problems.
"""

import json
import sys
from pathlib import Path
from typing import Dict, List, Any, Set


class OpenAPIValidator:
    """Validator for OpenAPI 3.0.3 schemas"""
    
    def __init__(self):
        self.errors: List[str] = []
        self.warnings: List[str] = []
        self.visited_refs: Set[str] = set()
    
    def validate_schema_file(self, file_path: str) -> bool:
        """Validate an OpenAPI schema file"""
        try:
            with open(file_path, 'r') as f:
                schema = json.load(f)
            
            print(f"Validating OpenAPI schema: {file_path}")
            
            # Validate root structure
            self._validate_root_structure(schema)
            
            # Validate components/schemas
            if 'components' in schema and 'schemas' in schema['components']:
                self._validate_schemas(schema['components']['schemas'])
            
            # Report results
            self._report_results()
            
            return len(self.errors) == 0
            
        except json.JSONDecodeError as e:
            print(f"ERROR: Invalid JSON in {file_path}: {e}")
            return False
        except FileNotFoundError:
            print(f"ERROR: File not found: {file_path}")
            return False
        except Exception as e:
            print(f"ERROR: Unexpected error validating {file_path}: {e}")
            return False
    
    def _validate_root_structure(self, schema: Dict[str, Any]):
        """Validate the root OpenAPI structure"""
        required_fields = ['openapi', 'info', 'paths']
        
        for field in required_fields:
            if field not in schema:
                self.errors.append(f"Missing required root field: {field}")
        
        # Check OpenAPI version
        if 'openapi' in schema:
            version = schema['openapi']
            if not version.startswith('3.0'):
                self.warnings.append(f"OpenAPI version {version} may not be fully supported")
    
    def _validate_schemas(self, schemas: Dict[str, Any]):
        """Validate all schema definitions"""
        for schema_name, schema_def in schemas.items():
            self._validate_schema_definition(schema_def, f"#/components/schemas/{schema_name}")
    
    def _validate_schema_definition(self, schema_def: Dict[str, Any], path: str):
        """Validate a single schema definition"""
        if not isinstance(schema_def, dict):
            self.errors.append(f"{path}: Schema definition must be an object")
            return
        
        # Check for $ref
        if '$ref' in schema_def:
            ref = schema_def['$ref']
            if ref not in self.visited_refs:
                self.visited_refs.add(ref)
                # Note: We don't resolve refs in this simple validator
            return
        
        # Check for type specification
        if 'type' not in schema_def:
            # Check if it has properties (indicating it should be an object)
            if 'properties' in schema_def:
                self.errors.append(f"{path}: Schema with 'properties' must specify 'type': 'object'")
            # Check if it has items (indicating it should be an array)
            elif 'items' in schema_def:
                self.errors.append(f"{path}: Schema with 'items' must specify 'type': 'array'")
            # Check if it has enum (could be string, number, etc.)
            elif 'enum' in schema_def:
                self.warnings.append(f"{path}: Schema with 'enum' should specify 'type'")
            else:
                self.warnings.append(f"{path}: Schema should specify 'type'")
        
        # Validate type-specific requirements
        schema_type = schema_def.get('type')
        
        if schema_type == 'object':
            self._validate_object_schema(schema_def, path)
        elif schema_type == 'array':
            self._validate_array_schema(schema_def, path)
        elif schema_type == 'string':
            self._validate_string_schema(schema_def, path)
        elif schema_type == 'integer' or schema_type == 'number':
            self._validate_numeric_schema(schema_def, path)
        
        # Recursively validate nested schemas
        if 'properties' in schema_def:
            for prop_name, prop_def in schema_def['properties'].items():
                self._validate_schema_definition(prop_def, f"{path}/properties/{prop_name}")
        
        if 'items' in schema_def:
            self._validate_schema_definition(schema_def['items'], f"{path}/items")
        
        if 'additionalProperties' in schema_def and isinstance(schema_def['additionalProperties'], dict):
            self._validate_schema_definition(schema_def['additionalProperties'], f"{path}/additionalProperties")
    
    def _validate_object_schema(self, schema_def: Dict[str, Any], path: str):
        """Validate object-type schema"""
        # Check for properties
        if 'properties' not in schema_def and 'additionalProperties' not in schema_def:
            self.warnings.append(f"{path}: Object schema should have 'properties' or 'additionalProperties'")
        
        # Validate required fields exist in properties
        if 'required' in schema_def and 'properties' in schema_def:
            required_fields = schema_def['required']
            properties = schema_def['properties']
            
            for field in required_fields:
                if field not in properties:
                    self.errors.append(f"{path}: Required field '{field}' not found in properties")
    
    def _validate_array_schema(self, schema_def: Dict[str, Any], path: str):
        """Validate array-type schema"""
        if 'items' not in schema_def:
            self.errors.append(f"{path}: Array schema must have 'items' property")
    
    def _validate_string_schema(self, schema_def: Dict[str, Any], path: str):
        """Validate string-type schema"""
        # Check for conflicting constraints
        if 'minLength' in schema_def and 'maxLength' in schema_def:
            min_len = schema_def['minLength']
            max_len = schema_def['maxLength']
            if min_len > max_len:
                self.errors.append(f"{path}: minLength ({min_len}) cannot be greater than maxLength ({max_len})")
    
    def _validate_numeric_schema(self, schema_def: Dict[str, Any], path: str):
        """Validate numeric-type schema"""
        # Check for conflicting constraints
        if 'minimum' in schema_def and 'maximum' in schema_def:
            minimum = schema_def['minimum']
            maximum = schema_def['maximum']
            if minimum > maximum:
                self.errors.append(f"{path}: minimum ({minimum}) cannot be greater than maximum ({maximum})")
    
    def _report_results(self):
        """Report validation results"""
        if self.errors:
            print(f"\n❌ ERRORS ({len(self.errors)}):")
            for error in self.errors:
                print(f"  • {error}")
        
        if self.warnings:
            print(f"\n⚠️  WARNINGS ({len(self.warnings)}):")
            for warning in self.warnings:
                print(f"  • {warning}")
        
        if not self.errors and not self.warnings:
            print("\n✅ Schema validation passed with no issues!")
        elif not self.errors:
            print(f"\n✅ Schema validation passed with {len(self.warnings)} warnings")
        else:
            print(f"\n❌ Schema validation failed with {len(self.errors)} errors and {len(self.warnings)} warnings")


def main():
    """Main function"""
    if len(sys.argv) > 1:
        schema_file = sys.argv[1]
    else:
        # Default to the user profile import schema
        schema_file = Path(__file__).parent.parent / 'schemas' / 'user_profile_import_schema.json'
    
    validator = OpenAPIValidator()
    success = validator.validate_schema_file(str(schema_file))
    
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
