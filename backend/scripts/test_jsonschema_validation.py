#!/usr/bin/env python3
"""
Test JSON Schema validation against the OpenAPI schema

This script tests the user_profile_import_schema.json file using the jsonschema library
to see if it produces the "type must be specified" error.
"""

import json
import sys
from pathlib import Path
import jsonschema
from jsonschema import Draft7Validator, ValidationError


def test_schema_validation():
    """Test the schema validation"""
    schema_file = Path(__file__).parent.parent / 'schemas' / 'user_profile_import_schema.json'
    
    try:
        with open(schema_file, 'r') as f:
            schema = json.load(f)
        
        print(f"Testing schema validation for: {schema_file}")
        
        # Test 1: Validate the schema itself
        print("\n1. Validating the schema structure...")
        try:
            # Check if it's a valid JSON Schema
            jsonschema.Draft7Validator.check_schema(schema)
            print("✅ Schema structure is valid")
        except jsonschema.SchemaError as e:
            print(f"❌ Schema structure error: {e}")
            return False
        
        # Test 2: Try to create a validator
        print("\n2. Creating validator...")
        try:
            validator = Draft7Validator(schema)
            print("✅ Validator created successfully")
        except Exception as e:
            print(f"❌ Validator creation error: {e}")
            return False
        
        # Test 3: Test with a sample profile data
        print("\n3. Testing with sample profile data...")
        sample_data = {
            "profile_data": {
                "user_account": {
                    "username": "testuser",
                    "email": "<EMAIL>"
                },
                "profile_name": "Test Profile"
            }
        }
        
        try:
            errors = list(validator.iter_errors(sample_data))
            if errors:
                print(f"⚠️  Validation errors found ({len(errors)}):")
                for error in errors[:5]:  # Show first 5 errors
                    print(f"  • {error.message} (at {'/'.join(str(p) for p in error.path)})")
                if len(errors) > 5:
                    print(f"  ... and {len(errors) - 5} more errors")
            else:
                print("✅ Sample data validates successfully")
        except Exception as e:
            print(f"❌ Validation error: {e}")
            return False
        
        # Test 4: Check specific schema components
        print("\n4. Checking schema components...")
        components = schema.get('components', {}).get('schemas', {})
        
        problematic_schemas = []
        for schema_name, schema_def in components.items():
            if isinstance(schema_def, dict):
                # Check if it has properties but no type
                if 'properties' in schema_def and 'type' not in schema_def:
                    problematic_schemas.append(f"{schema_name}: has 'properties' but no 'type'")
                # Check if it has items but no type
                elif 'items' in schema_def and 'type' not in schema_def:
                    problematic_schemas.append(f"{schema_name}: has 'items' but no 'type'")
        
        if problematic_schemas:
            print("⚠️  Potential issues found:")
            for issue in problematic_schemas:
                print(f"  • {issue}")
        else:
            print("✅ No obvious type specification issues found")
        
        # Test 5: Check for OpenAPI vs JSON Schema differences
        print("\n5. Checking OpenAPI vs JSON Schema compatibility...")
        openapi_version = schema.get('openapi', 'unknown')
        print(f"OpenAPI version: {openapi_version}")
        
        if openapi_version.startswith('3.0'):
            print("ℹ️  Note: OpenAPI 3.0 schemas may have different validation rules than pure JSON Schema")
        
        return True
        
    except FileNotFoundError:
        print(f"❌ Schema file not found: {schema_file}")
        return False
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False


def test_specific_components():
    """Test specific components that might be causing issues"""
    schema_file = Path(__file__).parent.parent / 'schemas' / 'user_profile_import_schema.json'
    
    try:
        with open(schema_file, 'r') as f:
            schema = json.load(f)
        
        print("\n6. Testing specific components...")
        components = schema.get('components', {}).get('schemas', {})
        
        # Test components that might have issues
        test_components = [
            'ActivitySupport',
            'PsychologicalQualities', 
            'AIGenerateResponse',
            'ErrorResponse'
        ]
        
        for component_name in test_components:
            if component_name in components:
                component = components[component_name]
                print(f"\nTesting {component_name}:")
                
                # Check for type specification
                if 'type' not in component:
                    print(f"  ⚠️  Missing 'type' field")
                else:
                    print(f"  ✅ Has 'type': {component['type']}")
                
                # Check properties
                if 'properties' in component:
                    props_without_type = []
                    for prop_name, prop_def in component['properties'].items():
                        if isinstance(prop_def, dict) and 'type' not in prop_def and '$ref' not in prop_def:
                            props_without_type.append(prop_name)
                    
                    if props_without_type:
                        print(f"  ⚠️  Properties without 'type': {', '.join(props_without_type)}")
                    else:
                        print(f"  ✅ All properties have 'type' or '$ref'")
        
    except Exception as e:
        print(f"❌ Error testing components: {e}")


def main():
    """Main function"""
    print("JSON Schema Validation Test")
    print("=" * 50)
    
    success = test_schema_validation()
    test_specific_components()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ Schema validation test completed")
    else:
        print("❌ Schema validation test failed")
    
    return 0 if success else 1


if __name__ == '__main__':
    sys.exit(main())
