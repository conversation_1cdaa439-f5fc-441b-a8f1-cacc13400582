#!/usr/bin/env python3
"""
Test the example user profile against the schema

This script tests the example_user_profile.json file against the user_profile_import_schema.json
to see if there are any validation issues.
"""

import json
import sys
from pathlib import Path
from apps.user.serializers.import_serializers import CompleteUserProfileSerializer
from rest_framework import serializers


def test_example_profile():
    """Test the example profile against the serializer"""
    schema_file = Path(__file__).parent.parent / 'schemas' / 'user_profile_import_schema.json'
    example_file = Path(__file__).parent.parent / 'schemas' / 'example_user_profile.json'
    
    try:
        # Load the example profile
        with open(example_file, 'r') as f:
            example_data = json.load(f)
        
        print(f"Testing example profile: {example_file}")
        print(f"Against schema: {schema_file}")
        
        # Test with Django REST Framework serializer
        print("\n1. Testing with Django REST Framework serializer...")
        try:
            serializer = CompleteUserProfileSerializer(data=example_data)
            if serializer.is_valid():
                print("✅ Example profile validates successfully with DRF serializer")
                validated_data = serializer.validated_data
                print(f"   Validated data keys: {list(validated_data.keys())}")
            else:
                print("❌ Example profile validation failed with DRF serializer")
                print("Errors:")
                for field, errors in serializer.errors.items():
                    print(f"  • {field}: {errors}")
                return False
        except Exception as e:
            print(f"❌ Error with DRF serializer: {e}")
            return False
        
        # Test specific components that might have issues
        print("\n2. Testing specific components...")
        
        # Test environments
        environments = example_data.get('environments', [])
        if environments:
            print(f"   Found {len(environments)} environment(s)")
            for i, env in enumerate(environments):
                print(f"   Environment {i+1}: {env.get('environment_name', 'Unknown')}")
                if 'time_availability' in env.get('activity_support', {}):
                    time_avail = env['activity_support']['time_availability']
                    print(f"     Time availability type: {type(time_avail)}")
                    print(f"     Time availability keys: {list(time_avail.keys()) if isinstance(time_avail, dict) else 'Not a dict'}")
        
        # Test psychological qualities
        for env in environments:
            if 'psychological_qualities' in env:
                psych = env['psychological_qualities']
                if 'emotional_associations' in psych:
                    emo_assoc = psych['emotional_associations']
                    print(f"   Emotional associations type: {type(emo_assoc)}")
                    print(f"   Emotional associations keys: {list(emo_assoc.keys()) if isinstance(emo_assoc, dict) else 'Not a dict'}")
        
        print("\n✅ Example profile test completed successfully")
        return True
        
    except FileNotFoundError as e:
        print(f"❌ File not found: {e}")
        return False
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_import_request_format():
    """Test the full import request format"""
    example_file = Path(__file__).parent.parent / 'schemas' / 'example_user_profile.json'
    
    try:
        with open(example_file, 'r') as f:
            profile_data = json.load(f)
        
        print("\n3. Testing full import request format...")
        
        # Create the full import request structure
        import_request = {
            "profile_data": profile_data,
            "options": {
                "overwriteExisting": False,
                "validateBeforeImport": True,
                "createBackup": True
            }
        }
        
        # Test with the import request serializer
        from apps.user.serializers.import_serializers import UserProfileImportRequestSerializer
        
        serializer = UserProfileImportRequestSerializer(data=import_request)
        if serializer.is_valid():
            print("✅ Full import request validates successfully")
            return True
        else:
            print("❌ Full import request validation failed")
            print("Errors:")
            for field, errors in serializer.errors.items():
                print(f"  • {field}: {errors}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing import request: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main function"""
    print("Example Profile Validation Test")
    print("=" * 50)
    
    success1 = test_example_profile()
    success2 = test_import_request_format()
    
    print("\n" + "=" * 50)
    if success1 and success2:
        print("✅ All tests passed")
        return 0
    else:
        print("❌ Some tests failed")
        return 1


if __name__ == '__main__':
    # Add the backend directory to the Python path
    import os
    import django
    from django.conf import settings
    
    # Setup Django
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
    django.setup()
    
    sys.exit(main())
