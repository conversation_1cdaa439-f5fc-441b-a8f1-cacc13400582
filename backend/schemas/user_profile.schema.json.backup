{"type": "object", "required": ["user_account", "profile_name", "demographics"], "properties": {"user_account": {"type": "object", "required": ["username", "email"], "properties": {"username": {"type": "string", "description": "Unique username for login (3-150 chars, letters/numbers/underscore only)"}, "email": {"type": "string", "description": "User's email address"}, "first_name": {"type": "string", "description": "User's first name"}, "last_name": {"type": "string", "description": "User's last name"}, "password": {"type": "string", "description": "User password (minimum 8 characters, will be hashed on server)"}}}, "profile_name": {"type": "string", "description": "Display name for the user profile (1-255 characters)"}, "is_real": {"type": "boolean", "description": "True for real users, False for test/archetypal profiles"}, "demographics": {"type": "object", "required": ["full_name", "age", "gender", "location", "language", "occupation"], "properties": {"full_name": {"type": "string", "description": "User's complete full name"}, "age": {"type": "integer", "description": "User's current age in years (13-120)"}, "gender": {"type": "string", "description": "User's gender identity"}, "location": {"type": "string", "description": "User's current location or residence (city, region, country)"}, "language": {"type": "string", "description": "User's preferred or native language"}, "occupation": {"type": "string", "description": "User's current occupation or professional role"}}}, "environment": {"type": "object", "required": ["environment_name", "environment_description", "is_current"], "properties": {"environment_name": {"type": "string", "description": "User's personal name for their primary environment"}, "environment_description": {"type": "string", "description": "Detailed description of the user's living/working environment"}, "is_current": {"type": "boolean", "description": "Whether this is the user's current primary environment (should be true)"}, "generic_environment_code": {"type": "string", "description": "Code referencing a generic environment archetype (optional)"}, "effective_start": {"type": "string", "description": "Date when user started using this environment (YYYY-MM-DD format)"}, "effective_end": {"type": "string", "description": "Date when user will stop using this environment (YYYY-MM-DD format, optional)"}, "physical_properties": {"type": "object", "properties": {"rurality": {"type": "integer", "description": "Urban to rural scale: 0=urban, 100=rural"}, "noise_level": {"type": "integer", "description": "Ambient noise level: 0=silent, 100=very noisy"}, "light_quality": {"type": "integer", "description": "Natural light quality: 0=poor, 100=excellent"}, "accessibility": {"type": "integer", "description": "Physical accessibility level: 0=poor, 100=excellent"}, "air_quality": {"type": "integer", "description": "Air quality rating: 0=poor, 100=excellent"}, "has_natural_elements": {"type": "boolean", "description": "Presence of natural elements like plants, water, etc."}, "space_size": {"type": "string", "description": "Available space size: small, medium, large, or very_large"}}}, "social_context": {"type": "object", "properties": {"privacy_level": {"type": "integer", "description": "Level of privacy available: 0=public, 100=completely private"}, "social_interaction_frequency": {"type": "integer", "description": "Frequency of social interactions in this environment: 0=none, 100=constant"}, "community_engagement": {"type": "integer", "description": "Level of community involvement: 0=isolated, 100=highly engaged"}, "relationship_quality": {"type": "integer", "description": "Quality of relationships with people in environment: 0=poor, 100=excellent"}}}, "activity_support": {"type": "object", "properties": {"creative_activities": {"type": "integer", "description": "Support for creative/artistic activities: 0=no support, 100=excellent support"}, "physical_activities": {"type": "integer", "description": "Support for physical exercise/movement: 0=no support, 100=excellent support"}, "intellectual_activities": {"type": "integer", "description": "Support for learning/intellectual pursuits: 0=no support, 100=excellent support"}, "social_activities": {"type": "integer", "description": "Support for social gatherings/interactions: 0=no support, 100=excellent support"}, "reflective_activities": {"type": "integer", "description": "Support for meditation/journaling/reflection: 0=no support, 100=excellent support"}, "productive_activities": {"type": "integer", "description": "Support for work/productive tasks: 0=no support, 100=excellent support"}}}, "psychological_qualities": {"type": "object", "properties": {"restorative_quality": {"type": "integer", "description": "How restorative and rejuvenating the environment feels: 0=draining, 100=highly restorative"}, "stimulation_level": {"type": "integer", "description": "Level of mental stimulation provided: 0=understimulating, 100=overstimulating"}, "aesthetic_appeal": {"type": "integer", "description": "Visual beauty and aesthetic appeal: 0=unappealing, 100=beautiful"}, "comfort_level": {"type": "integer", "description": "Physical and emotional comfort level: 0=uncomfortable, 100=very comfortable"}, "personal_significance": {"type": "integer", "description": "Personal meaning and emotional attachment: 0=no attachment, 100=deeply meaningful"}}}}}, "traits": {"type": "array", "description": "HEXACO personality trait inclinations (15-24 traits recommended)", "items": {"type": "object", "required": ["trait_code", "strength", "awareness"], "properties": {"trait_code": {"type": "string", "description": "HEXACO trait code (e.g., honesty_sincerity, extra_sociability, consc_organization)"}, "strength": {"type": "integer", "description": "Strength of this trait in personality: 0=very low, 100=very high"}, "awareness": {"type": "integer", "description": "User's self-awareness of this trait: 0=completely unaware, 100=highly aware"}, "development_notes": {"type": "string", "description": "Notes about how this trait developed or manifests in behavior"}}}}, "beliefs": {"type": "array", "description": "Core beliefs and worldview elements (3-8 beliefs recommended)", "items": {"type": "object", "required": ["belief_statement", "strength", "certainty"], "properties": {"belief_statement": {"type": "string", "description": "Clear, specific statement of what the user believes"}, "strength": {"type": "integer", "description": "How strongly the user holds this belief: 0=weak belief, 100=core conviction"}, "certainty": {"type": "integer", "description": "How certain the user is about this belief: 0=uncertain, 100=absolutely certain"}, "evidence_sources": {"type": "array", "items": {"type": "string"}, "description": "Sources or experiences that support this belief"}, "life_impact": {"type": "integer", "description": "How much this belief impacts daily decisions: 0=no impact, 100=guides all decisions"}}}}, "aspirations": {"type": "array", "description": "Long-term goals and vision elements (2-5 aspirations recommended)", "items": {"type": "object", "required": ["title", "description", "importance"], "properties": {"title": {"type": "string", "description": "Concise title of the aspiration"}, "description": {"type": "string", "description": "Detailed description of what the user wants to achieve or become"}, "importance": {"type": "integer", "description": "How important this aspiration is: 0=nice to have, 100=life defining"}, "time_horizon": {"type": "string", "description": "Expected timeframe (e.g., '5 years', 'lifetime', 'within 2 years')"}, "current_progress": {"type": "integer", "description": "Current progress toward this aspiration: 0=not started, 100=achieved"}}}}, "intentions": {"type": "array", "description": "Short to medium-term goals and plans (2-8 intentions recommended)", "items": {"type": "object", "required": ["title", "description", "target_date", "commitment_level"], "properties": {"title": {"type": "string", "description": "Clear title of the intention"}, "description": {"type": "string", "description": "Specific description of what the user intends to accomplish"}, "target_date": {"type": "string", "description": "Target completion date (YYYY-MM-DD format)"}, "commitment_level": {"type": "integer", "description": "How committed the user is to this intention: 0=weak commitment, 100=fully committed"}, "success_criteria": {"type": "string", "description": "How the user will know they've achieved this intention"}}}}, "inspirations": {"type": "array", "description": "Sources of motivation and inspiration (2-6 sources recommended)", "items": {"type": "object", "required": ["source", "description", "strength"], "properties": {"source": {"type": "string", "description": "Source of inspiration (person, book, experience, philosophy, etc.)"}, "description": {"type": "string", "description": "How this source inspires the user and why it matters"}, "strength": {"type": "integer", "description": "How much this source inspires the user: 0=mild inspiration, 100=life changing"}, "category": {"type": "string", "description": "Category of inspiration (person, media, experience, philosophy, nature, etc.)"}, "reference_url": {"type": "string", "description": "URL reference if applicable (books, articles, videos)"}}}}, "skills": {"type": "array", "description": "Personal skills and capabilities (5-15 skills recommended)", "items": {"type": "object", "required": ["skill_code", "description", "level"], "properties": {"skill_code": {"type": "string", "description": "Code referencing a generic skill type (e.g., communication, coding, cooking)"}, "description": {"type": "string", "description": "Personal description of how this skill manifests and is used"}, "level": {"type": "integer", "description": "Current skill level: 0=beginner, 50=intermediate, 100=expert"}, "user_awareness": {"type": "integer", "description": "How aware the user is of their skill level: 0=unaware, 100=very aware"}, "user_enjoyment": {"type": "integer", "description": "How much the user enjoys using this skill: 0=dislikes, 100=loves"}, "practice_frequency": {"type": "string", "description": "How often this skill is practiced (daily, weekly, monthly, rarely)"}, "acquisition_context": {"type": "string", "description": "How and where this skill was acquired (school, work, self-taught, etc.)"}}}}, "resources": {"type": "array", "description": "Available resources and tools (5-20 resources recommended)", "items": {"type": "object", "required": ["specific_name", "resource_code"], "properties": {"specific_name": {"type": "string", "description": "User's specific name for this resource"}, "resource_code": {"type": "string", "description": "Code referencing a generic resource type (e.g., laptop, car, gym_membership)"}, "location_details": {"type": "string", "description": "Where this resource is located or how it's accessed"}, "ownership_details": {"type": "string", "description": "How the user owns, rents, borrows, or accesses this resource"}, "availability": {"type": "string", "description": "When this resource is available for use"}, "condition": {"type": "string", "description": "Current condition or quality of the resource"}, "notes": {"type": "string", "description": "Additional relevant information about this resource"}}}}, "limitations": {"type": "array", "description": "Personal constraints and limitations (2-10 limitations recommended)", "items": {"type": "object", "required": ["limitation_code", "description", "severity"], "properties": {"limitation_code": {"type": "string", "description": "Code referencing a generic limitation type (e.g., time_constraints, physical_disability)"}, "description": {"type": "string", "description": "Personal description of how this limitation affects the user"}, "severity": {"type": "integer", "description": "How severely this limitation impacts the user: 0=minor inconvenience, 100=major barrier"}, "frequency": {"type": "string", "description": "How often this limitation is encountered (constantly, daily, weekly, situational)"}, "coping_strategies": {"type": "string", "description": "How the user currently copes with or works around this limitation"}, "is_temporary": {"type": "boolean", "description": "Whether this limitation is expected to be temporary or permanent"}}}}, "preferences": {"type": "array", "description": "Personal preferences and patterns (8-20 preferences recommended)", "items": {"type": "object", "required": ["pref_name", "pref_description", "pref_strength"], "properties": {"pref_name": {"type": "string", "description": "Name of the preference or pattern"}, "pref_description": {"type": "string", "description": "Detailed description of the preference and how it manifests"}, "pref_strength": {"type": "integer", "description": "Strength of preference: -100=strong dislike, 0=neutral, 100=strong preference"}, "user_awareness": {"type": "integer", "description": "How aware the user is of this preference: 0=unconscious, 100=very conscious"}, "context": {"type": "string", "description": "Context where this preference applies (work, social, personal, etc.)"}}}}, "current_mood": {"type": "object", "required": ["energy_level", "stress_level", "optimism", "social_engagement"], "properties": {"energy_level": {"type": "integer", "description": "Current energy level: 0=exhausted, 50=normal, 100=highly energetic"}, "stress_level": {"type": "integer", "description": "Current stress level: 0=completely relaxed, 100=extremely stressed"}, "optimism": {"type": "integer", "description": "Current optimism about the future: 0=pessimistic, 100=very optimistic"}, "social_engagement": {"type": "integer", "description": "Current desire for social interaction: 0=want to be alone, 100=want lots of social contact"}, "mood_description": {"type": "string", "description": "Qualitative description of overall current mood and emotional state"}}}, "trust_level": {"type": "object", "required": ["value"], "properties": {"value": {"type": "number", "description": "Overall trust level with guidance systems: 0=no trust, 100=complete trust"}, "domain_scores": {"type": "object", "properties": {"goal_setting": {"type": "number", "description": "Trust in goal-setting guidance: 0=no trust, 100=complete trust"}, "activity_recommendation": {"type": "number", "description": "Trust in activity recommendations: 0=no trust, 100=complete trust"}, "personal_growth": {"type": "number", "description": "Trust in personal growth guidance: 0=no trust, 100=complete trust"}, "lifestyle_guidance": {"type": "number", "description": "Trust in lifestyle guidance: 0=no trust, 100=complete trust"}}}, "progression_notes": {"type": "string", "description": "Notes about how trust has developed over time and current factors"}}}}}