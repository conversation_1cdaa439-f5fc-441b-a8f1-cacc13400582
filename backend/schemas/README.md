# User Profile Schema Management Guide

## 🎯 Overview

This directory contains the authoritative JSON schema for user profile imports. The schema serves as the single source of truth for validating user profile data before import into the Goali system.

## 📁 Files in This Directory

- **`user_profile.schema.json`** - Main JSON schema for user profile validation
- **`README.md`** - This guide (you are here)

## 🔧 Schema Modification Guidelines

### When to Update the Schema

Update the schema when:
- ✅ Adding new properties to user profile models
- ✅ Changing validation rules or constraints
- ✅ Adding new enum values for codes
- ✅ Modifying required fields
- ✅ Supporting new data structures (arrays, objects)

### Files That Must Be Updated

When modifying `user_profile.schema.json`, you **MUST** also update:

1. **Pydantic Business Objects** (`apps/user/services/user_profile_business_objects.py`)
   - Add corresponding Pydantic model fields
   - Include proper validation rules
   - Update type annotations

2. **Django Serializers** (`apps/user/serializers/import_serializers.py`)
   - Add new serializer fields
   - Update validation logic
   - Ensure consistency with Pydantic models

3. **Database Models** (`apps/user/models.py`)
   - Verify model fields support new schema properties
   - Create migrations if needed
   - Update model relationships

4. **Import Service** (`apps/user/services/profile_import_service.py`)
   - Update import logic for new fields
   - Add validation for new properties
   - Handle new data structures

5. **Documentation** (`docs/backend/users/questionnaire2json_PROMPT.md`)
   - Update AI generation instructions
   - Add examples for new fields
   - Update code references

6. **Tests** (`apps/user/tests/test_profile_import.py`)
   - Add test cases for new fields
   - Update existing test data
   - Verify validation behavior

### 🚨 Critical Validation Steps

After schema changes, **ALWAYS**:

1. **Regenerate Enum Constraints**
   ```bash
   docker exec -it backend-web-1 python manage.py generate_schema_with_enums
   ```

2. **Update Codes Catalog**
   ```bash
   docker exec -it backend-web-1 python manage.py generate_codes_catalog
   ```

3. **Run Import Tests**
   ```bash
   docker exec -it backend-web-1 python /usr/src/app/test_simple_profile_import.py
   ```

4. **Validate Schema Syntax**
   ```bash
   docker exec -it backend-web-1 python -c "import json; json.load(open('schemas/user_profile.schema.json'))"
   ```

## 📋 Schema Structure Guidelines

### Property Naming
- Use `snake_case` for all property names
- Be descriptive but concise
- Match Django model field names when possible

### Data Types
- **Strings**: Use for text, codes, descriptions
- **Integers**: Use for numeric values, ratings (0-100)
- **Booleans**: Use for true/false flags
- **Arrays**: Use for lists of objects (traits, skills, etc.)
- **Objects**: Use for nested structures (environment, demographics)

### Validation Rules
- **Required Fields**: Only mark truly essential fields as required
- **Enum Constraints**: Use for all code fields (trait_code, skill_code, etc.)
- **Range Validation**: Use min/max for numeric fields
- **String Length**: Set reasonable limits for text fields

### Array vs Single Object
- **Multiple Environments**: Use array `"environments": []`
- **Multiple Resource Inventories**: Use array `"resources": []`
- **Single Demographics**: Use object `"demographics": {}`
- **Single Current Mood**: Use object `"current_mood": {}`

## 🔄 Schema Update Process

### Step 1: Analyze Requirements
1. Identify missing properties from Django models
2. Determine data types and validation rules
3. Check if arrays or single objects are needed
4. Verify code enum requirements

### Step 2: Update Schema
1. Add new properties to appropriate sections
2. Update required fields list if needed
3. Add enum constraints for code fields
4. Include proper descriptions

### Step 3: Update Supporting Files
1. Update Pydantic models with new fields
2. Add serializer fields with validation
3. Update import service logic
4. Modify documentation and examples

### Step 4: Test and Validate
1. Run schema validation tests
2. Test import functionality
3. Verify enum constraints work
4. Check documentation accuracy

## 🎯 Common Schema Patterns

### Code Fields (with enum validation)
```json
"belief_code": {
  "type": "string",
  "description": "Belief code from authoritative catalog",
  "enum": ["self_worth_inherent", "self_efficacy_general", ...]
}
```

### Rating Fields (0-100 scale)
```json
"strength": {
  "type": "integer",
  "description": "Strength of this trait: 0=very low, 100=very high",
  "minimum": 0,
  "maximum": 100
}
```

### Array of Objects
```json
"environments": {
  "type": "array",
  "description": "User environments (1-5 environments recommended)",
  "items": {
    "type": "object",
    "required": ["environment_name", "is_current"],
    "properties": { ... }
  }
}
```

### Extended Range Fields (-100 to 100)
```json
"emotionality": {
  "type": "integer",
  "description": "Emotional charge: -100=very negative, 0=neutral, 100=very positive",
  "minimum": -100,
  "maximum": 100
}
```

## 🚀 Best Practices

### Schema Design
- ✅ Keep schema in sync with database models
- ✅ Use descriptive field names and descriptions
- ✅ Include validation constraints
- ✅ Support multiple instances where models allow (environments, resources)
- ✅ Use enum constraints for all code fields

### Code Management
- ✅ Always reference authoritative catalogs for codes
- ✅ Use exact code values from database
- ✅ Update enum constraints when codes change
- ✅ Validate codes during import process

### Documentation
- ✅ Update all related documentation
- ✅ Include examples for new fields
- ✅ Explain validation rules clearly
- ✅ Reference authoritative code sources

## 🔗 Related Documentation

- **Import System**: `docs/backend/users/USER_PROFILE_IMPORT_SYSTEM.md`
- **Code Validation**: `docs/backend/users/USER_CODES_AUTHORITATIVE.md`
- **AI Generation**: `docs/backend/users/questionnaire2json_PROMPT.md`
- **Codes Catalog**: `data/authoritative_catalogs/comprehensive_codes_catalog.md`

---

**Remember**: The schema is the foundation of the import system. Changes here ripple through the entire codebase. Always follow the complete update process to ensure system integrity.
