{"type": "object", "required": ["user_account", "profile_name", "demographics"], "properties": {"user_account": {"type": "object", "required": ["username", "email"], "properties": {"username": {"type": "string", "description": "Unique username for login (3-150 chars, letters/numbers/underscore only)"}, "email": {"type": "string", "description": "User's email address"}, "first_name": {"type": "string", "description": "User's first name"}, "last_name": {"type": "string", "description": "User's last name"}, "password": {"type": "string", "description": "User password (minimum 8 characters, will be hashed on server)"}}}, "profile_name": {"type": "string", "description": "Display name for the user profile (1-255 characters)"}, "is_real": {"type": "boolean", "description": "True for real users, False for test/archetypal profiles"}, "demographics": {"type": "object", "required": ["full_name", "age", "gender", "location", "language", "occupation"], "properties": {"full_name": {"type": "string", "description": "User's complete full name"}, "age": {"type": "integer", "description": "User's current age in years (13-120)"}, "gender": {"type": "string", "description": "User's gender identity"}, "location": {"type": "string", "description": "User's current location or residence (city, region, country)"}, "language": {"type": "string", "description": "User's preferred or native language"}, "occupation": {"type": "string", "description": "User's current occupation or professional role"}}}, "environments": {"type": "array", "description": "User environments (1-5 environments recommended)", "items": {"type": "object", "required": ["environment_name", "environment_description", "is_current"], "properties": {"environment_name": {"type": "string", "description": "User's personal name for their primary environment"}, "environment_description": {"type": "string", "description": "Detailed description of the user's living/working environment"}, "is_current": {"type": "boolean", "description": "Whether this is the user's current primary environment (should be true)"}, "generic_environment_code": {"type": "string", "description": "Code referencing a generic environment archetype (optional)"}, "effective_start": {"type": "string", "description": "Date when user started using this environment (YYYY-MM-DD format)"}, "effective_end": {"type": "string", "description": "Date when user will stop using this environment (YYYY-MM-DD format, optional)"}, "physical_properties": {"type": "object", "properties": {"rurality": {"type": "integer", "description": "Urban to rural scale: 0=urban, 100=rural"}, "noise_level": {"type": "integer", "description": "Ambient noise level: 0=silent, 100=very noisy"}, "light_quality": {"type": "integer", "description": "Natural light quality: 0=poor, 100=excellent"}, "accessibility": {"type": "integer", "description": "Physical accessibility level: 0=poor, 100=excellent"}, "air_quality": {"type": "integer", "description": "Air quality rating: 0=poor, 100=excellent"}, "has_natural_elements": {"type": "boolean", "description": "Presence of natural elements like plants, water, etc."}, "space_size": {"type": "string", "description": "Available space size: small, medium, large, or very_large"}}}, "social_context": {"type": "object", "properties": {"privacy_level": {"type": "integer", "description": "Level of privacy available: 0=public, 100=completely private"}, "social_interaction_frequency": {"type": "integer", "description": "Frequency of social interactions in this environment: 0=none, 100=constant"}, "community_engagement": {"type": "integer", "description": "Level of community involvement: 0=isolated, 100=highly engaged"}, "relationship_quality": {"type": "integer", "description": "Quality of relationships with people in environment: 0=poor, 100=excellent"}}}, "activity_support": {"type": "object", "properties": {"creative_activities": {"type": "integer", "description": "Support for creative/artistic activities: 0=no support, 100=excellent support"}, "physical_activities": {"type": "integer", "description": "Support for physical exercise/movement: 0=no support, 100=excellent support"}, "intellectual_activities": {"type": "integer", "description": "Support for learning/intellectual pursuits: 0=no support, 100=excellent support"}, "social_activities": {"type": "integer", "description": "Support for social gatherings/interactions: 0=no support, 100=excellent support"}, "reflective_activities": {"type": "integer", "description": "Support for meditation/journaling/reflection: 0=no support, 100=excellent support"}, "productive_activities": {"type": "integer", "description": "Support for work/productive tasks: 0=no support, 100=excellent support"}}}, "psychological_qualities": {"type": "object", "properties": {"restorative_quality": {"type": "integer", "description": "How restorative and rejuvenating the environment feels: 0=draining, 100=highly restorative"}, "stimulation_level": {"type": "integer", "description": "Level of mental stimulation provided: 0=understimulating, 100=overstimulating"}, "aesthetic_appeal": {"type": "integer", "description": "Visual beauty and aesthetic appeal: 0=unappealing, 100=beautiful"}, "comfort_level": {"type": "integer", "description": "Physical and emotional comfort level: 0=uncomfortable, 100=very comfortable"}, "personal_significance": {"type": "integer", "description": "Personal meaning and emotional attachment: 0=no attachment, 100=deeply meaningful"}}}}}}, "traits": {"type": "array", "description": "HEXACO personality trait inclinations (15-24 traits recommended)", "items": {"type": "object", "required": ["trait_code", "strength", "awareness"], "properties": {"trait_code": {"type": "string", "description": "HEXACO trait code from database (24 valid codes)", "enum": ["agree_flexibility", "agree_forgiveness", "agree_gentleness", "agree_patience", "consc_diligence", "consc_organization", "consc_perfectionism", "consc_prudence", "emotion_anxiety", "emotion_dependence", "emotion_fearfulness", "emotion_sentimentality", "extra_liveliness", "extra_self_esteem", "extra_sociability", "extra_social_boldness", "honesty_fairness", "honesty_greed_avoidance", "honesty_modesty", "honesty_sincerity", "open_aesthetic", "open_creativity", "open_inquisitive", "open_unconventional"]}, "strength": {"type": "integer", "description": "Strength of this trait in personality: 0=very low, 100=very high"}, "awareness": {"type": "integer", "description": "User's self-awareness of this trait: 0=completely unaware, 100=highly aware"}, "development_notes": {"type": "string", "description": "Notes about how this trait developed or manifests in behavior"}}}}, "beliefs": {"type": "array", "description": "Core beliefs and worldview elements (3-8 beliefs recommended)", "items": {"type": "object", "required": ["belief_statement", "strength", "certainty"], "properties": {"belief_statement": {"type": "string", "description": "Clear, specific statement of what the user believes"}, "strength": {"type": "integer", "description": "How strongly the user holds this belief: 0=weak belief, 100=core conviction"}, "certainty": {"type": "integer", "description": "How certain the user is about this belief: 0=uncertain, 100=absolutely certain"}, "evidence_sources": {"type": "array", "items": {"type": "string"}, "description": "Sources or experiences that support this belief"}, "life_impact": {"type": "integer", "description": "How much this belief impacts daily decisions: 0=no impact, 100=guides all decisions"}, "emotionality": {"type": "integer", "description": "Emotional charge of this belief: -100=very negative, 0=neutral, 100=very positive", "minimum": -100, "maximum": 100}, "user_confidence": {"type": "integer", "description": "User's personal conviction in this belief: 0=very uncertain, 100=absolutely certain"}, "stability": {"type": "integer", "description": "Resistance to change of this belief: 0=very changeable, 100=deeply ingrained"}, "user_awareness": {"type": "integer", "description": "User's awareness of holding this belief: 0=unconscious, 100=highly conscious"}}}}, "aspirations": {"type": "array", "description": "Long-term goals and vision elements (2-5 aspirations recommended)", "items": {"type": "object", "required": ["title", "description", "importance"], "properties": {"title": {"type": "string", "description": "Concise title of the aspiration"}, "description": {"type": "string", "description": "Detailed description of what the user wants to achieve or become"}, "importance": {"type": "integer", "description": "How important this aspiration is: 0=nice to have, 100=life defining"}, "time_horizon": {"type": "string", "description": "Expected timeframe (e.g., '5 years', 'lifetime', 'within 2 years')"}, "current_progress": {"type": "integer", "description": "Current progress toward this aspiration: 0=not started, 100=achieved"}}}}, "intentions": {"type": "array", "description": "Short to medium-term goals and plans (2-8 intentions recommended)", "items": {"type": "object", "required": ["title", "description", "target_date", "commitment_level"], "properties": {"title": {"type": "string", "description": "Clear title of the intention"}, "description": {"type": "string", "description": "Specific description of what the user intends to accomplish"}, "target_date": {"type": "string", "description": "Target completion date (YYYY-MM-DD format)"}, "commitment_level": {"type": "integer", "description": "How committed the user is to this intention: 0=weak commitment, 100=fully committed"}, "success_criteria": {"type": "string", "description": "How the user will know they've achieved this intention"}}}}, "inspirations": {"type": "array", "description": "Sources of motivation and inspiration (2-6 sources recommended)", "items": {"type": "object", "required": ["source", "description", "strength"], "properties": {"source": {"type": "string", "description": "Source of inspiration (person, book, experience, philosophy, etc.)"}, "description": {"type": "string", "description": "How this source inspires the user and why it matters"}, "strength": {"type": "integer", "description": "How much this source inspires the user: 0=mild inspiration, 100=life changing"}, "category": {"type": "string", "description": "Category of inspiration (person, media, experience, philosophy, nature, etc.)"}, "reference_url": {"type": "string", "description": "URL reference if applicable (books, articles, videos)"}}}}, "skills": {"type": "array", "description": "Personal skills and capabilities (5-15 skills recommended)", "items": {"type": "object", "required": ["skill_code", "description", "level"], "properties": {"skill_code": {"type": "string", "description": "Skill code from database (11 valid codes)", "enum": ["communication", "soft_communication", "soft_empathy", "soft_introspection", "soft_philosophical", "soft_service", "tech_ai_concepts", "tech_coding_python", "tech_graphic_design", "tech_sailing", "tech_writing"]}, "description": {"type": "string", "description": "Personal description of how this skill manifests and is used"}, "level": {"type": "integer", "description": "Current skill level: 0=beginner, 50=intermediate, 100=expert"}, "user_awareness": {"type": "integer", "description": "How aware the user is of their skill level: 0=unaware, 100=very aware"}, "user_enjoyment": {"type": "integer", "description": "How much the user enjoys using this skill: 0=dislikes, 100=loves"}, "practice_frequency": {"type": "string", "description": "How often this skill is practiced (daily, weekly, monthly, rarely)"}, "acquisition_context": {"type": "string", "description": "How and where this skill was acquired (school, work, self-taught, etc.)"}}}}, "resources": {"type": "array", "description": "User's specific resources and tools (5-20 resources recommended). Each resource creates a UserResource object linked to a GenericResource via resource_code.", "items": {"type": "object", "required": ["specific_name", "resource_code"], "properties": {"specific_name": {"type": "string", "description": "User's specific name for this resource (e.g., 'My MacBook Pro', 'Dad's workshop', 'Local gym membership')"}, "resource_code": {"type": "string", "description": "Generic resource code that this user resource relates to. The user resource will be linked to a GenericResource with this code. Use 'other' codes for resources that don't fit specific categories.", "enum": ["tech_smartphone", "tech_computer", "tech_laptop", "tech_camera", "tech_other", "equip_yoga_mat", "equip_dumbbells", "equip_bicycle", "equip_other", "creative_art_supplies", "creative_musical_instrument", "creative_camera", "creative_other", "leisure_books", "leisure_games", "leisure_sports_equipment", "leisure_other", "home_kitchen_tools", "home_garden_tools", "home_workspace", "home_other", "access_gym_membership", "access_library", "access_internet", "access_other", "specialized_professional_tools", "specialized_other", "resource_other"]}, "location_details": {"type": "string", "description": "Where this resource is located or how it's accessed"}, "ownership_details": {"type": "string", "description": "How the user owns, rents, borrows, or accesses this resource"}, "availability": {"type": "string", "description": "When this resource is available for use"}, "condition": {"type": "string", "description": "Current condition or quality of the resource"}, "notes": {"type": "string", "description": "Additional relevant information about this resource"}}}}, "limitations": {"type": "array", "description": "Personal constraints and limitations (2-10 limitations recommended)", "items": {"type": "object", "required": ["limitation_code", "description", "severity"], "properties": {"limitation_code": {"type": "string", "description": "Limitation code from database (50 valid codes)", "enum": ["cog_attention", "cog_executive", "cog_learning", "cog_literacy", "cog_math", "cog_memory", "cog_processing", "env_allergens", "env_crowds", "env_light", "env_noise", "env_outdoor", "env_temperature", "phys_balance", "phys_cardiovascular", "phys_chronic_pain", "phys_dexterity", "phys_hearing", "phys_mobility_general", "phys_mobility_lower", "phys_mobility_upper", "phys_respiration", "phys_speech", "phys_stamina", "phys_strength", "phys_vision", "psych_anxiety", "psych_confidence", "psych_depression", "psych_emotional_regulation", "psych_motivation", "psych_social_anxiety", "psych_stress", "psych_trauma", "res_digital", "res_equipment", "res_financial", "res_space", "res_support", "res_transportation", "social_communication", "social_conflict", "social_group", "social_interpretation", "social_strangers", "time_duration", "time_evening", "time_morning", "time_regularity", "time_transitions"]}, "description": {"type": "string", "description": "Personal description of how this limitation affects the user"}, "severity": {"type": "integer", "description": "How severely this limitation impacts the user: 0=minor inconvenience, 100=major barrier"}, "frequency": {"type": "string", "description": "How often this limitation is encountered (constantly, daily, weekly, situational)"}, "coping_strategies": {"type": "string", "description": "How the user currently copes with or works around this limitation"}, "is_temporary": {"type": "boolean", "description": "Whether this limitation is expected to be temporary or permanent"}}}}, "preferences": {"type": "array", "description": "Personal preferences and patterns (8-20 preferences recommended)", "items": {"type": "object", "required": ["pref_name", "pref_description", "pref_strength"], "properties": {"pref_name": {"type": "string", "description": "Name of the preference or pattern"}, "pref_description": {"type": "string", "description": "Detailed description of the preference and how it manifests"}, "pref_strength": {"type": "integer", "description": "Strength of preference: -100=strong dislike, 0=neutral, 100=strong preference"}, "user_awareness": {"type": "integer", "description": "How aware the user is of this preference: 0=unconscious, 100=very conscious"}, "context": {"type": "string", "description": "Context where this preference applies (work, social, personal, etc.)"}}}}, "current_mood": {"type": "object", "required": ["energy_level", "stress_level", "optimism", "social_engagement"], "properties": {"energy_level": {"type": "integer", "description": "Current energy level: 0=exhausted, 50=normal, 100=highly energetic"}, "stress_level": {"type": "integer", "description": "Current stress level: 0=completely relaxed, 100=extremely stressed"}, "optimism": {"type": "integer", "description": "Current optimism about the future: 0=pessimistic, 100=very optimistic"}, "social_engagement": {"type": "integer", "description": "Current desire for social interaction: 0=want to be alone, 100=want lots of social contact"}, "mood_description": {"type": "string", "description": "Qualitative description of overall current mood and emotional state"}}}, "trust_level": {"type": "object", "required": ["value"], "properties": {"value": {"type": "number", "description": "Overall trust level with guidance systems: 0=no trust, 100=complete trust"}, "domain_scores": {"type": "object", "properties": {"goal_setting": {"type": "number", "description": "Trust in goal-setting guidance: 0=no trust, 100=complete trust"}, "activity_recommendation": {"type": "number", "description": "Trust in activity recommendations: 0=no trust, 100=complete trust"}, "personal_growth": {"type": "number", "description": "Trust in personal growth guidance: 0=no trust, 100=complete trust"}, "lifestyle_guidance": {"type": "number", "description": "Trust in lifestyle guidance: 0=no trust, 100=complete trust"}}}, "progression_notes": {"type": "string", "description": "Notes about how trust has developed over time and current factors"}}}}}