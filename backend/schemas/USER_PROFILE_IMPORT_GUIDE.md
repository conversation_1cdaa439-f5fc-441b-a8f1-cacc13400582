# User Profile Import System - Documentation

## Overview

The User Profile Import System enables comprehensive user profile creation through three methods:
- **JSON Upload**: Import complete profiles from structured JSON files
- **JSON Paste**: Paste JSON data directly into the interface
- **AI Generate**: Create profiles from questionnaire responses using AI

## Quick Start

### 1. Access the Import Interface
Navigate to: `/admin/user-profiles/` → **Import User Profile** section

### 2. Choose Your Method

#### Method A: JSON Upload
1. Click "📄 JSON Upload" tab
2. Drag & drop `.json` file or click to browse
3. Review the profile preview
4. Configure import options
5. Click "✅ Import Profile"

#### Method B: JSON Paste
1. Click "📝 JSON Paste" tab
2. Paste your JSON data
3. Validation feedback appears automatically
4. Review preview and import

#### Method C: AI Generate
1. Click "🤖 AI Generate" tab
2. Paste questionnaire responses or interview transcript
3. Configure generation options:
   - ✅ Include Archetype Analysis
   - ✅ Infer Environment Details  
   - ✅ Extract Goals and Aspirations
4. Click "🤖 Generate Profile"
5. Review generated profile and import

## JSON Structure Reference

### Minimal Profile Example
```json
{
  "user_account": {
    "username": "john_doe",
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe"
  },
  "profile_name": "John Doe",
  "is_real": true
}
```

### Complete Profile Structure
See: `backend/schemas/example_user_profile.json` for a comprehensive example.

## Key Data Sections

### 1. Demographics
```json
"demographics": {
  "full_name": "John Doe",
  "age": 28,
  "gender": "Male",
  "location": "Portland, Oregon",
  "language": "English",
  "occupation": "Software Developer"
}
```

### 2. Environments
```json
"environments": [
  {
    "environment_name": "Home Office",
    "environment_description": "Dedicated workspace in apartment",
    "is_current": true,
    "physical_properties": {
      "noise_level": 30,
      "light_quality": 85
    }
  }
]
```

### 3. Personality Traits (HEXACO Model)
```json
"traits": [
  {
    "trait_code": "open_creativity",
    "strength": 85,
    "awareness": 80
  }
]
```

### 4. Goals & Aspirations
```json
"aspirations": [
  {
    "title": "Start a creative agency",
    "description": "Build a design studio focusing on sustainable brands",
    "importance_according_user": 90,
    "horizon": "Long-term"
  }
]
```

## AI Generation Guide

### Input Requirements
- **Minimum**: 100 characters of questionnaire data
- **Maximum**: 50,000 characters
- **Format**: Plain text responses, interview transcripts, or structured questionnaires

### Sample Input
```
I'm a 29-year-old graphic designer living in Portland. I love creating art but struggle with the business side of freelancing. I'm very creative and open to new experiences, but I'm not very organized. I want to build a sustainable creative practice that doesn't compromise my artistic integrity. I work best in the mornings with natural light, and I prefer small, meaningful projects over large corporate work...
```

### Generation Options

| Option | Description | Impact |
|--------|-------------|---------|
| **Archetype Analysis** | Detailed HEXACO personality assessment | More accurate trait inclinations and behavioral predictions |
| **Environment Inference** | Deduce physical and social environment details | Better activity recommendations based on space/context |
| **Goals Extraction** | Identify explicit and implicit goals | More targeted development pathways |

## API Usage

### Import Profile
```bash
POST /admin/user-profiles/import/
Content-Type: application/json

{
  "profile_data": { /* complete profile object */ },
  "options": {
    "overwriteExisting": false,
    "validateBeforeImport": true,
    "createBackup": true
  }
}
```

### AI Generate Profile
```bash
POST /admin/user-profiles/ai-generate/
Content-Type: application/json

{
  "questionnaire_data": "User responses here...",
  "options": {
    "includeArchetypeAnalysis": true,
    "includeEnvironmentInference": true,
    "includeGoalsExtraction": true
  }
}
```

### Get Schema
```bash
GET /admin/user-profiles/schema/
```

## Import Options

### Overwrite Existing
- ✅ **Enabled**: Update existing users with same username/email
- ❌ **Disabled**: Error if user already exists

### Validate Before Import
- ✅ **Enabled**: Check data integrity before database operations
- ❌ **Disabled**: Skip validation (faster but riskier)

### Create Backup
- ✅ **Enabled**: Backup existing data before updates
- ❌ **Disabled**: No backup (not recommended)

## Common Workflows

### 1. Import Test Profiles for Development
```json
{
  "user_account": {
    "username": "test_creative_user",
    "email": "<EMAIL>"
  },
  "profile_name": "Creative Explorer Archetype",
  "is_real": false,
  "demographics": { /* test data */ }
}
```

### 2. Bulk Profile Creation
1. Prepare multiple JSON profiles
2. Use batch import via API
3. Monitor import status and warnings

### 3. AI-Assisted Profile Creation
1. Collect user questionnaire responses
2. Use AI generation with full options enabled
3. Review and manually adjust generated profile
4. Import final profile

## Validation & Error Handling

### Common Validation Errors
- **Missing required fields**: username, email, profile_name
- **Invalid trait codes**: Use only valid HEXACO trait codes
- **Multiple current environments**: Only one environment can be marked as current
- **Invalid date formats**: Use YYYY-MM-DD format
- **Numeric range violations**: Values must be within 0-100 for ratings

### Error Response Format
```json
{
  "success": false,
  "error": "Validation failed",
  "validation_errors": {
    "field_name": ["Error message"]
  }
}
```

## Best Practices

### 1. Data Quality
- ✅ Use realistic, consistent data
- ✅ Include multiple profile sections for rich personalization
- ✅ Set appropriate confidence levels for inferred data

### 2. AI Generation
- ✅ Provide detailed, honest questionnaire responses
- ✅ Include context about living situation, work, relationships
- ✅ Mention specific challenges, goals, and preferences
- ❌ Avoid overly brief or generic responses

### 3. Testing
- ✅ Start with `is_real: false` for test profiles
- ✅ Validate generated profiles before production use
- ✅ Test import with minimal data first, then add complexity

### 4. Performance
- ✅ Import large batches during off-peak hours
- ✅ Enable validation for data integrity
- ✅ Monitor import logs for warnings and errors

## Troubleshooting

### Import Fails
1. Check JSON format validity
2. Verify required fields are present
3. Ensure trait codes match database values
4. Check date formats (YYYY-MM-DD)

### AI Generation Issues
1. Verify OpenAI API key is configured
2. Check questionnaire data length (100-50k characters)
3. Review generation options
4. Check server logs for detailed errors

### Performance Issues
1. Reduce profile complexity for faster imports
2. Disable validation for trusted data sources
3. Import during low-traffic periods
4. Monitor database performance during bulk operations

## Schema Downloads

- **Complete OpenAPI Schema**: Click "📊 Download Schema" button
- **Example Profile**: `backend/schemas/example_user_profile.json`
- **LLM Prompt Template**: `backend/schemas/llm_profile_generation_prompt.md`

## Support

For technical issues:
1. Check browser console for JavaScript errors
2. Review Django logs for server-side errors
3. Validate JSON structure against schema
4. Test with minimal profile data first

For questions about profile structure or AI generation, consult the full documentation in the schemas directory.
