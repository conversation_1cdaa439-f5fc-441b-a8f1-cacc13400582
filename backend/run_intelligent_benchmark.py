#!/usr/bin/env python3
import os
import sys
import django
import json
import time
from datetime import datetime

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from apps.main.models import BenchmarkScenario, EvaluationCriteriaTemplate
from apps.user.models import UserProfile
from apps.main.tasks.benchmark_tasks import run_template_test

def run_benchmark_test(scenario_id, template_id, user_id, execution_mode='full-real', runs=1):
    mode_params = {
        'mock': {'use_real_llm': False, 'use_real_tools': False, 'use_real_db': False},
        'full-real': {'use_real_llm': True, 'use_real_tools': True, 'use_real_db': True}
    }
    
    execution_params = mode_params.get(execution_mode, mode_params['mock'])
    
    print(f'🚀 Starting benchmark test:')
    print(f'  Scenario ID: {scenario_id}')
    print(f'  Template ID: {template_id}')
    print(f'  User ID: {user_id}')
    print(f'  Execution Mode: {execution_mode}')
    
    try:
        result = run_template_test.delay(
            scenario_id=scenario_id,
            template_id=template_id,
            user_id=user_id,
            runs=runs,
            **execution_params
        )
        
        print(f'✅ Benchmark task started with ID: {result.id}')
        print(f'⏳ Waiting for completion...')
        
        start_time = time.time()
        timeout = 300
        
        while not result.ready():
            elapsed = time.time() - start_time
            if elapsed > timeout:
                print(f'❌ Timeout after {timeout} seconds')
                return None
                
            print(f'⏳ Still running... ({elapsed:.1f}s elapsed)')
            time.sleep(10)
        
        if result.successful():
            task_result = result.get()
            print(f'🎉 Benchmark completed successfully!')
            print(f'📊 Results: {task_result}')
            return task_result
        else:
            print(f'❌ Benchmark failed: {result.result}')
            return None
            
    except Exception as e:
        print(f'❌ Error running benchmark: {e}')
        return None

def main():
    print('🧠 Intelligent Benchmark Runner')
    print('=' * 50)
    
    # Get wheel generation scenario (ID 22)
    wheel_scenario_id = 22
    
    # Get wheel generation template (ID 3)
    wheel_template_id = 3
    
    # Get fake user (ID 2)
    fake_user_id = 2
    
    print(f'Selected Scenario ID: {wheel_scenario_id}')
    print(f'Selected Template ID: {wheel_template_id}')
    print(f'Selected User ID: {fake_user_id}')
    
    result = run_benchmark_test(
        scenario_id=wheel_scenario_id,
        template_id=wheel_template_id,
        user_id=fake_user_id,
        execution_mode='full-real',
        runs=1
    )
    
    if result:
        print(f'✅ Benchmark test completed successfully!')
    else:
        print(f'❌ Benchmark test failed!')

if __name__ == '__main__':
    main()
