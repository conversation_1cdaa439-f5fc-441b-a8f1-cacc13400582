// ACTIVE_FILE - 29-05-2025
/**
 * Scenario Detail Modal JavaScript
 * 
 * Provides functionality for viewing detailed scenario information
 * in a rich modal interface with tabs and formatted data display.
 */

// Global variable to store current scenario data
let currentScenarioData = null;

/**
 * Show the scenario detail modal for a specific scenario
 */
async function showScenarioDetailModal(scenarioId) {
    try {
        // Show loading state
        const modal = document.getElementById('scenario-detail-modal');
        if (!modal) {
            console.error('Scenario detail modal not found');
            return;
        }

        // Show modal with loading state
        modal.style.display = 'block';
        document.getElementById('scenario-detail-modal-title').textContent = 'Loading Scenario...';

        // Fetch scenario data
        const response = await fetch(`${window.BENCHMARK_SCENARIOS_API_URL}${scenarioId}/`);
        if (!response.ok) {
            throw new Error(`HTTP error ${response.status}`);
        }

        const data = await response.json();
        currentScenarioData = data.scenario;

        // Populate modal with scenario data
        populateScenarioDetailModal(currentScenarioData);

        // Load run history
        loadScenarioRunHistory(scenarioId);

    } catch (error) {
        console.error('Error loading scenario details:', error);
        showError(`Error loading scenario details: ${error.message}`);
        hideScenarioDetailModal();
    }
}

/**
 * Populate the modal with scenario data
 */
function populateScenarioDetailModal(scenario) {
    // Update modal title
    document.getElementById('scenario-detail-modal-title').textContent = `Scenario: ${scenario.name}`;

    // Basic information
    document.getElementById('scenario-detail-name').textContent = scenario.name;
    document.getElementById('scenario-detail-agent-role').textContent = scenario.agent_role;
    document.getElementById('scenario-detail-workflow-type').textContent =
        scenario.workflow_type || 'Not specified';
    document.getElementById('scenario-detail-status').textContent =
        scenario.is_active ? 'Active' : 'Inactive';
    document.getElementById('scenario-detail-version').textContent = `v${scenario.version}`;

    // Metadata summary (now using structured fields)
    document.getElementById('scenario-detail-warmup-runs').textContent =
        scenario.warmup_runs || 'Default (1)';
    document.getElementById('scenario-detail-benchmark-runs').textContent =
        scenario.benchmark_runs || 'Default (3)';
    document.getElementById('scenario-detail-timeout').textContent =
        scenario.timeout_seconds || 'Default';
    document.getElementById('scenario-detail-eval-template').textContent =
        scenario.evaluation_template_name || scenario.evaluation_template_id || 'Not specified';

    // Description
    document.getElementById('scenario-detail-description').textContent = scenario.description;

    // Tags
    const tagsContainer = document.getElementById('scenario-detail-tags');
    tagsContainer.innerHTML = '';
    if (scenario.tags && scenario.tags.length > 0) {
        scenario.tags.forEach(tag => {
            const tagBadge = document.createElement('span');
            tagBadge.className = 'tag-badge';
            tagBadge.textContent = tag.name;
            tagsContainer.appendChild(tagBadge);
        });
    } else {
        tagsContainer.innerHTML = '<span class="text-muted">No tags</span>';
    }

    // Input data
    document.getElementById('scenario-detail-input-data').textContent = 
        JSON.stringify(scenario.input_data, null, 2);

    // Metadata
    document.getElementById('scenario-detail-metadata').textContent = 
        JSON.stringify(scenario.metadata, null, 2);

    // Quality criteria (now using structured field)
    populateQualityCriteria(scenario.expected_quality_criteria);

    // Mock responses (now using structured field)
    populateMockResponses(scenario.mock_tool_responses);

    // Context (now using structured fields)
    populateContext(scenario.user_profile_context || scenario.activity_context || scenario.metadata?.context);
}

/**
 * Populate quality criteria tab
 */
function populateQualityCriteria(criteria) {
    const container = document.getElementById('scenario-detail-quality-criteria');
    
    if (!criteria) {
        container.innerHTML = '<p class="text-muted">No quality criteria specified</p>';
        return;
    }

    let html = '';
    if (typeof criteria === 'object') {
        for (const [dimension, criteriaList] of Object.entries(criteria)) {
            html += `<div class="criteria-dimension">
                <h5>${dimension}</h5>
                <ul>`;
            
            if (Array.isArray(criteriaList)) {
                criteriaList.forEach(criterion => {
                    html += `<li>${criterion}</li>`;
                });
            } else {
                html += `<li>${criteriaList}</li>`;
            }
            
            html += `</ul></div>`;
        }
    } else {
        html = `<pre>${JSON.stringify(criteria, null, 2)}</pre>`;
    }

    container.innerHTML = html;
}

/**
 * Populate mock responses tab
 */
function populateMockResponses(mockResponses) {
    const container = document.getElementById('scenario-detail-mock-responses');
    
    if (!mockResponses) {
        container.innerHTML = '<p class="text-muted">No mock responses specified</p>';
        return;
    }

    let html = '';
    if (typeof mockResponses === 'object') {
        for (const [tool, response] of Object.entries(mockResponses)) {
            html += `<div class="mock-response-item">
                <h5>Tool: ${tool}</h5>
                <pre class="mock-response-content">${JSON.stringify(response, null, 2)}</pre>
            </div>`;
        }
    } else {
        html = `<pre>${JSON.stringify(mockResponses, null, 2)}</pre>`;
    }

    container.innerHTML = html;
}

/**
 * Populate context tab
 */
function populateContext(context) {
    const container = document.getElementById('scenario-detail-context');
    
    if (!context) {
        container.innerHTML = '<p class="text-muted">No context specified</p>';
        return;
    }

    let html = '';
    if (typeof context === 'object') {
        // Format context nicely if it's structured
        if (context.trust_level !== undefined) {
            html += `<div class="context-item">
                <strong>Trust Level:</strong> ${context.trust_level}
            </div>`;
        }
        if (context.user_profile) {
            html += `<div class="context-item">
                <strong>User Profile:</strong>
                <pre>${JSON.stringify(context.user_profile, null, 2)}</pre>
            </div>`;
        }
        // Add other context fields as needed
        for (const [key, value] of Object.entries(context)) {
            if (key !== 'trust_level' && key !== 'user_profile') {
                html += `<div class="context-item">
                    <strong>${key}:</strong> ${typeof value === 'object' ? JSON.stringify(value, null, 2) : value}
                </div>`;
            }
        }
    } else {
        html = `<pre>${JSON.stringify(context, null, 2)}</pre>`;
    }

    container.innerHTML = html;
}

/**
 * Load scenario run history
 */
async function loadScenarioRunHistory(scenarioId) {
    const container = document.getElementById('scenario-detail-history');
    
    try {
        // This would need to be implemented in the backend
        // For now, show a placeholder
        container.innerHTML = `
            <p class="text-muted">Run history functionality will be implemented.</p>
            <p>This will show recent benchmark runs for scenario ID: ${scenarioId}</p>
        `;
    } catch (error) {
        console.error('Error loading run history:', error);
        container.innerHTML = '<p class="text-danger">Error loading run history</p>';
    }
}

/**
 * Hide the scenario detail modal
 */
function hideScenarioDetailModal() {
    const modal = document.getElementById('scenario-detail-modal');
    if (modal) {
        modal.style.display = 'none';
    }
    currentScenarioData = null;
}

/**
 * Edit scenario from detail modal
 */
function editScenarioFromDetail() {
    if (currentScenarioData) {
        hideScenarioDetailModal();
        editScenario(currentScenarioData.id);
    }
}

/**
 * Validate scenario from detail modal
 */
function validateScenarioFromDetail() {
    if (currentScenarioData) {
        validateScenario(currentScenarioData.id);
    }
}

/**
 * Duplicate scenario
 */
function duplicateScenario() {
    if (currentScenarioData) {
        // This would open the create modal with pre-filled data
        console.log('Duplicate scenario functionality to be implemented');
        showSuccess('Duplicate functionality will be implemented');
    }
}

/**
 * Export scenario
 */
function exportScenario() {
    if (currentScenarioData) {
        const dataStr = JSON.stringify(currentScenarioData, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});
        const url = URL.createObjectURL(dataBlob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = `scenario_${currentScenarioData.name.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.json`;
        link.click();
        
        URL.revokeObjectURL(url);
        showSuccess('Scenario exported successfully');
    }
}

// Close modal when clicking outside
document.addEventListener('click', function(event) {
    const modal = document.getElementById('scenario-detail-modal');
    if (event.target === modal) {
        hideScenarioDetailModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        const modal = document.getElementById('scenario-detail-modal');
        if (modal && modal.style.display === 'block') {
            hideScenarioDetailModal();
        }
    }
});
