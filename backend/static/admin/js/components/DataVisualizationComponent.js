/**
 * DataVisualizationComponent - Reusable data visualization component
 * 
 * This component provides various chart types and data visualization options
 * for displaying agent evaluation metrics, performance data, and analytics.
 * 
 * Features:
 * - Multiple chart types (bar, line, pie, radar, gauge)
 * - Interactive tooltips and legends
 * - Responsive design
 * - Real-time data updates
 * - Export functionality
 * - Customizable themes and colors
 */

class DataVisualizationComponent extends BaseAgentComponent {
    constructor() {
        super();
        
        // Chart instance
        this.chart = null;
        this.chartType = 'bar';
        this.chartData = null;
        this.chartOptions = {};
        
        // Visualization state
        this.vizState = {
            theme: 'default',
            animated: true,
            responsive: true,
            showLegend: true,
            showTooltips: true
        };
    }
    
    /**
     * Define observed attributes
     */
    static get observedAttributes() {
        return ['data', 'chart-type', 'theme', 'title', 'width', 'height'];
    }
    
    /**
     * Get default state
     */
    getDefaultState() {
        return {
            ...super.getDefaultState(),
            chartReady: false,
            dataLoaded: false
        };
    }
    
    /**
     * Initialize component
     */
    initialize() {
        super.initialize();
        
        // Load Chart.js if not available
        this.loadChartLibrary();
        
        // Parse chart configuration
        this.parseChartConfig();
    }
    
    /**
     * Load Chart.js library
     */
    loadChartLibrary() {
        if (typeof Chart === 'undefined') {
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/chart.js';
            script.onload = () => {
                this.state.chartReady = true;
                this.update();
            };
            document.head.appendChild(script);
        } else {
            this.state.chartReady = true;
        }
    }
    
    /**
     * Parse chart configuration from attributes
     */
    parseChartConfig() {
        this.chartType = this.getAttribute('chart-type') || 'bar';
        this.vizState.theme = this.getAttribute('theme') || 'default';
        
        // Parse data
        const dataAttr = this.getAttribute('data');
        if (dataAttr) {
            try {
                this.chartData = JSON.parse(dataAttr);
                this.state.dataLoaded = true;
            } catch (error) {
                console.error('Failed to parse chart data:', error);
                this.handleError('Invalid chart data format', error);
            }
        }
    }
    
    /**
     * Get component title
     */
    getTitle() {
        return this.getAttribute('title') || '📊 Data Visualization';
    }
    
    /**
     * Get action buttons
     */
    getActionButtons() {
        return `
            ${super.getActionButtons()}
            <select data-action="change-chart-type" class="btn">
                <option value="bar" ${this.chartType === 'bar' ? 'selected' : ''}>Bar Chart</option>
                <option value="line" ${this.chartType === 'line' ? 'selected' : ''}>Line Chart</option>
                <option value="pie" ${this.chartType === 'pie' ? 'selected' : ''}>Pie Chart</option>
                <option value="radar" ${this.chartType === 'radar' ? 'selected' : ''}>Radar Chart</option>
                <option value="doughnut" ${this.chartType === 'doughnut' ? 'selected' : ''}>Doughnut</option>
            </select>
            <button class="btn" data-action="export-chart">💾 Export</button>
        `;
    }
    
    /**
     * Get component content
     */
    getContent() {
        if (this.state.loading) {
            return '<div class="loading">Loading visualization...</div>';
        }
        
        if (this.state.error) {
            return `<div class="error">${this.state.error}</div>`;
        }
        
        if (!this.state.chartReady) {
            return '<div class="loading">Loading Chart.js library...</div>';
        }
        
        if (!this.state.dataLoaded) {
            return '<div class="no-data">No chart data available</div>';
        }
        
        const width = this.getAttribute('width') || '100%';
        const height = this.getAttribute('height') || '400px';
        
        return `
            <div class="chart-container" style="width: ${width}; height: ${height};">
                <canvas id="chart-canvas-${this.getInstanceId()}" width="400" height="200"></canvas>
            </div>
            <div class="chart-controls">
                ${this.renderChartControls()}
            </div>
            <div class="chart-stats">
                ${this.renderChartStats()}
            </div>
        `;
    }
    
    /**
     * Render chart controls
     */
    renderChartControls() {
        return `
            <div class="controls-grid">
                <label class="control-item">
                    <input type="checkbox" data-action="toggle-legend" ${this.vizState.showLegend ? 'checked' : ''}>
                    Show Legend
                </label>
                <label class="control-item">
                    <input type="checkbox" data-action="toggle-tooltips" ${this.vizState.showTooltips ? 'checked' : ''}>
                    Show Tooltips
                </label>
                <label class="control-item">
                    <input type="checkbox" data-action="toggle-animation" ${this.vizState.animated ? 'checked' : ''}>
                    Animations
                </label>
                <label class="control-item">
                    <select data-action="change-theme">
                        <option value="default" ${this.vizState.theme === 'default' ? 'selected' : ''}>Default Theme</option>
                        <option value="dark" ${this.vizState.theme === 'dark' ? 'selected' : ''}>Dark Theme</option>
                        <option value="colorful" ${this.vizState.theme === 'colorful' ? 'selected' : ''}>Colorful Theme</option>
                    </select>
                </label>
            </div>
        `;
    }
    
    /**
     * Render chart statistics
     */
    renderChartStats() {
        if (!this.chartData || !this.chartData.datasets) {
            return '';
        }
        
        const stats = this.calculateStats();
        
        return `
            <div class="stats-grid">
                <div class="stat-item">
                    <span class="stat-label">Data Points:</span>
                    <span class="stat-value">${stats.dataPoints}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Max Value:</span>
                    <span class="stat-value">${stats.maxValue}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Min Value:</span>
                    <span class="stat-value">${stats.minValue}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Average:</span>
                    <span class="stat-value">${stats.average}</span>
                </div>
            </div>
        `;
    }
    
    /**
     * Post-render hook
     */
    postRender() {
        super.postRender();
        
        if (this.state.chartReady && this.state.dataLoaded) {
            this.createChart();
        }
    }
    
    /**
     * Create chart instance
     */
    createChart() {
        const canvas = this.shadowRoot.querySelector(`#chart-canvas-${this.getInstanceId()}`);
        if (!canvas) {
            console.error('Chart canvas not found');
            return;
        }
        
        // Destroy existing chart
        if (this.chart) {
            this.chart.destroy();
        }
        
        const ctx = canvas.getContext('2d');
        
        // Prepare chart configuration
        const config = {
            type: this.chartType,
            data: this.prepareChartData(),
            options: this.getChartOptions()
        };
        
        try {
            this.chart = new Chart(ctx, config);
            console.log('Chart created successfully');
        } catch (error) {
            console.error('Failed to create chart:', error);
            this.handleError('Failed to create chart', error);
        }
    }
    
    /**
     * Prepare chart data with theme colors
     */
    prepareChartData() {
        const data = { ...this.chartData };
        
        // Apply theme colors
        if (data.datasets) {
            data.datasets = data.datasets.map((dataset, index) => ({
                ...dataset,
                backgroundColor: this.getThemeColors(index, 0.7),
                borderColor: this.getThemeColors(index, 1),
                borderWidth: 2
            }));
        }
        
        return data;
    }
    
    /**
     * Get chart options
     */
    getChartOptions() {
        const baseOptions = {
            responsive: this.vizState.responsive,
            maintainAspectRatio: false,
            animation: {
                duration: this.vizState.animated ? 1000 : 0
            },
            plugins: {
                legend: {
                    display: this.vizState.showLegend,
                    position: 'top'
                },
                tooltip: {
                    enabled: this.vizState.showTooltips
                }
            }
        };
        
        // Chart type specific options
        switch (this.chartType) {
            case 'bar':
                return {
                    ...baseOptions,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                };
            case 'line':
                return {
                    ...baseOptions,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    },
                    elements: {
                        point: {
                            radius: 4,
                            hoverRadius: 6
                        }
                    }
                };
            case 'pie':
            case 'doughnut':
                return {
                    ...baseOptions,
                    plugins: {
                        ...baseOptions.plugins,
                        legend: {
                            ...baseOptions.plugins.legend,
                            position: 'right'
                        }
                    }
                };
            case 'radar':
                return {
                    ...baseOptions,
                    scales: {
                        r: {
                            beginAtZero: true
                        }
                    }
                };
            default:
                return baseOptions;
        }
    }
    
    /**
     * Get theme colors
     */
    getThemeColors(index, alpha = 1) {
        const themes = {
            default: [
                `rgba(54, 162, 235, ${alpha})`,
                `rgba(255, 99, 132, ${alpha})`,
                `rgba(255, 205, 86, ${alpha})`,
                `rgba(75, 192, 192, ${alpha})`,
                `rgba(153, 102, 255, ${alpha})`,
                `rgba(255, 159, 64, ${alpha})`
            ],
            dark: [
                `rgba(100, 149, 237, ${alpha})`,
                `rgba(220, 20, 60, ${alpha})`,
                `rgba(255, 215, 0, ${alpha})`,
                `rgba(32, 178, 170, ${alpha})`,
                `rgba(138, 43, 226, ${alpha})`,
                `rgba(255, 140, 0, ${alpha})`
            ],
            colorful: [
                `rgba(255, 0, 128, ${alpha})`,
                `rgba(0, 255, 128, ${alpha})`,
                `rgba(128, 0, 255, ${alpha})`,
                `rgba(255, 128, 0, ${alpha})`,
                `rgba(0, 128, 255, ${alpha})`,
                `rgba(128, 255, 0, ${alpha})`
            ]
        };
        
        const themeColors = themes[this.vizState.theme] || themes.default;
        return themeColors[index % themeColors.length];
    }
    
    /**
     * Calculate chart statistics
     */
    calculateStats() {
        if (!this.chartData || !this.chartData.datasets) {
            return { dataPoints: 0, maxValue: 0, minValue: 0, average: 0 };
        }
        
        const allValues = [];
        this.chartData.datasets.forEach(dataset => {
            if (dataset.data) {
                allValues.push(...dataset.data);
            }
        });
        
        if (allValues.length === 0) {
            return { dataPoints: 0, maxValue: 0, minValue: 0, average: 0 };
        }
        
        const maxValue = Math.max(...allValues);
        const minValue = Math.min(...allValues);
        const average = allValues.reduce((sum, val) => sum + val, 0) / allValues.length;
        
        return {
            dataPoints: allValues.length,
            maxValue: maxValue.toFixed(2),
            minValue: minValue.toFixed(2),
            average: average.toFixed(2)
        };
    }
    
    /**
     * Handle component actions
     */
    handleAction(action, element) {
        switch (action) {
            case 'change-chart-type':
                this.chartType = element.value;
                this.createChart();
                break;
            case 'change-theme':
                this.vizState.theme = element.value;
                this.createChart();
                break;
            case 'toggle-legend':
                this.vizState.showLegend = element.checked;
                this.createChart();
                break;
            case 'toggle-tooltips':
                this.vizState.showTooltips = element.checked;
                this.createChart();
                break;
            case 'toggle-animation':
                this.vizState.animated = element.checked;
                this.createChart();
                break;
            case 'export-chart':
                this.exportChart();
                break;
            default:
                super.handleAction(action, element);
        }
    }
    
    /**
     * Export chart as image
     */
    exportChart() {
        if (this.chart) {
            const canvas = this.chart.canvas;
            const url = canvas.toDataURL('image/png');
            
            const link = document.createElement('a');
            link.href = url;
            link.download = `chart-${this.chartType}-${Date.now()}.png`;
            link.click();
        }
    }
    
    /**
     * Update chart data
     */
    updateChartData(newData) {
        this.chartData = newData;
        this.state.dataLoaded = true;
        
        if (this.chart) {
            this.chart.data = this.prepareChartData();
            this.chart.update();
        } else {
            this.createChart();
        }
        
        this.update();
    }
    
    /**
     * Get unique instance ID
     */
    getInstanceId() {
        if (!this._instanceId) {
            this._instanceId = Math.random().toString(36).substr(2, 9);
        }
        return this._instanceId;
    }
    
    /**
     * Cleanup component
     */
    cleanup() {
        if (this.chart) {
            this.chart.destroy();
            this.chart = null;
        }
        super.cleanup();
    }
    
    /**
     * Get component styles
     */
    getStyles() {
        return `
            ${super.getStyles()}
            
            .chart-container {
                position: relative;
                margin-bottom: 20px;
                background: white;
                border-radius: 8px;
                padding: 15px;
                border: 1px solid #e9ecef;
            }
            
            .chart-controls {
                background: #f8f9fa;
                border-radius: 6px;
                padding: 15px;
                margin-bottom: 15px;
            }
            
            .controls-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 15px;
            }
            
            .control-item {
                display: flex;
                align-items: center;
                gap: 8px;
                font-size: 0.9em;
                cursor: pointer;
            }
            
            .control-item input,
            .control-item select {
                margin: 0;
            }
            
            .chart-stats {
                background: #f8f9fa;
                border-radius: 6px;
                padding: 15px;
            }
            
            .stats-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
                gap: 15px;
            }
            
            .stat-item {
                display: flex;
                flex-direction: column;
                align-items: center;
                text-align: center;
            }
            
            .stat-label {
                font-size: 0.85em;
                color: #6c757d;
                margin-bottom: 4px;
            }
            
            .stat-value {
                font-size: 1.1em;
                font-weight: 600;
                color: #495057;
            }
            
            @media (max-width: 768px) {
                .controls-grid {
                    grid-template-columns: 1fr;
                }
                
                .stats-grid {
                    grid-template-columns: repeat(2, 1fr);
                }
            }
        `;
    }
}

// Register the component
if (window.componentRegistry) {
    window.componentRegistry.registerComponent('DataVisualization', DataVisualizationComponent, {
        tagName: 'agent-data-viz',
        dependencies: [],
        metadata: {
            description: 'Reusable data visualization component with multiple chart types',
            version: '1.0.0',
            author: 'Goali System'
        }
    });
}

// Export for module use
window.DataVisualizationComponent = DataVisualizationComponent;
