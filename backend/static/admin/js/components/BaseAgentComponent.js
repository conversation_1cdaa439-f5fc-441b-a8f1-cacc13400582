/**
 * BaseAgentComponent - Foundation class for all agent evaluation components
 * 
 * This class provides the core functionality for creating modular, reusable
 * agent evaluation UI components using vanilla Web Components.
 * 
 * Features:
 * - Template-based rendering with inheritance
 * - Data binding and reactive updates
 * - Event handling and component communication
 * - Styling encapsulation with Shadow DOM
 * - Error handling and debugging support
 */

class BaseAgentComponent extends HTMLElement {
    constructor() {
        super();
        
        // Create shadow DOM for style encapsulation
        this.attachShadow({ mode: 'open' });
        
        // Component state
        this.state = {};
        this.data = {};
        this.isInitialized = false;
        
        // Event listeners registry
        this.eventListeners = new Map();
        
        // Template cache
        this.templateCache = new Map();
        
        // Bind methods
        this.render = this.render.bind(this);
        this.update = this.update.bind(this);
        this.handleError = this.handleError.bind(this);
    }
    
    /**
     * Called when component is connected to DOM
     */
    connectedCallback() {
        try {
            this.initialize();
            this.render();
            this.setupEventListeners();
            this.isInitialized = true;
            
            // Emit ready event
            this.dispatchEvent(new CustomEvent('component-ready', {
                detail: { component: this.constructor.name }
            }));
        } catch (error) {
            this.handleError('Failed to initialize component', error);
        }
    }
    
    /**
     * Called when component is disconnected from DOM
     */
    disconnectedCallback() {
        this.cleanup();
    }
    
    /**
     * Called when observed attributes change
     */
    attributeChangedCallback(name, oldValue, newValue) {
        if (oldValue !== newValue) {
            this.handleAttributeChange(name, oldValue, newValue);
            if (this.isInitialized) {
                this.update();
            }
        }
    }
    
    /**
     * Initialize component - override in subclasses
     */
    initialize() {
        // Default initialization
        this.loadData();
        this.setupState();
    }
    
    /**
     * Load component data - override in subclasses
     */
    loadData() {
        // Extract data from attributes
        const dataAttr = this.getAttribute('data');
        if (dataAttr) {
            try {
                this.data = JSON.parse(dataAttr);
            } catch (error) {
                console.warn('Failed to parse data attribute:', error);
                this.data = {};
            }
        }
    }
    
    /**
     * Setup component state - override in subclasses
     */
    setupState() {
        this.state = {
            loading: false,
            error: null,
            expanded: false,
            ...this.getDefaultState()
        };
    }
    
    /**
     * Get default state - override in subclasses
     */
    getDefaultState() {
        return {};
    }
    
    /**
     * Render component - override in subclasses
     */
    render() {
        try {
            const template = this.getTemplate();
            const styles = this.getStyles();
            
            this.shadowRoot.innerHTML = `
                <style>${styles}</style>
                ${template}
            `;
            
            this.postRender();
        } catch (error) {
            this.handleError('Failed to render component', error);
        }
    }
    
    /**
     * Get component template - override in subclasses
     */
    getTemplate() {
        return `
            <div class="base-component">
                <div class="component-header">
                    <h3>${this.getTitle()}</h3>
                    <div class="component-actions">
                        ${this.getActionButtons()}
                    </div>
                </div>
                <div class="component-content">
                    ${this.getContent()}
                </div>
            </div>
        `;
    }
    
    /**
     * Get component styles - override in subclasses
     */
    getStyles() {
        return `
            :host {
                display: block;
                margin: 15px 0;
                border-radius: 8px;
                overflow: hidden;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                background: white;
            }
            
            .base-component {
                width: 100%;
            }
            
            .component-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 15px 20px;
                background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                border-bottom: 1px solid #dee2e6;
            }
            
            .component-header h3 {
                margin: 0;
                font-size: 1.1em;
                font-weight: 600;
                color: #495057;
            }
            
            .component-actions {
                display: flex;
                gap: 8px;
            }
            
            .component-content {
                padding: 20px;
            }
            
            .btn {
                padding: 4px 12px;
                border: 1px solid #dee2e6;
                background: white;
                color: #495057;
                border-radius: 4px;
                cursor: pointer;
                font-size: 0.85em;
                transition: all 0.2s;
            }
            
            .btn:hover {
                background: #f8f9fa;
                border-color: #adb5bd;
            }
            
            .btn.primary {
                background: #007bff;
                color: white;
                border-color: #007bff;
            }
            
            .btn.primary:hover {
                background: #0056b3;
                border-color: #0056b3;
            }
            
            .loading {
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 40px;
                color: #6c757d;
            }
            
            .error {
                padding: 15px;
                background: #f8d7da;
                color: #721c24;
                border: 1px solid #f5c6cb;
                border-radius: 4px;
                margin: 10px 0;
            }
            
            .hidden {
                display: none !important;
            }
        `;
    }
    
    /**
     * Get component title - override in subclasses
     */
    getTitle() {
        return this.getAttribute('title') || 'Agent Component';
    }
    
    /**
     * Get action buttons HTML - override in subclasses
     */
    getActionButtons() {
        return `
            <button class="btn" data-action="toggle">
                ${this.state.expanded ? '▼' : '▶'}
            </button>
            <button class="btn" data-action="refresh">🔄</button>
        `;
    }
    
    /**
     * Get component content - override in subclasses
     */
    getContent() {
        if (this.state.loading) {
            return '<div class="loading">Loading...</div>';
        }
        
        if (this.state.error) {
            return `<div class="error">${this.state.error}</div>`;
        }
        
        return '<div>No content available</div>';
    }
    
    /**
     * Post-render hook - override in subclasses
     */
    postRender() {
        // Setup component-specific event listeners
        this.setupComponentEventListeners();
    }
    
    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Global event listeners
        this.addEventListener('click', this.handleClick.bind(this));
    }
    
    /**
     * Setup component-specific event listeners - override in subclasses
     */
    setupComponentEventListeners() {
        // Override in subclasses
    }
    
    /**
     * Handle click events
     */
    handleClick(event) {
        const action = event.target.getAttribute('data-action');
        if (action) {
            event.preventDefault();
            event.stopPropagation();
            this.handleAction(action, event.target);
        }
    }
    
    /**
     * Handle component actions - override in subclasses
     */
    handleAction(action, element) {
        switch (action) {
            case 'toggle':
                this.toggle();
                break;
            case 'refresh':
                this.refresh();
                break;
            default:
                console.warn(`Unknown action: ${action}`);
        }
    }
    
    /**
     * Handle attribute changes - override in subclasses
     */
    handleAttributeChange(name, oldValue, newValue) {
        if (name === 'data') {
            this.loadData();
        }
    }
    
    /**
     * Update component
     */
    update() {
        if (this.isInitialized) {
            this.render();
        }
    }
    
    /**
     * Toggle component expanded state
     */
    toggle() {
        this.state.expanded = !this.state.expanded;
        this.update();
        
        this.dispatchEvent(new CustomEvent('component-toggle', {
            detail: { expanded: this.state.expanded }
        }));
    }
    
    /**
     * Refresh component data
     */
    refresh() {
        this.state.loading = true;
        this.update();
        
        // Simulate async refresh - override in subclasses
        setTimeout(() => {
            this.loadData();
            this.state.loading = false;
            this.update();
            
            this.dispatchEvent(new CustomEvent('component-refresh', {
                detail: { component: this.constructor.name }
            }));
        }, 500);
    }
    
    /**
     * Handle errors
     */
    handleError(message, error) {
        console.error(`${this.constructor.name}: ${message}`, error);
        this.state.error = message;
        this.state.loading = false;
        
        if (this.isInitialized) {
            this.update();
        }
        
        this.dispatchEvent(new CustomEvent('component-error', {
            detail: { message, error }
        }));
    }
    
    /**
     * Cleanup component
     */
    cleanup() {
        // Remove event listeners
        this.eventListeners.forEach((listener, element) => {
            element.removeEventListener(listener.event, listener.handler);
        });
        this.eventListeners.clear();
        
        // Clear template cache
        this.templateCache.clear();
    }
    
    /**
     * Utility method to safely get nested object properties
     */
    safeGet(obj, path, defaultValue = null) {
        return path.split('.').reduce((current, key) => {
            return current && current[key] !== undefined ? current[key] : defaultValue;
        }, obj);
    }
    
    /**
     * Utility method to format values for display
     */
    formatValue(value, type = 'auto') {
        if (value === null || value === undefined) {
            return 'N/A';
        }
        
        switch (type) {
            case 'number':
                return typeof value === 'number' ? value.toLocaleString() : value;
            case 'percentage':
                return typeof value === 'number' ? `${(value * 100).toFixed(1)}%` : value;
            case 'currency':
                return typeof value === 'number' ? `$${value.toFixed(4)}` : value;
            case 'duration':
                return typeof value === 'number' ? `${value.toFixed(2)}ms` : value;
            default:
                return value;
        }
    }
}

// Export for use in other modules
window.BaseAgentComponent = BaseAgentComponent;
