/**
 * BaseModalComponent - Foundation for modular agent evaluation modals
 * 
 * This class provides a reusable foundation for creating specialized agent evaluation
 * components using modern web component patterns with inheritance.
 * 
 * Features:
 * - Template-based rendering with inheritance
 * - Data binding and reactive updates
 * - Event handling and component communication
 * - Modular section management
 * - Error handling and debugging support
 */

class BaseModalComponent extends HTMLElement {
    constructor() {
        super();
        this.attachShadow({ mode: 'open' });
        this.data = null;
        this.runId = null;
        this.state = {
            expanded: true,
            loading: false,
            error: null
        };
        this.sections = new Map();
    }

    static get observedAttributes() {
        return ['data', 'run-id', 'expanded', 'loading'];
    }

    connectedCallback() {
        this.loadData();
        this.render();
        this.setupEventListeners();
    }

    attributeChangedCallback(name, oldValue, newValue) {
        if (oldValue !== newValue) {
            this.handleAttributeChange(name, oldValue, newValue);
        }
    }

    /**
     * Load data from attributes or properties
     */
    loadData() {
        try {
            const dataAttr = this.getAttribute('data');
            if (dataAttr) {
                this.data = JSON.parse(dataAttr);
            }
            
            this.runId = this.getAttribute('run-id');
            this.state.expanded = this.getAttribute('expanded') !== 'false';
            this.state.loading = this.getAttribute('loading') === 'true';
        } catch (error) {
            console.error('Failed to load component data:', error);
            this.state.error = error.message;
        }
    }

    /**
     * Main render method - calls template and style methods
     */
    render() {
        try {
            const template = this.getTemplate();
            const styles = this.getStyles();
            
            this.shadowRoot.innerHTML = `
                <style>${styles}</style>
                ${template}
            `;
            
            this.postRender();
        } catch (error) {
            this.handleError('Failed to render component', error);
        }
    }

    /**
     * Get component template - override in subclasses
     */
    getTemplate() {
        if (this.state.error) {
            return this.getErrorTemplate();
        }
        
        if (this.state.loading) {
            return this.getLoadingTemplate();
        }

        return `
            <div class="modal-component">
                <div class="component-header">
                    <h3>${this.getTitle()}</h3>
                    <div class="component-actions">
                        ${this.getActionButtons()}
                    </div>
                </div>
                <div class="component-content ${this.state.expanded ? 'expanded' : 'collapsed'}">
                    ${this.getContent()}
                </div>
            </div>
        `;
    }

    /**
     * Get component styles - override in subclasses to extend
     */
    getStyles() {
        return `
            :host {
                display: block;
                margin-bottom: 20px;
                border-radius: 8px;
                overflow: hidden;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            }

            .modal-component {
                background: white;
                border: 1px solid #e0e0e0;
            }

            .component-header {
                background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                padding: 15px 20px;
                border-bottom: 1px solid #dee2e6;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .component-header h3 {
                margin: 0;
                font-size: 1.1em;
                font-weight: 600;
                color: #495057;
            }

            .component-actions {
                display: flex;
                gap: 8px;
            }

            .action-btn {
                padding: 4px 8px;
                border: 1px solid #dee2e6;
                background: white;
                border-radius: 4px;
                cursor: pointer;
                font-size: 0.8em;
                transition: all 0.2s;
            }

            .action-btn:hover {
                background: #f8f9fa;
                border-color: #adb5bd;
            }

            .component-content {
                padding: 20px;
                transition: all 0.3s ease;
            }

            .component-content.collapsed {
                display: none;
            }

            .loading-spinner {
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 40px;
                color: #6c757d;
            }

            .error-display {
                background: #f8d7da;
                border: 1px solid #f5c6cb;
                color: #721c24;
                padding: 15px;
                border-radius: 4px;
                margin: 10px;
            }

            .section {
                margin-bottom: 20px;
                border: 1px solid #e9ecef;
                border-radius: 6px;
                overflow: hidden;
            }

            .section-header {
                background: #f8f9fa;
                padding: 12px 15px;
                border-bottom: 1px solid #e9ecef;
                cursor: pointer;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .section-header:hover {
                background: #e9ecef;
            }

            .section-title {
                font-weight: 600;
                color: #495057;
            }

            .section-toggle {
                font-size: 0.8em;
                color: #6c757d;
            }

            .section-content {
                padding: 15px;
                background: white;
            }

            .section-content.collapsed {
                display: none;
            }

            .metrics-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
                margin-bottom: 15px;
            }

            .metric-item {
                display: flex;
                justify-content: space-between;
                padding: 8px 12px;
                background: #f8f9fa;
                border-radius: 4px;
                border: 1px solid #e9ecef;
            }

            .metric-label {
                font-weight: 500;
                color: #6c757d;
            }

            .metric-value {
                font-weight: 600;
                color: #495057;
            }
        `;
    }

    /**
     * Get component title - override in subclasses
     */
    getTitle() {
        return 'Base Modal Component';
    }

    /**
     * Get action buttons - override in subclasses
     */
    getActionButtons() {
        return `
            <button class="action-btn" data-action="toggle">
                ${this.state.expanded ? '▼' : '▶'}
            </button>
            <button class="action-btn" data-action="refresh">🔄</button>
        `;
    }

    /**
     * Get component content - override in subclasses
     */
    getContent() {
        return '<p>Base component content - override getContent() in subclass</p>';
    }

    /**
     * Get loading template
     */
    getLoadingTemplate() {
        return `
            <div class="loading-spinner">
                <div>🔄 Loading component data...</div>
            </div>
        `;
    }

    /**
     * Get error template
     */
    getErrorTemplate() {
        return `
            <div class="error-display">
                <h4>Component Error</h4>
                <p>${this.state.error}</p>
                <button class="action-btn" data-action="retry">Retry</button>
            </div>
        `;
    }

    /**
     * Post-render hook - override in subclasses
     */
    postRender() {
        // Override in subclasses for post-render logic
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        this.shadowRoot.addEventListener('click', (e) => {
            const action = e.target.getAttribute('data-action');
            if (action) {
                this.handleAction(action, e.target);
            }
        });
    }

    /**
     * Handle component actions - override in subclasses
     */
    handleAction(action, element) {
        switch (action) {
            case 'toggle':
                this.toggle();
                break;
            case 'refresh':
                this.refresh();
                break;
            case 'retry':
                this.state.error = null;
                this.render();
                break;
            default:
                console.warn(`Unknown action: ${action}`);
        }
    }

    /**
     * Handle attribute changes - override in subclasses
     */
    handleAttributeChange(name, oldValue, newValue) {
        if (name === 'data') {
            this.loadData();
            this.render();
        }
    }

    /**
     * Toggle component expanded state
     */
    toggle() {
        this.state.expanded = !this.state.expanded;
        this.render();
        
        this.dispatchEvent(new CustomEvent('component-toggle', {
            detail: { expanded: this.state.expanded }
        }));
    }

    /**
     * Refresh component
     */
    refresh() {
        this.state.loading = true;
        this.render();
        
        // Simulate refresh - override in subclasses
        setTimeout(() => {
            this.state.loading = false;
            this.render();
        }, 1000);
    }

    /**
     * Handle errors
     */
    handleError(message, error) {
        console.error(message, error);
        this.state.error = `${message}: ${error.message}`;
        this.render();
    }

    /**
     * Create a collapsible section
     */
    createSection(title, content, expanded = true) {
        const sectionId = `section-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        
        return `
            <div class="section" data-section="${sectionId}">
                <div class="section-header" data-action="toggle-section" data-section="${sectionId}">
                    <span class="section-title">${title}</span>
                    <span class="section-toggle">${expanded ? '▼' : '▶'}</span>
                </div>
                <div class="section-content ${expanded ? '' : 'collapsed'}" data-section-content="${sectionId}">
                    ${content}
                </div>
            </div>
        `;
    }

    /**
     * Utility method to format data for display
     */
    formatValue(value, type = 'auto') {
        if (value === null || value === undefined) {
            return 'N/A';
        }

        switch (type) {
            case 'duration':
                return typeof value === 'number' ? `${value.toFixed(2)}ms` : value;
            case 'percentage':
                return typeof value === 'number' ? `${(value * 100).toFixed(1)}%` : value;
            case 'currency':
                return typeof value === 'number' ? `$${value.toFixed(4)}` : value;
            case 'number':
                return typeof value === 'number' ? value.toLocaleString() : value;
            default:
                return value.toString();
        }
    }
}

// Export for use in other modules
window.BaseModalComponent = BaseModalComponent;
