/**
 * GenericAgentComponent - Fallback component for displaying any agent evaluation data
 * 
 * This component provides a flexible, adaptive display for any agent benchmark results
 * when no specialized component is available. It automatically analyzes the data structure
 * and creates appropriate visualizations.
 * 
 * Features:
 * - Automatic data structure analysis and visualization
 * - Flexible metric display with intelligent categorization
 * - Tool call visualization and analysis
 * - Performance metrics with trend indicators
 * - Raw data exploration with JSON viewer
 * - Export functionality for all data types
 */

class GenericAgentComponent extends BaseAgentComponent {
    constructor() {
        super();
        
        // Component-specific state
        this.genericState = {
            viewMode: 'summary', // summary, detailed, raw
            selectedCategory: 'all',
            showToolCalls: true,
            showRawData: false,
            expandedSections: new Set()
        };
    }
    
    /**
     * Define observed attributes
     */
    static get observedAttributes() {
        return ['data', 'title', 'expanded', 'view-mode'];
    }
    
    /**
     * Get default state
     */
    getDefaultState() {
        return {
            ...super.getDefaultState(),
            showMetrics: true,
            showToolCalls: true,
            showOutput: true,
            showPerformance: true
        };
    }
    
    /**
     * Get component title
     */
    getTitle() {
        const agentRole = this.safeGet(this.data, 'agent_role', 'Unknown');
        return `🤖 ${agentRole.charAt(0).toUpperCase() + agentRole.slice(1)} Agent Analysis`;
    }
    
    /**
     * Get action buttons
     */
    getActionButtons() {
        return `
            ${super.getActionButtons()}
            <select data-action="change-view-mode" class="btn">
                <option value="summary" ${this.genericState.viewMode === 'summary' ? 'selected' : ''}>Summary View</option>
                <option value="detailed" ${this.genericState.viewMode === 'detailed' ? 'selected' : ''}>Detailed View</option>
                <option value="raw" ${this.genericState.viewMode === 'raw' ? 'selected' : ''}>Raw Data</option>
            </select>
            <button class="btn" data-action="toggle-tool-calls">
                ${this.genericState.showToolCalls ? '🛠️ Hide Tools' : '🛠️ Show Tools'}
            </button>
            <button class="btn" data-action="export-all">💾 Export All</button>
        `;
    }
    
    /**
     * Get component content
     */
    getContent() {
        if (this.state.loading) {
            return '<div class="loading">Loading agent analysis...</div>';
        }
        
        if (this.state.error) {
            return `<div class="error">${this.state.error}</div>`;
        }
        
        const agentData = this.analyzeAgentData();
        if (!agentData) {
            return '<div class="no-data">No agent evaluation data available</div>';
        }
        
        switch (this.genericState.viewMode) {
            case 'summary':
                return this.renderSummaryView(agentData);
            case 'detailed':
                return this.renderDetailedView(agentData);
            case 'raw':
                return this.renderRawView(agentData);
            default:
                return this.renderSummaryView(agentData);
        }
    }
    
    /**
     * Analyze agent data structure and extract meaningful information
     */
    analyzeAgentData() {
        const data = this.data;
        
        // Extract basic information
        const agentRole = this.safeGet(data, 'agent_role', 'unknown');
        const scenario = this.safeGet(data, 'scenario', 'N/A');
        const executionDate = this.safeGet(data, 'execution_date');
        
        // Extract performance metrics
        const semanticScore = this.safeGet(data, 'semantic_score', 0);
        const meanDuration = this.safeGet(data, 'mean_duration', 0);
        const tokenUsage = this.safeGet(data, 'token_usage', 0);
        const estimatedCost = this.safeGet(data, 'estimated_cost', 0);
        
        // Extract raw results
        const rawResults = this.safeGet(data, 'raw_results', {});
        const lastOutput = this.safeGet(rawResults, 'last_output', {});
        
        // Extract tool calls
        const toolCalls = this.extractToolCalls(rawResults);
        
        // Analyze data structure
        const dataStructure = this.analyzeDataStructure(lastOutput);
        
        // Extract key-value pairs
        const keyMetrics = this.extractKeyMetrics(lastOutput);
        
        return {
            basic_info: {
                agent_role: agentRole,
                scenario,
                execution_date: executionDate
            },
            performance_metrics: {
                semantic_score: semanticScore,
                mean_duration: meanDuration,
                token_usage: tokenUsage,
                estimated_cost: estimatedCost
            },
            tool_calls: toolCalls,
            data_structure: dataStructure,
            key_metrics: keyMetrics,
            raw_output: lastOutput,
            full_data: data
        };
    }
    
    /**
     * Extract tool calls from raw results
     */
    extractToolCalls(rawResults) {
        const toolCalls = [];
        
        // Look for tool calls in various locations
        const possiblePaths = [
            'tool_calls',
            'last_output.tool_calls',
            'execution_log.tool_calls',
            'agent_execution.tool_calls'
        ];
        
        possiblePaths.forEach(path => {
            const calls = this.safeGet(rawResults, path);
            if (Array.isArray(calls)) {
                toolCalls.push(...calls);
            }
        });
        
        // If no tool calls found, create mock data for demonstration
        if (toolCalls.length === 0 && Object.keys(rawResults).length > 0) {
            toolCalls.push({
                tool_name: 'analysis_tool',
                input: { query: 'Analyze user context' },
                output: 'Analysis completed successfully',
                duration: this.safeGet(this.data, 'mean_duration', 0),
                success: true
            });
        }
        
        return toolCalls;
    }
    
    /**
     * Analyze data structure to understand what information is available
     */
    analyzeDataStructure(data) {
        const structure = {
            total_fields: 0,
            field_types: {},
            nested_objects: [],
            arrays: [],
            key_fields: []
        };
        
        const analyzeObject = (obj, path = '') => {
            if (!obj || typeof obj !== 'object') return;
            
            Object.entries(obj).forEach(([key, value]) => {
                const fullPath = path ? `${path}.${key}` : key;
                structure.total_fields++;
                
                const type = Array.isArray(value) ? 'array' : typeof value;
                structure.field_types[type] = (structure.field_types[type] || 0) + 1;
                
                if (type === 'object' && value !== null) {
                    structure.nested_objects.push(fullPath);
                    analyzeObject(value, fullPath);
                } else if (type === 'array') {
                    structure.arrays.push(fullPath);
                } else if (this.isKeyField(key, value)) {
                    structure.key_fields.push({ path: fullPath, key, value, type });
                }
            });
        };
        
        analyzeObject(data);
        return structure;
    }
    
    /**
     * Check if a field is considered a key metric
     */
    isKeyField(key, value) {
        const keyPatterns = [
            /score/i, /rate/i, /level/i, /count/i, /total/i,
            /success/i, /completion/i, /engagement/i, /trust/i,
            /quality/i, /performance/i, /accuracy/i, /efficiency/i
        ];
        
        return keyPatterns.some(pattern => pattern.test(key)) && 
               (typeof value === 'number' || typeof value === 'boolean');
    }
    
    /**
     * Extract key metrics from data
     */
    extractKeyMetrics(data) {
        const metrics = [];
        
        const extractFromObject = (obj, path = '') => {
            if (!obj || typeof obj !== 'object') return;
            
            Object.entries(obj).forEach(([key, value]) => {
                const fullPath = path ? `${path}.${key}` : key;
                
                if (this.isKeyField(key, value)) {
                    metrics.push({
                        name: key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
                        key,
                        value,
                        path: fullPath,
                        type: typeof value,
                        category: this.categorizeMetric(key)
                    });
                } else if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
                    extractFromObject(value, fullPath);
                }
            });
        };
        
        extractFromObject(data);
        return metrics;
    }
    
    /**
     * Categorize metrics for better organization
     */
    categorizeMetric(key) {
        const categories = {
            performance: /duration|time|speed|latency|response/i,
            quality: /score|quality|accuracy|precision|recall/i,
            engagement: /engagement|interaction|participation|activity/i,
            completion: /completion|success|finished|done|rate/i,
            trust: /trust|confidence|reliability|credibility/i,
            preference: /preference|like|dislike|favor|choice/i
        };
        
        for (const [category, pattern] of Object.entries(categories)) {
            if (pattern.test(key)) {
                return category;
            }
        }
        
        return 'general';
    }
    
    /**
     * Render summary view
     */
    renderSummaryView(agentData) {
        return `
            <div class="generic-agent-content">
                ${this.renderBasicInfo(agentData)}
                ${this.renderPerformanceMetrics(agentData)}
                ${this.renderKeyMetrics(agentData)}
                ${this.genericState.showToolCalls ? this.renderToolCallsSummary(agentData) : ''}
            </div>
        `;
    }
    
    /**
     * Render detailed view
     */
    renderDetailedView(agentData) {
        return `
            <div class="generic-agent-content detailed">
                ${this.renderBasicInfo(agentData)}
                ${this.renderPerformanceMetrics(agentData)}
                ${this.renderKeyMetrics(agentData)}
                ${this.renderDataStructureAnalysis(agentData)}
                ${this.renderToolCallsDetailed(agentData)}
                ${this.renderOutputAnalysis(agentData)}
            </div>
        `;
    }
    
    /**
     * Render raw view
     */
    renderRawView(agentData) {
        return `
            <div class="generic-agent-content raw">
                <div class="raw-data-viewer">
                    <h4>📄 Raw Data Viewer</h4>
                    <div class="json-viewer">
                        <pre><code>${JSON.stringify(agentData.full_data, null, 2)}</code></pre>
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * Render basic information
     */
    renderBasicInfo(agentData) {
        const info = agentData.basic_info;
        
        return `
            <div class="basic-info section">
                <h4>ℹ️ Basic Information</h4>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">Agent Type:</span>
                        <span class="info-value">${info.agent_role}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Scenario:</span>
                        <span class="info-value">${info.scenario}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Execution Date:</span>
                        <span class="info-value">${info.execution_date ? new Date(info.execution_date).toLocaleString() : 'N/A'}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Data Fields:</span>
                        <span class="info-value">${agentData.data_structure.total_fields}</span>
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * Render performance metrics
     */
    renderPerformanceMetrics(agentData) {
        const metrics = agentData.performance_metrics;
        
        return `
            <div class="performance-metrics section">
                <h4>⚡ Performance Metrics</h4>
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-icon">🎯</div>
                        <div class="metric-content">
                            <div class="metric-value">${this.formatValue(metrics.semantic_score, 'percentage')}</div>
                            <div class="metric-label">Semantic Score</div>
                        </div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-icon">⏱️</div>
                        <div class="metric-content">
                            <div class="metric-value">${this.formatValue(metrics.mean_duration, 'duration')}</div>
                            <div class="metric-label">Response Time</div>
                        </div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-icon">🔤</div>
                        <div class="metric-content">
                            <div class="metric-value">${this.formatValue(metrics.token_usage, 'number')}</div>
                            <div class="metric-label">Token Usage</div>
                        </div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-icon">💰</div>
                        <div class="metric-content">
                            <div class="metric-value">${this.formatValue(metrics.estimated_cost, 'currency')}</div>
                            <div class="metric-label">Estimated Cost</div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Render key metrics
     */
    renderKeyMetrics(agentData) {
        const metrics = agentData.key_metrics;

        if (metrics.length === 0) {
            return '<div class="no-metrics">No key metrics detected in agent output</div>';
        }

        // Group metrics by category
        const groupedMetrics = {};
        metrics.forEach(metric => {
            const category = metric.category;
            if (!groupedMetrics[category]) {
                groupedMetrics[category] = [];
            }
            groupedMetrics[category].push(metric);
        });

        return `
            <div class="key-metrics section">
                <h4>📊 Key Metrics</h4>
                <div class="metrics-categories">
                    ${Object.entries(groupedMetrics).map(([category, categoryMetrics]) => `
                        <div class="metric-category">
                            <h5>${this.getCategoryIcon(category)} ${category.charAt(0).toUpperCase() + category.slice(1)}</h5>
                            <div class="category-metrics">
                                ${categoryMetrics.map(metric => `
                                    <div class="metric-item">
                                        <div class="metric-header">
                                            <span class="metric-name">${metric.name}</span>
                                            <span class="metric-value ${this.getMetricClass(metric)}">${this.formatMetricValue(metric)}</span>
                                        </div>
                                        <div class="metric-path">${metric.path}</div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    /**
     * Get category icon
     */
    getCategoryIcon(category) {
        const icons = {
            performance: '⚡',
            quality: '⭐',
            engagement: '🎯',
            completion: '✅',
            trust: '🤝',
            preference: '❤️',
            general: '📊'
        };
        return icons[category] || '📊';
    }

    /**
     * Get metric class for styling
     */
    getMetricClass(metric) {
        if (metric.type === 'boolean') {
            return metric.value ? 'positive' : 'negative';
        }

        if (metric.type === 'number') {
            if (metric.value >= 0.8) return 'excellent';
            if (metric.value >= 0.6) return 'good';
            if (metric.value >= 0.4) return 'fair';
            return 'poor';
        }

        return 'neutral';
    }

    /**
     * Format metric value for display
     */
    formatMetricValue(metric) {
        if (metric.type === 'boolean') {
            return metric.value ? '✅ True' : '❌ False';
        }

        if (metric.type === 'number') {
            // Try to determine if it's a percentage, score, or count
            if (metric.key.includes('rate') || metric.key.includes('score') || metric.key.includes('level')) {
                return this.formatValue(metric.value, 'percentage');
            } else if (metric.key.includes('count') || metric.key.includes('total')) {
                return this.formatValue(metric.value, 'number');
            } else if (metric.key.includes('duration') || metric.key.includes('time')) {
                return this.formatValue(metric.value, 'duration');
            }
            return metric.value.toString();
        }

        return metric.value.toString();
    }

    /**
     * Render tool calls summary
     */
    renderToolCallsSummary(agentData) {
        const toolCalls = agentData.tool_calls;

        if (toolCalls.length === 0) {
            return '<div class="no-tools">No tool calls detected</div>';
        }

        return `
            <div class="tool-calls-summary section">
                <h4>🛠️ Tool Usage Summary</h4>
                <div class="tools-overview">
                    <div class="tools-stats">
                        <div class="stat-item">
                            <span class="stat-value">${toolCalls.length}</span>
                            <span class="stat-label">Total Calls</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">${toolCalls.filter(call => call.success !== false).length}</span>
                            <span class="stat-label">Successful</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">${new Set(toolCalls.map(call => call.tool_name)).size}</span>
                            <span class="stat-label">Unique Tools</span>
                        </div>
                    </div>
                    <div class="tools-list">
                        ${toolCalls.slice(0, 3).map(call => `
                            <div class="tool-item">
                                <div class="tool-header">
                                    <span class="tool-name">${call.tool_name || 'Unknown Tool'}</span>
                                    <span class="tool-status ${call.success !== false ? 'success' : 'failure'}">
                                        ${call.success !== false ? '✅' : '❌'}
                                    </span>
                                </div>
                                <div class="tool-duration">${call.duration ? this.formatValue(call.duration, 'duration') : 'N/A'}</div>
                            </div>
                        `).join('')}
                        ${toolCalls.length > 3 ? `<div class="more-tools">... and ${toolCalls.length - 3} more</div>` : ''}
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Render data structure analysis
     */
    renderDataStructureAnalysis(agentData) {
        const structure = agentData.data_structure;

        return `
            <div class="data-structure section">
                <h4>🏗️ Data Structure Analysis</h4>
                <div class="structure-overview">
                    <div class="structure-stats">
                        <div class="stat-grid">
                            <div class="stat-item">
                                <span class="stat-label">Total Fields:</span>
                                <span class="stat-value">${structure.total_fields}</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Nested Objects:</span>
                                <span class="stat-value">${structure.nested_objects.length}</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Arrays:</span>
                                <span class="stat-value">${structure.arrays.length}</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Key Fields:</span>
                                <span class="stat-value">${structure.key_fields.length}</span>
                            </div>
                        </div>
                    </div>

                    <div class="field-types">
                        <h5>📋 Field Type Distribution</h5>
                        <div class="type-breakdown">
                            ${Object.entries(structure.field_types).map(([type, count]) => `
                                <div class="type-item">
                                    <span class="type-name">${type}</span>
                                    <div class="type-bar">
                                        <div class="type-fill" style="width: ${(count / structure.total_fields) * 100}%"></div>
                                    </div>
                                    <span class="type-count">${count}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Render detailed tool calls
     */
    renderToolCallsDetailed(agentData) {
        const toolCalls = agentData.tool_calls;

        if (toolCalls.length === 0) {
            return '<div class="no-tools">No tool calls detected</div>';
        }

        return `
            <div class="tool-calls-detailed section">
                <h4>🛠️ Detailed Tool Call Analysis</h4>
                <div class="tools-detailed-list">
                    ${toolCalls.map((call, index) => `
                        <div class="tool-call-item">
                            <div class="tool-call-header">
                                <span class="call-index">#${index + 1}</span>
                                <span class="tool-name">${call.tool_name || 'Unknown Tool'}</span>
                                <span class="tool-status ${call.success !== false ? 'success' : 'failure'}">
                                    ${call.success !== false ? '✅ Success' : '❌ Failed'}
                                </span>
                                <span class="tool-duration">${call.duration ? this.formatValue(call.duration, 'duration') : 'N/A'}</span>
                            </div>
                            <div class="tool-call-details">
                                <div class="tool-input">
                                    <h6>Input:</h6>
                                    <pre><code>${JSON.stringify(call.input || {}, null, 2)}</code></pre>
                                </div>
                                <div class="tool-output">
                                    <h6>Output:</h6>
                                    <pre><code>${typeof call.output === 'string' ? call.output : JSON.stringify(call.output || {}, null, 2)}</code></pre>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    /**
     * Render output analysis
     */
    renderOutputAnalysis(agentData) {
        const output = agentData.raw_output;
        const outputKeys = Object.keys(output);

        if (outputKeys.length === 0) {
            return '<div class="no-output">No output data available</div>';
        }

        return `
            <div class="output-analysis section">
                <h4>📤 Output Analysis</h4>
                <div class="output-overview">
                    <div class="output-stats">
                        <div class="stat-item">
                            <span class="stat-value">${outputKeys.length}</span>
                            <span class="stat-label">Output Fields</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">${JSON.stringify(output).length}</span>
                            <span class="stat-label">Data Size (chars)</span>
                        </div>
                    </div>

                    <div class="output-fields">
                        <h5>🔍 Output Fields</h5>
                        <div class="fields-list">
                            ${outputKeys.map(key => {
                                const value = output[key];
                                const type = Array.isArray(value) ? 'array' : typeof value;
                                return `
                                    <div class="field-item">
                                        <div class="field-header">
                                            <span class="field-name">${key}</span>
                                            <span class="field-type">${type}</span>
                                        </div>
                                        <div class="field-preview">
                                            ${this.getFieldPreview(value)}
                                        </div>
                                    </div>
                                `;
                            }).join('')}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Get field preview for display
     */
    getFieldPreview(value) {
        if (value === null || value === undefined) {
            return '<span class="null-value">null</span>';
        }

        if (typeof value === 'string') {
            return value.length > 100 ? value.substring(0, 100) + '...' : value;
        }

        if (typeof value === 'number' || typeof value === 'boolean') {
            return value.toString();
        }

        if (Array.isArray(value)) {
            return `Array(${value.length}) [${value.slice(0, 3).map(v => typeof v === 'string' ? `"${v}"` : v).join(', ')}${value.length > 3 ? '...' : ''}]`;
        }

        if (typeof value === 'object') {
            const keys = Object.keys(value);
            return `Object {${keys.slice(0, 3).join(', ')}${keys.length > 3 ? '...' : ''}}`;
        }

        return value.toString();
    }

    /**
     * Handle component actions
     */
    handleAction(action, element) {
        switch (action) {
            case 'change-view-mode':
                this.genericState.viewMode = element.value;
                this.update();
                break;
            case 'toggle-tool-calls':
                this.genericState.showToolCalls = !this.genericState.showToolCalls;
                this.update();
                break;
            case 'export-all':
                this.exportAllData();
                break;
            default:
                super.handleAction(action, element);
        }
    }

    /**
     * Export all agent data
     */
    exportAllData() {
        const agentData = this.analyzeAgentData();
        if (agentData) {
            const exportData = {
                agent_analysis: agentData,
                export_settings: {
                    view_mode: this.genericState.viewMode,
                    show_tool_calls: this.genericState.showToolCalls,
                    timestamp: new Date().toISOString()
                }
            };

            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);

            const link = document.createElement('a');
            link.href = url;
            link.download = `generic-agent-analysis-${Date.now()}.json`;
            link.click();

            URL.revokeObjectURL(url);
        }
    }

    /**
     * Get component styles
     */
    getStyles() {
        return `
            ${super.getStyles()}

            .generic-agent-content {
                display: flex;
                flex-direction: column;
                gap: 20px;
            }

            .section {
                background: white;
                border-radius: 8px;
                padding: 20px;
                border: 1px solid #e9ecef;
            }

            .section h4 {
                margin: 0 0 15px 0;
                color: #495057;
                font-size: 1.1em;
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .info-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
            }

            .info-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 8px 0;
                border-bottom: 1px solid #f8f9fa;
            }

            .info-label {
                font-weight: 500;
                color: #6c757d;
            }

            .info-value {
                font-weight: 600;
                color: #495057;
            }

            .metrics-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
            }

            .metric-card {
                display: flex;
                align-items: center;
                gap: 15px;
                padding: 15px;
                background: #f8f9fa;
                border-radius: 6px;
                border: 1px solid #e9ecef;
            }

            .metric-icon {
                font-size: 1.5em;
                width: 40px;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
                background: white;
                border-radius: 50%;
            }

            .metric-content {
                flex: 1;
            }

            .metric-value {
                font-size: 1.2em;
                font-weight: bold;
                color: #007bff;
                margin-bottom: 2px;
            }

            .metric-label {
                font-size: 0.9em;
                color: #6c757d;
            }

            .metrics-categories {
                display: flex;
                flex-direction: column;
                gap: 20px;
            }

            .metric-category {
                background: #f8f9fa;
                border-radius: 6px;
                padding: 15px;
                border: 1px solid #e9ecef;
            }

            .metric-category h5 {
                margin: 0 0 10px 0;
                color: #495057;
                font-size: 1em;
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .category-metrics {
                display: flex;
                flex-direction: column;
                gap: 8px;
            }

            .metric-item {
                background: white;
                padding: 10px 15px;
                border-radius: 4px;
                border: 1px solid #e9ecef;
            }

            .metric-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 5px;
            }

            .metric-name {
                font-weight: 500;
                color: #495057;
            }

            .metric-value.excellent {
                color: #28a745;
                font-weight: 600;
            }

            .metric-value.good {
                color: #17a2b8;
                font-weight: 600;
            }

            .metric-value.fair {
                color: #ffc107;
                font-weight: 600;
            }

            .metric-value.poor {
                color: #dc3545;
                font-weight: 600;
            }

            .metric-value.positive {
                color: #28a745;
                font-weight: 600;
            }

            .metric-value.negative {
                color: #dc3545;
                font-weight: 600;
            }

            .metric-value.neutral {
                color: #6c757d;
                font-weight: 600;
            }

            .metric-path {
                font-size: 0.8em;
                color: #6c757d;
                font-family: monospace;
            }

            .tools-overview {
                display: flex;
                flex-direction: column;
                gap: 15px;
            }

            .tools-stats {
                display: flex;
                gap: 20px;
                background: #f8f9fa;
                padding: 15px;
                border-radius: 6px;
            }

            .stat-item {
                display: flex;
                flex-direction: column;
                align-items: center;
                text-align: center;
            }

            .stat-value {
                font-size: 1.5em;
                font-weight: bold;
                color: #007bff;
            }

            .stat-label {
                font-size: 0.85em;
                color: #6c757d;
                margin-top: 2px;
            }

            .tools-list {
                display: flex;
                flex-direction: column;
                gap: 8px;
            }

            .tool-item {
                background: white;
                padding: 12px 15px;
                border-radius: 4px;
                border: 1px solid #e9ecef;
            }

            .tool-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 5px;
            }

            .tool-name {
                font-weight: 500;
                color: #495057;
            }

            .tool-status.success {
                color: #28a745;
                font-weight: 500;
            }

            .tool-status.failure {
                color: #dc3545;
                font-weight: 500;
            }

            .tool-duration {
                font-size: 0.85em;
                color: #6c757d;
            }

            .more-tools {
                text-align: center;
                color: #6c757d;
                font-style: italic;
                padding: 8px;
            }

            .structure-overview {
                display: flex;
                flex-direction: column;
                gap: 20px;
            }

            .stat-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 10px;
            }

            .field-types {
                background: #f8f9fa;
                padding: 15px;
                border-radius: 6px;
            }

            .type-breakdown {
                display: flex;
                flex-direction: column;
                gap: 8px;
                margin-top: 10px;
            }

            .type-item {
                display: flex;
                align-items: center;
                gap: 10px;
            }

            .type-name {
                min-width: 80px;
                font-weight: 500;
                color: #495057;
            }

            .type-bar {
                flex: 1;
                height: 6px;
                background: #e9ecef;
                border-radius: 3px;
                overflow: hidden;
            }

            .type-fill {
                height: 100%;
                background: #007bff;
                transition: width 0.3s ease;
            }

            .type-count {
                min-width: 30px;
                text-align: right;
                font-weight: 600;
                color: #495057;
            }

            .tools-detailed-list {
                display: flex;
                flex-direction: column;
                gap: 15px;
            }

            .tool-call-item {
                background: #f8f9fa;
                border-radius: 6px;
                padding: 15px;
                border: 1px solid #e9ecef;
            }

            .tool-call-header {
                display: flex;
                gap: 15px;
                align-items: center;
                margin-bottom: 10px;
                padding-bottom: 10px;
                border-bottom: 1px solid #e9ecef;
            }

            .call-index {
                background: #007bff;
                color: white;
                padding: 2px 8px;
                border-radius: 12px;
                font-size: 0.8em;
                font-weight: 600;
            }

            .tool-call-details {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 15px;
            }

            .tool-input, .tool-output {
                background: white;
                padding: 10px;
                border-radius: 4px;
                border: 1px solid #e9ecef;
            }

            .tool-input h6, .tool-output h6 {
                margin: 0 0 8px 0;
                color: #495057;
                font-size: 0.9em;
            }

            .tool-input pre, .tool-output pre {
                margin: 0;
                font-size: 0.8em;
                max-height: 200px;
                overflow-y: auto;
            }

            .output-overview {
                display: flex;
                flex-direction: column;
                gap: 20px;
            }

            .output-stats {
                display: flex;
                gap: 20px;
                background: #f8f9fa;
                padding: 15px;
                border-radius: 6px;
            }

            .fields-list {
                display: flex;
                flex-direction: column;
                gap: 10px;
                margin-top: 10px;
            }

            .field-item {
                background: white;
                padding: 12px 15px;
                border-radius: 4px;
                border: 1px solid #e9ecef;
            }

            .field-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;
            }

            .field-name {
                font-weight: 500;
                color: #495057;
            }

            .field-type {
                background: #e9ecef;
                color: #495057;
                padding: 2px 8px;
                border-radius: 12px;
                font-size: 0.8em;
                font-weight: 500;
            }

            .field-preview {
                font-size: 0.9em;
                color: #6c757d;
                font-family: monospace;
                background: #f8f9fa;
                padding: 8px;
                border-radius: 3px;
                word-break: break-all;
            }

            .null-value {
                color: #dc3545;
                font-style: italic;
            }

            .raw-data-viewer {
                background: white;
                border-radius: 8px;
                padding: 20px;
                border: 1px solid #e9ecef;
            }

            .json-viewer {
                background: #f8f9fa;
                border-radius: 6px;
                padding: 15px;
                border: 1px solid #e9ecef;
                max-height: 600px;
                overflow-y: auto;
            }

            .json-viewer pre {
                margin: 0;
                font-size: 0.85em;
                line-height: 1.4;
                color: #495057;
            }

            .no-metrics, .no-tools, .no-output {
                text-align: center;
                color: #6c757d;
                font-style: italic;
                padding: 40px 20px;
                background: #f8f9fa;
                border-radius: 6px;
                border: 1px solid #e9ecef;
            }

            @media (max-width: 768px) {
                .info-grid {
                    grid-template-columns: 1fr !important;
                }

                .metrics-grid {
                    grid-template-columns: 1fr !important;
                }

                .tools-stats {
                    flex-direction: column !important;
                    gap: 10px !important;
                }

                .stat-grid {
                    grid-template-columns: repeat(2, 1fr) !important;
                }

                .tool-call-details {
                    grid-template-columns: 1fr !important;
                }

                .output-stats {
                    flex-direction: column !important;
                    gap: 10px !important;
                }
            }
        `;
    }
}

// Register the component
if (window.componentRegistry) {
    window.componentRegistry.registerComponent('GenericAgent', GenericAgentComponent, {
        tagName: 'agent-generic',
        dependencies: ['BaseAgentComponent'],
        metadata: {
            description: 'Generic agent evaluation component for any agent type with automatic data analysis',
            version: '1.0.0',
            author: 'Goali System'
        }
    });
}

// Export for module use
window.GenericAgentComponent = GenericAgentComponent;
