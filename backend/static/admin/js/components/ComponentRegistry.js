/**
 * ComponentRegistry - Central registry for managing agent evaluation components
 * 
 * This class provides a centralized system for registering, creating, and managing
 * modular agent evaluation components. It handles component lifecycle, dependencies,
 * and provides utilities for dynamic component creation.
 * 
 * Features:
 * - Component registration and discovery
 * - Dynamic component creation
 * - Dependency management
 * - Template and style management
 * - Component communication
 * - Error handling and debugging
 */

class ComponentRegistry {
    constructor() {
        this.components = new Map();
        this.templates = new Map();
        this.styles = new Map();
        this.instances = new Map();
        this.dependencies = new Map();
        
        // Component lifecycle hooks
        this.hooks = {
            beforeCreate: [],
            afterCreate: [],
            beforeDestroy: [],
            afterDestroy: []
        };
        
        // Initialize registry
        this.initialize();
    }
    
    /**
     * Initialize the component registry
     */
    initialize() {
        console.log('ComponentRegistry: Initializing...');
        
        // Setup global event listeners
        this.setupGlobalEventListeners();
        
        // Load default components
        this.loadDefaultComponents();
        
        console.log('ComponentRegistry: Initialized successfully');
    }
    
    /**
     * Register a component class
     */
    registerComponent(name, componentClass, options = {}) {
        try {
            // Validate component class
            if (!componentClass || typeof componentClass !== 'function') {
                throw new Error('Component class must be a constructor function');
            }
            
            // Check if component extends BaseAgentComponent
            if (!this.extendsBaseComponent(componentClass)) {
                console.warn(`Component ${name} does not extend BaseAgentComponent`);
            }
            
            // Register component
            this.components.set(name, {
                class: componentClass,
                tagName: options.tagName || this.generateTagName(name),
                dependencies: options.dependencies || [],
                template: options.template,
                styles: options.styles,
                metadata: options.metadata || {}
            });
            
            // Register custom element
            const tagName = this.components.get(name).tagName;
            if (!customElements.get(tagName)) {
                customElements.define(tagName, componentClass);
                console.log(`ComponentRegistry: Registered component '${name}' as '${tagName}'`);
            }
            
            // Store dependencies
            if (options.dependencies && options.dependencies.length > 0) {
                this.dependencies.set(name, options.dependencies);
            }
            
            return true;
        } catch (error) {
            console.error(`ComponentRegistry: Failed to register component '${name}':`, error);
            return false;
        }
    }
    
    /**
     * Create a component instance
     */
    createComponent(name, container, data = {}, options = {}) {
        try {
            // Check if component is registered
            if (!this.components.has(name)) {
                throw new Error(`Component '${name}' is not registered`);
            }
            
            // Load dependencies first
            if (!this.loadDependencies(name)) {
                throw new Error(`Failed to load dependencies for component '${name}'`);
            }
            
            // Get component info
            const componentInfo = this.components.get(name);
            const tagName = componentInfo.tagName;
            
            // Create element
            const element = document.createElement(tagName);
            
            // Set data attribute
            if (data && Object.keys(data).length > 0) {
                element.setAttribute('data', JSON.stringify(data));
            }
            
            // Set other attributes
            if (options.attributes) {
                Object.entries(options.attributes).forEach(([key, value]) => {
                    element.setAttribute(key, value);
                });
            }
            
            // Add to container
            if (container) {
                if (typeof container === 'string') {
                    container = document.querySelector(container);
                }
                
                if (container) {
                    container.appendChild(element);
                } else {
                    console.warn(`Container not found for component '${name}'`);
                }
            }
            
            // Store instance
            const instanceId = this.generateInstanceId(name);
            this.instances.set(instanceId, {
                element,
                name,
                container,
                data,
                options,
                created: new Date()
            });
            
            // Set instance ID on element
            element.setAttribute('data-instance-id', instanceId);
            
            // Execute lifecycle hooks
            this.executeHooks('afterCreate', { name, element, instanceId });
            
            console.log(`ComponentRegistry: Created component '${name}' with ID '${instanceId}'`);
            
            return {
                element,
                instanceId,
                destroy: () => this.destroyComponent(instanceId)
            };
            
        } catch (error) {
            console.error(`ComponentRegistry: Failed to create component '${name}':`, error);
            return null;
        }
    }
    
    /**
     * Destroy a component instance
     */
    destroyComponent(instanceId) {
        try {
            if (!this.instances.has(instanceId)) {
                console.warn(`ComponentRegistry: Instance '${instanceId}' not found`);
                return false;
            }
            
            const instance = this.instances.get(instanceId);
            
            // Execute lifecycle hooks
            this.executeHooks('beforeDestroy', instance);
            
            // Remove from DOM
            if (instance.element && instance.element.parentNode) {
                instance.element.parentNode.removeChild(instance.element);
            }
            
            // Remove from instances
            this.instances.delete(instanceId);
            
            // Execute lifecycle hooks
            this.executeHooks('afterDestroy', instance);
            
            console.log(`ComponentRegistry: Destroyed component instance '${instanceId}'`);
            
            return true;
        } catch (error) {
            console.error(`ComponentRegistry: Failed to destroy component '${instanceId}':`, error);
            return false;
        }
    }
    
    /**
     * Get component by name
     */
    getComponent(name) {
        return this.components.get(name);
    }
    
    /**
     * Get all registered components
     */
    getAllComponents() {
        return Array.from(this.components.keys());
    }
    
    /**
     * Get component instances
     */
    getInstances(name = null) {
        if (name) {
            return Array.from(this.instances.values()).filter(instance => instance.name === name);
        }
        return Array.from(this.instances.values());
    }
    
    /**
     * Register a template
     */
    registerTemplate(name, template) {
        this.templates.set(name, template);
        console.log(`ComponentRegistry: Registered template '${name}'`);
    }
    
    /**
     * Get a template
     */
    getTemplate(name) {
        return this.templates.get(name);
    }
    
    /**
     * Register styles
     */
    registerStyles(name, styles) {
        this.styles.set(name, styles);
        console.log(`ComponentRegistry: Registered styles '${name}'`);
    }
    
    /**
     * Get styles
     */
    getStyles(name) {
        return this.styles.get(name);
    }
    
    /**
     * Add lifecycle hook
     */
    addHook(event, callback) {
        if (this.hooks[event]) {
            this.hooks[event].push(callback);
        }
    }
    
    /**
     * Execute lifecycle hooks
     */
    executeHooks(event, data) {
        if (this.hooks[event]) {
            this.hooks[event].forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`ComponentRegistry: Hook '${event}' failed:`, error);
                }
            });
        }
    }
    
    /**
     * Load component dependencies
     */
    loadDependencies(name) {
        const dependencies = this.dependencies.get(name);
        if (!dependencies || dependencies.length === 0) {
            return true;
        }
        
        for (const dependency of dependencies) {
            if (!this.components.has(dependency)) {
                console.error(`ComponentRegistry: Dependency '${dependency}' not found for component '${name}'`);
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Check if component class extends BaseAgentComponent
     */
    extendsBaseComponent(componentClass) {
        let current = componentClass;
        while (current) {
            if (current.name === 'BaseAgentComponent') {
                return true;
            }
            current = Object.getPrototypeOf(current);
        }
        return false;
    }
    
    /**
     * Generate tag name from component name
     */
    generateTagName(name) {
        return `agent-${name.toLowerCase().replace(/([A-Z])/g, '-$1').replace(/^-/, '')}`;
    }
    
    /**
     * Generate unique instance ID
     */
    generateInstanceId(name) {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substr(2, 9);
        return `${name}-${timestamp}-${random}`;
    }
    
    /**
     * Setup global event listeners
     */
    setupGlobalEventListeners() {
        // Listen for component events
        document.addEventListener('component-ready', (event) => {
            console.log(`ComponentRegistry: Component ready - ${event.detail.component}`);
        });
        
        document.addEventListener('component-error', (event) => {
            console.error(`ComponentRegistry: Component error - ${event.detail.message}`, event.detail.error);
        });
        
        // Listen for DOM mutations to track component lifecycle
        if (window.MutationObserver) {
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE && node.tagName && node.tagName.startsWith('AGENT-')) {
                            console.log(`ComponentRegistry: Component added to DOM - ${node.tagName}`);
                        }
                    });
                    
                    mutation.removedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE && node.tagName && node.tagName.startsWith('AGENT-')) {
                            console.log(`ComponentRegistry: Component removed from DOM - ${node.tagName}`);
                        }
                    });
                });
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }
    }
    
    /**
     * Load default components
     */
    loadDefaultComponents() {
        // Register common templates
        this.registerTemplate('loading', '<div class="loading">Loading...</div>');
        this.registerTemplate('error', '<div class="error">{{message}}</div>');
        this.registerTemplate('no-data', '<div class="no-data">No data available</div>');
        
        // Register common styles
        this.registerStyles('common', `
            .loading {
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 20px;
                color: #6c757d;
            }
            
            .error {
                padding: 15px;
                background: #f8d7da;
                color: #721c24;
                border: 1px solid #f5c6cb;
                border-radius: 4px;
                margin: 10px 0;
            }
            
            .no-data {
                padding: 20px;
                text-align: center;
                color: #6c757d;
                font-style: italic;
            }
        `);
    }
    
    /**
     * Get registry statistics
     */
    getStats() {
        return {
            components: this.components.size,
            templates: this.templates.size,
            styles: this.styles.size,
            instances: this.instances.size,
            dependencies: this.dependencies.size
        };
    }
    
    /**
     * Debug information
     */
    debug() {
        console.group('ComponentRegistry Debug Information');
        console.log('Stats:', this.getStats());
        console.log('Registered Components:', Array.from(this.components.keys()));
        console.log('Active Instances:', Array.from(this.instances.keys()));
        console.log('Templates:', Array.from(this.templates.keys()));
        console.log('Styles:', Array.from(this.styles.keys()));
        console.groupEnd();
    }
}

// Create global registry instance
window.ComponentRegistry = ComponentRegistry;
window.componentRegistry = new ComponentRegistry();

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ComponentRegistry;
}
