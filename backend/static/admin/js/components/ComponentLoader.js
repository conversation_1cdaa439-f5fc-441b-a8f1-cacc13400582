/**
 * ComponentLoader - Centralized component loading and initialization system
 * 
 * This script handles the loading and initialization of all modular agent evaluation
 * components. It ensures proper dependency management, error handling, and provides
 * a unified interface for component management.
 * 
 * Features:
 * - Automatic component discovery and loading
 * - Dependency resolution
 * - Error handling and fallback mechanisms
 * - Performance monitoring
 * - Development mode debugging
 */

class ComponentLoader {
    constructor() {
        this.loadedComponents = new Set();
        this.loadingPromises = new Map();
        this.loadOrder = [];
        this.errors = [];
        this.startTime = Date.now();
        
        // Configuration
        this.config = {
            baseUrl: '/static/admin/js/components/',
            timeout: 10000,
            retryAttempts: 3,
            developmentMode: this.isDevelopmentMode()
        };
        
        // Component definitions
        this.components = [
            {
                name: 'BaseAgentComponent',
                file: 'BaseAgentComponent.js',
                dependencies: [],
                required: true
            },
            {
                name: 'ComponentRegistry',
                file: 'ComponentRegistry.js',
                dependencies: [],
                required: true
            },
            {
                name: 'DataVisualizationComponent',
                file: 'DataVisualizationComponent.js',
                dependencies: ['BaseAgentComponent'],
                required: false
            },
            {
                name: 'ResourceAgentComponent',
                file: 'ResourceAgentComponent.js',
                dependencies: ['BaseAgentComponent'],
                required: false
            },
            {
                name: 'MentorAgentComponent',
                file: 'MentorAgentComponent.js',
                dependencies: ['BaseAgentComponent'],
                required: false
            },
            {
                name: 'EngagementAgentComponent',
                file: 'EngagementAgentComponent.js',
                dependencies: ['BaseAgentComponent'],
                required: false
            },
            {
                name: 'GenericAgentComponent',
                file: 'GenericAgentComponent.js',
                dependencies: ['BaseAgentComponent'],
                required: false
            }
        ];
        
        this.initialize();
    }
    
    /**
     * Initialize the component loader
     */
    initialize() {
        console.log('ComponentLoader: Initializing...');
        
        if (this.config.developmentMode) {
            console.log('ComponentLoader: Development mode enabled');
            this.enableDebugMode();
        }
        
        // Start loading process
        this.loadAllComponents()
            .then(() => {
                this.onLoadComplete();
            })
            .catch((error) => {
                this.onLoadError(error);
            });
    }
    
    /**
     * Load all components in dependency order
     */
    async loadAllComponents() {
        console.log('ComponentLoader: Starting component loading...');
        
        try {
            // Sort components by dependencies
            const sortedComponents = this.sortComponentsByDependencies();
            
            // Load components in order
            for (const component of sortedComponents) {
                await this.loadComponent(component);
            }
            
            console.log('ComponentLoader: All components loaded successfully');
            
        } catch (error) {
            console.error('ComponentLoader: Failed to load components:', error);
            throw error;
        }
    }
    
    /**
     * Load a single component
     */
    async loadComponent(component) {
        if (this.loadedComponents.has(component.name)) {
            return;
        }
        
        // Check if already loading
        if (this.loadingPromises.has(component.name)) {
            return this.loadingPromises.get(component.name);
        }
        
        console.log(`ComponentLoader: Loading ${component.name}...`);
        
        const loadPromise = this.doLoadComponent(component);
        this.loadingPromises.set(component.name, loadPromise);
        
        try {
            await loadPromise;
            this.loadedComponents.add(component.name);
            this.loadOrder.push(component.name);
            console.log(`ComponentLoader: ${component.name} loaded successfully`);
        } catch (error) {
            this.errors.push({
                component: component.name,
                error: error.message,
                timestamp: new Date()
            });
            
            if (component.required) {
                throw new Error(`Required component ${component.name} failed to load: ${error.message}`);
            } else {
                console.warn(`ComponentLoader: Optional component ${component.name} failed to load:`, error);
            }
        } finally {
            this.loadingPromises.delete(component.name);
        }
    }
    
    /**
     * Actually load the component script
     */
    async doLoadComponent(component) {
        return new Promise((resolve, reject) => {
            // Load dependencies first
            const dependencyPromises = component.dependencies.map(dep => {
                const depComponent = this.components.find(c => c.name === dep);
                return depComponent ? this.loadComponent(depComponent) : Promise.resolve();
            });
            
            Promise.all(dependencyPromises)
                .then(() => {
                    // Create script element
                    const script = document.createElement('script');
                    script.src = this.config.baseUrl + component.file;
                    script.async = true;
                    
                    // Set up timeout
                    const timeout = setTimeout(() => {
                        reject(new Error(`Timeout loading ${component.name}`));
                    }, this.config.timeout);
                    
                    // Handle load success
                    script.onload = () => {
                        clearTimeout(timeout);
                        
                        // Verify component was actually loaded
                        if (this.verifyComponentLoaded(component)) {
                            resolve();
                        } else {
                            reject(new Error(`Component ${component.name} did not register properly`));
                        }
                    };
                    
                    // Handle load error
                    script.onerror = () => {
                        clearTimeout(timeout);
                        reject(new Error(`Failed to load script for ${component.name}`));
                    };
                    
                    // Add to document
                    document.head.appendChild(script);
                })
                .catch(reject);
        });
    }
    
    /**
     * Verify that a component was loaded correctly
     */
    verifyComponentLoaded(component) {
        switch (component.name) {
            case 'BaseAgentComponent':
                return typeof window.BaseAgentComponent === 'function';
            case 'ComponentRegistry':
                return typeof window.ComponentRegistry === 'function' && window.componentRegistry;
            case 'DataVisualizationComponent':
                return typeof window.DataVisualizationComponent === 'function';
            case 'ResourceAgentComponent':
                return typeof window.ResourceAgentComponent === 'function';
            case 'MentorAgentComponent':
                return typeof window.MentorAgentComponent === 'function';
            case 'EngagementAgentComponent':
                return typeof window.EngagementAgentComponent === 'function';
            case 'GenericAgentComponent':
                return typeof window.GenericAgentComponent === 'function';
            default:
                return true; // Assume loaded if we can't verify
        }
    }
    
    /**
     * Sort components by dependencies
     */
    sortComponentsByDependencies() {
        const sorted = [];
        const visited = new Set();
        const visiting = new Set();
        
        const visit = (component) => {
            if (visiting.has(component.name)) {
                throw new Error(`Circular dependency detected: ${component.name}`);
            }
            
            if (visited.has(component.name)) {
                return;
            }
            
            visiting.add(component.name);
            
            // Visit dependencies first
            component.dependencies.forEach(depName => {
                const depComponent = this.components.find(c => c.name === depName);
                if (depComponent) {
                    visit(depComponent);
                }
            });
            
            visiting.delete(component.name);
            visited.add(component.name);
            sorted.push(component);
        };
        
        this.components.forEach(visit);
        
        return sorted;
    }
    
    /**
     * Check if we're in development mode
     */
    isDevelopmentMode() {
        return window.location.hostname === 'localhost' || 
               window.location.hostname === '127.0.0.1' ||
               window.location.search.includes('debug=true');
    }
    
    /**
     * Enable debug mode
     */
    enableDebugMode() {
        // Add debug styles
        const debugStyles = document.createElement('style');
        debugStyles.textContent = `
            .component-debug-info {
                position: fixed;
                top: 10px;
                left: 10px;
                background: rgba(0,0,0,0.8);
                color: white;
                padding: 10px;
                border-radius: 4px;
                font-family: monospace;
                font-size: 12px;
                z-index: 10000;
                max-width: 300px;
            }
            
            .component-debug-info h4 {
                margin: 0 0 10px 0;
                color: #4CAF50;
            }
            
            .component-debug-info ul {
                margin: 0;
                padding-left: 20px;
            }
            
            .component-debug-info .error {
                color: #f44336;
            }
        `;
        document.head.appendChild(debugStyles);
        
        // Add debug panel
        this.createDebugPanel();
    }
    
    /**
     * Create debug panel
     */
    createDebugPanel() {
        const debugPanel = document.createElement('div');
        debugPanel.className = 'component-debug-info';
        debugPanel.innerHTML = `
            <h4>Component Loader Debug</h4>
            <div id="debug-status">Loading...</div>
            <div id="debug-components"></div>
            <div id="debug-errors"></div>
        `;
        document.body.appendChild(debugPanel);
        
        this.debugPanel = debugPanel;
        this.updateDebugPanel();
    }
    
    /**
     * Update debug panel
     */
    updateDebugPanel() {
        if (!this.debugPanel) return;
        
        const statusEl = this.debugPanel.querySelector('#debug-status');
        const componentsEl = this.debugPanel.querySelector('#debug-components');
        const errorsEl = this.debugPanel.querySelector('#debug-errors');
        
        // Update status
        const loadTime = Date.now() - this.startTime;
        statusEl.innerHTML = `
            Status: ${this.loadedComponents.size}/${this.components.length} loaded<br>
            Time: ${loadTime}ms
        `;
        
        // Update components list
        componentsEl.innerHTML = `
            <strong>Loaded Components:</strong>
            <ul>
                ${this.loadOrder.map(name => `<li>✓ ${name}</li>`).join('')}
            </ul>
        `;
        
        // Update errors
        if (this.errors.length > 0) {
            errorsEl.innerHTML = `
                <strong class="error">Errors:</strong>
                <ul>
                    ${this.errors.map(err => `<li class="error">✗ ${err.component}: ${err.error}</li>`).join('')}
                </ul>
            `;
        }
    }
    
    /**
     * Called when all components are loaded
     */
    onLoadComplete() {
        const loadTime = Date.now() - this.startTime;
        console.log(`ComponentLoader: All components loaded in ${loadTime}ms`);
        
        if (this.config.developmentMode) {
            this.updateDebugPanel();
        }
        
        // Dispatch global event
        window.dispatchEvent(new CustomEvent('components-loaded', {
            detail: {
                loadedComponents: Array.from(this.loadedComponents),
                loadTime,
                errors: this.errors
            }
        }));
        
        // Initialize component registry if available
        if (window.componentRegistry) {
            console.log('ComponentLoader: Component registry available');
            console.log('Registry stats:', window.componentRegistry.getStats());
        }
    }
    
    /**
     * Called when component loading fails
     */
    onLoadError(error) {
        console.error('ComponentLoader: Component loading failed:', error);
        
        if (this.config.developmentMode) {
            this.updateDebugPanel();
        }
        
        // Dispatch error event
        window.dispatchEvent(new CustomEvent('components-load-error', {
            detail: {
                error: error.message,
                loadedComponents: Array.from(this.loadedComponents),
                errors: this.errors
            }
        }));
    }
    
    /**
     * Get loading statistics
     */
    getStats() {
        return {
            totalComponents: this.components.length,
            loadedComponents: this.loadedComponents.size,
            errors: this.errors.length,
            loadTime: Date.now() - this.startTime,
            loadOrder: this.loadOrder
        };
    }
    
    /**
     * Check if a component is loaded
     */
    isComponentLoaded(componentName) {
        return this.loadedComponents.has(componentName);
    }
    
    /**
     * Wait for a specific component to load
     */
    async waitForComponent(componentName, timeout = 5000) {
        if (this.isComponentLoaded(componentName)) {
            return true;
        }
        
        return new Promise((resolve, reject) => {
            const timeoutId = setTimeout(() => {
                reject(new Error(`Timeout waiting for component ${componentName}`));
            }, timeout);
            
            const checkComponent = () => {
                if (this.isComponentLoaded(componentName)) {
                    clearTimeout(timeoutId);
                    resolve(true);
                } else {
                    setTimeout(checkComponent, 100);
                }
            };
            
            checkComponent();
        });
    }
}

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.componentLoader = new ComponentLoader();
    });
} else {
    window.componentLoader = new ComponentLoader();
}

// Export for module use
window.ComponentLoader = ComponentLoader;
