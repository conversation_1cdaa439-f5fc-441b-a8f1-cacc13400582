/**
 * EngagementAgentComponent - Specialized component for displaying Engagement & Pattern Analytics Agent evaluation data
 * 
 * This component provides a rich, interactive display for Engagement Agent benchmark results,
 * focusing on user engagement patterns, behavioral analysis, preference tracking, and activity completion metrics.
 * 
 * Features:
 * - Engagement pattern visualization with trend analysis
 * - Domain preference tracking with completion rates
 * - Behavioral pattern identification and consistency metrics
 * - Temporal engagement analysis (time-of-day, day-of-week patterns)
 * - Preference vs. behavior contradiction analysis
 * - Activity completion and abandonment tracking
 */

class EngagementAgentComponent extends BaseAgentComponent {
    constructor() {
        super();
        
        // Component-specific state
        this.engagementState = {
            selectedTimeframe: '30d',
            selectedDomain: 'all',
            showTrendAnalysis: true,
            showPatternDetails: true,
            filterByEngagement: 'all'
        };
    }
    
    /**
     * Define observed attributes
     */
    static get observedAttributes() {
        return ['data', 'title', 'expanded', 'show-trends'];
    }
    
    /**
     * Get default state
     */
    getDefaultState() {
        return {
            ...super.getDefaultState(),
            showEngagementMetrics: true,
            showDomainAnalysis: true,
            showBehavioralPatterns: true,
            showTemporalAnalysis: true
        };
    }
    
    /**
     * Get component title
     */
    getTitle() {
        const agentRole = this.safeGet(this.data, 'agent_role', 'Engagement');
        return `📊 ${agentRole.charAt(0).toUpperCase() + agentRole.slice(1)} & Pattern Analytics`;
    }
    
    /**
     * Get action buttons
     */
    getActionButtons() {
        return `
            ${super.getActionButtons()}
            <select data-action="change-timeframe" class="btn">
                <option value="7d" ${this.engagementState.selectedTimeframe === '7d' ? 'selected' : ''}>Last 7 Days</option>
                <option value="30d" ${this.engagementState.selectedTimeframe === '30d' ? 'selected' : ''}>Last 30 Days</option>
                <option value="90d" ${this.engagementState.selectedTimeframe === '90d' ? 'selected' : ''}>Last 90 Days</option>
                <option value="all" ${this.engagementState.selectedTimeframe === 'all' ? 'selected' : ''}>All Time</option>
            </select>
            <button class="btn" data-action="toggle-trends">
                ${this.engagementState.showTrendAnalysis ? '📈 Hide Trends' : '📈 Show Trends'}
            </button>
            <button class="btn" data-action="export-patterns">📋 Export</button>
        `;
    }
    
    /**
     * Get component content
     */
    getContent() {
        if (this.state.loading) {
            return '<div class="loading">Loading engagement analysis...</div>';
        }
        
        if (this.state.error) {
            return `<div class="error">${this.state.error}</div>`;
        }
        
        const engagementData = this.extractEngagementData();
        if (!engagementData) {
            return '<div class="no-data">No engagement pattern data available</div>';
        }
        
        return `
            <div class="engagement-agent-content">
                ${this.renderEngagementSummary(engagementData)}
                ${this.state.expanded ? this.renderDetailedAnalysis(engagementData) : ''}
            </div>
        `;
    }
    
    /**
     * Extract engagement-specific data from benchmark results
     */
    extractEngagementData() {
        // Try multiple paths to find engagement data
        const rawResults = this.safeGet(this.data, 'raw_results');
        const lastOutput = this.safeGet(rawResults, 'last_output');
        const engagementOutput = this.safeGet(lastOutput, 'engagement_output') || this.safeGet(this.data, 'engagement_output');
        
        // Extract pattern analysis data
        const patternAnalysis = this.safeGet(engagementOutput, 'pattern_analysis') || this.generateMockPatternData();
        const domainMetrics = this.safeGet(engagementOutput, 'domain_metrics') || this.generateMockDomainData();
        const behavioralInsights = this.safeGet(engagementOutput, 'behavioral_insights') || this.generateMockBehavioralData();
        
        // Extract completion and engagement metrics
        const completionRates = this.safeGet(engagementOutput, 'completion_rates') || {};
        const engagementScore = this.safeGet(this.data, 'semantic_score', 0);
        
        return {
            engagement_output: engagementOutput,
            pattern_analysis: patternAnalysis,
            domain_metrics: domainMetrics,
            behavioral_insights: behavioralInsights,
            completion_rates: completionRates,
            engagement_score: engagementScore,
            temporal_patterns: this.analyzeTemporalPatterns(engagementOutput),
            preference_consistency: this.analyzePreferenceConsistency(domainMetrics, behavioralInsights)
        };
    }
    
    /**
     * Generate mock pattern data for demonstration
     */
    generateMockPatternData() {
        return {
            recurring_patterns: [
                { pattern: 'Morning activity preference', confidence: 0.85, frequency: 'Daily' },
                { pattern: 'Creative domain avoidance', confidence: 0.72, frequency: 'Weekly' },
                { pattern: 'Physical activity completion', confidence: 0.91, frequency: 'Bi-weekly' }
            ],
            pattern_strength: 0.78,
            consistency_score: 0.83
        };
    }
    
    /**
     * Generate mock domain data for demonstration
     */
    generateMockDomainData() {
        return {
            physical: { completion_rate: 0.85, engagement_score: 0.78, preference_strength: 0.92 },
            creative: { completion_rate: 0.45, engagement_score: 0.52, preference_strength: 0.31 },
            social: { completion_rate: 0.73, engagement_score: 0.81, preference_strength: 0.67 },
            reflective: { completion_rate: 0.68, engagement_score: 0.74, preference_strength: 0.58 },
            learning: { completion_rate: 0.79, engagement_score: 0.83, preference_strength: 0.75 }
        };
    }
    
    /**
     * Generate mock behavioral data for demonstration
     */
    generateMockBehavioralData() {
        return {
            activity_abandonment_rate: 0.23,
            preference_drift: 0.15,
            engagement_volatility: 0.31,
            consistency_trends: {
                improving: ['physical', 'learning'],
                declining: ['creative'],
                stable: ['social', 'reflective']
            }
        };
    }
    
    /**
     * Analyze temporal patterns
     */
    analyzeTemporalPatterns(engagementOutput) {
        // Mock temporal analysis - in real implementation, this would analyze historical data
        return {
            optimal_times: ['09:00-11:00', '14:00-16:00', '19:00-21:00'],
            peak_engagement_day: 'Tuesday',
            lowest_engagement_day: 'Sunday',
            weekly_pattern_strength: 0.67,
            daily_pattern_strength: 0.74
        };
    }
    
    /**
     * Analyze preference consistency
     */
    analyzePreferenceConsistency(domainMetrics, behavioralInsights) {
        const domains = Object.keys(domainMetrics);
        let totalInconsistency = 0;
        const inconsistencies = [];
        
        domains.forEach(domain => {
            const metrics = domainMetrics[domain];
            const preferenceVsBehavior = Math.abs(metrics.preference_strength - metrics.completion_rate);
            
            if (preferenceVsBehavior > 0.3) {
                inconsistencies.push({
                    domain,
                    type: metrics.preference_strength > metrics.completion_rate ? 'stated_higher' : 'behavior_higher',
                    gap: preferenceVsBehavior,
                    preference: metrics.preference_strength,
                    behavior: metrics.completion_rate
                });
            }
            
            totalInconsistency += preferenceVsBehavior;
        });
        
        return {
            overall_consistency: 1 - (totalInconsistency / domains.length),
            inconsistencies,
            consistency_trend: behavioralInsights.preference_drift < 0.2 ? 'stable' : 'volatile'
        };
    }
    
    /**
     * Render engagement summary
     */
    renderEngagementSummary(engagementData) {
        const overallEngagement = engagementData.engagement_score;
        const patternStrength = engagementData.pattern_analysis.pattern_strength;
        const consistencyScore = engagementData.preference_consistency.overall_consistency;
        const completionRate = this.calculateOverallCompletionRate(engagementData.domain_metrics);
        
        return `
            <div class="engagement-summary">
                <div class="summary-grid">
                    <div class="summary-card engagement">
                        <div class="card-icon">🎯</div>
                        <div class="card-content">
                            <div class="card-value">${this.formatValue(overallEngagement, 'percentage')}</div>
                            <div class="card-label">Overall Engagement</div>
                            <div class="card-detail">Semantic analysis score</div>
                        </div>
                    </div>
                    
                    <div class="summary-card patterns">
                        <div class="card-icon">🔍</div>
                        <div class="card-content">
                            <div class="card-value">${this.formatValue(patternStrength, 'percentage')}</div>
                            <div class="card-label">Pattern Strength</div>
                            <div class="card-detail">Behavioral consistency</div>
                        </div>
                    </div>
                    
                    <div class="summary-card completion">
                        <div class="card-icon">✅</div>
                        <div class="card-content">
                            <div class="card-value">${this.formatValue(completionRate, 'percentage')}</div>
                            <div class="card-label">Completion Rate</div>
                            <div class="card-detail">Activity follow-through</div>
                        </div>
                    </div>
                    
                    <div class="summary-card consistency">
                        <div class="card-icon">🎭</div>
                        <div class="card-content">
                            <div class="card-value">${this.formatValue(consistencyScore, 'percentage')}</div>
                            <div class="card-label">Preference Consistency</div>
                            <div class="card-detail">Stated vs. actual behavior</div>
                        </div>
                    </div>
                </div>
                
                <div class="pattern-highlights">
                    <h4>🔍 Key Behavioral Patterns</h4>
                    <div class="patterns-list">
                        ${engagementData.pattern_analysis.recurring_patterns.map(pattern => `
                            <div class="pattern-item">
                                <div class="pattern-header">
                                    <span class="pattern-name">${pattern.pattern}</span>
                                    <span class="pattern-confidence">${this.formatValue(pattern.confidence, 'percentage')} confidence</span>
                                </div>
                                <div class="pattern-frequency">Frequency: ${pattern.frequency}</div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * Calculate overall completion rate
     */
    calculateOverallCompletionRate(domainMetrics) {
        const domains = Object.values(domainMetrics);
        if (domains.length === 0) return 0;
        
        const totalCompletion = domains.reduce((sum, domain) => sum + domain.completion_rate, 0);
        return totalCompletion / domains.length;
    }
    
    /**
     * Render detailed analysis
     */
    renderDetailedAnalysis(engagementData) {
        return `
            <div class="detailed-analysis">
                ${this.renderDomainAnalysis(engagementData)}
                ${this.renderBehavioralPatterns(engagementData)}
                ${this.renderTemporalAnalysis(engagementData)}
                ${this.renderPreferenceConsistency(engagementData)}
            </div>
        `;
    }
    
    /**
     * Render domain analysis
     */
    renderDomainAnalysis(engagementData) {
        const domains = Object.entries(engagementData.domain_metrics);
        
        return `
            <div class="domain-analysis section">
                <h4>🎨 Domain Engagement Analysis</h4>
                <div class="domains-grid">
                    ${domains.map(([domain, metrics]) => `
                        <div class="domain-card">
                            <div class="domain-header">
                                <h5>${this.getDomainIcon(domain)} ${domain.charAt(0).toUpperCase() + domain.slice(1)}</h5>
                                <span class="domain-score ${this.getEngagementClass(metrics.engagement_score)}">
                                    ${this.formatValue(metrics.engagement_score, 'percentage')}
                                </span>
                            </div>
                            <div class="domain-metrics">
                                <div class="metric-row">
                                    <span class="metric-label">Completion Rate:</span>
                                    <div class="metric-bar">
                                        <div class="metric-fill" style="width: ${metrics.completion_rate * 100}%"></div>
                                    </div>
                                    <span class="metric-value">${this.formatValue(metrics.completion_rate, 'percentage')}</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">Preference Strength:</span>
                                    <div class="metric-bar">
                                        <div class="metric-fill preference" style="width: ${metrics.preference_strength * 100}%"></div>
                                    </div>
                                    <span class="metric-value">${this.formatValue(metrics.preference_strength, 'percentage')}</span>
                                </div>
                                <div class="metric-row">
                                    <span class="metric-label">Engagement Score:</span>
                                    <div class="metric-bar">
                                        <div class="metric-fill engagement" style="width: ${metrics.engagement_score * 100}%"></div>
                                    </div>
                                    <span class="metric-value">${this.formatValue(metrics.engagement_score, 'percentage')}</span>
                                </div>
                            </div>
                            <div class="domain-insights">
                                ${this.generateDomainInsights(domain, metrics)}
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }
    
    /**
     * Get domain icon
     */
    getDomainIcon(domain) {
        const icons = {
            physical: '💪',
            creative: '🎨',
            social: '👥',
            reflective: '🧘',
            learning: '📚',
            wellness: '🌱'
        };
        return icons[domain] || '📊';
    }
    
    /**
     * Get engagement class for styling
     */
    getEngagementClass(score) {
        if (score >= 0.8) return 'excellent';
        if (score >= 0.6) return 'good';
        if (score >= 0.4) return 'fair';
        return 'poor';
    }
    
    /**
     * Generate domain-specific insights
     */
    generateDomainInsights(domain, metrics) {
        const completionGap = metrics.preference_strength - metrics.completion_rate;
        
        if (Math.abs(completionGap) > 0.3) {
            if (completionGap > 0) {
                return `<div class="insight warning">⚠️ High preference but low completion - investigate barriers</div>`;
            } else {
                return `<div class="insight positive">✅ Performing better than stated preference suggests</div>`;
            }
        } else if (metrics.engagement_score > 0.8) {
            return `<div class="insight excellent">🌟 Excellent engagement - maintain current approach</div>`;
        } else if (metrics.completion_rate < 0.5) {
            return `<div class="insight concern">🔍 Low completion rate - needs attention</div>`;
        }
        
        return `<div class="insight neutral">📊 Balanced performance across metrics</div>`;
    }

    /**
     * Render behavioral patterns analysis
     */
    renderBehavioralPatterns(engagementData) {
        const patterns = engagementData.pattern_analysis;
        const behavioral = engagementData.behavioral_insights;

        return `
            <div class="behavioral-patterns section">
                <h4>🧠 Behavioral Pattern Analysis</h4>
                <div class="patterns-overview">
                    <div class="pattern-metrics">
                        <div class="pattern-metric">
                            <span class="metric-label">Pattern Strength:</span>
                            <div class="metric-bar">
                                <div class="metric-fill" style="width: ${patterns.pattern_strength * 100}%"></div>
                            </div>
                            <span class="metric-value">${this.formatValue(patterns.pattern_strength, 'percentage')}</span>
                        </div>
                        <div class="pattern-metric">
                            <span class="metric-label">Consistency Score:</span>
                            <div class="metric-bar">
                                <div class="metric-fill" style="width: ${patterns.consistency_score * 100}%"></div>
                            </div>
                            <span class="metric-value">${this.formatValue(patterns.consistency_score, 'percentage')}</span>
                        </div>
                        <div class="pattern-metric">
                            <span class="metric-label">Abandonment Rate:</span>
                            <div class="metric-bar">
                                <div class="metric-fill abandonment" style="width: ${behavioral.activity_abandonment_rate * 100}%"></div>
                            </div>
                            <span class="metric-value">${this.formatValue(behavioral.activity_abandonment_rate, 'percentage')}</span>
                        </div>
                    </div>
                </div>

                <div class="recurring-patterns">
                    <h5>🔄 Recurring Patterns</h5>
                    <div class="patterns-list">
                        ${patterns.recurring_patterns.map(pattern => `
                            <div class="pattern-detail">
                                <div class="pattern-info">
                                    <div class="pattern-name">${pattern.pattern}</div>
                                    <div class="pattern-meta">
                                        <span class="confidence">Confidence: ${this.formatValue(pattern.confidence, 'percentage')}</span>
                                        <span class="frequency">Frequency: ${pattern.frequency}</span>
                                    </div>
                                </div>
                                <div class="pattern-strength">
                                    <div class="strength-bar">
                                        <div class="strength-fill" style="width: ${pattern.confidence * 100}%"></div>
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>

                <div class="consistency-trends">
                    <h5>📈 Consistency Trends</h5>
                    <div class="trends-grid">
                        <div class="trend-category improving">
                            <h6>📈 Improving</h6>
                            <div class="trend-items">
                                ${behavioral.consistency_trends.improving.map(domain =>
                                    `<span class="trend-item">${this.getDomainIcon(domain)} ${domain}</span>`
                                ).join('')}
                            </div>
                        </div>
                        <div class="trend-category stable">
                            <h6>➡️ Stable</h6>
                            <div class="trend-items">
                                ${behavioral.consistency_trends.stable.map(domain =>
                                    `<span class="trend-item">${this.getDomainIcon(domain)} ${domain}</span>`
                                ).join('')}
                            </div>
                        </div>
                        <div class="trend-category declining">
                            <h6>📉 Declining</h6>
                            <div class="trend-items">
                                ${behavioral.consistency_trends.declining.map(domain =>
                                    `<span class="trend-item">${this.getDomainIcon(domain)} ${domain}</span>`
                                ).join('')}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Render temporal analysis
     */
    renderTemporalAnalysis(engagementData) {
        if (!this.engagementState.showTrendAnalysis) return '';

        const temporal = engagementData.temporal_patterns;

        return `
            <div class="temporal-analysis section">
                <h4>⏰ Temporal Engagement Patterns</h4>
                <div class="temporal-grid">
                    <div class="optimal-times">
                        <h5>🕐 Optimal Activity Times</h5>
                        <div class="time-slots">
                            ${temporal.optimal_times.map(time => `
                                <div class="time-slot">
                                    <span class="time-icon">⏰</span>
                                    <span class="time-range">${time}</span>
                                </div>
                            `).join('')}
                        </div>
                        <div class="pattern-strength">
                            <span class="strength-label">Daily Pattern Strength:</span>
                            <span class="strength-value">${this.formatValue(temporal.daily_pattern_strength, 'percentage')}</span>
                        </div>
                    </div>

                    <div class="weekly-patterns">
                        <h5>📅 Weekly Engagement Patterns</h5>
                        <div class="weekly-insights">
                            <div class="peak-day">
                                <span class="day-label">Peak Engagement:</span>
                                <span class="day-value peak">${temporal.peak_engagement_day}</span>
                            </div>
                            <div class="low-day">
                                <span class="day-label">Lowest Engagement:</span>
                                <span class="day-value low">${temporal.lowest_engagement_day}</span>
                            </div>
                        </div>
                        <div class="pattern-strength">
                            <span class="strength-label">Weekly Pattern Strength:</span>
                            <span class="strength-value">${this.formatValue(temporal.weekly_pattern_strength, 'percentage')}</span>
                        </div>
                    </div>
                </div>

                <div class="temporal-recommendations">
                    <h5>💡 Temporal Optimization Recommendations</h5>
                    <div class="recommendations-list">
                        ${this.generateTemporalRecommendations(temporal).map(rec =>
                            `<div class="recommendation">${rec}</div>`
                        ).join('')}
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Generate temporal recommendations
     */
    generateTemporalRecommendations(temporal) {
        const recommendations = [];

        if (temporal.daily_pattern_strength > 0.7) {
            recommendations.push(`🎯 Strong daily patterns detected - schedule activities during optimal times: ${temporal.optimal_times.join(', ')}`);
        }

        if (temporal.weekly_pattern_strength > 0.6) {
            recommendations.push(`📅 Consider scheduling important activities on ${temporal.peak_engagement_day} for maximum engagement`);
            recommendations.push(`⚠️ Avoid scheduling challenging activities on ${temporal.lowest_engagement_day}`);
        }

        if (temporal.daily_pattern_strength < 0.4) {
            recommendations.push(`🔍 Weak daily patterns - experiment with different time slots to find optimal engagement windows`);
        }

        return recommendations;
    }

    /**
     * Render preference consistency analysis
     */
    renderPreferenceConsistency(engagementData) {
        const consistency = engagementData.preference_consistency;

        return `
            <div class="preference-consistency section">
                <h4>🎭 Preference vs. Behavior Analysis</h4>
                <div class="consistency-overview">
                    <div class="consistency-score">
                        <div class="score-circle">
                            <div class="score-fill" style="--consistency: ${consistency.overall_consistency * 100}%">
                                <span class="score-text">${this.formatValue(consistency.overall_consistency, 'percentage')}</span>
                            </div>
                        </div>
                        <div class="score-label">Overall Consistency</div>
                        <div class="score-trend ${consistency.consistency_trend}">
                            ${consistency.consistency_trend === 'stable' ? '➡️ Stable' : '📊 Volatile'}
                        </div>
                    </div>

                    <div class="consistency-insights">
                        <h5>🔍 Consistency Insights</h5>
                        <div class="insights-list">
                            ${consistency.inconsistencies.length === 0 ?
                                '<div class="insight positive">✅ Strong alignment between stated preferences and actual behavior</div>' :
                                consistency.inconsistencies.map(inc => `
                                    <div class="inconsistency-item">
                                        <div class="inconsistency-header">
                                            <span class="domain-name">${this.getDomainIcon(inc.domain)} ${inc.domain}</span>
                                            <span class="gap-size ${this.getGapClass(inc.gap)}">${this.formatValue(inc.gap, 'percentage')} gap</span>
                                        </div>
                                        <div class="inconsistency-details">
                                            <span class="preference-stat">Stated: ${this.formatValue(inc.preference, 'percentage')}</span>
                                            <span class="behavior-stat">Actual: ${this.formatValue(inc.behavior, 'percentage')}</span>
                                            <span class="gap-type">${inc.type === 'stated_higher' ? 'Says more than does' : 'Does more than says'}</span>
                                        </div>
                                    </div>
                                `).join('')
                            }
                        </div>
                    </div>
                </div>

                <div class="consistency-recommendations">
                    <h5>💡 Consistency Improvement Recommendations</h5>
                    <div class="recommendations-list">
                        ${this.generateConsistencyRecommendations(consistency).map(rec =>
                            `<div class="recommendation">${rec}</div>`
                        ).join('')}
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Get gap class for styling
     */
    getGapClass(gap) {
        if (gap > 0.5) return 'large';
        if (gap > 0.3) return 'medium';
        return 'small';
    }

    /**
     * Generate consistency recommendations
     */
    generateConsistencyRecommendations(consistency) {
        const recommendations = [];

        if (consistency.overall_consistency > 0.8) {
            recommendations.push('🌟 Excellent preference-behavior alignment - maintain current approach');
        } else if (consistency.overall_consistency < 0.5) {
            recommendations.push('🔍 Significant preference-behavior gaps detected - investigate underlying barriers');
        }

        consistency.inconsistencies.forEach(inc => {
            if (inc.type === 'stated_higher') {
                recommendations.push(`🎯 ${inc.domain}: Address barriers preventing follow-through on stated preferences`);
            } else {
                recommendations.push(`💭 ${inc.domain}: User may underestimate their actual engagement - highlight successes`);
            }
        });

        if (consistency.consistency_trend === 'volatile') {
            recommendations.push('📊 High preference volatility - focus on identifying stable core preferences');
        }

        return recommendations.slice(0, 5); // Limit to 5 recommendations
    }

    /**
     * Handle component actions
     */
    handleAction(action, element) {
        switch (action) {
            case 'change-timeframe':
                this.engagementState.selectedTimeframe = element.value;
                this.update();
                break;
            case 'toggle-trends':
                this.engagementState.showTrendAnalysis = !this.engagementState.showTrendAnalysis;
                this.update();
                break;
            case 'export-patterns':
                this.exportEngagementData();
                break;
            default:
                super.handleAction(action, element);
        }
    }

    /**
     * Export engagement pattern data
     */
    exportEngagementData() {
        const engagementData = this.extractEngagementData();
        if (engagementData) {
            const exportData = {
                engagement_metrics: {
                    overall_score: engagementData.engagement_score,
                    pattern_strength: engagementData.pattern_analysis.pattern_strength,
                    consistency_score: engagementData.preference_consistency.overall_consistency
                },
                domain_analysis: engagementData.domain_metrics,
                behavioral_patterns: engagementData.pattern_analysis,
                temporal_patterns: engagementData.temporal_patterns,
                preference_consistency: engagementData.preference_consistency,
                timeframe: this.engagementState.selectedTimeframe,
                timestamp: new Date().toISOString()
            };

            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);

            const link = document.createElement('a');
            link.href = url;
            link.download = `engagement-pattern-analysis-${Date.now()}.json`;
            link.click();

            URL.revokeObjectURL(url);
        }
    }

    /**
     * Get component styles
     */
    getStyles() {
        return `
            ${super.getStyles()}

            .engagement-agent-content {
                display: flex;
                flex-direction: column;
                gap: 20px;
            }

            .engagement-summary {
                background: #f8f9fa;
                border-radius: 8px;
                padding: 20px;
            }

            .pattern-highlights {
                margin-top: 20px;
                padding: 15px;
                background: white;
                border-radius: 6px;
                border: 1px solid #e9ecef;
            }

            .patterns-list {
                display: flex;
                flex-direction: column;
                gap: 10px;
                margin-top: 10px;
            }

            .pattern-item {
                padding: 12px;
                background: #f8f9fa;
                border-radius: 6px;
                border-left: 4px solid #007bff;
            }

            .pattern-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 5px;
            }

            .pattern-name {
                font-weight: 600;
                color: #495057;
            }

            .pattern-confidence {
                font-size: 0.85em;
                color: #007bff;
                font-weight: 500;
            }

            .pattern-frequency {
                font-size: 0.9em;
                color: #6c757d;
            }

            .domains-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 20px;
            }

            .domain-card {
                background: white;
                border-radius: 8px;
                padding: 15px;
                border: 1px solid #e9ecef;
            }

            .domain-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 15px;
            }

            .domain-header h5 {
                margin: 0;
                color: #495057;
                font-size: 1.1em;
            }

            .domain-score {
                padding: 4px 12px;
                border-radius: 12px;
                font-weight: 600;
                font-size: 0.85em;
            }

            .domain-score.excellent {
                background: #d4edda;
                color: #155724;
            }

            .domain-score.good {
                background: #d1ecf1;
                color: #0c5460;
            }

            .domain-score.fair {
                background: #fff3cd;
                color: #856404;
            }

            .domain-score.poor {
                background: #f8d7da;
                color: #721c24;
            }

            .domain-metrics {
                display: flex;
                flex-direction: column;
                gap: 10px;
                margin-bottom: 15px;
            }

            .metric-row {
                display: flex;
                align-items: center;
                gap: 10px;
            }

            .metric-label {
                min-width: 120px;
                font-size: 0.9em;
                color: #6c757d;
            }

            .metric-bar {
                flex: 1;
                height: 6px;
                background: #e9ecef;
                border-radius: 3px;
                overflow: hidden;
            }

            .metric-fill {
                height: 100%;
                background: #007bff;
                transition: width 0.3s ease;
            }

            .metric-fill.preference {
                background: #28a745;
            }

            .metric-fill.engagement {
                background: #17a2b8;
            }

            .metric-fill.abandonment {
                background: #dc3545;
            }

            .metric-value {
                min-width: 50px;
                text-align: right;
                font-weight: 600;
                font-size: 0.85em;
                color: #495057;
            }

            .domain-insights {
                margin-top: 10px;
            }

            .insight {
                padding: 8px 12px;
                border-radius: 4px;
                font-size: 0.85em;
                font-weight: 500;
            }

            .insight.positive {
                background: #d4edda;
                color: #155724;
            }

            .insight.warning {
                background: #fff3cd;
                color: #856404;
            }

            .insight.concern {
                background: #f8d7da;
                color: #721c24;
            }

            .insight.excellent {
                background: #d1ecf1;
                color: #0c5460;
            }

            .insight.neutral {
                background: #e2e3e5;
                color: #383d41;
            }

            .patterns-overview {
                margin-bottom: 20px;
            }

            .pattern-metrics {
                display: flex;
                flex-direction: column;
                gap: 15px;
                background: white;
                padding: 15px;
                border-radius: 6px;
                border: 1px solid #e9ecef;
            }

            .pattern-metric {
                display: flex;
                align-items: center;
                gap: 15px;
            }

            .recurring-patterns {
                margin-bottom: 20px;
            }

            .pattern-detail {
                display: flex;
                align-items: center;
                gap: 15px;
                padding: 12px;
                background: white;
                border-radius: 6px;
                border: 1px solid #e9ecef;
                margin-bottom: 10px;
            }

            .pattern-info {
                flex: 1;
            }

            .pattern-meta {
                display: flex;
                gap: 15px;
                margin-top: 5px;
                font-size: 0.85em;
                color: #6c757d;
            }

            .pattern-strength {
                width: 100px;
            }

            .strength-bar {
                height: 6px;
                background: #e9ecef;
                border-radius: 3px;
                overflow: hidden;
            }

            .strength-fill {
                height: 100%;
                background: #28a745;
                transition: width 0.3s ease;
            }

            .consistency-trends {
                background: white;
                padding: 15px;
                border-radius: 6px;
                border: 1px solid #e9ecef;
            }

            .trends-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
                margin-top: 10px;
            }

            .trend-category {
                padding: 12px;
                border-radius: 6px;
                border: 1px solid #e9ecef;
            }

            .trend-category.improving {
                background: #d4edda;
                border-color: #c3e6cb;
            }

            .trend-category.stable {
                background: #d1ecf1;
                border-color: #bee5eb;
            }

            .trend-category.declining {
                background: #f8d7da;
                border-color: #f5c6cb;
            }

            .trend-category h6 {
                margin: 0 0 8px 0;
                font-size: 0.9em;
                font-weight: 600;
            }

            .trend-items {
                display: flex;
                flex-wrap: wrap;
                gap: 5px;
            }

            .trend-item {
                padding: 2px 8px;
                background: rgba(255,255,255,0.7);
                border-radius: 12px;
                font-size: 0.8em;
                font-weight: 500;
            }

            .temporal-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
                margin-bottom: 20px;
            }

            .optimal-times, .weekly-patterns {
                background: white;
                padding: 15px;
                border-radius: 6px;
                border: 1px solid #e9ecef;
            }

            .time-slots {
                display: flex;
                flex-direction: column;
                gap: 8px;
                margin: 10px 0;
            }

            .time-slot {
                display: flex;
                align-items: center;
                gap: 10px;
                padding: 8px 12px;
                background: #f8f9fa;
                border-radius: 4px;
            }

            .time-icon {
                font-size: 1.1em;
            }

            .time-range {
                font-weight: 500;
                color: #495057;
            }

            .weekly-insights {
                margin: 10px 0;
            }

            .peak-day, .low-day {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 8px 0;
            }

            .day-value.peak {
                color: #28a745;
                font-weight: 600;
            }

            .day-value.low {
                color: #dc3545;
                font-weight: 600;
            }

            .pattern-strength {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-top: 10px;
                padding-top: 10px;
                border-top: 1px solid #e9ecef;
                font-size: 0.9em;
            }

            .strength-label {
                color: #6c757d;
            }

            .strength-value {
                font-weight: 600;
                color: #495057;
            }

            .consistency-overview {
                display: grid;
                grid-template-columns: auto 1fr;
                gap: 30px;
                margin-bottom: 20px;
            }

            .consistency-score {
                text-align: center;
            }

            .score-circle {
                width: 120px;
                height: 120px;
                border-radius: 50%;
                background: conic-gradient(#28a745 0deg, #28a745 var(--consistency, 0%), #e9ecef var(--consistency, 0%), #e9ecef 360deg);
                display: flex;
                align-items: center;
                justify-content: center;
                position: relative;
                margin-bottom: 10px;
            }

            .score-circle::before {
                content: '';
                width: 80px;
                height: 80px;
                background: white;
                border-radius: 50%;
                position: absolute;
            }

            .score-text {
                position: relative;
                z-index: 1;
                font-size: 1.2em;
                font-weight: bold;
                color: #495057;
            }

            .score-label {
                font-weight: 600;
                color: #495057;
                margin-bottom: 5px;
            }

            .score-trend {
                font-size: 0.9em;
                font-weight: 500;
            }

            .score-trend.stable {
                color: #28a745;
            }

            .score-trend.volatile {
                color: #fd7e14;
            }

            .consistency-insights {
                background: white;
                padding: 15px;
                border-radius: 6px;
                border: 1px solid #e9ecef;
            }

            .inconsistency-item {
                padding: 12px;
                background: #f8f9fa;
                border-radius: 6px;
                margin-bottom: 10px;
                border-left: 4px solid #fd7e14;
            }

            .inconsistency-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;
            }

            .domain-name {
                font-weight: 600;
                color: #495057;
            }

            .gap-size {
                font-weight: 600;
                font-size: 0.85em;
                padding: 2px 8px;
                border-radius: 12px;
            }

            .gap-size.large {
                background: #f8d7da;
                color: #721c24;
            }

            .gap-size.medium {
                background: #fff3cd;
                color: #856404;
            }

            .gap-size.small {
                background: #d1ecf1;
                color: #0c5460;
            }

            .inconsistency-details {
                display: flex;
                gap: 15px;
                font-size: 0.85em;
                color: #6c757d;
            }

            .recommendations-list {
                display: flex;
                flex-direction: column;
                gap: 8px;
                margin-top: 10px;
            }

            .recommendation {
                padding: 10px 15px;
                background: #f8f9fa;
                border-left: 4px solid #007bff;
                border-radius: 0 4px 4px 0;
                font-size: 0.9em;
                line-height: 1.4;
            }

            @media (max-width: 768px) {
                .domains-grid {
                    grid-template-columns: 1fr !important;
                }

                .temporal-grid {
                    grid-template-columns: 1fr !important;
                }

                .trends-grid {
                    grid-template-columns: 1fr !important;
                }

                .consistency-overview {
                    grid-template-columns: 1fr !important;
                    text-align: center;
                }

                .metric-row {
                    flex-direction: column;
                    align-items: stretch;
                    gap: 5px;
                }

                .metric-label {
                    min-width: auto;
                }
            }
        `;
    }
}

// Register the component
if (window.componentRegistry) {
    window.componentRegistry.registerComponent('EngagementAgent', EngagementAgentComponent, {
        tagName: 'agent-engagement',
        dependencies: ['BaseAgentComponent'],
        metadata: {
            description: 'Displays Engagement Agent evaluation data with pattern analysis and behavioral insights',
            version: '1.0.0',
            author: 'Goali System'
        }
    });
}

// Export for module use
window.EngagementAgentComponent = EngagementAgentComponent;
