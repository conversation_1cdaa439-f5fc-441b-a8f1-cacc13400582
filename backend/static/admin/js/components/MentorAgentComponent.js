/**
 * MentorAgentComponent - Specialized component for displaying Mentor Agent evaluation data
 *
 * This component provides a rich, interactive display for Mentor Agent benchmark results,
 * focusing on conversational quality, tone analysis, trust building, and communication effectiveness.
 *
 * Features:
 * - Communication tone analysis with sentiment visualization
 * - Trust level progression tracking
 * - Response quality assessment with metrics
 * - Philosophical framing evaluation
 * - User engagement analysis
 * - Communication pattern insights
 */

class MentorAgentComponent extends BaseAgentComponent {
    constructor() {
        super();

        // Component-specific state
        this.mentorState = {
            selectedMetric: 'tone',
            showTrustProgression: true,
            showCommunicationPatterns: true,
            filterByTrustPhase: 'all'
        };
    }

    /**
     * Define observed attributes
     */
    static get observedAttributes() {
        return ['data', 'title', 'expanded', 'show-trust-progression'];
    }

    /**
     * Get default state
     */
    getDefaultState() {
        return {
            ...super.getDefaultState(),
            showToneAnalysis: true,
            showTrustMetrics: true,
            showResponseQuality: true,
            showPhilosophicalFraming: true
        };
    }

    /**
     * Get component title
     */
    getTitle() {
        const agentRole = this.safeGet(this.data, 'agent_role', 'Mentor');
        return `🧠 ${agentRole.charAt(0).toUpperCase() + agentRole.slice(1)} Agent Analysis`;
    }

    /**
     * Get action buttons
     */
    getActionButtons() {
        return `
            ${super.getActionButtons()}
            <select data-action="filter-trust-phase" class="btn">
                <option value="all" ${this.mentorState.filterByTrustPhase === 'all' ? 'selected' : ''}>All Trust Phases</option>
                <option value="foundation" ${this.mentorState.filterByTrustPhase === 'foundation' ? 'selected' : ''}>Foundation Phase</option>
                <option value="expansion" ${this.mentorState.filterByTrustPhase === 'expansion' ? 'selected' : ''}>Expansion Phase</option>
            </select>
            <button class="btn" data-action="toggle-trust-progression">
                ${this.mentorState.showTrustProgression ? '📈 Hide Trust' : '📈 Show Trust'}
            </button>
            <button class="btn" data-action="export-conversation">💬 Export</button>
        `;
    }

    /**
     * Get component content
     */
    getContent() {
        if (this.state.loading) {
            return '<div class="loading">Loading mentor analysis...</div>';
        }

        if (this.state.error) {
            return `<div class="error">${this.state.error}</div>`;
        }

        const mentorData = this.extractMentorData();
        if (!mentorData) {
            return '<div class="no-data">No mentor evaluation data available</div>';
        }

        return `
            <div class="mentor-agent-content">
                ${this.renderMentorSummary(mentorData)}
                ${this.state.expanded ? this.renderDetailedAnalysis(mentorData) : ''}
            </div>
        `;
    }

    /**
     * Extract mentor-specific data from benchmark results
     */
    extractMentorData() {
        // Try multiple paths to find mentor data
        const rawResults = this.safeGet(this.data, 'raw_results');
        const lastOutput = this.safeGet(rawResults, 'last_output');
        const mentorOutput = this.safeGet(lastOutput, 'mentor_output') || this.safeGet(this.data, 'mentor_output');

        // Extract conversation data
        const userResponse = this.safeGet(lastOutput, 'user_response') || this.safeGet(this.data, 'user_response');
        const mentorResponse = this.safeGet(lastOutput, 'mentor_response') || this.safeGet(mentorOutput, 'mentor_response');

        // Extract trust and communication metrics
        const trustLevel = this.safeGet(mentorOutput, 'trust_level') || this.safeGet(this.data, 'trust_level', 0.5);
        const communicationTone = this.safeGet(mentorOutput, 'communication_tone') || 'encouraging';

        // Extract semantic scores and quality metrics
        const semanticScore = this.safeGet(this.data, 'semantic_score', 0);
        const responseQuality = this.calculateResponseQuality(mentorResponse, userResponse);

        return {
            mentor_output: mentorOutput,
            user_response: userResponse,
            mentor_response: mentorResponse,
            trust_level: trustLevel,
            communication_tone: communicationTone,
            semantic_score: semanticScore,
            response_quality: responseQuality,
            philosophical_framing: this.analyzePhilosophicalFraming(mentorResponse),
            conversation_flow: this.analyzeConversationFlow(userResponse, mentorResponse)
        };
    }

    /**
     * Calculate response quality metrics
     */
    calculateResponseQuality(mentorResponse, userResponse) {
        if (!mentorResponse) return { overall: 0, metrics: {} };

        const responseLength = mentorResponse.length;
        const hasPersonalization = mentorResponse.toLowerCase().includes('you') || mentorResponse.toLowerCase().includes('your');
        const hasEncouragement = /great|excellent|wonderful|amazing|fantastic/i.test(mentorResponse);
        const hasQuestions = mentorResponse.includes('?');
        const hasGoalConnection = /goal|growth|develop|journey|progress/i.test(mentorResponse);

        const metrics = {
            length_score: Math.min(responseLength / 200, 1), // Optimal around 200 chars
            personalization_score: hasPersonalization ? 1 : 0,
            encouragement_score: hasEncouragement ? 1 : 0,
            engagement_score: hasQuestions ? 1 : 0,
            goal_alignment_score: hasGoalConnection ? 1 : 0
        };

        const overall = Object.values(metrics).reduce((sum, score) => sum + score, 0) / Object.keys(metrics).length;

        return { overall, metrics };
    }

    /**
     * Analyze philosophical framing in response
     */
    analyzePhilosophicalFraming(mentorResponse) {
        if (!mentorResponse) return { score: 0, elements: [] };

        const philosophicalElements = [];
        const response = mentorResponse.toLowerCase();

        if (response.includes('journey') || response.includes('path')) {
            philosophicalElements.push('Journey Metaphor');
        }
        if (response.includes('growth') || response.includes('develop')) {
            philosophicalElements.push('Growth Mindset');
        }
        if (response.includes('choice') || response.includes('decision')) {
            philosophicalElements.push('Agency & Choice');
        }
        if (response.includes('explore') || response.includes('discover')) {
            philosophicalElements.push('Exploration Theme');
        }
        if (response.includes('commit') || response.includes('dedication')) {
            philosophicalElements.push('Commitment Value');
        }

        return {
            score: philosophicalElements.length / 5, // Normalize to 0-1
            elements: philosophicalElements
        };
    }

    /**
     * Analyze conversation flow quality
     */
    analyzeConversationFlow(userResponse, mentorResponse) {
        if (!userResponse || !mentorResponse) {
            return { quality: 0, analysis: 'Insufficient conversation data' };
        }

        const userLength = userResponse.length;
        const mentorLength = mentorResponse.length;
        const lengthRatio = mentorLength / Math.max(userLength, 1);

        // Analyze response appropriateness
        const isAppropriateLength = lengthRatio >= 0.5 && lengthRatio <= 3; // Mentor should respond proportionally
        const hasAcknowledgment = mentorResponse.toLowerCase().includes(userResponse.toLowerCase().split(' ')[0]);

        let quality = 0;
        let analysis = [];

        if (isAppropriateLength) {
            quality += 0.4;
            analysis.push('Appropriate response length');
        } else {
            analysis.push(lengthRatio < 0.5 ? 'Response too brief' : 'Response too lengthy');
        }

        if (hasAcknowledgment) {
            quality += 0.3;
            analysis.push('Acknowledges user input');
        }

        if (mentorResponse.includes('?')) {
            quality += 0.3;
            analysis.push('Engages with questions');
        }

        return {
            quality,
            analysis: analysis.join(', '),
            length_ratio: lengthRatio
        };
    }

    /**
     * Render mentor summary
     */
    renderMentorSummary(mentorData) {
        const trustPhase = mentorData.trust_level >= 0.6 ? 'Expansion' : 'Foundation';
        const qualityScore = mentorData.response_quality.overall;
        const semanticScore = mentorData.semantic_score;

        return `
            <div class="mentor-summary">
                <div class="summary-grid">
                    <div class="summary-card trust">
                        <div class="card-icon">🤝</div>
                        <div class="card-content">
                            <div class="card-value">${this.formatValue(mentorData.trust_level, 'percentage')}</div>
                            <div class="card-label">Trust Level</div>
                            <div class="card-detail">${trustPhase} Phase</div>
                        </div>
                    </div>

                    <div class="summary-card quality">
                        <div class="card-icon">⭐</div>
                        <div class="card-content">
                            <div class="card-value">${this.formatValue(qualityScore, 'percentage')}</div>
                            <div class="card-label">Response Quality</div>
                            <div class="card-detail">Communication effectiveness</div>
                        </div>
                    </div>

                    <div class="summary-card semantic">
                        <div class="card-icon">🎯</div>
                        <div class="card-content">
                            <div class="card-value">${this.formatValue(semanticScore, 'percentage')}</div>
                            <div class="card-label">Semantic Score</div>
                            <div class="card-detail">Content relevance</div>
                        </div>
                    </div>

                    <div class="summary-card tone">
                        <div class="card-icon">🎭</div>
                        <div class="card-content">
                            <div class="card-value">${mentorData.communication_tone}</div>
                            <div class="card-label">Communication Tone</div>
                            <div class="card-detail">Emotional approach</div>
                        </div>
                    </div>
                </div>

                <div class="philosophical-summary">
                    <h4>🧭 Philosophical Framing</h4>
                    <div class="philosophical-score">
                        <div class="score-bar">
                            <div class="score-fill" style="width: ${mentorData.philosophical_framing.score * 100}%"></div>
                        </div>
                        <span class="score-text">${this.formatValue(mentorData.philosophical_framing.score, 'percentage')}</span>
                    </div>
                    <div class="philosophical-elements">
                        ${mentorData.philosophical_framing.elements.map(element =>
                            `<span class="element-tag">${element}</span>`
                        ).join('')}
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Render detailed analysis
     */
    renderDetailedAnalysis(mentorData) {
        return `
            <div class="detailed-analysis">
                ${this.renderConversationAnalysis(mentorData)}
                ${this.renderTrustProgression(mentorData)}
                ${this.renderResponseQualityBreakdown(mentorData)}
                ${this.renderCommunicationPatterns(mentorData)}
            </div>
        `;
    }

    /**
     * Render conversation analysis
     */
    renderConversationAnalysis(mentorData) {
        return `
            <div class="conversation-analysis section">
                <h4>💬 Conversation Analysis</h4>
                <div class="conversation-grid">
                    <div class="conversation-card user-input">
                        <h5>👤 User Input</h5>
                        <div class="message-content">
                            ${mentorData.user_response || 'No user response available'}
                        </div>
                        <div class="message-metrics">
                            <span class="metric">Length: ${(mentorData.user_response || '').length} chars</span>
                        </div>
                    </div>

                    <div class="conversation-card mentor-response">
                        <h5>🧠 Mentor Response</h5>
                        <div class="message-content">
                            ${mentorData.mentor_response || 'No mentor response available'}
                        </div>
                        <div class="message-metrics">
                            <span class="metric">Length: ${(mentorData.mentor_response || '').length} chars</span>
                            <span class="metric">Quality: ${this.formatValue(mentorData.response_quality.overall, 'percentage')}</span>
                        </div>
                    </div>
                </div>

                <div class="conversation-flow">
                    <h5>🔄 Conversation Flow Analysis</h5>
                    <div class="flow-metrics">
                        <div class="flow-metric">
                            <span class="metric-label">Flow Quality:</span>
                            <div class="metric-bar">
                                <div class="metric-fill" style="width: ${mentorData.conversation_flow.quality * 100}%"></div>
                            </div>
                            <span class="metric-value">${this.formatValue(mentorData.conversation_flow.quality, 'percentage')}</span>
                        </div>
                        <div class="flow-analysis">
                            <strong>Analysis:</strong> ${mentorData.conversation_flow.analysis}
                        </div>
                        <div class="length-ratio">
                            <strong>Response Ratio:</strong> ${mentorData.conversation_flow.length_ratio.toFixed(2)}x
                            ${mentorData.conversation_flow.length_ratio < 0.5 ? '(Too brief)' :
                              mentorData.conversation_flow.length_ratio > 3 ? '(Too lengthy)' : '(Appropriate)'}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Render trust progression analysis
     */
    renderTrustProgression(mentorData) {
        if (!this.mentorState.showTrustProgression) return '';

        const trustLevel = mentorData.trust_level;
        const trustPhase = trustLevel >= 0.6 ? 'Expansion' : 'Foundation';
        const nextMilestone = trustLevel >= 0.6 ? 0.8 : 0.6;
        const progressToNext = trustLevel >= 0.6 ?
            ((trustLevel - 0.6) / 0.4) * 100 :
            (trustLevel / 0.6) * 100;

        return `
            <div class="trust-progression section">
                <h4>🤝 Trust Development Analysis</h4>
                <div class="trust-overview">
                    <div class="trust-level-display">
                        <div class="trust-circle">
                            <div class="trust-fill" style="--trust-level: ${trustLevel * 100}%">
                                <span class="trust-percentage">${this.formatValue(trustLevel, 'percentage')}</span>
                            </div>
                        </div>
                        <div class="trust-phase">
                            <span class="phase-label">Current Phase:</span>
                            <span class="phase-value ${trustPhase.toLowerCase()}">${trustPhase}</span>
                        </div>
                    </div>

                    <div class="trust-metrics">
                        <div class="trust-metric">
                            <span class="metric-label">Next Milestone:</span>
                            <span class="metric-value">${this.formatValue(nextMilestone, 'percentage')}</span>
                        </div>
                        <div class="trust-metric">
                            <span class="metric-label">Progress to Next:</span>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${progressToNext}%"></div>
                            </div>
                            <span class="metric-value">${progressToNext.toFixed(1)}%</span>
                        </div>
                    </div>
                </div>

                <div class="trust-recommendations">
                    <h5>📈 Trust Building Recommendations</h5>
                    <ul class="recommendations-list">
                        ${this.generateTrustRecommendations(trustLevel, mentorData).map(rec =>
                            `<li class="recommendation">${rec}</li>`
                        ).join('')}
                    </ul>
                </div>
            </div>
        `;
    }

    /**
     * Generate trust building recommendations
     */
    generateTrustRecommendations(trustLevel, mentorData) {
        const recommendations = [];

        if (trustLevel < 0.3) {
            recommendations.push('Focus on building basic rapport and demonstrating reliability');
            recommendations.push('Use more encouraging and supportive language');
            recommendations.push('Avoid challenging activities until trust foundation is stronger');
        } else if (trustLevel < 0.6) {
            recommendations.push('Continue building trust through consistent positive interactions');
            recommendations.push('Gradually introduce more personalized responses');
            recommendations.push('Begin incorporating gentle challenges');
        } else {
            recommendations.push('Leverage high trust for more ambitious growth activities');
            recommendations.push('Encourage deeper self-reflection and goal exploration');
            recommendations.push('Maintain trust through continued authenticity and support');
        }

        // Add specific recommendations based on response quality
        if (mentorData.response_quality.overall < 0.5) {
            recommendations.push('Improve response personalization and engagement');
        }

        if (mentorData.philosophical_framing.score < 0.3) {
            recommendations.push('Incorporate more philosophical framing and growth mindset language');
        }

        return recommendations;
    }

    /**
     * Render response quality breakdown
     */
    renderResponseQualityBreakdown(mentorData) {
        const metrics = mentorData.response_quality.metrics;

        return `
            <div class="response-quality section">
                <h4>⭐ Response Quality Breakdown</h4>
                <div class="quality-metrics">
                    ${Object.entries(metrics).map(([key, value]) => {
                        const label = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                        return `
                            <div class="quality-metric">
                                <div class="metric-header">
                                    <span class="metric-label">${label}</span>
                                    <span class="metric-score ${this.getScoreClass(value)}">${this.formatValue(value, 'percentage')}</span>
                                </div>
                                <div class="metric-bar">
                                    <div class="metric-fill ${this.getScoreClass(value)}" style="width: ${value * 100}%"></div>
                                </div>
                                <div class="metric-description">
                                    ${this.getMetricDescription(key, value)}
                                </div>
                            </div>
                        `;
                    }).join('')}
                </div>

                <div class="overall-quality">
                    <h5>🎯 Overall Quality Assessment</h5>
                    <div class="overall-score ${this.getScoreClass(mentorData.response_quality.overall)}">
                        ${this.formatValue(mentorData.response_quality.overall, 'percentage')}
                    </div>
                    <div class="quality-interpretation">
                        ${this.getQualityInterpretation(mentorData.response_quality.overall)}
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Get CSS class for score visualization
     */
    getScoreClass(score) {
        if (score >= 0.8) return 'excellent';
        if (score >= 0.6) return 'good';
        if (score >= 0.4) return 'fair';
        return 'poor';
    }

    /**
     * Get description for quality metrics
     */
    getMetricDescription(key, value) {
        const descriptions = {
            length_score: value > 0.8 ? 'Optimal response length' : value > 0.5 ? 'Adequate length' : 'Response too brief',
            personalization_score: value > 0.5 ? 'Good personalization' : 'Lacks personal connection',
            encouragement_score: value > 0.5 ? 'Encouraging tone present' : 'Could be more encouraging',
            engagement_score: value > 0.5 ? 'Engages user effectively' : 'Limited user engagement',
            goal_alignment_score: value > 0.5 ? 'Connects to user goals' : 'Weak goal connection'
        };
        return descriptions[key] || 'No description available';
    }

    /**
     * Get overall quality interpretation
     */
    getQualityInterpretation(score) {
        if (score >= 0.8) return 'Excellent mentor response with strong engagement and personalization';
        if (score >= 0.6) return 'Good mentor response with room for minor improvements';
        if (score >= 0.4) return 'Fair mentor response that needs significant improvement';
        return 'Poor mentor response requiring major enhancements';
    }

    /**
     * Render communication patterns analysis
     */
    renderCommunicationPatterns(mentorData) {
        if (!this.mentorState.showCommunicationPatterns) return '';

        return `
            <div class="communication-patterns section">
                <h4>🎭 Communication Patterns</h4>
                <div class="patterns-grid">
                    <div class="pattern-card tone-analysis">
                        <h5>🎵 Tone Analysis</h5>
                        <div class="tone-display">
                            <div class="current-tone">
                                <span class="tone-label">Current Tone:</span>
                                <span class="tone-value ${mentorData.communication_tone}">${mentorData.communication_tone}</span>
                            </div>
                            <div class="tone-appropriateness">
                                ${this.analyzeToneAppropriateness(mentorData.communication_tone, mentorData.trust_level)}
                            </div>
                        </div>
                    </div>

                    <div class="pattern-card philosophical-elements">
                        <h5>🧭 Philosophical Elements</h5>
                        <div class="elements-list">
                            ${mentorData.philosophical_framing.elements.length > 0 ?
                                mentorData.philosophical_framing.elements.map(element =>
                                    `<div class="element-item">
                                        <span class="element-icon">✓</span>
                                        <span class="element-text">${element}</span>
                                    </div>`
                                ).join('') :
                                '<div class="no-elements">No philosophical elements detected</div>'
                            }
                        </div>
                        <div class="elements-score">
                            <span class="score-label">Philosophical Depth:</span>
                            <span class="score-value">${this.formatValue(mentorData.philosophical_framing.score, 'percentage')}</span>
                        </div>
                    </div>
                </div>

                <div class="communication-recommendations">
                    <h5>💡 Communication Improvement Suggestions</h5>
                    <div class="recommendations-grid">
                        ${this.generateCommunicationRecommendations(mentorData).map(rec =>
                            `<div class="recommendation-card">
                                <div class="rec-icon">${rec.icon}</div>
                                <div class="rec-content">
                                    <div class="rec-title">${rec.title}</div>
                                    <div class="rec-description">${rec.description}</div>
                                </div>
                            </div>`
                        ).join('')}
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Analyze tone appropriateness for trust level
     */
    analyzeToneAppropriateness(tone, trustLevel) {
        const appropriateTones = {
            foundation: ['encouraging', 'supportive', 'gentle', 'warm'],
            expansion: ['encouraging', 'challenging', 'inspiring', 'confident']
        };

        const phase = trustLevel >= 0.6 ? 'expansion' : 'foundation';
        const isAppropriate = appropriateTones[phase].includes(tone.toLowerCase());

        return `
            <div class="tone-assessment ${isAppropriate ? 'appropriate' : 'inappropriate'}">
                <span class="assessment-icon">${isAppropriate ? '✅' : '⚠️'}</span>
                <span class="assessment-text">
                    ${isAppropriate ?
                        `Tone is appropriate for ${phase} phase` :
                        `Consider adjusting tone for ${phase} phase`
                    }
                </span>
            </div>
        `;
    }

    /**
     * Generate communication improvement recommendations
     */
    generateCommunicationRecommendations(mentorData) {
        const recommendations = [];

        // Trust-based recommendations
        if (mentorData.trust_level < 0.4) {
            recommendations.push({
                icon: '🤝',
                title: 'Build Trust Foundation',
                description: 'Focus on reliability, consistency, and supportive language to establish basic trust'
            });
        }

        // Quality-based recommendations
        if (mentorData.response_quality.metrics.personalization_score < 0.5) {
            recommendations.push({
                icon: '👤',
                title: 'Increase Personalization',
                description: 'Use more "you" language and reference user-specific context and goals'
            });
        }

        if (mentorData.response_quality.metrics.engagement_score < 0.5) {
            recommendations.push({
                icon: '❓',
                title: 'Improve Engagement',
                description: 'Ask more questions to encourage user participation and reflection'
            });
        }

        // Philosophical framing recommendations
        if (mentorData.philosophical_framing.score < 0.4) {
            recommendations.push({
                icon: '🧭',
                title: 'Enhance Philosophical Framing',
                description: 'Incorporate more growth mindset language and journey metaphors'
            });
        }

        // Conversation flow recommendations
        if (mentorData.conversation_flow.quality < 0.5) {
            recommendations.push({
                icon: '🔄',
                title: 'Improve Conversation Flow',
                description: 'Better acknowledge user input and maintain appropriate response length'
            });
        }

        return recommendations.slice(0, 4); // Limit to 4 recommendations
    }

    /**
     * Handle component actions
     */
    handleAction(action, element) {
        switch (action) {
            case 'filter-trust-phase':
                this.mentorState.filterByTrustPhase = element.value;
                this.update();
                break;
            case 'toggle-trust-progression':
                this.mentorState.showTrustProgression = !this.mentorState.showTrustProgression;
                this.update();
                break;
            case 'export-conversation':
                this.exportConversationData();
                break;
            default:
                super.handleAction(action, element);
        }
    }

    /**
     * Export conversation data
     */
    exportConversationData() {
        const mentorData = this.extractMentorData();
        if (mentorData) {
            const exportData = {
                conversation: {
                    user_response: mentorData.user_response,
                    mentor_response: mentorData.mentor_response
                },
                metrics: {
                    trust_level: mentorData.trust_level,
                    response_quality: mentorData.response_quality,
                    semantic_score: mentorData.semantic_score,
                    communication_tone: mentorData.communication_tone
                },
                analysis: {
                    philosophical_framing: mentorData.philosophical_framing,
                    conversation_flow: mentorData.conversation_flow
                },
                timestamp: new Date().toISOString()
            };

            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);

            const link = document.createElement('a');
            link.href = url;
            link.download = `mentor-conversation-analysis-${Date.now()}.json`;
            link.click();

            URL.revokeObjectURL(url);
        }
    }

    /**
     * Get component styles
     */
    getStyles() {
        return `
            ${super.getStyles()}

            .mentor-agent-content {
                display: flex;
                flex-direction: column;
                gap: 20px;
            }

            .mentor-summary {
                background: #f8f9fa;
                border-radius: 8px;
                padding: 20px;
            }

            .philosophical-summary {
                margin-top: 20px;
                padding: 15px;
                background: white;
                border-radius: 6px;
                border: 1px solid #e9ecef;
            }

            .philosophical-score {
                display: flex;
                align-items: center;
                gap: 15px;
                margin: 10px 0;
            }

            .score-bar {
                flex: 1;
                height: 8px;
                background: #e9ecef;
                border-radius: 4px;
                overflow: hidden;
            }

            .score-fill {
                height: 100%;
                background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
                transition: width 0.3s ease;
            }

            .score-text {
                font-weight: 600;
                color: #495057;
                min-width: 50px;
            }

            .philosophical-elements {
                display: flex;
                flex-wrap: wrap;
                gap: 8px;
                margin-top: 10px;
            }

            .element-tag {
                padding: 4px 12px;
                background: #e3f2fd;
                color: #1976d2;
                border-radius: 12px;
                font-size: 0.85em;
                font-weight: 500;
            }

            .conversation-analysis .conversation-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 20px;
                margin-bottom: 20px;
            }

            .conversation-card {
                background: white;
                border-radius: 8px;
                padding: 15px;
                border: 1px solid #e9ecef;
            }

            .conversation-card h5 {
                margin: 0 0 10px 0;
                color: #495057;
                font-size: 1em;
            }

            .message-content {
                background: #f8f9fa;
                padding: 12px;
                border-radius: 6px;
                border-left: 4px solid #007bff;
                margin-bottom: 10px;
                font-style: italic;
                line-height: 1.4;
            }

            .message-metrics {
                display: flex;
                gap: 15px;
                font-size: 0.85em;
                color: #6c757d;
            }

            .conversation-flow {
                background: white;
                border-radius: 8px;
                padding: 15px;
                border: 1px solid #e9ecef;
            }

            .flow-metrics {
                display: flex;
                flex-direction: column;
                gap: 10px;
            }

            .flow-metric {
                display: flex;
                align-items: center;
                gap: 10px;
            }

            .metric-label {
                min-width: 100px;
                font-weight: 500;
                color: #495057;
            }

            .metric-bar {
                flex: 1;
                height: 6px;
                background: #e9ecef;
                border-radius: 3px;
                overflow: hidden;
            }

            .metric-fill {
                height: 100%;
                background: #007bff;
                transition: width 0.3s ease;
            }

            .metric-value {
                min-width: 50px;
                text-align: right;
                font-weight: 600;
                color: #495057;
            }

            .trust-progression {
                background: white;
                border-radius: 8px;
                padding: 20px;
                border: 1px solid #e9ecef;
            }

            .trust-overview {
                display: grid;
                grid-template-columns: auto 1fr;
                gap: 30px;
                margin-bottom: 20px;
            }

            .trust-circle {
                width: 120px;
                height: 120px;
                border-radius: 50%;
                background: conic-gradient(#28a745 0deg, #28a745 var(--trust-level, 0%), #e9ecef var(--trust-level, 0%), #e9ecef 360deg);
                display: flex;
                align-items: center;
                justify-content: center;
                position: relative;
            }

            .trust-circle::before {
                content: '';
                width: 80px;
                height: 80px;
                background: white;
                border-radius: 50%;
                position: absolute;
            }

            .trust-percentage {
                position: relative;
                z-index: 1;
                font-size: 1.2em;
                font-weight: bold;
                color: #495057;
            }

            .trust-phase {
                display: flex;
                flex-direction: column;
                gap: 5px;
                margin-top: 10px;
            }

            .phase-value.foundation {
                color: #fd7e14;
                font-weight: 600;
            }

            .phase-value.expansion {
                color: #28a745;
                font-weight: 600;
            }

            .trust-metrics {
                display: flex;
                flex-direction: column;
                gap: 15px;
            }

            .trust-metric {
                display: flex;
                align-items: center;
                gap: 15px;
            }

            .progress-bar {
                flex: 1;
                height: 8px;
                background: #e9ecef;
                border-radius: 4px;
                overflow: hidden;
            }

            .progress-fill {
                height: 100%;
                background: #007bff;
                transition: width 0.3s ease;
            }

            .recommendations-list {
                list-style: none;
                padding: 0;
                margin: 0;
            }

            .recommendation {
                padding: 10px 15px;
                background: #f8f9fa;
                border-left: 4px solid #007bff;
                margin-bottom: 8px;
                border-radius: 0 4px 4px 0;
            }

            .quality-metrics {
                display: flex;
                flex-direction: column;
                gap: 15px;
                margin-bottom: 20px;
            }

            .quality-metric {
                background: white;
                border-radius: 6px;
                padding: 15px;
                border: 1px solid #e9ecef;
            }

            .metric-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;
            }

            .metric-score {
                font-weight: 600;
                padding: 2px 8px;
                border-radius: 12px;
                font-size: 0.85em;
            }

            .metric-score.excellent {
                background: #d4edda;
                color: #155724;
            }

            .metric-score.good {
                background: #d1ecf1;
                color: #0c5460;
            }

            .metric-score.fair {
                background: #fff3cd;
                color: #856404;
            }

            .metric-score.poor {
                background: #f8d7da;
                color: #721c24;
            }

            .metric-fill.excellent {
                background: #28a745;
            }

            .metric-fill.good {
                background: #17a2b8;
            }

            .metric-fill.fair {
                background: #ffc107;
            }

            .metric-fill.poor {
                background: #dc3545;
            }

            .overall-quality {
                text-align: center;
                padding: 20px;
                background: #f8f9fa;
                border-radius: 8px;
            }

            .overall-score {
                font-size: 2em;
                font-weight: bold;
                margin-bottom: 10px;
            }

            .patterns-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 20px;
                margin-bottom: 20px;
            }

            .pattern-card {
                background: white;
                border-radius: 8px;
                padding: 15px;
                border: 1px solid #e9ecef;
            }

            .tone-display {
                display: flex;
                flex-direction: column;
                gap: 10px;
            }

            .current-tone {
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .tone-value {
                padding: 4px 12px;
                border-radius: 12px;
                font-weight: 600;
                text-transform: capitalize;
            }

            .tone-value.encouraging {
                background: #d4edda;
                color: #155724;
            }

            .tone-value.supportive {
                background: #d1ecf1;
                color: #0c5460;
            }

            .tone-value.challenging {
                background: #fff3cd;
                color: #856404;
            }

            .tone-assessment {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 8px 12px;
                border-radius: 6px;
            }

            .tone-assessment.appropriate {
                background: #d4edda;
                color: #155724;
            }

            .tone-assessment.inappropriate {
                background: #fff3cd;
                color: #856404;
            }

            .elements-list {
                margin: 10px 0;
            }

            .element-item {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 5px 0;
            }

            .element-icon {
                color: #28a745;
                font-weight: bold;
            }

            .no-elements {
                color: #6c757d;
                font-style: italic;
                padding: 10px 0;
            }

            .recommendations-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 15px;
            }

            .recommendation-card {
                display: flex;
                gap: 12px;
                padding: 15px;
                background: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
            }

            .rec-icon {
                font-size: 1.5em;
                flex-shrink: 0;
            }

            .rec-title {
                font-weight: 600;
                color: #495057;
                margin-bottom: 5px;
            }

            .rec-description {
                font-size: 0.9em;
                color: #6c757d;
                line-height: 1.4;
            }

            @media (max-width: 768px) {
                .conversation-grid {
                    grid-template-columns: 1fr !important;
                }

                .trust-overview {
                    grid-template-columns: 1fr !important;
                    text-align: center;
                }

                .patterns-grid {
                    grid-template-columns: 1fr !important;
                }

                .recommendations-grid {
                    grid-template-columns: 1fr !important;
                }
            }
        `;
    }
}

// Register the component
if (window.componentRegistry) {
    window.componentRegistry.registerComponent('MentorAgent', MentorAgentComponent, {
        tagName: 'agent-mentor',
        dependencies: ['BaseAgentComponent'],
        metadata: {
            description: 'Displays Mentor Agent evaluation data with conversation analysis and trust metrics',
            version: '1.0.0',
            author: 'Goali System'
        }
    });
}

// Export for module use
window.MentorAgentComponent = MentorAgentComponent;