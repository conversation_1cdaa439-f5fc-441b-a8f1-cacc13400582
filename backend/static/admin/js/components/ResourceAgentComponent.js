/**
 * ResourceAgentComponent - Specialized component for displaying Resource Agent evaluation data
 * 
 * This component provides a rich, interactive display for Resource Agent benchmark results,
 * focusing on inventory items, capabilities, limitations, and resource analysis.
 * 
 * Features:
 * - Inventory visualization with categorization
 * - Capabilities matrix with skill levels
 * - Limitations analysis with severity indicators
 * - Resource availability scoring
 * - Environment context display
 * - Interactive filtering and sorting
 */

class ResourceAgentComponent extends BaseAgentComponent {
    constructor() {
        super();
        
        // Component-specific state
        this.resourceState = {
            selectedCategory: 'all',
            sortBy: 'name',
            sortOrder: 'asc',
            showDetails: false,
            filterText: ''
        };
    }
    
    /**
     * Define observed attributes
     */
    static get observedAttributes() {
        return ['data', 'title', 'expanded', 'show-details'];
    }
    
    /**
     * Get default state
     */
    getDefaultState() {
        return {
            ...super.getDefaultState(),
            showInventory: true,
            showCapabilities: true,
            showLimitations: true,
            showEnvironment: true
        };
    }
    
    /**
     * Get component title
     */
    getTitle() {
        const agentRole = this.safeGet(this.data, 'agent_role', 'Resource');
        return `🎒 ${agentRole.charAt(0).toUpperCase() + agentRole.slice(1)} Agent Analysis`;
    }
    
    /**
     * Get action buttons
     */
    getActionButtons() {
        return `
            ${super.getActionButtons()}
            <button class="btn" data-action="toggle-details">
                ${this.resourceState.showDetails ? '📋 Hide Details' : '📋 Show Details'}
            </button>
            <button class="btn" data-action="export-data">💾 Export</button>
        `;
    }
    
    /**
     * Get component content
     */
    getContent() {
        if (this.state.loading) {
            return '<div class="loading">Loading resource analysis...</div>';
        }
        
        if (this.state.error) {
            return `<div class="error">${this.state.error}</div>`;
        }
        
        const resourceContext = this.safeGet(this.data, 'raw_results.last_output.resource_context');
        if (!resourceContext) {
            return '<div class="no-data">No resource context data available</div>';
        }
        
        return `
            <div class="resource-agent-content">
                ${this.renderResourceSummary(resourceContext)}
                ${this.state.expanded ? this.renderDetailedAnalysis(resourceContext) : ''}
            </div>
        `;
    }
    
    /**
     * Render resource summary
     */
    renderResourceSummary(resourceContext) {
        const resources = resourceContext.resources || {};
        const environment = resourceContext.environment || {};
        const analysis = resourceContext.analysis_summary || {};
        
        return `
            <div class="resource-summary">
                <div class="summary-grid">
                    <div class="summary-card inventory">
                        <div class="card-icon">🎒</div>
                        <div class="card-content">
                            <div class="card-value">${resources.inventory_count || 0}</div>
                            <div class="card-label">Inventory Items</div>
                            <div class="card-detail">Available resources</div>
                        </div>
                    </div>
                    
                    <div class="summary-card capabilities">
                        <div class="card-icon">⭐</div>
                        <div class="card-content">
                            <div class="card-value">${this.safeGet(resources, 'capabilities_summary.total_skills', 0)}</div>
                            <div class="card-label">Skills</div>
                            <div class="card-detail">Identified capabilities</div>
                        </div>
                    </div>
                    
                    <div class="summary-card limitations">
                        <div class="card-icon">⚠️</div>
                        <div class="card-content">
                            <div class="card-value">${resources.limitations_count || 0}</div>
                            <div class="card-label">Limitations</div>
                            <div class="card-detail">Reported constraints</div>
                        </div>
                    </div>
                    
                    <div class="summary-card availability">
                        <div class="card-icon">📊</div>
                        <div class="card-content">
                            <div class="card-value">${this.formatValue(resources.resource_availability_score, 'percentage')}</div>
                            <div class="card-label">Availability</div>
                            <div class="card-detail">Resource score</div>
                        </div>
                    </div>
                </div>
                
                <div class="environment-summary">
                    <h4>🌍 Environment Context</h4>
                    <div class="environment-tags">
                        <span class="env-tag type">${environment.analyzed_type || 'Unknown'}</span>
                        <span class="env-tag privacy">Privacy: ${environment.privacy_level || 0}%</span>
                        <span class="env-tag size">${environment.space_size || 'Unknown'} space</span>
                        <span class="env-tag noise">${environment.noise_level || 'Unknown'} noise</span>
                    </div>
                </div>
                
                ${analysis.recommended_activity_types ? `
                <div class="recommendations-summary">
                    <h4>💡 Recommended Activities</h4>
                    <div class="activity-tags">
                        ${analysis.recommended_activity_types.map(activity => 
                            `<span class="activity-tag">${activity}</span>`
                        ).join('')}
                    </div>
                </div>
                ` : ''}
            </div>
        `;
    }
    
    /**
     * Render detailed analysis
     */
    renderDetailedAnalysis(resourceContext) {
        return `
            <div class="detailed-analysis">
                ${this.renderToolCallsSection()}
                ${this.renderInventoryDetails(resourceContext.resources)}
                ${this.renderCapabilitiesDetails(resourceContext.resources)}
                ${this.renderLimitationsDetails(resourceContext.resources)}
                ${this.renderEnvironmentDetails(resourceContext.environment)}
                ${this.renderPerformanceMetrics()}
            </div>
        `;
    }
    
    /**
     * Render inventory details
     */
    renderInventoryDetails(resources) {
        const inventory = resources.available_inventory || [];
        if (inventory.length === 0) {
            return '<div class="no-data">No inventory items available</div>';
        }
        
        // Group by category
        const categories = {};
        inventory.forEach(item => {
            const category = item.category || 'Other';
            if (!categories[category]) {
                categories[category] = [];
            }
            categories[category].push(item);
        });
        
        return `
            <div class="inventory-details section">
                <h4>🎒 Inventory Analysis</h4>
                <div class="inventory-filters">
                    <select data-action="filter-category">
                        <option value="all">All Categories</option>
                        ${Object.keys(categories).map(cat => 
                            `<option value="${cat}" ${this.resourceState.selectedCategory === cat ? 'selected' : ''}>${cat}</option>`
                        ).join('')}
                    </select>
                    <input type="text" placeholder="Filter items..." data-action="filter-text" value="${this.resourceState.filterText}">
                </div>
                
                <div class="inventory-grid">
                    ${Object.entries(categories).map(([category, items]) => {
                        if (this.resourceState.selectedCategory !== 'all' && this.resourceState.selectedCategory !== category) {
                            return '';
                        }
                        
                        return `
                            <div class="category-section">
                                <h5>${category} (${items.length})</h5>
                                <div class="items-grid">
                                    ${items.filter(item => this.matchesFilter(item)).map(item => `
                                        <div class="inventory-item">
                                            <div class="item-header">
                                                <span class="item-name">${item.name}</span>
                                                <span class="item-code">${item.code}</span>
                                            </div>
                                            <div class="item-details">
                                                <div class="item-location">📍 ${item.location}</div>
                                                <div class="item-ownership">👤 ${item.ownership}</div>
                                                ${item.notes ? `<div class="item-notes">💭 ${item.notes}</div>` : ''}
                                            </div>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        `;
                    }).join('')}
                </div>
            </div>
        `;
    }
    
    /**
     * Render capabilities details
     */
    renderCapabilitiesDetails(resources) {
        const capabilities = resources.capabilities || {};
        const domains = Object.keys(capabilities);
        
        if (domains.length === 0) {
            return '<div class="no-data">No capabilities data available</div>';
        }
        
        return `
            <div class="capabilities-details section">
                <h4>⭐ Capabilities Analysis</h4>
                ${domains.map(domain => `
                    <div class="domain-section">
                        <h5>${domain} Domain</h5>
                        <div class="skills-grid">
                            ${capabilities[domain].map(skill => `
                                <div class="skill-item">
                                    <div class="skill-header">
                                        <span class="skill-name">${skill.name}</span>
                                        <span class="skill-level level-${this.getSkillLevelClass(skill.level)}">${skill.level}</span>
                                    </div>
                                    <div class="skill-metrics">
                                        <div class="metric">
                                            <span class="metric-label">Awareness</span>
                                            <div class="metric-bar">
                                                <div class="metric-fill" style="width: ${skill.awareness}%"></div>
                                            </div>
                                            <span class="metric-value">${skill.awareness}%</span>
                                        </div>
                                        <div class="metric">
                                            <span class="metric-label">Enjoyment</span>
                                            <div class="metric-bar">
                                                <div class="metric-fill enjoyment" style="width: ${skill.enjoyment}%"></div>
                                            </div>
                                            <span class="metric-value">${skill.enjoyment}%</span>
                                        </div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }
    
    /**
     * Render limitations details
     */
    renderLimitationsDetails(resources) {
        const limitations = resources.reported_limitations || [];
        
        if (limitations.length === 0) {
            return '<div class="no-data">No limitations reported</div>';
        }
        
        return `
            <div class="limitations-details section">
                <h4>⚠️ Limitations Analysis</h4>
                <div class="limitations-grid">
                    ${limitations.map(limitation => `
                        <div class="limitation-item severity-${this.getSeverityClass(limitation.severity)}">
                            <div class="limitation-header">
                                <span class="limitation-type">${limitation.type}</span>
                                <span class="limitation-severity">${limitation.severity}/100</span>
                                <span class="limitation-status ${limitation.is_active ? 'active' : 'inactive'}">
                                    ${limitation.is_active ? '🔴 Active' : '🟢 Inactive'}
                                </span>
                            </div>
                            <div class="limitation-description">${limitation.description}</div>
                            <div class="severity-bar">
                                <div class="severity-fill" style="width: ${limitation.severity}%"></div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }
    
    /**
     * Render environment details
     */
    renderEnvironmentDetails(environment) {
        return `
            <div class="environment-details section">
                <h4>🌍 Environment Analysis</h4>
                <div class="environment-grid">
                    <div class="env-card">
                        <h5>📍 Location Context</h5>
                        <div class="env-details">
                            <div class="env-item">
                                <span class="env-label">Type:</span>
                                <span class="env-value">${environment.analyzed_type || 'Unknown'}</span>
                            </div>
                            <div class="env-item">
                                <span class="env-label">Space Size:</span>
                                <span class="env-value">${environment.space_size || 'Unknown'}</span>
                            </div>
                            <div class="env-item">
                                <span class="env-label">Privacy Level:</span>
                                <span class="env-value">${environment.privacy_level || 0}%</span>
                            </div>
                            <div class="env-item">
                                <span class="env-label">Noise Level:</span>
                                <span class="env-value">${environment.noise_level || 'Unknown'}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="env-card">
                        <h5>👥 Social Context</h5>
                        <div class="social-indicators">
                            <div class="social-item ${environment.social_context?.alone ? 'active' : 'inactive'}">
                                <span class="social-icon">🧍</span>
                                <span class="social-label">Alone</span>
                            </div>
                            <div class="social-item ${environment.social_context?.with_others ? 'active' : 'inactive'}">
                                <span class="social-icon">👥</span>
                                <span class="social-label">With Others</span>
                            </div>
                            <div class="social-item ${environment.social_context?.public_space ? 'active' : 'inactive'}">
                                <span class="social-icon">🏛️</span>
                                <span class="social-label">Public Space</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="env-card">
                        <h5>🎯 Activity Support</h5>
                        <div class="activity-support">
                            ${Object.entries(environment.activity_support || {}).map(([activity, supported]) => `
                                <div class="support-item ${supported ? 'supported' : 'not-supported'}">
                                    <span class="support-icon">${supported ? '✅' : '❌'}</span>
                                    <span class="support-label">${activity.replace('_', ' ')}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * Handle component actions
     */
    handleAction(action, element) {
        switch (action) {
            case 'toggle-details':
                this.resourceState.showDetails = !this.resourceState.showDetails;
                this.update();
                break;
            case 'export-data':
                this.exportResourceData();
                break;
            case 'filter-category':
                this.resourceState.selectedCategory = element.value;
                this.update();
                break;
            case 'filter-text':
                this.resourceState.filterText = element.value;
                this.update();
                break;
            default:
                super.handleAction(action, element);
        }
    }
    
    /**
     * Export resource data
     */
    exportResourceData() {
        const resourceContext = this.safeGet(this.data, 'raw_results.last_output.resource_context');
        if (resourceContext) {
            const dataStr = JSON.stringify(resourceContext, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);

            const link = document.createElement('a');
            link.href = url;
            link.download = `resource-agent-data-${Date.now()}.json`;
            link.click();

            URL.revokeObjectURL(url);
        }
    }

    /**
     * Export inventory data as CSV
     */
    exportInventory() {
        const inventory = this.safeGet(this.data, 'raw_results.last_output.resource_context.resources.available_inventory', []);
        if (inventory.length === 0) {
            alert('No inventory data available to export');
            return;
        }

        const csvContent = this.convertInventoryToCSV(inventory);
        this.downloadCSV(csvContent, `inventory-${Date.now()}.csv`);
    }

    /**
     * Convert inventory to CSV format
     */
    convertInventoryToCSV(inventory) {
        const headers = ['Code', 'Name', 'Category', 'Location', 'Ownership', 'Notes'];
        const rows = inventory.map(item => [
            item.code || '',
            item.name || '',
            item.category || '',
            item.location || '',
            item.ownership || '',
            item.notes || ''
        ]);

        return [headers, ...rows].map(row =>
            row.map(field => `"${field.replace(/"/g, '""')}"`).join(',')
        ).join('\n');
    }

    /**
     * Download CSV file
     */
    downloadCSV(content, filename) {
        const blob = new Blob([content], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.click();
        window.URL.revokeObjectURL(url);
    }

    /**
     * Analyze limitations in detail
     */
    analyzeLimitations() {
        const limitations = this.safeGet(this.data, 'raw_results.last_output.resource_context.resources.reported_limitations', []);
        if (limitations.length === 0) {
            alert('No limitations data available for analysis');
            return;
        }

        // Create detailed analysis
        const analysis = this.generateLimitationsAnalysis(limitations);
        console.log('Detailed limitations analysis:', analysis);

        // Show analysis in a modal or alert
        const summary = `Limitations Analysis:\n\n${analysis.summary}\n\nRecommendations:\n${analysis.recommendations.join('\n')}`;
        alert(summary);
    }

    /**
     * Generate limitations analysis
     */
    generateLimitationsAnalysis(limitations) {
        const activeLimitations = limitations.filter(l => l.is_active);
        const criticalLimitations = limitations.filter(l => l.severity >= 80);
        const avgSeverity = limitations.reduce((sum, l) => sum + l.severity, 0) / limitations.length;

        const summary = `Total: ${limitations.length}, Active: ${activeLimitations.length}, Critical: ${criticalLimitations.length}, Avg Severity: ${avgSeverity.toFixed(1)}`;

        const recommendations = [];
        if (criticalLimitations.length > 0) {
            recommendations.push('Address critical limitations immediately');
        }
        if (activeLimitations.length > limitations.length * 0.7) {
            recommendations.push('Consider reviewing limitation management strategy');
        }
        if (avgSeverity > 60) {
            recommendations.push('Focus on reducing overall limitation severity');
        }

        return { summary, recommendations };
    }

    /**
     * Generate intelligent recommendations based on resource context
     */
    generateRecommendations(resourceContext, feasibilityScore) {
        const recommendations = [];

        // Feasibility-based recommendations
        if (feasibilityScore < 0.5) {
            recommendations.push({
                priority: 'high',
                icon: '🚨',
                title: 'Low Feasibility Score',
                description: 'The current resource configuration shows low feasibility for activity generation.',
                actions: [
                    'Review and update user inventory',
                    'Address active limitations',
                    'Consider environment changes'
                ]
            });
        }

        // Inventory recommendations
        const inventoryCount = resourceContext.resources?.inventory_count || 0;
        if (inventoryCount < 5) {
            recommendations.push({
                priority: 'medium',
                icon: '📦',
                title: 'Limited Inventory',
                description: 'Low inventory count may limit activity variety and personalization.',
                actions: [
                    'Add more diverse inventory items',
                    'Include tools and equipment',
                    'Consider digital resources'
                ]
            });
        }

        // Limitations recommendations
        const limitationsCount = resourceContext.resources?.limitations_count || 0;
        if (limitationsCount > 5) {
            recommendations.push({
                priority: 'medium',
                icon: '⚠️',
                title: 'High Limitation Count',
                description: 'Multiple active limitations may significantly constrain activity options.',
                actions: [
                    'Review limitation severity levels',
                    'Consider mitigation strategies',
                    'Update limitation status if resolved'
                ]
            });
        }

        return recommendations;
    }
    
    /**
     * Check if item matches filter
     */
    matchesFilter(item) {
        if (!this.resourceState.filterText) return true;
        
        const searchText = this.resourceState.filterText.toLowerCase();
        return item.name.toLowerCase().includes(searchText) ||
               item.category.toLowerCase().includes(searchText) ||
               (item.notes && item.notes.toLowerCase().includes(searchText));
    }
    
    /**
     * Get skill level CSS class
     */
    getSkillLevelClass(level) {
        if (level >= 80) return 'expert';
        if (level >= 60) return 'advanced';
        if (level >= 40) return 'intermediate';
        if (level >= 20) return 'beginner';
        return 'novice';
    }
    
    /**
     * Get severity CSS class
     */
    getSeverityClass(severity) {
        if (severity >= 80) return 'critical';
        if (severity >= 60) return 'high';
        if (severity >= 40) return 'medium';
        return 'low';
    }

    /**
     * Render tool calls section
     */
    renderToolCallsSection() {
        const toolCallDetails = this.safeGet(this.data, 'tool_call_details', {});
        const toolBreakdown = this.safeGet(this.data, 'tool_breakdown', {});

        return `
            <div class="tool-calls-details section">
                <h4>🛠️ Tool Usage Analysis</h4>
                <div class="tool-summary">
                    <div class="tool-metrics">
                        <div class="tool-metric">
                            <span class="metric-label">Total Calls</span>
                            <span class="metric-value">${toolCallDetails.total_calls || 0}</span>
                        </div>
                        <div class="tool-metric">
                            <span class="metric-label">Real Calls</span>
                            <span class="metric-value real">${toolCallDetails.real_calls || 0}</span>
                        </div>
                        <div class="tool-metric">
                            <span class="metric-label">Mocked Calls</span>
                            <span class="metric-value mocked">${toolCallDetails.mocked_calls || 0}</span>
                        </div>
                    </div>

                    ${Object.keys(toolBreakdown).length > 0 ? `
                    <div class="tool-breakdown">
                        <h5>Tool Usage Breakdown</h5>
                        <div class="tool-list">
                            ${Object.entries(toolBreakdown).map(([tool, count]) => `
                                <div class="tool-item">
                                    <span class="tool-name">${tool}</span>
                                    <span class="tool-count">${count} calls</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                    ` : '<p class="no-tools">No tool calls recorded for this agent execution.</p>'}
                </div>
            </div>
        `;
    }

    /**
     * Render performance metrics section
     */
    renderPerformanceMetrics() {
        const duration = this.safeGet(this.data, 'mean_duration');
        const tokenUsage = this.safeGet(this.data, 'token_usage');
        const cost = this.safeGet(this.data, 'estimated_cost');
        const successRate = this.safeGet(this.data, 'success_rate');

        return `
            <div class="performance-metrics section">
                <h4>📊 Performance Metrics</h4>
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-icon">⏱️</div>
                        <div class="metric-content">
                            <div class="metric-value">${this.formatValue(duration, 'duration')}</div>
                            <div class="metric-label">Response Time</div>
                        </div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-icon">🔤</div>
                        <div class="metric-content">
                            <div class="metric-value">${tokenUsage || 'N/A'}</div>
                            <div class="metric-label">Token Usage</div>
                        </div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-icon">💰</div>
                        <div class="metric-content">
                            <div class="metric-value">${this.formatValue(cost, 'currency')}</div>
                            <div class="metric-label">Estimated Cost</div>
                        </div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-icon">✅</div>
                        <div class="metric-content">
                            <div class="metric-value">${this.formatValue(successRate, 'percentage')}</div>
                            <div class="metric-label">Success Rate</div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * Get component styles
     */
    getStyles() {
        return `
            ${super.getStyles()}
            
            .resource-agent-content {
                display: flex;
                flex-direction: column;
                gap: 20px;
            }
            
            .resource-summary {
                background: #f8f9fa;
                border-radius: 8px;
                padding: 20px;
            }
            
            .summary-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
                margin-bottom: 20px;
            }
            
            .summary-card {
                background: white;
                border-radius: 8px;
                padding: 15px;
                display: flex;
                align-items: center;
                gap: 15px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            
            .card-icon {
                font-size: 24px;
                width: 40px;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
                background: #e3f2fd;
                border-radius: 50%;
            }
            
            .card-content {
                flex: 1;
            }
            
            .card-value {
                font-size: 1.5em;
                font-weight: bold;
                color: #007bff;
            }
            
            .card-label {
                font-weight: 600;
                color: #495057;
                margin-bottom: 2px;
            }
            
            .card-detail {
                font-size: 0.85em;
                color: #6c757d;
            }
            
            .environment-summary, .recommendations-summary {
                margin-top: 15px;
            }
            
            .environment-tags, .activity-tags {
                display: flex;
                flex-wrap: wrap;
                gap: 8px;
                margin-top: 10px;
            }
            
            .env-tag, .activity-tag {
                padding: 4px 12px;
                background: #e9ecef;
                border-radius: 12px;
                font-size: 0.85em;
                font-weight: 500;
            }
            
            .detailed-analysis {
                display: flex;
                flex-direction: column;
                gap: 25px;
            }
            
            .section {
                background: white;
                border-radius: 8px;
                padding: 20px;
                border: 1px solid #e9ecef;
            }
            
            .section h4 {
                margin: 0 0 15px 0;
                color: #495057;
                font-size: 1.1em;
            }
            
            .inventory-filters {
                display: flex;
                gap: 10px;
                margin-bottom: 15px;
            }
            
            .inventory-filters select,
            .inventory-filters input {
                padding: 6px 12px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                font-size: 0.9em;
            }
            
            .inventory-grid .category-section {
                margin-bottom: 20px;
            }
            
            .items-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
                gap: 15px;
            }
            
            .inventory-item {
                background: #f8f9fa;
                border-radius: 6px;
                padding: 15px;
                border: 1px solid #e9ecef;
            }
            
            .item-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 10px;
            }
            
            .item-name {
                font-weight: 600;
                color: #495057;
            }
            
            .item-code {
                font-family: monospace;
                background: #e9ecef;
                padding: 2px 6px;
                border-radius: 3px;
                font-size: 0.8em;
            }
            
            .item-details {
                display: flex;
                flex-direction: column;
                gap: 5px;
                font-size: 0.9em;
                color: #6c757d;
            }
            
            .skills-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
                gap: 15px;
            }
            
            .skill-item {
                background: #f8f9fa;
                border-radius: 6px;
                padding: 15px;
                border: 1px solid #e9ecef;
            }
            
            .skill-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 10px;
            }
            
            .skill-name {
                font-weight: 600;
                color: #495057;
            }
            
            .skill-level {
                padding: 2px 8px;
                border-radius: 12px;
                font-size: 0.8em;
                font-weight: 600;
            }
            
            .skill-level.expert { background: #d4edda; color: #155724; }
            .skill-level.advanced { background: #d1ecf1; color: #0c5460; }
            .skill-level.intermediate { background: #fff3cd; color: #856404; }
            .skill-level.beginner { background: #f8d7da; color: #721c24; }
            .skill-level.novice { background: #e2e3e5; color: #383d41; }
            
            .skill-metrics {
                display: flex;
                flex-direction: column;
                gap: 8px;
            }
            
            .metric {
                display: flex;
                align-items: center;
                gap: 10px;
                font-size: 0.85em;
            }
            
            .metric-label {
                min-width: 80px;
                color: #6c757d;
            }
            
            .metric-bar {
                flex: 1;
                height: 6px;
                background: #e9ecef;
                border-radius: 3px;
                overflow: hidden;
            }
            
            .metric-fill {
                height: 100%;
                background: #007bff;
                transition: width 0.3s ease;
            }
            
            .metric-fill.enjoyment {
                background: #28a745;
            }
            
            .metric-value {
                min-width: 35px;
                text-align: right;
                font-weight: 600;
            }
            
            .limitations-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
                gap: 15px;
            }
            
            .limitation-item {
                background: #f8f9fa;
                border-radius: 6px;
                padding: 15px;
                border-left: 4px solid #6c757d;
            }
            
            .limitation-item.severity-critical { border-left-color: #dc3545; }
            .limitation-item.severity-high { border-left-color: #fd7e14; }
            .limitation-item.severity-medium { border-left-color: #ffc107; }
            .limitation-item.severity-low { border-left-color: #28a745; }
            
            .limitation-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;
            }
            
            .limitation-type {
                font-weight: 600;
                color: #495057;
            }
            
            .limitation-severity {
                font-weight: 600;
                color: #dc3545;
            }
            
            .limitation-status.active {
                color: #dc3545;
            }
            
            .limitation-status.inactive {
                color: #28a745;
            }
            
            .limitation-description {
                color: #6c757d;
                margin-bottom: 10px;
            }
            
            .severity-bar {
                height: 4px;
                background: #e9ecef;
                border-radius: 2px;
                overflow: hidden;
            }
            
            .severity-fill {
                height: 100%;
                background: #dc3545;
                transition: width 0.3s ease;
            }
            
            .environment-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
            }
            
            .env-card {
                background: #f8f9fa;
                border-radius: 6px;
                padding: 15px;
                border: 1px solid #e9ecef;
            }
            
            .env-card h5 {
                margin: 0 0 15px 0;
                color: #495057;
            }
            
            .env-details {
                display: flex;
                flex-direction: column;
                gap: 8px;
            }
            
            .env-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            
            .env-label {
                color: #6c757d;
                font-size: 0.9em;
            }
            
            .env-value {
                font-weight: 600;
                color: #495057;
            }
            
            .social-indicators, .activity-support {
                display: flex;
                flex-direction: column;
                gap: 8px;
            }
            
            .social-item, .support-item {
                display: flex;
                align-items: center;
                gap: 10px;
                padding: 6px 0;
            }
            
            .social-item.active, .support-item.supported {
                color: #28a745;
            }
            
            .social-item.inactive, .support-item.not-supported {
                color: #6c757d;
            }
            
            /* Tool calls section styles */
            .tool-calls-details {
                background: #f8f9fa;
                border-radius: 8px;
                padding: 20px;
                margin-bottom: 20px;
            }

            .tool-summary {
                display: flex;
                flex-direction: column;
                gap: 15px;
            }

            .tool-metrics {
                display: flex;
                gap: 20px;
                flex-wrap: wrap;
            }

            .tool-metric {
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 10px 15px;
                background: white;
                border-radius: 6px;
                border: 1px solid #e9ecef;
                min-width: 100px;
            }

            .tool-metric .metric-value {
                font-size: 1.2em;
                font-weight: 600;
                margin-bottom: 4px;
            }

            .tool-metric .metric-value.real {
                color: #28a745;
            }

            .tool-metric .metric-value.mocked {
                color: #ffc107;
            }

            .tool-breakdown h5 {
                margin: 0 0 10px 0;
                color: #495057;
            }

            .tool-list {
                display: flex;
                flex-direction: column;
                gap: 8px;
            }

            .tool-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 8px 12px;
                background: white;
                border-radius: 4px;
                border: 1px solid #e9ecef;
            }

            .tool-name {
                font-weight: 500;
                color: #495057;
            }

            .tool-count {
                font-size: 0.9em;
                color: #6c757d;
                background: #e9ecef;
                padding: 2px 6px;
                border-radius: 3px;
            }

            .no-tools {
                color: #6c757d;
                font-style: italic;
                text-align: center;
                padding: 20px;
                background: white;
                border-radius: 6px;
                border: 1px solid #e9ecef;
            }

            /* Performance metrics styles */
            .performance-metrics {
                background: #f8f9fa;
                border-radius: 8px;
                padding: 20px;
                margin-bottom: 20px;
            }

            .performance-metrics .metrics-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
            }

            .performance-metrics .metric-card {
                background: white;
                border-radius: 8px;
                padding: 15px;
                display: flex;
                align-items: center;
                gap: 12px;
                border: 1px solid #e9ecef;
                transition: transform 0.2s, box-shadow 0.2s;
            }

            .performance-metrics .metric-card:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            }

            .performance-metrics .metric-icon {
                font-size: 1.5em;
                width: 40px;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
                background: #e3f2fd;
                border-radius: 50%;
            }

            .performance-metrics .metric-content {
                flex: 1;
            }

            .performance-metrics .metric-value {
                font-size: 1.2em;
                font-weight: 600;
                color: #495057;
                margin-bottom: 2px;
            }

            .performance-metrics .metric-label {
                font-size: 0.9em;
                color: #6c757d;
            }

            /* Recommendation styles */
            .recommendation-item {
                border: 1px solid #e9ecef;
                border-radius: 6px;
                padding: 15px;
                background: white;
                margin-bottom: 10px;
            }

            .recommendation-item.high {
                border-left: 4px solid #dc3545;
            }

            .recommendation-item.medium {
                border-left: 4px solid #ffc107;
            }

            .recommendation-item.low {
                border-left: 4px solid #28a745;
            }

            @media (max-width: 768px) {
                .summary-grid {
                    grid-template-columns: 1fr;
                }

                .items-grid, .skills-grid, .limitations-grid {
                    grid-template-columns: 1fr;
                }

                .environment-grid {
                    grid-template-columns: 1fr;
                }

                .tool-metrics {
                    flex-direction: column;
                    gap: 10px;
                }

                .performance-metrics .metrics-grid {
                    grid-template-columns: 1fr;
                }
            }
        `;
    }
}

// Register the component
if (window.componentRegistry) {
    window.componentRegistry.registerComponent('ResourceAgent', ResourceAgentComponent, {
        tagName: 'agent-resource',
        dependencies: [],
        metadata: {
            description: 'Displays Resource Agent evaluation data with inventory, capabilities, and limitations',
            version: '1.0.0',
            author: 'Goali System'
        }
    });
}

// Export for module use
window.ResourceAgentComponent = ResourceAgentComponent;
