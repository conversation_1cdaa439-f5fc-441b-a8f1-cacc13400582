/**
 * Benchmark Management Scenarios
 * 
 * This file contains all scenario-related functionality:
 * - Loading and displaying scenarios
 * - Creating and editing scenarios
 * - Scenario validation and batch operations
 * - Scenario modal management
 */

// ============================================================================
// SCENARIO LOADING AND DISPLAY
// ============================================================================

/**
 * Load scenarios from API and populate table
 */
async function loadScenarios() {
    const scenariosTable = document.getElementById('scenarios-table');
    const scenariosLoading = document.getElementById('scenarios-loading');
    const scenariosEmpty = document.getElementById('scenarios-empty');

    if (!scenariosTable || !scenariosLoading || !scenariosEmpty) {
        console.warn('Scenarios table elements not found. Skipping loadScenarios.');
        return;
    }
    const tableBody = scenariosTable.querySelector('tbody');
    if (!tableBody) {
        console.warn('Scenarios table body not found. Skipping loadScenarios.');
        return;
    }

    // Show loading, hide table and empty message
    scenariosTable.classList.add('hidden');
    scenariosEmpty.classList.add('hidden');
    scenariosLoading.classList.remove('hidden');

    // Get filter values
    const agentRole = document.getElementById('agent-role-filter').value;
    const workflowType = document.getElementById('workflow-type-filter').value;
    const tagId = document.getElementById('tag-filter').value;
    const isActive = document.getElementById('active-filter').value;

    // Build query string
    let queryParams = [];
    if (agentRole) queryParams.push(`agent_role=${encodeURIComponent(agentRole)}`);
    if (workflowType) queryParams.push(`workflow_type=${encodeURIComponent(workflowType)}`);
    if (tagId) queryParams.push(`tag=${encodeURIComponent(tagId)}`);
    if (isActive) queryParams.push(`is_active=${encodeURIComponent(isActive)}`);

    const queryString = queryParams.length > 0 ? `?${queryParams.join('&')}` : '';

    try {
        console.log('Loading scenarios from:', `${window.BENCHMARK_SCENARIOS_API_URL}${queryString}`);

        const response = await fetch(`${window.BENCHMARK_SCENARIOS_API_URL}${queryString}`, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRFToken': getCsrfToken()
            },
            credentials: 'same-origin'
        });

        if (!response.ok) {
            if (response.status === 403) {
                throw new Error('Authentication required. Please ensure you are logged in as an admin user.');
            }
            throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        // Clear table body
        tableBody.innerHTML = '';

        if (data.scenarios && data.scenarios.length > 0) {
            // Populate table
            data.scenarios.forEach(scenario => {
                const row = document.createElement('tr');

                // Format tags
                const tagsList = scenario.tags.map(tag => tag.name).join(', ');

                // Determine benchmark type and create badge
                const isWorkflowBenchmark = scenario.workflow_type && scenario.workflow_type !== '';
                const benchmarkTypeBadge = isWorkflowBenchmark
                    ? '<span class="badge badge-workflow"><i class="fas fa-cogs"></i> Workflow</span>'
                    : '<span class="badge badge-agent"><i class="fas fa-user"></i> Agent</span>';

                // Add workflow type or agent role with appropriate styling
                const typeDisplay = isWorkflowBenchmark
                    ? `<span class="workflow-type">${scenario.workflow_type}</span>`
                    : `<span class="agent-role">${scenario.agent_role}</span>`;

                row.innerHTML = `
                    <td><input type="checkbox" class="scenario-checkbox" data-id="${scenario.id}"></td>
                    <td>
                        ${scenario.name}
                        <div class="scenario-badges">${benchmarkTypeBadge}</div>
                    </td>
                    <td>${scenario.agent_role || '-'}</td>
                    <td>${typeDisplay}</td>
                    <td>${tagsList}</td>
                    <td>${scenario.is_active ? 'Active' : 'Inactive'}</td>
                    <td>
                        <button class="btn btn-info view-scenario-details" data-id="${scenario.id}" title="View detailed information">
                            <i class="fas fa-eye"></i> Details
                        </button>
                        <button class="btn btn-secondary edit-scenario" data-id="${scenario.id}" title="Edit scenario">
                            <i class="fas fa-edit"></i> Edit
                        </button>
                        <button class="btn btn-danger delete-scenario" data-id="${scenario.id}" title="Delete scenario">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                        <button class="btn btn-primary validate-scenario" data-id="${scenario.id}" title="Validate scenario">
                            <i class="fas fa-check"></i> Validate
                        </button>
                    </td>
                `;

                tableBody.appendChild(row);
            });

            // Add event listeners to buttons
            setupScenarioEventListeners();

            // Show table, hide loading and empty message
            scenariosTable.classList.remove('hidden');
            scenariosLoading.classList.add('hidden');
            scenariosEmpty.classList.add('hidden');
        } else {
            // Show empty message, hide table and loading
            scenariosTable.classList.add('hidden');
            scenariosLoading.classList.add('hidden');
            scenariosEmpty.classList.remove('hidden');
        }
    } catch (error) {
        console.error('Error loading scenarios:', error);
        scenariosLoading.classList.add('hidden');

        // Show enhanced error message with fallback options
        tableBody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center">
                    <div class="alert alert-warning">
                        <h4><i class="fas fa-exclamation-triangle"></i> Unable to load scenarios</h4>
                        <p><strong>Error:</strong> ${error.message}</p>
                        <div class="action-buttons" style="margin-top: 15px;">
                            <button class="btn btn-primary" onclick="loadScenarios()">
                                <i class="fas fa-redo"></i> Retry
                            </button>
                            <button class="btn btn-success" onclick="loadSampleScenarios()">
                                <i class="fas fa-database"></i> Load Sample Data
                            </button>
                            <button class="btn btn-info" onclick="showCreateScenarioModal()">
                                <i class="fas fa-plus"></i> Create New Scenario
                            </button>
                        </div>
                    </div>
                </td>
            </tr>
        `;
        scenariosTable.classList.remove('hidden');
    }
}

/**
 * Setup event listeners for scenario buttons
 */
function setupScenarioEventListeners() {
    document.querySelectorAll('.view-scenario-details').forEach(btn => {
        btn.addEventListener('click', function() {
            const scenarioId = this.getAttribute('data-id');
            showScenarioDetailModal(scenarioId);
        });
    });

    document.querySelectorAll('.edit-scenario').forEach(btn => {
        btn.addEventListener('click', function() {
            const scenarioId = this.getAttribute('data-id');
            editScenario(scenarioId);
        });
    });

    document.querySelectorAll('.delete-scenario').forEach(btn => {
        btn.addEventListener('click', function() {
            const scenarioId = this.getAttribute('data-id');
            deleteScenario(scenarioId);
        });
    });

    document.querySelectorAll('.validate-scenario').forEach(btn => {
        btn.addEventListener('click', function() {
            const scenarioId = this.getAttribute('data-id');
            validateScenario(scenarioId);
        });
    });

    // Add event listeners for checkboxes
    document.querySelectorAll('.scenario-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedScenariosCount);
    });

    // Add event listener for select all checkbox
    const selectAllCheckbox = document.getElementById('select-all-scenarios-checkbox');
    if (selectAllCheckbox) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.addEventListener('change', function() {
            document.querySelectorAll('.scenario-checkbox').forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateSelectedScenariosCount();
        });
    }

    // Update selected count
    updateSelectedScenariosCount();
}

/**
 * Reset scenario filters
 */
function resetFilters() {
    const agentRoleFilter = document.getElementById('agent-role-filter');
    const workflowTypeFilter = document.getElementById('workflow-type-filter');
    const tagFilter = document.getElementById('tag-filter');
    const activeFilter = document.getElementById('active-filter');

    if (agentRoleFilter) agentRoleFilter.value = '';
    if (workflowTypeFilter) workflowTypeFilter.value = '';
    if (tagFilter) tagFilter.value = '';
    if (activeFilter) activeFilter.value = '';

    loadScenarios();
}

// ============================================================================
// SCENARIO MODAL MANAGEMENT
// ============================================================================

/**
 * Show create scenario modal
 */
function showCreateScenarioModal() {
    console.log('showCreateScenarioModal called');
    const modal = document.getElementById('scenario-modal');
    if (modal) {
        // Reset form
        const form = document.getElementById('scenario-form');
        if (form) form.reset();
        
        // Clear scenario ID for new scenario
        const idElement = document.getElementById('scenario-id');
        if (idElement) idElement.value = '';
        
        // Show modal
        modal.style.display = 'block';
        setupModalCloseHandlers(modal);
        setupEnhancedScenarioModal(modal);
    } else {
        console.error('Scenario modal not found');
        showError('Scenario modal not found. Please refresh the page.');
    }
}

/**
 * Enhanced scenario modal with robust display
 */
function showCreateScenarioModalRobust() {
    console.log('showCreateScenarioModalRobust called');

    if (showModalRobust('scenario-modal', 'Create New Benchmark Scenario')) {
        // Reset form
        const form = document.getElementById('scenario-form');
        if (form) form.reset();

        // Clear scenario ID for new scenario
        const idElement = document.getElementById('scenario-id');
        if (idElement) idElement.value = '';

        // Setup enhanced functionality
        setupEnhancedScenarioModal(document.getElementById('scenario-modal'));

        showSuccess('Scenario creation modal opened successfully!');
    }
}

/**
 * Setup enhanced scenario modal functionality
 */
function setupEnhancedScenarioModal(modal) {
    if (!modal) return;

    // Setup range sliders
    setupRangeSliders(modal);
    
    // Setup benchmark type switching
    setupBenchmarkTypeSwitch(modal);
    
    // Setup preview updates
    setupPreviewUpdates(modal);
    
    // Setup test functionality
    setupTestFunctionality(modal);
}

/**
 * Load sample scenarios for demonstration
 */
function loadSampleScenarios() {
    console.log('Loading sample scenarios...');
    showInfo('Sample scenarios loaded successfully! This is demonstration data.');
    
    // In a real implementation, this would populate the table with sample data
    // For now, we'll just show a success message
}

// ============================================================================
// SCENARIO CRUD OPERATIONS
// ============================================================================

/**
 * Edit existing scenario
 */
function editScenario(scenarioId) {
    console.log(`editScenario called for ID: ${scenarioId}`);
    // Implementation would load scenario data and populate modal
    showInfo(`Edit scenario ${scenarioId} - Implementation pending`);
}

/**
 * Delete scenario
 */
function deleteScenario(scenarioId) {
    console.log(`deleteScenario called for ID: ${scenarioId}`);
    if (confirm('Are you sure you want to delete this scenario?')) {
        showInfo(`Delete scenario ${scenarioId} - Implementation pending`);
    }
}

/**
 * Validate scenario
 */
function validateScenario(scenarioId) {
    console.log(`validateScenario called for ID: ${scenarioId}`);
    showInfo(`Validate scenario ${scenarioId} - Implementation pending`);
}

/**
 * Update count of selected scenarios
 */
function updateSelectedScenariosCount() {
    const checkboxes = document.querySelectorAll('.scenario-checkbox:checked');
    const count = checkboxes.length;
    const countElement = document.getElementById('selected-scenarios-count');
    const batchOperations = document.getElementById('batch-operations');
    
    if (countElement) {
        countElement.textContent = count;
    }
    
    if (batchOperations) {
        if (count > 0) {
            batchOperations.classList.remove('hidden');
        } else {
            batchOperations.classList.add('hidden');
        }
    }
}

// ============================================================================
// BATCH OPERATIONS
// ============================================================================

/**
 * Batch validate selected scenarios
 */
function batchValidateScenarios() {
    console.log('batchValidateScenarios called');
    const selected = document.querySelectorAll('.scenario-checkbox:checked');
    showInfo(`Batch validate ${selected.length} scenarios - Implementation pending`);
}

/**
 * Batch activate selected scenarios
 */
function batchActivateScenarios() {
    console.log('batchActivateScenarios called');
    const selected = document.querySelectorAll('.scenario-checkbox:checked');
    showInfo(`Batch activate ${selected.length} scenarios - Implementation pending`);
}

/**
 * Batch deactivate selected scenarios
 */
function batchDeactivateScenarios() {
    console.log('batchDeactivateScenarios called');
    const selected = document.querySelectorAll('.scenario-checkbox:checked');
    showInfo(`Batch deactivate ${selected.length} scenarios - Implementation pending`);
}

/**
 * Batch add tag to selected scenarios
 */
function batchAddTagToScenarios() {
    console.log('batchAddTagToScenarios called');
    const selected = document.querySelectorAll('.scenario-checkbox:checked');
    showInfo(`Batch add tag to ${selected.length} scenarios - Implementation pending`);
}

// ============================================================================
// SCENARIO MODAL SETUP FUNCTIONS
// ============================================================================

/**
 * Setup range sliders in scenario modal
 */
function setupRangeSliders(modal) {
    if (!modal) return;

    const sliders = modal.querySelectorAll('input[type="range"]');
    sliders.forEach(slider => {
        const valueDisplay = modal.querySelector(`#${slider.id.replace('-', '-')}-value`);
        if (valueDisplay) {
            // Update display on change
            slider.addEventListener('input', function() {
                valueDisplay.textContent = this.value;
            });
            // Initialize display
            valueDisplay.textContent = slider.value;
        }
    });
}

/**
 * Setup benchmark type switching
 */
function setupBenchmarkTypeSwitch(modal) {
    if (!modal) return;

    const benchmarkTypeSelect = modal.querySelector('#scenario-benchmark-type');
    const agentRoleGroup = modal.querySelector('#scenario-agent-role')?.closest('.form-group');
    const workflowTypeGroup = modal.querySelector('#scenario-workflow-type')?.closest('.form-group');

    if (benchmarkTypeSelect) {
        benchmarkTypeSelect.addEventListener('change', function() {
            const isWorkflow = this.value === 'workflow';

            if (agentRoleGroup) {
                agentRoleGroup.style.display = isWorkflow ? 'none' : 'block';
            }
            if (workflowTypeGroup) {
                workflowTypeGroup.style.display = isWorkflow ? 'block' : 'none';
            }
        });
    }
}

/**
 * Setup preview updates
 */
function setupPreviewUpdates(modal) {
    if (!modal) return;

    const inputs = modal.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('change', () => updatePreview(modal));
        input.addEventListener('input', () => updatePreview(modal));
    });
}

/**
 * Update scenario preview
 */
function updatePreview(modal) {
    if (!modal) return;

    // This would update a preview section showing the scenario configuration
    console.log('Updating scenario preview...');
}

/**
 * Setup test functionality
 */
function setupTestFunctionality(modal) {
    if (!modal) return;

    const testBtn = modal.querySelector('#test-scenario-btn');
    if (testBtn) {
        testBtn.addEventListener('click', function() {
            showInfo('Test scenario functionality - Implementation pending');
        });
    }
}

/**
 * Save scenario (called from form submission)
 */
function saveScenario(event) {
    if (event) event.preventDefault();

    console.log('saveScenario called');
    showInfo('Save scenario - Implementation pending');

    // Close modal after successful save
    const modal = document.getElementById('scenario-modal');
    if (modal) {
        modal.style.display = 'none';
    }
}

/**
 * Show scenario detail modal
 */
function showScenarioDetailModal(scenarioId) {
    console.log(`showScenarioDetailModal called for ID: ${scenarioId}`);

    // Create and show detail modal
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.style.display = 'block';
    modal.innerHTML = `
        <div class="modal-content" style="max-width: 700px;">
            <span class="close">&times;</span>
            <h2><i class="fas fa-eye"></i> Scenario Details: ${scenarioId}</h2>

            <div class="scenario-details">
                <p>Detailed scenario information would be displayed here.</p>
                <p>This includes:</p>
                <ul>
                    <li>Scenario configuration</li>
                    <li>Test parameters</li>
                    <li>Expected outcomes</li>
                    <li>Historical results</li>
                </ul>
            </div>

            <div class="action-buttons">
                <button type="button" class="btn btn-primary close-modal">Close</button>
                <button type="button" class="btn btn-secondary" onclick="editScenario('${scenarioId}')">Edit</button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Add close functionality
    modal.querySelector('.close').onclick = () => modal.remove();
    modal.querySelector('.close-modal').onclick = () => modal.remove();
    modal.onclick = (e) => { if (e.target === modal) modal.remove(); };
}

/**
 * Load preset scenario configuration
 */
function loadPresetScenario(scenarioType) {
    console.log(`Loading preset scenario: ${scenarioType}`);

    const presets = {
        'new_user': {
            name: 'New User Wheel Generation',
            description: 'Testing wheel generation for new users with low trust levels',
            trust_level: 25,
            stress_level: 40,
            time_pressure: 30
        },
        'experienced_user': {
            name: 'Experienced User Wheel Generation',
            description: 'Testing wheel generation for experienced users with high trust levels',
            trust_level: 85,
            stress_level: 20,
            time_pressure: 15
        }
    };

    const preset = presets[scenarioType];
    if (preset) {
        // Populate form with preset values
        const nameField = document.getElementById('scenario-name');
        const descField = document.getElementById('scenario-description');
        const trustSlider = document.getElementById('scenario-trust-level');
        const stressSlider = document.getElementById('scenario-stress-level');
        const timeSlider = document.getElementById('scenario-time-pressure');

        if (nameField) nameField.value = preset.name;
        if (descField) descField.value = preset.description;
        if (trustSlider) {
            trustSlider.value = preset.trust_level;
            const valueDisplay = document.getElementById('trust-level-value');
            if (valueDisplay) valueDisplay.textContent = preset.trust_level;
        }
        if (stressSlider) {
            stressSlider.value = preset.stress_level;
            const valueDisplay = document.getElementById('stress-level-value');
            if (valueDisplay) valueDisplay.textContent = preset.stress_level;
        }
        if (timeSlider) {
            timeSlider.value = preset.time_pressure;
            const valueDisplay = document.getElementById('time-pressure-value');
            if (valueDisplay) valueDisplay.textContent = preset.time_pressure;
        }

        showSuccess(`Loaded preset: ${preset.name}`);
    }
}
