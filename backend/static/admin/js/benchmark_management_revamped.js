/**
 * Revamped Benchmark Management JavaScript
 * Following the working quick-test modal pattern for reliable functionality
 */

// Global state
let currentTab = 'scenarios';

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Benchmark Management Revamped - Initializing...');

    initializeTabs();
    initializeModals();
    initializeEventListeners();
    initializeQuickTest();

    console.log('✅ Benchmark Management Revamped - Initialized successfully');
});

/**
 * Initialize QuickTest functionality
 */
function initializeQuickTest() {
    console.log('⚡ Initializing QuickTest...');

    // Check if QuickTest class is available
    if (typeof QuickTest === 'undefined') {
        console.error('❌ QuickTest class not found! Make sure quick_test.js is loaded.');
        return;
    }

    try {
        // Initialize QuickTest instance
        window.quickTest = new QuickTest();
        console.log('✅ QuickTest initialized successfully');
    } catch (error) {
        console.error('❌ Failed to initialize QuickTest:', error);
    }
}

/**
 * Initialize tab navigation (simplified approach)
 */
function initializeTabs() {
    console.log('🔧 Initializing tabs...');

    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');

    console.log(`Found ${tabButtons.length} tab buttons and ${tabContents.length} tab contents`);

    if (tabButtons.length === 0) {
        console.warn('No tab buttons found');
        return;
    }

    tabButtons.forEach((button, index) => {
        const tabId = button.getAttribute('data-tab');
        console.log(`Setting up tab button ${index}: ${tabId}`);

        button.addEventListener('click', function() {
            const targetTabId = this.getAttribute('data-tab');
            console.log(`Tab clicked: ${targetTabId}`);
            switchTab(targetTabId);
        });
    });

    // Show initial tab
    console.log('Setting initial active tab to: scenarios');
    switchTab('scenarios');
}

/**
 * Switch between tabs (simple and reliable)
 */
function switchTab(tabId) {
    console.log(`🔄 Switching to tab: ${tabId}`);

    // Update button states
    const tabButtons = document.querySelectorAll('.tab-button');
    console.log(`Found ${tabButtons.length} tab buttons to update`);

    tabButtons.forEach(btn => {
        btn.classList.remove('active');
    });

    const activeButton = document.querySelector(`[data-tab="${tabId}"]`);
    if (activeButton) {
        activeButton.classList.add('active');
        console.log(`✅ Activated button for tab: ${tabId}`);
    } else {
        console.error(`❌ Button not found for tab: ${tabId}`);
    }

    // Update content visibility
    const tabContents = document.querySelectorAll('.tab-content');
    console.log(`Found ${tabContents.length} tab contents to update`);

    tabContents.forEach(content => {
        content.classList.remove('active');
        content.style.display = 'none';
    });

    const targetContent = document.getElementById(tabId);
    if (targetContent) {
        targetContent.classList.add('active');
        targetContent.style.display = 'block';
        console.log(`✅ Activated content for tab: ${tabId}`);
    } else {
        console.error(`❌ Content not found for tab: ${tabId}`);
    }

    currentTab = tabId;
    console.log(`🎯 Current tab set to: ${currentTab}`);
}

/**
 * Initialize modal functionality (following quick-test pattern)
 */
function initializeModals() {
    // Close modals when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target.classList.contains('modal')) {
            hideAllModals();
        }
    });
    
    // Close modals with Escape key
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            hideAllModals();
        }
    });
}

/**
 * Initialize event listeners for buttons and forms
 */
function initializeEventListeners() {
    // Scenario management buttons
    const createScenarioBtn = document.getElementById('create-scenario-btn');
    if (createScenarioBtn) {
        createScenarioBtn.addEventListener('click', showScenarioModal);
    }
    
    const importScenarioBtn = document.getElementById('import-scenario-btn');
    if (importScenarioBtn) {
        importScenarioBtn.addEventListener('click', showImportModal);
    }
    
    // Template management buttons
    const createTemplateBtn = document.getElementById('create-template-btn');
    if (createTemplateBtn) {
        createTemplateBtn.addEventListener('click', showTemplateModal);
    }
    
    // User profile management buttons
    const createProfileBtn = document.getElementById('create-profile-btn');
    if (createProfileBtn) {
        createProfileBtn.addEventListener('click', showUserProfileModal);
    }
    
    // Help buttons
    const conceptHelpBtn = document.getElementById('concept-help-btn');
    if (conceptHelpBtn) {
        conceptHelpBtn.addEventListener('click', showConceptModal);
    }
    
    // Form submissions
    initializeFormHandlers();
}

/**
 * Initialize form submission handlers
 */
function initializeFormHandlers() {
    // Scenario form
    const scenarioForm = document.getElementById('scenario-form');
    if (scenarioForm) {
        scenarioForm.addEventListener('submit', handleScenarioSubmit);
    }
    
    // Template form
    const templateForm = document.getElementById('template-form');
    if (templateForm) {
        templateForm.addEventListener('submit', handleTemplateSubmit);
    }
    
    // User profile form
    const userProfileForm = document.getElementById('user-profile-form');
    if (userProfileForm) {
        userProfileForm.addEventListener('submit', handleUserProfileSubmit);
    }
    
    // Import form
    const importForm = document.getElementById('import-scenario-form');
    if (importForm) {
        importForm.addEventListener('submit', handleImportSubmit);
    }
}

/**
 * Modal display functions (following quick-test pattern)
 */
function showScenarioModal() {
    console.log('📝 Opening scenario modal...');
    const modal = document.getElementById('scenario-modal');
    if (modal) {
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden'; // Prevent background scrolling
    }
}

function hideScenarioModal() {
    console.log('❌ Closing scenario modal...');
    const modal = document.getElementById('scenario-modal');
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = ''; // Restore scrolling
    }
}

function showTemplateModal() {
    console.log('📋 Opening template modal...');
    const modal = document.getElementById('template-modal');
    if (modal) {
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden';
    }
}

function hideTemplateModal() {
    console.log('❌ Closing template modal...');
    const modal = document.getElementById('template-modal');
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = '';
    }
}

function showUserProfileModal() {
    console.log('👤 Opening user profile modal...');
    const modal = document.getElementById('user-profile-modal');
    if (modal) {
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden';
    }
}

function hideUserProfileModal() {
    console.log('❌ Closing user profile modal...');
    const modal = document.getElementById('user-profile-modal');
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = '';
    }
}

function showImportModal() {
    console.log('📥 Opening import modal...');
    const modal = document.getElementById('import-scenario-modal');
    if (modal) {
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden';
    }
}

function hideImportModal() {
    console.log('❌ Closing import modal...');
    const modal = document.getElementById('import-scenario-modal');
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = '';
    }
}

function showConceptModal() {
    console.log('💡 Opening concept help modal...');
    const modal = document.getElementById('concept-help-modal');
    if (modal) {
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden';
    }
}

function hideConceptModal() {
    console.log('❌ Closing concept help modal...');
    const modal = document.getElementById('concept-help-modal');
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = '';
    }
}

function hideAllModals() {
    hideScenarioModal();
    hideTemplateModal();
    hideUserProfileModal();
    hideImportModal();
    hideConceptModal();
}

/**
 * Form submission handlers
 */
function handleScenarioSubmit(event) {
    event.preventDefault();
    console.log('💾 Submitting scenario form...');
    
    const formData = new FormData(event.target);
    const scenarioData = {
        name: formData.get('scenario-name') || document.getElementById('scenario-name').value,
        description: formData.get('scenario-description') || document.getElementById('scenario-description').value,
        agent_role: formData.get('scenario-agent-role') || document.getElementById('scenario-agent-role').value,
        workflow_type: formData.get('scenario-workflow-type') || document.getElementById('scenario-workflow-type').value,
        tags: formData.get('scenario-tags') || document.getElementById('scenario-tags').value,
        is_active: formData.get('scenario-is-active') || document.getElementById('scenario-is-active').value,
        input_data: formData.get('scenario-input-data') || document.getElementById('scenario-input-data').value,
        metadata: formData.get('scenario-metadata') || document.getElementById('scenario-metadata').value
    };
    
    console.log('Scenario data:', scenarioData);
    
    // TODO: Implement actual API call
    alert('Scenario form submitted! (Implementation needed)');
    hideScenarioModal();
}

function handleTemplateSubmit(event) {
    event.preventDefault();
    console.log('💾 Submitting template form...');
    
    // TODO: Implement template submission
    alert('Template form submitted! (Implementation needed)');
    hideTemplateModal();
}

function handleUserProfileSubmit(event) {
    event.preventDefault();
    console.log('💾 Submitting user profile form...');
    
    // TODO: Implement user profile submission
    alert('User profile form submitted! (Implementation needed)');
    hideUserProfileModal();
}

function handleImportSubmit(event) {
    event.preventDefault();
    console.log('📥 Submitting import form...');
    
    // TODO: Implement import functionality
    alert('Import form submitted! (Implementation needed)');
    hideImportModal();
}

// Make functions globally available for onclick handlers
window.showScenarioModal = showScenarioModal;
window.hideScenarioModal = hideScenarioModal;
window.showTemplateModal = showTemplateModal;
window.hideTemplateModal = hideTemplateModal;
window.showUserProfileModal = showUserProfileModal;
window.hideUserProfileModal = hideUserProfileModal;
window.showImportModal = showImportModal;
window.hideImportModal = hideImportModal;
window.showConceptModal = showConceptModal;
window.hideConceptModal = hideConceptModal;
window.switchTab = switchTab;

// Quick Test modal functions (delegate to QuickTest instance)
window.showQuickTestModal = function() {
    console.log('🔧 Opening quick test modal...');
    if (window.quickTest) {
        window.quickTest.showConfig();
    } else {
        console.error('❌ QuickTest instance not available');
    }
};

window.hideQuickTestModal = function() {
    console.log('❌ Closing quick test modal...');
    if (window.quickTest) {
        window.quickTest.hideConfig();
    } else {
        console.error('❌ QuickTest instance not available');
    }
};

console.log('📄 Benchmark Management Revamped JS loaded');
