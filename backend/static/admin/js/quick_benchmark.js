/**
 * Quick Benchmark System JavaScript
 * Extracted from benchmark_management.html for better maintainability
 */

// Quick Benchmark Configuration Storage
const QUICK_BENCHMARK_CONFIG_KEY = 'quick_benchmark_last_config';

// Quick Benchmark Functionality
document.addEventListener('DOMContentLoaded', function() {
    // Initialize quick benchmark when tab is selected
    const quickBenchmarkTab = document.querySelector('[data-tab="quick-benchmark"]');
    if (quickBenchmarkTab) {
        quickBenchmarkTab.addEventListener('click', function() {
            setTimeout(initializeQuickBenchmark, 100);
        });
    }
});

function initializeQuickBenchmark() {
    console.log('Initializing Quick Benchmark functionality...');

    // Load available options
    loadQuickBenchmarkOptions();

    // Setup form submission
    setupQuickBenchmarkForm();
    
    // Restore previous configuration
    restorePreviousConfiguration();
}

function loadQuickBenchmarkOptions() {
    console.log('Loading quick benchmark options...');

    fetch(window.QUICK_BENCHMARK_API_URL)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.options) {
                populateQuickBenchmarkOptions(data.options);
            } else {
                console.error('Failed to load options:', data);
                showQuickBenchmarkError('Failed to load benchmark options');
            }
        })
        .catch(error => {
            console.error('Error loading options:', error);
            showQuickBenchmarkError('Error loading benchmark options: ' + error.message);
        });
}

function populateQuickBenchmarkOptions(options) {
    console.log('Populating options:', options);

    // Populate workflows
    const workflowSelect = document.getElementById('quick-workflow-type');
    if (workflowSelect && options.available_workflows) {
        workflowSelect.innerHTML = '<option value="">Select Workflow...</option>';
        options.available_workflows.forEach(workflow => {
            const option = document.createElement('option');
            option.value = workflow.value;
            option.textContent = workflow.label;
            workflowSelect.appendChild(option);
        });
    }

    // Populate agents
    const agentSelect = document.getElementById('quick-agent-name');
    if (agentSelect && options.available_agents) {
        agentSelect.innerHTML = '<option value="">Select Agent...</option>';
        options.available_agents.forEach(agent => {
            const option = document.createElement('option');
            option.value = agent.role;
            option.textContent = `${agent.role} (${agent.description.substring(0, 50)}...)`;
            agentSelect.appendChild(option);
        });
    }

    // Populate user profiles (real users, not templates)
    const profileSelect = document.getElementById('quick-profile-template');
    if (profileSelect && options.user_profiles) {
        profileSelect.innerHTML = '<option value="">Select User Profile...</option>';
        
        // Group profiles by type
        const realProfiles = options.user_profiles.filter(profile => profile.is_real);
        const testProfiles = options.user_profiles.filter(profile => !profile.is_real);
        
        if (realProfiles.length > 0) {
            const realGroup = document.createElement('optgroup');
            realGroup.label = 'Real Users';
            realProfiles.forEach(profile => {
                const option = document.createElement('option');
                option.value = profile.id;
                option.textContent = `${profile.profile_name} (Real User)`;
                option.title = profile.description || 'Real user profile';
                realGroup.appendChild(option);
            });
            profileSelect.appendChild(realGroup);
        }
        
        if (testProfiles.length > 0) {
            const testGroup = document.createElement('optgroup');
            testGroup.label = 'Test Profiles';
            testProfiles.forEach(profile => {
                const option = document.createElement('option');
                option.value = profile.id;
                option.textContent = `${profile.profile_name} (Test)`;
                option.title = profile.description || 'Test profile for benchmarking';
                testGroup.appendChild(option);
            });
            profileSelect.appendChild(testGroup);
        }
    }

    // Populate evaluation templates
    const evaluationSelect = document.getElementById('quick-evaluation-template');
    if (evaluationSelect && options.evaluation_templates) {
        evaluationSelect.innerHTML = '<option value="">Select Evaluation...</option>';
        options.evaluation_templates.forEach(template => {
            const option = document.createElement('option');
            option.value = template;
            option.textContent = template.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
            evaluationSelect.appendChild(option);
        });
    }

    // Setup workflow and agent change handlers for evaluation context loading
    setupWorkflowAgentHandlers();

    console.log('Options populated successfully');
}

function setupWorkflowAgentHandlers() {
    const workflowSelect = document.getElementById('quick-workflow-type');
    const agentSelect = document.getElementById('quick-agent-name');

    if (workflowSelect) {
        workflowSelect.addEventListener('change', function() {
            loadEvaluationContexts();
        });
    }

    if (agentSelect) {
        agentSelect.addEventListener('change', function() {
            loadEvaluationContexts();
        });
    }
}

function loadEvaluationContexts() {
    const workflowType = document.getElementById('quick-workflow-type')?.value;
    const agentRole = document.getElementById('quick-agent-name')?.value;
    const contextSelect = document.getElementById('quick-evaluation-context');

    if (!contextSelect) return;

    // Clear existing options
    contextSelect.innerHTML = '<option value="">Select Context (Optional)...</option>';

    if (!workflowType && !agentRole) {
        return;
    }

    // Build API URL based on available filters
    let apiUrl = '/admin/benchmarks/api/evaluation-contexts/';
    const params = new URLSearchParams();

    if (workflowType) {
        params.append('workflow_type', workflowType);
    }
    if (agentRole) {
        params.append('agent_role', agentRole);
    }

    if (params.toString()) {
        apiUrl += '?' + params.toString();
    }

    fetch(apiUrl)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.contexts) {
                data.contexts.forEach(context => {
                    const option = document.createElement('option');
                    option.value = context.id;
                    option.textContent = `${context.name} (${context.trust_phase})`;
                    option.title = context.description;
                    contextSelect.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.warn('Failed to load evaluation contexts:', error);
        });
}

function setupQuickBenchmarkForm() {
    const form = document.getElementById('quick-benchmark-form');
    const refreshBtn = document.getElementById('load-quick-options-btn');

    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            runQuickBenchmark();
        });
        
        // Save configuration on form changes
        form.addEventListener('change', saveCurrentConfiguration);
    }

    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            loadQuickBenchmarkOptions();
        });
    }
}

function saveCurrentConfiguration() {
    const config = {
        agent_name: document.getElementById('quick-agent-name')?.value || '',
        workflow_type: document.getElementById('quick-workflow-type')?.value || '',
        evaluation_context_id: document.getElementById('quick-evaluation-context')?.value || '',
        profile_id: document.getElementById('quick-profile-template')?.value || '',
        evaluation_template: document.getElementById('quick-evaluation-template')?.value || '',
        user_input: document.getElementById('quick-user-input')?.value || '',
        timestamp: new Date().toISOString()
    };

    localStorage.setItem(QUICK_BENCHMARK_CONFIG_KEY, JSON.stringify(config));
    console.log('Saved configuration:', config);
}

function restorePreviousConfiguration() {
    try {
        const savedConfig = localStorage.getItem(QUICK_BENCHMARK_CONFIG_KEY);
        if (savedConfig) {
            const config = JSON.parse(savedConfig);
            console.log('Restoring configuration:', config);

            // Restore form values
            const workflowSelect = document.getElementById('quick-workflow-type');
            const agentSelect = document.getElementById('quick-agent-name');
            const contextSelect = document.getElementById('quick-evaluation-context');
            const profileSelect = document.getElementById('quick-profile-template');
            const evaluationSelect = document.getElementById('quick-evaluation-template');
            const userInput = document.getElementById('quick-user-input');

            if (workflowSelect && config.workflow_type) {
                workflowSelect.value = config.workflow_type;
            }
            if (agentSelect && config.agent_name) {
                agentSelect.value = config.agent_name;
            }
            if (contextSelect && config.evaluation_context_id) {
                contextSelect.value = config.evaluation_context_id;
            }
            if (profileSelect && config.profile_id) {
                profileSelect.value = config.profile_id;
            }
            if (evaluationSelect && config.evaluation_template) {
                evaluationSelect.value = config.evaluation_template;
            }
            if (userInput && config.user_input) {
                userInput.value = config.user_input;
            }

            // Trigger evaluation context loading if workflow/agent are restored
            if (config.workflow_type || config.agent_name) {
                setTimeout(loadEvaluationContexts, 100);
            }
        }
    } catch (error) {
        console.warn('Failed to restore previous configuration:', error);
    }
}

function runQuickBenchmark() {
    console.log('Running quick benchmark...');

    // Get form data
    const workflowType = document.getElementById('quick-workflow-type').value;
    const agentName = document.getElementById('quick-agent-name').value;
    const evaluationContextId = document.getElementById('quick-evaluation-context').value;
    const profileId = document.getElementById('quick-profile-template').value;
    const evaluationTemplate = document.getElementById('quick-evaluation-template').value;
    const userInput = document.getElementById('quick-user-input').value;

    // Validate required fields
    if (!agentName || !profileId || !evaluationTemplate) {
        showQuickBenchmarkError('Please fill in all required fields');
        return;
    }

    // Save current configuration
    saveCurrentConfiguration();

    // Show status
    showQuickBenchmarkStatus('Initializing benchmark...');
    updateQuickBenchmarkProgress(10);

    // Determine benchmark type and prepare request data
    let requestData;
    let apiEndpoint = window.QUICK_BENCHMARK_API_URL;

    if (workflowType && evaluationContextId) {
        // Workflow-aware benchmarking
        showQuickBenchmarkStatus('Running workflow-aware benchmark...');
        requestData = {
            agent_role: agentName,
            workflow_type: workflowType,
            evaluation_context_id: evaluationContextId,
            user_profile_id: profileId,
            evaluation_template: evaluationTemplate,
            use_real_tools: true,
            use_real_db: true
        };
        apiEndpoint = '/admin/benchmarks/api/workflow-benchmark/';
    } else {
        // Standard agent benchmarking
        requestData = {
            agent_name: agentName,
            user_profile_id: profileId,
            evaluation_template: evaluationTemplate,
            use_real_tools: true,
            use_real_db: true
        };

        if (workflowType) {
            requestData.workflow_type = workflowType;
        }
    }

    if (userInput.trim()) {
        requestData.scenario_context = {
            user_input: userInput.trim()
        };
    }

    // Submit benchmark
    fetch(apiEndpoint, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showQuickBenchmarkResults(data);
        } else {
            showQuickBenchmarkError(data.error || 'Benchmark execution failed');
        }
    })
    .catch(error => {
        console.error('Benchmark error:', error);
        showQuickBenchmarkError('Error running benchmark: ' + error.message);
    });
}

function showQuickBenchmarkStatus(message) {
    const statusDiv = document.getElementById('quick-benchmark-status');
    const statusText = document.getElementById('quick-benchmark-status-text');
    const resultsDiv = document.getElementById('quick-benchmark-results');

    if (statusDiv) {
        statusDiv.classList.remove('hidden');
    }
    if (statusText) {
        statusText.textContent = message;
    }
    if (resultsDiv) {
        resultsDiv.classList.add('hidden');
    }
}

function updateQuickBenchmarkProgress(percentage) {
    const progressBar = document.getElementById('quick-benchmark-progress');
    if (progressBar) {
        progressBar.style.width = percentage + '%';
    }
}

function showQuickBenchmarkResults(data) {
    console.log('Showing benchmark results:', data);

    const statusDiv = document.getElementById('quick-benchmark-status');
    const resultsDiv = document.getElementById('quick-benchmark-results');
    const resultsContent = document.getElementById('quick-benchmark-results-content');
    const viewResultsBtn = document.getElementById('view-detailed-results-btn');

    // Update status to completion before hiding
    showQuickBenchmarkStatus('Benchmark completed successfully!');
    updateQuickBenchmarkProgress(100);

    // Small delay to show completion status, then hide and show results
    setTimeout(() => {
        if (statusDiv) {
            statusDiv.classList.add('hidden');
        }
        if (resultsDiv) {
            resultsDiv.classList.remove('hidden');
        }
    }, 1000);

    // Populate results
    if (resultsContent) {
        const summary = data.results_summary || {};
        resultsContent.innerHTML = `
            <div class="benchmark-results-summary">
                <div class="result-item">
                    <strong>Benchmark Run ID:</strong> ${data.benchmark_run_id || 'N/A'}
                </div>
                <div class="result-item">
                    <strong>Execution Time:</strong> ${summary.execution_time || 'N/A'}s
                </div>
                <div class="result-item">
                    <strong>Token Usage:</strong> ${summary.token_usage || 'N/A'}
                </div>
                <div class="result-item">
                    <strong>Estimated Cost:</strong> ${summary.cost_estimate || 'N/A'}
                </div>
                <div class="result-item">
                    <strong>Output Length:</strong> ${summary.agent_output_length || 'N/A'} characters
                </div>
                <div class="result-item">
                    <strong>Enhanced Debugging:</strong> 
                    <span class="badge ${summary.has_enhanced_debugging ? 'badge-success' : 'badge-warning'}">
                        ${summary.has_enhanced_debugging ? 'Available' : 'Limited'}
                    </span>
                </div>
            </div>
        `;
    }

    // Set up view detailed results button to open agent evaluation modal
    if (viewResultsBtn && data.benchmark_run_id) {
        viewResultsBtn.onclick = function() {
            // Wait for components to load, then try enhanced modal first
            if (window.componentLoader) {
                window.componentLoader.waitForComponent('ComponentRegistry', 3000)
                    .then(() => {
                        if (typeof openEnhancedAgentEvaluationModal === 'function' && window.componentRegistry) {
                            console.log('Opening enhanced agent evaluation modal');
                            openEnhancedAgentEvaluationModal(data.benchmark_run_id);
                        } else {
                            console.log('Enhanced modal not available, using legacy modal');
                            openAgentEvaluationModal(data.benchmark_run_id);
                        }
                    })
                    .catch(error => {
                        console.warn('Component loading timeout, using legacy modal:', error);
                        openAgentEvaluationModal(data.benchmark_run_id);
                    });
            } else {
                // Fallback to legacy modal if component loader not available
                console.log('Component loader not available, using legacy modal');
                openAgentEvaluationModal(data.benchmark_run_id);
            }
        };
        viewResultsBtn.style.display = 'inline-block';
    }

    // Setup run another benchmark button
    const runAnotherBtn = document.getElementById('run-another-benchmark-btn');
    if (runAnotherBtn) {
        runAnotherBtn.addEventListener('click', function() {
            resultsDiv.classList.add('hidden');
            // Don't reset the form - keep the previous configuration
        });
    }
}

function openAgentEvaluationModal(benchmarkRunId) {
    console.log('Opening agent evaluation modal for run:', benchmarkRunId);

    // Check if modal elements exist
    const modal = document.getElementById('agent-details-modal');
    const modalBody = document.getElementById('agent-modal-body');

    if (!modal || !modalBody) {
        console.error('Agent modal elements not found on page');
        showQuickBenchmarkError('Agent evaluation modal not available on this page');
        return;
    }

    // Check if renderAgentDetails function is available
    if (typeof window.renderAgentDetails !== 'function') {
        console.error('renderAgentDetails function not found');
        showQuickBenchmarkError('Agent evaluation modal functions not loaded');
        return;
    }

    // Show modal with loading state
    modal.style.display = 'block';
    modalBody.innerHTML = '<div class="loader"></div> Loading benchmark details...';

    // Fetch benchmark run data
    fetch(`/admin/benchmarks/api/run/${benchmarkRunId}/`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Fetched benchmark run data:', data);

            // Validate data structure
            if (data && data.id) {
                // Render the agent details using the global function
                window.renderAgentDetails(modalBody, data, benchmarkRunId);
            } else {
                throw new Error('Invalid benchmark run data structure');
            }
        })
        .catch(error => {
            console.error('Error fetching benchmark details:', error);
            modalBody.innerHTML = `
                <div class="alert alert-danger">
                    <h4>Error Loading Benchmark Details</h4>
                    <p>${error.message}</p>
                    <button class="btn btn-secondary" onclick="document.getElementById('agent-details-modal').style.display='none'">Close</button>
                </div>
            `;
        });
}

// Note: Agent evaluation modal is included directly in the benchmark management page
// No need for dynamic loading

function showQuickBenchmarkError(message) {
    console.error('Quick benchmark error:', message);

    const statusDiv = document.getElementById('quick-benchmark-status');
    const statusText = document.getElementById('quick-benchmark-status-text');

    if (statusDiv) {
        statusDiv.classList.remove('hidden');
        statusDiv.style.borderLeftColor = '#dc3545';
    }
    if (statusText) {
        statusText.innerHTML = `<span style="color: #dc3545;">❌ Error: ${message}</span>`;
    }

    updateQuickBenchmarkProgress(0);
}

// Utility function to get CSRF token
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
