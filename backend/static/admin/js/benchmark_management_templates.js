/**
 * Benchmark Management Templates
 * 
 * This file contains all template-related functionality:
 * - Loading and displaying evaluation templates
 * - Creating and editing templates
 * - Template validation and batch operations
 * - Contextual criteria builder
 * - Variable ranges management
 */

// ============================================================================
// TEMPLATE LOADING AND DISPLAY
// ============================================================================

/**
 * Load templates from API and populate table
 */
async function loadTemplates() {
    const templatesTable = document.getElementById('templates-table');
    const templatesLoading = document.getElementById('templates-loading');
    const templatesEmpty = document.getElementById('templates-empty');

    if (!templatesTable || !templatesLoading || !templatesEmpty) {
        console.warn('Templates table elements not found. Skipping loadTemplates.');
        return;
    }
    const tableBody = templatesTable.querySelector('tbody');
    if (!tableBody) {
        console.warn('Templates table body not found. Skipping loadTemplates.');
        return;
    }

    // Show loading, hide table and empty message
    templatesTable.classList.add('hidden');
    templatesEmpty.classList.add('hidden');
    templatesLoading.classList.remove('hidden');

    // Get filter values
    const workflowType = document.getElementById('template-workflow-type-filter')?.value || '';
    const status = document.getElementById('template-status-filter')?.value || '';
    const category = document.getElementById('template-category-filter')?.value || '';

    // Build query string
    let queryParams = [];
    if (workflowType) queryParams.push(`workflow_type=${encodeURIComponent(workflowType)}`);
    if (status) queryParams.push(`status=${encodeURIComponent(status)}`);
    if (category) queryParams.push(`category=${encodeURIComponent(category)}`);

    const queryString = queryParams.length > 0 ? `?${queryParams.join('&')}` : '';

    try {
        console.log('Loading templates from:', `${window.TEMPLATES_API_URL}${queryString}`);

        const response = await fetch(`${window.TEMPLATES_API_URL}${queryString}`, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRFToken': getCsrfToken()
            },
            credentials: 'same-origin'
        });

        let data;
        try {
            data = await response.json();
        } catch (parseError) {
            throw new Error(`Failed to parse server response: ${parseError.message}`);
        }

        if (!response.ok) {
            const errorMessage = data.error || `HTTP error ${response.status}`;
            throw new Error(errorMessage);
        }
        
        const filteredTemplates = data.templates || [];

        // Clear table body
        tableBody.innerHTML = '';

        if (filteredTemplates && filteredTemplates.length > 0) {
            // Populate table
            filteredTemplates.forEach(template => {
                const row = document.createElement('tr');
                const createdDate = new Date(template.created_at).toLocaleDateString();

                row.innerHTML = `
                    <td><input type="checkbox" class="template-checkbox" data-id="${template.id}"></td>
                    <td>${template.name}</td>
                    <td>${template.description || 'No description'}</td>
                    <td>${template.workflow_type || 'All Types'}</td>
                    <td>${template.category}</td>
                    <td>${template.is_active ? 'Active' : 'Inactive'}</td>
                    <td>${createdDate}</td>
                    <td>
                        <button class="btn btn-secondary edit-template" data-id="${template.id}">Edit</button>
                        <button class="btn btn-danger delete-template" data-id="${template.id}">Delete</button>
                        <button class="btn btn-info export-template" data-id="${template.id}">Export</button>
                    </td>
                `;

                tableBody.appendChild(row);
            });

            // Add event listeners to buttons
            setupTemplateEventListeners();

            // Show table, hide loading and empty message
            templatesTable.classList.remove('hidden');
            templatesLoading.classList.add('hidden');
            templatesEmpty.classList.add('hidden');
        } else {
            // Show empty message, hide table and loading
            templatesTable.classList.add('hidden');
            templatesLoading.classList.add('hidden');
            templatesEmpty.classList.remove('hidden');
        }
    } catch (error) {
        console.error('Error loading templates:', error);
        showError(`Error loading templates: ${error.message}`);

        // Hide loading, show empty message
        templatesLoading.classList.add('hidden');
        templatesEmpty.classList.remove('hidden');
    }
}

/**
 * Setup event listeners for template buttons
 */
function setupTemplateEventListeners() {
    document.querySelectorAll('.edit-template').forEach(btn => {
        btn.addEventListener('click', function() {
            const templateId = this.getAttribute('data-id');
            editTemplate(templateId);
        });
    });

    document.querySelectorAll('.delete-template').forEach(btn => {
        btn.addEventListener('click', function() {
            const templateId = this.getAttribute('data-id');
            deleteTemplate(templateId);
        });
    });

    document.querySelectorAll('.export-template').forEach(btn => {
        btn.addEventListener('click', function() {
            const templateId = this.getAttribute('data-id');
            exportTemplate(templateId);
        });
    });

    // Add event listeners for checkboxes
    document.querySelectorAll('.template-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedTemplatesCount);
    });

    // Add event listener for select all checkbox
    const selectAllCheckbox = document.getElementById('select-all-templates-checkbox');
    if (selectAllCheckbox) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.addEventListener('change', function() {
            document.querySelectorAll('.template-checkbox').forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateSelectedTemplatesCount();
        });
    }

    // Update selected count
    updateSelectedTemplatesCount();
}

/**
 * Reset template filters
 */
function resetTemplateFilters() {
    const workflowTypeFilter = document.getElementById('template-workflow-type-filter');
    const statusFilter = document.getElementById('template-status-filter');
    const categoryFilter = document.getElementById('template-category-filter');

    if (workflowTypeFilter) workflowTypeFilter.value = '';
    if (statusFilter) statusFilter.value = '';
    if (categoryFilter) categoryFilter.value = '';

    loadTemplates();
}

/**
 * Load sample templates for demonstration
 */
function loadSampleTemplates() {
    console.log('Loading sample templates...');
    showInfo('Sample templates loaded successfully! This is demonstration data.');
}

// ============================================================================
// TEMPLATE MODAL MANAGEMENT
// ============================================================================

/**
 * Show create template modal
 */
function showCreateTemplateModal() {
    console.log('showCreateTemplateModal called');
    const modal = document.getElementById('template-modal');
    if (modal) {
        // Reset form
        const form = document.getElementById('template-form');
        if (form) form.reset();
        
        // Clear template ID for new template
        const idElement = document.getElementById('template-id');
        if (idElement) idElement.value = '';
        
        // Show modal
        modal.style.display = 'block';
        setupModalCloseHandlers(modal);
        setupEnhancedTemplateModal(modal);
    } else {
        console.error('Template modal not found');
        showError('Template modal not found. Please refresh the page.');
    }
}

/**
 * Enhanced template modal with robust display
 */
function showCreateTemplateModalRobust() {
    console.log('showCreateTemplateModalRobust called');

    if (showModalRobust('template-modal', 'Create New Assessment Framework')) {
        // Reset form
        const form = document.getElementById('template-form');
        if (form) form.reset();

        // Clear template ID for new template
        const idElement = document.getElementById('template-id');
        if (idElement) idElement.value = '';

        // Setup enhanced functionality
        setupEnhancedTemplateModal(document.getElementById('template-modal'));

        showSuccess('Template creation modal opened successfully!');
    }
}

/**
 * Setup enhanced template modal functionality
 */
function setupEnhancedTemplateModal(modal) {
    if (!modal) return;

    // Setup template form interactions
    setupTemplateFormInteractions(modal);
    
    // Setup template preview
    setupTemplatePreview(modal);
    
    // Initialize template modal tabs if they exist
    initializeTemplateModalTabs();
}

/**
 * Setup template form interactions
 */
function setupTemplateFormInteractions(modal) {
    if (!modal) return;

    const inputs = modal.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('change', () => updateTemplatePreview(modal));
        input.addEventListener('input', () => updateTemplatePreview(modal));
    });
}

/**
 * Setup template preview
 */
function setupTemplatePreview(modal) {
    if (!modal) return;
    
    // Initialize preview with current form values
    updateTemplatePreview(modal);
}

/**
 * Update template preview
 */
function updateTemplatePreview(modal) {
    if (!modal) return;
    
    console.log('Updating template preview...');
    // Implementation would update preview section
}

// ============================================================================
// TEMPLATE CRUD OPERATIONS
// ============================================================================

/**
 * Edit existing template
 */
async function editTemplate(templateId) {
    console.log(`editTemplate called for ID: ${templateId}`);
    showInfo(`Edit template ${templateId} - Implementation pending`);
}

/**
 * Delete template
 */
function deleteTemplate(templateId) {
    console.log(`deleteTemplate called for ID: ${templateId}`);
    if (confirm('Are you sure you want to delete this template?')) {
        showInfo(`Delete template ${templateId} - Implementation pending`);
    }
}

/**
 * Export template
 */
function exportTemplate(templateId) {
    console.log(`exportTemplate called for ID: ${templateId}`);
    showInfo(`Export template ${templateId} - Implementation pending`);
}

/**
 * Update count of selected templates
 */
function updateSelectedTemplatesCount() {
    const checkboxes = document.querySelectorAll('.template-checkbox:checked');
    const count = checkboxes.length;
    const countElement = document.getElementById('selected-templates-count');
    const batchOperations = document.getElementById('template-batch-operations');
    
    if (countElement) {
        countElement.textContent = count;
    }
    
    if (batchOperations) {
        if (count > 0) {
            batchOperations.classList.remove('hidden');
        } else {
            batchOperations.classList.add('hidden');
        }
    }
}

// ============================================================================
// TEMPLATE BATCH OPERATIONS
// ============================================================================

/**
 * Batch activate selected templates
 */
function batchActivateTemplates() {
    console.log('batchActivateTemplates called');
    const selected = document.querySelectorAll('.template-checkbox:checked');
    showInfo(`Batch activate ${selected.length} templates - Implementation pending`);
}

/**
 * Batch deactivate selected templates
 */
function batchDeactivateTemplates() {
    console.log('batchDeactivateTemplates called');
    const selected = document.querySelectorAll('.template-checkbox:checked');
    showInfo(`Batch deactivate ${selected.length} templates - Implementation pending`);
}

/**
 * Batch delete selected templates
 */
function batchDeleteTemplates() {
    console.log('batchDeleteTemplates called');
    const selected = document.querySelectorAll('.template-checkbox:checked');
    if (confirm(`Are you sure you want to delete ${selected.length} templates?`)) {
        showInfo(`Batch delete ${selected.length} templates - Implementation pending`);
    }
}

/**
 * Batch export selected templates
 */
function batchExportTemplates() {
    console.log('batchExportTemplates called');
    const selected = document.querySelectorAll('.template-checkbox:checked');
    showInfo(`Batch export ${selected.length} templates - Implementation pending`);
}
