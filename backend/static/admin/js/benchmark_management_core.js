/**
 * Benchmark Management Core Utilities
 * 
 * This file contains core utility functions used across the benchmark management system:
 * - CSRF token handling
 * - Success/error message display
 * - Modal utilities
 * - Common helper functions
 * - Page initialization
 */

// ============================================================================
// CORE UTILITY FUNCTIONS
// ============================================================================

/**
 * Get CSRF token from cookies for Django requests
 */
function getCsrfToken() {
    const name = 'csrftoken';
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

/**
 * Show success message to user
 */
function showSuccess(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success';
    alertDiv.textContent = message;

    // Insert at the top of the content
    const contentMain = document.querySelector('.content-main');
    if (contentMain) {
        contentMain.insertBefore(alertDiv, contentMain.firstChild);
    } else {
        document.body.insertBefore(alertDiv, document.body.firstChild);
    }

    // Remove after 5 seconds
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

/**
 * Show error message to user
 */
function showError(message, targetElementId) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger';
    alertDiv.textContent = message;

    let targetElement;
    if (targetElementId) {
        targetElement = document.getElementById(targetElementId);
        if (targetElement) {
            targetElement.innerHTML = ''; // Clear previous errors
            targetElement.appendChild(alertDiv);
            targetElement.classList.remove('hidden');
        } else {
            console.error(`Error target element with ID "${targetElementId}" not found.`);
            // Fallback to main window if target element not found
            targetElement = document.querySelector('.content-main');
            if (targetElement) {
                targetElement.insertBefore(alertDiv, targetElement.firstChild);
            } else {
                document.body.insertBefore(alertDiv, document.body.firstChild);
            }
        }
    } else {
        // Default to inserting at the top of the content
        targetElement = document.querySelector('.content-main');
        if (targetElement) {
            targetElement.insertBefore(alertDiv, targetElement.firstChild);
        } else {
            document.body.insertBefore(alertDiv, document.body.firstChild);
        }
    }

    // Remove after 5 seconds if not in a modal (modals handle their own clearing)
    if (!targetElementId || (targetElement && !targetElement.closest('.modal-content'))) {
        setTimeout(() => {
            alertDiv.remove();
        }, 5000);
    }
}

/**
 * Show info message to user
 */
function showInfo(message) {
    showNotification(message, 'info');
}

/**
 * Show notification with specified type
 */
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 5px;
        color: white;
        font-weight: bold;
        z-index: 10000;
        max-width: 400px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    `;

    switch (type) {
        case 'success':
            notification.style.backgroundColor = '#28a745';
            break;
        case 'error':
            notification.style.backgroundColor = '#dc3545';
            break;
        case 'warning':
            notification.style.backgroundColor = '#ffc107';
            notification.style.color = '#212529';
            break;
        default:
            notification.style.backgroundColor = '#17a2b8';
    }

    notification.innerHTML = `
        <div style="display: flex; align-items: center; gap: 10px;">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; color: inherit; font-size: 18px; cursor: pointer; margin-left: auto;">&times;</button>
        </div>
    `;

    document.body.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// ============================================================================
// MODAL UTILITIES
// ============================================================================

/**
 * Setup modal close handlers
 */
function setupModalCloseHandlers(modal) {
    if (!modal) return;

    // Close button handler
    const closeBtn = modal.querySelector('.close');
    if (closeBtn) {
        closeBtn.addEventListener('click', function() {
            modal.style.display = 'none';
        });
    }

    // Click outside to close
    modal.addEventListener('click', function(event) {
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    });

    // ESC key to close
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape' && modal.style.display === 'block') {
            modal.style.display = 'none';
        }
    });
}

/**
 * Enhanced modal display function with robust error handling
 */
function showModalRobust(modalId, title = null) {
    console.log(`Attempting to show modal: ${modalId}`);

    const modal = document.getElementById(modalId);
    if (!modal) {
        console.error(`Modal with ID '${modalId}' not found`);
        showError(`Modal '${modalId}' not found. Please refresh the page.`);
        return false;
    }

    // Set title if provided
    if (title) {
        const titleElement = modal.querySelector('h2');
        if (titleElement) {
            titleElement.innerHTML = `<i class="fas fa-plus-circle"></i> ${title}`;
        }
    }

    // Reset any previous state
    modal.classList.remove('hidden');
    modal.style.visibility = 'visible';
    modal.style.opacity = '1';

    // Force display with multiple methods
    modal.style.display = 'block';
    modal.style.zIndex = '9999';
    modal.classList.add('show');

    // Ensure modal content is visible
    const modalContent = modal.querySelector('.modal-content');
    if (modalContent) {
        modalContent.style.display = 'block';
        modalContent.style.visibility = 'visible';
    }

    // Add enhanced close handlers
    setupRobustCloseHandlers(modal);

    // Check if modal is actually visible
    setTimeout(() => {
        const rect = modal.getBoundingClientRect();
        if (rect.width === 0 || rect.height === 0) {
            console.warn('Modal appears to have zero dimensions');
            // Try alternative display method
            modal.style.cssText = `
                display: block !important;
                position: fixed !important;
                z-index: 9999 !important;
                left: 0 !important;
                top: 0 !important;
                width: 100% !important;
                height: 100% !important;
                background-color: rgba(0,0,0,0.5) !important;
                visibility: visible !important;
                opacity: 1 !important;
            `;
        } else {
            console.log(`Modal '${modalId}' successfully displayed with dimensions:`, rect);
        }
    }, 100);

    return true;
}

/**
 * Setup robust close handlers for modals
 */
function setupRobustCloseHandlers(modal) {
    if (!modal) return;

    // Remove existing handlers to prevent duplicates
    const existingHandlers = modal.querySelectorAll('.close, .close-modal');
    existingHandlers.forEach(handler => {
        handler.replaceWith(handler.cloneNode(true));
    });

    // Add close button handlers
    const closeButtons = modal.querySelectorAll('.close, .close-modal');
    closeButtons.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            hideModalRobust(modal);
        });
    });

    // Click outside to close
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            hideModalRobust(modal);
        }
    });

    // ESC key to close
    const escHandler = function(e) {
        if (e.key === 'Escape') {
            hideModalRobust(modal);
            document.removeEventListener('keydown', escHandler);
        }
    };
    document.addEventListener('keydown', escHandler);
}

/**
 * Hide modal robustly
 */
function hideModalRobust(modal) {
    if (!modal) return;

    modal.style.display = 'none';
    modal.style.visibility = 'hidden';
    modal.style.opacity = '0';
    modal.classList.remove('show');
    modal.classList.add('hidden');

    console.log('Modal hidden');
}

// ============================================================================
// CONCEPT HELP MODAL
// ============================================================================

/**
 * Show core concepts help modal
 */
function showConceptHelpModal() {
    const modal = document.getElementById('concept-help-modal');
    if (modal) {
        modal.style.display = 'block';
    }
}

/**
 * Hide core concepts help modal
 */
function hideConceptHelpModal() {
    const modal = document.getElementById('concept-help-modal');
    if (modal) {
        modal.style.display = 'none';
    }
}
