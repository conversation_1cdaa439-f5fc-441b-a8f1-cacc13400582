/**
 * Benchmark Management Testing System Integration
 * 
 * This file contains all testing system integration functionality:
 * - Backend health checking
 * - Comprehensive validation
 * - Testing system modal
 * - Progress tracking
 * - Results display
 */

// ============================================================================
// TESTING SYSTEM INTEGRATION
// ============================================================================

/**
 * Run backend health check using the AI testing system
 */
async function runBackendHealthCheck() {
    const healthCheckBtn = document.getElementById('run-health-check-btn');
    const originalText = healthCheckBtn.innerHTML;

    try {
        // Update button state
        healthCheckBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Running Health Check...';
        healthCheckBtn.disabled = true;

        // Simulate health check (in real implementation, this would call the testing system)
        const healthResults = await simulateHealthCheck();

        if (healthResults.overall === 'healthy') {
            showSuccess(`✅ Backend Health Check Passed! All ${healthResults.checks} systems healthy.`);
        } else {
            showError(`⚠️ Health Check Issues: ${healthResults.issues.join(', ')}`);
        }

    } catch (error) {
        console.error('Health check failed:', error);
        showError(`❌ Health Check Failed: ${error.message}`);
    } finally {
        // Restore button state
        healthCheckBtn.innerHTML = originalText;
        healthCheckBtn.disabled = false;
    }
}

/**
 * Run comprehensive validation using the testing system
 */
async function runFullValidation() {
    const validationBtn = document.getElementById('run-full-validation-btn');
    const originalText = validationBtn.innerHTML;

    try {
        // Update button state
        validationBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Running Full Validation...';
        validationBtn.disabled = true;

        // Show progress modal
        showValidationProgressModal();

        // Run comprehensive validation
        const validationResults = await runComprehensiveValidation();

        // Hide progress modal
        hideValidationProgressModal();

        // Show results
        showValidationResults(validationResults);

    } catch (error) {
        console.error('Full validation failed:', error);
        hideValidationProgressModal();
        showError(`❌ Validation Failed: ${error.message}`);
    } finally {
        // Restore button state
        validationBtn.innerHTML = originalText;
        validationBtn.disabled = false;
    }
}

/**
 * Show testing system information modal
 */
function showTestingSystemModal() {
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.style.display = 'block';
    modal.innerHTML = `
        <div class="modal-content" style="max-width: 800px;">
            <span class="close">&times;</span>
            <h2><i class="fas fa-flask"></i> AI Live Testing System</h2>

            <div class="alert alert-info">
                <h4>🚀 Enhanced Testing Capabilities</h4>
                <p>This benchmark management system is integrated with our comprehensive AI Live Testing Tools for real-time validation and debugging.</p>
            </div>

            <div class="testing-features">
                <h3>Available Testing Tools:</h3>
                <ul>
                    <li><strong>Backend Health Checker:</strong> Validates WebSocket, HTTP, Database, and LLM connectivity</li>
                    <li><strong>User Story Simulator:</strong> Complete end-to-end workflow testing</li>
                    <li><strong>WebSocket Monitor:</strong> Real-time message flow analysis</li>
                    <li><strong>Integration Test Suite:</strong> Comprehensive system validation</li>
                    <li><strong>Performance Benchmarking:</strong> Load testing and bottleneck detection</li>
                </ul>

                <h3>Testing Workflow:</h3>
                <ol>
                    <li><strong>Health Check:</strong> Verify all systems are operational</li>
                    <li><strong>Quick Test:</strong> Run targeted benchmark scenarios</li>
                    <li><strong>Full Validation:</strong> Comprehensive end-to-end testing</li>
                    <li><strong>Performance Analysis:</strong> Monitor and optimize system performance</li>
                </ol>

                <h3>Access Testing Tools:</h3>
                <p>The testing tools are located in <code>frontend/ai-live-testing-tools/</code> and can be run directly:</p>
                <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px;">
cd frontend/ai-live-testing-tools
node backend-health-checker.js
node user-story-simulator.js
node integration-test-suite.js</pre>
            </div>

            <div class="action-buttons">
                <button type="button" class="btn btn-primary close-modal">Got it!</button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Add close functionality
    modal.querySelector('.close').onclick = () => modal.remove();
    modal.querySelector('.close-modal').onclick = () => modal.remove();
    modal.onclick = (e) => { if (e.target === modal) modal.remove(); };
}

// ============================================================================
// HEALTH CHECK SIMULATION
// ============================================================================

/**
 * Simulate health check (placeholder for real implementation)
 */
async function simulateHealthCheck() {
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Simulate health check results
    return {
        overall: 'healthy',
        checks: 6,
        systems: {
            websocket: 'healthy',
            http: 'healthy',
            database: 'healthy',
            llm: 'healthy',
            apis: 'healthy',
            celery: 'healthy'
        }
    };
}

// ============================================================================
// COMPREHENSIVE VALIDATION
// ============================================================================

/**
 * Run comprehensive validation
 */
async function runComprehensiveValidation() {
    const steps = [
        'Backend Health Check',
        'Database Connectivity',
        'WebSocket Communication',
        'User Story Simulation',
        'Benchmark Execution',
        'Performance Analysis'
    ];

    const results = [];

    for (let i = 0; i < steps.length; i++) {
        updateValidationProgress(i + 1, steps.length, steps[i]);

        // Simulate step execution
        await new Promise(resolve => setTimeout(resolve, 1500));

        results.push({
            step: steps[i],
            status: 'passed',
            details: `${steps[i]} completed successfully`
        });
    }

    return {
        overall: 'passed',
        steps: results,
        summary: 'All validation steps completed successfully'
    };
}

// ============================================================================
// VALIDATION PROGRESS MODAL
// ============================================================================

/**
 * Show validation progress modal
 */
function showValidationProgressModal() {
    const modal = document.createElement('div');
    modal.id = 'validation-progress-modal';
    modal.className = 'modal';
    modal.style.display = 'block';
    modal.innerHTML = `
        <div class="modal-content">
            <h2><i class="fas fa-check-double"></i> Running Comprehensive Validation</h2>

            <div class="progress-container">
                <div class="progress-bar">
                    <div id="validation-progress-bar" class="progress-fill" style="width: 0%;"></div>
                </div>
                <div id="validation-progress-text">Initializing validation...</div>
            </div>

            <div id="validation-steps" class="validation-steps">
                <!-- Steps will be populated dynamically -->
            </div>
        </div>
    `;

    document.body.appendChild(modal);
}

/**
 * Update validation progress
 */
function updateValidationProgress(current, total, currentStep) {
    const progressBar = document.getElementById('validation-progress-bar');
    const progressText = document.getElementById('validation-progress-text');

    if (progressBar && progressText) {
        const percentage = (current / total) * 100;
        progressBar.style.width = `${percentage}%`;
        progressText.textContent = `Step ${current}/${total}: ${currentStep}`;
    }
}

/**
 * Hide validation progress modal
 */
function hideValidationProgressModal() {
    const modal = document.getElementById('validation-progress-modal');
    if (modal) {
        modal.remove();
    }
}

// ============================================================================
// VALIDATION RESULTS
// ============================================================================

/**
 * Show validation results
 */
function showValidationResults(results) {
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.style.display = 'block';
    modal.innerHTML = `
        <div class="modal-content" style="max-width: 700px;">
            <span class="close">&times;</span>
            <h2><i class="fas fa-check-circle" style="color: green;"></i> Validation Results</h2>

            <div class="alert alert-success">
                <h4>✅ ${results.summary}</h4>
            </div>

            <div class="validation-results">
                <h3>Detailed Results:</h3>
                ${results.steps.map(step => `
                    <div class="validation-step">
                        <i class="fas fa-check" style="color: green;"></i>
                        <strong>${step.step}:</strong> ${step.details}
                    </div>
                `).join('')}
            </div>

            <div class="action-buttons">
                <button type="button" class="btn btn-primary close-modal">Close</button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Add close functionality
    modal.querySelector('.close').onclick = () => modal.remove();
    modal.querySelector('.close-modal').onclick = () => modal.remove();
    modal.onclick = (e) => { if (e.target === modal) modal.remove(); };
}

// ============================================================================
// SYSTEM HEALTH CHECK
// ============================================================================

/**
 * Run system health check
 */
function runSystemHealthCheck() {
    console.log('Running system health check...');
    
    showInfo('System health check initiated. This would verify all backend systems are operational.');
    
    // In a real implementation, this would:
    // 1. Check database connectivity
    // 2. Verify WebSocket connections
    // 3. Test API endpoints
    // 4. Validate LLM connectivity
    // 5. Check Celery workers
    // 6. Monitor system resources
}

/**
 * Validate all scenarios
 */
function validateAllScenarios() {
    console.log('Validating all scenarios...');
    
    showInfo('Scenario validation initiated. This would run validation checks on all active scenarios.');
    
    // In a real implementation, this would:
    // 1. Load all active scenarios
    // 2. Validate scenario configurations
    // 3. Check for missing dependencies
    // 4. Verify evaluation criteria
    // 5. Test scenario execution
}

/**
 * Generate validation report
 */
function generateValidationReport() {
    console.log('Generating validation report...');
    
    showInfo('Validation report generation initiated. This would create a comprehensive system validation report.');
    
    // In a real implementation, this would:
    // 1. Collect validation results
    // 2. Generate detailed report
    // 3. Include performance metrics
    // 4. Provide recommendations
    // 5. Export to PDF/HTML
}

/**
 * Initialize quick test functionality
 */
function initializeQuickTest() {
    console.log('Initializing Quick Test functionality...');
    
    // Quick Test functionality is handled by the QuickTest module
    // This function ensures integration with the testing system
    
    const quickTestBtn = document.getElementById('quick-test-btn');
    if (quickTestBtn) {
        quickTestBtn.addEventListener('click', function() {
            console.log('Quick Test button clicked - delegating to QuickTest module');
            // The actual functionality is in quick_test.js
        });
    }
}
