/* ==========================================================================
   Modal Components
   ========================================================================== */

/* Base modal styles - extends Bootstrap modals */
.modal-base {
    z-index: 1050;
}

.modal-large {
    max-width: 90%;
}

.modal-small {
    max-width: 400px;
}

.modal-extra-large {
    max-width: 95%;
}

/* Modal header enhancements */
.modal-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border-bottom: none;
    border-radius: 8px 8px 0 0;
}

.modal-header .modal-title {
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.modal-header .btn-close {
    filter: invert(1);
    opacity: 0.8;
}

.modal-header .btn-close:hover {
    opacity: 1;
}

/* Modal body enhancements */
.modal-body {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
}

.modal-body-scrollable {
    max-height: 60vh;
    overflow-y: auto;
}

/* Modal footer enhancements */
.modal-footer {
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
    border-radius: 0 0 8px 8px;
    padding: 15px 20px;
}

/* Modal loading states */
.modal-loading {
    text-align: center;
    padding: 40px;
}

.modal-loading .spinner-border {
    width: 3rem;
    height: 3rem;
    color: #007bff;
}

/* Modal content sections */
.modal-section {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.modal-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.modal-section-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.modal-section-content {
    color: #6c757d;
    line-height: 1.5;
}

/* Modal tabs */
.modal-tabs {
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 20px;
}

.modal-tab-nav {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
}

.modal-tab-item {
    margin-right: 10px;
}

.modal-tab-link {
    display: block;
    padding: 10px 15px;
    color: #6c757d;
    text-decoration: none;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.modal-tab-link:hover {
    color: #007bff;
    background-color: #f8f9fa;
}

.modal-tab-link.active {
    color: #007bff;
    border-bottom-color: #007bff;
    background-color: #f8f9fa;
}

.modal-tab-content {
    display: none;
}

.modal-tab-content.active {
    display: block;
}

/* Modal forms */
.modal-form-group {
    margin-bottom: 15px;
}

.modal-form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
    display: block;
}

.modal-form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.modal-form-control:focus {
    border-color: #007bff;
    outline: none;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.modal-form-text {
    font-size: 12px;
    color: #6c757d;
    margin-top: 5px;
}

/* Modal alerts */
.modal-alert {
    padding: 12px 15px;
    border-radius: 4px;
    margin-bottom: 15px;
    border: 1px solid transparent;
}

.modal-alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.modal-alert-error {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.modal-alert-warning {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.modal-alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

/* Modal data display */
.modal-data-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.modal-data-item {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.modal-data-label {
    font-size: 12px;
    font-weight: 600;
    color: #6c757d;
    text-transform: uppercase;
    margin-bottom: 5px;
}

.modal-data-value {
    font-size: 14px;
    color: #495057;
    font-weight: 500;
}

/* Modal buttons */
.modal-btn-group {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.modal-btn-group .btn {
    min-width: 100px;
}

/* Modal progress */
.modal-progress {
    margin: 15px 0;
}

.modal-progress-bar {
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.modal-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #28a745);
    transition: width 0.3s ease;
    border-radius: 4px;
}

.modal-progress-text {
    font-size: 12px;
    color: #6c757d;
    text-align: center;
    margin-top: 5px;
}

/* Modal responsive design */
@media (max-width: 768px) {
    .modal-dialog {
        margin: 10px;
        max-width: calc(100% - 20px);
    }

    .modal-large,
    .modal-extra-large {
        max-width: 100%;
    }

    .modal-body {
        padding: 15px;
        max-height: 60vh;
    }

    .modal-data-grid {
        grid-template-columns: 1fr;
    }

    .modal-tab-nav {
        flex-direction: column;
    }

    .modal-tab-item {
        margin-right: 0;
        margin-bottom: 5px;
    }

    .modal-btn-group {
        flex-direction: column;
    }

    .modal-btn-group .btn {
        width: 100%;
        margin-bottom: 10px;
    }

    .modal-btn-group .btn:last-child {
        margin-bottom: 0;
    }
}

/* Specific modal overrides */
.user-profile-modal .modal-dialog {
    max-width: 90%;
}

.user-environment-modal .modal-dialog {
    max-width: 80%;
}

.inventory-modal .modal-dialog {
    max-width: 85%;
}

/* Modal backdrop customization */
.modal-backdrop.show {
    opacity: 0.6;
}

/* Modal animation enhancements */
.modal.fade .modal-dialog {
    transition: transform 0.3s ease-out;
    transform: translate(0, -50px);
}

.modal.show .modal-dialog {
    transform: none;
}

/* Modal close button positioning */
.modal-header .btn-close {
    position: absolute;
    right: 15px;
    top: 15px;
    z-index: 1;
}
