/* ==========================================================================
   Catalog Viewer Component Styles
   ========================================================================== */

/* Modal base styles */
.catalog-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.catalog-modal.active {
    opacity: 1;
    visibility: visible;
}

.catalog-modal-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 1200px;
    height: 90%;
    max-height: 800px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.catalog-modal.active .catalog-modal-content {
    transform: scale(1);
}

/* Modal header */
.catalog-modal-header {
    padding: 20px;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.catalog-modal-title {
    margin: 0;
    color: #2c3e50;
    font-size: 1.5rem;
}

.catalog-modal-subtitle {
    color: #6c757d;
    font-size: 0.9rem;
    margin-top: 5px;
}

.catalog-close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #6c757d;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.catalog-close-btn:hover {
    background: #e9ecef;
    color: #495057;
}

/* Modal body */
.catalog-modal-body {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

/* Catalog metadata */
.catalog-metadata {
    padding: 15px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.metadata-item {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.metadata-label {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: bold;
    text-transform: uppercase;
}

.metadata-value {
    font-size: 0.9rem;
    color: #495057;
}

/* Catalog tabs */
.catalog-tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.catalog-tab {
    padding: 12px 20px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 0.9rem;
    color: #6c757d;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
}

.catalog-tab:hover {
    background: #e9ecef;
    color: #495057;
}

.catalog-tab.active {
    color: #007bff;
    border-bottom-color: #007bff;
    background: white;
}

/* Tab content */
.catalog-tab-content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.catalog-tab-pane {
    flex: 1;
    overflow: auto;
    padding: 20px;
    display: none;
}

.catalog-tab-pane.active {
    display: block;
}

/* Search and filter bar */
.catalog-search-bar {
    padding: 15px 20px;
    background: white;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
}

.catalog-search-input {
    flex: 1;
    min-width: 200px;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 0.9rem;
}

.catalog-filter-select {
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 0.9rem;
    background: white;
}

.catalog-search-stats {
    font-size: 0.8rem;
    color: #6c757d;
}

/* Data grid */
.catalog-data-grid {
    flex: 1;
    overflow: auto;
}

.catalog-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.catalog-table th,
.catalog-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.catalog-table th {
    background: #f8f9fa;
    font-weight: bold;
    color: #495057;
    position: sticky;
    top: 0;
    z-index: 10;
}

.catalog-table tr:hover {
    background: #f8f9fa;
}

.catalog-table .code-cell {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    background: #f8f9fa;
    border-radius: 3px;
    padding: 4px 6px;
    font-size: 0.8rem;
}

.catalog-table .description-cell {
    max-width: 300px;
    word-wrap: break-word;
}

/* Tree view for hierarchical data */
.catalog-tree {
    padding: 0;
    margin: 0;
    list-style: none;
}

.catalog-tree-item {
    margin: 0;
    padding: 0;
}

.catalog-tree-node {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    cursor: pointer;
    transition: background 0.2s ease;
}

.catalog-tree-node:hover {
    background: #f8f9fa;
}

.catalog-tree-toggle {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    font-size: 0.8rem;
    color: #6c757d;
}

.catalog-tree-icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    opacity: 0.7;
}

.catalog-tree-label {
    flex: 1;
    font-size: 0.9rem;
    color: #495057;
}

.catalog-tree-children {
    margin-left: 20px;
    border-left: 1px solid #dee2e6;
    padding-left: 10px;
}

.catalog-tree-children.collapsed {
    display: none;
}

/* JSON viewer */
.catalog-json-viewer {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 15px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.8rem;
    line-height: 1.4;
    overflow: auto;
    max-height: 400px;
}

.catalog-json-viewer pre {
    margin: 0;
    white-space: pre-wrap;
    word-wrap: break-word;
}

/* Statistics panel */
.catalog-stats-panel {
    background: #f8f9fa;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
}

.catalog-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.catalog-stat-item {
    text-align: center;
    padding: 10px;
    background: white;
    border-radius: 4px;
    border: 1px solid #dee2e6;
}

.catalog-stat-number {
    font-size: 1.5rem;
    font-weight: bold;
    color: #007bff;
    margin-bottom: 5px;
}

.catalog-stat-label {
    font-size: 0.8rem;
    color: #6c757d;
    text-transform: uppercase;
}

/* Export controls */
.catalog-export-controls {
    padding: 15px 20px;
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.catalog-export-btn {
    padding: 8px 16px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background 0.3s ease;
}

.catalog-export-btn:hover {
    background: #0056b3;
}

.catalog-export-btn.secondary {
    background: #6c757d;
}

.catalog-export-btn.secondary:hover {
    background: #545b62;
}

/* Loading states */
.catalog-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #6c757d;
}

.catalog-loading-spinner {
    width: 24px;
    height: 24px;
    border: 2px solid #dee2e6;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error states */
.catalog-error {
    padding: 20px;
    text-align: center;
    color: #dc3545;
}

.catalog-error-icon {
    font-size: 2rem;
    margin-bottom: 10px;
}

.catalog-error-message {
    font-size: 1rem;
    margin-bottom: 10px;
}

.catalog-error-details {
    font-size: 0.8rem;
    color: #6c757d;
    background: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    margin-top: 10px;
}

/* Responsive design */
@media (max-width: 768px) {
    .catalog-modal-content {
        width: 95%;
        height: 95%;
        margin: 2.5%;
    }
    
    .catalog-metadata {
        flex-direction: column;
        gap: 10px;
    }
    
    .catalog-search-bar {
        flex-direction: column;
        align-items: stretch;
    }
    
    .catalog-stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .catalog-export-controls {
        flex-direction: column;
    }
}
