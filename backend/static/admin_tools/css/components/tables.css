/* ==========================================================================
   Table Components
   ========================================================================== */

/* Base table styles */
.admin-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
    background: #fff;
}

.admin-table th,
.admin-table td {
    padding: 12px;
    border-bottom: 1px solid #ddd;
    text-align: left;
    vertical-align: top;
}

.admin-table th {
    background-color: #f8f8f8;
    font-weight: bold;
    position: sticky;
    top: 0;
    z-index: 10;
}

.admin-table tr:hover {
    background-color: #f5f5f5;
}

/* Profile table specific styles */
.profile-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.profile-table th,
.profile-table td {
    padding: 12px;
    border-bottom: 1px solid #ddd;
    text-align: left;
    vertical-align: top;
}

.profile-table th {
    background-color: #f8f8f8;
    font-weight: bold;
    position: sticky;
    top: 0;
    z-index: 10;
}

.profile-table tr:hover {
    background-color: #f5f5f5;
}

.profile-table th:first-child,
.profile-table td:first-child {
    width: 40px;
    text-align: center;
}

/* Selected row highlighting */
.profile-table tr.selected {
    background-color: #e7f3ff;
}

/* Checkbox styling */
.profile-checkbox, 
#select-all-checkbox {
    transform: scale(1.2);
    margin: 0;
}

/* Profile type badges */
.profile-type-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.profile-type-real {
    background: #d4edda;
    color: #155724;
}

.profile-type-test {
    background: #fff3cd;
    color: #856404;
}

/* Completeness bars */
.completeness-bar {
    width: 100px;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.completeness-fill {
    height: 100%;
    background: linear-gradient(90deg, #dc3545 0%, #ffc107 50%, #28a745 100%);
    transition: width 0.3s ease;
}

.completeness-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 11px;
    font-weight: bold;
    color: #333;
}

/* Quality scores */
.quality-score {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: bold;
}

.quality-excellent {
    background-color: #d4edda;
    color: #155724;
}

.quality-good {
    background-color: #d1ecf1;
    color: #0c5460;
}

.quality-fair {
    background-color: #fff3cd;
    color: #856404;
}

.quality-poor {
    background-color: #f8d7da;
    color: #721c24;
}

/* Import history table */
.import-history-table-container {
    overflow-x: auto;
    margin: 20px 0;
}

.import-history-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.import-history-table th,
.import-history-table td {
    padding: 12px 8px;
    border-bottom: 1px solid #ddd;
    text-align: left;
    vertical-align: middle;
}

.import-history-table th {
    background-color: #f8f8f8;
    font-weight: bold;
    position: sticky;
    top: 0;
    z-index: 10;
}

.import-history-table tr:hover {
    background-color: #f5f5f5;
}

/* History actions */
.history-actions {
    display: flex;
    gap: 5px;
}

.history-actions .btn {
    padding: 4px 8px;
    font-size: 11px;
    margin: 0;
}

/* Pagination */
.table-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-top: 1px solid #dee2e6;
}

.pagination-info {
    color: #6c757d;
    font-size: 14px;
}

.pagination-controls {
    display: flex;
    gap: 10px;
}

/* Table sorting */
.sortable-header {
    cursor: pointer;
    user-select: none;
    position: relative;
}

.sortable-header:hover {
    background-color: #e9ecef;
}

.sortable-header::after {
    content: '';
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-bottom: 4px solid #6c757d;
    opacity: 0.3;
}

.sortable-header.sort-asc::after {
    border-bottom: 4px solid #007bff;
    opacity: 1;
}

.sortable-header.sort-desc::after {
    border-bottom: none;
    border-top: 4px solid #007bff;
    opacity: 1;
}

/* Table filters */
.table-filters {
    margin-bottom: 15px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.filter-row {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filter-group label {
    font-size: 12px;
    font-weight: 600;
    color: #495057;
}

.filter-group select,
.filter-group input {
    padding: 6px 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
}

/* Search box */
.search-box {
    width: 300px;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
}

/* Table responsive design */
@media (max-width: 768px) {
    .admin-table,
    .profile-table,
    .import-history-table {
        font-size: 12px;
    }

    .admin-table th,
    .admin-table td,
    .profile-table th,
    .profile-table td,
    .import-history-table th,
    .import-history-table td {
        padding: 8px 4px;
    }

    .search-box {
        width: 100%;
    }

    .filter-row {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-group select,
    .filter-group input {
        width: 100%;
    }

    .completeness-bar {
        width: 80px;
        height: 16px;
    }

    .completeness-text {
        font-size: 10px;
    }

    .table-pagination {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .pagination-controls {
        justify-content: center;
    }
}

/* Table loading states */
.table-loading {
    text-align: center;
    padding: 40px;
    color: #6c757d;
}

.table-loading .loader {
    margin: 0 auto 15px;
}

/* Empty table states */
.table-empty {
    text-align: center;
    padding: 40px;
    color: #6c757d;
    font-style: italic;
}

.table-empty h4 {
    margin-bottom: 10px;
    color: #495057;
}

/* Table actions toolbar */
.table-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px 0;
}

.table-toolbar-left {
    display: flex;
    gap: 10px;
    align-items: center;
}

.table-toolbar-right {
    display: flex;
    gap: 10px;
    align-items: center;
}

/* Bulk selection */
.bulk-selection-info {
    background: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 4px;
    padding: 10px 15px;
    margin-bottom: 15px;
    display: none;
}

.bulk-selection-info.active {
    display: block;
}

.bulk-selection-count {
    font-weight: 600;
    color: #0c5460;
}

.bulk-actions {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}
