/* ==========================================================================
   Generic Skill Management Admin Interface Styles
   ========================================================================== */

/* Main container styles */
.generic-skill-admin {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Skill card styles */
.skill-card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: box-shadow 0.3s ease;
}

.skill-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.skill-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.skill-name {
    font-size: 1.4em;
    font-weight: bold;
    color: #333;
    margin: 0;
}

.skill-code {
    font-family: 'Courier New', monospace;
    background: #f5f5f5;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.9em;
    color: #666;
}

/* Skill properties display */
.skill-properties {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.property-item {
    display: flex;
    flex-direction: column;
    padding: 10px;
    background: #f9f9f9;
    border-radius: 6px;
}

.property-label {
    font-weight: bold;
    color: #555;
    font-size: 0.9em;
    margin-bottom: 5px;
}

.property-value {
    color: #333;
    font-size: 1em;
}

/* Difficulty and decay rate visual indicators */
.difficulty-indicator,
.decay-indicator {
    display: inline-block;
    width: 100px;
    height: 8px;
    background: #e0e0e0;
    border-radius: 4px;
    position: relative;
    margin-top: 5px;
}

.difficulty-bar,
.decay-bar {
    height: 100%;
    border-radius: 4px;
    transition: width 0.3s ease;
}

.difficulty-bar {
    background: linear-gradient(90deg, #4CAF50, #FF9800, #F44336);
}

.decay-bar {
    background: linear-gradient(90deg, #4CAF50, #FF9800, #F44336);
}

/* Development timeframe badges */
.timeframe-badge {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8em;
    font-weight: bold;
    text-transform: uppercase;
}

.timeframe-immediate { background: #E3F2FD; color: #1976D2; }
.timeframe-short { background: #E8F5E8; color: #388E3C; }
.timeframe-medium { background: #FFF3E0; color: #F57C00; }
.timeframe-long { background: #FCE4EC; color: #C2185B; }
.timeframe-lifetime { background: #F3E5F5; color: #7B1FA2; }

/* Relationship sections */
.relationships-section {
    margin-top: 25px;
}

.relationship-header {
    font-size: 1.2em;
    font-weight: bold;
    color: #333;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #007cba;
}

.relationship-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
}

.relationship-item {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 12px;
    transition: background-color 0.2s ease;
}

.relationship-item:hover {
    background: #e9ecef;
}

.relationship-name {
    font-weight: bold;
    color: #495057;
    margin-bottom: 5px;
}

.relationship-details {
    font-size: 0.9em;
    color: #6c757d;
}

/* Impact indicators for trait relationships */
.impact-indicator {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: bold;
    margin-left: 8px;
}

.impact-positive { background: #d4edda; color: #155724; }
.impact-negative { background: #f8d7da; color: #721c24; }
.impact-neutral { background: #e2e3e5; color: #383d41; }

/* Strength indicators for domain relationships */
.strength-indicator {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: bold;
    margin-left: 8px;
}

.strength-primary { background: #d1ecf1; color: #0c5460; }
.strength-significant { background: #d4edda; color: #155724; }
.strength-moderate { background: #fff3cd; color: #856404; }
.strength-minimal { background: #f8d7da; color: #721c24; }

/* Form styles */
.skill-form-section {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-label {
    font-weight: bold;
    color: #333;
    margin-bottom: 8px;
}

.form-input,
.form-select,
.form-textarea {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1em;
    transition: border-color 0.3s ease;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: #007cba;
    box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.2);
}

/* Action buttons */
.skill-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.btn-primary,
.btn-secondary,
.btn-danger {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    font-size: 1em;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.btn-primary {
    background: #007cba;
    color: white;
}

.btn-primary:hover {
    background: #005a87;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

/* Responsive design */
@media (max-width: 768px) {
    .skill-properties {
        grid-template-columns: 1fr;
    }
    
    .relationship-grid {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .skill-card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}

/* Utility classes */
.text-center { text-align: center; }
.text-right { text-align: right; }
.mb-10 { margin-bottom: 10px; }
.mb-20 { margin-bottom: 20px; }
.mt-10 { margin-top: 10px; }
.mt-20 { margin-top: 20px; }

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007cba;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
