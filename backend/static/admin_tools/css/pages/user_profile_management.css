/* ==========================================================================
   User Profile Management Page Styles
   ========================================================================== */

/* Page container */
.profile-container {
    max-width: 1600px;
    margin: 0 auto;
    padding: 20px;
}

/* Import Profile Styles */
.import-profile-card {
    margin-bottom: 20px;
}

.import-method-tabs {
    display: flex;
    border-bottom: 2px solid #dee2e6;
    margin-bottom: 20px;
}

/* Upload area */
.upload-area {
    border: 2px dashed #ced4da;
    border-radius: 8px;
    padding: 40px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: #f8f9fa;
}

.upload-area:hover {
    border-color: #007bff;
    background-color: #e7f3ff;
}

.upload-area.dragover {
    border-color: #28a745;
    background-color: #d4edda;
}

.upload-icon {
    font-size: 48px;
    margin-bottom: 10px;
}

.upload-text {
    color: #6c757d;
}

/* JSON input section */
.json-input-section label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
}

#json-textarea, 
#questionnaire-data {
    width: 100%;
    min-height: 200px;
    padding: 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    resize: vertical;
}

.json-validation {
    margin-top: 10px;
    padding: 8px;
    border-radius: 4px;
    font-size: 14px;
}

.json-validation.valid {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.json-validation.invalid {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* AI options */
.ai-options {
    margin: 15px 0;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.ai-options label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: normal;
}

/* Profile preview */
.profile-preview {
    margin-top: 20px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.preview-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.preview-stat {
    background: white;
    padding: 12px;
    border-radius: 6px;
    border: 1px solid #dee2e6;
    text-align: center;
}

.preview-stat-number {
    font-size: 18px;
    font-weight: bold;
    color: #007bff;
    display: block;
}

.preview-stat-label {
    font-size: 12px;
    color: #6c757d;
    margin-top: 4px;
}

.preview-details {
    background: white;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #dee2e6;
    max-height: 300px;
    overflow-y: auto;
}

/* Import controls */
.import-controls {
    margin-top: 20px;
    padding: 20px;
    background-color: #fff3cd;
    border-radius: 8px;
    border: 1px solid #ffeaa7;
}

.import-options {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 15px;
}

.import-options label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: normal;
}

.import-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* Import status */
.import-status {
    margin-top: 20px;
    padding: 15px;
    border-radius: 8px;
}

.import-status.success {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.import-status.error {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.import-status.warning {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

.import-status.info {
    background-color: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
}

.status-message {
    font-weight: 600;
    margin-bottom: 8px;
}

.status-details {
    font-size: 14px;
    max-height: 200px;
    overflow-y: auto;
}

/* Import History Styles */
.import-history-controls {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.history-filters {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
}

.history-filters select,
.history-filters input[type="text"] {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    min-width: 150px;
}

.import-history-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-top: 1px solid #dee2e6;
}

/* Modal z-index fixes */
#profileRepairModal {
    z-index: 1060 !important;
}

#profileRepairModal .modal-backdrop {
    z-index: 1055 !important;
}

#userAccountSelectionModal {
    z-index: 1056 !important;
}

#userAccountSelectionModal .modal-backdrop {
    z-index: 1051 !important;
}

/* Responsive design for user profile management */
@media (max-width: 768px) {
    .import-method-tabs {
        flex-direction: column;
    }

    .tab-btn {
        border-bottom: none;
        border-left: 3px solid transparent;
        text-align: left;
    }

    .tab-btn.active {
        border-left-color: #007bff;
        border-bottom-color: transparent;
    }

    .import-buttons {
        flex-direction: column;
    }

    .preview-summary {
        grid-template-columns: repeat(2, 1fr);
    }

    .history-filters {
        flex-direction: column;
        align-items: stretch;
    }

    .history-filters select,
    .history-filters input[type="text"] {
        min-width: auto;
        width: 100%;
    }
}
