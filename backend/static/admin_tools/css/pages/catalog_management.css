/* ==========================================================================
   Catalog Management Page Styles
   ========================================================================== */

/* Main container styles */
.catalog-management-container {
    padding: 20px;
    background: #f8f9fa;
    min-height: 100vh;
}

.catalog-header {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.catalog-header h1 {
    margin: 0 0 10px 0;
    color: #2c3e50;
    font-size: 2rem;
}

.catalog-stats {
    display: flex;
    gap: 20px;
    margin-top: 15px;
}

.stat-item {
    background: #e9ecef;
    padding: 10px 15px;
    border-radius: 6px;
    text-align: center;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: bold;
    color: #495057;
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-top: 5px;
}

/* Catalog status overview */
.catalog-status-overview {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.catalog-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 15px;
}

.catalog-card {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    transition: all 0.3s ease;
}

.catalog-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.catalog-card.valid {
    border-left: 4px solid #28a745;
}

.catalog-card.invalid {
    border-left: 4px solid #dc3545;
}

.catalog-card-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 10px;
}

.catalog-title {
    font-weight: bold;
    font-size: 1.1rem;
    color: #495057;
}

.catalog-status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}

.catalog-status.valid {
    background: #d4edda;
    color: #155724;
}

.catalog-status.invalid {
    background: #f8d7da;
    color: #721c24;
}

.catalog-actions {
    margin-top: 10px;
    display: flex;
    gap: 10px;
}

.catalog-errors {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 10px;
    margin-top: 10px;
    font-size: 0.9rem;
}

.catalog-errors ul {
    margin: 0;
    padding-left: 20px;
}

/* Command cards */
.commands-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.commands-grid {
    display: grid;
    gap: 20px;
    margin-top: 20px;
}

.command-card {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    background: #f9f9f9;
    transition: all 0.3s ease;
    display: block !important;
    visibility: visible !important;
    min-height: 200px;
    margin-bottom: 20px;
}

.command-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.command-card.high-risk {
    border-left: 4px solid #dc3545;
}

.command-card.medium-risk {
    border-left: 4px solid #ffc107;
}

.command-card.low-risk {
    border-left: 4px solid #28a745;
}

.command-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.command-info h3 {
    margin: 0 0 5px 0;
    color: #2c3e50;
}

.command-category {
    background: #e9ecef;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    color: #495057;
    margin-bottom: 10px;
    display: inline-block;
}

.command-description {
    color: #6c757d;
    margin-bottom: 10px;
    line-height: 1.4;
}

.command-meta {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
    font-size: 0.9rem;
}

.command-meta span {
    color: #6c757d;
}

.risk-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: bold;
}

.risk-badge.high {
    background: #f8d7da;
    color: #721c24;
}

.risk-badge.medium {
    background: #fff3cd;
    color: #856404;
}

.risk-badge.low {
    background: #d4edda;
    color: #155724;
}

/* Parameters section */
.parameters-section {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #dee2e6;
}

.parameters-section h4 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 1rem;
}

.parameter-group {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.parameter-input {
    margin-bottom: 15px;
}

.parameter-input label {
    display: block;
    font-weight: bold;
    margin-bottom: 5px;
    color: #495057;
}

.parameter-input input,
.parameter-input select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.parameter-input input:focus,
.parameter-input select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.parameter-input .help-text {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 5px;
}

.parameter-input.checkbox {
    display: flex;
    align-items: center;
    gap: 10px;
}

.parameter-input.checkbox input {
    width: auto;
}

.parameter-input.file {
    position: relative;
}

.file-upload-area {
    border: 2px dashed #ced4da;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: #007bff;
    background: #f8f9fa;
}

.file-upload-area.dragover {
    border-color: #28a745;
    background: #d4edda;
}

/* Command execution */
.command-actions {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-top: 15px;
}

.execute-btn {
    background: #007bff;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.execute-btn:hover {
    background: #0056b3;
    transform: translateY(-1px);
}

.execute-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
}

.execute-btn.loading {
    position: relative;
    color: transparent;
}

.execute-btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border: 2px solid #ffffff;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Enhanced command output styling */
.command-output {
    margin-top: 15px;
    border-radius: 8px;
    display: block !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    min-height: 50px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    padding: 15px;
}

.command-output:empty {
    display: none !important;
}

.command-output.success {
    border: 2px solid #28a745;
}

.command-output.error {
    border: 2px solid #dc3545;
}

.command-result-container {
    background: white;
    border-radius: 6px;
    overflow: hidden;
}

/* Status banners */
.success-banner, .error-banner {
    padding: 15px 20px;
    color: white;
    font-weight: 500;
}

.success-banner {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.error-banner {
    background: linear-gradient(135deg, #dc3545, #e74c3c);
}

.banner-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

.status-icon {
    font-size: 18px;
}

.timestamp {
    margin-left: auto;
    font-size: 12px;
    opacity: 0.9;
}

/* Output sections */
.output-section {
    padding: 20px;
}

.command-output-content {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 15px;
    margin: 10px 0;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    line-height: 1.4;
    white-space: pre-wrap;
    word-wrap: break-word;
    max-height: 300px;
    overflow-y: auto;
}

.error-details, .warning-details {
    margin: 15px 0;
}

.error-details h4 {
    color: #dc3545;
    margin: 0 0 10px 0;
    font-size: 14px;
}

.warning-details h4 {
    color: #ffc107;
    margin: 0 0 10px 0;
    font-size: 14px;
}

.error-content, .warning-content {
    background: #fff5f5;
    border: 1px solid #fed7d7;
    border-radius: 4px;
    padding: 10px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    color: #c53030;
}

.warning-content {
    background: #fffbeb;
    border-color: #fbd38d;
    color: #c05621;
}

/* Execution metrics */
.execution-metrics {
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    padding: 15px 20px;
}

.metric {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 5px 0;
    font-size: 13px;
}

.metric-label {
    font-weight: 500;
    color: #495057;
}

.metric-value {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    background: white;
    padding: 2px 8px;
    border-radius: 3px;
    border: 1px solid #dee2e6;
    font-size: 12px;
}

/* Enhanced loading states */
.command-card.executing {
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.progress-bar {
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
    padding: 10px 15px;
    display: none;
}

.progress-fill {
    height: 4px;
    background: linear-gradient(90deg, #007bff, #0056b3);
    border-radius: 2px;
    animation: progressAnimation 2s ease-in-out infinite;
}

.progress-text {
    font-size: 12px;
    color: #6c757d;
    margin-top: 5px;
    text-align: center;
}

@keyframes progressAnimation {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
}

.loading-spinner {
    animation: spin 1s linear infinite;
    display: inline-block;
    margin-right: 5px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Notification system */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    max-width: 400px;
}

.notification {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    margin-bottom: 10px;
    transform: translateX(100%);
    transition: transform 0.3s ease-in-out;
    border-left: 4px solid #007bff;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-left-color: #28a745;
}

.notification.error {
    border-left-color: #dc3545;
}

.notification.warning {
    border-left-color: #ffc107;
}

.notification-content {
    padding: 15px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.notification-message {
    font-size: 14px;
    font-weight: 500;
}

.notification-close {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #6c757d;
    padding: 0;
    margin-left: 10px;
}

.notification-close:hover {
    color: #495057;
}

.execution-info {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 10px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 4px;
}

/* Real-time Progress Bars */
.progress-container {
    margin: 10px 0;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #007bff;
    transition: all 0.3s ease;
}

.progress-bar-wrapper {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #0056b3);
    border-radius: 4px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-bar.error {
    background: linear-gradient(90deg, #dc3545, #c82333);
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-message {
    font-size: 0.85rem;
    color: #495057;
    font-weight: 500;
}

.progress-message.error {
    color: #dc3545;
}

/* Enhanced Error Displays */
.error-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    padding: 10px;
    background: #f8d7da;
    border-radius: 4px;
    border-left: 4px solid #dc3545;
}

.error-icon {
    font-size: 1.2rem;
}

.error-timestamp {
    margin-left: auto;
    font-size: 0.8rem;
    color: #6c757d;
}

.error-message {
    padding: 10px;
    background: #fff5f5;
    border-radius: 4px;
    margin-bottom: 10px;
    color: #721c24;
    font-weight: 500;
}

.error-details {
    margin-top: 10px;
}

.error-details details {
    background: #f8f9fa;
    border-radius: 4px;
    padding: 10px;
}

.error-details summary {
    cursor: pointer;
    font-weight: 500;
    color: #495057;
    margin-bottom: 10px;
}

.error-details pre {
    background: #2d3748;
    color: #e2e8f0;
    padding: 15px;
    border-radius: 4px;
    overflow-x: auto;
    font-size: 0.85rem;
    line-height: 1.4;
    margin: 0;
}

/* Execution Header */
.execution-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    padding: 10px;
    background: #e3f2fd;
    border-radius: 4px;
    border-left: 4px solid #2196f3;
}

.execution-icon {
    font-size: 1.2rem;
}

.execution-time {
    margin-left: auto;
    font-size: 0.8rem;
    color: #6c757d;
}

/* Progress Messages */
.progress-messages {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    background: #fff;
}

.progress-message {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 12px;
    border-bottom: 1px solid #f1f3f4;
    font-size: 0.85rem;
}

.progress-message:last-child {
    border-bottom: none;
}

.progress-timestamp {
    color: #6c757d;
    font-size: 0.75rem;
    min-width: 80px;
}

.progress-stage {
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.7rem;
    font-weight: 600;
    color: #495057;
    min-width: 100px;
    text-align: center;
}

.progress-text {
    color: #495057;
    flex: 1;
}

/* Responsive design */
@media (max-width: 768px) {
    .catalog-management-container {
        padding: 10px;
    }
    
    .catalog-stats {
        flex-direction: column;
        gap: 10px;
    }
    
    .catalog-grid {
        grid-template-columns: 1fr;
    }
    
    .command-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .command-meta {
        flex-direction: column;
        gap: 5px;
    }
    
    .parameter-group {
        grid-template-columns: 1fr;
    }
}
