/* ==========================================================================
   Layout Styles
   ========================================================================== */

/* Grid system */
.row {
    display: flex;
    flex-wrap: wrap;
    margin-left: -15px;
    margin-right: -15px;
}

.col,
.col-1, .col-2, .col-3, .col-4, .col-5, .col-6,
.col-7, .col-8, .col-9, .col-10, .col-11, .col-12,
.col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6,
.col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12 {
    position: relative;
    width: 100%;
    padding-left: 15px;
    padding-right: 15px;
}

.col { flex: 1 0 0%; }
.col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
.col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.col-3 { flex: 0 0 25%; max-width: 25%; }
.col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
.col-6 { flex: 0 0 50%; max-width: 50%; }
.col-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
.col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.col-9 { flex: 0 0 75%; max-width: 75%; }
.col-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
.col-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
.col-12 { flex: 0 0 100%; max-width: 100%; }

/* Medium screens and up */
@media (min-width: 768px) {
    .col-md-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
    .col-md-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
    .col-md-3 { flex: 0 0 25%; max-width: 25%; }
    .col-md-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
    .col-md-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
    .col-md-6 { flex: 0 0 50%; max-width: 50%; }
    .col-md-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
    .col-md-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
    .col-md-9 { flex: 0 0 75%; max-width: 75%; }
    .col-md-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
    .col-md-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
    .col-md-12 { flex: 0 0 100%; max-width: 100%; }
}

/* Statistics grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.stat-card {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    text-align: center;
}

.stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #007bff;
    display: block;
}

.stat-label {
    font-size: 14px;
    color: #6c757d;
    margin-top: 5px;
}

/* Filter layouts */
.filters {
    margin-bottom: 20px;
    background: #f8f8f8;
    padding: 15px;
    border-radius: 4px;
    border: 1px solid #eee;
}

.filter-form {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    align-items: flex-end;
}

/* Table containers */
.table-container {
    max-height: 70vh;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* Action button groups */
.action-buttons {
    display: flex;
    gap: 5px;
}

.action-buttons .btn {
    padding: 4px 8px;
    font-size: 12px;
    margin-right: 0;
}

/* Batch actions layout */
.batch-actions-card {
    background: #e7f3ff;
    border: 1px solid #b3d9ff;
    margin-bottom: 15px;
}

.batch-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
}

.batch-info {
    font-weight: 600;
    color: #495057;
}

.batch-buttons {
    display: flex;
    gap: 10px;
}

.batch-buttons .btn {
    padding: 6px 12px;
    font-size: 14px;
}

/* Tab layouts */
.tab-container {
    margin-bottom: 20px;
}

.tab-nav {
    display: flex;
    border-bottom: 2px solid #dee2e6;
    margin-bottom: 20px;
}

.tab-btn {
    background: none;
    border: none;
    padding: 12px 20px;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    font-weight: 500;
    transition: all 0.3s ease;
}

.tab-btn:hover {
    background-color: #f8f9fa;
}

.tab-btn.active {
    border-bottom-color: #007bff;
    color: #007bff;
    background-color: #f8f9fa;
}

.tab-content {
    display: none;
    padding: 20px 0;
}

.tab-content.active {
    display: block;
}

/* Section layouts */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #dee2e6;
}

.section-title {
    margin: 0;
    font-size: 1.5rem;
    color: #495057;
}

.section-actions {
    display: flex;
    gap: 10px;
}

/* Sidebar layouts */
.sidebar-layout {
    display: flex;
    gap: 20px;
}

.sidebar {
    flex: 0 0 250px;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
}

.main-content {
    flex: 1;
}

/* Panel layouts */
.panel {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 20px;
}

.panel-header {
    background: #f8f9fa;
    padding: 15px 20px;
    border-bottom: 1px solid #dee2e6;
    border-radius: 8px 8px 0 0;
}

.panel-title {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.panel-body {
    padding: 20px;
}

.panel-footer {
    background: #f8f9fa;
    padding: 15px 20px;
    border-top: 1px solid #dee2e6;
    border-radius: 0 0 8px 8px;
}

/* Responsive design */
@media (max-width: 768px) {
    .filter-form {
        flex-direction: column;
        align-items: stretch;
    }

    .form-group select,
    .form-group input[type="text"] {
        min-width: auto;
        width: 100%;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .batch-actions {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .batch-buttons {
        flex-direction: column;
    }

    .tab-nav {
        flex-direction: column;
    }

    .tab-btn {
        border-bottom: none;
        border-left: 3px solid transparent;
        text-align: left;
    }

    .tab-btn.active {
        border-left-color: #007bff;
        border-bottom-color: transparent;
    }

    .sidebar-layout {
        flex-direction: column;
    }

    .sidebar {
        flex: none;
    }

    .section-header {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .section-actions {
        justify-content: center;
    }
}
