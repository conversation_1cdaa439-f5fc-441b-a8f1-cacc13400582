/* User Profile Management Styles */

/* Main container styles */
.user-profile-management {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

/* Card styles */
.card {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 15px 20px;
    font-weight: 600;
}

.card-body {
    padding: 20px;
}

/* Table styles */
.table {
    margin-bottom: 0;
}

.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    padding: 12px;
}

.table td {
    padding: 12px;
    vertical-align: middle;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0,0,0,.02);
}

/* Profile table specific styles */
.profile-table {
    font-size: 14px;
}

.profile-table .profile-checkbox {
    margin: 0;
}

.profile-table tr.selected {
    background-color: #e3f2fd !important;
}

.profile-actions {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.profile-actions .btn {
    padding: 4px 8px;
    font-size: 12px;
}

/* Batch actions */
.batch-actions-card {
    background-color: #e8f4fd;
    border: 1px solid #b3d9ff;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
    display: none;
}

.batch-actions-card .btn {
    margin-right: 10px;
    margin-bottom: 5px;
}

/* Import/Export styles */
.import-export-section {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.tab-navigation {
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 20px;
}

.tab-btn {
    background: none;
    border: none;
    padding: 10px 20px;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    font-weight: 500;
}

.tab-btn.active {
    border-bottom-color: #007bff;
    color: #007bff;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Upload area styles */
.upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 40px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: #fafafa;
}

.upload-area:hover {
    border-color: #007bff;
    background-color: #f0f8ff;
}

.upload-area.dragover {
    border-color: #007bff;
    background-color: #e3f2fd;
}

/* Form styles */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    font-weight: 600;
    margin-bottom: 8px;
    display: block;
}

.form-control {
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 14px;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

/* Status and validation styles */
.import-status {
    padding: 15px;
    border-radius: 6px;
    margin: 15px 0;
    display: none;
}

.import-status.success {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.import-status.error {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.import-status.warning {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

.import-status.info {
    background-color: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
}

.json-validation {
    padding: 10px;
    border-radius: 4px;
    margin-top: 10px;
    font-size: 14px;
}

.json-validation.valid {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.json-validation.invalid {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

/* Profile preview styles */
.profile-preview {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    margin: 15px 0;
    display: none;
}

.preview-summary {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.preview-stat {
    text-align: center;
    padding: 10px;
    background-color: white;
    border-radius: 4px;
    border: 1px solid #dee2e6;
    min-width: 80px;
}

.preview-stat-number {
    display: block;
    font-size: 24px;
    font-weight: bold;
    color: #007bff;
}

.preview-stat-label {
    font-size: 12px;
    color: #6c757d;
    margin-top: 5px;
}

.preview-details {
    background-color: white;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 15px;
    max-height: 300px;
    overflow-y: auto;
}

/* Import controls */
.import-controls {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    margin: 15px 0;
    display: none;
}

.import-controls .btn {
    margin-right: 10px;
    margin-bottom: 5px;
}

/* History table styles */
.history-table {
    font-size: 13px;
}

.history-actions {
    display: flex;
    gap: 5px;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-completed {
    background-color: #d4edda;
    color: #155724;
}

.status-failed {
    background-color: #f8d7da;
    color: #721c24;
}

.status-partial {
    background-color: #fff3cd;
    color: #856404;
}

.status-in_progress {
    background-color: #d1ecf1;
    color: #0c5460;
}

.status-pending {
    background-color: #e2e3e5;
    color: #383d41;
}

.quality-score {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
}

.quality-excellent {
    background-color: #d4edda;
    color: #155724;
}

.quality-good {
    background-color: #d1ecf1;
    color: #0c5460;
}

.quality-fair {
    background-color: #fff3cd;
    color: #856404;
}

.quality-poor {
    background-color: #f8d7da;
    color: #721c24;
}

/* Pagination styles */
.pagination-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 15px;
}

.pagination-info {
    font-size: 14px;
    color: #6c757d;
}

/* Responsive design */
@media (max-width: 768px) {
    .user-profile-management {
        padding: 10px;
    }
    
    .profile-actions {
        flex-direction: column;
    }
    
    .preview-summary {
        flex-direction: column;
        gap: 10px;
    }
    
    .batch-actions-card .btn {
        width: 100%;
        margin-right: 0;
    }
    
    .table-responsive {
        font-size: 12px;
    }
}

/* Utility classes */
.text-muted {
    color: #6c757d !important;
}

.text-success {
    color: #28a745 !important;
}

.text-danger {
    color: #dc3545 !important;
}

.text-warning {
    color: #ffc107 !important;
}

.text-info {
    color: #17a2b8 !important;
}

.mb-0 {
    margin-bottom: 0 !important;
}

.mt-3 {
    margin-top: 1rem !important;
}

.mb-3 {
    margin-bottom: 1rem !important;
}

.d-none {
    display: none !important;
}

.d-block {
    display: block !important;
}

/* Completeness bar styles */
.completeness-bar {
    position: relative;
    width: 100px;
    height: 20px;
    background-color: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
}

.completeness-fill {
    height: 100%;
    background: linear-gradient(90deg, #dc3545 0%, #ffc107 50%, #28a745 100%);
    border-radius: 10px;
    transition: width 0.3s ease;
}

.completeness-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 11px;
    font-weight: 600;
    color: #333;
    text-shadow: 0 0 2px rgba(255,255,255,0.8);
}

/* Profile container styles */
.profile-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Additional responsive styles */
@media (max-width: 576px) {
    .completeness-bar {
        width: 80px;
        height: 16px;
    }

    .completeness-text {
        font-size: 10px;
    }
}
