/**
 * User Profile Management JavaScript
 * Handles all interactive functionality for the user profile management interface
 */

// Global variables
let selectedProfiles = new Set();
let currentHistoryPage = 1;
const historyPageSize = 20;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeBatchActions();
    initializeImportFunctionality();
    initializeHistoryManagement();
    initializeSchemaValidation();
    initializeProfileActions();
});

/**
 * Initialize batch action functionality
 */
function initializeBatchActions() {
    const selectAllCheckbox = document.getElementById('select-all-checkbox');
    const profileCheckboxes = document.querySelectorAll('.profile-checkbox');
    const batchActionsCard = document.querySelector('.batch-actions-card');
    const selectedCountSpan = document.getElementById('selected-count');
    const selectAllBtn = document.getElementById('select-all-btn');
    const deselectAllBtn = document.getElementById('deselect-all-btn');
    const batchDeleteBtn = document.getElementById('batch-delete-btn');
    const batchExportBtn = document.getElementById('batch-export-btn');

    if (!selectAllCheckbox || !batchActionsCard) return;

    function updateBatchUI() {
        const count = selectedProfiles.size;
        selectedCountSpan.textContent = `${count} profile${count !== 1 ? 's' : ''} selected`;

        // Show/hide batch actions
        batchActionsCard.style.display = count > 0 ? 'block' : 'none';

        // Enable/disable buttons
        if (batchDeleteBtn) batchDeleteBtn.disabled = count === 0;
        if (batchExportBtn) batchExportBtn.disabled = count === 0;

        // Update select all checkbox state
        if (count === 0) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = false;
        } else if (count === profileCheckboxes.length) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = true;
        } else {
            selectAllCheckbox.indeterminate = true;
        }

        // Update row highlighting
        profileCheckboxes.forEach(checkbox => {
            const row = checkbox.closest('tr');
            if (selectedProfiles.has(checkbox.value)) {
                row.classList.add('selected');
            } else {
                row.classList.remove('selected');
            }
        });
    }

    // Handle individual checkbox changes
    profileCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            if (this.checked) {
                selectedProfiles.add(this.value);
            } else {
                selectedProfiles.delete(this.value);
            }
            updateBatchUI();
        });
    });

    // Handle select all checkbox
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            if (this.checked) {
                profileCheckboxes.forEach(checkbox => {
                    checkbox.checked = true;
                    selectedProfiles.add(checkbox.value);
                });
            } else {
                profileCheckboxes.forEach(checkbox => {
                    checkbox.checked = false;
                    selectedProfiles.delete(checkbox.value);
                });
            }
            updateBatchUI();
        });
    }

    // Handle select all button
    if (selectAllBtn) {
        selectAllBtn.addEventListener('click', function() {
            profileCheckboxes.forEach(checkbox => {
                checkbox.checked = true;
                selectedProfiles.add(checkbox.value);
            });
            updateBatchUI();
        });
    }

    // Handle deselect all button
    if (deselectAllBtn) {
        deselectAllBtn.addEventListener('click', function() {
            profileCheckboxes.forEach(checkbox => {
                checkbox.checked = false;
                selectedProfiles.delete(checkbox.value);
            });
            updateBatchUI();
        });
    }

    // Handle batch delete
    if (batchDeleteBtn) {
        batchDeleteBtn.addEventListener('click', function() {
            if (selectedProfiles.size === 0) return;

            const count = selectedProfiles.size;
            const confirmMessage = `Are you sure you want to delete ${count} profile${count !== 1 ? 's' : ''}? This action cannot be undone.`;

            if (confirm(confirmMessage)) {
                performBatchDelete(Array.from(selectedProfiles));
            }
        });
    }

    // Handle batch export
    if (batchExportBtn) {
        batchExportBtn.addEventListener('click', function() {
            if (selectedProfiles.size === 0) return;
            performBatchExport(Array.from(selectedProfiles));
        });
    }

    // Initial UI update
    updateBatchUI();
}

/**
 * Initialize import functionality
 */
function initializeImportFunctionality() {
    // Import functionality is now handled by ImportManager
    // We just need to ensure the ImportManager is available
    if (!window.importManager) {
        console.warn('ImportManager not initialized, some import functionality may not work');
    }

    // Schema management buttons that are not handled by ImportManager
    const downloadSchemaBtn = document.getElementById('download-schema-btn');
    const validateSchemaBtn = document.getElementById('validate-schema-btn');

    if (downloadSchemaBtn) downloadSchemaBtn.addEventListener('click', downloadSchema);
    if (validateSchemaBtn) validateSchemaBtn.addEventListener('click', validateSchemaCompliance);
}

/**
 * Initialize history management
 */
function initializeHistoryManagement() {
    // Load initial import history
    loadImportHistory(1);

    // Set up event listeners for import history
    const refreshBtn = document.getElementById('refresh-history-btn');
    const prevBtn = document.getElementById('history-prev-btn');
    const nextBtn = document.getElementById('history-next-btn');
    
    if (refreshBtn) {
        refreshBtn.addEventListener('click', () => {
            loadImportHistory(currentHistoryPage);
        });
    }

    if (prevBtn) {
        prevBtn.addEventListener('click', () => {
            if (currentHistoryPage > 1) {
                loadImportHistory(currentHistoryPage - 1);
            }
        });
    }

    if (nextBtn) {
        nextBtn.addEventListener('click', () => {
            loadImportHistory(currentHistoryPage + 1);
        });
    }

    // Set up filter change listeners
    ['history-status-filter', 'history-type-filter', 'history-user-filter'].forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('change', () => {
                loadImportHistory(1); // Reset to first page when filtering
            });
        }
    });

    // Set up clear history button
    const clearHistoryBtn = document.getElementById('clear-history-btn');
    if (clearHistoryBtn) {
        clearHistoryBtn.addEventListener('click', function() {
            if (confirm('Are you sure you want to clear old import history records? This will delete records older than 30 days.')) {
                // TODO: Implement clear old records functionality
                alert('Clear old records functionality will be implemented in the next update.');
            }
        });
    }
}

/**
 * Initialize schema validation
 */
function initializeSchemaValidation() {
    const validateAllBtn = document.getElementById('validate-all-catalogs-btn');
    const refreshStatusBtn = document.getElementById('refresh-catalog-status-btn');

    if (validateAllBtn) {
        validateAllBtn.addEventListener('click', validateAllCatalogs);
    }

    if (refreshStatusBtn) {
        refreshStatusBtn.addEventListener('click', refreshCatalogStatus);
    }
}

/**
 * Initialize profile action buttons
 */
function initializeProfileActions() {
    // View profile buttons
    document.querySelectorAll('.view-profile-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const profileId = this.getAttribute('data-profile-id');
            if (profileId && typeof openProfileModal === 'function') {
                openProfileModal(profileId, 'view');
            } else {
                console.error('Profile modal functionality not available');
                showAlert('Profile modal functionality not available', 'warning');
            }
        });
    });

    // Edit profile buttons
    document.querySelectorAll('.edit-profile-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const profileId = this.getAttribute('data-profile-id');
            if (profileId && typeof openProfileModal === 'function') {
                openProfileModal(profileId, 'edit');
            } else {
                console.error('Profile modal functionality not available');
                showAlert('Profile modal functionality not available', 'warning');
            }
        });
    });
}

/**
 * Get CSRF token for AJAX requests
 */
function getCsrfToken() {
    return document.querySelector('[name=csrfmiddlewaretoken]').value ||
           document.querySelector('meta[name=csrf-token]').getAttribute('content') ||
           '';
}

/**
 * Show alert message
 */
function showAlert(message, type = 'info') {
    // Create alert element
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // Insert at top of content
    const content = document.querySelector('.user-profile-management');
    if (content) {
        content.insertBefore(alertDiv, content.firstChild);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
}

// Import tab switching and file handling functions are now handled by ImportManager
// These functions are kept as wrappers for backward compatibility

/**
 * Switch import tab (delegated to ImportManager)
 */
function switchImportTab(tabName) {
    if (window.importManager) {
        window.importManager.switchTab(tabName);
    } else {
        console.warn('ImportManager not available for tab switching');
    }
}

/**
 * Process uploaded file (delegated to ImportManager)
 */
function processFile(file) {
    if (window.importManager) {
        window.importManager.processFile(file);
    } else {
        console.warn('ImportManager not available for file processing');
        showImportStatus('error', 'Import system not available', 'Please refresh the page and try again.');
    }
}

/**
 * Validate JSON input (delegated to ImportManager)
 */
async function validateJsonInput() {
    if (window.importManager) {
        await window.importManager.validateJsonInput();
    } else {
        console.warn('ImportManager not available for JSON validation');
    }
}

/**
 * Show import status
 */
function showImportStatus(type, message, details) {
    const status = document.getElementById('import-status');
    const messageEl = document.getElementById('status-message');
    const detailsEl = document.getElementById('status-details');

    if (!status || !messageEl || !detailsEl) return;

    status.className = `import-status ${type}`;
    messageEl.textContent = message;
    detailsEl.textContent = details;
    status.style.display = 'block';
}

/**
 * Clear import data (delegated to ImportManager)
 */
function clearImportData() {
    if (window.importManager) {
        window.importManager.clearImportData();
    } else {
        console.warn('ImportManager not available for clearing data');
    }
}

/**
 * Hide profile preview (delegated to ImportManager)
 */
function hideProfilePreview() {
    if (window.importManager) {
        window.importManager.hideProfilePreview();
    }
}

/**
 * Show import controls (delegated to ImportManager)
 */
function showImportControls() {
    if (window.importManager) {
        window.importManager.showImportControls();
    }
}

/**
 * Hide import controls (delegated to ImportManager)
 */
function hideImportControls() {
    if (window.importManager) {
        window.importManager.hideImportControls();
    }
}

/**
 * Perform batch delete
 */
async function performBatchDelete(profileIds) {
    try {
        const response = await fetch('/admin/user-profiles/batch-delete/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken(),
            },
            body: JSON.stringify({ profile_ids: profileIds })
        });

        if (response.ok) {
            const result = await response.json();
            showAlert(`Successfully deleted ${result.deleted_count} profiles.`, 'success');
            location.reload(); // Refresh the page
        } else {
            const error = await response.json();
            showAlert(`Error deleting profiles: ${error.error}`, 'danger');
        }
    } catch (error) {
        console.error('Batch delete error:', error);
        showAlert('An error occurred while deleting profiles.', 'danger');
    }
}

/**
 * Perform batch export
 */
async function performBatchExport(profileIds) {
    try {
        const response = await fetch('/admin/user-profiles/batch-export/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken(),
            },
            body: JSON.stringify({ profile_ids: profileIds })
        });

        if (response.ok) {
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `user_profiles_export_${new Date().toISOString().split('T')[0]}.csv`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        } else {
            const error = await response.json();
            showAlert(`Error exporting profiles: ${error.error}`, 'danger');
        }
    } catch (error) {
        console.error('Batch export error:', error);
        showAlert('An error occurred while exporting profiles.', 'danger');
    }
}

/**
 * Load import history
 */
function loadImportHistory(page = 1) {
    const statusFilter = document.getElementById('history-status-filter');
    const typeFilter = document.getElementById('history-type-filter');
    const userFilter = document.getElementById('history-user-filter');

    const params = new URLSearchParams({
        page: page,
        page_size: historyPageSize
    });

    if (statusFilter && statusFilter.value) params.append('status', statusFilter.value);
    if (typeFilter && typeFilter.value) params.append('import_type', typeFilter.value);
    if (userFilter && userFilter.value) params.append('user', userFilter.value);

    fetch(`/admin/import-history/api/?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.histories) {
                renderImportHistory(data.histories);
                updateHistoryPagination(data.pagination);
                currentHistoryPage = page;
            } else {
                console.error('Error loading import history:', data.error);
                showImportHistoryError('Failed to load import history');
            }
        })
        .catch(error => {
            console.error('Error loading import history:', error);
            showImportHistoryError('Failed to load import history');
        });
}

/**
 * Render import history
 */
function renderImportHistory(histories) {
    const tbody = document.getElementById('import-history-tbody');
    if (!tbody) return;

    if (histories.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="10" class="no-data">
                    No import history records found.
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = histories.map(history => {
        const statusBadge = getStatusBadge(history.status);
        const qualityBadge = getQualityBadge(history.data_quality_score);
        const duration = history.duration ? formatDuration(history.duration) : 'N/A';
        const successRate = `${history.success_rate.toFixed(1)}%`;

        return `
            <tr>
                <td>
                    <code style="font-size: 11px;">${history.id.substring(0, 8)}...</code>
                </td>
                <td>
                    <span class="badge badge-secondary">${history.import_type}</span>
                </td>
                <td>${statusBadge}</td>
                <td>${history.source_profile_name || 'Unknown'}</td>
                <td>${history.initiated_by_username || 'System'}</td>
                <td>
                    <small>${new Date(history.started_at).toLocaleString()}</small>
                </td>
                <td>${duration}</td>
                <td>${successRate}</td>
                <td>${qualityBadge}</td>
                <td>
                    <div class="history-actions">
                        <button class="btn btn-sm btn-outline-primary" onclick="viewImportDetails('${history.id}')">
                            👁️ View
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteImportHistory('${history.id}')">
                            🗑️ Delete
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

/**
 * Get status badge HTML
 */
function getStatusBadge(status) {
    const statusClasses = {
        'completed': 'status-completed',
        'failed': 'status-failed',
        'partial': 'status-partial',
        'in_progress': 'status-in_progress',
        'pending': 'status-pending'
    };

    const className = statusClasses[status] || 'status-pending';
    return `<span class="status-badge ${className}">${status}</span>`;
}

/**
 * Get quality badge HTML
 */
function getQualityBadge(score) {
    if (!score) return '<span class="quality-score quality-poor">N/A</span>';

    const percentage = (score * 100).toFixed(0);
    let className = 'quality-poor';

    if (score >= 0.8) className = 'quality-excellent';
    else if (score >= 0.6) className = 'quality-good';
    else if (score >= 0.4) className = 'quality-fair';

    return `<span class="quality-score ${className}">${percentage}%</span>`;
}

/**
 * Format duration string
 */
function formatDuration(durationStr) {
    // Parse duration string like "0:00:05.123456"
    const parts = durationStr.split(':');
    if (parts.length === 3) {
        const hours = parseInt(parts[0]);
        const minutes = parseInt(parts[1]);
        const seconds = parseFloat(parts[2]);

        if (hours > 0) {
            return `${hours}h ${minutes}m ${seconds.toFixed(1)}s`;
        } else if (minutes > 0) {
            return `${minutes}m ${seconds.toFixed(1)}s`;
        } else {
            return `${seconds.toFixed(1)}s`;
        }
    }
    return durationStr;
}

/**
 * Update history pagination
 */
function updateHistoryPagination(pagination) {
    const prevBtn = document.getElementById('history-prev-btn');
    const nextBtn = document.getElementById('history-next-btn');
    const pageInfo = document.getElementById('history-page-info');

    if (prevBtn) prevBtn.disabled = !pagination.has_previous;
    if (nextBtn) nextBtn.disabled = !pagination.has_next;
    if (pageInfo) pageInfo.textContent = `Page ${pagination.page} of ${pagination.total_pages}`;
}

/**
 * Show import history error
 */
function showImportHistoryError(message) {
    const tbody = document.getElementById('import-history-tbody');
    if (!tbody) return;

    tbody.innerHTML = `
        <tr>
            <td colspan="10" class="error" style="color: #721c24; background-color: #f8d7da; padding: 20px; text-align: center;">
                ❌ ${message}
            </td>
        </tr>
    `;
}

/**
 * Validate all catalogs
 */
function validateAllCatalogs() {
    const statusDiv = document.getElementById('catalog-validation-status');
    const resultsDiv = document.getElementById('catalog-validation-results');

    if (!statusDiv || !resultsDiv) return;

    statusDiv.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div> Validating catalogs...';
    resultsDiv.style.display = 'none';

    fetch('/admin/commands/status/')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayCatalogValidationResults(data.catalog_status);
        } else {
            statusDiv.innerHTML = '<div class="alert alert-danger">Failed to validate catalogs: ' + data.error + '</div>';
        }
    })
    .catch(error => {
        statusDiv.innerHTML = '<div class="alert alert-danger">Network error: ' + error.message + '</div>';
    });
}

/**
 * Refresh catalog status
 */
function refreshCatalogStatus() {
    validateAllCatalogs();
}

/**
 * Display catalog validation results
 */
function displayCatalogValidationResults(catalogStatus) {
    const statusDiv = document.getElementById('catalog-validation-status');
    const resultsDiv = document.getElementById('catalog-validation-results');

    if (!statusDiv || !resultsDiv) return;

    let validCount = 0;
    let totalCount = 0;
    let resultsHtml = '<div class="row">';

    for (const [catalogName, status] of Object.entries(catalogStatus)) {
        totalCount++;
        if (status.valid) validCount++;

        const statusClass = status.valid ? 'success' : 'danger';
        const statusIcon = status.valid ? '✅' : '❌';

        resultsHtml += `
            <div class="col-md-6 mb-3">
                <div class="card border-${statusClass}">
                    <div class="card-body">
                        <h5 class="card-title">${statusIcon} ${catalogName}</h5>
                        <p class="card-text">
                            <span class="badge bg-${statusClass}">${status.valid ? 'Valid' : 'Invalid'}</span>
                        </p>
                        ${!status.valid && status.errors ? `
                            <div class="alert alert-warning">
                                <strong>Errors:</strong>
                                <ul class="mb-0">
                                    ${status.errors.slice(0, 3).map(error => `<li>${error}</li>`).join('')}
                                    ${status.errors.length > 3 ? `<li>... and ${status.errors.length - 3} more</li>` : ''}
                                </ul>
                            </div>
                        ` : ''}
                        <small class="text-muted">Last checked: ${new Date(status.last_checked).toLocaleString()}</small>
                    </div>
                </div>
            </div>
        `;
    }

    resultsHtml += '</div>';

    statusDiv.innerHTML = `
        <div class="alert alert-${validCount === totalCount ? 'success' : 'warning'}">
            <strong>Validation Complete:</strong> ${validCount}/${totalCount} catalogs are valid
        </div>
    `;

    resultsDiv.innerHTML = resultsHtml;
    resultsDiv.style.display = 'block';
}

/**
 * Download schema
 */
async function downloadSchema() {
    try {
        const response = await fetch('/admin/user-profiles/schema/', {
            method: 'GET',
            headers: {
                'Accept': 'application/json'
            }
        });

        if (response.ok) {
            const schema = await response.json();
            const blob = new Blob([JSON.stringify(schema, null, 2)], { type: 'application/json' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'user_profile_schema.json';
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        } else {
            showImportStatus('error', 'Schema download failed', 'Could not retrieve the profile schema.');
        }
    } catch (error) {
        console.error('Schema download error:', error);
        showImportStatus('error', 'Download error', 'An error occurred while downloading the schema.');
    }
}

/**
 * Validate schema compliance
 */
async function validateSchemaCompliance() {
    showImportStatus('info', 'Validating schema compliance...', 'Checking schema coverage against Django models.');

    try {
        const response = await fetch('/admin/user-profiles/validate-schema/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken(),
            },
            body: JSON.stringify({
                action: 'analyze_coverage'
            })
        });

        if (response.ok) {
            const result = await response.json();
            displaySchemaValidationResults(result);
        } else {
            const error = await response.json();
            showImportStatus('error', 'Schema validation failed', error.error || 'Could not validate schema compliance');
        }
    } catch (error) {
        console.error('Schema validation error:', error);
        showImportStatus('error', 'Schema validation error', 'An error occurred while validating schema compliance');
    }
}

/**
 * Perform validation only (delegated to ImportManager)
 */
async function performValidationOnly() {
    if (window.importManager) {
        await window.importManager.performValidationOnly();
    } else {
        console.warn('ImportManager not available for validation');
        showImportStatus('error', 'Import system not available', 'Please refresh the page and try again.');
    }
}

/**
 * Perform profile import (delegated to ImportManager)
 */
async function performImport() {
    if (window.importManager) {
        await window.importManager.performImport();
    } else {
        console.warn('ImportManager not available for import');
        showImportStatus('error', 'Import system not available', 'Please refresh the page and try again.');
    }
}

/**
 * Generate profile from questionnaire using AI (delegated to ImportManager)
 */
async function generateProfileFromQuestionnaire() {
    if (window.importManager) {
        await window.importManager.generateProfileFromQuestionnaire();
    } else {
        console.warn('ImportManager not available for AI generation');
        showImportStatus('error', 'Import system not available', 'Please refresh the page and try again.');
    }
}

/**
 * Display profile preview (delegated to ImportManager)
 */
function displayProfilePreview(profileData) {
    if (window.importManager) {
        window.importManager.displayProfilePreview(profileData);
    } else {
        console.warn('ImportManager not available for profile preview');
    }
}

/**
 * Show profile repair modal
 */
function showProfileRepairModal(profileData, analysisData) {
    // Initialize profile repair manager if not available
    if (!window.profileRepairManager && window.ProfileRepairManager) {
        window.profileRepairManager = new ProfileRepairManager();
    }

    if (window.profileRepairManager) {
        window.profileRepairManager.showRepairModal(profileData, analysisData);
    } else {
        console.warn('Profile repair functionality not available');
        showImportStatus('warning', 'Repair functionality unavailable',
            'Profile repair modal is not available. Please check the console for errors.');
    }
}

/**
 * Get profile data from the currently active tab (delegated to ImportManager)
 */
function getProfileDataFromCurrentTab() {
    if (window.importManager && window.importManager.currentProfileData) {
        return window.importManager.currentProfileData;
    }

    // Fallback: Check if we have stored profile data from file upload or AI generation
    if (window.currentProfileData) {
        return window.currentProfileData;
    }

    // Check JSON paste tab
    const jsonTextarea = document.getElementById('json-textarea');
    if (jsonTextarea && jsonTextarea.value.trim()) {
        try {
            return JSON.parse(jsonTextarea.value);
        } catch (error) {
            console.error('Error parsing JSON from textarea:', error);
            return null;
        }
    }

    return null;
}

/**
 * Get import options from the UI
 */
function getImportOptions() {
    const overwriteExisting = document.getElementById('overwrite-existing')?.checked || false;
    const validateBeforeImport = document.getElementById('validate-before-import')?.checked || true;
    const createBackup = document.getElementById('create-backup')?.checked || true;

    return {
        overwrite_existing: overwriteExisting,
        validate_before_import: validateBeforeImport,
        create_backup: createBackup
    };
}

/**
 * Check if account selection is needed
 */
async function checkIfAccountSelectionNeeded(profileData) {
    if (!profileData.user_account) return false;

    const username = profileData.user_account.username;
    const email = profileData.user_account.email;

    if (!username && !email) return false;

    // Check if user already exists
    try {
        const response = await fetch(`/admin/user-profiles/check-user/?username=${encodeURIComponent(username)}&email=${encodeURIComponent(email)}`);
        const result = await response.json();
        return result.user_exists;
    } catch (error) {
        console.error('Error checking user existence:', error);
        return false;
    }
}

/**
 * Show user account selection modal
 */
function showUserAccountSelectionModal(profileData) {
    const modal = document.getElementById('userAccountSelectionModal');
    if (!modal) return;

    // Populate modal with profile data
    document.getElementById('modal-profile-name').textContent = profileData.profile_name || 'Unknown';
    document.getElementById('modal-username').textContent = profileData.user_account?.username || 'N/A';
    document.getElementById('modal-email').textContent = profileData.user_account?.email || 'N/A';
    document.getElementById('modal-full-name').textContent = profileData.demographics?.full_name || 'N/A';

    // Set up event listeners for the modal
    setupUserAccountModalListeners(profileData);

    // Show the modal
    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();
}

/**
 * Set up event listeners for user account modal
 */
function setupUserAccountModalListeners(profileData) {
    const proceedBtn = document.getElementById('proceedWithImportBtn');
    const connectExistingRadio = document.getElementById('connectExistingAccount');
    const existingAccountSelection = document.getElementById('existingAccountSelection');
    const existingUserSelect = document.getElementById('existingUserSelect');

    // Handle radio button changes
    document.querySelectorAll('input[name="accountOption"]').forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.value === 'connect') {
                existingAccountSelection.style.display = 'block';
                loadExistingUsers();
            } else {
                existingAccountSelection.style.display = 'none';
            }
        });
    });

    // Handle proceed button
    proceedBtn.onclick = function() {
        const selectedOption = document.querySelector('input[name="accountOption"]:checked').value;
        const options = getImportOptions();

        if (selectedOption === 'connect') {
            const selectedUserId = existingUserSelect.value;
            if (!selectedUserId) {
                alert('Please select an existing user account.');
                return;
            }
            options.connect_existing_user = selectedUserId;
        }

        // Close modal and proceed with import
        const modal = bootstrap.Modal.getInstance(document.getElementById('userAccountSelectionModal'));
        modal.hide();

        // Perform the actual import
        performActualImport(profileData, options);
    };
}

/**
 * Load existing users for selection
 */
async function loadExistingUsers() {
    const select = document.getElementById('existingUserSelect');
    if (!select) return;

    try {
        const response = await fetch('/admin/user-profiles/api/users/');
        const result = await response.json();

        if (result.success && result.users) {
            select.innerHTML = '<option value="">Select a user...</option>';
            result.users.forEach(user => {
                const option = document.createElement('option');
                option.value = user.id;
                option.textContent = `${user.username} (${user.email})`;
                select.appendChild(option);
            });
        }
    } catch (error) {
        console.error('Error loading users:', error);
        select.innerHTML = '<option value="">Error loading users</option>';
    }
}

/**
 * Perform the actual import with resolved options
 */
async function performActualImport(profileData, options) {
    showImportStatus('info', 'Importing profile...', 'Creating user profile and related data.');

    try {
        const response = await fetch('/admin/user-profiles/import/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken(),
            },
            body: JSON.stringify({
                profile_data: profileData,
                options: options
            })
        });

        const result = await response.json();

        if (response.ok && result.success) {
            showImportStatus('success', 'Import successful',
                `Profile '${result.profile_name || 'Unknown'}' imported successfully. ` +
                `Created ${result.created_records || 0} records, updated ${result.updated_records || 0} records.`);

            if (result.warnings && result.warnings.length > 0) {
                const warningsHtml = result.warnings.map(w => `<li>${w}</li>`).join('');
                showImportStatus('warning', 'Import warnings',
                    `<ul>${warningsHtml}</ul>`);
            }

            // Refresh the page after successful import
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        } else {
            showImportStatus('error', 'Import failed',
                result.error || 'Profile import failed.');

            if (result.details) {
                console.error('Import error details:', result.details);
            }
        }
    } catch (error) {
        console.error('Import error:', error);
        showImportStatus('error', 'Import error', 'An error occurred during import.');
    }
}

function performEnhancedValidation(jsonData) {
    // Enhanced validation is now handled by the backend
    // This function can be used for client-side pre-validation if needed
    console.log('Enhanced validation delegated to backend', jsonData);
}

function displaySchemaValidationResults(result) {
    // Placeholder for schema validation results display
    console.log('Schema validation results would be displayed here', result);
}

// Export functions for global access
window.UserProfileManagement = {
    getCsrfToken,
    showAlert,
    showImportStatus,
    clearImportData,
    switchImportTab,
    processFile,
    validateJsonInput,
    performBatchDelete,
    performBatchExport,
    loadImportHistory,
    renderImportHistory,
    getStatusBadge,
    getQualityBadge,
    formatDuration,
    updateHistoryPagination,
    showImportHistoryError,
    validateAllCatalogs,
    refreshCatalogStatus,
    displayCatalogValidationResults,
    downloadSchema,
    validateSchemaCompliance,
    performValidationOnly,
    performImport,
    generateProfileFromQuestionnaire,
    displayProfilePreview,
    getProfileDataFromCurrentTab,
    getImportOptions,
    checkIfAccountSelectionNeeded,
    showUserAccountSelectionModal,
    setupUserAccountModalListeners,
    loadExistingUsers,
    performActualImport,
    performEnhancedValidation,
    selectedProfiles,
    currentHistoryPage
};
