/**
 * Profile Repair Modal JavaScript
 * Handles interactive profile repair functionality
 */

class ProfileRepairManager {
    constructor() {
        this.originalProfileData = null;
        this.currentProfileData = null;
        this.analysisData = null;
        this.repairSuggestions = [];
        
        this.initializeEventListeners();
    }
    
    initializeEventListeners() {
        // Auto-repair buttons
        document.getElementById('applyAutoRepairsBtn')?.addEventListener('click', () => {
            this.applyAutoRepairs();
        });
        
        document.getElementById('previewRepairsBtn')?.addEventListener('click', () => {
            this.previewRepairs();
        });
        
        // Manual repair buttons
        document.getElementById('revalidateBtn')?.addEventListener('click', () => {
            this.revalidateProfile();
        });
        
        document.getElementById('applyRepairsBtn')?.addEventListener('click', () => {
            this.applyRepairsAndImport();
        });
        
        // JSON editor buttons
        document.getElementById('formatJsonBtn')?.addEventListener('click', () => {
            this.formatJson();
        });
        
        document.getElementById('validateJsonBtn')?.addEventListener('click', () => {
            this.validateJson();
        });
        
        document.getElementById('resetJsonBtn')?.addEventListener('click', () => {
            this.resetJson();
        });
        
        // Repair preview modal
        document.getElementById('confirmRepairsBtn')?.addEventListener('click', () => {
            this.confirmRepairs();
        });
        
        // JSON editor real-time validation
        document.getElementById('profileDataEditor')?.addEventListener('input', () => {
            this.debounce(this.validateJsonRealTime.bind(this), 500)();
        });
    }
    
    showRepairModal(profileData, analysisData) {
        this.originalProfileData = JSON.parse(JSON.stringify(profileData));
        this.currentProfileData = JSON.parse(JSON.stringify(profileData));
        this.analysisData = analysisData;

        this.populateModal();

        // Close any existing modals first to prevent backdrop conflicts
        const existingModals = document.querySelectorAll('.modal.show');
        existingModals.forEach(modal => {
            const bsModal = bootstrap.Modal.getInstance(modal);
            if (bsModal) {
                bsModal.hide();
            }
        });

        // Wait a bit for existing modals to close, then show repair modal
        setTimeout(() => {
            const modalElement = document.getElementById('profileRepairModal');
            const modal = new bootstrap.Modal(modalElement, {
                backdrop: 'static',  // Prevent closing by clicking backdrop
                keyboard: false      // Prevent closing with ESC key
            });
            modal.show();
        }, 300);
    }
    
    populateModal() {
        // Update summary
        this.updateErrorSummary();
        
        // Populate auto-repair section
        this.populateAutoRepairSection();
        
        // Populate manual repair section
        this.populateManualRepairSection();
        
        // Initialize JSON editor
        this.initializeJsonEditor();
        
        // Update next steps
        this.updateNextSteps();
    }
    
    updateErrorSummary() {
        const analysis = this.analysisData;

        document.getElementById('summaryText').textContent = analysis.summary || 'No analysis available';

        const severityBreakdown = analysis.severity_breakdown || {};
        document.getElementById('criticalCount').textContent = `${severityBreakdown.critical || 0} Critical`;
        document.getElementById('highCount').textContent = `${severityBreakdown.high || 0} High`;
        document.getElementById('mediumCount').textContent = `${severityBreakdown.medium || 0} Medium`;
        document.getElementById('autoFixableCount').textContent = `${analysis.auto_fixable_count || 0} Auto-fixable`;

        // Show architectural issues if any
        this.populateArchitecturalIssues();
    }

    populateArchitecturalIssues() {
        const architecturalIssues = this.analysisData.diagnostics?.filter(d => d.category === 'architecture') || [];
        const section = document.getElementById('architecturalIssues');
        const list = document.getElementById('architecturalIssuesList');

        if (architecturalIssues.length > 0) {
            section.style.display = 'block';

            let html = '';
            architecturalIssues.forEach((issue, index) => {
                const severityClass = this.getSeverityClass(issue.severity);
                const severityIcon = this.getSeverityIcon(issue.severity);

                html += `
                    <div class="card mb-3 border-${severityClass}">
                        <div class="card-header bg-${severityClass} bg-opacity-10">
                            <div class="d-flex justify-content-between align-items-start">
                                <h6 class="mb-0">
                                    ${severityIcon} ${issue.message}
                                </h6>
                                <span class="badge bg-${severityClass}">${issue.severity}</span>
                            </div>
                            <small class="text-muted">${issue.field_path}</small>
                        </div>
                        <div class="card-body">
                            <p class="mb-3">${issue.description}</p>

                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Current Value:</h6>
                                    <code class="d-block p-2 bg-light rounded">${JSON.stringify(issue.current_value, null, 2)}</code>
                                </div>
                                <div class="col-md-6">
                                    <h6>Expected Format:</h6>
                                    <code class="d-block p-2 bg-light rounded">${issue.expected_format}</code>
                                </div>
                            </div>

                            <div class="mt-3">
                                <h6>Fixing Guidelines:</h6>
                                <ul class="mb-0">
                                    ${issue.suggestions.map(suggestion => `<li>${suggestion}</li>`).join('')}
                                </ul>
                            </div>

                            ${issue.examples ? `
                                <div class="mt-3">
                                    <h6>Examples:</h6>
                                    <div class="bg-light p-2 rounded">
                                        ${issue.examples.map(example => `<code class="d-block">${example}</code>`).join('')}
                                    </div>
                                </div>
                            ` : ''}

                            ${issue.documentation_link ? `
                                <div class="mt-3">
                                    <a href="${issue.documentation_link}" class="btn btn-sm btn-outline-info" target="_blank">
                                        <i class="fas fa-book me-1"></i>View Documentation
                                    </a>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                `;
            });

            list.innerHTML = html;
        } else {
            section.style.display = 'none';
        }
    }

    getSeverityClass(severity) {
        const classes = {
            'critical': 'danger',
            'high': 'warning',
            'medium': 'info',
            'low': 'secondary',
            'info': 'light'
        };
        return classes[severity] || 'secondary';
    }

    getSeverityIcon(severity) {
        const icons = {
            'critical': '<i class="fas fa-times-circle text-danger"></i>',
            'high': '<i class="fas fa-exclamation-triangle text-warning"></i>',
            'medium': '<i class="fas fa-info-circle text-info"></i>',
            'low': '<i class="fas fa-minus-circle text-secondary"></i>',
            'info': '<i class="fas fa-lightbulb text-light"></i>'
        };
        return icons[severity] || '<i class="fas fa-question-circle"></i>';
    }

    populateAutoRepairSection() {
        const autoFixableErrors = this.analysisData.diagnostics?.filter(d => d.auto_fixable) || [];
        const autoRepairSection = document.getElementById('autoRepairSection');
        const autoRepairList = document.getElementById('autoRepairList');
        
        if (autoFixableErrors.length > 0) {
            autoRepairSection.style.display = 'block';
            autoRepairList.innerHTML = '';
            
            autoFixableErrors.forEach(error => {
                const listItem = document.createElement('li');
                listItem.className = 'list-group-item d-flex justify-content-between align-items-start';
                listItem.innerHTML = `
                    <div class="ms-2 me-auto">
                        <div class="fw-bold">${error.message}</div>
                        <small class="text-muted">
                            <span class="error-field-path">${error.field_path}</span>
                        </small>
                        <div class="mt-1">
                            <small class="text-success">
                                <i class="fas fa-magic me-1"></i>
                                ${error.suggestions[0] || 'Automatic repair available'}
                            </small>
                        </div>
                    </div>
                    <span class="badge bg-success rounded-pill">Auto-fix</span>
                `;
                autoRepairList.appendChild(listItem);
            });
        } else {
            autoRepairSection.style.display = 'none';
        }
    }
    
    populateManualRepairSection() {
        const manualErrors = this.analysisData.diagnostics?.filter(d => !d.auto_fixable) || [];
        const manualRepairList = document.getElementById('manualRepairList');
        
        if (manualErrors.length === 0) {
            manualRepairList.innerHTML = '<p class="text-success"><i class="fas fa-check me-2"></i>No manual repairs required!</p>';
            return;
        }
        
        manualRepairList.innerHTML = '';
        
        manualErrors.forEach((error, index) => {
            const errorDiv = document.createElement('div');
            errorDiv.className = `error-item ${this.getSeverityClass(error.severity)}`;
            errorDiv.innerHTML = `
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <h6 class="mb-1">${error.message}</h6>
                    <span class="badge bg-${this.getSeverityBadgeClass(error.severity)}">${error.severity}</span>
                </div>
                <p class="mb-2 text-muted">${error.description}</p>
                <div class="mb-2">
                    <strong>Field:</strong> <span class="error-field-path">${error.field_path}</span>
                </div>
                <div class="mb-2">
                    <strong>Current Value:</strong> 
                    <code>${JSON.stringify(error.current_value)}</code>
                </div>
                <div class="mb-3">
                    <strong>Expected:</strong> ${error.expected_format}
                </div>
                <div class="suggestions">
                    <strong>Suggestions:</strong>
                    ${error.suggestions.map(suggestion => `
                        <div class="suggestion-item">
                            <i class="fas fa-lightbulb me-2 text-warning"></i>
                            ${suggestion}
                        </div>
                    `).join('')}
                </div>
                ${error.examples && error.examples.length > 0 ? `
                    <div class="mt-2">
                        <strong>Examples:</strong>
                        <div class="mt-1">
                            ${error.examples.slice(0, 3).map(example => `
                                <code class="me-2">${example}</code>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}
                ${error.documentation_link ? `
                    <div class="mt-2">
                        <a href="${error.documentation_link}" target="_blank" class="btn btn-sm btn-outline-info">
                            <i class="fas fa-external-link-alt me-1"></i>Documentation
                        </a>
                    </div>
                ` : ''}
            `;
            manualRepairList.appendChild(errorDiv);
        });
    }
    
    initializeJsonEditor() {
        const editor = document.getElementById('profileDataEditor');
        editor.value = JSON.stringify(this.currentProfileData, null, 2);
    }
    
    updateNextSteps() {
        const nextStepsList = document.getElementById('nextStepsList');
        const nextSteps = this.analysisData.next_steps || [];
        
        nextStepsList.innerHTML = '';
        nextSteps.forEach(step => {
            const listItem = document.createElement('li');
            listItem.innerHTML = step;
            nextStepsList.appendChild(listItem);
        });
    }
    
    async applyAutoRepairs() {
        try {
            this.showLoading('Applying automatic repairs...');
            
            const response = await fetch('/admin/user-profiles/auto-repair/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCsrfToken()
                },
                body: JSON.stringify({
                    profile_data: this.currentProfileData,
                    repair_options: {
                        trust_repair_strategy: 'increase_overall'
                    }
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.currentProfileData = result.repaired_data;
                this.initializeJsonEditor();
                
                this.showSuccess(`Applied ${result.repairs_applied.length} automatic repairs`);
                
                // Re-analyze the repaired data
                await this.revalidateProfile();
            } else {
                this.showError('Auto-repair failed: ' + (result.error || 'Unknown error'));
            }
        } catch (error) {
            this.showError('Error applying auto-repairs: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }
    
    async revalidateProfile() {
        try {
            this.showLoading('Re-validating profile...');
            
            // Get current data from editor
            const editorData = document.getElementById('profileDataEditor').value;
            try {
                this.currentProfileData = JSON.parse(editorData);
            } catch (e) {
                this.showError('Invalid JSON in editor. Please fix JSON syntax first.');
                return;
            }
            
            const response = await fetch('/admin/user-profiles/analyze/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCsrfToken()
                },
                body: JSON.stringify({
                    profile_data: this.currentProfileData
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.analysisData = result.analysis;
                this.populateModal();
                
                if (result.analysis.total_errors === 0) {
                    this.showSuccess('Profile validation passed! Ready for import.');
                } else {
                    this.showInfo(`Found ${result.analysis.total_errors} remaining issues.`);
                }
            } else {
                this.showError('Validation failed: ' + (result.error || 'Unknown error'));
            }
        } catch (error) {
            this.showError('Error during validation: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    async applyRepairsAndImport() {
        try {
            this.showLoading('Applying repairs and importing profile...');

            // Get current data from editor
            const editorData = document.getElementById('profileDataEditor').value;
            try {
                this.currentProfileData = JSON.parse(editorData);
            } catch (e) {
                this.showError('Invalid JSON in editor. Please fix JSON syntax first.');
                return;
            }

            // First apply auto-repairs if available
            const autoFixableErrors = this.analysisData.diagnostics?.filter(d => d.auto_fixable) || [];
            if (autoFixableErrors.length > 0) {
                const repairResponse = await fetch('/admin/user-profiles/auto-repair/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': this.getCsrfToken()
                    },
                    body: JSON.stringify({
                        profile_data: this.currentProfileData,
                        repair_options: {
                            trust_repair_strategy: 'increase_overall'
                        }
                    })
                });

                const repairResult = await repairResponse.json();

                if (repairResult.success) {
                    this.currentProfileData = repairResult.repaired_data;
                    this.showInfo(`Applied ${repairResult.repairs_applied.length} automatic repairs`);
                } else {
                    this.showWarning('Some auto-repairs failed, proceeding with manual data');
                }
            }

            // Now attempt the import with repaired data
            const importResponse = await fetch('/admin/user-profiles/import/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCsrfToken()
                },
                body: JSON.stringify({
                    profile_data: this.currentProfileData,
                    options: {
                        overwriteExisting: true,
                        createBackup: true
                    }
                })
            });

            const importResult = await importResponse.json();

            if (importResult.success) {
                this.showSuccess(`Import successful! Created ${importResult.created_records} records.`);

                // Close modal and refresh page after delay
                setTimeout(() => {
                    const modal = bootstrap.Modal.getInstance(document.getElementById('profileRepairModal'));
                    modal.hide();
                    location.reload();
                }, 2000);
            } else {
                this.showError('Import failed: ' + (importResult.error || 'Unknown error'));

                // If import failed, re-analyze to show new issues
                if (importResult.analysis) {
                    this.analysisData = importResult.analysis;
                    this.populateModal();
                }
            }

        } catch (error) {
            this.showError('Error during repair and import: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }
    
    formatJson() {
        const editor = document.getElementById('profileDataEditor');
        try {
            const data = JSON.parse(editor.value);
            editor.value = JSON.stringify(data, null, 2);
            this.showSuccess('JSON formatted successfully');
        } catch (error) {
            this.showError('Invalid JSON: ' + error.message);
        }
    }
    
    validateJson() {
        const editor = document.getElementById('profileDataEditor');
        const validation = document.getElementById('editorValidation');
        
        try {
            JSON.parse(editor.value);
            validation.className = 'alert alert-success json-valid';
            validation.innerHTML = '<i class="fas fa-check me-2"></i>Valid JSON';
            validation.style.display = 'block';
        } catch (error) {
            validation.className = 'alert alert-danger json-error';
            validation.innerHTML = `<i class="fas fa-times me-2"></i>Invalid JSON: ${error.message}`;
            validation.style.display = 'block';
        }
    }
    
    resetJson() {
        document.getElementById('profileDataEditor').value = JSON.stringify(this.originalProfileData, null, 2);
        this.currentProfileData = JSON.parse(JSON.stringify(this.originalProfileData));
        this.showInfo('Reset to original profile data');
    }
    
    getSeverityClass(severity) {
        const classes = {
            'critical': 'error',
            'high': 'warning',
            'medium': 'info',
            'low': 'success'
        };
        return classes[severity] || 'info';
    }
    
    getSeverityBadgeClass(severity) {
        const classes = {
            'critical': 'danger',
            'high': 'warning',
            'medium': 'info',
            'low': 'success'
        };
        return classes[severity] || 'secondary';
    }
    
    getCsrfToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]')?.value || '';
    }
    
    showLoading(message) {
        // Implementation for loading indicator
        console.log('Loading:', message);
    }
    
    hideLoading() {
        // Implementation for hiding loading indicator
        console.log('Loading complete');
    }
    
    showSuccess(message) {
        this.showToast(message, 'success');
    }
    
    showError(message) {
        this.showToast(message, 'error');
    }
    
    showInfo(message) {
        this.showToast(message, 'info');
    }
    
    showToast(message, type) {
        // Simple toast implementation
        console.log(`${type.toUpperCase()}: ${message}`);
    }
    
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Initialize the repair manager when the page loads
document.addEventListener('DOMContentLoaded', function() {
    window.profileRepairManager = new ProfileRepairManager();
});
