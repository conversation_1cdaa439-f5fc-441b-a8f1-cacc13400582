/**
 * Generic Skill Management JavaScript
 * Handles interactive functionality for the GenericSkill admin interface
 */

class GenericSkillManager {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeVisualIndicators();
        this.setupFormValidation();
        this.initializeRelationshipManagement();
    }

    setupEventListeners() {
        // Form submission handling
        document.addEventListener('submit', (e) => {
            if (e.target.classList.contains('skill-form')) {
                this.handleFormSubmission(e);
            }
        });

        // Dynamic form field updates
        document.addEventListener('change', (e) => {
            if (e.target.name === 'base_difficulty') {
                this.updateDifficultyIndicator(e.target);
            }
            if (e.target.name === 'decay_rate') {
                this.updateDecayIndicator(e.target);
            }
            if (e.target.name === 'development_timeframe') {
                this.updateTimeframeBadge(e.target);
            }
        });

        // Relationship management
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('add-relationship')) {
                this.addRelationship(e);
            }
            if (e.target.classList.contains('remove-relationship')) {
                this.removeRelationship(e);
            }
        });

        // Search and filter functionality
        const searchInput = document.getElementById('skill-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.filterSkills(e.target.value);
            });
        }
    }

    initializeVisualIndicators() {
        // Initialize difficulty indicators
        document.querySelectorAll('[data-difficulty]').forEach(element => {
            const difficulty = parseInt(element.dataset.difficulty);
            this.updateDifficultyBar(element, difficulty);
        });

        // Initialize decay indicators
        document.querySelectorAll('[data-decay]').forEach(element => {
            const decay = parseInt(element.dataset.decay);
            this.updateDecayBar(element, decay);
        });

        // Initialize timeframe badges
        document.querySelectorAll('[data-timeframe]').forEach(element => {
            const timeframe = element.dataset.timeframe;
            this.updateTimeframeBadgeClass(element, timeframe);
        });
    }

    updateDifficultyIndicator(input) {
        const value = parseInt(input.value);
        const indicator = input.parentElement.querySelector('.difficulty-indicator');
        if (indicator) {
            this.updateDifficultyBar(indicator, value);
        }
    }

    updateDifficultyBar(container, value) {
        const bar = container.querySelector('.difficulty-bar');
        if (bar) {
            const percentage = Math.min(100, Math.max(0, value));
            bar.style.width = `${percentage}%`;
        }
    }

    updateDecayIndicator(input) {
        const value = parseInt(input.value);
        const indicator = input.parentElement.querySelector('.decay-indicator');
        if (indicator) {
            this.updateDecayBar(indicator, value);
        }
    }

    updateDecayBar(container, value) {
        const bar = container.querySelector('.decay-bar');
        if (bar) {
            const percentage = Math.min(100, Math.max(0, value));
            bar.style.width = `${percentage}%`;
        }
    }

    updateTimeframeBadge(select) {
        const value = select.value;
        const badge = select.parentElement.querySelector('.timeframe-badge');
        if (badge) {
            this.updateTimeframeBadgeClass(badge, value);
        }
    }

    updateTimeframeBadgeClass(element, timeframe) {
        // Remove existing timeframe classes
        element.classList.remove('timeframe-immediate', 'timeframe-short', 'timeframe-medium', 'timeframe-long', 'timeframe-lifetime');
        
        // Add new timeframe class
        if (timeframe) {
            element.classList.add(`timeframe-${timeframe}`);
            element.textContent = timeframe.charAt(0).toUpperCase() + timeframe.slice(1);
        }
    }

    setupFormValidation() {
        const forms = document.querySelectorAll('.skill-form');
        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                if (!this.validateForm(form)) {
                    e.preventDefault();
                    this.showValidationErrors(form);
                }
            });
        });
    }

    validateForm(form) {
        let isValid = true;
        const errors = [];

        // Validate required fields
        const requiredFields = form.querySelectorAll('[required]');
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                isValid = false;
                errors.push(`${field.name} is required`);
                field.classList.add('error');
            } else {
                field.classList.remove('error');
            }
        });

        // Validate code format
        const codeField = form.querySelector('[name="code"]');
        if (codeField && codeField.value) {
            const codePattern = /^[a-z_]+$/;
            if (!codePattern.test(codeField.value)) {
                isValid = false;
                errors.push('Code must contain only lowercase letters and underscores');
                codeField.classList.add('error');
            }
        }

        // Validate numeric ranges
        const difficultyField = form.querySelector('[name="base_difficulty"]');
        if (difficultyField && difficultyField.value) {
            const difficulty = parseInt(difficultyField.value);
            if (difficulty < 1 || difficulty > 100) {
                isValid = false;
                errors.push('Base difficulty must be between 1 and 100');
                difficultyField.classList.add('error');
            }
        }

        const decayField = form.querySelector('[name="decay_rate"]');
        if (decayField && decayField.value) {
            const decay = parseInt(decayField.value);
            if (decay < 0 || decay > 100) {
                isValid = false;
                errors.push('Decay rate must be between 0 and 100');
                decayField.classList.add('error');
            }
        }

        return isValid;
    }

    showValidationErrors(form) {
        // Remove existing error messages
        const existingErrors = form.querySelectorAll('.error-message');
        existingErrors.forEach(error => error.remove());

        // Show new error messages
        const errorFields = form.querySelectorAll('.error');
        errorFields.forEach(field => {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.style.color = '#dc3545';
            errorDiv.style.fontSize = '0.9em';
            errorDiv.style.marginTop = '5px';
            errorDiv.textContent = this.getFieldErrorMessage(field);
            field.parentElement.appendChild(errorDiv);
        });
    }

    getFieldErrorMessage(field) {
        const fieldName = field.name || 'Field';
        if (!field.value.trim()) {
            return `${fieldName} is required`;
        }
        if (field.name === 'code' && !/^[a-z_]+$/.test(field.value)) {
            return 'Code must contain only lowercase letters and underscores';
        }
        if (field.name === 'base_difficulty') {
            return 'Base difficulty must be between 1 and 100';
        }
        if (field.name === 'decay_rate') {
            return 'Decay rate must be between 0 and 100';
        }
        return 'Invalid value';
    }

    initializeRelationshipManagement() {
        // Initialize sortable relationships if library is available
        if (typeof Sortable !== 'undefined') {
            const relationshipLists = document.querySelectorAll('.relationship-list');
            relationshipLists.forEach(list => {
                new Sortable(list, {
                    animation: 150,
                    ghostClass: 'sortable-ghost'
                });
            });
        }
    }

    addRelationship(event) {
        const button = event.target;
        const relationshipType = button.dataset.relationshipType;
        const container = button.closest('.relationship-section');
        
        if (relationshipType === 'domain') {
            this.addDomainRelationship(container);
        } else if (relationshipType === 'trait') {
            this.addTraitRelationship(container);
        } else if (relationshipType === 'prerequisite') {
            this.addPrerequisiteRelationship(container);
        }
    }

    removeRelationship(event) {
        const button = event.target;
        const relationshipItem = button.closest('.relationship-item');
        
        if (confirm('Are you sure you want to remove this relationship?')) {
            relationshipItem.remove();
        }
    }

    filterSkills(searchTerm) {
        const skillCards = document.querySelectorAll('.skill-card');
        const term = searchTerm.toLowerCase();

        skillCards.forEach(card => {
            const name = card.querySelector('.skill-name')?.textContent.toLowerCase() || '';
            const code = card.querySelector('.skill-code')?.textContent.toLowerCase() || '';
            const description = card.querySelector('.skill-description')?.textContent.toLowerCase() || '';

            const matches = name.includes(term) || code.includes(term) || description.includes(term);
            card.style.display = matches ? 'block' : 'none';
        });
    }

    handleFormSubmission(event) {
        const form = event.target;
        const submitButton = form.querySelector('[type="submit"]');
        
        // Show loading state
        if (submitButton) {
            submitButton.disabled = true;
            submitButton.innerHTML = '<span class="spinner"></span> Saving...';
        }

        // Add loading class to form
        form.classList.add('loading');
    }

    // Utility methods
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 4px;
            color: white;
            z-index: 1000;
            animation: slideIn 0.3s ease;
        `;

        if (type === 'success') {
            notification.style.backgroundColor = '#28a745';
        } else if (type === 'error') {
            notification.style.backgroundColor = '#dc3545';
        } else {
            notification.style.backgroundColor = '#007cba';
        }

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 5000);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.GenericSkillManager = new GenericSkillManager();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GenericSkillManager;
}
