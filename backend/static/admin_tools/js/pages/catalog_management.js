/**
 * Catalog Management JavaScript
 * Enhanced functionality for catalog management interface
 */

class CatalogManagement {
    constructor() {
        this.init();
        this.setupEventListeners();
        this.setupWebSocket();
        this.loadCatalogStatus();
        this.activeExecutions = new Map(); // Track active command executions
    }

    init() {
        // Initialize CSRF token for AJAX requests
        this.csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value || 
                        document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        
        // Initialize file upload handlers
        this.initFileUploads();
        
        // Initialize tooltips and help text
        this.initTooltips();
    }

    setupWebSocket() {
        // Connect to admin WebSocket for real-time progress and error reporting
        const protocol = window.location.protocol === 'https:' ? 'wss' : 'ws';
        const wsUrl = `${protocol}://${window.location.host}/ws/benchmark-dashboard/`;

        try {
            this.socket = new WebSocket(wsUrl);

            this.socket.onopen = () => {
                console.log('📡 Connected to admin WebSocket for command progress');
                this.showNotification('Connected to real-time command monitoring', 'success');
            };

            this.socket.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    this.handleWebSocketMessage(data);
                } catch (error) {
                    console.error('Failed to parse WebSocket message:', error);
                }
            };

            this.socket.onclose = () => {
                console.log('📡 Admin WebSocket disconnected');
                this.showNotification('Real-time monitoring disconnected', 'warning');

                // Attempt to reconnect after 5 seconds
                setTimeout(() => {
                    console.log('🔄 Attempting to reconnect WebSocket...');
                    this.setupWebSocket();
                }, 5000);
            };

            this.socket.onerror = (error) => {
                console.error('📡 WebSocket error:', error);
                this.showNotification('Real-time monitoring error', 'error');
            };

        } catch (error) {
            console.error('Failed to setup WebSocket:', error);
        }
    }

    handleWebSocketMessage(data) {
        // Add debugging to see the actual message structure
        console.log('📡 Raw WebSocket message received:', data);

        // Handle different types of WebSocket messages
        switch (data.type) {
            case 'command_progress':
                console.log('📊 Command progress data:', data.data);
                this.handleCommandProgress(data.data);
                break;
            case 'error':
                console.log('❌ Error data:', data.data);
                this.handleCommandError(data.data);
                break;
            case 'debug_info':
                console.log('🐛 Debug info:', data.data);
                if (data.data && data.data.type === 'command_execution_error') {
                    this.handleCommandError(data.data.error_details);
                }
                break;
            default:
                console.log('📡 Unhandled WebSocket message type:', data.type, data);
        }
    }

    handleCommandProgress(progressData) {
        // Safety check for progressData
        if (!progressData) {
            console.error('❌ handleCommandProgress called with undefined progressData');
            return;
        }

        console.log('📊 Processing progress data:', progressData);

        // Safely destructure with defaults
        const {
            execution_id = null,
            stage = 'unknown',
            progress = 0,
            message = 'Processing...',
            details = {}
        } = progressData;

        if (!execution_id) {
            console.error('❌ Progress data missing execution_id:', progressData);
            return;
        }

        // Find the command card for this execution
        const executionInfo = this.activeExecutions.get(execution_id);
        if (!executionInfo) {
            console.log('📊 Progress for unknown execution:', execution_id);
            return;
        }

        const { commandId, button, outputDiv } = executionInfo;

        // Update progress bar
        this.updateProgressBar(commandId, progress, message);

        // Update button text with current stage
        if (button && !button.disabled) {
            button.innerHTML = `
                <span class="loading-spinner">⏳</span>
                <span class="loading-text">${stage}: ${progress}%</span>
            `;
        }

        // Add progress message to output
        if (outputDiv) {
            const progressMsg = document.createElement('div');
            progressMsg.className = 'progress-message';
            progressMsg.innerHTML = `
                <span class="progress-timestamp">${new Date().toLocaleTimeString()}</span>
                <span class="progress-stage">[${stage.toUpperCase()}]</span>
                <span class="progress-text">${message}</span>
            `;
            outputDiv.appendChild(progressMsg);
            outputDiv.scrollTop = outputDiv.scrollHeight;
        }

        console.log(`📊 Command progress: ${execution_id} - ${stage} - ${progress}% - ${message}`);
    }

    handleCommandError(errorData) {
        const { execution_id, error_type, message, details } = errorData;

        // Find the command card for this execution
        const executionInfo = this.activeExecutions.get(execution_id);
        if (executionInfo) {
            const { commandId, button, outputDiv } = executionInfo;

            // Update UI to show error state
            this.setLoadingState(button, false);
            this.updateProgressBar(commandId, 100, 'Error occurred', true);

            // Display detailed error information
            if (outputDiv) {
                outputDiv.className = 'command-output error';
                outputDiv.innerHTML = `
                    <div class="error-header">
                        <span class="error-icon">❌</span>
                        <strong>Command Failed: ${error_type}</strong>
                        <span class="error-timestamp">${new Date().toLocaleTimeString()}</span>
                    </div>
                    <div class="error-message">${message}</div>
                    ${details ? `<div class="error-details">
                        <details>
                            <summary>Error Details</summary>
                            <pre>${JSON.stringify(details, null, 2)}</pre>
                        </details>
                    </div>` : ''}
                `;
            }

            // Clean up execution tracking
            this.activeExecutions.delete(execution_id);
        }

        // Show notification
        this.showNotification(`Command failed: ${message}`, 'error');

        console.error(`❌ Command error: ${execution_id} - ${error_type} - ${message}`, details);
    }

    setupEventListeners() {
        // Command execution buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.execute-btn')) {
                const commandId = e.target.dataset.command;
                this.executeCommand(commandId);
            }
            
            if (e.target.matches('.validate-catalog-btn')) {
                const catalogName = e.target.dataset.catalog;
                this.validateCatalog(catalogName);
            }
            
            if (e.target.matches('.view-catalog-btn')) {
                const catalogName = e.target.dataset.catalog;
                this.viewCatalog(catalogName);
            }
        });

        // Parameter change handlers
        document.addEventListener('change', (e) => {
            if (e.target.matches('.bypass-checkbox')) {
                this.handleBypassChange(e.target);
            }
            
            if (e.target.matches('.external-json-file')) {
                this.handleFileUpload(e.target);
            }
        });

        // Auto-refresh status
        setInterval(() => this.refreshStatus(), 30000);
    }

    initFileUploads() {
        const fileInputs = document.querySelectorAll('.external-json-file');
        fileInputs.forEach(input => {
            const uploadArea = input.closest('.file-upload-area');
            if (uploadArea) {
                this.setupDragAndDrop(uploadArea, input);
            }
        });
    }

    setupDragAndDrop(uploadArea, input) {
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                input.files = files;
                this.handleFileUpload(input);
            }
        });

        uploadArea.addEventListener('click', () => {
            input.click();
        });
    }

    initTooltips() {
        // Add tooltips to help icons and risk badges
        const tooltipElements = document.querySelectorAll('[data-tooltip]');
        tooltipElements.forEach(element => {
            element.addEventListener('mouseenter', (e) => {
                this.showTooltip(e.target, e.target.dataset.tooltip);
            });
            
            element.addEventListener('mouseleave', () => {
                this.hideTooltip();
            });
        });
    }

    async executeCommand(commandId) {
        const button = document.querySelector(`[data-command="${commandId}"]`);
        const outputDiv = document.getElementById(`output_${commandId}`);
        const infoDiv = document.getElementById(`info_${commandId}`);

        if (!button || !outputDiv) return;

        let data = null; // Declare data in function scope

        try {
            // Collect parameters
            const parameters = this.collectParameters(commandId);
            
            // Show loading state
            this.setLoadingState(button, true);
            outputDiv.style.display = 'block';
            outputDiv.textContent = 'Executing command...';
            outputDiv.className = 'command-output';

            // Check if we have file uploads
            const hasFiles = this.hasFileUploads(commandId);
            
            let response;
            if (hasFiles) {
                // Use FormData for file uploads
                const formData = new FormData();
                formData.append('command', commandId);
                
                // Add parameters to FormData
                Object.entries(parameters).forEach(([key, value]) => {
                    if (value instanceof File) {
                        formData.append(key, value);
                    } else {
                        formData.append(key, value);
                    }
                });

                response = await fetch('/admin/commands/execute/', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-CSRFToken': this.csrfToken
                    }
                });
            } else {
                // Use JSON for regular parameters
                response = await fetch('/admin/commands/execute/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': this.csrfToken
                    },
                    body: JSON.stringify({
                        command: commandId,
                        parameters: parameters
                    })
                });
            }

            const data = await response.json();

            // Track execution if we got an execution_id
            if (data.execution_id) {
                this.activeExecutions.set(data.execution_id, {
                    commandId,
                    button,
                    outputDiv,
                    startTime: Date.now()
                });

                // Show initial progress
                this.updateProgressBar(commandId, 0, 'Command started...');
                outputDiv.innerHTML = `
                    <div class="execution-header">
                        <span class="execution-icon">🚀</span>
                        <strong>Execution ID:</strong> ${data.execution_id}
                        <span class="execution-time">${new Date().toLocaleTimeString()}</span>
                    </div>
                    <div class="progress-messages"></div>
                `;
            }

            this.handleCommandResponse(data, outputDiv, infoDiv, button);

        } catch (error) {
            this.handleNetworkError(error, outputDiv);
        } finally {
            // Don't reset loading state immediately if we're tracking execution
            // But if the command failed without WebSocket, reset the button
            if (!data || !data.execution_id || (data && !data.success)) {
                this.setLoadingState(button, false);
            }
        }
    }

    collectParameters(commandId) {
        const parameters = {};
        const paramInputs = document.querySelectorAll(`[data-command-param="${commandId}"]`);
        
        paramInputs.forEach(input => {
            const paramName = input.dataset.paramName;
            
            if (input.type === 'checkbox') {
                parameters[paramName] = input.checked;
            } else if (input.type === 'file') {
                if (input.files && input.files.length > 0) {
                    parameters[paramName] = input.files[0];
                }
            } else {
                const value = input.value.trim();
                if (value) {
                    parameters[paramName] = value;
                }
            }
        });
        
        return parameters;
    }

    hasFileUploads(commandId) {
        const fileInputs = document.querySelectorAll(`[data-command-param="${commandId}"][type="file"]`);
        return Array.from(fileInputs).some(input => input.files && input.files.length > 0);
    }

    updateProgressBar(commandId, progress, message, isError = false) {
        // Find or create progress bar for this command
        const commandCard = document.querySelector(`[data-command="${commandId}"]`).closest('.command-card');
        if (!commandCard) return;

        let progressContainer = commandCard.querySelector('.progress-container');
        if (!progressContainer) {
            progressContainer = document.createElement('div');
            progressContainer.className = 'progress-container';
            progressContainer.innerHTML = `
                <div class="progress-bar-wrapper">
                    <div class="progress-bar" style="width: 0%"></div>
                </div>
                <div class="progress-message"></div>
            `;
            commandCard.appendChild(progressContainer);
        }

        const progressBar = progressContainer.querySelector('.progress-bar');
        const progressMessage = progressContainer.querySelector('.progress-message');

        // Update progress bar
        progressBar.style.width = `${progress}%`;
        progressBar.className = `progress-bar ${isError ? 'error' : ''}`;

        // Update message
        progressMessage.textContent = message;
        progressMessage.className = `progress-message ${isError ? 'error' : ''}`;

        // Hide progress bar when complete (after a delay)
        if (progress >= 100) {
            setTimeout(() => {
                if (progressContainer.parentNode) {
                    progressContainer.style.opacity = '0';
                    setTimeout(() => {
                        if (progressContainer.parentNode) {
                            progressContainer.remove();
                        }
                    }, 1000);
                }
            }, isError ? 5000 : 2000); // Keep error messages longer
        }
    }

    handleNetworkError(error, outputDiv) {
        outputDiv.className = 'command-output error';
        outputDiv.innerHTML = `
            <div class="error-header">
                <span class="error-icon">🌐</span>
                <strong>Network Error</strong>
                <span class="error-timestamp">${new Date().toLocaleTimeString()}</span>
            </div>
            <div class="error-message">Failed to communicate with server: ${error.message}</div>
            <div class="error-details">
                <details>
                    <summary>Technical Details</summary>
                    <pre>Error Type: ${error.constructor.name}
Stack: ${error.stack || 'No stack trace available'}</pre>
                </details>
            </div>
        `;

        this.showNotification(`Network error: ${error.message}`, 'error');
    }

    handleCommandResponse(data, outputDiv, infoDiv, button) {
        // Create enhanced result display
        const resultContainer = document.createElement('div');
        resultContainer.className = 'command-result-container';

        // Status header with icon and timestamp
        const timestamp = new Date().toLocaleString();

        if (data.success) {
            resultContainer.innerHTML = `
                <div class="success-banner">
                    <div class="banner-content">
                        <span class="status-icon">✅</span>
                        <strong>Command Executed Successfully</strong>
                        <span class="timestamp">${timestamp}</span>
                    </div>
                </div>
            `;
            outputDiv.className = 'command-output success';
        } else {
            resultContainer.innerHTML = `
                <div class="error-banner">
                    <div class="banner-content">
                        <span class="status-icon">❌</span>
                        <strong>Command Failed</strong>
                        <span class="timestamp">${timestamp}</span>
                    </div>
                </div>
            `;
            outputDiv.className = 'command-output error';
        }

        // Output section with better formatting
        const outputSection = document.createElement('div');
        outputSection.className = 'output-section';

        if (data.output) {
            const outputContent = document.createElement('pre');
            outputContent.className = 'command-output-content';
            outputContent.textContent = data.output;
            outputSection.appendChild(outputContent);
        }

        // Error details section
        if (data.error) {
            const errorSection = document.createElement('div');
            errorSection.className = 'error-details';
            errorSection.innerHTML = `
                <h4>Error Details:</h4>
                <pre class="error-content">${data.error}</pre>
            `;
            outputSection.appendChild(errorSection);
        }

        // Warnings section
        if (data.error_output) {
            const warningSection = document.createElement('div');
            warningSection.className = 'warning-details';
            warningSection.innerHTML = `
                <h4>Warnings:</h4>
                <pre class="warning-content">${data.error_output}</pre>
            `;
            outputSection.appendChild(warningSection);
        }

        resultContainer.appendChild(outputSection);

        // Execution metrics
        if (data.execution_time !== undefined) {
            const metricsSection = document.createElement('div');
            metricsSection.className = 'execution-metrics';
            metricsSection.innerHTML = `
                <div class="metric">
                    <span class="metric-label">⏱️ Execution Time:</span>
                    <span class="metric-value">${data.execution_time.toFixed(2)}s</span>
                </div>
            `;

            if (data.started_at) {
                metricsSection.innerHTML += `
                    <div class="metric">
                        <span class="metric-label">🚀 Started:</span>
                        <span class="metric-value">${new Date(data.started_at).toLocaleString()}</span>
                    </div>
                `;
            }

            if (data.completed_at) {
                metricsSection.innerHTML += `
                    <div class="metric">
                        <span class="metric-label">✅ Completed:</span>
                        <span class="metric-value">${new Date(data.completed_at).toLocaleString()}</span>
                    </div>
                `;
            }

            resultContainer.appendChild(metricsSection);
        }

        // Replace the old output div content
        outputDiv.innerHTML = '';
        outputDiv.appendChild(resultContainer);
        outputDiv.style.display = 'block';

        // Show success notification
        if (data.success) {
            this.showNotification('✅ Command executed successfully!', 'success');
            setTimeout(() => this.refreshStatus(), 1000);
        } else {
            this.showNotification('❌ Command failed. Check details below.', 'error');
        }
    }

    handleCommandError(error, outputDiv) {
        outputDiv.className = 'command-output error';
        outputDiv.textContent = `Network error: ${error.message}`;
    }

    setLoadingState(button, loading) {
        if (loading) {
            button.classList.add('loading');
            button.disabled = true;
            button.dataset.originalText = button.textContent;
            button.innerHTML = `
                <span class="loading-spinner">⏳</span>
                <span class="loading-text">Executing...</span>
            `;

            // Add progress indicator to the command card
            const commandCard = button.closest('.command-card');
            if (commandCard) {
                commandCard.classList.add('executing');

                // Create progress bar
                let progressBar = commandCard.querySelector('.progress-bar');
                if (!progressBar) {
                    progressBar = document.createElement('div');
                    progressBar.className = 'progress-bar';
                    progressBar.innerHTML = `
                        <div class="progress-fill"></div>
                        <div class="progress-text">Executing command...</div>
                    `;
                    commandCard.appendChild(progressBar);
                }
                progressBar.style.display = 'block';
            }
        } else {
            button.classList.remove('loading');
            button.disabled = false;
            button.innerHTML = `🚀 ${button.dataset.originalText || 'Execute Command'}`;

            // Remove progress indicator
            const commandCard = button.closest('.command-card');
            if (commandCard) {
                commandCard.classList.remove('executing');
                const progressBar = commandCard.querySelector('.progress-bar');
                if (progressBar) {
                    progressBar.style.display = 'none';
                }
            }
        }
    }

    showNotification(message, type = 'info') {
        // Create notification container if it doesn't exist
        let notificationContainer = document.querySelector('.notification-container');
        if (!notificationContainer) {
            notificationContainer = document.createElement('div');
            notificationContainer.className = 'notification-container';
            document.body.appendChild(notificationContainer);
        }

        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <span class="notification-message">${message}</span>
                <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;

        // Add to container
        notificationContainer.appendChild(notification);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);

        // Animate in
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
    }

    async validateCatalog(catalogName) {
        try {
            const response = await fetch(`/admin/commands/validate/${catalogName}/`);
            const data = await response.json();
            
            if (data.success) {
                // Refresh the page to show updated status
                location.reload();
            } else {
                alert(`Validation failed: ${data.error}`);
            }
        } catch (error) {
            alert(`Network error: ${error.message}`);
        }
    }

    async viewCatalog(catalogName) {
        try {
            const response = await fetch(`/admin/commands/catalog/${catalogName}/`);
            const data = await response.json();
            
            if (data.success) {
                this.showCatalogModal(data.catalog);
            } else {
                alert(`Error loading catalog: ${data.error}`);
            }
        } catch (error) {
            alert(`Network error: ${error.message}`);
        }
    }

    async loadCatalogStatus() {
        try {
            const response = await fetch('/admin/commands/catalogs/');
            const data = await response.json();
            
            if (data.success) {
                this.updateCatalogDisplay(data.catalogs);
            }
        } catch (error) {
            console.error('Failed to load catalog status:', error);
        }
    }

    async refreshStatus() {
        try {
            const response = await fetch('/admin/commands/status/');
            const data = await response.json();
            
            if (data.success) {
                this.updateStatusIndicators(data.catalog_status);
            }
        } catch (error) {
            console.log('Status check failed:', error);
        }
    }

    handleBypassChange(checkbox) {
        const commandCard = checkbox.closest('.command-card');
        if (commandCard) {
            if (checkbox.checked) {
                commandCard.classList.add('bypass-enabled');
                this.showWarning('Bypass mode enabled - this will force re-seeding even if already applied');
            } else {
                commandCard.classList.remove('bypass-enabled');
            }
        }
    }

    handleFileUpload(input) {
        const uploadArea = input.closest('.file-upload-area');
        const fileName = input.files[0]?.name;
        
        if (fileName) {
            uploadArea.querySelector('.upload-text').textContent = `Selected: ${fileName}`;
            uploadArea.classList.add('file-selected');
        } else {
            uploadArea.querySelector('.upload-text').textContent = 'Click or drag JSON file here';
            uploadArea.classList.remove('file-selected');
        }
    }

    showCatalogModal(catalog) {
        // Check if CatalogViewer is available, with retry mechanism
        const initializeCatalogViewer = () => {
            if (typeof window.CatalogViewer === 'undefined') {
                console.warn('CatalogViewer class not yet available. Retrying in 100ms...');
                setTimeout(initializeCatalogViewer, 100);
                return;
            }

            // Initialize catalog viewer if not already done
            if (!this.catalogViewer) {
                this.catalogViewer = new window.CatalogViewer();
            }

            // Show the catalog in the viewer
            this.catalogViewer.show(catalog.name);
        };

        initializeCatalogViewer();
    }

    updateCatalogDisplay(catalogs) {
        // Update catalog cards with fresh data
        catalogs.forEach(catalog => {
            const card = document.querySelector(`[data-catalog="${catalog.name}"]`);
            if (card) {
                // Update status, item count, etc.
                this.updateCatalogCard(card, catalog);
            }
        });
    }

    updateStatusIndicators(catalogStatus) {
        Object.entries(catalogStatus).forEach(([catalogName, status]) => {
            const statusElement = document.querySelector(`[data-catalog-status="${catalogName}"]`);
            if (statusElement) {
                statusElement.className = `catalog-status ${status.valid ? 'valid' : 'invalid'}`;
                statusElement.textContent = status.valid ? '✓ Valid' : '✗ Invalid';
            }
        });
    }

    updateCatalogCard(card, catalog) {
        // Update item count
        const itemCount = card.querySelector('.item-count');
        if (itemCount) {
            itemCount.textContent = catalog.item_count || 0;
        }
        
        // Update last modified
        const lastModified = card.querySelector('.last-modified');
        if (lastModified) {
            lastModified.textContent = new Date(catalog.last_modified).toLocaleString();
        }
    }

    showWarning(message) {
        // Simple warning notification
        const warning = document.createElement('div');
        warning.className = 'warning-notification';
        warning.textContent = message;
        warning.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #fff3cd;
            color: #856404;
            padding: 10px 15px;
            border-radius: 4px;
            border: 1px solid #ffeaa7;
            z-index: 1000;
            max-width: 300px;
        `;
        
        document.body.appendChild(warning);
        
        setTimeout(() => {
            warning.remove();
        }, 5000);
    }

    showTooltip(element, text) {
        // Simple tooltip implementation
        const tooltip = document.createElement('div');
        tooltip.className = 'tooltip';
        tooltip.textContent = text;
        tooltip.style.cssText = `
            position: absolute;
            background: #333;
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1001;
            pointer-events: none;
        `;
        
        document.body.appendChild(tooltip);
        
        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + 'px';
        tooltip.style.top = (rect.top - tooltip.offsetHeight - 5) + 'px';
        
        this.currentTooltip = tooltip;
    }

    hideTooltip() {
        if (this.currentTooltip) {
            this.currentTooltip.remove();
            this.currentTooltip = null;
        }
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.catalogManagement = new CatalogManagement();
});

// Export for use in other modules
window.CatalogManagement = CatalogManagement;
