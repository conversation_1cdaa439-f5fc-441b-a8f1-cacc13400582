/**
 * User Profile Management Page
 * Main JavaScript for the user profile management admin page
 */

class UserProfileManagement {
    constructor() {
        this.init();
    }

    init() {
        this.bindEventListeners();
        this.initializeComponents();
        this.loadInitialData();
    }

    bindEventListeners() {
        // Handle view profile buttons
        document.querySelectorAll('.view-profile-btn').forEach(button => {
            button.addEventListener('click', (e) => {
                const profileId = e.target.getAttribute('data-profile-id');
                this.openProfileModal(profileId, 'view');
            });
        });

        // Handle edit profile buttons
        document.querySelectorAll('.edit-profile-btn').forEach(button => {
            button.addEventListener('click', (e) => {
                const profileId = e.target.getAttribute('data-profile-id');
                this.openProfileModal(profileId, 'edit');
            });
        });

        // Schema management buttons
        const validateSchemaBtn = document.querySelector('[onclick="validateSchema()"]');
        const viewSchemaBtn = document.querySelector('[onclick="viewSchema()"]');
        const generateReportBtn = document.querySelector('[onclick="generateMismatchReport()"]');

        if (validateSchemaBtn) {
            validateSchemaBtn.removeAttribute('onclick');
            validateSchemaBtn.addEventListener('click', () => this.validateSchema());
        }

        if (viewSchemaBtn) {
            viewSchemaBtn.removeAttribute('onclick');
            viewSchemaBtn.addEventListener('click', () => this.viewSchema());
        }

        if (generateReportBtn) {
            generateReportBtn.removeAttribute('onclick');
            generateReportBtn.addEventListener('click', () => this.generateMismatchReport());
        }

        // Catalog validation buttons
        const validateCatalogsBtn = document.querySelector('[onclick="validateAllCatalogs()"]');
        const refreshCatalogBtn = document.querySelector('[onclick="refreshCatalogStatus()"]');

        if (validateCatalogsBtn) {
            validateCatalogsBtn.removeAttribute('onclick');
            validateCatalogsBtn.addEventListener('click', () => this.validateAllCatalogs());
        }

        if (refreshCatalogBtn) {
            refreshCatalogBtn.removeAttribute('onclick');
            refreshCatalogBtn.addEventListener('click', () => this.refreshCatalogStatus());
        }
    }

    initializeComponents() {
        // Initialize batch actions if not already done
        if (!window.batchActionsManager && document.querySelector('.profile-checkbox')) {
            window.batchActionsManager = new BatchActionsManager();
        }

        // Initialize import manager if not already done
        if (!window.importManager && (document.getElementById('json-upload-area') || document.getElementById('json-textarea'))) {
            window.importManager = new ImportManager();
        }

        // Initialize import history
        this.initializeImportHistory();

        // Update completeness display
        this.updateCompletenessDisplay();
    }

    loadInitialData() {
        // Load import history
        this.loadImportHistory(1);
    }

    openProfileModal(profileId, mode) {
        // This function should be defined in the modal template
        if (typeof openProfileModal === 'function') {
            openProfileModal(profileId, mode);
        } else {
            console.error('openProfileModal function not found');
            AdminUtils.showAlert('Profile modal functionality not available', 'warning');
        }
    }

    updateCompletenessDisplay() {
        // The completeness is already calculated by the backend and displayed in the template
        // This function is kept for future enhancements when we add dynamic updates
        const completenessElements = document.querySelectorAll('.completeness-fill');
        completenessElements.forEach(element => {
            const width = element.style.width;
            if (width) {
                // Add animation class if needed
                element.classList.add('animated');
            }
        });
    }

    // Schema Management Methods
    async validateSchema() {
        const resultsDiv = document.getElementById('schemaValidationResults');
        if (!resultsDiv) return;

        resultsDiv.style.display = 'block';
        resultsDiv.innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin"></i> Validating schema pipeline...</div>';

        try {
            const response = await fetch('/admin/user-profiles/validate-schema/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': AdminUtils.getCsrfToken()
                }
            });

            const result = await response.json();

            if (result.success) {
                this.displayValidationResults(result.validation_report);
            } else {
                resultsDiv.innerHTML = `<div class="alert alert-danger">Validation failed: ${result.error}</div>`;
            }
        } catch (error) {
            resultsDiv.innerHTML = `<div class="alert alert-danger">Error during validation: ${error.message}</div>`;
        }
    }

    displayValidationResults(report) {
        const resultsDiv = document.getElementById('schemaValidationResults');
        if (!resultsDiv) return;

        let html = `
            <div class="card">
                <div class="card-header">
                    <h5>Schema Validation Report</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6>Total Entities</h6>
                                <span class="badge bg-info fs-6">${report.total_entities}</span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6>Total Mismatches</h6>
                                <span class="badge bg-warning fs-6">${report.total_mismatches}</span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6>Critical Issues</h6>
                                <span class="badge bg-danger fs-6">${report.mismatches_by_severity.critical}</span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6>High Priority</h6>
                                <span class="badge bg-warning fs-6">${report.mismatches_by_severity.high}</span>
                            </div>
                        </div>
                    </div>
                    ${report.field_mismatches && report.field_mismatches.length > 0 ? this.generateMismatchesTable(report.field_mismatches) : ''}
                    ${report.recommendations && report.recommendations.length > 0 ? this.generateRecommendations(report.recommendations) : ''}
                </div>
            </div>
        `;

        resultsDiv.innerHTML = html;
    }

    generateMismatchesTable(mismatches) {
        let html = `
            <h6>Field Mismatches</h6>
            <div class="table-responsive">
                <table class="table table-sm table-striped">
                    <thead>
                        <tr>
                            <th>Entity</th>
                            <th>Field</th>
                            <th>Schema</th>
                            <th>Business Object</th>
                            <th>Model</th>
                            <th>Severity</th>
                            <th>Fix Suggestion</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        mismatches.forEach(mismatch => {
            const severityClass = {
                'critical': 'danger',
                'high': 'warning',
                'medium': 'info',
                'low': 'secondary'
            }[mismatch.severity] || 'secondary';

            html += `
                <tr>
                    <td>${mismatch.entity_name}</td>
                    <td><code>${mismatch.field_name}</code></td>
                    <td>${mismatch.schema_present ? '<i class="fas fa-check text-success"></i>' : '<i class="fas fa-times text-danger"></i>'}</td>
                    <td>${mismatch.business_object_present ? '<i class="fas fa-check text-success"></i>' : '<i class="fas fa-times text-danger"></i>'}</td>
                    <td>${mismatch.model_present ? '<i class="fas fa-check text-success"></i>' : '<i class="fas fa-times text-danger"></i>'}</td>
                    <td><span class="badge bg-${severityClass}">${mismatch.severity}</span></td>
                    <td><small>${mismatch.fix_suggestion || 'No suggestion'}</small></td>
                </tr>
            `;
        });

        html += `
                    </tbody>
                </table>
            </div>
        `;

        return html;
    }

    generateRecommendations(recommendations) {
        let html = `
            <h6 class="mt-4">Recommendations</h6>
            <ul class="list-group">
        `;

        recommendations.forEach(rec => {
            html += `<li class="list-group-item">${rec}</li>`;
        });

        html += `</ul>`;
        return html;
    }

    async viewSchema() {
        try {
            const response = await fetch('/admin/user-profiles/view-schema/', {
                method: 'GET',
                headers: {
                    'X-CSRFToken': AdminUtils.getCsrfToken()
                }
            });

            const result = await response.json();

            if (result.success) {
                this.showSchemaModal(result.schema);
            } else {
                AdminUtils.showAlert('Failed to load schema: ' + result.error, 'danger');
            }
        } catch (error) {
            AdminUtils.showAlert('Error loading schema: ' + error.message, 'danger');
        }
    }

    showSchemaModal(schema) {
        const modalHtml = `
            <div class="modal fade" id="schemaModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">User Profile JSON Schema</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <pre><code class="language-json">${JSON.stringify(schema, null, 2)}</code></pre>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <button type="button" class="btn btn-primary" onclick="downloadSchema()">Download Schema</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById('schemaModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add new modal
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('schemaModal'));
        modal.show();
    }

    async generateMismatchReport() {
        const resultsDiv = document.getElementById('schemaValidationResults');
        if (!resultsDiv) return;

        resultsDiv.style.display = 'block';
        resultsDiv.innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin"></i> Generating comprehensive mismatch report...</div>';

        try {
            const response = await fetch('/admin/user-profiles/mismatch-report/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': AdminUtils.getCsrfToken()
                }
            });

            const result = await response.json();

            if (result.success) {
                this.displayMismatchReport(result.report);
            } else {
                resultsDiv.innerHTML = `<div class="alert alert-danger">Report generation failed: ${result.error}</div>`;
            }
        } catch (error) {
            resultsDiv.innerHTML = `<div class="alert alert-danger">Error generating report: ${error.message}</div>`;
        }
    }

    displayMismatchReport(report) {
        const resultsDiv = document.getElementById('schemaValidationResults');
        if (!resultsDiv) return;

        let html = `
            <div class="card">
                <div class="card-header">
                    <h5>Comprehensive Mismatch Report</h5>
                    <small class="text-muted">Generated: ${new Date().toLocaleString()}</small>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6>Summary</h6>
                        <p>${report.summary}</p>
                    </div>
                    ${report.critical_issues && report.critical_issues.length > 0 ? `
                        <div class="alert alert-danger">
                            <h6>Critical Issues (${report.critical_issues.length})</h6>
                            <ul>
                                ${report.critical_issues.map(issue => `<li>${issue}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}
                    ${report.fixes_applied && report.fixes_applied.length > 0 ? `
                        <div class="alert alert-success">
                            <h6>Fixes Applied (${report.fixes_applied.length})</h6>
                            <ul>
                                ${report.fixes_applied.map(fix => `<li>${fix}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}
                    <div class="mt-3">
                        <button type="button" class="btn btn-primary" onclick="AdminUtils.downloadFile('${JSON.stringify(report)}', 'schema_mismatch_report_${new Date().toISOString().split('T')[0]}.json')">
                            <i class="fas fa-download"></i> Download Report
                        </button>
                    </div>
                </div>
            </div>
        `;

        resultsDiv.innerHTML = html;
    }

    // Catalog Validation Methods
    async validateAllCatalogs() {
        const statusDiv = document.getElementById('catalog-validation-status');
        const resultsDiv = document.getElementById('catalog-validation-results');

        if (!statusDiv) return;

        statusDiv.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div> Validating catalogs...';
        if (resultsDiv) resultsDiv.style.display = 'none';

        try {
            const response = await fetch('/admin/commands/status/');
            const data = await response.json();

            if (data.success) {
                this.displayCatalogValidationResults(data.catalog_status);
            } else {
                statusDiv.innerHTML = '<div class="alert alert-danger">Failed to validate catalogs: ' + data.error + '</div>';
            }
        } catch (error) {
            statusDiv.innerHTML = '<div class="alert alert-danger">Network error: ' + error.message + '</div>';
        }
    }

    refreshCatalogStatus() {
        this.validateAllCatalogs();
    }

    displayCatalogValidationResults(catalogStatus) {
        const statusDiv = document.getElementById('catalog-validation-status');
        const resultsDiv = document.getElementById('catalog-validation-results');

        if (!statusDiv || !resultsDiv) return;

        let validCount = 0;
        let totalCount = 0;
        let resultsHtml = '<div class="row">';

        for (const [catalogName, status] of Object.entries(catalogStatus)) {
            totalCount++;
            if (status.valid) validCount++;

            const statusClass = status.valid ? 'success' : 'danger';
            const statusIcon = status.valid ? '✅' : '❌';

            resultsHtml += `
                <div class="col-md-6 mb-3">
                    <div class="card border-${statusClass}">
                        <div class="card-body">
                            <h5 class="card-title">${statusIcon} ${catalogName}</h5>
                            <p class="card-text">
                                <span class="badge bg-${statusClass}">${status.valid ? 'Valid' : 'Invalid'}</span>
                            </p>
                            ${!status.valid && status.errors ? `
                                <div class="alert alert-warning">
                                    <strong>Errors:</strong>
                                    <ul class="mb-0">
                                        ${status.errors.slice(0, 3).map(error => `<li>${error}</li>`).join('')}
                                        ${status.errors.length > 3 ? `<li>... and ${status.errors.length - 3} more</li>` : ''}
                                    </ul>
                                </div>
                            ` : ''}
                            <small class="text-muted">Last checked: ${new Date(status.last_checked).toLocaleString()}</small>
                        </div>
                    </div>
                </div>
            `;
        }

        resultsHtml += '</div>';

        statusDiv.innerHTML = `
            <div class="alert alert-${validCount === totalCount ? 'success' : 'warning'}">
                <strong>Validation Complete:</strong> ${validCount}/${totalCount} catalogs are valid
            </div>
        `;

        resultsDiv.innerHTML = resultsHtml;
        resultsDiv.style.display = 'block';
    }

    // Import History Methods
    initializeImportHistory() {
        // Set up event listeners for import history
        const refreshBtn = document.getElementById('refresh-history-btn');
        const prevBtn = document.getElementById('history-prev-btn');
        const nextBtn = document.getElementById('history-next-btn');
        const clearBtn = document.getElementById('clear-history-btn');

        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.loadImportHistory(this.currentHistoryPage || 1);
            });
        }

        if (prevBtn) {
            prevBtn.addEventListener('click', () => {
                if (this.currentHistoryPage > 1) {
                    this.loadImportHistory(this.currentHistoryPage - 1);
                }
            });
        }

        if (nextBtn) {
            nextBtn.addEventListener('click', () => {
                this.loadImportHistory((this.currentHistoryPage || 1) + 1);
            });
        }

        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                if (confirm('Are you sure you want to clear old import history records? This will delete records older than 30 days.')) {
                    AdminUtils.showAlert('Clear old records functionality will be implemented in the next update.', 'info');
                }
            });
        }

        // Set up filter change listeners
        ['history-status-filter', 'history-type-filter', 'history-user-filter'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', () => {
                    this.loadImportHistory(1); // Reset to first page when filtering
                });
            }
        });
    }

    loadImportHistory(page = 1) {
        this.currentHistoryPage = page;
        
        // This would load import history data
        // For now, just show a placeholder
        const tbody = document.getElementById('import-history-tbody');
        if (tbody) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="10" class="loading">
                        <div class="loader"></div>
                        Loading import history...
                    </td>
                </tr>
            `;
        }
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.userProfileManagement = new UserProfileManagement();
});

// Export for use in other modules
window.UserProfileManagement = UserProfileManagement;
