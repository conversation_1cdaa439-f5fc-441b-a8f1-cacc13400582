/**
 * Import Manager Module
 * Handles profile import functionality including file upload, validation, and processing
 */

class ImportManager {
    constructor(options = {}) {
        this.options = {
            uploadAreaId: 'json-upload-area',
            fileInputId: 'json-file-input',
            jsonTextareaId: 'json-textarea',
            questionnaireDataId: 'questionnaire-data',
            validationId: 'json-validation',
            previewId: 'profile-preview',
            controlsId: 'import-controls',
            statusId: 'import-status',
            validateEndpoint: '/admin/user-profiles/validate/',
            importEndpoint: '/admin/user-profiles/import/',
            analyzeEndpoint: '/admin/user-profiles/analyze/',
            aiGenerateEndpoint: '/admin/user-profiles/ai-generate/',
            ...options
        };

        this.currentProfileData = null;
        this.init();
    }

    init() {
        this.bindElements();
        this.attachEventListeners();
    }

    bindElements() {
        this.uploadArea = document.getElementById(this.options.uploadAreaId);
        this.fileInput = document.getElementById(this.options.fileInputId);
        this.jsonTextarea = document.getElementById(this.options.jsonTextareaId);
        this.questionnaireData = document.getElementById(this.options.questionnaireDataId);
        this.validation = document.getElementById(this.options.validationId);
        this.preview = document.getElementById(this.options.previewId);
        this.controls = document.getElementById(this.options.controlsId);
        this.status = document.getElementById(this.options.statusId);
    }

    attachEventListeners() {
        // Tab switching
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const targetTab = e.target.getAttribute('data-tab');
                this.switchTab(targetTab);
            });
        });

        // File upload handling
        if (this.uploadArea && this.fileInput) {
            this.uploadArea.addEventListener('click', () => this.fileInput.click());
            this.uploadArea.addEventListener('dragover', (e) => this.handleDragOver(e));
            this.uploadArea.addEventListener('dragleave', (e) => this.handleDragLeave(e));
            this.uploadArea.addEventListener('drop', (e) => this.handleFileDrop(e));
            this.fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
        }

        // JSON textarea validation
        if (this.jsonTextarea) {
            this.jsonTextarea.addEventListener('input', this.debounce(() => {
                this.validateJsonInput();
            }, 500));
        }

        // AI generation
        const generateBtn = document.getElementById('generate-profile-btn');
        if (generateBtn) {
            generateBtn.addEventListener('click', () => this.generateProfileFromQuestionnaire());
        }

        // Import controls
        const validateBtn = document.getElementById('validate-only-btn');
        const importBtn = document.getElementById('import-profile-btn');
        const clearBtn = document.getElementById('clear-import-btn');

        if (validateBtn) validateBtn.addEventListener('click', () => this.performValidationOnly());
        if (importBtn) importBtn.addEventListener('click', () => this.performImport());
        if (clearBtn) clearBtn.addEventListener('click', () => this.clearImportData());
    }

    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.querySelector(`.tab-content[data-tab="${tabName}"]`).classList.add('active');

        // Clear previous data when switching tabs
        this.clearImportData();
    }

    handleDragOver(e) {
        e.preventDefault();
        e.currentTarget.classList.add('dragover');
    }

    handleDragLeave(e) {
        e.preventDefault();
        e.currentTarget.classList.remove('dragover');
    }

    handleFileDrop(e) {
        e.preventDefault();
        e.currentTarget.classList.remove('dragover');

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            this.processFile(files[0]);
        }
    }

    handleFileSelect(e) {
        const file = e.target.files[0];
        if (file) {
            this.processFile(file);
        }
    }

    processFile(file) {
        if (!file.name.endsWith('.json')) {
            this.showImportStatus('error', 'Invalid file type', 'Please upload a .json file.');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const jsonData = JSON.parse(e.target.result);
                this.currentProfileData = jsonData;
                this.displayProfilePreview(jsonData);
                this.showImportControls();
            } catch (error) {
                this.showImportStatus('error', 'Invalid JSON', `Error parsing JSON file: ${error.message}`);
            }
        };
        reader.readAsText(file);
    }

    async validateJsonInput() {
        if (!this.jsonTextarea || !this.validation) return;

        const value = this.jsonTextarea.value.trim();
        if (!value) {
            this.validation.style.display = 'none';
            return;
        }

        try {
            const jsonData = JSON.parse(value);
            this.currentProfileData = jsonData;

            // Show basic JSON validation success
            this.validation.className = 'json-validation valid';
            this.validation.textContent = '✅ Valid JSON format - Validating profile structure...';
            this.validation.style.display = 'block';

            // Perform enhanced validation
            await this.performEnhancedValidation(jsonData);

        } catch (error) {
            this.validation.className = 'json-validation invalid';
            this.validation.textContent = `❌ Invalid JSON: ${error.message}`;
            this.validation.style.display = 'block';

            this.hideProfilePreview();
            this.hideImportControls();
        }
    }

    async performEnhancedValidation(profileData) {
        try {
            const response = await fetch(this.options.validateEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCsrfToken(),
                },
                body: JSON.stringify({
                    profile_data: profileData,
                    options: { validateBeforeImport: true }
                })
            });

            if (response.ok) {
                const result = await response.json();
                this.displayValidationResults(result.validation_result);

                if (result.validation_result.is_valid) {
                    this.displayProfilePreview(profileData);
                    this.showImportControls();
                } else {
                    this.hideProfilePreview();
                    this.hideImportControls();
                }
            } else {
                const error = await response.json();
                this.showValidationError('Validation service error', error.error || 'Unknown error');
            }
        } catch (error) {
            console.error('Enhanced validation error:', error);
            this.showValidationError('Validation error', 'Could not validate profile structure');
        }
    }

    displayValidationResults(validationResult) {
        if (!this.validation) return;

        if (validationResult.is_valid) {
            if (validationResult.warnings && validationResult.warnings.length > 0) {
                this.validation.className = 'json-validation valid';
                this.validation.innerHTML = `
                    <div>✅ Profile structure is valid</div>
                    <div style="margin-top: 8px; font-size: 12px;">
                        ⚠️ ${validationResult.warnings.length} warning(s) - see details below
                    </div>
                `;
            } else {
                this.validation.className = 'json-validation valid';
                this.validation.textContent = '✅ Profile structure is valid and complete';
            }
        } else {
            this.validation.className = 'json-validation invalid';
            this.validation.innerHTML = `
                <div>❌ Profile validation failed</div>
                <div style="margin-top: 8px; font-size: 12px;">
                    ${validationResult.errors.length} error(s) found - see details below
                </div>
            `;
        }

        this.validation.style.display = 'block';
    }

    showValidationError(title, message) {
        if (!this.validation) return;
        
        this.validation.className = 'json-validation invalid';
        this.validation.innerHTML = `<div>❌ ${title}</div><div style="margin-top: 5px; font-size: 12px;">${message}</div>`;
        this.validation.style.display = 'block';
    }

    async generateProfileFromQuestionnaire() {
        if (!this.questionnaireData) return;

        const questionnaireText = this.questionnaireData.value.trim();
        if (!questionnaireText) {
            this.showImportStatus('warning', 'No questionnaire data', 'Please paste questionnaire responses or interview transcript.');
            return;
        }

        const options = {
            includeArchetypeAnalysis: document.getElementById('include-archetype-analysis')?.checked || false,
            includeEnvironmentInference: document.getElementById('include-environment-inference')?.checked || false,
            includeGoalsExtraction: document.getElementById('include-goals-extraction')?.checked || false
        };

        this.showImportStatus('info', 'Generating profile...', 'AI is analyzing the questionnaire responses and creating a comprehensive user profile.');

        try {
            const response = await fetch(this.options.aiGenerateEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCsrfToken(),
                },
                body: JSON.stringify({
                    questionnaire_data: questionnaireText,
                    options: options
                })
            });

            if (response.ok) {
                const result = await response.json();
                this.currentProfileData = result.profile_data;
                this.displayProfilePreview(result.profile_data);
                this.showImportControls();
                this.showImportStatus('success', 'Profile generated successfully', 'Review the generated profile and adjust options if needed.');
            } else {
                const error = await response.json();
                this.showImportStatus('error', 'Generation failed', error.error || 'Failed to generate profile from questionnaire data.');
            }
        } catch (error) {
            console.error('Profile generation error:', error);
            this.showImportStatus('error', 'Generation error', 'An error occurred while generating the profile.');
        }
    }

    displayProfilePreview(profileData) {
        if (!this.preview) return;

        const summary = document.getElementById('preview-summary');
        const details = document.getElementById('preview-details');

        if (summary) {
            const stats = this.calculateProfileStats(profileData);
            summary.innerHTML = Object.entries(stats).map(([key, value]) => `
                <div class="preview-stat">
                    <span class="preview-stat-number">${value}</span>
                    <div class="preview-stat-label">${key}</div>
                </div>
            `).join('');
        }

        if (details) {
            details.innerHTML = `<pre>${JSON.stringify(profileData, null, 2)}</pre>`;
        }

        this.preview.style.display = 'block';
    }

    calculateProfileStats(profileData) {
        const stats = {};

        stats['Demographics'] = profileData.demographics ? 1 : 0;
        stats['Environments'] = profileData.environments ? profileData.environments.length : 0;
        stats['Skills'] = profileData.skills ? profileData.skills.length : 0;
        stats['Resources'] = profileData.resources ? profileData.resources.length : 0;
        stats['Preferences'] = profileData.preferences ? profileData.preferences.length : 0;
        stats['Goals'] = (profileData.aspirations ? profileData.aspirations.length : 0) +
                        (profileData.intentions ? profileData.intentions.length : 0);
        stats['Beliefs'] = profileData.beliefs ? profileData.beliefs.length : 0;
        stats['Traits'] = profileData.traits ? profileData.traits.length : 0;
        stats['Limitations'] = profileData.limitations ? profileData.limitations.length : 0;

        return stats;
    }

    hideProfilePreview() {
        if (this.preview) {
            this.preview.style.display = 'none';
        }
    }

    showImportControls() {
        if (this.controls) {
            this.controls.style.display = 'block';
        }
    }

    hideImportControls() {
        if (this.controls) {
            this.controls.style.display = 'none';
        }
    }

    async performValidationOnly() {
        if (!this.currentProfileData) {
            this.showImportStatus('warning', 'No data to validate', 'Please upload or paste profile data first.');
            return;
        }

        this.showImportStatus('info', 'Analyzing...', 'Performing comprehensive profile analysis with error detection and repair suggestions.');

        try {
            const response = await fetch(this.options.analyzeEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCsrfToken(),
                },
                body: JSON.stringify({
                    profile_data: this.currentProfileData
                })
            });

            const result = await response.json();

            if (result.success) {
                const analysis = result.analysis;

                if (analysis.total_errors === 0) {
                    this.showImportStatus('success', 'Validation passed!', 'Profile is valid and ready for import. No errors found.');
                } else {
                    // Show repair modal if available
                    if (window.profileRepairManager) {
                        window.profileRepairManager.showRepairModal(this.currentProfileData, analysis);
                    } else if (typeof showProfileRepairModal === 'function') {
                        // Use global function if available
                        showProfileRepairModal(this.currentProfileData, analysis);
                    } else {
                        this.showImportStatus('warning', 'Issues found', `Found ${analysis.total_errors} issues. ${analysis.auto_fixable_count} can be automatically repaired.`);
                    }
                }
            } else {
                this.showImportStatus('error', 'Analysis failed', result.error || 'Unknown analysis error');
            }
        } catch (error) {
            console.error('Validation error:', error);
            this.showImportStatus('error', 'Validation error', 'An error occurred while validating the profile.');
        }
    }

    async performImport() {
        if (!this.currentProfileData) {
            this.showImportStatus('warning', 'No data to import', 'Please upload or paste profile data first.');
            return;
        }

        const options = {
            overwriteExisting: document.getElementById('overwrite-existing')?.checked || false,
            validateBeforeImport: document.getElementById('validate-before-import')?.checked || true,
            createBackup: document.getElementById('create-backup')?.checked || true
        };

        // Show user account selection modal
        await this.showUserAccountSelectionModal(this.currentProfileData, options);
    }

    async showUserAccountSelectionModal(profileData, options) {
        // Check if user account creation is needed
        const needsAccountSelection = await this.checkIfAccountSelectionNeeded(profileData);
        if (!needsAccountSelection) {
            // No account selection needed, proceed directly
            await this.performActualImport(profileData, options);
            return;
        }

        // Use the global function from user_profile_management.js
        if (typeof showUserAccountSelectionModal === 'function') {
            showUserAccountSelectionModal(profileData);
        } else {
            console.warn('showUserAccountSelectionModal function not found, proceeding with direct import');
            await this.performActualImport(profileData, options);
        }
    }

    async checkIfAccountSelectionNeeded(profileData) {
        if (!profileData.user_account) return false;

        const username = profileData.user_account.username;
        const email = profileData.user_account.email;

        if (!username && !email) return false;

        // Check if user already exists
        try {
            const response = await fetch(`/admin/user-profiles/check-user/?username=${encodeURIComponent(username)}&email=${encodeURIComponent(email)}`);
            const result = await response.json();
            return result.user_exists;
        } catch (error) {
            console.error('Error checking user existence:', error);
            return false;
        }
    }

    async performActualImport(profileData, options) {
        this.showImportStatus('info', 'Importing profile...', 'Processing user profile data and creating database records.');

        try {
            const response = await fetch(this.options.importEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCsrfToken(),
                },
                body: JSON.stringify({
                    profile_data: profileData,
                    options: options
                })
            });

            if (response.ok) {
                const result = await response.json();
                this.showImportStatus('success', 'Import successful',
                    `Successfully imported user profile: ${result.profile_name}. Created ${result.created_records} new records.`);

                // Refresh the page after a delay
                setTimeout(() => {
                    location.reload();
                }, 2000);
            } else {
                const error = await response.json();
                this.showImportStatus('error', 'Import failed', error.error || 'Failed to import user profile.');
            }
        } catch (error) {
            console.error('Import error:', error);
            this.showImportStatus('error', 'Import error', 'An error occurred while importing the profile.');
        }
    }

    clearImportData() {
        // Clear all inputs
        if (this.jsonTextarea) this.jsonTextarea.value = '';
        if (this.questionnaireData) this.questionnaireData.value = '';
        if (this.fileInput) this.fileInput.value = '';

        // Clear validation
        if (this.validation) this.validation.style.display = 'none';

        // Hide preview and controls
        this.hideProfilePreview();
        this.hideImportControls();

        // Hide status
        if (this.status) this.status.style.display = 'none';

        // Clear current data
        this.currentProfileData = null;
    }

    showImportStatus(type, message, details) {
        if (!this.status) return;

        const messageEl = document.getElementById('status-message');
        const detailsEl = document.getElementById('status-details');

        this.status.className = `import-status ${type}`;
        if (messageEl) messageEl.textContent = message;
        if (detailsEl) detailsEl.textContent = details;
        this.status.style.display = 'block';
    }

    // Utility functions
    getCsrfToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]')?.value ||
               document.querySelector('meta[name=csrf-token]')?.getAttribute('content') ||
               '';
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Initialize import manager when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Check if we're on a page that needs import functionality
    if (document.getElementById('json-upload-area') || document.getElementById('json-textarea')) {
        window.importManager = new ImportManager();
    }
});

// Export for use in other modules
window.ImportManager = ImportManager;
