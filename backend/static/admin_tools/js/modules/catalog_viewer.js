/**
 * Catalog Viewer Module
 * Provides rich visualization for catalog data with search, filtering, and export capabilities
 */

class CatalogViewer {
    constructor(options = {}) {
        this.options = {
            modalId: 'catalog-modal',
            apiEndpoint: '/admin/commands/catalog/',
            ...options
        };
        
        this.currentCatalog = null;
        this.filteredData = null;
        this.searchTerm = '';
        this.activeFilter = 'all';
        
        this.init();
    }

    init() {
        this.createModal();
        this.setupEventListeners();
    }

    createModal() {
        // Remove existing modal if present
        const existingModal = document.getElementById(this.options.modalId);
        if (existingModal) {
            existingModal.remove();
        }

        // Create modal HTML
        const modalHTML = `
            <div id="${this.options.modalId}" class="catalog-modal">
                <div class="catalog-modal-content">
                    <div class="catalog-modal-header">
                        <div>
                            <h2 class="catalog-modal-title">Catalog Viewer</h2>
                            <div class="catalog-modal-subtitle">Loading...</div>
                        </div>
                        <button class="catalog-close-btn" type="button">&times;</button>
                    </div>
                    
                    <div class="catalog-metadata">
                        <div class="metadata-item">
                            <div class="metadata-label">Version</div>
                            <div class="metadata-value" id="catalog-version">-</div>
                        </div>
                        <div class="metadata-item">
                            <div class="metadata-label">Items</div>
                            <div class="metadata-value" id="catalog-item-count">-</div>
                        </div>
                        <div class="metadata-item">
                            <div class="metadata-label">File Size</div>
                            <div class="metadata-value" id="catalog-file-size">-</div>
                        </div>
                        <div class="metadata-item">
                            <div class="metadata-label">Last Modified</div>
                            <div class="metadata-value" id="catalog-last-modified">-</div>
                        </div>
                    </div>
                    
                    <div class="catalog-tabs">
                        <button class="catalog-tab active" data-tab="overview">📊 Overview</button>
                        <button class="catalog-tab" data-tab="data">📋 Data</button>
                        <button class="catalog-tab" data-tab="tree">🌳 Tree View</button>
                        <button class="catalog-tab" data-tab="json">📄 Raw JSON</button>
                    </div>
                    
                    <div class="catalog-modal-body">
                        <div class="catalog-tab-content">
                            <div class="catalog-tab-pane active" id="overview-pane">
                                <div class="catalog-loading">
                                    <div class="catalog-loading-spinner"></div>
                                    Loading catalog data...
                                </div>
                            </div>
                            
                            <div class="catalog-tab-pane" id="data-pane">
                                <div class="catalog-search-bar">
                                    <input type="text" class="catalog-search-input" placeholder="Search catalog items..." id="catalog-search">
                                    <select class="catalog-filter-select" id="catalog-filter">
                                        <option value="all">All Items</option>
                                    </select>
                                    <div class="catalog-search-stats" id="catalog-search-stats">0 items</div>
                                </div>
                                <div class="catalog-data-grid" id="catalog-data-grid">
                                    <!-- Data table will be inserted here -->
                                </div>
                            </div>
                            
                            <div class="catalog-tab-pane" id="tree-pane">
                                <div class="catalog-tree-container" id="catalog-tree-container">
                                    <!-- Tree view will be inserted here -->
                                </div>
                            </div>
                            
                            <div class="catalog-tab-pane" id="json-pane">
                                <div class="catalog-json-viewer" id="catalog-json-viewer">
                                    <!-- Raw JSON will be inserted here -->
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="catalog-export-controls">
                        <button class="catalog-export-btn secondary" id="catalog-export-csv">Export CSV</button>
                        <button class="catalog-export-btn secondary" id="catalog-export-json">Export JSON</button>
                        <button class="catalog-export-btn" id="catalog-close">Close</button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
    }

    setupEventListeners() {
        const modal = document.getElementById(this.options.modalId);
        
        // Close modal events
        modal.querySelector('.catalog-close-btn').addEventListener('click', () => this.hide());
        modal.querySelector('#catalog-close').addEventListener('click', () => this.hide());
        modal.addEventListener('click', (e) => {
            if (e.target === modal) this.hide();
        });

        // Tab switching
        modal.querySelectorAll('.catalog-tab').forEach(tab => {
            tab.addEventListener('click', (e) => this.switchTab(e.target.dataset.tab));
        });

        // Search and filter
        modal.querySelector('#catalog-search').addEventListener('input', (e) => {
            this.searchTerm = e.target.value;
            this.filterData();
        });

        modal.querySelector('#catalog-filter').addEventListener('change', (e) => {
            this.activeFilter = e.target.value;
            this.filterData();
        });

        // Export buttons
        modal.querySelector('#catalog-export-csv').addEventListener('click', () => this.exportData('csv'));
        modal.querySelector('#catalog-export-json').addEventListener('click', () => this.exportData('json'));

        // Keyboard events
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isVisible()) {
                this.hide();
            }
        });
    }

    async show(catalogName) {
        const modal = document.getElementById(this.options.modalId);
        modal.classList.add('active');
        
        try {
            await this.loadCatalog(catalogName);
        } catch (error) {
            this.showError('Failed to load catalog', error.message);
        }
    }

    hide() {
        const modal = document.getElementById(this.options.modalId);
        modal.classList.remove('active');
        this.currentCatalog = null;
        this.filteredData = null;
    }

    isVisible() {
        const modal = document.getElementById(this.options.modalId);
        return modal && modal.classList.contains('active');
    }

    async loadCatalog(catalogName) {
        try {
            const response = await fetch(`${this.options.apiEndpoint}${catalogName}/`);
            const data = await response.json();
            
            if (!data.success) {
                throw new Error(data.error || 'Failed to load catalog');
            }
            
            this.currentCatalog = data.catalog;
            this.updateModalTitle(catalogName);
            this.updateMetadata();
            this.renderOverview();
            this.renderDataTable();
            this.renderTreeView();
            this.renderRawJSON();
            this.setupFilters();
            
        } catch (error) {
            this.showError('Failed to load catalog', error.message);
        }
    }

    updateModalTitle(catalogName) {
        const title = document.querySelector('.catalog-modal-title');
        const subtitle = document.querySelector('.catalog-modal-subtitle');
        
        title.textContent = `${catalogName.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())} Catalog`;
        subtitle.textContent = this.currentCatalog.data.metadata?.description || 'Catalog data visualization';
    }

    updateMetadata() {
        const catalog = this.currentCatalog;
        
        document.getElementById('catalog-version').textContent = 
            catalog.data.metadata?.version || 'Unknown';
        
        document.getElementById('catalog-file-size').textContent = 
            this.formatFileSize(catalog.file_size);
        
        document.getElementById('catalog-last-modified').textContent = 
            new Date(catalog.last_modified).toLocaleString();
        
        // Count total items
        const itemCount = this.countCatalogItems(catalog.data);
        document.getElementById('catalog-item-count').textContent = itemCount;
    }

    countCatalogItems(data) {
        let count = 0;
        for (const [key, value] of Object.entries(data)) {
            if (key === 'metadata') continue;
            
            if (Array.isArray(value)) {
                count += value.length;
            } else if (typeof value === 'object' && value !== null) {
                for (const subValue of Object.values(value)) {
                    if (Array.isArray(subValue)) {
                        count += subValue.length;
                    } else {
                        count += 1;
                    }
                }
            }
        }
        return count;
    }

    renderOverview() {
        const pane = document.getElementById('overview-pane');
        const data = this.currentCatalog.data;
        
        // Create statistics
        const stats = this.generateStatistics(data);
        
        const overviewHTML = `
            <div class="catalog-stats-panel">
                <h3>Catalog Statistics</h3>
                <div class="catalog-stats-grid">
                    ${stats.map(stat => `
                        <div class="catalog-stat-item">
                            <div class="catalog-stat-number">${stat.value}</div>
                            <div class="catalog-stat-label">${stat.label}</div>
                        </div>
                    `).join('')}
                </div>
            </div>
            
            <div class="catalog-categories">
                <h3>Categories</h3>
                ${this.renderCategoryOverview(data)}
            </div>
        `;
        
        pane.innerHTML = overviewHTML;
    }

    generateStatistics(data) {
        const stats = [];
        
        // Total items
        stats.push({
            label: 'Total Items',
            value: this.countCatalogItems(data)
        });
        
        // Categories
        const categories = Object.keys(data).filter(key => key !== 'metadata');
        stats.push({
            label: 'Categories',
            value: categories.length
        });
        
        // Version
        if (data.metadata?.version) {
            stats.push({
                label: 'Version',
                value: data.metadata.version
            });
        }
        
        return stats;
    }

    renderCategoryOverview(data) {
        const categories = Object.entries(data).filter(([key]) => key !== 'metadata');
        
        return categories.map(([category, items]) => {
            const itemCount = Array.isArray(items) ? items.length : 
                             typeof items === 'object' ? Object.keys(items).length : 1;
            
            return `
                <div class="category-item">
                    <strong>${category.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}</strong>
                    <span class="category-count">${itemCount} items</span>
                </div>
            `;
        }).join('');
    }

    renderDataTable() {
        const pane = document.getElementById('catalog-data-grid');
        const data = this.currentCatalog.data;
        
        // Flatten data for table view
        const flatData = this.flattenCatalogData(data);
        this.filteredData = flatData;
        
        const tableHTML = `
            <table class="catalog-table">
                <thead>
                    <tr>
                        <th>Code</th>
                        <th>Name</th>
                        <th>Category</th>
                        <th>Description</th>
                        <th>Type</th>
                    </tr>
                </thead>
                <tbody>
                    ${flatData.map(item => `
                        <tr>
                            <td class="code-cell">${item.code || '-'}</td>
                            <td>${item.name || '-'}</td>
                            <td>${item.category || '-'}</td>
                            <td class="description-cell">${item.description || '-'}</td>
                            <td>${item.type || '-'}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;
        
        pane.innerHTML = tableHTML;
        this.updateSearchStats();
    }

    flattenCatalogData(data) {
        const flattened = [];
        
        for (const [category, items] of Object.entries(data)) {
            if (category === 'metadata') continue;
            
            if (Array.isArray(items)) {
                items.forEach(item => {
                    flattened.push({
                        ...item,
                        category: category,
                        type: 'array_item'
                    });
                });
            } else if (typeof items === 'object' && items !== null) {
                for (const [subCategory, subItems] of Object.entries(items)) {
                    if (Array.isArray(subItems)) {
                        subItems.forEach(item => {
                            flattened.push({
                                ...item,
                                category: `${category}.${subCategory}`,
                                type: 'nested_item'
                            });
                        });
                    } else {
                        flattened.push({
                            code: subCategory,
                            name: subItems.name || subCategory,
                            description: subItems.description || '',
                            category: category,
                            type: 'object_item'
                        });
                    }
                }
            }
        }
        
        return flattened;
    }

    renderTreeView() {
        const pane = document.getElementById('catalog-tree-container');
        const data = this.currentCatalog.data;
        
        const treeHTML = this.buildTreeHTML(data);
        pane.innerHTML = `<ul class="catalog-tree">${treeHTML}</ul>`;
        
        // Add tree interaction
        pane.addEventListener('click', (e) => {
            if (e.target.classList.contains('catalog-tree-toggle')) {
                const children = e.target.closest('.catalog-tree-item').querySelector('.catalog-tree-children');
                if (children) {
                    children.classList.toggle('collapsed');
                    e.target.textContent = children.classList.contains('collapsed') ? '▶' : '▼';
                }
            }
        });
    }

    buildTreeHTML(data, level = 0) {
        let html = '';
        
        for (const [key, value] of Object.entries(data)) {
            if (key === 'metadata') continue;
            
            const hasChildren = typeof value === 'object' && value !== null && !Array.isArray(value);
            const isArray = Array.isArray(value);
            
            html += `
                <li class="catalog-tree-item">
                    <div class="catalog-tree-node">
                        <span class="catalog-tree-toggle">${hasChildren ? '▼' : ''}</span>
                        <span class="catalog-tree-icon">${isArray ? '📋' : hasChildren ? '📁' : '📄'}</span>
                        <span class="catalog-tree-label">${key}</span>
                        <span class="catalog-tree-count">(${isArray ? value.length : hasChildren ? Object.keys(value).length : 1})</span>
                    </div>
                    ${hasChildren ? `<ul class="catalog-tree-children">${this.buildTreeHTML(value, level + 1)}</ul>` : ''}
                </li>
            `;
        }
        
        return html;
    }

    renderRawJSON() {
        const pane = document.getElementById('catalog-json-viewer');
        const data = this.currentCatalog.data;
        
        pane.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
    }

    setupFilters() {
        const filterSelect = document.getElementById('catalog-filter');
        const data = this.currentCatalog.data;
        
        // Get unique categories
        const categories = new Set();
        for (const key of Object.keys(data)) {
            if (key !== 'metadata') {
                categories.add(key);
            }
        }
        
        // Populate filter options
        filterSelect.innerHTML = `
            <option value="all">All Items</option>
            ${Array.from(categories).map(cat => 
                `<option value="${cat}">${cat.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}</option>`
            ).join('')}
        `;
    }

    filterData() {
        if (!this.filteredData) return;
        
        let filtered = this.filteredData;
        
        // Apply search filter
        if (this.searchTerm) {
            const term = this.searchTerm.toLowerCase();
            filtered = filtered.filter(item => 
                (item.code && item.code.toLowerCase().includes(term)) ||
                (item.name && item.name.toLowerCase().includes(term)) ||
                (item.description && item.description.toLowerCase().includes(term))
            );
        }
        
        // Apply category filter
        if (this.activeFilter !== 'all') {
            filtered = filtered.filter(item => 
                item.category && item.category.startsWith(this.activeFilter)
            );
        }
        
        // Re-render table with filtered data
        this.renderFilteredTable(filtered);
        this.updateSearchStats(filtered.length);
    }

    renderFilteredTable(data) {
        const tbody = document.querySelector('.catalog-table tbody');
        
        tbody.innerHTML = data.map(item => `
            <tr>
                <td class="code-cell">${item.code || '-'}</td>
                <td>${item.name || '-'}</td>
                <td>${item.category || '-'}</td>
                <td class="description-cell">${item.description || '-'}</td>
                <td>${item.type || '-'}</td>
            </tr>
        `).join('');
    }

    updateSearchStats(count = null) {
        const statsElement = document.getElementById('catalog-search-stats');
        const total = this.filteredData ? this.filteredData.length : 0;
        const showing = count !== null ? count : total;
        
        statsElement.textContent = `${showing} of ${total} items`;
    }

    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.catalog-tab').forEach(tab => {
            tab.classList.toggle('active', tab.dataset.tab === tabName);
        });
        
        // Update tab panes
        document.querySelectorAll('.catalog-tab-pane').forEach(pane => {
            pane.classList.toggle('active', pane.id === `${tabName}-pane`);
        });
    }

    exportData(format) {
        if (!this.currentCatalog) return;
        
        const data = this.currentCatalog.data;
        const filename = `${this.currentCatalog.name}_catalog.${format}`;
        
        if (format === 'json') {
            this.downloadFile(JSON.stringify(data, null, 2), filename, 'application/json');
        } else if (format === 'csv') {
            const csv = this.convertToCSV(this.filteredData || []);
            this.downloadFile(csv, filename, 'text/csv');
        }
    }

    convertToCSV(data) {
        if (!data.length) return '';
        
        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row => 
                headers.map(header => 
                    JSON.stringify(row[header] || '')
                ).join(',')
            )
        ].join('\n');
        
        return csvContent;
    }

    downloadFile(content, filename, mimeType) {
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    showError(title, message) {
        const pane = document.getElementById('overview-pane');
        pane.innerHTML = `
            <div class="catalog-error">
                <div class="catalog-error-icon">⚠️</div>
                <div class="catalog-error-message">${title}</div>
                <div class="catalog-error-details">${message}</div>
            </div>
        `;
    }
}

// Export for use in other modules
window.CatalogViewer = CatalogViewer;
