/**
 * Batch Actions Component
 * Handles bulk selection and operations on table rows
 */

class BatchActionsManager {
    constructor(options = {}) {
        this.options = {
            selectAllCheckboxId: 'select-all-checkbox',
            itemCheckboxClass: 'profile-checkbox',
            batchActionsCardClass: 'batch-actions-card',
            selectedCountId: 'selected-count',
            selectAllBtnId: 'select-all-btn',
            deselectAllBtnId: 'deselect-all-btn',
            batchDeleteBtnId: 'batch-delete-btn',
            batchExportBtnId: 'batch-export-btn',
            deleteEndpoint: '/admin/user-profiles/batch-delete/',
            exportEndpoint: '/admin/user-profiles/batch-export/',
            ...options
        };

        this.selectedItems = new Set();
        this.init();
    }

    init() {
        this.bindElements();
        this.attachEventListeners();
        this.updateUI();
    }

    bindElements() {
        this.selectAllCheckbox = document.getElementById(this.options.selectAllCheckboxId);
        this.itemCheckboxes = document.querySelectorAll(`.${this.options.itemCheckboxClass}`);
        this.batchActionsCard = document.querySelector(`.${this.options.batchActionsCardClass}`);
        this.selectedCountSpan = document.getElementById(this.options.selectedCountId);
        this.selectAllBtn = document.getElementById(this.options.selectAllBtnId);
        this.deselectAllBtn = document.getElementById(this.options.deselectAllBtnId);
        this.batchDeleteBtn = document.getElementById(this.options.batchDeleteBtnId);
        this.batchExportBtn = document.getElementById(this.options.batchExportBtnId);
    }

    attachEventListeners() {
        // Handle individual checkbox changes
        this.itemCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                this.handleItemCheckboxChange(e.target);
            });
        });

        // Handle select all checkbox
        if (this.selectAllCheckbox) {
            this.selectAllCheckbox.addEventListener('change', (e) => {
                this.handleSelectAllChange(e.target.checked);
            });
        }

        // Handle select all button
        if (this.selectAllBtn) {
            this.selectAllBtn.addEventListener('click', () => {
                this.selectAll();
            });
        }

        // Handle deselect all button
        if (this.deselectAllBtn) {
            this.deselectAllBtn.addEventListener('click', () => {
                this.deselectAll();
            });
        }

        // Handle batch delete
        if (this.batchDeleteBtn) {
            this.batchDeleteBtn.addEventListener('click', () => {
                this.handleBatchDelete();
            });
        }

        // Handle batch export
        if (this.batchExportBtn) {
            this.batchExportBtn.addEventListener('click', () => {
                this.handleBatchExport();
            });
        }
    }

    handleItemCheckboxChange(checkbox) {
        if (checkbox.checked) {
            this.selectedItems.add(checkbox.value);
        } else {
            this.selectedItems.delete(checkbox.value);
        }
        this.updateUI();
    }

    handleSelectAllChange(checked) {
        if (checked) {
            this.selectAll();
        } else {
            this.deselectAll();
        }
    }

    selectAll() {
        this.itemCheckboxes.forEach(checkbox => {
            checkbox.checked = true;
            this.selectedItems.add(checkbox.value);
        });
        this.updateUI();
    }

    deselectAll() {
        this.itemCheckboxes.forEach(checkbox => {
            checkbox.checked = false;
            this.selectedItems.delete(checkbox.value);
        });
        this.updateUI();
    }

    updateUI() {
        const count = this.selectedItems.size;
        const totalItems = this.itemCheckboxes.length;

        // Update selected count display
        if (this.selectedCountSpan) {
            this.selectedCountSpan.textContent = `${count} item${count !== 1 ? 's' : ''} selected`;
        }

        // Show/hide batch actions card
        if (this.batchActionsCard) {
            this.batchActionsCard.style.display = count > 0 ? 'block' : 'none';
        }

        // Enable/disable action buttons
        if (this.batchDeleteBtn) {
            this.batchDeleteBtn.disabled = count === 0;
        }
        if (this.batchExportBtn) {
            this.batchExportBtn.disabled = count === 0;
        }

        // Update select all checkbox state
        if (this.selectAllCheckbox) {
            if (count === 0) {
                this.selectAllCheckbox.indeterminate = false;
                this.selectAllCheckbox.checked = false;
            } else if (count === totalItems) {
                this.selectAllCheckbox.indeterminate = false;
                this.selectAllCheckbox.checked = true;
            } else {
                this.selectAllCheckbox.indeterminate = true;
            }
        }

        // Update row highlighting
        this.itemCheckboxes.forEach(checkbox => {
            const row = checkbox.closest('tr');
            if (row) {
                if (this.selectedItems.has(checkbox.value)) {
                    row.classList.add('selected');
                } else {
                    row.classList.remove('selected');
                }
            }
        });
    }

    async handleBatchDelete() {
        if (this.selectedItems.size === 0) return;

        const count = this.selectedItems.size;
        const confirmMessage = `Are you sure you want to delete ${count} item${count !== 1 ? 's' : ''}? This action cannot be undone.`;

        if (!confirm(confirmMessage)) return;

        try {
            AdminUtils.showLoading(document.body, 'Deleting items...');

            const response = await fetch(this.options.deleteEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': AdminUtils.getCsrfToken(),
                },
                body: JSON.stringify({ 
                    profile_ids: Array.from(this.selectedItems) 
                })
            });

            if (response.ok) {
                const result = await response.json();
                AdminUtils.showAlert(`Successfully deleted ${result.deleted_count} items.`, 'success');
                
                // Refresh the page or remove deleted rows
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                const error = await response.json();
                AdminUtils.showAlert(`Error deleting items: ${error.error}`, 'danger');
            }
        } catch (error) {
            console.error('Batch delete error:', error);
            AdminUtils.showAlert('An error occurred while deleting items.', 'danger');
        } finally {
            AdminUtils.hideLoading(document.body);
        }
    }

    async handleBatchExport() {
        if (this.selectedItems.size === 0) return;

        try {
            AdminUtils.showLoading(document.body, 'Preparing export...');

            const response = await fetch(this.options.exportEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': AdminUtils.getCsrfToken(),
                },
                body: JSON.stringify({ 
                    profile_ids: Array.from(this.selectedItems) 
                })
            });

            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `export_${new Date().toISOString().split('T')[0]}.csv`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
                
                AdminUtils.showAlert('Export completed successfully!', 'success');
            } else {
                const error = await response.json();
                AdminUtils.showAlert(`Error exporting items: ${error.error}`, 'danger');
            }
        } catch (error) {
            console.error('Batch export error:', error);
            AdminUtils.showAlert('An error occurred while exporting items.', 'danger');
        } finally {
            AdminUtils.hideLoading(document.body);
        }
    }

    // Public methods for external use
    getSelectedItems() {
        return Array.from(this.selectedItems);
    }

    getSelectedCount() {
        return this.selectedItems.size;
    }

    clearSelection() {
        this.deselectAll();
    }

    selectItems(itemIds) {
        itemIds.forEach(id => {
            const checkbox = document.querySelector(`input[value="${id}"]`);
            if (checkbox) {
                checkbox.checked = true;
                this.selectedItems.add(id);
            }
        });
        this.updateUI();
    }

    refresh() {
        // Re-bind elements in case DOM has changed
        this.bindElements();
        this.selectedItems.clear();
        this.updateUI();
    }
}

// Initialize batch actions when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Check if we're on a page that needs batch actions
    if (document.querySelector('.profile-checkbox')) {
        window.batchActionsManager = new BatchActionsManager();
    }
});

// Export for use in other modules
window.BatchActionsManager = BatchActionsManager;
