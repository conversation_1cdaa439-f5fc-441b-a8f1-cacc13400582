#!/usr/bin/env python3
"""
Setup LLM Configuration for Goali Backend

This script creates the necessary LLM configuration in the database
to fix the "No LLMConfig provided" errors when the frontend connects.
"""

import os
import sys
import django

# Add the backend directory to the Python path
sys.path.insert(0, '/usr/src/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')
django.setup()

from apps.main.models import LLMConfig

def setup_default_llm_config():
    """Create a default LLM configuration if none exists"""
    
    print("🔧 Setting up LLM Configuration...")
    
    # Check if a default LLM config already exists
    existing_default = LLMConfig.objects.filter(is_default=True, is_evaluation=False).first()
    
    if existing_default:
        print(f"✅ Default LLM config already exists: {existing_default.name}")
        print(f"   Model: {existing_default.model_name}")
        print(f"   Temperature: {existing_default.temperature}")
        return existing_default
    
    # Create a new default LLM configuration
    print("📝 Creating new default LLM configuration...")
    
    # Use environment variables if available, otherwise use sensible defaults
    model_name = os.getenv("DEFAULT_LLM_MODEL_NAME", "mistral-small-latest")
    temperature = float(os.getenv("DEFAULT_LLM_TEMPERATURE", "0.7"))
    input_token_price = float(os.getenv("DEFAULT_LLM_INPUT_TOKEN_PRICE", "0.1"))
    output_token_price = float(os.getenv("DEFAULT_LLM_OUTPUT_TOKEN_PRICE", "0.3"))
    
    # Check if Mistral API key is available
    api_key = os.getenv("MISTRAL_API_KEY")
    if not api_key:
        print("⚠️  Warning: MISTRAL_API_KEY not found in environment variables.")
        print("   The LLM configuration will be created but may not work without an API key.")
    else:
        print(f"✅ Found Mistral API key: {api_key[:8]}...")
    
    llm_config = LLMConfig.objects.create(
        name="Default Mistral Config",
        model_name=model_name,
        temperature=temperature,
        input_token_price=input_token_price,
        output_token_price=output_token_price,
        is_default=True,
        is_evaluation=False
    )
    
    print(f"✅ Created default LLM configuration:")
    print(f"   ID: {llm_config.id}")
    print(f"   Name: {llm_config.name}")
    print(f"   Model: {llm_config.model_name}")
    print(f"   Temperature: {llm_config.temperature}")
    print(f"   Input Token Price: ${llm_config.input_token_price}")
    print(f"   Output Token Price: ${llm_config.output_token_price}")
    
    return llm_config

def setup_evaluation_llm_config():
    """Create a default evaluation LLM configuration if none exists"""
    
    print("\n🔧 Setting up Evaluation LLM Configuration...")
    
    # Check if a default evaluation LLM config already exists
    existing_eval = LLMConfig.objects.filter(is_default=True, is_evaluation=True).first()
    
    if existing_eval:
        print(f"✅ Default evaluation LLM config already exists: {existing_eval.name}")
        return existing_eval
    
    # Create a new default evaluation LLM configuration
    print("📝 Creating new default evaluation LLM configuration...")
    
    eval_config = LLMConfig.objects.create(
        name="Default Evaluation Config",
        model_name="mistral-small-latest",
        temperature=0.7,
        input_token_price=0.1,
        output_token_price=0.3,
        is_default=True,
        is_evaluation=True
    )
    
    print(f"✅ Created default evaluation LLM configuration:")
    print(f"   ID: {eval_config.id}")
    print(f"   Name: {eval_config.name}")
    print(f"   Model: {eval_config.model_name}")
    
    return eval_config

def verify_setup():
    """Verify that the LLM configuration is working"""
    
    print("\n🧪 Verifying LLM Configuration Setup...")
    
    try:
        # Test getting default config
        default_config = LLMConfig.get_default(evaluation=False)
        if default_config:
            print(f"✅ Default LLM config accessible: {default_config.name}")
        else:
            print("❌ Could not retrieve default LLM config")
            return False
        
        # Test getting evaluation config
        eval_config = LLMConfig.get_default(evaluation=True)
        if eval_config:
            print(f"✅ Evaluation LLM config accessible: {eval_config.name}")
        else:
            print("❌ Could not retrieve evaluation LLM config")
            return False
        
        # Test creating LLM client (without actually making API calls)
        try:
            from apps.main.llm.service import RealLLMClient
            client = RealLLMClient(llm_config=default_config)
            print("✅ LLM client can be instantiated successfully")
        except Exception as e:
            print(f"⚠️  LLM client instantiation warning: {e}")
            # This might fail due to API key issues, but config is still valid
        
        return True
        
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        return False

def main():
    """Main setup function"""
    
    print("🎯 Goali Backend LLM Configuration Setup")
    print("=" * 50)
    
    try:
        # Setup default LLM config
        default_config = setup_default_llm_config()
        
        # Setup evaluation LLM config
        eval_config = setup_evaluation_llm_config()
        
        # Verify setup
        if verify_setup():
            print("\n🎉 LLM Configuration setup completed successfully!")
            print("\nThe backend should now be able to handle frontend requests without")
            print("'No LLMConfig provided' errors.")
            
            print("\n📋 Summary:")
            print(f"   • Default LLM Config: {default_config.name} (ID: {default_config.id})")
            print(f"   • Evaluation LLM Config: {eval_config.name} (ID: {eval_config.id})")
            
            if not os.getenv("MISTRAL_API_KEY"):
                print("\n⚠️  Note: Set MISTRAL_API_KEY environment variable for full functionality")
            
        else:
            print("\n❌ Setup completed but verification failed. Check the logs above.")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n❌ Setup failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
