{"metadata": {"version": "1.0.0", "generated_from": "seed_db_30_domains.py", "last_updated": "2025-06-29", "description": "Authoritative catalog of activity domains extracted from seeding files", "source_file": "seed_db_30_domains.py"}, "domains": {"PHYSICAL": [{"code": "physical", "name": "Physical", "description": "a broad domain encompassing all physical activities that engage the body and promote health and fitness.", "primary_category": "PHYSICAL"}, {"code": "phys_cardio", "name": "Cardiovascular Exercise", "description": "Activities that increase heart rate and improve cardiovascular health, such as running, cycling, or swimming.", "primary_category": "PHYSICAL"}, {"code": "phys_strength", "name": "Strength Training", "description": "Activities focused on building muscle strength through resistance, such as weightlifting or bodyweight exercises.", "primary_category": "PHYSICAL"}, {"code": "phys_chill", "name": "Physical but chill", "description": "Activities that are physical, but somehow chill (like walking).", "primary_category": "PHYSICAL"}, {"code": "phys_flexibility", "name": "Flexibility & Mobility", "description": "Activities that improve range of motion, joint health, and muscle elasticity, such as yoga, stretching, or pilates.", "primary_category": "PHYSICAL"}, {"code": "phys_sports", "name": "Recreational Sports", "description": "Structured physical activities with rules and competitive elements, including team sports or individual athletic pursuits.", "primary_category": "PHYSICAL"}, {"code": "phys_outdoor", "name": "Outdoor Activities", "description": "Physical activities conducted in natural settings, such as hiking, climbing, or kayaking.", "primary_category": "PHYSICAL"}, {"code": "phys_dance", "name": "Dance & Movement", "description": "Expressive physical activities involving rhythmic body movement, including various dance styles and movement practices.", "primary_category": "PHYSICAL"}, {"code": "phys_martial", "name": "Martial Arts", "description": "Structured systems of combat and self-defense training that often incorporate physical conditioning and philosophical elements.", "primary_category": "PHYSICAL"}, {"code": "phys_balance", "name": "Balance & Coordination", "description": "Activities that develop proprioception, stability, and motor control.", "primary_category": "PHYSICAL"}], "SOCIAL": [{"code": "social", "name": "Social", "description": "a broad domain encompassing all social activities that engage users with others.", "primary_category": "SOCIAL"}, {"code": "soc_connecting", "name": "Social Connection", "description": "Activities focused on forming or strengthening interpersonal bonds through conversation, shared experiences, or mutual support.", "primary_category": "SOCIAL"}, {"code": "soc_group", "name": "Group Dynamics", "description": "Activities involving interaction within larger social structures, such as team building, community engagement, or navigating organizational systems.", "primary_category": "SOCIAL"}, {"code": "soc_comm", "name": "Communication Skills", "description": "Activities developing verbal and non-verbal communication, including public speaking, active listening, or conflict resolution.", "primary_category": "SOCIAL"}, {"code": "soc_empathy", "name": "Empathy Building", "description": "Activities designed to enhance understanding of others' perspectives, emotions, and experiences.", "primary_category": "SOCIAL"}, {"code": "soc_network", "name": "Networking", "description": "Structured activities to expand professional or personal circles, create new connections, and develop a support system.", "primary_category": "SOCIAL"}, {"code": "soc_romance", "name": "Romantic Relationship", "description": "Activities focused on developing or strengthening intimate partner relationships.", "primary_category": "SOCIAL"}, {"code": "soc_family", "name": "Family Bonding", "description": "Activities that enhance connections between family members and foster a supportive family environment.", "primary_category": "SOCIAL"}, {"code": "soc_leadership", "name": "Leadership", "description": "Activities that develop skills in guiding, influencing, and motivating others toward shared goals.", "primary_category": "SOCIAL"}, {"code": "soc_conflict", "name": "Conflict Resolution", "description": "Activities that develop skills in solving conflicts.", "primary_category": "SOCIAL"}], "CREATIVE": [{"code": "creative", "name": "Creative", "description": "a broad domain encompassing all creative activities.", "primary_category": "CREATIVE"}, {"code": "creative_visual", "name": "Visual Arts", "description": "Creative expression through visual mediums including painting, drawing, sculpture, or digital art.", "primary_category": "CREATIVE"}, {"code": "creative_observation", "name": "Creative observation", "description": "Creative thoughts from observing.", "primary_category": "CREATIVE"}, {"code": "creative_auditory", "name": "Creative auditory", "description": "Creative thoughts from listening.", "primary_category": "CREATIVE"}, {"code": "creative_music", "name": "Music Creation", "description": "Activities involving creating or performing music, such as playing instruments, singing, composing, or producing.", "primary_category": "CREATIVE"}, {"code": "creative_writing", "name": "Creative Writing", "description": "Expression through written language, including fiction, poetry, journaling, or scriptwriting.", "primary_category": "CREATIVE"}, {"code": "creative_design", "name": "Design Thinking", "description": "Problem-solving through creative design processes, including graphic design, product design, or architectural thinking.", "primary_category": "CREATIVE"}, {"code": "creative_culinary", "name": "Culinary Arts", "description": "Creative expression through food preparation, recipe development, or gastronomic experimentation.", "primary_category": "CREATIVE"}, {"code": "creative_perform", "name": "Performance Arts", "description": "Creative activities involving live presentation, such as theater, comedy, or storytelling.", "primary_category": "CREATIVE"}, {"code": "creative_craft", "name": "Crafts & Making", "description": "Hands-on creation of physical objects, including textile arts, woodworking, pottery, or DIY projects.", "primary_category": "CREATIVE"}, {"code": "creative_improv", "name": "Improvisation", "description": "Spontaneous creative expression and adaptation without predetermined structure.", "primary_category": "CREATIVE"}], "INTELLECTUAL": [{"code": "intellectual", "name": "Intellectual", "description": "a broad domain encompassing all intellectual activities.", "primary_category": "INTELLECTUAL"}, {"code": "intel_learn", "name": "Learning & Study", "description": "Structured acquisition of knowledge or skills in specific domains through courses, research, or self-directed study.", "primary_category": "INTELLECTUAL"}, {"code": "intel_problem", "name": "Problem Solving", "description": "Activities that develop analytical thinking and systematic approaches to overcoming challenges.", "primary_category": "INTELLECTUAL"}, {"code": "intel_audio", "name": "Intellectual audio", "description": "Activities that requires focused listening.", "primary_category": "INTELLECTUAL"}, {"code": "intel_strategic", "name": "Strategic Thinking", "description": "Long-term planning and development of approaches to achieve goals while considering multiple variables and possible outcomes.", "primary_category": "INTELLECTUAL"}, {"code": "intel_curiosity", "name": "Intellectual Curiosity", "description": "Exploration of ideas driven by interest and wonder, such as reading broadly or engaging with new concepts.", "primary_category": "INTELLECTUAL"}, {"code": "intel_language", "name": "Language & Linguistics", "description": "Activities focused on language acquisition, linguistic analysis, or cross-cultural communication.", "primary_category": "INTELLECTUAL"}, {"code": "intel_debate", "name": "Critical Discourse", "description": "Engagement in reasoned argument, debate, or discussion that evaluates ideas from multiple perspectives.", "primary_category": "INTELLECTUAL"}, {"code": "intel_science", "name": "Scientific Inquiry", "description": "Activities involving observation, hypothesis formation, experimentation, and evidence-based reasoning.", "primary_category": "INTELLECTUAL"}, {"code": "intel_tech", "name": "Technology & Digital Skills", "description": "Learning and applying digital tools, programming, or computational thinking.", "primary_category": "INTELLECTUAL"}], "REFLECTIVE": [{"code": "reflective", "name": "Reflective", "description": "a broad domain encompassing all reflective activities.", "primary_category": "REFLECTIVE"}, {"code": "refl_meditate", "name": "Meditation", "description": "Practices that develop focused attention, awareness, and mental clarity through various meditation techniques.", "primary_category": "REFLECTIVE"}, {"code": "refl_journal", "name": "Journaling & Self-Reflection", "description": "Written or structured contemplation of personal experiences, emotions, and thoughts to gain insight.", "primary_category": "REFLECTIVE"}, {"code": "refl_mindful", "name": "Mindfulness Practice", "description": "Activities cultivating present-moment awareness and non-judgmental observation of internal and external experiences.", "primary_category": "REFLECTIVE"}, {"code": "refl_values", "name": "Values Clarification", "description": "Exploration and identification of personal principles, ethics, and priorities that guide decision-making.", "primary_category": "REFLECTIVE"}, {"code": "refl_persp", "name": "Perspective Taking", "description": "Activities that encourage viewing situations from different angles or through others' experiences.", "primary_category": "REFLECTIVE"}, {"code": "refl_philos", "name": "Philosophical Contemplation", "description": "Engagement with fundamental questions about existence, meaning, ethics, and the nature of reality.", "primary_category": "REFLECTIVE"}, {"code": "refl_grat", "name": "Gratitude Practice", "description": "Structured recognition and appreciation of positive aspects of life and experiences.", "primary_category": "REFLECTIVE"}, {"code": "refl_comfort", "name": "Comfort Zone Reflection", "description": "Examination of personal boundaries, limitations, and opportunities for growth through controlled discomfort.", "primary_category": "REFLECTIVE"}, {"code": "refl_micro", "name": "Micro-Wellness Activities", "description": "Brief wellness activities (2-15 minutes) designed for busy schedules and transition moments, such as quick breathing exercises or brief mindfulness practices.", "primary_category": "REFLECTIVE"}], "EMOTIONAL": [{"code": "emotional", "name": "Emotional", "description": "a broad domain encompassing all activities that engege the users with their emotions.", "primary_category": "EMOTIONAL"}, {"code": "emot_aware", "name": "Emotional Awareness", "description": "Activities that develop recognition and understanding of emotional states in oneself and others.", "primary_category": "EMOTIONAL"}, {"code": "emot_regulate", "name": "Emotion Regulation", "description": "Practices for managing emotional responses, processing difficult feelings, and developing emotional resilience.", "primary_category": "EMOTIONAL"}, {"code": "emot_express", "name": "Emotional Expression", "description": "Healthy outlets for communicating and releasing emotions through verbal, physical, or creative means.", "primary_category": "EMOTIONAL"}, {"code": "emot_compass", "name": "Self-Compassion", "description": "Activities that nurture kindness and understanding toward oneself, especially during challenges or failures.", "primary_category": "EMOTIONAL"}, {"code": "emot_joy", "name": "Joy & Pleasure", "description": "Intentional engagement in activities that bring happiness, satisfaction, or delight.", "primary_category": "EMOTIONAL"}, {"code": "emot_stress", "name": "Stress Management", "description": "Techniques and practices that reduce or help cope with stress and anxiety.", "primary_category": "EMOTIONAL"}, {"code": "emot_forgive", "name": "Forgiveness & Letting Go", "description": "Processes for releasing resentment, anger, or attachment to past hurts or disappointments.", "primary_category": "EMOTIONAL"}, {"code": "emot_comfort", "name": "Comfort & Nurturing", "description": "Activities that provide emotional security, soothing, and care during difficult times.", "primary_category": "EMOTIONAL"}], "SPIRITUAL_EXISTENTIAL": [{"code": "spiritual_existential", "name": "Spiritual & Existential", "description": "a broad domain encompassing all spiritual activities.", "primary_category": "SPIRITUAL_EXISTENTIAL"}, {"code": "spirit_purpose", "name": "Purpose & Meaning", "description": "Exploration of personal significance, life direction, and sources of meaning beyond immediate goals.", "primary_category": "SPIRITUAL_EXISTENTIAL"}, {"code": "spirit_connect", "name": "Spiritual Connection", "description": "Activities that foster connection with transcendent elements, whether through religious practices or secular spirituality.", "primary_category": "SPIRITUAL_EXISTENTIAL"}, {"code": "spirit_ritual", "name": "Ritual & Practice", "description": "Structured ceremonies or habits that connect to cultural, spiritual, or personal significance beyond their practical function.", "primary_category": "SPIRITUAL_EXISTENTIAL"}, {"code": "spirit_nature", "name": "Nature Connection", "description": "Experiences that develop awareness of and relationship with the natural world and ecological systems.", "primary_category": "SPIRITUAL_EXISTENTIAL"}, {"code": "spirit_transced", "name": "Transcendent Experience", "description": "Activities oriented toward peak experiences, states of flow, or moments that transcend ordinary consciousness.", "primary_category": "SPIRITUAL_EXISTENTIAL"}, {"code": "spirit_death", "name": "Mortality Contemplation", "description": "Reflective consideration of life's impermanence and one's relationship with the concept of death.", "primary_category": "SPIRITUAL_EXISTENTIAL"}, {"code": "spirit_commun", "name": "Spiritual Community", "description": "Engagement with others who share similar existential or spiritual orientations, practices, or beliefs.", "primary_category": "SPIRITUAL_EXISTENTIAL"}, {"code": "spirit_wisdom", "name": "Wisdom Traditions", "description": "Exploration of or participation in established philosophical, spiritual, or cultural wisdom traditions.", "primary_category": "SPIRITUAL_EXISTENTIAL"}], "EXPLORATORY_ADVENTUROUS": [{"code": "exploratory_adventurous", "name": "Exploratory & Adventurous", "description": "a broad domain encompassing all exploratory and adventurous activities.", "primary_category": "EXPLORATORY_ADVENTUROUS"}, {"code": "explor_travel", "name": "Travel & Discovery", "description": "Activities involving geographical exploration, cultural immersion, or visiting new places.", "primary_category": "EXPLORATORY_ADVENTUROUS"}, {"code": "explor_risk", "name": "Controlled Risk-Taking", "description": "Activities that involve stepping beyond comfort zones with calculated challenges and reasonable safety measures.", "primary_category": "EXPLORATORY_ADVENTUROUS"}, {"code": "explor_sensory", "name": "Sensory Exploration", "description": "Experiences that engage or expand awareness through the five senses in novel or intensive ways.", "primary_category": "EXPLORATORY_ADVENTUROUS"}, {"code": "explor_cultural", "name": "Cultural Exploration", "description": "Engagement with unfamiliar traditions, perspectives, or creative expressions from different cultures.", "primary_category": "EXPLORATORY_ADVENTUROUS"}, {"code": "explor_novel", "name": "Novelty Seeking", "description": "Pursuit of new experiences, skills, or environments purely for the sake of freshness and variety.", "primary_category": "EXPLORATORY_ADVENTUROUS"}, {"code": "explor_adren", "name": "Adrenaline Activities", "description": "High-energy experiences that produce excitement, thrill, or exhilaration through physical or psychological intensity.", "primary_category": "EXPLORATORY_ADVENTUROUS"}, {"code": "explor_improv", "name": "Spontaneity & Improvisation", "description": "Activities with minimal planning that embrace unexpected outcomes and in-the-moment decision making.", "primary_category": "EXPLORATORY_ADVENTUROUS"}, {"code": "explor_unknown", "name": "Embracing Uncertainty", "description": "Deliberate engagement with the unknown or unpredictable aspects of life or specific domains.", "primary_category": "EXPLORATORY_ADVENTUROUS"}, {"code": "explor_digital", "name": "Digital-Physical Hybrid", "description": "Activities that blend digital tools with physical actions, such as app-guided workouts, virtual reality experiences, or augmented reality exploration.", "primary_category": "EXPLORATORY_ADVENTUROUS"}], "PRODUCTIVE_PRACTICAL": [{"code": "productive_practical", "name": "Productive & Practical", "description": "a broad domain encompassing all productive and practical activities.", "primary_category": "PRODUCTIVE_PRACTICAL"}, {"code": "prod_organize", "name": "Organization & Planning", "description": "Systems and activities for creating order, structure, and efficiency in physical spaces or processes.", "primary_category": "PRODUCTIVE_PRACTICAL"}, {"code": "prod_habit", "name": "Habit Formation", "description": "Development of consistent beneficial behaviors through intentional practice and routine building.", "primary_category": "PRODUCTIVE_PRACTICAL"}, {"code": "prod_time", "name": "Time Management", "description": "Approaches to allocating time effectively based on priorities, energy levels, and productivity principles.", "primary_category": "PRODUCTIVE_PRACTICAL"}, {"code": "prod_skill", "name": "Practical Skill Development", "description": "Learning and application of concrete abilities useful in everyday life or specific contexts.", "primary_category": "PRODUCTIVE_PRACTICAL"}, {"code": "prod_financial", "name": "Financial Management", "description": "Activities related to budgeting, saving, investing, or otherwise managing personal economic resources.", "primary_category": "PRODUCTIVE_PRACTICAL"}, {"code": "prod_career", "name": "Career Development", "description": "Advancement of professional skills, networks, or positioning within work contexts.", "primary_category": "PRODUCTIVE_PRACTICAL"}, {"code": "prod_health", "name": "Health & Wellness Systems", "description": "Practical approaches to maintaining or improving physical and mental wellbeing through consistent practices.", "primary_category": "PRODUCTIVE_PRACTICAL"}, {"code": "prod_home", "name": "Home Management", "description": "Activities related to maintaining, improving, or organizing living spaces for functionality and comfort.", "primary_category": "PRODUCTIVE_PRACTICAL"}, {"code": "prod_transition", "name": "Transition Moments", "description": "Activities designed for in-between times such as waiting, commuting, or brief breaks between tasks that maximize productivity during idle moments.", "primary_category": "PRODUCTIVE_PRACTICAL"}], "LEISURE_RECREATIONAL": [{"code": "leisure_recreational", "name": "Leisure & Recreational", "description": "a broad domain encompassing all recreational and leisure activities.", "primary_category": "LEISURE_RECREATIONAL"}, {"code": "leisure_relax", "name": "Relaxation", "description": "Activities specifically oriented toward rest, recovery, and the release of tension or effort.", "primary_category": "LEISURE_RECREATIONAL"}, {"code": "leisure_play", "name": "Play & Games", "description": "Engagement in activities pursued primarily for enjoyment rather than external outcomes, including structured games or free play.", "primary_category": "LEISURE_RECREATIONAL"}, {"code": "leisure_entertain", "name": "Entertainment Consumption", "description": "Enjoyment of media, performances, or experiences created by others, such as films, music, or shows.", "primary_category": "LEISURE_RECREATIONAL"}, {"code": "leisure_collect", "name": "Collection & Curation", "description": "Activities involving gathering, organizing, and appreciating objects or information of personal interest.", "primary_category": "LEISURE_RECREATIONAL"}, {"code": "leisure_nature", "name": "Recreational Nature Activities", "description": "Enjoyable outdoor pursuits that connect with natural environments in a leisurely context.", "primary_category": "LEISURE_RECREATIONAL"}, {"code": "leisure_social", "name": "Social Recreation", "description": "Leisure activities specifically focused on enjoying time with others in low-pressure social contexts.", "primary_category": "LEISURE_RECREATIONAL"}, {"code": "leisure_hobby", "name": "<PERSON><PERSON> Participation", "description": "Regular engagement in non-professional interests that provide satisfaction and enjoyment.", "primary_category": "LEISURE_RECREATIONAL"}, {"code": "leisure_festive", "name": "Celebration & Festivity", "description": "Participation in events or traditions that mark special occasions or cultural significance in enjoyable ways.", "primary_category": "LEISURE_RECREATIONAL"}], "GENERAL": [{"code": "general", "name": "General", "description": "A fallback domain for activities that do not fit into specific categories or when domain classification is uncertain.", "primary_category": "LEISURE_RECREATIONAL"}]}}