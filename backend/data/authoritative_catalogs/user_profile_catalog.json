{"metadata": {"version": "1.0.0", "generated_from": "seeding_commands", "last_updated": "2025-06-29", "description": "Authoritative catalog of user profile components extracted from seeding files", "source_files": ["seed_db_10_hexacos.py", "seed_db_20_limitations.py", "seed_db_50_skill_system.py", "seed_db_60_beliefs.py"]}, "hexaco_traits": {"HONESTYHUMILITY": [{"code": "honesty_sincerity", "name": "Sincerity", "description": "Genuineness in self-expression and relationships without manipulation.", "trait_type": "HONESTYHUMILITY"}, {"code": "honesty_fairness", "name": "Fairness", "description": "Tendency to avoid exploiting others for personal gain.", "trait_type": "HONESTYHUMILITY"}, {"code": "honesty_greed_avoidance", "name": "Greed Avoidance", "description": "Level of disinterest in luxury, wealth, and social status.", "trait_type": "HONESTYHUMILITY"}, {"code": "honesty_modesty", "name": "Modesty", "description": "Tendency to be humble and unassuming about achievements.", "trait_type": "HONESTYHUMILITY"}], "EMOTIONALITY": [{"code": "emotion_fearfulness", "name": "Fearfulness", "description": "Tendency to experience fear in response to potential dangers.", "trait_type": "EMOTIONALITY"}, {"code": "emotion_anxiety", "name": "Anxiety", "description": "Tendency to worry in a variety of contexts.", "trait_type": "EMOTIONALITY"}, {"code": "emotion_dependence", "name": "Dependence", "description": "Need for emotional support and reassurance from others.", "trait_type": "EMOTIONALITY"}, {"code": "emotion_sentimentality", "name": "Sentimentality", "description": "Tendency to form strong emotional bonds and empathic responses.", "trait_type": "EMOTIONALITY"}], "EXTRAVERSION": [{"code": "extra_self_esteem", "name": "Social Self-Esteem", "description": "Confidence and positive self-evaluation in social contexts.", "trait_type": "EXTRAVERSION"}, {"code": "extra_social_boldness", "name": "Social Boldness", "description": "<PERSON><PERSON><PERSON> in a variety of social situations and leadership roles.", "trait_type": "EXTRAVERSION"}, {"code": "extra_sociability", "name": "Sociability", "description": "Enjoyment of social gatherings and interactions with others.", "trait_type": "EXTRAVERSION"}, {"code": "extra_liveliness", "name": "Liveliness", "description": "Energy level and enthusiasm in social and activity contexts.", "trait_type": "EXTRAVERSION"}], "AGREEABLENESS": [{"code": "agree_forgiveness", "name": "Forgiveness", "description": "Willingness to trust and forgive those who have caused harm.", "trait_type": "AGREEABLENESS"}, {"code": "agree_gentleness", "name": "Gentleness", "description": "Tendency to be mild and lenient in interactions with others.", "trait_type": "AGREEABLENESS"}, {"code": "agree_flexibility", "name": "Flexibility", "description": "Willingness to compromise and cooperate with others.", "trait_type": "AGREEABLENESS"}, {"code": "agree_patience", "name": "Patience", "description": "Tendency to remain calm rather than becoming angry.", "trait_type": "AGREEABLENESS"}], "CONSCIENTIOUSNESS": [{"code": "consc_organization", "name": "Organization", "description": "Tendency to seek order and structure in the physical environment.", "trait_type": "CONSCIENTIOUSNESS"}, {"code": "consc_diligence", "name": "Diligence", "description": "Work ethic and persistence in pursuing goals.", "trait_type": "CONSCIENTIOUSNESS"}, {"code": "consc_perfectionism", "name": "Perfectionism", "description": "Thoroughness and concern with details and accuracy.", "trait_type": "CONSCIENTIOUSNESS"}, {"code": "consc_prudence", "name": "Prudence", "description": "Tendency to deliberate carefully and inhibit impulses.", "trait_type": "CONSCIENTIOUSNESS"}], "OPENNESS": [{"code": "open_aesthetic", "name": "Aesthetic Appreciation", "description": "Enjoyment of beauty in art, music, and nature.", "trait_type": "OPENNESS"}, {"code": "open_inquisitive", "name": "Inquisitiveness", "description": "Interest in exploring new ideas and understanding complex concepts.", "trait_type": "OPENNESS"}, {"code": "open_creativity", "name": "Creativity", "description": "Preference for innovation and experimentation.", "trait_type": "OPENNESS"}, {"code": "open_unconventional", "name": "Unconventionality", "description": "Willingness to accept the unusual and challenge tradition.", "trait_type": "OPENNESS"}]}, "limitations": {"PHYSICAL": [{"code": "phys_mobility_general", "description": "General mobility limitations affecting overall movement", "limitation_type": "PHYSICAL"}, {"code": "phys_mobility_upper", "description": "Limited mobility in upper body or arms", "limitation_type": "PHYSICAL"}, {"code": "phys_mobility_lower", "description": "Limited mobility in lower body or legs", "limitation_type": "PHYSICAL"}, {"code": "phys_stamina", "description": "Limited physical stamina or endurance", "limitation_type": "PHYSICAL"}, {"code": "phys_strength", "description": "Limited physical strength", "limitation_type": "PHYSICAL"}, {"code": "phys_balance", "description": "Balance or coordination challenges", "limitation_type": "PHYSICAL"}, {"code": "phys_dexterity", "description": "Reduced fine motor skills or dexterity", "limitation_type": "PHYSICAL"}, {"code": "phys_vision", "description": "Visual impairment", "limitation_type": "PHYSICAL"}, {"code": "phys_hearing", "description": "Hearing impairment", "limitation_type": "PHYSICAL"}, {"code": "phys_speech", "description": "Speech or communication difficulties", "limitation_type": "PHYSICAL"}, {"code": "phys_respiration", "description": "Respiratory limitations (asthma, COPD, etc.)", "limitation_type": "PHYSICAL"}, {"code": "phys_cardiovascular", "description": "Cardiovascular limitations", "limitation_type": "PHYSICAL"}, {"code": "phys_chronic_pain", "description": "Chronic pain condition", "limitation_type": "PHYSICAL"}], "COGNITIVE": [{"code": "cog_attention", "description": "Attention or focus difficulties", "limitation_type": "COGNITIVE"}, {"code": "cog_processing", "description": "Information processing speed limitations", "limitation_type": "COGNITIVE"}, {"code": "cog_memory", "description": "Memory or recall challenges", "limitation_type": "COGNITIVE"}, {"code": "cog_executive", "description": "Executive function limitations", "limitation_type": "COGNITIVE"}, {"code": "cog_learning", "description": "Learning or comprehension challenges", "limitation_type": "COGNITIVE"}, {"code": "cog_literacy", "description": "Reading or writing difficulties", "limitation_type": "COGNITIVE"}, {"code": "cog_math", "description": "Mathematical or numerical processing limitations", "limitation_type": "COGNITIVE"}], "PSYCHOLOGICAL": [{"code": "psych_anxiety", "description": "Anxiety-related limitations", "limitation_type": "PSYCHOLOGICAL"}, {"code": "psych_depression", "description": "Depression-related limitations", "limitation_type": "PSYCHOLOGICAL"}, {"code": "psych_stress", "description": "Stress sensitivity or management difficulties", "limitation_type": "PSYCHOLOGICAL"}, {"code": "psych_emotional_regulation", "description": "Emotional regulation challenges", "limitation_type": "PSYCHOLOGICAL"}, {"code": "psych_social_anxiety", "description": "Social anxiety or discomfort", "limitation_type": "PSYCHOLOGICAL"}, {"code": "psych_confidence", "description": "Self-confidence or self-esteem limitations", "limitation_type": "PSYCHOLOGICAL"}, {"code": "psych_trauma", "description": "Trauma-related sensitivities", "limitation_type": "PSYCHOLOGICAL"}, {"code": "psych_motivation", "description": "Motivation or initiative difficulties", "limitation_type": "PSYCHOLOGICAL"}], "SOCIAL": [{"code": "social_communication", "description": "Social communication challenges", "limitation_type": "SOCIAL"}, {"code": "social_interpretation", "description": "Difficulty interpreting social cues", "limitation_type": "SOCIAL"}, {"code": "social_group", "description": "Discomfort in group settings", "limitation_type": "SOCIAL"}, {"code": "social_strangers", "description": "Difficulty interacting with unfamiliar people", "limitation_type": "SOCIAL"}, {"code": "social_conflict", "description": "Challenges handling social conflict or criticism", "limitation_type": "SOCIAL"}], "ENVIRONMENTAL": [{"code": "env_outdoor", "description": "Limitations in outdoor environments", "limitation_type": "ENVIRONMENTAL"}, {"code": "env_noise", "description": "Sensitivity to loud or persistent noise", "limitation_type": "ENVIRONMENTAL"}, {"code": "env_light", "description": "Sensitivity to bright or flashing lights", "limitation_type": "ENVIRONMENTAL"}, {"code": "env_temperature", "description": "Sensitivity to temperature extremes", "limitation_type": "ENVIRONMENTAL"}, {"code": "env_crowds", "description": "Discomfort in crowded spaces", "limitation_type": "ENVIRONMENTAL"}, {"code": "env_allergens", "description": "Environmental allergies or sensitivities", "limitation_type": "ENVIRONMENTAL"}], "TEMPORAL": [{"code": "time_morning", "description": "Difficulty with morning activities", "limitation_type": "TEMPORAL"}, {"code": "time_evening", "description": "Difficulty with evening activities", "limitation_type": "TEMPORAL"}, {"code": "time_duration", "description": "Limitations with extended duration activities", "limitation_type": "TEMPORAL"}, {"code": "time_regularity", "description": "Challenges maintaining regular schedules", "limitation_type": "TEMPORAL"}, {"code": "time_transitions", "description": "Difficulty with transitions between activities", "limitation_type": "TEMPORAL"}], "RESOURCE": [{"code": "res_financial", "description": "Financial resource limitations", "limitation_type": "RESOURCE"}, {"code": "res_transportation", "description": "Transportation access limitations", "limitation_type": "RESOURCE"}, {"code": "res_space", "description": "Limited physical space availability", "limitation_type": "RESOURCE"}, {"code": "res_equipment", "description": "Limited access to necessary equipment", "limitation_type": "RESOURCE"}, {"code": "res_digital", "description": "Limited digital device or internet access", "limitation_type": "RESOURCE"}, {"code": "res_support", "description": "Limited social or professional support", "limitation_type": "RESOURCE"}]}, "skill_attributes": {"cognitive": [{"code": "cog_analytical", "name": "Analytical Reasoning", "description": "Ability to break down complex information, identify patterns, and draw logical conclusions.", "base_decay_rate": 20, "development_difficulty": 70, "development_timeframe": "long"}, {"code": "cog_creative", "name": "Creative Thinking", "description": "Ability to generate novel ideas, make unexpected connections, and approach problems from multiple perspectives.", "base_decay_rate": 25, "development_difficulty": 60, "development_timeframe": "medium"}, {"code": "cog_spatial", "name": "Spatial Reasoning", "description": "Ability to visualize and manipulate objects and spatial relationships in the mind.", "base_decay_rate": 30, "development_difficulty": 65, "development_timeframe": "medium"}, {"code": "cog_verbal", "name": "Verbal Processing", "description": "Ability to understand, process, and produce language effectively.", "base_decay_rate": 15, "development_difficulty": 50, "development_timeframe": "long"}, {"code": "cog_numerical", "name": "Numerical Processing", "description": "Ability to work with numbers, understand mathematical concepts, and perform calculations.", "base_decay_rate": 35, "development_difficulty": 75, "development_timeframe": "long"}, {"code": "cog_memory", "name": "Memory Capacity", "description": "Ability to encode, store, and retrieve information effectively.", "base_decay_rate": 40, "development_difficulty": 60, "development_timeframe": "medium"}, {"code": "cog_attention", "name": "Sustained Attention", "description": "Ability to maintain focus on tasks or stimuli over time without distraction.", "base_decay_rate": 45, "development_difficulty": 55, "development_timeframe": "medium"}], "physical": [{"code": "phys_strength", "name": "Physical Strength", "description": "Muscular power and force generation capacity.", "base_decay_rate": 50, "development_difficulty": 60, "development_timeframe": "medium"}, {"code": "phys_endurance", "name": "Physical Endurance", "description": "Ability to sustain physical activity over extended periods.", "base_decay_rate": 45, "development_difficulty": 65, "development_timeframe": "medium"}, {"code": "phys_flexibility", "name": "Flexibility", "description": "Range of motion in joints and muscles.", "base_decay_rate": 40, "development_difficulty": 50, "development_timeframe": "short"}, {"code": "phys_balance", "name": "Balance & Coordination", "description": "Ability to maintain equilibrium and coordinate movements effectively.", "base_decay_rate": 35, "development_difficulty": 65, "development_timeframe": "medium"}, {"code": "phys_fine_motor", "name": "Fine Motor Control", "description": "Precision in small, detailed movements, particularly of the hands and fingers.", "base_decay_rate": 30, "development_difficulty": 70, "development_timeframe": "long"}], "social": [{"code": "soc_empathy", "name": "Empathic Understanding", "description": "Ability to perceive and understand others' emotions, perspectives, and needs.", "base_decay_rate": 20, "development_difficulty": 60, "development_timeframe": "long"}, {"code": "soc_expression", "name": "Emotional Expression", "description": "Ability to convey one's feelings, intentions, and ideas clearly to others.", "base_decay_rate": 25, "development_difficulty": 55, "development_timeframe": "medium"}, {"code": "soc_persuasion", "name": "Persuasive Communication", "description": "Ability to influence others' thoughts or actions through communication.", "base_decay_rate": 30, "development_difficulty": 70, "development_timeframe": "long"}, {"code": "soc_conflict", "name": "Conflict Resolution", "description": "Ability to navigate and resolve interpersonal conflicts constructively.", "base_decay_rate": 35, "development_difficulty": 75, "development_timeframe": "long"}, {"code": "soc_leadership", "name": "Leadership Capacity", "description": "Ability to inspire, direct, and coordinate group efforts toward goals.", "base_decay_rate": 30, "development_difficulty": 80, "development_timeframe": "long"}], "emotional": [{"code": "emot_awareness", "name": "Emotional Awareness", "description": "Ability to recognize and understand one's own emotional states.", "base_decay_rate": 20, "development_difficulty": 65, "development_timeframe": "medium"}, {"code": "emot_regulation", "name": "Emotional Regulation", "description": "Ability to manage emotional responses adaptively in various situations.", "base_decay_rate": 30, "development_difficulty": 75, "development_timeframe": "long"}, {"code": "emot_resilience", "name": "Psychological Resilience", "description": "Ability to adapt to adversity, recover from setbacks, and grow from challenges.", "base_decay_rate": 25, "development_difficulty": 80, "development_timeframe": "long"}, {"code": "emot_mindfulness", "name": "Mindful Awareness", "description": "Ability to maintain present-moment awareness with acceptance and without judgment.", "base_decay_rate": 40, "development_difficulty": 60, "development_timeframe": "medium"}, {"code": "emot_expression", "name": "Emotional Expression", "description": "Ability to express one's personal emotion.", "base_decay_rate": 40, "development_difficulty": 60, "development_timeframe": "medium"}], "creative": [{"code": "create_visual", "name": "Visual Creativity", "description": "Ability to conceptualize and create visually expressive or innovative content.", "base_decay_rate": 30, "development_difficulty": 65, "development_timeframe": "long"}, {"code": "create_verbal", "name": "Verbal Creativity", "description": "Ability to use language in novel, expressive, or evocative ways.", "base_decay_rate": 25, "development_difficulty": 60, "development_timeframe": "medium"}, {"code": "create_musical", "name": "Musical Aptitude", "description": "Sensitivity to and ability to work with musical elements like rhythm, pitch, and harmony.", "base_decay_rate": 35, "development_difficulty": 70, "development_timeframe": "long"}, {"code": "create_bodily", "name": "Kinesthetic Creativity", "description": "Ability to express ideas and emotions through body movement and physical performance.", "base_decay_rate": 40, "development_difficulty": 65, "development_timeframe": "medium"}], "practical": [{"code": "tech_digital", "name": "Digital Literacy", "description": "Ability to use, understand, and adapt to digital tools and technologies.", "base_decay_rate": 40, "development_difficulty": 55, "development_timeframe": "medium"}, {"code": "tech_mechanical", "name": "Mechanical Aptitude", "description": "Understanding of mechanical principles and ability to work with physical systems.", "base_decay_rate": 30, "development_difficulty": 65, "development_timeframe": "medium"}, {"code": "tech_project", "name": "Project Organization", "description": "Ability to plan, organize, and execute complex multi-step projects.", "base_decay_rate": 25, "development_difficulty": 60, "development_timeframe": "medium"}]}, "skill_definitions": [{"code": "creative_writing", "name": "Creative Writing", "description": "Ability to craft engaging written content with originality and expressiveness.", "tags": ["creative", "communication", "artistic"]}, {"code": "public_speaking", "name": "Public Speaking", "description": "Ability to deliver clear, compelling presentations to audiences of various sizes.", "tags": ["communication", "social", "professional"]}, {"code": "problem_solving", "name": "Problem Solving", "description": "Ability to identify challenges, analyze possible solutions, and implement effective approaches.", "tags": ["cognitive", "analytical", "professional"]}, {"code": "visual_art", "name": "Visual Art Creation", "description": "Ability to express ideas, emotions, and concepts through visual media and techniques.", "tags": ["creative", "artistic", "visual"]}, {"code": "physical_fitness", "name": "Physical Fitness Training", "description": "Knowledge and ability to develop physical capabilities through targeted exercise and recovery.", "tags": ["physical", "health", "lifestyle"]}, {"code": "emotional_intelligence", "name": "Emotional Intelligence", "description": "Ability to recognize, understand, and manage emotions in oneself and others.", "tags": ["social", "emotional", "interpersonal"]}, {"code": "programming", "name": "Computer Programming", "description": "Ability to design, write, test, and maintain computer code to create software.", "tags": ["technical", "digital", "professional"]}, {"code": "leadership", "name": "Leadership", "description": "Ability to guide, inspire, and coordinate groups toward shared objectives.", "tags": ["social", "professional", "management"]}, {"code": "meditation", "name": "Meditation Practice", "description": "Ability to cultivate focused attention, awareness, and mental clarity through structured mindfulness techniques.", "tags": ["mindfulness", "emotional", "spiritual"]}, {"code": "negotiation", "name": "Negotiation", "description": "Ability to reach agreements through effective communication, understanding of interests, and strategic problem-solving.", "tags": ["social", "professional", "communication"]}, {"code": "communication", "name": "Core Communication", "description": "Core verbal and written communication abilities", "tags": ["communication", "social", "professional"]}, {"code": "soft_communication", "name": "Advanced Interpersonal Communication", "description": "Advanced interpersonal communication nuances", "tags": ["communication", "social", "interpersonal"]}, {"code": "soft_empathy", "name": "Empathy", "description": "Understanding and responding to others' emotions", "tags": ["social", "emotional", "interpersonal"]}, {"code": "soft_conflict_resolution", "name": "Conflict Resolution", "description": "Managing disagreements constructively", "tags": ["social", "professional", "communication"]}, {"code": "soft_leadership", "name": "Leadership Skills", "description": "Guiding and motivating others effectively", "tags": ["social", "professional", "management"]}, {"code": "soft_presentation", "name": "Presentation Skills", "description": "Public speaking and formal presentation skills", "tags": ["communication", "social", "professional"]}, {"code": "soft_networking", "name": "Networking", "description": "Building and maintaining professional relationships", "tags": ["social", "professional", "communication"]}, {"code": "soft_active_listening", "name": "Active Listening", "description": "Focused attention and responsive listening skills", "tags": ["communication", "social", "interpersonal"]}, {"code": "soft_persuasion", "name": "Persuasion", "description": "Influencing others through reasoning and appeal", "tags": ["communication", "social", "professional"]}, {"code": "soft_cultural_competence", "name": "Cultural Competence", "description": "Understanding and working across cultural differences", "tags": ["social", "communication", "interpersonal"]}, {"code": "soft_team_collaboration", "name": "Team Collaboration", "description": "Working effectively within group dynamics", "tags": ["social", "professional", "teamwork"]}, {"code": "soft_introspection", "name": "Introspection", "description": "Self-awareness and reflection capabilities", "tags": ["emotional", "psychological", "self-development"]}, {"code": "soft_emotional_regulation", "name": "Emotional Regulation", "description": "Managing one's own emotional responses", "tags": ["emotional", "psychological", "self-management"]}, {"code": "soft_stress_management", "name": "Stress Management", "description": "Coping with pressure and high-demand situations", "tags": ["emotional", "psychological", "health"]}, {"code": "soft_resilience", "name": "Resilience", "description": "Bouncing back from setbacks and challenges", "tags": ["emotional", "psychological", "self-development"]}, {"code": "soft_mindfulness", "name": "Mindfulness", "description": "Present-moment awareness and attention practices", "tags": ["emotional", "psychological", "spiritual"]}, {"code": "soft_therapeutic", "name": "Therapeutic Skills", "description": "Supporting others through emotional difficulties", "tags": ["social", "emotional", "helping"]}, {"code": "soft_meditation", "name": "Meditation", "description": "Contemplative practices for mental clarity", "tags": ["emotional", "psychological", "spiritual"]}, {"code": "soft_grief_processing", "name": "Grief Processing", "description": "Navigating loss and life transitions", "tags": ["emotional", "psychological", "therapeutic"]}, {"code": "soft_philosophical", "name": "Philosophical Thinking", "description": "Abstract thinking and questioning fundamental concepts", "tags": ["intellectual", "analytical", "reflective"]}, {"code": "soft_critical_thinking", "name": "Critical Thinking", "description": "Analyzing information objectively and systematically", "tags": ["intellectual", "analytical", "cognitive"]}, {"code": "soft_problem_solving", "name": "Problem Solving", "description": "Breaking down complex challenges into manageable parts", "tags": ["intellectual", "analytical", "cognitive"]}, {"code": "soft_research", "name": "Research Skills", "description": "Finding, evaluating, and synthesizing information", "tags": ["intellectual", "analytical", "academic"]}, {"code": "soft_strategic_thinking", "name": "Strategic Thinking", "description": "Long-term planning and systems perspective", "tags": ["intellectual", "analytical", "professional"]}, {"code": "soft_mathematical", "name": "Mathematical Reasoning", "description": "Numerical reasoning and quantitative analysis", "tags": ["intellectual", "analytical", "technical"]}, {"code": "soft_scientific", "name": "Scientific Thinking", "description": "Hypothesis formation and experimental thinking", "tags": ["intellectual", "analytical", "scientific"]}, {"code": "tech_ai_concepts", "name": "AI Concepts", "description": "Understanding AI, machine learning, and automation", "tags": ["technical", "digital", "modern"]}, {"code": "tech_coding_python", "name": "Python Programming", "description": "Programming in Python language", "tags": ["technical", "digital", "programming"]}, {"code": "tech_coding_javascript", "name": "JavaScript Programming", "description": "Programming in JavaScript language", "tags": ["technical", "digital", "programming"]}, {"code": "tech_coding_web", "name": "Web Development", "description": "HTML, CSS, web development fundamentals", "tags": ["technical", "digital", "programming"]}], "beliefs": {"SELF_WORTH": [{"code": "self_worth_inherent", "name": "Inherent Value", "description": "Belief that one has intrinsic value regardless of achievements or external validation.", "typical_stability": 70, "category": "SELF_WORTH"}, {"code": "self_worth_conditional", "name": "Conditional Self-Worth", "description": "Belief that one's value depends on meeting certain conditions, achievements, or standards.", "typical_stability": 75, "category": "SELF_WORTH"}, {"code": "self_worth_comparative", "name": "Comparative Value", "description": "Belief that one's worth is determined by comparison to others.", "typical_stability": 65, "category": "SELF_WORTH"}, {"code": "self_worth_permanent", "name": "Permanence of Worth", "description": "Belief that one's value is stable and enduring rather than fluctuating based on circumstances.", "typical_stability": 80, "category": "SELF_WORTH"}], "SELF_EFFICACY": [{"code": "self_efficacy_general", "name": "General Self-Efficacy", "description": "Belief in one's overall ability to perform tasks and achieve goals across various domains.", "typical_stability": 65, "category": "SELF_EFFICACY"}, {"code": "self_efficacy_domain", "name": "Domain-Specific Efficacy", "description": "Belief in one's ability to succeed in specific areas or types of tasks.", "typical_stability": 60, "category": "SELF_EFFICACY"}, {"code": "self_efficacy_control", "name": "Control Beliefs", "description": "Beliefs about the degree of control one has over outcomes and life circumstances.", "typical_stability": 70, "category": "SELF_EFFICACY"}, {"code": "self_efficacy_resilience", "name": "Resilience Beliefs", "description": "Beliefs about one's ability to recover from setbacks and adapt to challenges.", "typical_stability": 65, "category": "SELF_EFFICACY"}], "IDENTITY": [{"code": "identity_core", "name": "Core Identity", "description": "Fundamental beliefs about who one is as a person, including essential traits and characteristics.", "typical_stability": 85, "category": "IDENTITY"}, {"code": "identity_role", "name": "Role Identity", "description": "Beliefs about one's purpose or function in various social contexts and relationships.", "typical_stability": 75, "category": "IDENTITY"}, {"code": "identity_malleability", "name": "Identity Malleability", "description": "Beliefs about whether one's identity is fixed or can change over time.", "typical_stability": 70, "category": "IDENTITY"}, {"code": "identity_integration", "name": "Identity Integration", "description": "Beliefs about how various aspects of identity connect or conflict with each other.", "typical_stability": 65, "category": "IDENTITY"}], "POTENTIAL": [{"code": "potential_capacity", "name": "Growth Capacity", "description": "Beliefs about the extent to which one can develop new abilities and qualities.", "typical_stability": 60, "category": "POTENTIAL"}, {"code": "potential_trajectory", "name": "Growth Trajectory", "description": "Beliefs about the pace and pattern of personal development over time.", "typical_stability": 55, "category": "POTENTIAL"}, {"code": "potential_limitation", "name": "Inherent Limitations", "description": "Beliefs about fundamental constraints on one's development or achievement.", "typical_stability": 75, "category": "POTENTIAL"}, {"code": "potential_agency", "name": "Growth Agency", "description": "Beliefs about one's ability to actively direct and shape personal development.", "typical_stability": 65, "category": "POTENTIAL"}], "SKILL_LEARNING": [{"code": "learning_intelligence", "name": "Intelligence Beliefs", "description": "Beliefs about whether intelligence is fixed or can be developed through effort.", "typical_stability": 70, "category": "SKILL_LEARNING"}, {"code": "learning_effort", "name": "Effort Value", "description": "Beliefs about the relationship between effort and achievement in skill development.", "typical_stability": 65, "category": "SKILL_LEARNING"}, {"code": "learning_mistakes", "name": "Learning from Mistakes", "description": "Beliefs about the role of failures and mistakes in the learning process.", "typical_stability": 60, "category": "SKILL_LEARNING"}, {"code": "learning_efficiency", "name": "Learning Efficiency", "description": "Beliefs about one's rate of skill acquisition relative to others or to personal expectations.", "typical_stability": 55, "category": "SKILL_LEARNING"}], "TALENT_INNATE": [{"code": "talent_inborn", "name": "Innate Talents", "description": "Beliefs about inherent abilities or predispositions for certain domains.", "typical_stability": 75, "category": "TALENT_INNATE"}, {"code": "talent_domain", "name": "Domain-Specific Talent", "description": "Beliefs about one's innate abilities in particular areas of activity.", "typical_stability": 70, "category": "TALENT_INNATE"}, {"code": "talent_discovery", "name": "Talent Discovery", "description": "Beliefs about how and when talents are identified or revealed.", "typical_stability": 60, "category": "TALENT_INNATE"}, {"code": "talent_development", "name": "Talent Development", "description": "Beliefs about the relationship between innate talent and deliberate practice.", "typical_stability": 65, "category": "TALENT_INNATE"}], "FEAR_PATTERN": [{"code": "fear_failure", "name": "Fear of Failure", "description": "Beliefs about the consequences and meaning of failure or defeat.", "typical_stability": 75, "category": "FEAR_PATTERN"}, {"code": "fear_rejection", "name": "Fear of Rejection", "description": "Beliefs about social exclusion, abandonment, or disapproval.", "typical_stability": 80, "category": "FEAR_PATTERN"}, {"code": "fear_uncertainty", "name": "Fear of Uncertainty", "description": "Beliefs about ambiguity, unpredictability, and the unknown.", "typical_stability": 70, "category": "FEAR_PATTERN"}, {"code": "fear_vulnerability", "name": "Fear of Vulnerability", "description": "Beliefs about the risks associated with emotional openness or authenticity.", "typical_stability": 75, "category": "FEAR_PATTERN"}], "MEANING": [{"code": "meaning_purpose", "name": "Life Purpose", "description": "Beliefs about one's unique contribution, role, or ultimate destination in life.", "typical_stability": 75, "category": "MEANING"}, {"code": "meaning_coherence", "name": "Life Coherence", "description": "Beliefs about the order, structure, and comprehensibility of life events.", "typical_stability": 70, "category": "MEANING"}, {"code": "meaning_significance", "name": "Personal Significance", "description": "Beliefs about the impact and importance of one's existence and actions.", "typical_stability": 80, "category": "MEANING"}, {"code": "meaning_transcendence", "name": "Transcendent Meaning", "description": "Beliefs about spiritual or existential dimensions of life's meaning.", "typical_stability": 85, "category": "MEANING"}]}}