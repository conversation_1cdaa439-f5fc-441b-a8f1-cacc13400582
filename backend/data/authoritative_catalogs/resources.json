{"metadata": {"version": "1.0.0", "generated_from": "seed_db_70_activities.py analysis", "last_updated": "2025-06-29", "description": "Authoritative catalog of resources extracted from activity requirements", "source_file": "seed_db_70_activities.py", "total_resources": 74}, "resources": {"technology": [{"code": "tech_smartphone", "name": "Smartphone", "description": "A mobile device with internet connectivity for apps, communication, and digital activities", "resource_type": "TECHNOLOGY", "typical_cost": 0.0, "acquisition_cost": 150.0, "availability": "COMMON"}, {"code": "tech_computer", "name": "Computer/Laptop", "description": "A personal computer or laptop for research, learning, and digital work", "resource_type": "TECHNOLOGY", "typical_cost": 0.0, "acquisition_cost": 500.0, "availability": "COMMON"}, {"code": "tech_computer_internet", "name": "Computer with Internet", "description": "Computer with reliable internet connection for online research and learning", "resource_type": "TECHNOLOGY", "typical_cost": 50.0, "acquisition_cost": 850.0, "availability": "COMMON"}, {"code": "tech_phone", "name": "Phone", "description": "Basic phone for communication and simple apps", "resource_type": "TECHNOLOGY", "typical_cost": 0.0, "acquisition_cost": 200.0, "availability": "VERY_COMMON"}, {"code": "tech_camera", "name": "Camera", "description": "Digital camera for photography activities", "resource_type": "TECHNOLOGY", "typical_cost": 0.0, "acquisition_cost": 400.0, "availability": "COMMON"}, {"code": "tech_gaming", "name": "Gaming Device", "description": "Gaming console, computer, or device for digital gaming activities", "resource_type": "TECHNOLOGY", "typical_cost": 0.0, "acquisition_cost": 300.0, "availability": "COMMON"}, {"code": "tech_screen", "name": "Screen/Display", "description": "Television, monitor, or large screen for viewing content", "resource_type": "TECHNOLOGY", "typical_cost": 0.0, "acquisition_cost": 300.0, "availability": "COMMON"}, {"code": "tech_music_player", "name": "Music Player", "description": "Device for playing music, including speakers or headphones", "resource_type": "TECHNOLOGY", "typical_cost": 0.0, "acquisition_cost": 100.0, "availability": "COMMON"}, {"code": "tech_navigation", "name": "Navigation Device", "description": "GPS device or navigation app for travel and exploration", "resource_type": "TECHNOLOGY", "typical_cost": 0.0, "acquisition_cost": 150.0, "availability": "COMMON"}, {"code": "tech_vr_headset", "name": "VR Headset", "description": "Virtual reality headset for immersive digital experiences", "resource_type": "TECHNOLOGY", "typical_cost": 0.0, "acquisition_cost": 400.0, "availability": "UNCOMMON"}], "equipment": [{"code": "equip_exercise_mat", "name": "Exercise Mat", "description": "Yoga or exercise mat for floor-based physical activities", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 30.0, "availability": "COMMON"}, {"code": "equip_yoga_mat", "name": "Yoga Mat", "description": "Specialized mat for yoga practice with proper grip and cushioning", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 40.0, "availability": "COMMON"}, {"code": "equip_yoga_block", "name": "Yoga Block", "description": "Supportive block for yoga poses and stretching", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 15.0, "availability": "COMMON"}, {"code": "equip_running_shoes", "name": "Running Shoes", "description": "Proper athletic footwear for running and cardio activities", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 120.0, "availability": "COMMON"}, {"code": "equip_walking_shoes", "name": "Walking Shoes", "description": "Comfortable shoes suitable for walking and light exercise", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 80.0, "availability": "VERY_COMMON"}, {"code": "equip_athletic_wear", "name": "Athletic Wear", "description": "Appropriate clothing for physical exercise and sports", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 60.0, "availability": "COMMON"}, {"code": "equip_protective_gear", "name": "Protective Gear", "description": "Safety equipment for high-risk physical activities", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 100.0, "availability": "COMMON"}, {"code": "equip_training_shoes", "name": "Training Shoes", "description": "Cross-training shoes for varied physical activities", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 100.0, "availability": "COMMON"}, {"code": "equip_comfortable_shoes", "name": "Comfortable Shoes", "description": "General comfortable footwear for daily activities", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 70.0, "availability": "VERY_COMMON"}, {"code": "equip_timer", "name": "Timer", "description": "Device for timing activities and exercises", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 20.0, "availability": "COMMON"}, {"code": "equip_water_bottle", "name": "Water Bottle", "description": "Reusable water bottle for hydration during activities", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 15.0, "availability": "VERY_COMMON"}, {"code": "equip_day_pack", "name": "Day Pack", "description": "Small backpack for carrying essentials during outings", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 50.0, "availability": "COMMON"}, {"code": "equip_outdoor_basic", "name": "Basic Outdoor Equipment", "description": "Essential gear for outdoor activities and exploration", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 75.0, "availability": "COMMON"}, {"code": "equip_blindfold", "name": "<PERSON>fold", "description": "Simple blindfold for trust exercises and sensory activities", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 10.0, "availability": "COMMON"}, {"code": "equip_cleaning_tools", "name": "Cleaning Tools", "description": "Basic cleaning supplies and tools for organization activities", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 40.0, "availability": "VERY_COMMON"}, {"code": "equip_learning_tools", "name": "Learning Tools", "description": "Educational materials and tools for skill development", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 60.0, "availability": "COMMON"}, {"code": "equip_professional_attire", "name": "Professional Attire", "description": "Appropriate clothing for professional networking events", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 200.0, "availability": "COMMON"}, {"code": "equip_business_cards", "name": "Business Cards", "description": "Professional business cards for networking", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 25.0, "availability": "COMMON"}, {"code": "equip_board_game", "name": "Board Game", "description": "Strategy or social board game for intellectual activities", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 40.0, "availability": "COMMON"}, {"code": "equip_kitchen_basic", "name": "Basic Kitchen Equipment", "description": "Essential kitchen tools for cooking activities", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 100.0, "availability": "COMMON"}, {"code": "equip_kettle", "name": "Kettle", "description": "Electric or stovetop kettle for heating water", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 30.0, "availability": "COMMON"}], "stationery": [{"code": "stationery_journal", "name": "Journal", "description": "Notebook or journal for writing, reflection, and planning", "resource_type": "STATIONERY", "typical_cost": 0.0, "acquisition_cost": 15.0, "availability": "VERY_COMMON"}, {"code": "stationery_paper", "name": "Paper", "description": "Writing paper for notes, exercises, and creative activities", "resource_type": "STATIONERY", "typical_cost": 0.0, "acquisition_cost": 5.0, "availability": "VERY_COMMON"}, {"code": "stationery_pen", "name": "Pen", "description": "Writing pen for journaling and note-taking", "resource_type": "STATIONERY", "typical_cost": 0.0, "acquisition_cost": 2.0, "availability": "VERY_COMMON"}, {"code": "stationery_pencil", "name": "Pencil", "description": "Pencil for writing, drawing, and problem-solving", "resource_type": "STATIONERY", "typical_cost": 0.0, "acquisition_cost": 1.0, "availability": "VERY_COMMON"}, {"code": "stationery_markers", "name": "Markers", "description": "Colored markers for creative and organizational activities", "resource_type": "STATIONERY", "typical_cost": 0.0, "acquisition_cost": 10.0, "availability": "COMMON"}, {"code": "equip_journal", "name": "Journal/Notebook", "description": "Dedicated journal or notebook for reflection and planning", "resource_type": "STATIONERY", "typical_cost": 0.0, "acquisition_cost": 15.0, "availability": "VERY_COMMON"}, {"code": "equip_planner", "name": "Planner", "description": "Structured planner for goal setting and time management", "resource_type": "STATIONERY", "typical_cost": 0.0, "acquisition_cost": 25.0, "availability": "COMMON"}, {"code": "equip_writing_tool", "name": "Writing Tool", "description": "Pen, pencil, or other writing implement", "resource_type": "STATIONERY", "typical_cost": 0.0, "acquisition_cost": 3.0, "availability": "VERY_COMMON"}, {"code": "edu_writing_materials", "name": "Educational Writing Materials", "description": "Comprehensive writing supplies for learning activities", "resource_type": "STATIONERY", "typical_cost": 0.0, "acquisition_cost": 20.0, "availability": "COMMON"}], "creative": [{"code": "creative_instrument", "name": "Musical Instrument", "description": "Musical instrument for creative expression and performance", "resource_type": "CREATIVE", "typical_cost": 0.0, "acquisition_cost": 200.0, "availability": "COMMON"}, {"code": "creative_drawing_materials", "name": "Drawing Materials", "description": "Art supplies for drawing and sketching activities", "resource_type": "CREATIVE", "typical_cost": 0.0, "acquisition_cost": 30.0, "availability": "COMMON"}, {"code": "art_basic_supplies", "name": "Basic Art Supplies", "description": "Essential art materials for creative expression", "resource_type": "CREATIVE", "typical_cost": 0.0, "acquisition_cost": 40.0, "availability": "COMMON"}, {"code": "art_paper", "name": "Art Paper", "description": "Specialized paper for drawing and artistic activities", "resource_type": "CREATIVE", "typical_cost": 0.0, "acquisition_cost": 10.0, "availability": "COMMON"}], "leisure": [{"code": "leisure_board_games", "name": "Board Games", "description": "Collection of board games for social entertainment", "resource_type": "LEISURE", "typical_cost": 0.0, "acquisition_cost": 80.0, "availability": "COMMON"}, {"code": "leisure_chess_set", "name": "Chess Set", "description": "Chess board and pieces for strategic gameplay", "resource_type": "LEISURE", "typical_cost": 0.0, "acquisition_cost": 35.0, "availability": "COMMON"}, {"code": "leisure_puzzle", "name": "Puzzle", "description": "Jigsaw puzzle or brain teaser for mental stimulation", "resource_type": "LEISURE", "typical_cost": 0.0, "acquisition_cost": 20.0, "availability": "COMMON"}, {"code": "leisure_snacks", "name": "Snacks", "description": "Light snacks for leisure activities and entertainment", "resource_type": "LEISURE", "typical_cost": 5.0, "acquisition_cost": 5.0, "availability": "VERY_COMMON"}], "food": [{"code": "food_snack", "name": "Snack Food", "description": "Light snack for mindful eating activities", "resource_type": "FOOD", "typical_cost": 3.0, "acquisition_cost": 3.0, "availability": "VERY_COMMON"}, {"code": "food_tea", "name": "Tea", "description": "Tea for mindful drinking and relaxation rituals", "resource_type": "FOOD", "typical_cost": 2.0, "acquisition_cost": 10.0, "availability": "VERY_COMMON"}, {"code": "cook_equipment", "name": "Cooking Equipment", "description": "Basic cooking tools and equipment for meal preparation", "resource_type": "FOOD", "typical_cost": 0.0, "acquisition_cost": 150.0, "availability": "COMMON"}, {"code": "cook_ingredients", "name": "Cooking Ingredients", "description": "Fresh ingredients for cooking and meal preparation", "resource_type": "FOOD", "typical_cost": 20.0, "acquisition_cost": 20.0, "availability": "COMMON"}], "garden": [{"code": "garden_tools", "name": "Garden Tools", "description": "Basic gardening tools for plant care activities", "resource_type": "GARDEN", "typical_cost": 0.0, "acquisition_cost": 60.0, "availability": "COMMON"}, {"code": "garden_plants", "name": "Plants", "description": "Plants, seeds, or seedlings for gardening activities", "resource_type": "GARDEN", "typical_cost": 5.0, "acquisition_cost": 25.0, "availability": "COMMON"}], "outdoor": [{"code": "outdoor_footwear", "name": "Outdoor Footwear", "description": "Appropriate footwear for outdoor activities and hiking", "resource_type": "OUTDOOR", "typical_cost": 0.0, "acquisition_cost": 100.0, "availability": "COMMON"}, {"code": "outdoor_water", "name": "Water Supply", "description": "Clean water for outdoor activities and hydration", "resource_type": "OUTDOOR", "typical_cost": 2.0, "acquisition_cost": 2.0, "availability": "VERY_COMMON"}], "media": [{"code": "media_music", "name": "Music", "description": "Music streaming or audio content for activities", "resource_type": "MEDIA", "typical_cost": 10.0, "acquisition_cost": 10.0, "availability": "VERY_COMMON"}], "finance": [{"code": "finance_small", "name": "Small Financial Amount", "description": "Small amount of money for minor purchases or activities", "resource_type": "FINANCE", "typical_cost": 10.0, "acquisition_cost": 10.0, "availability": "COMMON"}, {"code": "finance_medium", "name": "Medium Financial Amount", "description": "Moderate amount of money for activities requiring investment", "resource_type": "FINANCE", "typical_cost": 50.0, "acquisition_cost": 50.0, "availability": "COMMON"}, {"code": "finance_activity_fee", "name": "Activity Fee", "description": "Payment for organized activities or classes", "resource_type": "FINANCE", "typical_cost": 25.0, "acquisition_cost": 25.0, "availability": "COMMON"}, {"code": "finance_dining", "name": "Dining Budget", "description": "Money for dining out or food-related activities", "resource_type": "FINANCE", "typical_cost": 30.0, "acquisition_cost": 30.0, "availability": "COMMON"}, {"code": "finance_entertainment", "name": "Entertainment Budget", "description": "Money for entertainment and leisure activities", "resource_type": "FINANCE", "typical_cost": 20.0, "acquisition_cost": 20.0, "availability": "COMMON"}, {"code": "finance_travel", "name": "Travel Budget", "description": "Money for travel and exploration activities", "resource_type": "FINANCE", "typical_cost": 100.0, "acquisition_cost": 100.0, "availability": "COMMON"}], "access": [{"code": "access_transportation", "name": "Transportation Access", "description": "Access to public or private transportation for activities", "resource_type": "ACCESS", "typical_cost": 15.0, "acquisition_cost": 15.0, "availability": "COMMON"}, {"code": "access_walkable_area", "name": "Walkable Area Access", "description": "Access to safe, walkable areas for exploration", "resource_type": "ACCESS", "typical_cost": 0.0, "acquisition_cost": 0.0, "availability": "COMMON"}], "home": [{"code": "home_dining_space", "name": "Dining Space", "description": "Dedicated space for dining and entertaining guests", "resource_type": "HOME", "typical_cost": 0.0, "acquisition_cost": 0.0, "availability": "COMMON"}, {"code": "home_kitchen", "name": "Kitchen Access", "description": "Access to a functional kitchen for cooking activities", "resource_type": "HOME", "typical_cost": 0.0, "acquisition_cost": 0.0, "availability": "COMMON"}, {"code": "office_desk", "name": "Office Desk", "description": "Desk or workspace for office-based activities", "resource_type": "HOME", "typical_cost": 0.0, "acquisition_cost": 200.0, "availability": "COMMON"}], "education": [{"code": "edu_book", "name": "Educational Book", "description": "Book for learning and educational activities", "resource_type": "EDUCATION", "typical_cost": 0.0, "acquisition_cost": 25.0, "availability": "COMMON"}], "specialized": [{"code": "skill_specific_tools", "name": "Skill-Specific Tools", "description": "Specialized tools required for specific skill development", "resource_type": "SPECIALIZED", "typical_cost": 0.0, "acquisition_cost": 100.0, "availability": "UNCOMMON"}, {"code": "specialized_other", "name": "Other Specialized Resource", "description": "Any specialized resource not covered by specific categories", "resource_type": "SPECIALIZED", "typical_cost": 0.0, "acquisition_cost": 50.0, "availability": "VARIABLE"}], "other": [{"code": "tech_other", "name": "Other Technology", "description": "Any technology resource not covered by specific technology categories", "resource_type": "TECHNOLOGY", "typical_cost": 0.0, "acquisition_cost": 100.0, "availability": "VARIABLE"}, {"code": "equip_other", "name": "Other Equipment", "description": "Any equipment or tool not covered by specific equipment categories", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 75.0, "availability": "VARIABLE"}, {"code": "creative_other", "name": "Other Creative Resource", "description": "Any creative material or tool not covered by specific creative categories", "resource_type": "CREATIVE", "typical_cost": 0.0, "acquisition_cost": 50.0, "availability": "VARIABLE"}, {"code": "leisure_other", "name": "Other Leisure Resource", "description": "Any leisure or recreational resource not covered by specific categories", "resource_type": "LEISURE", "typical_cost": 0.0, "acquisition_cost": 40.0, "availability": "VARIABLE"}, {"code": "home_other", "name": "Other Home Resource", "description": "Any home or household resource not covered by specific categories", "resource_type": "HOME", "typical_cost": 0.0, "acquisition_cost": 60.0, "availability": "VARIABLE"}, {"code": "access_other", "name": "Other Access Resource", "description": "Any access, service, or membership not covered by specific categories", "resource_type": "ACCESS", "typical_cost": 25.0, "acquisition_cost": 100.0, "availability": "VARIABLE"}, {"code": "resource_other", "name": "Other Resource", "description": "Any resource, tool, or asset not covered by any specific category", "resource_type": "OTHER", "typical_cost": 0.0, "acquisition_cost": 50.0, "availability": "VARIABLE"}]}}