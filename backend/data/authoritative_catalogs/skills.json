{"metadata": {"version": "1.1.0", "generated_from": "seed_db_50_skill_system.py", "last_updated": "2025-07-02", "description": "Authoritative catalog of skill system components including attributes, definitions, trait influences, and domain applications", "source_files": ["seed_db_50_skill_system.py"], "total_skill_attributes": 26, "total_skill_definitions": 10, "total_trait_influences": 27, "total_domain_applications": 70}, "skill_attributes": {"cognitive": [{"code": "cog_analytical", "name": "Analytical Reasoning", "description": "Ability to break down complex information, identify patterns, and draw logical conclusions.", "base_decay_rate": 20, "development_difficulty": 70, "development_timeframe": "long"}, {"code": "cog_creative", "name": "Creative Thinking", "description": "Ability to generate novel ideas, make unexpected connections, and approach problems from multiple perspectives.", "base_decay_rate": 25, "development_difficulty": 60, "development_timeframe": "medium"}, {"code": "cog_spatial", "name": "Spatial Reasoning", "description": "Ability to visualize and manipulate objects and spatial relationships in the mind.", "base_decay_rate": 30, "development_difficulty": 65, "development_timeframe": "medium"}, {"code": "cog_verbal", "name": "Verbal Processing", "description": "Ability to understand, process, and produce language effectively.", "base_decay_rate": 15, "development_difficulty": 50, "development_timeframe": "long"}, {"code": "cog_numerical", "name": "Numerical Processing", "description": "Ability to work with numbers, understand mathematical concepts, and perform calculations.", "base_decay_rate": 35, "development_difficulty": 75, "development_timeframe": "long"}, {"code": "cog_memory", "name": "Memory Capacity", "description": "Ability to encode, store, and retrieve information effectively.", "base_decay_rate": 40, "development_difficulty": 60, "development_timeframe": "medium"}, {"code": "cog_attention", "name": "Sustained Attention", "description": "Ability to maintain focus on tasks or stimuli over time without distraction.", "base_decay_rate": 45, "development_difficulty": 55, "development_timeframe": "medium"}], "physical": [{"code": "phys_strength", "name": "Physical Strength", "description": "Muscular power and force generation capacity.", "base_decay_rate": 50, "development_difficulty": 60, "development_timeframe": "medium"}, {"code": "phys_endurance", "name": "Physical Endurance", "description": "Ability to sustain physical activity over extended periods.", "base_decay_rate": 45, "development_difficulty": 65, "development_timeframe": "medium"}, {"code": "phys_flexibility", "name": "Flexibility", "description": "Range of motion in joints and muscles.", "base_decay_rate": 40, "development_difficulty": 50, "development_timeframe": "short"}, {"code": "phys_balance", "name": "Balance & Coordination", "description": "Ability to maintain equilibrium and coordinate movements effectively.", "base_decay_rate": 35, "development_difficulty": 65, "development_timeframe": "medium"}, {"code": "phys_fine_motor", "name": "Fine Motor Control", "description": "Precision in small, detailed movements, particularly of the hands and fingers.", "base_decay_rate": 30, "development_difficulty": 70, "development_timeframe": "long"}], "social": [{"code": "soc_empathy", "name": "Empathic Understanding", "description": "Ability to perceive and understand others' emotions, perspectives, and needs.", "base_decay_rate": 20, "development_difficulty": 60, "development_timeframe": "long"}, {"code": "soc_expression", "name": "Emotional Expression", "description": "Ability to convey one's feelings, intentions, and ideas clearly to others.", "base_decay_rate": 25, "development_difficulty": 55, "development_timeframe": "medium"}, {"code": "soc_persuasion", "name": "Persuasive Communication", "description": "Ability to influence others' thoughts or actions through communication.", "base_decay_rate": 30, "development_difficulty": 70, "development_timeframe": "long"}, {"code": "soc_conflict", "name": "Conflict Resolution", "description": "Ability to navigate and resolve interpersonal conflicts constructively.", "base_decay_rate": 35, "development_difficulty": 75, "development_timeframe": "long"}, {"code": "soc_leadership", "name": "Leadership Capacity", "description": "Ability to inspire, direct, and coordinate group efforts toward goals.", "base_decay_rate": 30, "development_difficulty": 80, "development_timeframe": "long"}], "emotional": [{"code": "emot_awareness", "name": "Emotional Awareness", "description": "Ability to recognize and understand one's own emotional states.", "base_decay_rate": 20, "development_difficulty": 65, "development_timeframe": "medium"}, {"code": "emot_regulation", "name": "Emotional Regulation", "description": "Ability to manage emotional responses adaptively in various situations.", "base_decay_rate": 30, "development_difficulty": 75, "development_timeframe": "long"}, {"code": "emot_resilience", "name": "Psychological Resilience", "description": "Ability to adapt to adversity, recover from setbacks, and grow from challenges.", "base_decay_rate": 25, "development_difficulty": 80, "development_timeframe": "long"}, {"code": "emot_mindfulness", "name": "Mindful Awareness", "description": "Ability to maintain present-moment awareness with acceptance and without judgment.", "base_decay_rate": 40, "development_difficulty": 60, "development_timeframe": "medium"}, {"code": "emot_expression", "name": "Emotional Expression", "description": "Ability to express one's personal emotion.", "base_decay_rate": 40, "development_difficulty": 60, "development_timeframe": "medium"}], "creative": [{"code": "create_visual", "name": "Visual Creativity", "description": "Ability to conceptualize and create visually expressive or innovative content.", "base_decay_rate": 30, "development_difficulty": 65, "development_timeframe": "long"}, {"code": "create_verbal", "name": "Verbal Creativity", "description": "Ability to use language in novel, expressive, or evocative ways.", "base_decay_rate": 25, "development_difficulty": 60, "development_timeframe": "medium"}, {"code": "create_musical", "name": "Musical Aptitude", "description": "Sensitivity to and ability to work with musical elements like rhythm, pitch, and harmony.", "base_decay_rate": 35, "development_difficulty": 70, "development_timeframe": "long"}, {"code": "create_bodily", "name": "Kinesthetic Creativity", "description": "Ability to express ideas and emotions through body movement and physical performance.", "base_decay_rate": 40, "development_difficulty": 65, "development_timeframe": "medium"}], "technical": [{"code": "tech_digital", "name": "Digital Literacy", "description": "Ability to use, understand, and adapt to digital tools and technologies.", "base_decay_rate": 40, "development_difficulty": 55, "development_timeframe": "medium"}, {"code": "tech_mechanical", "name": "Mechanical Aptitude", "description": "Understanding of mechanical principles and ability to work with physical systems.", "base_decay_rate": 30, "development_difficulty": 65, "development_timeframe": "medium"}, {"code": "tech_project", "name": "Project Organization", "description": "Ability to plan, organize, and execute complex multi-step projects.", "base_decay_rate": 25, "development_difficulty": 60, "development_timeframe": "medium"}]}, "skill_definitions": [{"code": "creative_writing", "name": "Creative Writing", "description": "Ability to craft engaging written content with originality and expressiveness.", "tags": ["creative", "communication", "artistic"], "attribute_compositions": [{"attribute_code": "create_verbal", "weight": 3.0}, {"attribute_code": "cog_creative", "weight": 2.5}, {"attribute_code": "cog_verbal", "weight": 2.0}, {"attribute_code": "tech_project", "weight": 1.0}, {"attribute_code": "emot_awareness", "weight": 0.5}]}, {"code": "public_speaking", "name": "Public Speaking", "description": "Ability to deliver clear, compelling presentations to audiences of various sizes.", "tags": ["communication", "social", "professional"], "attribute_compositions": [{"attribute_code": "soc_expression", "weight": 3.0}, {"attribute_code": "cog_verbal", "weight": 2.0}, {"attribute_code": "emot_regulation", "weight": 2.0}, {"attribute_code": "soc_persuasion", "weight": 1.5}, {"attribute_code": "cog_attention", "weight": 0.5}]}, {"code": "problem_solving", "name": "Problem Solving", "description": "Ability to identify challenges, analyze possible solutions, and implement effective approaches.", "tags": ["cognitive", "analytical", "professional"], "attribute_compositions": [{"attribute_code": "cog_analytical", "weight": 3.0}, {"attribute_code": "cog_creative", "weight": 2.0}, {"attribute_code": "tech_project", "weight": 1.5}, {"attribute_code": "emot_resilience", "weight": 1.0}, {"attribute_code": "soc_conflict", "weight": 0.5}]}, {"code": "visual_art", "name": "Visual Art Creation", "description": "Ability to express ideas, emotions, and concepts through visual media and techniques.", "tags": ["creative", "artistic", "visual"], "attribute_compositions": [{"attribute_code": "create_visual", "weight": 3.0}, {"attribute_code": "cog_spatial", "weight": 2.0}, {"attribute_code": "phys_fine_motor", "weight": 2.0}, {"attribute_code": "cog_creative", "weight": 1.5}, {"attribute_code": "emot_expression", "weight": 1.0}]}, {"code": "physical_fitness", "name": "Physical Fitness Training", "description": "Knowledge and ability to develop physical capabilities through targeted exercise and recovery.", "tags": ["physical", "health", "lifestyle"], "attribute_compositions": [{"attribute_code": "phys_strength", "weight": 2.5}, {"attribute_code": "phys_endurance", "weight": 2.5}, {"attribute_code": "phys_flexibility", "weight": 1.5}, {"attribute_code": "emot_regulation", "weight": 1.0}, {"attribute_code": "tech_project", "weight": 0.5}]}, {"code": "emotional_intelligence", "name": "Emotional Intelligence", "description": "Ability to recognize, understand, and manage emotions in oneself and others.", "tags": ["social", "emotional", "interpersonal"], "attribute_compositions": [{"attribute_code": "emot_awareness", "weight": 2.5}, {"attribute_code": "soc_empathy", "weight": 2.5}, {"attribute_code": "emot_regulation", "weight": 2.0}, {"attribute_code": "soc_expression", "weight": 1.0}, {"attribute_code": "emot_resilience", "weight": 1.0}]}, {"code": "programming", "name": "Computer Programming", "description": "Ability to design, write, test, and maintain computer code to create software.", "tags": ["technical", "digital", "professional"], "attribute_compositions": [{"attribute_code": "cog_analytical", "weight": 3.0}, {"attribute_code": "tech_digital", "weight": 2.5}, {"attribute_code": "cog_creative", "weight": 1.5}, {"attribute_code": "tech_project", "weight": 1.5}, {"attribute_code": "cog_attention", "weight": 1.0}]}, {"code": "leadership", "name": "Leadership", "description": "Ability to guide, inspire, and coordinate groups toward shared objectives.", "tags": ["social", "professional", "management"], "attribute_compositions": [{"attribute_code": "soc_leadership", "weight": 3.0}, {"attribute_code": "soc_persuasion", "weight": 2.0}, {"attribute_code": "emot_regulation", "weight": 1.5}, {"attribute_code": "tech_project", "weight": 1.5}, {"attribute_code": "soc_empathy", "weight": 1.0}]}, {"code": "meditation", "name": "Meditation Practice", "description": "Ability to cultivate focused attention, awareness, and mental clarity through structured mindfulness techniques.", "tags": ["mindfulness", "emotional", "spiritual"], "attribute_compositions": [{"attribute_code": "emot_mindfulness", "weight": 3.0}, {"attribute_code": "cog_attention", "weight": 2.5}, {"attribute_code": "emot_regulation", "weight": 2.0}, {"attribute_code": "emot_awareness", "weight": 1.5}, {"attribute_code": "emot_resilience", "weight": 1.0}]}, {"code": "negotiation", "name": "Negotiation", "description": "Ability to reach agreements through effective communication, understanding of interests, and strategic problem-solving.", "tags": ["social", "professional", "communication"], "attribute_compositions": [{"attribute_code": "soc_persuasion", "weight": 3.0}, {"attribute_code": "soc_empathy", "weight": 2.0}, {"attribute_code": "cog_analytical", "weight": 1.5}, {"attribute_code": "emot_regulation", "weight": 1.5}, {"attribute_code": "soc_conflict", "weight": 1.0}]}], "trait_influences": [{"trait_code": "open_aesthetic", "attribute_code": "create_visual", "impact": 3}, {"trait_code": "open_aesthetic", "attribute_code": "create_musical", "impact": 2}, {"trait_code": "open_creativity", "attribute_code": "cog_creative", "impact": 3}, {"trait_code": "open_creativity", "attribute_code": "create_verbal", "impact": 2}, {"trait_code": "open_inquisitive", "attribute_code": "cog_analytical", "impact": 2}, {"trait_code": "open_unconventional", "attribute_code": "cog_creative", "impact": 2}, {"trait_code": "consc_organization", "attribute_code": "tech_project", "impact": 3}, {"trait_code": "consc_diligence", "attribute_code": "phys_endurance", "impact": 2}, {"trait_code": "consc_diligence", "attribute_code": "cog_attention", "impact": 3}, {"trait_code": "consc_perfectionism", "attribute_code": "phys_fine_motor", "impact": 2}, {"trait_code": "consc_perfectionism", "attribute_code": "cog_attention", "impact": 1}, {"trait_code": "consc_prudence", "attribute_code": "emot_regulation", "impact": 2}, {"trait_code": "extra_sociability", "attribute_code": "soc_expression", "impact": 3}, {"trait_code": "extra_social_boldness", "attribute_code": "soc_leadership", "impact": 3}, {"trait_code": "extra_self_esteem", "attribute_code": "emot_resilience", "impact": 2}, {"trait_code": "extra_liveliness", "attribute_code": "phys_endurance", "impact": 1}, {"trait_code": "agree_forgiveness", "attribute_code": "soc_conflict", "impact": 3}, {"trait_code": "agree_gentleness", "attribute_code": "soc_empathy", "impact": 2}, {"trait_code": "agree_flexibility", "attribute_code": "emot_regulation", "impact": 1}, {"trait_code": "agree_patience", "attribute_code": "cog_attention", "impact": 1}, {"trait_code": "emotion_anxiety", "attribute_code": "cog_attention", "impact": -1}, {"trait_code": "emotion_anxiety", "attribute_code": "emot_resilience", "impact": -2}, {"trait_code": "emotion_sentimentality", "attribute_code": "soc_empathy", "impact": 3}, {"trait_code": "emotion_fearfulness", "attribute_code": "phys_strength", "impact": -1}, {"trait_code": "honesty_fairness", "attribute_code": "soc_leadership", "impact": 1}, {"trait_code": "honesty_modesty", "attribute_code": "soc_persuasion", "impact": -1}, {"trait_code": "honesty_sincerity", "attribute_code": "soc_empathy", "impact": 1}], "domain_applications": [{"skill_code": "creative_writing", "domain_code": "creative_writing", "relevance": 100, "transfer_coefficient": 1.0, "domain_specific_properties": {}}, {"skill_code": "creative_writing", "domain_code": "creative_visual", "relevance": 30, "transfer_coefficient": 0.7, "domain_specific_properties": {"decay_rate": 20}}, {"skill_code": "creative_writing", "domain_code": "intel_language", "relevance": 70, "transfer_coefficient": 0.9, "domain_specific_properties": {}}, {"skill_code": "public_speaking", "domain_code": "soc_comm", "relevance": 100, "transfer_coefficient": 1.0, "domain_specific_properties": {}}, {"skill_code": "public_speaking", "domain_code": "soc_leadership", "relevance": 70, "transfer_coefficient": 0.8, "domain_specific_properties": {}}, {"skill_code": "public_speaking", "domain_code": "soc_connecting", "relevance": 30, "transfer_coefficient": 0.7, "domain_specific_properties": {}}, {"skill_code": "problem_solving", "domain_code": "intel_problem", "relevance": 100, "transfer_coefficient": 1.0, "domain_specific_properties": {}}, {"skill_code": "problem_solving", "domain_code": "intel_strategic", "relevance": 70, "transfer_coefficient": 0.9, "domain_specific_properties": {}}, {"skill_code": "problem_solving", "domain_code": "prod_career", "relevance": 70, "transfer_coefficient": 0.8, "domain_specific_properties": {}}, {"skill_code": "visual_art", "domain_code": "creative_visual", "relevance": 100, "transfer_coefficient": 1.0, "domain_specific_properties": {}}, {"skill_code": "visual_art", "domain_code": "creative_craft", "relevance": 70, "transfer_coefficient": 0.8, "domain_specific_properties": {}}, {"skill_code": "visual_art", "domain_code": "creative_observation", "relevance": 30, "transfer_coefficient": 0.7, "domain_specific_properties": {}}, {"skill_code": "physical_fitness", "domain_code": "phys_cardio", "relevance": 100, "transfer_coefficient": 1.0, "domain_specific_properties": {}}, {"skill_code": "physical_fitness", "domain_code": "phys_strength", "relevance": 100, "transfer_coefficient": 1.0, "domain_specific_properties": {}}, {"skill_code": "physical_fitness", "domain_code": "phys_flexibility", "relevance": 70, "transfer_coefficient": 0.9, "domain_specific_properties": {}}, {"skill_code": "physical_fitness", "domain_code": "phys_sports", "relevance": 70, "transfer_coefficient": 0.8, "domain_specific_properties": {}}, {"skill_code": "physical_fitness", "domain_code": "emot_stress", "relevance": 30, "transfer_coefficient": 0.6, "domain_specific_properties": {}}, {"skill_code": "emotional_intelligence", "domain_code": "emot_aware", "relevance": 100, "transfer_coefficient": 1.0, "domain_specific_properties": {}}, {"skill_code": "emotional_intelligence", "domain_code": "emot_regulate", "relevance": 100, "transfer_coefficient": 1.0, "domain_specific_properties": {}}, {"skill_code": "emotional_intelligence", "domain_code": "soc_empathy", "relevance": 70, "transfer_coefficient": 0.9, "domain_specific_properties": {}}, {"skill_code": "emotional_intelligence", "domain_code": "soc_connecting", "relevance": 70, "transfer_coefficient": 0.8, "domain_specific_properties": {}}, {"skill_code": "emotional_intelligence", "domain_code": "soc_family", "relevance": 30, "transfer_coefficient": 0.7, "domain_specific_properties": {}}, {"skill_code": "programming", "domain_code": "intel_tech", "relevance": 100, "transfer_coefficient": 1.0, "domain_specific_properties": {}}, {"skill_code": "programming", "domain_code": "intel_problem", "relevance": 70, "transfer_coefficient": 0.8, "domain_specific_properties": {"attribute_modifiers": {"cog_analytical": 1.2}}}, {"skill_code": "programming", "domain_code": "prod_skill", "relevance": 70, "transfer_coefficient": 0.8, "domain_specific_properties": {}}, {"skill_code": "programming", "domain_code": "creative_design", "relevance": 30, "transfer_coefficient": 0.6, "domain_specific_properties": {}}, {"skill_code": "leadership", "domain_code": "soc_leadership", "relevance": 100, "transfer_coefficient": 1.0, "domain_specific_properties": {}}, {"skill_code": "leadership", "domain_code": "prod_career", "relevance": 70, "transfer_coefficient": 0.8, "domain_specific_properties": {}}, {"skill_code": "leadership", "domain_code": "soc_group", "relevance": 70, "transfer_coefficient": 0.9, "domain_specific_properties": {}}, {"skill_code": "leadership", "domain_code": "soc_conflict", "relevance": 30, "transfer_coefficient": 0.7, "domain_specific_properties": {}}, {"skill_code": "meditation", "domain_code": "refl_meditate", "relevance": 100, "transfer_coefficient": 1.0, "domain_specific_properties": {}}, {"skill_code": "meditation", "domain_code": "refl_mindful", "relevance": 100, "transfer_coefficient": 1.0, "domain_specific_properties": {}}, {"skill_code": "meditation", "domain_code": "emot_stress", "relevance": 70, "transfer_coefficient": 0.9, "domain_specific_properties": {}}, {"skill_code": "meditation", "domain_code": "refl_comfort", "relevance": 30, "transfer_coefficient": 0.7, "domain_specific_properties": {}}, {"skill_code": "negotiation", "domain_code": "soc_comm", "relevance": 100, "transfer_coefficient": 1.0, "domain_specific_properties": {}}, {"skill_code": "negotiation", "domain_code": "soc_conflict", "relevance": 100, "transfer_coefficient": 1.0, "domain_specific_properties": {}}, {"skill_code": "negotiation", "domain_code": "prod_career", "relevance": 70, "transfer_coefficient": 0.8, "domain_specific_properties": {}}, {"skill_code": "negotiation", "domain_code": "soc_leadership", "relevance": 30, "transfer_coefficient": 0.7, "domain_specific_properties": {}}]}