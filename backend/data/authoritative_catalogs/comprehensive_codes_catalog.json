{"metadata": {"version": "1.0.0", "generated_at": "2025-06-29T15:13:03.542613", "description": "Comprehensive catalog of all codes used in the Goali system", "source_files": ["environments.json", "resources.json", "domains.json", "user_profile_catalog.json"], "total_codes": 348}, "codes_by_category": {"environments_healthcare": {"description": "Environment codes for healthcare category", "codes": {"ind_healthcare_hospital_ward": {"code": "ind_healthcare_hospital_ward", "name": "Hospital General Ward", "description": "A typical hospital inpatient ward where multiple patients receive care in a shared space with beds separated by curtains or partitions. Features regular monitoring by nursing staff, medical equipment, and limited personal space.", "category": "healthcare", "type": "environment", "source_file": "environments.json"}, "ind_healthcare_private_room": {"code": "ind_healthcare_private_room", "name": "Hospital Private Room", "description": "An individual hospital room designed for a single patient, offering increased privacy, personal space, and often amenities like a private bathroom, television, and space for visitors.", "category": "healthcare", "type": "environment", "source_file": "environments.json"}, "ind_healthcare_icu": {"code": "ind_healthcare_icu", "name": "Intensive Care Unit", "description": "A specialized hospital department providing intensive treatment and monitoring for critically ill patients. Features advanced life support equipment, continuous monitoring, and a high staff-to-patient ratio.", "category": "healthcare", "type": "environment", "source_file": "environments.json"}, "ind_healthcare_waiting_room": {"code": "ind_healthcare_waiting_room", "name": "Medical Waiting Room", "description": "A communal space where patients and their companions wait before appointments or procedures. Usually furnished with chairs, reading materials, and sometimes a television to help pass time.", "category": "healthcare", "type": "environment", "source_file": "environments.json"}, "ind_healthcare_outpatient_clinic": {"code": "ind_healthcare_outpatient_clinic", "name": "Outpatient Clinic", "description": "A medical facility providing diagnosis, care, and treatment for patients who do not require overnight hospitalization. Typically includes examination rooms, specialty equipment, and consultation spaces.", "category": "healthcare", "type": "environment", "source_file": "environments.json"}, "ind_healthcare_physical_therapy": {"code": "ind_healthcare_physical_therapy", "name": "Physical Therapy Center", "description": "A specialized facility equipped for rehabilitation and physical therapy services. Features exercise equipment, treatment tables, and open spaces for movement-based therapies.", "category": "healthcare", "type": "environment", "source_file": "environments.json"}, "ind_healthcare_mental_health_facility": {"code": "ind_healthcare_mental_health_facility", "name": "Mental Health Facility", "description": "A facility designed for the treatment and support of individuals with mental health conditions. Includes therapy rooms, community spaces, and secure environments tailored to various psychiatric needs.", "category": "healthcare", "type": "environment", "source_file": "environments.json"}, "ind_healthcare_dental_office": {"code": "ind_healthcare_dental_office", "name": "Dental Office", "description": "A healthcare facility specialized in oral health, featuring dental chairs, specialized equipment for examination and treatment of teeth and gums, and often x-ray capabilities.", "category": "healthcare", "type": "environment", "source_file": "environments.json"}, "ind_healthcare_laboratory": {"code": "ind_healthcare_laboratory", "name": "Medical Laboratory", "description": "A specialized facility where clinical specimens are analyzed to obtain information about the health of a patient. Features specialized equipment for testing, analyzing samples, and research.", "category": "healthcare", "type": "environment", "source_file": "environments.json"}, "ind_healthcare_imaging_center": {"code": "ind_healthcare_imaging_center", "name": "Medical Imaging Center", "description": "A facility specializing in diagnostic imaging services such as X-rays, MRIs, CT scans, and ultrasounds to help diagnose and monitor medical conditions.", "category": "healthcare", "type": "environment", "source_file": "environments.json"}, "ind_healthcare_pharmacy": {"code": "ind_healthcare_pharmacy", "name": "Pharmacy", "description": "A facility where medications are dispensed, medication information is provided, and sometimes basic health screenings are offered. Features medication storage, consultation areas, and health product displays.", "category": "healthcare", "type": "environment", "source_file": "environments.json"}, "ind_healthcare_hospice": {"code": "ind_healthcare_hospice", "name": "Hospice Care Center", "description": "A facility specializing in care for individuals with terminal illnesses, focusing on comfort, dignity, and quality of life rather than curative treatment. Designed to create a home-like, peaceful environment.", "category": "healthcare", "type": "environment", "source_file": "environments.json"}, "ind_healthcare_emergency_room": {"code": "ind_healthcare_emergency_room", "name": "Emergency Department", "description": "A critical care facility designed for rapid treatment of acute illnesses and injuries. Features specialized equipment for emergency medicine, triage areas, and trauma rooms.", "category": "healthcare", "type": "environment", "source_file": "environments.json"}, "ind_healthcare_rehab_center": {"code": "ind_healthcare_rehab_center", "name": "Rehabilitation Center", "description": "A facility dedicated to helping patients recover physical, mental, and cognitive abilities after illness, injury, or surgery through therapeutic programs and specialized care.", "category": "healthcare", "type": "environment", "source_file": "environments.json"}, "out_healthcare_healing_garden": {"code": "out_healthcare_healing_garden", "name": "Therapeutic Healing Garden", "description": "An outdoor space specifically designed to promote healing and wellbeing within a healthcare setting. Features sensory plants, accessible pathways, seating areas, and elements designed to reduce stress and promote recovery.", "category": "healthcare", "type": "environment", "source_file": "environments.json"}, "ind_healthcare_maternity_ward": {"code": "ind_healthcare_maternity_ward", "name": "Maternity Ward", "description": "A specialized hospital unit devoted to caring for women during childbirth and postpartum recovery, as well as their newborn babies. Features specialized equipment for labor, delivery, and infant care.", "category": "healthcare", "type": "environment", "source_file": "environments.json"}}}, "environments_cultural": {"description": "Environment codes for cultural category", "codes": {"ind_healthcare_hospital_ward": {"code": "ind_healthcare_hospital_ward", "name": "Hospital General Ward", "description": "A typical hospital inpatient ward where multiple patients receive care in a shared space with beds separated by curtains or partitions. Features regular monitoring by nursing staff, medical equipment, and limited personal space.", "category": "cultural", "type": "environment", "source_file": "environments.json"}, "ind_healthcare_private_room": {"code": "ind_healthcare_private_room", "name": "Hospital Private Room", "description": "An individual hospital room designed for a single patient, offering increased privacy, personal space, and often amenities like a private bathroom, television, and space for visitors.", "category": "cultural", "type": "environment", "source_file": "environments.json"}, "ind_healthcare_icu": {"code": "ind_healthcare_icu", "name": "Intensive Care Unit", "description": "A specialized hospital department providing intensive treatment and monitoring for critically ill patients. Features advanced life support equipment, continuous monitoring, and a high staff-to-patient ratio.", "category": "cultural", "type": "environment", "source_file": "environments.json"}, "ind_healthcare_waiting_room": {"code": "ind_healthcare_waiting_room", "name": "Medical Waiting Room", "description": "A communal space where patients and their companions wait before appointments or procedures. Usually furnished with chairs, reading materials, and sometimes a television to help pass time.", "category": "cultural", "type": "environment", "source_file": "environments.json"}, "ind_healthcare_outpatient_clinic": {"code": "ind_healthcare_outpatient_clinic", "name": "Outpatient Clinic", "description": "A medical facility providing diagnosis, care, and treatment for patients who do not require overnight hospitalization. Typically includes examination rooms, specialty equipment, and consultation spaces.", "category": "cultural", "type": "environment", "source_file": "environments.json"}, "ind_healthcare_physical_therapy": {"code": "ind_healthcare_physical_therapy", "name": "Physical Therapy Center", "description": "A specialized facility equipped for rehabilitation and physical therapy services. Features exercise equipment, treatment tables, and open spaces for movement-based therapies.", "category": "cultural", "type": "environment", "source_file": "environments.json"}, "ind_healthcare_mental_health_facility": {"code": "ind_healthcare_mental_health_facility", "name": "Mental Health Facility", "description": "A facility designed for the treatment and support of individuals with mental health conditions. Includes therapy rooms, community spaces, and secure environments tailored to various psychiatric needs.", "category": "cultural", "type": "environment", "source_file": "environments.json"}, "ind_healthcare_dental_office": {"code": "ind_healthcare_dental_office", "name": "Dental Office", "description": "A healthcare facility specialized in oral health, featuring dental chairs, specialized equipment for examination and treatment of teeth and gums, and often x-ray capabilities.", "category": "cultural", "type": "environment", "source_file": "environments.json"}, "ind_healthcare_laboratory": {"code": "ind_healthcare_laboratory", "name": "Medical Laboratory", "description": "A specialized facility where clinical specimens are analyzed to obtain information about the health of a patient. Features specialized equipment for testing, analyzing samples, and research.", "category": "cultural", "type": "environment", "source_file": "environments.json"}, "ind_healthcare_imaging_center": {"code": "ind_healthcare_imaging_center", "name": "Medical Imaging Center", "description": "A facility specializing in diagnostic imaging services such as X-rays, MRIs, CT scans, and ultrasounds to help diagnose and monitor medical conditions.", "category": "cultural", "type": "environment", "source_file": "environments.json"}, "ind_healthcare_pharmacy": {"code": "ind_healthcare_pharmacy", "name": "Pharmacy", "description": "A facility where medications are dispensed, medication information is provided, and sometimes basic health screenings are offered. Features medication storage, consultation areas, and health product displays.", "category": "cultural", "type": "environment", "source_file": "environments.json"}, "ind_healthcare_hospice": {"code": "ind_healthcare_hospice", "name": "Hospice Care Center", "description": "A facility specializing in care for individuals with terminal illnesses, focusing on comfort, dignity, and quality of life rather than curative treatment. Designed to create a home-like, peaceful environment.", "category": "cultural", "type": "environment", "source_file": "environments.json"}, "ind_healthcare_emergency_room": {"code": "ind_healthcare_emergency_room", "name": "Emergency Department", "description": "A critical care facility designed for rapid treatment of acute illnesses and injuries. Features specialized equipment for emergency medicine, triage areas, and trauma rooms.", "category": "cultural", "type": "environment", "source_file": "environments.json"}, "ind_healthcare_rehab_center": {"code": "ind_healthcare_rehab_center", "name": "Rehabilitation Center", "description": "A facility dedicated to helping patients recover physical, mental, and cognitive abilities after illness, injury, or surgery through therapeutic programs and specialized care.", "category": "cultural", "type": "environment", "source_file": "environments.json"}, "out_healthcare_healing_garden": {"code": "out_healthcare_healing_garden", "name": "Therapeutic Healing Garden", "description": "An outdoor space specifically designed to promote healing and wellbeing within a healthcare setting. Features sensory plants, accessible pathways, seating areas, and elements designed to reduce stress and promote recovery.", "category": "cultural", "type": "environment", "source_file": "environments.json"}, "ind_healthcare_maternity_ward": {"code": "ind_healthcare_maternity_ward", "name": "Maternity Ward", "description": "A specialized hospital unit devoted to caring for women during childbirth and postpartum recovery, as well as their newborn babies. Features specialized equipment for labor, delivery, and infant care.", "category": "cultural", "type": "environment", "source_file": "environments.json"}}}, "environments_transportation": {"description": "Environment codes for transportation category", "codes": {"ind_cultural_museum": {"code": "ind_cultural_museum", "name": "Museum", "description": "An indoor space dedicated to the preservation and exhibition of artistic, cultural, historical, or scientific artifacts. Museums provide a quiet, contemplative environment for learning and appreciation.", "category": "transportation", "type": "environment", "source_file": "environments.json"}, "ind_cultural_artgallery": {"code": "ind_cultural_artgallery", "name": "Art Gallery", "description": "A dedicated indoor space for displaying visual art, primarily paintings, sculptures, photographs, and other aesthetic creations. Galleries provide a focused environment for artistic appreciation and contemplation.", "category": "transportation", "type": "environment", "source_file": "environments.json"}, "ind_cultural_theater": {"code": "ind_cultural_theater", "name": "Theater", "description": "An indoor venue designed for live performances such as plays, musicals, dance, or other staged productions. Theaters feature seating arrangements focused on a central stage or performance space.", "category": "transportation", "type": "environment", "source_file": "environments.json"}, "ind_cultural_concerthall": {"code": "ind_cultural_concerthall", "name": "Concert Hall", "description": "A specialized indoor venue designed for musical performances, featuring exceptional acoustics and seating oriented toward a central stage. Concert halls provide an immersive audio experience for audience members.", "category": "transportation", "type": "environment", "source_file": "environments.json"}, "ind_cultural_library": {"code": "ind_cultural_library", "name": "Library", "description": "An indoor space dedicated to the collection and preservation of books, periodicals, and other media. Libraries provide quiet environments for reading, research, study, and community programs.", "category": "transportation", "type": "environment", "source_file": "environments.json"}, "ind_cultural_cinemacomplex": {"code": "ind_cultural_cinemacomplex", "name": "Cinema Complex", "description": "An indoor facility with multiple screening rooms for showing films. Cinema complexes include comfortable seating, large screens, and advanced audio systems for immersive viewing experiences.", "category": "transportation", "type": "environment", "source_file": "environments.json"}, "ind_cultural_artisanworkshop": {"code": "ind_cultural_artisanworkshop", "name": "Artisan Workshop", "description": "An indoor space equipped for creating handcrafted work in traditional arts, crafts, or trades. Workshops typically include specialized tools, materials, and workstations for various creative practices.", "category": "transportation", "type": "environment", "source_file": "environments.json"}, "ind_cultural_dancestudio": {"code": "ind_cultural_dancestudio", "name": "Dance Studio", "description": "An indoor space designed specifically for dance practice, training, and rehearsal. Dance studios feature appropriate flooring, mirrors, barres, and sound systems to support various dance forms.", "category": "transportation", "type": "environment", "source_file": "environments.json"}, "ind_cultural_recordingstudio": {"code": "ind_cultural_recordingstudio", "name": "Recording Studio", "description": "A specially designed indoor space for recording audio with controlled acoustics, sound insulation, and professional recording equipment. Recording studios provide isolated environments for music production, voiceover, and other audio work.", "category": "transportation", "type": "environment", "source_file": "environments.json"}, "ind_cultural_culinaryarts": {"code": "ind_cultural_culinaryarts", "name": "Culinary Arts Center", "description": "An indoor facility dedicated to cooking education, demonstrations, and cultural food experiences. Culinary centers include cooking stations, specialized equipment, and areas for instruction and tasting.", "category": "transportation", "type": "environment", "source_file": "environments.json"}, "out_cultural_amphitheater": {"code": "out_cultural_amphitheater", "name": "Outdoor Amphitheater", "description": "An open-air venue with a central performance space surrounded by tiered seating. Amphitheaters are designed for theatrical performances, concerts, and other cultural events in natural settings.", "category": "transportation", "type": "environment", "source_file": "environments.json"}, "out_cultural_heritage": {"code": "out_cultural_heritage", "name": "Heritage Site", "description": "An outdoor location of historical or cultural significance, often preserved as a landmark or monument. Heritage sites include ruins, historic buildings, archaeological sites, and culturally important landscapes.", "category": "transportation", "type": "environment", "source_file": "environments.json"}, "out_cultural_sculpture": {"code": "out_cultural_sculpture", "name": "Sculpture Garden", "description": "An outdoor area dedicated to displaying three-dimensional artworks in a landscaped setting. Sculpture gardens combine artistic appreciation with natural elements for a multisensory experience.", "category": "transportation", "type": "environment", "source_file": "environments.json"}, "out_cultural_festival": {"code": "out_cultural_festival", "name": "Festival Grounds", "description": "Temporary or permanent outdoor spaces designed for cultural festivals, musical events, and community celebrations. Festival grounds typically accommodate large crowds with multiple activity areas.", "category": "transportation", "type": "environment", "source_file": "environments.json"}, "ind_cultural_arthouse": {"code": "ind_cultural_arthouse", "name": "Art House Cinema", "description": "A specialized indoor cinema focusing on independent, foreign, documentary, or experimental films. Art house cinemas provide spaces for viewing films outside mainstream commercial releases.", "category": "transportation", "type": "environment", "source_file": "environments.json"}, "ind_cultural_operahouse": {"code": "ind_cultural_operahouse", "name": "Opera House", "description": "A grand indoor venue specifically designed for operatic performances, featuring exceptional acoustics, elaborate stage facilities, and formal audience seating. Opera houses embody cultural tradition and artistic excellence.", "category": "transportation", "type": "environment", "source_file": "environments.json"}, "out_cultural_openairdance": {"code": "out_cultural_openairdance", "name": "Open-Air Dance Plaza", "description": "A public outdoor space designed or adapted for community dance events, often featuring a central dance floor with surrounding seating areas. These spaces encourage spontaneous or organized social dancing.", "category": "transportation", "type": "environment", "source_file": "environments.json"}, "ind_cultural_historicalarchive": {"code": "ind_cultural_historicalarchive", "name": "Historical Archive", "description": "A dedicated indoor facility for preserving, organizing, and providing access to historical documents, records, and artifacts. Archives maintain controlled environments to protect sensitive materials while enabling research.", "category": "transportation", "type": "environment", "source_file": "environments.json"}, "ind_cultural_musicschool": {"code": "ind_cultural_musicschool", "name": "Music School", "description": "An educational facility dedicated to music instruction featuring practice rooms, classrooms, performance spaces, and specialized equipment. Music schools provide environments optimized for learning and practicing musical skills.", "category": "transportation", "type": "environment", "source_file": "environments.json"}, "ind_cultural_literarysalon": {"code": "ind_cultural_literarysalon", "name": "Literary Salon", "description": "An intimate indoor space dedicated to literary discussion, readings, and intellectual exchange. Literary salons provide comfortable seating, appropriate acoustics, and an atmosphere conducive to sharing and discussing written works.", "category": "transportation", "type": "environment", "source_file": "environments.json"}}}, "environments_residential": {"description": "Environment codes for residential category", "codes": {"ind_residential_living_room": {"code": "ind_residential_living_room", "name": "Living Room", "description": "The main social space in a home, typically featuring comfortable seating, entertainment options, and decorative elements. Serves as a gathering place for family and guests.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "ind_residential_bedroom": {"code": "ind_residential_bedroom", "name": "Bedroom", "description": "A private space primarily for sleeping and personal activities. Often personalized to reflect individual tastes and preferences, serving as a retreat from shared household spaces.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "ind_residential_home_office": {"code": "ind_residential_home_office", "name": "Home Office", "description": "A dedicated workspace within a residential setting, designed for productivity and focus. Typically equipped with work essentials and minimized distractions.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "ind_residential_kitchen": {"code": "ind_residential_kitchen", "name": "Kitchen", "description": "The culinary heart of the home where meals are prepared and often shared. Combines functionality with social aspects of food preparation and consumption.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "ind_residential_dining_room": {"code": "ind_residential_dining_room", "name": "Dining Room", "description": "A dedicated space for formal and family meals. Combines functionality with social interaction around food consumption and conversation.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "ind_residential_bathroom": {"code": "ind_residential_bathroom", "name": "Bathroom", "description": "A private space for personal hygiene and self-care routines. Combines functionality with comfort elements that support wellness activities.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "ind_residential_basement": {"code": "ind_residential_basement", "name": "Basement", "description": "A below-ground level space in a home, often used for storage, recreation, or as a flexible multi-purpose area. Typically less finished than main living areas.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "ind_residential_attic": {"code": "ind_residential_attic", "name": "Attic", "description": "An upper-level space below the roof, often used for storage or converted into living space. Characterized by sloped ceilings and sometimes limited accessibility.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "ind_residential_hallway": {"code": "ind_residential_hallway", "name": "Hallway", "description": "A transitional space connecting different rooms in a home. Primarily functional but often decorated with personal items or artwork.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "ind_residential_guest_room": {"code": "ind_residential_guest_room", "name": "Guest Room", "description": "A bedroom specifically designated for visitors. Often neutral in decor and equipped with essentials for temporary stays.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "out_residential_backyard": {"code": "out_residential_backyard", "name": "Backyard", "description": "The private outdoor space behind a residential dwelling. Often used for recreation, relaxation, gardening, and outdoor entertaining.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "out_residential_front_yard": {"code": "out_residential_front_yard", "name": "Front Yard", "description": "The outdoor space between a residential dwelling and the street. Functions as both a transitional space and a public-facing representation of the home.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "ind_residential_sunroom": {"code": "ind_residential_sunroom", "name": "Sunroom", "description": "A room with extensive window area designed to allow in abundant natural light, often serving as a transitional space between indoors and outdoors.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "out_residential_patio_deck": {"code": "out_residential_patio_deck", "name": "Patio/Deck", "description": "An outdoor living space attached to a residence, typically featuring hard surfaces and designed for outdoor dining, entertaining, and relaxation.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "ind_residential_laundry_room": {"code": "ind_residential_laundry_room", "name": "Laundry Room", "description": "A utility space dedicated to washing, drying, and sometimes folding clothes. Primarily functional but increasingly designed for comfort and efficiency.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "ind_residential_home_gym": {"code": "ind_residential_home_gym", "name": "Home Gym", "description": "A dedicated space for physical exercise within a residence, equipped with fitness equipment and often designed to motivate physical activity.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "ind_residential_hobby_room": {"code": "ind_residential_hobby_room", "name": "Hobby Room", "description": "A space dedicated to creative and leisure pursuits, customized for specific interests such as crafting, model building, or artistic endeavors.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "ind_residential_game_room": {"code": "ind_residential_game_room", "name": "Game Room", "description": "A recreational space designed for entertainment through games, featuring video games, board games, or recreational equipment like pool tables or ping pong.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "out_residential_garden": {"code": "out_residential_garden", "name": "Garden", "description": "A cultivated outdoor space dedicated to growing plants, whether ornamental, edible, or both. Provides connection with natural cycles and opportunities for nurturing living things.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "ind_residential_meditation_space": {"code": "ind_residential_meditation_space", "name": "Meditation Space", "description": "A quiet area specifically designated for mindfulness, meditation, or spiritual practices. Designed to minimize distractions and foster inner calm.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "ind_residential_reading_nook": {"code": "ind_residential_reading_nook", "name": "Reading Nook", "description": "A small, cozy corner designed specifically for reading and quiet contemplation. Often featuring comfortable seating and good lighting in a secluded area.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "ind_quiet_space": {"code": "ind_quiet_space", "name": "Quiet Indoor Space", "description": "A flexible indoor environment with minimal noise and distractions, suitable for focused activities such as meditation, reading, or brief wellness practices.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "ind_micro_space": {"code": "ind_micro_space", "name": "Micro-Activity Space", "description": "Very small spaces suitable for brief activities such as desk areas, corners of rooms, or even standing spaces. Designed for 2-15 minute wellness breaks.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "ind_digital_enabled": {"code": "ind_digital_enabled", "name": "Digital-Enabled Indoor Space", "description": "Indoor space equipped with digital devices and internet connectivity for hybrid digital-physical activities such as app-guided workouts or virtual reality experiences.", "category": "residential", "type": "environment", "source_file": "environments.json"}}}, "environments_natural": {"description": "Environment codes for natural category", "codes": {"out_nat_forest": {"code": "out_nat_forest", "name": "Forest", "description": "A wooded area with a variety of trees, plants, and wildlife. Offers natural shade, biodiversity, and opportunities for hiking, wildlife observation, and nature connection.", "category": "natural", "type": "environment", "source_file": "environments.json"}, "out_nat_beach": {"code": "out_nat_beach", "name": "Beach", "description": "A sandy or rocky shoreline along a body of water (ocean, sea, lake, or river). Provides open space, water access, and sensory experiences including the sounds of waves and the feeling of sand underfoot.", "category": "natural", "type": "environment", "source_file": "environments.json"}, "out_nat_mountain": {"code": "out_nat_mountain", "name": "Mountain", "description": "Elevated terrain characterized by steep slopes, peaks, and valleys. Offers challenging physical activities, breathtaking views, and a sense of accomplishment when summits are reached.", "category": "natural", "type": "environment", "source_file": "environments.json"}, "out_nat_meadow": {"code": "out_nat_meadow", "name": "Meadow", "description": "An open grassland area with wildflowers and low vegetation. Provides wide-open space, contact with diverse plant life, and opportunities for relaxation and nature observation.", "category": "natural", "type": "environment", "source_file": "environments.json"}, "out_nat_desert": {"code": "out_nat_desert", "name": "Desert", "description": "Arid landscape with minimal vegetation, characterized by sand, rocks, and extreme temperature variations. Offers unique geological features, wide-open spaces, and clarity of night skies.", "category": "natural", "type": "environment", "source_file": "environments.json"}, "out_nat_wetland": {"code": "out_nat_wetland", "name": "Wetland", "description": "Areas saturated with water, such as marshes, swamps, and bogs. Rich in biodiversity, these environments offer unique plant and animal observation opportunities and peaceful water features.", "category": "natural", "type": "environment", "source_file": "environments.json"}, "out_nat_river": {"code": "out_nat_river", "name": "River", "description": "Flowing body of water with surrounding riverbanks and shoreline. Provides opportunities for water activities, wildlife observation, and the soothing sounds of flowing water.", "category": "natural", "type": "environment", "source_file": "environments.json"}, "out_nat_lake": {"code": "out_nat_lake", "name": "Lake", "description": "Still body of water surrounded by shoreline and natural features. Offers calm water activities, scenic views, and a peaceful atmosphere for reflection and recreation.", "category": "natural", "type": "environment", "source_file": "environments.json"}, "out_nat_cliffside": {"code": "out_nat_cliffside", "name": "Cliffside", "description": "Steep rock face overlooking the surrounding landscape, often with dramatic views. Provides perspectives on vast expanses, challenging activities, and a sense of exposure to the elements.", "category": "natural", "type": "environment", "source_file": "environments.json"}, "out_nat_garden": {"code": "out_nat_garden", "name": "Garden", "description": "Cultivated outdoor space with designed plantings, paths, and features. Combines natural elements with human design to create a space for relaxation, observation, and engagement with plants.", "category": "natural", "type": "environment", "source_file": "environments.json"}, "out_nat_tundra": {"code": "out_nat_tundra", "name": "<PERSON><PERSON>", "description": "Cold, treeless plain with permafrost underground. Characterized by vast open spaces, extreme conditions, and unique flora and fauna adapted to harsh environments.", "category": "natural", "type": "environment", "source_file": "environments.json"}, "out_nat_savanna": {"code": "out_nat_savanna", "name": "Savanna", "description": "Grassland ecosystem with scattered trees and diverse wildlife. Provides open vistas, wildlife observation opportunities, and connection to primal landscapes.", "category": "natural", "type": "environment", "source_file": "environments.json"}, "ind_nat_conservatory": {"code": "ind_nat_conservatory", "name": "Conservatory", "description": "Indoor space with glass walls and ceiling designed to house and display plants. Provides a controlled natural environment for plant observation, learning, and relaxation in all weather conditions.", "category": "natural", "type": "environment", "source_file": "environments.json"}, "ind_nat_atrium": {"code": "ind_nat_atrium", "name": "Indoor Atrium", "description": "Open central space within a building, often featuring plants, water features, and natural light. Combines architectural elements with natural features to create a transitional space between indoors and outdoors.", "category": "natural", "type": "environment", "source_file": "environments.json"}, "ind_nat_botanical_garden": {"code": "ind_nat_botanical_garden", "name": "Indoor Botanical Garden", "description": "Large indoor facility dedicated to plant collection, cultivation, and display. Offers educational opportunities, diverse plant experiences, and calm natural spaces regardless of outside weather.", "category": "natural", "type": "environment", "source_file": "environments.json"}, "ind_nat_aquarium": {"code": "ind_nat_aquarium", "name": "Aquarium", "description": "Indoor facility housing aquatic life in tanks and habitats. Provides opportunities to observe underwater ecosystems and marine life in a controlled environment with educational components.", "category": "natural", "type": "environment", "source_file": "environments.json"}, "ind_nat_indoor_waterfall": {"code": "ind_nat_indoor_waterfall", "name": "Indoor Waterfall Environment", "description": "Built environment featuring artificial waterfalls, streams, and related natural elements. Combines moving water, plants, and designed spaces to create a sensory-rich natural experience indoors.", "category": "natural", "type": "environment", "source_file": "environments.json"}, "out_nat_volcanic_area": {"code": "out_nat_volcanic_area", "name": "Volcanic Area", "description": "Landscape shaped by volcanic activity, featuring lava fields, geothermal features, and unique geology. Offers otherworldly scenery, dynamic natural processes, and educational opportunities about Earth\\'s forces.", "category": "natural", "type": "environment", "source_file": "environments.json"}, "out_nat_canyon": {"code": "out_nat_canyon", "name": "Canyon", "description": "Deep ravine between cliffs often carved by rivers over millennia. Provides dramatic vertical perspectives, challenging terrain, and exposure to geological history through visible rock layers.", "category": "natural", "type": "environment", "source_file": "environments.json"}, "out_nat_farm": {"code": "out_nat_farm", "name": "Rural Farm", "description": "Agricultural land with crops, livestock, and farm buildings. Provides connection to food production, seasonal cycles, and rural lifestyle. Features open fields, barns, and agricultural activities.", "category": "natural", "type": "environment", "source_file": "environments.json"}, "out_nat_coral_reef": {"code": "out_nat_coral_reef", "name": "Coral Reef", "description": "Underwater ecosystem built by colonies of coral polyps, teeming with marine life. Provides immersive aquatic experiences, colorful visual stimuli, and opportunities to observe complex ecological relationships.", "category": "natural", "type": "environment", "source_file": "environments.json"}, "ind_nat_terrarium": {"code": "ind_nat_terrarium", "name": "Terrarium Room", "description": "Space featuring multiple enclosed mini-ecosystems in glass containers. Provides opportunities to observe self-contained natural worlds and intricate ecological relationships at a small scale.", "category": "natural", "type": "environment", "source_file": "environments.json"}}}, "environments_recreational": {"description": "Environment codes for recreational category", "codes": {"ind_rec_gym": {"code": "ind_rec_gym", "name": "Fitness Gym", "description": "A dedicated indoor facility with various exercise equipment, weight machines, cardio stations, and possibly group exercise rooms. Designed for physical conditioning, strength training, and general fitness activities.", "category": "recreational", "type": "environment", "source_file": "environments.json"}, "ind_rec_yoga_studio": {"code": "ind_rec_yoga_studio", "name": "Yoga Studio", "description": "A tranquil indoor space designed for yoga practice, meditation, and mindful movement. Features cushioned flooring, minimal aesthetic, and a calming atmosphere conducive to focus and inner awareness.", "category": "recreational", "type": "environment", "source_file": "environments.json"}, "ind_rec_bowling": {"code": "ind_rec_bowling", "name": "Bowling Alley", "description": "An indoor recreational facility featuring lanes for bowling, automatic scoring systems, and rental equipment. Often includes arcade games, food service, and social areas designed for casual entertainment.", "category": "recreational", "type": "environment", "source_file": "environments.json"}, "ind_rec_arcade": {"code": "ind_rec_arcade", "name": "Gaming Arcade", "description": "An indoor entertainment center filled with video games, redemption games, and interactive gaming experiences. Designed for casual play, competition, and social interaction around digital entertainment.", "category": "recreational", "type": "environment", "source_file": "environments.json"}, "ind_rec_movie_theater": {"code": "ind_rec_movie_theater", "name": "Movie Theater", "description": "An indoor venue designed for film viewing with large screens, immersive sound systems, and tiered seating. Offers a shared viewing experience in a controlled environment optimized for audiovisual entertainment.", "category": "recreational", "type": "environment", "source_file": "environments.json"}, "ind_rec_pool_hall": {"code": "ind_rec_pool_hall", "name": "Billiards Club", "description": "An indoor recreational space centered around billiards tables and sometimes featuring other table games like foosball or darts. Combines skill-based gaming with social interaction in a casual atmosphere.", "category": "recreational", "type": "environment", "source_file": "environments.json"}, "ind_rec_indoor_climbing": {"code": "ind_rec_indoor_climbing", "name": "Indoor Climbing Gym", "description": "An indoor facility with artificial climbing walls of various heights and difficulties, featuring holds, routes, and safety systems. Designed for sport climbing, bouldering, and training in a controlled environment.", "category": "recreational", "type": "environment", "source_file": "environments.json"}, "ind_rec_swimming_pool": {"code": "ind_rec_swimming_pool", "name": "Indoor Swimming Pool", "description": "A climate-controlled aquatic facility with one or more swimming pools designed for exercise, recreation, and instruction. May include lanes for lap swimming, diving areas, and shallow sections for casual use.", "category": "recreational", "type": "environment", "source_file": "environments.json"}, "ind_rec_dance_studio": {"code": "ind_rec_dance_studio", "name": "Dance Studio", "description": "A specialized indoor space for dance practice and instruction, featuring mirrors, barres, appropriate flooring, and sound systems. Designed for movement arts ranging from ballet to contemporary styles.", "category": "recreational", "type": "environment", "source_file": "environments.json"}, "ind_rec_board_game_cafe": {"code": "ind_rec_board_game_cafe", "name": "Board Game Café", "description": "A cozy indoor venue combining café service with an extensive library of board games, card games, and tabletop activities. Designed for social gaming experiences in a comfortable setting with food and beverage options.", "category": "recreational", "type": "environment", "source_file": "environments.json"}, "ind_rec_trampoline_park": {"code": "ind_rec_trampoline_park", "name": "Trampoline Park", "description": "An indoor recreational facility featuring interconnected trampolines, foam pits, dodgeball courts, and other bounce-based activities. Designed for high-energy play, physical activity, and adventurous recreation.", "category": "recreational", "type": "environment", "source_file": "environments.json"}, "out_rec_playground": {"code": "out_rec_playground", "name": "Public Playground", "description": "An outdoor recreational space designed primarily for children, featuring play equipment like swings, slides, climbing structures, and open areas. Promotes physical activity, social interaction, and imaginative play in a public setting.", "category": "recreational", "type": "environment", "source_file": "environments.json"}, "out_rec_skatepark": {"code": "out_rec_skatepark", "name": "Skate Park", "description": "An outdoor facility designed specifically for skateboarding, BMX riding, and sometimes inline skating. Features ramps, rails, pipes, bowls, and other structures for performing tricks and developing skills in action sports.", "category": "recreational", "type": "environment", "source_file": "environments.json"}, "out_rec_sports_field": {"code": "out_rec_sports_field", "name": "Sports Field Complex", "description": "An outdoor facility with multiple fields and courts for organized sports and casual play. May include soccer fields, baseball diamonds, tennis courts, and supporting amenities designed for team and individual sports activities.", "category": "recreational", "type": "environment", "source_file": "environments.json"}, "out_rec_beach": {"code": "out_rec_beach", "name": "Public Beach", "description": "A recreational shoreline area where land meets a body of water, typically featuring sand or pebbles, swimming areas, and various amenities. Supports swimming, sunbathing, beach sports, and passive enjoyment of the natural water setting.", "category": "recreational", "type": "environment", "source_file": "environments.json"}, "out_rec_golf_course": {"code": "out_rec_golf_course", "name": "Golf Course", "description": "A large outdoor facility designed for playing golf, featuring manicured grass areas, hazards, putting greens, and typically 9 or 18 holes. Combines elements of sport, leisure, social interaction, and appreciation of landscaped natural settings.", "category": "recreational", "type": "environment", "source_file": "environments.json"}, "out_rec_hiking_trail": {"code": "out_rec_hiking_trail", "name": "Hiking Trail System", "description": "A network of outdoor paths designed for pedestrian use through natural environments, with varying degrees of development, markers, and amenities. Facilitates exercise, nature exploration, and scenic appreciation.", "category": "recreational", "type": "environment", "source_file": "environments.json"}, "out_rec_dog_park": {"code": "out_rec_dog_park", "name": "Dog Park", "description": "A designated outdoor area where dogs can exercise and play off-leash in a controlled environment under owner supervision. Features fencing, waste stations, water sources, and sometimes separate areas for different dog sizes or temperaments.", "category": "recreational", "type": "environment", "source_file": "environments.json"}, "out_rec_water_park": {"code": "out_rec_water_park", "name": "Outdoor Water Park", "description": "A recreational facility featuring water-based attractions such as slides, wave pools, lazy rivers, and splash pads. Designed for aquatic play and cooling recreation during warm weather.", "category": "recreational", "type": "environment", "source_file": "environments.json"}, "out_rec_outdoor_fitness": {"code": "out_rec_outdoor_fitness", "name": "Outdoor Fitness Area", "description": "A public exercise zone featuring weather-resistant equipment designed for strength training, flexibility, and cardiovascular workouts. Allows for free, accessible physical activity in an open-air environment.", "category": "recreational", "type": "environment", "source_file": "environments.json"}, "ind_rec_escape_room": {"code": "ind_rec_escape_room", "name": "Escape Room Facility", "description": "An immersive indoor entertainment venue where teams solve puzzles, find clues, and complete objectives to \"escape\" from themed rooms within a time limit. Combines problem-solving with storytelling and team collaboration.", "category": "recreational", "type": "environment", "source_file": "environments.json"}}}, "environments_commercial": {"description": "Environment codes for commercial category", "codes": {"ind_comm_cafe": {"code": "ind_comm_cafe", "name": "Café", "description": "A cozy indoor café environment with seating, ambient music, and a social atmosphere. Cafés typically offer beverages, light meals, and often provide Wi-Fi access for patrons who wish to work or socialize.", "category": "commercial", "type": "environment", "source_file": "environments.json"}, "ind_comm_restaurant": {"code": "ind_comm_restaurant", "name": "Restaurant", "description": "An establishment where meals are prepared and served to customers, ranging from casual diners to fine dining experiences. Restaurants provide tables, seating, and service staff in a controlled indoor environment.", "category": "commercial", "type": "environment", "source_file": "environments.json"}, "ind_comm_retail_store": {"code": "ind_comm_retail_store", "name": "Retail Store", "description": "A commercial space designed for selling goods to consumers. Retail stores feature display areas, merchandise arranged for browsing, checkout counters, and sometimes fitting rooms or testing areas for products.", "category": "commercial", "type": "environment", "source_file": "environments.json"}, "ind_comm_mall": {"code": "ind_comm_mall", "name": "Shopping Mall", "description": "A large indoor complex housing multiple retail stores, restaurants, and sometimes entertainment venues. Malls typically feature wide corridors for foot traffic, common areas, food courts, and various amenities for shoppers.", "category": "commercial", "type": "environment", "source_file": "environments.json"}, "ind_comm_supermarket": {"code": "ind_comm_supermarket", "name": "Supermarket", "description": "A large retail store specializing in groceries and household items, with organized aisles, refrigerated sections, and checkout areas. Supermarkets provide a self-service shopping experience for food and daily necessities.", "category": "commercial", "type": "environment", "source_file": "environments.json"}, "ind_comm_bookstore": {"code": "ind_comm_bookstore", "name": "Bookstore", "description": "A retail establishment specializing in books, with shelves organized by genre or topic, reading areas, and sometimes a café section. Bookstores provide a quiet, browsing-friendly environment for literature enthusiasts.", "category": "commercial", "type": "environment", "source_file": "environments.json"}, "ind_comm_cinema": {"code": "ind_comm_cinema", "name": "Movie Theater", "description": "An entertainment venue with screening rooms featuring large screens, seating arranged in rows, and advanced sound systems for showing films. Movie theaters also typically include concession stands and lobby areas.", "category": "commercial", "type": "environment", "source_file": "environments.json"}, "ind_comm_salon_spa": {"code": "ind_comm_salon_spa", "name": "Salon & Spa", "description": "A service-oriented business providing beauty treatments, hair styling, and wellness services. Salon and spa environments feature treatment stations, relaxation areas, and specialized equipment for various beauty and wellness services.", "category": "commercial", "type": "environment", "source_file": "environments.json"}, "ind_comm_hotel_lobby": {"code": "ind_comm_hotel_lobby", "name": "Hotel Lobby", "description": "The entrance and common area of a hotel, featuring reception desks, seating areas, and sometimes amenities like bars or restaurants. Hotel lobbies are designed to welcome guests and provide communal spaces for relaxation and casual meetings.", "category": "commercial", "type": "environment", "source_file": "environments.json"}, "ind_comm_arcade": {"code": "ind_comm_arcade", "name": "Gaming Arcade", "description": "An entertainment venue filled with video games, redemption games, and sometimes physical activities like bowling or mini-golf. Arcades feature a lively atmosphere with colorful lighting, game sounds, and various interactive entertainment options.", "category": "commercial", "type": "environment", "source_file": "environments.json"}, "ind_comm_bar_nightclub": {"code": "ind_comm_bar_nightclub", "name": "Bar or Nightclub", "description": "An establishment serving alcoholic beverages, often featuring music, dancing areas, and social spaces. Bars and nightclubs are designed for evening entertainment, socializing, and sometimes live performances.", "category": "commercial", "type": "environment", "source_file": "environments.json"}, "ind_comm_cowork_space": {"code": "ind_comm_cowork_space", "name": "Coworking Space", "description": "A shared working environment that provides desks, offices, meeting rooms, and amenities for independent professionals and small teams. Coworking spaces combine productive work environments with community-building elements.", "category": "commercial", "type": "environment", "source_file": "environments.json"}, "out_comm_market": {"code": "out_comm_market", "name": "Outdoor Market", "description": "An open-air commercial space with multiple vendors selling goods from stalls or booths. Outdoor markets may specialize in produce, crafts, antiques, or offer a variety of merchandise in a vibrant, community-oriented setting.", "category": "commercial", "type": "environment", "source_file": "environments.json"}, "out_comm_shopping_street": {"code": "out_comm_shopping_street", "name": "Shopping Street", "description": "A public thoroughfare lined with retail shops, restaurants, and other commercial establishments. Shopping streets combine pedestrian access with commercial offerings in an open, urban environment.", "category": "commercial", "type": "environment", "source_file": "environments.json"}, "out_comm_food_truck_park": {"code": "out_comm_food_truck_park", "name": "Food Truck Park", "description": "An outdoor venue where multiple mobile food vendors gather, typically featuring seating areas, sometimes entertainment, and a variety of cuisine options. Food truck parks offer casual dining in a social, often festive atmosphere.", "category": "commercial", "type": "environment", "source_file": "environments.json"}, "ind_comm_fitness_center": {"code": "ind_comm_fitness_center", "name": "Fitness Center", "description": "A facility equipped with exercise machines, weights, and sometimes group fitness rooms and pools for physical activity. Fitness centers provide structured environments for workouts, training, and health improvement.", "category": "commercial", "type": "environment", "source_file": "environments.json"}}}, "environments_educational": {"description": "Environment codes for educational category", "codes": {"ind_edu_classroom": {"code": "ind_edu_classroom", "name": "Traditional Classroom", "description": "A standard classroom setting with desks arranged in rows or groups, a whiteboard/blackboard, and typical educational materials. This environment is designed for structured learning activities with teacher-led instruction and student participation.", "category": "educational", "type": "environment", "source_file": "environments.json"}, "ind_edu_lecture_hall": {"code": "ind_edu_lecture_hall", "name": "University Lecture Hall", "description": "A large tiered auditorium-style room designed for lectures and presentations to large groups of students. Often features fixed seating with small writing surfaces and excellent sight lines to the presenter area.", "category": "educational", "type": "environment", "source_file": "environments.json"}, "ind_edu_lab_science": {"code": "ind_edu_lab_science", "name": "Science Laboratory", "description": "A specialized classroom equipped with scientific equipment for practical experiments and demonstrations. Features lab benches, sinks, safety equipment, and storage for materials and specimens.", "category": "educational", "type": "environment", "source_file": "environments.json"}, "ind_edu_computer_lab": {"code": "ind_edu_computer_lab", "name": "Computer Laboratory", "description": "A dedicated room filled with computer workstations arranged for individual or collaborative work. Designed for digital learning, programming, research, and media production activities.", "category": "educational", "type": "environment", "source_file": "environments.json"}, "ind_edu_art_studio": {"code": "ind_edu_art_studio", "name": "Art Studio Classroom", "description": "A bright, open space designed for visual arts instruction and creation. Features easels, tables, sinks, storage for materials, and display areas for works in progress and completed pieces.", "category": "educational", "type": "environment", "source_file": "environments.json"}, "ind_edu_music_room": {"code": "ind_edu_music_room", "name": "Music Classroom", "description": "A specialized classroom for music instruction and practice, with acoustical treatments, instrument storage, and space for group performances. May include practice rooms attached.", "category": "educational", "type": "environment", "source_file": "environments.json"}, "ind_edu_library": {"code": "ind_edu_library", "name": "Educational Library", "description": "A quiet study environment with extensive collections of books, research materials, and digital resources. Features varied study spaces for individual and group work, reference assistance, and computer access.", "category": "educational", "type": "environment", "source_file": "environments.json"}, "ind_edu_maker_space": {"code": "ind_edu_maker_space", "name": "Educational Makerspace", "description": "A collaborative workshop environment equipped with tools and materials for hands-on creation, design, and engineering projects. Supports innovation, experimentation, and practical skill development.", "category": "educational", "type": "environment", "source_file": "environments.json"}, "ind_edu_theater": {"code": "ind_edu_theater", "name": "Educational Theater", "description": "A performance space with stage, lighting, seating, and backstage areas for theatrical productions, presentations, and performances. Used for drama education and school productions.", "category": "educational", "type": "environment", "source_file": "environments.json"}, "ind_edu_language_lab": {"code": "ind_edu_language_lab", "name": "Language Learning Laboratory", "description": "A specialized classroom equipped with audio-visual technology for language instruction. Features individual listening stations, recording capabilities, and interactive software for practice and assessment.", "category": "educational", "type": "environment", "source_file": "environments.json"}, "ind_edu_conference_room": {"code": "ind_edu_conference_room", "name": "Educational Conference Room", "description": "A meeting space designed for small to medium-sized groups, often used for faculty meetings, student group work, thesis defenses, and small seminars. Features a central table or configurable seating arrangement.", "category": "educational", "type": "environment", "source_file": "environments.json"}, "ind_edu_gymnasium": {"code": "ind_edu_gymnasium", "name": "Educational Gymnasium", "description": "A large indoor space designed for physical education, sports training, and school athletic events. Features a main court area, sometimes with bleachers, and often includes storage for sports equipment.", "category": "educational", "type": "environment", "source_file": "environments.json"}, "out_edu_campus_quad": {"code": "out_edu_campus_quad", "name": "University Quadrangle", "description": "An open outdoor space at the center of a college or university campus, typically surrounded by academic buildings. Used for casual gathering, studying, events, and transit between classes.", "category": "educational", "type": "environment", "source_file": "environments.json"}, "out_edu_school_yard": {"code": "out_edu_school_yard", "name": "School Playground/Yard", "description": "An outdoor space at a primary or secondary school designed for recreation, physical education, and social interaction during breaks. May include play equipment, sports areas, and seating.", "category": "educational", "type": "environment", "source_file": "environments.json"}, "out_edu_outdoor_classroom": {"code": "out_edu_outdoor_classroom", "name": "Outdoor Learning Space", "description": "A designated outdoor area designed specifically for educational activities. May include seating arrangements, teaching walls or boards, shade structures, and demonstration areas for nature study.", "category": "educational", "type": "environment", "source_file": "environments.json"}, "ind_edu_stem_lab": {"code": "ind_edu_stem_lab", "name": "STEM Learning Laboratory", "description": "An integrated laboratory environment designed for interdisciplinary science, technology, engineering, and mathematics education. Features flexible workspaces and tools for hands-on experimentation and project-based learning.", "category": "educational", "type": "environment", "source_file": "environments.json"}, "ind_edu_quiet_study": {"code": "ind_edu_quiet_study", "name": "Silent Study Room", "description": "A designated quiet space for focused individual study with minimal distractions. Features individual carrels or tables and strict noise policies to maintain a concentrated atmosphere.", "category": "educational", "type": "environment", "source_file": "environments.json"}, "ind_edu_virtual_reality": {"code": "ind_edu_virtual_reality", "name": "Virtual Reality Learning Lab", "description": "A specialized lab equipped with virtual reality headsets and stations for immersive educational experiences. Used for simulations, virtual field trips, and interactive 3D learning across various subjects.", "category": "educational", "type": "environment", "source_file": "environments.json"}, "ind_edu_sensory_room": {"code": "ind_edu_sensory_room", "name": "Educational Sensory Room", "description": "A specialized environment designed to develop students\\' sensory processing skills through various stimuli including lights, textures, sounds, and aromas. Particularly valuable for special education and early childhood development.", "category": "educational", "type": "environment", "source_file": "environments.json"}, "ind_edu_cooking_classroom": {"code": "ind_edu_cooking_classroom", "name": "Culinary Education Kitchen", "description": "A specialized classroom equipped with cooking stations, appliances, and preparation areas for culinary education. Designed for food science, nutrition, and cooking skills instruction.", "category": "educational", "type": "environment", "source_file": "environments.json"}}}, "environments_professional": {"description": "Environment codes for professional category", "codes": {"ind_prof_office_private": {"code": "ind_prof_office_private", "name": "Private Office", "description": "A dedicated office space for a single professional, typically enclosed with walls and a door offering privacy for focused work, confidential calls, and meetings. Often contains a desk, ergonomic chair, computer setup, filing cabinets, and personal storage.", "category": "professional", "type": "environment", "source_file": "environments.json"}, "ind_prof_cubicle": {"code": "ind_prof_cubicle", "name": "Office Cubicle", "description": "A partially enclosed workspace within a larger office floor plan, separated by dividers or partitions that provide limited visual privacy but little sound isolation. Typically includes a desk, computer workstation, and limited personal storage.", "category": "professional", "type": "environment", "source_file": "environments.json"}, "ind_prof_openplan": {"code": "ind_prof_openplan", "name": "Open-Plan Office", "description": "A large, open workspace where multiple employees work without walls or partitions between them. Designed to encourage collaboration and communication, but often lacks privacy and can be noisy. Usually features clusters of desks or workstations grouped by team or function.", "category": "professional", "type": "environment", "source_file": "environments.json"}, "ind_prof_conference": {"code": "ind_prof_conference", "name": "Conference Room", "description": "A dedicated meeting space designed for formal gatherings, presentations, and discussions. Usually features a large central table with multiple chairs, presentation equipment, and sometimes video conferencing capabilities. Designed for group collaboration and decision-making.", "category": "professional", "type": "environment", "source_file": "environments.json"}, "ind_prof_breakroom": {"code": "ind_prof_breakroom", "name": "Office Break Room", "description": "A casual space within a workplace designated for employees to take breaks, eat meals, and socialize. Typically includes kitchen facilities, tables and chairs, and sometimes recreational elements. Designed to provide respite from work and foster informal interactions.", "category": "professional", "type": "environment", "source_file": "environments.json"}, "ind_prof_creativestudio": {"code": "ind_prof_creativestudio", "name": "Creative Studio", "description": "A flexible workspace designed specifically for creative professionals such as designers, architects, or artists. Features large work surfaces, specialized equipment, and space for displaying and reviewing creative work. Promotes visual thinking and creative collaboration.", "category": "professional", "type": "environment", "source_file": "environments.json"}, "ind_prof_techlab": {"code": "ind_prof_techlab", "name": "Technical Laboratory", "description": "A specialized workspace equipped for technical research, development, or testing. Contains specialized equipment, instruments, and safety features specific to the field (e.g., electronics, chemistry, biology). Designed for precise, controlled work and experimentation.", "category": "professional", "type": "environment", "source_file": "environments.json"}, "ind_prof_coworking": {"code": "ind_prof_coworking", "name": "Coworking Space", "description": "A shared workplace where professionals from different organizations work independently or collaboratively. Offers a combination of open workspaces, private areas, and communal facilities. Designed to provide professional amenities while fostering community and networking.", "category": "professional", "type": "environment", "source_file": "environments.json"}, "ind_prof_servercenter": {"code": "ind_prof_servercenter", "name": "Data Center / Server Room", "description": "A specialized facility dedicated to housing computer systems, servers, and networking equipment. Features extensive cooling systems, security measures, and power redundancies. Designed for maximum uptime and equipment protection rather than human comfort.", "category": "professional", "type": "environment", "source_file": "environments.json"}, "ind_prof_trainingroom": {"code": "ind_prof_trainingroom", "name": "Training Room", "description": "A specialized space designed for professional education, workshops, and skills development. Typically arranged classroom-style or in flexible configurations to facilitate learning. Equipped with presentation technologies and interactive tools.", "category": "professional", "type": "environment", "source_file": "environments.json"}, "ind_prof_callcenter": {"code": "ind_prof_callcenter", "name": "Call Center", "description": "A workspace designed for high-volume telephone or digital customer interactions. Features numerous workstations, often closely arranged, with acoustic considerations. Optimized for communication efficiency and monitoring capabilities.", "category": "professional", "type": "environment", "source_file": "environments.json"}, "out_prof_campusgrounds": {"code": "out_prof_campusgrounds", "name": "Corporate Campus Grounds", "description": "The outdoor areas surrounding corporate buildings, often landscaped and designed for employee use. May include walking paths, seating areas, and outdoor meeting spaces. Provides natural elements within a professional setting.", "category": "professional", "type": "environment", "source_file": "environments.json"}, "ind_prof_rooftopspace": {"code": "ind_prof_rooftopspace", "name": "Office Rooftop Space", "description": "A converted rooftop area on an office building designed for work, breaks, or events. Combines outdoor elements with workplace proximity. Often features views and open air while maintaining professional amenities.", "category": "professional", "type": "environment", "source_file": "environments.json"}, "ind_prof_executivesuite": {"code": "ind_prof_executivesuite", "name": "Executive Suite", "description": "A premium office space for senior management, featuring high-end furnishings, enhanced privacy, and dedicated meeting areas. Often includes a private office, reception area, and personal bathroom. Designed to facilitate leadership functions and confidential discussions.", "category": "professional", "type": "environment", "source_file": "environments.json"}, "ind_prof_workshop": {"code": "ind_prof_workshop", "name": "Professional Workshop", "description": "A hands-on workspace designed for building, repairing, or manipulating physical objects. Contains specialized tools, equipment, and durable work surfaces. Emphasizes functionality and safety over comfort.", "category": "professional", "type": "environment", "source_file": "environments.json"}, "ind_prof_quietroom": {"code": "ind_prof_quietroom", "name": "Quiet Work Room", "description": "A designated silent workspace within an office environment where talking and phone calls are prohibited. Features enhanced sound insulation and minimal distractions. Designed for deep focus and concentration on complex tasks.", "category": "professional", "type": "environment", "source_file": "environments.json"}}}, "resources_technology": {"description": "Resource codes for technology category", "codes": {"tech_smartphone": {"code": "tech_smartphone", "name": "Smartphone", "description": "A mobile device with internet connectivity for apps, communication, and digital activities", "category": "technology", "type": "resource", "resource_type": "TECHNOLOGY", "typical_cost": 0.0, "acquisition_cost": 500.0, "availability": "COMMON", "source_file": "resources.json"}, "tech_computer": {"code": "tech_computer", "name": "Computer/Laptop", "description": "A personal computer or laptop for research, learning, and digital work", "category": "technology", "type": "resource", "resource_type": "TECHNOLOGY", "typical_cost": 0.0, "acquisition_cost": 800.0, "availability": "COMMON", "source_file": "resources.json"}, "tech_computer_internet": {"code": "tech_computer_internet", "name": "Computer with Internet", "description": "Computer with reliable internet connection for online research and learning", "category": "technology", "type": "resource", "resource_type": "TECHNOLOGY", "typical_cost": 50.0, "acquisition_cost": 850.0, "availability": "COMMON", "source_file": "resources.json"}, "tech_phone": {"code": "tech_phone", "name": "Phone", "description": "Basic phone for communication and simple apps", "category": "technology", "type": "resource", "resource_type": "TECHNOLOGY", "typical_cost": 0.0, "acquisition_cost": 200.0, "availability": "VERY_COMMON", "source_file": "resources.json"}, "tech_camera": {"code": "tech_camera", "name": "Camera", "description": "Digital camera for photography activities", "category": "technology", "type": "resource", "resource_type": "TECHNOLOGY", "typical_cost": 0.0, "acquisition_cost": 400.0, "availability": "COMMON", "source_file": "resources.json"}, "tech_gaming": {"code": "tech_gaming", "name": "Gaming Device", "description": "Gaming console, computer, or device for digital gaming activities", "category": "technology", "type": "resource", "resource_type": "TECHNOLOGY", "typical_cost": 0.0, "acquisition_cost": 300.0, "availability": "COMMON", "source_file": "resources.json"}, "tech_screen": {"code": "tech_screen", "name": "Screen/Display", "description": "Television, monitor, or large screen for viewing content", "category": "technology", "type": "resource", "resource_type": "TECHNOLOGY", "typical_cost": 0.0, "acquisition_cost": 300.0, "availability": "COMMON", "source_file": "resources.json"}, "tech_music_player": {"code": "tech_music_player", "name": "Music Player", "description": "Device for playing music, including speakers or headphones", "category": "technology", "type": "resource", "resource_type": "TECHNOLOGY", "typical_cost": 0.0, "acquisition_cost": 100.0, "availability": "COMMON", "source_file": "resources.json"}, "tech_navigation": {"code": "tech_navigation", "name": "Navigation Device", "description": "GPS device or navigation app for travel and exploration", "category": "technology", "type": "resource", "resource_type": "TECHNOLOGY", "typical_cost": 0.0, "acquisition_cost": 150.0, "availability": "COMMON", "source_file": "resources.json"}, "tech_vr_headset": {"code": "tech_vr_headset", "name": "VR Headset", "description": "Virtual reality headset for immersive digital experiences", "category": "technology", "type": "resource", "resource_type": "TECHNOLOGY", "typical_cost": 0.0, "acquisition_cost": 400.0, "availability": "UNCOMMON", "source_file": "resources.json"}}}, "resources_equipment": {"description": "Resource codes for equipment category", "codes": {"equip_exercise_mat": {"code": "equip_exercise_mat", "name": "Exercise Mat", "description": "Yoga or exercise mat for floor-based physical activities", "category": "equipment", "type": "resource", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 30.0, "availability": "COMMON", "source_file": "resources.json"}, "equip_yoga_mat": {"code": "equip_yoga_mat", "name": "Yoga Mat", "description": "Specialized mat for yoga practice with proper grip and cushioning", "category": "equipment", "type": "resource", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 40.0, "availability": "COMMON", "source_file": "resources.json"}, "equip_yoga_block": {"code": "equip_yoga_block", "name": "Yoga Block", "description": "Supportive block for yoga poses and stretching", "category": "equipment", "type": "resource", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 15.0, "availability": "COMMON", "source_file": "resources.json"}, "equip_running_shoes": {"code": "equip_running_shoes", "name": "Running Shoes", "description": "Proper athletic footwear for running and cardio activities", "category": "equipment", "type": "resource", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 120.0, "availability": "COMMON", "source_file": "resources.json"}, "equip_walking_shoes": {"code": "equip_walking_shoes", "name": "Walking Shoes", "description": "Comfortable shoes suitable for walking and light exercise", "category": "equipment", "type": "resource", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 80.0, "availability": "VERY_COMMON", "source_file": "resources.json"}, "equip_athletic_wear": {"code": "equip_athletic_wear", "name": "Athletic Wear", "description": "Appropriate clothing for physical exercise and sports", "category": "equipment", "type": "resource", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 60.0, "availability": "COMMON", "source_file": "resources.json"}, "equip_protective_gear": {"code": "equip_protective_gear", "name": "Protective Gear", "description": "Safety equipment for high-risk physical activities", "category": "equipment", "type": "resource", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 100.0, "availability": "COMMON", "source_file": "resources.json"}, "equip_training_shoes": {"code": "equip_training_shoes", "name": "Training Shoes", "description": "Cross-training shoes for varied physical activities", "category": "equipment", "type": "resource", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 100.0, "availability": "COMMON", "source_file": "resources.json"}, "equip_comfortable_shoes": {"code": "equip_comfortable_shoes", "name": "Comfortable Shoes", "description": "General comfortable footwear for daily activities", "category": "equipment", "type": "resource", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 70.0, "availability": "VERY_COMMON", "source_file": "resources.json"}, "equip_timer": {"code": "equip_timer", "name": "Timer", "description": "Device for timing activities and exercises", "category": "equipment", "type": "resource", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 20.0, "availability": "COMMON", "source_file": "resources.json"}, "equip_water_bottle": {"code": "equip_water_bottle", "name": "Water Bottle", "description": "Reusable water bottle for hydration during activities", "category": "equipment", "type": "resource", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 15.0, "availability": "VERY_COMMON", "source_file": "resources.json"}, "equip_day_pack": {"code": "equip_day_pack", "name": "Day Pack", "description": "Small backpack for carrying essentials during outings", "category": "equipment", "type": "resource", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 50.0, "availability": "COMMON", "source_file": "resources.json"}, "equip_outdoor_basic": {"code": "equip_outdoor_basic", "name": "Basic Outdoor Equipment", "description": "Essential gear for outdoor activities and exploration", "category": "equipment", "type": "resource", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 75.0, "availability": "COMMON", "source_file": "resources.json"}, "equip_blindfold": {"code": "equip_blindfold", "name": "<PERSON>fold", "description": "Simple blindfold for trust exercises and sensory activities", "category": "equipment", "type": "resource", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 10.0, "availability": "COMMON", "source_file": "resources.json"}, "equip_cleaning_tools": {"code": "equip_cleaning_tools", "name": "Cleaning Tools", "description": "Basic cleaning supplies and tools for organization activities", "category": "equipment", "type": "resource", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 40.0, "availability": "VERY_COMMON", "source_file": "resources.json"}, "equip_learning_tools": {"code": "equip_learning_tools", "name": "Learning Tools", "description": "Educational materials and tools for skill development", "category": "equipment", "type": "resource", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 60.0, "availability": "COMMON", "source_file": "resources.json"}, "equip_professional_attire": {"code": "equip_professional_attire", "name": "Professional Attire", "description": "Appropriate clothing for professional networking events", "category": "equipment", "type": "resource", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 200.0, "availability": "COMMON", "source_file": "resources.json"}, "equip_business_cards": {"code": "equip_business_cards", "name": "Business Cards", "description": "Professional business cards for networking", "category": "equipment", "type": "resource", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 25.0, "availability": "COMMON", "source_file": "resources.json"}, "equip_board_game": {"code": "equip_board_game", "name": "Board Game", "description": "Strategy or social board game for intellectual activities", "category": "equipment", "type": "resource", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 40.0, "availability": "COMMON", "source_file": "resources.json"}, "equip_kitchen_basic": {"code": "equip_kitchen_basic", "name": "Basic Kitchen Equipment", "description": "Essential kitchen tools for cooking activities", "category": "equipment", "type": "resource", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 100.0, "availability": "COMMON", "source_file": "resources.json"}, "equip_kettle": {"code": "equip_kettle", "name": "Kettle", "description": "Electric or stovetop kettle for heating water", "category": "equipment", "type": "resource", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 30.0, "availability": "COMMON", "source_file": "resources.json"}}}, "resources_stationery": {"description": "Resource codes for stationery category", "codes": {"stationery_journal": {"code": "stationery_journal", "name": "Journal", "description": "Notebook or journal for writing, reflection, and planning", "category": "stationery", "type": "resource", "resource_type": "STATIONERY", "typical_cost": 0.0, "acquisition_cost": 15.0, "availability": "VERY_COMMON", "source_file": "resources.json"}, "stationery_paper": {"code": "stationery_paper", "name": "Paper", "description": "Writing paper for notes, exercises, and creative activities", "category": "stationery", "type": "resource", "resource_type": "STATIONERY", "typical_cost": 0.0, "acquisition_cost": 5.0, "availability": "VERY_COMMON", "source_file": "resources.json"}, "stationery_pen": {"code": "stationery_pen", "name": "Pen", "description": "Writing pen for journaling and note-taking", "category": "stationery", "type": "resource", "resource_type": "STATIONERY", "typical_cost": 0.0, "acquisition_cost": 2.0, "availability": "VERY_COMMON", "source_file": "resources.json"}, "stationery_pencil": {"code": "stationery_pencil", "name": "Pencil", "description": "Pencil for writing, drawing, and problem-solving", "category": "stationery", "type": "resource", "resource_type": "STATIONERY", "typical_cost": 0.0, "acquisition_cost": 1.0, "availability": "VERY_COMMON", "source_file": "resources.json"}, "stationery_markers": {"code": "stationery_markers", "name": "Markers", "description": "Colored markers for creative and organizational activities", "category": "stationery", "type": "resource", "resource_type": "STATIONERY", "typical_cost": 0.0, "acquisition_cost": 10.0, "availability": "COMMON", "source_file": "resources.json"}, "equip_journal": {"code": "equip_journal", "name": "Journal/Notebook", "description": "Dedicated journal or notebook for reflection and planning", "category": "stationery", "type": "resource", "resource_type": "STATIONERY", "typical_cost": 0.0, "acquisition_cost": 15.0, "availability": "VERY_COMMON", "source_file": "resources.json"}, "equip_planner": {"code": "equip_planner", "name": "Planner", "description": "Structured planner for goal setting and time management", "category": "stationery", "type": "resource", "resource_type": "STATIONERY", "typical_cost": 0.0, "acquisition_cost": 25.0, "availability": "COMMON", "source_file": "resources.json"}, "equip_writing_tool": {"code": "equip_writing_tool", "name": "Writing Tool", "description": "Pen, pencil, or other writing implement", "category": "stationery", "type": "resource", "resource_type": "STATIONERY", "typical_cost": 0.0, "acquisition_cost": 3.0, "availability": "VERY_COMMON", "source_file": "resources.json"}, "edu_writing_materials": {"code": "edu_writing_materials", "name": "Educational Writing Materials", "description": "Comprehensive writing supplies for learning activities", "category": "stationery", "type": "resource", "resource_type": "STATIONERY", "typical_cost": 0.0, "acquisition_cost": 20.0, "availability": "COMMON", "source_file": "resources.json"}}}, "resources_creative": {"description": "Resource codes for creative category", "codes": {"creative_instrument": {"code": "creative_instrument", "name": "Musical Instrument", "description": "Musical instrument for creative expression and performance", "category": "creative", "type": "resource", "resource_type": "CREATIVE", "typical_cost": 0.0, "acquisition_cost": 200.0, "availability": "COMMON", "source_file": "resources.json"}, "creative_drawing_materials": {"code": "creative_drawing_materials", "name": "Drawing Materials", "description": "Art supplies for drawing and sketching activities", "category": "creative", "type": "resource", "resource_type": "CREATIVE", "typical_cost": 0.0, "acquisition_cost": 30.0, "availability": "COMMON", "source_file": "resources.json"}, "art_basic_supplies": {"code": "art_basic_supplies", "name": "Basic Art Supplies", "description": "Essential art materials for creative expression", "category": "creative", "type": "resource", "resource_type": "CREATIVE", "typical_cost": 0.0, "acquisition_cost": 40.0, "availability": "COMMON", "source_file": "resources.json"}, "art_paper": {"code": "art_paper", "name": "Art Paper", "description": "Specialized paper for drawing and artistic activities", "category": "creative", "type": "resource", "resource_type": "CREATIVE", "typical_cost": 0.0, "acquisition_cost": 10.0, "availability": "COMMON", "source_file": "resources.json"}}}, "resources_leisure": {"description": "Resource codes for leisure category", "codes": {"leisure_board_games": {"code": "leisure_board_games", "name": "Board Games", "description": "Collection of board games for social entertainment", "category": "leisure", "type": "resource", "resource_type": "LEISURE", "typical_cost": 0.0, "acquisition_cost": 80.0, "availability": "COMMON", "source_file": "resources.json"}, "leisure_chess_set": {"code": "leisure_chess_set", "name": "Chess Set", "description": "Chess board and pieces for strategic gameplay", "category": "leisure", "type": "resource", "resource_type": "LEISURE", "typical_cost": 0.0, "acquisition_cost": 35.0, "availability": "COMMON", "source_file": "resources.json"}, "leisure_puzzle": {"code": "leisure_puzzle", "name": "Puzzle", "description": "Jigsaw puzzle or brain teaser for mental stimulation", "category": "leisure", "type": "resource", "resource_type": "LEISURE", "typical_cost": 0.0, "acquisition_cost": 20.0, "availability": "COMMON", "source_file": "resources.json"}, "leisure_snacks": {"code": "leisure_snacks", "name": "Snacks", "description": "Light snacks for leisure activities and entertainment", "category": "leisure", "type": "resource", "resource_type": "LEISURE", "typical_cost": 5.0, "acquisition_cost": 5.0, "availability": "VERY_COMMON", "source_file": "resources.json"}}}, "resources_food": {"description": "Resource codes for food category", "codes": {"food_snack": {"code": "food_snack", "name": "Snack Food", "description": "Light snack for mindful eating activities", "category": "food", "type": "resource", "resource_type": "FOOD", "typical_cost": 3.0, "acquisition_cost": 3.0, "availability": "VERY_COMMON", "source_file": "resources.json"}, "food_tea": {"code": "food_tea", "name": "Tea", "description": "Tea for mindful drinking and relaxation rituals", "category": "food", "type": "resource", "resource_type": "FOOD", "typical_cost": 2.0, "acquisition_cost": 10.0, "availability": "VERY_COMMON", "source_file": "resources.json"}, "cook_equipment": {"code": "cook_equipment", "name": "Cooking Equipment", "description": "Basic cooking tools and equipment for meal preparation", "category": "food", "type": "resource", "resource_type": "FOOD", "typical_cost": 0.0, "acquisition_cost": 150.0, "availability": "COMMON", "source_file": "resources.json"}, "cook_ingredients": {"code": "cook_ingredients", "name": "Cooking Ingredients", "description": "Fresh ingredients for cooking and meal preparation", "category": "food", "type": "resource", "resource_type": "FOOD", "typical_cost": 20.0, "acquisition_cost": 20.0, "availability": "COMMON", "source_file": "resources.json"}}}, "resources_garden": {"description": "Resource codes for garden category", "codes": {"garden_tools": {"code": "garden_tools", "name": "Garden Tools", "description": "Basic gardening tools for plant care activities", "category": "garden", "type": "resource", "resource_type": "GARDEN", "typical_cost": 0.0, "acquisition_cost": 60.0, "availability": "COMMON", "source_file": "resources.json"}, "garden_plants": {"code": "garden_plants", "name": "Plants", "description": "Plants, seeds, or seedlings for gardening activities", "category": "garden", "type": "resource", "resource_type": "GARDEN", "typical_cost": 5.0, "acquisition_cost": 25.0, "availability": "COMMON", "source_file": "resources.json"}}}, "resources_outdoor": {"description": "Resource codes for outdoor category", "codes": {"outdoor_footwear": {"code": "outdoor_footwear", "name": "Outdoor Footwear", "description": "Appropriate footwear for outdoor activities and hiking", "category": "outdoor", "type": "resource", "resource_type": "OUTDOOR", "typical_cost": 0.0, "acquisition_cost": 100.0, "availability": "COMMON", "source_file": "resources.json"}, "outdoor_water": {"code": "outdoor_water", "name": "Water Supply", "description": "Clean water for outdoor activities and hydration", "category": "outdoor", "type": "resource", "resource_type": "OUTDOOR", "typical_cost": 2.0, "acquisition_cost": 2.0, "availability": "VERY_COMMON", "source_file": "resources.json"}}}, "resources_media": {"description": "Resource codes for media category", "codes": {"media_music": {"code": "media_music", "name": "Music", "description": "Music streaming or audio content for activities", "category": "media", "type": "resource", "resource_type": "MEDIA", "typical_cost": 10.0, "acquisition_cost": 10.0, "availability": "VERY_COMMON", "source_file": "resources.json"}}}, "resources_finance": {"description": "Resource codes for finance category", "codes": {"finance_small": {"code": "finance_small", "name": "Small Financial Amount", "description": "Small amount of money for minor purchases or activities", "category": "finance", "type": "resource", "resource_type": "FINANCE", "typical_cost": 10.0, "acquisition_cost": 10.0, "availability": "COMMON", "source_file": "resources.json"}, "finance_medium": {"code": "finance_medium", "name": "Medium Financial Amount", "description": "Moderate amount of money for activities requiring investment", "category": "finance", "type": "resource", "resource_type": "FINANCE", "typical_cost": 50.0, "acquisition_cost": 50.0, "availability": "COMMON", "source_file": "resources.json"}, "finance_activity_fee": {"code": "finance_activity_fee", "name": "Activity Fee", "description": "Payment for organized activities or classes", "category": "finance", "type": "resource", "resource_type": "FINANCE", "typical_cost": 25.0, "acquisition_cost": 25.0, "availability": "COMMON", "source_file": "resources.json"}, "finance_dining": {"code": "finance_dining", "name": "Dining Budget", "description": "Money for dining out or food-related activities", "category": "finance", "type": "resource", "resource_type": "FINANCE", "typical_cost": 30.0, "acquisition_cost": 30.0, "availability": "COMMON", "source_file": "resources.json"}, "finance_entertainment": {"code": "finance_entertainment", "name": "Entertainment Budget", "description": "Money for entertainment and leisure activities", "category": "finance", "type": "resource", "resource_type": "FINANCE", "typical_cost": 20.0, "acquisition_cost": 20.0, "availability": "COMMON", "source_file": "resources.json"}, "finance_travel": {"code": "finance_travel", "name": "Travel Budget", "description": "Money for travel and exploration activities", "category": "finance", "type": "resource", "resource_type": "FINANCE", "typical_cost": 100.0, "acquisition_cost": 100.0, "availability": "COMMON", "source_file": "resources.json"}}}, "resources_access": {"description": "Resource codes for access category", "codes": {"access_transportation": {"code": "access_transportation", "name": "Transportation Access", "description": "Access to public or private transportation for activities", "category": "access", "type": "resource", "resource_type": "ACCESS", "typical_cost": 15.0, "acquisition_cost": 15.0, "availability": "COMMON", "source_file": "resources.json"}, "access_walkable_area": {"code": "access_walkable_area", "name": "Walkable Area Access", "description": "Access to safe, walkable areas for exploration", "category": "access", "type": "resource", "resource_type": "ACCESS", "typical_cost": 0.0, "acquisition_cost": 0.0, "availability": "COMMON", "source_file": "resources.json"}}}, "resources_home": {"description": "Resource codes for home category", "codes": {"home_dining_space": {"code": "home_dining_space", "name": "Dining Space", "description": "Dedicated space for dining and entertaining guests", "category": "home", "type": "resource", "resource_type": "HOME", "typical_cost": 0.0, "acquisition_cost": 0.0, "availability": "COMMON", "source_file": "resources.json"}, "home_kitchen": {"code": "home_kitchen", "name": "Kitchen Access", "description": "Access to a functional kitchen for cooking activities", "category": "home", "type": "resource", "resource_type": "HOME", "typical_cost": 0.0, "acquisition_cost": 0.0, "availability": "COMMON", "source_file": "resources.json"}, "office_desk": {"code": "office_desk", "name": "Office Desk", "description": "Desk or workspace for office-based activities", "category": "home", "type": "resource", "resource_type": "HOME", "typical_cost": 0.0, "acquisition_cost": 200.0, "availability": "COMMON", "source_file": "resources.json"}}}, "resources_education": {"description": "Resource codes for education category", "codes": {"edu_book": {"code": "edu_book", "name": "Educational Book", "description": "Book for learning and educational activities", "category": "education", "type": "resource", "resource_type": "EDUCATION", "typical_cost": 0.0, "acquisition_cost": 25.0, "availability": "COMMON", "source_file": "resources.json"}}}, "resources_specialized": {"description": "Resource codes for specialized category", "codes": {"skill_specific_tools": {"code": "skill_specific_tools", "name": "Skill-Specific Tools", "description": "Specialized tools required for specific skill development", "category": "specialized", "type": "resource", "resource_type": "SPECIALIZED", "typical_cost": 0.0, "acquisition_cost": 100.0, "availability": "UNCOMMON", "source_file": "resources.json"}}}, "domains_physical": {"description": "Domain codes for PHYSICAL category", "codes": {"physical": {"code": "physical", "name": "Physical", "description": "a broad domain encompassing all physical activities that engage the body and promote health and fitness.", "category": "PHYSICAL", "type": "domain", "source_file": "domains.json"}, "phys_cardio": {"code": "phys_cardio", "name": "Cardiovascular Exercise", "description": "Activities that increase heart rate and improve cardiovascular health, such as running, cycling, or swimming.", "category": "PHYSICAL", "type": "domain", "source_file": "domains.json"}, "phys_strength": {"code": "phys_strength", "name": "Strength Training", "description": "Activities focused on building muscle strength through resistance, such as weightlifting or bodyweight exercises.", "category": "PHYSICAL", "type": "domain", "source_file": "domains.json"}, "phys_chill": {"code": "phys_chill", "name": "Physical but chill", "description": "Activities that are physical, but somehow chill (like walking).", "category": "PHYSICAL", "type": "domain", "source_file": "domains.json"}, "phys_flexibility": {"code": "phys_flexibility", "name": "Flexibility & Mobility", "description": "Activities that improve range of motion, joint health, and muscle elasticity, such as yoga, stretching, or pilates.", "category": "PHYSICAL", "type": "domain", "source_file": "domains.json"}, "phys_sports": {"code": "phys_sports", "name": "Recreational Sports", "description": "Structured physical activities with rules and competitive elements, including team sports or individual athletic pursuits.", "category": "PHYSICAL", "type": "domain", "source_file": "domains.json"}, "phys_outdoor": {"code": "phys_outdoor", "name": "Outdoor Activities", "description": "Physical activities conducted in natural settings, such as hiking, climbing, or kayaking.", "category": "PHYSICAL", "type": "domain", "source_file": "domains.json"}, "phys_dance": {"code": "phys_dance", "name": "Dance & Movement", "description": "Expressive physical activities involving rhythmic body movement, including various dance styles and movement practices.", "category": "PHYSICAL", "type": "domain", "source_file": "domains.json"}, "phys_martial": {"code": "phys_martial", "name": "Martial Arts", "description": "Structured systems of combat and self-defense training that often incorporate physical conditioning and philosophical elements.", "category": "PHYSICAL", "type": "domain", "source_file": "domains.json"}, "phys_balance": {"code": "phys_balance", "name": "Balance & Coordination", "description": "Activities that develop proprioception, stability, and motor control.", "category": "PHYSICAL", "type": "domain", "source_file": "domains.json"}}}, "domains_social": {"description": "Domain codes for SOCIAL category", "codes": {"social": {"code": "social", "name": "Social", "description": "a broad domain encompassing all social activities that engage users with others.", "category": "SOCIAL", "type": "domain", "source_file": "domains.json"}, "soc_connecting": {"code": "soc_connecting", "name": "Social Connection", "description": "Activities focused on forming or strengthening interpersonal bonds through conversation, shared experiences, or mutual support.", "category": "SOCIAL", "type": "domain", "source_file": "domains.json"}, "soc_group": {"code": "soc_group", "name": "Group Dynamics", "description": "Activities involving interaction within larger social structures, such as team building, community engagement, or navigating organizational systems.", "category": "SOCIAL", "type": "domain", "source_file": "domains.json"}, "soc_comm": {"code": "soc_comm", "name": "Communication Skills", "description": "Activities developing verbal and non-verbal communication, including public speaking, active listening, or conflict resolution.", "category": "SOCIAL", "type": "domain", "source_file": "domains.json"}, "soc_empathy": {"code": "soc_empathy", "name": "Empathy Building", "description": "Activities designed to enhance understanding of others' perspectives, emotions, and experiences.", "category": "SOCIAL", "type": "domain", "source_file": "domains.json"}, "soc_network": {"code": "soc_network", "name": "Networking", "description": "Structured activities to expand professional or personal circles, create new connections, and develop a support system.", "category": "SOCIAL", "type": "domain", "source_file": "domains.json"}, "soc_romance": {"code": "soc_romance", "name": "Romantic Relationship", "description": "Activities focused on developing or strengthening intimate partner relationships.", "category": "SOCIAL", "type": "domain", "source_file": "domains.json"}, "soc_family": {"code": "soc_family", "name": "Family Bonding", "description": "Activities that enhance connections between family members and foster a supportive family environment.", "category": "SOCIAL", "type": "domain", "source_file": "domains.json"}, "soc_leadership": {"code": "soc_leadership", "name": "Leadership", "description": "Activities that develop skills in guiding, influencing, and motivating others toward shared goals.", "category": "SOCIAL", "type": "domain", "source_file": "domains.json"}, "soc_conflict": {"code": "soc_conflict", "name": "Conflict Resolution", "description": "Activities that develop skills in solving conflicts.", "category": "SOCIAL", "type": "domain", "source_file": "domains.json"}}}, "domains_creative": {"description": "Domain codes for CREATIVE category", "codes": {"creative": {"code": "creative", "name": "Creative", "description": "a broad domain encompassing all creative activities.", "category": "CREATIVE", "type": "domain", "source_file": "domains.json"}, "creative_visual": {"code": "creative_visual", "name": "Visual Arts", "description": "Creative expression through visual mediums including painting, drawing, sculpture, or digital art.", "category": "CREATIVE", "type": "domain", "source_file": "domains.json"}, "creative_observation": {"code": "creative_observation", "name": "Creative observation", "description": "Creative thoughts from observing.", "category": "CREATIVE", "type": "domain", "source_file": "domains.json"}, "creative_auditory": {"code": "creative_auditory", "name": "Creative auditory", "description": "Creative thoughts from listening.", "category": "CREATIVE", "type": "domain", "source_file": "domains.json"}, "creative_music": {"code": "creative_music", "name": "Music Creation", "description": "Activities involving creating or performing music, such as playing instruments, singing, composing, or producing.", "category": "CREATIVE", "type": "domain", "source_file": "domains.json"}, "creative_writing": {"code": "creative_writing", "name": "Creative Writing", "description": "Expression through written language, including fiction, poetry, journaling, or scriptwriting.", "category": "CREATIVE", "type": "domain", "source_file": "domains.json"}, "creative_design": {"code": "creative_design", "name": "Design Thinking", "description": "Problem-solving through creative design processes, including graphic design, product design, or architectural thinking.", "category": "CREATIVE", "type": "domain", "source_file": "domains.json"}, "creative_culinary": {"code": "creative_culinary", "name": "Culinary Arts", "description": "Creative expression through food preparation, recipe development, or gastronomic experimentation.", "category": "CREATIVE", "type": "domain", "source_file": "domains.json"}, "creative_perform": {"code": "creative_perform", "name": "Performance Arts", "description": "Creative activities involving live presentation, such as theater, comedy, or storytelling.", "category": "CREATIVE", "type": "domain", "source_file": "domains.json"}, "creative_craft": {"code": "creative_craft", "name": "Crafts & Making", "description": "Hands-on creation of physical objects, including textile arts, woodworking, pottery, or DIY projects.", "category": "CREATIVE", "type": "domain", "source_file": "domains.json"}, "creative_improv": {"code": "creative_improv", "name": "Improvisation", "description": "Spontaneous creative expression and adaptation without predetermined structure.", "category": "CREATIVE", "type": "domain", "source_file": "domains.json"}}}, "domains_intellectual": {"description": "Domain codes for INTELLECTUAL category", "codes": {"intellectual": {"code": "intellectual", "name": "Intellectual", "description": "a broad domain encompassing all intellectual activities.", "category": "INTELLECTUAL", "type": "domain", "source_file": "domains.json"}, "intel_learn": {"code": "intel_learn", "name": "Learning & Study", "description": "Structured acquisition of knowledge or skills in specific domains through courses, research, or self-directed study.", "category": "INTELLECTUAL", "type": "domain", "source_file": "domains.json"}, "intel_problem": {"code": "intel_problem", "name": "Problem Solving", "description": "Activities that develop analytical thinking and systematic approaches to overcoming challenges.", "category": "INTELLECTUAL", "type": "domain", "source_file": "domains.json"}, "intel_audio": {"code": "intel_audio", "name": "Intellectual audio", "description": "Activities that requires focused listening.", "category": "INTELLECTUAL", "type": "domain", "source_file": "domains.json"}, "intel_strategic": {"code": "intel_strategic", "name": "Strategic Thinking", "description": "Long-term planning and development of approaches to achieve goals while considering multiple variables and possible outcomes.", "category": "INTELLECTUAL", "type": "domain", "source_file": "domains.json"}, "intel_curiosity": {"code": "intel_curiosity", "name": "Intellectual Curiosity", "description": "Exploration of ideas driven by interest and wonder, such as reading broadly or engaging with new concepts.", "category": "INTELLECTUAL", "type": "domain", "source_file": "domains.json"}, "intel_language": {"code": "intel_language", "name": "Language & Linguistics", "description": "Activities focused on language acquisition, linguistic analysis, or cross-cultural communication.", "category": "INTELLECTUAL", "type": "domain", "source_file": "domains.json"}, "intel_debate": {"code": "intel_debate", "name": "Critical Discourse", "description": "Engagement in reasoned argument, debate, or discussion that evaluates ideas from multiple perspectives.", "category": "INTELLECTUAL", "type": "domain", "source_file": "domains.json"}, "intel_science": {"code": "intel_science", "name": "Scientific Inquiry", "description": "Activities involving observation, hypothesis formation, experimentation, and evidence-based reasoning.", "category": "INTELLECTUAL", "type": "domain", "source_file": "domains.json"}, "intel_tech": {"code": "intel_tech", "name": "Technology & Digital Skills", "description": "Learning and applying digital tools, programming, or computational thinking.", "category": "INTELLECTUAL", "type": "domain", "source_file": "domains.json"}}}, "domains_reflective": {"description": "Domain codes for REFLECTIVE category", "codes": {"reflective": {"code": "reflective", "name": "Reflective", "description": "a broad domain encompassing all reflective activities.", "category": "REFLECTIVE", "type": "domain", "source_file": "domains.json"}, "refl_meditate": {"code": "refl_meditate", "name": "Meditation", "description": "Practices that develop focused attention, awareness, and mental clarity through various meditation techniques.", "category": "REFLECTIVE", "type": "domain", "source_file": "domains.json"}, "refl_journal": {"code": "refl_journal", "name": "Journaling & Self-Reflection", "description": "Written or structured contemplation of personal experiences, emotions, and thoughts to gain insight.", "category": "REFLECTIVE", "type": "domain", "source_file": "domains.json"}, "refl_mindful": {"code": "refl_mindful", "name": "Mindfulness Practice", "description": "Activities cultivating present-moment awareness and non-judgmental observation of internal and external experiences.", "category": "REFLECTIVE", "type": "domain", "source_file": "domains.json"}, "refl_values": {"code": "refl_values", "name": "Values Clarification", "description": "Exploration and identification of personal principles, ethics, and priorities that guide decision-making.", "category": "REFLECTIVE", "type": "domain", "source_file": "domains.json"}, "refl_persp": {"code": "refl_persp", "name": "Perspective Taking", "description": "Activities that encourage viewing situations from different angles or through others' experiences.", "category": "REFLECTIVE", "type": "domain", "source_file": "domains.json"}, "refl_philos": {"code": "refl_philos", "name": "Philosophical Contemplation", "description": "Engagement with fundamental questions about existence, meaning, ethics, and the nature of reality.", "category": "REFLECTIVE", "type": "domain", "source_file": "domains.json"}, "refl_grat": {"code": "refl_grat", "name": "Gratitude Practice", "description": "Structured recognition and appreciation of positive aspects of life and experiences.", "category": "REFLECTIVE", "type": "domain", "source_file": "domains.json"}, "refl_comfort": {"code": "refl_comfort", "name": "Comfort Zone Reflection", "description": "Examination of personal boundaries, limitations, and opportunities for growth through controlled discomfort.", "category": "REFLECTIVE", "type": "domain", "source_file": "domains.json"}, "refl_micro": {"code": "refl_micro", "name": "Micro-Wellness Activities", "description": "Brief wellness activities (2-15 minutes) designed for busy schedules and transition moments, such as quick breathing exercises or brief mindfulness practices.", "category": "REFLECTIVE", "type": "domain", "source_file": "domains.json"}}}, "domains_emotional": {"description": "Domain codes for EMOTIONAL category", "codes": {"emotional": {"code": "emotional", "name": "Emotional", "description": "a broad domain encompassing all activities that engege the users with their emotions.", "category": "EMOTIONAL", "type": "domain", "source_file": "domains.json"}, "emot_aware": {"code": "emot_aware", "name": "Emotional Awareness", "description": "Activities that develop recognition and understanding of emotional states in oneself and others.", "category": "EMOTIONAL", "type": "domain", "source_file": "domains.json"}, "emot_regulate": {"code": "emot_regulate", "name": "Emotion Regulation", "description": "Practices for managing emotional responses, processing difficult feelings, and developing emotional resilience.", "category": "EMOTIONAL", "type": "domain", "source_file": "domains.json"}, "emot_express": {"code": "emot_express", "name": "Emotional Expression", "description": "Healthy outlets for communicating and releasing emotions through verbal, physical, or creative means.", "category": "EMOTIONAL", "type": "domain", "source_file": "domains.json"}, "emot_compass": {"code": "emot_compass", "name": "Self-Compassion", "description": "Activities that nurture kindness and understanding toward oneself, especially during challenges or failures.", "category": "EMOTIONAL", "type": "domain", "source_file": "domains.json"}, "emot_joy": {"code": "emot_joy", "name": "Joy & Pleasure", "description": "Intentional engagement in activities that bring happiness, satisfaction, or delight.", "category": "EMOTIONAL", "type": "domain", "source_file": "domains.json"}, "emot_stress": {"code": "emot_stress", "name": "Stress Management", "description": "Techniques and practices that reduce or help cope with stress and anxiety.", "category": "EMOTIONAL", "type": "domain", "source_file": "domains.json"}, "emot_forgive": {"code": "emot_forgive", "name": "Forgiveness & Letting Go", "description": "Processes for releasing resentment, anger, or attachment to past hurts or disappointments.", "category": "EMOTIONAL", "type": "domain", "source_file": "domains.json"}, "emot_comfort": {"code": "emot_comfort", "name": "Comfort & Nurturing", "description": "Activities that provide emotional security, soothing, and care during difficult times.", "category": "EMOTIONAL", "type": "domain", "source_file": "domains.json"}}}, "domains_spiritual_existential": {"description": "Domain codes for SPIRITUAL_EXISTENTIAL category", "codes": {"spiritual_existential": {"code": "spiritual_existential", "name": "Spiritual & Existential", "description": "a broad domain encompassing all spiritual activities.", "category": "SPIRITUAL_EXISTENTIAL", "type": "domain", "source_file": "domains.json"}, "spirit_purpose": {"code": "spirit_purpose", "name": "Purpose & Meaning", "description": "Exploration of personal significance, life direction, and sources of meaning beyond immediate goals.", "category": "SPIRITUAL_EXISTENTIAL", "type": "domain", "source_file": "domains.json"}, "spirit_connect": {"code": "spirit_connect", "name": "Spiritual Connection", "description": "Activities that foster connection with transcendent elements, whether through religious practices or secular spirituality.", "category": "SPIRITUAL_EXISTENTIAL", "type": "domain", "source_file": "domains.json"}, "spirit_ritual": {"code": "spirit_ritual", "name": "Ritual & Practice", "description": "Structured ceremonies or habits that connect to cultural, spiritual, or personal significance beyond their practical function.", "category": "SPIRITUAL_EXISTENTIAL", "type": "domain", "source_file": "domains.json"}, "spirit_nature": {"code": "spirit_nature", "name": "Nature Connection", "description": "Experiences that develop awareness of and relationship with the natural world and ecological systems.", "category": "SPIRITUAL_EXISTENTIAL", "type": "domain", "source_file": "domains.json"}, "spirit_transced": {"code": "spirit_transced", "name": "Transcendent Experience", "description": "Activities oriented toward peak experiences, states of flow, or moments that transcend ordinary consciousness.", "category": "SPIRITUAL_EXISTENTIAL", "type": "domain", "source_file": "domains.json"}, "spirit_death": {"code": "spirit_death", "name": "Mortality Contemplation", "description": "Reflective consideration of life's impermanence and one's relationship with the concept of death.", "category": "SPIRITUAL_EXISTENTIAL", "type": "domain", "source_file": "domains.json"}, "spirit_commun": {"code": "spirit_commun", "name": "Spiritual Community", "description": "Engagement with others who share similar existential or spiritual orientations, practices, or beliefs.", "category": "SPIRITUAL_EXISTENTIAL", "type": "domain", "source_file": "domains.json"}, "spirit_wisdom": {"code": "spirit_wisdom", "name": "Wisdom Traditions", "description": "Exploration of or participation in established philosophical, spiritual, or cultural wisdom traditions.", "category": "SPIRITUAL_EXISTENTIAL", "type": "domain", "source_file": "domains.json"}}}, "domains_exploratory_adventurous": {"description": "Domain codes for EXPLORATORY_ADVENTUROUS category", "codes": {"exploratory_adventurous": {"code": "exploratory_adventurous", "name": "Exploratory & Adventurous", "description": "a broad domain encompassing all exploratory and adventurous activities.", "category": "EXPLORATORY_ADVENTUROUS", "type": "domain", "source_file": "domains.json"}, "explor_travel": {"code": "explor_travel", "name": "Travel & Discovery", "description": "Activities involving geographical exploration, cultural immersion, or visiting new places.", "category": "EXPLORATORY_ADVENTUROUS", "type": "domain", "source_file": "domains.json"}, "explor_risk": {"code": "explor_risk", "name": "Controlled Risk-Taking", "description": "Activities that involve stepping beyond comfort zones with calculated challenges and reasonable safety measures.", "category": "EXPLORATORY_ADVENTUROUS", "type": "domain", "source_file": "domains.json"}, "explor_sensory": {"code": "explor_sensory", "name": "Sensory Exploration", "description": "Experiences that engage or expand awareness through the five senses in novel or intensive ways.", "category": "EXPLORATORY_ADVENTUROUS", "type": "domain", "source_file": "domains.json"}, "explor_cultural": {"code": "explor_cultural", "name": "Cultural Exploration", "description": "Engagement with unfamiliar traditions, perspectives, or creative expressions from different cultures.", "category": "EXPLORATORY_ADVENTUROUS", "type": "domain", "source_file": "domains.json"}, "explor_novel": {"code": "explor_novel", "name": "Novelty Seeking", "description": "Pursuit of new experiences, skills, or environments purely for the sake of freshness and variety.", "category": "EXPLORATORY_ADVENTUROUS", "type": "domain", "source_file": "domains.json"}, "explor_adren": {"code": "explor_adren", "name": "Adrenaline Activities", "description": "High-energy experiences that produce excitement, thrill, or exhilaration through physical or psychological intensity.", "category": "EXPLORATORY_ADVENTUROUS", "type": "domain", "source_file": "domains.json"}, "explor_improv": {"code": "explor_improv", "name": "Spontaneity & Improvisation", "description": "Activities with minimal planning that embrace unexpected outcomes and in-the-moment decision making.", "category": "EXPLORATORY_ADVENTUROUS", "type": "domain", "source_file": "domains.json"}, "explor_unknown": {"code": "explor_unknown", "name": "Embracing Uncertainty", "description": "Deliberate engagement with the unknown or unpredictable aspects of life or specific domains.", "category": "EXPLORATORY_ADVENTUROUS", "type": "domain", "source_file": "domains.json"}, "explor_digital": {"code": "explor_digital", "name": "Digital-Physical Hybrid", "description": "Activities that blend digital tools with physical actions, such as app-guided workouts, virtual reality experiences, or augmented reality exploration.", "category": "EXPLORATORY_ADVENTUROUS", "type": "domain", "source_file": "domains.json"}}}, "domains_productive_practical": {"description": "Domain codes for PRODUCTIVE_PRACTICAL category", "codes": {"productive_practical": {"code": "productive_practical", "name": "Productive & Practical", "description": "a broad domain encompassing all productive and practical activities.", "category": "PRODUCTIVE_PRACTICAL", "type": "domain", "source_file": "domains.json"}, "prod_organize": {"code": "prod_organize", "name": "Organization & Planning", "description": "Systems and activities for creating order, structure, and efficiency in physical spaces or processes.", "category": "PRODUCTIVE_PRACTICAL", "type": "domain", "source_file": "domains.json"}, "prod_habit": {"code": "prod_habit", "name": "Habit Formation", "description": "Development of consistent beneficial behaviors through intentional practice and routine building.", "category": "PRODUCTIVE_PRACTICAL", "type": "domain", "source_file": "domains.json"}, "prod_time": {"code": "prod_time", "name": "Time Management", "description": "Approaches to allocating time effectively based on priorities, energy levels, and productivity principles.", "category": "PRODUCTIVE_PRACTICAL", "type": "domain", "source_file": "domains.json"}, "prod_skill": {"code": "prod_skill", "name": "Practical Skill Development", "description": "Learning and application of concrete abilities useful in everyday life or specific contexts.", "category": "PRODUCTIVE_PRACTICAL", "type": "domain", "source_file": "domains.json"}, "prod_financial": {"code": "prod_financial", "name": "Financial Management", "description": "Activities related to budgeting, saving, investing, or otherwise managing personal economic resources.", "category": "PRODUCTIVE_PRACTICAL", "type": "domain", "source_file": "domains.json"}, "prod_career": {"code": "prod_career", "name": "Career Development", "description": "Advancement of professional skills, networks, or positioning within work contexts.", "category": "PRODUCTIVE_PRACTICAL", "type": "domain", "source_file": "domains.json"}, "prod_health": {"code": "prod_health", "name": "Health & Wellness Systems", "description": "Practical approaches to maintaining or improving physical and mental wellbeing through consistent practices.", "category": "PRODUCTIVE_PRACTICAL", "type": "domain", "source_file": "domains.json"}, "prod_home": {"code": "prod_home", "name": "Home Management", "description": "Activities related to maintaining, improving, or organizing living spaces for functionality and comfort.", "category": "PRODUCTIVE_PRACTICAL", "type": "domain", "source_file": "domains.json"}, "prod_transition": {"code": "prod_transition", "name": "Transition Moments", "description": "Activities designed for in-between times such as waiting, commuting, or brief breaks between tasks that maximize productivity during idle moments.", "category": "PRODUCTIVE_PRACTICAL", "type": "domain", "source_file": "domains.json"}}}, "domains_leisure_recreational": {"description": "Domain codes for LEISURE_RECREATIONAL category", "codes": {"leisure_recreational": {"code": "leisure_recreational", "name": "Leisure & Recreational", "description": "a broad domain encompassing all recreational and leisure activities.", "category": "LEISURE_RECREATIONAL", "type": "domain", "source_file": "domains.json"}, "leisure_relax": {"code": "leisure_relax", "name": "Relaxation", "description": "Activities specifically oriented toward rest, recovery, and the release of tension or effort.", "category": "LEISURE_RECREATIONAL", "type": "domain", "source_file": "domains.json"}, "leisure_play": {"code": "leisure_play", "name": "Play & Games", "description": "Engagement in activities pursued primarily for enjoyment rather than external outcomes, including structured games or free play.", "category": "LEISURE_RECREATIONAL", "type": "domain", "source_file": "domains.json"}, "leisure_entertain": {"code": "leisure_entertain", "name": "Entertainment Consumption", "description": "Enjoyment of media, performances, or experiences created by others, such as films, music, or shows.", "category": "LEISURE_RECREATIONAL", "type": "domain", "source_file": "domains.json"}, "leisure_collect": {"code": "leisure_collect", "name": "Collection & Curation", "description": "Activities involving gathering, organizing, and appreciating objects or information of personal interest.", "category": "LEISURE_RECREATIONAL", "type": "domain", "source_file": "domains.json"}, "leisure_nature": {"code": "leisure_nature", "name": "Recreational Nature Activities", "description": "Enjoyable outdoor pursuits that connect with natural environments in a leisurely context.", "category": "LEISURE_RECREATIONAL", "type": "domain", "source_file": "domains.json"}, "leisure_social": {"code": "leisure_social", "name": "Social Recreation", "description": "Leisure activities specifically focused on enjoying time with others in low-pressure social contexts.", "category": "LEISURE_RECREATIONAL", "type": "domain", "source_file": "domains.json"}, "leisure_hobby": {"code": "leisure_hobby", "name": "<PERSON><PERSON> Participation", "description": "Regular engagement in non-professional interests that provide satisfaction and enjoyment.", "category": "LEISURE_RECREATIONAL", "type": "domain", "source_file": "domains.json"}, "leisure_festive": {"code": "leisure_festive", "name": "Celebration & Festivity", "description": "Participation in events or traditions that mark special occasions or cultural significance in enjoyable ways.", "category": "LEISURE_RECREATIONAL", "type": "domain", "source_file": "domains.json"}}}, "domains_general": {"description": "Domain codes for GENERAL category", "codes": {"general": {"code": "general", "name": "General", "description": "A fallback domain for activities that do not fit into specific categories or when domain classification is uncertain.", "category": "GENERAL", "type": "domain", "source_file": "domains.json"}}}, "traits_honestyhumility": {"description": "HEXACO trait codes for HONESTYHUMILITY", "codes": {"honesty_sincerity": {"code": "honesty_sincerity", "name": "Sincerity", "description": "Genuineness in self-expression and relationships without manipulation.", "category": "HONESTYHUMILITY", "type": "trait", "source_file": "user_profile_catalog.json"}, "honesty_fairness": {"code": "honesty_fairness", "name": "Fairness", "description": "Tendency to avoid exploiting others for personal gain.", "category": "HONESTYHUMILITY", "type": "trait", "source_file": "user_profile_catalog.json"}, "honesty_greed_avoidance": {"code": "honesty_greed_avoidance", "name": "Greed Avoidance", "description": "Level of disinterest in luxury, wealth, and social status.", "category": "HONESTYHUMILITY", "type": "trait", "source_file": "user_profile_catalog.json"}, "honesty_modesty": {"code": "honesty_modesty", "name": "Modesty", "description": "Tendency to be humble and unassuming about achievements.", "category": "HONESTYHUMILITY", "type": "trait", "source_file": "user_profile_catalog.json"}}}, "traits_emotionality": {"description": "HEXACO trait codes for EMOTIONALITY", "codes": {"emotion_fearfulness": {"code": "emotion_fearfulness", "name": "Fearfulness", "description": "Tendency to experience fear in response to potential dangers.", "category": "EMOTIONALITY", "type": "trait", "source_file": "user_profile_catalog.json"}, "emotion_anxiety": {"code": "emotion_anxiety", "name": "Anxiety", "description": "Tendency to worry in a variety of contexts.", "category": "EMOTIONALITY", "type": "trait", "source_file": "user_profile_catalog.json"}, "emotion_dependence": {"code": "emotion_dependence", "name": "Dependence", "description": "Need for emotional support and reassurance from others.", "category": "EMOTIONALITY", "type": "trait", "source_file": "user_profile_catalog.json"}, "emotion_sentimentality": {"code": "emotion_sentimentality", "name": "Sentimentality", "description": "Tendency to form strong emotional bonds and empathic responses.", "category": "EMOTIONALITY", "type": "trait", "source_file": "user_profile_catalog.json"}}}, "traits_extraversion": {"description": "HEXACO trait codes for EXTRAVERSION", "codes": {"extra_self_esteem": {"code": "extra_self_esteem", "name": "Social Self-Esteem", "description": "Confidence and positive self-evaluation in social contexts.", "category": "EXTRAVERSION", "type": "trait", "source_file": "user_profile_catalog.json"}, "extra_social_boldness": {"code": "extra_social_boldness", "name": "Social Boldness", "description": "<PERSON><PERSON><PERSON> in a variety of social situations and leadership roles.", "category": "EXTRAVERSION", "type": "trait", "source_file": "user_profile_catalog.json"}, "extra_sociability": {"code": "extra_sociability", "name": "Sociability", "description": "Enjoyment of social gatherings and interactions with others.", "category": "EXTRAVERSION", "type": "trait", "source_file": "user_profile_catalog.json"}, "extra_liveliness": {"code": "extra_liveliness", "name": "Liveliness", "description": "Energy level and enthusiasm in social and activity contexts.", "category": "EXTRAVERSION", "type": "trait", "source_file": "user_profile_catalog.json"}}}, "traits_agreeableness": {"description": "HEXACO trait codes for AGREEABLENESS", "codes": {"agree_forgiveness": {"code": "agree_forgiveness", "name": "Forgiveness", "description": "Willingness to trust and forgive those who have caused harm.", "category": "AGREEABLENESS", "type": "trait", "source_file": "user_profile_catalog.json"}, "agree_gentleness": {"code": "agree_gentleness", "name": "Gentleness", "description": "Tendency to be mild and lenient in interactions with others.", "category": "AGREEABLENESS", "type": "trait", "source_file": "user_profile_catalog.json"}, "agree_flexibility": {"code": "agree_flexibility", "name": "Flexibility", "description": "Willingness to compromise and cooperate with others.", "category": "AGREEABLENESS", "type": "trait", "source_file": "user_profile_catalog.json"}, "agree_patience": {"code": "agree_patience", "name": "Patience", "description": "Tendency to remain calm rather than becoming angry.", "category": "AGREEABLENESS", "type": "trait", "source_file": "user_profile_catalog.json"}}}, "traits_conscientiousness": {"description": "HEXACO trait codes for CONSCIENTIOUSNESS", "codes": {"consc_organization": {"code": "consc_organization", "name": "Organization", "description": "Tendency to seek order and structure in the physical environment.", "category": "CONSCIENTIOUSNESS", "type": "trait", "source_file": "user_profile_catalog.json"}, "consc_diligence": {"code": "consc_diligence", "name": "Diligence", "description": "Work ethic and persistence in pursuing goals.", "category": "CONSCIENTIOUSNESS", "type": "trait", "source_file": "user_profile_catalog.json"}, "consc_perfectionism": {"code": "consc_perfectionism", "name": "Perfectionism", "description": "Thoroughness and concern with details and accuracy.", "category": "CONSCIENTIOUSNESS", "type": "trait", "source_file": "user_profile_catalog.json"}, "consc_prudence": {"code": "consc_prudence", "name": "Prudence", "description": "Tendency to deliberate carefully and inhibit impulses.", "category": "CONSCIENTIOUSNESS", "type": "trait", "source_file": "user_profile_catalog.json"}}}, "traits_openness": {"description": "HEXACO trait codes for OPENNESS", "codes": {"open_aesthetic": {"code": "open_aesthetic", "name": "Aesthetic Appreciation", "description": "Enjoyment of beauty in art, music, and nature.", "category": "OPENNESS", "type": "trait", "source_file": "user_profile_catalog.json"}, "open_inquisitive": {"code": "open_inquisitive", "name": "Inquisitiveness", "description": "Interest in exploring new ideas and understanding complex concepts.", "category": "OPENNESS", "type": "trait", "source_file": "user_profile_catalog.json"}, "open_creativity": {"code": "open_creativity", "name": "Creativity", "description": "Preference for innovation and experimentation.", "category": "OPENNESS", "type": "trait", "source_file": "user_profile_catalog.json"}, "open_unconventional": {"code": "open_unconventional", "name": "Unconventionality", "description": "Willingness to accept the unusual and challenge tradition.", "category": "OPENNESS", "type": "trait", "source_file": "user_profile_catalog.json"}}}, "limitations": {"description": "User limitation codes", "codes": {}}}, "all_codes": {"ind_healthcare_hospital_ward": {"code": "ind_healthcare_hospital_ward", "name": "Hospital General Ward", "description": "A typical hospital inpatient ward where multiple patients receive care in a shared space with beds separated by curtains or partitions. Features regular monitoring by nursing staff, medical equipment, and limited personal space.", "category": "cultural", "type": "environment", "source_file": "environments.json"}, "ind_healthcare_private_room": {"code": "ind_healthcare_private_room", "name": "Hospital Private Room", "description": "An individual hospital room designed for a single patient, offering increased privacy, personal space, and often amenities like a private bathroom, television, and space for visitors.", "category": "cultural", "type": "environment", "source_file": "environments.json"}, "ind_healthcare_icu": {"code": "ind_healthcare_icu", "name": "Intensive Care Unit", "description": "A specialized hospital department providing intensive treatment and monitoring for critically ill patients. Features advanced life support equipment, continuous monitoring, and a high staff-to-patient ratio.", "category": "cultural", "type": "environment", "source_file": "environments.json"}, "ind_healthcare_waiting_room": {"code": "ind_healthcare_waiting_room", "name": "Medical Waiting Room", "description": "A communal space where patients and their companions wait before appointments or procedures. Usually furnished with chairs, reading materials, and sometimes a television to help pass time.", "category": "cultural", "type": "environment", "source_file": "environments.json"}, "ind_healthcare_outpatient_clinic": {"code": "ind_healthcare_outpatient_clinic", "name": "Outpatient Clinic", "description": "A medical facility providing diagnosis, care, and treatment for patients who do not require overnight hospitalization. Typically includes examination rooms, specialty equipment, and consultation spaces.", "category": "cultural", "type": "environment", "source_file": "environments.json"}, "ind_healthcare_physical_therapy": {"code": "ind_healthcare_physical_therapy", "name": "Physical Therapy Center", "description": "A specialized facility equipped for rehabilitation and physical therapy services. Features exercise equipment, treatment tables, and open spaces for movement-based therapies.", "category": "cultural", "type": "environment", "source_file": "environments.json"}, "ind_healthcare_mental_health_facility": {"code": "ind_healthcare_mental_health_facility", "name": "Mental Health Facility", "description": "A facility designed for the treatment and support of individuals with mental health conditions. Includes therapy rooms, community spaces, and secure environments tailored to various psychiatric needs.", "category": "cultural", "type": "environment", "source_file": "environments.json"}, "ind_healthcare_dental_office": {"code": "ind_healthcare_dental_office", "name": "Dental Office", "description": "A healthcare facility specialized in oral health, featuring dental chairs, specialized equipment for examination and treatment of teeth and gums, and often x-ray capabilities.", "category": "cultural", "type": "environment", "source_file": "environments.json"}, "ind_healthcare_laboratory": {"code": "ind_healthcare_laboratory", "name": "Medical Laboratory", "description": "A specialized facility where clinical specimens are analyzed to obtain information about the health of a patient. Features specialized equipment for testing, analyzing samples, and research.", "category": "cultural", "type": "environment", "source_file": "environments.json"}, "ind_healthcare_imaging_center": {"code": "ind_healthcare_imaging_center", "name": "Medical Imaging Center", "description": "A facility specializing in diagnostic imaging services such as X-rays, MRIs, CT scans, and ultrasounds to help diagnose and monitor medical conditions.", "category": "cultural", "type": "environment", "source_file": "environments.json"}, "ind_healthcare_pharmacy": {"code": "ind_healthcare_pharmacy", "name": "Pharmacy", "description": "A facility where medications are dispensed, medication information is provided, and sometimes basic health screenings are offered. Features medication storage, consultation areas, and health product displays.", "category": "cultural", "type": "environment", "source_file": "environments.json"}, "ind_healthcare_hospice": {"code": "ind_healthcare_hospice", "name": "Hospice Care Center", "description": "A facility specializing in care for individuals with terminal illnesses, focusing on comfort, dignity, and quality of life rather than curative treatment. Designed to create a home-like, peaceful environment.", "category": "cultural", "type": "environment", "source_file": "environments.json"}, "ind_healthcare_emergency_room": {"code": "ind_healthcare_emergency_room", "name": "Emergency Department", "description": "A critical care facility designed for rapid treatment of acute illnesses and injuries. Features specialized equipment for emergency medicine, triage areas, and trauma rooms.", "category": "cultural", "type": "environment", "source_file": "environments.json"}, "ind_healthcare_rehab_center": {"code": "ind_healthcare_rehab_center", "name": "Rehabilitation Center", "description": "A facility dedicated to helping patients recover physical, mental, and cognitive abilities after illness, injury, or surgery through therapeutic programs and specialized care.", "category": "cultural", "type": "environment", "source_file": "environments.json"}, "out_healthcare_healing_garden": {"code": "out_healthcare_healing_garden", "name": "Therapeutic Healing Garden", "description": "An outdoor space specifically designed to promote healing and wellbeing within a healthcare setting. Features sensory plants, accessible pathways, seating areas, and elements designed to reduce stress and promote recovery.", "category": "cultural", "type": "environment", "source_file": "environments.json"}, "ind_healthcare_maternity_ward": {"code": "ind_healthcare_maternity_ward", "name": "Maternity Ward", "description": "A specialized hospital unit devoted to caring for women during childbirth and postpartum recovery, as well as their newborn babies. Features specialized equipment for labor, delivery, and infant care.", "category": "cultural", "type": "environment", "source_file": "environments.json"}, "ind_cultural_museum": {"code": "ind_cultural_museum", "name": "Museum", "description": "An indoor space dedicated to the preservation and exhibition of artistic, cultural, historical, or scientific artifacts. Museums provide a quiet, contemplative environment for learning and appreciation.", "category": "transportation", "type": "environment", "source_file": "environments.json"}, "ind_cultural_artgallery": {"code": "ind_cultural_artgallery", "name": "Art Gallery", "description": "A dedicated indoor space for displaying visual art, primarily paintings, sculptures, photographs, and other aesthetic creations. Galleries provide a focused environment for artistic appreciation and contemplation.", "category": "transportation", "type": "environment", "source_file": "environments.json"}, "ind_cultural_theater": {"code": "ind_cultural_theater", "name": "Theater", "description": "An indoor venue designed for live performances such as plays, musicals, dance, or other staged productions. Theaters feature seating arrangements focused on a central stage or performance space.", "category": "transportation", "type": "environment", "source_file": "environments.json"}, "ind_cultural_concerthall": {"code": "ind_cultural_concerthall", "name": "Concert Hall", "description": "A specialized indoor venue designed for musical performances, featuring exceptional acoustics and seating oriented toward a central stage. Concert halls provide an immersive audio experience for audience members.", "category": "transportation", "type": "environment", "source_file": "environments.json"}, "ind_cultural_library": {"code": "ind_cultural_library", "name": "Library", "description": "An indoor space dedicated to the collection and preservation of books, periodicals, and other media. Libraries provide quiet environments for reading, research, study, and community programs.", "category": "transportation", "type": "environment", "source_file": "environments.json"}, "ind_cultural_cinemacomplex": {"code": "ind_cultural_cinemacomplex", "name": "Cinema Complex", "description": "An indoor facility with multiple screening rooms for showing films. Cinema complexes include comfortable seating, large screens, and advanced audio systems for immersive viewing experiences.", "category": "transportation", "type": "environment", "source_file": "environments.json"}, "ind_cultural_artisanworkshop": {"code": "ind_cultural_artisanworkshop", "name": "Artisan Workshop", "description": "An indoor space equipped for creating handcrafted work in traditional arts, crafts, or trades. Workshops typically include specialized tools, materials, and workstations for various creative practices.", "category": "transportation", "type": "environment", "source_file": "environments.json"}, "ind_cultural_dancestudio": {"code": "ind_cultural_dancestudio", "name": "Dance Studio", "description": "An indoor space designed specifically for dance practice, training, and rehearsal. Dance studios feature appropriate flooring, mirrors, barres, and sound systems to support various dance forms.", "category": "transportation", "type": "environment", "source_file": "environments.json"}, "ind_cultural_recordingstudio": {"code": "ind_cultural_recordingstudio", "name": "Recording Studio", "description": "A specially designed indoor space for recording audio with controlled acoustics, sound insulation, and professional recording equipment. Recording studios provide isolated environments for music production, voiceover, and other audio work.", "category": "transportation", "type": "environment", "source_file": "environments.json"}, "ind_cultural_culinaryarts": {"code": "ind_cultural_culinaryarts", "name": "Culinary Arts Center", "description": "An indoor facility dedicated to cooking education, demonstrations, and cultural food experiences. Culinary centers include cooking stations, specialized equipment, and areas for instruction and tasting.", "category": "transportation", "type": "environment", "source_file": "environments.json"}, "out_cultural_amphitheater": {"code": "out_cultural_amphitheater", "name": "Outdoor Amphitheater", "description": "An open-air venue with a central performance space surrounded by tiered seating. Amphitheaters are designed for theatrical performances, concerts, and other cultural events in natural settings.", "category": "transportation", "type": "environment", "source_file": "environments.json"}, "out_cultural_heritage": {"code": "out_cultural_heritage", "name": "Heritage Site", "description": "An outdoor location of historical or cultural significance, often preserved as a landmark or monument. Heritage sites include ruins, historic buildings, archaeological sites, and culturally important landscapes.", "category": "transportation", "type": "environment", "source_file": "environments.json"}, "out_cultural_sculpture": {"code": "out_cultural_sculpture", "name": "Sculpture Garden", "description": "An outdoor area dedicated to displaying three-dimensional artworks in a landscaped setting. Sculpture gardens combine artistic appreciation with natural elements for a multisensory experience.", "category": "transportation", "type": "environment", "source_file": "environments.json"}, "out_cultural_festival": {"code": "out_cultural_festival", "name": "Festival Grounds", "description": "Temporary or permanent outdoor spaces designed for cultural festivals, musical events, and community celebrations. Festival grounds typically accommodate large crowds with multiple activity areas.", "category": "transportation", "type": "environment", "source_file": "environments.json"}, "ind_cultural_arthouse": {"code": "ind_cultural_arthouse", "name": "Art House Cinema", "description": "A specialized indoor cinema focusing on independent, foreign, documentary, or experimental films. Art house cinemas provide spaces for viewing films outside mainstream commercial releases.", "category": "transportation", "type": "environment", "source_file": "environments.json"}, "ind_cultural_operahouse": {"code": "ind_cultural_operahouse", "name": "Opera House", "description": "A grand indoor venue specifically designed for operatic performances, featuring exceptional acoustics, elaborate stage facilities, and formal audience seating. Opera houses embody cultural tradition and artistic excellence.", "category": "transportation", "type": "environment", "source_file": "environments.json"}, "out_cultural_openairdance": {"code": "out_cultural_openairdance", "name": "Open-Air Dance Plaza", "description": "A public outdoor space designed or adapted for community dance events, often featuring a central dance floor with surrounding seating areas. These spaces encourage spontaneous or organized social dancing.", "category": "transportation", "type": "environment", "source_file": "environments.json"}, "ind_cultural_historicalarchive": {"code": "ind_cultural_historicalarchive", "name": "Historical Archive", "description": "A dedicated indoor facility for preserving, organizing, and providing access to historical documents, records, and artifacts. Archives maintain controlled environments to protect sensitive materials while enabling research.", "category": "transportation", "type": "environment", "source_file": "environments.json"}, "ind_cultural_musicschool": {"code": "ind_cultural_musicschool", "name": "Music School", "description": "An educational facility dedicated to music instruction featuring practice rooms, classrooms, performance spaces, and specialized equipment. Music schools provide environments optimized for learning and practicing musical skills.", "category": "transportation", "type": "environment", "source_file": "environments.json"}, "ind_cultural_literarysalon": {"code": "ind_cultural_literarysalon", "name": "Literary Salon", "description": "An intimate indoor space dedicated to literary discussion, readings, and intellectual exchange. Literary salons provide comfortable seating, appropriate acoustics, and an atmosphere conducive to sharing and discussing written works.", "category": "transportation", "type": "environment", "source_file": "environments.json"}, "ind_residential_living_room": {"code": "ind_residential_living_room", "name": "Living Room", "description": "The main social space in a home, typically featuring comfortable seating, entertainment options, and decorative elements. Serves as a gathering place for family and guests.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "ind_residential_bedroom": {"code": "ind_residential_bedroom", "name": "Bedroom", "description": "A private space primarily for sleeping and personal activities. Often personalized to reflect individual tastes and preferences, serving as a retreat from shared household spaces.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "ind_residential_home_office": {"code": "ind_residential_home_office", "name": "Home Office", "description": "A dedicated workspace within a residential setting, designed for productivity and focus. Typically equipped with work essentials and minimized distractions.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "ind_residential_kitchen": {"code": "ind_residential_kitchen", "name": "Kitchen", "description": "The culinary heart of the home where meals are prepared and often shared. Combines functionality with social aspects of food preparation and consumption.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "ind_residential_dining_room": {"code": "ind_residential_dining_room", "name": "Dining Room", "description": "A dedicated space for formal and family meals. Combines functionality with social interaction around food consumption and conversation.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "ind_residential_bathroom": {"code": "ind_residential_bathroom", "name": "Bathroom", "description": "A private space for personal hygiene and self-care routines. Combines functionality with comfort elements that support wellness activities.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "ind_residential_basement": {"code": "ind_residential_basement", "name": "Basement", "description": "A below-ground level space in a home, often used for storage, recreation, or as a flexible multi-purpose area. Typically less finished than main living areas.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "ind_residential_attic": {"code": "ind_residential_attic", "name": "Attic", "description": "An upper-level space below the roof, often used for storage or converted into living space. Characterized by sloped ceilings and sometimes limited accessibility.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "ind_residential_hallway": {"code": "ind_residential_hallway", "name": "Hallway", "description": "A transitional space connecting different rooms in a home. Primarily functional but often decorated with personal items or artwork.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "ind_residential_guest_room": {"code": "ind_residential_guest_room", "name": "Guest Room", "description": "A bedroom specifically designated for visitors. Often neutral in decor and equipped with essentials for temporary stays.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "out_residential_backyard": {"code": "out_residential_backyard", "name": "Backyard", "description": "The private outdoor space behind a residential dwelling. Often used for recreation, relaxation, gardening, and outdoor entertaining.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "out_residential_front_yard": {"code": "out_residential_front_yard", "name": "Front Yard", "description": "The outdoor space between a residential dwelling and the street. Functions as both a transitional space and a public-facing representation of the home.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "ind_residential_sunroom": {"code": "ind_residential_sunroom", "name": "Sunroom", "description": "A room with extensive window area designed to allow in abundant natural light, often serving as a transitional space between indoors and outdoors.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "out_residential_patio_deck": {"code": "out_residential_patio_deck", "name": "Patio/Deck", "description": "An outdoor living space attached to a residence, typically featuring hard surfaces and designed for outdoor dining, entertaining, and relaxation.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "ind_residential_laundry_room": {"code": "ind_residential_laundry_room", "name": "Laundry Room", "description": "A utility space dedicated to washing, drying, and sometimes folding clothes. Primarily functional but increasingly designed for comfort and efficiency.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "ind_residential_home_gym": {"code": "ind_residential_home_gym", "name": "Home Gym", "description": "A dedicated space for physical exercise within a residence, equipped with fitness equipment and often designed to motivate physical activity.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "ind_residential_hobby_room": {"code": "ind_residential_hobby_room", "name": "Hobby Room", "description": "A space dedicated to creative and leisure pursuits, customized for specific interests such as crafting, model building, or artistic endeavors.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "ind_residential_game_room": {"code": "ind_residential_game_room", "name": "Game Room", "description": "A recreational space designed for entertainment through games, featuring video games, board games, or recreational equipment like pool tables or ping pong.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "out_residential_garden": {"code": "out_residential_garden", "name": "Garden", "description": "A cultivated outdoor space dedicated to growing plants, whether ornamental, edible, or both. Provides connection with natural cycles and opportunities for nurturing living things.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "ind_residential_meditation_space": {"code": "ind_residential_meditation_space", "name": "Meditation Space", "description": "A quiet area specifically designated for mindfulness, meditation, or spiritual practices. Designed to minimize distractions and foster inner calm.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "ind_residential_reading_nook": {"code": "ind_residential_reading_nook", "name": "Reading Nook", "description": "A small, cozy corner designed specifically for reading and quiet contemplation. Often featuring comfortable seating and good lighting in a secluded area.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "ind_quiet_space": {"code": "ind_quiet_space", "name": "Quiet Indoor Space", "description": "A flexible indoor environment with minimal noise and distractions, suitable for focused activities such as meditation, reading, or brief wellness practices.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "ind_micro_space": {"code": "ind_micro_space", "name": "Micro-Activity Space", "description": "Very small spaces suitable for brief activities such as desk areas, corners of rooms, or even standing spaces. Designed for 2-15 minute wellness breaks.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "ind_digital_enabled": {"code": "ind_digital_enabled", "name": "Digital-Enabled Indoor Space", "description": "Indoor space equipped with digital devices and internet connectivity for hybrid digital-physical activities such as app-guided workouts or virtual reality experiences.", "category": "residential", "type": "environment", "source_file": "environments.json"}, "out_nat_forest": {"code": "out_nat_forest", "name": "Forest", "description": "A wooded area with a variety of trees, plants, and wildlife. Offers natural shade, biodiversity, and opportunities for hiking, wildlife observation, and nature connection.", "category": "natural", "type": "environment", "source_file": "environments.json"}, "out_nat_beach": {"code": "out_nat_beach", "name": "Beach", "description": "A sandy or rocky shoreline along a body of water (ocean, sea, lake, or river). Provides open space, water access, and sensory experiences including the sounds of waves and the feeling of sand underfoot.", "category": "natural", "type": "environment", "source_file": "environments.json"}, "out_nat_mountain": {"code": "out_nat_mountain", "name": "Mountain", "description": "Elevated terrain characterized by steep slopes, peaks, and valleys. Offers challenging physical activities, breathtaking views, and a sense of accomplishment when summits are reached.", "category": "natural", "type": "environment", "source_file": "environments.json"}, "out_nat_meadow": {"code": "out_nat_meadow", "name": "Meadow", "description": "An open grassland area with wildflowers and low vegetation. Provides wide-open space, contact with diverse plant life, and opportunities for relaxation and nature observation.", "category": "natural", "type": "environment", "source_file": "environments.json"}, "out_nat_desert": {"code": "out_nat_desert", "name": "Desert", "description": "Arid landscape with minimal vegetation, characterized by sand, rocks, and extreme temperature variations. Offers unique geological features, wide-open spaces, and clarity of night skies.", "category": "natural", "type": "environment", "source_file": "environments.json"}, "out_nat_wetland": {"code": "out_nat_wetland", "name": "Wetland", "description": "Areas saturated with water, such as marshes, swamps, and bogs. Rich in biodiversity, these environments offer unique plant and animal observation opportunities and peaceful water features.", "category": "natural", "type": "environment", "source_file": "environments.json"}, "out_nat_river": {"code": "out_nat_river", "name": "River", "description": "Flowing body of water with surrounding riverbanks and shoreline. Provides opportunities for water activities, wildlife observation, and the soothing sounds of flowing water.", "category": "natural", "type": "environment", "source_file": "environments.json"}, "out_nat_lake": {"code": "out_nat_lake", "name": "Lake", "description": "Still body of water surrounded by shoreline and natural features. Offers calm water activities, scenic views, and a peaceful atmosphere for reflection and recreation.", "category": "natural", "type": "environment", "source_file": "environments.json"}, "out_nat_cliffside": {"code": "out_nat_cliffside", "name": "Cliffside", "description": "Steep rock face overlooking the surrounding landscape, often with dramatic views. Provides perspectives on vast expanses, challenging activities, and a sense of exposure to the elements.", "category": "natural", "type": "environment", "source_file": "environments.json"}, "out_nat_garden": {"code": "out_nat_garden", "name": "Garden", "description": "Cultivated outdoor space with designed plantings, paths, and features. Combines natural elements with human design to create a space for relaxation, observation, and engagement with plants.", "category": "natural", "type": "environment", "source_file": "environments.json"}, "out_nat_tundra": {"code": "out_nat_tundra", "name": "<PERSON><PERSON>", "description": "Cold, treeless plain with permafrost underground. Characterized by vast open spaces, extreme conditions, and unique flora and fauna adapted to harsh environments.", "category": "natural", "type": "environment", "source_file": "environments.json"}, "out_nat_savanna": {"code": "out_nat_savanna", "name": "Savanna", "description": "Grassland ecosystem with scattered trees and diverse wildlife. Provides open vistas, wildlife observation opportunities, and connection to primal landscapes.", "category": "natural", "type": "environment", "source_file": "environments.json"}, "ind_nat_conservatory": {"code": "ind_nat_conservatory", "name": "Conservatory", "description": "Indoor space with glass walls and ceiling designed to house and display plants. Provides a controlled natural environment for plant observation, learning, and relaxation in all weather conditions.", "category": "natural", "type": "environment", "source_file": "environments.json"}, "ind_nat_atrium": {"code": "ind_nat_atrium", "name": "Indoor Atrium", "description": "Open central space within a building, often featuring plants, water features, and natural light. Combines architectural elements with natural features to create a transitional space between indoors and outdoors.", "category": "natural", "type": "environment", "source_file": "environments.json"}, "ind_nat_botanical_garden": {"code": "ind_nat_botanical_garden", "name": "Indoor Botanical Garden", "description": "Large indoor facility dedicated to plant collection, cultivation, and display. Offers educational opportunities, diverse plant experiences, and calm natural spaces regardless of outside weather.", "category": "natural", "type": "environment", "source_file": "environments.json"}, "ind_nat_aquarium": {"code": "ind_nat_aquarium", "name": "Aquarium", "description": "Indoor facility housing aquatic life in tanks and habitats. Provides opportunities to observe underwater ecosystems and marine life in a controlled environment with educational components.", "category": "natural", "type": "environment", "source_file": "environments.json"}, "ind_nat_indoor_waterfall": {"code": "ind_nat_indoor_waterfall", "name": "Indoor Waterfall Environment", "description": "Built environment featuring artificial waterfalls, streams, and related natural elements. Combines moving water, plants, and designed spaces to create a sensory-rich natural experience indoors.", "category": "natural", "type": "environment", "source_file": "environments.json"}, "out_nat_volcanic_area": {"code": "out_nat_volcanic_area", "name": "Volcanic Area", "description": "Landscape shaped by volcanic activity, featuring lava fields, geothermal features, and unique geology. Offers otherworldly scenery, dynamic natural processes, and educational opportunities about Earth\\'s forces.", "category": "natural", "type": "environment", "source_file": "environments.json"}, "out_nat_canyon": {"code": "out_nat_canyon", "name": "Canyon", "description": "Deep ravine between cliffs often carved by rivers over millennia. Provides dramatic vertical perspectives, challenging terrain, and exposure to geological history through visible rock layers.", "category": "natural", "type": "environment", "source_file": "environments.json"}, "out_nat_farm": {"code": "out_nat_farm", "name": "Rural Farm", "description": "Agricultural land with crops, livestock, and farm buildings. Provides connection to food production, seasonal cycles, and rural lifestyle. Features open fields, barns, and agricultural activities.", "category": "natural", "type": "environment", "source_file": "environments.json"}, "out_nat_coral_reef": {"code": "out_nat_coral_reef", "name": "Coral Reef", "description": "Underwater ecosystem built by colonies of coral polyps, teeming with marine life. Provides immersive aquatic experiences, colorful visual stimuli, and opportunities to observe complex ecological relationships.", "category": "natural", "type": "environment", "source_file": "environments.json"}, "ind_nat_terrarium": {"code": "ind_nat_terrarium", "name": "Terrarium Room", "description": "Space featuring multiple enclosed mini-ecosystems in glass containers. Provides opportunities to observe self-contained natural worlds and intricate ecological relationships at a small scale.", "category": "natural", "type": "environment", "source_file": "environments.json"}, "ind_rec_gym": {"code": "ind_rec_gym", "name": "Fitness Gym", "description": "A dedicated indoor facility with various exercise equipment, weight machines, cardio stations, and possibly group exercise rooms. Designed for physical conditioning, strength training, and general fitness activities.", "category": "recreational", "type": "environment", "source_file": "environments.json"}, "ind_rec_yoga_studio": {"code": "ind_rec_yoga_studio", "name": "Yoga Studio", "description": "A tranquil indoor space designed for yoga practice, meditation, and mindful movement. Features cushioned flooring, minimal aesthetic, and a calming atmosphere conducive to focus and inner awareness.", "category": "recreational", "type": "environment", "source_file": "environments.json"}, "ind_rec_bowling": {"code": "ind_rec_bowling", "name": "Bowling Alley", "description": "An indoor recreational facility featuring lanes for bowling, automatic scoring systems, and rental equipment. Often includes arcade games, food service, and social areas designed for casual entertainment.", "category": "recreational", "type": "environment", "source_file": "environments.json"}, "ind_rec_arcade": {"code": "ind_rec_arcade", "name": "Gaming Arcade", "description": "An indoor entertainment center filled with video games, redemption games, and interactive gaming experiences. Designed for casual play, competition, and social interaction around digital entertainment.", "category": "recreational", "type": "environment", "source_file": "environments.json"}, "ind_rec_movie_theater": {"code": "ind_rec_movie_theater", "name": "Movie Theater", "description": "An indoor venue designed for film viewing with large screens, immersive sound systems, and tiered seating. Offers a shared viewing experience in a controlled environment optimized for audiovisual entertainment.", "category": "recreational", "type": "environment", "source_file": "environments.json"}, "ind_rec_pool_hall": {"code": "ind_rec_pool_hall", "name": "Billiards Club", "description": "An indoor recreational space centered around billiards tables and sometimes featuring other table games like foosball or darts. Combines skill-based gaming with social interaction in a casual atmosphere.", "category": "recreational", "type": "environment", "source_file": "environments.json"}, "ind_rec_indoor_climbing": {"code": "ind_rec_indoor_climbing", "name": "Indoor Climbing Gym", "description": "An indoor facility with artificial climbing walls of various heights and difficulties, featuring holds, routes, and safety systems. Designed for sport climbing, bouldering, and training in a controlled environment.", "category": "recreational", "type": "environment", "source_file": "environments.json"}, "ind_rec_swimming_pool": {"code": "ind_rec_swimming_pool", "name": "Indoor Swimming Pool", "description": "A climate-controlled aquatic facility with one or more swimming pools designed for exercise, recreation, and instruction. May include lanes for lap swimming, diving areas, and shallow sections for casual use.", "category": "recreational", "type": "environment", "source_file": "environments.json"}, "ind_rec_dance_studio": {"code": "ind_rec_dance_studio", "name": "Dance Studio", "description": "A specialized indoor space for dance practice and instruction, featuring mirrors, barres, appropriate flooring, and sound systems. Designed for movement arts ranging from ballet to contemporary styles.", "category": "recreational", "type": "environment", "source_file": "environments.json"}, "ind_rec_board_game_cafe": {"code": "ind_rec_board_game_cafe", "name": "Board Game Café", "description": "A cozy indoor venue combining café service with an extensive library of board games, card games, and tabletop activities. Designed for social gaming experiences in a comfortable setting with food and beverage options.", "category": "recreational", "type": "environment", "source_file": "environments.json"}, "ind_rec_trampoline_park": {"code": "ind_rec_trampoline_park", "name": "Trampoline Park", "description": "An indoor recreational facility featuring interconnected trampolines, foam pits, dodgeball courts, and other bounce-based activities. Designed for high-energy play, physical activity, and adventurous recreation.", "category": "recreational", "type": "environment", "source_file": "environments.json"}, "out_rec_playground": {"code": "out_rec_playground", "name": "Public Playground", "description": "An outdoor recreational space designed primarily for children, featuring play equipment like swings, slides, climbing structures, and open areas. Promotes physical activity, social interaction, and imaginative play in a public setting.", "category": "recreational", "type": "environment", "source_file": "environments.json"}, "out_rec_skatepark": {"code": "out_rec_skatepark", "name": "Skate Park", "description": "An outdoor facility designed specifically for skateboarding, BMX riding, and sometimes inline skating. Features ramps, rails, pipes, bowls, and other structures for performing tricks and developing skills in action sports.", "category": "recreational", "type": "environment", "source_file": "environments.json"}, "out_rec_sports_field": {"code": "out_rec_sports_field", "name": "Sports Field Complex", "description": "An outdoor facility with multiple fields and courts for organized sports and casual play. May include soccer fields, baseball diamonds, tennis courts, and supporting amenities designed for team and individual sports activities.", "category": "recreational", "type": "environment", "source_file": "environments.json"}, "out_rec_beach": {"code": "out_rec_beach", "name": "Public Beach", "description": "A recreational shoreline area where land meets a body of water, typically featuring sand or pebbles, swimming areas, and various amenities. Supports swimming, sunbathing, beach sports, and passive enjoyment of the natural water setting.", "category": "recreational", "type": "environment", "source_file": "environments.json"}, "out_rec_golf_course": {"code": "out_rec_golf_course", "name": "Golf Course", "description": "A large outdoor facility designed for playing golf, featuring manicured grass areas, hazards, putting greens, and typically 9 or 18 holes. Combines elements of sport, leisure, social interaction, and appreciation of landscaped natural settings.", "category": "recreational", "type": "environment", "source_file": "environments.json"}, "out_rec_hiking_trail": {"code": "out_rec_hiking_trail", "name": "Hiking Trail System", "description": "A network of outdoor paths designed for pedestrian use through natural environments, with varying degrees of development, markers, and amenities. Facilitates exercise, nature exploration, and scenic appreciation.", "category": "recreational", "type": "environment", "source_file": "environments.json"}, "out_rec_dog_park": {"code": "out_rec_dog_park", "name": "Dog Park", "description": "A designated outdoor area where dogs can exercise and play off-leash in a controlled environment under owner supervision. Features fencing, waste stations, water sources, and sometimes separate areas for different dog sizes or temperaments.", "category": "recreational", "type": "environment", "source_file": "environments.json"}, "out_rec_water_park": {"code": "out_rec_water_park", "name": "Outdoor Water Park", "description": "A recreational facility featuring water-based attractions such as slides, wave pools, lazy rivers, and splash pads. Designed for aquatic play and cooling recreation during warm weather.", "category": "recreational", "type": "environment", "source_file": "environments.json"}, "out_rec_outdoor_fitness": {"code": "out_rec_outdoor_fitness", "name": "Outdoor Fitness Area", "description": "A public exercise zone featuring weather-resistant equipment designed for strength training, flexibility, and cardiovascular workouts. Allows for free, accessible physical activity in an open-air environment.", "category": "recreational", "type": "environment", "source_file": "environments.json"}, "ind_rec_escape_room": {"code": "ind_rec_escape_room", "name": "Escape Room Facility", "description": "An immersive indoor entertainment venue where teams solve puzzles, find clues, and complete objectives to \"escape\" from themed rooms within a time limit. Combines problem-solving with storytelling and team collaboration.", "category": "recreational", "type": "environment", "source_file": "environments.json"}, "ind_comm_cafe": {"code": "ind_comm_cafe", "name": "Café", "description": "A cozy indoor café environment with seating, ambient music, and a social atmosphere. Cafés typically offer beverages, light meals, and often provide Wi-Fi access for patrons who wish to work or socialize.", "category": "commercial", "type": "environment", "source_file": "environments.json"}, "ind_comm_restaurant": {"code": "ind_comm_restaurant", "name": "Restaurant", "description": "An establishment where meals are prepared and served to customers, ranging from casual diners to fine dining experiences. Restaurants provide tables, seating, and service staff in a controlled indoor environment.", "category": "commercial", "type": "environment", "source_file": "environments.json"}, "ind_comm_retail_store": {"code": "ind_comm_retail_store", "name": "Retail Store", "description": "A commercial space designed for selling goods to consumers. Retail stores feature display areas, merchandise arranged for browsing, checkout counters, and sometimes fitting rooms or testing areas for products.", "category": "commercial", "type": "environment", "source_file": "environments.json"}, "ind_comm_mall": {"code": "ind_comm_mall", "name": "Shopping Mall", "description": "A large indoor complex housing multiple retail stores, restaurants, and sometimes entertainment venues. Malls typically feature wide corridors for foot traffic, common areas, food courts, and various amenities for shoppers.", "category": "commercial", "type": "environment", "source_file": "environments.json"}, "ind_comm_supermarket": {"code": "ind_comm_supermarket", "name": "Supermarket", "description": "A large retail store specializing in groceries and household items, with organized aisles, refrigerated sections, and checkout areas. Supermarkets provide a self-service shopping experience for food and daily necessities.", "category": "commercial", "type": "environment", "source_file": "environments.json"}, "ind_comm_bookstore": {"code": "ind_comm_bookstore", "name": "Bookstore", "description": "A retail establishment specializing in books, with shelves organized by genre or topic, reading areas, and sometimes a café section. Bookstores provide a quiet, browsing-friendly environment for literature enthusiasts.", "category": "commercial", "type": "environment", "source_file": "environments.json"}, "ind_comm_cinema": {"code": "ind_comm_cinema", "name": "Movie Theater", "description": "An entertainment venue with screening rooms featuring large screens, seating arranged in rows, and advanced sound systems for showing films. Movie theaters also typically include concession stands and lobby areas.", "category": "commercial", "type": "environment", "source_file": "environments.json"}, "ind_comm_salon_spa": {"code": "ind_comm_salon_spa", "name": "Salon & Spa", "description": "A service-oriented business providing beauty treatments, hair styling, and wellness services. Salon and spa environments feature treatment stations, relaxation areas, and specialized equipment for various beauty and wellness services.", "category": "commercial", "type": "environment", "source_file": "environments.json"}, "ind_comm_hotel_lobby": {"code": "ind_comm_hotel_lobby", "name": "Hotel Lobby", "description": "The entrance and common area of a hotel, featuring reception desks, seating areas, and sometimes amenities like bars or restaurants. Hotel lobbies are designed to welcome guests and provide communal spaces for relaxation and casual meetings.", "category": "commercial", "type": "environment", "source_file": "environments.json"}, "ind_comm_arcade": {"code": "ind_comm_arcade", "name": "Gaming Arcade", "description": "An entertainment venue filled with video games, redemption games, and sometimes physical activities like bowling or mini-golf. Arcades feature a lively atmosphere with colorful lighting, game sounds, and various interactive entertainment options.", "category": "commercial", "type": "environment", "source_file": "environments.json"}, "ind_comm_bar_nightclub": {"code": "ind_comm_bar_nightclub", "name": "Bar or Nightclub", "description": "An establishment serving alcoholic beverages, often featuring music, dancing areas, and social spaces. Bars and nightclubs are designed for evening entertainment, socializing, and sometimes live performances.", "category": "commercial", "type": "environment", "source_file": "environments.json"}, "ind_comm_cowork_space": {"code": "ind_comm_cowork_space", "name": "Coworking Space", "description": "A shared working environment that provides desks, offices, meeting rooms, and amenities for independent professionals and small teams. Coworking spaces combine productive work environments with community-building elements.", "category": "commercial", "type": "environment", "source_file": "environments.json"}, "out_comm_market": {"code": "out_comm_market", "name": "Outdoor Market", "description": "An open-air commercial space with multiple vendors selling goods from stalls or booths. Outdoor markets may specialize in produce, crafts, antiques, or offer a variety of merchandise in a vibrant, community-oriented setting.", "category": "commercial", "type": "environment", "source_file": "environments.json"}, "out_comm_shopping_street": {"code": "out_comm_shopping_street", "name": "Shopping Street", "description": "A public thoroughfare lined with retail shops, restaurants, and other commercial establishments. Shopping streets combine pedestrian access with commercial offerings in an open, urban environment.", "category": "commercial", "type": "environment", "source_file": "environments.json"}, "out_comm_food_truck_park": {"code": "out_comm_food_truck_park", "name": "Food Truck Park", "description": "An outdoor venue where multiple mobile food vendors gather, typically featuring seating areas, sometimes entertainment, and a variety of cuisine options. Food truck parks offer casual dining in a social, often festive atmosphere.", "category": "commercial", "type": "environment", "source_file": "environments.json"}, "ind_comm_fitness_center": {"code": "ind_comm_fitness_center", "name": "Fitness Center", "description": "A facility equipped with exercise machines, weights, and sometimes group fitness rooms and pools for physical activity. Fitness centers provide structured environments for workouts, training, and health improvement.", "category": "commercial", "type": "environment", "source_file": "environments.json"}, "ind_edu_classroom": {"code": "ind_edu_classroom", "name": "Traditional Classroom", "description": "A standard classroom setting with desks arranged in rows or groups, a whiteboard/blackboard, and typical educational materials. This environment is designed for structured learning activities with teacher-led instruction and student participation.", "category": "educational", "type": "environment", "source_file": "environments.json"}, "ind_edu_lecture_hall": {"code": "ind_edu_lecture_hall", "name": "University Lecture Hall", "description": "A large tiered auditorium-style room designed for lectures and presentations to large groups of students. Often features fixed seating with small writing surfaces and excellent sight lines to the presenter area.", "category": "educational", "type": "environment", "source_file": "environments.json"}, "ind_edu_lab_science": {"code": "ind_edu_lab_science", "name": "Science Laboratory", "description": "A specialized classroom equipped with scientific equipment for practical experiments and demonstrations. Features lab benches, sinks, safety equipment, and storage for materials and specimens.", "category": "educational", "type": "environment", "source_file": "environments.json"}, "ind_edu_computer_lab": {"code": "ind_edu_computer_lab", "name": "Computer Laboratory", "description": "A dedicated room filled with computer workstations arranged for individual or collaborative work. Designed for digital learning, programming, research, and media production activities.", "category": "educational", "type": "environment", "source_file": "environments.json"}, "ind_edu_art_studio": {"code": "ind_edu_art_studio", "name": "Art Studio Classroom", "description": "A bright, open space designed for visual arts instruction and creation. Features easels, tables, sinks, storage for materials, and display areas for works in progress and completed pieces.", "category": "educational", "type": "environment", "source_file": "environments.json"}, "ind_edu_music_room": {"code": "ind_edu_music_room", "name": "Music Classroom", "description": "A specialized classroom for music instruction and practice, with acoustical treatments, instrument storage, and space for group performances. May include practice rooms attached.", "category": "educational", "type": "environment", "source_file": "environments.json"}, "ind_edu_library": {"code": "ind_edu_library", "name": "Educational Library", "description": "A quiet study environment with extensive collections of books, research materials, and digital resources. Features varied study spaces for individual and group work, reference assistance, and computer access.", "category": "educational", "type": "environment", "source_file": "environments.json"}, "ind_edu_maker_space": {"code": "ind_edu_maker_space", "name": "Educational Makerspace", "description": "A collaborative workshop environment equipped with tools and materials for hands-on creation, design, and engineering projects. Supports innovation, experimentation, and practical skill development.", "category": "educational", "type": "environment", "source_file": "environments.json"}, "ind_edu_theater": {"code": "ind_edu_theater", "name": "Educational Theater", "description": "A performance space with stage, lighting, seating, and backstage areas for theatrical productions, presentations, and performances. Used for drama education and school productions.", "category": "educational", "type": "environment", "source_file": "environments.json"}, "ind_edu_language_lab": {"code": "ind_edu_language_lab", "name": "Language Learning Laboratory", "description": "A specialized classroom equipped with audio-visual technology for language instruction. Features individual listening stations, recording capabilities, and interactive software for practice and assessment.", "category": "educational", "type": "environment", "source_file": "environments.json"}, "ind_edu_conference_room": {"code": "ind_edu_conference_room", "name": "Educational Conference Room", "description": "A meeting space designed for small to medium-sized groups, often used for faculty meetings, student group work, thesis defenses, and small seminars. Features a central table or configurable seating arrangement.", "category": "educational", "type": "environment", "source_file": "environments.json"}, "ind_edu_gymnasium": {"code": "ind_edu_gymnasium", "name": "Educational Gymnasium", "description": "A large indoor space designed for physical education, sports training, and school athletic events. Features a main court area, sometimes with bleachers, and often includes storage for sports equipment.", "category": "educational", "type": "environment", "source_file": "environments.json"}, "out_edu_campus_quad": {"code": "out_edu_campus_quad", "name": "University Quadrangle", "description": "An open outdoor space at the center of a college or university campus, typically surrounded by academic buildings. Used for casual gathering, studying, events, and transit between classes.", "category": "educational", "type": "environment", "source_file": "environments.json"}, "out_edu_school_yard": {"code": "out_edu_school_yard", "name": "School Playground/Yard", "description": "An outdoor space at a primary or secondary school designed for recreation, physical education, and social interaction during breaks. May include play equipment, sports areas, and seating.", "category": "educational", "type": "environment", "source_file": "environments.json"}, "out_edu_outdoor_classroom": {"code": "out_edu_outdoor_classroom", "name": "Outdoor Learning Space", "description": "A designated outdoor area designed specifically for educational activities. May include seating arrangements, teaching walls or boards, shade structures, and demonstration areas for nature study.", "category": "educational", "type": "environment", "source_file": "environments.json"}, "ind_edu_stem_lab": {"code": "ind_edu_stem_lab", "name": "STEM Learning Laboratory", "description": "An integrated laboratory environment designed for interdisciplinary science, technology, engineering, and mathematics education. Features flexible workspaces and tools for hands-on experimentation and project-based learning.", "category": "educational", "type": "environment", "source_file": "environments.json"}, "ind_edu_quiet_study": {"code": "ind_edu_quiet_study", "name": "Silent Study Room", "description": "A designated quiet space for focused individual study with minimal distractions. Features individual carrels or tables and strict noise policies to maintain a concentrated atmosphere.", "category": "educational", "type": "environment", "source_file": "environments.json"}, "ind_edu_virtual_reality": {"code": "ind_edu_virtual_reality", "name": "Virtual Reality Learning Lab", "description": "A specialized lab equipped with virtual reality headsets and stations for immersive educational experiences. Used for simulations, virtual field trips, and interactive 3D learning across various subjects.", "category": "educational", "type": "environment", "source_file": "environments.json"}, "ind_edu_sensory_room": {"code": "ind_edu_sensory_room", "name": "Educational Sensory Room", "description": "A specialized environment designed to develop students\\' sensory processing skills through various stimuli including lights, textures, sounds, and aromas. Particularly valuable for special education and early childhood development.", "category": "educational", "type": "environment", "source_file": "environments.json"}, "ind_edu_cooking_classroom": {"code": "ind_edu_cooking_classroom", "name": "Culinary Education Kitchen", "description": "A specialized classroom equipped with cooking stations, appliances, and preparation areas for culinary education. Designed for food science, nutrition, and cooking skills instruction.", "category": "educational", "type": "environment", "source_file": "environments.json"}, "ind_prof_office_private": {"code": "ind_prof_office_private", "name": "Private Office", "description": "A dedicated office space for a single professional, typically enclosed with walls and a door offering privacy for focused work, confidential calls, and meetings. Often contains a desk, ergonomic chair, computer setup, filing cabinets, and personal storage.", "category": "professional", "type": "environment", "source_file": "environments.json"}, "ind_prof_cubicle": {"code": "ind_prof_cubicle", "name": "Office Cubicle", "description": "A partially enclosed workspace within a larger office floor plan, separated by dividers or partitions that provide limited visual privacy but little sound isolation. Typically includes a desk, computer workstation, and limited personal storage.", "category": "professional", "type": "environment", "source_file": "environments.json"}, "ind_prof_openplan": {"code": "ind_prof_openplan", "name": "Open-Plan Office", "description": "A large, open workspace where multiple employees work without walls or partitions between them. Designed to encourage collaboration and communication, but often lacks privacy and can be noisy. Usually features clusters of desks or workstations grouped by team or function.", "category": "professional", "type": "environment", "source_file": "environments.json"}, "ind_prof_conference": {"code": "ind_prof_conference", "name": "Conference Room", "description": "A dedicated meeting space designed for formal gatherings, presentations, and discussions. Usually features a large central table with multiple chairs, presentation equipment, and sometimes video conferencing capabilities. Designed for group collaboration and decision-making.", "category": "professional", "type": "environment", "source_file": "environments.json"}, "ind_prof_breakroom": {"code": "ind_prof_breakroom", "name": "Office Break Room", "description": "A casual space within a workplace designated for employees to take breaks, eat meals, and socialize. Typically includes kitchen facilities, tables and chairs, and sometimes recreational elements. Designed to provide respite from work and foster informal interactions.", "category": "professional", "type": "environment", "source_file": "environments.json"}, "ind_prof_creativestudio": {"code": "ind_prof_creativestudio", "name": "Creative Studio", "description": "A flexible workspace designed specifically for creative professionals such as designers, architects, or artists. Features large work surfaces, specialized equipment, and space for displaying and reviewing creative work. Promotes visual thinking and creative collaboration.", "category": "professional", "type": "environment", "source_file": "environments.json"}, "ind_prof_techlab": {"code": "ind_prof_techlab", "name": "Technical Laboratory", "description": "A specialized workspace equipped for technical research, development, or testing. Contains specialized equipment, instruments, and safety features specific to the field (e.g., electronics, chemistry, biology). Designed for precise, controlled work and experimentation.", "category": "professional", "type": "environment", "source_file": "environments.json"}, "ind_prof_coworking": {"code": "ind_prof_coworking", "name": "Coworking Space", "description": "A shared workplace where professionals from different organizations work independently or collaboratively. Offers a combination of open workspaces, private areas, and communal facilities. Designed to provide professional amenities while fostering community and networking.", "category": "professional", "type": "environment", "source_file": "environments.json"}, "ind_prof_servercenter": {"code": "ind_prof_servercenter", "name": "Data Center / Server Room", "description": "A specialized facility dedicated to housing computer systems, servers, and networking equipment. Features extensive cooling systems, security measures, and power redundancies. Designed for maximum uptime and equipment protection rather than human comfort.", "category": "professional", "type": "environment", "source_file": "environments.json"}, "ind_prof_trainingroom": {"code": "ind_prof_trainingroom", "name": "Training Room", "description": "A specialized space designed for professional education, workshops, and skills development. Typically arranged classroom-style or in flexible configurations to facilitate learning. Equipped with presentation technologies and interactive tools.", "category": "professional", "type": "environment", "source_file": "environments.json"}, "ind_prof_callcenter": {"code": "ind_prof_callcenter", "name": "Call Center", "description": "A workspace designed for high-volume telephone or digital customer interactions. Features numerous workstations, often closely arranged, with acoustic considerations. Optimized for communication efficiency and monitoring capabilities.", "category": "professional", "type": "environment", "source_file": "environments.json"}, "out_prof_campusgrounds": {"code": "out_prof_campusgrounds", "name": "Corporate Campus Grounds", "description": "The outdoor areas surrounding corporate buildings, often landscaped and designed for employee use. May include walking paths, seating areas, and outdoor meeting spaces. Provides natural elements within a professional setting.", "category": "professional", "type": "environment", "source_file": "environments.json"}, "ind_prof_rooftopspace": {"code": "ind_prof_rooftopspace", "name": "Office Rooftop Space", "description": "A converted rooftop area on an office building designed for work, breaks, or events. Combines outdoor elements with workplace proximity. Often features views and open air while maintaining professional amenities.", "category": "professional", "type": "environment", "source_file": "environments.json"}, "ind_prof_executivesuite": {"code": "ind_prof_executivesuite", "name": "Executive Suite", "description": "A premium office space for senior management, featuring high-end furnishings, enhanced privacy, and dedicated meeting areas. Often includes a private office, reception area, and personal bathroom. Designed to facilitate leadership functions and confidential discussions.", "category": "professional", "type": "environment", "source_file": "environments.json"}, "ind_prof_workshop": {"code": "ind_prof_workshop", "name": "Professional Workshop", "description": "A hands-on workspace designed for building, repairing, or manipulating physical objects. Contains specialized tools, equipment, and durable work surfaces. Emphasizes functionality and safety over comfort.", "category": "professional", "type": "environment", "source_file": "environments.json"}, "ind_prof_quietroom": {"code": "ind_prof_quietroom", "name": "Quiet Work Room", "description": "A designated silent workspace within an office environment where talking and phone calls are prohibited. Features enhanced sound insulation and minimal distractions. Designed for deep focus and concentration on complex tasks.", "category": "professional", "type": "environment", "source_file": "environments.json"}, "tech_smartphone": {"code": "tech_smartphone", "name": "Smartphone", "description": "A mobile device with internet connectivity for apps, communication, and digital activities", "category": "technology", "type": "resource", "resource_type": "TECHNOLOGY", "typical_cost": 0.0, "acquisition_cost": 500.0, "availability": "COMMON", "source_file": "resources.json"}, "tech_computer": {"code": "tech_computer", "name": "Computer/Laptop", "description": "A personal computer or laptop for research, learning, and digital work", "category": "technology", "type": "resource", "resource_type": "TECHNOLOGY", "typical_cost": 0.0, "acquisition_cost": 800.0, "availability": "COMMON", "source_file": "resources.json"}, "tech_computer_internet": {"code": "tech_computer_internet", "name": "Computer with Internet", "description": "Computer with reliable internet connection for online research and learning", "category": "technology", "type": "resource", "resource_type": "TECHNOLOGY", "typical_cost": 50.0, "acquisition_cost": 850.0, "availability": "COMMON", "source_file": "resources.json"}, "tech_phone": {"code": "tech_phone", "name": "Phone", "description": "Basic phone for communication and simple apps", "category": "technology", "type": "resource", "resource_type": "TECHNOLOGY", "typical_cost": 0.0, "acquisition_cost": 200.0, "availability": "VERY_COMMON", "source_file": "resources.json"}, "tech_camera": {"code": "tech_camera", "name": "Camera", "description": "Digital camera for photography activities", "category": "technology", "type": "resource", "resource_type": "TECHNOLOGY", "typical_cost": 0.0, "acquisition_cost": 400.0, "availability": "COMMON", "source_file": "resources.json"}, "tech_gaming": {"code": "tech_gaming", "name": "Gaming Device", "description": "Gaming console, computer, or device for digital gaming activities", "category": "technology", "type": "resource", "resource_type": "TECHNOLOGY", "typical_cost": 0.0, "acquisition_cost": 300.0, "availability": "COMMON", "source_file": "resources.json"}, "tech_screen": {"code": "tech_screen", "name": "Screen/Display", "description": "Television, monitor, or large screen for viewing content", "category": "technology", "type": "resource", "resource_type": "TECHNOLOGY", "typical_cost": 0.0, "acquisition_cost": 300.0, "availability": "COMMON", "source_file": "resources.json"}, "tech_music_player": {"code": "tech_music_player", "name": "Music Player", "description": "Device for playing music, including speakers or headphones", "category": "technology", "type": "resource", "resource_type": "TECHNOLOGY", "typical_cost": 0.0, "acquisition_cost": 100.0, "availability": "COMMON", "source_file": "resources.json"}, "tech_navigation": {"code": "tech_navigation", "name": "Navigation Device", "description": "GPS device or navigation app for travel and exploration", "category": "technology", "type": "resource", "resource_type": "TECHNOLOGY", "typical_cost": 0.0, "acquisition_cost": 150.0, "availability": "COMMON", "source_file": "resources.json"}, "tech_vr_headset": {"code": "tech_vr_headset", "name": "VR Headset", "description": "Virtual reality headset for immersive digital experiences", "category": "technology", "type": "resource", "resource_type": "TECHNOLOGY", "typical_cost": 0.0, "acquisition_cost": 400.0, "availability": "UNCOMMON", "source_file": "resources.json"}, "equip_exercise_mat": {"code": "equip_exercise_mat", "name": "Exercise Mat", "description": "Yoga or exercise mat for floor-based physical activities", "category": "equipment", "type": "resource", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 30.0, "availability": "COMMON", "source_file": "resources.json"}, "equip_yoga_mat": {"code": "equip_yoga_mat", "name": "Yoga Mat", "description": "Specialized mat for yoga practice with proper grip and cushioning", "category": "equipment", "type": "resource", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 40.0, "availability": "COMMON", "source_file": "resources.json"}, "equip_yoga_block": {"code": "equip_yoga_block", "name": "Yoga Block", "description": "Supportive block for yoga poses and stretching", "category": "equipment", "type": "resource", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 15.0, "availability": "COMMON", "source_file": "resources.json"}, "equip_running_shoes": {"code": "equip_running_shoes", "name": "Running Shoes", "description": "Proper athletic footwear for running and cardio activities", "category": "equipment", "type": "resource", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 120.0, "availability": "COMMON", "source_file": "resources.json"}, "equip_walking_shoes": {"code": "equip_walking_shoes", "name": "Walking Shoes", "description": "Comfortable shoes suitable for walking and light exercise", "category": "equipment", "type": "resource", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 80.0, "availability": "VERY_COMMON", "source_file": "resources.json"}, "equip_athletic_wear": {"code": "equip_athletic_wear", "name": "Athletic Wear", "description": "Appropriate clothing for physical exercise and sports", "category": "equipment", "type": "resource", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 60.0, "availability": "COMMON", "source_file": "resources.json"}, "equip_protective_gear": {"code": "equip_protective_gear", "name": "Protective Gear", "description": "Safety equipment for high-risk physical activities", "category": "equipment", "type": "resource", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 100.0, "availability": "COMMON", "source_file": "resources.json"}, "equip_training_shoes": {"code": "equip_training_shoes", "name": "Training Shoes", "description": "Cross-training shoes for varied physical activities", "category": "equipment", "type": "resource", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 100.0, "availability": "COMMON", "source_file": "resources.json"}, "equip_comfortable_shoes": {"code": "equip_comfortable_shoes", "name": "Comfortable Shoes", "description": "General comfortable footwear for daily activities", "category": "equipment", "type": "resource", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 70.0, "availability": "VERY_COMMON", "source_file": "resources.json"}, "equip_timer": {"code": "equip_timer", "name": "Timer", "description": "Device for timing activities and exercises", "category": "equipment", "type": "resource", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 20.0, "availability": "COMMON", "source_file": "resources.json"}, "equip_water_bottle": {"code": "equip_water_bottle", "name": "Water Bottle", "description": "Reusable water bottle for hydration during activities", "category": "equipment", "type": "resource", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 15.0, "availability": "VERY_COMMON", "source_file": "resources.json"}, "equip_day_pack": {"code": "equip_day_pack", "name": "Day Pack", "description": "Small backpack for carrying essentials during outings", "category": "equipment", "type": "resource", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 50.0, "availability": "COMMON", "source_file": "resources.json"}, "equip_outdoor_basic": {"code": "equip_outdoor_basic", "name": "Basic Outdoor Equipment", "description": "Essential gear for outdoor activities and exploration", "category": "equipment", "type": "resource", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 75.0, "availability": "COMMON", "source_file": "resources.json"}, "equip_blindfold": {"code": "equip_blindfold", "name": "<PERSON>fold", "description": "Simple blindfold for trust exercises and sensory activities", "category": "equipment", "type": "resource", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 10.0, "availability": "COMMON", "source_file": "resources.json"}, "equip_cleaning_tools": {"code": "equip_cleaning_tools", "name": "Cleaning Tools", "description": "Basic cleaning supplies and tools for organization activities", "category": "equipment", "type": "resource", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 40.0, "availability": "VERY_COMMON", "source_file": "resources.json"}, "equip_learning_tools": {"code": "equip_learning_tools", "name": "Learning Tools", "description": "Educational materials and tools for skill development", "category": "equipment", "type": "resource", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 60.0, "availability": "COMMON", "source_file": "resources.json"}, "equip_professional_attire": {"code": "equip_professional_attire", "name": "Professional Attire", "description": "Appropriate clothing for professional networking events", "category": "equipment", "type": "resource", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 200.0, "availability": "COMMON", "source_file": "resources.json"}, "equip_business_cards": {"code": "equip_business_cards", "name": "Business Cards", "description": "Professional business cards for networking", "category": "equipment", "type": "resource", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 25.0, "availability": "COMMON", "source_file": "resources.json"}, "equip_board_game": {"code": "equip_board_game", "name": "Board Game", "description": "Strategy or social board game for intellectual activities", "category": "equipment", "type": "resource", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 40.0, "availability": "COMMON", "source_file": "resources.json"}, "equip_kitchen_basic": {"code": "equip_kitchen_basic", "name": "Basic Kitchen Equipment", "description": "Essential kitchen tools for cooking activities", "category": "equipment", "type": "resource", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 100.0, "availability": "COMMON", "source_file": "resources.json"}, "equip_kettle": {"code": "equip_kettle", "name": "Kettle", "description": "Electric or stovetop kettle for heating water", "category": "equipment", "type": "resource", "resource_type": "EQUIPMENT", "typical_cost": 0.0, "acquisition_cost": 30.0, "availability": "COMMON", "source_file": "resources.json"}, "stationery_journal": {"code": "stationery_journal", "name": "Journal", "description": "Notebook or journal for writing, reflection, and planning", "category": "stationery", "type": "resource", "resource_type": "STATIONERY", "typical_cost": 0.0, "acquisition_cost": 15.0, "availability": "VERY_COMMON", "source_file": "resources.json"}, "stationery_paper": {"code": "stationery_paper", "name": "Paper", "description": "Writing paper for notes, exercises, and creative activities", "category": "stationery", "type": "resource", "resource_type": "STATIONERY", "typical_cost": 0.0, "acquisition_cost": 5.0, "availability": "VERY_COMMON", "source_file": "resources.json"}, "stationery_pen": {"code": "stationery_pen", "name": "Pen", "description": "Writing pen for journaling and note-taking", "category": "stationery", "type": "resource", "resource_type": "STATIONERY", "typical_cost": 0.0, "acquisition_cost": 2.0, "availability": "VERY_COMMON", "source_file": "resources.json"}, "stationery_pencil": {"code": "stationery_pencil", "name": "Pencil", "description": "Pencil for writing, drawing, and problem-solving", "category": "stationery", "type": "resource", "resource_type": "STATIONERY", "typical_cost": 0.0, "acquisition_cost": 1.0, "availability": "VERY_COMMON", "source_file": "resources.json"}, "stationery_markers": {"code": "stationery_markers", "name": "Markers", "description": "Colored markers for creative and organizational activities", "category": "stationery", "type": "resource", "resource_type": "STATIONERY", "typical_cost": 0.0, "acquisition_cost": 10.0, "availability": "COMMON", "source_file": "resources.json"}, "equip_journal": {"code": "equip_journal", "name": "Journal/Notebook", "description": "Dedicated journal or notebook for reflection and planning", "category": "stationery", "type": "resource", "resource_type": "STATIONERY", "typical_cost": 0.0, "acquisition_cost": 15.0, "availability": "VERY_COMMON", "source_file": "resources.json"}, "equip_planner": {"code": "equip_planner", "name": "Planner", "description": "Structured planner for goal setting and time management", "category": "stationery", "type": "resource", "resource_type": "STATIONERY", "typical_cost": 0.0, "acquisition_cost": 25.0, "availability": "COMMON", "source_file": "resources.json"}, "equip_writing_tool": {"code": "equip_writing_tool", "name": "Writing Tool", "description": "Pen, pencil, or other writing implement", "category": "stationery", "type": "resource", "resource_type": "STATIONERY", "typical_cost": 0.0, "acquisition_cost": 3.0, "availability": "VERY_COMMON", "source_file": "resources.json"}, "edu_writing_materials": {"code": "edu_writing_materials", "name": "Educational Writing Materials", "description": "Comprehensive writing supplies for learning activities", "category": "stationery", "type": "resource", "resource_type": "STATIONERY", "typical_cost": 0.0, "acquisition_cost": 20.0, "availability": "COMMON", "source_file": "resources.json"}, "creative_instrument": {"code": "creative_instrument", "name": "Musical Instrument", "description": "Musical instrument for creative expression and performance", "category": "creative", "type": "resource", "resource_type": "CREATIVE", "typical_cost": 0.0, "acquisition_cost": 200.0, "availability": "COMMON", "source_file": "resources.json"}, "creative_drawing_materials": {"code": "creative_drawing_materials", "name": "Drawing Materials", "description": "Art supplies for drawing and sketching activities", "category": "creative", "type": "resource", "resource_type": "CREATIVE", "typical_cost": 0.0, "acquisition_cost": 30.0, "availability": "COMMON", "source_file": "resources.json"}, "art_basic_supplies": {"code": "art_basic_supplies", "name": "Basic Art Supplies", "description": "Essential art materials for creative expression", "category": "creative", "type": "resource", "resource_type": "CREATIVE", "typical_cost": 0.0, "acquisition_cost": 40.0, "availability": "COMMON", "source_file": "resources.json"}, "art_paper": {"code": "art_paper", "name": "Art Paper", "description": "Specialized paper for drawing and artistic activities", "category": "creative", "type": "resource", "resource_type": "CREATIVE", "typical_cost": 0.0, "acquisition_cost": 10.0, "availability": "COMMON", "source_file": "resources.json"}, "leisure_board_games": {"code": "leisure_board_games", "name": "Board Games", "description": "Collection of board games for social entertainment", "category": "leisure", "type": "resource", "resource_type": "LEISURE", "typical_cost": 0.0, "acquisition_cost": 80.0, "availability": "COMMON", "source_file": "resources.json"}, "leisure_chess_set": {"code": "leisure_chess_set", "name": "Chess Set", "description": "Chess board and pieces for strategic gameplay", "category": "leisure", "type": "resource", "resource_type": "LEISURE", "typical_cost": 0.0, "acquisition_cost": 35.0, "availability": "COMMON", "source_file": "resources.json"}, "leisure_puzzle": {"code": "leisure_puzzle", "name": "Puzzle", "description": "Jigsaw puzzle or brain teaser for mental stimulation", "category": "leisure", "type": "resource", "resource_type": "LEISURE", "typical_cost": 0.0, "acquisition_cost": 20.0, "availability": "COMMON", "source_file": "resources.json"}, "leisure_snacks": {"code": "leisure_snacks", "name": "Snacks", "description": "Light snacks for leisure activities and entertainment", "category": "leisure", "type": "resource", "resource_type": "LEISURE", "typical_cost": 5.0, "acquisition_cost": 5.0, "availability": "VERY_COMMON", "source_file": "resources.json"}, "food_snack": {"code": "food_snack", "name": "Snack Food", "description": "Light snack for mindful eating activities", "category": "food", "type": "resource", "resource_type": "FOOD", "typical_cost": 3.0, "acquisition_cost": 3.0, "availability": "VERY_COMMON", "source_file": "resources.json"}, "food_tea": {"code": "food_tea", "name": "Tea", "description": "Tea for mindful drinking and relaxation rituals", "category": "food", "type": "resource", "resource_type": "FOOD", "typical_cost": 2.0, "acquisition_cost": 10.0, "availability": "VERY_COMMON", "source_file": "resources.json"}, "cook_equipment": {"code": "cook_equipment", "name": "Cooking Equipment", "description": "Basic cooking tools and equipment for meal preparation", "category": "food", "type": "resource", "resource_type": "FOOD", "typical_cost": 0.0, "acquisition_cost": 150.0, "availability": "COMMON", "source_file": "resources.json"}, "cook_ingredients": {"code": "cook_ingredients", "name": "Cooking Ingredients", "description": "Fresh ingredients for cooking and meal preparation", "category": "food", "type": "resource", "resource_type": "FOOD", "typical_cost": 20.0, "acquisition_cost": 20.0, "availability": "COMMON", "source_file": "resources.json"}, "garden_tools": {"code": "garden_tools", "name": "Garden Tools", "description": "Basic gardening tools for plant care activities", "category": "garden", "type": "resource", "resource_type": "GARDEN", "typical_cost": 0.0, "acquisition_cost": 60.0, "availability": "COMMON", "source_file": "resources.json"}, "garden_plants": {"code": "garden_plants", "name": "Plants", "description": "Plants, seeds, or seedlings for gardening activities", "category": "garden", "type": "resource", "resource_type": "GARDEN", "typical_cost": 5.0, "acquisition_cost": 25.0, "availability": "COMMON", "source_file": "resources.json"}, "outdoor_footwear": {"code": "outdoor_footwear", "name": "Outdoor Footwear", "description": "Appropriate footwear for outdoor activities and hiking", "category": "outdoor", "type": "resource", "resource_type": "OUTDOOR", "typical_cost": 0.0, "acquisition_cost": 100.0, "availability": "COMMON", "source_file": "resources.json"}, "outdoor_water": {"code": "outdoor_water", "name": "Water Supply", "description": "Clean water for outdoor activities and hydration", "category": "outdoor", "type": "resource", "resource_type": "OUTDOOR", "typical_cost": 2.0, "acquisition_cost": 2.0, "availability": "VERY_COMMON", "source_file": "resources.json"}, "media_music": {"code": "media_music", "name": "Music", "description": "Music streaming or audio content for activities", "category": "media", "type": "resource", "resource_type": "MEDIA", "typical_cost": 10.0, "acquisition_cost": 10.0, "availability": "VERY_COMMON", "source_file": "resources.json"}, "finance_small": {"code": "finance_small", "name": "Small Financial Amount", "description": "Small amount of money for minor purchases or activities", "category": "finance", "type": "resource", "resource_type": "FINANCE", "typical_cost": 10.0, "acquisition_cost": 10.0, "availability": "COMMON", "source_file": "resources.json"}, "finance_medium": {"code": "finance_medium", "name": "Medium Financial Amount", "description": "Moderate amount of money for activities requiring investment", "category": "finance", "type": "resource", "resource_type": "FINANCE", "typical_cost": 50.0, "acquisition_cost": 50.0, "availability": "COMMON", "source_file": "resources.json"}, "finance_activity_fee": {"code": "finance_activity_fee", "name": "Activity Fee", "description": "Payment for organized activities or classes", "category": "finance", "type": "resource", "resource_type": "FINANCE", "typical_cost": 25.0, "acquisition_cost": 25.0, "availability": "COMMON", "source_file": "resources.json"}, "finance_dining": {"code": "finance_dining", "name": "Dining Budget", "description": "Money for dining out or food-related activities", "category": "finance", "type": "resource", "resource_type": "FINANCE", "typical_cost": 30.0, "acquisition_cost": 30.0, "availability": "COMMON", "source_file": "resources.json"}, "finance_entertainment": {"code": "finance_entertainment", "name": "Entertainment Budget", "description": "Money for entertainment and leisure activities", "category": "finance", "type": "resource", "resource_type": "FINANCE", "typical_cost": 20.0, "acquisition_cost": 20.0, "availability": "COMMON", "source_file": "resources.json"}, "finance_travel": {"code": "finance_travel", "name": "Travel Budget", "description": "Money for travel and exploration activities", "category": "finance", "type": "resource", "resource_type": "FINANCE", "typical_cost": 100.0, "acquisition_cost": 100.0, "availability": "COMMON", "source_file": "resources.json"}, "access_transportation": {"code": "access_transportation", "name": "Transportation Access", "description": "Access to public or private transportation for activities", "category": "access", "type": "resource", "resource_type": "ACCESS", "typical_cost": 15.0, "acquisition_cost": 15.0, "availability": "COMMON", "source_file": "resources.json"}, "access_walkable_area": {"code": "access_walkable_area", "name": "Walkable Area Access", "description": "Access to safe, walkable areas for exploration", "category": "access", "type": "resource", "resource_type": "ACCESS", "typical_cost": 0.0, "acquisition_cost": 0.0, "availability": "COMMON", "source_file": "resources.json"}, "home_dining_space": {"code": "home_dining_space", "name": "Dining Space", "description": "Dedicated space for dining and entertaining guests", "category": "home", "type": "resource", "resource_type": "HOME", "typical_cost": 0.0, "acquisition_cost": 0.0, "availability": "COMMON", "source_file": "resources.json"}, "home_kitchen": {"code": "home_kitchen", "name": "Kitchen Access", "description": "Access to a functional kitchen for cooking activities", "category": "home", "type": "resource", "resource_type": "HOME", "typical_cost": 0.0, "acquisition_cost": 0.0, "availability": "COMMON", "source_file": "resources.json"}, "office_desk": {"code": "office_desk", "name": "Office Desk", "description": "Desk or workspace for office-based activities", "category": "home", "type": "resource", "resource_type": "HOME", "typical_cost": 0.0, "acquisition_cost": 200.0, "availability": "COMMON", "source_file": "resources.json"}, "edu_book": {"code": "edu_book", "name": "Educational Book", "description": "Book for learning and educational activities", "category": "education", "type": "resource", "resource_type": "EDUCATION", "typical_cost": 0.0, "acquisition_cost": 25.0, "availability": "COMMON", "source_file": "resources.json"}, "skill_specific_tools": {"code": "skill_specific_tools", "name": "Skill-Specific Tools", "description": "Specialized tools required for specific skill development", "category": "specialized", "type": "resource", "resource_type": "SPECIALIZED", "typical_cost": 0.0, "acquisition_cost": 100.0, "availability": "UNCOMMON", "source_file": "resources.json"}, "physical": {"code": "physical", "name": "Physical", "description": "a broad domain encompassing all physical activities that engage the body and promote health and fitness.", "category": "PHYSICAL", "type": "domain", "source_file": "domains.json"}, "phys_cardio": {"code": "phys_cardio", "name": "Cardiovascular Exercise", "description": "Activities that increase heart rate and improve cardiovascular health, such as running, cycling, or swimming.", "category": "PHYSICAL", "type": "domain", "source_file": "domains.json"}, "phys_strength": {"code": "phys_strength", "name": "Strength Training", "description": "Activities focused on building muscle strength through resistance, such as weightlifting or bodyweight exercises.", "category": "PHYSICAL", "type": "domain", "source_file": "domains.json"}, "phys_chill": {"code": "phys_chill", "name": "Physical but chill", "description": "Activities that are physical, but somehow chill (like walking).", "category": "PHYSICAL", "type": "domain", "source_file": "domains.json"}, "phys_flexibility": {"code": "phys_flexibility", "name": "Flexibility & Mobility", "description": "Activities that improve range of motion, joint health, and muscle elasticity, such as yoga, stretching, or pilates.", "category": "PHYSICAL", "type": "domain", "source_file": "domains.json"}, "phys_sports": {"code": "phys_sports", "name": "Recreational Sports", "description": "Structured physical activities with rules and competitive elements, including team sports or individual athletic pursuits.", "category": "PHYSICAL", "type": "domain", "source_file": "domains.json"}, "phys_outdoor": {"code": "phys_outdoor", "name": "Outdoor Activities", "description": "Physical activities conducted in natural settings, such as hiking, climbing, or kayaking.", "category": "PHYSICAL", "type": "domain", "source_file": "domains.json"}, "phys_dance": {"code": "phys_dance", "name": "Dance & Movement", "description": "Expressive physical activities involving rhythmic body movement, including various dance styles and movement practices.", "category": "PHYSICAL", "type": "domain", "source_file": "domains.json"}, "phys_martial": {"code": "phys_martial", "name": "Martial Arts", "description": "Structured systems of combat and self-defense training that often incorporate physical conditioning and philosophical elements.", "category": "PHYSICAL", "type": "domain", "source_file": "domains.json"}, "phys_balance": {"code": "phys_balance", "name": "Balance & Coordination", "description": "Activities that develop proprioception, stability, and motor control.", "category": "PHYSICAL", "type": "domain", "source_file": "domains.json"}, "social": {"code": "social", "name": "Social", "description": "a broad domain encompassing all social activities that engage users with others.", "category": "SOCIAL", "type": "domain", "source_file": "domains.json"}, "soc_connecting": {"code": "soc_connecting", "name": "Social Connection", "description": "Activities focused on forming or strengthening interpersonal bonds through conversation, shared experiences, or mutual support.", "category": "SOCIAL", "type": "domain", "source_file": "domains.json"}, "soc_group": {"code": "soc_group", "name": "Group Dynamics", "description": "Activities involving interaction within larger social structures, such as team building, community engagement, or navigating organizational systems.", "category": "SOCIAL", "type": "domain", "source_file": "domains.json"}, "soc_comm": {"code": "soc_comm", "name": "Communication Skills", "description": "Activities developing verbal and non-verbal communication, including public speaking, active listening, or conflict resolution.", "category": "SOCIAL", "type": "domain", "source_file": "domains.json"}, "soc_empathy": {"code": "soc_empathy", "name": "Empathy Building", "description": "Activities designed to enhance understanding of others' perspectives, emotions, and experiences.", "category": "SOCIAL", "type": "domain", "source_file": "domains.json"}, "soc_network": {"code": "soc_network", "name": "Networking", "description": "Structured activities to expand professional or personal circles, create new connections, and develop a support system.", "category": "SOCIAL", "type": "domain", "source_file": "domains.json"}, "soc_romance": {"code": "soc_romance", "name": "Romantic Relationship", "description": "Activities focused on developing or strengthening intimate partner relationships.", "category": "SOCIAL", "type": "domain", "source_file": "domains.json"}, "soc_family": {"code": "soc_family", "name": "Family Bonding", "description": "Activities that enhance connections between family members and foster a supportive family environment.", "category": "SOCIAL", "type": "domain", "source_file": "domains.json"}, "soc_leadership": {"code": "soc_leadership", "name": "Leadership", "description": "Activities that develop skills in guiding, influencing, and motivating others toward shared goals.", "category": "SOCIAL", "type": "domain", "source_file": "domains.json"}, "soc_conflict": {"code": "soc_conflict", "name": "Conflict Resolution", "description": "Activities that develop skills in solving conflicts.", "category": "SOCIAL", "type": "domain", "source_file": "domains.json"}, "creative": {"code": "creative", "name": "Creative", "description": "a broad domain encompassing all creative activities.", "category": "CREATIVE", "type": "domain", "source_file": "domains.json"}, "creative_visual": {"code": "creative_visual", "name": "Visual Arts", "description": "Creative expression through visual mediums including painting, drawing, sculpture, or digital art.", "category": "CREATIVE", "type": "domain", "source_file": "domains.json"}, "creative_observation": {"code": "creative_observation", "name": "Creative observation", "description": "Creative thoughts from observing.", "category": "CREATIVE", "type": "domain", "source_file": "domains.json"}, "creative_auditory": {"code": "creative_auditory", "name": "Creative auditory", "description": "Creative thoughts from listening.", "category": "CREATIVE", "type": "domain", "source_file": "domains.json"}, "creative_music": {"code": "creative_music", "name": "Music Creation", "description": "Activities involving creating or performing music, such as playing instruments, singing, composing, or producing.", "category": "CREATIVE", "type": "domain", "source_file": "domains.json"}, "creative_writing": {"code": "creative_writing", "name": "Creative Writing", "description": "Expression through written language, including fiction, poetry, journaling, or scriptwriting.", "category": "CREATIVE", "type": "domain", "source_file": "domains.json"}, "creative_design": {"code": "creative_design", "name": "Design Thinking", "description": "Problem-solving through creative design processes, including graphic design, product design, or architectural thinking.", "category": "CREATIVE", "type": "domain", "source_file": "domains.json"}, "creative_culinary": {"code": "creative_culinary", "name": "Culinary Arts", "description": "Creative expression through food preparation, recipe development, or gastronomic experimentation.", "category": "CREATIVE", "type": "domain", "source_file": "domains.json"}, "creative_perform": {"code": "creative_perform", "name": "Performance Arts", "description": "Creative activities involving live presentation, such as theater, comedy, or storytelling.", "category": "CREATIVE", "type": "domain", "source_file": "domains.json"}, "creative_craft": {"code": "creative_craft", "name": "Crafts & Making", "description": "Hands-on creation of physical objects, including textile arts, woodworking, pottery, or DIY projects.", "category": "CREATIVE", "type": "domain", "source_file": "domains.json"}, "creative_improv": {"code": "creative_improv", "name": "Improvisation", "description": "Spontaneous creative expression and adaptation without predetermined structure.", "category": "CREATIVE", "type": "domain", "source_file": "domains.json"}, "intellectual": {"code": "intellectual", "name": "Intellectual", "description": "a broad domain encompassing all intellectual activities.", "category": "INTELLECTUAL", "type": "domain", "source_file": "domains.json"}, "intel_learn": {"code": "intel_learn", "name": "Learning & Study", "description": "Structured acquisition of knowledge or skills in specific domains through courses, research, or self-directed study.", "category": "INTELLECTUAL", "type": "domain", "source_file": "domains.json"}, "intel_problem": {"code": "intel_problem", "name": "Problem Solving", "description": "Activities that develop analytical thinking and systematic approaches to overcoming challenges.", "category": "INTELLECTUAL", "type": "domain", "source_file": "domains.json"}, "intel_audio": {"code": "intel_audio", "name": "Intellectual audio", "description": "Activities that requires focused listening.", "category": "INTELLECTUAL", "type": "domain", "source_file": "domains.json"}, "intel_strategic": {"code": "intel_strategic", "name": "Strategic Thinking", "description": "Long-term planning and development of approaches to achieve goals while considering multiple variables and possible outcomes.", "category": "INTELLECTUAL", "type": "domain", "source_file": "domains.json"}, "intel_curiosity": {"code": "intel_curiosity", "name": "Intellectual Curiosity", "description": "Exploration of ideas driven by interest and wonder, such as reading broadly or engaging with new concepts.", "category": "INTELLECTUAL", "type": "domain", "source_file": "domains.json"}, "intel_language": {"code": "intel_language", "name": "Language & Linguistics", "description": "Activities focused on language acquisition, linguistic analysis, or cross-cultural communication.", "category": "INTELLECTUAL", "type": "domain", "source_file": "domains.json"}, "intel_debate": {"code": "intel_debate", "name": "Critical Discourse", "description": "Engagement in reasoned argument, debate, or discussion that evaluates ideas from multiple perspectives.", "category": "INTELLECTUAL", "type": "domain", "source_file": "domains.json"}, "intel_science": {"code": "intel_science", "name": "Scientific Inquiry", "description": "Activities involving observation, hypothesis formation, experimentation, and evidence-based reasoning.", "category": "INTELLECTUAL", "type": "domain", "source_file": "domains.json"}, "intel_tech": {"code": "intel_tech", "name": "Technology & Digital Skills", "description": "Learning and applying digital tools, programming, or computational thinking.", "category": "INTELLECTUAL", "type": "domain", "source_file": "domains.json"}, "reflective": {"code": "reflective", "name": "Reflective", "description": "a broad domain encompassing all reflective activities.", "category": "REFLECTIVE", "type": "domain", "source_file": "domains.json"}, "refl_meditate": {"code": "refl_meditate", "name": "Meditation", "description": "Practices that develop focused attention, awareness, and mental clarity through various meditation techniques.", "category": "REFLECTIVE", "type": "domain", "source_file": "domains.json"}, "refl_journal": {"code": "refl_journal", "name": "Journaling & Self-Reflection", "description": "Written or structured contemplation of personal experiences, emotions, and thoughts to gain insight.", "category": "REFLECTIVE", "type": "domain", "source_file": "domains.json"}, "refl_mindful": {"code": "refl_mindful", "name": "Mindfulness Practice", "description": "Activities cultivating present-moment awareness and non-judgmental observation of internal and external experiences.", "category": "REFLECTIVE", "type": "domain", "source_file": "domains.json"}, "refl_values": {"code": "refl_values", "name": "Values Clarification", "description": "Exploration and identification of personal principles, ethics, and priorities that guide decision-making.", "category": "REFLECTIVE", "type": "domain", "source_file": "domains.json"}, "refl_persp": {"code": "refl_persp", "name": "Perspective Taking", "description": "Activities that encourage viewing situations from different angles or through others' experiences.", "category": "REFLECTIVE", "type": "domain", "source_file": "domains.json"}, "refl_philos": {"code": "refl_philos", "name": "Philosophical Contemplation", "description": "Engagement with fundamental questions about existence, meaning, ethics, and the nature of reality.", "category": "REFLECTIVE", "type": "domain", "source_file": "domains.json"}, "refl_grat": {"code": "refl_grat", "name": "Gratitude Practice", "description": "Structured recognition and appreciation of positive aspects of life and experiences.", "category": "REFLECTIVE", "type": "domain", "source_file": "domains.json"}, "refl_comfort": {"code": "refl_comfort", "name": "Comfort Zone Reflection", "description": "Examination of personal boundaries, limitations, and opportunities for growth through controlled discomfort.", "category": "REFLECTIVE", "type": "domain", "source_file": "domains.json"}, "refl_micro": {"code": "refl_micro", "name": "Micro-Wellness Activities", "description": "Brief wellness activities (2-15 minutes) designed for busy schedules and transition moments, such as quick breathing exercises or brief mindfulness practices.", "category": "REFLECTIVE", "type": "domain", "source_file": "domains.json"}, "emotional": {"code": "emotional", "name": "Emotional", "description": "a broad domain encompassing all activities that engege the users with their emotions.", "category": "EMOTIONAL", "type": "domain", "source_file": "domains.json"}, "emot_aware": {"code": "emot_aware", "name": "Emotional Awareness", "description": "Activities that develop recognition and understanding of emotional states in oneself and others.", "category": "EMOTIONAL", "type": "domain", "source_file": "domains.json"}, "emot_regulate": {"code": "emot_regulate", "name": "Emotion Regulation", "description": "Practices for managing emotional responses, processing difficult feelings, and developing emotional resilience.", "category": "EMOTIONAL", "type": "domain", "source_file": "domains.json"}, "emot_express": {"code": "emot_express", "name": "Emotional Expression", "description": "Healthy outlets for communicating and releasing emotions through verbal, physical, or creative means.", "category": "EMOTIONAL", "type": "domain", "source_file": "domains.json"}, "emot_compass": {"code": "emot_compass", "name": "Self-Compassion", "description": "Activities that nurture kindness and understanding toward oneself, especially during challenges or failures.", "category": "EMOTIONAL", "type": "domain", "source_file": "domains.json"}, "emot_joy": {"code": "emot_joy", "name": "Joy & Pleasure", "description": "Intentional engagement in activities that bring happiness, satisfaction, or delight.", "category": "EMOTIONAL", "type": "domain", "source_file": "domains.json"}, "emot_stress": {"code": "emot_stress", "name": "Stress Management", "description": "Techniques and practices that reduce or help cope with stress and anxiety.", "category": "EMOTIONAL", "type": "domain", "source_file": "domains.json"}, "emot_forgive": {"code": "emot_forgive", "name": "Forgiveness & Letting Go", "description": "Processes for releasing resentment, anger, or attachment to past hurts or disappointments.", "category": "EMOTIONAL", "type": "domain", "source_file": "domains.json"}, "emot_comfort": {"code": "emot_comfort", "name": "Comfort & Nurturing", "description": "Activities that provide emotional security, soothing, and care during difficult times.", "category": "EMOTIONAL", "type": "domain", "source_file": "domains.json"}, "spiritual_existential": {"code": "spiritual_existential", "name": "Spiritual & Existential", "description": "a broad domain encompassing all spiritual activities.", "category": "SPIRITUAL_EXISTENTIAL", "type": "domain", "source_file": "domains.json"}, "spirit_purpose": {"code": "spirit_purpose", "name": "Purpose & Meaning", "description": "Exploration of personal significance, life direction, and sources of meaning beyond immediate goals.", "category": "SPIRITUAL_EXISTENTIAL", "type": "domain", "source_file": "domains.json"}, "spirit_connect": {"code": "spirit_connect", "name": "Spiritual Connection", "description": "Activities that foster connection with transcendent elements, whether through religious practices or secular spirituality.", "category": "SPIRITUAL_EXISTENTIAL", "type": "domain", "source_file": "domains.json"}, "spirit_ritual": {"code": "spirit_ritual", "name": "Ritual & Practice", "description": "Structured ceremonies or habits that connect to cultural, spiritual, or personal significance beyond their practical function.", "category": "SPIRITUAL_EXISTENTIAL", "type": "domain", "source_file": "domains.json"}, "spirit_nature": {"code": "spirit_nature", "name": "Nature Connection", "description": "Experiences that develop awareness of and relationship with the natural world and ecological systems.", "category": "SPIRITUAL_EXISTENTIAL", "type": "domain", "source_file": "domains.json"}, "spirit_transced": {"code": "spirit_transced", "name": "Transcendent Experience", "description": "Activities oriented toward peak experiences, states of flow, or moments that transcend ordinary consciousness.", "category": "SPIRITUAL_EXISTENTIAL", "type": "domain", "source_file": "domains.json"}, "spirit_death": {"code": "spirit_death", "name": "Mortality Contemplation", "description": "Reflective consideration of life's impermanence and one's relationship with the concept of death.", "category": "SPIRITUAL_EXISTENTIAL", "type": "domain", "source_file": "domains.json"}, "spirit_commun": {"code": "spirit_commun", "name": "Spiritual Community", "description": "Engagement with others who share similar existential or spiritual orientations, practices, or beliefs.", "category": "SPIRITUAL_EXISTENTIAL", "type": "domain", "source_file": "domains.json"}, "spirit_wisdom": {"code": "spirit_wisdom", "name": "Wisdom Traditions", "description": "Exploration of or participation in established philosophical, spiritual, or cultural wisdom traditions.", "category": "SPIRITUAL_EXISTENTIAL", "type": "domain", "source_file": "domains.json"}, "exploratory_adventurous": {"code": "exploratory_adventurous", "name": "Exploratory & Adventurous", "description": "a broad domain encompassing all exploratory and adventurous activities.", "category": "EXPLORATORY_ADVENTUROUS", "type": "domain", "source_file": "domains.json"}, "explor_travel": {"code": "explor_travel", "name": "Travel & Discovery", "description": "Activities involving geographical exploration, cultural immersion, or visiting new places.", "category": "EXPLORATORY_ADVENTUROUS", "type": "domain", "source_file": "domains.json"}, "explor_risk": {"code": "explor_risk", "name": "Controlled Risk-Taking", "description": "Activities that involve stepping beyond comfort zones with calculated challenges and reasonable safety measures.", "category": "EXPLORATORY_ADVENTUROUS", "type": "domain", "source_file": "domains.json"}, "explor_sensory": {"code": "explor_sensory", "name": "Sensory Exploration", "description": "Experiences that engage or expand awareness through the five senses in novel or intensive ways.", "category": "EXPLORATORY_ADVENTUROUS", "type": "domain", "source_file": "domains.json"}, "explor_cultural": {"code": "explor_cultural", "name": "Cultural Exploration", "description": "Engagement with unfamiliar traditions, perspectives, or creative expressions from different cultures.", "category": "EXPLORATORY_ADVENTUROUS", "type": "domain", "source_file": "domains.json"}, "explor_novel": {"code": "explor_novel", "name": "Novelty Seeking", "description": "Pursuit of new experiences, skills, or environments purely for the sake of freshness and variety.", "category": "EXPLORATORY_ADVENTUROUS", "type": "domain", "source_file": "domains.json"}, "explor_adren": {"code": "explor_adren", "name": "Adrenaline Activities", "description": "High-energy experiences that produce excitement, thrill, or exhilaration through physical or psychological intensity.", "category": "EXPLORATORY_ADVENTUROUS", "type": "domain", "source_file": "domains.json"}, "explor_improv": {"code": "explor_improv", "name": "Spontaneity & Improvisation", "description": "Activities with minimal planning that embrace unexpected outcomes and in-the-moment decision making.", "category": "EXPLORATORY_ADVENTUROUS", "type": "domain", "source_file": "domains.json"}, "explor_unknown": {"code": "explor_unknown", "name": "Embracing Uncertainty", "description": "Deliberate engagement with the unknown or unpredictable aspects of life or specific domains.", "category": "EXPLORATORY_ADVENTUROUS", "type": "domain", "source_file": "domains.json"}, "explor_digital": {"code": "explor_digital", "name": "Digital-Physical Hybrid", "description": "Activities that blend digital tools with physical actions, such as app-guided workouts, virtual reality experiences, or augmented reality exploration.", "category": "EXPLORATORY_ADVENTUROUS", "type": "domain", "source_file": "domains.json"}, "productive_practical": {"code": "productive_practical", "name": "Productive & Practical", "description": "a broad domain encompassing all productive and practical activities.", "category": "PRODUCTIVE_PRACTICAL", "type": "domain", "source_file": "domains.json"}, "prod_organize": {"code": "prod_organize", "name": "Organization & Planning", "description": "Systems and activities for creating order, structure, and efficiency in physical spaces or processes.", "category": "PRODUCTIVE_PRACTICAL", "type": "domain", "source_file": "domains.json"}, "prod_habit": {"code": "prod_habit", "name": "Habit Formation", "description": "Development of consistent beneficial behaviors through intentional practice and routine building.", "category": "PRODUCTIVE_PRACTICAL", "type": "domain", "source_file": "domains.json"}, "prod_time": {"code": "prod_time", "name": "Time Management", "description": "Approaches to allocating time effectively based on priorities, energy levels, and productivity principles.", "category": "PRODUCTIVE_PRACTICAL", "type": "domain", "source_file": "domains.json"}, "prod_skill": {"code": "prod_skill", "name": "Practical Skill Development", "description": "Learning and application of concrete abilities useful in everyday life or specific contexts.", "category": "PRODUCTIVE_PRACTICAL", "type": "domain", "source_file": "domains.json"}, "prod_financial": {"code": "prod_financial", "name": "Financial Management", "description": "Activities related to budgeting, saving, investing, or otherwise managing personal economic resources.", "category": "PRODUCTIVE_PRACTICAL", "type": "domain", "source_file": "domains.json"}, "prod_career": {"code": "prod_career", "name": "Career Development", "description": "Advancement of professional skills, networks, or positioning within work contexts.", "category": "PRODUCTIVE_PRACTICAL", "type": "domain", "source_file": "domains.json"}, "prod_health": {"code": "prod_health", "name": "Health & Wellness Systems", "description": "Practical approaches to maintaining or improving physical and mental wellbeing through consistent practices.", "category": "PRODUCTIVE_PRACTICAL", "type": "domain", "source_file": "domains.json"}, "prod_home": {"code": "prod_home", "name": "Home Management", "description": "Activities related to maintaining, improving, or organizing living spaces for functionality and comfort.", "category": "PRODUCTIVE_PRACTICAL", "type": "domain", "source_file": "domains.json"}, "prod_transition": {"code": "prod_transition", "name": "Transition Moments", "description": "Activities designed for in-between times such as waiting, commuting, or brief breaks between tasks that maximize productivity during idle moments.", "category": "PRODUCTIVE_PRACTICAL", "type": "domain", "source_file": "domains.json"}, "leisure_recreational": {"code": "leisure_recreational", "name": "Leisure & Recreational", "description": "a broad domain encompassing all recreational and leisure activities.", "category": "LEISURE_RECREATIONAL", "type": "domain", "source_file": "domains.json"}, "leisure_relax": {"code": "leisure_relax", "name": "Relaxation", "description": "Activities specifically oriented toward rest, recovery, and the release of tension or effort.", "category": "LEISURE_RECREATIONAL", "type": "domain", "source_file": "domains.json"}, "leisure_play": {"code": "leisure_play", "name": "Play & Games", "description": "Engagement in activities pursued primarily for enjoyment rather than external outcomes, including structured games or free play.", "category": "LEISURE_RECREATIONAL", "type": "domain", "source_file": "domains.json"}, "leisure_entertain": {"code": "leisure_entertain", "name": "Entertainment Consumption", "description": "Enjoyment of media, performances, or experiences created by others, such as films, music, or shows.", "category": "LEISURE_RECREATIONAL", "type": "domain", "source_file": "domains.json"}, "leisure_collect": {"code": "leisure_collect", "name": "Collection & Curation", "description": "Activities involving gathering, organizing, and appreciating objects or information of personal interest.", "category": "LEISURE_RECREATIONAL", "type": "domain", "source_file": "domains.json"}, "leisure_nature": {"code": "leisure_nature", "name": "Recreational Nature Activities", "description": "Enjoyable outdoor pursuits that connect with natural environments in a leisurely context.", "category": "LEISURE_RECREATIONAL", "type": "domain", "source_file": "domains.json"}, "leisure_social": {"code": "leisure_social", "name": "Social Recreation", "description": "Leisure activities specifically focused on enjoying time with others in low-pressure social contexts.", "category": "LEISURE_RECREATIONAL", "type": "domain", "source_file": "domains.json"}, "leisure_hobby": {"code": "leisure_hobby", "name": "<PERSON><PERSON> Participation", "description": "Regular engagement in non-professional interests that provide satisfaction and enjoyment.", "category": "LEISURE_RECREATIONAL", "type": "domain", "source_file": "domains.json"}, "leisure_festive": {"code": "leisure_festive", "name": "Celebration & Festivity", "description": "Participation in events or traditions that mark special occasions or cultural significance in enjoyable ways.", "category": "LEISURE_RECREATIONAL", "type": "domain", "source_file": "domains.json"}, "general": {"code": "general", "name": "General", "description": "A fallback domain for activities that do not fit into specific categories or when domain classification is uncertain.", "category": "GENERAL", "type": "domain", "source_file": "domains.json"}, "honesty_sincerity": {"code": "honesty_sincerity", "name": "Sincerity", "description": "Genuineness in self-expression and relationships without manipulation.", "category": "HONESTYHUMILITY", "type": "trait", "source_file": "user_profile_catalog.json"}, "honesty_fairness": {"code": "honesty_fairness", "name": "Fairness", "description": "Tendency to avoid exploiting others for personal gain.", "category": "HONESTYHUMILITY", "type": "trait", "source_file": "user_profile_catalog.json"}, "honesty_greed_avoidance": {"code": "honesty_greed_avoidance", "name": "Greed Avoidance", "description": "Level of disinterest in luxury, wealth, and social status.", "category": "HONESTYHUMILITY", "type": "trait", "source_file": "user_profile_catalog.json"}, "honesty_modesty": {"code": "honesty_modesty", "name": "Modesty", "description": "Tendency to be humble and unassuming about achievements.", "category": "HONESTYHUMILITY", "type": "trait", "source_file": "user_profile_catalog.json"}, "emotion_fearfulness": {"code": "emotion_fearfulness", "name": "Fearfulness", "description": "Tendency to experience fear in response to potential dangers.", "category": "EMOTIONALITY", "type": "trait", "source_file": "user_profile_catalog.json"}, "emotion_anxiety": {"code": "emotion_anxiety", "name": "Anxiety", "description": "Tendency to worry in a variety of contexts.", "category": "EMOTIONALITY", "type": "trait", "source_file": "user_profile_catalog.json"}, "emotion_dependence": {"code": "emotion_dependence", "name": "Dependence", "description": "Need for emotional support and reassurance from others.", "category": "EMOTIONALITY", "type": "trait", "source_file": "user_profile_catalog.json"}, "emotion_sentimentality": {"code": "emotion_sentimentality", "name": "Sentimentality", "description": "Tendency to form strong emotional bonds and empathic responses.", "category": "EMOTIONALITY", "type": "trait", "source_file": "user_profile_catalog.json"}, "extra_self_esteem": {"code": "extra_self_esteem", "name": "Social Self-Esteem", "description": "Confidence and positive self-evaluation in social contexts.", "category": "EXTRAVERSION", "type": "trait", "source_file": "user_profile_catalog.json"}, "extra_social_boldness": {"code": "extra_social_boldness", "name": "Social Boldness", "description": "<PERSON><PERSON><PERSON> in a variety of social situations and leadership roles.", "category": "EXTRAVERSION", "type": "trait", "source_file": "user_profile_catalog.json"}, "extra_sociability": {"code": "extra_sociability", "name": "Sociability", "description": "Enjoyment of social gatherings and interactions with others.", "category": "EXTRAVERSION", "type": "trait", "source_file": "user_profile_catalog.json"}, "extra_liveliness": {"code": "extra_liveliness", "name": "Liveliness", "description": "Energy level and enthusiasm in social and activity contexts.", "category": "EXTRAVERSION", "type": "trait", "source_file": "user_profile_catalog.json"}, "agree_forgiveness": {"code": "agree_forgiveness", "name": "Forgiveness", "description": "Willingness to trust and forgive those who have caused harm.", "category": "AGREEABLENESS", "type": "trait", "source_file": "user_profile_catalog.json"}, "agree_gentleness": {"code": "agree_gentleness", "name": "Gentleness", "description": "Tendency to be mild and lenient in interactions with others.", "category": "AGREEABLENESS", "type": "trait", "source_file": "user_profile_catalog.json"}, "agree_flexibility": {"code": "agree_flexibility", "name": "Flexibility", "description": "Willingness to compromise and cooperate with others.", "category": "AGREEABLENESS", "type": "trait", "source_file": "user_profile_catalog.json"}, "agree_patience": {"code": "agree_patience", "name": "Patience", "description": "Tendency to remain calm rather than becoming angry.", "category": "AGREEABLENESS", "type": "trait", "source_file": "user_profile_catalog.json"}, "consc_organization": {"code": "consc_organization", "name": "Organization", "description": "Tendency to seek order and structure in the physical environment.", "category": "CONSCIENTIOUSNESS", "type": "trait", "source_file": "user_profile_catalog.json"}, "consc_diligence": {"code": "consc_diligence", "name": "Diligence", "description": "Work ethic and persistence in pursuing goals.", "category": "CONSCIENTIOUSNESS", "type": "trait", "source_file": "user_profile_catalog.json"}, "consc_perfectionism": {"code": "consc_perfectionism", "name": "Perfectionism", "description": "Thoroughness and concern with details and accuracy.", "category": "CONSCIENTIOUSNESS", "type": "trait", "source_file": "user_profile_catalog.json"}, "consc_prudence": {"code": "consc_prudence", "name": "Prudence", "description": "Tendency to deliberate carefully and inhibit impulses.", "category": "CONSCIENTIOUSNESS", "type": "trait", "source_file": "user_profile_catalog.json"}, "open_aesthetic": {"code": "open_aesthetic", "name": "Aesthetic Appreciation", "description": "Enjoyment of beauty in art, music, and nature.", "category": "OPENNESS", "type": "trait", "source_file": "user_profile_catalog.json"}, "open_inquisitive": {"code": "open_inquisitive", "name": "Inquisitiveness", "description": "Interest in exploring new ideas and understanding complex concepts.", "category": "OPENNESS", "type": "trait", "source_file": "user_profile_catalog.json"}, "open_creativity": {"code": "open_creativity", "name": "Creativity", "description": "Preference for innovation and experimentation.", "category": "OPENNESS", "type": "trait", "source_file": "user_profile_catalog.json"}, "open_unconventional": {"code": "open_unconventional", "name": "Unconventionality", "description": "Willingness to accept the unusual and challenge tradition.", "category": "OPENNESS", "type": "trait", "source_file": "user_profile_catalog.json"}}}