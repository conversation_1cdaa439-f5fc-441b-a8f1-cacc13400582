#!/usr/bin/env python3
"""
Update the user profile JSON schema to match the current database codes.

This script synchronizes the JSON schema with the actual database state,
fixing the root cause of the import validation issues.

Usage: docker exec -it backend-web-1 python /usr/src/app/update_schema_from_database.py
"""

import os
import sys
import django
import json
from datetime import datetime

# Setup Django
sys.path.append('/usr/src/app')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from apps.user.models import GenericResource, GenericEnvironment
from apps.activity.models import GenericDomain

def update_generic_resources_in_schema():
    """Update resource codes in the JSON schema to match database."""
    print("🔄 Updating Resource Codes in Schema...")
    
    # Get current database codes
    db_codes = list(GenericResource.objects.values_list('code', flat=True).order_by('code'))
    print(f"📊 Found {len(db_codes)} resource codes in database")
    
    # Load current schema
    schema_path = '/usr/src/app/schemas/user_profile.schema.json'
    with open(schema_path, 'r') as f:
        schema = json.load(f)
    
    # Update resource codes
    old_codes = schema['properties']['resources']['items']['properties']['generic_resource']['enum']
    schema['properties']['resources']['items']['properties']['generic_resource']['enum'] = db_codes
    
    print(f"📊 Updated resource codes: {len(old_codes)} → {len(db_codes)}")
    
    return schema, old_codes, db_codes

def update_environment_codes_in_schema(schema):
    """Update environment codes in the JSON schema to match database."""
    print("🔄 Updating Environment Codes in Schema...")

    # Get current database codes
    db_codes = list(GenericEnvironment.objects.values_list('code', flat=True).order_by('code'))
    print(f"📊 Found {len(db_codes)} environment codes in database")

    # Check if environment codes field exists and update it
    env_props = schema['properties']['environments']['items']['properties']
    if 'generic_environment_code' in env_props:
        # Add enum constraint if it doesn't exist
        if 'enum' not in env_props['generic_environment_code']:
            env_props['generic_environment_code']['enum'] = db_codes
            old_codes = []
        else:
            old_codes = env_props['generic_environment_code']['enum']
            env_props['generic_environment_code']['enum'] = db_codes

        print(f"📊 Updated environment codes: {len(old_codes)} → {len(db_codes)}")
        return schema, old_codes, db_codes
    else:
        print("⚠️ No generic_environment_code field found in schema")
        return schema, [], db_codes

def update_domain_codes_in_schema(schema):
    """Update domain codes in the JSON schema to match database."""
    print("🔄 Updating Domain Codes in Schema...")
    
    # Get current database codes
    db_codes = list(GenericDomain.objects.values_list('code', flat=True).order_by('code'))
    print(f"📊 Found {len(db_codes)} domain codes in database")
    
    # Find all domain code references in schema
    updated_count = 0
    
    # Update aspirations domain codes
    if 'aspirations' in schema['properties']:
        aspirations_items = schema['properties']['aspirations']['items']['properties']
        if 'domain_code' in aspirations_items:
            old_codes = aspirations_items['domain_code']['enum']
            aspirations_items['domain_code']['enum'] = db_codes
            print(f"📊 Updated aspirations domain codes: {len(old_codes)} → {len(db_codes)}")
            updated_count += 1
    
    # Update trust_level domain scores
    if 'trust_level' in schema['properties']:
        trust_props = schema['properties']['trust_level']['properties']
        if 'domain_scores' in trust_props:
            # Domain scores use the same domain codes as keys
            # Update the description to reflect current domains
            trust_props['domain_scores']['description'] = f"Trust scores for each domain. Valid domains: {', '.join(db_codes)}"
            updated_count += 1
    
    print(f"📊 Updated {updated_count} domain code references")
    
    return schema

def backup_and_save_schema(schema):
    """Backup the old schema and save the new one."""
    print("💾 Backing up and saving schema...")
    
    schema_path = '/usr/src/app/schemas/user_profile.schema.json'
    backup_path = f'/usr/src/app/schemas/user_profile.schema.json.backup.{datetime.now().strftime("%Y%m%d_%H%M%S")}'
    
    # Create backup
    with open(schema_path, 'r') as f:
        old_schema = f.read()
    
    with open(backup_path, 'w') as f:
        f.write(old_schema)
    
    print(f"📁 Backup saved to: {backup_path}")
    
    # Add metadata to new schema
    schema['_metadata'] = {
        'updated_at': datetime.now().isoformat(),
        'updated_by': 'update_schema_from_database.py',
        'description': 'Schema synchronized with database codes',
        'backup_file': backup_path
    }
    
    # Save updated schema
    with open(schema_path, 'w') as f:
        json.dump(schema, f, indent=2)
    
    print(f"✅ Updated schema saved to: {schema_path}")

def test_updated_schema():
    """Test the updated schema with the fixed profile."""
    print("\n🧪 Testing Updated Schema...")
    
    from django.contrib.auth import get_user_model
    from django.test import Client
    
    User = get_user_model()
    
    # Create or get a staff user
    try:
        user = User.objects.get(username='admin')
    except User.DoesNotExist:
        user = User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
    
    # Load the fixed profile
    try:
        with open('/usr/src/app/guigui_fixed_complete.json', 'r') as f:
            profile_data = json.load(f)
    except FileNotFoundError:
        print("❌ Fixed profile not found, using original")
        with open('/usr/src/app/guigui_db_compliant.json', 'r') as f:
            profile_data = json.load(f)
    
    # Create a test client and login
    client = Client()
    client.force_login(user)
    
    # Test validation
    request_data = {
        'profile_data': profile_data,
        'options': {
            'validateBeforeImport': True
        }
    }
    
    response = client.post(
        '/admin/user-profiles/validate/',
        data=json.dumps(request_data),
        content_type='application/json'
    )
    
    print(f"📊 Validation response status: {response.status_code}")
    
    if response.status_code == 200:
        print("✅ Schema validation PASSED!")
        result = response.json()
        if result.get('success'):
            print("  ✅ Profile validation successful")
            return True
        else:
            print(f"  ❌ Validation failed: {result.get('error', 'Unknown error')}")
            return False
    else:
        print("❌ Schema validation FAILED!")
        try:
            error_data = response.json()
            print(f"  Error: {error_data.get('error', 'Unknown error')}")
            if 'schema_errors' in error_data:
                for error in error_data['schema_errors']:
                    print(f"    - {error}")
        except:
            print(f"  Raw response: {response.content.decode()}")
        return False

def main():
    """Main function to update schema from database."""
    print("🚀 Update JSON Schema from Database")
    print("=" * 50)
    
    try:
        # Step 1: Update resource codes
        schema, old_generic_resources, new_generic_resources = update_generic_resources_in_schema()
        
        # Step 2: Update environment codes
        schema, old_env_codes, new_env_codes = update_environment_codes_in_schema(schema)
        
        # Step 3: Update domain codes
        schema = update_domain_codes_in_schema(schema)
        
        # Step 4: Backup and save
        backup_and_save_schema(schema)
        
        # Step 5: Test the updated schema
        success = test_updated_schema()
        
        # Step 6: Summary
        print("\n" + "=" * 50)
        print("📋 SUMMARY")
        print("=" * 50)
        
        if success:
            print("✅ SCHEMA UPDATE SUCCESSFUL!")
            print("✅ Schema now matches database codes")
            print("✅ Profile validation passes")
        else:
            print("❌ Schema updated but validation still fails")
            print("❌ Additional investigation needed")
        
        print(f"\n📊 Changes Made:")
        print(f"  - Resource codes: {len(old_generic_resources)} → {len(new_generic_resources)}")
        print(f"  - Environment codes: {len(old_env_codes)} → {len(new_env_codes)}")
        print(f"  - Domain code references updated")
        
        print(f"\n📁 Files:")
        print(f"  - Schema: /usr/src/app/schemas/user_profile.schema.json")
        print(f"  - Backup: Created with timestamp")
        
        return success
        
    except Exception as e:
        print(f"❌ Error updating schema: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    main()
