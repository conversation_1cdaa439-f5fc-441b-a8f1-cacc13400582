#!/usr/bin/env node

/**
 * Comprehensive UX Test - Test the complete user experience
 */

const { chromium } = require('playwright');

async function testComprehensiveUX() {
    console.log('🎨 Starting comprehensive UX test...');
    
    const browser = await chromium.launch({ headless: false });
    const page = await browser.newPage();
    
    const uxMetrics = {
        loadTime: 0,
        connectionTime: 0,
        responseTime: 0,
        wheelGenerationTime: 0,
        userInteractionResponsiveness: [],
        visualElements: {},
        accessibility: {},
        errors: []
    };
    
    const startTime = Date.now();
    
    try {
        // Test 1: Page Load Performance
        console.log('📱 Testing page load performance...');
        const loadStart = Date.now();
        await page.goto('http://localhost:3000/');
        await page.waitForLoadState('networkidle');
        uxMetrics.loadTime = Date.now() - loadStart;
        console.log(`✅ Page loaded in ${uxMetrics.loadTime}ms`);
        
        // Test 2: Connection Establishment
        console.log('🔌 Testing connection establishment...');
        const connectionStart = Date.now();
        await page.waitForFunction(() => {
            const appShell = document.querySelector('app-shell');
            if (!appShell || !appShell.shadowRoot) return false;
            const chatInterface = appShell.shadowRoot.querySelector('chat-interface');
            return chatInterface && chatInterface.connectionStatus === 'connected';
        }, { timeout: 15000 });
        uxMetrics.connectionTime = Date.now() - connectionStart;
        console.log(`✅ Connected in ${uxMetrics.connectionTime}ms`);
        
        // Test 3: Visual Elements Assessment
        console.log('🎨 Assessing visual elements...');
        uxMetrics.visualElements = await page.evaluate(() => {
            const appShell = document.querySelector('app-shell');
            if (!appShell || !appShell.shadowRoot) return { error: 'No app shell' };
            
            const header = appShell.shadowRoot.querySelector('.header');
            const wheelSection = appShell.shadowRoot.querySelector('.wheel-section');
            const chatSection = appShell.shadowRoot.querySelector('.chat-section');
            const chatInterface = appShell.shadowRoot.querySelector('chat-interface');
            
            return {
                header: {
                    exists: !!header,
                    hasLogo: !!header?.querySelector('.logo'),
                    hasConnectionStatus: !!header?.querySelector('.connection-status'),
                    debugMode: header?.textContent?.includes('DEBUG') || false
                },
                wheelSection: {
                    exists: !!wheelSection,
                    hasPlaceholder: !!wheelSection?.querySelector('.wheel-placeholder'),
                    placeholderText: wheelSection?.querySelector('.wheel-placeholder h3')?.textContent || 'none'
                },
                chatSection: {
                    exists: !!chatSection,
                    hasChatInterface: !!chatInterface,
                    chatTitle: chatInterface?.shadowRoot?.querySelector('.chat-header')?.textContent || 'none'
                }
            };
        });
        
        console.log('📊 Visual elements:', JSON.stringify(uxMetrics.visualElements, null, 2));
        
        // Test 4: Chat Interface Responsiveness
        console.log('💬 Testing chat interface responsiveness...');
        
        const interactionTests = [
            {
                name: 'Focus textarea',
                action: async () => {
                    const focusStart = Date.now();
                    await page.evaluate(() => {
                        const appShell = document.querySelector('app-shell');
                        const chatInterface = appShell.shadowRoot.querySelector('chat-interface');
                        const textarea = chatInterface.shadowRoot.querySelector('textarea');
                        textarea.focus();
                    });
                    return Date.now() - focusStart;
                }
            },
            {
                name: 'Type message',
                action: async () => {
                    const typeStart = Date.now();
                    await page.evaluate(() => {
                        const appShell = document.querySelector('app-shell');
                        const chatInterface = appShell.shadowRoot.querySelector('chat-interface');
                        const textarea = chatInterface.shadowRoot.querySelector('textarea');
                        textarea.value = "I'm feeling creative and have 45 minutes. What should I do?";
                        textarea.dispatchEvent(new Event('input', { bubbles: true }));
                    });
                    return Date.now() - typeStart;
                }
            },
            {
                name: 'Send message',
                action: async () => {
                    const sendStart = Date.now();
                    await page.evaluate(() => {
                        const appShell = document.querySelector('app-shell');
                        const chatInterface = appShell.shadowRoot.querySelector('chat-interface');
                        const textarea = chatInterface.shadowRoot.querySelector('textarea');
                        const enterEvent = new KeyboardEvent('keypress', {
                            key: 'Enter',
                            code: 'Enter',
                            keyCode: 13,
                            which: 13,
                            bubbles: true
                        });
                        textarea.dispatchEvent(enterEvent);
                    });
                    return Date.now() - sendStart;
                }
            }
        ];
        
        for (const test of interactionTests) {
            const responseTime = await test.action();
            uxMetrics.userInteractionResponsiveness.push({
                action: test.name,
                responseTime: responseTime
            });
            console.log(`  ✅ ${test.name}: ${responseTime}ms`);
            await page.waitForTimeout(500); // Small delay between interactions
        }
        
        // Test 5: Wheel Generation Performance
        console.log('🎡 Testing wheel generation performance...');
        const wheelStart = Date.now();
        
        // Wait for wheel to appear
        await page.waitForFunction(() => {
            const appShell = document.querySelector('app-shell');
            if (!appShell || !appShell.shadowRoot) return false;
            const gameWheel = appShell.shadowRoot.querySelector('game-wheel');
            return !!gameWheel;
        }, { timeout: 120000 });
        
        uxMetrics.wheelGenerationTime = Date.now() - wheelStart;
        console.log(`✅ Wheel generated in ${uxMetrics.wheelGenerationTime}ms`);
        
        // Test 6: Final State Assessment
        console.log('🔍 Assessing final state...');
        const finalState = await page.evaluate(() => {
            const appShell = document.querySelector('app-shell');
            if (!appShell || !appShell.shadowRoot) return { error: 'No app shell' };
            
            const gameWheel = appShell.shadowRoot.querySelector('game-wheel');
            const chatInterface = appShell.shadowRoot.querySelector('chat-interface');
            
            return {
                wheelRendered: !!gameWheel,
                wheelSegments: gameWheel?.wheelData?.segments?.length || 0,
                chatMessages: chatInterface?.messages?.length || 0,
                connectionStatus: chatInterface?.connectionStatus || 'unknown',
                isProcessing: chatInterface?.isProcessing || false
            };
        });
        
        console.log('📊 Final state:', JSON.stringify(finalState, null, 2));
        
        // Test 7: Accessibility Check
        console.log('♿ Testing accessibility...');
        uxMetrics.accessibility = await page.evaluate(() => {
            const results = {
                hasAltText: true,
                hasAriaLabels: true,
                hasKeyboardNavigation: true,
                hasSemanticHTML: true,
                colorContrast: 'unknown'
            };
            
            // Check for images without alt text
            const images = document.querySelectorAll('img');
            for (const img of images) {
                if (!img.alt) {
                    results.hasAltText = false;
                    break;
                }
            }
            
            // Check for semantic HTML
            const hasMain = !!document.querySelector('main');
            const hasHeader = !!document.querySelector('header');
            const hasSection = !!document.querySelector('section');
            results.hasSemanticHTML = hasMain && hasHeader && hasSection;
            
            return results;
        });
        
        console.log('♿ Accessibility results:', JSON.stringify(uxMetrics.accessibility, null, 2));
        
    } catch (error) {
        console.error('❌ UX test error:', error);
        uxMetrics.errors.push(error.message);
    }
    
    const totalTime = Date.now() - startTime;
    
    // Generate UX Report
    console.log('\n🎨 COMPREHENSIVE UX REPORT');
    console.log('════════════════════════════════════════════════════════════');
    
    console.log('\n⚡ Performance Metrics:');
    console.log(`  Page Load Time: ${uxMetrics.loadTime}ms ${uxMetrics.loadTime < 3000 ? '✅' : '⚠️'}`);
    console.log(`  Connection Time: ${uxMetrics.connectionTime}ms ${uxMetrics.connectionTime < 5000 ? '✅' : '⚠️'}`);
    console.log(`  Wheel Generation: ${uxMetrics.wheelGenerationTime}ms ${uxMetrics.wheelGenerationTime < 60000 ? '✅' : '⚠️'}`);
    console.log(`  Total Test Time: ${totalTime}ms`);
    
    console.log('\n🖱️  Interaction Responsiveness:');
    uxMetrics.userInteractionResponsiveness.forEach(interaction => {
        const status = interaction.responseTime < 100 ? '✅' : interaction.responseTime < 300 ? '⚠️' : '❌';
        console.log(`  ${interaction.action}: ${interaction.responseTime}ms ${status}`);
    });
    
    console.log('\n🎨 Visual Elements:');
    console.log(`  Header: ${uxMetrics.visualElements.header?.exists ? '✅' : '❌'}`);
    console.log(`  Wheel Section: ${uxMetrics.visualElements.wheelSection?.exists ? '✅' : '❌'}`);
    console.log(`  Chat Interface: ${uxMetrics.visualElements.chatSection?.exists ? '✅' : '❌'}`);
    console.log(`  Debug Mode: ${uxMetrics.visualElements.header?.debugMode ? '✅ Active' : '⚠️ Inactive'}`);
    
    console.log('\n♿ Accessibility:');
    console.log(`  Alt Text: ${uxMetrics.accessibility.hasAltText ? '✅' : '❌'}`);
    console.log(`  Semantic HTML: ${uxMetrics.accessibility.hasSemanticHTML ? '✅' : '❌'}`);
    
    if (uxMetrics.errors.length > 0) {
        console.log('\n❌ Errors:');
        uxMetrics.errors.forEach((error, i) => {
            console.log(`  ${i+1}. ${error}`);
        });
    }
    
    // Overall UX Score
    const performanceScore = (uxMetrics.loadTime < 3000 ? 25 : 0) + 
                           (uxMetrics.connectionTime < 5000 ? 25 : 0) + 
                           (uxMetrics.wheelGenerationTime < 60000 ? 25 : 0);
    
    const interactionScore = uxMetrics.userInteractionResponsiveness.reduce((score, interaction) => {
        return score + (interaction.responseTime < 100 ? 10 : interaction.responseTime < 300 ? 5 : 0);
    }, 0);
    
    const visualScore = (uxMetrics.visualElements.header?.exists ? 10 : 0) + 
                       (uxMetrics.visualElements.wheelSection?.exists ? 10 : 0) + 
                       (uxMetrics.visualElements.chatSection?.exists ? 5 : 0);
    
    const totalScore = performanceScore + Math.min(interactionScore, 25) + visualScore;
    
    console.log(`\n🏆 OVERALL UX SCORE: ${totalScore}/100`);
    if (totalScore >= 90) {
        console.log('🎉 EXCELLENT UX - Production ready!');
    } else if (totalScore >= 75) {
        console.log('✅ GOOD UX - Minor improvements possible');
    } else if (totalScore >= 60) {
        console.log('⚠️  ACCEPTABLE UX - Some improvements needed');
    } else {
        console.log('❌ POOR UX - Significant improvements required');
    }
    
    await browser.close();
    return uxMetrics;
}

testComprehensiveUX().catch(console.error);
