#!/usr/bin/env node

/**
 * Debug Wheel Frontend - Detailed WebSocket message inspection
 * This tool monitors WebSocket messages and analyzes wheel data processing
 */

const puppeteer = require('puppeteer');
const WebSocket = require('ws');

class WheelFrontendDebugger {
  constructor() {
    this.browser = null;
    this.page = null;
    this.wsClient = null;
    this.receivedMessages = [];
  }

  async initialize() {
    console.log('🔍 Initializing Wheel Frontend Debugger...');
    
    // Launch browser
    this.browser = await puppeteer.launch({
      headless: false,
      devtools: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    this.page = await this.browser.newPage();
    
    // Enable console logging
    this.page.on('console', msg => {
      const type = msg.type();
      const text = msg.text();
      console.log(`🖥️  [${type}] ${text}`);
    });
    
    // Enable error logging
    this.page.on('pageerror', error => {
      console.log(`❌ Page error: ${error.message}`);
    });
    
    console.log('✅ Debugger initialized');
  }

  async loadFrontend() {
    console.log('🌐 Loading frontend...');
    await this.page.goto('http://localhost:3000', { waitUntil: 'networkidle0' });
    
    // Wait for WebSocket connection
    await this.page.waitForFunction(() => {
      return window.goaliApp && window.goaliApp.isConnected;
    }, { timeout: 10000 });
    
    console.log('✅ Frontend loaded and connected');
  }

  async setupWebSocketMonitor() {
    console.log('🔌 Setting up WebSocket monitor...');
    
    // Monitor WebSocket messages from the page
    await this.page.evaluateOnNewDocument(() => {
      window.wheelDebugMessages = [];
      
      // Override WebSocket to capture messages
      const originalWebSocket = window.WebSocket;
      window.WebSocket = function(url, protocols) {
        const ws = new originalWebSocket(url, protocols);
        
        const originalOnMessage = ws.onmessage;
        ws.onmessage = function(event) {
          try {
            const data = JSON.parse(event.data);
            window.wheelDebugMessages.push({
              timestamp: new Date().toISOString(),
              type: data.type,
              data: data
            });
            
            if (data.type === 'wheel_data') {
              console.log('🎡 WHEEL_DATA MESSAGE RECEIVED:', JSON.stringify(data, null, 2));
            }
          } catch (e) {
            console.log('📨 Raw WebSocket message:', event.data);
          }
          
          if (originalOnMessage) {
            originalOnMessage.call(this, event);
          }
        };
        
        return ws;
      };
    });
    
    console.log('✅ WebSocket monitor setup complete');
  }

  async sendWheelRequest() {
    console.log('💬 Sending wheel generation request...');
    
    // Send message through the frontend
    await this.page.evaluate(() => {
      if (window.goaliApp && window.goaliApp.sendMessage) {
        window.goaliApp.sendMessage("hey, I'm feeling energetic and I have 2h free ahead of me. It's very hot outside though. Generate me the perfect wheel !");
      }
    });
    
    console.log('✅ Message sent');
  }

  async waitForWheelData(timeoutMs = 120000) {
    console.log('⏳ Waiting for wheel data...');
    
    try {
      await this.page.waitForFunction(() => {
        return window.wheelDebugMessages && 
               window.wheelDebugMessages.some(msg => msg.type === 'wheel_data');
      }, { timeout: timeoutMs });
      
      console.log('✅ Wheel data received');
      return true;
    } catch (error) {
      console.log('❌ Timeout waiting for wheel data');
      return false;
    }
  }

  async analyzeWheelData() {
    console.log('🔍 Analyzing wheel data...');
    
    const analysis = await this.page.evaluate(() => {
      const wheelMessages = window.wheelDebugMessages.filter(msg => msg.type === 'wheel_data');
      
      if (wheelMessages.length === 0) {
        return { error: 'No wheel_data messages found' };
      }
      
      const latestWheelMessage = wheelMessages[wheelMessages.length - 1];
      const wheelData = latestWheelMessage.data.data;
      
      return {
        messageCount: wheelMessages.length,
        latestMessage: {
          timestamp: latestWheelMessage.timestamp,
          hasSegments: !!(wheelData && wheelData.segments),
          segmentCount: wheelData && wheelData.segments ? wheelData.segments.length : 0,
          hasItems: !!(wheelData && wheelData.items),
          itemCount: wheelData && wheelData.items ? wheelData.items.length : 0,
          hasActivities: !!(wheelData && wheelData.activities),
          activityCount: wheelData && wheelData.activities ? wheelData.activities.length : 0,
          wheelDataKeys: wheelData ? Object.keys(wheelData) : [],
          firstItem: wheelData && wheelData.items && wheelData.items[0] ? wheelData.items[0] : null,
          firstActivity: wheelData && wheelData.activities && wheelData.activities[0] ? wheelData.activities[0] : null
        }
      };
    });
    
    console.log('📊 WHEEL DATA ANALYSIS:');
    console.log(JSON.stringify(analysis, null, 2));
    
    return analysis;
  }

  async checkWheelDisplay() {
    console.log('🎡 Checking wheel display...');
    
    const displayInfo = await this.page.evaluate(() => {
      // Check for wheel container
      const wheelContainer = document.querySelector('.wheel-container, #wheel-container, [data-testid="wheel"]');
      
      // Check for wheel segments
      const segments = document.querySelectorAll('.wheel-segment, .segment, [data-testid="segment"]');
      
      // Check for wheel canvas
      const canvas = document.querySelector('canvas');
      
      // Check for wheel component
      const wheelComponent = document.querySelector('game-wheel');
      
      return {
        hasWheelContainer: !!wheelContainer,
        wheelContainerClass: wheelContainer ? wheelContainer.className : null,
        segmentCount: segments.length,
        hasCanvas: !!canvas,
        canvasSize: canvas ? { width: canvas.width, height: canvas.height } : null,
        hasWheelComponent: !!wheelComponent,
        wheelComponentData: wheelComponent ? {
          hasWheelData: !!wheelComponent.wheelData,
          segmentCount: wheelComponent.segments ? wheelComponent.segments.length : 0
        } : null
      };
    });
    
    console.log('🖼️  WHEEL DISPLAY INFO:');
    console.log(JSON.stringify(displayInfo, null, 2));
    
    return displayInfo;
  }

  async getAllMessages() {
    console.log('📨 Getting all WebSocket messages...');
    
    const messages = await this.page.evaluate(() => {
      return window.wheelDebugMessages || [];
    });
    
    console.log(`📊 Total messages received: ${messages.length}`);
    
    const messageTypes = {};
    messages.forEach(msg => {
      messageTypes[msg.type] = (messageTypes[msg.type] || 0) + 1;
    });
    
    console.log('📈 Message types:', messageTypes);
    
    return messages;
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
    }
  }

  async runFullDebugSession() {
    try {
      await this.initialize();
      await this.setupWebSocketMonitor();
      await this.loadFrontend();
      
      // Wait a moment for everything to settle
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      await this.sendWheelRequest();
      
      const wheelDataReceived = await this.waitForWheelData();
      
      if (wheelDataReceived) {
        await this.analyzeWheelData();
        await this.checkWheelDisplay();
      }
      
      await this.getAllMessages();
      
      console.log('🎯 Debug session completed');
      
    } catch (error) {
      console.error('❌ Debug session failed:', error);
    } finally {
      // Keep browser open for manual inspection
      console.log('🔍 Browser kept open for manual inspection. Press Ctrl+C to exit.');
      
      // Wait indefinitely
      await new Promise(() => {});
    }
  }
}

// Run the debugger
const wheelDebugger = new WheelFrontendDebugger();
wheelDebugger.runFullDebugSession().catch(console.error);
