name: monkfish-app
region: lon
services:
  - name: goali-backend
    github:
      repo: elgui/goali
      branch: gguine/prod-slow
      deploy_on_push: true
    build_command: python manage.py migrate --noinput && python manage.py shell -c "from django.contrib.auth import get_user_model; import os; User = get_user_model(); User.objects.create_superuser('admin', '<EMAIL>', os.environ.get('DJANGO_ADMIN_PASSWORD', 'defaultpassword')) if not User.objects.filter(username='admin').exists() else None"
    run_command: python manage.py start_production
    source_dir: backend
    environment_slug: python
    envs:
      - key: DJANGO_SETTINGS_MODULE
        value: config.settings.prod
        scope: RUN_AND_BUILD_TIME
      - key: STATIC_ROOT
        value: /app/staticfiles
        scope: RUN_AND_BUILD_TIME
      - key: DJANGO_ALLOWED_HOSTS
        value: 127.0.0.1,localhost,monkfish-app-jvgae.ondigitalocean.app
        scope: RUN_AND_BUILD_TIME
      - key: DJANGO_ADMIN_PASSWORD
        value: EV[1:GS5Kb9jYNZ7BX90j7xjLjBk3rf9CKUsF:ZhDXcL9Ia+lkoDyIU81XXZkjgh0WB/Zem+urTtKgoA==]
        scope: RUN_AND_BUILD_TIME
        type: SECRET
      - key: DATABASE_URL
        value: EV[1:4M7RY7uDcyYqa1mz1FgYdbb2jQyq+SD4:lAHqDaLVw1kVaytnVH1sFiuhNkhrS7d2d5sLKm6V61KGud4PeT7OV6JAJxkeOM1I+dbIJu28pJxfB8hnZ9nuDKLW8eDM0fLcJ6EdNQnM2RnrSEd+QbCsrc6mnqNFtUEABsPEM7CynxIvKKC/3j+/JRhB2oNeHS+xBUrOqmjsJkKeiDrxi6HzE9mFxRJKx2WeH72O588=]
        scope: RUN_AND_BUILD_TIME
        type: SECRET
      - key: MISTRAL_API_KEY
        value: EV[1:ozWac/j5PFsbweHDnazsxKtUIVL7RCgQ:ZDf9uMMDmsWM79OfG5z9Rfjmafl5y2osvHA7Y9Njhjy9nJ2kWsMtWblkFa+9W8Tz]
        scope: RUN_AND_BUILD_TIME
        type: SECRET
      - key: DJANGO_SECRET_KEY
        value: EV[1:fOT1spL8aMqulKRDB3mqegtnDenVz9bs:1Ycxz/cXHRl3YV30PfVWqpgx9gEbEA44MoBgXn58nQazUSjYBYzIg7hAg/VuWShwvtTgQps1JFKPEbm6WcghiFjQmU4=]
        scope: RUN_AND_BUILD_TIME
        type: SECRET
      - key: REDIS_URL
        value: rediss://default:<EMAIL>:25061/0
        scope: RUN_AND_BUILD_TIME
    instance_size_slug: apps-s-1vcpu-1gb
    instance_count: 2
    http_port: 8080

static_sites:
  - name: goali-frontend
    github:
      repo: elgui/goali
      branch: gguine/prod-slow
      deploy_on_push: true
    build_command: npm ci && npm run build:prod
    source_dir: frontend
    output_dir: /dist
  
  - name: static-files
    github:
      repo: elgui/goali
      branch: gguine/prod-slow
      deploy_on_push: true
    build_command: DJANGO_SETTINGS_MODULE=config.settings.prod python manage.py collectstatic --noinput --clear
    source_dir: backend
    output_dir: staticfiles
    envs:
      - key: DJANGO_SETTINGS_MODULE
        value: config.settings.prod
        scope: BUILD_TIME
      - key: STATIC_ROOT
        value: /app/staticfiles
        scope: BUILD_TIME
      - key: DATABASE_URL
        value: EV[1:4M7RY7uDcyYqa1mz1FgYdbb2jQyq+SD4:lAHqDaLVw1kVaytnVH1sFiuhNkhrS7d2d5sLKm6V61KGud4PeT7OV6JAJxkeOM1I+dbIJu28pJxfB8hnZ9nuDKLW8eDM0fLcJ6EdNQnM2RnrSEd+QbCsrc6mnqNFtUEABsPEM7CynxIvKKC/3j+/JRhB2oNeHS+xBUrOqmjsJkKeiDrxi6HzE9mFxRJKx2WeH72O588=]
        scope: BUILD_TIME
        type: SECRET
      - key: REDIS_URL
        value: rediss://default:<EMAIL>:25061/0
        scope: BUILD_TIME

alerts:
  - rule: DEPLOYMENT_FAILED
  - rule: DOMAIN_FAILED

ingress:
  rules:
    - match:
        path:
          prefix: /api
      component:
        name: goali-backend
        preserve_path_prefix: true
    - match:
        path:
          prefix: /health
      component:
        name: goali-backend
        preserve_path_prefix: true
    - match:
        path:
          prefix: /admin
      component:
        name: goali-backend
        preserve_path_prefix: true
    - match:
        path:
          prefix: /ws
      component:
        name: goali-backend
        preserve_path_prefix: true
    - match:
        path:
          prefix: /static
      component:
        name: static-files
    - match:
        path:
          prefix: /
      component:
        name: goali-frontend

features:
  - buildpack-stack=ubuntu-22
