# How to Add Static-Files Component - Step by Step Guide

## Current Situation ✅
- **Django service**: Working perfectly, collecting 191 static files during build
- **Static files location**: `/workspace/backend/staticfiles` (confirmed via build logs)
- **Problem**: No component serves these files at `/static` route

## Solution: Add Static-Files Component via Digital Ocean Dashboard

### Step 1: Access Digital Ocean Dashboard
1. Go to https://cloud.digitalocean.com/apps
2. Click on your app: **monkfish-app**
3. Click the **"Components"** tab

### Step 2: Add Static Site Component
1. Click **"Add Component"** button
2. Select **"Static Site"** from the dropdown
3. Configure the component:

```
Component Type: Static Site
Name: static-files
Source: GitHub
Repository: elgui/goali
Branch: gguine/prod-slow
Source Directory: backend
Build Command: DJANGO_SETTINGS_MODULE=config.settings.prod python manage.py collectstatic --noinput --clear
Output Directory: staticfiles
```

### Step 3: Configure Routes
**CRITICAL**: In the Routes section, add:
```
Route: /static
```

### Step 4: Save and Deploy
1. Click **"Create Component"**
2. Wait for deployment to complete (should take 2-3 minutes)

## Expected Result
After deployment, the app configuration should show:
- **goali-backend** (Django service)
- **goali-frontend** (Frontend static site)  
- **static-files** (Django static files) ← NEW

And the ingress rules should include:
```
/static → static-files component
```

## Verification Steps
1. Navigate to: https://monkfish-app-jvgae.ondigitalocean.app/admin
2. Check browser network tab - static files should return 200 (not 404)
3. Admin interface should have proper CSS styling

## Troubleshooting
If the component doesn't appear after creation:
1. Check the Components tab in Digital Ocean dashboard
2. Look for any error messages during deployment
3. Verify the build command uses the correct Django settings
4. Ensure the output directory matches where Django collectstatic puts files

## Why This Approach Works
1. **Separation of concerns**: Django handles dynamic content, static component handles static files
2. **Build isolation**: Static component can run collectstatic without interfering with Django service
3. **Performance**: Static files served directly by Digital Ocean's CDN
4. **Cost effective**: Static sites are free when paired with a service

## Alternative: CLI Method (if dashboard doesn't work)
If the dashboard approach fails, we can modify the `do.yaml` file directly to add the static site component, but the dashboard method is recommended first.
