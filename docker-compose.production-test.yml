version: '3.8'

services:
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: goali_prod_test
      POSTGRES_USER: goali_user
      POSTGRES_PASSWORD: secure_prod_password_123
    volumes:
      - postgres_prod_test_data:/var/lib/postgresql/data
    ports:
      - "5433:5432"  # Different port to avoid conflicts
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U goali_user -d goali_prod_test"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    ports:
      - "6380:6379"  # Different port to avoid conflicts
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  web:
    build: 
      context: ./backend
      dockerfile: Dockerfile
    environment:
      # Production-like settings
      - DEBUG=False
      - SECRET_KEY=production-test-secret-key-change-in-real-production
      - DATABASE_URL=********************************************************/goali_prod_test
      - REDIS_URL=redis://redis:6379/0
      - ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0
      - CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
      - DJANGO_SETTINGS_MODULE=settings
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      # Security settings
      - SECURE_SSL_REDIRECT=False  # Set to True in real production with HTTPS
      - SECURE_HSTS_SECONDS=31536000
      - SECURE_HSTS_INCLUDE_SUBDOMAINS=True
      - SECURE_HSTS_PRELOAD=True
      - SECURE_CONTENT_TYPE_NOSNIFF=True
      - SECURE_BROWSER_XSS_FILTER=True
      - X_FRAME_OPTIONS=DENY
      - SECURE_REFERRER_POLICY=strict-origin-when-cross-origin
      # Session security
      - SESSION_COOKIE_SECURE=False  # Set to True in real production with HTTPS
      - SESSION_COOKIE_HTTPONLY=True
      - SESSION_COOKIE_SAMESITE=Lax
      - CSRF_COOKIE_SECURE=False  # Set to True in real production with HTTPS
      - CSRF_COOKIE_HTTPONLY=True
      - CSRF_COOKIE_SAMESITE=Lax
    ports:
      - "8001:8000"  # Different port to avoid conflicts
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./backend:/usr/src/app
      - static_prod_test_volume:/usr/src/app/staticfiles
      - media_prod_test_volume:/usr/src/app/media
    command: >
      sh -c "
        echo 'Waiting for database...' &&
        python manage.py migrate &&
        echo 'Creating superuser if not exists...' &&
        python manage.py shell -c \"
        from django.contrib.auth import get_user_model;
        User = get_user_model();
        if not User.objects.filter(username='admin').exists():
            User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
        \" &&
        echo 'Collecting static files...' &&
        python manage.py collectstatic --noinput &&
        echo 'Starting production-test server...' &&
        gunicorn --bind 0.0.0.0:8000 --workers 3 --timeout 120 --access-logfile - --error-logfile - backend.wsgi:application
      "
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/health/"]
      interval: 30s
      timeout: 10s
      retries: 3

  celery:
    build: 
      context: ./backend
      dockerfile: Dockerfile
    environment:
      - DEBUG=False
      - SECRET_KEY=production-test-secret-key-change-in-real-production
      - DATABASE_URL=********************************************************/goali_prod_test
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - DJANGO_SETTINGS_MODULE=settings
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./backend:/usr/src/app
    command: celery -A backend worker --loglevel=info --concurrency=2
    healthcheck:
      test: ["CMD", "celery", "-A", "backend", "inspect", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  nginx:
    image: nginx:alpine
    ports:
      - "3001:80"  # Different port to avoid conflicts
    volumes:
      - ./nginx/production-test.conf:/etc/nginx/conf.d/default.conf
      - static_prod_test_volume:/usr/share/nginx/html/static
      - media_prod_test_volume:/usr/share/nginx/html/media
      - ./frontend:/usr/share/nginx/html/frontend
    depends_on:
      - web
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_prod_test_data:
  static_prod_test_volume:
  media_prod_test_volume:

networks:
  default:
    name: goali_prod_test_network
