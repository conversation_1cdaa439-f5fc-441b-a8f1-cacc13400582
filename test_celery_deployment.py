#!/usr/bin/env python3
"""
Test script to verify Celery worker deployment and functionality.
This script can be run locally to test the Celery configuration before deployment.
"""

import os
import sys
import django
import time
import json

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')
django.setup()

from apps.main.tasks.test_tasks import (
    test_worker_health,
    test_api_call,
    test_ml_simulation,
    test_combined_workload
)


def test_celery_configuration():
    """Test basic Celery configuration"""
    print("🔧 Testing Celery Configuration...")
    
    try:
        from celery import current_app
        print(f"✅ Celery app name: {current_app.main}")
        print(f"✅ Broker URL: {current_app.conf.broker_url}")
        print(f"✅ Result backend: {current_app.conf.result_backend}")
        return True
    except Exception as e:
        print(f"❌ Celery configuration error: {e}")
        return False


def test_task_submission():
    """Test task submission without waiting for results"""
    print("\n📤 Testing Task Submission...")
    
    tests = [
        ("Health Check", test_worker_health),
        ("API Call", lambda: test_api_call("https://httpbin.org/json")),
        ("ML Simulation", lambda: test_ml_simulation("simple")),
        ("Combined Workload", lambda: test_combined_workload("https://httpbin.org/delay/1"))
    ]
    
    submitted_tasks = []
    
    for test_name, task_func in tests:
        try:
            result = task_func()
            print(f"✅ {test_name} task submitted: {result.id}")
            submitted_tasks.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} task submission failed: {e}")
    
    return submitted_tasks


def test_task_execution(submitted_tasks, timeout=30):
    """Test task execution by waiting for results"""
    print(f"\n⏳ Testing Task Execution (timeout: {timeout}s)...")
    
    for test_name, result in submitted_tasks:
        try:
            print(f"Waiting for {test_name}...")
            task_result = result.get(timeout=timeout)
            print(f"✅ {test_name} completed successfully")
            print(f"   Result: {json.dumps(task_result, indent=2)[:200]}...")
        except Exception as e:
            print(f"❌ {test_name} failed or timed out: {e}")
            print(f"   Task state: {result.state}")


def test_worker_availability():
    """Test if Celery workers are available"""
    print("\n👷 Testing Worker Availability...")
    
    try:
        from celery import current_app
        inspect = current_app.control.inspect()
        
        # Check active workers
        active_workers = inspect.active()
        if active_workers:
            print(f"✅ Active workers found: {list(active_workers.keys())}")
            return True
        else:
            print("❌ No active workers found")
            return False
            
    except Exception as e:
        print(f"❌ Worker inspection failed: {e}")
        return False


def main():
    """Main test function"""
    print("🚀 Starting Celery Deployment Test")
    print("=" * 50)
    
    # Test 1: Configuration
    config_ok = test_celery_configuration()
    
    # Test 2: Worker availability
    workers_ok = test_worker_availability()
    
    # Test 3: Task submission
    submitted_tasks = test_task_submission()
    
    # Test 4: Task execution (only if workers are available)
    if workers_ok and submitted_tasks:
        test_task_execution(submitted_tasks)
    elif not workers_ok:
        print("\n⚠️  Skipping task execution test - no workers available")
        print("   This is expected if you haven't started Celery workers yet")
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 Test Summary:")
    print(f"   Configuration: {'✅ OK' if config_ok else '❌ FAILED'}")
    print(f"   Workers: {'✅ Available' if workers_ok else '❌ Not Available'}")
    print(f"   Task Submission: {'✅ OK' if submitted_tasks else '❌ FAILED'}")
    
    if config_ok and submitted_tasks:
        print("\n🎉 Celery configuration is ready for deployment!")
        if not workers_ok:
            print("💡 Start workers with: celery -A config worker --loglevel=info")
    else:
        print("\n🔧 Please fix the configuration issues before deployment")
    
    return config_ok and bool(submitted_tasks)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
