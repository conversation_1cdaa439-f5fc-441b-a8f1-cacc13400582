#!/usr/bin/env python3
"""
Test script to verify DigitalOcean deployment with Celery worker
"""
import requests
import json
import time
import sys

BASE_URL = "https://monkfish-app-jvgae.ondigitalocean.app"

def test_health_endpoint():
    """Test basic health endpoint"""
    print("🔍 Testing basic health endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/api/health/", timeout=10)
        if response.status_code == 200:
            print("✅ Basic health endpoint working")
            return True
        else:
            print(f"❌ Health endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health endpoint error: {e}")
        return False

def test_celery_health():
    """Test Celery health endpoint"""
    print("🔍 Testing Celery health endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/api/celery/health/", timeout=10)
        data = response.json()
        print(f"📊 Celery health response: {json.dumps(data, indent=2)}")
        
        if data.get('status') == 'healthy':
            print("✅ Celery health check passed")
            return True
        else:
            print(f"❌ Celery health check failed: {data.get('error', 'Unknown error')}")
            return False
    except Exception as e:
        print(f"❌ Celery health error: {e}")
        return False

def test_celery_task():
    """Test Celery task execution"""
    print("🔍 Testing Celery task execution...")
    try:
        # Test simple health task
        response = requests.post(
            f"{BASE_URL}/api/celery/test/",
            json={"task_type": "health", "wait": True, "timeout": 30},
            timeout=35
        )
        data = response.json()
        print(f"📊 Task response: {json.dumps(data, indent=2)}")
        
        if data.get('status') == 'success':
            print("✅ Celery task execution successful")
            return True
        else:
            print(f"❌ Celery task failed: {data.get('error', 'Unknown error')}")
            return False
    except Exception as e:
        print(f"❌ Celery task error: {e}")
        return False

def test_celery_stats():
    """Test Celery stats endpoint"""
    print("🔍 Testing Celery stats endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/api/celery/stats/", timeout=10)
        data = response.json()
        print(f"📊 Celery stats: {json.dumps(data, indent=2)}")
        
        if 'workers' in data:
            print("✅ Celery stats available")
            return True
        else:
            print("❌ Celery stats incomplete")
            return False
    except Exception as e:
        print(f"❌ Celery stats error: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting deployment verification tests...")
    print(f"🌐 Testing deployment at: {BASE_URL}")
    print("=" * 60)
    
    tests = [
        ("Basic Health", test_health_endpoint),
        ("Celery Health", test_celery_health),
        ("Celery Task", test_celery_task),
        ("Celery Stats", test_celery_stats),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} test...")
        result = test_func()
        results.append((test_name, result))
        time.sleep(2)  # Brief pause between tests
    
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY:")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:20} {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 All tests passed! Deployment is working correctly.")
        sys.exit(0)
    else:
        print("⚠️  Some tests failed. Check the logs above for details.")
        sys.exit(1)

if __name__ == "__main__":
    main()
