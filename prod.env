# Production Environment Variables for DigitalOcean App Platform
# Generated from do.yaml configuration

# =============================================================================
# GOALI-BACKEND SERVICE ENVIRONMENT VARIABLES
# =============================================================================

# Django Configuration
DJANGO_SETTINGS_MODULE=config.settings.prod
DJANGO_ALLOWED_HOSTS=127.0.0.1,localhost,monkfish-app-jvgae.ondigitalocean.app

# Static Files
STATIC_ROOT=/app/staticfiles

# LLM Configuration
DEFAULT_LLM_MODEL_NAME=mistral-small-latest
DEFAULT_LLM_TEMPERATURE=0.7

# Secrets (values need to be set in DO UI as secrets)
DJANGO_SECRET_KEY=<SECRET_VALUE>
MISTRAL_API_KEY=<SECRET_VALUE>
DJANGO_ADMIN_PASSWORD=<SECRET_VALUE>
DATABASE_URL=<SECRET_VALUE>
REDIS_URL=<SECRET_VALUE>

# =============================================================================
# WORKER (CELERY) SERVICE ENVIRONMENT VARIABLES
# =============================================================================

# Django Configuration
DJANGO_SETTINGS_MODULE=config.settings.prod
DJANGO_ALLOWED_HOSTS=127.0.0.1,localhost,monkfish-app-jvgae.ondigitalocean.app

# LLM Configuration
DEFAULT_LLM_MODEL_NAME=mistral-small-latest
DEFAULT_LLM_TEMPERATURE=0.7

# Redis Configuration (hardcoded in do.yaml for worker)
REDIS_URL=rediss://default:<EMAIL>:25061/0

# Secrets (values need to be set in DO UI as secrets)
DJANGO_SECRET_KEY=<SECRET_VALUE>
MISTRAL_API_KEY=<SECRET_VALUE>
DATABASE_URL=<SECRET_VALUE>

# =============================================================================
# GOALI-FRONTEND STATIC SITE BUILD ENVIRONMENT VARIABLES
# =============================================================================

# Vite Security Configuration
VITE_SECURITY_REQUIRE_AUTH=true
VITE_SECURITY_TOKEN_VALIDATION=true
VITE_SECURITY_SESSION_TIMEOUT=1800000

# =============================================================================
# SCOPE REFERENCE
# =============================================================================
# RUN_AND_BUILD_TIME: Available during both build and runtime
# RUN_TIME: Available only during runtime
# BUILD_TIME: Available only during build

# =============================================================================
# CRITICAL VARIABLES FOR LLM ISSUE
# =============================================================================
# These are the variables causing the current issue:
# - DEFAULT_LLM_MODEL_NAME (should be: mistral-small-latest)
# - DEFAULT_LLM_TEMPERATURE (should be: 0.7)
# 
# Both need to be set for:
# 1. goali-backend service
# 2. worker service
# 
# Current issue: These are being read as string "None" instead of actual values
