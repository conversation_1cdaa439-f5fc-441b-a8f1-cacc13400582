# Technical Architecture: Lean Agent Benchmarking Extension

## 🏗️ **Architectural Overview**

This document outlines the technical architecture for extending the Goali benchmarking system following **enterprise software engineering patterns** and **Django framework best practices**. The design adheres to **SOLID principles**, **<PERSON>'s Enterprise Application Architecture patterns**, and **Domain-Driven Design (DDD)** principles.

## **Architecture Diagram**

```
┌─────────────────────────────────────────────────────────────┐
│                    Lean Benchmark Extension                  │
├─────────────────────────────────────────────────────────────┤
│  API Layer (Django Views)                                   │
│  ├─── QuickBenchmarkAPI ──────────────────────────────────┐ │
│  └─── BenchmarkOptionsAPI ────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  Service Layer (Business Logic)                             │
│  ├─── QuickBenchmarkService (Facade Pattern) ──────────────┐ │
│  ├─── BenchmarkProfileFactory (Factory Pattern) ───────────┤ │
│  └─── SimpleEvaluationAdapter (Adapter Pattern) ───────────┘ │
├─────────────────────────────────────────────────────────────┤
│  Existing Goali Infrastructure (90% Reuse)                  │
│  ├─── AgentBenchmarker ─────────────────────────────────────┐ │
│  ├─── SemanticEvaluator ────────────────────────────────────┤ │
│  ├─── BenchmarkService ─────────────────────────────────────┤ │
│  └─── UserProfile Model (is_real=False) ───────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  Data Layer (Django ORM)                                    │
│  ├─── UserProfile (existing) ──────────────────────────────┐ │
│  ├─── BenchmarkRun (existing) ─────────────────────────────┤ │
│  ├─── BenchmarkScenario (existing) ────────────────────────┤ │
│  └─── GenericAgent (existing) ─────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## **Design Patterns Applied**

### **1. Facade Pattern (GoF Structural Pattern)**
**Reference**: Gang of Four Design Patterns, Martin Fowler's Enterprise Application Architecture

```python
class QuickBenchmarkService:
    """
    Facade Pattern Implementation
    Provides simplified interface to complex benchmarking subsystem
    
    Benefits:
    - Reduces coupling between client and subsystem
    - Simplifies complex operations into single method calls
    - Encapsulates business logic orchestration
    """
    
    def __init__(self):
        # Dependency injection following IoC principle
        self.profile_factory = BenchmarkProfileFactory()
        self.evaluation_adapter = SimpleEvaluationAdapter()
        self.agent_benchmarker = AgentBenchmarker()  # Existing service
    
    def run_quick_benchmark(self, agent_name: str, profile_template: str, 
                          evaluation_template: str) -> BenchmarkRun:
        """
        Orchestrates complex benchmark workflow through simple interface
        Implements Transaction Script pattern for this specific use case
        """
        # Implementation details in IMPLEMENTATION_GUIDE.md
```

**Architectural Benefits**:
- **Loose Coupling**: Client code doesn't depend on internal complexity
- **Simplified Interface**: Single method encapsulates multi-step process
- **Maintainability**: Changes to internal structure don't affect clients

### **2. Factory Pattern (GoF Creational Pattern)**
**Reference**: Effective Java (Joshua Bloch), Clean Code (Robert Martin)

```python
class BenchmarkProfileFactory:
    """
    Factory Pattern Implementation
    Encapsulates complex UserProfile creation logic
    
    Follows:
    - Static Factory Method pattern
    - Template Method pattern for profile creation steps
    - Builder pattern for complex object construction
    """
    
    @classmethod
    @transaction.atomic  # Django transaction management
    def create_benchmark_profile(cls, template_name: str) -> UserProfile:
        """
        Static factory method following Joshua Bloch's recommendations
        
        Benefits:
        - Named constructors for clarity
        - Encapsulates complex creation logic
        - Enables object caching and reuse
        - Validation and error handling centralization
        """
        # Implementation follows Django best practices
```

**Design Rationale**:
- **Single Responsibility**: Factory only handles profile creation
- **Open/Closed Principle**: Easy to add new profile templates
- **Dependency Inversion**: Clients depend on factory interface, not implementation

### **3. Adapter Pattern (GoF Structural Pattern)**
**Reference**: Design Patterns (GoF), Architecture Patterns (Buschmann)

```python
class SimpleEvaluationAdapter:
    """
    Adapter Pattern Implementation
    Bridges simple natural language prompts to existing SemanticEvaluator
    
    Classic Adapter Pattern:
    - Target: SemanticEvaluator interface
    - Adaptee: Simple natural language prompts
    - Adapter: This class
    """
    
    def __init__(self, semantic_evaluator: SemanticEvaluator):
        self.semantic_evaluator = semantic_evaluator  # Composition over inheritance
    
    def evaluate_with_simple_prompt(self, response: str, template: str, 
                                   context: Dict, run: BenchmarkRun) -> Dict:
        """
        Adapts simple template to existing evaluation criteria format
        Implements Object Adapter pattern using composition
        """
        criteria = self._convert_to_criteria_format(template)
        return self.semantic_evaluator.evaluate(response, criteria, context, run)
```

## **Service Layer Architecture**

### **Layered Architecture Pattern**
**Reference**: Martin Fowler's Enterprise Application Architecture, Domain-Driven Design (Eric Evans)

Following **Hexagonal Architecture** principles and **Django service layer best practices**:

```python
# apps/main/services/
├── benchmark_profile_factory.py    # Domain Service
├── simple_evaluation_adapter.py    # Infrastructure Adapter  
├── quick_benchmark_service.py      # Application Service (Facade)
└── __init__.py
```

### **Service Layer Principles**

#### **1. Application Services (Orchestration)**
```python
class QuickBenchmarkService:
    """
    Application Service following DDD principles
    
    Responsibilities:
    - Orchestrate domain operations
    - Handle transaction boundaries
    - Coordinate between domain services
    - Manage cross-cutting concerns (logging, monitoring)
    """
    
    @transaction.atomic  # Transaction boundary management
    def run_quick_benchmark(self, ...):
        # Orchestration logic without business rules
        profile = self.profile_factory.get_or_create_benchmark_profile(...)
        scenario = self._create_scenario(...)
        run = self.agent_benchmarker.run_benchmark(...)
        evaluation = self.evaluation_adapter.evaluate(...)
        return self._finalize_benchmark(run, evaluation)
```

#### **2. Domain Services (Business Logic)**
```python
class BenchmarkProfileFactory:
    """
    Domain Service following DDD principles
    
    Responsibilities:
    - Encapsulate domain knowledge
    - Maintain business invariants
    - Provide domain-specific operations
    """
    
    PROFILE_TEMPLATES = {
        # Domain knowledge: psychology-based user profiles
        'anxious_new_user': {
            'personality_traits': {
                'neuroticism': 0.8,  # Based on Big Five model
                'openness': 0.4,
                # ... following psychological research
            }
        }
    }
```

## **Data Architecture Integration**

### **Repository Pattern Integration**
**Reference**: Domain-Driven Design (Eric Evans), Django ORM best practices

```python
# Leveraging Django ORM as Repository implementation
class QuickBenchmarkService:
    def _get_agent(self, agent_name: str) -> GenericAgent:
        """
        Repository pattern through Django ORM
        Following specification pattern for complex queries
        """
        try:
            return GenericAgent.objects.select_related('llm_config').get(
                name=agent_name,
                is_active=True  # Business rule enforcement
            )
        except GenericAgent.DoesNotExist:
            # Domain exception handling
            raise DomainValidationError(f"Agent '{agent_name}' not found")
```

### **Domain Model Reuse**
**Reference**: Domain-Driven Design, Django Model best practices

```python
# Existing UserProfile model with is_real field
class UserProfile(models.Model):
    profile_name = models.CharField(max_length=255)
    is_real = models.BooleanField(default=True)  # Key architectural insight
    
    class Meta:
        indexes = [
            models.Index(fields=['is_real', 'profile_name']),  # Query optimization
        ]
    
    def __str__(self):
        prefix = "Real" if self.is_real else "Benchmark"
        return f"{prefix}: {self.profile_name}"
```

## **API Architecture**

### **RESTful API Design**
**Reference**: RESTful Web Services (Richardson & Ruby), Django REST Framework best practices

```python
# REST Resource Design
POST /api/quick-benchmark/           # Create benchmark run
GET  /api/quick-benchmark/options/   # Get available options
GET  /api/benchmark-runs/{id}/       # Get specific run (existing endpoint)
```

### **Request/Response Schema**
**Reference**: JSON API specification, OpenAPI 3.0

```python
# Request Schema (JSON Schema validation)
QUICK_BENCHMARK_REQUEST = {
    "type": "object",
    "required": ["agent_name", "profile_template", "evaluation_template"],
    "properties": {
        "agent_name": {"type": "string", "enum": ["mentor", "strategy", "ethical"]},
        "profile_template": {"type": "string", "enum": ["anxious_new_user", ...]},
        "evaluation_template": {"type": "string", "enum": ["mentor_helpfulness", ...]},
        "scenario_context": {
            "type": "object",
            "properties": {
                "user_input": {"type": "string"},
                "workflow_type": {"type": "string"}
            }
        }
    }
}

# Response Schema
QUICK_BENCHMARK_RESPONSE = {
    "type": "object",
    "properties": {
        "success": {"type": "boolean"},
        "benchmark_run_id": {"type": "string", "format": "uuid"},
        "admin_url": {"type": "string", "format": "uri"},
        "results_summary": {
            "type": "object",
            "properties": {
                "execution_time": {"type": "number"},
                "token_usage": {"type": "integer"},
                "cost_estimate": {"type": "number"}
            }
        }
    }
}
```

## **Security Architecture**

### **Authentication & Authorization**
**Reference**: Django Security Documentation, OWASP Security Guidelines

```python
from django.contrib.admin.views.decorators import staff_member_required
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator

@method_decorator([
    staff_member_required,  # Existing admin authentication
    csrf_exempt,           # For API usage - implement proper CSRF token handling
], name='dispatch')
class QuickBenchmarkAPIView(View):
    """
    Security Implementation:
    - Reuses existing Django admin authentication
    - Staff-level permissions required
    - CSRF protection (configure for API usage)
    - Input validation through JSON schema
    """
```

### **Input Validation**
**Reference**: OWASP Input Validation Guidelines, Django Security Best Practices

```python
import json
from django.core.exceptions import ValidationError
from jsonschema import validate, ValidationError as JSONValidationError

def validate_quick_benchmark_request(request_data: dict) -> dict:
    """
    Multi-layer input validation following OWASP guidelines
    
    1. JSON Schema validation (structure)
    2. Business rule validation (domain)
    3. Security validation (injection prevention)
    """
    try:
        # Schema validation
        validate(request_data, QUICK_BENCHMARK_REQUEST)
        
        # Business validation
        _validate_agent_exists(request_data['agent_name'])
        _validate_template_exists(request_data['profile_template'])
        
        # Security validation
        _sanitize_user_input(request_data.get('scenario_context', {}))
        
        return request_data
        
    except JSONValidationError as e:
        raise ValidationError(f"Invalid request format: {e.message}")
```

## **Error Handling Architecture**

### **Exception Hierarchy**
**Reference**: Effective Java (Joshua Bloch), Python Exception Handling Best Practices

```python
# Custom exception hierarchy
class BenchmarkDomainError(Exception):
    """Base exception for benchmark domain errors"""
    pass

class BenchmarkValidationError(BenchmarkDomainError):
    """Validation errors in benchmark operations"""
    pass

class BenchmarkExecutionError(BenchmarkDomainError):
    """Runtime errors during benchmark execution"""
    pass

class BenchmarkConfigurationError(BenchmarkDomainError):
    """Configuration or setup errors"""
    pass
```

### **Error Response Format**
**Reference**: Problem Details for HTTP APIs (RFC 7807), JSON API Error Objects

```python
# Standardized error response format
ERROR_RESPONSE_FORMAT = {
    "type": "object",
    "properties": {
        "error": {
            "type": "object",
            "properties": {
                "type": {"type": "string"},        # Error category
                "title": {"type": "string"},       # Human-readable summary
                "detail": {"type": "string"},      # Specific error description
                "instance": {"type": "string"},    # Request identifier
                "status": {"type": "integer"}      # HTTP status code
            }
        }
    }
}
```

## **Performance Architecture**

### **Caching Strategy**
**Reference**: Django Caching Framework, High Performance Django

```python
from django.core.cache import cache
from django.views.decorators.cache import cache_page

class BenchmarkProfileFactory:
    @classmethod
    def get_or_create_benchmark_profile(cls, template_name: str) -> UserProfile:
        """
        Implements caching for frequently accessed benchmark profiles
        Cache key strategy: benchmark_profile:{template_name}
        TTL: 1 hour (profiles are relatively static)
        """
        cache_key = f"benchmark_profile:{template_name}"
        profile = cache.get(cache_key)
        
        if profile is None:
            profile = cls._get_or_create_profile(template_name)
            cache.set(cache_key, profile, 3600)  # 1 hour TTL
            
        return profile
```

### **Database Optimization**
**Reference**: Django ORM Performance, High Performance Django

```python
# Query optimization strategies
class QuickBenchmarkService:
    def _get_agent_with_config(self, agent_name: str) -> GenericAgent:
        """
        Optimized query using select_related to prevent N+1 queries
        Following Django ORM best practices
        """
        return GenericAgent.objects.select_related(
            'llm_config',           # Prevent additional query for LLM config
            'evaluation_criteria'   # Prevent additional query for criteria
        ).prefetch_related(
            'tools'                 # Efficient loading of related tools
        ).get(name=agent_name)
```

## **Monitoring & Observability**

### **Logging Architecture**
**Reference**: Python Logging Best Practices, Django Logging Configuration

```python
import logging
import structlog

# Structured logging configuration
logger = structlog.get_logger(__name__)

class QuickBenchmarkService:
    def run_quick_benchmark(self, agent_name: str, profile_template: str, 
                          evaluation_template: str) -> BenchmarkRun:
        """
        Comprehensive logging for observability
        Following structured logging best practices
        """
        logger.info(
            "quick_benchmark_started",
            agent_name=agent_name,
            profile_template=profile_template,
            evaluation_template=evaluation_template,
            request_id=self._get_request_id()
        )
        
        try:
            result = self._execute_benchmark(...)
            
            logger.info(
                "quick_benchmark_completed",
                benchmark_run_id=str(result.id),
                execution_time=result.execution_time,
                token_usage=result.total_tokens,
                cost_estimate=result.cost_estimate
            )
            
            return result
            
        except Exception as e:
            logger.error(
                "quick_benchmark_failed",
                error_type=type(e).__name__,
                error_message=str(e),
                agent_name=agent_name,
                profile_template=profile_template
            )
            raise
```

### **Metrics Collection**
**Reference**: Django Prometheus Integration, Application Performance Monitoring

```python
from prometheus_client import Counter, Histogram, Gauge

# Prometheus metrics
BENCHMARK_REQUESTS = Counter(
    'quick_benchmark_requests_total',
    'Total quick benchmark requests',
    ['agent_name', 'profile_template', 'status']
)

BENCHMARK_DURATION = Histogram(
    'quick_benchmark_duration_seconds',
    'Quick benchmark execution time',
    ['agent_name', 'profile_template']
)

class QuickBenchmarkService:
    def run_quick_benchmark(self, ...):
        start_time = time.time()
        
        try:
            result = self._execute_benchmark(...)
            BENCHMARK_REQUESTS.labels(
                agent_name=agent_name,
                profile_template=profile_template,
                status='success'
            ).inc()
            
        except Exception as e:
            BENCHMARK_REQUESTS.labels(
                agent_name=agent_name,
                profile_template=profile_template,
                status='error'
            ).inc()
            raise
            
        finally:
            duration = time.time() - start_time
            BENCHMARK_DURATION.labels(
                agent_name=agent_name,
                profile_template=profile_template
            ).observe(duration)
```

## **Testing Architecture**

### **Testing Strategy**
**Reference**: Growing Object-Oriented Software, Guided by Tests (Freeman & Pryce)

```python
# Test pyramid implementation
├── Unit Tests (70%)
│   ├── Service Layer Tests
│   ├── Factory Tests  
│   └── Adapter Tests
├── Integration Tests (20%)
│   ├── API Integration Tests
│   ├── Database Integration Tests
│   └── Service Integration Tests
└── End-to-End Tests (10%)
    ├── Full Benchmark Workflow Tests
    └── Admin Interface Integration Tests
```

### **Test Doubles Strategy**
**Reference**: xUnit Test Patterns (Gerard Meszaros), Django Testing Best Practices

```python
from unittest.mock import Mock, patch
import pytest

class TestQuickBenchmarkService:
    """
    Unit tests using Test Doubles for isolation
    Following Arrange-Act-Assert pattern
    """
    
    @pytest.fixture
    def mock_dependencies(self):
        """
        Dependency injection for testing
        Using Mock objects for external dependencies
        """
        return {
            'profile_factory': Mock(spec=BenchmarkProfileFactory),
            'evaluation_adapter': Mock(spec=SimpleEvaluationAdapter),
            'agent_benchmarker': Mock(spec=AgentBenchmarker)
        }
    
    def test_run_quick_benchmark_success(self, mock_dependencies):
        """
        Test successful benchmark execution path
        Using Given-When-Then BDD structure
        """
        # Given (Arrange)
        service = QuickBenchmarkService(**mock_dependencies)
        mock_dependencies['agent_benchmarker'].run_benchmark.return_value = Mock(
            spec=BenchmarkRun,
            id='test-benchmark-id',
            agent_output='test response'
        )
        
        # When (Act)
        result = service.run_quick_benchmark(
            agent_name='mentor',
            profile_template='anxious_new_user',
            evaluation_template='mentor_helpfulness'
        )
        
        # Then (Assert)
        assert result.id == 'test-benchmark-id'
        mock_dependencies['profile_factory'].get_or_create_benchmark_profile.assert_called_once()
        mock_dependencies['agent_benchmarker'].run_benchmark.assert_called_once()
```

## **Compliance & Standards**

### **Code Quality Standards**
- **PEP 8**: Python Style Guide compliance
- **PEP 257**: Docstring conventions
- **Django Coding Style**: Framework-specific best practices
- **Type Hints**: PEP 484 type annotations for better IDE support

### **Security Standards**
- **OWASP Top 10**: Web application security risks mitigation
- **Django Security Checklist**: Framework security best practices
- **Input Validation**: Comprehensive sanitization and validation
- **Authentication**: Proper session and permission management

### **Documentation Standards**
- **Technical Documentation**: Architecture Decision Records (ADRs)
- **API Documentation**: OpenAPI 3.0 specification
- **Code Documentation**: Comprehensive docstrings and comments
- **User Documentation**: Clear usage examples and guides

This architecture follows industry-standard patterns and practices, ensuring maintainability, scalability, and professional code quality while maximally leveraging existing infrastructure.
