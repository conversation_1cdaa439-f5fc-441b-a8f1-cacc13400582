# Phase 2: Evaluation Context System (REVISED)
**Timeline**: 2-3 days

## Step 2.1: Create Evaluation Context Model (Day 1)

### Add to `backend/apps/main/models.py`:

```python
class EvaluationContext(models.Model):
    """
    Evaluation context that combines workflow state with variable overrides
    Works with existing UserProfile data but can override specific values for evaluation
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    name = models.CharField(max_length=100, help_text="e.g., 'Stressed_Evening_WheelGen_Mentor'")
    description = models.TextField(help_text="Description of this evaluation context scenario")
    
    # Current workflow context (integrates with your existing workflows)
    current_workflow_type = models.CharField(
        max_length=50, 
        choices=[
            ('wheel_generation', 'Wheel Generation'),
            ('activity_feedback', 'Activity Feedback'),
            ('discussion', 'Discussion'),
            ('onboarding', 'Onboarding'),
            ('post_spin', 'Post Spin'),
            ('pre_spin_feedback', 'Pre Spin Feedback'),
        ],
        help_text="Current workflow being executed"
    )
    workflow_stage = models.Char<PERSON>ield(
        max_length=50,
        choices=[
            ('initiation', 'Workflow Initiation'),
            ('agent_processing', 'Multi-Agent Processing'),
            ('result_delivery', 'Result Delivery'),
            ('user_response', 'Awaiting User Response'),
            ('error_recovery', 'Error Recovery'),
        ],
        default='agent_processing',
        help_text="Current stage in workflow"
    )
    agent_role_being_evaluated = models.CharField(
        max_length=50,
        choices=[
            ('mentor', 'Mentor Agent'),
            ('orchestrator', 'Orchestrator Agent'),
            ('resource', 'Resource Agent'),
            ('engagement', 'Engagement Agent'),
            ('psychological', 'Psychological Agent'),
            ('strategy', 'Strategy Agent'),
            ('wheel_activity', 'Wheel/Activity Agent'),
            ('ethical', 'Ethical Agent'),
        ],
        help_text="Which agent is being evaluated"
    )
    
    # Context variable overrides (null = use UserProfile values)
    # These override the UserProfile's actual values for evaluation purposes
    trust_level_override = models.IntegerField(
        null=True, blank=True,
        validators=[models.validators.MinValueValidator(0), models.validators.MaxValueValidator(100)],
        help_text="Override trust level for evaluation (null = use UserProfile value)"
    )
    mood_valence_override = models.FloatField(
        null=True, blank=True,
        validators=[models.validators.MinValueValidator(-1.0), models.validators.MaxValueValidator(1.0)],
        help_text="Override mood valence for evaluation (null = use UserProfile value)"
    )
    mood_arousal_override = models.FloatField(
        null=True, blank=True,
        validators=[models.validators.MinValueValidator(-1.0), models.validators.MaxValueValidator(1.0)],
        help_text="Override mood arousal for evaluation (null = use UserProfile value)"
    )
    
    # Environmental context overrides
    reported_environment_override = models.CharField(
        max_length=50,
        choices=[
            ('home', 'Home'),
            ('work', 'Work'),
            ('public', 'Public Space'),
            ('transit', 'In Transit'),
        ],
        null=True, blank=True,
        help_text="Override environment for evaluation"
    )
    stress_level_override = models.IntegerField(
        null=True, blank=True,
        validators=[models.validators.MinValueValidator(0), models.validators.MaxValueValidator(100)],
        help_text="Override stress level for evaluation"
    )
    time_pressure_override = models.IntegerField(
        null=True, blank=True,
        validators=[models.validators.MinValueValidator(0), models.validators.MaxValueValidator(100)],
        help_text="Override time pressure for evaluation"
    )
    reported_time_availability_override = models.CharField(
        max_length=50,
        choices=[
            ('5_minutes', '5 minutes'),
            ('15_minutes', '15 minutes'),
            ('30_minutes', '30 minutes'),
            ('60_minutes', '60+ minutes'),
        ],
        null=True, blank=True,
        help_text="Override time availability for evaluation"
    )
    reported_focus_override = models.CharField(
        max_length=100,
        null=True, blank=True,
        help_text="Override focus area for evaluation (e.g., 'creative', 'physical')"
    )
    
    # Agent coordination context (simulated workflow state)
    previous_agent_outputs = models.JSONField(
        default=list,
        help_text="Simulated outputs from previous agents in workflow"
    )
    expected_next_agents = models.JSONField(
        default=list,
        help_text="Which agents should be called after this one"
    )
    
    # State management
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        verbose_name = "Evaluation Context"
        verbose_name_plural = "Evaluation Contexts"
    
    def __str__(self):
        return f"{self.name} ({self.current_workflow_type} - {self.agent_role_being_evaluated})"
    
    def get_effective_context(self, user_profile: 'UserProfile') -> Dict[str, Any]:
        """
        Get effective context by combining UserProfile data with overrides
        This is the key method that merges real user data with evaluation overrides
        """
        # Start with UserProfile data (you'll need to adjust field names)
        context = {
            'user_profile_id': str(user_profile.id),
            'profile_name': user_profile.profile_name,
            'is_real': user_profile.is_real,
            
            # Workflow context (always from evaluation context)
            'workflow_type': self.current_workflow_type,
            'workflow_stage': self.workflow_stage,
            'agent_role': self.agent_role_being_evaluated,
            
            # Variable context (UserProfile values with overrides)
            'trust_level': self.trust_level_override if self.trust_level_override is not None else getattr(user_profile, 'trust_level', 50),
            'mood': {
                'valence': self.mood_valence_override if self.mood_valence_override is not None else getattr(user_profile, 'mood_valence', 0.0),
                'arousal': self.mood_arousal_override if self.mood_arousal_override is not None else getattr(user_profile, 'mood_arousal', 0.0),
            },
            'environment': {
                'type': self.reported_environment_override or getattr(user_profile, 'reported_environment', 'home'),
                'stress_level': self.stress_level_override if self.stress_level_override is not None else getattr(user_profile, 'stress_level', 30),
                'time_pressure': self.time_pressure_override if self.time_pressure_override is not None else getattr(user_profile, 'time_pressure', 30),
            },
            'time_availability': self.reported_time_availability_override or getattr(user_profile, 'reported_time_availability', '30_minutes'),
            'reported_focus': self.reported_focus_override or getattr(user_profile, 'reported_focus', 'general'),
            
            # Agent coordination context
            'agent_context': {
                'previous_outputs': self.previous_agent_outputs,
                'expected_next': self.expected_next_agents
            },
            
            # Real UserProfile relationships (leverage existing data)
            'personality_traits': self._get_personality_traits(user_profile),
            'user_goals': self._get_user_goals(user_profile),
            'user_beliefs': self._get_user_beliefs(user_profile),
            'user_limitations': self._get_user_limitations(user_profile),
        }
        
        return context
    
    def _get_personality_traits(self, user_profile) -> Dict[str, float]:
        """Extract personality traits from UserProfile (adjust based on your model)"""
        traits = {}
        # Adjust this based on your UserTraitInclination model structure
        for trait in user_profile.trait_inclinations.all():
            traits[trait.trait_name] = trait.strength  # Adjust field names
        return traits
    
    def _get_user_goals(self, user_profile) -> List[str]:
        """Extract user goals from UserProfile (adjust based on your model)"""
        # Adjust this based on your UserGoal model structure
        return [goal.description for goal in user_profile.goals.all()]
    
    def _get_user_beliefs(self, user_profile) -> List[str]:
        """Extract user beliefs from UserProfile (adjust based on your model)"""
        # Adjust this based on your Belief model structure
        return [belief.description for belief in user_profile.beliefs.all()]
    
    def _get_user_limitations(self, user_profile) -> List[str]:
        """Extract user limitations from UserProfile (adjust based on your model)"""
        # Adjust this based on how limitations are stored in your model
        return getattr(user_profile, 'limitations', [])
    
    @property
    def trust_phase(self) -> str:
        """Calculate trust phase from effective trust level"""
        effective_trust = self.trust_level_override if self.trust_level_override is not None else 50
        if effective_trust < 40:
            return "foundation"
        elif effective_trust < 70:
            return "expansion"
        else:
            return "integration"
```

### Run migration:
```bash
python manage.py makemigrations main --name="add_evaluation_context"
python manage.py migrate
```

## Step 2.2: Create Evaluation Context Service (Day 1-2)

### Create `backend/apps/main/services/evaluation_context_service.py`:

```python
import logging
from typing import Dict, Any, List, Optional
from apps.main.models import EvaluationContext
from apps.user.models import UserProfile

logger = logging.getLogger(__name__)

class EvaluationContextService:
    """
    Service for managing evaluation contexts and combining them with UserProfiles
    """
    
    def create_context(self, context_data: Dict[str, Any]) -> EvaluationContext:
        """Create a new evaluation context"""
        context = EvaluationContext.objects.create(**context_data)
        logger.info(f"Created evaluation context: {context.name}")
        return context
    
    def get_combined_context(
        self, 
        context_id: str, 
        user_profile: UserProfile
    ) -> Dict[str, Any]:
        """
        Get combined context for agent evaluation
        This merges UserProfile data with EvaluationContext overrides
        """
        try:
            evaluation_context = EvaluationContext.objects.get(id=context_id, is_active=True)
            
            # This is the key method - combines real user data with evaluation context
            combined_context = evaluation_context.get_effective_context(user_profile)
            
            # Add evaluation metadata
            combined_context.update({
                'evaluation_context_id': str(evaluation_context.id),
                'evaluation_context_name': evaluation_context.name,
                'evaluation_context_description': evaluation_context.description,
                'evaluation_timestamp': evaluation_context.created_at.isoformat()
            })
            
            return combined_context
            
        except EvaluationContext.DoesNotExist:
            logger.error(f"Evaluation context {context_id} not found")
            return {}
    
    def list_contexts_for_agent(self, agent_role: str) -> List[Dict[str, Any]]:
        """List all contexts suitable for evaluating a specific agent"""
        contexts = EvaluationContext.objects.filter(
            agent_role_being_evaluated=agent_role,
            is_active=True
        ).order_by('name')
        
        return [
            {
                'id': str(ctx.id),
                'name': ctx.name,
                'description': ctx.description,
                'workflow_type': ctx.current_workflow_type,
                'workflow_stage': ctx.workflow_stage,
                'trust_level_override': ctx.trust_level_override,
                'trust_phase': ctx.trust_phase,
                'stress_level_override': ctx.stress_level_override,
                'mood_summary': self._format_mood_summary(ctx),
                'has_overrides': self._has_context_overrides(ctx)
            }
            for ctx in contexts
        ]
    
    def _format_mood_summary(self, ctx: EvaluationContext) -> str:
        """Format mood override summary"""
        if ctx.mood_valence_override is not None and ctx.mood_arousal_override is not None:
            return f"valence={ctx.mood_valence_override:.1f}, arousal={ctx.mood_arousal_override:.1f}"
        elif ctx.mood_valence_override is not None:
            return f"valence={ctx.mood_valence_override:.1f}, arousal=default"
        elif ctx.mood_arousal_override is not None:
            return f"valence=default, arousal={ctx.mood_arousal_override:.1f}"
        else:
            return "default mood"
    
    def _has_context_overrides(self, ctx: EvaluationContext) -> bool:
        """Check if context has any variable overrides"""
        override_fields = [
            ctx.trust_level_override,
            ctx.mood_valence_override, 
            ctx.mood_arousal_override,
            ctx.stress_level_override,
            ctx.time_pressure_override,
            ctx.reported_environment_override,
            ctx.reported_time_availability_override,
            ctx.reported_focus_override
        ]
        return any(field is not None for field in override_fields)
    
    def get_contexts_by_workflow(self, workflow_type: str) -> List[EvaluationContext]:
        """Get all contexts for a specific workflow type"""
        return EvaluationContext.objects.filter(
            current_workflow_type=workflow_type,
            is_active=True
        ).order_by('agent_role_being_evaluated', 'name')
    
    def create_context_for_userprofile_combination(
        self,
        base_context_id: str,
        user_profile: UserProfile,
        context_name_suffix: str = ""
    ) -> Dict[str, Any]:
        """
        Create a preview of how a context would work with a specific UserProfile
        Useful for testing context-user combinations
        """
        try:
            base_context = EvaluationContext.objects.get(id=base_context_id)
            combined_context = base_context.get_effective_context(user_profile)
            
            # Add combination metadata
            combined_context.update({
                'combination_name': f"{base_context.name} + {user_profile.profile_name}{context_name_suffix}",
                'base_context_name': base_context.name,
                'user_profile_name': user_profile.profile_name,
                'is_fake_profile': not user_profile.is_real,
                'context_overrides_applied': self._has_context_overrides(base_context)
            })
            
            return combined_context
            
        except EvaluationContext.DoesNotExist:
            logger.error(f"Base context {base_context_id} not found")
            return {}
```

## Step 2.3: Seed Evaluation Contexts (Day 2)

### Create `backend/apps/main/management/commands/seed_evaluation_contexts.py`:

```python
from django.core.management.base import BaseCommand
from apps.main.services.evaluation_context_service import EvaluationContextService

class Command(BaseCommand):
    help = 'Seed evaluation contexts for agent evaluation'
    
    def handle(self, *args, **options):
        service = EvaluationContextService()
        
        contexts_data = [
            # Mentor Agent Contexts
            {
                'name': 'Stressed_Evening_WheelGen_Mentor',
                'description': 'Stressed user in evening, wheel generation result delivery, evaluating mentor agent',
                'current_workflow_type': 'wheel_generation',
                'workflow_stage': 'result_delivery',
                'agent_role_being_evaluated': 'mentor',
                
                # Context overrides (will override UserProfile values)
                'trust_level_override': 45,  # Force expansion phase
                'mood_valence_override': -0.3,  # Negative mood
                'mood_arousal_override': 0.2,   # Low energy
                'stress_level_override': 75,    # High stress
                'time_pressure_override': 60,   # Time pressure
                'reported_time_availability_override': '15_minutes',
                'reported_focus_override': 'quick stress relief',
                
                'previous_agent_outputs': [
                    'final_wheel: 6_calming_foundation_activities'
                ],
                'expected_next_agents': []  # Mentor is final
            },
            {
                'name': 'Confident_Morning_Discussion_Mentor',
                'description': 'Confident user seeking guidance, discussion workflow, evaluating mentor agent',
                'current_workflow_type': 'discussion',
                'workflow_stage': 'agent_processing',
                'agent_role_being_evaluated': 'mentor',
                
                'trust_level_override': 78,    # Integration phase
                'mood_valence_override': 0.6,  # Positive mood
                'mood_arousal_override': 0.4,  # Moderate energy
                'stress_level_override': 20,   # Low stress
                'reported_environment_override': 'home',
                'reported_time_availability_override': '60_minutes',
                'reported_focus_override': 'creative and challenging activities',
                
                'previous_agent_outputs': [],
                'expected_next_agents': []
            },
            
            # Psychological Agent Contexts
            {
                'name': 'New_User_WheelGen_Psychological',
                'description': 'New anxious user, wheel generation processing, evaluating psychological agent',
                'current_workflow_type': 'wheel_generation',
                'workflow_stage': 'agent_processing',
                'agent_role_being_evaluated': 'psychological',
                
                'trust_level_override': 25,    # Foundation phase
                'mood_valence_override': -0.1, # Slightly negative
                'mood_arousal_override': 0.6,  # Anxious energy
                'stress_level_override': 50,   # Moderate stress
                'reported_environment_override': 'home',
                'reported_focus_override': 'something easy to start with',
                
                'previous_agent_outputs': [
                    'resource_agent: limited_energy_home_environment',
                    'engagement_agent: no_clear_patterns_new_user'
                ],
                'expected_next_agents': ['strategy', 'wheel_activity', 'ethical']
            },
            
            # Strategy Agent Contexts  
            {
                'name': 'ADHD_User_WheelGen_Strategy',
                'description': 'ADHD user, wheel generation processing, evaluating strategy agent',
                'current_workflow_type': 'wheel_generation',
                'workflow_stage': 'agent_processing',
                'agent_role_being_evaluated': 'strategy',
                
                'trust_level_override': 82,    # Integration phase
                'mood_valence_override': 0.5,  # Positive
                'mood_arousal_override': 0.7,  # High energy (ADHD)
                'stress_level_override': 30,   # Low stress
                'reported_focus_override': 'creative and novel activities',
                
                'previous_agent_outputs': [
                    'resource_agent: high_energy_creative_materials_available',
                    'engagement_agent: strong_preference_for_novelty_and_creativity',
                    'psychological_agent: integration_phase_ready_for_complex_challenges'
                ],
                'expected_next_agents': ['wheel_activity', 'ethical']
            },
            
            # Activity Feedback Contexts
            {
                'name': 'Post_Success_Feedback_Psychological',
                'description': 'User completed challenging activity successfully, evaluating psychological agent',
                'current_workflow_type': 'activity_feedback',
                'workflow_stage': 'agent_processing',
                'agent_role_being_evaluated': 'psychological',
                
                'trust_level_override': 68,    # Expansion phase
                'mood_valence_override': 0.8,  # Very positive after success
                'mood_arousal_override': 0.5,  # Energized
                'stress_level_override': 15,   # Very low stress
                'reported_focus_override': 'share success and plan next steps',
                
                'previous_agent_outputs': [
                    'activity_completed: creative_writing_exercise_30min',
                    'user_feedback: felt_challenging_but_very_rewarding'
                ],
                'expected_next_agents': ['mentor']
            }
        ]
        
        for context_data in contexts_data:
            service.create_context(context_data)
            self.stdout.write(
                self.style.SUCCESS(f"Created evaluation context: {context_data['name']}")
            )
        
        self.stdout.write(
            self.style.SUCCESS(f"Successfully seeded {len(contexts_data)} evaluation contexts")
        )
```

### Run seeding:
```bash
python manage.py seed_evaluation_contexts
```

## Step 2.4: Test Context Integration (Day 3)

### Create `backend/apps/main/tests/test_evaluation_context_service.py`:

```python
from django.test import TestCase
from apps.main.services.evaluation_context_service import EvaluationContextService
from apps.main.services.fake_userprofile_service import FakeUserProfileService
from apps.main.models import EvaluationContext
from apps.user.models import UserProfile

class TestEvaluationContextService(TestCase):
    def setUp(self):
        self.context_service = EvaluationContextService()
        self.userprofile_service = FakeUserProfileService()
        
        # Create test fake UserProfile
        self.fake_userprofile = self.userprofile_service.create_fake_userprofile({
            'profile_name': 'Test_Context_User',
            'personality_traits': {'openness': 0.7, 'conscientiousness': 0.6},
            'user_goals': ['test goal 1'],
            'user_beliefs': ['test belief 1']
        })
        
        # Create test evaluation context
        self.context_data = {
            'name': 'Test_WheelGen_Mentor',
            'description': 'Test context for wheel generation mentor evaluation',
            'current_workflow_type': 'wheel_generation',
            'workflow_stage': 'result_delivery',
            'agent_role_being_evaluated': 'mentor',
            'trust_level_override': 55,
            'mood_valence_override': 0.2,
            'stress_level_override': 40
        }
    
    def test_create_context(self):
        context = self.context_service.create_context(self.context_data)
        self.assertEqual(context.name, 'Test_WheelGen_Mentor')
        self.assertEqual(context.trust_phase, 'expansion')  # 55 is in expansion phase
    
    def test_get_combined_context(self):
        context = self.context_service.create_context(self.context_data)
        
        combined_context = self.context_service.get_combined_context(
            str(context.id), 
            self.fake_userprofile
        )
        
        # Test that overrides are applied
        self.assertEqual(combined_context['trust_level'], 55)  # Override value
        self.assertEqual(combined_context['mood']['valence'], 0.2)  # Override value
        self.assertEqual(combined_context['environment']['stress_level'], 40)  # Override value
        
        # Test that UserProfile data is included
        self.assertEqual(combined_context['profile_name'], 'Test_Context_User')
        self.assertFalse(combined_context['is_real'])
        self.assertIn('personality_traits', combined_context)
        self.assertIn('user_goals', combined_context)
        self.assertIn('user_beliefs', combined_context)
        
        # Test workflow context
        self.assertEqual(combined_context['workflow_type'], 'wheel_generation')
        self.assertEqual(combined_context['agent_role'], 'mentor')
    
    def test_list_contexts_for_agent(self):
        # Create multiple contexts
        mentor_context = self.context_service.create_context(self.context_data)
        
        psychological_context_data = self.context_data.copy()
        psychological_context_data['name'] = 'Test_Psychological_Context'
        psychological_context_data['agent_role_being_evaluated'] = 'psychological'
        psychological_context = self.context_service.create_context(psychological_context_data)
        
        # Test filtering by agent role
        mentor_contexts = self.context_service.list_contexts_for_agent('mentor')
        self.assertEqual(len(mentor_contexts), 1)
        
        psychological_contexts = self.context_service.list_contexts_for_agent('psychological')
        self.assertEqual(len(psychological_contexts), 1)
    
    def test_context_without_overrides(self):
        """Test that context works when no overrides are specified"""
        context_data_no_overrides = {
            'name': 'Test_Default_Context',
            'description': 'Test context with no overrides',
            'current_workflow_type': 'discussion',
            'workflow_stage': 'agent_processing',
            'agent_role_being_evaluated': 'mentor'
            # No override fields specified
        }
        
        context = self.context_service.create_context(context_data_no_overrides)
        combined_context = self.context_service.get_combined_context(
            str(context.id),
            self.fake_userprofile
        )
        
        # Should use UserProfile defaults when no overrides
        # (You'll need to set up your fake UserProfile with default values)
        self.assertIsNotNone(combined_context['trust_level'])
        self.assertIsNotNone(combined_context['mood']['valence'])
        
    def test_create_context_for_userprofile_combination(self):
        """Test creating a context-userprofile combination preview"""
        context = self.context_service.create_context(self.context_data)
        
        combination = self.context_service.create_context_for_userprofile_combination(
            str(context.id),
            self.fake_userprofile,
            "_test_combination"
        )
        
        self.assertIn('combination_name', combination)
        self.assertTrue(combination['is_fake_profile'])
        self.assertTrue(combination['context_overrides_applied'])
        self.assertEqual(combination['base_context_name'], 'Test_WheelGen_Mentor')
```

### Run tests:
```bash
python manage.py test apps.main.tests.test_evaluation_context_service
```

**Phase 2 Complete**: You now have evaluation contexts that can override specific UserProfile values while leveraging all the real user data and relationships!

## Key Benefits of This Approach:

1. **Real Data Integration**: Uses actual UserProfile with all relationships (traits, goals, beliefs)
2. **Flexible Overrides**: Can override specific context variables for evaluation scenarios
3. **Workflow Integration**: Captures actual workflow and agent coordination context
4. **Backward Compatibility**: Works with existing UserProfile model and relationships
5. **Test Realism**: Tests against real data structures, catching actual system issues
