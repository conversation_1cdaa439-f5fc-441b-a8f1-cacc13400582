# Implementation Guide: Lean Agent Benchmarking Extension

## 🎯 **Implementation Methodology**

This guide follows **Iterative Development** principles with **Test-Driven Development (TDD)** practices, implementing the lean benchmarking extension through **four distinct phases**. Each phase includes comprehensive **unit testing**, **integration validation**, and **quality assurance checkpoints**.

**Reference Standards:**
- **Django Best Practices** (Django Software Foundation)
- **Python Enhancement Proposals** (PEP 8, PEP 257, PEP 484)
- **Test-Driven Development** (Kent Beck)
- **Clean Code Principles** (<PERSON>)
- **Domain-Driven Design** (<PERSON>)

## **Prerequisites Validation**

### **Environment Setup Verification**
```bash
# Verify Django environment and dependencies
cd /projects/goali1/backend

# Check Django installation and version
python manage.py version

# Verify existing models and services
python manage.py shell << 'EOF'
from apps.user.models import UserProfile
from apps.main.models import BenchmarkRun, GenericAgent
from apps.main.services.benchmark_service import AgentBenchmarker

print("✓ UserProfile model accessible")
print("✓ BenchmarkRun model accessible") 
print("✓ GenericAgent model accessible")
print("✓ AgentBenchmarker service accessible")
EOF

# Verify database migrations are current
python manage.py migrate --check
```

### **Dependency Analysis**
```python
# Required existing components (validation script)
# File: scripts/validate_prerequisites.py

import sys
from pathlib import Path

def validate_existing_infrastructure():
    """
    Validates existing Goali infrastructure for compatibility
    Following dependency analysis best practices
    """
    required_components = {
        'models': [
            'apps.user.models.UserProfile',
            'apps.main.models.BenchmarkRun',
            'apps.main.models.BenchmarkScenario',
            'apps.main.models.GenericAgent'
        ],
        'services': [
            'apps.main.services.benchmark_service.AgentBenchmarker',
            'apps.main.services.semantic_evaluator.SemanticEvaluator'
        ]
    }
    
    validation_results = []
    
    for component_type, components in required_components.items():
        for component in components:
            try:
                # Dynamic import validation
                module_path, class_name = component.rsplit('.', 1)
                module = __import__(module_path, fromlist=[class_name])
                getattr(module, class_name)
                validation_results.append(f"✓ {component}")
            except (ImportError, AttributeError) as e:
                validation_results.append(f"✗ {component}: {e}")
                
    return validation_results

if __name__ == "__main__":
    results = validate_existing_infrastructure()
    for result in results:
        print(result)
```

## **Phase 1: Benchmark Profile Factory Implementation**

### **1.1 Service Layer Implementation**

**File**: `backend/apps/main/services/benchmark_profile_factory.py`

```python
"""
Benchmark Profile Factory Service Implementation
Following Factory Pattern (GoF), Django Service Layer Pattern

References:
- Design Patterns: Elements of Reusable Object-Oriented Software (GoF)
- Django Best Practices (Django Software Foundation)
- Clean Code: A Handbook of Agile Software Craftsmanship (Robert C. Martin)
"""

import logging
import uuid
from typing import Dict, List, Optional, Any
from decimal import Decimal
from django.db import transaction, IntegrityError
from django.core.exceptions import ValidationError
from django.utils import timezone

from apps.user.models import (
    UserProfile, UserTraitInclination, UserGoal, 
    Belief, UserLimitation, Limitation
)
from apps.main.models import PersonalityTrait, Domain

logger = logging.getLogger(__name__)

class BenchmarkProfileFactoryError(Exception):
    """Custom exception for profile factory operations"""
    pass

class BenchmarkProfileFactory:
    """
    Factory service for creating benchmark-ready UserProfiles
    
    Implementation follows:
    - Factory Pattern for object creation encapsulation
    - Static Factory Method pattern for named constructors
    - Template Method pattern for profile creation steps
    - Transaction Script pattern for data consistency
    
    Psychology Framework Integration:
    - Big Five personality model (OCEAN)
    - Psychological stress and mood modeling
    - Evidence-based user limitation categories
    """
    
    # Profile templates based on psychological research
    # Reference: Big Five personality traits (McCrae & Costa, 1987)
    PROFILE_TEMPLATES = {
        'anxious_new_user': {
            'profile_name': 'Benchmark_Anxious_New_User',
            'description': 'High neuroticism, low confidence, new to coaching systems',
            'personality_traits': {
                'openness': 0.4,           # Low openness to new experiences
                'conscientiousness': 0.7,   # High conscientiousness (rule-following)
                'extraversion': 0.3,        # Low extraversion (introverted tendencies)
                'agreeableness': 0.8,       # High agreeableness (cooperative)
                'neuroticism': 0.8,         # High neuroticism (anxiety-prone)
                'honesty_humility': 0.7     # High honesty-humility
            },
            'goals': [
                'Reduce social anxiety in group settings',
                'Build confidence through small achievements',
                'Improve social skills gradually',
                'Develop coping strategies for stress'
            ],
            'beliefs': [
                'I am not good enough for others',
                'People will judge me harshly if I make mistakes',
                'I need to be perfect to be accepted',
                'Change is difficult and scary'
            ],
            'limitations': [
                'Social interaction anxiety',
                'Low self-confidence',
                'Perfectionism tendencies',
                'Fear of judgment'
            ],
            'psychological_profile': {
                'stress_level': 7,          # Scale 1-10
                'mood_valence': 3.0,        # Scale 1-10 (positive/negative)
                'mood_arousal': 6.0,        # Scale 1-10 (calm/excited)
                'trust_level': 4,           # Scale 1-10 (system trust)
                'motivation_level': 6       # Scale 1-10
            }
        },
        
        'confident_adhd_user': {
            'profile_name': 'Benchmark_Confident_ADHD_User',
            'description': 'High openness, low conscientiousness, creative energy with attention challenges',
            'personality_traits': {
                'openness': 0.9,            # Very high openness (creative, curious)
                'conscientiousness': 0.4,   # Low conscientiousness (spontaneous)
                'extraversion': 0.7,        # High extraversion (outgoing)
                'agreeableness': 0.6,       # Moderate agreeableness
                'neuroticism': 0.3,         # Low neuroticism (emotionally stable)
                'honesty_humility': 0.6     # Moderate honesty-humility
            },
            'goals': [
                'Channel creative energy into structured projects',
                'Improve focus and attention management',
                'Complete long-term creative endeavors',
                'Develop time management systems that work'
            ],
            'beliefs': [
                'I can figure out creative solutions to any problem',
                'Challenges are opportunities for growth and innovation',
                'My unique perspective adds value to situations',
                'Structure can enhance rather than limit creativity'
            ],
            'limitations': [
                'Attention regulation difficulties',
                'Task completion challenges',
                'Time estimation problems',
                'Detail management issues'
            ],
            'psychological_profile': {
                'stress_level': 4,
                'mood_valence': 7.0,
                'mood_arousal': 8.0,
                'trust_level': 7,
                'motivation_level': 8
            }
        },
        
        'stressed_professional': {
            'profile_name': 'Benchmark_Stressed_Professional',
            'description': 'High conscientiousness, work-life balance issues, achievement-oriented',
            'personality_traits': {
                'openness': 0.6,            # Moderate openness
                'conscientiousness': 0.9,   # Very high conscientiousness
                'extraversion': 0.5,        # Moderate extraversion
                'agreeableness': 0.5,       # Moderate agreeableness
                'neuroticism': 0.6,         # Moderate-high neuroticism
                'honesty_humility': 0.8     # High honesty-humility
            },
            'goals': [
                'Manage work-related stress effectively',
                'Achieve sustainable work-life balance',
                'Increase productivity without burnout',
                'Develop delegation and boundary-setting skills'
            ],
            'beliefs': [
                'Hard work always pays off in the end',
                'I must do everything perfectly to be valuable',
                'Taking breaks is a sign of weakness',
                'Success requires personal sacrifice'
            ],
            'limitations': [
                'Perfectionism patterns',
                'Work-life boundary issues',
                'Delegation difficulties',
                'Burnout vulnerability'
            ],
            'psychological_profile': {
                'stress_level': 8,
                'mood_valence': 5.0,
                'mood_arousal': 7.0,
                'trust_level': 6,
                'motivation_level': 9
            }
        },
        
        'creative_explorer': {
            'profile_name': 'Benchmark_Creative_Explorer',
            'description': 'High openness, artistic tendencies, seeking self-expression',
            'personality_traits': {
                'openness': 0.95,           # Extremely high openness
                'conscientiousness': 0.5,   # Moderate conscientiousness
                'extraversion': 0.6,        # Moderate-high extraversion
                'agreeableness': 0.7,       # High agreeableness
                'neuroticism': 0.4,         # Low-moderate neuroticism
                'honesty_humility': 0.7     # High honesty-humility
            },
            'goals': [
                'Develop and express authentic creative voice',
                'Build sustainable creative practices',
                'Connect with like-minded creative community',
                'Balance artistic pursuits with practical needs'
            ],
            'beliefs': [
                'Creativity is essential to human flourishing',
                'Authentic self-expression leads to fulfillment',
                'Art has the power to create positive change',
                'Creative blocks are temporary and surmountable'
            ],
            'limitations': [
                'Financial planning challenges',
                'Practical task management',
                'Creative perfectionism',
                'Market-oriented thinking difficulties'
            ],
            'psychological_profile': {
                'stress_level': 5,
                'mood_valence': 7.0,
                'mood_arousal': 6.0,
                'trust_level': 6,
                'motivation_level': 8
            }
        }
    }
    
    @classmethod
    @transaction.atomic
    def create_benchmark_profile(cls, template_name: str) -> UserProfile:
        """
        Create a benchmark UserProfile from template using Factory Pattern
        
        Args:
            template_name: Key from PROFILE_TEMPLATES dictionary
            
        Returns:
            UserProfile: Created profile with is_real=False
            
        Raises:
            BenchmarkProfileFactoryError: If template not found or creation fails
            ValidationError: If profile data validation fails
            
        Implementation Notes:
        - Uses Django transaction.atomic for data consistency
        - Follows Template Method pattern for profile creation steps
        - Implements comprehensive error handling and logging
        - Validates psychological profile data against research-based ranges
        """
        logger.info(f"Creating benchmark profile: {template_name}")
        
        if template_name not in cls.PROFILE_TEMPLATES:
            available_templates = list(cls.PROFILE_TEMPLATES.keys())
            error_msg = f"Unknown template: {template_name}. Available: {available_templates}"
            logger.error(error_msg)
            raise BenchmarkProfileFactoryError(error_msg)
            
        template = cls.PROFILE_TEMPLATES[template_name]
        
        try:
            # Step 1: Create base UserProfile with psychological data
            profile = cls._create_base_profile(template)
            
            # Step 2: Create personality trait relationships
            cls._create_personality_traits(profile, template['personality_traits'])
            
            # Step 3: Create goal relationships
            cls._create_user_goals(profile, template['goals'])
            
            # Step 4: Create belief relationships
            cls._create_user_beliefs(profile, template['beliefs'])
            
            # Step 5: Create limitation relationships
            cls._create_user_limitations(profile, template['limitations'])
            
            # Step 6: Validate complete profile
            cls._validate_profile_integrity(profile)
            
            logger.info(
                f"Successfully created benchmark profile: {profile.profile_name} (ID: {profile.id})"
            )
            return profile
            
        except Exception as e:
            logger.error(f"Failed to create benchmark profile {template_name}: {e}", exc_info=True)
            raise BenchmarkProfileFactoryError(f"Profile creation failed: {e}") from e
    
    @classmethod
    def get_or_create_benchmark_profile(cls, template_name: str) -> UserProfile:
        """
        Get existing benchmark profile or create new one (idempotent operation)
        
        Args:
            template_name: Template identifier
            
        Returns:
            UserProfile: Existing or newly created profile
            
        Implementation Notes:
        - Implements idempotent profile creation pattern
        - Uses database constraints to prevent duplicates
        - Provides caching optimization for frequently accessed profiles
        """
        if template_name not in cls.PROFILE_TEMPLATES:
            raise BenchmarkProfileFactoryError(f"Unknown template: {template_name}")
            
        template = cls.PROFILE_TEMPLATES[template_name]
        profile_name = template['profile_name']
        
        try:
            # Attempt to retrieve existing profile
            profile = UserProfile.objects.get(
                profile_name=profile_name,
                is_real=False
            )
            logger.info(f"Retrieved existing benchmark profile: {profile_name}")
            return profile
            
        except UserProfile.DoesNotExist:
            # Create new profile if not found
            logger.info(f"Creating new benchmark profile: {profile_name}")
            return cls.create_benchmark_profile(template_name)
    
    @classmethod
    def list_available_templates(cls) -> List[Dict[str, str]]:
        """
        Return metadata for all available profile templates
        
        Returns:
            List of dictionaries containing template metadata
        """
        return [
            {
                'template_name': name,
                'profile_name': template['profile_name'],
                'description': template['description'],
                'personality_summary': cls._summarize_personality(template['personality_traits']),
                'stress_level': template['psychological_profile']['stress_level'],
                'goal_count': len(template['goals']),
                'belief_count': len(template['beliefs']),
                'limitation_count': len(template['limitations'])
            }
            for name, template in cls.PROFILE_TEMPLATES.items()
        ]
    
    @classmethod
    def _create_base_profile(cls, template: Dict[str, Any]) -> UserProfile:
        """
        Create base UserProfile with psychological data
        
        Implementation follows Django model best practices with proper field validation
        """
        psychological_profile = template['psychological_profile']
        
        # Validate psychological data ranges (research-based)
        cls._validate_psychological_ranges(psychological_profile)
        
        profile = UserProfile.objects.create(
            profile_name=template['profile_name'],
            description=template['description'],
            is_real=False,  # Critical: marks as benchmark profile
            stress_level=psychological_profile['stress_level'],
            mood_valence=Decimal(str(psychological_profile['mood_valence'])),
            mood_arousal=Decimal(str(psychological_profile['mood_arousal'])),
            trust_level=psychological_profile.get('trust_level', 5),
            motivation_level=psychological_profile.get('motivation_level', 5),
            created_at=timezone.now(),
            updated_at=timezone.now()
        )
        
        return profile
    
    @classmethod
    def _create_personality_traits(cls, profile: UserProfile, traits: Dict[str, float]):
        """
        Create UserTraitInclination objects using Big Five personality model
        
        References:
        - McCrae, R. R., & Costa, P. T. (1987). Validation of the five-factor model
        - John, O. P., & Srivastava, S. (1999). The Big Five trait taxonomy
        """
        for trait_name, strength in traits.items():
            try:
                # Validate trait strength (0.0-1.0 range)
                if not 0.0 <= strength <= 1.0:
                    raise ValidationError(f"Trait strength must be 0.0-1.0, got {strength}")
                
                # Get or create personality trait
                trait, created = PersonalityTrait.objects.get_or_create(
                    name=trait_name.lower(),
                    defaults={
                        'name': trait_name.lower(),
                        'description': cls._get_trait_description(trait_name),
                        'category': 'big_five'
                    }
                )
                
                if created:
                    logger.debug(f"Created new personality trait: {trait_name}")
                
                # Create trait inclination
                UserTraitInclination.objects.create(
                    user_profile=profile,
                    trait=trait,
                    strength=Decimal(str(strength)),
                    source='benchmark_template',
                    confidence=Decimal('0.95')  # High confidence for template data
                )
                
            except Exception as e:
                logger.warning(f"Failed to create trait {trait_name} for profile {profile.id}: {e}")
                # Continue with other traits rather than failing completely
    
    @classmethod
    def _create_user_goals(cls, profile: UserProfile, goals: List[str]):
        """
        Create UserGoal objects with proper prioritization and categorization
        """
        for i, goal_description in enumerate(goals):
            try:
                # Validate goal description
                if not goal_description or len(goal_description) < 10:
                    logger.warning(f"Skipping invalid goal: {goal_description}")
                    continue
                
                UserGoal.objects.create(
                    user_profile=profile,
                    description=goal_description.strip(),
                    priority=i + 1,
                    status='active',
                    category=cls._categorize_goal(goal_description),
                    created_at=timezone.now(),
                    target_completion_date=None,  # Open-ended goals for benchmarking
                    progress_percentage=0
                )
                
            except Exception as e:
                logger.warning(f"Failed to create goal '{goal_description}' for profile {profile.id}: {e}")
    
    @classmethod
    def _create_user_beliefs(cls, profile: UserProfile, beliefs: List[str]):
        """
        Create Belief objects with psychological categorization
        """
        for belief_text in beliefs:
            try:
                # Validate belief text
                if not belief_text or len(belief_text) < 5:
                    logger.warning(f"Skipping invalid belief: {belief_text}")
                    continue
                
                Belief.objects.create(
                    user_profile=profile,
                    description=belief_text.strip(),
                    strength=Decimal('0.8'),  # Strong beliefs for clear benchmarking
                    category=cls._categorize_belief(belief_text),
                    valence=cls._determine_belief_valence(belief_text),
                    source='initial_assessment',
                    created_at=timezone.now()
                )
                
            except Exception as e:
                logger.warning(f"Failed to create belief '{belief_text}' for profile {profile.id}: {e}")
    
    @classmethod
    def _create_user_limitations(cls, profile: UserProfile, limitations: List[str]):
        """
        Create UserLimitation objects with severity assessment
        """
        for limitation_text in limitations:
            try:
                # Get or create limitation category
                limitation, created = Limitation.objects.get_or_create(
                    name=limitation_text.strip(),
                    defaults={
                        'name': limitation_text.strip(),
                        'description': f"Benchmark limitation: {limitation_text}",
                        'category': cls._categorize_limitation(limitation_text)
                    }
                )
                
                if created:
                    logger.debug(f"Created new limitation category: {limitation_text}")
                
                # Create user limitation relationship
                from apps.user.models import UserLimitation
                UserLimitation.objects.create(
                    user_profile=profile,
                    limitation=limitation,
                    severity=cls._assess_limitation_severity(limitation_text),
                    impact_areas=['coaching', 'goal_setting'],  # Default areas for benchmarking
                    accommodation_needed=True,
                    created_at=timezone.now()
                )
                
            except Exception as e:
                logger.warning(f"Failed to create limitation '{limitation_text}' for profile {profile.id}: {e}")
    
    @classmethod
    def _validate_psychological_ranges(cls, psychological_profile: Dict[str, Any]):
        """
        Validate psychological data against research-based ranges
        
        References:
        - Watson, D., & Tellegen, A. (1985). Toward a consensual structure of mood
        - Russell, J. A. (1980). A circumplex model of affect
        """
        validations = [
            ('stress_level', 1, 10),
            ('mood_valence', 1.0, 10.0),
            ('mood_arousal', 1.0, 10.0),
            ('trust_level', 1, 10),
            ('motivation_level', 1, 10)
        ]
        
        for field, min_val, max_val in validations:
            if field in psychological_profile:
                value = psychological_profile[field]
                if not min_val <= value <= max_val:
                    raise ValidationError(
                        f"{field} must be between {min_val} and {max_val}, got {value}"
                    )
    
    @classmethod
    def _validate_profile_integrity(cls, profile: UserProfile):
        """
        Validate complete profile integrity after creation
        """
        validations = [
            (profile.trait_inclinations.count() > 0, "Profile must have personality traits"),
            (profile.goals.count() > 0, "Profile must have goals"),
            (profile.beliefs.count() > 0, "Profile must have beliefs"),
            (not profile.is_real, "Benchmark profile must have is_real=False"),
            (profile.profile_name.startswith('Benchmark_'), "Profile name must start with 'Benchmark_'")
        ]
        
        for condition, error_message in validations:
            if not condition:
                raise ValidationError(f"Profile integrity violation: {error_message}")
    
    @classmethod
    def _get_trait_description(cls, trait_name: str) -> str:
        """Get research-based trait descriptions"""
        descriptions = {
            'openness': 'Openness to experience - creativity, curiosity, intellectual engagement',
            'conscientiousness': 'Conscientiousness - organization, discipline, goal-directed behavior',
            'extraversion': 'Extraversion - sociability, assertiveness, positive emotions',
            'agreeableness': 'Agreeableness - cooperation, trust, empathy',
            'neuroticism': 'Neuroticism - emotional instability, anxiety, negative emotions',
            'honesty_humility': 'Honesty-Humility - sincerity, fairness, modesty'
        }
        return descriptions.get(trait_name.lower(), f"{trait_name.title()} personality dimension")
    
    @classmethod
    def _categorize_goal(cls, goal_description: str) -> str:
        """Categorize goals based on content analysis"""
        goal_lower = goal_description.lower()
        
        if any(word in goal_lower for word in ['social', 'relationship', 'communication']):
            return 'social'
        elif any(word in goal_lower for word in ['stress', 'anxiety', 'mental', 'emotional']):
            return 'mental_health'
        elif any(word in goal_lower for word in ['work', 'career', 'professional', 'productivity']):
            return 'professional'
        elif any(word in goal_lower for word in ['creative', 'artistic', 'expression']):
            return 'creative'
        else:
            return 'personal_development'
    
    @classmethod
    def _categorize_belief(cls, belief_text: str) -> str:
        """Categorize beliefs for psychological analysis"""
        belief_lower = belief_text.lower()
        
        if any(word in belief_lower for word in ['not good', 'not enough', 'failure', 'judge']):
            return 'limiting'
        elif any(word in belief_lower for word in ['can', 'able', 'succeed', 'growth']):
            return 'empowering'
        elif any(word in belief_lower for word in ['must', 'should', 'have to', 'perfect']):
            return 'prescriptive'
        else:
            return 'descriptive'
    
    @classmethod
    def _determine_belief_valence(cls, belief_text: str) -> str:
        """Determine emotional valence of beliefs"""
        negative_indicators = ['not', 'never', 'failure', 'wrong', 'bad', 'judge', 'difficult']
        positive_indicators = ['can', 'will', 'good', 'opportunity', 'growth', 'succeed']
        
        belief_lower = belief_text.lower()
        negative_count = sum(1 for word in negative_indicators if word in belief_lower)
        positive_count = sum(1 for word in positive_indicators if word in belief_lower)
        
        if negative_count > positive_count:
            return 'negative'
        elif positive_count > negative_count:
            return 'positive'
        else:
            return 'neutral'
    
    @classmethod
    def _categorize_limitation(cls, limitation_text: str) -> str:
        """Categorize limitations for accommodation planning"""
        limitation_lower = limitation_text.lower()
        
        if any(word in limitation_lower for word in ['anxiety', 'social', 'confidence']):
            return 'psychological'
        elif any(word in limitation_lower for word in ['attention', 'focus', 'adhd', 'concentration']):
            return 'cognitive'
        elif any(word in limitation_lower for word in ['time', 'organization', 'planning']):
            return 'executive_function'
        elif any(word in limitation_lower for word in ['perfection', 'control', 'rigid']):
            return 'behavioral'
        else:
            return 'other'
    
    @classmethod
    def _assess_limitation_severity(cls, limitation_text: str) -> str:
        """Assess limitation severity for accommodation planning"""
        high_severity_indicators = ['severe', 'extreme', 'major', 'significant']
        moderate_severity_indicators = ['moderate', 'some', 'occasional']
        
        limitation_lower = limitation_text.lower()
        
        if any(word in limitation_lower for word in high_severity_indicators):
            return 'high'
        elif any(word in limitation_lower for word in moderate_severity_indicators):
            return 'moderate'
        else:
            return 'moderate'  # Default to moderate for benchmarking
    
    @classmethod
    def _summarize_personality(cls, traits: Dict[str, float]) -> str:
        """Create personality summary for template metadata"""
        high_traits = [name for name, value in traits.items() if value >= 0.7]
        low_traits = [name for name, value in traits.items() if value <= 0.3]
        
        summary_parts = []
        if high_traits:
            summary_parts.append(f"High: {', '.join(high_traits)}")
        if low_traits:
            summary_parts.append(f"Low: {', '.join(low_traits)}")
            
        return '; '.join(summary_parts) if summary_parts else 'Balanced personality profile'
```

### **1.2 Unit Testing Implementation**

**File**: `backend/apps/main/tests/test_benchmark_profile_factory.py`

```python
"""
Comprehensive unit tests for BenchmarkProfileFactory
Following TDD principles and Django testing best practices

References:
- Test-Driven Development: By Example (Kent Beck)
- Django Testing Documentation
- Python unittest and pytest best practices
"""

import pytest
from decimal import Decimal
from django.test import TestCase, TransactionTestCase
from django.core.exceptions import ValidationError
from django.db import IntegrityError

from apps.main.services.benchmark_profile_factory import (
    BenchmarkProfileFactory, 
    BenchmarkProfileFactoryError
)
from apps.user.models import UserProfile, UserTraitInclination, UserGoal, Belief
from apps.main.models import PersonalityTrait

class TestBenchmarkProfileFactory(TransactionTestCase):
    """
    Test suite for BenchmarkProfileFactory following AAA pattern
    (Arrange-Act-Assert)
    """
    
    def setUp(self):
        """Set up test fixtures following Django testing best practices"""
        self.factory = BenchmarkProfileFactory()
        self.valid_template_name = 'anxious_new_user'
        self.invalid_template_name = 'nonexistent_template'
    
    def test_create_benchmark_profile_success(self):
        """
        Test successful benchmark profile creation
        
        Validates:
        - Profile creation with correct attributes
        - Related object creation (traits, goals, beliefs, limitations)
        - Database integrity constraints
        """
        # Arrange
        initial_profile_count = UserProfile.objects.count()
        
        # Act
        profile = self.factory.create_benchmark_profile(self.valid_template_name)
        
        # Assert
        self.assertIsInstance(profile, UserProfile)
        self.assertEqual(UserProfile.objects.count(), initial_profile_count + 1)
        
        # Verify profile attributes
        self.assertFalse(profile.is_real)  # Critical assertion
        self.assertEqual(profile.profile_name, 'Benchmark_Anxious_New_User')
        self.assertIsNotNone(profile.description)
        
        # Verify psychological attributes
        self.assertEqual(profile.stress_level, 7)
        self.assertEqual(profile.mood_valence, Decimal('3.0'))
        self.assertEqual(profile.mood_arousal, Decimal('6.0'))
        
        # Verify related objects creation
        self.assertTrue(profile.trait_inclinations.exists())
        self.assertTrue(profile.goals.exists())
        self.assertTrue(profile.beliefs.exists())
        
        # Verify personality traits count (Big Five + Honesty-Humility = 6)
        self.assertEqual(profile.trait_inclinations.count(), 6)
        
        # Verify goals creation
        goals = list(profile.goals.values_list('description', flat=True))
        expected_goals = [
            'Reduce social anxiety in group settings',
            'Build confidence through small achievements',
            'Improve social skills gradually',
            'Develop coping strategies for stress'
        ]
        self.assertEqual(len(goals), len(expected_goals))
        for expected_goal in expected_goals:
            self.assertIn(expected_goal, goals)
    
    def test_create_benchmark_profile_invalid_template(self):
        """
        Test error handling for invalid template names
        
        Validates:
        - Proper exception raising for unknown templates
        - Error message content
        - No database side effects
        """
        # Arrange
        initial_profile_count = UserProfile.objects.count()
        
        # Act & Assert
        with self.assertRaises(BenchmarkProfileFactoryError) as context:
            self.factory.create_benchmark_profile(self.invalid_template_name)
        
        # Verify error message includes available templates
        error_message = str(context.exception)
        self.assertIn(self.invalid_template_name, error_message)
        self.assertIn('Available:', error_message)
        
        # Verify no database changes
        self.assertEqual(UserProfile.objects.count(), initial_profile_count)
    
    def test_get_or_create_benchmark_profile_idempotent(self):
        """
        Test idempotent behavior of get_or_create_benchmark_profile
        
        Validates:
        - First call creates profile
        - Second call returns same profile
        - No duplicate profiles created
        """
        # Arrange
        initial_count = UserProfile.objects.count()
        
        # Act - First call
        profile1 = self.factory.get_or_create_benchmark_profile(self.valid_template_name)
        
        # Assert - Profile created
        self.assertEqual(UserProfile.objects.count(), initial_count + 1)
        self.assertFalse(profile1.is_real)
        
        # Act - Second call
        profile2 = self.factory.get_or_create_benchmark_profile(self.valid_template_name)
        
        # Assert - Same profile returned, no duplicates
        self.assertEqual(profile1.id, profile2.id)
        self.assertEqual(UserProfile.objects.count(), initial_count + 1)
    
    def test_personality_traits_creation_accuracy(self):
        """
        Test accuracy of personality trait creation
        
        Validates:
        - Correct trait values according to Big Five model
        - Proper trait strength ranges (0.0-1.0)
        - All expected traits created
        """
        # Arrange & Act
        profile = self.factory.create_benchmark_profile(self.valid_template_name)
        
        # Assert
        trait_inclinations = {
            ti.trait.name: ti.strength 
            for ti in profile.trait_inclinations.all()
        }
        
        expected_traits = {
            'openness': Decimal('0.4'),
            'conscientiousness': Decimal('0.7'),
            'extraversion': Decimal('0.3'),
            'agreeableness': Decimal('0.8'),
            'neuroticism': Decimal('0.8'),
            'honesty_humility': Decimal('0.7')
        }
        
        for trait_name, expected_strength in expected_traits.items():
            self.assertIn(trait_name, trait_inclinations)
            self.assertEqual(trait_inclinations[trait_name], expected_strength)
    
    def test_list_available_templates(self):
        """
        Test template metadata retrieval
        
        Validates:
        - All templates included in results
        - Correct metadata structure
        - Summary information accuracy
        """
        # Act
        templates = self.factory.list_available_templates()
        
        # Assert
        self.assertIsInstance(templates, list)
        self.assertGreater(len(templates), 0)
        
        # Verify template structure
        for template in templates:
            required_fields = [
                'template_name', 'profile_name', 'description',
                'personality_summary', 'stress_level', 'goal_count',
                'belief_count', 'limitation_count'
            ]
            for field in required_fields:
                self.assertIn(field, template)
        
        # Verify specific template present
        anxious_template = next(
            (t for t in templates if t['template_name'] == 'anxious_new_user'),
            None
        )
        self.assertIsNotNone(anxious_template)
        self.assertEqual(anxious_template['stress_level'], 7)
        self.assertEqual(anxious_template['goal_count'], 4)
    
    def test_profile_integrity_validation(self):
        """
        Test profile integrity validation
        
        Validates:
        - Profile completeness checks
        - Required relationship verification
        - Business rule enforcement
        """
        # Act
        profile = self.factory.create_benchmark_profile(self.valid_template_name)
        
        # Assert integrity constraints
        self.assertFalse(profile.is_real)  # Must be benchmark profile
        self.assertTrue(profile.profile_name.startswith('Benchmark_'))
        self.assertGreater(profile.trait_inclinations.count(), 0)
        self.assertGreater(profile.goals.count(), 0)
        self.assertGreater(profile.beliefs.count(), 0)
    
    def test_psychological_range_validation(self):
        """
        Test psychological data range validation
        
        Validates:
        - Stress level within 1-10 range
        - Mood valence within 1-10 range  
        - Mood arousal within 1-10 range
        - Other psychological metrics within valid ranges
        """
        # Act
        profile = self.factory.create_benchmark_profile(self.valid_template_name)
        
        # Assert psychological ranges
        self.assertTrue(1 <= profile.stress_level <= 10)
        self.assertTrue(1.0 <= profile.mood_valence <= 10.0)
        self.assertTrue(1.0 <= profile.mood_arousal <= 10.0)
        
        if hasattr(profile, 'trust_level'):
            self.assertTrue(1 <= profile.trust_level <= 10)
        if hasattr(profile, 'motivation_level'):
            self.assertTrue(1 <= profile.motivation_level <= 10)
    
    def test_goal_categorization(self):
        """
        Test goal categorization logic
        
        Validates:
        - Correct category assignment based on content
        - Priority ordering
        - Status assignment
        """
        # Act
        profile = self.factory.create_benchmark_profile(self.valid_template_name)
        
        # Assert
        goals = profile.goals.all().order_by('priority')
        
        # Verify priorities are sequential
        priorities = [goal.priority for goal in goals]
        expected_priorities = list(range(1, len(goals) + 1))
        self.assertEqual(priorities, expected_priorities)
        
        # Verify all goals are active
        for goal in goals:
            self.assertEqual(goal.status, 'active')
    
    def test_belief_valence_detection(self):
        """
        Test belief valence detection algorithm
        
        Validates:
        - Negative beliefs correctly identified
        - Positive beliefs correctly identified
        - Valence categorization accuracy
        """
        # Act
        profile = self.factory.create_benchmark_profile(self.valid_template_name)
        
        # Assert
        beliefs = profile.beliefs.all()
        
        # Check for expected negative beliefs
        negative_beliefs = beliefs.filter(valence='negative')
        self.assertGreater(negative_beliefs.count(), 0)
        
        # Verify specific belief valences
        belief_texts = {belief.description: belief.valence for belief in beliefs}
        
        # Limiting beliefs should be negative
        limiting_belief = "I am not good enough for others"
        if limiting_belief in belief_texts:
            self.assertEqual(belief_texts[limiting_belief], 'negative')
    
    def test_all_templates_creation(self):
        """
        Test creation of all available templates
        
        Validates:
        - All templates can be successfully created
        - No template creation failures
        - Unique profile names
        """
        created_profiles = []
        
        # Act - Create all templates
        for template_name in self.factory.PROFILE_TEMPLATES.keys():
            profile = self.factory.create_benchmark_profile(template_name)
            created_profiles.append(profile)
        
        # Assert
        self.assertEqual(len(created_profiles), len(self.factory.PROFILE_TEMPLATES))
        
        # Verify unique profile names
        profile_names = [p.profile_name for p in created_profiles]
        self.assertEqual(len(profile_names), len(set(profile_names)))
        
        # Verify all are benchmark profiles
        for profile in created_profiles:
            self.assertFalse(profile.is_real)
    
    def tearDown(self):
        """Clean up test data following Django testing best practices"""
        UserProfile.objects.filter(is_real=False).delete()
        PersonalityTrait.objects.filter(category='big_five').delete()


@pytest.mark.django_db
class TestBenchmarkProfileFactoryPytest:
    """
    Additional pytest-style tests for modern testing patterns
    """
    
    def test_factory_error_handling_with_fixtures(self, db):
        """Test error handling using pytest fixtures"""
        factory = BenchmarkProfileFactory()
        
        with pytest.raises(BenchmarkProfileFactoryError) as exc_info:
            factory.create_benchmark_profile('invalid_template')
        
        assert 'Unknown template' in str(exc_info.value)
        assert 'Available:' in str(exc_info.value)
    
    @pytest.mark.parametrize('template_name', [
        'anxious_new_user',
        'confident_adhd_user', 
        'stressed_professional',
        'creative_explorer'
    ])
    def test_all_templates_parameterized(self, db, template_name):
        """Parameterized test for all templates"""
        factory = BenchmarkProfileFactory()
        
        profile = factory.create_benchmark_profile(template_name)
        
        assert profile is not None
        assert not profile.is_real
        assert profile.profile_name.startswith('Benchmark_')
        assert profile.trait_inclinations.count() > 0
        assert profile.goals.count() > 0
        assert profile.beliefs.count() > 0
```

### **1.3 Management Command Implementation**

**File**: `backend/apps/main/management/commands/seed_benchmark_profiles.py`

```python
"""
Management command for seeding benchmark UserProfiles
Following Django management command best practices

References:
- Django Management Commands Documentation
- Command Line Interface Design Patterns
- Python argparse best practices
"""

import sys
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction, IntegrityError
from django.utils import timezone

from apps.main.services.benchmark_profile_factory import (
    BenchmarkProfileFactory, 
    BenchmarkProfileFactoryError
)
from apps.user.models import UserProfile

class Command(BaseCommand):
    help = 'Seed benchmark UserProfiles for agent benchmarking system'
    
    def add_arguments(self, parser):
        """
        Add command line arguments following argparse best practices
        
        Implementation includes:
        - Optional template specification
        - Dry run mode for safety
        - Force recreation option
        - Verbose output control
        """
        parser.add_argument(
            '--template',
            type=str,
            help='Specific template to create (default: all templates)',
            choices=list(BenchmarkProfileFactory.PROFILE_TEMPLATES.keys())
        )
        
        parser.add_argument(
            '--recreate',
            action='store_true',
            help='Delete existing profiles and recreate them'
        )
        
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be created without actually creating profiles'
        )
        
        parser.add_argument(
            '--force',
            action='store_true',
            help='Skip confirmation prompts (use with caution)'
        )
        
        parser.add_argument(
            '--list-templates',
            action='store_true',
            help='List available templates and exit'
        )
    
    def handle(self, *args, **options):
        """
        Execute command with comprehensive error handling and user feedback
        
        Implementation follows:
        - Command pattern for operation encapsulation
        - Comprehensive logging and user feedback
        - Transaction management for data consistency
        - Graceful error handling and recovery
        """
        try:
            # Handle list templates option
            if options['list_templates']:
                self._list_available_templates()
                return
            
            # Handle dry run mode
            if options['dry_run']:
                self._execute_dry_run(options)
                return
            
            # Handle profile recreation
            if options['recreate']:
                if not options['force']:
                    self._confirm_recreation()
                self._recreate_profiles(options)
            else:
                self._create_profiles(options)
                
        except KeyboardInterrupt:
            self.stdout.write(
                self.style.WARNING('\nOperation cancelled by user')
            )
            sys.exit(1)
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Command failed: {e}')
            )
            raise CommandError(f"Profile seeding failed: {e}")
    
    def _list_available_templates(self):
        """Display available profile templates with metadata"""
        self.stdout.write(
            self.style.SUCCESS('Available Benchmark Profile Templates:')
        )
        self.stdout.write('')
        
        templates = BenchmarkProfileFactory.list_available_templates()
        
        for template in templates:
            self.stdout.write(f"📋 {template['template_name']}")
            self.stdout.write(f"   Name: {template['profile_name']}")
            self.stdout.write(f"   Description: {template['description']}")
            self.stdout.write(f"   Personality: {template['personality_summary']}")
            self.stdout.write(f"   Stress Level: {template['stress_level']}/10")
            self.stdout.write(f"   Goals: {template['goal_count']}")
            self.stdout.write(f"   Beliefs: {template['belief_count']}")
            self.stdout.write(f"   Limitations: {template['limitation_count']}")
            self.stdout.write('')
    
    def _execute_dry_run(self, options):
        """Execute dry run mode showing what would be created"""
        self.stdout.write(
            self.style.WARNING('🔍 DRY RUN MODE - No profiles will be created')
        )
        self.stdout.write('')
        
        templates_to_process = self._get_templates_to_process(options)
        
        for template_name in templates_to_process:
            template_data = BenchmarkProfileFactory.PROFILE_TEMPLATES[template_name]
            
            # Check if profile already exists
            profile_name = template_data['profile_name']
            exists = UserProfile.objects.filter(
                profile_name=profile_name,
                is_real=False
            ).exists()
            
            status = "EXISTS" if exists else "CREATE"
            style = self.style.WARNING if exists else self.style.SUCCESS
            
            self.stdout.write(style(f"[{status}] {profile_name}"))
            self.stdout.write(f"         Description: {template_data['description']}")
            self.stdout.write(f"         Goals: {len(template_data['goals'])}")
            self.stdout.write(f"         Beliefs: {len(template_data['beliefs'])}")
            self.stdout.write(f"         Limitations: {len(template_data['limitations'])}")
            self.stdout.write('')
        
        total_templates = len(templates_to_process)
        existing_count = UserProfile.objects.filter(
            profile_name__in=[
                BenchmarkProfileFactory.PROFILE_TEMPLATES[name]['profile_name']
                for name in templates_to_process
            ],
            is_real=False
        ).count()
        
        self.stdout.write(
            self.style.SUCCESS(
                f"Summary: {total_templates} templates, "
                f"{existing_count} existing, "
                f"{total_templates - existing_count} would be created"
            )
        )
    
    @transaction.atomic
    def _create_profiles(self, options):
        """Create benchmark profiles with transaction safety"""
        self.stdout.write(
            self.style.SUCCESS('🚀 Creating benchmark profiles...')
        )
        self.stdout.write('')
        
        templates_to_process = self._get_templates_to_process(options)
        factory = BenchmarkProfileFactory()
        
        results = {
            'created': 0,
            'skipped': 0,
            'errors': 0
        }
        
        for template_name in templates_to_process:
            try:
                # Use get_or_create for idempotent operation
                profile = factory.get_or_create_benchmark_profile(template_name)
                
                # Check if profile was just created or already existed
                was_created = not UserProfile.objects.filter(
                    id=profile.id
                ).exclude(
                    created_at=profile.created_at
                ).exists()
                
                if was_created or abs(
                    (timezone.now() - profile.created_at).total_seconds()
                ) < 60:  # Created within last minute
                    self.stdout.write(
                        self.style.SUCCESS(f"✅ Created: {profile.profile_name}")
                    )
                    results['created'] += 1
                else:
                    self.stdout.write(
                        self.style.WARNING(f"⚪ Skipped (exists): {profile.profile_name}")
                    )
                    results['skipped'] += 1
                    
            except BenchmarkProfileFactoryError as e:
                self.stdout.write(
                    self.style.ERROR(f"❌ Failed to create {template_name}: {e}")
                )
                results['errors'] += 1
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"❌ Unexpected error for {template_name}: {e}")
                )
                results['errors'] += 1
        
        # Display summary
        self.stdout.write('')
        self.stdout.write(
            self.style.SUCCESS(
                f"📊 Summary: {results['created']} created, "
                f"{results['skipped']} skipped, "
                f"{results['errors']} errors"
            )
        )
        
        if results['errors'] > 0:
            self.stdout.write(
                self.style.WARNING(
                    "⚠️  Some profiles failed to create. Check logs for details."
                )
            )
    
    def _recreate_profiles(self, options):
        """Delete existing profiles and recreate them"""
        self.stdout.write(
            self.style.WARNING('🔄 Recreating benchmark profiles...')
        )
        
        templates_to_process = self._get_templates_to_process(options)
        
        # Delete existing profiles
        profile_names = [
            BenchmarkProfileFactory.PROFILE_TEMPLATES[name]['profile_name']
            for name in templates_to_process
        ]
        
        with transaction.atomic():
            deleted_count, _ = UserProfile.objects.filter(
                profile_name__in=profile_names,
                is_real=False
            ).delete()
            
            if deleted_count > 0:
                self.stdout.write(
                    self.style.WARNING(f"🗑️  Deleted {deleted_count} existing profiles")
                )
            
            # Create new profiles
            self._create_profiles(options)
    
    def _confirm_recreation(self):
        """Confirm profile recreation with user"""
        self.stdout.write(
            self.style.WARNING(
                '⚠️  WARNING: This will delete existing benchmark profiles and recreate them.'
            )
        )
        
        confirm = input('Are you sure you want to continue? (yes/no): ')
        if confirm.lower() not in ['yes', 'y']:
            self.stdout.write('Operation cancelled.')
            sys.exit(0)
    
    def _get_templates_to_process(self, options):
        """Get list of templates to process based on options"""
        if options['template']:
            return [options['template']]
        else:
            return list(BenchmarkProfileFactory.PROFILE_TEMPLATES.keys())
    
    def _validate_prerequisites(self):
        """Validate that required models and services are available"""
        try:
            from apps.user.models import UserProfile
            from apps.main.models import PersonalityTrait
            from apps.main.services.benchmark_profile_factory import BenchmarkProfileFactory
            
            # Test basic operations
            UserProfile.objects.all().count()
            PersonalityTrait.objects.all().count()
            
            return True
        except Exception as e:
            raise CommandError(f"Prerequisites validation failed: {e}")
```

### **1.4 Integration Testing**

**File**: `backend/apps/main/tests/test_benchmark_profile_integration.py`

```python
"""
Integration tests for benchmark profile system
Testing interaction between factory, models, and database

References:
- Django Integration Testing Documentation
- Database Testing Best Practices
- Integration Testing Patterns (Martin Fowler)
"""

from django.test import TransactionTestCase
from django.db import transaction
from django.core.management import call_command
from django.core.management.base import CommandError
from io import StringIO

from apps.main.services.benchmark_profile_factory import BenchmarkProfileFactory
from apps.user.models import UserProfile, UserTraitInclination, UserGoal, Belief
from apps.main.models import PersonalityTrait

class BenchmarkProfileIntegrationTest(TransactionTestCase):
    """
    Integration tests for complete benchmark profile system
    
    Tests the full workflow from factory creation to database persistence
    and management command integration
    """
    
    def setUp(self):
        """Set up integration test environment"""
        self.factory = BenchmarkProfileFactory()
        # Clear any existing test data
        UserProfile.objects.filter(is_real=False).delete()
    
    def test_complete_profile_creation_workflow(self):
        """
        Test complete profile creation workflow including all relationships
        
        Validates:
        - Profile creation with all related objects
        - Database constraint enforcement
        - Relationship integrity
        - Query performance optimization
        """
        # Arrange
        template_name = 'anxious_new_user'
        
        # Act
        with self.assertNumQueries(50):  # Query optimization check
            profile = self.factory.create_benchmark_profile(template_name)
        
        # Assert - Verify complete profile structure
        self.assertIsNotNone(profile.id)
        self.assertFalse(profile.is_real)
        
        # Verify personality traits
        traits = profile.trait_inclinations.select_related('trait').all()
        self.assertEqual(len(traits), 6)  # Big Five + Honesty-Humility
        
        trait_names = {t.trait.name for t in traits}
        expected_traits = {
            'openness', 'conscientiousness', 'extraversion', 
            'agreeableness', 'neuroticism', 'honesty_humility'
        }
        self.assertEqual(trait_names, expected_traits)
        
        # Verify goals
        goals = profile.goals.all()
        self.assertGreaterEqual(len(goals), 3)
        
        # Verify all goals have proper attributes
        for goal in goals:
            self.assertIsNotNone(goal.description)
            self.assertEqual(goal.status, 'active')
            self.assertGreater(goal.priority, 0)
        
        # Verify beliefs
        beliefs = profile.beliefs.all()
        self.assertGreaterEqual(len(beliefs), 3)
        
        # Verify belief categorization
        belief_categories = {b.category for b in beliefs if hasattr(b, 'category')}
        self.assertIn('limiting', belief_categories)  # Should have limiting beliefs
        
        # Verify limitations if they exist
        if hasattr(profile, 'limitations'):
            limitations = profile.limitations.all()
            for limitation in limitations:
                self.assertIsNotNone(limitation.severity)
    
    def test_multiple_profile_creation_isolation(self):
        """
        Test creation of multiple profiles maintains data isolation
        
        Validates:
        - Profiles don't interfere with each other
        - Unique constraint enforcement
        - Proper relationship isolation
        """
        # Arrange
        template_names = ['anxious_new_user', 'confident_adhd_user', 'stressed_professional']
        
        # Act
        profiles = []
        for template_name in template_names:
            profile = self.factory.create_benchmark_profile(template_name)
            profiles.append(profile)
        
        # Assert
        self.assertEqual(len(profiles), 3)
        
        # Verify unique profile names
        profile_names = [p.profile_name for p in profiles]
        self.assertEqual(len(profile_names), len(set(profile_names)))
        
        # Verify trait isolation (no trait sharing between profiles)
        for i, profile in enumerate(profiles):
            traits = profile.trait_inclinations.all()
            self.assertEqual(len(traits), 6)
            
            # Each profile should have unique trait inclination instances
            for trait in traits:
                other_profiles = [p for j, p in enumerate(profiles) if j != i]
                for other_profile in other_profiles:
                    other_trait_ids = set(
                        other_profile.trait_inclinations.values_list('id', flat=True)
                    )
                    self.assertNotIn(trait.id, other_trait_ids)
    
    def test_management_command_integration(self):
        """
        Test integration with Django management command
        
        Validates:
        - Command execution without errors
        - Proper profile creation through command
        - Command output and reporting
        """
        # Arrange
        out = StringIO()
        
        # Act - Execute management command
        call_command('seed_benchmark_profiles', stdout=out)
        
        # Assert
        output = out.getvalue()
        
        # Verify command executed successfully
        self.assertIn('Created:', output)
        
        # Verify profiles were created
        created_profiles = UserProfile.objects.filter(is_real=False)
        self.assertGreater(created_profiles.count(), 0)
        
        # Verify all profiles start with 'Benchmark_'
        for profile in created_profiles:
            self.assertTrue(profile.profile_name.startswith('Benchmark_'))
    
    def test_management_command_dry_run(self):
        """
        Test management command dry run functionality
        
        Validates:
        - Dry run doesn't create profiles
        - Proper preview output
        - No database modifications
        """
        # Arrange
        initial_count = UserProfile.objects.filter(is_real=False).count()
        out = StringIO()
        
        # Act
        call_command('seed_benchmark_profiles', '--dry-run', stdout=out)
        
        # Assert
        output = out.getvalue()
        
        # Verify dry run mode indicated
        self.assertIn('DRY RUN MODE', output)
        
        # Verify no profiles were created
        final_count = UserProfile.objects.filter(is_real=False).count()
        self.assertEqual(initial_count, final_count)
        
        # Verify preview information provided
        self.assertIn('would be created', output)
    
    def test_profile_recreation_workflow(self):
        """
        Test profile recreation functionality
        
        Validates:
        - Existing profiles properly deleted
        - New profiles created with updated data
        - No orphaned relationships
        """
        # Arrange - Create initial profile
        initial_profile = self.factory.create_benchmark_profile('anxious_new_user')
        initial_id = initial_profile.id
        
        # Verify initial creation
        self.assertTrue(
            UserProfile.objects.filter(id=initial_id, is_real=False).exists()
        )
        
        # Act - Recreate profile
        out = StringIO()
        call_command(
            'seed_benchmark_profiles', 
            '--template', 'anxious_new_user',
            '--recreate', 
            '--force',
            stdout=out
        )
        
        # Assert
        output = out.getvalue()
        
        # Verify deletion occurred
        self.assertIn('Deleted', output)
        
        # Verify old profile no longer exists
        self.assertFalse(
            UserProfile.objects.filter(id=initial_id).exists()
        )
        
        # Verify new profile exists
        new_profiles = UserProfile.objects.filter(
            profile_name='Benchmark_Anxious_New_User',
            is_real=False
        )
        self.assertEqual(new_profiles.count(), 1)
        
        new_profile = new_profiles.first()
        self.assertNotEqual(new_profile.id, initial_id)  # Different instance
    
    def test_database_constraint_enforcement(self):
        """
        Test database constraint enforcement
        
        Validates:
        - Foreign key constraints
        - Unique constraints
        - Check constraints
        """
        # Create profile
        profile = self.factory.create_benchmark_profile('anxious_new_user')
        
        # Test trait inclination constraints
        traits = profile.trait_inclinations.all()
        for trait in traits:
            # Verify strength is within valid range (handled by model validation)
            self.assertTrue(0.0 <= trait.strength <= 1.0)
            
            # Verify foreign key relationships
            self.assertIsNotNone(trait.user_profile_id)
            self.assertIsNotNone(trait.trait_id)
        
        # Test goal constraints
        goals = profile.goals.all()
        priorities = [goal.priority for goal in goals]
        
        # Verify priorities are unique and sequential
        self.assertEqual(len(priorities), len(set(priorities)))
        self.assertEqual(min(priorities), 1)
    
    def test_performance_optimization(self):
        """
        Test query performance optimization
        
        Validates:
        - Efficient query patterns
        - No N+1 query problems
        - Proper use of select_related/prefetch_related
        """
        # Create profile
        profile = self.factory.create_benchmark_profile('anxious_new_user')
        
        # Test optimized profile retrieval
        with self.assertNumQueries(4):  # Profile + traits + goals + beliefs
            retrieved_profile = UserProfile.objects.select_related().prefetch_related(
                'trait_inclinations__trait',
                'goals',
                'beliefs'
            ).get(id=profile.id)
            
            # Access all related data (should not generate additional queries)
            trait_count = retrieved_profile.trait_inclinations.count()
            goal_count = retrieved_profile.goals.count()
            belief_count = retrieved_profile.beliefs.count()
            
            self.assertGreater(trait_count, 0)
            self.assertGreater(goal_count, 0)
            self.assertGreater(belief_count, 0)
    
    def test_error_handling_rollback(self):
        """
        Test transaction rollback on errors
        
        Validates:
        - Atomic transaction behavior
        - Proper cleanup on failures
        - No partial profile creation
        """
        initial_count = UserProfile.objects.filter(is_real=False).count()
        
        # Mock a failure during profile creation
        with self.assertRaises(Exception):
            with transaction.atomic():
                # Create profile
                profile = UserProfile.objects.create(
                    profile_name='Test_Failure_Profile',
                    is_real=False
                )
                
                # Simulate failure after partial creation
                raise Exception("Simulated failure")
        
        # Verify rollback occurred
        final_count = UserProfile.objects.filter(is_real=False).count()
        self.assertEqual(initial_count, final_count)
        
        # Verify no partial profile exists
        self.assertFalse(
            UserProfile.objects.filter(
                profile_name='Test_Failure_Profile'
            ).exists()
        )
    
    def tearDown(self):
        """Clean up integration test data"""
        UserProfile.objects.filter(is_real=False).delete()
        PersonalityTrait.objects.filter(category='big_five').delete()
```

## **Quality Assurance Checkpoint: Phase 1**

### **Validation Script**

**File**: `scripts/validate_phase_1.py`

```python
"""
Phase 1 validation script
Ensures benchmark profile factory implementation meets quality standards
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')
django.setup()

from apps.main.services.benchmark_profile_factory import BenchmarkProfileFactory
from apps.user.models import UserProfile

def validate_phase_1():
    """Comprehensive Phase 1 validation"""
    print("🔍 Validating Phase 1 Implementation...")
    
    checks = []
    
    # Check 1: Factory service exists and works
    try:
        factory = BenchmarkProfileFactory()
        templates = factory.list_available_templates()
        checks.append(("✅", "Factory service operational"))
        checks.append(("✅", f"Templates available: {len(templates)}"))
    except Exception as e:
        checks.append(("❌", f"Factory service error: {e}"))
        return checks
    
    # Check 2: Profile creation works
    try:
        profile = factory.create_benchmark_profile('anxious_new_user')
        checks.append(("✅", f"Profile creation successful: {profile.profile_name}"))
        
        # Verify profile attributes
        if not profile.is_real:
            checks.append(("✅", "Profile marked as benchmark (is_real=False)"))
        else:
            checks.append(("❌", "Profile not marked as benchmark"))
            
    except Exception as e:
        checks.append(("❌", f"Profile creation failed: {e}"))
        return checks
    
    # Check 3: Related objects created
    try:
        trait_count = profile.trait_inclinations.count()
        goal_count = profile.goals.count()
        belief_count = profile.beliefs.count()
        
        checks.append(("✅" if trait_count >= 6 else "❌", f"Personality traits: {trait_count}"))
        checks.append(("✅" if goal_count >= 3 else "❌", f"Goals: {goal_count}"))
        checks.append(("✅" if belief_count >= 3 else "❌", f"Beliefs: {belief_count}"))
        
    except Exception as e:
        checks.append(("❌", f"Related objects check failed: {e}"))
    
    # Check 4: Management command exists
    try:
        from django.core.management import get_commands
        commands = get_commands()
        if 'seed_benchmark_profiles' in commands:
            checks.append(("✅", "Management command available"))
        else:
            checks.append(("❌", "Management command not found"))
    except Exception as e:
        checks.append(("❌", f"Management command check failed: {e}"))
    
    # Check 5: Tests exist and can be imported
    try:
        from apps.main.tests.test_benchmark_profile_factory import TestBenchmarkProfileFactory
        checks.append(("✅", "Unit tests available"))
    except ImportError:
        checks.append(("❌", "Unit tests not found"))
    
    return checks

if __name__ == "__main__":
    validation_results = validate_phase_1()
    
    print("\n📋 Phase 1 Validation Results:")
    for status, message in validation_results:
        print(f"   {status} {message}")
    
    failed_checks = [r for r in validation_results if r[0] == "❌"]
    if failed_checks:
        print(f"\n⚠️  {len(failed_checks)} validation(s) failed")
        sys.exit(1)
    else:
        print("\n🎉 Phase 1 validation successful!")
        print("✅ Ready to proceed to Phase 2")
```

### **Execution Instructions**

```bash
# Navigate to project backend
cd /projects/goali1/backend

# Run Phase 1 implementation
echo "🚀 Phase 1: Benchmark Profile Factory Implementation"

# 1. Create service file
mkdir -p apps/main/services
touch apps/main/services/benchmark_profile_factory.py
# (Implement code from above)

# 2. Create unit tests
mkdir -p apps/main/tests
touch apps/main/tests/test_benchmark_profile_factory.py
# (Implement code from above)

# 3. Create management command
mkdir -p apps/main/management/commands
touch apps/main/management/commands/seed_benchmark_profiles.py
# (Implement code from above)

# 4. Create integration tests
touch apps/main/tests/test_benchmark_profile_integration.py
# (Implement code from above)

# 5. Run tests
python manage.py test apps.main.tests.test_benchmark_profile_factory -v 2
python manage.py test apps.main.tests.test_benchmark_profile_integration -v 2

# 6. Validate implementation
python ../scripts/validate_phase_1.py

# 7. Seed benchmark profiles
python manage.py seed_benchmark_profiles --dry-run
python manage.py seed_benchmark_profiles

echo "✅ Phase 1 Complete"
```

**Phase 1 establishes the foundation with comprehensive profile factory implementation following enterprise software engineering standards. This includes proper error handling, transaction management, comprehensive testing, and management command integration.**

---

## **Phase 2: Simple Evaluation Adapter Implementation**

### **2.1 Adapter Service Implementation**

**File**: `backend/apps/main/services/simple_evaluation_adapter.py`

```python
"""
Simple Evaluation Adapter Service Implementation
Implements Adapter Pattern to bridge natural language prompts to existing SemanticEvaluator

References:
- Design Patterns: Elements of Reusable Object-Oriented Software (GoF)
- Enterprise Integration Patterns (Hohpe & Woolf)
- Clean Architecture: A Craftsman's Guide (Robert C. Martin)
"""

import logging
import json
import uuid
from typing import Dict, List, Optional, Any, Union
from decimal import Decimal
from django.core.exceptions import ValidationError
from django.utils import timezone

from apps.main.services.semantic_evaluator import SemanticEvaluator
from apps.main.models import BenchmarkRun, EvaluationCriteria

logger = logging.getLogger(__name__)

class SimpleEvaluationAdapterError(Exception):
    """Custom exception for evaluation adapter operations"""
    pass

class SimpleEvaluationAdapter:
    """
    Adapter service for converting simple natural language evaluation prompts
    to existing SemanticEvaluator criteria format
    
    Implementation follows:
    - Adapter Pattern (GoF) for interface conversion
    - Strategy Pattern for different evaluation approaches
    - Template Method Pattern for evaluation workflow
    - Dependency Injection for SemanticEvaluator integration
    
    Evaluation Framework Integration:
    - Natural language prompt processing
    - Criteria weight validation and normalization
    - Integration with existing evaluation infrastructure
    - Context-aware evaluation parameter adjustment
    """
    
    # Pre-defined evaluation templates for common agent scenarios
    # Based on coaching psychology and agent interaction research
    EVALUATION_TEMPLATES = {
        'mentor_helpfulness': {
            'name': 'Mentor Agent Helpfulness Evaluation',
            'description': 'Evaluates mentor agent responses for helpfulness and appropriateness',
            'evaluation_prompt': """
Evaluate the mentor agent's response for coaching effectiveness:

1. **Helpfulness (0-10)**: How well does the response address the user's specific needs and situation?
   - Consider relevance to user's goals and current challenges
   - Assess practical value and actionability
   - Evaluate appropriateness for user's experience level

2. **Tone Appropriateness (0-10)**: Is the tone supportive, empathetic, and trust-building?
   - Assess warmth and supportiveness
   - Check for appropriate professional boundaries
   - Evaluate empathy and understanding demonstration

3. **Actionability (0-10)**: Does the response provide clear, specific next steps?
   - Assess clarity of suggested actions
   - Evaluate feasibility of recommendations
   - Check for appropriate pacing and difficulty

4. **Trust Building (0-10)**: Does the response build trust and rapport with the user?
   - Assess validation of user's feelings/experiences
   - Evaluate respect for user autonomy
   - Check for consistency with coaching principles

Provide numerical scores (0-10) and brief justifications for each dimension.
Consider the user's psychological profile and current state when evaluating.
            """,
            'criteria_mapping': {
                'helpfulness': 0.3,
                'tone_appropriateness': 0.25,
                'actionability': 0.25,
                'trust_building': 0.2
            },
            'evaluation_context': 'agent_communication',
            'applicable_agents': ['mentor'],
            'psychological_factors': True
        },
        
        'agent_accuracy': {
            'name': 'General Agent Accuracy Evaluation',
            'description': 'Evaluates agent responses for factual accuracy and relevance',
            'evaluation_prompt': """
Evaluate the agent's response for accuracy and relevance:

1. **Factual Accuracy (0-10)**: Are the facts, information, and claims correct?
   - Verify factual statements against known information
   - Assess logical consistency within the response
   - Check for evidence-based recommendations

2. **Relevance (0-10)**: How relevant is the response to the user's situation and request?
   - Assess alignment with user's stated needs
   - Evaluate context-appropriateness
   - Check for addressing the right problem/question

3. **Completeness (0-10)**: Does the response address all aspects of the user's request?
   - Assess coverage of main points
   - Check for addressing underlying concerns
   - Evaluate thoroughness without overwhelming

4. **Clarity (0-10)**: Is the response clear, understandable, and well-organized?
   - Assess language appropriateness for user level
   - Evaluate structure and organization
   - Check for clear communication without jargon

Provide scores and specific justifications referencing the response content.
            """,
            'criteria_mapping': {
                'factual_accuracy': 0.3,
                'relevance': 0.3,
                'completeness': 0.2,
                'clarity': 0.2
            },
            'evaluation_context': 'agent_output',
            'applicable_agents': ['strategy', 'resource', 'ethical', 'psychological'],
            'psychological_factors': False
        },
        
        'creative_agent_quality': {
            'name': 'Creative Agent Output Quality',
            'description': 'Evaluates creative agents for innovation and inspiration',
            'evaluation_prompt': """
Evaluate the creative agent's response for creative quality:

1. **Creativity (0-10)**: How original and innovative are the ideas presented?
   - Assess novelty and uniqueness of suggestions
   - Evaluate creative problem-solving approaches
   - Check for inspiring and imaginative elements

2. **Practical Applicability (0-10)**: Can the creative suggestions be realistically implemented?
   - Assess feasibility within user's constraints
   - Evaluate resource requirements
   - Check for actionable creative steps

3. **Inspiration Value (0-10)**: Does the response inspire and motivate the user?
   - Assess motivational language and energy
   - Evaluate ability to spark enthusiasm
   - Check for encouraging creative confidence

4. **Creative Process Support (0-10)**: Does the response support the user's creative development?
   - Assess guidance for creative skill building
   - Evaluate support for creative risk-taking
   - Check for addressing creative blocks or fears

Consider the user's creative experience level and current creative challenges.
            """,
            'criteria_mapping': {
                'creativity': 0.35,
                'practical_applicability': 0.25,
                'inspiration_value': 0.25,
                'creative_process_support': 0.15
            },
            'evaluation_context': 'creative_output',
            'applicable_agents': ['creative', 'strategy'],
            'psychological_factors': True
        },
        
        'technical_agent_precision': {
            'name': 'Technical Agent Precision Evaluation',
            'description': 'Evaluates technical agents for precision and expertise',
            'evaluation_prompt': """
Evaluate the technical agent's response for precision and expertise:

1. **Technical Accuracy (0-10)**: Are the technical details and procedures correct?
   - Verify technical specifications and methods
   - Assess adherence to best practices
   - Check for accurate use of terminology

2. **Implementation Clarity (0-10)**: Are implementation steps clear and detailed?
   - Assess step-by-step guidance quality
   - Evaluate prerequisite identification
   - Check for potential pitfall warnings

3. **Appropriate Complexity (0-10)**: Is the technical level appropriate for the user?
   - Assess complexity matching user's skill level
   - Evaluate learning curve appropriateness
   - Check for proper scaffolding of concepts

4. **Problem-Solving Approach (0-10)**: Does the response demonstrate systematic problem-solving?
   - Assess logical problem breakdown
   - Evaluate diagnostic reasoning
   - Check for alternative solution consideration

Focus on technical competence and pedagogical effectiveness.
            """,
            'criteria_mapping': {
                'technical_accuracy': 0.4,
                'implementation_clarity': 0.25,
                'appropriate_complexity': 0.2,
                'problem_solving_approach': 0.15
            },
            'evaluation_context': 'technical_output',
            'applicable_agents': ['technical', 'resource'],
            'psychological_factors': False
        }
    }
    
    def __init__(self, semantic_evaluator: Optional[SemanticEvaluator] = None):
        """
        Initialize adapter with optional semantic evaluator dependency injection
        
        Args:
            semantic_evaluator: Optional SemanticEvaluator instance for testing/mocking
        """
        self.semantic_evaluator = semantic_evaluator or SemanticEvaluator()
        logger.info("SimpleEvaluationAdapter initialized")
    
    def evaluate_with_simple_prompt(
        self, 
        agent_response: str,
        evaluation_template: str,
        context: Dict[str, Any],
        benchmark_run: BenchmarkRun
    ) -> Dict[str, Any]:
        """
        Evaluate agent response using simple natural language template
        
        Args:
            agent_response: The agent's response text to evaluate
            evaluation_template: Template key from EVALUATION_TEMPLATES
            context: Additional context for evaluation (user profile, scenario, etc.)
            benchmark_run: Associated benchmark run for result storage
            
        Returns:
            Dict containing evaluation results with scores and analysis
            
        Raises:
            SimpleEvaluationAdapterError: If template not found or evaluation fails
            ValidationError: If input parameters are invalid
        """
        logger.info(
            f"Starting simple evaluation: template={evaluation_template}, "
            f"response_length={len(agent_response)}, run_id={benchmark_run.id}"
        )
        
        # Validate inputs
        self._validate_evaluation_inputs(
            agent_response, evaluation_template, context, benchmark_run
        )
        
        if evaluation_template not in self.EVALUATION_TEMPLATES:
            available_templates = list(self.EVALUATION_TEMPLATES.keys())
            error_msg = (
                f"Unknown evaluation template: '{evaluation_template}'. "
                f"Available templates: {available_templates}"
            )
            logger.error(error_msg)
            raise SimpleEvaluationAdapterError(error_msg)
        
        template = self.EVALUATION_TEMPLATES[evaluation_template]
        
        try:
            # Convert simple template to existing criteria format
            evaluation_criteria = self._convert_to_criteria_format(
                template, context
            )
            
            # Enhance context with template-specific information
            enhanced_context = self._enhance_evaluation_context(
                context, template, benchmark_run
            )
            
            # Use existing SemanticEvaluator with converted criteria
            evaluation_result = self.semantic_evaluator.evaluate(
                response_text=agent_response,
                criteria=evaluation_criteria,
                context=enhanced_context,
                benchmark_run=benchmark_run
            )
            
            # Post-process results with template-specific analysis
            final_result = self._post_process_evaluation_result(
                evaluation_result, template, context
            )
            
            logger.info(
                f"Simple evaluation completed successfully: "
                f"template={evaluation_template}, overall_score={final_result.get('overall_score', 'N/A')}"
            )
            
            return final_result
            
        except Exception as e:
            error_msg = f"Simple evaluation failed for template '{evaluation_template}': {e}"
            logger.error(error_msg, exc_info=True)
            raise SimpleEvaluationAdapterError(error_msg) from e
    
    def create_custom_evaluation(
        self,
        prompt: str,
        criteria_weights: Dict[str, float],
        template_name: Optional[str] = None,
        applicable_agents: Optional[List[str]] = None
    ) -> str:
        """
        Create custom evaluation template from prompt and criteria weights
        
        Args:
            prompt: Natural language evaluation prompt
            criteria_weights: Dictionary mapping criteria names to weights (must sum to 1.0)
            template_name: Optional name for the template (auto-generated if None)
            applicable_agents: Optional list of agent types this applies to
            
        Returns:
            String key for the created template
            
        Raises:
            ValidationError: If criteria weights don't sum to 1.0 or other validation fails
        """
        # Validate weights
        total_weight = sum(criteria_weights.values())
        if abs(total_weight - 1.0) > 0.01:
            raise ValidationError(
                f"Criteria weights must sum to 1.0, got {total_weight:.3f}. "
                f"Weights: {criteria_weights}"
            )
        
        # Validate prompt content
        if not prompt or len(prompt.strip()) < 50:
            raise ValidationError("Evaluation prompt must be at least 50 characters")
        
        # Generate template name if not provided
        if template_name is None:
            template_name = f"custom_{uuid.uuid4().hex[:8]}"
        
        # Validate template name uniqueness
        if template_name in self.EVALUATION_TEMPLATES:
            raise ValidationError(f"Template name '{template_name}' already exists")
        
        # Create template structure
        custom_template = {
            'name': f'Custom Evaluation: {template_name}',
            'description': f'Custom evaluation template created at {timezone.now()}',
            'evaluation_prompt': prompt.strip(),
            'criteria_mapping': criteria_weights,
            'evaluation_context': 'custom',
            'applicable_agents': applicable_agents or [],
            'psychological_factors': 'psychological' in prompt.lower(),
            'created_at': timezone.now().isoformat(),
            'is_custom': True
        }
        
        # Add to templates dictionary
        self.EVALUATION_TEMPLATES[template_name] = custom_template
        
        logger.info(
            f"Created custom evaluation template: {template_name} "
            f"with {len(criteria_weights)} criteria"
        )
        
        return template_name
    
    def get_available_templates(
        self, 
        agent_type: Optional[str] = None,
        include_custom: bool = True
    ) -> List[Dict[str, Any]]:
        """
        Get list of available evaluation templates with metadata
        
        Args:
            agent_type: Optional filter by agent type
            include_custom: Whether to include custom templates
            
        Returns:
            List of template metadata dictionaries
        """
        templates = []
        
        for template_key, template_data in self.EVALUATION_TEMPLATES.items():
            # Filter by agent type if specified
            if agent_type and agent_type not in template_data.get('applicable_agents', []):
                continue
            
            # Filter custom templates if requested
            if not include_custom and template_data.get('is_custom', False):
                continue
            
            template_info = {
                'template_key': template_key,
                'name': template_data['name'],
                'description': template_data['description'],
                'criteria_count': len(template_data['criteria_mapping']),
                'applicable_agents': template_data.get('applicable_agents', []),
                'evaluation_context': template_data.get('evaluation_context', 'general'),
                'psychological_factors': template_data.get('psychological_factors', False),
                'is_custom': template_data.get('is_custom', False)
            }
            
            templates.append(template_info)
        
        # Sort by name for consistent ordering
        templates.sort(key=lambda t: t['name'])
        
        return templates
    
    def _validate_evaluation_inputs(
        self,
        agent_response: str,
        evaluation_template: str, 
        context: Dict[str, Any],
        benchmark_run: BenchmarkRun
    ):
        """
        Validate evaluation inputs following defensive programming principles
        """
        validations = [
            (agent_response, "Agent response cannot be empty"),
            (evaluation_template, "Evaluation template cannot be empty"),
            (isinstance(context, dict), "Context must be a dictionary"),
            (benchmark_run and benchmark_run.id, "Valid benchmark run required")
        ]
        
        for condition, error_message in validations:
            if not condition:
                raise ValidationError(f"Input validation failed: {error_message}")
        
        # Additional content validations
        if len(agent_response.strip()) < 10:
            raise ValidationError("Agent response too short for meaningful evaluation")
        
        if len(agent_response) > 50000:  # Reasonable limit for evaluation
            logger.warning(
                f"Very long agent response ({len(agent_response)} chars) may affect evaluation quality"
            )
    
    def _convert_to_criteria_format(
        self, 
        template: Dict[str, Any], 
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Convert simple template to existing SemanticEvaluator criteria format
        
        Maintains compatibility with existing evaluation infrastructure
        while enabling natural language prompt specification
        """
        # Extract user profile information for context-aware evaluation
        user_profile = context.get('user_profile')
        scenario = context.get('scenario')
        
        # Build criteria dictionary with proper structure
        criteria = {
            'template_name': 'simple_evaluation_adapter',
            'template_version': '1.0',
            'description': template['description'],
            'evaluation_prompt': template['evaluation_prompt'],
            'criteria': template['criteria_mapping'],
            'dimension_weights': template['criteria_mapping'].copy(),
            'evaluation_context': template.get('evaluation_context', 'general'),
            'psychological_factors_enabled': template.get('psychological_factors', False)
        }
        
        # Add context-specific adjustments
        if user_profile and template.get('psychological_factors', False):
            criteria['user_context'] = {
                'stress_level': getattr(user_profile, 'stress_level', 5),
                'mood_valence': float(getattr(user_profile, 'mood_valence', 5.0)),
                'trust_level': getattr(user_profile, 'trust_level', 5),
                'personality_summary': self._summarize_user_personality(user_profile)
            }
        
        if scenario:
            criteria['scenario_context'] = {
                'scenario_type': getattr(scenario, 'scenario_type', 'general'),
                'workflow_type': getattr(scenario, 'workflow_type', 'discussion'),
                'expected_behavior': getattr(scenario, 'expected_behavior', 'appropriate_response')
            }
        
        return criteria
    
    def _enhance_evaluation_context(
        self,
        context: Dict[str, Any],
        template: Dict[str, Any],
        benchmark_run: BenchmarkRun
    ) -> Dict[str, Any]:
        """
        Enhance evaluation context with template-specific information
        """
        enhanced_context = context.copy()
        
        # Add template metadata
        enhanced_context['evaluation_template'] = {
            'name': template['name'],
            'context_type': template.get('evaluation_context', 'general'),
            'psychological_factors': template.get('psychological_factors', False)
        }
        
        # Add benchmark run metadata
        enhanced_context['benchmark_metadata'] = {
            'run_id': str(benchmark_run.id),
            'agent_name': getattr(benchmark_run, 'agent_name', 'unknown'),
            'execution_time': getattr(benchmark_run, 'execution_time', None),
            'token_usage': getattr(benchmark_run, 'total_tokens', None)
        }
        
        # Add timestamp for evaluation tracking
        enhanced_context['evaluation_timestamp'] = timezone.now().isoformat()
        
        return enhanced_context
    
    def _post_process_evaluation_result(
        self,
        evaluation_result: Dict[str, Any],
        template: Dict[str, Any],
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Post-process evaluation results with template-specific analysis
        """
        processed_result = evaluation_result.copy()
        
        # Add template-specific metadata
        processed_result['evaluation_metadata'] = {
            'template_name': template['name'],
            'template_key': next(
                (k for k, v in self.EVALUATION_TEMPLATES.items() if v == template),
                'unknown'
            ),
            'criteria_count': len(template['criteria_mapping']),
            'evaluation_context': template.get('evaluation_context', 'general'),
            'psychological_factors_used': template.get('psychological_factors', False)
        }
        
        # Calculate weighted overall score if not present
        if 'overall_score' not in processed_result and 'dimension_scores' in processed_result:
            overall_score = self._calculate_weighted_score(
                processed_result['dimension_scores'],
                template['criteria_mapping']
            )
            processed_result['overall_score'] = overall_score
        
        # Add interpretation based on score ranges
        if 'overall_score' in processed_result:
            processed_result['score_interpretation'] = self._interpret_evaluation_score(
                processed_result['overall_score'],
                template
            )
        
        # Add improvement suggestions if score is low
        if processed_result.get('overall_score', 10) < 6.0:
            processed_result['improvement_suggestions'] = self._generate_improvement_suggestions(
                processed_result,
                template
            )
        
        return processed_result
    
    def _summarize_user_personality(self, user_profile) -> Dict[str, Any]:
        """
        Create personality summary for evaluation context
        """
        try:
            # Get personality traits if available
            traits = {}
            if hasattr(user_profile, 'trait_inclinations'):
                trait_inclinations = user_profile.trait_inclinations.select_related('trait').all()
                traits = {
                    ti.trait.name: float(ti.strength)
                    for ti in trait_inclinations
                }
            
            # Identify dominant traits (> 0.7) and low traits (< 0.3)
            high_traits = [name for name, value in traits.items() if value >= 0.7]
            low_traits = [name for name, value in traits.items() if value <= 0.3]
            
            return {
                'high_traits': high_traits,
                'low_traits': low_traits,
                'trait_scores': traits,
                'stress_level': getattr(user_profile, 'stress_level', 5),
                'mood_state': {
                    'valence': float(getattr(user_profile, 'mood_valence', 5.0)),
                    'arousal': float(getattr(user_profile, 'mood_arousal', 5.0))
                }
            }
            
        except Exception as e:
            logger.warning(f"Failed to summarize user personality: {e}")
            return {'error': 'personality_summary_failed'}
    
    def _calculate_weighted_score(
        self,
        dimension_scores: Dict[str, float],
        weights: Dict[str, float]
    ) -> float:
        """
        Calculate weighted overall score from dimension scores
        """
        total_score = 0.0
        total_weight = 0.0
        
        for dimension, score in dimension_scores.items():
            weight = weights.get(dimension, 0.0)
            total_score += score * weight
            total_weight += weight
        
        # Normalize if weights don't sum to 1.0
        if total_weight > 0:
            return total_score / total_weight
        else:
            return 0.0
    
    def _interpret_evaluation_score(
        self,
        overall_score: float,
        template: Dict[str, Any]
    ) -> Dict[str, str]:
        """
        Provide interpretation of evaluation scores
        """
        if overall_score >= 8.5:
            level = 'excellent'
            description = 'Outstanding performance meeting or exceeding expectations'
        elif overall_score >= 7.0:
            level = 'good'
            description = 'Good performance with minor areas for improvement'
        elif overall_score >= 5.5:
            level = 'satisfactory'
            description = 'Satisfactory performance with several improvement opportunities'
        elif overall_score >= 4.0:
            level = 'needs_improvement'
            description = 'Performance needs significant improvement'
        else:
            level = 'poor'
            description = 'Poor performance requiring major attention'
        
        return {
            'level': level,
            'description': description,
            'score_range': f"{overall_score:.1f}/10"
        }
    
    def _generate_improvement_suggestions(
        self,
        evaluation_result: Dict[str, Any],
        template: Dict[str, Any]
    ) -> List[str]:
        """
        Generate improvement suggestions based on low scores
        """
        suggestions = []
        dimension_scores = evaluation_result.get('dimension_scores', {})
        
        # Identify lowest scoring dimensions
        low_dimensions = [
            (dim, score) for dim, score in dimension_scores.items()
            if score < 6.0
        ]
        low_dimensions.sort(key=lambda x: x[1])  # Sort by score, lowest first
        
        # Generate specific suggestions based on template type and low dimensions
        template_key = next(
            (k for k, v in self.EVALUATION_TEMPLATES.items() if v == template),
            'general'
        )
        
        if template_key == 'mentor_helpfulness':
            if any(dim == 'helpfulness' for dim, _ in low_dimensions):
                suggestions.append(
                    "Focus on directly addressing the user's specific situation and needs"
                )
            if any(dim == 'tone_appropriateness' for dim, _ in low_dimensions):
                suggestions.append(
                    "Adopt a more empathetic and supportive tone in communications"
                )
            if any(dim == 'actionability' for dim, _ in low_dimensions):
                suggestions.append(
                    "Provide more specific, concrete next steps the user can take"
                )
        
        elif template_key == 'agent_accuracy':
            if any(dim == 'factual_accuracy' for dim, _ in low_dimensions):
                suggestions.append(
                    "Verify factual claims and ensure information accuracy"
                )
            if any(dim == 'relevance' for dim, _ in low_dimensions):
                suggestions.append(
                    "Better align response with the user's specific request and context"
                )
        
        # Generic suggestions if no specific ones apply
        if not suggestions and low_dimensions:
            lowest_dim = low_dimensions[0][0]
            suggestions.append(
                f"Focus on improving {lowest_dim.replace('_', ' ')} in future responses"
            )
        
        return suggestions[:3]  # Limit to top 3 suggestions
```

### **2.2 Unit Testing Implementation**

**File**: `backend/apps/main/tests/test_simple_evaluation_adapter.py`

```python
"""
Comprehensive unit tests for SimpleEvaluationAdapter
Following TDD principles and adapter pattern testing best practices

References:
- Test-Driven Development: By Example (Kent Beck)
- xUnit Test Patterns (Gerard Meszaros)
- Django Testing Documentation
"""

import pytest
import json
from unittest.mock import Mock, patch, MagicMock
from decimal import Decimal
from django.test import TestCase
from django.core.exceptions import ValidationError
from django.utils import timezone

from apps.main.services.simple_evaluation_adapter import (
    SimpleEvaluationAdapter,
    SimpleEvaluationAdapterError
)
from apps.main.models import BenchmarkRun
from apps.user.models import UserProfile

class TestSimpleEvaluationAdapter(TestCase):
    """
    Test suite for SimpleEvaluationAdapter following AAA pattern
    Tests the adapter pattern implementation and integration capabilities
    """
    
    def setUp(self):
        """
        Set up test fixtures with mock dependencies
        Following dependency injection testing patterns
        """
        # Create mock SemanticEvaluator
        self.mock_semantic_evaluator = Mock()
        self.mock_semantic_evaluator.evaluate.return_value = {
            'overall_score': 7.5,
            'dimension_scores': {
                'helpfulness': 8.0,
                'tone_appropriateness': 7.0,
                'actionability': 7.5,
                'trust_building': 7.5
            },
            'evaluation_summary': 'Good response with minor improvements needed'
        }
        
        # Initialize adapter with mock
        self.adapter = SimpleEvaluationAdapter(
            semantic_evaluator=self.mock_semantic_evaluator
        )
        
        # Create test fixtures
        self.test_response = "Thank you for sharing your concerns about social anxiety. I understand this can be challenging. Let's start with some small, manageable steps to build your confidence gradually."
        
        self.mock_benchmark_run = Mock(spec=BenchmarkRun)
        self.mock_benchmark_run.id = 'test-benchmark-123'
        self.mock_benchmark_run.agent_name = 'mentor'
        
        self.test_context = {
            'user_profile': self._create_mock_user_profile(),
            'scenario': self._create_mock_scenario()
        }
    
    def _create_mock_user_profile(self):
        """Create mock user profile for testing"""
        mock_profile = Mock(spec=UserProfile)
        mock_profile.stress_level = 7
        mock_profile.mood_valence = Decimal('4.0')
        mock_profile.mood_arousal = Decimal('6.0')
        mock_profile.trust_level = 5
        
        # Mock trait inclinations
        mock_trait_inclinations = Mock()
        mock_trait_inclinations.select_related.return_value.all.return_value = [
            self._create_mock_trait_inclination('neuroticism', 0.8),
            self._create_mock_trait_inclination('openness', 0.4),
            self._create_mock_trait_inclination('conscientiousness', 0.7)
        ]
        mock_profile.trait_inclinations = mock_trait_inclinations
        
        return mock_profile
    
    def _create_mock_trait_inclination(self, trait_name, strength):
        """Create mock trait inclination"""
        mock_trait = Mock()
        mock_trait.name = trait_name
        
        mock_inclination = Mock()
        mock_inclination.trait = mock_trait
        mock_inclination.strength = Decimal(str(strength))
        
        return mock_inclination
    
    def _create_mock_scenario(self):
        """Create mock scenario for testing"""
        mock_scenario = Mock()
        mock_scenario.scenario_type = 'mentor_interaction'
        mock_scenario.workflow_type = 'discussion'
        mock_scenario.expected_behavior = 'supportive_guidance'
        return mock_scenario
    
    def test_evaluate_with_simple_prompt_success(self):
        """
        Test successful evaluation with simple prompt
        
        Validates:
        - Adapter converts template to criteria format
        - SemanticEvaluator is called with correct parameters
        - Results are properly post-processed
        - Template metadata is included
        """
        # Arrange
        template_name = 'mentor_helpfulness'
        
        # Act
        result = self.adapter.evaluate_with_simple_prompt(
            agent_response=self.test_response,
            evaluation_template=template_name,
            context=self.test_context,
            benchmark_run=self.mock_benchmark_run
        )
        
        # Assert
        self.assertIsInstance(result, dict)
        
        # Verify SemanticEvaluator was called
        self.mock_semantic_evaluator.evaluate.assert_called_once()
        call_args = self.mock_semantic_evaluator.evaluate.call_args
        
        # Verify response text passed correctly
        self.assertEqual(call_args[1]['response_text'], self.test_response)
        
        # Verify criteria format conversion
        criteria = call_args[1]['criteria']
        self.assertIn('template_name', criteria)
        self.assertIn('evaluation_prompt', criteria)
        self.assertIn('criteria', criteria)
        self.assertIn('dimension_weights', criteria)
        
        # Verify context enhancement
        enhanced_context = call_args[1]['context']
        self.assertIn('evaluation_template', enhanced_context)
        self.assertIn('benchmark_metadata', enhanced_context)
        
        # Verify post-processing
        self.assertIn('evaluation_metadata', result)
        self.assertIn('score_interpretation', result)
    
    def test_evaluate_with_invalid_template(self):
        """
        Test error handling for invalid template names
        
        Validates:
        - Proper exception raising for unknown templates
        - Error message includes available templates
        - No calls to SemanticEvaluator on invalid input
        """
        # Arrange
        invalid_template = 'nonexistent_template'
        
        # Act & Assert
        with self.assertRaises(SimpleEvaluationAdapterError) as context:
            self.adapter.evaluate_with_simple_prompt(
                agent_response=self.test_response,
                evaluation_template=invalid_template,
                context=self.test_context,
                benchmark_run=self.mock_benchmark_run
            )
        
        # Verify error message content
        error_message = str(context.exception)
        self.assertIn(invalid_template, error_message)
        self.assertIn('Available templates:', error_message)
        
        # Verify SemanticEvaluator was not called
        self.mock_semantic_evaluator.evaluate.assert_not_called()
    
    def test_input_validation(self):
        """
        Test comprehensive input validation
        
        Validates:
        - Empty agent response rejection
        - Invalid context type rejection
        - Missing benchmark run rejection
        - Too short response rejection
        """
        template_name = 'mentor_helpfulness'
        
        # Test empty agent response
        with self.assertRaises(ValidationError):
            self.adapter.evaluate_with_simple_prompt(
                agent_response="",
                evaluation_template=template_name,
                context=self.test_context,
                benchmark_run=self.mock_benchmark_run
            )
        
        # Test too short response
        with self.assertRaises(ValidationError):
            self.adapter.evaluate_with_simple_prompt(
                agent_response="Hi",
                evaluation_template=template_name,
                context=self.test_context,
                benchmark_run=self.mock_benchmark_run
            )
        
        # Test invalid context type
        with self.assertRaises(ValidationError):
            self.adapter.evaluate_with_simple_prompt(
                agent_response=self.test_response,
                evaluation_template=template_name,
                context="invalid_context",
                benchmark_run=self.mock_benchmark_run
            )
        
        # Test missing benchmark run
        with self.assertRaises(ValidationError):
            self.adapter.evaluate_with_simple_prompt(
                agent_response=self.test_response,
                evaluation_template=template_name,
                context=self.test_context,
                benchmark_run=None
            )
    
    def test_create_custom_evaluation_success(self):
        """
        Test successful custom evaluation template creation
        
        Validates:
        - Custom template creation with valid weights
        - Weight validation (must sum to 1.0)
        - Template storage and retrieval
        - Unique template name enforcement
        """
        # Arrange
        custom_prompt = """
Evaluate the response for custom criteria:
1. Innovation (0-10): How innovative is the response?
2. Practicality (0-10): How practical are the suggestions?
3. Engagement (0-10): How engaging is the communication?
        """
        
        criteria_weights = {
            'innovation': 0.4,
            'practicality': 0.35,
            'engagement': 0.25
        }
        
        # Act
        template_key = self.adapter.create_custom_evaluation(
            prompt=custom_prompt,
            criteria_weights=criteria_weights,
            template_name='custom_innovation_test'
        )
        
        # Assert
        self.assertEqual(template_key, 'custom_innovation_test')
        self.assertIn(template_key, self.adapter.EVALUATION_TEMPLATES)
        
        # Verify template structure
        template = self.adapter.EVALUATION_TEMPLATES[template_key]
        self.assertEqual(template['evaluation_prompt'], custom_prompt.strip())
        self.assertEqual(template['criteria_mapping'], criteria_weights)
        self.assertTrue(template['is_custom'])
    
    def test_create_custom_evaluation_invalid_weights(self):
        """
        Test validation of criteria weights in custom templates
        
        Validates:
        - Weights must sum to 1.0 (within tolerance)
        - Proper error messages for weight validation failures
        - No template creation on validation failure
        """
        custom_prompt = "Test prompt for weight validation"
        
        # Test weights that don't sum to 1.0
        invalid_weights = {
            'criterion1': 0.5,
            'criterion2': 0.3,
            'criterion3': 0.3  # Total = 1.1
        }
        
        with self.assertRaises(ValidationError) as context:
            self.adapter.create_custom_evaluation(
                prompt=custom_prompt,
                criteria_weights=invalid_weights
            )
        
        error_message = str(context.exception)
        self.assertIn('must sum to 1.0', error_message)
        self.assertIn('1.100', error_message)  # Should show actual sum
    
    def test_get_available_templates(self):
        """
        Test template listing functionality
        
        Validates:
        - All predefined templates included
        - Correct template metadata structure
        - Agent type filtering
        - Custom template inclusion/exclusion
        """
        # Test getting all templates
        all_templates = self.adapter.get_available_templates()
        
        self.assertIsInstance(all_templates, list)
        self.assertGreater(len(all_templates), 0)
        
        # Verify template structure
        for template in all_templates:
            required_fields = [
                'template_key', 'name', 'description', 'criteria_count',
                'applicable_agents', 'evaluation_context', 'psychological_factors'
            ]
            for field in required_fields:
                self.assertIn(field, template)
        
        # Test agent type filtering
        mentor_templates = self.adapter.get_available_templates(agent_type='mentor')
        
        # Should have at least mentor_helpfulness template
        mentor_template_keys = [t['template_key'] for t in mentor_templates]
        self.assertIn('mentor_helpfulness', mentor_template_keys)
    
    def test_criteria_format_conversion(self):
        """
        Test conversion of simple templates to SemanticEvaluator format
        
        Validates:
        - Proper criteria structure creation
        - Weight mapping accuracy
        - Context integration
        - Psychological factor handling
        """
        # Arrange
        template = self.adapter.EVALUATION_TEMPLATES['mentor_helpfulness']
        
        # Act
        criteria = self.adapter._convert_to_criteria_format(
            template, self.test_context
        )
        
        # Assert
        self.assertIn('template_name', criteria)
        self.assertIn('evaluation_prompt', criteria)
        self.assertIn('criteria', criteria)
        self.assertIn('dimension_weights', criteria)
        
        # Verify weights are properly mapped
        expected_weights = template['criteria_mapping']
        self.assertEqual(criteria['dimension_weights'], expected_weights)
        
        # Verify psychological context inclusion
        if template.get('psychological_factors', False):
            self.assertIn('user_context', criteria)
            user_context = criteria['user_context']
            self.assertIn('stress_level', user_context)
            self.assertIn('personality_summary', user_context)
    
    def test_context_enhancement(self):
        """
        Test evaluation context enhancement functionality
        
        Validates:
        - Template metadata addition
        - Benchmark run metadata inclusion
        - Timestamp addition
        - Original context preservation
        """
        # Arrange
        template = self.adapter.EVALUATION_TEMPLATES['mentor_helpfulness']
        original_context = self.test_context.copy()
        
        # Act
        enhanced_context = self.adapter._enhance_evaluation_context(
            self.test_context, template, self.mock_benchmark_run
        )
        
        # Assert
        # Verify original context preserved
        for key, value in original_context.items():
            self.assertEqual(enhanced_context[key], value)
        
        # Verify enhancements added
        self.assertIn('evaluation_template', enhanced_context)
        self.assertIn('benchmark_metadata', enhanced_context)
        self.assertIn('evaluation_timestamp', enhanced_context)
        
        # Verify template metadata structure
        template_metadata = enhanced_context['evaluation_template']
        self.assertEqual(template_metadata['name'], template['name'])
        self.assertIn('context_type', template_metadata)
        self.assertIn('psychological_factors', template_metadata)
    
    def test_post_processing_with_low_scores(self):
        """
        Test post-processing with low evaluation scores
        
        Validates:
        - Overall score calculation
        - Score interpretation generation
        - Improvement suggestion generation
        - Metadata addition
        """
        # Arrange - Mock low scores
        low_score_result = {
            'dimension_scores': {
                'helpfulness': 4.0,
                'tone_appropriateness': 5.0,
                'actionability': 3.5,
                'trust_building': 4.5
            }
        }
        
        template = self.adapter.EVALUATION_TEMPLATES['mentor_helpfulness']
        
        # Act
        processed_result = self.adapter._post_process_evaluation_result(
            low_score_result, template, self.test_context
        )
        
        # Assert
        self.assertIn('overall_score', processed_result)
        self.assertIn('score_interpretation', processed_result)
        self.assertIn('improvement_suggestions', processed_result)
        self.assertIn('evaluation_metadata', processed_result)
        
        # Verify overall score calculation
        overall_score = processed_result['overall_score']
        self.assertIsInstance(overall_score, float)
        self.assertLess(overall_score, 6.0)  # Should be low
        
        # Verify score interpretation
        interpretation = processed_result['score_interpretation']
        self.assertIn('level', interpretation)
        self.assertIn('description', interpretation)
        
        # Verify improvement suggestions exist for low scores
        suggestions = processed_result['improvement_suggestions']
        self.assertIsInstance(suggestions, list)
        self.assertGreater(len(suggestions), 0)
    
    def test_personality_summarization(self):
        """
        Test user personality summarization for evaluation context
        
        Validates:
        - Trait extraction from user profile
        - High/low trait identification
        - Mood state integration
        - Error handling for missing data
        """
        # Act
        personality_summary = self.adapter._summarize_user_personality(
            self.test_context['user_profile']
        )
        
        # Assert
        self.assertIsInstance(personality_summary, dict)
        self.assertIn('high_traits', personality_summary)
        self.assertIn('low_traits', personality_summary)
        self.assertIn('trait_scores', personality_summary)
        self.assertIn('mood_state', personality_summary)
        
        # Verify trait categorization
        high_traits = personality_summary['high_traits']
        self.assertIn('neuroticism', high_traits)  # 0.8 should be high
        
        low_traits = personality_summary['low_traits']
        self.assertIn('openness', low_traits)  # 0.4 should be low
        
        # Verify mood state structure
        mood_state = personality_summary['mood_state']
        self.assertIn('valence', mood_state)
        self.assertIn('arousal', mood_state)
    
    @patch('apps.main.services.simple_evaluation_adapter.logger')
    def test_error_handling_and_logging(self, mock_logger):
        """
        Test comprehensive error handling and logging
        
        Validates:
        - Exception propagation and wrapping
        - Appropriate logging on errors
        - Error context preservation
        - Graceful degradation where possible
        """
        # Arrange - Mock SemanticEvaluator to raise exception
        self.mock_semantic_evaluator.evaluate.side_effect = Exception("Test error")
        
        # Act & Assert
        with self.assertRaises(SimpleEvaluationAdapterError) as context:
            self.adapter.evaluate_with_simple_prompt(
                agent_response=self.test_response,
                evaluation_template='mentor_helpfulness',
                context=self.test_context,
                benchmark_run=self.mock_benchmark_run
            )
        
        # Verify error wrapping
        self.assertIn('mentor_helpfulness', str(context.exception))
        self.assertIn('Test error', str(context.exception))
        
        # Verify error logging
        mock_logger.error.assert_called_once()
        error_call = mock_logger.error.call_args[0][0]
        self.assertIn('Simple evaluation failed', error_call)
    
    def tearDown(self):
        """
        Clean up test state
        """
        # Reset adapter templates to original state
        # Remove any custom templates created during testing
        original_templates = {
            k: v for k, v in self.adapter.EVALUATION_TEMPLATES.items()
            if not v.get('is_custom', False)
        }
        self.adapter.EVALUATION_TEMPLATES = original_templates


@pytest.mark.django_db
class TestSimpleEvaluationAdapterPytest:
    """
    Additional pytest-style tests for modern testing patterns
    """
    
    def test_adapter_initialization_without_dependencies(self):
        """Test adapter initialization creates default SemanticEvaluator"""
        adapter = SimpleEvaluationAdapter()
        
        assert adapter.semantic_evaluator is not None
        assert hasattr(adapter.semantic_evaluator, 'evaluate')
    
    @pytest.mark.parametrize('template_name', [
        'mentor_helpfulness',
        'agent_accuracy',
        'creative_agent_quality',
        'technical_agent_precision'
    ])
    def test_all_templates_have_required_fields(self, template_name):
        """Parameterized test ensuring all templates have required structure"""
        adapter = SimpleEvaluationAdapter()
        template = adapter.EVALUATION_TEMPLATES[template_name]
        
        required_fields = [
            'name', 'description', 'evaluation_prompt',
            'criteria_mapping', 'evaluation_context'
        ]
        
        for field in required_fields:
            assert field in template, f"Template {template_name} missing {field}"
        
        # Verify criteria weights sum to 1.0
        total_weight = sum(template['criteria_mapping'].values())
        assert abs(total_weight - 1.0) < 0.01, f"Weights in {template_name} don't sum to 1.0"
    
    def test_weighted_score_calculation_edge_cases(self):
        """Test weighted score calculation with edge cases"""
        adapter = SimpleEvaluationAdapter()
        
        # Test empty scores
        result = adapter._calculate_weighted_score({}, {})
        assert result == 0.0
        
        # Test mismatched dimensions
        dimension_scores = {'a': 5.0, 'b': 7.0}
        weights = {'a': 0.6, 'c': 0.4}  # 'c' not in scores, 'b' not in weights
        result = adapter._calculate_weighted_score(dimension_scores, weights)
        assert result == 3.0  # Only 'a' contributes: 5.0 * 0.6 / 0.6
    
    def test_improvement_suggestions_generation(self):
        """Test improvement suggestions for different template types"""
        adapter = SimpleEvaluationAdapter()
        
        # Test mentor template suggestions
        mentor_template = adapter.EVALUATION_TEMPLATES['mentor_helpfulness']
        evaluation_result = {
            'dimension_scores': {
                'helpfulness': 3.0,  # Low score
                'tone_appropriateness': 8.0,
                'actionability': 5.0,
                'trust_building': 7.0
            }
        }
        
        suggestions = adapter._generate_improvement_suggestions(
            evaluation_result, mentor_template
        )
        
        assert isinstance(suggestions, list)
        assert len(suggestions) <= 3  # Should limit suggestions
        assert any('specific situation' in s.lower() for s in suggestions)  # Should address helpfulness
```

### **2.3 Integration Testing Implementation**

**File**: `backend/apps/main/tests/test_simple_evaluation_integration.py`

```python
"""
Integration tests for Simple Evaluation Adapter with existing systems
Testing integration with SemanticEvaluator and benchmark infrastructure

References:
- Integration Testing Patterns (Martin Fowler)
- Django Integration Testing Documentation
- Test Doubles and Integration Testing (Gerard Meszaros)
"""

from django.test import TestCase, TransactionTestCase
from unittest.mock import Mock, patch
from decimal import Decimal

from apps.main.services.simple_evaluation_adapter import SimpleEvaluationAdapter
from apps.main.services.semantic_evaluator import SemanticEvaluator
from apps.main.services.benchmark_profile_factory import BenchmarkProfileFactory
from apps.user.models import UserProfile
from apps.main.models import BenchmarkRun, BenchmarkScenario, GenericAgent

class SimpleEvaluationIntegrationTest(TransactionTestCase):
    """
    Integration tests for SimpleEvaluationAdapter with real dependencies
    Tests full evaluation workflow with actual SemanticEvaluator integration
    """
    
    def setUp(self):
        """Set up integration test environment with real components"""
        # Create real adapter with real SemanticEvaluator
        self.adapter = SimpleEvaluationAdapter()
        
        # Create benchmark profile for testing
        self.profile_factory = BenchmarkProfileFactory()
        self.test_profile = self.profile_factory.create_benchmark_profile(
            'anxious_new_user'
        )
        
        # Create test agent
        self.test_agent = GenericAgent.objects.create(
            name='test_mentor',
            role='mentor',
            description='Test mentor agent for integration testing',
            is_active=True
        )
        
        # Create test scenario
        self.test_scenario = BenchmarkScenario.objects.create(
            name='Integration_Test_Scenario',
            description='Test scenario for adapter integration',
            agent_role='mentor',
            workflow_type='discussion',
            user_input='I am feeling anxious about social situations',
            expected_behavior='Provide supportive guidance',
            evaluation_focus='response_quality'
        )
        
        # Create test benchmark run
        self.test_benchmark_run = BenchmarkRun.objects.create(
            scenario=self.test_scenario,
            agent_name='test_mentor',
            user_input='I am feeling anxious about social situations',
            agent_output='I understand your anxiety about social situations. This is very common and there are effective ways to manage it. Let\'s start with small steps.',
            execution_time=1.2,
            total_tokens=150,
            cost_estimate=Decimal('0.01')
        )
    
    def test_end_to_end_evaluation_workflow(self):
        """
        Test complete evaluation workflow from adapter to SemanticEvaluator
        
        Validates:
        - Real adapter-evaluator integration
        - Actual criteria conversion and processing
        - Result generation and formatting
        - Database integration
        """
        # Arrange
        test_context = {
            'user_profile': self.test_profile,
            'scenario': self.test_scenario,
            'agent': self.test_agent
        }
        
        agent_response = self.test_benchmark_run.agent_output
        
        # Act - Run evaluation through adapter
        with patch.object(
            self.adapter.semantic_evaluator, 
            'evaluate',
            return_value={
                'overall_score': 7.8,
                'dimension_scores': {
                    'helpfulness': 8.0,
                    'tone_appropriateness': 7.5,
                    'actionability': 7.0,
                    'trust_building': 8.5
                },
                'evaluation_summary': 'Good supportive response with clear empathy',
                'detailed_analysis': {
                    'helpfulness': 'Response directly addresses anxiety concerns',
                    'tone_appropriateness': 'Warm and supportive tone maintained',
                    'actionability': 'Mentions steps but could be more specific',
                    'trust_building': 'Validates user feelings effectively'
                }
            }
        ) as mock_evaluate:
            
            result = self.adapter.evaluate_with_simple_prompt(
                agent_response=agent_response,
                evaluation_template='mentor_helpfulness',
                context=test_context,
                benchmark_run=self.test_benchmark_run
            )
        
        # Assert
        self.assertIsInstance(result, dict)
        
        # Verify SemanticEvaluator was called with correct format
        mock_evaluate.assert_called_once()
        call_args = mock_evaluate.call_args[1]
        
        # Verify criteria conversion worked
        criteria = call_args['criteria']
        self.assertEqual(criteria['template_name'], 'simple_evaluation_adapter')
        self.assertIn('evaluation_prompt', criteria)
        self.assertIn('helpfulness', criteria['criteria'])
        
        # Verify context enhancement
        enhanced_context = call_args['context']
        self.assertIn('evaluation_template', enhanced_context)
        self.assertIn('user_context', enhanced_context)  # Should include psychological factors
        
        # Verify post-processing
        self.assertIn('evaluation_metadata', result)
        self.assertIn('score_interpretation', result)
        self.assertEqual(result['overall_score'], 7.8)
    
    def test_integration_with_benchmark_profile_psychology(self):
        """
        Test integration with psychological data from benchmark profiles
        
        Validates:
        - Personality trait extraction and usage
        - Psychological context integration
        - Stress and mood factor consideration
        - User-specific evaluation adjustments
        """
        # Arrange - Get profile with specific psychological characteristics
        context = {
            'user_profile': self.test_profile,
            'scenario': self.test_scenario
        }
        
        # Act
        with patch.object(
            self.adapter.semantic_evaluator,
            'evaluate',
            return_value={'overall_score': 6.5, 'dimension_scores': {}}
        ) as mock_evaluate:
            
            self.adapter.evaluate_with_simple_prompt(
                agent_response="Test response for psychological integration",
                evaluation_template='mentor_helpfulness',
                context=context,
                benchmark_run=self.test_benchmark_run
            )
        
        # Assert - Verify psychological context was included
        call_args = mock_evaluate.call_args[1]
        criteria = call_args['criteria']
        
        self.assertIn('user_context', criteria)
        user_context = criteria['user_context']
        
        # Verify psychological data inclusion
        self.assertIn('stress_level', user_context)
        self.assertIn('mood_valence', user_context)
        self.assertIn('personality_summary', user_context)
        
        # Verify personality summary structure
        personality = user_context['personality_summary']
        self.assertIn('high_traits', personality)
        self.assertIn('low_traits', personality)
        
        # Verify specific trait inclusion (anxious user should have high neuroticism)
        if 'trait_scores' in personality:
            self.assertIn('neuroticism', personality['trait_scores'])
    
    def test_multiple_template_integration(self):
        """
        Test integration with different evaluation templates
        
        Validates:
        - Template-specific criteria conversion
        - Different evaluation contexts
        - Template metadata preservation
        - Context-appropriate enhancements
        """
        templates_to_test = [
            'mentor_helpfulness',
            'agent_accuracy',
            'creative_agent_quality'
        ]
        
        context = {
            'user_profile': self.test_profile,
            'scenario': self.test_scenario
        }
        
        for template_name in templates_to_test:
            with self.subTest(template=template_name):
                # Arrange
                mock_result = {
                    'overall_score': 7.0,
                    'dimension_scores': {k: 7.0 for k in self.adapter.EVALUATION_TEMPLATES[template_name]['criteria_mapping'].keys()}
                }
                
                # Act
                with patch.object(
                    self.adapter.semantic_evaluator,
                    'evaluate',
                    return_value=mock_result
                ) as mock_evaluate:
                    
                    result = self.adapter.evaluate_with_simple_prompt(
                        agent_response="Test response for template integration",
                        evaluation_template=template_name,
                        context=context,
                        benchmark_run=self.test_benchmark_run
                    )
                
                # Assert
                call_args = mock_evaluate.call_args[1]
                criteria = call_args['criteria']
                
                # Verify template-specific criteria
                template_data = self.adapter.EVALUATION_TEMPLATES[template_name]
                expected_criteria = template_data['criteria_mapping']
                
                self.assertEqual(criteria['criteria'], expected_criteria)
                self.assertEqual(criteria['dimension_weights'], expected_criteria)
                
                # Verify evaluation context
                self.assertEqual(
                    criteria['evaluation_context'],
                    template_data.get('evaluation_context', 'general')
                )
                
                # Verify result metadata
                self.assertIn('evaluation_metadata', result)
                metadata = result['evaluation_metadata']
                self.assertEqual(metadata['template_key'], template_name)
    
    def test_custom_template_integration(self):
        """
        Test integration with custom evaluation templates
        
        Validates:
        - Custom template creation and usage
        - Custom criteria integration with SemanticEvaluator
        - Custom template metadata handling
        - Integration workflow consistency
        """
        # Arrange - Create custom template
        custom_prompt = """
Evaluate the response for custom integration testing:
1. Integration Quality (0-10): How well does the response integrate with the system?
2. Custom Metrics (0-10): How well does it meet custom requirements?
        """
        
        custom_weights = {
            'integration_quality': 0.6,
            'custom_metrics': 0.4
        }
        
        template_key = self.adapter.create_custom_evaluation(
            prompt=custom_prompt,
            criteria_weights=custom_weights,
            template_name='integration_test_custom'
        )
        
        context = {
            'user_profile': self.test_profile,
            'scenario': self.test_scenario
        }
        
        # Act
        with patch.object(
            self.adapter.semantic_evaluator,
            'evaluate',
            return_value={
                'overall_score': 8.0,
                'dimension_scores': {
                    'integration_quality': 8.5,
                    'custom_metrics': 7.5
                }
            }
        ) as mock_evaluate:
            
            result = self.adapter.evaluate_with_simple_prompt(
                agent_response="Test response for custom template",
                evaluation_template=template_key,
                context=context,
                benchmark_run=self.test_benchmark_run
            )
        
        # Assert
        call_args = mock_evaluate.call_args[1]
        criteria = call_args['criteria']
        
        # Verify custom criteria integration
        self.assertEqual(criteria['criteria'], custom_weights)
        self.assertEqual(criteria['evaluation_prompt'], custom_prompt.strip())
        
        # Verify custom template metadata
        self.assertIn('evaluation_metadata', result)
        metadata = result['evaluation_metadata']
        self.assertEqual(metadata['template_key'], template_key)
        self.assertTrue(metadata.get('is_custom', False))
    
    def test_error_propagation_integration(self):
        """
        Test error handling integration across components
        
        Validates:
        - Error propagation from SemanticEvaluator
        - Adapter error handling and wrapping
        - Transaction rollback behavior
        - Error context preservation
        """
        # Arrange
        context = {
            'user_profile': self.test_profile,
            'scenario': self.test_scenario
        }
        
        # Mock SemanticEvaluator to raise an exception
        with patch.object(
            self.adapter.semantic_evaluator,
            'evaluate',
            side_effect=Exception("Integration test error")
        ):
            
            # Act & Assert
            with self.assertRaises(SimpleEvaluationAdapterError) as context_manager:
                self.adapter.evaluate_with_simple_prompt(
                    agent_response="Test error handling",
                    evaluation_template='mentor_helpfulness',
                    context=context,
                    benchmark_run=self.test_benchmark_run
                )
            
            # Verify error context preservation
            error_message = str(context_manager.exception)
            self.assertIn('mentor_helpfulness', error_message)
            self.assertIn('Integration test error', error_message)
    
    def test_performance_integration(self):
        """
        Test performance characteristics of integration
        
        Validates:
        - Reasonable execution time
        - Memory usage patterns
        - Database query optimization
        - Resource cleanup
        """
        import time
        
        # Arrange
        context = {
            'user_profile': self.test_profile,
            'scenario': self.test_scenario
        }
        
        # Mock for consistent timing
        with patch.object(
            self.adapter.semantic_evaluator,
            'evaluate',
            return_value={'overall_score': 7.0, 'dimension_scores': {}}
        ):
            
            # Act - Measure execution time
            start_time = time.time()
            
            result = self.adapter.evaluate_with_simple_prompt(
                agent_response="Performance test response" * 50,  # Longer response
                evaluation_template='mentor_helpfulness',
                context=context,
                benchmark_run=self.test_benchmark_run
            )
            
            execution_time = time.time() - start_time
        
        # Assert
        # Should complete within reasonable time (adjust threshold as needed)
        self.assertLess(execution_time, 5.0, "Evaluation took too long")
        
        # Verify result was generated
        self.assertIsInstance(result, dict)
        self.assertIn('evaluation_metadata', result)
    
    def tearDown(self):
        """Clean up integration test data"""
        # Clean up in reverse order of creation
        BenchmarkRun.objects.filter(id=self.test_benchmark_run.id).delete()
        BenchmarkScenario.objects.filter(id=self.test_scenario.id).delete()
        GenericAgent.objects.filter(id=self.test_agent.id).delete()
        UserProfile.objects.filter(id=self.test_profile.id).delete()

class SimpleEvaluationAdapterRealSemanticEvaluatorTest(TestCase):
    """
    Integration tests using real SemanticEvaluator (if available)
    These tests may be skipped if SemanticEvaluator requires external services
    """
    
    def setUp(self):
        """Set up for real SemanticEvaluator testing"""
        try:
            # Try to create real SemanticEvaluator
            self.real_evaluator = SemanticEvaluator()
            self.adapter = SimpleEvaluationAdapter(self.real_evaluator)
            self.real_evaluator_available = True
        except Exception as e:
            self.real_evaluator_available = False
            self.skipTest(f"Real SemanticEvaluator not available: {e}")
    
    def test_real_semantic_evaluator_integration(self):
        """
        Test integration with real SemanticEvaluator if available
        
        This test may require API keys or external services
        Skip if not available in test environment
        """
        if not self.real_evaluator_available:
            self.skipTest("Real SemanticEvaluator not available")
        
        # Create minimal test setup
        mock_profile = Mock()
        mock_profile.stress_level = 5
        mock_profile.mood_valence = Decimal('5.0')
        mock_profile.trait_inclinations.select_related.return_value.all.return_value = []
        
        mock_benchmark_run = Mock()
        mock_benchmark_run.id = 'real-test-123'
        
        context = {
            'user_profile': mock_profile,
            'scenario': Mock()
        }
        
        # This test might need to be mocked or skipped in CI/CD
        # depending on availability of LLM services
        try:
            result = self.adapter.evaluate_with_simple_prompt(
                agent_response="This is a test response for real integration.",
                evaluation_template='agent_accuracy',
                context=context,
                benchmark_run=mock_benchmark_run
            )
            
            # Basic validation of real result
            self.assertIsInstance(result, dict)
            self.assertIn('evaluation_metadata', result)
            
        except Exception as e:
            # If real evaluator fails, log but don't fail test
            self.skipTest(f"Real SemanticEvaluator integration failed: {e}")
```

## **Quality Assurance Checkpoint: Phase 2**

### **Validation Script**

**File**: `scripts/validate_phase_2.py`

```python
"""
Phase 2 validation script
Ensures Simple Evaluation Adapter implementation meets quality standards
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')
django.setup()

from unittest.mock import Mock
from apps.main.services.simple_evaluation_adapter import SimpleEvaluationAdapter
from apps.main.models import BenchmarkRun

def validate_phase_2():
    """Comprehensive Phase 2 validation"""
    print("🔍 Validating Phase 2 Implementation...")
    
    checks = []
    
    # Check 1: Adapter service exists and initializes
    try:
        adapter = SimpleEvaluationAdapter()
        checks.append(("✅", "Adapter service operational"))
        
        # Check template availability
        templates = adapter.get_available_templates()
        checks.append(("✅", f"Evaluation templates available: {len(templates)}"))
        
    except Exception as e:
        checks.append(("❌", f"Adapter service error: {e}"))
        return checks
    
    # Check 2: Template structure validation
    try:
        for template in templates:
            required_fields = ['template_key', 'name', 'description', 'criteria_count']
            missing_fields = [f for f in required_fields if f not in template]
            if missing_fields:
                checks.append(("❌", f"Template {template['template_key']} missing: {missing_fields}"))
            
        checks.append(("✅", "All templates have required structure"))
        
    except Exception as e:
        checks.append(("❌", f"Template validation failed: {e}"))
    
    # Check 3: Custom template creation
    try:
        custom_key = adapter.create_custom_evaluation(
            prompt="Test custom evaluation: 1. Quality (0-10): How good is it?",
            criteria_weights={'quality': 1.0},
            template_name='validation_test_custom'
        )
        checks.append(("✅", f"Custom template creation works: {custom_key}"))
        
    except Exception as e:
        checks.append(("❌", f"Custom template creation failed: {e}"))
    
    # Check 4: Evaluation workflow (with mocks)
    try:
        mock_semantic_evaluator = Mock()
        mock_semantic_evaluator.evaluate.return_value = {
            'overall_score': 7.5,
            'dimension_scores': {'helpfulness': 7.5}
        }
        
        test_adapter = SimpleEvaluationAdapter(mock_semantic_evaluator)
        
        mock_benchmark_run = Mock(spec=BenchmarkRun)
        mock_benchmark_run.id = 'validation-test-123'
        
        result = test_adapter.evaluate_with_simple_prompt(
            agent_response="Test response for validation",
            evaluation_template='mentor_helpfulness',
            context={'user_profile': Mock(), 'scenario': Mock()},
            benchmark_run=mock_benchmark_run
        )
        
        checks.append(("✅", "Evaluation workflow functional"))
        
        # Verify result structure
        required_result_fields = ['evaluation_metadata', 'score_interpretation']
        missing_result_fields = [f for f in required_result_fields if f not in result]
        if missing_result_fields:
            checks.append(("❌", f"Result missing fields: {missing_result_fields}"))
        else:
            checks.append(("✅", "Evaluation results properly structured"))
        
    except Exception as e:
        checks.append(("❌", f"Evaluation workflow failed: {e}"))
    
    # Check 5: Tests exist and can be imported
    try:
        from apps.main.tests.test_simple_evaluation_adapter import TestSimpleEvaluationAdapter
        checks.append(("✅", "Unit tests available"))
    except ImportError:
        checks.append(("❌", "Unit tests not found"))
    
    try:
        from apps.main.tests.test_simple_evaluation_integration import SimpleEvaluationIntegrationTest
        checks.append(("✅", "Integration tests available"))
    except ImportError:
        checks.append(("❌", "Integration tests not found"))
    
    return checks

if __name__ == "__main__":
    validation_results = validate_phase_2()
    
    print("\n📋 Phase 2 Validation Results:")
    for status, message in validation_results:
        print(f"   {status} {message}")
    
    failed_checks = [r for r in validation_results if r[0] == "❌"]
    if failed_checks:
        print(f"\n⚠️  {len(failed_checks)} validation(s) failed")
        sys.exit(1)
    else:
        print("\n🎉 Phase 2 validation successful!")
        print("✅ Ready to proceed to Phase 3")
```

### **Execution Instructions**

```bash
# Navigate to project backend
cd /projects/goali1/backend

# Run Phase 2 implementation
echo "🚀 Phase 2: Simple Evaluation Adapter Implementation"

# 1. Create adapter service file
touch apps/main/services/simple_evaluation_adapter.py
# (Implement code from above)

# 2. Create unit tests
touch apps/main/tests/test_simple_evaluation_adapter.py
# (Implement code from above)

# 3. Create integration tests
touch apps/main/tests/test_simple_evaluation_integration.py
# (Implement code from above)

# 4. Run unit tests
python manage.py test apps.main.tests.test_simple_evaluation_adapter -v 2

# 5. Run integration tests
python manage.py test apps.main.tests.test_simple_evaluation_integration -v 2

# 6. Validate implementation
python ../scripts/validate_phase_2.py

echo "✅ Phase 2 Complete"
```

**Phase 2 establishes the evaluation adapter with comprehensive template management, custom evaluation creation, and robust integration with the existing SemanticEvaluator infrastructure.**

---

## **Phase 3: Quick Benchmark Service Implementation**

### **3.1 Facade Service Implementation**

**File**: `backend/apps/main/services/quick_benchmark_service.py`

```python
"""
Quick Benchmark Service Implementation
Implements Facade Pattern to orchestrate existing benchmarking infrastructure

References:
- Design Patterns: Elements of Reusable Object-Oriented Software (GoF)
- Enterprise Application Architecture (Martin Fowler)
- Clean Architecture: A Craftsman's Guide (Robert C. Martin)
"""

import logging
import uuid
from typing import Dict, List, Optional, Any
from decimal import Decimal
from django.db import transaction
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.urls import reverse

from apps.user.models import UserProfile
from apps.main.models import (
    BenchmarkRun, BenchmarkScenario, GenericAgent,
    LLMConfig, EvaluationCriteria
)
from apps.main.services.benchmark_service import AgentBenchmarker
from apps.main.services.benchmark_profile_factory import (
    BenchmarkProfileFactory, BenchmarkProfileFactoryError
)
from apps.main.services.simple_evaluation_adapter import (
    SimpleEvaluationAdapter, SimpleEvaluationAdapterError
)

logger = logging.getLogger(__name__)

class QuickBenchmarkServiceError(Exception):
    """Custom exception for quick benchmark service operations"""
    pass

class QuickBenchmarkService:
    """
    Facade service for quick agent benchmarking operations
    
    Implementation follows:
    - Facade Pattern (GoF) for simplified interface to complex subsystem
    - Service Layer Pattern for business logic encapsulation
    - Transaction Script Pattern for workflow orchestration
    - Dependency Injection for testability and flexibility
    
    Orchestration Responsibilities:
    - Coordinate between profile factory, evaluation adapter, and benchmarker
    - Manage benchmark scenario creation and configuration
    - Handle quick benchmark workflow execution
    - Provide simplified API for complex benchmarking operations
    """
    
    def __init__(
        self,
        profile_factory: Optional[BenchmarkProfileFactory] = None,
        evaluation_adapter: Optional[SimpleEvaluationAdapter] = None,
        agent_benchmarker: Optional[AgentBenchmarker] = None
    ):
        """
        Initialize service with dependency injection for all components
        
        Args:
            profile_factory: Optional factory for benchmark profiles
            evaluation_adapter: Optional adapter for simple evaluations
            agent_benchmarker: Optional benchmarker for agent execution
        """
        self.profile_factory = profile_factory or BenchmarkProfileFactory()
        self.evaluation_adapter = evaluation_adapter or SimpleEvaluationAdapter()
        self.agent_benchmarker = agent_benchmarker or AgentBenchmarker()
        
        logger.info("QuickBenchmarkService initialized with all dependencies")
    
    @transaction.atomic
    def run_quick_benchmark(
        self,
        agent_name: str,
        profile_template: str,
        evaluation_template: str,
        scenario_context: Optional[Dict[str, Any]] = None
    ) -> BenchmarkRun:
        """
        Execute complete quick benchmark workflow
        
        This method orchestrates the entire benchmarking process:
        1. Get or create benchmark UserProfile
        2. Validate and retrieve target agent
        3. Create appropriate benchmark scenario
        4. Execute agent benchmark
        5. Perform enhanced evaluation
        6. Store and return results
        
        Args:
            agent_name: Name of the agent to benchmark
            profile_template: Template key for UserProfile creation
            evaluation_template: Template key for evaluation criteria
            scenario_context: Optional context for scenario customization
            
        Returns:
            BenchmarkRun: Completed benchmark run with evaluation results
            
        Raises:
            QuickBenchmarkServiceError: If any step in the workflow fails
            ValidationError: If input parameters are invalid
        """
        logger.info(
            f"Starting quick benchmark: agent={agent_name}, "
            f"profile={profile_template}, evaluation={evaluation_template}"
        )
        
        try:
            # Step 1: Get or create benchmark UserProfile
            user_profile = self._get_or_create_benchmark_profile(profile_template)
            
            # Step 2: Validate and retrieve target agent
            agent = self._get_and_validate_agent(agent_name)
            
            # Step 3: Create benchmark scenario
            scenario = self._create_quick_scenario(
                agent, user_profile, evaluation_template, scenario_context
            )
            
            # Step 4: Execute agent benchmark using existing infrastructure
            benchmark_run = self._execute_agent_benchmark(
                scenario, user_profile, agent
            )
            
            # Step 5: Perform enhanced evaluation with simple adapter
            evaluation_result = self._perform_enhanced_evaluation(
                benchmark_run, evaluation_template, user_profile, scenario
            )
            
            # Step 6: Update benchmark run with evaluation results
            self._finalize_benchmark_run(benchmark_run, evaluation_result)
            
            logger.info(
                f"Quick benchmark completed successfully: {benchmark_run.id}, "
                f"score={evaluation_result.get('overall_score', 'N/A')}"
            )
            
            return benchmark_run
            
        except (BenchmarkProfileFactoryError, SimpleEvaluationAdapterError) as e:
            logger.error(f"Quick benchmark failed at component level: {e}")
            raise QuickBenchmarkServiceError(
                f"Benchmark component error: {e}"
            ) from e
        except Exception as e:
            logger.error(f"Quick benchmark failed with unexpected error: {e}", exc_info=True)
            raise QuickBenchmarkServiceError(
                f"Benchmark execution failed: {e}"
            ) from e
    
    def get_available_options(self) -> Dict[str, Any]:
        """
        Get all available options for quick benchmarking
        
        Returns comprehensive metadata for:
        - Available profile templates
        - Available evaluation templates
        - Available agents
        - System configuration options
        
        Returns:
            Dict containing all available benchmarking options
        """
        try:
            return {
                'profile_templates': self.profile_factory.list_available_templates(),
                'evaluation_templates': self.evaluation_adapter.get_available_templates(),
                'available_agents': self._get_available_agents(),
                'system_info': self._get_system_info(),
                'default_scenario_contexts': self._get_default_scenario_contexts()
            }
        except Exception as e:
            logger.error(f"Failed to get available options: {e}")
            raise QuickBenchmarkServiceError(f"Options retrieval failed: {e}")
    
    def get_benchmark_results_url(self, benchmark_run: BenchmarkRun) -> str:
        """
        Get URL for viewing benchmark results in existing admin interface
        
        Args:
            benchmark_run: The benchmark run to generate URL for
            
        Returns:
            String URL for admin interface viewing
        """
        try:
            return reverse('admin:main_benchmarkrun_change', args=[benchmark_run.pk])
        except Exception as e:
            logger.warning(f"Failed to generate admin URL: {e}")
            return f"/admin/main/benchmarkrun/{benchmark_run.pk}/change/"
    
    def validate_quick_benchmark_request(
        self,
        agent_name: str,
        profile_template: str,
        evaluation_template: str,
        scenario_context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Validate quick benchmark request parameters
        
        Args:
            agent_name: Name of agent to validate
            profile_template: Profile template to validate
            evaluation_template: Evaluation template to validate
            scenario_context: Optional scenario context to validate
            
        Returns:
            Dict containing validation results and any issues found
            
        Raises:
            ValidationError: If critical validation failures occur
        """
        validation_results = {
            'valid': True,
            'errors': [],
            'warnings': [],
            'suggestions': []
        }
        
        # Validate agent exists and is active
        try:
            agent = self._get_and_validate_agent(agent_name)
            validation_results['agent_info'] = {
                'name': agent.name,
                'role': agent.role,
                'is_active': agent.is_active
            }
        except Exception as e:
            validation_results['valid'] = False
            validation_results['errors'].append(f"Agent validation failed: {e}")
        
        # Validate profile template
        try:
            available_templates = [t['template_name'] for t in self.profile_factory.list_available_templates()]
            if profile_template not in available_templates:
                validation_results['valid'] = False
                validation_results['errors'].append(
                    f"Profile template '{profile_template}' not found. Available: {available_templates}"
                )
        except Exception as e:
            validation_results['errors'].append(f"Profile template validation failed: {e}")
        
        # Validate evaluation template
        try:
            available_eval_templates = [t['template_key'] for t in self.evaluation_adapter.get_available_templates()]
            if evaluation_template not in available_eval_templates:
                validation_results['valid'] = False
                validation_results['errors'].append(
                    f"Evaluation template '{evaluation_template}' not found. Available: {available_eval_templates}"
                )
        except Exception as e:
            validation_results['errors'].append(f"Evaluation template validation failed: {e}")
        
        # Validate scenario context if provided
        if scenario_context:
            context_validation = self._validate_scenario_context(scenario_context)
            validation_results['warnings'].extend(context_validation.get('warnings', []))
            validation_results['suggestions'].extend(context_validation.get('suggestions', []))
        
        return validation_results
    
    def _get_or_create_benchmark_profile(self, profile_template: str) -> UserProfile:
        """
        Get or create benchmark profile using factory
        
        Args:
            profile_template: Template name for profile creation
            
        Returns:
            UserProfile: The benchmark profile instance
            
        Raises:
            QuickBenchmarkServiceError: If profile creation fails
        """
        try:
            profile = self.profile_factory.get_or_create_benchmark_profile(
                profile_template
            )
            
            logger.debug(
                f"Using benchmark profile: {profile.profile_name} (ID: {profile.id})"
            )
            
            return profile
            
        except BenchmarkProfileFactoryError as e:
            raise QuickBenchmarkServiceError(
                f"Failed to get/create benchmark profile '{profile_template}': {e}"
            ) from e
    
    def _get_and_validate_agent(self, agent_name: str) -> GenericAgent:
        """
        Get agent by name with comprehensive validation
        
        Args:
            agent_name: Name of the agent to retrieve
            
        Returns:
            GenericAgent: The validated agent instance
            
        Raises:
            QuickBenchmarkServiceError: If agent not found or invalid
        """
        try:
            agent = GenericAgent.objects.select_related('llm_config').get(
                name=agent_name,
                is_active=True
            )
            
            # Additional agent validation
            if not agent.llm_config:
                logger.warning(f"Agent {agent_name} has no LLM configuration")
            
            logger.debug(f"Using agent: {agent.name} (Role: {agent.role})")
            
            return agent
            
        except GenericAgent.DoesNotExist:
            available_agents = list(
                GenericAgent.objects.filter(is_active=True).values_list('name', flat=True)
            )
            raise QuickBenchmarkServiceError(
                f"Agent '{agent_name}' not found or inactive. "
                f"Available agents: {available_agents}"
            )
        except Exception as e:
            raise QuickBenchmarkServiceError(
                f"Agent validation failed for '{agent_name}': {e}"
            ) from e
    
    def _create_quick_scenario(
        self,
        agent: GenericAgent,
        user_profile: UserProfile,
        evaluation_template: str,
        scenario_context: Optional[Dict[str, Any]]
    ) -> BenchmarkScenario:
        """
        Create benchmark scenario optimized for quick benchmarking
        
        Args:
            agent: The agent to benchmark
            user_profile: The benchmark user profile
            evaluation_template: The evaluation template being used
            scenario_context: Optional scenario customization
            
        Returns:
            BenchmarkScenario: Created scenario for benchmarking
        """
        # Generate scenario name with timestamp for uniqueness
        timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')
        scenario_name = f"Quick_{agent.name}_{user_profile.profile_name}_{timestamp}"
        
        # Build scenario data with intelligent defaults
        scenario_data = {
            'name': scenario_name,
            'description': (
                f"Quick benchmark scenario for {agent.name} agent "
                f"with {user_profile.profile_name} profile using "
                f"{evaluation_template} evaluation"
            ),
            'agent_role': agent.role,
            'workflow_type': self._determine_workflow_type(agent, scenario_context),
            'user_input': self._generate_user_input(agent, user_profile, scenario_context),
            'expected_behavior': self._generate_expected_behavior(agent, evaluation_template),
            'evaluation_focus': 'response_quality',
            'scenario_type': 'quick_benchmark',
            'created_by': 'quick_benchmark_service'
        }
        
        # Apply scenario context overrides
        if scenario_context:
            scenario_data.update({
                k: v for k, v in scenario_context.items()
                if k in ['user_input', 'workflow_type', 'expected_behavior']
            })
        
        try:
            scenario = BenchmarkScenario.objects.create(**scenario_data)
            logger.debug(f"Created benchmark scenario: {scenario.name}")
            return scenario
            
        except Exception as e:
            raise QuickBenchmarkServiceError(
                f"Failed to create benchmark scenario: {e}"
            ) from e
    
    def _execute_agent_benchmark(
        self,
        scenario: BenchmarkScenario,
        user_profile: UserProfile,
        agent: GenericAgent
    ) -> BenchmarkRun:
        """
        Execute agent benchmark using existing AgentBenchmarker
        
        Args:
            scenario: The benchmark scenario
            user_profile: The user profile for context
            agent: The agent being benchmarked
            
        Returns:
            BenchmarkRun: The executed benchmark run
        """
        try:
            # Use existing AgentBenchmarker infrastructure
            benchmark_run = self.agent_benchmarker.run_benchmark(
                scenario=scenario,
                user_profile=user_profile,
                agent=agent
            )
            
            logger.debug(
                f"Agent benchmark completed: {benchmark_run.id}, "
                f"execution_time={benchmark_run.execution_time}s"
            )
            
            return benchmark_run
            
        except Exception as e:
            raise QuickBenchmarkServiceError(
                f"Agent benchmark execution failed: {e}"
            ) from e
    
    def _perform_enhanced_evaluation(
        self,
        benchmark_run: BenchmarkRun,
        evaluation_template: str,
        user_profile: UserProfile,
        scenario: BenchmarkScenario
    ) -> Dict[str, Any]:
        """
        Perform enhanced evaluation using SimpleEvaluationAdapter
        
        Args:
            benchmark_run: The completed benchmark run
            evaluation_template: The evaluation template to use
            user_profile: The user profile for context
            scenario: The benchmark scenario for context
            
        Returns:
            Dict containing evaluation results
        """
        if not benchmark_run.agent_output:
            raise QuickBenchmarkServiceError(
                "No agent output available for evaluation"
            )
        
        # Build evaluation context
        evaluation_context = {
            'user_profile': user_profile,
            'scenario': scenario,
            'agent_name': benchmark_run.agent_name,
            'execution_metadata': {
                'execution_time': benchmark_run.execution_time,
                'token_usage': benchmark_run.total_tokens,
                'cost_estimate': float(benchmark_run.cost_estimate or 0)
            }
        }
        
        try:
            evaluation_result = self.evaluation_adapter.evaluate_with_simple_prompt(
                agent_response=benchmark_run.agent_output,
                evaluation_template=evaluation_template,
                context=evaluation_context,
                benchmark_run=benchmark_run
            )
            
            logger.debug(
                f"Enhanced evaluation completed: "
                f"overall_score={evaluation_result.get('overall_score', 'N/A')}"
            )
            
            return evaluation_result
            
        except SimpleEvaluationAdapterError as e:
            raise QuickBenchmarkServiceError(
                f"Enhanced evaluation failed: {e}"
            ) from e
    
    def _finalize_benchmark_run(
        self,
        benchmark_run: BenchmarkRun,
        evaluation_result: Dict[str, Any]
    ):
        """
        Update benchmark run with evaluation results and metadata
        
        Args:
            benchmark_run: The benchmark run to update
            evaluation_result: The evaluation results to store
        """
        try:
            # Store evaluation results
            benchmark_run.semantic_evaluation = evaluation_result
            
            # Update overall score if available
            if 'overall_score' in evaluation_result:
                benchmark_run.overall_score = evaluation_result['overall_score']
            
            # Add quick benchmark metadata
            benchmark_run.metadata = benchmark_run.metadata or {}
            benchmark_run.metadata.update({
                'quick_benchmark': True,
                'evaluation_template': evaluation_result.get('evaluation_metadata', {}).get('template_key'),
                'evaluation_timestamp': timezone.now().isoformat(),
                'service_version': '1.0'
            })
            
            benchmark_run.save()
            
            logger.debug(f"Finalized benchmark run: {benchmark_run.id}")
            
        except Exception as e:
            logger.error(f"Failed to finalize benchmark run: {e}")
            # Don't raise exception here as benchmark succeeded
    
    def _get_available_agents(self) -> List[Dict[str, Any]]:
        """
        Get list of available agents with metadata
        
        Returns:
            List of agent metadata dictionaries
        """
        try:
            agents = GenericAgent.objects.filter(is_active=True).select_related('llm_config')
            
            return [
                {
                    'name': agent.name,
                    'role': agent.role,
                    'description': agent.description,
                    'has_llm_config': bool(agent.llm_config),
                    'llm_model': agent.llm_config.model_name if agent.llm_config else None,
                    'capabilities': getattr(agent, 'capabilities', [])
                }
                for agent in agents
            ]
        except Exception as e:
            logger.error(f"Failed to get available agents: {e}")
            return []
    
    def _get_system_info(self) -> Dict[str, Any]:
        """
        Get system information for quick benchmark options
        
        Returns:
            Dict containing system configuration info
        """
        return {
            'service_version': '1.0',
            'max_response_length': 50000,
            'supported_workflows': ['discussion', 'analysis', 'planning', 'creative'],
            'evaluation_score_range': '0-10',
            'benchmark_retention_days': 90
        }
    
    def _get_default_scenario_contexts(self) -> List[Dict[str, Any]]:
        """
        Get default scenario context templates
        
        Returns:
            List of default scenario context options
        """
        return [
            {
                'name': 'general_discussion',
                'workflow_type': 'discussion',
                'user_input': 'I would like to discuss my current situation and get some guidance.',
                'description': 'General discussion scenario for coaching conversations'
            },
            {
                'name': 'goal_setting',
                'workflow_type': 'planning',
                'user_input': 'I want to set some goals and create a plan to achieve them.',
                'description': 'Goal setting and planning scenario'
            },
            {
                'name': 'problem_solving',
                'workflow_type': 'analysis',
                'user_input': 'I have a specific problem I need help solving.',
                'description': 'Problem-solving and analysis scenario'
            },
            {
                'name': 'creative_exploration',
                'workflow_type': 'creative',
                'user_input': 'I want to explore some creative ideas and possibilities.',
                'description': 'Creative exploration and brainstorming scenario'
            }
        ]
    
    def _determine_workflow_type(
        self,
        agent: GenericAgent,
        scenario_context: Optional[Dict[str, Any]]
    ) -> str:
        """
        Determine appropriate workflow type based on agent and context
        """
        if scenario_context and 'workflow_type' in scenario_context:
            return scenario_context['workflow_type']
        
        # Agent role-based workflow mapping
        role_workflow_map = {
            'mentor': 'discussion',
            'strategy': 'planning',
            'resource': 'analysis',
            'creative': 'creative',
            'ethical': 'analysis',
            'psychological': 'discussion'
        }
        
        return role_workflow_map.get(agent.role, 'discussion')
    
    def _generate_user_input(
        self,
        agent: GenericAgent,
        user_profile: UserProfile,
        scenario_context: Optional[Dict[str, Any]]
    ) -> str:
        """
        Generate appropriate user input based on agent, profile, and context
        """
        if scenario_context and 'user_input' in scenario_context:
            return scenario_context['user_input']
        
        # Profile-based input generation
        profile_name = user_profile.profile_name.lower()
        
        if 'anxious' in profile_name:
            return "I've been feeling quite anxious lately and I'm not sure how to handle it. Can you help me?"
        elif 'adhd' in profile_name:
            return "I have ADHD and I'm struggling with focus and organization. I need some strategies that actually work for me."
        elif 'stressed' in profile_name:
            return "I'm feeling overwhelmed with work and life balance. How can I manage my stress better?"
        elif 'creative' in profile_name:
            return "I want to explore my creativity but I'm not sure where to start. Can you guide me?"
        else:
            return "Hello, I'm looking for some guidance and support. Can you help me?"
    
    def _generate_expected_behavior(
        self,
        agent: GenericAgent,
        evaluation_template: str
    ) -> str:
        """
        Generate expected behavior description based on agent and evaluation template
        """
        role_behaviors = {
            'mentor': 'Provide empathetic, supportive guidance with actionable advice',
            'strategy': 'Offer strategic analysis and planning recommendations',
            'resource': 'Provide accurate information and relevant resources',
            'creative': 'Inspire creativity and provide innovative suggestions',
            'ethical': 'Evaluate ethical considerations and provide balanced perspective',
            'psychological': 'Offer psychologically-informed insights and support'
        }
        
        base_behavior = role_behaviors.get(
            agent.role, 
            'Provide appropriate, helpful response'
        )
        
        # Add evaluation template specific expectations
        if evaluation_template == 'mentor_helpfulness':
            return f"{base_behavior} with focus on helpfulness and trust-building"
        elif evaluation_template == 'agent_accuracy':
            return f"{base_behavior} with emphasis on factual accuracy and relevance"
        elif evaluation_template == 'creative_agent_quality':
            return f"{base_behavior} with creative innovation and practical applicability"
        elif evaluation_template == 'technical_agent_precision':
            return f"{base_behavior} with technical precision and clear implementation guidance"
        else:
            return base_behavior
    
    def _validate_scenario_context(
        self,
        scenario_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Validate scenario context and provide suggestions
        
        Args:
            scenario_context: The scenario context to validate
            
        Returns:
            Dict containing validation warnings and suggestions
        """
        validation_result = {
            'warnings': [],
            'suggestions': []
        }
        
        # Check for overly long user input
        user_input = scenario_context.get('user_input', '')
        if len(user_input) > 500:
            validation_result['warnings'].append(
                "User input is quite long and may affect evaluation quality"
            )
        
        # Check for missing context elements
        recommended_fields = ['user_input', 'workflow_type', 'expected_behavior']
        missing_fields = [f for f in recommended_fields if f not in scenario_context]
        if missing_fields:
            validation_result['suggestions'].append(
                f"Consider adding {', '.join(missing_fields)} for better scenario definition"
            )
        
        # Validate workflow type
        valid_workflows = ['discussion', 'analysis', 'planning', 'creative']
        workflow_type = scenario_context.get('workflow_type')
        if workflow_type and workflow_type not in valid_workflows:
            validation_result['warnings'].append(
                f"Unknown workflow type '{workflow_type}'. Valid types: {valid_workflows}"
            )
        
        return validation_result
```

### **3.2 API Views Implementation**

**File**: `backend/apps/main/views.py` (extend existing file)

```python
"""
Quick Benchmark API Views
Add to existing views.py - RESTful API endpoints for quick benchmarking

References:
- Django REST Framework Best Practices
- RESTful Web Services (Richardson & Ruby)
- API Design Patterns (JJ Geewax)
"""

# Add these imports to existing imports
import json
import logging
from typing import Dict, Any
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.contrib.admin.views.decorators import staff_member_required
from django.core.exceptions import ValidationError
from django.utils.decorators import method_decorator
from django.views import View

from apps.main.services.quick_benchmark_service import (
    QuickBenchmarkService, 
    QuickBenchmarkServiceError
)

logger = logging.getLogger(__name__)

# Add these view classes/functions to existing views.py

@method_decorator([
    staff_member_required,
    csrf_exempt,  # Configure proper CSRF handling for production
], name='dispatch')
class QuickBenchmarkAPIView(View):
    """
    RESTful API view for quick agent benchmarking
    
    Provides simplified interface for executing quick benchmarks
    with comprehensive error handling and response formatting
    """
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.quick_benchmark_service = QuickBenchmarkService()
    
    def post(self, request):
        """
        Execute quick benchmark
        
        Expected JSON payload:
        {
            "agent_name": "mentor",
            "profile_template": "anxious_new_user",
            "evaluation_template": "mentor_helpfulness",
            "scenario_context": {  // Optional
                "user_input": "Custom user input",
                "workflow_type": "discussion"
            }
        }
        
        Returns:
        {
            "success": true,
            "benchmark_run_id": "uuid",
            "admin_url": "/admin/...",
            "results_summary": {
                "overall_score": 7.5,
                "execution_time": 1.2,
                "token_usage": 150,
                "cost_estimate": 0.01
            },
            "evaluation_summary": {
                "score_interpretation": {...},
                "improvement_suggestions": [...]
            }
        }
        """
        try:
            # Parse and validate request data
            request_data = self._parse_request_data(request)
            
            # Validate request parameters
            validation_result = self.quick_benchmark_service.validate_quick_benchmark_request(
                agent_name=request_data['agent_name'],
                profile_template=request_data['profile_template'],
                evaluation_template=request_data['evaluation_template'],
                scenario_context=request_data.get('scenario_context')
            )
            
            if not validation_result['valid']:
                return JsonResponse({
                    'success': False,
                    'error': 'Validation failed',
                    'validation_errors': validation_result['errors'],
                    'warnings': validation_result.get('warnings', [])
                }, status=400)
            
            # Execute quick benchmark
            benchmark_run = self.quick_benchmark_service.run_quick_benchmark(
                agent_name=request_data['agent_name'],
                profile_template=request_data['profile_template'],
                evaluation_template=request_data['evaluation_template'],
                scenario_context=request_data.get('scenario_context')
            )
            
            # Build response with comprehensive information
            response_data = self._build_success_response(
                benchmark_run, validation_result
            )
            
            logger.info(
                f"Quick benchmark API success: {benchmark_run.id} for user {request.user}"
            )
            
            return JsonResponse(response_data, status=201)
            
        except QuickBenchmarkServiceError as e:
            logger.warning(f"Quick benchmark service error: {e}")
            return JsonResponse({
                'success': False,
                'error': 'Benchmark execution failed',
                'message': str(e),
                'error_type': 'service_error'
            }, status=400)
            
        except ValidationError as e:
            logger.warning(f"Quick benchmark validation error: {e}")
            return JsonResponse({
                'success': False,
                'error': 'Invalid request data',
                'message': str(e),
                'error_type': 'validation_error'
            }, status=400)
            
        except Exception as e:
            logger.error(f"Quick benchmark API error: {e}", exc_info=True)
            return JsonResponse({
                'success': False,
                'error': 'Internal server error',
                'message': 'An unexpected error occurred. Please check logs.',
                'error_type': 'internal_error'
            }, status=500)
    
    def _parse_request_data(self, request) -> Dict[str, Any]:
        """
        Parse and validate JSON request data
        
        Args:
            request: Django request object
            
        Returns:
            Dict containing parsed request data
            
        Raises:
            ValidationError: If request data is invalid
        """
        try:
            data = json.loads(request.body)
        except json.JSONDecodeError as e:
            raise ValidationError(f"Invalid JSON in request body: {e}")
        
        # Validate required fields
        required_fields = ['agent_name', 'profile_template', 'evaluation_template']
        missing_fields = [field for field in required_fields if field not in data]
        if missing_fields:
            raise ValidationError(
                f"Missing required fields: {', '.join(missing_fields)}"
            )
        
        # Validate field types
        if not all(isinstance(data[field], str) for field in required_fields):
            raise ValidationError("Required fields must be strings")
        
        # Validate optional scenario_context
        if 'scenario_context' in data and not isinstance(data['scenario_context'], dict):
            raise ValidationError("scenario_context must be a dictionary")
        
        return data
    
    def _build_success_response(
        self, 
        benchmark_run, 
        validation_result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Build comprehensive success response
        
        Args:
            benchmark_run: The completed benchmark run
            validation_result: Validation results from request
            
        Returns:
            Dict containing formatted response data
        """
        # Extract evaluation results
        semantic_evaluation = benchmark_run.semantic_evaluation or {}
        
        response = {
            'success': True,
            'benchmark_run_id': str(benchmark_run.id),
            'admin_url': self.quick_benchmark_service.get_benchmark_results_url(benchmark_run),
            'results_summary': {
                'overall_score': semantic_evaluation.get('overall_score'),
                'execution_time': benchmark_run.execution_time,
                'token_usage': benchmark_run.total_tokens,
                'cost_estimate': float(benchmark_run.cost_estimate) if benchmark_run.cost_estimate else None,
                'agent_response_length': len(benchmark_run.agent_output or '')
            },
            'evaluation_summary': {
                'template_used': semantic_evaluation.get('evaluation_metadata', {}).get('template_key'),
                'score_interpretation': semantic_evaluation.get('score_interpretation'),
                'dimension_scores': semantic_evaluation.get('dimension_scores'),
                'improvement_suggestions': semantic_evaluation.get('improvement_suggestions', [])
            },
            'metadata': {
                'scenario_name': benchmark_run.scenario.name if benchmark_run.scenario else None,
                'profile_used': getattr(benchmark_run, 'user_profile_name', 'unknown'),
                'timestamp': benchmark_run.created_at.isoformat() if benchmark_run.created_at else None,
                'service_version': '1.0'
            }
        }
        
        # Add validation warnings if any
        if validation_result.get('warnings'):
            response['warnings'] = validation_result['warnings']
        
        return response

@staff_member_required
@require_http_methods(["GET"])
def quick_benchmark_options(request):
    """
    Get available options for quick benchmarking
    
    Returns all available templates, agents, and configuration options
    for quick benchmark setup.
    
    Returns:
    {
        "success": true,
        "options": {
            "profile_templates": [...],
            "evaluation_templates": [...],
            "available_agents": [...],
            "system_info": {...},
            "default_scenario_contexts": [...]
        }
    }
    """
    try:
        service = QuickBenchmarkService()
        options = service.get_available_options()
        
        return JsonResponse({
            'success': True,
            'options': options,
            'metadata': {
                'timestamp': timezone.now().isoformat(),
                'user': request.user.username if request.user.is_authenticated else 'anonymous'
            }
        })
        
    except Exception as e:
        logger.error(f"Failed to get benchmark options: {e}", exc_info=True)
        return JsonResponse({
            'success': False,
            'error': 'Failed to retrieve options',
            'message': str(e)
        }, status=500)

@staff_member_required
@require_http_methods(["POST"])
@csrf_exempt
def validate_benchmark_request(request):
    """
    Validate quick benchmark request without executing
    
    Useful for frontend validation before actual benchmark execution.
    
    Expected JSON payload: Same as quick_benchmark_api
    
    Returns:
    {
        "valid": true/false,
        "errors": [...],
        "warnings": [...],
        "suggestions": [...],
        "agent_info": {...}
    }
    """
    try:
        data = json.loads(request.body)
        
        required_fields = ['agent_name', 'profile_template', 'evaluation_template']
        missing_fields = [field for field in required_fields if field not in data]
        if missing_fields:
            return JsonResponse({
                'valid': False,
                'errors': [f"Missing required fields: {', '.join(missing_fields)}"]
            }, status=400)
        
        service = QuickBenchmarkService()
        validation_result = service.validate_quick_benchmark_request(
            agent_name=data['agent_name'],
            profile_template=data['profile_template'],
            evaluation_template=data['evaluation_template'],
            scenario_context=data.get('scenario_context')
        )
        
        return JsonResponse(validation_result)
        
    except json.JSONDecodeError as e:
        return JsonResponse({
            'valid': False,
            'errors': [f"Invalid JSON: {e}"]
        }, status=400)
        
    except Exception as e:
        logger.error(f"Validation request failed: {e}", exc_info=True)
        return JsonResponse({
            'valid': False,
            'errors': [f"Validation failed: {e}"]
        }, status=500)

# Legacy function-based view for backward compatibility
@staff_member_required
@require_http_methods(["POST"])
@csrf_exempt
def quick_benchmark_api(request):
    """
    Legacy function-based API endpoint
    
    Delegates to class-based view for consistency
    """
    view = QuickBenchmarkAPIView()
    return view.post(request)
```

### **3.3 URL Configuration**

**File**: `backend/apps/main/urls.py` (extend existing file)

```python
"""
Add Quick Benchmark URLs to existing URL configuration
"""

# Add these imports to existing imports
from .views import (
    QuickBenchmarkAPIView,
    quick_benchmark_options,
    validate_benchmark_request,
    quick_benchmark_api  # Legacy endpoint
)

# Add these patterns to existing urlpatterns
quick_benchmark_patterns = [
    # Modern class-based API
    path('api/quick-benchmark/', QuickBenchmarkAPIView.as_view(), name='quick_benchmark_api_v2'),
    
    # Support endpoints
    path('api/quick-benchmark/options/', quick_benchmark_options, name='quick_benchmark_options'),
    path('api/quick-benchmark/validate/', validate_benchmark_request, name='validate_benchmark_request'),
    
    # Legacy function-based API for backward compatibility
    path('api/quick-benchmark/legacy/', quick_benchmark_api, name='quick_benchmark_api_legacy'),
]

# Extend existing urlpatterns
urlpatterns = urlpatterns + quick_benchmark_patterns
```

## **Quality Assurance Checkpoint: Phase 3**

### **Validation Script**

**File**: `scripts/validate_phase_3.py`

```python
"""
Phase 3 validation script
Ensures Quick Benchmark Service implementation meets quality standards
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')
django.setup()

from unittest.mock import Mock, patch
from apps.main.services.quick_benchmark_service import QuickBenchmarkService
from apps.main.models import GenericAgent, BenchmarkRun
from apps.user.models import UserProfile

def validate_phase_3():
    """Comprehensive Phase 3 validation"""
    print("🔍 Validating Phase 3 Implementation...")
    
    checks = []
    
    # Check 1: Service exists and initializes
    try:
        service = QuickBenchmarkService()
        checks.append(("✅", "Quick benchmark service operational"))
    except Exception as e:
        checks.append(("❌", f"Service initialization failed: {e}"))
        return checks
    
    # Check 2: Options retrieval works
    try:
        options = service.get_available_options()
        required_option_keys = [
            'profile_templates', 'evaluation_templates', 
            'available_agents', 'system_info'
        ]
        
        missing_keys = [k for k in required_option_keys if k not in options]
        if missing_keys:
            checks.append(("❌", f"Options missing keys: {missing_keys}"))
        else:
            checks.append(("✅", "Options retrieval functional"))
            
        # Check option content
        if len(options.get('profile_templates', [])) > 0:
            checks.append(("✅", f"Profile templates available: {len(options['profile_templates'])}"))
        else:
            checks.append(("❌", "No profile templates found"))
            
    except Exception as e:
        checks.append(("❌", f"Options retrieval failed: {e}"))
    
    # Check 3: Request validation works
    try:
        # Create mock data for validation
        validation_result = service.validate_quick_benchmark_request(
            agent_name="test_agent",
            profile_template="anxious_new_user",
            evaluation_template="mentor_helpfulness"
        )
        
        required_validation_keys = ['valid', 'errors', 'warnings']
        missing_keys = [k for k in required_validation_keys if k not in validation_result]
        if missing_keys:
            checks.append(("❌", f"Validation result missing keys: {missing_keys}"))
        else:
            checks.append(("✅", "Request validation functional"))
            
    except Exception as e:
        checks.append(("❌", f"Request validation failed: {e}"))
    
    # Check 4: URL patterns exist
    try:
        from django.urls import resolve
        from django.urls.exceptions import Resolver404
        
        url_tests = [
            '/api/quick-benchmark/',
            '/api/quick-benchmark/options/',
            '/api/quick-benchmark/validate/'
        ]
        
        url_results = []
        for url in url_tests:
            try:
                resolve(url)
                url_results.append(f"✅ {url}")
            except Resolver404:
                url_results.append(f"❌ {url}")
        
        if all(result.startswith("✅") for result in url_results):
            checks.append(("✅", "All URL patterns configured"))
        else:
            checks.append(("❌", f"URL issues: {[r for r in url_results if r.startswith('❌')]}"))
            
    except Exception as e:
        checks.append(("❌", f"URL validation failed: {e}"))
    
    # Check 5: Mock benchmark execution (without real agent)
    try:
        # Mock all dependencies to test service orchestration
        with patch.object(service, 'profile_factory') as mock_profile_factory, \
             patch.object(service, 'agent_benchmarker') as mock_benchmarker, \
             patch.object(service, 'evaluation_adapter') as mock_evaluator:
            
            # Setup mocks
            mock_profile = Mock(spec=UserProfile)
            mock_profile.id = 'test-profile-123'
            mock_profile.profile_name = 'Test_Profile'
            mock_profile_factory.get_or_create_benchmark_profile.return_value = mock_profile
            
            mock_agent = Mock(spec=GenericAgent)
            mock_agent.name = 'test_agent'
            mock_agent.role = 'mentor'
            
            mock_benchmark_run = Mock(spec=BenchmarkRun)
            mock_benchmark_run.id = 'test-run-123'
            mock_benchmark_run.agent_output = 'Test agent response'
            mock_benchmark_run.execution_time = 1.5
            mock_benchmark_run.total_tokens = 100
            mock_benchmark_run.cost_estimate = 0.01
            mock_benchmarker.run_benchmark.return_value = mock_benchmark_run
            
            mock_evaluation = {
                'overall_score': 7.5,
                'evaluation_metadata': {'template_key': 'mentor_helpfulness'}
            }
            mock_evaluator.evaluate_with_simple_prompt.return_value = mock_evaluation
            
            # Mock GenericAgent.objects.select_related().get()
            with patch('apps.main.models.GenericAgent.objects') as mock_agent_objects:
                mock_agent_objects.select_related.return_value.get.return_value = mock_agent
                
                # Mock BenchmarkScenario.objects.create()
                with patch('apps.main.models.BenchmarkScenario.objects') as mock_scenario_objects:
                    mock_scenario = Mock()
                    mock_scenario.name = 'Test_Scenario'
                    mock_scenario_objects.create.return_value = mock_scenario
                    
                    # This should succeed with mocks
                    result = service.run_quick_benchmark(
                        agent_name="test_agent",
                        profile_template="anxious_new_user",
                        evaluation_template="mentor_helpfulness"
                    )
                    
                    checks.append(("✅", "Service orchestration functional"))
                    
    except Exception as e:
        checks.append(("❌", f"Service orchestration failed: {e}"))
    
    # Check 6: Tests exist
    try:
        from apps.main.tests.test_quick_benchmark_service import TestQuickBenchmarkService
        checks.append(("✅", "Unit tests available"))
    except ImportError:
        checks.append(("❌", "Unit tests not found"))
    
    return checks

if __name__ == "__main__":
    validation_results = validate_phase_3()
    
    print("\n📋 Phase 3 Validation Results:")
    for status, message in validation_results:
        print(f"   {status} {message}")
    
    failed_checks = [r for r in validation_results if r[0] == "❌"]
    if failed_checks:
        print(f"\n⚠️  {len(failed_checks)} validation(s) failed")
        sys.exit(1)
    else:
        print("\n🎉 Phase 3 validation successful!")
        print("✅ Ready to proceed to Phase 4")
```

### **Execution Instructions**

```bash
# Navigate to project backend
cd /projects/goali1/backend

# Run Phase 3 implementation
echo "🚀 Phase 3: Quick Benchmark Service Implementation"

# 1. Create service file
touch apps/main/services/quick_benchmark_service.py
# (Implement code from above)

# 2. Update views.py
# (Add API view classes and functions from above)

# 3. Update urls.py
# (Add URL patterns from above)

# 4. Run quick tests
python manage.py check

# 5. Test URL resolution
python manage.py shell -c "from django.urls import resolve; print('URLs OK' if resolve('/api/quick-benchmark/options/') else 'URL Error')"

# 6. Validate implementation
python ../scripts/validate_phase_3.py

echo "✅ Phase 3 Complete"
```

**Phase 3 establishes the complete service orchestration with RESTful API endpoints, comprehensive validation, and professional error handling.**

Continue to **Phase 4** for final integration, testing, and deployment once Phase 3 validation passes.
