# Phase 3: Simple LLM Evaluation Engine
**Timeline**: 3-4 days

## Step 3.1: Create Simple Evaluation Criteria Model (Day 1)

### Add to `backend/apps/main/models.py`:

```python
class SimpleEvaluationCriteria(models.Model):
    """
    Simple, LLM-friendly evaluation criteria using natural language
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    name = models.CharField(max_length=100, help_text="e.g., 'Mentor_Communication_Trust_Aware'")
    description = models.TextField(help_text="What this criteria evaluates")
    
    # Agent and workflow targeting
    agent_role = models.CharField(
        max_length=50,
        choices=[
            ('mentor', 'Mentor Agent'),
            ('orchestrator', 'Orchestrator Agent'),
            ('resource', 'Resource Agent'),
            ('engagement', 'Engagement Agent'),
            ('psychological', 'Psychological Agent'),
            ('strategy', 'Strategy Agent'),
            ('wheel_activity', 'Wheel/Activity Agent'),
            ('ethical', 'Ethical Agent'),
        ],
        help_text="Which agent this criteria is for"
    )
    workflow_types = models.J<PERSON><PERSON>ield(
        default=list,
        help_text="List of workflow types this applies to, e.g., ['wheel_generation', 'discussion']"
    )
    
    # Simple LLM evaluation prompt
    evaluation_prompt = models.TextField(
        help_text="Natural language instructions for LLM evaluator"
    )
    
    # Context adaptation rules (simple)
    trust_phase_adaptations = models.JSONField(
        default=dict,
        help_text="Simple adaptations based on trust phase: {'foundation': 'extra instruction', 'expansion': '...'}"
    )
    stress_level_adaptations = models.JSONField(
        default=dict,
        help_text="Adaptations based on stress: {'high': 'extra instruction for high stress', 'low': '...'}"
    )
    workflow_stage_adaptations = models.JSONField(
        default=dict,
        help_text="Adaptations based on workflow stage: {'agent_processing': '...', 'result_delivery': '...'}"
    )
    
    # Simple scoring
    scoring_scale = models.CharField(
        max_length=50,
        choices=[
            ('1_to_5', '1-5 Scale'),
            ('1_to_10', '1-10 Scale'),
            ('percentage', 'Percentage (0-100)'),
            ('pass_fail', 'Pass/Fail'),
        ],
        default='1_to_10'
    )
    
    # Metadata
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "Simple Evaluation Criteria"
        verbose_name_plural = "Simple Evaluation Criteria"
    
    def __str__(self):
        return f"{self.name} ({self.agent_role})"
    
    def get_adapted_prompt(self, context: Dict[str, Any]) -> str:
        """
        Get evaluation prompt adapted to the current context
        """
        prompt = self.evaluation_prompt
        
        # Apply trust phase adaptations
        trust_phase = context.get('trust_phase', 'expansion')
        if trust_phase in self.trust_phase_adaptations:
            adaptation = self.trust_phase_adaptations[trust_phase]
            prompt += f"\n\nAdditional consideration for {trust_phase} phase: {adaptation}"
        
        # Apply stress level adaptations
        stress_level = context.get('stress_level', 30)
        if stress_level > 70 and 'high' in self.stress_level_adaptations:
            adaptation = self.stress_level_adaptations['high']
            prompt += f"\n\nAdditional consideration for high stress: {adaptation}"
        elif stress_level < 30 and 'low' in self.stress_level_adaptations:
            adaptation = self.stress_level_adaptations['low']
            prompt += f"\n\nAdditional consideration for low stress: {adaptation}"
        
        # Apply workflow stage adaptations
        workflow_stage = context.get('workflow_stage', 'agent_processing')
        if workflow_stage in self.workflow_stage_adaptations:
            adaptation = self.workflow_stage_adaptations[workflow_stage]
            prompt += f"\n\nAdditional consideration for {workflow_stage} stage: {adaptation}"
        
        return prompt


class AgentEvaluationRun(models.Model):
    """
    Record of an agent evaluation run
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    
    # What was evaluated
    fake_user = models.ForeignKey(FakeUser, on_delete=models.CASCADE, related_name='evaluation_runs')
    variable_context = models.ForeignKey(VariableContext, on_delete=models.CASCADE, related_name='evaluation_runs')
    evaluation_criteria = models.ForeignKey(SimpleEvaluationCriteria, on_delete=models.CASCADE)
    
    # Agent output that was evaluated
    agent_output = models.TextField(help_text="The agent response that was evaluated")
    agent_role = models.CharField(max_length=50, help_text="Role of agent that generated the output")
    
    # Evaluation results
    llm_evaluator_response = models.TextField(help_text="Full LLM evaluator response")
    score = models.FloatField(help_text="Numeric score extracted from LLM response")
    score_scale = models.CharField(max_length=50, help_text="Scale used for scoring")
    
    # Context used for evaluation
    adapted_prompt = models.TextField(help_text="The actual prompt sent to LLM evaluator")
    context_summary = models.JSONField(help_text="Summary of context variables used")
    
    # Metadata
    evaluation_duration_seconds = models.FloatField(null=True, blank=True)
    llm_model_used = models.CharField(max_length=100, default='mistral-small-latest')
    token_usage = models.JSONField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        verbose_name = "Agent Evaluation Run"
        verbose_name_plural = "Agent Evaluation Runs"
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.agent_role} evaluation - {self.score}/{self.score_scale} ({self.created_at.strftime('%Y-%m-%d %H:%M')})"
```

### Run migration:
```bash
python manage.py makemigrations main
python manage.py migrate
```

## Step 3.2: Create Simple Evaluation Service (Day 1-2)

### Create `backend/apps/main/services/simple_evaluation_service.py`:

```python
import logging
import time
import json
import re
from typing import Dict, Any, Optional, List
from django.conf import settings
from apps.main.models import (
    AgentEvaluationRun, 
    SimpleEvaluationCriteria, 
    FakeUser, 
    VariableContext
)
from apps.main.llm.client import get_llm_client

logger = logging.getLogger(__name__)

class SimpleEvaluationService:
    """
    Simple LLM-based evaluation service for individual agents
    """
    
    def __init__(self):
        self.llm_client = get_llm_client()
    
    def evaluate_agent_output(
        self,
        fake_user_id: str,
        variable_context_id: str,
        evaluation_criteria_id: str,
        agent_output: str,
        agent_role: str
    ) -> Dict[str, Any]:
        """
        Evaluate an agent's output using LLM-based evaluation
        """
        start_time = time.time()
        
        try:
            # Get evaluation components
            fake_user = FakeUser.objects.get(id=fake_user_id)
            variable_context = VariableContext.objects.get(id=variable_context_id)
            criteria = SimpleEvaluationCriteria.objects.get(id=evaluation_criteria_id)
            
            # Build evaluation context
            context = self._build_evaluation_context(fake_user, variable_context)
            
            # Get adapted evaluation prompt
            adapted_prompt = criteria.get_adapted_prompt(context)
            
            # Create full evaluation prompt
            full_prompt = self._create_evaluation_prompt(
                agent_output=agent_output,
                agent_role=agent_role,
                evaluation_criteria=adapted_prompt,
                context=context,
                scoring_scale=criteria.scoring_scale
            )
            
            # Send to LLM for evaluation
            llm_response = self._call_llm_evaluator(full_prompt)
            
            # Extract score from LLM response
            score = self._extract_score(llm_response, criteria.scoring_scale)
            
            # Calculate duration
            duration = time.time() - start_time
            
            # Save evaluation run
            evaluation_run = AgentEvaluationRun.objects.create(
                fake_user=fake_user,
                variable_context=variable_context,
                evaluation_criteria=criteria,
                agent_output=agent_output,
                agent_role=agent_role,
                llm_evaluator_response=llm_response,
                score=score,
                score_scale=criteria.scoring_scale,
                adapted_prompt=adapted_prompt,
                context_summary=context,
                evaluation_duration_seconds=duration,
                llm_model_used='mistral-small-latest'  # Or get from settings
            )
            
            logger.info(
                f"Evaluated {agent_role} output: score={score}/{criteria.scoring_scale} "
                f"in {duration:.2f}s"
            )
            
            return {
                'evaluation_run_id': str(evaluation_run.id),
                'score': score,
                'scale': criteria.scoring_scale,
                'llm_response': llm_response,
                'duration_seconds': duration,
                'context_summary': context
            }
            
        except Exception as e:
            logger.error(f"Error in agent evaluation: {e}", exc_info=True)
            return {
                'error': str(e),
                'duration_seconds': time.time() - start_time
            }
    
    def _build_evaluation_context(
        self, 
        fake_user: FakeUser, 
        variable_context: VariableContext
    ) -> Dict[str, Any]:
        """Build evaluation context from user and variable context"""
        return {
            # User context
            'trust_level': variable_context.trust_level,
            'trust_phase': variable_context.trust_phase,
            'user_personality': fake_user.personality_traits,
            'user_limitations': fake_user.user_limitations,
            'user_capabilities': fake_user.user_capabilities,
            'user_goals': fake_user.user_goals,
            'interaction_count': fake_user.interaction_count,
            'completion_rate': fake_user.completion_rate,
            
            # Current state context
            'workflow_type': variable_context.current_workflow_type,
            'workflow_stage': variable_context.workflow_stage,
            'mood_valence': variable_context.mood_valence,
            'mood_arousal': variable_context.mood_arousal,
            'stress_level': variable_context.stress_level,
            'time_pressure': variable_context.time_pressure,
            'environment': variable_context.reported_environment,
            'time_availability': variable_context.reported_time_availability,
            
            # Agent coordination context
            'previous_agent_outputs': variable_context.previous_agent_outputs,
            'expected_next_agents': variable_context.expected_next_agents
        }
    
    def _create_evaluation_prompt(
        self,
        agent_output: str,
        agent_role: str,
        evaluation_criteria: str,
        context: Dict[str, Any],
        scoring_scale: str
    ) -> str:
        """Create the full evaluation prompt for the LLM"""
        
        # Get scoring instructions based on scale
        scoring_instructions = {
            '1_to_5': "Rate from 1 (poor) to 5 (excellent)",
            '1_to_10': "Rate from 1 (poor) to 10 (excellent)", 
            'percentage': "Rate from 0% (poor) to 100% (excellent)",
            'pass_fail': "Rate as either PASS or FAIL"
        }
        
        scale_instruction = scoring_instructions.get(scoring_scale, "Rate from 1 to 10")
        
        prompt = f"""You are evaluating an AI agent's output for quality and appropriateness.

**AGENT BEING EVALUATED:** {agent_role}

**USER CONTEXT:**
- Trust Level: {context['trust_level']}/100 (Phase: {context['trust_phase']})
- Personality Traits: {json.dumps(context['user_personality'], indent=2)}
- User Limitations: {', '.join(context['user_limitations'])}
- User Goals: {', '.join(context['user_goals'])}
- Interaction History: {context['interaction_count']} interactions, {context['completion_rate']:.1%} completion rate

**CURRENT SITUATION:**
- Workflow: {context['workflow_type']} (Stage: {context['workflow_stage']})
- Mood: valence={context['mood_valence']:.1f}, arousal={context['mood_arousal']:.1f}
- Stress Level: {context['stress_level']}/100
- Environment: {context['environment']}
- Time Available: {context['time_availability']}

**AGENT COORDINATION CONTEXT:**
- Previous Agent Outputs: {json.dumps(context['previous_agent_outputs'], indent=2)}
- Expected Next Agents: {', '.join(context['expected_next_agents'])}

**AGENT OUTPUT TO EVALUATE:**
{agent_output}

**EVALUATION CRITERIA:**
{evaluation_criteria}

**SCORING:**
{scale_instruction}

Please provide:
1. A brief analysis of the agent output's quality
2. How well it fits the user context and situation
3. Any concerns or issues identified
4. Your final score: [SCORE]

Format your response with the score clearly marked as [SCORE: X] at the end."""

        return prompt
    
    def _call_llm_evaluator(self, prompt: str) -> str:
        """Call LLM to evaluate the agent output"""
        try:
            response = self.llm_client.chat_completion(
                messages=[{"role": "user", "content": prompt}],
                model="mistral-small-latest",
                max_tokens=1000,
                temperature=0.1  # Low temperature for consistent evaluation
            )
            return response.content
        except Exception as e:
            logger.error(f"LLM evaluation failed: {e}")
            return f"LLM evaluation failed: {str(e)}"
    
    def _extract_score(self, llm_response: str, scoring_scale: str) -> float:
        """Extract numeric score from LLM response"""
        try:
            # Look for [SCORE: X] pattern
            score_pattern = r'\[SCORE:\s*([^\]]+)\]'
            match = re.search(score_pattern, llm_response, re.IGNORECASE)
            
            if match:
                score_text = match.group(1).strip()
                
                if scoring_scale == 'pass_fail':
                    return 1.0 if 'PASS' in score_text.upper() else 0.0
                elif scoring_scale == 'percentage':
                    # Extract percentage
                    percent_match = re.search(r'(\d+)%?', score_text)
                    if percent_match:
                        return float(percent_match.group(1))
                else:
                    # Extract numeric score
                    number_match = re.search(r'(\d+\.?\d*)', score_text)
                    if number_match:
                        return float(number_match.group(1))
            
            # Fallback: look for any number in the response
            numbers = re.findall(r'\b(\d+\.?\d*)\b', llm_response)
            if numbers:
                return float(numbers[-1])  # Take the last number found
            
            logger.warning(f"Could not extract score from: {llm_response}")
            return 0.0
            
        except Exception as e:
            logger.error(f"Score extraction failed: {e}")
            return 0.0
```

## Step 3.3: Seed Simple Evaluation Criteria (Day 2-3)

### Create `backend/apps/main/management/commands/seed_simple_evaluation_criteria.py`:

```python
from django.core.management.base import BaseCommand
from apps.main.models import SimpleEvaluationCriteria

class Command(BaseCommand):
    help = 'Seed simple evaluation criteria for agent evaluation'
    
    def handle(self, *args, **options):
        criteria_data = [
            {
                'name': 'Mentor_Communication_Quality',
                'description': 'Evaluates mentor agent communication quality and user-facing appropriateness',
                'agent_role': 'mentor',
                'workflow_types': ['wheel_generation', 'discussion', 'activity_feedback'],
                'evaluation_prompt': """Evaluate the mentor agent's communication quality based on:

1. **Tone Appropriateness**: Is the tone supportive, empathetic, and trust-building?
2. **Clarity**: Is the message clear, understandable, and actionable?
3. **User Context Awareness**: Does the response show awareness of the user's current state, limitations, and goals?
4. **Trust Building**: Does the response build or maintain trust appropriately for the user's trust phase?
5. **Personalization**: Is the response tailored to the user's personality, preferences, and situation?

Consider the user's trust phase, stress level, and current emotional state when evaluating appropriateness.""",
                'trust_phase_adaptations': {
                    'foundation': 'Pay special attention to safety, reassurance, and avoiding overwhelming the user. Look for extra gentleness and support.',
                    'expansion': 'Look for balanced support with appropriate challenges and growth encouragement.',
                    'integration': 'Expect more sophisticated communication with deeper insights and collaborative tone.'
                },
                'stress_level_adaptations': {
                    'high': 'Look for stress acknowledgment, calming tone, simplified guidance, and immediate support.',
                    'low': 'Expect more detailed guidance and potentially more challenging suggestions.'
                },
                'workflow_stage_adaptations': {
                    'result_delivery': 'Focus on clear presentation of results and next steps.',
                    'user_response': 'Look for appropriate response to user feedback and adaptation.'
                },
                'scoring_scale': '1_to_10'
            },
            
            {
                'name': 'Psychological_Agent_Assessment_Quality',
                'description': 'Evaluates psychological agent assessment accuracy and appropriateness',
                'agent_role': 'psychological',
                'workflow_types': ['wheel_generation', 'activity_feedback'],
                'evaluation_prompt': """Evaluate the psychological agent's assessment quality based on:

1. **Assessment Accuracy**: Are the psychological insights accurate given the user profile and context?
2. **Trust Phase Determination**: Is the trust phase correctly identified and appropriately used?
3. **Challenge Calibration**: Are challenge level recommendations appropriate for the user's current state?
4. **Safety Boundaries**: Are psychological safety boundaries properly identified and respected?
5. **Growth Opportunities**: Are growth opportunities realistically identified and prioritized?
6. **Evidence-Based Reasoning**: Are conclusions well-supported by available user data?

This agent doesn't communicate directly with users, so focus on accuracy and appropriateness of the psychological analysis.""",
                'trust_phase_adaptations': {
                    'foundation': 'Expect more conservative challenge recommendations and stronger safety emphasis.',
                    'expansion': 'Look for balanced growth opportunities with appropriate challenge levels.',
                    'integration': 'Expect sophisticated insights and readiness for complex growth challenges.'
                },
                'stress_level_adaptations': {
                    'high': 'Assessment should recognize stress impact and recommend stress-aware activities.',
                    'low': 'Assessment can include more challenging growth opportunities.'
                },
                'scoring_scale': '1_to_10'
            },
            
            {
                'name': 'Strategy_Agent_Synthesis_Quality',
                'description': 'Evaluates strategy agent synthesis and decision-making quality',
                'agent_role': 'strategy',
                'workflow_types': ['wheel_generation'],
                'evaluation_prompt': """Evaluate the strategy agent's synthesis quality based on:

1. **Multi-Agent Integration**: How well does the strategy integrate inputs from resource, engagement, and psychological agents?
2. **Domain Distribution**: Is the domain distribution appropriate for the user's goals, preferences, and current state?
3. **Challenge Calibration**: Are challenge levels appropriately balanced across activities?
4. **Gap Analysis**: Does the strategy identify and address gaps between user traits and goals?
5. **Context Awareness**: Does the strategy appropriately consider current mood, stress, time availability?
6. **Growth Alignment**: Are strategic decisions aligned with the user's growth trajectory and trust phase?

This agent synthesizes complex inputs into actionable strategy, so focus on integration quality and strategic thinking.""",
                'trust_phase_adaptations': {
                    'foundation': 'Strategy should prioritize safety and positive experiences over rapid growth.',
                    'expansion': 'Look for balanced growth strategies with measured challenge increases.',
                    'integration': 'Expect sophisticated strategies that leverage user strengths for growth.'
                },
                'workflow_stage_adaptations': {
                    'agent_processing': 'Focus on integration of previous agent outputs and strategic reasoning.'
                },
                'scoring_scale': '1_to_10'
            },
            
            {
                'name': 'Resource_Agent_Feasibility_Analysis',
                'description': 'Evaluates resource agent feasibility analysis accuracy',
                'agent_role': 'resource',
                'workflow_types': ['wheel_generation'],
                'evaluation_prompt': """Evaluate the resource agent's feasibility analysis based on:

1. **Environmental Analysis**: Is the environmental assessment accurate and comprehensive?
2. **Time Analysis**: Is time availability correctly parsed and categorized?
3. **Resource Availability**: Are user resources and limitations accurately identified?
4. **Constraint Identification**: Are primary constraints correctly identified and prioritized?
5. **Opportunity Recognition**: Are key opportunities for enhanced activities properly highlighted?
6. **Confidence Scoring**: Are confidence scores realistic and well-calibrated?

This agent provides foundational feasibility analysis, so focus on accuracy and completeness of resource assessment.""",
                'stress_level_adaptations': {
                    'high': 'Should recognize stress as a limiting factor and adjust feasibility accordingly.',
                    'low': 'Can provide more optimistic feasibility assessments for challenging activities.'
                },
                'scoring_scale': '1_to_10'
            }
        ]
        
        for criteria_data in criteria_data:
            criteria = SimpleEvaluationCriteria.objects.create(**criteria_data)
            self.stdout.write(
                self.style.SUCCESS(f"Created evaluation criteria: {criteria.name}")
            )
        
        self.stdout.write(
            self.style.SUCCESS(f"Successfully seeded {len(criteria_data)} evaluation criteria")
        )
```

### Run seeding:
```bash
python manage.py seed_simple_evaluation_criteria
```

## Step 3.4: Test Evaluation Engine (Day 3-4)

### Create `backend/apps/main/tests/test_simple_evaluation_service.py`:

```python
from django.test import TestCase
from unittest.mock import Mock, patch
from apps.main.services.simple_evaluation_service import SimpleEvaluationService
from apps.main.services.fake_user_service import FakeUserService
from apps.main.services.variable_context_service import VariableContextService
from apps.main.models import SimpleEvaluationCriteria, AgentEvaluationRun

class TestSimpleEvaluationService(TestCase):
    def setUp(self):
        self.evaluation_service = SimpleEvaluationService()
        self.user_service = FakeUserService()
        self.context_service = VariableContextService()
        
        # Create test user
        self.fake_user = self.user_service.create_fake_user({
            'name': 'Test_Eval_User',
            'description': 'User for evaluation testing',
            'trust_level': 45,
            'personality_traits': {'openness': 0.6, 'neuroticism': 0.7},
            'user_limitations': ['social_anxiety'],
            'user_goals': ['build_confidence']
        })
        
        # Create test context
        self.context = self.context_service.create_context({
            'name': 'Test_Evaluation_Context',
            'description': 'Context for evaluation testing',
            'current_workflow_type': 'wheel_generation',
            'workflow_stage': 'result_delivery',
            'agent_role_being_evaluated': 'mentor',
            'trust_level': 45,
            'mood_valence': -0.2,
            'stress_level': 60
        })
        
        # Create test criteria
        self.criteria = SimpleEvaluationCriteria.objects.create(
            name='Test_Mentor_Criteria',
            description='Test criteria for mentor evaluation',
            agent_role='mentor',
            workflow_types=['wheel_generation'],
            evaluation_prompt='Evaluate the mentor response for clarity and supportiveness.',
            scoring_scale='1_to_10'
        )
    
    @patch('apps.main.services.simple_evaluation_service.get_llm_client')
    def test_evaluate_agent_output(self, mock_llm_client):
        # Mock LLM response
        mock_client = Mock()
        mock_response = Mock()
        mock_response.content = """The mentor response is clear and supportive. 
        It appropriately addresses the user's anxiety and provides gentle guidance.
        The tone is encouraging and builds trust effectively.
        [SCORE: 8]"""
        mock_client.chat_completion.return_value = mock_response
        mock_llm_client.return_value = mock_client
        
        # Test agent output
        agent_output = "I understand you're feeling anxious about trying new activities. Let's start with something gentle that aligns with your interests. Here's a wheel with some calming, creative activities that might feel manageable right now."
        
        # Run evaluation
        result = self.evaluation_service.evaluate_agent_output(
            fake_user_id=str(self.fake_user.id),
            variable_context_id=str(self.context.id),
            evaluation_criteria_id=str(self.criteria.id),
            agent_output=agent_output,
            agent_role='mentor'
        )
        
        # Check results
        self.assertIn('score', result)
        self.assertEqual(result['score'], 8.0)
        self.assertEqual(result['scale'], '1_to_10')
        self.assertIn('evaluation_run_id', result)
        
        # Check that evaluation run was saved
        evaluation_run = AgentEvaluationRun.objects.get(
            id=result['evaluation_run_id']
        )
        self.assertEqual(evaluation_run.score, 8.0)
        self.assertEqual(evaluation_run.agent_role, 'mentor')
        self.assertEqual(evaluation_run.fake_user, self.fake_user)
    
    def test_extract_score_various_formats(self):
        # Test score extraction with different formats
        test_cases = [
            ("[SCORE: 7]", '1_to_10', 7.0),
            ("[SCORE: 85%]", 'percentage', 85.0),
            ("[SCORE: PASS]", 'pass_fail', 1.0),
            ("[SCORE: FAIL]", 'pass_fail', 0.0),
            ("Overall rating: 6.5 out of 10", '1_to_10', 6.5),
            ("No clear score format", '1_to_10', 0.0)
        ]
        
        for llm_response, scale, expected_score in test_cases:
            with self.subTest(response=llm_response, scale=scale):
                score = self.evaluation_service._extract_score(llm_response, scale)
                self.assertEqual(score, expected_score)
    
    def test_adapted_prompt_generation(self):
        # Test that prompts are adapted based on context
        context = {
            'trust_phase': 'foundation',
            'stress_level': 80,
            'workflow_stage': 'result_delivery'
        }
        
        # Add adaptations to criteria
        self.criteria.trust_phase_adaptations = {
            'foundation': 'Look for extra gentleness and reassurance'
        }
        self.criteria.stress_level_adaptations = {
            'high': 'Look for stress acknowledgment and calming tone'
        }
        self.criteria.save()
        
        adapted_prompt = self.criteria.get_adapted_prompt(context)
        
        self.assertIn('extra gentleness', adapted_prompt)
        self.assertIn('stress acknowledgment', adapted_prompt)
```

### Run tests:
```bash
python manage.py test apps.main.tests.test_simple_evaluation_service
```

**Phase 3 Complete**: You now have a simple LLM-based evaluation engine that can evaluate individual agent outputs with context-aware criteria.
