# Phase 1: Fake User Management System
**Timeline**: 2-3 days

## Step 1.1: Create Fake User Models (Day 1)

### Create `backend/apps/main/models.py` additions:

```python
class FakeUser(models.Model):
    """
    Fake user for benchmarking with pre-defined realistic profile and trajectory
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    name = models.CharField(max_length=100, help_text="Human-readable name like 'Stressed_ADHD_User'")
    description = models.TextField(help_text="Description of user archetype and trajectory")
    
    # User profile data (pre-populated realistic data)
    trust_level = models.IntegerField(default=50, validators=[MinValueValidator(0), MaxValueValidator(100)])
    personality_traits = models.JSONField(default=dict, help_text="HEXACO traits as dict")
    user_limitations = models.JSONField(default=list, help_text="List of limitations")
    user_capabilities = models.J<PERSON><PERSON>ield(default=list, help_text="List of capabilities")
    user_goals = models.J<PERSON><PERSON><PERSON>(default=list, help_text="List of goals and aspirations")
    user_beliefs = models.J<PERSON><PERSON><PERSON>(default=list, help_text="Core beliefs")
    
    # Fake trajectory data (simulated history)
    interaction_count = models.IntegerField(default=0, help_text="Simulated number of interactions")
    completion_rate = models.FloatField(default=0.7, help_text="Simulated activity completion rate")
    trust_trajectory = models.CharField(max_length=50, default="stable", help_text="building, stable, declining")
    last_activities = models.JSONField(default=list, help_text="Last few activities completed")
    
    # State management
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        verbose_name = "Fake User"
        verbose_name_plural = "Fake Users"
    
    def __str__(self):
        return f"{self.name} (Trust: {self.trust_level})"


class FakeUserSnapshot(models.Model):
    """
    Snapshot of fake user state for restoration after benchmarking
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    fake_user = models.ForeignKey(FakeUser, on_delete=models.CASCADE, related_name='snapshots')
    snapshot_name = models.CharField(max_length=100, help_text="e.g., 'original_state', 'before_evaluation_X'")
    
    # Snapshot data
    user_state_data = models.JSONField(help_text="Complete user state as JSON")
    trust_level = models.IntegerField()
    interaction_count = models.IntegerField()
    completion_rate = models.FloatField()
    last_activities = models.JSONField()
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        verbose_name = "Fake User Snapshot"
        unique_together = [['fake_user', 'snapshot_name']]
```

### Run migrations:
```bash
cd /projects/goali1/backend
python manage.py makemigrations main
python manage.py migrate
```

## Step 1.2: Create Fake User Service (Day 1)

### Create `backend/apps/main/services/fake_user_service.py`:

```python
import json
import logging
from typing import Dict, Any, List, Optional
from django.db import transaction
from apps.main.models import FakeUser, FakeUserSnapshot

logger = logging.getLogger(__name__)

class FakeUserService:
    """
    Service for managing fake users and their state snapshots
    """
    
    def create_fake_user(self, user_data: Dict[str, Any]) -> FakeUser:
        """Create a new fake user with realistic data"""
        fake_user = FakeUser.objects.create(**user_data)
        
        # Create initial snapshot
        self.create_snapshot(fake_user, "original_state")
        
        logger.info(f"Created fake user: {fake_user.name}")
        return fake_user
    
    def create_snapshot(self, fake_user: FakeUser, snapshot_name: str) -> FakeUserSnapshot:
        """Create snapshot of current fake user state"""
        snapshot_data = {
            'trust_level': fake_user.trust_level,
            'personality_traits': fake_user.personality_traits,
            'user_limitations': fake_user.user_limitations,
            'user_capabilities': fake_user.user_capabilities,
            'user_goals': fake_user.user_goals,
            'user_beliefs': fake_user.user_beliefs,
            'interaction_count': fake_user.interaction_count,
            'completion_rate': fake_user.completion_rate,
            'trust_trajectory': fake_user.trust_trajectory,
            'last_activities': fake_user.last_activities,
        }
        
        snapshot = FakeUserSnapshot.objects.update_or_create(
            fake_user=fake_user,
            snapshot_name=snapshot_name,
            defaults={
                'user_state_data': snapshot_data,
                'trust_level': fake_user.trust_level,
                'interaction_count': fake_user.interaction_count,
                'completion_rate': fake_user.completion_rate,
                'last_activities': fake_user.last_activities,
            }
        )[0]
        
        logger.info(f"Created snapshot '{snapshot_name}' for {fake_user.name}")
        return snapshot
    
    def restore_snapshot(self, fake_user: FakeUser, snapshot_name: str = "original_state") -> bool:
        """Restore fake user to a previous snapshot state"""
        try:
            snapshot = FakeUserSnapshot.objects.get(
                fake_user=fake_user, 
                snapshot_name=snapshot_name
            )
            
            # Restore state from snapshot
            state_data = snapshot.user_state_data
            for field, value in state_data.items():
                setattr(fake_user, field, value)
            
            fake_user.save()
            
            logger.info(f"Restored {fake_user.name} to snapshot '{snapshot_name}'")
            return True
            
        except FakeUserSnapshot.DoesNotExist:
            logger.error(f"Snapshot '{snapshot_name}' not found for {fake_user.name}")
            return False
    
    def get_fake_user_for_benchmark(self, user_id: str) -> Optional[FakeUser]:
        """Get fake user by ID, ensuring it's ready for benchmarking"""
        try:
            fake_user = FakeUser.objects.get(id=user_id, is_active=True)
            
            # Create pre-benchmark snapshot if not exists
            snapshot_name = f"before_benchmark_{fake_user.interaction_count}"
            self.create_snapshot(fake_user, snapshot_name)
            
            return fake_user
        except FakeUser.DoesNotExist:
            logger.error(f"Fake user {user_id} not found")
            return None
    
    def list_fake_users(self) -> List[Dict[str, Any]]:
        """List all active fake users with basic info"""
        users = FakeUser.objects.filter(is_active=True).order_by('name')
        return [
            {
                'id': str(user.id),
                'name': user.name,
                'description': user.description,
                'trust_level': user.trust_level,
                'trust_trajectory': user.trust_trajectory,
                'interaction_count': user.interaction_count,
                'completion_rate': user.completion_rate,
                'snapshots_count': user.snapshots.count()
            }
            for user in users
        ]
```

## Step 1.3: Create Fake User Seeding (Day 2)

### Create `backend/apps/main/management/commands/seed_fake_users.py`:

```python
from django.core.management.base import BaseCommand
from apps.main.services.fake_user_service import FakeUserService

class Command(BaseCommand):
    help = 'Seed fake users for benchmarking'
    
    def handle(self, *args, **options):
        service = FakeUserService()
        
        fake_users_data = [
            {
                'name': 'New_Anxious_User',
                'description': 'First-time user with anxiety, low trust, needs reassurance',
                'trust_level': 25,
                'personality_traits': {
                    'openness': 0.4,
                    'conscientiousness': 0.7,
                    'extraversion': 0.3,
                    'agreeableness': 0.8,
                    'neuroticism': 0.8,
                    'honesty_humility': 0.7
                },
                'user_limitations': ['social_anxiety', 'time_pressure_sensitivity', 'needs_reassurance'],
                'user_capabilities': ['analytical_thinking', 'detail_oriented', 'empathetic'],
                'user_goals': ['reduce_anxiety', 'build_confidence', 'improve_social_skills'],
                'user_beliefs': ['I am not good enough', 'Others judge me harshly'],
                'interaction_count': 3,
                'completion_rate': 0.4,
                'trust_trajectory': 'building',
                'last_activities': ['breathing_exercise', 'journaling']
            },
            {
                'name': 'Confident_ADHD_User',
                'description': 'Experienced user with ADHD, high trust, likes challenges',
                'trust_level': 82,
                'personality_traits': {
                    'openness': 0.9,
                    'conscientiousness': 0.4,
                    'extraversion': 0.7,
                    'agreeableness': 0.6,
                    'neuroticism': 0.3,
                    'honesty_humility': 0.6
                },
                'user_limitations': ['attention_span_variability', 'needs_novelty'],
                'user_capabilities': ['creative_thinking', 'high_energy', 'adaptable'],
                'user_goals': ['channel_creativity', 'improve_focus', 'complete_projects'],
                'user_beliefs': ['I can figure things out', 'Challenges are opportunities'],
                'interaction_count': 47,
                'completion_rate': 0.73,
                'trust_trajectory': 'stable',
                'last_activities': ['creative_writing', 'dance_session', 'organize_space']
            },
            {
                'name': 'Stressed_Professional',
                'description': 'Busy professional, moderate trust, time-constrained',
                'trust_level': 55,
                'personality_traits': {
                    'openness': 0.6,
                    'conscientiousness': 0.9,
                    'extraversion': 0.5,
                    'agreeableness': 0.5,
                    'neuroticism': 0.6,
                    'honesty_humility': 0.8
                },
                'user_limitations': ['time_constraints', 'high_stress', 'perfectionism'],
                'user_capabilities': ['organized', 'goal_oriented', 'efficient'],
                'user_goals': ['manage_stress', 'work_life_balance', 'efficiency'],
                'user_beliefs': ['Hard work pays off', 'I must do everything perfectly'],
                'interaction_count': 15,
                'completion_rate': 0.6,
                'trust_trajectory': 'stable',
                'last_activities': ['quick_meditation', 'stretching']
            }
        ]
        
        for user_data in fake_users_data:
            service.create_fake_user(user_data)
            self.stdout.write(
                self.style.SUCCESS(f"Created fake user: {user_data['name']}")
            )
        
        self.stdout.write(
            self.style.SUCCESS(f"Successfully seeded {len(fake_users_data)} fake users")
        )
```

### Run seeding:
```bash
python manage.py seed_fake_users
```

## Step 1.4: Test Fake User System (Day 2-3)

### Create `backend/apps/main/tests/test_fake_user_service.py`:

```python
from django.test import TestCase
from apps.main.services.fake_user_service import FakeUserService
from apps.main.models import FakeUser, FakeUserSnapshot

class TestFakeUserService(TestCase):
    def setUp(self):
        self.service = FakeUserService()
        self.user_data = {
            'name': 'Test_User',
            'description': 'Test user for unit tests',
            'trust_level': 50,
            'personality_traits': {'openness': 0.5},
            'interaction_count': 10,
            'completion_rate': 0.7
        }
    
    def test_create_fake_user(self):
        user = self.service.create_fake_user(self.user_data)
        self.assertEqual(user.name, 'Test_User')
        self.assertEqual(user.trust_level, 50)
        
        # Check original snapshot was created
        self.assertTrue(
            FakeUserSnapshot.objects.filter(
                fake_user=user, 
                snapshot_name='original_state'
            ).exists()
        )
    
    def test_snapshot_and_restore(self):
        user = self.service.create_fake_user(self.user_data)
        
        # Modify user
        user.trust_level = 75
        user.interaction_count = 20
        user.save()
        
        # Restore to original
        restored = self.service.restore_snapshot(user, 'original_state')
        self.assertTrue(restored)
        
        user.refresh_from_db()
        self.assertEqual(user.trust_level, 50)
        self.assertEqual(user.interaction_count, 10)
```

### Run tests:
```bash
python manage.py test apps.main.tests.test_fake_user_service
```

**Phase 1 Complete**: You now have fake users with state management and snapshot/restore capabilities.
