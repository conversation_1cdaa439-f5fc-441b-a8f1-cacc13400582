# Phase 2: Variable Context & State Management
**Timeline**: 2-3 days

## Step 2.1: Create Variable Context Model (Day 1)

### Add to `backend/apps/main/models.py`:

```python
class VariableContext(models.Model):
    """
    Current moment state for evaluation - simple and focused
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    name = models.CharField(max_length=100, help_text="e.g., 'Stressed_Evening_WheelGen', 'Calm_Morning_Discussion'")
    description = models.TextField(help_text="Description of this context scenario")
    
    # Current workflow context (the critical missing piece!)
    current_workflow_type = models.CharField(
        max_length=50, 
        choices=[
            ('wheel_generation', 'Wheel Generation'),
            ('activity_feedback', 'Activity Feedback'),
            ('discussion', 'Discussion'),
            ('onboarding', 'Onboarding'),
            ('post_spin', 'Post Spin'),
            ('pre_spin_feedback', 'Pre Spin Feedback'),
        ],
        help_text="Current workflow being executed"
    )
    workflow_stage = models.CharField(
        max_length=50,
        choices=[
            ('initiation', 'Workflow Initiation'),
            ('agent_processing', 'Multi-Agent Processing'),
            ('result_delivery', 'Result Delivery'),
            ('user_response', 'Awaiting User Response'),
            ('error_recovery', 'Error Recovery'),
        ],
        default='agent_processing',
        help_text="Current stage in workflow"
    )
    agent_role_being_evaluated = models.CharField(
        max_length=50,
        choices=[
            ('mentor', 'Mentor Agent'),
            ('orchestrator', 'Orchestrator Agent'),
            ('resource', 'Resource Agent'),
            ('engagement', 'Engagement Agent'),
            ('psychological', 'Psychological Agent'),
            ('strategy', 'Strategy Agent'),
            ('wheel_activity', 'Wheel/Activity Agent'),
            ('ethical', 'Ethical Agent'),
        ],
        help_text="Which agent is being evaluated"
    )
    
    # User state variables (from existing system)
    trust_level = models.IntegerField(
        default=50, 
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text="Current trust level (0-100)"
    )
    mood_valence = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(-1.0), MaxValueValidator(1.0)],
        help_text="Mood valence (-1.0 negative to 1.0 positive)"
    )
    mood_arousal = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(-1.0), MaxValueValidator(1.0)],
        help_text="Mood arousal (-1.0 calm to 1.0 energetic)"
    )
    
    # Environmental context (from context packet)
    reported_environment = models.CharField(
        max_length=50,
        choices=[
            ('home', 'Home'),
            ('work', 'Work'),
            ('public', 'Public Space'),
            ('transit', 'In Transit'),
        ],
        default='home'
    )
    stress_level = models.IntegerField(
        default=30,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text="Current stress level (0-100)"
    )
    time_pressure = models.IntegerField(
        default=30,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text="Current time pressure (0-100)"
    )
    reported_time_availability = models.CharField(
        max_length=50,
        choices=[
            ('5_minutes', '5 minutes'),
            ('15_minutes', '15 minutes'),
            ('30_minutes', '30 minutes'),
            ('60_minutes', '60+ minutes'),
        ],
        default='30_minutes'
    )
    reported_focus = models.CharField(
        max_length=100,
        default='general',
        help_text="e.g., 'creative', 'physical', 'social', 'wellness'"
    )
    
    # Agent coordination context
    previous_agent_outputs = models.JSONField(
        default=list,
        help_text="Simulated outputs from previous agents in workflow"
    )
    expected_next_agents = models.JSONField(
        default=list,
        help_text="Which agents should be called after this one"
    )
    
    # State management
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        verbose_name = "Variable Context"
        verbose_name_plural = "Variable Contexts"
    
    def __str__(self):
        return f"{self.name} ({self.current_workflow_type} - {self.agent_role_being_evaluated})"
    
    @property
    def trust_phase(self) -> str:
        """Calculate trust phase from trust level"""
        if self.trust_level < 40:
            return "foundation"
        elif self.trust_level < 70:
            return "expansion"
        else:
            return "integration"
    
    def to_context_packet(self) -> Dict[str, Any]:
        """Convert to context packet format for agent evaluation"""
        return {
            'workflow_type': self.current_workflow_type,
            'workflow_stage': self.workflow_stage,
            'trust_level': self.trust_level,
            'trust_phase': self.trust_phase,
            'mood': {
                'valence': self.mood_valence,
                'arousal': self.mood_arousal
            },
            'environment': {
                'type': self.reported_environment,
                'stress_level': self.stress_level,
                'time_pressure': self.time_pressure
            },
            'time_availability': self.reported_time_availability,
            'reported_focus': self.reported_focus,
            'agent_context': {
                'previous_outputs': self.previous_agent_outputs,
                'expected_next': self.expected_next_agents
            }
        }
```

### Run migration:
```bash
python manage.py makemigrations main
python manage.py migrate
```

## Step 2.2: Create Variable Context Service (Day 1-2)

### Create `backend/apps/main/services/variable_context_service.py`:

```python
import logging
from typing import Dict, Any, List, Optional
from apps.main.models import VariableContext, FakeUser

logger = logging.getLogger(__name__)

class VariableContextService:
    """
    Service for managing variable contexts (current moment states)
    """
    
    def create_context(self, context_data: Dict[str, Any]) -> VariableContext:
        """Create a new variable context"""
        context = VariableContext.objects.create(**context_data)
        logger.info(f"Created variable context: {context.name}")
        return context
    
    def get_context_for_evaluation(
        self, 
        context_id: str, 
        fake_user: FakeUser
    ) -> Dict[str, Any]:
        """
        Get complete context for agent evaluation, combining variable context with fake user
        """
        try:
            context = VariableContext.objects.get(id=context_id, is_active=True)
            
            # Combine variable context with fake user data
            evaluation_context = {
                # From variable context (current moment)
                'variable_context': context.to_context_packet(),
                'agent_role': context.agent_role_being_evaluated,
                'workflow_type': context.current_workflow_type,
                'workflow_stage': context.workflow_stage,
                
                # From fake user (profile & trajectory)
                'user_profile': {
                    'user_id': str(fake_user.id),
                    'trust_level': context.trust_level,  # Use context trust level
                    'personality_traits': fake_user.personality_traits,
                    'limitations': fake_user.user_limitations,
                    'capabilities': fake_user.user_capabilities,
                    'goals': fake_user.user_goals,
                    'beliefs': fake_user.user_beliefs,
                    'interaction_count': fake_user.interaction_count,
                    'completion_rate': fake_user.completion_rate,
                    'trust_trajectory': fake_user.trust_trajectory,
                    'last_activities': fake_user.last_activities
                },
                
                # Context metadata
                'context_name': context.name,
                'context_description': context.description,
                'evaluation_timestamp': context.created_at.isoformat()
            }
            
            return evaluation_context
            
        except VariableContext.DoesNotExist:
            logger.error(f"Variable context {context_id} not found")
            return {}
    
    def list_contexts_for_agent(self, agent_role: str) -> List[Dict[str, Any]]:
        """List all contexts suitable for evaluating a specific agent"""
        contexts = VariableContext.objects.filter(
            agent_role_being_evaluated=agent_role,
            is_active=True
        ).order_by('name')
        
        return [
            {
                'id': str(ctx.id),
                'name': ctx.name,
                'description': ctx.description,
                'workflow_type': ctx.current_workflow_type,
                'workflow_stage': ctx.workflow_stage,
                'trust_level': ctx.trust_level,
                'trust_phase': ctx.trust_phase,
                'stress_level': ctx.stress_level,
                'mood_summary': f"valence={ctx.mood_valence:.1f}, arousal={ctx.mood_arousal:.1f}"
            }
            for ctx in contexts
        ]
    
    def get_contexts_by_workflow(self, workflow_type: str) -> List[VariableContext]:
        """Get all contexts for a specific workflow type"""
        return VariableContext.objects.filter(
            current_workflow_type=workflow_type,
            is_active=True
        ).order_by('agent_role_being_evaluated', 'name')
```

## Step 2.3: Seed Variable Contexts (Day 2)

### Create `backend/apps/main/management/commands/seed_variable_contexts.py`:

```python
from django.core.management.base import BaseCommand
from apps.main.services.variable_context_service import VariableContextService

class Command(BaseCommand):
    help = 'Seed variable contexts for agent evaluation'
    
    def handle(self, *args, **options):
        service = VariableContextService()
        
        contexts_data = [
            # Wheel Generation contexts
            {
                'name': 'Stressed_Evening_WheelGen_Psychological',
                'description': 'Stressed user in evening, wheel generation workflow, evaluating psychological agent',
                'current_workflow_type': 'wheel_generation',
                'workflow_stage': 'agent_processing',
                'agent_role_being_evaluated': 'psychological',
                'trust_level': 45,
                'mood_valence': -0.3,
                'mood_arousal': 0.2,
                'reported_environment': 'home',
                'stress_level': 75,
                'time_pressure': 60,
                'reported_time_availability': '15_minutes',
                'reported_focus': 'quick stress relief',
                'previous_agent_outputs': [
                    'resource_agent: limited_time_low_energy_resources',
                    'engagement_agent: prefers_calm_activities_when_stressed'
                ],
                'expected_next_agents': ['strategy', 'wheel_activity', 'ethical']
            },
            {
                'name': 'Confident_Morning_WheelGen_Strategy',
                'description': 'Confident user in morning, wheel generation workflow, evaluating strategy agent',
                'current_workflow_type': 'wheel_generation',
                'workflow_stage': 'agent_processing',
                'agent_role_being_evaluated': 'strategy',
                'trust_level': 78,
                'mood_valence': 0.6,
                'mood_arousal': 0.4,
                'reported_environment': 'home',
                'stress_level': 20,
                'time_pressure': 25,
                'reported_time_availability': '60_minutes',
                'reported_focus': 'creative and challenging activities',
                'previous_agent_outputs': [
                    'resource_agent: high_availability_creative_materials',
                    'engagement_agent: loves_novel_challenging_activities',
                    'psychological_agent: integration_phase_ready_for_growth'
                ],
                'expected_next_agents': ['wheel_activity', 'ethical']
            },
            {
                'name': 'New_User_WheelGen_Mentor',
                'description': 'New anxious user, wheel generation workflow, evaluating mentor agent',
                'current_workflow_type': 'wheel_generation',
                'workflow_stage': 'result_delivery',
                'agent_role_being_evaluated': 'mentor',
                'trust_level': 25,
                'mood_valence': -0.1,
                'mood_arousal': 0.6,  # Anxious energy
                'reported_environment': 'home',
                'stress_level': 50,
                'time_pressure': 40,
                'reported_time_availability': '30_minutes',
                'reported_focus': 'something easy to start with',
                'previous_agent_outputs': [
                    'final_wheel: 6_gentle_foundation_activities'
                ],
                'expected_next_agents': []  # Mentor is final step
            },
            
            # Discussion contexts
            {
                'name': 'Overwhelmed_Discussion_Mentor',
                'description': 'Overwhelmed user seeking support, discussion workflow, evaluating mentor agent',
                'current_workflow_type': 'discussion',
                'workflow_stage': 'agent_processing',
                'agent_role_being_evaluated': 'mentor',
                'trust_level': 55,
                'mood_valence': -0.7,
                'mood_arousal': 0.3,
                'reported_environment': 'work',
                'stress_level': 85,
                'time_pressure': 80,
                'reported_time_availability': '5_minutes',
                'reported_focus': 'need support and guidance',
                'previous_agent_outputs': [],
                'expected_next_agents': []
            },
            
            # Activity Feedback contexts
            {
                'name': 'Post_Success_Feedback_Psychological',
                'description': 'User completed challenging activity successfully, evaluating psychological agent',
                'current_workflow_type': 'activity_feedback',
                'workflow_stage': 'agent_processing',
                'agent_role_being_evaluated': 'psychological',
                'trust_level': 68,
                'mood_valence': 0.8,
                'mood_arousal': 0.5,
                'reported_environment': 'home',
                'stress_level': 15,
                'time_pressure': 20,
                'reported_time_availability': '30_minutes',
                'reported_focus': 'share success and plan next steps',
                'previous_agent_outputs': [
                    'activity_completed: creative_writing_exercise',
                    'user_feedback: felt_challenging_but_rewarding'
                ],
                'expected_next_agents': ['mentor']
            }
        ]
        
        for context_data in contexts_data:
            service.create_context(context_data)
            self.stdout.write(
                self.style.SUCCESS(f"Created context: {context_data['name']}")
            )
        
        self.stdout.write(
            self.style.SUCCESS(f"Successfully seeded {len(contexts_data)} variable contexts")
        )
```

### Run seeding:
```bash
python manage.py seed_variable_contexts
```

## Step 2.4: Test Context Integration (Day 3)

### Create `backend/apps/main/tests/test_variable_context_service.py`:

```python
from django.test import TestCase
from apps.main.services.variable_context_service import VariableContextService
from apps.main.services.fake_user_service import FakeUserService
from apps.main.models import VariableContext

class TestVariableContextService(TestCase):
    def setUp(self):
        self.context_service = VariableContextService()
        self.user_service = FakeUserService()
        
        # Create test user
        self.fake_user = self.user_service.create_fake_user({
            'name': 'Test_Context_User',
            'description': 'User for context testing',
            'trust_level': 60,
            'personality_traits': {'openness': 0.7}
        })
        
        # Create test context
        self.context_data = {
            'name': 'Test_WheelGen_Mentor',
            'description': 'Test context for wheel generation mentor evaluation',
            'current_workflow_type': 'wheel_generation',
            'workflow_stage': 'result_delivery',
            'agent_role_being_evaluated': 'mentor',
            'trust_level': 55,
            'mood_valence': 0.2,
            'stress_level': 40
        }
    
    def test_create_context(self):
        context = self.context_service.create_context(self.context_data)
        self.assertEqual(context.name, 'Test_WheelGen_Mentor')
        self.assertEqual(context.trust_phase, 'expansion')  # 55 is in expansion phase
    
    def test_get_evaluation_context(self):
        context = self.context_service.create_context(self.context_data)
        
        eval_context = self.context_service.get_context_for_evaluation(
            str(context.id), 
            self.fake_user
        )
        
        self.assertEqual(eval_context['agent_role'], 'mentor')
        self.assertEqual(eval_context['workflow_type'], 'wheel_generation')
        self.assertIn('user_profile', eval_context)
        self.assertIn('variable_context', eval_context)
        
        # Check context packet format
        context_packet = eval_context['variable_context']
        self.assertIn('mood', context_packet)
        self.assertIn('environment', context_packet)
        self.assertEqual(context_packet['trust_level'], 55)
    
    def test_list_contexts_for_agent(self):
        # Create multiple contexts
        self.context_service.create_context(self.context_data)
        
        mentor_context_data = self.context_data.copy()
        mentor_context_data['name'] = 'Another_Mentor_Context'
        self.context_service.create_context(mentor_context_data)
        
        psychological_context_data = self.context_data.copy()
        psychological_context_data['name'] = 'Psychological_Context'
        psychological_context_data['agent_role_being_evaluated'] = 'psychological'
        self.context_service.create_context(psychological_context_data)
        
        # Test filtering by agent role
        mentor_contexts = self.context_service.list_contexts_for_agent('mentor')
        self.assertEqual(len(mentor_contexts), 2)
        
        psychological_contexts = self.context_service.list_contexts_for_agent('psychological')
        self.assertEqual(len(psychological_contexts), 1)
```

### Run tests:
```bash
python manage.py test apps.main.tests.test_variable_context_service
```

**Phase 2 Complete**: You now have variable contexts that capture the current workflow state, user state, and agent coordination context for evaluation.
