# Quick Start Implementation Guide
**Execute these commands to get the simple benchmarking system running**

## Step 1: Implement the Core Models

First, let's add the new models to your existing system:

```bash
cd /projects/goali1/backend
```

### Add the models to `apps/main/models.py`:

```python
# Add these imports at the top if not already present
import uuid
from django.core.validators import MinValueValidator, MaxValueValidator

# Add these models at the end of the file (after your existing models)

class FakeUser(models.Model):
    """
    Fake user for benchmarking with pre-defined realistic profile and trajectory
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    name = models.CharField(max_length=100, help_text="Human-readable name like 'Stressed_ADHD_User'")
    description = models.TextField(help_text="Description of user archetype and trajectory")
    
    # User profile data (pre-populated realistic data)
    trust_level = models.IntegerField(default=50, validators=[MinValue<PERSON>alida<PERSON>(0), MaxValueValida<PERSON>(100)])
    personality_traits = models.JSONField(default=dict, help_text="HEXACO traits as dict")
    user_limitations = models.JSONField(default=list, help_text="List of limitations")
    user_capabilities = models.JSONField(default=list, help_text="List of capabilities")
    user_goals = models.JSONField(default=list, help_text="List of goals and aspirations")
    user_beliefs = models.JSONField(default=list, help_text="Core beliefs")
    
    # Fake trajectory data (simulated history)
    interaction_count = models.IntegerField(default=0, help_text="Simulated number of interactions")
    completion_rate = models.FloatField(default=0.7, help_text="Simulated activity completion rate")
    trust_trajectory = models.CharField(max_length=50, default="stable", help_text="building, stable, declining")
    last_activities = models.JSONField(default=list, help_text="Last few activities completed")
    
    # State management
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        verbose_name = "Fake User"
        verbose_name_plural = "Fake Users"
    
    def __str__(self):
        return f"{self.name} (Trust: {self.trust_level})"


class FakeUserSnapshot(models.Model):
    """
    Snapshot of fake user state for restoration after benchmarking
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    fake_user = models.ForeignKey(FakeUser, on_delete=models.CASCADE, related_name='snapshots')
    snapshot_name = models.CharField(max_length=100, help_text="e.g., 'original_state', 'before_evaluation_X'")
    
    # Snapshot data
    user_state_data = models.JSONField(help_text="Complete user state as JSON")
    trust_level = models.IntegerField()
    interaction_count = models.IntegerField()
    completion_rate = models.FloatField()
    last_activities = models.JSONField()
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        verbose_name = "Fake User Snapshot"
        unique_together = [['fake_user', 'snapshot_name']]
```

### Create and run migrations:

```bash
python manage.py makemigrations main --name="add_fake_user_models"
python manage.py migrate
```

## Step 2: Create the Fake User Service

Create the service file:

```bash
mkdir -p apps/main/services
touch apps/main/services/fake_user_service.py
```

Then copy the content from `phase_1_fake_users.md` Step 1.2 into this file.

## Step 3: Create the Seeding Command

```bash
mkdir -p apps/main/management/commands
touch apps/main/management/commands/seed_fake_users.py
```

Copy the content from `phase_1_fake_users.md` Step 1.3 into this file.

## Step 4: Test the Fake User System

Create the test file:

```bash
mkdir -p apps/main/tests
touch apps/main/tests/test_fake_user_service.py
```

Copy the content from `phase_1_fake_users.md` Step 1.4 into this file.

## Step 5: Run Initial Setup

```bash
# Seed the fake users
python manage.py seed_fake_users

# Run the tests
python manage.py test apps.main.tests.test_fake_user_service

# Verify in Django admin (optional)
python manage.py createsuperuser  # if you don't have one
python manage.py runserver
# Visit http://localhost:8000/admin and check "Fake Users"
```

## Next Steps

Once Phase 1 is working, continue with:

1. **Phase 2**: Implement Variable Context models and services
2. **Phase 3**: Add Simple Evaluation Criteria and LLM evaluation engine
3. **Phase 4**: Create APIs and admin dashboard

Each phase builds on the previous one, so complete them in order.

## Verification Commands

```bash
# Check if fake users were created
python manage.py shell -c "from apps.main.models import FakeUser; print(f'Fake users: {FakeUser.objects.count()}')"

# List fake users
python manage.py shell -c "
from apps.main.models import FakeUser
for user in FakeUser.objects.all():
    print(f'{user.name}: Trust={user.trust_level}, Interactions={user.interaction_count}')
"

# Test snapshot functionality
python manage.py shell -c "
from apps.main.services.fake_user_service import FakeUserService
from apps.main.models import FakeUser
service = FakeUserService()
user = FakeUser.objects.first()
print(f'Original trust: {user.trust_level}')
user.trust_level = 99
user.save()
print(f'Modified trust: {user.trust_level}')
service.restore_snapshot(user, 'original_state')
user.refresh_from_db()
print(f'Restored trust: {user.trust_level}')
"
```

## Troubleshooting

**If migrations fail:**
- Check that all imports are correct in models.py
- Make sure you're in the backend directory
- Try `python manage.py makemigrations` without the app name first

**If seeding fails:**
- Check that the FakeUser model is properly imported
- Verify the service file is in the correct location
- Run `python manage.py shell` and test imports manually

**If tests fail:**
- Ensure test database is set up correctly
- Check that all model relationships are properly defined
- Run individual test methods to isolate issues

Ready to start implementing Phase 1?
