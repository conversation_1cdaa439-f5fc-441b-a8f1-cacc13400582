# Revised Architecture: Using Existing UserProfile Model

## 🎯 **Core Insight**: Use `UserProfile` with `is_real=False`

Instead of creating a separate `FakeUser` model, leverage your existing `UserProfile` model which already has:
- `is_real` property for distinguishing fake vs real users
- All the relationships (traits, goals, beliefs, limitations, trust levels)
- Integration with your existing agent tools and workflows

## **Revised Model Architecture**

### **1. User Management → "Fake UserProfile Management"**
```python
# Use existing UserProfile model, just create instances with is_real=False
fake_user_profile = UserProfile.objects.create(
    profile_name="Stressed_ADHD_User_Benchmark",
    is_real=False,  # Key difference
    # ... all other fields work the same
)
```

### **2. State Snapshots → "UserProfile Snapshots"**
```python
class UserProfileSnapshot(models.Model):
    """
    Snapshot system for UserProfile state (works with both real and fake profiles)
    """
    user_profile = models.ForeignKey(UserProfile, on_delete=models.CASCADE, related_name='snapshots')
    snapshot_name = models.CharField(max_length=100)
    snapshot_data = models.JSONField()  # Complete profile state as JSON
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = [['user_profile', 'snapshot_name']]
```

### **3. Variable Context → "Evaluation Context"** 
```python
class EvaluationContext(models.Model):
    """
    Current moment state for evaluation - workflow and agent context
    """
    name = models.CharField(max_length=100)
    
    # Current workflow context
    current_workflow_type = models.CharField(max_length=50, choices=WORKFLOW_CHOICES)
    workflow_stage = models.CharField(max_length=50, choices=STAGE_CHOICES)
    agent_role_being_evaluated = models.CharField(max_length=50, choices=AGENT_CHOICES)
    
    # Context variables (override UserProfile values for evaluation)
    trust_level_override = models.IntegerField(null=True, blank=True)
    mood_valence_override = models.FloatField(null=True, blank=True) 
    mood_arousal_override = models.FloatField(null=True, blank=True)
    stress_level_override = models.IntegerField(null=True, blank=True)
    
    # Agent coordination context
    previous_agent_outputs = models.JSONField(default=list)
    expected_next_agents = models.JSONField(default=list)
```

### **4. Evaluation Criteria → "Simple LLM Criteria"** (unchanged)
```python
class SimpleEvaluationCriteria(models.Model):
    """Natural language evaluation criteria for LLM evaluator"""
    name = models.CharField(max_length=100)
    agent_role = models.CharField(max_length=50, choices=AGENT_CHOICES)
    evaluation_prompt = models.TextField()  # Natural language for LLM
    # ... context adaptations
```

## **Revised Implementation Benefits**

### **🎯 Advantages of Using UserProfile:**
1. **Real Model Testing**: Catches actual issues with UserProfile relationships
2. **No Duplication**: Leverages existing traits, goals, beliefs, trust models
3. **Tool Compatibility**: Works with existing agent tools that expect UserProfile
4. **Relationship Power**: Uses existing UserTraitInclination, UserGoal, Belief, etc.
5. **Future-Proof**: Any UserProfile improvements automatically benefit benchmarking

### **🔧 Implementation Strategy:**
1. Create fake UserProfile instances with realistic data
2. Add snapshot system that works with any UserProfile
3. Create evaluation contexts that can override specific values
4. Build LLM evaluation service that uses real UserProfile data

## **Revised Phase Implementation**

### **Phase 1: Fake UserProfile + Snapshot System**
- Service to create fake UserProfile instances (is_real=False)
- UserProfileSnapshot model for state management
- Seeding command for realistic fake profiles
- Snapshot/restore functionality

### **Phase 2: Evaluation Context System** 
- EvaluationContext model for current evaluation state
- Context override system for specific evaluation scenarios  
- Integration with existing workflow and agent systems

### **Phase 3: LLM Evaluation Engine**
- SimpleEvaluationCriteria with natural language prompts
- Evaluation service that combines UserProfile + EvaluationContext
- Context-aware evaluation using real user data structures

### **Phase 4: Integration & APIs**
- API endpoints for evaluation management
- Integration with existing admin system
- Management commands and dashboard

## **Key Architecture Changes**

### **Before (Separate Model)**:
```python
FakeUser.objects.create(name="Test", trust_level=50, ...)
```

### **After (Real Model)**:
```python
UserProfile.objects.create(
    profile_name="Benchmark_Stressed_Professional", 
    is_real=False,
    # Uses real UserTraitInclination, UserGoal, etc.
)
```

This approach is **much more powerful** because it tests the real system architecture while being equally simple to implement.
