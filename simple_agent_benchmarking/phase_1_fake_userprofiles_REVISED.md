# Phase 1: Fake UserProfile + Snapshot System (REVISED)
**Timeline**: 2-3 days

## Step 1.1: Create UserProfile Snapshot Model (Day 1)

### Add to `backend/apps/main/models.py`:

```python
class UserProfileSnapshot(models.Model):
    """
    Snapshot system for UserProfile state (works with both real and fake profiles)
    Can restore any UserProfile to a previous state after evaluation
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    user_profile = models.ForeignKey(
        'user.UserProfile', 
        on_delete=models.CASCADE, 
        related_name='snapshots'
    )
    snapshot_name = models.CharField(
        max_length=100, 
        help_text="e.g., 'original_state', 'before_evaluation_X'"
    )
    
    # Complete profile state as JSON
    profile_data = models.JSONField(help_text="Complete UserProfile state")
    related_data = models.JSONField(help_text="Related models data (traits, goals, beliefs, etc.)")
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        verbose_name = "User Profile Snapshot"
        unique_together = [['user_profile', 'snapshot_name']]
    
    def __str__(self):
        return f"{self.user_profile.profile_name} - {self.snapshot_name}"
```

### Run migration:
```bash
cd /projects/goali1/backend
python manage.py makemigrations main --name="add_userprofile_snapshot"
python manage.py migrate
```

## Step 1.2: Create Fake UserProfile Service (Day 1-2)

### Create `backend/apps/main/services/fake_userprofile_service.py`:

```python
import json
import logging
from typing import Dict, Any, List, Optional
from django.db import transaction
from apps.main.models import UserProfileSnapshot
from apps.user.models import UserProfile, UserTraitInclination, UserGoal, Belief
from django.contrib.contenttypes.models import ContentType

logger = logging.getLogger(__name__)

class FakeUserProfileService:
    """
    Service for managing fake UserProfiles (is_real=False) and their state snapshots
    """
    
    def create_fake_userprofile(self, profile_data: Dict[str, Any]) -> UserProfile:
        """Create a new fake UserProfile with realistic related data"""
        with transaction.atomic():
            # Create the UserProfile with is_real=False
            profile_data['is_real'] = False  # Key distinction
            user_profile = UserProfile.objects.create(**profile_data)
            
            # Create related data if provided
            if 'personality_traits' in profile_data:
                self._create_personality_traits(user_profile, profile_data['personality_traits'])
            
            if 'user_goals' in profile_data:
                self._create_user_goals(user_profile, profile_data['user_goals'])
                
            if 'user_beliefs' in profile_data:
                self._create_user_beliefs(user_profile, profile_data['user_beliefs'])
            
            # Create initial snapshot
            self.create_snapshot(user_profile, "original_state")
            
            logger.info(f"Created fake UserProfile: {user_profile.profile_name}")
            return user_profile
    
    def _create_personality_traits(self, user_profile: UserProfile, traits: Dict[str, float]):
        """Create UserTraitInclination objects from traits dict"""
        for trait_name, value in traits.items():
            # You'll need to adjust this based on your actual trait model structure
            UserTraitInclination.objects.create(
                user_profile=user_profile,
                trait_name=trait_name,  # Adjust field names as needed
                strength=value,
                # Add other required fields
            )
    
    def _create_user_goals(self, user_profile: UserProfile, goals: List[str]):
        """Create UserGoal objects from goals list"""
        for i, goal_description in enumerate(goals):
            UserGoal.objects.create(
                user_profile=user_profile,
                description=goal_description,
                priority=i + 1,
                # Add other required fields
            )
    
    def _create_user_beliefs(self, user_profile: UserProfile, beliefs: List[str]):
        """Create Belief objects from beliefs list"""
        for belief_text in beliefs:
            Belief.objects.create(
                user_profile=user_profile,
                description=belief_text,
                # Add other required fields
            )
    
    def create_snapshot(self, user_profile: UserProfile, snapshot_name: str) -> UserProfileSnapshot:
        """Create snapshot of current UserProfile state and all related data"""
        with transaction.atomic():
            # Capture main profile data
            profile_data = {
                'profile_name': user_profile.profile_name,
                'is_real': user_profile.is_real,
                # Add all other UserProfile fields you want to snapshot
            }
            
            # Capture related data
            related_data = {
                'traits': list(user_profile.trait_inclinations.values()),  # Adjust field name
                'goals': list(user_profile.goals.values()),  # Adjust field name  
                'beliefs': list(user_profile.beliefs.values()),  # Adjust field name
                # Add other related models as needed
            }
            
            snapshot = UserProfileSnapshot.objects.update_or_create(
                user_profile=user_profile,
                snapshot_name=snapshot_name,
                defaults={
                    'profile_data': profile_data,
                    'related_data': related_data,
                }
            )[0]
            
            logger.info(f"Created snapshot '{snapshot_name}' for {user_profile.profile_name}")
            return snapshot
    
    def restore_snapshot(self, user_profile: UserProfile, snapshot_name: str = "original_state") -> bool:
        """Restore UserProfile and all related data to a previous snapshot state"""
        try:
            snapshot = UserProfileSnapshot.objects.get(
                user_profile=user_profile, 
                snapshot_name=snapshot_name
            )
            
            with transaction.atomic():
                # Restore main profile data
                profile_data = snapshot.profile_data
                for field, value in profile_data.items():
                    setattr(user_profile, field, value)
                user_profile.save()
                
                # Restore related data
                related_data = snapshot.related_data
                
                # Clear and recreate traits
                user_profile.trait_inclinations.all().delete()  # Adjust field name
                for trait_data in related_data.get('traits', []):
                    UserTraitInclination.objects.create(user_profile=user_profile, **trait_data)
                
                # Clear and recreate goals  
                user_profile.goals.all().delete()  # Adjust field name
                for goal_data in related_data.get('goals', []):
                    UserGoal.objects.create(user_profile=user_profile, **goal_data)
                
                # Clear and recreate beliefs
                user_profile.beliefs.all().delete()  # Adjust field name
                for belief_data in related_data.get('beliefs', []):
                    Belief.objects.create(user_profile=user_profile, **belief_data)
            
            logger.info(f"Restored {user_profile.profile_name} to snapshot '{snapshot_name}'")
            return True
            
        except UserProfileSnapshot.DoesNotExist:
            logger.error(f"Snapshot '{snapshot_name}' not found for {user_profile.profile_name}")
            return False
        except Exception as e:
            logger.error(f"Error restoring snapshot: {e}")
            return False
    
    def get_fake_userprofile_for_benchmark(self, profile_id: str) -> Optional[UserProfile]:
        """Get fake UserProfile by ID, ensuring it's ready for benchmarking"""
        try:
            user_profile = UserProfile.objects.get(
                id=profile_id, 
                is_real=False  # Only fake profiles for benchmarking
            )
            
            # Create pre-benchmark snapshot
            snapshot_name = f"before_benchmark_{user_profile.snapshots.count()}"
            self.create_snapshot(user_profile, snapshot_name)
            
            return user_profile
        except UserProfile.DoesNotExist:
            logger.error(f"Fake UserProfile {profile_id} not found")
            return None
    
    def list_fake_userprofiles(self) -> List[Dict[str, Any]]:
        """List all fake UserProfiles with basic info"""
        profiles = UserProfile.objects.filter(is_real=False).order_by('profile_name')
        return [
            {
                'id': str(profile.id),
                'profile_name': profile.profile_name,
                'snapshots_count': profile.snapshots.count(),
                # Add other relevant fields from your UserProfile model
            }
            for profile in profiles
        ]
```

## Step 1.3: Create Fake UserProfile Seeding (Day 2)

### Create `backend/apps/main/management/commands/seed_fake_userprofiles.py`:

```python
from django.core.management.base import BaseCommand
from apps.main.services.fake_userprofile_service import FakeUserProfileService

class Command(BaseCommand):
    help = 'Seed fake UserProfiles for benchmarking'
    
    def handle(self, *args, **options):
        service = FakeUserProfileService()
        
        fake_profiles_data = [
            {
                'profile_name': 'Benchmark_New_Anxious_User',
                'personality_traits': {
                    'openness': 0.4,
                    'conscientiousness': 0.7,
                    'extraversion': 0.3,
                    'agreeableness': 0.8,
                    'neuroticism': 0.8,
                    'honesty_humility': 0.7
                },
                'user_goals': [
                    'Reduce social anxiety in group settings',
                    'Build confidence through small achievements', 
                    'Improve social skills gradually'
                ],
                'user_beliefs': [
                    'I am not good enough for others',
                    'People will judge me harshly if I make mistakes'
                ],
                # Add other UserProfile fields as needed
            },
            {
                'profile_name': 'Benchmark_Confident_ADHD_User',
                'personality_traits': {
                    'openness': 0.9,
                    'conscientiousness': 0.4,
                    'extraversion': 0.7,
                    'agreeableness': 0.6,
                    'neuroticism': 0.3,
                    'honesty_humility': 0.6
                },
                'user_goals': [
                    'Channel creative energy into projects',
                    'Improve focus and attention management',
                    'Complete long-term creative projects'
                ],
                'user_beliefs': [
                    'I can figure out creative solutions to problems',
                    'Challenges are opportunities for growth'
                ],
            },
            {
                'profile_name': 'Benchmark_Stressed_Professional',
                'personality_traits': {
                    'openness': 0.6,
                    'conscientiousness': 0.9,
                    'extraversion': 0.5,
                    'agreeableness': 0.5,
                    'neuroticism': 0.6,
                    'honesty_humility': 0.8
                },
                'user_goals': [
                    'Manage work-related stress effectively',
                    'Achieve better work-life balance',
                    'Increase productivity and efficiency'
                ],
                'user_beliefs': [
                    'Hard work always pays off in the end',
                    'I must do everything perfectly to be valuable'
                ],
            }
        ]
        
        for profile_data in fake_profiles_data:
            service.create_fake_userprofile(profile_data)
            self.stdout.write(
                self.style.SUCCESS(f"Created fake UserProfile: {profile_data['profile_name']}")
            )
        
        self.stdout.write(
            self.style.SUCCESS(f"Successfully seeded {len(fake_profiles_data)} fake UserProfiles")
        )
```

### Run seeding:
```bash
python manage.py seed_fake_userprofiles
```

## Step 1.4: Test Fake UserProfile System (Day 2-3)

### Create `backend/apps/main/tests/test_fake_userprofile_service.py`:

```python
from django.test import TestCase
from apps.main.services.fake_userprofile_service import FakeUserProfileService
from apps.main.models import UserProfileSnapshot
from apps.user.models import UserProfile

class TestFakeUserProfileService(TestCase):
    def setUp(self):
        self.service = FakeUserProfileService()
        self.profile_data = {
            'profile_name': 'Test_Benchmark_User',
            'personality_traits': {'openness': 0.5, 'conscientiousness': 0.7},
            'user_goals': ['test goal 1', 'test goal 2'],
            'user_beliefs': ['test belief 1']
        }
    
    def test_create_fake_userprofile(self):
        profile = self.service.create_fake_userprofile(self.profile_data)
        
        # Check profile was created correctly
        self.assertEqual(profile.profile_name, 'Test_Benchmark_User')
        self.assertFalse(profile.is_real)  # Key test
        
        # Check original snapshot was created
        self.assertTrue(
            UserProfileSnapshot.objects.filter(
                user_profile=profile, 
                snapshot_name='original_state'
            ).exists()
        )
        
        # Check related data was created (adjust based on your models)
        self.assertTrue(profile.trait_inclinations.exists())
        self.assertTrue(profile.goals.exists())
        self.assertTrue(profile.beliefs.exists())
    
    def test_snapshot_and_restore(self):
        profile = self.service.create_fake_userprofile(self.profile_data)
        
        # Modify profile
        original_name = profile.profile_name
        profile.profile_name = 'Modified_Name'
        profile.save()
        
        # Clear some related data
        profile.goals.all().delete()
        
        # Restore to original
        restored = self.service.restore_snapshot(profile, 'original_state')
        self.assertTrue(restored)
        
        profile.refresh_from_db()
        self.assertEqual(profile.profile_name, original_name)
        
        # Check related data was restored
        self.assertTrue(profile.goals.exists())
    
    def test_list_fake_userprofiles(self):
        # Create multiple profiles
        profile1 = self.service.create_fake_userprofile(self.profile_data)
        
        profile2_data = self.profile_data.copy()
        profile2_data['profile_name'] = 'Test_Benchmark_User_2'
        profile2 = self.service.create_fake_userprofile(profile2_data)
        
        # Test listing
        profiles_list = self.service.list_fake_userprofiles()
        self.assertEqual(len(profiles_list), 2)
        
        # Check only fake profiles are returned
        for profile_data in profiles_list:
            profile = UserProfile.objects.get(id=profile_data['id'])
            self.assertFalse(profile.is_real)
    
    def test_get_fake_userprofile_for_benchmark(self):
        profile = self.service.create_fake_userprofile(self.profile_data)
        
        # Get profile for benchmark
        benchmark_profile = self.service.get_fake_userprofile_for_benchmark(str(profile.id))
        self.assertEqual(benchmark_profile.id, profile.id)
        
        # Check that a pre-benchmark snapshot was created
        self.assertTrue(
            UserProfileSnapshot.objects.filter(
                user_profile=profile,
                snapshot_name__startswith='before_benchmark_'
            ).exists()
        )
```

### Run tests:
```bash
python manage.py test apps.main.tests.test_fake_userprofile_service
```

## Important Notes:

1. **Adjust Field Names**: You'll need to modify the service code to match your actual UserProfile, UserTraitInclination, UserGoal, and Belief model field names.

2. **Model Relationships**: Update the related_name references to match your actual model relationships.

3. **Required Fields**: Add any required fields for your models in the creation methods.

4. **Snapshot Scope**: Decide which related models to include in snapshots based on what changes during evaluations.

**Phase 1 Complete**: You now have fake UserProfiles with full snapshot/restore capabilities using your existing data model!
