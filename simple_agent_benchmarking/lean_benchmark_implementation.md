# Lean Benchmark Extension: Technical Implementation Guide

## 🏗 **Architecture Overview**

This implementation follows **Django service layer pattern** and **dependency injection principles** to extend your existing benchmarking infrastructure without duplication.

### **Design Patterns Applied**
- **Factory Pattern**: `BenchmarkProfileFactory` for UserProfile creation
- **Adapter Pattern**: `SimpleEvaluationAdapter` to bridge natural language prompts to existing criteria
- **Facade Pattern**: `QuickBenchmarkService` to simplify complex benchmark orchestration
- **Strategy Pattern**: Leverage existing `SemanticEvaluator` strategies

## 📁 **File Structure & Implementation**

### **1. Service Layer Extension**
```
backend/apps/main/services/
├── benchmark_profile_factory.py     # New: Factory for benchmark UserProfiles  
├── simple_evaluation_adapter.py     # New: Bridge to existing evaluation system
├── quick_benchmark_service.py       # New: Orchestration facade
└── benchmark_service.py             # Existing: Extend with simple benchmark support
```

### **2. Management Commands**
```
backend/apps/main/management/commands/
├── seed_benchmark_profiles.py       # New: Create benchmark UserProfile templates
└── run_benchmarks.py               # Existing: Extend with quick benchmark mode
```

### **3. API Views**
```
backend/apps/main/views.py           # Extend existing views
backend/apps/main/urls.py            # Add quick benchmark endpoint
```

## 🔧 **Implementation Details**

### **Phase 1: Benchmark Profile Factory Service**

**File**: `backend/apps/main/services/benchmark_profile_factory.py`

```python
"""
Benchmark Profile Factory Service
Follows Factory Pattern and Django service layer best practices
"""
import logging
from typing import Dict, List, Optional
from django.db import transaction
from django.core.exceptions import ValidationError

from apps.user.models import UserProfile, UserTraitInclination, UserGoal, Belief
from apps.main.models import PersonalityTrait, Domain

logger = logging.getLogger(__name__)

class BenchmarkProfileFactory:
    """
    Factory service for creating benchmark-ready UserProfiles.
    Implements Factory Pattern with proper error handling and validation.
    """
    
    # Profile templates following established psychology frameworks
    PROFILE_TEMPLATES = {
        'anxious_new_user': {
            'profile_name': 'Benchmark_Anxious_New_User',
            'description': 'High neuroticism, low confidence, new to system',
            'personality_traits': {
                'openness': 0.4,
                'conscientiousness': 0.7,
                'extraversion': 0.3,
                'agreeableness': 0.8,
                'neuroticism': 0.8,
                'honesty_humility': 0.7
            },
            'goals': [
                'Reduce social anxiety in group settings',
                'Build confidence through small achievements',
                'Improve social skills gradually'
            ],
            'beliefs': [
                'I am not good enough for others',
                'People will judge me harshly if I make mistakes'
            ],
            'limitations': ['Social interaction anxiety', 'Low self-confidence'],
            'stress_level': 7,
            'mood_valence': 3.0,
            'mood_arousal': 6.0
        },
        'confident_adhd_user': {
            'profile_name': 'Benchmark_Confident_ADHD_User', 
            'description': 'High openness, low conscientiousness, creative energy',
            'personality_traits': {
                'openness': 0.9,
                'conscientiousness': 0.4,
                'extraversion': 0.7,
                'agreeableness': 0.6,
                'neuroticism': 0.3,
                'honesty_humility': 0.6
            },
            'goals': [
                'Channel creative energy into projects',
                'Improve focus and attention management',
                'Complete long-term creative projects'
            ],
            'beliefs': [
                'I can figure out creative solutions to problems',
                'Challenges are opportunities for growth'
            ],
            'limitations': ['Attention regulation difficulties', 'Task completion challenges'],
            'stress_level': 4,
            'mood_valence': 7.0,
            'mood_arousal': 8.0
        },
        'stressed_professional': {
            'profile_name': 'Benchmark_Stressed_Professional',
            'description': 'High conscientiousness, work-life balance issues',
            'personality_traits': {
                'openness': 0.6,
                'conscientiousness': 0.9,
                'extraversion': 0.5,
                'agreeableness': 0.5,
                'neuroticism': 0.6,
                'honesty_humility': 0.8
            },
            'goals': [
                'Manage work-related stress effectively',
                'Achieve better work-life balance',
                'Increase productivity and efficiency'
            ],
            'beliefs': [
                'Hard work always pays off in the end',
                'I must do everything perfectly to be valuable'
            ],
            'limitations': ['Perfectionism', 'Work-life boundary issues'],
            'stress_level': 8,
            'mood_valence': 5.0,
            'mood_arousal': 7.0
        }
    }
    
    @classmethod
    @transaction.atomic
    def create_benchmark_profile(cls, template_name: str) -> UserProfile:
        """
        Create a benchmark UserProfile from template.
        
        Args:
            template_name: Key from PROFILE_TEMPLATES
            
        Returns:
            UserProfile: Created profile with is_real=False
            
        Raises:
            ValidationError: If template not found or creation fails
        """
        if template_name not in cls.PROFILE_TEMPLATES:
            raise ValidationError(f"Unknown template: {template_name}")
            
        template = cls.PROFILE_TEMPLATES[template_name]
        
        try:
            # Create UserProfile with is_real=False (your key insight)
            profile = UserProfile.objects.create(
                profile_name=template['profile_name'],
                description=template['description'],
                is_real=False,  # Critical: marks as benchmark profile
                stress_level=template.get('stress_level', 5),
                mood_valence=template.get('mood_valence', 5.0),
                mood_arousal=template.get('mood_arousal', 5.0)
            )
            
            # Create personality traits using existing relationship
            cls._create_personality_traits(profile, template['personality_traits'])
            
            # Create goals using existing relationship  
            cls._create_user_goals(profile, template['goals'])
            
            # Create beliefs using existing relationship
            cls._create_user_beliefs(profile, template['beliefs'])
            
            # Create limitations if provided
            if 'limitations' in template:
                cls._create_user_limitations(profile, template['limitations'])
            
            logger.info(f"Created benchmark profile: {profile.profile_name}")
            return profile
            
        except Exception as e:
            logger.error(f"Failed to create benchmark profile {template_name}: {e}")
            raise ValidationError(f"Profile creation failed: {e}")
    
    @classmethod
    def get_or_create_benchmark_profile(cls, template_name: str) -> UserProfile:
        """
        Get existing benchmark profile or create new one.
        Implements idempotent profile creation.
        """
        if template_name not in cls.PROFILE_TEMPLATES:
            raise ValidationError(f"Unknown template: {template_name}")
            
        template = cls.PROFILE_TEMPLATES[template_name]
        profile_name = template['profile_name']
        
        # Try to get existing profile
        profile = UserProfile.objects.filter(
            profile_name=profile_name,
            is_real=False
        ).first()
        
        if profile:
            logger.info(f"Using existing benchmark profile: {profile_name}")
            return profile
        
        # Create new profile
        return cls.create_benchmark_profile(template_name)
    
    @classmethod
    def list_available_templates(cls) -> List[Dict[str, str]]:
        """Return list of available profile templates with descriptions."""
        return [
            {
                'template_name': name,
                'profile_name': template['profile_name'],
                'description': template['description']
            }
            for name, template in cls.PROFILE_TEMPLATES.items()
        ]
    
    @classmethod  
    def _create_personality_traits(cls, profile: UserProfile, traits: Dict[str, float]):
        """Create UserTraitInclination objects from traits dictionary."""
        for trait_name, strength in traits.items():
            try:
                # Get or create personality trait
                trait, _ = PersonalityTrait.objects.get_or_create(
                    name=trait_name,
                    defaults={'description': f'{trait_name.title()} personality dimension'}
                )
                
                # Create trait inclination
                UserTraitInclination.objects.create(
                    user_profile=profile,
                    trait=trait,
                    strength=strength
                )
            except Exception as e:
                logger.warning(f"Failed to create trait {trait_name}: {e}")
    
    @classmethod
    def _create_user_goals(cls, profile: UserProfile, goals: List[str]):
        """Create UserGoal objects from goals list."""
        for i, goal_description in enumerate(goals):
            try:
                UserGoal.objects.create(
                    user_profile=profile,
                    description=goal_description,
                    priority=i + 1,
                    status='active'
                )
            except Exception as e:
                logger.warning(f"Failed to create goal '{goal_description}': {e}")
    
    @classmethod
    def _create_user_beliefs(cls, profile: UserProfile, beliefs: List[str]):
        """Create Belief objects from beliefs list."""
        for belief_text in beliefs:
            try:
                Belief.objects.create(
                    user_profile=profile,
                    description=belief_text,
                    strength=0.8  # Default strong belief
                )
            except Exception as e:
                logger.warning(f"Failed to create belief '{belief_text}': {e}")
    
    @classmethod
    def _create_user_limitations(cls, profile: UserProfile, limitations: List[str]):
        """Create UserLimitation objects from limitations list."""
        from apps.user.models import UserLimitation, Limitation
        
        for limitation_text in limitations:
            try:
                # Get or create limitation
                limitation, _ = Limitation.objects.get_or_create(
                    name=limitation_text,
                    defaults={'description': limitation_text}
                )
                
                # Create user limitation
                UserLimitation.objects.create(
                    user_profile=profile,
                    limitation=limitation,
                    severity='moderate'
                )
            except Exception as e:
                logger.warning(f"Failed to create limitation '{limitation_text}': {e}")
```

### **Phase 2: Simple Evaluation Adapter**

**File**: `backend/apps/main/services/simple_evaluation_adapter.py`

```python
"""
Simple Evaluation Adapter Service
Implements Adapter Pattern to bridge natural language prompts to existing evaluation criteria
"""
import logging
from typing import Dict, Any, Optional
from django.core.exceptions import ValidationError

from apps.main.services.semantic_evaluator import SemanticEvaluator
from apps.main.models import BenchmarkRun

logger = logging.getLogger(__name__)

class SimpleEvaluationAdapter:
    """
    Adapter to convert simple natural language evaluation prompts 
    to existing SemanticEvaluator criteria format.
    """
    
    # Pre-defined evaluation templates for common scenarios
    EVALUATION_TEMPLATES = {
        'mentor_helpfulness': {
            'evaluation_prompt': """
            Evaluate the mentor agent's response for helpfulness and appropriateness:
            
            1. Helpfulness (0-10): How well does the response address the user's needs?
            2. Tone Appropriateness (0-10): Is the tone supportive and empathetic?
            3. Actionability (0-10): Does the response provide clear next steps?
            4. Trust Building (0-10): Does the response build trust with the user?
            
            Provide scores and brief justifications for each dimension.
            """,
            'criteria_mapping': {
                'helpfulness': 0.3,
                'tone_appropriateness': 0.25,
                'actionability': 0.25,
                'trust_building': 0.2
            }
        },
        'agent_accuracy': {
            'evaluation_prompt': """
            Evaluate the agent's response for accuracy and relevance:
            
            1. Factual Accuracy (0-10): Are the facts and information correct?
            2. Relevance (0-10): How relevant is the response to the user's situation?
            3. Completeness (0-10): Does the response address all aspects of the request?
            4. Clarity (0-10): Is the response clear and understandable?
            
            Provide scores and justifications.
            """,
            'criteria_mapping': {
                'factual_accuracy': 0.3,
                'relevance': 0.3,
                'completeness': 0.2,
                'clarity': 0.2
            }
        }
    }
    
    def __init__(self, semantic_evaluator: Optional[SemanticEvaluator] = None):
        """Initialize adapter with optional semantic evaluator dependency injection."""
        self.semantic_evaluator = semantic_evaluator or SemanticEvaluator()
    
    def evaluate_with_simple_prompt(
        self, 
        agent_response: str,
        evaluation_template: str,
        context: Dict[str, Any],
        benchmark_run: BenchmarkRun
    ) -> Dict[str, Any]:
        """
        Evaluate agent response using simple template.
        
        Args:
            agent_response: The agent's response to evaluate
            evaluation_template: Key from EVALUATION_TEMPLATES  
            context: Additional context for evaluation
            benchmark_run: Associated benchmark run
            
        Returns:
            Dict containing evaluation results
            
        Raises:
            ValidationError: If template not found or evaluation fails
        """
        if evaluation_template not in self.EVALUATION_TEMPLATES:
            raise ValidationError(f"Unknown evaluation template: {evaluation_template}")
        
        template = self.EVALUATION_TEMPLATES[evaluation_template]
        
        try:
            # Convert simple prompt to existing criteria format
            evaluation_criteria = self._convert_to_criteria_format(template)
            
            # Use existing SemanticEvaluator with converted criteria
            evaluation_result = self.semantic_evaluator.evaluate(
                response_text=agent_response,
                criteria=evaluation_criteria,
                context=context,
                benchmark_run=benchmark_run
            )
            
            logger.info(f"Completed simple evaluation with template: {evaluation_template}")
            return evaluation_result
            
        except Exception as e:
            logger.error(f"Simple evaluation failed: {e}")
            raise ValidationError(f"Evaluation failed: {e}")
    
    def _convert_to_criteria_format(self, template: Dict[str, Any]) -> Dict[str, Any]:
        """
        Convert simple template to existing SemanticEvaluator criteria format.
        Maintains compatibility with existing evaluation infrastructure.
        """
        return {
            'template_name': 'simple_evaluation',
            'description': 'Simple evaluation using natural language prompt',
            'evaluation_prompt': template['evaluation_prompt'],
            'criteria': template['criteria_mapping'],
            'dimension_weights': template['criteria_mapping']
        }
    
    def create_custom_evaluation(
        self,
        prompt: str,
        criteria_weights: Dict[str, float]
    ) -> str:
        """
        Create custom evaluation template from prompt and weights.
        Validates that weights sum to 1.0.
        """
        # Validate weights
        total_weight = sum(criteria_weights.values())
        if abs(total_weight - 1.0) > 0.01:
            raise ValidationError(f"Criteria weights must sum to 1.0, got {total_weight}")
        
        template_key = f"custom_{hash(prompt) % 10000}"
        
        self.EVALUATION_TEMPLATES[template_key] = {
            'evaluation_prompt': prompt,
            'criteria_mapping': criteria_weights
        }
        
        return template_key
```

### **Phase 3: Quick Benchmark Service**

**File**: `backend/apps/main/services/quick_benchmark_service.py`

```python
"""
Quick Benchmark Service 
Implements Facade Pattern to orchestrate existing benchmarking infrastructure
"""
import logging
from typing import Dict, Any, Optional
from django.db import transaction
from django.core.exceptions import ValidationError

from apps.user.models import UserProfile
from apps.main.models import BenchmarkRun, BenchmarkScenario, GenericAgent
from apps.main.services.benchmark_service import AgentBenchmarker
from apps.main.services.benchmark_profile_factory import BenchmarkProfileFactory
from apps.main.services.simple_evaluation_adapter import SimpleEvaluationAdapter

logger = logging.getLogger(__name__)

class QuickBenchmarkService:
    """
    Facade service for quick agent benchmarking.
    Orchestrates existing services without duplicating functionality.
    """
    
    def __init__(self):
        """Initialize service with dependency injection of existing services."""
        self.profile_factory = BenchmarkProfileFactory()
        self.evaluation_adapter = SimpleEvaluationAdapter()
        self.agent_benchmarker = AgentBenchmarker()
    
    @transaction.atomic
    def run_quick_benchmark(
        self,
        agent_name: str,
        profile_template: str,
        evaluation_template: str,
        scenario_context: Optional[Dict[str, Any]] = None
    ) -> BenchmarkRun:
        """
        Run a quick benchmark using existing infrastructure.
        
        Args:
            agent_name: Name of the agent to benchmark
            profile_template: Template for benchmark UserProfile
            evaluation_template: Template for evaluation criteria
            scenario_context: Optional context for scenario customization
            
        Returns:
            BenchmarkRun: Completed benchmark run with results
            
        Raises:
            ValidationError: If agent not found or benchmark fails
        """
        try:
            # 1. Get or create benchmark UserProfile (leverages existing model)
            user_profile = self.profile_factory.get_or_create_benchmark_profile(
                profile_template
            )
            
            # 2. Get agent (leverages existing agent system)
            agent = self._get_agent(agent_name)
            
            # 3. Create scenario (leverages existing scenario system)
            scenario = self._create_quick_scenario(
                agent, user_profile, scenario_context
            )
            
            # 4. Run benchmark (leverages existing AgentBenchmarker)
            benchmark_run = self.agent_benchmarker.run_benchmark(
                scenario=scenario,
                user_profile=user_profile
            )
            
            # 5. Enhanced evaluation (uses adapter to existing SemanticEvaluator)
            evaluation_result = self.evaluation_adapter.evaluate_with_simple_prompt(
                agent_response=benchmark_run.agent_output,
                evaluation_template=evaluation_template,
                context={'user_profile': user_profile, 'scenario': scenario},
                benchmark_run=benchmark_run
            )
            
            # 6. Update benchmark run with evaluation (existing pattern)
            benchmark_run.semantic_evaluation = evaluation_result
            benchmark_run.save()
            
            logger.info(f"Quick benchmark completed: {benchmark_run.id}")
            return benchmark_run
            
        except Exception as e:
            logger.error(f"Quick benchmark failed: {e}")
            raise ValidationError(f"Benchmark execution failed: {e}")
    
    def _get_agent(self, agent_name: str) -> GenericAgent:
        """Get agent by name with proper error handling."""
        try:
            return GenericAgent.objects.get(name=agent_name)
        except GenericAgent.DoesNotExist:
            available_agents = list(GenericAgent.objects.values_list('name', flat=True))
            raise ValidationError(
                f"Agent '{agent_name}' not found. Available: {available_agents}"
            )
    
    def _create_quick_scenario(
        self,
        agent: GenericAgent,
        user_profile: UserProfile,
        context: Optional[Dict[str, Any]]
    ) -> BenchmarkScenario:
        """
        Create a quick benchmark scenario.
        Leverages existing BenchmarkScenario model and patterns.
        """
        scenario_data = {
            'name': f"Quick_{agent.name}_{user_profile.profile_name}",
            'description': f"Quick benchmark for {agent.name} with {user_profile.profile_name}",
            'agent_role': agent.role,
            'workflow_type': context.get('workflow_type', 'discussion') if context else 'discussion',
            'user_input': context.get('user_input', 'Hello, I need help') if context else 'Hello, I need help',
            'expected_behavior': f"Agent should respond appropriately to {user_profile.profile_name} characteristics",
            'evaluation_focus': 'response_quality',
            'scenario_type': 'quick_benchmark'
        }
        
        # Create scenario (leverages existing model)
        return BenchmarkScenario.objects.create(**scenario_data)
    
    def get_available_options(self) -> Dict[str, Any]:
        """
        Get available options for quick benchmarking.
        Returns templates, agents, and evaluation options.
        """
        return {
            'profile_templates': self.profile_factory.list_available_templates(),
            'evaluation_templates': list(self.evaluation_adapter.EVALUATION_TEMPLATES.keys()),
            'available_agents': list(
                GenericAgent.objects.values('name', 'role', 'description')
            )
        }
    
    def get_benchmark_results_url(self, benchmark_run: BenchmarkRun) -> str:
        """Get URL for viewing benchmark results in existing admin interface."""
        from django.urls import reverse
        return reverse('admin:main_benchmarkrun_change', args=[benchmark_run.pk])
```

### **Phase 4: Management Command**

**File**: `backend/apps/main/management/commands/seed_benchmark_profiles.py`

```python
"""
Management command to seed benchmark UserProfiles
Follows Django management command best practices
"""
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction

from apps.main.services.benchmark_profile_factory import BenchmarkProfileFactory

class Command(BaseCommand):
    help = 'Seed benchmark UserProfiles for quick agent benchmarking'
    
    def add_arguments(self, parser):
        """Add command line arguments following Django conventions."""
        parser.add_argument(
            '--template',
            type=str,
            help='Specific template to create (default: all templates)'
        )
        parser.add_argument(
            '--recreate',
            action='store_true',
            help='Delete existing profiles and recreate them'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be created without actually creating'
        )
    
    def handle(self, *args, **options):
        """Execute command with proper error handling and logging."""
        try:
            if options['dry_run']:
                self._dry_run_mode(options)
                return
            
            if options['recreate']:
                self._recreate_profiles(options)
            else:
                self._create_profiles(options)
                
        except Exception as e:
            raise CommandError(f"Command failed: {e}")
    
    def _dry_run_mode(self, options):
        """Show what would be created in dry run mode."""
        templates = self._get_templates_to_process(options)
        
        self.stdout.write(
            self.style.WARNING('DRY RUN MODE - No profiles will be created')
        )
        
        for template_name in templates:
            template = BenchmarkProfileFactory.PROFILE_TEMPLATES[template_name]
            self.stdout.write(f"Would create: {template['profile_name']}")
            self.stdout.write(f"  Description: {template['description']}")
            self.stdout.write(f"  Goals: {len(template['goals'])}")
            self.stdout.write(f"  Beliefs: {len(template['beliefs'])}")
            self.stdout.write("")
    
    @transaction.atomic
    def _create_profiles(self, options):
        """Create benchmark profiles."""
        templates = self._get_templates_to_process(options)
        created_count = 0
        skipped_count = 0
        
        for template_name in templates:
            try:
                # Use get_or_create to avoid duplicates
                profile = BenchmarkProfileFactory.get_or_create_benchmark_profile(
                    template_name
                )
                
                if profile:
                    self.stdout.write(
                        self.style.SUCCESS(f"✓ Created: {profile.profile_name}")
                    )
                    created_count += 1
                else:
                    self.stdout.write(
                        self.style.WARNING(f"- Skipped (exists): {template_name}")
                    )
                    skipped_count += 1
                    
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"✗ Failed to create {template_name}: {e}")
                )
        
        self.stdout.write(
            self.style.SUCCESS(
                f"\nCompleted: {created_count} created, {skipped_count} skipped"
            )
        )
    
    def _recreate_profiles(self, options):
        """Delete existing profiles and recreate them."""
        from apps.user.models import UserProfile
        
        templates = self._get_templates_to_process(options)
        
        # Delete existing benchmark profiles
        template_objects = BenchmarkProfileFactory.PROFILE_TEMPLATES
        profile_names = [
            template_objects[name]['profile_name'] 
            for name in templates
        ]
        
        deleted_count = UserProfile.objects.filter(
            profile_name__in=profile_names,
            is_real=False
        ).delete()[0]
        
        if deleted_count > 0:
            self.stdout.write(
                self.style.WARNING(f"Deleted {deleted_count} existing profiles")
            )
        
        # Create new profiles
        self._create_profiles(options)
    
    def _get_templates_to_process(self, options):
        """Get list of templates to process based on options."""
        if options['template']:
            template_name = options['template']
            if template_name not in BenchmarkProfileFactory.PROFILE_TEMPLATES:
                available = list(BenchmarkProfileFactory.PROFILE_TEMPLATES.keys())
                raise CommandError(
                    f"Unknown template '{template_name}'. Available: {available}"
                )
            return [template_name]
        else:
            return list(BenchmarkProfileFactory.PROFILE_TEMPLATES.keys())
```

### **Phase 5: API Integration**

**File**: `backend/apps/main/views.py` (extend existing file)

```python
"""
Add quick benchmark API endpoint to existing views
Follows Django REST patterns and existing authentication
"""
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.contrib.admin.views.decorators import staff_member_required
import json
import logging

from apps.main.services.quick_benchmark_service import QuickBenchmarkService

logger = logging.getLogger(__name__)

@staff_member_required  # Use existing admin authentication
@require_http_methods(["POST"])
@csrf_exempt  # For API usage, implement proper CSRF as needed
def quick_benchmark_api(request):
    """
    API endpoint for quick agent benchmarking.
    Integrates with existing admin authentication and error handling patterns.
    """
    try:
        # Parse request data
        data = json.loads(request.body)
        
        # Validate required fields
        required_fields = ['agent_name', 'profile_template', 'evaluation_template']
        missing_fields = [field for field in required_fields if field not in data]
        if missing_fields:
            return JsonResponse({
                'error': f'Missing required fields: {missing_fields}'
            }, status=400)
        
        # Initialize service
        service = QuickBenchmarkService()
        
        # Run benchmark
        benchmark_run = service.run_quick_benchmark(
            agent_name=data['agent_name'],
            profile_template=data['profile_template'],
            evaluation_template=data['evaluation_template'],
            scenario_context=data.get('scenario_context')
        )
        
        # Return response with link to existing admin interface
        return JsonResponse({
            'success': True,
            'benchmark_run_id': str(benchmark_run.id),
            'admin_url': service.get_benchmark_results_url(benchmark_run),
            'results_summary': {
                'agent_output_length': len(benchmark_run.agent_output or ''),
                'execution_time': benchmark_run.execution_time,
                'token_usage': benchmark_run.total_tokens,
                'cost_estimate': benchmark_run.cost_estimate
            }
        })
        
    except ValidationError as e:
        logger.warning(f"Quick benchmark validation error: {e}")
        return JsonResponse({'error': str(e)}, status=400)
        
    except Exception as e:
        logger.error(f"Quick benchmark API error: {e}")
        return JsonResponse({
            'error': 'Internal server error. Check logs for details.'
        }, status=500)

@staff_member_required
@require_http_methods(["GET"])
def quick_benchmark_options(request):
    """
    Get available options for quick benchmarking.
    Returns templates, agents, and evaluation criteria.
    """
    try:
        service = QuickBenchmarkService()
        options = service.get_available_options()
        
        return JsonResponse({
            'success': True,
            'options': options
        })
        
    except Exception as e:
        logger.error(f"Failed to get benchmark options: {e}")
        return JsonResponse({
            'error': 'Failed to retrieve options'
        }, status=500)
```

**File**: `backend/apps/main/urls.py` (extend existing file)

```python
"""
Add quick benchmark URLs to existing URL configuration
"""
from django.urls import path
from . import views

# Add these to existing urlpatterns
urlpatterns = [
    # ... existing patterns ...
    
    # Quick benchmark API endpoints
    path('api/quick-benchmark/', views.quick_benchmark_api, name='quick_benchmark_api'),
    path('api/quick-benchmark/options/', views.quick_benchmark_options, name='quick_benchmark_options'),
]
```

## 🧪 **Testing Strategy**

### **Unit Tests**

**File**: `backend/apps/main/tests/test_quick_benchmark_service.py`

```python
"""
Comprehensive unit tests for quick benchmark functionality
Follows Django testing best practices and existing test patterns
"""
from django.test import TestCase, TransactionTestCase
from django.core.exceptions import ValidationError
from unittest.mock import Mock, patch

from apps.main.services.benchmark_profile_factory import BenchmarkProfileFactory
from apps.main.services.simple_evaluation_adapter import SimpleEvaluationAdapter
from apps.main.services.quick_benchmark_service import QuickBenchmarkService
from apps.user.models import UserProfile
from apps.main.models import GenericAgent, BenchmarkRun

class TestBenchmarkProfileFactory(TestCase):
    """Test benchmark profile factory functionality."""
    
    def test_create_benchmark_profile_success(self):
        """Test successful profile creation."""
        profile = BenchmarkProfileFactory.create_benchmark_profile('anxious_new_user')
        
        self.assertIsInstance(profile, UserProfile)
        self.assertFalse(profile.is_real)  # Key assertion
        self.assertEqual(profile.profile_name, 'Benchmark_Anxious_New_User')
        self.assertTrue(profile.trait_inclinations.exists())
        self.assertTrue(profile.goals.exists())
        self.assertTrue(profile.beliefs.exists())
    
    def test_create_benchmark_profile_invalid_template(self):
        """Test error handling for invalid template."""
        with self.assertRaises(ValidationError):
            BenchmarkProfileFactory.create_benchmark_profile('invalid_template')
    
    def test_get_or_create_idempotent(self):
        """Test that get_or_create is idempotent."""
        profile1 = BenchmarkProfileFactory.get_or_create_benchmark_profile('anxious_new_user')
        profile2 = BenchmarkProfileFactory.get_or_create_benchmark_profile('anxious_new_user')
        
        self.assertEqual(profile1.id, profile2.id)

class TestSimpleEvaluationAdapter(TestCase):
    """Test simple evaluation adapter functionality."""
    
    def setUp(self):
        self.adapter = SimpleEvaluationAdapter()
        self.mock_benchmark_run = Mock(spec=BenchmarkRun)
    
    @patch('apps.main.services.simple_evaluation_adapter.SemanticEvaluator')
    def test_evaluate_with_simple_prompt_success(self, mock_evaluator):
        """Test successful evaluation with simple prompt."""
        mock_evaluator.return_value.evaluate.return_value = {'score': 8.5}
        
        result = self.adapter.evaluate_with_simple_prompt(
            agent_response="Test response",
            evaluation_template="mentor_helpfulness",
            context={},
            benchmark_run=self.mock_benchmark_run
        )
        
        self.assertEqual(result['score'], 8.5)
        mock_evaluator.return_value.evaluate.assert_called_once()
    
    def test_evaluate_invalid_template(self):
        """Test error handling for invalid evaluation template."""
        with self.assertRaises(ValidationError):
            self.adapter.evaluate_with_simple_prompt(
                agent_response="Test",
                evaluation_template="invalid_template",
                context={},
                benchmark_run=self.mock_benchmark_run
            )

class TestQuickBenchmarkService(TransactionTestCase):
    """Test quick benchmark service integration."""
    
    def setUp(self):
        # Create test agent
        self.agent = GenericAgent.objects.create(
            name='test_mentor',
            role='mentor',
            description='Test mentor agent'
        )
        
        self.service = QuickBenchmarkService()
    
    @patch('apps.main.services.quick_benchmark_service.AgentBenchmarker')
    def test_run_quick_benchmark_success(self, mock_benchmarker):
        """Test successful quick benchmark execution."""
        # Mock benchmark run result
        mock_run = Mock(spec=BenchmarkRun)
        mock_run.agent_output = "Test agent response"
        mock_benchmarker.return_value.run_benchmark.return_value = mock_run
        
        result = self.service.run_quick_benchmark(
            agent_name='test_mentor',
            profile_template='anxious_new_user',
            evaluation_template='mentor_helpfulness'
        )
        
        self.assertEqual(result, mock_run)
        mock_benchmarker.return_value.run_benchmark.assert_called_once()
    
    def test_run_quick_benchmark_invalid_agent(self):
        """Test error handling for invalid agent name."""
        with self.assertRaises(ValidationError):
            self.service.run_quick_benchmark(
                agent_name='nonexistent_agent',
                profile_template='anxious_new_user',
                evaluation_template='mentor_helpfulness'
            )
    
    def test_get_available_options(self):
        """Test retrieval of available benchmark options."""
        options = self.service.get_available_options()
        
        self.assertIn('profile_templates', options)
        self.assertIn('evaluation_templates', options)
        self.assertIn('available_agents', options)
        self.assertGreater(len(options['profile_templates']), 0)
```

## 📋 **Deployment Checklist**

### **Pre-Deployment**
- [ ] Run all existing tests to ensure no regressions
- [ ] Run new unit tests for quick benchmark functionality
- [ ] Test management command in development environment
- [ ] Verify API endpoints with authentication
- [ ] Check logging configuration for new services

### **Database Migration**
```bash
# No new models needed - leverages existing UserProfile with is_real=False
python manage.py makemigrations --check  # Should show no new migrations needed
```

### **Deployment Steps**
```bash
# 1. Deploy code
git push origin main

# 2. Seed benchmark profiles  
python manage.py seed_benchmark_profiles

# 3. Test quick benchmark API
curl -X POST /api/quick-benchmark/ \
  -H "Content-Type: application/json" \
  -d '{"agent_name": "mentor", "profile_template": "anxious_new_user", "evaluation_template": "mentor_helpfulness"}'

# 4. Verify results in existing admin interface
```

### **Monitoring & Observability**
- [ ] Monitor new service logs for errors
- [ ] Track benchmark execution times and costs
- [ ] Set up alerts for benchmark failures
- [ ] Monitor UserProfile creation patterns

## 🎯 **Success Metrics**

1. **Implementation Time**: Complete implementation in 4 hours
2. **Code Reuse**: 90%+ reuse of existing benchmarking infrastructure
3. **Test Coverage**: 100% coverage for new service classes
4. **Performance**: Quick benchmarks complete in under 30 seconds
5. **Usability**: Single API call creates and executes benchmark
6. **Maintainability**: Zero additional maintenance burden beyond existing system

This implementation follows established Django patterns, leverages your existing sophisticated benchmarking infrastructure, and provides the simplicity you're looking for without architectural redundancy.
