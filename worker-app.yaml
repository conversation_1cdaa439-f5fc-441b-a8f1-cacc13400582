name: goali-worker-app
region: lon
services:
- name: goali-worker
  environment_slug: python
  instance_size_slug: apps-s-1vcpu-1gb
  instance_count: 1
  source_dir: backend
  github:
    branch: gguine/prod-slow
    deploy_on_push: true
    repo: elgui/goali
  run_command: celery -A config worker --loglevel=info --concurrency=2
  envs:
  - key: DJANGO_SETTINGS_MODULE
    scope: RUN_AND_BUILD_TIME
    value: config.settings.prod
  - key: DJANGO_SECRET_KEY
    scope: RUN_AND_BUILD_TIME
    type: SECRET
  - key: MISTRAL_API_KEY
    scope: RUN_AND_BUILD_TIME
    type: SECRET
  - key: DATABASE_URL
    scope: RUN_AND_BUILD_TIME
    type: SECRET
  - key: REDIS_URL
    scope: RUN_AND_BUILD_TIME
    type: SECRET
alerts:
- rule: DEPLOYMENT_FAILED
- rule: DOMAIN_FAILED
features:
- buildpack-stack=ubuntu-22
