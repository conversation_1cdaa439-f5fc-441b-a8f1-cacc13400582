#!/bin/bash

# Production Test Environment Startup Script
# This script starts Goali in a production-like environment for security testing

set -e

echo "🚀 Starting Goali in Production-Test Mode"
echo "=========================================="

# Check if Docker and Docker Compose are available
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed or not in PATH"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed or not in PATH"
    exit 1
fi

# Check if production-test configuration exists
if [ ! -f "docker-compose.production-test.yml" ]; then
    echo "❌ Production-test configuration not found: docker-compose.production-test.yml"
    exit 1
fi

if [ ! -f "nginx/production-test.conf" ]; then
    echo "❌ Nginx production-test configuration not found: nginx/production-test.conf"
    exit 1
fi

# Stop any existing containers
echo "🛑 Stopping existing containers..."
docker-compose -f docker-compose.production-test.yml down --remove-orphans

# Clean up old volumes if requested
if [ "$1" = "--clean" ]; then
    echo "🧹 Cleaning up old volumes..."
    docker-compose -f docker-compose.production-test.yml down -v
    docker volume prune -f
fi

# Build and start services
echo "🔨 Building and starting production-test services..."
docker-compose -f docker-compose.production-test.yml up --build -d

# Wait for services to be healthy
echo "⏳ Waiting for services to be healthy..."
sleep 10

# Check service health
echo "🔍 Checking service health..."

# Check database
if docker-compose -f docker-compose.production-test.yml exec -T db pg_isready -U goali_user -d goali_prod_test; then
    echo "✅ Database is healthy"
else
    echo "❌ Database is not healthy"
    docker-compose -f docker-compose.production-test.yml logs db
    exit 1
fi

# Check Redis
if docker-compose -f docker-compose.production-test.yml exec -T redis redis-cli ping | grep -q PONG; then
    echo "✅ Redis is healthy"
else
    echo "❌ Redis is not healthy"
    docker-compose -f docker-compose.production-test.yml logs redis
    exit 1
fi

# Check web service
if curl -f http://localhost:8001/api/health/ > /dev/null 2>&1; then
    echo "✅ Web service is healthy"
else
    echo "❌ Web service is not healthy"
    docker-compose -f docker-compose.production-test.yml logs web
    exit 1
fi

# Check Nginx
if curl -f http://localhost:3001/health > /dev/null 2>&1; then
    echo "✅ Nginx is healthy"
else
    echo "❌ Nginx is not healthy"
    docker-compose -f docker-compose.production-test.yml logs nginx
    exit 1
fi

echo ""
echo "🎉 Production-Test Environment Started Successfully!"
echo "=================================================="
echo ""
echo "🌐 Frontend URL: http://localhost:3001"
echo "🔧 Backend API: http://localhost:8001/api/"
echo "👨‍💼 Django Admin: http://localhost:8001/admin/"
echo ""
echo "🔐 Default Admin Credentials:"
echo "   Username: admin"
echo "   Password: admin123"
echo ""
echo "🔒 Security Features Enabled:"
echo "   ✅ Authentication required"
echo "   ✅ Authorization checks"
echo "   ✅ CSRF protection"
echo "   ✅ Security headers"
echo "   ✅ Rate limiting"
echo "   ❌ Debug mode disabled"
echo "   ❌ Demo mode disabled"
echo ""
echo "📊 Monitoring:"
echo "   docker-compose -f docker-compose.production-test.yml logs -f"
echo ""
echo "🛑 To stop:"
echo "   docker-compose -f docker-compose.production-test.yml down"
echo ""
echo "⚠️  This is a production-test environment for security validation."
echo "   Real production deployment requires additional security measures:"
echo "   - HTTPS/TLS certificates"
echo "   - Secure secrets management"
echo "   - Database security hardening"
echo "   - Network security (VPC, firewalls)"
echo "   - Monitoring and alerting"
echo "   - Backup and disaster recovery"
echo ""
