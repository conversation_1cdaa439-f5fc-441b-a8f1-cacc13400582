#!/bin/bash

# Security Testing Script for Goali
# Tests authentication, authorization, and security fixes

set -e

echo "🔒 Goali Security Testing Suite"
echo "==============================="

# Configuration
BACKEND_URL="http://localhost:8001"
FRONTEND_URL="http://localhost:3001"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test counters
TESTS_PASSED=0
TESTS_FAILED=0
TESTS_TOTAL=0

# Helper functions
log_test() {
    echo -e "${BLUE}[TEST]${NC} $1"
    TESTS_TOTAL=$((TESTS_TOTAL + 1))
}

log_pass() {
    echo -e "${GREEN}[PASS]${NC} $1"
    TESTS_PASSED=$((TESTS_PASSED + 1))
}

log_fail() {
    echo -e "${RED}[FAIL]${NC} $1"
    TESTS_FAILED=$((TESTS_FAILED + 1))
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# Check if services are running
check_services() {
    log_info "Checking if services are running..."
    
    if ! curl -s "$BACKEND_URL/api/health/" > /dev/null; then
        echo -e "${RED}❌ Backend service is not running at $BACKEND_URL${NC}"
        echo "Please start the production-test environment first:"
        echo "  ./scripts/start-production-test.sh"
        exit 1
    fi
    
    if ! curl -s "$FRONTEND_URL/health" > /dev/null; then
        echo -e "${RED}❌ Frontend service is not running at $FRONTEND_URL${NC}"
        echo "Please start the production-test environment first:"
        echo "  ./scripts/start-production-test.sh"
        exit 1
    fi
    
    log_info "✅ Services are running"
}

# Test 1: Unauthenticated access should be denied
test_unauthenticated_access() {
    log_test "Testing unauthenticated access to user profile API"
    
    # Try to access user profile without authentication
    response=$(curl -s -w "%{http_code}" -o /dev/null "$BACKEND_URL/api/user/profile/2/")
    
    if [ "$response" = "401" ]; then
        log_pass "Unauthenticated access properly denied (HTTP 401)"
    else
        log_fail "Unauthenticated access not properly denied (HTTP $response, expected 401)"
    fi
}

# Test 2: Create test users and authenticate
create_test_users() {
    log_info "Creating test users for security testing..."
    
    # Create test users via Django shell
    docker-compose -f docker-compose.production-test.yml exec -T web python manage.py shell << 'EOF'
from django.contrib.auth.models import User
from apps.user.models import UserProfile

# Create test user 1
if not User.objects.filter(username='testuser1').exists():
    user1 = User.objects.create_user('testuser1', '<EMAIL>', 'testpass123')
    profile1 = UserProfile.objects.create(user=user1, profile_name='Test User 1')
    print(f"Created user1 with profile ID: {profile1.id}")
else:
    user1 = User.objects.get(username='testuser1')
    profile1 = UserProfile.objects.get(user=user1)
    print(f"User1 exists with profile ID: {profile1.id}")

# Create test user 2
if not User.objects.filter(username='testuser2').exists():
    user2 = User.objects.create_user('testuser2', '<EMAIL>', 'testpass123')
    profile2 = UserProfile.objects.create(user=user2, profile_name='Test User 2')
    print(f"Created user2 with profile ID: {profile2.id}")
else:
    user2 = User.objects.get(username='testuser2')
    profile2 = UserProfile.objects.get(user=user2)
    print(f"User2 exists with profile ID: {profile2.id}")
EOF
    
    log_info "✅ Test users created/verified"
}

# Test 3: Authentication and session management
test_authentication() {
    log_test "Testing user authentication"
    
    # Test login with valid credentials
    response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d '{"username":"testuser1","password":"testpass123"}' \
        -c cookies.txt \
        -w "%{http_code}" \
        "$BACKEND_URL/api/auth/login/")
    
    if echo "$response" | grep -q "200"; then
        log_pass "User authentication successful"
        
        # Test token verification
        verify_response=$(curl -s -X POST \
            -b cookies.txt \
            -w "%{http_code}" \
            "$BACKEND_URL/api/auth/verify/")
        
        if echo "$verify_response" | grep -q "200"; then
            log_pass "Token verification successful"
        else
            log_fail "Token verification failed"
        fi
    else
        log_fail "User authentication failed (HTTP $response)"
    fi
    
    # Test login with invalid credentials
    invalid_response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d '{"username":"testuser1","password":"wrongpassword"}' \
        -w "%{http_code}" \
        -o /dev/null \
        "$BACKEND_URL/api/auth/login/")
    
    if [ "$invalid_response" = "401" ]; then
        log_pass "Invalid credentials properly rejected (HTTP 401)"
    else
        log_fail "Invalid credentials not properly rejected (HTTP $invalid_response, expected 401)"
    fi
}

# Test 4: Authorization - users can only access their own data
test_authorization() {
    log_test "Testing authorization - users can only access their own data"
    
    # Login as testuser1
    curl -s -X POST \
        -H "Content-Type: application/json" \
        -d '{"username":"testuser1","password":"testpass123"}' \
        -c cookies_user1.txt \
        "$BACKEND_URL/api/auth/login/" > /dev/null
    
    # Login as testuser2
    curl -s -X POST \
        -H "Content-Type: application/json" \
        -d '{"username":"testuser2","password":"testpass123"}' \
        -c cookies_user2.txt \
        "$BACKEND_URL/api/auth/login/" > /dev/null
    
    # Get user1's profile ID
    user1_profile_id=$(docker-compose -f docker-compose.production-test.yml exec -T web python manage.py shell -c "
from django.contrib.auth.models import User
from apps.user.models import UserProfile
user = User.objects.get(username='testuser1')
profile = UserProfile.objects.get(user=user)
print(profile.id)
" | tail -n 1 | tr -d '\r')
    
    # Test: user1 accessing their own profile (should work)
    own_profile_response=$(curl -s -b cookies_user1.txt \
        -w "%{http_code}" \
        -o /dev/null \
        "$BACKEND_URL/api/user/profile/")
    
    if [ "$own_profile_response" = "200" ]; then
        log_pass "User can access their own profile"
    else
        log_fail "User cannot access their own profile (HTTP $own_profile_response)"
    fi
    
    # Test: user1 trying to access user2's profile (should fail)
    other_profile_response=$(curl -s -b cookies_user1.txt \
        -w "%{http_code}" \
        -o /dev/null \
        "$BACKEND_URL/api/user/profile/$user1_profile_id/")
    
    if [ "$other_profile_response" = "403" ]; then
        log_pass "Non-staff user cannot access other user's profile (HTTP 403)"
    else
        log_fail "Non-staff user can access other user's profile (HTTP $other_profile_response, expected 403)"
    fi
}

# Test 5: Staff privileges
test_staff_privileges() {
    log_test "Testing staff privileges"
    
    # Login as admin (staff user)
    curl -s -X POST \
        -H "Content-Type: application/json" \
        -d '{"username":"admin","password":"admin123"}' \
        -c cookies_admin.txt \
        "$BACKEND_URL/api/auth/login/" > /dev/null
    
    # Get a regular user's profile ID
    user_profile_id=$(docker-compose -f docker-compose.production-test.yml exec -T web python manage.py shell -c "
from django.contrib.auth.models import User
from apps.user.models import UserProfile
user = User.objects.get(username='testuser1')
profile = UserProfile.objects.get(user=user)
print(profile.id)
" | tail -n 1 | tr -d '\r')
    
    # Test: admin accessing another user's profile (should work)
    admin_access_response=$(curl -s -b cookies_admin.txt \
        -w "%{http_code}" \
        -o /dev/null \
        "$BACKEND_URL/api/user/profile/$user_profile_id/")
    
    if [ "$admin_access_response" = "200" ]; then
        log_pass "Staff user can access other user's profile"
    else
        log_fail "Staff user cannot access other user's profile (HTTP $admin_access_response)"
    fi
}

# Test 6: Security headers
test_security_headers() {
    log_test "Testing security headers"
    
    headers=$(curl -s -I "$FRONTEND_URL/")
    
    if echo "$headers" | grep -q "X-Frame-Options: DENY"; then
        log_pass "X-Frame-Options header present"
    else
        log_fail "X-Frame-Options header missing"
    fi
    
    if echo "$headers" | grep -q "X-Content-Type-Options: nosniff"; then
        log_pass "X-Content-Type-Options header present"
    else
        log_fail "X-Content-Type-Options header missing"
    fi
    
    if echo "$headers" | grep -q "X-XSS-Protection"; then
        log_pass "X-XSS-Protection header present"
    else
        log_fail "X-XSS-Protection header missing"
    fi
}

# Test 7: Debug mode disabled
test_debug_mode() {
    log_test "Testing debug mode is disabled"
    
    # Try to access debug endpoint
    debug_response=$(curl -s -w "%{http_code}" -o /dev/null "$BACKEND_URL/api/debug/users/")
    
    if [ "$debug_response" = "403" ]; then
        log_pass "Debug endpoints properly disabled (HTTP 403)"
    else
        log_fail "Debug endpoints not properly disabled (HTTP $debug_response, expected 403)"
    fi
}

# Cleanup function
cleanup() {
    log_info "Cleaning up test files..."
    rm -f cookies*.txt
}

# Test 8: Advanced penetration testing
test_advanced_penetration() {
    log_test "Advanced penetration testing"

    # SQL Injection attempts
    log_info "Testing SQL injection protection..."
    sql_payloads=(
        "'; DROP TABLE auth_user; --"
        "1' OR '1'='1"
        "UNION SELECT * FROM auth_user"
        "admin'/**/OR/**/1=1#"
    )

    for payload in "${sql_payloads[@]}"; do
        response=$(curl -s -w "%{http_code}" -o /dev/null \
            "$BACKEND_URL/api/activities/catalog/?search=$(echo "$payload" | sed 's/ /%20/g')")

        if [ "$response" != "500" ]; then
            log_pass "SQL injection payload blocked: $payload"
        else
            log_fail "SQL injection payload caused server error: $payload"
        fi
    done

    # XSS attempts
    log_info "Testing XSS protection..."
    xss_payloads=(
        "<script>alert('xss')</script>"
        "javascript:alert('xss')"
        "<img src=x onerror=alert('xss')>"
        "';alert('xss');//"
    )

    # Login as test user for XSS testing
    curl -s -X POST \
        -H "Content-Type: application/json" \
        -d '{"username":"testuser1","password":"testpass123"}' \
        -c cookies_xss.txt \
        "$BACKEND_URL/api/auth/login/" > /dev/null

    for payload in "${xss_payloads[@]}"; do
        response=$(curl -s -b cookies_xss.txt \
            -X POST \
            -H "Content-Type: application/json" \
            -d "{\"name\":\"$payload\",\"description\":\"Test\"}" \
            -w "%{http_code}" \
            -o /dev/null \
            "$BACKEND_URL/api/activities/create/")

        if [ "$response" = "200" ] || [ "$response" = "400" ]; then
            log_pass "XSS payload handled safely: $payload"
        else
            log_warn "XSS payload response: $response for $payload"
        fi
    done
}

# Test 9: Session security
test_session_security() {
    log_test "Session security testing"

    # Test session fixation
    log_info "Testing session fixation protection..."

    # Get initial session
    initial_cookies=$(curl -s -c initial_session.txt -b initial_session.txt \
        "$BACKEND_URL/api/health/" && cat initial_session.txt | grep -o 'sessionid[^;]*' || echo "no_session")

    # Login
    curl -s -X POST \
        -H "Content-Type: application/json" \
        -d '{"username":"testuser1","password":"testpass123"}' \
        -c post_login_session.txt \
        "$BACKEND_URL/api/auth/login/" > /dev/null

    # Check if session changed after login
    post_login_cookies=$(cat post_login_session.txt | grep -o 'sessionid[^;]*' || echo "no_session")

    if [ "$initial_cookies" != "$post_login_cookies" ]; then
        log_pass "Session ID changes after login (session fixation protection)"
    else
        log_warn "Session ID unchanged after login"
    fi

    # Test concurrent sessions
    log_info "Testing concurrent session handling..."

    # Login from two different "browsers"
    curl -s -X POST \
        -H "Content-Type: application/json" \
        -d '{"username":"testuser1","password":"testpass123"}' \
        -c session1.txt \
        "$BACKEND_URL/api/auth/login/" > /dev/null

    curl -s -X POST \
        -H "Content-Type: application/json" \
        -d '{"username":"testuser1","password":"testpass123"}' \
        -c session2.txt \
        "$BACKEND_URL/api/auth/login/" > /dev/null

    # Both sessions should be valid (or implement session limiting)
    session1_valid=$(curl -s -b session1.txt \
        -X POST \
        "$BACKEND_URL/api/auth/verify/" | grep -o '"valid":true' || echo "invalid")

    session2_valid=$(curl -s -b session2.txt \
        -X POST \
        "$BACKEND_URL/api/auth/verify/" | grep -o '"valid":true' || echo "invalid")

    if [ "$session1_valid" = '"valid":true' ] && [ "$session2_valid" = '"valid":true' ]; then
        log_pass "Concurrent sessions handled appropriately"
    else
        log_warn "Concurrent session handling needs review"
    fi
}

# Test 10: Rate limiting and DoS protection
test_rate_limiting() {
    log_test "Rate limiting and DoS protection"

    log_info "Testing authentication rate limiting..."

    # Attempt rapid authentication requests
    rate_limit_triggered=false
    for i in {1..15}; do
        response=$(curl -s -w "%{http_code}" -o /dev/null \
            -X POST \
            -H "Content-Type: application/json" \
            -d '{"username":"testuser1","password":"wrongpassword"}' \
            "$BACKEND_URL/api/auth/login/")

        if [ "$response" = "429" ]; then
            rate_limit_triggered=true
            break
        fi

        sleep 0.1
    done

    if [ "$rate_limit_triggered" = true ]; then
        log_pass "Rate limiting active for authentication"
    else
        log_warn "Rate limiting not detected for authentication"
    fi
}

# Main test execution
main() {
    echo ""
    check_services
    echo ""

    create_test_users
    echo ""

    test_unauthenticated_access
    test_authentication
    test_authorization
    test_staff_privileges
    test_security_headers
    test_debug_mode
    test_advanced_penetration
    test_session_security
    test_rate_limiting

    echo ""
    echo "🔒 Security Test Results"
    echo "========================"
    echo -e "Total Tests: $TESTS_TOTAL"
    echo -e "${GREEN}Passed: $TESTS_PASSED${NC}"
    echo -e "${RED}Failed: $TESTS_FAILED${NC}"
    echo ""

    if [ $TESTS_FAILED -eq 0 ]; then
        echo -e "${GREEN}🎉 All security tests passed!${NC}"
        echo "The application appears to be secure for production deployment."
        echo ""
        echo "🛡️  Security Validation Complete"
        echo "================================"
        echo "✅ Authentication working correctly"
        echo "✅ Authorization properly enforced"
        echo "✅ Staff privileges controlled"
        echo "✅ Security headers present"
        echo "✅ Debug mode disabled"
        echo "✅ SQL injection protection active"
        echo "✅ XSS protection implemented"
        echo "✅ Session security measures in place"
        echo "✅ Rate limiting functional"
    else
        echo -e "${RED}❌ Some security tests failed!${NC}"
        echo "Please review and fix the failing tests before production deployment."
        exit 1
    fi

    cleanup
}

# Run tests
main
