#!/usr/bin/env python3
"""
Debug WebSocket Connections Script

This script helps identify what's trying to connect to WebSocket endpoints
and provides debugging information for WebSocket routing issues.
"""

import os
import django
import logging
from datetime import datetime

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')
django.setup()

# Configure logging to capture WebSocket connection attempts
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def check_websocket_routes():
    """Check configured WebSocket routes"""
    print("🔍 Checking WebSocket Route Configuration")
    print("=" * 50)
    
    try:
        from apps.main.routing import websocket_urlpatterns as main_patterns
        from apps.admin_tools.routing import websocket_urlpatterns as admin_patterns
        
        print("📋 Main App WebSocket Routes:")
        for pattern in main_patterns:
            print(f"  - {pattern.pattern.pattern}")
        
        print("\n📋 Admin Tools WebSocket Routes:")
        for pattern in admin_patterns:
            print(f"  - {pattern.pattern.pattern}")
            
        print(f"\n✅ Total configured routes: {len(main_patterns) + len(admin_patterns)}")
        
    except Exception as e:
        print(f"❌ Error checking routes: {e}")

def check_asgi_configuration():
    """Check ASGI application configuration"""
    print("\n🔍 Checking ASGI Configuration")
    print("=" * 50)
    
    try:
        from config.asgi import application
        print("✅ ASGI application loaded successfully")
        
        # Check if it's a ProtocolTypeRouter
        if hasattr(application, 'application_mapping'):
            protocols = list(application.application_mapping.keys())
            print(f"📋 Supported protocols: {protocols}")
        else:
            print("⚠️  Application is not a ProtocolTypeRouter")
            
    except Exception as e:
        print(f"❌ Error checking ASGI: {e}")

def check_channels_configuration():
    """Check Django Channels configuration"""
    print("\n🔍 Checking Channels Configuration")
    print("=" * 50)
    
    try:
        from django.conf import settings
        
        asgi_app = getattr(settings, 'ASGI_APPLICATION', None)
        print(f"📋 ASGI_APPLICATION: {asgi_app}")
        
        channel_layers = getattr(settings, 'CHANNEL_LAYERS', {})
        if channel_layers:
            default_layer = channel_layers.get('default', {})
            backend = default_layer.get('BACKEND', 'Not configured')
            config = default_layer.get('CONFIG', {})
            print(f"📋 Channel Layer Backend: {backend}")
            print(f"📋 Channel Layer Config: {config}")
        else:
            print("⚠️  No CHANNEL_LAYERS configured")
            
    except Exception as e:
        print(f"❌ Error checking channels: {e}")

def check_redis_connection():
    """Check Redis connection for channels"""
    print("\n🔍 Checking Redis Connection")
    print("=" * 50)
    
    try:
        import redis
        from django.conf import settings
        
        channel_layers = getattr(settings, 'CHANNEL_LAYERS', {})
        if channel_layers:
            config = channel_layers.get('default', {}).get('CONFIG', {})
            hosts = config.get('hosts', [])
            
            if hosts:
                host_info = hosts[0]
                if isinstance(host_info, tuple):
                    host, port = host_info
                else:
                    host, port = host_info, 6379
                
                print(f"📋 Connecting to Redis at {host}:{port}")
                
                r = redis.Redis(host=host, port=port, decode_responses=True)
                r.ping()
                print("✅ Redis connection successful")
                
                # Check Redis info
                info = r.info()
                print(f"📋 Redis version: {info.get('redis_version', 'Unknown')}")
                print(f"📋 Connected clients: {info.get('connected_clients', 'Unknown')}")
                
            else:
                print("⚠️  No Redis hosts configured")
        else:
            print("⚠️  No channel layers configured")
            
    except Exception as e:
        print(f"❌ Error checking Redis: {e}")

def provide_debugging_tips():
    """Provide debugging tips for WebSocket issues"""
    print("\n💡 WebSocket Debugging Tips")
    print("=" * 50)
    
    tips = [
        "1. Check browser developer tools for WebSocket connection attempts",
        "2. Look for JavaScript errors in browser console",
        "3. Verify that frontend is using correct WebSocket URLs",
        "4. Check if any browser tabs are still open with old connections",
        "5. Clear browser cache and cookies",
        "6. Restart Docker containers to clear any cached connections",
        "7. Check Docker logs for more detailed error information",
        "8. Verify that Redis is running and accessible",
        "9. Test WebSocket connection with a simple client",
        "10. Check firewall and network configuration"
    ]
    
    for tip in tips:
        print(f"  {tip}")

def main():
    """Main debugging function"""
    print("🚀 WebSocket Connection Debugger")
    print("=" * 50)
    print(f"⏰ Started at: {datetime.now()}")
    print()
    
    check_websocket_routes()
    check_asgi_configuration()
    check_channels_configuration()
    check_redis_connection()
    provide_debugging_tips()
    
    print("\n" + "=" * 50)
    print("🏁 Debugging complete!")
    print("\n💡 If the issue persists:")
    print("   1. Close all browser tabs")
    print("   2. Restart Docker containers: docker-compose restart")
    print("   3. Check for any applications trying to connect to ws://localhost:8000/ws")

if __name__ == "__main__":
    main()
