#!/usr/bin/env python3
"""
Debug script to test agent output data flow
"""
import os
import sys
import django
import asyncio
import json

# Add the backend directory to the Python path
sys.path.insert(0, '/usr/src/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from apps.main.agents.resource_agent import ResourceAgent
from apps.main.services.database_service import RealDatabaseService
from apps.main.llm.service import RealLLMClient
from apps.main.models import LLMConfig
from apps.main.graphs.wheel_generation_graph import WheelGenerationState

async def test_resource_agent():
    """Test ResourceAgent output data directly"""
    print("🔍 Testing ResourceAgent output data flow...")
    
    # Get LLM config
    llm_config = LLMConfig.objects.filter(model_name__icontains='gpt-4o-mini').first()
    if not llm_config:
        llm_config = LLMConfig.objects.first()
    
    if not llm_config:
        print("❌ No LLM config found")
        return
    
    # Create agent
    agent = ResourceAgent(
        user_profile_id="2",
        llm_config=llm_config,
        llm_client=RealLLMClient(llm_config),
        db_service=RealDatabaseService()
    )
    
    # Create test state
    state = WheelGenerationState(
        user_profile_id="2",
        context_packet={
            "user_id": "2",
            "task_type": "wheel_generation",
            "trust_level": 65,
            "user_message": "I want to improve my wellness and productivity",
            "session_timestamp": "2025-06-01T09:14:20.958395",
            "user_ws_session_name": "test-session",
            "reported_environment": "",
            "reported_time_availability": ""
        },
        use_real_llm=True,
        use_real_tools=True,
        use_real_db=True
    )
    
    print(f"📝 Initial state.output_data: {state.output_data}")
    
    # Call agent process method directly
    print("🚀 Calling agent.process() directly...")
    try:
        state_updates = await agent.process(state)
        print(f"✅ Agent.process() returned: {type(state_updates)}")
        print(f"📝 State updates keys: {list(state_updates.keys()) if isinstance(state_updates, dict) else 'Not a dict'}")
        
        if isinstance(state_updates, dict) and 'output_data' in state_updates:
            output_data = state_updates['output_data']
            print(f"📝 Output data type: {type(output_data)}")
            print(f"📝 Output data keys: {list(output_data.keys()) if isinstance(output_data, dict) else 'Not a dict'}")
            
            if isinstance(output_data, dict):
                # Check for rich data structures
                if 'combined_resource_context' in output_data:
                    print("✅ Found combined_resource_context")
                    crc = output_data['combined_resource_context']
                    print(f"   Type: {type(crc)}")
                    if isinstance(crc, dict):
                        print(f"   Keys: {list(crc.keys())}")
                else:
                    print("❌ No combined_resource_context found")
                
                if 'resource_context' in output_data:
                    print("✅ Found resource_context")
                else:
                    print("❌ No resource_context found")
        else:
            print("❌ No output_data in state_updates")
            
    except Exception as e:
        print(f"❌ Error calling agent.process(): {e}")
        import traceback
        traceback.print_exc()
    
    # Now test the full __call__ method
    print("\n🚀 Calling agent.__call__() (BaseAgent.__call__)...")
    try:
        updated_state = await agent(state)
        print(f"✅ Agent.__call__() returned: {type(updated_state)}")
        print(f"📝 Updated state.output_data: {updated_state.output_data}")
        
        if updated_state.output_data:
            print(f"📝 Updated state.output_data keys: {list(updated_state.output_data.keys())}")
            if 'combined_resource_context' in updated_state.output_data:
                print("✅ Found combined_resource_context in updated state")
            else:
                print("❌ No combined_resource_context in updated state")
        else:
            print("❌ Updated state.output_data is empty")
            
    except Exception as e:
        print(f"❌ Error calling agent.__call__(): {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_resource_agent())
