#!/usr/bin/env python3
"""
Intelligent Benchmark Runner

This script mimics the quick_test.js functionality to run meaningful benchmarks
with proper scenario and criteria selection, using real fake user profiles.
"""

import os
import sys
import django
import json
import time
import requests
from datetime import datetime

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from apps.main.models import BenchmarkScenario, EvaluationCriteriaTemplate
from apps.user.models import UserProfile
from apps.main.tasks.benchmark_tasks import run_template_test


def get_available_scenarios():
    """Get available active scenarios."""
    scenarios = BenchmarkScenario.objects.filter(is_active=True)
    print("Available scenarios:")
    for scenario in scenarios:
        print(f"  ID: {scenario.id}, Name: {scenario.name}, Agent: {scenario.agent_role}")
    return scenarios


def get_available_templates():
    """Get available active evaluation templates."""
    templates = EvaluationCriteriaTemplate.objects.filter(is_active=True)
    print("Available evaluation templates:")
    for template in templates:
        print(f"  ID: {template.id}, Name: {template.name}, Category: {template.category}")
    return templates


def get_fake_users():
    """Get available fake user profiles."""
    users = UserProfile.objects.filter(is_real=False)
    print("Available fake users:")
    for user in users:
        print(f"  ID: {user.id}, Profile: {user.profile_name}")
    return users


def run_benchmark_test(scenario_id, template_id, user_id, execution_mode='full-real', runs=1):
    """
    Run a benchmark test with the specified parameters.
    
    Args:
        scenario_id: ID of the benchmark scenario
        template_id: ID of the evaluation criteria template
        user_id: ID of the fake user profile
        execution_mode: Execution mode ('mock', 'real-tools', 'real-llm', 'real-db', 'partial-real', 'full-real')
        runs: Number of runs to execute
    """
    
    # Map execution modes to parameters
    mode_params = {
        'mock': {
            'use_real_llm': False,
            'use_real_tools': False,
            'use_real_db': False
        },
        'real-tools': {
            'use_real_llm': False,
            'use_real_tools': True,
            'use_real_db': False
        },
        'real-llm': {
            'use_real_llm': True,
            'use_real_tools': False,
            'use_real_db': False
        },
        'real-db': {
            'use_real_llm': False,
            'use_real_tools': False,
            'use_real_db': True
        },
        'partial-real': {
            'use_real_llm': False,
            'use_real_tools': True,
            'use_real_db': True
        },
        'full-real': {
            'use_real_llm': True,
            'use_real_tools': True,
            'use_real_db': True
        }
    }
    
    execution_params = mode_params.get(execution_mode, mode_params['mock'])
    
    print(f"\n🚀 Starting benchmark test:")
    print(f"  Scenario ID: {scenario_id}")
    print(f"  Template ID: {template_id}")
    print(f"  User ID: {user_id}")
    print(f"  Execution Mode: {execution_mode}")
    print(f"  Runs: {runs}")
    print(f"  Real LLM: {execution_params['use_real_llm']}")
    print(f"  Real Tools: {execution_params['use_real_tools']}")
    print(f"  Real DB: {execution_params['use_real_db']}")
    
    try:
        # Run the benchmark test
        result = run_template_test.delay(
            scenario_id=scenario_id,
            template_id=template_id,
            user_id=user_id,
            runs=runs,
            **execution_params
        )
        
        print(f"\n✅ Benchmark task started with ID: {result.id}")
        print(f"⏳ Waiting for completion...")
        
        # Wait for completion with timeout
        start_time = time.time()
        timeout = 300  # 5 minutes
        
        while not result.ready():
            elapsed = time.time() - start_time
            if elapsed > timeout:
                print(f"❌ Timeout after {timeout} seconds")
                return None
                
            print(f"⏳ Still running... ({elapsed:.1f}s elapsed)")
            time.sleep(10)
        
        # Get the result
        if result.successful():
            task_result = result.get()
            print(f"\n🎉 Benchmark completed successfully!")
            print(f"📊 Results:")
            
            if isinstance(task_result, dict):
                for key, value in task_result.items():
                    if key == 'semantic_score':
                        print(f"  {key}: {value:.4f}")
                    elif key == 'execution_time':
                        print(f"  {key}: {value:.2f}s")
                    elif key == 'token_usage':
                        if isinstance(value, dict):
                            print(f"  {key}: {value}")
                        else:
                            print(f"  {key}: {value}")
                    else:
                        print(f"  {key}: {value}")
            else:
                print(f"  Result: {task_result}")
                
            return task_result
        else:
            print(f"❌ Benchmark failed: {result.result}")
            return None
            
    except Exception as e:
        print(f"❌ Error running benchmark: {e}")
        return None


def main():
    """Main function to run intelligent benchmark tests."""
    print("🧠 Intelligent Benchmark Runner")
    print("=" * 50)
    
    # Get available options
    scenarios = get_available_scenarios()
    print()
    templates = get_available_templates()
    print()
    users = get_fake_users()
    print()
    
    # Intelligent selection for wheel generation workflow
    print("🎯 Intelligent Selection for Wheel Generation Workflow:")
    
    # Select wheel generation scenario
    wheel_scenario = None
    for scenario in scenarios:
        if 'wheel' in scenario.name.lower() and scenario.agent_role == 'workflow':
            wheel_scenario = scenario
            break
    
    if not wheel_scenario:
        print("❌ No wheel generation workflow scenario found")
        return
    
    # Select wheel generation evaluation template
    wheel_template = None
    for template in templates:
        if 'wheel' in template.name.lower():
            wheel_template = template
            break
    
    if not wheel_template:
        print("❌ No wheel generation evaluation template found")
        return
    
    # Select a fake user (use the first available)
    fake_user = users.first() if users.exists() else None
    if not fake_user:
        print("❌ No fake users found")
        return
    
    print(f"  Selected Scenario: {wheel_scenario.name} (ID: {wheel_scenario.id})")
    print(f"  Selected Template: {wheel_template.name} (ID: {wheel_template.id})")
    print(f"  Selected User: {fake_user.profile_name} (ID: {fake_user.id})")
    
    # Run the benchmark test
    result = run_benchmark_test(
        scenario_id=wheel_scenario.id,
        template_id=wheel_template.id,
        user_id=fake_user.id,
        execution_mode='full-real',
        runs=1
    )
    
    if result:
        print(f"\n✅ Benchmark test completed successfully!")
    else:
        print(f"\n❌ Benchmark test failed!")


if __name__ == "__main__":
    main()
