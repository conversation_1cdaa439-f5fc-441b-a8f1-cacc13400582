#!/usr/bin/env python3
"""
Integration Validation Script

This script validates the alignment between frontend and backend systems
according to the official WebSocket message specifications.

Usage:
    python tools/integration_validation.py

Requirements:
    - Backend server running on localhost:8000
    - Frontend development server running on localhost:3000
    - Redis server running for WebSocket channels
"""

import asyncio
import json
import websockets
import requests
import time
from typing import Dict, Any, List
from dataclasses import dataclass
from datetime import datetime

@dataclass
class ValidationResult:
    test_name: str
    status: str  # 'PASS', 'FAIL', 'SKIP'
    message: str
    timestamp: datetime
    details: Dict[str, Any] = None

class IntegrationValidator:
    def __init__(self, backend_url: str = "ws://localhost:8000/ws/game/", 
                 frontend_url: str = "http://localhost:3000"):
        self.backend_url = backend_url
        self.frontend_url = frontend_url
        self.results: List[ValidationResult] = []
        self.websocket = None
        
    async def connect_websocket(self) -> bool:
        """Establish WebSocket connection to backend"""
        try:
            self.websocket = await websockets.connect(self.backend_url)
            return True
        except Exception as e:
            self.add_result("WebSocket Connection", "FAIL", f"Failed to connect: {e}")
            return False
    
    async def disconnect_websocket(self):
        """Close WebSocket connection"""
        if self.websocket:
            await self.websocket.close()
    
    def add_result(self, test_name: str, status: str, message: str, details: Dict[str, Any] = None):
        """Add a validation result"""
        result = ValidationResult(
            test_name=test_name,
            status=status,
            message=message,
            timestamp=datetime.now(),
            details=details or {}
        )
        self.results.append(result)
        print(f"[{status}] {test_name}: {message}")
    
    async def test_basic_connection(self) -> bool:
        """Test basic WebSocket connection"""
        if not await self.connect_websocket():
            return False
        
        try:
            # Wait for welcome message
            welcome_msg = await asyncio.wait_for(self.websocket.recv(), timeout=5.0)
            welcome_data = json.loads(welcome_msg)
            
            if welcome_data.get('type') == 'system_message':
                self.add_result("Welcome Message", "PASS", 
                              f"Received welcome: {welcome_data.get('content', '')}")
                return True
            else:
                self.add_result("Welcome Message", "FAIL", 
                              f"Unexpected welcome format: {welcome_data}")
                return False
                
        except asyncio.TimeoutError:
            self.add_result("Welcome Message", "FAIL", "No welcome message received within 5 seconds")
            return False
        except Exception as e:
            self.add_result("Welcome Message", "FAIL", f"Error receiving welcome: {e}")
            return False
    
    async def test_chat_message_format(self) -> bool:
        """Test chat message format compliance"""
        test_message = {
            "type": "chat_message",
            "content": {
                "message": "Hello, this is a test message for integration validation",
                "user_profile_id": "test-user-integration-123",
                "timestamp": datetime.now().isoformat(),
                "metadata": {
                    "test_mode": True,
                    "validation_run": True
                }
            }
        }
        
        try:
            await self.websocket.send(json.dumps(test_message))
            
            # Wait for echo and response
            messages_received = []
            for _ in range(3):  # Expect echo, processing status, and response
                try:
                    msg = await asyncio.wait_for(self.websocket.recv(), timeout=10.0)
                    messages_received.append(json.loads(msg))
                except asyncio.TimeoutError:
                    break
            
            # Validate message formats
            echo_found = False
            processing_found = False
            response_found = False
            
            for msg in messages_received:
                if msg.get('type') == 'chat_message' and msg.get('is_user'):
                    echo_found = True
                elif msg.get('type') == 'processing_status':
                    processing_found = True
                elif msg.get('type') == 'chat_message' and not msg.get('is_user'):
                    response_found = True
            
            if echo_found and processing_found:
                self.add_result("Chat Message Format", "PASS", 
                              f"Received {len(messages_received)} messages with correct format")
                return True
            else:
                self.add_result("Chat Message Format", "FAIL", 
                              f"Missing expected messages. Echo: {echo_found}, Processing: {processing_found}")
                return False
                
        except Exception as e:
            self.add_result("Chat Message Format", "FAIL", f"Error testing chat message: {e}")
            return False
    
    async def test_workflow_specific_message(self) -> bool:
        """Test workflow-specific message handling"""
        wheel_message = {
            "type": "chat_message",
            "content": {
                "message": "I want to create a wheel with creative activities",
                "user_profile_id": "test-user-integration-123",
                "timestamp": datetime.now().isoformat(),
                "metadata": {
                    "requested_workflow": "wheel_generation"
                }
            }
        }
        
        try:
            await self.websocket.send(json.dumps(wheel_message))
            
            # Wait for wheel data response
            wheel_data_received = False
            messages_received = []
            
            for _ in range(10):  # Wait longer for workflow completion
                try:
                    msg = await asyncio.wait_for(self.websocket.recv(), timeout=30.0)
                    msg_data = json.loads(msg)
                    messages_received.append(msg_data)
                    
                    if msg_data.get('type') == 'wheel_data':
                        wheel_data_received = True
                        
                        # Validate wheel data structure
                        wheel = msg_data.get('wheel', {})
                        items = wheel.get('items', [])
                        
                        if len(items) > 0:
                            self.add_result("Wheel Generation Workflow", "PASS", 
                                          f"Received wheel with {len(items)} items")
                            
                            # Check for enhanced metadata
                            has_mentor_context = 'mentor_context' in msg_data
                            has_workflow_insights = 'workflow_insights' in msg_data
                            has_debug_data = 'enhanced_debugging_data' in msg_data
                            
                            self.add_result("Enhanced Metadata", "PASS" if has_mentor_context else "FAIL",
                                          f"Mentor context: {has_mentor_context}, "
                                          f"Workflow insights: {has_workflow_insights}, "
                                          f"Debug data: {has_debug_data}")
                            
                            return True
                        else:
                            self.add_result("Wheel Generation Workflow", "FAIL", 
                                          "Received wheel data but no items")
                            return False
                            
                except asyncio.TimeoutError:
                    break
            
            if not wheel_data_received:
                self.add_result("Wheel Generation Workflow", "FAIL", 
                              f"No wheel data received. Got {len(messages_received)} messages")
                return False
                
        except Exception as e:
            self.add_result("Wheel Generation Workflow", "FAIL", f"Error testing workflow: {e}")
            return False
    
    async def test_spin_result_message(self) -> bool:
        """Test spin result message format"""
        spin_message = {
            "type": "spin_result",
            "content": {
                "activity_tailored_id": "test-activity-123",
                "name": "Test Creative Activity",
                "description": "A test activity for validation",
                "user_profile_id": "test-user-integration-123"
            }
        }
        
        try:
            await self.websocket.send(json.dumps(spin_message))
            
            # Wait for activity details response
            activity_details_received = False
            
            for _ in range(5):
                try:
                    msg = await asyncio.wait_for(self.websocket.recv(), timeout=15.0)
                    msg_data = json.loads(msg)
                    
                    if msg_data.get('type') == 'activity_details':
                        activity_details_received = True
                        details = msg_data.get('details', {})
                        
                        if details.get('name') and details.get('detailed_description'):
                            self.add_result("Spin Result Message", "PASS", 
                                          f"Received activity details for: {details.get('name')}")
                            return True
                        else:
                            self.add_result("Spin Result Message", "FAIL", 
                                          "Activity details incomplete")
                            return False
                            
                except asyncio.TimeoutError:
                    break
            
            if not activity_details_received:
                self.add_result("Spin Result Message", "FAIL", "No activity details received")
                return False
                
        except Exception as e:
            self.add_result("Spin Result Message", "FAIL", f"Error testing spin result: {e}")
            return False
    
    def test_frontend_availability(self) -> bool:
        """Test if frontend is available"""
        try:
            response = requests.get(self.frontend_url, timeout=5)
            if response.status_code == 200:
                self.add_result("Frontend Availability", "PASS", 
                              f"Frontend accessible at {self.frontend_url}")
                return True
            else:
                self.add_result("Frontend Availability", "FAIL", 
                              f"Frontend returned status {response.status_code}")
                return False
        except Exception as e:
            self.add_result("Frontend Availability", "FAIL", f"Frontend not accessible: {e}")
            return False
    
    def test_backend_health(self) -> bool:
        """Test backend health endpoint"""
        try:
            # Try to access Django admin or health endpoint
            health_url = self.frontend_url.replace('3000', '8000').replace('ws://', 'http://') + '/admin/'
            response = requests.get(health_url, timeout=5)
            
            if response.status_code in [200, 302]:  # 302 for admin redirect
                self.add_result("Backend Health", "PASS", 
                              f"Backend accessible at {health_url}")
                return True
            else:
                self.add_result("Backend Health", "FAIL", 
                              f"Backend returned status {response.status_code}")
                return False
        except Exception as e:
            self.add_result("Backend Health", "FAIL", f"Backend not accessible: {e}")
            return False
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all validation tests"""
        print("🧪 Starting Frontend-Backend Integration Validation")
        print("=" * 60)
        
        start_time = time.time()
        
        # Test system availability
        self.test_frontend_availability()
        self.test_backend_health()
        
        # Test WebSocket integration
        if await self.test_basic_connection():
            await self.test_chat_message_format()
            await self.test_workflow_specific_message()
            await self.test_spin_result_message()
        
        await self.disconnect_websocket()
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Generate summary
        passed = len([r for r in self.results if r.status == 'PASS'])
        failed = len([r for r in self.results if r.status == 'FAIL'])
        skipped = len([r for r in self.results if r.status == 'SKIP'])
        total = len(self.results)
        
        summary = {
            'total_tests': total,
            'passed': passed,
            'failed': failed,
            'skipped': skipped,
            'duration_seconds': duration,
            'success_rate': (passed / total * 100) if total > 0 else 0,
            'results': [
                {
                    'test_name': r.test_name,
                    'status': r.status,
                    'message': r.message,
                    'timestamp': r.timestamp.isoformat()
                }
                for r in self.results
            ]
        }
        
        print("\n" + "=" * 60)
        print("📊 VALIDATION SUMMARY")
        print("=" * 60)
        print(f"✅ Passed: {passed}")
        print(f"❌ Failed: {failed}")
        print(f"⏭️  Skipped: {skipped}")
        print(f"📊 Total: {total}")
        print(f"⏱️  Duration: {duration:.2f} seconds")
        print(f"📈 Success Rate: {summary['success_rate']:.1f}%")
        
        if failed == 0:
            print("\n🎉 ALL TESTS PASSED! Frontend-Backend integration is fully aligned.")
        else:
            print(f"\n⚠️  {failed} test(s) failed. Please review the results above.")
        
        return summary

async def main():
    """Main validation function"""
    validator = IntegrationValidator()
    summary = await validator.run_all_tests()
    
    # Save results to file
    with open('integration_validation_results.json', 'w') as f:
        json.dump(summary, f, indent=2)
    
    print(f"\n📄 Results saved to: integration_validation_results.json")
    
    # Exit with appropriate code
    exit_code = 0 if summary['failed'] == 0 else 1
    return exit_code

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
