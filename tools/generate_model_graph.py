import subprocess
import os

def generate_model_graph():
    print("--- Django Model Graph Generator ---")
    print("This script generates a graphical overview of your Django models.")

    # 1. Get app names
    app_names_input = input("Enter Django app names (space-separated, e.g., main admin_tools): ").strip()
    app_names = app_names_input.split()

    if not app_names:
        print("No app names provided. Exiting.")
        return

    # 2. Get output file name
    output_file = input("Enter output image file name (e.g., my_models.png): ").strip()
    if not output_file:
        output_file = "django_models.png"
        print(f"No output file name provided. Using default: {output_file}")

    # 3. Ask about grouping models
    group_models = input("Group models by app? (y/n, default n): ").strip().lower()
    group_flag = "-g" if group_models == "y" else ""

    # 4. Ask about rankdir
    rankdir_input = input("Enter graph layout direction (TB, LR, BT, RL, default TB): ").strip().upper()
    rankdir_flag = f"--rankdir {rankdir_input}" if rankdir_input in ["TB", "LR", "BT", "RL"] else ""
    if not rankdir_flag:
        print("Invalid rankdir. Using default: TB")
        rankdir_flag = "--rankdir TB"

    # Construct the command
    # We need to run this inside the 'web' docker container
    # The manage.py is located in the backend directory
    # So the command will be: docker-compose exec web python manage.py graph_models ...
    
    # Base command for docker-compose exec
    docker_command = ["docker-compose", "exec", "web", "python", "manage.py", "graph_models"]

    # Add app names
    docker_command.extend(app_names)

    # Add grouping flag if chosen
    if group_flag:
        docker_command.append(group_flag)

    # Add rankdir flag if chosen
    if rankdir_flag:
        docker_command.extend(rankdir_flag.split())

    # Add output file
    docker_command.extend(["-o", output_file])

    # Add --pygraphviz or --pydot
    docker_command.append("--pydot")

    print(f"\nExecuting command: {' '.join(docker_command)}")
    print("This might take a moment...")

    try:
        # Run the command
        # We need to run docker-compose from the backend directory
        # The current working directory is /Users/<USER>/dev/goali
        # So we need to change directory to backend first
        subprocess.run(docker_command, check=True, cwd="backend")
        print(f"\nSuccessfully generated model graph: {output_file}")
        print(f"The image should be located in the 'backend' directory.")
        print(f"You can view it by running: open backend/{output_file}")
    except subprocess.CalledProcessError as e:
        print(f"\nError generating model graph: {e}")
        print("Please ensure 'docker-compose' is running and 'pydot' is installed in the 'web' container.")
    except FileNotFoundError:
        print("\nError: 'docker-compose' command not found.")
        print("Please ensure Docker is installed and 'docker-compose' is in your PATH.")

if __name__ == "__main__":
    generate_model_graph()
