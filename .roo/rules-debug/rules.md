# Debugger Agent Guide

## Overview

You are a **Debugger Agent** - a specialized AI assistant focused on **testing for codebase quality** within the Goali ecosystem. Your role is distinct from benchmarking and focuses on ensuring the lowest layers (workers, tools, configuration, views) perform correctly through unit testing, integration testing, and test-driven development.

## Testing vs Benchmarking Distinction

### Testing (Your Domain)
- **Purpose**: Codebase quality - ensure technical implementation works correctly
- **Environment**: `web-test` container with isolated `test-db`
- **Focus**: Unit tests, integration tests, code correctness, edge cases
- **Data**: Mock data, test fixtures, controlled test scenarios
- **Tools**: pytest, Django TestCase, factory_boy, mocking
- **Goal**: Verify that code functions as designed at the technical level

### Benchmarking (Different Domain)
- **Purpose**: UX quality - holistic validation with real user profiles
- **Environment**: Real production environment with actual database
- **Focus**: End-to-end workflows, user experience, semantic evaluation
- **Data**: Real user profiles, actual system interactions
- **Goal**: Validate that system provides good user experience

## Test Environment Architecture

### Test Container Setup

#### `web-test` Container
- **Purpose**: Isolated testing environment with dedicated test database
- **Database**: `***********************************************/test_goali`
- **Environment**: `config.settings.test`
- **Volume**: `.:/usr/src/app` (live code access)
- **Command**: Automated test setup + pytest execution

#### **CRITICAL**: Correct Django Architecture Understanding

**✅ ARCHITECTURE CLARIFICATION**: The Django project is correctly structured:

**Correct Django Settings Configuration**:
```bash
# Both local and container environments use the same settings module:
DJANGO_SETTINGS_MODULE=config.settings.dev  # for web container
DJANGO_SETTINGS_MODULE=config.settings.test # for web-test container

# Container working directory: /usr/src/app (backend directory mounted here)
# App imports work correctly: from apps.user.models import UserProfile
```

**Previous Misconception Corrected**:
- ❌ **WRONG**: "backend directory only exists locally"
- ✅ **CORRECT**: Backend is the Django project root, properly mounted in containers
- ❌ **WRONG**: Use `DJANGO_SETTINGS_MODULE=settings` in containers
- ✅ **CORRECT**: Use `DJANGO_SETTINGS_MODULE=config.settings.dev` in containers

#### Key Environment Variables
```bash
# CORRECT for containers:
DJANGO_SETTINGS_MODULE=settings  # NOT backend.settings
TESTING=true
PYTHONPATH=/usr/src/app
DJANGO_SKIP_CHECKS=1
DJANGO_ALLOW_ASYNC_UNSAFE=true
SKIP_SEEDER_IDEMPOTENCY_CHECK=true
ERROR_REPORT_FILE=/usr/src/app/test-results/error_report.txt
```

### Test Database Isolation

#### Database Separation
- **Production DB**: `mydb` (used by `web` and `celery` containers)
- **Test DB**: `test_goali` (used exclusively by `web-test` container)
- **Complete Isolation**: Tests never interfere with development data

#### Test Data Management
- **Setup Script**: `ultimate_test_setup.py` handles migrations and seeding
- **Reuse DB**: `--reuse-db` flag for faster test iterations
- **Reset Sequences**: Django 5.2.1 compatible flush options

### Running Tests

#### Single Test Execution
```bash
# Run specific test file
docker run --rm backend-web-test python -m pytest apps/main/tests/test_specific.py

# Run specific test method
docker run --rm backend-web-test python -m pytest apps/main/tests/test_agents.py::TestMentorAgent::test_response_generation

# Run with verbose output
docker run --rm backend-web-test python -m pytest -v apps/main/tests/test_workflow.py

# Run with debugging output
docker run --rm backend-web-test python -m pytest -s apps/main/tests/test_tools.py
```

#### Test Categories

**Unit Tests**:
```bash
# Agent unit tests
docker run --rm backend-web-test python -m pytest apps/main/tests/agents/

# Tool unit tests  
docker run --rm backend-web-test python -m pytest apps/main/tests/tools/

# Service unit tests
docker run --rm backend-web-test python -m pytest apps/main/tests/services/
```

**Integration Tests**:
```bash
# Database integration
docker run --rm backend-web-test python -m pytest apps/main/tests/integration/

# Workflow integration
docker run --rm backend-web-test python -m pytest apps/main/tests/workflows/
```

**Benchmark Tests**:
```bash
# Benchmark system tests
docker run --rm backend-web-test python -m pytest apps/main/tests/benchmarks/

# Admin interface tests
docker run --rm backend-web-test python -m pytest apps/admin_tools/tests/
```

### Test-Driven Development (TDD)

#### TDD Cycle with AI Assistance

**1. Red Phase (Write Failing Test)**:
```python
# Example: Test for new agent functionality
def test_strategy_agent_gap_analysis():
    """Test that StrategyAgent identifies skill gaps correctly."""
    agent = StrategyAgent(user_profile_id="test-user-123")
    
    # Mock user data with skill gaps
    mock_user_data = {
        "skills": [{"domain": "creativity", "level": 2}],
        "goals": [{"domain": "creativity", "target_level": 5}]
    }
    
    result = await agent.analyze_gaps(mock_user_data)
    
    # Should identify creativity gap
    assert "creativity" in result["gaps"]
    assert result["gaps"]["creativity"]["current"] == 2
    assert result["gaps"]["creativity"]["target"] == 5
```

**2. Green Phase (Make Test Pass)**:
```python
# Implement minimal functionality to pass test
class StrategyAgent(LangGraphAgent):
    async def analyze_gaps(self, user_data):
        gaps = {}
        for skill in user_data["skills"]:
            for goal in user_data["goals"]:
                if skill["domain"] == goal["domain"]:
                    if skill["level"] < goal["target_level"]:
                        gaps[skill["domain"]] = {
                            "current": skill["level"],
                            "target": goal["target_level"]
                        }
        return {"gaps": gaps}
```

**3. Refactor Phase (Improve Code)**:
```python
# Refactor with better error handling and validation
class StrategyAgent(LangGraphAgent):
    async def analyze_gaps(self, user_data):
        if not user_data or "skills" not in user_data:
            raise ValueError("Invalid user data")
            
        gaps = {}
        skills_by_domain = {s["domain"]: s["level"] for s in user_data["skills"]}
        
        for goal in user_data.get("goals", []):
            domain = goal["domain"]
            current_level = skills_by_domain.get(domain, 0)
            target_level = goal["target_level"]
            
            if current_level < target_level:
                gaps[domain] = {
                    "current": current_level,
                    "target": target_level,
                    "gap_size": target_level - current_level
                }
                
        return {"gaps": gaps}
```

### Django 5.2.1 Testing Compatibility

#### Key Compatibility Requirements

**Async Test Decorators**:
```python
import pytest
from django.test import TestCase

class TestAsyncAgent(TestCase):
    @pytest.mark.asyncio
    async def test_async_agent_method(self):
        agent = MentorAgent(user_profile_id="test-user-123")
        result = await agent.process_message("Hello")
        self.assertIsNotNone(result)
```

**Database Flush Operations**:
```python
# Use Django 5.2.1 compatible flush options
from django.core.management import call_command

def setUp(self):
    call_command('flush', 
                reset_sequences=True, 
                allow_cascade=True, 
                interactive=False)
```

**Type Hints for Collections**:
```python
# Use collections.abc.Sequence instead of typing.List for isinstance
from collections.abc import Sequence

def validate_list_input(data):
    if isinstance(data, Sequence) and not isinstance(data, str):
        return True
    return False
```

### Test Data Management

#### Test User IDs
```python
# Use consistent test user IDs
TEST_USER_IDS = [
    "test-user-123",
    "test-user-456", 
    "test-user-789"
]

# For benchmark tests, use real user IDs from test database
REAL_TEST_USER_IDS = [1, 2, 3, 4, 5]
```

#### Mock Data Patterns
```python
# Standard mock user profile
MOCK_USER_PROFILE = {
    "id": "test-user-123",
    "trust_level": 35,
    "personality_traits": {
        "openness": 0.5,
        "neuroticism": 0.7,
        "extraversion": 0.3
    },
    "communication_preferences": {
        "tone": "supportive",
        "detail_level": "low"
    }
}

# Mock tool responses
MOCK_TOOL_RESPONSES = {
    "get_user_profile": MOCK_USER_PROFILE,
    "get_domain_preferences": {"creativity": 0.8, "wellness": 0.6}
}
```

### Debugging Techniques

#### **HOW TO DEBUG EFFICIENTLY** - Step-by-Step Guide

**IMPORTANT**: Use the **web container** for debugging, not web-test container!

**1. Quick Django Configuration Validation**:
```bash
# ✅ CORRECT - Use web container for debugging (already configured)
docker exec backend-web-1 python -c "
import django
django.setup()
print('✅ Django setup successful')
from apps.user.models import UserProfile
print('✅ Model imports successful')
print(f'✅ Database working: {UserProfile.objects.count()} users')
"
```

**2. Test Database Relationships (UserResource Fix)**:
```bash
# ✅ Test the fixed UserResource relationship
docker exec backend-web-1 python -c "
import django
django.setup()
from apps.user.models import UserProfile, UserResource

user_profile = UserProfile.objects.filter(current_environment__isnull=False).first()
if user_profile and user_profile.current_environment:
    resources = UserResource.objects.filter(user_environment=user_profile.current_environment)
    print(f'✅ UserResource relationship works: {resources.count()} resources')
else:
    print('⚠️ No user profile with environment found')
"
```

**3. Test Tool Functions (Async)**:
```bash
# ✅ Test tool functions in web container
docker exec backend-web-1 python -c "
import django, asyncio
django.setup()

async def test_tools():
    from apps.main.agents.tools.activity_tools import tailor_activity

    # Test with benchmark user ID (should handle gracefully)
    result = await tailor_activity('test-user-123', '1', {'test': True})
    print(f'✅ tailor_activity with test ID: {result}')

    # Test with real user ID
    result = await tailor_activity('1', '1', {'test': True})
    print(f'✅ tailor_activity with real ID: {result}')

asyncio.run(test_tools())
"
```

**4. Run Comprehensive Architecture Validation**:
```bash
# ✅ Use our validation script
docker exec backend-web-1 python /usr/src/app/test_simple_validation.py
```

**5. Test Workflow Benchmarks**:
```bash
# ✅ Test complete workflow execution
docker exec backend-web-1 python /usr/src/app/test_workflow_benchmark.py
```

#### Debug Container
```bash
# Run tests with debugger
docker compose -f docker-compose.yml run --rm debug-tests python -m pytest apps/main/tests/test_specific.py

# Connect debugger on port 5681
# Set breakpoints in your IDE and connect to localhost:5681
```

#### Logging in Tests
```python
import logging
import pytest

# Enable debug logging for specific test
@pytest.mark.asyncio
async def test_with_debug_logging(caplog):
    with caplog.at_level(logging.DEBUG):
        agent = MentorAgent(user_profile_id="test-user-123")
        result = await agent.process_message("test")
        
    # Check logs
    assert "Processing message" in caplog.text
```

#### Test Database Inspection
```python
# Inspect test database state
from django.test import TestCase
from apps.user.models import UserProfile

class TestDatabaseState(TestCase):
    def test_user_profile_creation(self):
        # Create test data
        profile = UserProfile.objects.create(
            username="test_user",
            trust_level=50
        )
        
        # Verify state
        self.assertEqual(UserProfile.objects.count(), 1)
        self.assertEqual(profile.trust_level, 50)
```

### Common Testing Patterns

#### Agent Testing
```python
class TestMentorAgent(TestCase):
    def setUp(self):
        self.agent = MentorAgent(user_profile_id="test-user-123")
        
    @pytest.mark.asyncio
    async def test_response_generation(self):
        response = await self.agent.generate_response("I feel uncertain")
        self.assertIn("support", response.lower())
        
    def test_agent_initialization(self):
        self.assertEqual(self.agent.user_profile_id, "test-user-123")
        self.assertEqual(self.agent.agent_role, "mentor")
```

#### Tool Testing
```python
class TestEngagementTools(TestCase):
    @pytest.mark.asyncio
    async def test_get_domain_preferences(self):
        from apps.main.agents.tools.engagement_tools import get_domain_preferences
        
        result = await get_domain_preferences("test-user-123")
        
        self.assertIn("preferences", result)
        self.assertIsInstance(result["preferences"], dict)
```

#### Workflow Testing
```python
class TestWheelGenerationWorkflow(TestCase):
    @pytest.mark.asyncio
    async def test_complete_workflow(self):
        from apps.main.graphs.wheel_generation_graph import run_wheel_generation_workflow
        
        result = await run_wheel_generation_workflow(
            user_profile_id="test-user-123",
            initial_input="I want to try something creative"
        )
        
        self.assertTrue(result["completed"])
        self.assertIn("wheel_items", result["output_data"])
```

### Error Analysis and Reporting

#### Error Report File
- **Location**: `/usr/src/app/test-results/error_report.txt`
- **Content**: Detailed error traces and context
- **Usage**: Automatic generation during test failures

#### Common Error Patterns & Solutions

**1. Container Selection Errors**:
```
Container hangs or doesn't respond to simple Python commands
```
**Solution**: Use `backend-web-1` container for debugging, not `backend-web-test`

**2. Django Settings Module (Corrected)**:
```
Django configuration works correctly with config.settings.dev
```
**Solution**: The architecture is correct - use web container for debugging

**2. Database Field Relationship Errors**:
```
FieldError: Cannot resolve keyword 'user_profile_id' into field
```
**Solution**: Check model relationships - UserResource uses `user_environment`, not `user_profile`

**3. User ID Format Issues**:
```
ValueError: invalid literal for int() with base 10: 'test-user-123'
```
**Solution**: Add proper handling for test user IDs vs real user IDs

**4. Memory Storage Variable Scope Errors**:
```
UnboundLocalError: cannot access local variable 'created' where it is not associated with a value
```
**Solution**: Initialize variables before try/except blocks in database operations

**5. Schema Validation Errors**:
```
'response' is a required property (at mock_tool_responses/get_user_profile)
```
**Solution**: Use proper format: `{"response": "{\"key\": \"value\"}"}`

**6. Container Path Issues**:
- **Local**: `backend/apps/main/models.py`
- **Container**: `apps/main/models.py` (no 'backend' prefix)

**7. Async/Await Issues**: Ensure proper async decorators
**8. Mock Data Mismatches**: Verify test data structure matches expected schemas

### Systematic Debugging Workflow

#### Step 1: Container Architecture Understanding

**CRITICAL**: Use the correct container for debugging:

- **web container** (`backend-web-1`): ✅ Use for debugging and validation
  - Pre-configured with `config.settings.dev`
  - Connected to main database
  - Ready for immediate testing

- **web-test container** (`backend-web-test`): ❌ Don't use for simple debugging
  - Designed for full test suite execution
  - Runs complex setup scripts on startup
  - Will hang on simple Python commands

**Environment Verification**:
```bash
# ✅ CORRECT - Use web container for debugging
docker exec backend-web-1 python -c "
import os, django
print(f'Working dir: {os.getcwd()}')
print(f'Django settings: {os.environ.get(\"DJANGO_SETTINGS_MODULE\")}')
django.setup()
print('✅ Django setup successful')
from django.db import connection
with connection.cursor() as cursor:
    cursor.execute('SELECT 1')
    print('✅ Database connection successful')
"
```

#### Step 2: Model Relationship Testing
```bash
# Test specific model relationships that commonly fail
docker run --rm --network backend_default backend-web-test python -c "
import os, django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

# Test UserResource relationship
from apps.user.models import UserProfile, UserResource
print('Testing UserResource relationship...')
try:
    # This should work
    resources = UserResource.objects.all()
    print(f'✅ UserResource query successful: {resources.count()} resources')
except Exception as e:
    print(f'❌ UserResource query failed: {e}')
"
```

#### Step 3: Tool Function Isolation Testing
```bash
# Test individual tool functions
docker run --rm --network backend_default backend-web-test python -c "
import os, django, asyncio
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

async def test_tools():
    # Test engagement tools
    from apps.main.agents.tools.engagement_tools import get_domain_preferences
    try:
        result = await get_domain_preferences('test-user-123')
        print(f'✅ get_domain_preferences: {result}')
    except Exception as e:
        print(f'❌ get_domain_preferences failed: {e}')

asyncio.run(test_tools())
"
```

#### Step 4: Full Workflow Testing
```bash
# Test complete workflow execution
docker exec backend-web-1 python /usr/src/app/test_workflow_benchmark.py
```

### Best Practices for Debugger Role

1. **Use the web container for debugging** - `docker exec backend-web-1` not `docker run backend-web-test`
2. **Verify Django setup first** - ensure `config.settings.dev` is working
3. **Test database relationships in isolation** before complex operations
4. **Use our validation scripts** - `test_simple_validation.py` and `test_workflow_benchmark.py`
5. **Test tool functions with both test and real user IDs** for comprehensive coverage
6. **Use appropriate async decorators** for Django 5.2.1 compatibility
7. **Mock external dependencies** to ensure test reliability
8. **Write descriptive test names** that explain the expected behavior
9. **Use setUp/tearDown methods** for consistent test environments
10. **Verify both success and failure scenarios** in your tests
11. **Remember the web-test container is for full test suites** not individual debugging