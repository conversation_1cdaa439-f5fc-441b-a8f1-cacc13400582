# Data Flow Authoritative Specs - The One Source of Truth

## Overview

This document serves as the authoritative specification for all data flows in the Goali backend system. It centralizes all critical information about how data moves through the system, from WebSocket connections to workflow execution.

## System Architecture

### Core Components

1. **WebSocket Layer** - Handles real-time client connections
2. **ConversationDispatcher** - Routes messages and manages workflow orchestration
3. **MentorService** - Maintains per-user state and context
4. **Workflow Engine** - Executes LangGraph-based workflows
5. **Agent System** - Individual AI agents (Mentor, Orchestrator, etc.)
6. **Database Layer** - Persistent storage with async context management

### Data Flow Sequence

```
Client WebSocket → ConversationDispatcher → MentorService → Workflow Engine → Agents → Database
```

## Message Processing Pipeline

### 1. WebSocket Message Reception

**Input Format:**
```json
{
  "type": "chat_message",
  "content": {
    "message": "User message text",
    "user_profile_id": "debugger-user-1"
  }
}
```

**Processing Steps:**
1. WebSocket receives message
2. Message validated and parsed
3. Routed to ConversationDispatcher

### 2. ConversationDispatcher Processing

**Key Functions:**
- Message classification using LLM
- Context extraction and enrichment
- Workflow type determination
- Action requirement checking

**Debug Output Example:**
```json
{
  "source": "ConversationDispatcher",
  "message": "Message classified successfully",
  "details": {
    "workflow_type": "wheel_generation",
    "confidence": 0.9,
    "reason": "User expressing boredom implies looking for activities"
  }
}
```

### 3. MentorService Integration

**Responsibilities:**
- Per-user singleton state management
- Trust level tracking
- Communication preferences
- Conversation context maintenance

**State Structure:**
```json
{
  "trust_level": 0.5,
  "communication_preferences": {},
  "conversation_context": {
    "last_message": "I'm bored",
    "last_message_time": "2025-06-10T22:12:27.164300",
    "message_metadata": {}
  },
  "mentor_assessment": {
    "emotional_tone": "neutral",
    "urgency_level": "medium",
    "support_needed": "informational"
  }
}
```

### 4. Context Packet Construction

**Complete Context Packet:**
```json
{
  "user_id": "debugger-user-1",
  "user_profile_id": "debugger-user-1",
  "session_timestamp": "2025-06-10T22:12:27.164300",
  "reported_mood": "",
  "reported_environment": "",
  "reported_time_availability": "",
  "reported_focus": "",
  "reported_satisfaction": "",
  "extraction_confidence": 0.5,
  "entities": [],
  "user_ws_session_name": "client_session_...",
  "mentor_context": { /* MentorService state */ },
  "workflow_metadata": {
    "intended_workflow": "wheel_generation",
    "classification_confidence": 0.9,
    "classification_reason": "...",
    "llm_classification_used": true
  },
  "system_metadata": {
    "dispatcher_version": "2.0.0",
    "mentor_service_available": true,
    "llm_client_available": true,
    "llm_initialization_error": null,
    "processing_timestamp": "2025-06-10T22:12:27.164372",
    "enhanced_architecture": true
  }
}
```

## Workflow Routing Logic

### Classification Rules

1. **wheel_generation**: User expressing boredom or requesting activities
2. **discussion**: General conversation, missing information, or fallback
3. **activity_feedback**: User providing feedback on completed activities
4. **post_spin**: User activity selection after wheel spin (comprehensive multi-agent workflow)
5. **progress_review**: User requesting progress analysis

### Action Requirements

**Missing Information Handling:**
```json
{
  "action_required": {
    "type": "gather_information",
    "missing_field": "time_availability",
    "prompt": "Before I suggest activities, could you let me know how much time you have available right now?"
  }
}
```

**Workflow Override:**
When information is missing, system overrides to discussion workflow with target workflow preserved.

## Comprehensive Post-Spin Workflow

### Overview

The post-spin workflow implements a comprehensive multi-agent system according to the official post_spin_FLOW.md specification. It handles user activity selection after wheel spinning with full orchestration supporting both acceptance and emergency refusal paths.

### Agent Architecture

1. **Mentor Agent**: Activity presentation and emergency response collection
2. **Orchestrator Agent**: Path determination and workflow coordination
3. **Pattern Analytics Agent**: Emergency pattern analysis and trust impact assessment
4. **Psychological Monitoring Agent**: Emergency alternative analysis and trust recovery
5. **Activity Generation Agent**: Emergency alternative creation with constraint accommodation
6. **Ethical Oversight Agent**: Final validation and implementation guidance

### Workflow Paths

**Acceptance Path:**
```
Mentor Agent → Orchestrator Agent → Finalize
```

**Emergency Refusal Path:**
```
Mentor Agent → Orchestrator Agent → Pattern Analytics → Psychological Monitoring → Activity Generation → Ethical Oversight → Finalize
```

### Data Flow Example

**Input (spin_result):**
```json
{
  "type": "spin_result",
  "content": {
    "activity_tailored_id": "tailored-123",
    "name": "Nature Walk",
    "user_profile_id": "2"
  }
}
```

**Output (acceptance path):**
```json
{
  "workflow_id": "a57af86e-299a-4427-bde1-43e1190f6f04",
  "user_profile_id": "2",
  "completed": true,
  "output_data": {
    "activity_info": {
      "activity_name": "Nature Walk",
      "activity_tailored_id": "tailored-123"
    },
    "workflow_type": "post_spin",
    "workflow_version": "comprehensive_v1.0",
    "user_response": "Great choice! 'Nature Walk' is an excellent activity to try...",
    "orchestrator_analysis": {
      "path_determination": "acceptance",
      "activity_preparation": { /* detailed preparation data */ }
    },
    "workflow_metadata": {
      "agents_executed": ["mentor", "orchestrator", "finalize"],
      "response_type": "acceptance",
      "emergency_handled": false
    }
  }
}
```

## Agent System

### Mentor Agent

**Validation Logic:**
- Accepts `debugger-user-*`, `test-user-*`, `benchmark-user-*` patterns
- Converts valid string IDs to integers for database operations
- Returns structured error responses for invalid formats

**Error Response Format:**
```json
{
  "error": "Invalid user_profile_id format: invalid-id",
  "output_data": {
    "error": "Invalid user_profile_id format: invalid-id",
    "debug": {"last_error": "Invalid user_profile_id format: invalid-id"}
  }
}
```

### Database Integration

**Async Context Management:**
- All database operations use proper async context
- Connection pooling managed automatically
- Health monitoring via system health endpoint

## System Health Monitoring

**Health Check Response:**
```json
{
  "redis": "healthy",
  "celery_workers": "1 active",
  "database": "healthy",
  "memory_usage": "unknown",
  "cpu_usage": "unknown",
  "redis_clients": 29,
  "redis_memory": "2.36M"
}
```

## Debug Information Flow

### Debug Message Types

1. **MentorService**: Message processing, trust level updates
2. **ConversationDispatcher**: Classification, routing, context building
3. **Agent**: Process start/completion, output summaries
4. **Workflow**: Status updates, transitions

### Debug Message Format

```json
{
  "timestamp": "2025-06-10T22:12:27.164390+00:00",
  "source": "ConversationDispatcher",
  "level": "info",
  "message": "Message classified successfully",
  "details": { /* Context-specific data */ }
}
```

## Error Handling

### Error Broadcasting

Errors are broadcast through the WebSocket system with user-friendly messages:
```json
{
  "type": "error",
  "content": "An unexpected error occurred while retrieving your profile."
}
```

### Error Recovery

- System continues processing despite individual component failures
- Graceful degradation with fallback workflows
- Comprehensive error logging for debugging

## Performance Characteristics

### Typical Processing Times

- Message classification: ~100-500ms
- Context extraction: ~50-200ms
- Workflow initiation: ~100-300ms
- Agent processing: ~1-5 seconds (depending on LLM calls)

### Token Tracking

System tracks LLM token usage across all components:
```json
{
  "llm_input_tokens": 150,
  "llm_output_tokens": 75,
  "total_cost_estimate": "$0.0045"
}
```

## Testing and Validation

### Test User Patterns

- `debugger-user-*`: For debugging and development
- `test-user-*`: For automated testing
- `benchmark-user-*`: For benchmarking and performance testing

### Validation Tools

- Enhanced Packet Debugger: Real-time WebSocket monitoring
- System Health Endpoints: Component status monitoring
- Debug Information Stream: Detailed processing insights

## Configuration

### Environment Variables

- `DATABASE_URL`: Database connection string
- `REDIS_URL`: Redis connection for caching and pub/sub
- `LLM_API_KEY`: API key for LLM services
- `DEBUG_MODE`: Enable/disable debug information broadcasting

### Feature Flags

- `enhanced_architecture`: Enable Phase 2 architecture features
- `mentor_service_available`: MentorService integration status
- `llm_client_available`: LLM client availability

## Troubleshooting

### Common Issues

1. **Database Connection**: Check async context management
2. **User Profile Validation**: Verify ID format patterns
3. **Workflow Routing**: Check classification confidence levels
4. **Agent Errors**: Review validation logic and error handling
5. **Spin Result Routing**: Ensure `spin_result` messages use top-level `type` field, not metadata

## User Preference Data Flow (January 27, 2025)

### Energy Level and Time Available Transmission

**Frontend Capture (app-shell.ts)**:
- Energy level captured from slider as percentage (0-100)
- Time available captured from slider and converted to minutes (10-240 range)
- Values stored in component state: `energyLevel` and `timeAvailable`

**WebSocket Message Format**:
```json
{
  "type": "chat_message",
  "content": {
    "message": "Generate a personalized activity wheel (Energy: 75%, Time: 45 minutes)",
    "user_profile_id": "user-id",
    "timestamp": "2025-01-27T14:35:00Z",
    "energy_level": 75,
    "time_available_minutes": 45,
    "metadata": {
      "forced_wheel_generation": true
    }
  }
}
```

**Backend Processing (ConversationDispatcher)**:
- Values extracted from message content
- Added to context packet under `user_input_context`:
```json
{
  "user_input_context": {
    "energy_level": 75,
    "time_available": 45,
    "forced_wheel_item_count": 5,
    "direct_input": true
  }
}
```

## Domain Assignment Data Flow

### Activity Catalog to Wheel Items

**Enhanced Activity Catalog**:
- Provides diverse activities with proper domains: creativity, wellness, physical, learning, social
- Each activity has format: `{"id": "creative_1", "domain": "creativity", ...}`

**Activity Tailoring**:
- `tailor_activity` function preserves original domains from catalog
- Enhanced catalog IDs (e.g., "creative_1") are handled correctly
- Domain mapping maintained throughout tailoring process

**Wheel Item Creation**:
- Wheel activity agent processes tailored activities
- Domain assignment logic ensures each wheel item gets proper domain
- Fallback domain changed from "general" to "wellness" for better diversity
- Color assignment based on domain for visual differentiation

**Frontend Display**:
- Wheel segments receive domain information in wheel data
- Frontend uses domain for color assignment and categorization
- Each segment structure: `{"text": "Activity Name", "domain": "creativity", "color": "#E67E22"}`

### Recent Fixes (June 13, 2025)

**✅ RESOLVED: Spin Result Message Routing**
- **Issue**: `spin_result` messages were being routed to `user_onboarding` instead of `post_spin` workflow
- **Root Cause**: ConversationDispatcher was checking `metadata.get("type")` instead of top-level `type` field
- **Fix**: Added priority check for `user_message.get("type") == "spin_result"` before metadata checks
- **Impact**: Frontend wheel spin results now correctly trigger post-spin workflow

**✅ RESOLVED: Silent Fallbacks to Mock Mode Eliminated**
- **Issue**: System defaulted to mock mode, causing silent fallbacks when real components weren't explicitly configured
- **Root Cause**: All execution mode parameters (`use_real_llm`, `use_real_tools`, `use_real_db`) defaulted to `False`
- **Fixes Applied**:
  1. Changed `WheelGenerationState` defaults from `False` to `True` for all real mode parameters
  2. Updated legacy interface to default to real mode instead of mock mode
  3. Added production mode check in ConversationDispatcher to prevent LLM fallbacks
  4. Updated admin interface to default to 'full-real' instead of 'mock'
  5. Reordered benchmark script modes to prioritize 'full-real' over 'mock'
- **Impact**: System now defaults to real mode, eliminating silent fallbacks to mock implementations

**✅ RESOLVED: Domain Assignment Issues in Wheel Generation (January 27, 2025)**
- **Issue**: All wheel activities showing as "general" domain instead of diverse domains
- **Root Cause**: Multiple fallback points in wheel activity agent defaulting to "general" domain
- **Fixes Applied**:
  1. Changed default fallback domain from "general" to "wellness" in wheel activity agent
  2. Enhanced domain extraction logging for better debugging
  3. Improved domain assignment logic in wheel item processing
  4. Fixed color assignment to use proper domain-based colors
  5. Enhanced activity catalog provides diverse domains (creativity, wellness, physical, learning, social)
- **Testing**: Created comprehensive test suite validating domain assignment throughout pipeline
- **Impact**: Wheel generation now produces diverse domains with proper color differentiation

### Diagnostic Commands

```bash
# Check system health
curl http://localhost:8000/api/health/

# Monitor WebSocket connections
node frontend/ai-live-testing-tools/enhanced-packet-debugger.js

# Check database connectivity
docker exec -it backend-web-1 python manage.py check --database default
```

---

**Last Updated**: 2025-06-10
**Version**: 2.0.0
**Status**: Authoritative Specification
