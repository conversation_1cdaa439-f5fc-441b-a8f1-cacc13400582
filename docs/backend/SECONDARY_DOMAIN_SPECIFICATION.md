# Secondary Domain Specification - Industry-Grade Authoritative Guide

## Overview

This document defines the industry-grade specification for secondary domains (sub-domains) in the Goali activity system. Based on the authoritative GenericDomain model with 105 validated domains across 10 primary categories, this specification ensures high-quality activity selection through sophisticated multi-dimensional categorization.

## Authoritative Domain Architecture

### 1. Complete Domain Hierarchy
```
PRIMARY CATEGORIES (10)
├── PHYSICAL (10 domains)
├── SOCIAL (10 domains)
├── CREATIVE (10 domains)
├── INTELLECTUAL (10 domains)
├── REFLECTIVE (10 domains)
├── EMOTIONAL (8 domains)
├── SPIRITUAL_EXISTENTIAL (8 domains)
├── EXPLORATORY_ADVENTUROUS (9 domains)
├── PRODUCTIVE_PRACTICAL (9 domains)
├── LEISURE_RECREATIONAL (8 domains)
└── GENERAL (1 fallback domain)
```

### 2. Relationship Strength Architecture
- **PRIMARY (100)**: Single domain that defines the activity's core essence
- **SIGNIFICANT (70)**: Major secondary aspect that substantially influences experience
- **MODERATE (30)**: Important contextual element that affects selection criteria
- **MINIMAL (10)**: Tangential connection providing additional filtering granularity

### 3. Industry-Grade Selection Principles
1. **Multi-Dimensional Excellence**: Activities characterized across multiple domain categories
2. **Cross-Category Synergy**: Leverage relationships between different primary categories
3. **Granular Precision**: Specific sub-domains within categories for exact matching
4. **Business Intelligence**: Domain relationships that enhance recommendation algorithms
5. **User Experience Optimization**: Rich metadata enabling sophisticated filtering and discovery

## Secondary Domain Patterns

### Pattern 1: Cross-Category Enhancement
**Purpose**: Enrich activities with multi-dimensional characteristics

**Examples**:
```python
# Yoga Activity
primary_domain: 'phys_flexibility'
secondary_domains: [
    {'domain': 'refl_mindful', 'strength': 70},  # Reflective aspect
    {'domain': 'emot_stress', 'strength': 30},   # Emotional benefit
]

# Creative Writing Workshop
primary_domain: 'creative_writing'
secondary_domains: [
    {'domain': 'soc_connecting', 'strength': 70}, # Social interaction
    {'domain': 'intel_learn', 'strength': 30},    # Learning component
]
```

### Pattern 2: Granular Specialization
**Purpose**: Provide specific categorization within primary categories

**Examples**:
```python
# Advanced Rock Climbing
primary_domain: 'phys_outdoor'
secondary_domains: [
    {'domain': 'phys_strength', 'strength': 70},  # Strength requirement
    {'domain': 'explor_risk', 'strength': 30},    # Adventure element
]

# Meditation Retreat
primary_domain: 'refl_meditate'
secondary_domains: [
    {'domain': 'spirit_connect', 'strength': 70}, # Spiritual aspect
    {'domain': 'refl_mindful', 'strength': 30},   # Mindfulness practice
]
```

### Pattern 3: Contextual Relevance
**Purpose**: Add context that affects user experience or selection

**Examples**:
```python
# Board Game Night
primary_domain: 'leisure_play'
secondary_domains: [
    {'domain': 'soc_connecting', 'strength': 70}, # Social context
    {'domain': 'intel_strategic', 'strength': 30}, # Thinking required
]

# Cooking Class
primary_domain: 'creative_culinary'
secondary_domains: [
    {'domain': 'intel_learn', 'strength': 70},    # Learning component
    {'domain': 'soc_group', 'strength': 30},      # Group setting
]
```

## Domain Relationship Matrix

### High-Synergy Domain Combinations
These combinations frequently appear together and provide excellent user value:

#### Physical + Reflective
- `phys_flexibility` + `refl_mindful` (Yoga, Tai Chi)
- `phys_outdoor` + `spirit_nature` (Hiking, Nature walks)
- `phys_dance` + `emot_express` (Expressive dance)

#### Creative + Social
- `creative_music` + `soc_connecting` (Jam sessions, Choir)
- `creative_writing` + `soc_group` (Writing workshops)
- `creative_perform` + `soc_leadership` (Theater direction)

#### Intellectual + Practical
- `intel_learn` + `prod_skill` (Skill-building courses)
- `intel_problem` + `prod_organize` (System design)
- `intel_strategic` + `prod_career` (Career planning)

#### Emotional + Social
- `emot_aware` + `soc_empathy` (Emotional intelligence training)
- `emot_express` + `soc_comm` (Communication workshops)
- `emot_comfort` + `soc_connecting` (Support groups)

### Cross-Category Enhancement Opportunities
Activities benefit from secondary domains in different primary categories:

#### Leisure Activities Enhanced by:
- **Intellectual**: `leisure_play` + `intel_strategic`
- **Social**: `leisure_hobby` + `soc_connecting`
- **Creative**: `leisure_entertain` + `creative_visual`

#### Productive Activities Enhanced by:
- **Reflective**: `prod_organize` + `refl_values`
- **Emotional**: `prod_habit` + `emot_regulate`
- **Social**: `prod_career` + `soc_network`

## Implementation Guidelines

### 1. Secondary Domain Assignment Rules

**Maximum Secondary Domains**: 3 per activity
**Minimum Strength**: 10 (minimal connection)
**Recommended Distribution**:
- 1 SIGNIFICANT (70) secondary domain
- 1-2 MODERATE (30) secondary domains
- 0-1 MINIMAL (10) secondary domains

### 2. Validation Rules

```python
def validate_secondary_domains(primary_domain, secondary_domains):
    """Validate secondary domain assignments."""
    
    # Rule 1: No duplicate domains
    domain_codes = [primary_domain] + [sd['domain'] for sd in secondary_domains]
    if len(domain_codes) != len(set(domain_codes)):
        raise ValidationError("Duplicate domains not allowed")
    
    # Rule 2: Maximum 3 secondary domains
    if len(secondary_domains) > 3:
        raise ValidationError("Maximum 3 secondary domains allowed")
    
    # Rule 3: Strength values must be valid
    valid_strengths = [10, 30, 70]
    for sd in secondary_domains:
        if sd['strength'] not in valid_strengths:
            raise ValidationError(f"Invalid strength: {sd['strength']}")
    
    # Rule 4: At least one cross-category domain (recommended)
    primary_category = get_domain_category(primary_domain)
    cross_category_count = sum(1 for sd in secondary_domains 
                              if get_domain_category(sd['domain']) != primary_category)
    
    if cross_category_count == 0:
        # Warning, not error - still valid but less optimal
        logger.warning("No cross-category secondary domains found")
    
    return True
```

### 3. Quality Metrics

**High-Quality Domain Assignment Indicators**:
- ✅ 1-2 cross-category secondary domains
- ✅ Logical strength progression (70 > 30 > 10)
- ✅ Contextually relevant combinations
- ✅ Enhances user selection accuracy

**Low-Quality Domain Assignment Indicators**:
- ❌ All domains from same primary category
- ❌ Arbitrary or illogical strength assignments
- ❌ Too many secondary domains (>3)
- ❌ Irrelevant domain combinations

## Business Impact

### 1. Activity Selection Quality
Secondary domains enable:
- **Precise Filtering**: Users can find activities matching multiple criteria
- **Contextual Matching**: Activities match user's current needs and preferences
- **Serendipitous Discovery**: Cross-category relationships reveal unexpected options

### 2. Wheel Generation Excellence
Secondary domains improve:
- **Diversity**: Ensures varied activity types in generated wheels
- **Relevance**: Activities match user profile across multiple dimensions
- **Balance**: Prevents over-concentration in single domain categories

### 3. User Experience Enhancement
Secondary domains provide:
- **Granular Control**: Users can specify detailed preferences
- **Rich Metadata**: Activities have comprehensive descriptive information
- **Intelligent Recommendations**: System can suggest based on multi-faceted interests

## Migration and Implementation Strategy

### Phase 1: Audit Current Assignments
1. Analyze existing secondary domain patterns
2. Identify missing cross-category relationships
3. Flag low-quality domain assignments

### Phase 2: Systematic Enhancement
1. Apply secondary domain patterns to all activities
2. Ensure cross-category representation
3. Validate strength assignments

### Phase 3: Quality Assurance
1. Test activity selection accuracy
2. Measure wheel generation diversity
3. Validate user experience improvements

## Success Metrics

### Technical Metrics
- **Domain Coverage**: >80% of activities have 2+ secondary domains
- **Cross-Category Rate**: >60% of activities have cross-category secondary domains
- **Validation Success**: 100% of domain assignments pass validation rules

### Business Metrics
- **Selection Accuracy**: Improved user satisfaction with activity recommendations
- **Wheel Diversity**: Increased variety in generated wheels
- **User Engagement**: Higher activity completion rates

## Conclusion

The secondary domain specification provides the architectural foundation for high-quality, granular activity categorization. By following these patterns and guidelines, we ensure that the domain system preserves the richness needed for excellent activity selection while maintaining consistency and scalability.

This specification is the cornerstone of business-critical activity matching and must be implemented with excellence to achieve the quality standards required for the core Goali experience.

---

## APPENDIX: Complete Authoritative Domain Catalog

### 1. PHYSICAL CATEGORY (10 domains)
**Primary Code:** `physical`
**Description:** Activities that engage the body and promote health and fitness.

**Sub-Domain Portfolio:**
- `phys_cardio` - Cardiovascular Exercise (heart rate, endurance activities)
- `phys_strength` - Strength Training (resistance, muscle building)
- `phys_chill` - Physical but Chill (walking, gentle movement)
- `phys_flexibility` - Flexibility & Mobility (yoga, stretching, range of motion)
- `phys_sports` - Recreational Sports (structured, competitive elements)
- `phys_outdoor` - Outdoor Activities (natural settings, fresh air)
- `phys_dance` - Dance & Movement (rhythmic, expressive movement)
- `phys_martial` - Martial Arts (combat systems, self-defense)
- `phys_balance` - Balance & Coordination (proprioception, stability)

### 2. SOCIAL CATEGORY (10 domains)
**Primary Code:** `social`
**Description:** Activities that engage users with others in interpersonal contexts.

**Sub-Domain Portfolio:**
- `soc_connecting` - Social Connection (bonding, relationship building)
- `soc_group` - Group Dynamics (team interaction, community engagement)
- `soc_comm` - Communication Skills (verbal/non-verbal, active listening)
- `soc_empathy` - Empathy Building (perspective understanding, emotional intelligence)
- `soc_network` - Networking (professional/personal circle expansion)
- `soc_romance` - Romantic Relationship (intimate partner connection)
- `soc_family` - Family Bonding (family relationship enhancement)
- `soc_leadership` - Leadership (guiding, influencing, motivating others)
- `soc_conflict` - Conflict Resolution (problem-solving, mediation)

### 3. CREATIVE CATEGORY (10 domains)
**Primary Code:** `creative`
**Description:** Activities involving creative expression and artistic endeavors.

**Sub-Domain Portfolio:**
- `creative_visual` - Visual Arts (painting, drawing, sculpture, digital art)
- `creative_observation` - Creative Observation (observational creativity)
- `creative_auditory` - Creative Auditory (listening-based creativity)
- `creative_music` - Music Creation (instruments, singing, composition)
- `creative_writing` - Creative Writing (fiction, poetry, narrative)
- `creative_design` - Design Thinking (problem-solving, aesthetic creation)
- `creative_culinary` - Culinary Arts (food preparation, recipe development)
- `creative_perform` - Performance Arts (theater, comedy, live presentation)
- `creative_craft` - Crafts & Making (hands-on object creation)
- `creative_improv` - Improvisation (spontaneous creative expression)

### 4. INTELLECTUAL CATEGORY (10 domains)
**Primary Code:** `intellectual`
**Description:** Activities that engage cognitive abilities and knowledge acquisition.

**Sub-Domain Portfolio:**
- `intel_learn` - Learning & Study (structured knowledge acquisition)
- `intel_problem` - Problem Solving (analytical thinking, systematic approaches)
- `intel_audio` - Intellectual Audio (focused listening activities)
- `intel_strategic` - Strategic Thinking (long-term planning, goal achievement)
- `intel_curiosity` - Intellectual Curiosity (exploration driven by interest)
- `intel_language` - Language & Linguistics (language acquisition, communication)
- `intel_debate` - Critical Discourse (reasoned argument, perspective evaluation)
- `intel_science` - Scientific Inquiry (observation, experimentation, evidence-based reasoning)
- `intel_tech` - Technology & Digital Skills (digital tools, computational thinking)

### 5. REFLECTIVE CATEGORY (10 domains)
**Primary Code:** `reflective`
**Description:** Activities focused on introspection and self-awareness.

**Sub-Domain Portfolio:**
- `refl_meditate` - Meditation (focused attention, mental clarity)
- `refl_journal` - Journaling & Self-Reflection (written contemplation, insight)
- `refl_mindful` - Mindfulness Practice (present-moment awareness)
- `refl_values` - Values Clarification (principles, ethics, priorities)
- `refl_persp` - Perspective Taking (viewing from different angles)
- `refl_philos` - Philosophical Contemplation (existence, meaning, reality)
- `refl_grat` - Gratitude Practice (appreciation, positive recognition)
- `refl_comfort` - Comfort Zone Reflection (boundary examination, growth)
- `refl_micro` - Micro-Wellness Activities (brief wellness practices, 2-15 minutes)

### 6. EMOTIONAL CATEGORY (8 domains)
**Primary Code:** `emotional`
**Description:** Activities that engage users with their emotional experiences.

**Sub-Domain Portfolio:**
- `emot_aware` - Emotional Awareness (recognition, understanding of emotions)
- `emot_regulate` - Emotion Regulation (managing responses, resilience)
- `emot_express` - Emotional Expression (healthy emotional outlets)
- `emot_compass` - Self-Compassion (kindness toward oneself)
- `emot_joy` - Joy & Pleasure (happiness, satisfaction, delight)
- `emot_stress` - Stress Management (anxiety reduction, coping techniques)
- `emot_forgive` - Forgiveness & Letting Go (releasing resentment, healing)
- `emot_comfort` - Comfort & Nurturing (emotional security, soothing)

### 7. SPIRITUAL_EXISTENTIAL CATEGORY (8 domains)
**Primary Code:** `spiritual_existential`
**Description:** Activities exploring meaning, transcendence, and spiritual connection.

**Sub-Domain Portfolio:**
- `spirit_purpose` - Purpose & Meaning (life direction, personal significance)
- `spirit_connect` - Spiritual Connection (transcendent elements, spirituality)
- `spirit_ritual` - Ritual & Practice (ceremonies, spiritual habits)
- `spirit_nature` - Nature Connection (natural world relationship)
- `spirit_transced` - Transcendent Experience (peak experiences, flow states)
- `spirit_death` - Mortality Contemplation (impermanence, death relationship)
- `spirit_commun` - Spiritual Community (shared spiritual orientations)
- `spirit_wisdom` - Wisdom Traditions (philosophical, spiritual traditions)

### 8. EXPLORATORY_ADVENTUROUS CATEGORY (9 domains)
**Primary Code:** `exploratory_adventurous`
**Description:** Activities involving exploration, adventure, and novelty seeking.

**Sub-Domain Portfolio:**
- `explor_travel` - Travel & Discovery (geographical exploration, cultural immersion)
- `explor_risk` - Controlled Risk-Taking (calculated challenges, comfort zone expansion)
- `explor_sensory` - Sensory Exploration (five senses engagement, awareness expansion)
- `explor_cultural` - Cultural Exploration (unfamiliar traditions, perspectives)
- `explor_novel` - Novelty Seeking (new experiences, freshness, variety)
- `explor_adren` - Adrenaline Activities (high-energy, excitement, thrill)
- `explor_improv` - Spontaneity & Improvisation (minimal planning, unexpected outcomes)
- `explor_unknown` - Embracing Uncertainty (unknown, unpredictable engagement)
- `explor_digital` - Digital-Physical Hybrid (technology-enhanced physical activities)

### 9. PRODUCTIVE_PRACTICAL CATEGORY (9 domains)
**Primary Code:** `productive_practical`
**Description:** Activities focused on productivity, efficiency, and practical skill development.

**Sub-Domain Portfolio:**
- `prod_organize` - Organization & Planning (order, structure, efficiency)
- `prod_habit` - Habit Formation (consistent behaviors, routine building)
- `prod_time` - Time Management (effective allocation, productivity principles)
- `prod_skill` - Practical Skill Development (concrete abilities, everyday utility)
- `prod_financial` - Financial Management (budgeting, investing, economic resources)
- `prod_career` - Career Development (professional advancement, work skills)
- `prod_health` - Health & Wellness Systems (wellbeing maintenance, health practices)
- `prod_home` - Home Management (living space organization, functionality)
- `prod_transition` - Transition Moments (in-between times, idle moment optimization)

### 10. LEISURE_RECREATIONAL CATEGORY (8 domains)
**Primary Code:** `leisure_recreational`
**Description:** Activities pursued for enjoyment, relaxation, and recreational purposes.

**Sub-Domain Portfolio:**
- `leisure_relax` - Relaxation (rest, recovery, tension release)
- `leisure_play` - Play & Games (enjoyment-focused, structured/free play)
- `leisure_entertain` - Entertainment Consumption (media, performances, shows)
- `leisure_collect` - Collection & Curation (gathering, organizing interests)
- `leisure_nature` - Recreational Nature Activities (leisurely outdoor pursuits)
- `leisure_social` - Social Recreation (low-pressure social enjoyment)
- `leisure_hobby` - Hobby Participation (non-professional interests, satisfaction)
- `leisure_festive` - Celebration & Festivity (special occasions, cultural significance)

### 11. GENERAL CATEGORY (1 domain)
**Primary Code:** `general`
**Description:** Fallback domain for activities that don't fit specific categories.

**Sub-Domain Portfolio:**
- `general` - General (uncertain classification, fallback categorization)

---

## Industry-Grade Implementation Standards

This authoritative catalog represents the complete domain architecture for the Goali activity system. Each domain has been carefully designed to:

1. **Preserve Granularity**: Maintain the detailed categorization needed for high-quality activity selection
2. **Enable Cross-Category Relationships**: Support multi-dimensional activity characterization
3. **Facilitate Business Intelligence**: Provide rich metadata for recommendation algorithms
4. **Ensure Scalability**: Support future domain expansion and refinement
5. **Maintain Consistency**: Follow standardized naming and description patterns

The 105 domains across 11 categories provide comprehensive coverage for all activity types while maintaining the architectural excellence required for core business operations.
