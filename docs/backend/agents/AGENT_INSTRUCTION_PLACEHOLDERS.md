# Agent Instruction Placeholders System

*Last Updated: 2025-06-20*

## Overview

This document defines the authoritative placeholder system for agent instructions in the Goali backend. The placeholder system allows agent instructions stored in the database to contain dynamic variables that are injected at runtime with user-specific context, ensuring personalized and contextually relevant LLM interactions.

## Architecture

### Placeholder Format

All placeholders use the format: `{{PLACEHOLDER_NAME}}`

- **Case Sensitive**: Placeholder names are case-sensitive
- **No Spaces**: No spaces allowed within placeholder names
- **Alphanumeric + Underscore**: Only alphanumeric characters and underscores allowed
- **Clear Naming**: Use descriptive names that clearly indicate the data being injected

### Placeholder Categories

#### 1. Temporal Context
- `{{LOCAL_DATE}}` - User's local date (YYYY-MM-DD format)
- `{{LOCAL_TIME}}` - User's local time (HH:MM format)
- `{{LOCAL_DATETIME}}` - User's local date and time (YYYY-MM-DD HH:MM format)
- `{{DAY_OF_WEEK}}` - User's current day of the week (e.g., "Monday")
- `{{TIME_OF_DAY}}` - Time period (e.g., "morning", "afternoon", "evening")

#### 2. User Profile Context
- `{{USER_NAME}}` - User's preferred name or profile name
- `{{USER_AGE}}` - User's age (if available)
- `{{USER_LOCATION}}` - User's general location (city/region)
- `{{PROFILE_COMPLETION}}` - Profile completion percentage

#### 3. Psychological Context
- `{{CURRENT_MOOD}}` - User's current mood state
- `{{ENERGY_LEVEL}}` - User's current energy level
- `{{STRESS_LEVEL}}` - User's current stress level
- `{{DOMINANT_TRAITS}}` - List of user's dominant personality traits
- `{{TRAIT_OPENNESS}}` - Openness trait score and description
- `{{TRAIT_CONSCIENTIOUSNESS}}` - Conscientiousness trait score and description
- `{{TRAIT_EXTRAVERSION}}` - Extraversion trait score and description
- `{{TRAIT_AGREEABLENESS}}` - Agreeableness trait score and description
- `{{TRAIT_NEUROTICISM}}` - Neuroticism trait score and description
- `{{TRAIT_HONESTY_HUMILITY}}` - Honesty-Humility trait score and description

#### 4. Environmental Context
- `{{CURRENT_ENVIRONMENT}}` - User's current environment description
- `{{ENVIRONMENT_TYPE}}` - Environment type (home, office, public, etc.)
- `{{PRIVACY_LEVEL}}` - Privacy level (private, semi-private, public)
- `{{SPACE_SIZE}}` - Available space size (small, medium, large)
- `{{NOISE_LEVEL}}` - Current noise level (quiet, moderate, noisy)
- `{{SOCIAL_CONTEXT}}` - Social context (alone, with family, with friends, etc.)

#### 5. Resource Context
- `{{TIME_AVAILABLE}}` - User's available time in minutes
- `{{TIME_FLEXIBILITY}}` - Time flexibility level (rigid, flexible, very flexible)
- `{{AVAILABLE_RESOURCES}}` - List of available resources/inventory items
- `{{PHYSICAL_LIMITATIONS}}` - User's physical limitations (if any)
- `{{COGNITIVE_LIMITATIONS}}` - User's cognitive limitations (if any)
- `{{CAPABILITIES}}` - User's capabilities and skills

#### 6. Goal and Aspiration Context
- `{{PRIMARY_GOALS}}` - User's primary goals
- `{{CURRENT_ASPIRATIONS}}` - User's current aspirations
- `{{FOCUS_AREAS}}` - Areas user wants to focus on
- `{{GROWTH_PRIORITIES}}` - User's growth priorities

#### 7. Historical Context
- `{{RECENT_ACTIVITIES}}` - Recently completed activities
- `{{PREFERRED_DOMAINS}}` - User's preferred activity domains
- `{{COMPLETION_PATTERNS}}` - User's activity completion patterns
- `{{FEEDBACK_THEMES}}` - Common themes from user feedback

#### 8. Trust and Relationship Context
- `{{TRUST_LEVEL}}` - User's trust level with the system
- `{{TRUST_PHASE}}` - Current trust phase (foundation, expansion, etc.)
- `{{INTERACTION_HISTORY}}` - Summary of interaction history
- `{{COMMUNICATION_PREFERENCES}}` - User's communication preferences

## Implementation Guidelines

### 1. Placeholder Injection Process

```python
def inject_placeholders(instruction_template: str, context: dict) -> str:
    """
    Inject context variables into instruction template.
    
    Args:
        instruction_template: Template string with placeholders
        context: Dictionary containing placeholder values
        
    Returns:
        Instruction string with placeholders replaced
    """
    # Implementation handles missing placeholders gracefully
    # Logs warnings for undefined placeholders
    # Provides fallback values where appropriate
```

### 2. Context Building

Each agent should build a comprehensive context dictionary containing all available placeholder values:

```python
context = {
    'LOCAL_DATE': user_timezone.localize(datetime.now()).strftime('%Y-%m-%d'),
    'LOCAL_TIME': user_timezone.localize(datetime.now()).strftime('%H:%M'),
    'USER_NAME': user_profile.profile_name,
    'CURRENT_MOOD': current_mood.mood_state,
    'ENERGY_LEVEL': current_mood.energy_level,
    # ... all other placeholders
}
```

### 3. Error Handling

- **Missing Placeholders**: Log warning and leave placeholder unchanged
- **Invalid Context**: Use fallback values or skip placeholder
- **Type Mismatches**: Convert to string representation
- **Null Values**: Use appropriate default or "not specified"

## Usage Examples

### Basic Instruction Template

```
You are helping {{USER_NAME}} on {{LOCAL_DATE}} at {{LOCAL_TIME}}.

Current Context:
- Mood: {{CURRENT_MOOD}}
- Energy Level: {{ENERGY_LEVEL}}
- Environment: {{CURRENT_ENVIRONMENT}}
- Time Available: {{TIME_AVAILABLE}} minutes

User's Psychological Profile:
- Dominant Traits: {{DOMINANT_TRAITS}}
- Openness: {{TRAIT_OPENNESS}}
- Conscientiousness: {{TRAIT_CONSCIENTIOUSNESS}}

Please tailor your response considering these factors.
```

### Activity Tailoring Template

```
Tailor this activity for {{USER_NAME}} considering:

Temporal Context:
- Current time: {{LOCAL_DATETIME}}
- Day of week: {{DAY_OF_WEEK}}

Environmental Context:
- Location: {{CURRENT_ENVIRONMENT}}
- Privacy: {{PRIVACY_LEVEL}}
- Space: {{SPACE_SIZE}}
- Social context: {{SOCIAL_CONTEXT}}

Personal Context:
- Available time: {{TIME_AVAILABLE}} minutes
- Energy level: {{ENERGY_LEVEL}}
- Current mood: {{CURRENT_MOOD}}
- Key traits: {{DOMINANT_TRAITS}}

Resources:
- Available items: {{AVAILABLE_RESOURCES}}
- Limitations: {{PHYSICAL_LIMITATIONS}}

Goals:
- Primary focus: {{PRIMARY_GOALS}}
- Growth areas: {{GROWTH_PRIORITIES}}
```

## Best Practices

1. **Comprehensive Context**: Always provide as many placeholders as possible
2. **Graceful Degradation**: Handle missing data gracefully
3. **Logging**: Log placeholder injection for debugging
4. **Performance**: Cache context building where possible
5. **Security**: Sanitize user data before injection
6. **Testing**: Test with various placeholder combinations

## Validation

All placeholder implementations must:
- Handle missing data gracefully
- Provide meaningful fallback values
- Log injection process for debugging
- Maintain consistent formatting
- Support internationalization where applicable

## Future Extensions

- **Conditional Placeholders**: `{{IF_CONDITION:value}}`
- **Formatted Placeholders**: `{{DATE:format}}`
- **Calculated Placeholders**: `{{CALC:expression}}`
- **Localized Placeholders**: `{{LOCALIZED:key}}`
