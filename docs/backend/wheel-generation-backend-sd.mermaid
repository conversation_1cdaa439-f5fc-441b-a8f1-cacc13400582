sequenceDiagram
    participant User as 👤 User
    participant WS as 🔌 WebSocket<br/>(consumers.py)
    participant Dispatcher as 🎯 ConversationDispatcher<br/>(conversation_dispatcher.py)
    participant CeleryTask as ⚙️ Celery Task<br/>(wheel_generation_tasks.py)
    participant WheelGraph as 🕸️ LangGraph Workflow<br/>(wheel_generation_graph.py)
    participant WheelAgent as 🤖 WheelActivityAgent<br/>(wheel_activity_agent.py)
    participant WheelService as 🏗️ WheelGenerationService<br/>(wheel_generation_service.py)
    participant ActivitySelector as 🎯 ActivitySelectionService<br/>(activity_selection_service.py)
    participant IntelligentSelector as 🧠 IntelligentActivitySelector<br/>(programmatic_activity_selector.py)
    participant ActivityTailorer as 🎨 ActivityTailoringService<br/>(activity_tailoring_service.py)
    participant LLMAgent as 🤖 LLM Agent<br/>(activity_tailoring_service.py)
    participant WheelBuilder as 🏗️ WheelBuilderService<br/>(wheel_builder_service.py)
    participant WheelRepo as 💾 WheelRepository<br/>(django_wheel_repository.py)
    participant ActivityRepo as 💾 ActivityRepository<br/>(django_activity_repository.py)
    participant DB as 🗄️ Database<br/>(Django ORM)

    %% 1. User Initiates Wheel Generation
    User->>WS: WebSocket Message<br/>{"type": "wheel_generation", "time_available": 10, "energy_level": 80}
    Note over WS: consumers.py<br/>receive() method
    
    %% 2. WebSocket Processing
    WS->>WS: Extract user_profile_id<br/>from session
    WS->>Dispatcher: process_message()<br/>with user context
    Note over Dispatcher: conversation_dispatcher.py<br/>Classifies intent as "wheel_generation"
    
    %% 3. Celery Task Dispatch
    Dispatcher->>CeleryTask: execute_wheel_generation_workflow.delay()<br/>(user_profile_id, context_packet)
    Note over CeleryTask: wheel_generation_tasks.py<br/>@shared_task decorator
    CeleryTask->>WS: Immediate response<br/>{"status": "processing"}
    WS->>User: Processing notification
    
    %% 4. LangGraph Workflow Initialization
    CeleryTask->>WheelGraph: create_wheel_generation_graph()<br/>Initialize workflow state
    Note over WheelGraph: wheel_generation_graph.py<br/>StateGraph with multi-agent flow
    WheelGraph->>WheelAgent: invoke_wheel_activity_agent()<br/>with workflow state
    
    %% 5. Agent Coordination Layer
    Note over WheelAgent: wheel_activity_agent.py<br/>Thin coordinator pattern
    WheelAgent->>WheelAgent: _extract_context_from_state()<br/>Parse workflow state
    WheelAgent->>WheelAgent: _build_wheel_generation_request()<br/>Create domain model
    
    %% 6. Business Logic Delegation
    WheelAgent->>WheelService: generate_wheel(request)<br/>Delegate to domain service
    Note over WheelService: wheel_generation_service.py<br/>Pure business logic
    
    %% 7. Activity Selection Phase
    WheelService->>ActivitySelector: select_activities(criteria)<br/>Time=10min, Energy=80%
    Note over ActivitySelector: activity_selection_service.py<br/>Business rules validation
    ActivitySelector->>IntelligentSelector: select_activities()<br/>Programmatic selection
    Note over IntelligentSelector: programmatic_activity_selector.py<br/>Energy-based distribution logic
    
    %% 8. Database Query for Activities
    IntelligentSelector->>DB: Query GenericActivity<br/>Filter by time/energy constraints
    DB-->>IntelligentSelector: Raw activity data
    IntelligentSelector->>IntelligentSelector: Apply intelligent scoring<br/>Energy distribution algorithm
    IntelligentSelector-->>ActivitySelector: Selected activities (6 items)
    ActivitySelector-->>WheelService: ActivityData objects
    
    %% 9. Activity Tailoring Phase
    WheelService->>ActivityTailorer: tailor_activities(activities, user_context)<br/>Personalization phase
    Note over ActivityTailorer: activity_tailoring_service.py<br/>LLM-based personalization
    
    loop For each activity (6 iterations)
        ActivityTailorer->>ActivityTailorer: _apply_business_adjustments()<br/>Duration/difficulty adjustment
        ActivityTailorer->>ActivityTailorer: Check if already tailored<br/>Prevent duplicate suffixes
        
        alt Activity not already tailored
            ActivityTailorer->>LLMAgent: LLM tailoring call<br/>Personalize for user context
            Note over LLMAgent: Uses Mistral LLM<br/>Structured output generation
            LLMAgent-->>ActivityTailorer: Tailored activity<br/>with "(Tailored for 10min)" suffix
        else Activity already tailored
            ActivityTailorer->>ActivityTailorer: Update duration only<br/>Skip LLM processing
        end
        
        ActivityTailorer->>ActivityRepo: get_or_create()<br/>ActivityTailored object
        Note over ActivityRepo: django_activity_repository.py<br/>Repository pattern with caching
        ActivityRepo->>DB: INSERT/SELECT ActivityTailored<br/>Unique constraint handling
        DB-->>ActivityRepo: ActivityTailored ID
        ActivityRepo-->>ActivityTailorer: Persisted activity
    end
    
    ActivityTailorer-->>WheelService: Tailored activities list
    
    %% 10. Wheel Building Phase
    WheelService->>WheelBuilder: build_wheel(activities, config)<br/>Structure creation
    Note over WheelBuilder: wheel_builder_service.py<br/>Wheel structure logic
    WheelBuilder->>WheelBuilder: Calculate percentages<br/>Equal distribution (16.67% each)
    WheelBuilder->>WheelBuilder: Apply domain colors<br/>Color system integration
    WheelBuilder-->>WheelService: Complete wheel structure
    
    %% 11. Wheel Persistence
    WheelService->>WheelRepo: save_wheel(wheel, user_profile_id)<br/>Persist complete wheel
    Note over WheelRepo: django_wheel_repository.py<br/>Repository pattern
    WheelRepo->>DB: INSERT Wheel + WheelItems<br/>Transactional operation
    DB-->>WheelRepo: Wheel ID
    WheelRepo-->>WheelService: Persisted wheel ID
    
    %% 12. Result Transformation
    WheelService-->>WheelAgent: WheelGenerationResult<br/>Domain model result
    WheelAgent->>WheelAgent: _transform_result_for_workflow()<br/>Convert to workflow format
    WheelAgent-->>WheelGraph: Wheel data for workflow
    
    %% 13. Workflow Completion
    WheelGraph->>WheelGraph: Update workflow state<br/>Mark completion
    WheelGraph-->>CeleryTask: Workflow result
    
    %% 14. WebSocket Response
    CeleryTask->>WS: send_wheel_data()<br/>Channel layer message
    Note over WS: consumers.py<br/>wheel_data() method
    WS->>WS: _validate_wheel_data()<br/>API contract validation
    WS->>User: WebSocket message<br/>{"type": "wheel_data", "wheel": {...}}
    
    %% 15. User Receives Wheel
    Note over User: Frontend receives wheel<br/>6 activities, 10min each<br/>Proper domain colors