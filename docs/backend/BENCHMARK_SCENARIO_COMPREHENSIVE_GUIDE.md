# Benchmark Scenario Comprehensive Guide

## Overview

This document provides a comprehensive guide to the BenchmarkScenario system in Goali, covering its structure, usage patterns, and all components involved in the benchmarking ecosystem.

## Table of Contents

1. [Model Structure](#model-structure)
2. [Usage Inventory](#usage-inventory)
3. [Data Flow](#data-flow)
4. [API Endpoints](#api-endpoints)
5. [Management Commands](#management-commands)
6. [Frontend Integration](#frontend-integration)
7. [Schema Validation](#schema-validation)
8. [File System Organization](#file-system-organization)
9. [Migration History](#migration-history)
10. [Best Practices](#best-practices)

## Model Structure

### Core Model: BenchmarkScenario

Located in: `backend/apps/main/models.py`

The BenchmarkScenario model has evolved from a metadata-heavy approach to a structured field approach:

#### Structured Fields (New)
- `workflow_type`: CharField - Type of workflow being tested
- `warmup_runs`: PositiveIntegerField - Number of warmup runs (default: 1)
- `benchmark_runs`: PositiveInte<PERSON><PERSON>ield - Number of benchmark runs (default: 3)
- `timeout_seconds`: PositiveIntegerField - Execution timeout
- `evaluation_template_id`: PositiveIntegerField - ID of evaluation template
- `evaluation_template_name`: CharField - Name of evaluation template
- `expected_quality_criteria`: JSONField - Quality criteria for evaluation
- `mock_tool_responses`: JSONField - Mock responses for tool calls
- `user_profile_context`: JSONField - User profile context
- `activity_context`: JSONField - Activity-specific context

#### Legacy Fields
- `metadata`: JSONField - Backward compatibility and additional custom fields

#### Core Fields
- `name`: CharField(100) - Unique scenario name
- `description`: TextField - Detailed description
- `agent_role`: CharField(50) - Role of the agent being tested
- `input_data`: JSONField - Input data for the scenario
- `tags`: ManyToManyField(BenchmarkTag) - Categorization tags
- `is_active`: BooleanField - Whether scenario is active
- `version`: PositiveIntegerField - Version number
- `parent_scenario`: ForeignKey - Link to previous version
- `is_latest`: BooleanField - Latest version flag
- `variations`: ManyToManyField - Related scenario variations

### Related Models

#### BenchmarkTag
- Simple categorization system for scenarios
- Used for filtering and organization

#### BenchmarkRun
- Records execution results of scenarios
- Links to BenchmarkScenario via ForeignKey
- Contains performance metrics, token usage, and evaluation results

## Usage Inventory

### 1. Model Imports and Direct Usage

**Files that import BenchmarkScenario:**
- `backend/apps/main/models.py` - Model definition
- `backend/apps/admin_tools/benchmark/views.py` - Admin API views (CRUD operations)
- `backend/apps/main/services/benchmark_service.py` - Service layer (async operations)
- `backend/apps/main/tasks/benchmark_tasks.py` - Celery tasks (background execution)
- `backend/real_condition_tests/test_celery_benchmark_user_id.py` - Integration testing
- `backend/real_condition_tests/test_admin_interface_exact_path.py` - Admin interface testing

**Database Operations Performed:**
- **CREATE**: `objects.create()`, `objects.update_or_create()`
- **READ**: `objects.get()`, `objects.filter()`, `objects.all()`, `objects.prefetch_related()`
- **UPDATE**: `save()`, `update()`, field modifications
- **DELETE**: `delete()` (individual and bulk)
- **RELATIONSHIPS**: `tags.add()`, `tags.clear()`, `tags.set()`

### 2. Management Commands

**Scenario Creation Commands:**
- `seed_benchmark_scenarios.py` - Creates sample scenarios
- `create_benchmark_scenarios_v3.py` - Creates scenarios from JSON files
- `create_workflow_benchmark_scenarios.py` - Creates workflow-specific scenarios

**Validation Commands:**
- `validate_benchmarks_v3.py` - Validates scenario structure and content

### 3. API Endpoints

**Admin Interface APIs:**
- `GET /admin/benchmarks/api/scenarios/` - List scenarios with filtering
- `GET /admin/benchmarks/api/scenarios/<id>/` - Get specific scenario
- `POST /admin/benchmarks/api/scenarios/` - Create new scenario
- `PUT /admin/benchmarks/api/scenarios/<id>/` - Update scenario
- `DELETE /admin/benchmarks/api/scenarios/<id>/` - Delete scenario
- `PATCH /admin/benchmarks/api/scenarios/` - Batch operations

### 4. Frontend Integration

**Templates:**
- `backend/templates/admin_tools/benchmark_management.html` - Main management interface
- `backend/templates/admin_tools/benchmark_history.html` - History view
- `backend/templates/admin_tools/modals/scenario_detail_modal.html` - Detail modal
- `backend/templates/admin_tools/modals/quick_test_config_modal.html` - Quick test configuration

**JavaScript Files:**
- `backend/static/admin/js/benchmark_management.js` - Main management logic
- `backend/static/admin/js/scenario_detail_modal.js` - Detail modal functionality
- `backend/static/admin/js/scenario_editing_modal.js` - Editing functionality
- `backend/static/admin/js/quick_test_modal_enhancements.js` - Quick test enhancements

**JavaScript Functions that Manipulate Scenarios:**
- `loadScenarios()` - Fetches and displays scenario list
- `editScenario(id)` - Opens scenario editing modal
- `deleteScenario(id)` - Deletes scenario with confirmation
- `validateScenario(id)` - Validates scenario structure
- `showScenarioDetailModal(id)` - Shows detailed scenario view
- `duplicateScenario()` - Creates copy of scenario
- `exportScenario()` - Exports scenario to JSON
- `validateBenchmarkScenario(data)` - Client-side validation

### 5. Schema Validation

**Pydantic Models:**
- `backend/apps/main/schemas/benchmark/scenarios.py` - Schema definitions
- `BenchmarkScenario` - Main scenario schema
- `BenchmarkScenarioMetadata` - Metadata schema
- `EvaluationCriteria` - Quality criteria schema
- `ToolExpectation` - Mock tool response schema

### 6. Service Layer

**Services:**
- `benchmark_service.py` - Async workflow benchmark execution
- `benchmark_manager.py` - Agent benchmark execution
- `async_workflow_manager.py` - Workflow orchestration

### 7. Task System

**Celery Tasks:**
- `run_workflow_benchmark` - Executes workflow benchmarks
- `run_agent_benchmark` - Executes agent benchmarks

## Data Flow

### Scenario Creation Flow
1. **File-based Creation**: JSON files → Management commands → Database
2. **Admin Interface**: Web form → API endpoint → Database
3. **Programmatic**: Service layer → Model creation → Database

### Scenario Execution Flow
1. **Trigger**: Admin interface or API call
2. **Task Queue**: Celery task creation
3. **Execution**: Workflow/Agent execution with mocked tools
4. **Results**: BenchmarkRun creation with metrics
5. **Display**: Admin interface shows results

### Scenario Management Flow
1. **Discovery**: File system scanning or database queries
2. **Validation**: Schema validation and structure checks
3. **Organization**: Tags, versions, and relationships
4. **Lifecycle**: Active/inactive status, versioning

## File System Organization

### Directory Structure
```
backend/testing/benchmark_data/
├── agents/
│   ├── mentor/
│   │   ├── wheel_generation/
│   │   ├── discussion/
│   │   └── feedback/
│   ├── orchestrator/
│   └── strategy/
├── workflows/
│   ├── wheel_generation/
│   └── activity_feedback/
├── templates/
│   └── evaluation_criteria/
└── contextual_templates/
```

### File Naming Convention
```
{agent_role}_{workflow_type}_{scenario_type}_{variant}_{id}.json
```

Examples:
- `mentor_wheel_generation_basic_001.json`
- `mentor_discussion_complex_trust_high_002.json`
- `orchestrator_activity_feedback_error_handling_003.json`

## Migration History

### Recent Structural Changes

**Migration 0019: Add Structured Fields**
- Added structured fields to replace common metadata usage
- Extracted frequently used metadata into dedicated fields
- Maintained backward compatibility with metadata field

**Migration 0020: Data Migration**
- Migrated existing metadata to structured fields
- Preserved original metadata for backward compatibility
- Updated all queries to use new structured fields

### Field Evolution

**Before (Metadata-heavy):**
```json
{
  "metadata": {
    "workflow_type": "wheel_generation",
    "warmup_runs": 2,
    "benchmark_runs": 5,
    "expected_quality_criteria": {...},
    "mock_tool_responses": {...}
  }
}
```

**After (Structured):**
```python
BenchmarkScenario(
  workflow_type="wheel_generation",
  warmup_runs=2,
  benchmark_runs=5,
  expected_quality_criteria={...},
  mock_tool_responses={...},
  metadata={...}  # Additional/legacy fields
)
```

## Best Practices

### 1. Scenario Design
- Use descriptive names that indicate purpose
- Include comprehensive descriptions
- Set appropriate warmup and benchmark run counts
- Define clear quality criteria

### 2. Mock Tool Responses
- Provide realistic mock responses
- Include parameter validation
- Test edge cases and error conditions
- Document expected tool behavior

**IMPORTANT: Full Real Mode Behavior (Fixed December 2025)**
- When `use_real_tools=True`, mock_tool_responses are completely ignored
- The system will use real tools and real user profiles instead of mock data
- Any user_profile_id in mock_tool_responses (like "test-user-123") will not be used
- The provided user_profile_id parameter takes precedence over scenario data
- This prevents timeouts and ensures authentic benchmark results

### 3. Versioning
- Increment versions for significant changes
- Maintain parent-child relationships
- Use `is_latest` flag appropriately
- Document version changes

### 4. Organization
- Use consistent tagging
- Follow naming conventions
- Group related scenarios
- Maintain directory structure

### 5. Validation
- Always validate scenarios before deployment
- Use schema validation tools
- Test execution paths
- Verify mock tool responses

### 6. Testing Mock Tool Response Handling
**Critical Test for Full Real Mode:**
```bash
# Test that mock_tool_responses are properly ignored in full real mode
docker exec backend-web-1 python /usr/src/app/real_condition_tests/test_quick_test_exact_path.py
```

**Expected Results:**
- ✅ No timeout (completes in ~20 seconds)
- ✅ No "test-user-123" in final API results (0 occurrences)
- ✅ Real user ID used as primary identifier (thousands of traces)
- ✅ Workflow executes with real tools and real database
- ⚠️ Some "test-user-123" traces may remain in historical/logging data (acceptable)

## Integration Points

### With Workflow System
- Scenarios define workflow execution parameters
- Mock tools replace real tool calls during testing
- Results feed back into workflow optimization

### With Agent System
- Agent roles determine execution context
- Agent definitions link to benchmark runs
- Performance metrics guide agent improvements

### With Evaluation System
- Quality criteria define success metrics
- Semantic evaluation provides qualitative assessment
- Results inform evaluation template improvements

### With Admin Interface
- Rich management interface for scenario CRUD
- Detailed viewing and editing capabilities
- Batch operations for efficiency
- Integration with benchmark execution

## Complete File Inventory

### Core System Files
**Model and Schema:**
- `backend/apps/main/models.py` - Django model definition
- `backend/apps/main/schemas/benchmark/scenarios.py` - Pydantic schema
- `backend/apps/main/schemas/benchmark/__init__.py` - Schema exports

**Migrations:**
- `backend/apps/main/migrations/0019_add_scenario_structured_fields.py` - Added structured fields
- `backend/apps/main/migrations/0020_migrate_metadata_to_structured_fields.py` - Data migration

**Management Commands:**
- `backend/apps/main/management/commands/seed_benchmark_scenarios.py` - Sample scenarios
- `backend/apps/main/management/commands/create_benchmark_scenarios_v3.py` - JSON file import
- `backend/apps/main/management/commands/create_workflow_benchmark_scenarios.py` - Workflow scenarios
- `backend/apps/main/management/commands/validate_benchmarks_v3.py` - Validation

**Services and Tasks:**
- `backend/apps/main/services/benchmark_service.py` - Service layer
- `backend/apps/main/tasks/benchmark_tasks.py` - Celery tasks

**Admin Interface:**
- `backend/apps/admin_tools/benchmark/views.py` - API views
- `backend/apps/admin_tools/benchmark/urls.py` - URL routing
- `backend/config/admin.py` - Admin site configuration

**Frontend:**
- `backend/templates/admin_tools/benchmark_management.html` - Main interface
- `backend/templates/admin_tools/benchmark_history.html` - History view
- `backend/templates/admin_tools/modals/scenario_detail_modal.html` - Detail modal
- `backend/static/admin/js/benchmark_management.js` - Main JS logic
- `backend/static/admin/js/scenario_detail_modal.js` - Detail modal JS
- `backend/static/admin/js/scenario_editing_modal.js` - Editing JS
- `backend/static/admin/js/quick_test_modal_enhancements.js` - Quick test JS

**Testing:**
- `backend/real_condition_tests/test_celery_benchmark_user_id.py` - Integration tests
- `backend/real_condition_tests/test_admin_interface_exact_path.py` - Admin tests

**Documentation:**
- `docs/backend/BENCHMARK_SCENARIO_COMPREHENSIVE_GUIDE.md` - This document
- `docs/backend/BENCHMARKING_SYSTEM.md` - System overview
- `next/BENCHMARK_SCHEMA_SYSTEM.md` - Schema documentation
- `next/BENCHMARK_MIGRATION_GUIDE.md` - Migration guide
- `next/BENCHMARK_ADMIN_FRONTEND.md` - Frontend documentation

### Data Files
**Scenario Storage:**
- `backend/testing/benchmark_data/scenarios/` - JSON scenario files
- `backend/testing/benchmark_data/agents/` - Agent-specific scenarios
- `backend/testing/benchmark_data/workflows/` - Workflow scenarios
- `backend/testing/benchmark_data/templates/` - Evaluation templates

## Future Considerations

### Planned Improvements
1. **Enhanced Schema Validation**: More comprehensive Pydantic models
2. **Better Versioning**: Git-like branching and merging
3. **Template System**: Reusable scenario templates
4. **Performance Optimization**: Caching and indexing improvements
5. **Integration Testing**: End-to-end scenario validation

### Deprecation Path
- Gradual migration from metadata to structured fields
- Removal of legacy metadata patterns
- Consolidation of duplicate functionality
- Standardization of naming conventions
