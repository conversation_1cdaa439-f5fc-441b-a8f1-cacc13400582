# Comprehensive Debugging Guide

## Overview

This guide provides comprehensive debugging procedures for the Goali backend system, including tools, techniques, and troubleshooting workflows.

## Quick Diagnostic Commands

### System Health Check
```bash
# Check overall system health
curl http://localhost:8000/api/health/

# Check database connectivity
docker exec -it backend-web-1 python manage.py check --database default

# Check container status
docker ps | grep backend
```

### Real-Time Monitoring
```bash
# Launch enhanced packet debugger
cd frontend/ai-live-testing-tools
node enhanced-packet-debugger.js

# Monitor backend logs
docker logs -f backend-web-1

# Monitor celery logs
docker logs -f backend-celery-1
```

## Enhanced Packet Debugger

### Features

1. **Real-Time WebSocket Monitoring**
   - Live packet inspection
   - Multi-client simulation
   - Admin socket monitoring
   - System health tracking

2. **Interactive Commands**
   ```
   📋 list - Show all connected clients
   🔍 select <client_id> - Focus on specific client
   📦 packets [n] - Show last n packets
   📤 send <client_id> <json> - Send JSON to client
   🔧 diagnose - Run diagnostics
   💾 save - Save packet history
   ```

3. **Automated Testing**
   - Creates 3 test clients automatically
   - Sends test messages with debugger user IDs
   - Monitors system responses
   - Tracks connection health

### Usage Examples

```bash
# Start debugger
node enhanced-packet-debugger.js

# Send test message
send 1 {"type": "chat_message", "content": {"message": "I'm bored", "user_profile_id": "debugger-user-1"}}

# Check system health
admin {"type": "get_system_health"}

# Save debugging session
save
```

## Debug Information Analysis

### Message Types

1. **MentorService Debug Messages**
   ```json
   {
     "source": "MentorService",
     "level": "info",
     "message": "Message processed by MentorService",
     "details": {
       "message_length": 9,
       "trust_level": 0.5,
       "has_mentor_assessment": true
     }
   }
   ```

2. **ConversationDispatcher Debug Messages**
   ```json
   {
     "source": "ConversationDispatcher",
     "level": "info",
     "message": "Message classified successfully",
     "details": {
       "workflow_type": "wheel_generation",
       "confidence": 0.9,
       "reason": "User expressing boredom implies looking for activities"
     }
   }
   ```

3. **Agent Debug Messages**
   ```json
   {
     "source": "Agent:mentor",
     "level": "info",
     "message": "Agent 'mentor' process completed",
     "details": {
       "output_keys": ["error", "output_data"],
       "output_data_summary": "{'error': '...', 'debug': {...}}"
     }
   }
   ```

### Key Debug Indicators

**Healthy System Indicators**:
- `"database": "healthy"`
- `"redis": "healthy"`
- `"celery_workers": "1 active"`
- Classification confidence > 0.8
- Processing times < 2000ms

**Warning Indicators**:
- Classification confidence < 0.7
- Processing times > 5000ms
- Redis memory > 10M
- Error rate > 5%

**Critical Indicators**:
- `"database": "unhealthy"`
- `"redis": "unhealthy"`
- `"celery_workers": "0 active"`
- Repeated agent validation errors
- WebSocket connection failures

## Critical Fixes Applied

### Workflow Results Not Delivered to Users (FIXED - 2025-06-10)

**Issue**: Users were not receiving workflow results (wheel data) via WebSocket connections, despite workflows completing successfully. The system showed "No WebSocket session found" warnings in Celery logs.

**Root Cause**: In `backend/apps/main/celery_results.py`, the `handle_graph_workflow_result` function was incorrectly extracting workflow results using `result.get('result', {})`, expecting a nested format. However, workflow functions return results directly, causing the `user_ws_session_name` to be lost.

**Symptoms**:
- Workflows complete successfully in Celery logs
- Users receive chat messages but not wheel data
- "No WebSocket session found in result for workflow" warnings
- Missing `user_ws_session_name` in workflow result processing

**Fix Applied**: Updated the result extraction logic to handle both nested and direct result formats:

```python
# FIXED: Handle both nested and direct result formats
# Some workflows return results nested under 'result' key, others return directly
if 'result' in result and isinstance(result['result'], dict):
    # Nested format: {'workflow_id': '...', 'result': {...}}
    workflow_result = result['result']
else:
    # Direct format: {'workflow_id': '...', 'user_ws_session_name': '...', 'output_data': {...}, ...}
    workflow_result = result
```

**Impact**: This fix ensures that all workflow results are properly delivered to users via WebSocket connections, restoring the complete user experience.

**Files Modified**:
- `backend/apps/main/celery_results.py` (lines 186-196)

**Testing**: Verified using the user story simulator in `frontend/ai-live-testing-tools/user-story-simulator.js`

**Verification Commands**:
```bash
# Test workflow result delivery
cd frontend/ai-live-testing-tools
node user-story-simulator.js

# Monitor Celery logs for warnings
docker logs -f backend-celery-1 | grep -E "(No WebSocket session|user_ws_session_name)"

# Should see no "No WebSocket session found" warnings after fix
```

## Troubleshooting Workflows

### 1. Database Issues

**Symptoms**:
- `"database": "unhealthy"` in health check
- Agent validation errors
- Async context errors

**Diagnostic Steps**:
```bash
# Check database connection
docker exec -it backend-web-1 python manage.py check --database default

# Check database logs
docker logs backend-db-1

# Test database query
docker exec -it backend-web-1 python manage.py shell
>>> from django.db import connection
>>> connection.ensure_connection()
```

**Common Solutions**:
- Restart database container
- Check database credentials
- Verify async context decorators
- Review migration status

### 2. WebSocket Connection Issues

**Symptoms**:
- Clients cannot connect
- Messages not being received
- Connection tracking issues

**Diagnostic Steps**:
```bash
# Test WebSocket connection
node enhanced-packet-debugger.js

# Check WebSocket logs
docker logs -f backend-web-1 | grep -i websocket

# Monitor connection count
curl http://localhost:8000/api/admin/connections/
```

**Common Solutions**:
- Restart web container
- Check Redis connectivity
- Verify WebSocket routing
- Review CORS settings

### 3. Agent Validation Errors

**Symptoms**:
- `"Invalid user_profile_id format"` errors
- Agent processing failures
- Workflow routing issues

**Diagnostic Steps**:
```bash
# Check agent validation patterns
grep -r "user_profile_id" backend/apps/main/agents/

# Test with different user ID patterns
send 1 {"type": "chat_message", "content": {"message": "test", "user_profile_id": "test-user-1"}}
```

**Common Solutions**:
- Update validation patterns
- Check ID format consistency
- Review agent initialization
- Verify error handling

### 4. Workflow Routing Issues

**Symptoms**:
- Incorrect workflow classification
- Missing context information
- Action requirement failures

**Diagnostic Steps**:
```bash
# Monitor classification debug messages
# Look for "Message classified successfully" messages
# Check confidence levels and reasoning

# Test different message types
send 1 {"type": "chat_message", "content": {"message": "I need help", "user_profile_id": "debugger-user-1"}}
```

**Common Solutions**:
- Review LLM classification prompts
- Check context extraction logic
- Verify action requirement rules
- Update workflow routing logic

## Performance Debugging

### Timing Analysis

**Normal Processing Times**:
- Message reception: < 50ms
- Classification: 100-500ms
- Context building: 50-200ms
- Agent processing: 1-5 seconds
- Total end-to-end: 2-8 seconds

**Performance Bottlenecks**:
- LLM API calls (1-3 seconds)
- Database queries (50-200ms)
- Context extraction (100-300ms)
- Agent initialization (25-100ms)

### Memory Monitoring

```bash
# Check Redis memory usage
docker exec -it backend-redis-1 redis-cli info memory

# Check container memory
docker stats backend-web-1

# Monitor Python memory usage
docker exec -it backend-web-1 python -c "import psutil; print(f'Memory: {psutil.virtual_memory().percent}%')"
```

## Error Pattern Analysis

### Common Error Patterns

1. **Database Connection Errors**
   ```
   django.db.utils.OperationalError: database is locked
   ```
   **Solution**: Check async context management

2. **Redis Connection Errors**
   ```
   redis.exceptions.ConnectionError: Connection refused
   ```
   **Solution**: Restart Redis container

3. **Agent Validation Errors**
   ```
   Invalid user_profile_id format: debugger-user-1
   ```
   **Solution**: Update validation patterns

4. **WebSocket Errors**
   ```
   WebSocket connection failed
   ```
   **Solution**: Check CORS and routing

### Error Recovery Procedures

1. **Graceful Restart**
   ```bash
   docker-compose restart web
   # Wait 30 seconds for full initialization
   ```

2. **Full System Restart**
   ```bash
   docker-compose down
   docker-compose up -d
   # Wait 2-3 minutes for full startup
   ```

3. **Database Reset** (Development Only)
   ```bash
   docker-compose down
   docker volume rm goali_postgres_data
   docker-compose up -d
   python manage.py migrate
   ```

## Monitoring Best Practices

### Continuous Monitoring

1. **System Health Checks**
   - Monitor every 30 seconds
   - Alert on unhealthy status
   - Track performance trends

2. **Error Rate Monitoring**
   - Track error percentage
   - Alert on > 5% error rate
   - Monitor error patterns

3. **Performance Monitoring**
   - Track processing times
   - Monitor memory usage
   - Alert on performance degradation

### Alerting Thresholds

**Critical Alerts**:
- Database unhealthy
- Redis unhealthy
- Error rate > 10%
- Processing time > 30 seconds

**Warning Alerts**:
- Error rate > 5%
- Processing time > 10 seconds
- Memory usage > 80%
- Redis memory > 50M

## Development Debugging

### Local Development Setup

```bash
# Enable debug mode
export DEBUG_MODE=true

# Start with verbose logging
docker-compose up -d --build

# Monitor all logs
docker-compose logs -f
```

### Testing Procedures

1. **Unit Testing**
   ```bash
   docker exec -it backend-web-1 python manage.py test
   ```

2. **Integration Testing**
   ```bash
   node enhanced-packet-debugger.js
   # Run through user stories manually
   ```

3. **Performance Testing**
   ```bash
   # Load test with multiple clients
   # Monitor system health during load
   ```

## Emergency Procedures

### System Down

1. Check container status
2. Review recent logs
3. Restart affected containers
4. Verify system health
5. Test critical user flows

### Data Corruption

1. Stop all services
2. Backup current state
3. Restore from last known good backup
4. Verify data integrity
5. Resume services

### Security Incident

1. Isolate affected components
2. Review access logs
3. Update security credentials
4. Patch vulnerabilities
5. Monitor for continued threats

---

**Last Updated**: 2025-06-10
**Version**: 2.0.0
**Maintainer**: Backend Team
