# Specialized Agent Components System

## Overview

The Goali benchmarking system now includes specialized agent evaluation components that provide rich, interactive displays tailored to each agent type. These components automatically analyze agent-specific data and present meaningful insights through intuitive visualizations.

## Component Architecture

### Base Component System
All specialized components inherit from `BaseAgentComponent`, which provides:
- Common UI patterns and styling
- Data safety utilities (`safeGet`, `formatValue`)
- State management and event handling
- Expandable/collapsible interface
- Export functionality

### Component Registry
The `ComponentRegistry` manages component lifecycle:
- Dynamic component creation and registration
- Dependency resolution
- Automatic component selection based on agent type
- Error handling and fallback mechanisms

## Specialized Components

### 1. MentorAgentComponent 🧠

**Purpose**: Analyzes Mentor Agent performance focusing on conversational quality, trust building, and communication effectiveness.

**Key Features**:
- **Trust Level Analysis**: Tracks trust progression with visual indicators
- **Communication Tone Assessment**: Evaluates appropriateness for trust phase
- **Response Quality Metrics**: Measures personalization, engagement, and goal alignment
- **Philosophical Framing**: Detects growth mindset and journey metaphors
- **Conversation Flow Analysis**: Assesses response appropriateness and acknowledgment

**Data Structure Expected**:
```javascript
{
  agent_role: 'mentor',
  raw_results: {
    last_output: {
      mentor_output: {
        trust_level: 0.75,
        communication_tone: 'encouraging',
        mentor_response: "Great progress on your goals..."
      },
      user_response: "I want to improve my fitness"
    }
  },
  semantic_score: 0.85
}
```

**Usage**:
```javascript
const mentorComponent = window.componentRegistry.createComponent(
  'MentorAgent',
  container,
  data,
  { attributes: { expanded: 'true' } }
);
```

### 2. EngagementAgentComponent 🎯

**Purpose**: Analyzes user engagement patterns, behavioral consistency, and domain preferences.

**Key Features**:
- **Domain Engagement Analysis**: Tracks completion rates across activity domains
- **Behavioral Pattern Detection**: Identifies recurring patterns and consistency
- **Temporal Analysis**: Optimal activity times and weekly patterns
- **Preference vs. Behavior Consistency**: Detects gaps between stated and actual preferences
- **Trend Analysis**: Improving, stable, and declining engagement areas

**Data Structure Expected**:
```javascript
{
  agent_role: 'engagement',
  raw_results: {
    last_output: {
      engagement_output: {
        pattern_analysis: {
          recurring_patterns: [...],
          pattern_strength: 0.78
        },
        domain_metrics: {
          physical: { completion_rate: 0.85, engagement_score: 0.78 },
          creative: { completion_rate: 0.45, engagement_score: 0.52 }
        }
      }
    }
  }
}
```

### 3. GenericAgentComponent 🤖

**Purpose**: Provides flexible analysis for any agent type when no specialized component exists.

**Key Features**:
- **Automatic Data Structure Analysis**: Discovers and categorizes available data
- **Intelligent Metric Detection**: Identifies key performance indicators
- **Multiple View Modes**: Summary, detailed, and raw data views
- **Tool Call Visualization**: Shows agent tool usage and success rates
- **Adaptive Display**: Adjusts to any data structure automatically

**View Modes**:
- **Summary**: High-level metrics and key insights
- **Detailed**: Comprehensive analysis with tool calls and data structure
- **Raw**: Complete JSON data viewer for debugging

### 4. ResourceAgentComponent 🎒

**Purpose**: Displays resource inventory, capabilities, and environmental context.

**Key Features**:
- **Resource Inventory**: Categorized list of available resources
- **Capabilities Analysis**: Skills and abilities breakdown
- **Limitations Tracking**: Known constraints and restrictions
- **Environment Context**: Privacy levels and location analysis
- **Resource Utilization**: Usage patterns and availability

## Component Selection Logic

The system automatically selects the appropriate component based on the `agent_role` field:

```javascript
function selectAgentComponent(data) {
  const agentRole = data.agent_role?.toLowerCase();
  
  switch (agentRole) {
    case 'mentor':
      return 'MentorAgent';
    case 'engagement':
    case 'engagement_agent':
      return 'EngagementAgent';
    case 'resource':
    case 'resource_agent':
      return 'ResourceAgent';
    default:
      return 'GenericAgent'; // Fallback for unknown types
  }
}
```

## Integration with Benchmark System

### Enhanced Modal Integration

The enhanced agent evaluation modal automatically loads the appropriate component:

```javascript
async function loadAgentComponents(container, data, runId) {
  const componentType = selectAgentComponent(data);
  
  try {
    const component = window.componentRegistry.createComponent(
      componentType,
      container,
      data,
      { attributes: { expanded: 'true' } }
    );
    
    if (!component) {
      throw new Error(`Failed to create ${componentType} component`);
    }
  } catch (error) {
    // Fallback to generic component
    loadGenericAgentComponents(container, data, runId);
  }
}
```

### Benchmark History Integration

Components are automatically loaded when viewing benchmark results in the admin interface. The system:

1. Detects the agent type from benchmark data
2. Loads the appropriate specialized component
3. Falls back to generic component if specialized one fails
4. Provides error handling and user feedback

## Data Safety and Error Handling

All components use robust data access patterns:

```javascript
// Safe data access with fallbacks
const trustLevel = this.safeGet(data, 'raw_results.last_output.mentor_output.trust_level', 0.5);

// Graceful error handling
try {
  const component = this.createSpecializedView(data);
} catch (error) {
  console.error('Component creation failed:', error);
  return this.createFallbackView(data);
}
```

## Styling and Theming

Components use consistent styling with:
- Responsive grid layouts
- Color-coded metrics (excellent/good/fair/poor)
- Interactive elements with hover states
- Mobile-friendly responsive design
- Consistent iconography and typography

## Performance Considerations

- **Lazy Loading**: Components are loaded only when needed
- **Async Operations**: Non-blocking component initialization
- **Memory Management**: Proper cleanup on component destruction
- **Caching**: Reuse of processed data within component lifecycle

## Testing and Validation

The system includes comprehensive tests for:
- Component structure validation
- Data handling capabilities
- Error recovery mechanisms
- Integration points
- Performance characteristics

Run tests with:
```bash
docker exec -it backend-web-1 python /usr/src/app/real_condition_tests/test_modular_web_components.py
```

## Future Extensions

The component system is designed for easy extension:

1. **New Agent Types**: Create new specialized components by extending `BaseAgentComponent`
2. **Enhanced Visualizations**: Add chart libraries and interactive elements
3. **Real-time Updates**: WebSocket integration for live data updates
4. **Export Formats**: Additional export options (PDF, CSV, etc.)
5. **Collaborative Features**: Sharing and annotation capabilities

## Best Practices

### Component Development
- Always extend `BaseAgentComponent`
- Use `safeGet` for data access
- Implement proper error handling
- Register components with the registry
- Follow consistent naming conventions

### Data Structure Design
- Use predictable field names
- Nest related data logically
- Include metadata for context
- Provide fallback values
- Document expected structure

### Performance Optimization
- Minimize DOM manipulation
- Use efficient data processing
- Implement proper cleanup
- Cache expensive calculations
- Optimize for mobile devices

## Troubleshooting

### Common Issues

1. **Component Not Loading**
   - Check agent_role field in data
   - Verify component registration
   - Check browser console for errors

2. **Data Not Displaying**
   - Validate data structure
   - Check for missing required fields
   - Use browser dev tools to inspect data

3. **Styling Issues**
   - Ensure CSS is loaded
   - Check for conflicting styles
   - Verify responsive breakpoints

### Debug Mode

Enable debug logging:
```javascript
window.componentRegistry.setDebugMode(true);
```

This provides detailed logging of component creation, data processing, and error handling.
