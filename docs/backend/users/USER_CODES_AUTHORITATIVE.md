# User Profile Codes - Authoritative Reference

This document defines all valid codes for user profile components. It serves as the **single source of truth** for profile imports and system validation.

**Generated and validated against database schema on 2025-06-29**

---

## Trait Codes (24 available)

### HEXACO Personality Framework

The system uses the HEXACO-60 personality model, providing more precise assessment than traditional Big Five approaches.

#### Honesty-Humility (4 traits)
```
honesty_fairness     # Avoids cheating, treats others equitably, follows rules
honesty_greed_avoidance  # Not motivated by material wealth, uninterested in luxury
honesty_modesty      # Modest about achievements, not pretentious or entitled  
honesty_sincerity    # Genuine in relationships, not manipulative or deceptive
```

#### Emotionality (4 traits)  
```
emotion_anxiety      # Tendency toward worry, stress, and fearfulness
emotion_dependence   # Need for emotional support from others
emotion_fearfulness  # Physical fear responses, caution in dangerous situations
emotion_sentimentality  # Emotional attachment, sentimentality toward others
```

#### Extraversion (4 traits)
```
extra_liveliness     # Energy, enthusiasm, cheerfulness in social situations
extra_self_esteem    # Confidence, self-worth, positive self-regard
extra_sociability    # Enjoys social gatherings, likes interaction with others
extra_social_boldness    # Willingness to approach others, comfort in social leadership
```

#### Agreeableness (4 traits)
```
agree_flexibility    # Willingness to compromise, cooperate in conflicts
agree_forgiveness    # Readiness to forgive wrongdoings, overlook faults
agree_gentleness     # Mild and tolerant in dealing with others
agree_patience       # Remains calm under stress, tolerates inconvenience
```

#### Conscientiousness (4 traits)
```
consc_diligence      # Works hard, perseveres through challenges
consc_organization   # Structured approach, keeps things orderly
consc_perfectionism  # Thorough, careful, strives for accuracy
consc_prudence       # Deliberate, cautious in decision-making
```

#### Openness to Experience (4 traits)
```
open_aesthetic       # Appreciation for beauty in art, nature, design
open_creativity      # Imaginative, innovative, seeks novel solutions
open_inquisitive     # Curious, seeks information, enjoys learning
open_unconventional  # Accepts unusual ideas, people, creative expressions
```

---

## Skill Codes (55 available)

### Communication & Interpersonal (12 skills)
```
communication        # Core verbal and written communication abilities
soft_communication  # Advanced interpersonal communication nuances
soft_empathy        # Understanding and responding to others' emotions
soft_conflict_resolution  # Managing disagreements constructively
soft_leadership     # Guiding and motivating others effectively
soft_presentation   # Public speaking and formal presentation skills
soft_networking     # Building and maintaining professional relationships
soft_negotiation    # Reaching mutually beneficial agreements
soft_active_listening # Focused attention and responsive listening skills
soft_persuasion     # Influencing others through reasoning and appeal
soft_cultural_competence # Understanding and working across cultural differences
soft_team_collaboration # Working effectively within group dynamics
```

### Emotional & Psychological (8 skills)
```
soft_introspection  # Self-awareness and reflection capabilities
soft_emotional_regulation  # Managing one's own emotional responses
soft_stress_management  # Coping with pressure and high-demand situations  
soft_resilience     # Bouncing back from setbacks and challenges
soft_mindfulness    # Present-moment awareness and attention practices
soft_therapeutic    # Supporting others through emotional difficulties
soft_meditation     # Contemplative practices for mental clarity
soft_grief_processing # Navigating loss and life transitions
```

### Intellectual & Analytical (10 skills)
```
soft_philosophical  # Abstract thinking and questioning fundamental concepts
soft_critical_thinking  # Analyzing information objectively and systematically
soft_problem_solving    # Breaking down complex challenges into manageable parts
soft_research       # Finding, evaluating, and synthesizing information
soft_strategic_thinking # Long-term planning and systems perspective
soft_mathematical   # Numerical reasoning and quantitative analysis
soft_scientific     # Hypothesis formation and experimental thinking
soft_pattern_recognition # Identifying trends and relationships in data
soft_systems_thinking # Understanding interconnected processes and feedback loops
soft_decision_making # Weighing options and making informed choices
```

### Technical & Digital (15 skills)
```
tech_ai_concepts    # Understanding AI, machine learning, and automation
tech_coding_python  # Programming in Python language
tech_coding_javascript # Programming in JavaScript language
tech_coding_web     # HTML, CSS, web development fundamentals
tech_database       # SQL and database management concepts
tech_data_analysis  # Working with datasets, visualization, statistics
tech_cybersecurity  # Information security principles and practices
tech_cloud_computing # Understanding distributed systems and cloud services
tech_mobile_development # Creating apps for mobile platforms
tech_automation     # Setting up workflows and automated processes
tech_hardware       # Understanding computer hardware and electronics
tech_software_architecture # Designing scalable software systems
tech_version_control # Git and collaborative code management
tech_testing        # Software quality assurance and debugging
tech_devops         # Deployment, monitoring, and infrastructure management
```

### Creative & Artistic (6 skills)
```
tech_graphic_design # Visual design, layout, color theory, typography
tech_writing        # Content creation, storytelling, editing
creative_music      # Musical composition, performance, production
creative_visual_arts # Drawing, painting, sculpture, visual expression
creative_performing_arts # Acting, dancing, theatrical expression
creative_craftsmanship # Hands-on making, woodworking, metalwork, pottery
```

### Practical & Service (4 skills)
```
soft_service        # Helping others, customer service, care provision
tech_sailing        # Navigation, boat handling, maritime skills
practical_mechanical # Repair, maintenance, understanding how things work
practical_financial # Personal finance, budgeting, investment basics
```

---

## Resource Codes (45 available)

### Technology & Digital (15 resources)
```
tech_laptop         # Personal computer for work and productivity
tech_smartphone     # Mobile device for communication and apps
tech_tablet         # Portable device for reading and light computing
ai_access           # Access to AI tools and language models
connectivity_internet # High-speed internet connection for communication and research
tech_cloud_storage  # Online file storage and backup services
tech_software_licenses # Professional software subscriptions and tools
tech_development_tools # Programming IDEs, version control, development environments
tech_hardware_advanced # Specialized computing equipment (servers, workstations)
tech_smart_home     # IoT devices and home automation systems
tech_camera_video   # Recording equipment for content creation
tech_audio_equipment # Microphones, speakers, audio production gear
tech_gaming_console # Gaming systems for entertainment and social connection
tech_vr_equipment   # Virtual and augmented reality devices
tech_drone          # Unmanned aerial vehicles for photography/surveying
```

### Learning & Information (8 resources)
```
books_reading       # Personal library and reading materials
books_audiobooks    # Audio content and podcasts access
learning_courses    # Online learning platforms and subscriptions
learning_mentorship # Access to experienced guides and teachers
research_databases  # Academic and professional information sources
learning_workshops  # In-person skill development opportunities
learning_conferences # Professional development events
books_reference     # Specialized manuals and technical documentation
```

### Physical & Environmental (8 resources)
```
space_workshop      # Dedicated area for making and building
space_office        # Productive work environment at home
space_studio        # Creative space for artistic work
space_garden        # Outdoor growing and nature space
space_quiet         # Distraction-free zone for focus work
space_social        # Area for gatherings and collaboration
space_exercise      # Physical fitness and movement space
space_storage       # Organized storage for tools and materials
```

### Tools & Equipment (9 resources)
```
workshop_tools      # Hand tools and basic equipment for building
material_wood       # Raw materials for crafting and construction
tools_specialized   # Professional-grade equipment for specific trades
tools_kitchen       # Cooking equipment and appliances
tools_garden        # Gardening implements and supplies
tools_art           # Brushes, canvases, art-making materials
music_instruments   # Musical instruments and music-making equipment
tools_exercise      # Fitness equipment and sports gear
tools_laboratory    # Scientific instruments and testing equipment
```

### Mobility & Access (5 resources)
```
transport_bicycle   # Personal bike for local transportation
transport_specialcycle # E-bike, cargo bike, or specialized cycling equipment
transport_vehicle   # Car or motorcycle for broader mobility
transport_public    # Access to buses, trains, transit systems
transport_boat      # Watercraft for marine transportation and recreation
```

---

## Environment Codes (150 available)

### Indoor Commercial (13 environments)
```
ind_comm_arcade                  # Gaming and entertainment venue
ind_comm_bar_nightclub           # Evening entertainment and social space
ind_comm_bookstore               # Literary and educational material retail
ind_comm_cafe                    # Casual dining and social gathering place
ind_comm_cinema                  # Movie theater and film viewing space
ind_comm_cowork_space            # Shared professional workspace
ind_comm_fitness_center          # Equipment-based exercise and training facility
ind_comm_hotel_lobby             # Hospitality reception and waiting area
ind_comm_mall                    # Large commercial complex with multiple stores
ind_comm_restaurant              # Dining and culinary experience space
ind_comm_retail_store            # Shopping and product selection environment
ind_comm_salon_spa               # Personal care and beauty services space
ind_comm_supermarket             # Large-scale food and goods shopping
```

### Indoor Cultural (16 environments)
```
ind_cultural_artgallery          # Visual arts display and appreciation space
ind_cultural_arthouse            # Independent film and alternative media venue
ind_cultural_artisanworkshop     # Traditional craft and skill-learning space
ind_cultural_cinemacomplex       # Multi-screen movie theater facility
ind_cultural_concerthall         # Musical performance and acoustic space
ind_cultural_culinaryarts        # Cooking and food preparation learning space
ind_cultural_dancestudio         # Movement and choreography practice space
ind_cultural_historicalarchive   # Document preservation and research facility
ind_cultural_library             # Public access to books and information
ind_cultural_literarysalon       # Reading and discussion gathering space
ind_cultural_museum              # Educational exhibition and artifact display
ind_cultural_musicschool         # Musical education and instruction facility
ind_cultural_operahouse          # Classical performance and formal arts venue
ind_cultural_recordingstudio     # Professional audio production facility
ind_cultural_theater             # Live performance and dramatic arts venue
ind_digital_enabled              # Fully connected smart environment with IoT integration
```

### Indoor Educational (16 environments)
```
ind_edu_art_studio               # Creative space for visual arts education
ind_edu_classroom                # Traditional teaching and learning space
ind_edu_computer_lab             # Technology-focused learning environment
ind_edu_conference_room          # Meeting space for group discussions and presentations
ind_edu_cooking_classroom        # Kitchen space for culinary education
ind_edu_gymnasium                # Large space for physical education and sports
ind_edu_lab_science              # Controlled space for scientific experiments
ind_edu_language_lab             # Specialized space for language learning
ind_edu_lecture_hall             # Large presentation space for groups
ind_edu_library                  # Quiet study space with information resources
ind_edu_maker_space              # Hands-on space for building and prototyping
ind_edu_music_room               # Sound-controlled space for musical instruction
ind_edu_quiet_study              # Individual focus areas for concentration
ind_edu_sensory_room             # Specialized space for sensory processing support
ind_edu_stem_lab                 # Science, technology, engineering, math laboratory
ind_edu_theater                  # Performance space for dramatic arts
ind_edu_virtual_reality          # Technology space for immersive learning
```

### Indoor Healthcare (15 environments)
```
ind_healthcare_dental_office     # Oral health care and treatment space
ind_healthcare_emergency_room    # Urgent care and crisis intervention space
ind_healthcare_hospice           # End-of-life care and comfort facility
ind_healthcare_hospital_ward     # General patient care and monitoring area
ind_healthcare_icu               # Critical patient monitoring environment
ind_healthcare_imaging_center    # Diagnostic scanning and X-ray facility
ind_healthcare_laboratory        # Medical testing and analysis space
ind_healthcare_maternity_ward    # Specialized care for childbirth
ind_healthcare_mental_health_facility # Therapeutic counseling and psychiatric support space
ind_healthcare_outpatient_clinic # Non-residential medical care facility
ind_healthcare_pharmacy          # Medication preparation and dispensing area
ind_healthcare_physical_therapy  # Rehabilitation and movement therapy area
ind_healthcare_private_room      # Individual patient care and recovery space
ind_healthcare_rehab_center      # Long-term recovery and therapy facility
ind_healthcare_waiting_room      # Patient reception and preparation area
```

### Indoor Micro & Specialized (3 environments)
```
ind_micro_space                  # Very small, confined areas requiring adaptation
ind_nat_aquarium                 # Large-scale aquatic life display and education
ind_nat_atrium                   # Glass-covered courtyard with natural lighting
ind_nat_botanical_garden         # Indoor plant conservation and display
ind_nat_conservatory             # Climate-controlled greenhouse for exotic plants
ind_nat_indoor_waterfall         # Interior water feature for ambiance and sound
ind_nat_terrarium                # Enclosed ecosystem for small-scale nature observation
```

### Indoor Professional (15 environments)
```
ind_prof_breakroom               # Casual space for relaxation and meals
ind_prof_callcenter              # Space optimized for phone-based work
ind_prof_conference              # Meeting room for group discussions
ind_prof_coworking               # Shared professional workspace
ind_prof_creativestudio          # Space designed for artistic and creative work
ind_prof_cubicle                 # Semi-private workspace within larger area
ind_prof_executivesuite          # High-level meeting and decision-making space
ind_prof_office_private          # Individual workspace with privacy
ind_prof_openplan                # Shared workspace with minimal barriers
ind_prof_quietroom               # Designated silent workspace for deep focus
ind_prof_rooftopspace            # Elevated outdoor professional area
ind_prof_servercenter            # Technical space housing computing equipment
ind_prof_techlab                 # Hands-on space for technical development and testing
ind_prof_trainingroom            # Space designed for learning and skill development
ind_prof_workshop                # Hands-on space for building and making
```

### Indoor Quiet Spaces (1 environment)
```
ind_quiet_space                  # Specifically designed for minimal noise and deep focus
```

### Indoor Residential (16 environments)
```
ind_residential_attic            # Upper storage and potential activity space
ind_residential_basement         # Below-ground multipurpose area
ind_residential_bathroom         # Personal care and hygiene space
ind_residential_bedroom          # Personal sleeping and private space
ind_residential_dining_room      # Formal eating and gathering space
ind_residential_game_room        # Entertainment and recreational space
ind_residential_guest_room       # Visitor accommodation space
ind_residential_hallway          # Connecting passages and corridors
ind_residential_hobby_room       # Dedicated space for personal interests
ind_residential_home_gym         # Personal fitness and exercise area
ind_residential_home_office      # Dedicated work and study area
ind_residential_kitchen          # Food preparation and cooking area
ind_residential_laundry_room     # Clothing care and maintenance
ind_residential_living_room      # Main family gathering space
ind_residential_meditation_space # Peaceful area for reflection and mindfulness
ind_residential_reading_nook     # Quiet corner for focused reading
ind_residential_sunroom          # Glass-enclosed space with natural light
```

### Indoor Transportation (11 environments)
```
ind_trans_airplane               # Aircraft passenger cabin environment
ind_trans_airport                # Air travel facility and terminal
ind_trans_bus                    # Public bus passenger space
ind_trans_bus_station            # Public transportation terminal
ind_trans_cable_car              # Cable-suspended transportation vehicle
ind_trans_car                    # Personal vehicle interior space
ind_trans_cruise_ship            # Large vessel recreational and accommodation space
ind_trans_ferry                  # Water vessel passenger area
ind_trans_rideshare              # Shared vehicle transportation
ind_trans_subway                 # Underground train passenger environment
ind_trans_train                  # Rail passenger car environment
ind_trans_train_station          # Rail travel hub and waiting area
```

### Outdoor Commercial (3 environments)
```
out_comm_food_truck_park         # Mobile dining and street food area
out_comm_market                  # Outdoor vendors and fresh goods shopping
out_comm_shopping_street         # Pedestrian commercial district
```

### Outdoor Cultural (5 environments)
```
out_cultural_amphitheater        # Outdoor performance venue with tiered seating
out_cultural_festival            # Temporary outdoor cultural celebration space
out_cultural_heritage            # Historical and culturally significant locations
out_cultural_openairdance        # Open-air space for dance and movement
out_cultural_sculpture           # Outdoor art display in natural setting
```

### Outdoor Educational (3 environments)
```
out_edu_campus_quad              # Central outdoor gathering space
out_edu_outdoor_classroom        # Natural teaching environment
out_edu_school_yard              # Recreational space for physical development
```

### Outdoor Healthcare (1 environment)
```
out_healthcare_healing_garden    # Therapeutic outdoor space for recovery
```

### Outdoor Natural (19 environments)
```
out_nat_beach                    # Sandy or rocky shoreline area
out_nat_canyon                   # Deep gorge carved by water erosion
out_nat_cliffside                # Elevated rocky terrain with steep drops
out_nat_coral_reef               # Underwater marine ecosystem
out_nat_desert                   # Arid landscape with minimal vegetation
out_nat_farm                     # Agricultural land for crop and livestock
out_nat_forest                   # Dense woodland with tree coverage
out_nat_garden                   # Cultivated outdoor space with plants
out_nat_lake                     # Large body of freshwater
out_nat_meadow                   # Open grassland area
out_nat_mountain                 # Elevated terrain with significant elevation
out_nat_river                    # Flowing water course
out_nat_savanna                  # Grassland with scattered trees
out_nat_tundra                   # Cold climate with minimal tree growth
out_nat_volcanic_area            # Terrain shaped by volcanic activity
out_nat_wetland                  # Marsh or swamp ecosystem
```

### Outdoor Professional (1 environment)
```
out_prof_campusgrounds           # Outdoor professional or educational spaces
```

### Outdoor Residential (4 environments)
```
out_residential_backyard         # Private outdoor space behind home
out_residential_front_yard       # Public-facing outdoor entrance area
out_residential_garden           # Cultivated space for growing plants
out_residential_patio_deck       # Outdoor platform for relaxation and dining
```

### Outdoor Transportation (4 environments)
```
out_trans_bikepath               # Dedicated cycling route
out_trans_ferry_deck             # Open-air portion of water vessel
out_trans_highway_rest           # Roadside stopping and service area
out_trans_sidewalk               # Pedestrian walkway along roads
```

---

## Limitation Codes (75 available)

### Cognitive & Mental Processing (10 limitations)
```
cog_attention                    # Difficulty maintaining focus on tasks
cog_executive                    # Challenges with planning and organization
cog_learning                     # Slower information acquisition and retention
cog_literacy                     # Reading and writing comprehension difficulties
cog_math                         # Numerical processing and calculation challenges
cog_memory                       # Difficulty storing and retrieving information
cog_processing                   # Slower cognitive processing speed
cog_decision_making              # Difficulty making choices under pressure
cog_multitasking                 # Challenges managing multiple simultaneous tasks
cog_abstract_thinking            # Difficulty with conceptual and theoretical ideas
```

### Physical Mobility & Motor (12 limitations)
```
phys_mobility_general            # Overall movement and locomotion challenges
phys_mobility_lower              # Leg, hip, and lower body movement limitations
phys_mobility_upper              # Arm, shoulder, and upper body movement restrictions
phys_balance                     # Difficulty maintaining stability and coordination
phys_dexterity                   # Fine motor control and precise hand movements
phys_strength                    # Reduced physical power and lifting capacity
phys_stamina                     # Limited endurance for sustained activity
phys_chronic_pain                # Ongoing discomfort affecting activity levels
phys_cardiovascular              # Heart and circulation limitations
phys_respiration                 # Breathing and lung function challenges
phys_flexibility                 # Reduced range of motion in joints
phys_reaction_time               # Slower response to stimuli and emergencies
```

### Sensory & Perception (5 limitations)
```
phys_vision                      # Sight impairments affecting daily activities
phys_hearing                     # Auditory processing and sound detection issues
phys_speech                      # Communication and vocal expression difficulties
phys_sensory_processing          # Over- or under-sensitivity to sensory input
phys_vestibular                  # Balance and spatial orientation challenges
```

### Psychological & Emotional (15 limitations)
```
psych_anxiety                    # Excessive worry and fear responses
psych_depression                 # Low mood affecting motivation and energy
psych_social_anxiety             # Fear and discomfort in social situations
psych_confidence                 # Low self-esteem and self-doubt
psych_emotional_regulation       # Difficulty managing emotional responses
psych_stress                     # Heightened response to pressure and demands
psych_trauma                     # Past experiences affecting current functioning
psych_motivation                 # Difficulty initiating and sustaining effort
psych_perfectionism              # Paralysis from fear of making mistakes
psych_rejection_sensitivity      # Heightened fear of criticism or disapproval
psych_imposter_syndrome          # Feeling undeserving of success or recognition
psych_overwhelm                  # Difficulty coping with complex or demanding situations
psych_grief                      # Processing loss affecting daily functioning
psych_addiction                  # Compulsive behaviors affecting life balance
psych_eating_disorders           # Unhealthy relationship with food and body image
```

### Social & Communication (8 limitations)
```
social_communication             # Difficulty expressing thoughts and understanding others
social_conflict                  # Challenges managing disagreements and tension
social_group                     # Discomfort or difficulty in group settings
social_interpretation            # Misunderstanding social cues and nonverbal communication
social_strangers                 # Anxiety or difficulty interacting with new people
social_authority                 # Challenges with hierarchical relationships
social_intimacy                  # Difficulty forming close personal relationships
social_professional              # Challenges in workplace social dynamics
```

### Environmental Sensitivity (10 limitations)
```
env_noise                        # Sensitivity to sound levels and acoustic environments
env_light                        # Sensitivity to brightness and visual stimulation
env_crowds                       # Discomfort in high-density social environments
env_temperature                  # Sensitivity to heat, cold, or temperature changes
env_allergens                    # Reactions to environmental substances
env_air_quality                  # Sensitivity to pollution, dust, or chemical exposure
env_weather                      # Challenges with seasonal or weather changes
env_electromagnetic              # Sensitivity to electronic devices and signals
env_scents                       # Reactions to perfumes, chemicals, or strong odors
env_outdoor                      # Difficulty with natural outdoor environments
```

### Resource & Access (10 limitations)
```
res_financial                    # Limited money for activities, tools, or experiences
res_transportation               # Difficulty accessing different locations
res_equipment                    # Lack of tools or technology needed for activities
res_space                        # Insufficient physical area for desired activities
res_support                      # Limited help from family, friends, or professionals
res_digital                      # Restricted access to internet or digital tools
res_healthcare                   # Limited access to medical or therapeutic services
res_childcare                    # Responsibilities limiting available time and freedom
res_eldercare                    # Caregiving duties affecting personal time
res_housing                      # Living situation constraints on activities
```

### Time & Schedule (5 limitations)
```
time_duration                    # Difficulty with activities requiring sustained time
time_regularity                  # Challenges maintaining consistent schedules
time_morning                     # Reduced energy or availability in early hours
time_evening                     # Reduced energy or availability in late hours
time_transitions                 # Difficulty switching between different activities
```

---

## Usage Guidelines

### Validation Rules
- All codes must exactly match those listed above
- Import systems validate against this authoritative list
- Invalid codes will cause import failures with specific error messages
- Regular database synchronization ensures list accuracy

### Code Structure
- **Trait codes**: Prefix indicates HEXACO domain (honesty_, emotion_, extra_, agree_, consc_, open_)
- **Skill codes**: Prefix indicates category (soft_, tech_, creative_, practical_)
- **Resource codes**: Prefix indicates type (tech_, books_, space_, tools_, transport_, learning_, social_)
- **Environment codes**: Prefix indicates location type (ind_, out_) and category
- **Limitation codes**: Prefix indicates domain (cog_, phys_, psych_, social_, env_, res_, time_)

### JSON Examples

#### Complete User Profile Import
```json
{
  "profile_name": "Detailed User Example",
  "traits": [
    {
      "trait_code": "open_creativity",
      "strength": 85,
      "awareness": 70,
      "notes": "Strong creative drive with good self-awareness"
    },
    {
      "trait_code": "consc_organization",
      "strength": 45,
      "awareness": 80,
      "notes": "Aware of organizational challenges, working to improve"
    }
  ],
  "skills": [
    {
      "skill_code": "tech_coding_python",
      "description": "5 years experience in web development and data analysis",
      "level": 75,
      "user_awareness": 80,
      "user_enjoyment": 90
    },
    {
      "skill_code": "creative_visual_arts",
      "description": "Weekend hobby painter, mostly watercolors",
      "level": 35,
      "user_awareness": 40,
      "user_enjoyment": 85
    }
  ],
  "resources": [
    {
      "generic_resource": "tech_laptop",
      "specific_name": "MacBook Pro M2",
      "location_details": "Home office desk",
      "ownership_details": "Personal ownership, excellent condition"
    },
    {
      "generic_resource": "space_workshop",
      "specific_name": "Garage workshop",
      "location_details": "Attached garage converted to workspace",
      "ownership_details": "Shared with spouse, available weekends"
    }
  ],
  "limitations": [
    {
      "limitation_code": "cog_attention",
      "severity": 60,
      "strategies": ["Pomodoro technique", "Noise-canceling headphones"],
      "notes": "ADHD diagnosis, managed with medication and techniques"
    },
    {
      "limitation_code": "phys_chronic_pain",
      "severity": 40,
      "strategies": ["Regular breaks", "Ergonomic setup"],
      "notes": "Lower back issues from desk work"
    }
  ],
  "preferred_environments": [
    {
      "environment_code": "ind_residential_home_office",
      "preference_strength": 85,
      "notes": "Highly productive in personal workspace"
    },
    {
      "environment_code": "out_nat_forest",
      "preference_strength": 70,
      "notes": "Nature walks help with focus and creativity"
    }
  ]
}
```

### Changes from Previous Version

#### Preserved Original Codes
All original codes have been preserved exactly as specified:
- All 24 trait codes unchanged
- All 11 original skill codes preserved
- All 10 original resource codes preserved (including `music_instruments`)
- All 50 original limitation codes preserved
- All 150 original environment codes preserved

#### Additions Made
- **Skills**: Expanded from 11 to 55 codes (44 new codes added)
- **Resources**: Expanded from 10 to 45 codes (35 new codes added) 
- **Limitations**: Expanded from 50 to 75 codes (25 new codes added)
- **Environments**: Maintained exact 150 codes from original list

#### Key Corrections
- Restored `music_instruments` resource code (was incorrectly changed to `tools_musical`)
- Ensured all original environment codes are preserved in exact original form
- Maintained original naming conventions and categorization structure

---

**Last Updated**: 2025-06-29  
**Schema Version**: 2.1.1  
**Total Codes**: 349 (24 traits + 55 skills + 45 resources + 150 environments + 75 limitations)