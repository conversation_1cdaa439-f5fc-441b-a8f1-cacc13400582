# Valid Reference Codes

This document lists all valid codes that can be used in user profile JSON files.
**Generated from database on 2025-06-28T21:00:40.032026**

## Trait Codes (24 available)

```
agree_flexibility
agree_forgiveness
agree_gentleness
agree_patience
consc_diligence
consc_organization
consc_perfectionism
consc_prudence
emotion_anxiety
emotion_dependence
emotion_fearfulness
emotion_sentimentality
extra_liveliness
extra_self_esteem
extra_sociability
extra_social_boldness
honesty_fairness
honesty_greed_avoidance
honesty_modesty
honesty_sincerity
open_aesthetic
open_creativity
open_inquisitive
open_unconventional
```

## Skill Codes (11 available)

```
communication
soft_communication
soft_empathy
soft_introspection
soft_philosophical
soft_service
tech_ai_concepts
tech_coding_python
tech_graphic_design
tech_sailing
tech_writing
```

## Resource Codes (10 available)

```
ai_access
books_reading
connectivity_internet
material_wood
music_instruments
space_workshop
tech_laptop
transport_bicycle
transport_specialcycle
workshop_tools
```

## Limitation Codes (50 available)

```
cog_attention
cog_executive
cog_learning
cog_literacy
cog_math
cog_memory
cog_processing
env_allergens
env_crowds
env_light
env_noise
env_outdoor
env_temperature
phys_balance
phys_cardiovascular
phys_chronic_pain
phys_dexterity
phys_hearing
phys_mobility_general
phys_mobility_lower
phys_mobility_upper
phys_respiration
phys_speech
phys_stamina
phys_strength
phys_vision
psych_anxiety
psych_confidence
psych_depression
psych_emotional_regulation
psych_motivation
psych_social_anxiety
psych_stress
psych_trauma
res_digital
res_equipment
res_financial
res_space
res_support
res_transportation
social_communication
social_conflict
social_group
social_interpretation
social_strangers
time_duration
time_evening
time_morning
time_regularity
time_transitions
```

## Environment Codes (150 available)

```
ind_comm_arcade
ind_comm_bar_nightclub
ind_comm_bookstore
ind_comm_cafe
ind_comm_cinema
ind_comm_cowork_space
ind_comm_fitness_center
ind_comm_hotel_lobby
ind_comm_mall
ind_comm_restaurant
ind_comm_retail_store
ind_comm_salon_spa
ind_comm_supermarket
ind_cultural_artgallery
ind_cultural_arthouse
ind_cultural_artisanworkshop
ind_cultural_cinemacomplex
ind_cultural_concerthall
ind_cultural_culinaryarts
ind_cultural_dancestudio
ind_cultural_historicalarchive
ind_cultural_library
ind_cultural_literarysalon
ind_cultural_museum
ind_cultural_musicschool
ind_cultural_operahouse
ind_cultural_recordingstudio
ind_cultural_theater
ind_digital_enabled
ind_edu_art_studio
ind_edu_classroom
ind_edu_computer_lab
ind_edu_conference_room
ind_edu_cooking_classroom
ind_edu_gymnasium
ind_edu_lab_science
ind_edu_language_lab
ind_edu_lecture_hall
ind_edu_library
ind_edu_maker_space
ind_edu_music_room
ind_edu_quiet_study
ind_edu_sensory_room
ind_edu_stem_lab
ind_edu_theater
ind_edu_virtual_reality
ind_healthcare_dental_office
ind_healthcare_emergency_room
ind_healthcare_hospice
ind_healthcare_hospital_ward
ind_healthcare_icu
ind_healthcare_imaging_center
ind_healthcare_laboratory
ind_healthcare_maternity_ward
ind_healthcare_mental_health_facility
ind_healthcare_outpatient_clinic
ind_healthcare_pharmacy
ind_healthcare_physical_therapy
ind_healthcare_private_room
ind_healthcare_rehab_center
ind_healthcare_waiting_room
ind_micro_space
ind_nat_aquarium
ind_nat_atrium
ind_nat_botanical_garden
ind_nat_conservatory
ind_nat_indoor_waterfall
ind_nat_terrarium
ind_prof_breakroom
ind_prof_callcenter
ind_prof_conference
ind_prof_coworking
ind_prof_creativestudio
ind_prof_cubicle
ind_prof_executivesuite
ind_prof_office_private
ind_prof_openplan
ind_prof_quietroom
ind_prof_rooftopspace
ind_prof_servercenter
ind_prof_techlab
ind_prof_trainingroom
ind_prof_workshop
ind_quiet_space
ind_residential_attic
ind_residential_basement
ind_residential_bathroom
ind_residential_bedroom
ind_residential_dining_room
ind_residential_game_room
ind_residential_guest_room
ind_residential_hallway
ind_residential_hobby_room
ind_residential_home_gym
ind_residential_home_office
ind_residential_kitchen
ind_residential_laundry_room
ind_residential_living_room
ind_residential_meditation_space
ind_residential_reading_nook
ind_residential_sunroom
ind_trans_airplane
ind_trans_airport
ind_trans_bus
ind_trans_bus_station
ind_trans_cable_car
ind_trans_car
ind_trans_cruise_ship
ind_trans_ferry
ind_trans_rideshare
ind_trans_subway
ind_trans_train
ind_trans_train_station
out_comm_food_truck_park
out_comm_market
out_comm_shopping_street
out_cultural_amphitheater
out_cultural_festival
out_cultural_heritage
out_cultural_openairdance
out_cultural_sculpture
out_edu_campus_quad
out_edu_outdoor_classroom
out_edu_school_yard
out_healthcare_healing_garden
out_nat_beach
out_nat_canyon
out_nat_cliffside
out_nat_coral_reef
out_nat_desert
out_nat_farm
out_nat_forest
out_nat_garden
out_nat_lake
out_nat_meadow
out_nat_mountain
out_nat_river
out_nat_savanna
out_nat_tundra
out_nat_volcanic_area
out_nat_wetland
out_prof_campusgrounds
out_residential_backyard
out_residential_front_yard
out_residential_garden
out_residential_patio_deck
out_trans_bikepath
out_trans_ferry_deck
out_trans_highway_rest
out_trans_sidewalk
```

## Usage Notes

- These codes are the **single source of truth** for profile imports
- All codes are validated against the database during import
- Using invalid codes will result in import failure
- This list is automatically generated from the database

## Examples

### Trait Example
```json
{
  "trait_code": "agree_flexibility",
  "strength": 75,
  "awareness": 80
}
```

### Skill Example
```json
{
  "skill_code": "communication",
  "description": "My experience with this skill",
  "level": 65,
  "user_awareness": 70,
  "user_enjoyment": 85
}
```

### Resource Example
```json
{
  "generic_resource": "ai_access",
  "specific_name": "My laptop",
  "location_details": "Home office",
  "ownership_details": "Personal ownership"
}
```
