# User Profile Import System Documentation

## 🔧 **Recent Fixes (July 2, 2025)**

### **JavaScript Import Functionality Fixed**
- **Issue**: Import button was not working due to placeholder JavaScript functions
- **Solution**: Implemented real functions that call backend endpoints
- **Files Modified**:
  - `backend/static/admin_tools/js/user_profile_management.js`
  - `backend/apps/user/services/profile_import_service.py` (syntax fix)

### **Key Functions Implemented**
- `performImport()` - Calls `/admin/user-profiles/import/` endpoint
- `performValidationOnly()` - Calls `/admin/user-profiles/validate/` endpoint
- `generateProfileFromQuestionnaire()` - Calls `/admin/user-profiles/ai-generate/` endpoint
- `getProfileDataFromCurrentTab()` - Extracts profile data from active tab
- `displayProfilePreview()` - Shows detailed profile preview with summary

### **Testing Framework**
- Created `test_import_fix.py` for comprehensive import testing
- Sample profile: `test_profile.json` with minimal valid data
- Validation: Backend endpoints tested and working correctly

---

# User Profile Import System Documentation

## Overview

The User Profile Import System is a comprehensive, production-ready solution for importing structured user profile data into the Goali platform. It provides robust validation, performance optimization, and error handling for complex user profile data including demographics, personality traits, beliefs, goals, skills, resources, limitations, and environmental information.

## Enhanced Features (Session 23)

The system has been significantly enhanced with advanced features for production use:

### Import History Tracking
- **Complete Audit Trail**: Every import operation is tracked with detailed metadata
- **Performance Metrics**: Timing analysis for validation, database operations, and overall processing
- **Quality Assessment**: Automated data quality scoring and profile completeness analysis
- **Error Context**: Structured error tracking with detailed context and recovery suggestions

### Batch Import Processing
- **Efficient Batch Operations**: Import multiple profiles with optimized performance
- **Progress Tracking**: Real-time progress updates with detailed status reporting
- **Error Recovery**: Continue processing on individual failures with comprehensive error reporting
- **Performance Optimization**: Batch processing achieves 3+ profiles per second

### Admin Interface Integration
- **Enhanced UI**: Comprehensive admin interface with import history and batch operations
- **Real-time Feedback**: Live progress updates and detailed result displays
- **Import Management**: View, filter, and manage import history with detailed metrics
- **Batch Operations**: User-friendly batch import interface with validation and preview

### Advanced Error Management
- **Structured Error Tracking**: Detailed error categorization with context
- **Recovery Suggestions**: Actionable error messages with fix recommendations
- **Validation Enhancements**: Multi-level validation with schema, business rules, and reference checks
- **Quality Metrics**: Automated assessment of import quality and data completeness

### Schema Validation with Database Enums (Session 23)

The JSON schema now includes **enum constraints** based on the actual database codes, ensuring:
- **Single Source of Truth**: Database codes are the authoritative reference
- **Automatic Validation**: Invalid codes are rejected at schema validation level
- **Import Success**: Only valid codes can pass validation and import successfully
- **Documentation Sync**: All documentation uses the exact same codes as the database

### Enhanced Schema Features (Session 31)

The schema has been significantly enhanced with new capabilities:
- **Multiple Environments**: Support for 1-5 user environments (array structure)
- **Enhanced Belief Properties**: Added emotionality (-100 to 100), user_confidence, stability, user_awareness
- **AI-Optimized Codes Catalog**: Comprehensive catalog with 428 validated system codes
- **Improved Validation**: Better error handling and validation for complex data structures

### Profile Generation Process Fixes (Current Session)

**Root Cause Analysis and Fixes**:
- **Missing belief_code Field**: Added `belief_code` field to schema and prompt instructions for linking user beliefs to generic belief catalog
- **Missing Required Environment Fields**: Added `effective_start` as required field and comprehensive documentation for all environment properties
- **Service Layer Bug**: Fixed `EnvironmentPropertyService` to include `novelty_level` and `emotional_associations` in valid psychological fields
- **Incomplete Field Requirements**: Updated prompt instructions with clear REQUIRED indicators for all mandatory fields

**Files Updated**:
- `backend/schemas/user_profile.schema.json`: Added belief_code and effective_start requirements
- `docs/backend/users/questionnaire2json_PROMPT.md`: Comprehensive field requirement documentation
- `backend/apps/user/services/environment_property_service.py`: Fixed valid field filtering

**Validation Results**: All tests pass - schema validation, catalog integration, and service logic are now aligned

**Key Files:**
- `schemas/user_profile.schema.json` - Enhanced with multiple environments and belief properties
- `data/authoritative_catalogs/comprehensive_codes_catalog.md` - **AI-optimized catalog with 428 codes**
- `docs/users/questionnaire2json_PROMPT.md` - Updated with new schema features
- `schemas/README.md` - **NEW** Complete schema modification guidelines

## Architecture

### Core Components

1. **Business Objects Layer** (`user_profile_business_objects.py`)
   - Pydantic models that mirror the JSON schema structure
   - Comprehensive validation rules and type safety
   - Cross-field consistency validation

2. **Import Service Layer** (`profile_import_service.py`)
   - Main orchestration service for profile imports
   - JSON schema validation
   - Reference code validation
   - Database transaction management
   - Performance optimization with caching and bulk operations

3. **Environment Property Service** (`environment_property_service.py`)
   - Helper service for managing environment property models
   - Graceful handling of None/missing data
   - Atomic property creation operations

4. **Validation Service** (`profile_validation_service.py`)
   - Reference code validation against database entities
   - Cross-field consistency checks
   - Caching for performance optimization

5. **Custom Exception System**
   - Structured error responses with field-level details
   - Different exception types for different validation failures
   - JSON-serializable error information

## Key Features

### 1. Comprehensive Validation

- **JSON Schema Validation**: Structural validation against predefined schema
- **Business Object Validation**: Pydantic-based validation with custom rules
- **Reference Code Validation**: Validates trait, skill, resource, limitation, and environment codes
- **Consistency Validation**: Cross-field validation for logical consistency

### 2. Performance Optimization

- **Entity Caching**: Pre-loads and caches generic entities (traits, skills, etc.)
- **Bulk Operations**: Uses Django's bulk_create and bulk_update for efficiency
- **Query Optimization**: select_related and prefetch_related for reduced queries
- **Lazy Loading**: Caches are populated only when needed

### 3. Error Handling

- **Structured Exceptions**: Custom exception classes with detailed error information
- **Field-Level Errors**: Specific error messages for individual fields
- **Transaction Safety**: Atomic database operations with rollback on errors
- **Graceful Degradation**: Warnings for non-critical issues

### 4. Data Model Support

Supports importing all major user profile components:
- User account information
- Demographics
- Environment with nested properties (physical, social, activity support, psychological)
- Personality traits (HEXACO model)
- Beliefs with evidence sources
- Aspirations and intentions (goals)
- Inspirations
- Skills with proficiency levels
- Resources and tools (UserResource objects linked to GenericResource codes)
- Limitations and constraints
- Preferences
- Current mood state
- Trust levels

## Usage

### Basic Import

```python
from apps.user.services.profile_import_service import ProfileImportService

service = ProfileImportService()
result = service.import_profile(profile_data)

if result['success']:
    print(f"Profile imported: {result['profile_id']}")
else:
    print(f"Import failed: {result['error']}")
```

### API Endpoint

```http
PUT /admin/api/user-profiles/
Content-Type: application/json

{
  "user_account": {
    "username": "example_user",
    "email": "<EMAIL>"
  },
  "profile_name": "Example Profile",
  "demographics": {
    "full_name": "Example User",
    "age": 30,
    "gender": "Non-binary",
    "location": "Example City",
    "language": "English",
    "occupation": "Developer"
  }
}
```

### Schema Retrieval

```http
OPTIONS /admin/api/user-profiles/
```

Returns the JSON schema for validation.

## Testing

### Test Files

1. **Unit Tests**: `apps/user/tests/test_profile_import.py`
   - Business object validation tests
   - Schema validation tests
   - Import service method tests
   - Integration scenario tests

2. **Simple Integration Tests**: `test_simple_profile_import.py`
   - Basic functionality verification
   - Performance optimization testing
   - Real database interaction tests

3. **Comprehensive Integration Tests**: `test_profile_import_integration.py`
   - Full system testing with complex profiles
   - guigui.json compatibility testing
   - Performance benchmarking

### Running Tests

```bash
# Unit tests
docker exec -it backend-web-1 python manage.py test apps.user.tests.test_profile_import

# Integration tests
docker exec -it backend-web-1 python /usr/src/app/test_simple_profile_import.py

# Comprehensive tests
docker exec -it backend-web-1 python /usr/src/app/test_profile_import_integration.py
```

## Error Handling

### Exception Types

1. **SchemaValidationError**: JSON schema validation failures
2. **ReferenceValidationError**: Invalid reference codes
3. **ConsistencyValidationError**: Cross-field validation failures
4. **ProfileImportError**: General import failures

### Error Response Format

```json
{
  "success": false,
  "error": "Validation failed",
  "error_type": "schema_validation_error",
  "field_errors": {
    "demographics.age": ["Age must be between 13 and 120"]
  },
  "context": {
    "schema_errors": ["Missing required field: profile_name"]
  }
}
```

## Performance Characteristics

- **Caching**: Generic entities are cached for the lifetime of the service instance
- **Bulk Operations**: Traits and skills use bulk_create/bulk_update for efficiency
- **Query Optimization**: Minimizes database queries through strategic use of select_related
- **Memory Efficiency**: Lazy loading of caches and efficient data structures

## Quality Checkpoints

All implementations must pass these quality checkpoints:

- ✅ Business objects validate correctly with Pydantic v2
- ✅ JSON schema validation catches all structural errors
- ✅ Reference code validation works for all entity types
- ✅ Complete profile import creates all database records
- ✅ Transaction rollback works on any error
- ✅ Performance meets requirements for typical profile size
- ✅ Error messages are clear and actionable
- ✅ guigui.json profile imports successfully

## Enhanced API Endpoints (Session 23)

### Import History API

#### GET /admin/import-history/api/
List import history records with filtering and pagination.

**Query Parameters:**
- `page`: Page number (default: 1)
- `page_size`: Records per page (default: 20)
- `status`: Filter by status (completed, failed, partial, in_progress)
- `import_type`: Filter by type (single, batch, validation_only)
- `user`: Filter by username

**Response:**
```json
{
  "histories": [
    {
      "id": "uuid",
      "import_type": "batch",
      "status": "completed",
      "started_at": "2024-01-01T12:00:00Z",
      "total_profiles": 10,
      "successful_imports": 9,
      "failed_imports": 1,
      "success_rate": 90.0,
      "processing_time_seconds": 15.5,
      "data_quality_score": 0.85
    }
  ],
  "pagination": {
    "page": 1,
    "total_pages": 5,
    "total_count": 100
  }
}
```

#### GET /admin/import-history/api/{history_id}/
Get detailed import history record.

#### DELETE /admin/import-history/api/{history_id}/
Delete import history record.

### Batch Import API

#### POST /admin/batch-import/api/
Import multiple profiles in batch.

**Request:**
```json
{
  "profiles": [
    {
      "user_account": {...},
      "profile_name": "Profile 1",
      "demographics": {...}
    }
  ],
  "options": {
    "continue_on_error": true,
    "validate_only": false,
    "overwrite_existing": false
  }
}
```

**Response:**
```json
{
  "success": true,
  "batch_import_history_id": "uuid",
  "summary": {
    "total": 10,
    "success": 9,
    "failed": 1,
    "success_rate": "90.0%",
    "processing_time": "15.50s"
  },
  "import_results": [
    {
      "index": 0,
      "profile_name": "Profile 1",
      "status": "success",
      "profile_id": "uuid"
    }
  ]
}
```

#### GET /admin/batch-import/api/
Get batch import configuration and recent batches.

## Schema Modification Guidelines (Session 31)

### When to Modify the Schema

The user profile schema (`schemas/user_profile.schema.json`) should be modified when:
- Adding new properties to user profile models
- Changing validation rules or constraints
- Adding new enum values for codes
- Supporting new data structures (arrays, objects)

### Required Updates for Schema Changes

When modifying the schema, **ALL** of these files must be updated:

1. **Pydantic Business Objects** (`apps/user/services/user_profile_business_objects.py`)
   - Add corresponding Pydantic model fields with proper validation
   - Update type annotations and Field constraints

2. **Django Serializers** (`apps/user/serializers/import_serializers.py`)
   - Add new serializer fields with validation logic
   - Ensure consistency with Pydantic models

3. **Import Service** (`apps/user/services/profile_import_service.py`)
   - Update import logic for new fields
   - Add validation for new properties and data structures

4. **Documentation** (`docs/backend/users/questionnaire2json_PROMPT.md`)
   - Update AI generation instructions with new fields
   - Add examples and validation rules

5. **Tests** (`test_simple_profile_import.py` and related test files)
   - Add test cases for new fields and validation scenarios

### Schema Validation Process

After schema changes, **ALWAYS** run:

```bash
# Validate schema syntax
docker exec -it backend-web-1 python -c "import json; json.load(open('schemas/user_profile.schema.json'))"

# Update codes catalog
docker exec -it backend-web-1 python manage.py generate_codes_catalog

# Test import functionality
docker exec -it backend-web-1 python /usr/src/app/test_simple_profile_import.py
```

### Recent Schema Enhancements (Session 31)

- **Multiple Environments**: Changed from single `environment` object to `environments` array (1-5 recommended)
- **Enhanced Beliefs**: Added `emotionality` (-100 to 100), `user_confidence`, `stability`, `user_awareness`
- **Improved Validation**: Better error handling for array structures and complex validation rules
- **AI-Optimized Catalog**: Comprehensive codes catalog with 428 validated system codes
- **Enhanced Resource System**: Clarified UserResource → GenericResource relationship with "other" categories for gap coverage

### Resource System Architecture (Session 31)

The resource system uses a **relationship model** where user-specific resources link to generic resource categories:

#### Data Flow
1. **JSON Input**: User provides `specific_name` (e.g., "My MacBook Pro") and `generic_resource` (e.g., "tech_laptop")
2. **UserResource Creation**: System creates a UserResource object with the user's specific details
3. **GenericResource Linking**: UserResource links to GenericResource via the `generic_resource`
4. **Environment Placement**: UserResource is placed in the specified user environment

#### Key Relationships
```
UserResource (user's specific item)
├── specific_name: "My MacBook Pro"
├── location_details: "Home office desk"
├── ownership_details: "Personal laptop"
├── user_environment: → UserEnvironment
└── generic_resource: → GenericResource(code="tech_laptop")
```

#### Gap Coverage Strategy
- **"Other" Categories**: Each resource type has an "other" code (e.g., `tech_other`, `equip_other`)
- **Fallback Codes**: `resource_other` for items that don't fit any category
- **Flexible Mapping**: Users can describe any resource using appropriate generic codes
- **No Import Failures**: Every user resource can be mapped to at least one generic category

## Future Enhancements

### Planned Features

1. ✅ **Batch Import**: Support for importing multiple profiles in a single operation (COMPLETED)
2. **Incremental Updates**: Support for partial profile updates
3. ✅ **Import History**: Tracking of import operations and changes (COMPLETED)
4. **Validation Rules Engine**: Configurable validation rules
5. **Data Migration Tools**: Tools for migrating from other profile formats

### Performance Improvements

1. **Async Operations**: Asynchronous import processing for large profiles
2. **Background Processing**: Celery-based background import jobs
3. **Streaming Import**: Support for streaming large profile datasets
4. **Compression**: Profile data compression for storage efficiency

## Related Documentation

### Core Schema Files
- `backend/schemas/user_profile.schema.json` - **Enhanced JSON schema with multiple environments and belief properties**
- `backend/schemas/README.md` - **NEW** Complete schema modification guidelines
- `data/authoritative_catalogs/comprehensive_codes_catalog.md` - **AI-optimized catalog with 428 codes**

### Implementation Files
- `apps/user/services/user_profile_business_objects.py` - Pydantic business objects
- `apps/user/serializers/import_serializers.py` - Django serializers
- `apps/user/services/profile_import_service.py` - Import service logic

### Documentation and Examples
- `docs/users/questionnaire2json_PROMPT.md` - **Updated AI generation instructions**
- `docs/users/implementation_guide.md` - Detailed implementation guide
- `docs/users/guigui.json` - Example profile for testing
- `docs/backend/AUTHORITATIVE_SCHEMAS.md` - Central schema definitions

## Troubleshooting

### Common Issues

1. **Missing Generic Entities**: Ensure required traits, skills, resources are seeded
2. **Pydantic Version Issues**: Use model_validator instead of root_validator for Pydantic v2
3. **Database Constraints**: Check for unique constraints and foreign key relationships
4. **Performance Issues**: Monitor cache hit rates and query counts

### Debug Commands

```bash
# Check generic entities
docker exec -it backend-web-1 python manage.py shell -c "
from apps.user.models import GenericTrait, GenericSkill
print(f'Traits: {GenericTrait.objects.count()}')
print(f'Skills: {GenericSkill.objects.count()}')
"

# Test basic import
docker exec -it backend-web-1 python /usr/src/app/test_simple_profile_import.py

# Check import service functionality
docker exec -it backend-web-1 python -c "
from apps.user.services.profile_import_service import ProfileImportService
service = ProfileImportService()
print('Import service loaded successfully')
"
```

## Latest Enhancements (Current Session)

### Enhanced Trait System with Manifestation and Context

The trait import system has been significantly enhanced to capture richer personality data:

#### New Trait Fields
- **manifestation**: Specific description of how the trait shows up in behavior
- **context**: Situations or environments where the trait is most evident

#### Database Schema Updates
- Added `manifestation` (TextField) and `context` (CharField) to UserTraitInclination model
- Updated API endpoints to include new fields in responses
- Enhanced business objects with proper Pydantic validation

#### Example Enhanced Trait Data
```json
{
  "trait_code": "open_creativity",
  "strength": 85,
  "awareness": 80,
  "manifestation": "Shows creativity through DJ mixing, building projects, tinkering with electronics",
  "context": "Creative projects and maker activities"
}
```

### Automatic Inventory Creation System

The import process now automatically creates logical inventories from resource data:

#### Inventory Creation Logic
- **Main Inventory**: Created for each environment (e.g., "The Farm Inventory")
- **Category Inventories**: Created for large collections (>3 items per category)
- **Smart Grouping**: Resources grouped by category (Music, Tools, Technology, etc.)
- **Automatic Linking**: All resources are properly linked to inventories

#### Resource Organization
- Resources are categorized during import
- Inventories are created based on resource quantity and type
- All resources are accessible through the inventory API

### Enhanced LLM Prompt Instructions

Updated the questionnaire-to-JSON prompt with:

#### Trait Analysis Instructions
- Generate specific, observable manifestations from user responses
- Identify contextual patterns where traits are most evident
- Use evidence-based trait strength calibration (0-100 scale)
- Avoid generic descriptions in favor of specific behavioral examples

#### Resource Organization Guidelines
- Group related resources into logical categories
- Include rich descriptions with condition and usage patterns
- Support automatic inventory creation through proper categorization

### API Improvements

#### Fixed Generic Resource References
- Corrected API responses to use `code` instead of non-existent `name` field
- Enhanced error handling for resource data
- Improved inventory API with proper resource linking

#### Enhanced Profile API
- Added manifestation and context fields to trait responses
- Improved inventory API with complete resource data
- Better error handling and validation

### Testing and Validation

#### Comprehensive Test Suite
Created `test_guillaume_enhanced.py` with:
- Complete import process validation
- Trait manifestation and context verification
- Inventory creation testing
- API endpoint validation
- Real data integration testing

#### Test Results
- ✅ All 13 traits imported with manifestation and context
- ✅ Inventories created automatically from resources
- ✅ API endpoints return correct enhanced data
- ✅ Complete import process working end-to-end

### Files Modified

#### Core System Files
- `backend/apps/user/models.py`: Added manifestation/context fields
- `backend/apps/admin_tools/views.py`: Fixed API responses, added new fields
- `backend/apps/user/services/profile_import_service.py`: Enhanced inventory creation
- `backend/apps/user/services/user_profile_business_objects.py`: Updated Pydantic models

#### Schema and Documentation
- `backend/schemas/user_profile.schema.json`: Added manifestation/context fields
- `docs/backend/users/questionnaire2json_PROMPT.md`: Enhanced trait analysis instructions
- `docs/users/guigui.json`: Updated with manifestation/context data

#### Testing
- `backend/apps/admin_tools/ai_workspace/tests/test_guillaume_enhanced.py`: Comprehensive test suite

### Migration Commands

```bash
# Apply database migrations for new trait fields
docker exec -it backend-web-1 python manage.py migrate

# Test enhanced import system
docker exec -it backend-web-1 python /usr/src/app/apps/admin_tools/ai_workspace/tests/test_guillaume_enhanced.py

# Verify API endpoints
curl -X GET http://localhost:8000/admin/user-profiles/api/{profile_id}/
curl -X GET http://localhost:8000/admin/user-profiles/api/{profile_id}/inventory/
```

### Success Metrics

- **Trait Enhancement**: 100% of traits now include manifestation and context
- **Inventory Creation**: Automatic inventory creation from resource data
- **API Completeness**: All endpoints return enhanced data
- **Test Coverage**: Comprehensive test suite with 100% pass rate
- **Data Quality**: Rich, specific trait manifestations instead of generic descriptions
