# Activity Domain Management - Authoritative Guide

## Overview

This document defines the authoritative approach to activity domain management in Goali. It establishes the GenericDomain model as the single source of truth and provides comprehensive guidelines for domain validation, usage, and architectural patterns.

## Architecture Principles

### 1. Single Source of Truth
- **GenericDomain model** is the ONLY authoritative source for domain definitions
- All domain references MUST validate against existing GenericDomain records
- No hardcoded domain enums or mappings outside the model

### 2. Hierarchical Domain Structure
```
Primary Category (10 categories)
├── Specific Domains (70+ domains)
└── Entity Relationships (with strength indicators)
```

### 3. Relationship-Based Architecture
- Activities connect to domains via `EntityDomainRelationship`
- Relationship strength: PRIMARY (100), SIGNIFICANT (70), MODERATE (30), MINIMAL (10)
- Multiple domains per activity with different strengths

## Domain Categories (Primary Categories)

### 1. PHYSICAL
**Code:** `physical`
**Description:** All physical activities that engage the body and promote health and fitness.

**Specific Domains:**
- `phys_cardio` - Cardiovascular Exercise
- `phys_strength` - Strength Training  
- `phys_chill` - Physical but chill (like walking)
- `phys_flexibility` - Flexibility & Mobility
- `phys_sports` - Recreational Sports
- `phys_outdoor` - Outdoor Activities
- `phys_dance` - Dance & Movement
- `phys_martial` - Martial Arts
- `phys_balance` - Balance & Coordination

### 2. SOCIAL
**Code:** `social`
**Description:** All social activities that engage users with others.

**Specific Domains:**
- `soc_connecting` - Social Connection
- `soc_group` - Group Dynamics
- `soc_comm` - Communication Skills
- `soc_empathy` - Empathy Building
- `soc_network` - Networking
- `soc_romance` - Romantic Relationship
- `soc_family` - Family Bonding
- `soc_leadership` - Leadership
- `soc_conflict` - Conflict Resolution

### 3. CREATIVE
**Code:** `creative`
**Description:** All creative activities.

**Specific Domains:**
- `creative_visual` - Visual Arts
- `creative_observation` - Creative observation
- `creative_auditory` - Creative auditory
- `creative_music` - Music Creation
- `creative_writing` - Creative Writing
- `creative_design` - Design Thinking
- `creative_culinary` - Culinary Arts
- `creative_perform` - Performance Arts
- `creative_craft` - Crafts & Making
- `creative_improv` - Improvisation

### 4. INTELLECTUAL
**Code:** `intellectual`
**Description:** All intellectual activities.

**Specific Domains:**
- `intel_learn` - Learning & Study
- `intel_problem` - Problem Solving
- `intel_audio` - Intellectual audio
- `intel_strategic` - Strategic Thinking
- `intel_curiosity` - Intellectual Curiosity
- `intel_language` - Language & Linguistics
- `intel_debate` - Critical Discourse
- `intel_science` - Scientific Inquiry
- `intel_tech` - Technology & Digital

### 5. REFLECTIVE
**Code:** `reflective`
**Description:** All reflective activities.

**Specific Domains:**
- `refl_meditate` - Meditation
- `refl_journal` - Journaling & Self-Reflection
- `refl_mindful` - Mindfulness Practice
- `refl_values` - Values Clarification
- `refl_persp` - Perspective Taking
- `refl_philos` - Philosophical Contemplation
- `refl_grat` - Gratitude Practice
- `refl_comfort` - Comfort Zone Reflection
- `refl_micro` - Micro-Wellness Activities

### 6. EMOTIONAL
**Code:** `emotional`
**Description:** All activities that engage users with their emotions.

**Specific Domains:**
- `emot_aware` - Emotional Awareness
- `emot_regulate` - Emotion Regulation
- `emot_express` - Emotional Expression
- `emot_compass` - Self-Compassion
- `emot_joy` - Joy & Pleasure
- `emot_stress` - Stress Management
- `emot_forgive` - Forgiveness & Letting Go
- `emot_comfort` - Comfort & Nurturing

### 7. SPIRITUAL_EXISTENTIAL
**Code:** `spiritual_existential`
**Description:** All spiritual activities.

**Specific Domains:**
- `spirit_purpose` - Purpose & Meaning
- `spirit_connect` - Spiritual Connection
- `spirit_ritual` - Ritual & Practice
- `spirit_nature` - Nature Connection
- `spirit_transced` - Transcendent Experience
- `spirit_death` - Mortality Contemplation
- `spirit_commun` - Spiritual Community
- `spirit_wisdom` - Wisdom Traditions

### 8. EXPLORATORY_ADVENTUROUS
**Code:** `exploratory_adventurous`
**Description:** All exploratory and adventurous activities.

**Specific Domains:**
- `explor_travel` - Travel & Discovery
- `explor_risk` - Controlled Risk-Taking
- `explor_sensory` - Sensory Exploration
- `explor_cultural` - Cultural Exploration
- `explor_novel` - Novelty Seeking
- `explor_adren` - Adrenaline Activities
- `explor_improv` - Spontaneity & Improvisation
- `explor_unknown` - Embracing Uncertainty
- `explor_digital` - Digital-Physical Hybrid

### 9. PRODUCTIVE_PRACTICAL
**Code:** `productive_practical`
**Description:** All productive and practical activities.

**Specific Domains:**
- `prod_organize` - Organization & Planning
- `prod_habit` - Habit Formation
- `prod_time` - Time Management
- `prod_skill` - Practical Skill Development
- `prod_financial` - Financial Management
- `prod_career` - Career Development
- `prod_health` - Health & Wellness Systems
- `prod_home` - Home Management
- `prod_transition` - Transition Moments

### 10. LEISURE_RECREATIONAL
**Code:** `leisure_recreational`
**Description:** All recreational and leisure activities.

**Specific Domains:**
- `leisure_relax` - Relaxation
- `leisure_play` - Play & Games
- `leisure_entertain` - Entertainment Consumption
- `leisure_collect` - Collection & Curation
- `leisure_nature` - Recreational Nature Activities
- `leisure_social` - Social Recreation
- `leisure_hobby` - Hobby Participation
- `leisure_festive` - Celebration & Festivity

## Domain Validation Rules

### 1. Mandatory Validation
- ALL domain references MUST exist in GenericDomain table
- Seeding processes MUST validate domain codes before creation
- Invalid domains MUST cause seeding to fail with clear error messages

### 2. Domain Code Format
- Primary categories: Use enum values (`physical`, `social`, etc.)
- Specific domains: Use underscore format (`phys_cardio`, `soc_connecting`)
- No spaces, special characters, or mixed case

### 3. Relationship Strength Guidelines
- **PRIMARY (100)**: Main domain that best describes the activity
- **SIGNIFICANT (70)**: Important secondary aspect
- **MODERATE (30)**: Notable but not central aspect  
- **MINIMAL (10)**: Tangential connection

## Implementation Guidelines

### 1. Seeding Data Requirements
```python
# CORRECT format for activity seeding
{
    'primary_domain': 'refl_mindful',  # Must exist in GenericDomain
    'secondary_domains': [
        {'domain': 'emot_stress', 'strength': 70},  # Must exist
        {'domain': 'phys_chill', 'strength': 30},   # Must exist
    ]
}
```

### 2. Domain Extraction Pattern
```python
def get_primary_domain(activity):
    """Get primary domain from activity relationships."""
    relationship = activity.domain_relationships.filter(
        strength=EntityDomainRelationship.RelationshipStrength.PRIMARY
    ).first()
    return relationship.domain if relationship else None
```

### 3. Validation Service Pattern
```python
def validate_domain_code(domain_code: str) -> bool:
    """Validate domain code exists in GenericDomain model."""
    return GenericDomain.objects.filter(code=domain_code).exists()
```

## Migration Strategy

### Phase 1: Remove Pydantic ActivityDomain Enum
- Replace enum with dynamic domain loading from GenericDomain
- Update all references to use string domain codes
- Remove hardcoded domain mappings

### Phase 2: Fix Seeding Data
- Validate all domain codes in seed_db_70_activities.py
- Correct invalid domain assignments
- Add validation to seeding process

### Phase 3: Update Services
- Modify DomainManagementService to use GenericDomain as source
- Remove hardcoded mappings from ActivityConverter
- Implement proper domain validation throughout pipeline

## Best Practices

1. **Always validate domain codes** before using them
2. **Use specific domains** rather than primary categories when possible
3. **Assign appropriate relationship strengths** based on activity content
4. **Document domain choices** in activity descriptions
5. **Test domain relationships** in development environment

## Common Mistakes to Avoid

1. ❌ Using non-existent domain codes
2. ❌ Hardcoding domain mappings outside the model
3. ❌ Skipping domain validation in seeding
4. ❌ Using inconsistent domain naming patterns
5. ❌ Assigning inappropriate relationship strengths

## Troubleshooting

### Domain Not Found Errors
1. Check if domain code exists in GenericDomain table
2. Verify correct spelling and format
3. Use `python manage.py shell` to query available domains

### Invalid Relationship Errors
1. Ensure EntityDomainRelationship is properly created
2. Check relationship strength values (10, 30, 70, 100)
3. Verify content_type and object_id are correct

### Seeding Failures
1. Run domain validation before seeding activities
2. Check for typos in domain codes
3. Ensure all referenced domains exist in database
