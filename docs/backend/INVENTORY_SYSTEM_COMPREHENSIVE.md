# 📦 Goali Inventory System - Comprehensive Documentation

## 🎯 Overview

The Goali Inventory System is a comprehensive resource management solution that tracks what resources are available to users, where they are located, their condition, and how they can be used in activity planning. This system bridges the gap between generic resource catalogs and user-specific resource availability.

## 🏗️ Architecture

### Core Models

#### 1. UserResource (Enhanced)
The foundation of the inventory system, representing a specific resource a user has access to.

**Key Fields:**
- `specific_name`: User's personal name for the resource
- `generic_resource`: Link to GenericResource catalog
- `location_details`: Specific location within environment
- `ownership_details`: How the user accesses this resource
- `availability`: Boolean - currently available for use
- `condition`: Enum (Broken=1, Poor=2, Fair=3, Good=4, Excellent=5)
- `transportability`: Enum (Fixed=1, Heavy=2, Portable=3, Pocket=4)
- `notes`: Additional information

**New Properties:**
- `is_mobile`: Returns True if transportability >= Portable
- `is_usable`: Returns True if available and condition >= Fair

#### 2. Inventory (Completely Refactored)
Represents collections of resources available to a user in specific contexts.

**Key Fields:**
- `user_profile`: Owner of the inventory (ForeignKey)
- `user_environment`: Environment (nullable for mobile inventories)
- `resources`: Many-to-many relationship with UserResource
- `name`: Descriptive name for the inventory
- `valid_until`: Optional expiry date for temporary inventories
- `notes`: Additional remarks
- `created_at`/`updated_at`: Timestamps

**Properties:**
- `is_mobile`: True if not tied to a specific environment
- `is_temporary`: True if has expiration date
- `is_expired`: True if past expiration date

**Methods:**
- `get_available_resources()`: Only currently available resources
- `get_portable_resources()`: Resources that can be transported
- `get_resources_by_condition(min_condition)`: Resources meeting condition requirements

#### 3. UserProfile (Enhanced)
Added inventory management helper methods.

**New Methods:**
- `get_mobile_inventory()`: Get user's mobile resource collection
- `get_environment_inventory(environment)`: Get inventory for specific environment
- `get_or_create_mobile_inventory()`: Ensure mobile inventory exists
- `get_all_available_resources()`: All available resources across inventories

## 🔄 Business Logic

### Inventory Types

#### Mobile Inventories
- `user_environment = None`
- Contains resources the user can take anywhere
- Typically includes portable and pocket-sized items
- Used for travel planning and activity preparation

#### Environment-Specific Inventories
- Tied to a specific UserEnvironment
- Contains all resources available in that location
- Includes fixed, heavy, and portable resources
- Used for location-based activity planning

#### Temporary Inventories
- Have `valid_until` date set
- Automatically expire after the date
- Used for borrowed resources, rentals, or time-limited access

### Resource Classification

#### By Condition
1. **Broken (1)**: Not functional, needs repair
2. **Poor (2)**: Barely functional, unreliable
3. **Fair (3)**: Functional but showing wear
4. **Good (4)**: Reliable, normal wear
5. **Excellent (5)**: Like new, perfect condition

#### By Transportability
1. **Fixed (1)**: Cannot be moved (built-in appliances, furniture)
2. **Heavy (2)**: Requires effort/tools to move (workshop equipment)
3. **Portable (3)**: Can be carried by hand (laptop, books)
4. **Pocket (4)**: Fits in pocket (phone, keys, wallet)

## 🔧 Implementation Details

### Database Migration
The system includes a comprehensive migration (`0011_enhance_inventory_system.py`) that:
- Adds new fields to UserResource with proper defaults
- Refactors Inventory model completely
- Preserves existing data through data migration
- Updates model metadata and relationships

### Business Objects Integration
Enhanced `ResourceBO` in `user_profile_business_objects.py`:
- Added `transportability` field with validation
- Enhanced `condition` field with enum validation
- Flexible validation that accepts various input formats
- Automatic conversion from text descriptions to enum values

### Schema Updates
Updated `user_profile.schema.json`:
- Added `transportability` field with enum constraints
- Enhanced `condition` field with strict enum validation
- Updated field descriptions for clarity
- Maintained backward compatibility

## 📋 Usage Examples

### Creating Inventories

```python
# Get or create mobile inventory
mobile_inventory = user_profile.get_or_create_mobile_inventory()

# Create environment-specific inventory
home_inventory = Inventory.objects.create(
    user_profile=user_profile,
    user_environment=home_environment,
    name="Home Workshop Inventory",
    notes="All tools and equipment at home"
)

# Add resources to inventory
portable_resources = UserResource.objects.filter(
    user_environment__user_profile=user_profile,
    transportability__gte=UserResource.Transportability.PORTABLE,
    availability=True
)
mobile_inventory.resources.add(*portable_resources)
```

### Querying Resources

```python
# Find what user can take on a trip
mobile_inventory = user_profile.get_mobile_inventory()
travel_resources = mobile_inventory.get_portable_resources().filter(
    condition__gte=UserResource.Condition.FAIR
)

# Find resources needing maintenance
broken_resources = UserResource.objects.filter(
    user_environment__user_profile=user_profile,
    condition__lte=UserResource.Condition.POOR
)

# Find resources available at specific location
home_inventory = user_profile.get_environment_inventory(home_env)
available_at_home = home_inventory.get_available_resources()
```

### Business Scenarios

#### Travel Planning
```python
def get_travel_kit(user_profile):
    mobile_inventory = user_profile.get_mobile_inventory()
    if not mobile_inventory:
        return []
    
    return mobile_inventory.get_portable_resources().filter(
        availability=True,
        condition__gte=UserResource.Condition.FAIR
    )
```

#### Activity Resource Matching
```python
def find_suitable_resources(user_profile, required_condition=UserResource.Condition.GOOD):
    all_available = user_profile.get_all_available_resources()
    return all_available.filter(
        condition__gte=required_condition,
        availability=True
    )
```

## 🧪 Testing

### Test Files
- `test_inventory_direct.py`: Direct model functionality testing
- `test_inventory_comprehensive.py`: Full system integration testing
- `test_inventory_final.py`: Production readiness validation

### Test Coverage
- ✅ Model creation and relationships
- ✅ Business logic methods
- ✅ Inventory queries and filtering
- ✅ Resource classification
- ✅ Real-world business scenarios
- ✅ Migration data preservation
- ✅ Schema validation

## 🔮 Future Enhancements

### Planned Features
1. **Resource Sharing**: Allow users to share resources with others
2. **Maintenance Tracking**: Schedule and track resource maintenance
3. **Usage Analytics**: Track which resources are used most frequently
4. **Smart Recommendations**: Suggest resources based on activity patterns
5. **Inventory Optimization**: Recommend resource additions/removals

### Integration Points
1. **Wheel Generation**: Use inventory data for activity feasibility
2. **Activity Recommendations**: Filter by available resources
3. **Context Matching**: Match activities to resource availability
4. **User Onboarding**: Guide users through inventory setup

## 📚 Related Documentation
- `docs/backend/users/USER_PROFILE_IMPORT_SYSTEM.md`: Profile import with resources
- `docs/backend/users/questionnaire2json_PROMPT.md`: Resource data collection
- `backend/schemas/user_profile.schema.json`: Resource schema definition
- `backend/apps/user/models.py`: Model implementations
- `backend/apps/user/services/user_profile_business_objects.py`: Business logic

## 🎯 Key Benefits

1. **Comprehensive Resource Tracking**: Know exactly what resources are available
2. **Context-Aware Planning**: Match activities to resource availability
3. **Mobile Resource Management**: Support for travel and location changes
4. **Condition Monitoring**: Track resource health and maintenance needs
5. **Flexible Organization**: Support multiple inventory types and contexts
6. **Production Ready**: Fully tested and migration-safe implementation
