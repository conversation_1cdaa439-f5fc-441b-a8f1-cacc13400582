# WebSocket Manager Architecture

## Overview

The WebSocket Manager is a comprehensive, scalable solution for managing thousands of concurrent WebSocket connections in the Goali application. It addresses connection leaks, session management, message routing, and provides robust monitoring capabilities.

## Architecture Components

### 1. WebSocket Manager (`websocket_manager.py`)

**Core Features:**
- **Singleton Pattern**: Global connection state management
- **Connection Registry**: Centralized tracking of all WebSocket connections
- **Automatic Cleanup**: Background tasks for removing stale connections
- **Heartbeat Monitoring**: Health checks for active connections
- **Message Routing**: Efficient message delivery to users and sessions
- **Metrics Collection**: Real-time monitoring and observability
- **Event System**: Callbacks for connection lifecycle events

**Key Classes:**
- `ConnectionState`: Enum for connection lifecycle states
- `ConnectionInfo`: Dataclass containing comprehensive connection metadata
- `WebSocketManager`: Main singleton class managing all connections

### 2. WebSocket Integration (`websocket_integration.py`)

**Purpose:** Clean integration layer between Django Channels consumers and the WebSocket Manager.

**Key Methods:**
- `on_connect()`: Handle new WebSocket connections
- `on_authenticate()`: Handle user authentication
- `on_disconnect()`: Handle connection cleanup
- `on_message_received()`: Track message activity
- `send_message_to_user()`: Route messages to specific users
- `broadcast_message()`: Send messages to all connections

### 3. Updated Consumer (`consumers.py`)

**Changes Made:**
- Integrated WebSocket Manager for connection lifecycle
- Maintained backward compatibility with existing monitoring systems
- Improved error handling and graceful degradation
- Enhanced session management and user authentication

## Scalability Features

### Connection Management
- **Connection Limits**: Configurable per-user and global connection limits
- **Automatic Cleanup**: Background tasks remove stale connections every 60 seconds
- **Weak References**: Prevents memory leaks from circular references
- **Thread Safety**: Concurrent access protection with proper locking

### Message Routing
- **Efficient Delivery**: Direct routing to specific sessions or users
- **Fallback Mechanisms**: Graceful handling of failed message delivery
- **Broadcast Capabilities**: Efficient message distribution to multiple connections
- **Message Queuing**: Handles high-volume message scenarios

### Monitoring & Observability
- **Real-time Metrics**: Connection counts, message rates, error rates
- **Health Monitoring**: Heartbeat checks and connection state tracking
- **Event Callbacks**: Extensible event system for custom monitoring
- **Dashboard Integration**: Compatible with existing admin tools

## Configuration

### Default Settings
```python
heartbeat_interval = 30  # seconds
connection_timeout = 300  # 5 minutes
cleanup_interval = 60  # 1 minute
max_connections_per_user = 5
max_total_connections = 10000
```

### Environment Variables
- `WEBSOCKET_HEARTBEAT_INTERVAL`: Heartbeat frequency
- `WEBSOCKET_CONNECTION_TIMEOUT`: Connection timeout duration
- `WEBSOCKET_MAX_CONNECTIONS`: Global connection limit
- `WEBSOCKET_MAX_USER_CONNECTIONS`: Per-user connection limit

## Connection Lifecycle

### 1. Connection Establishment
```
Client connects → WebSocket Manager registers connection → 
Session ID generated → Connection state: CONNECTING → 
Welcome message sent → Connection state: CONNECTED
```

### 2. User Authentication
```
User sends first message with user_profile_id → 
WebSocket Manager authenticates connection → 
Connection state: AUTHENTICATED → 
User-specific groups joined
```

### 3. Active Communication
```
Messages received → Activity tracking → 
Connection state: ACTIVE → 
Heartbeat monitoring → 
Message routing
```

### 4. Connection Cleanup
```
Connection timeout OR consumer instance lost → 
Connection state: STALE → 
Cleanup task removes connection → 
Connection state: DISCONNECTED
```

## Message Flow

### Incoming Messages
1. Consumer receives WebSocket message
2. WebSocket Manager tracks activity
3. Message processed by application logic
4. Response routed back through WebSocket Manager

### Outgoing Messages
1. Application generates response
2. WebSocket Manager routes to appropriate sessions
3. Message delivered to client(s)
4. Delivery status tracked and logged

## Error Handling

### Connection Failures
- **Graceful Degradation**: System continues operating with reduced connections
- **Automatic Retry**: Failed connections automatically cleaned up
- **Error Logging**: Comprehensive error tracking and reporting
- **Fallback Mechanisms**: Alternative message delivery paths

### Memory Management
- **Weak References**: Prevents circular reference memory leaks
- **Automatic Cleanup**: Regular cleanup of stale connections
- **Resource Limits**: Configurable limits prevent resource exhaustion
- **Garbage Collection**: Proper cleanup of connection resources

## Performance Optimizations

### Efficient Data Structures
- **Hash Maps**: O(1) lookup for connections and users
- **Set Operations**: Efficient user session management
- **Weak References**: Memory-efficient consumer references

### Background Tasks
- **Asynchronous Cleanup**: Non-blocking connection cleanup
- **Batched Operations**: Efficient bulk operations
- **Configurable Intervals**: Tunable performance parameters

### Message Routing
- **Direct Routing**: Bypass unnecessary group operations
- **Bulk Operations**: Efficient multi-user message delivery
- **Connection Pooling**: Reuse of connection resources

## Integration with Existing Systems

### Backward Compatibility
- **Legacy Support**: Maintains compatibility with existing monitoring
- **Gradual Migration**: Can be deployed alongside existing systems
- **API Consistency**: Preserves existing WebSocket API contracts

### Admin Tools Integration
- **Connection Dashboard**: Real-time connection monitoring
- **Metrics Display**: Integration with existing admin interfaces
- **Debug Tools**: Enhanced debugging capabilities

## Monitoring & Metrics

### Key Metrics
- `total_connections`: Current total connections
- `active_connections`: Currently active connections
- `messages_sent`: Total messages sent
- `messages_failed`: Failed message deliveries
- `connections_created`: Total connections created
- `connections_closed`: Total connections closed
- `stale_connections_removed`: Cleanup efficiency

### Health Checks
- Connection state monitoring
- Heartbeat response tracking
- Consumer instance validation
- Resource usage monitoring

## Future Enhancements

### Redis Integration
- **Distributed State**: Multi-server connection state sharing
- **Pub/Sub Messaging**: Efficient cross-server message routing
- **Session Persistence**: Connection state persistence across restarts

### Load Balancing
- **Connection Distribution**: Intelligent connection routing
- **Server Health**: Automatic failover capabilities
- **Horizontal Scaling**: Support for multiple WebSocket servers

### Advanced Monitoring
- **Predictive Analytics**: Connection pattern analysis
- **Performance Optimization**: Automatic parameter tuning
- **Alerting System**: Proactive issue detection

## Usage Examples

### Basic Connection Management
```python
from apps.main.services.websocket_integration import ws_integration

# In consumer connect method
session_id = ws_integration.on_connect(self, user_id=None)

# In consumer disconnect method
ws_integration.on_disconnect(self)
```

### Message Routing
```python
# Send to specific user
ws_integration.send_message_to_user(user_id, message)

# Send to specific session
ws_integration.send_message_to_session(session_id, message)

# Broadcast to all
ws_integration.broadcast_message(message)
```

### Monitoring
```python
# Get metrics
metrics = ws_integration.get_metrics()

# Get connection info
info = ws_integration.get_connection_info(session_id)

# Get user sessions
sessions = ws_integration.get_user_sessions(user_id)
```

## Testing

### Unit Tests
- Connection lifecycle testing
- Message routing validation
- Cleanup mechanism verification
- Error handling validation

### Integration Tests
- End-to-end message flow
- Multi-user scenarios
- High-load testing
- Failure recovery testing

### Performance Tests
- Connection limit testing
- Message throughput testing
- Memory usage validation
- Cleanup efficiency testing

## Root Cause Analysis & Resolution

### Critical Issue Identified
**Problem**: Massive connection leaks in ConnectionMonitorConsumer causing server overload
- **Symptoms**: 5000+ open WebSocket connections, HTTP requests hanging, server unresponsive
- **Root Cause**: Browser opening multiple connection monitor WebSockets without proper cleanup
- **Impact**: Complete system failure, unable to handle new connections

### Emergency Fixes Applied
1. **Connection Limits**: Added strict limits (max 3 monitor connections)
2. **Connection Rejection**: Reject new connections when limit reached
3. **Aggressive Cleanup**: Force cleanup of stale consumers on disconnect
4. **Monitoring**: Enhanced logging for connection tracking

### Verification Results
✅ **WebSocket connections working**: Main game WebSocket connects successfully
✅ **Wheel generation working**: Full workflow completes end-to-end
✅ **Connection limits enforced**: Monitor connections properly limited
✅ **System stability**: Server responsive to HTTP and WebSocket requests

## Deployment Considerations

### Resource Requirements
- **Memory**: ~1MB per 1000 connections
- **CPU**: Minimal overhead for monitoring tasks
- **Network**: Efficient message routing reduces bandwidth

### Configuration Tuning
- Adjust limits based on server capacity
- Tune cleanup intervals for performance
- Configure heartbeat frequency for reliability

### Monitoring Setup
- Enable comprehensive logging
- Set up metrics collection
- Configure alerting thresholds
- Monitor resource usage patterns

### macOS Docker Environment Considerations
- **Connection Handling**: Docker on macOS M1 handles WebSocket connections properly
- **Performance**: No specific issues with Docker networking on macOS Ventura
- **Scalability**: System can handle expected load with proper connection management
- **Debugging**: Standard debugging tools work effectively in containerized environment
