<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Connection Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .dashboard {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 8px;
        }
        
        .header p {
            color: #666;
            font-size: 1.1rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }
        
        .stat-header {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            font-size: 1.5rem;
        }
        
        .stat-icon.connections {
            background: linear-gradient(135deg, #4facfe, #00f2fe);
        }
        
        .stat-icon.messages {
            background: linear-gradient(135deg, #43e97b, #38f9d7);
        }
        
        .stat-icon.workflows {
            background: linear-gradient(135deg, #fa709a, #fee140);
        }
        
        .stat-icon.errors {
            background: linear-gradient(135deg, #ff9a9e, #fecfef);
        }
        
        .stat-title {
            font-size: 0.9rem;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-weight: 600;
        }
        
        .stat-value {
            font-size: 2.2rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 8px;
        }
        
        .stat-change {
            font-size: 0.85rem;
            padding: 4px 8px;
            border-radius: 8px;
            font-weight: 500;
        }
        
        .stat-change.positive {
            background: rgba(67, 233, 123, 0.1);
            color: #1a9852;
        }
        
        .stat-change.negative {
            background: rgba(255, 154, 158, 0.1);
            color: #e63946;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 24px;
        }
        
        .connections-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .panel-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: #333;
        }
        
        .filter-controls {
            display: flex;
            gap: 12px;
            margin-bottom: 20px;
        }
        
        .filter-btn {
            padding: 8px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            background: white;
            color: #666;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .filter-btn.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-color: transparent;
        }
        
        .connection-list {
            max-height: 500px;
            overflow-y: auto;
        }
        
        .connection-item {
            display: flex;
            align-items: center;
            padding: 16px;
            border-radius: 12px;
            margin-bottom: 12px;
            background: #f8f9fa;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .connection-item:hover {
            background: #e9ecef;
            transform: translateX(4px);
        }
        
        .connection-status {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 16px;
        }
        
        .connection-status.connected {
            background: #28a745;
            box-shadow: 0 0 8px rgba(40, 167, 69, 0.5);
        }
        
        .connection-status.disconnected {
            background: #dc3545;
        }
        
        .connection-info {
            flex: 1;
        }
        
        .connection-user {
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }
        
        .connection-details {
            font-size: 0.85rem;
            color: #666;
        }
        
        .connection-metrics {
            text-align: right;
            font-size: 0.8rem;
            color: #666;
        }
        
        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 24px;
        }
        
        .activity-panel, .system-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .activity-chart {
            height: 200px;
            background: linear-gradient(135deg, #f6f9fc, #eef4f7);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-style: italic;
        }
        
        .system-status {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }
        
        .system-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .system-label {
            font-weight: 500;
            color: #333;
        }
        
        .system-value {
            font-size: 0.9rem;
            padding: 4px 8px;
            border-radius: 6px;
            font-weight: 500;
        }
        
        .system-value.good {
            background: rgba(67, 233, 123, 0.1);
            color: #1a9852;
        }
        
        .system-value.warning {
            background: rgba(255, 193, 7, 0.1);
            color: #d39e00;
        }
        
        .system-value.error {
            background: rgba(255, 154, 158, 0.1);
            color: #e63946;
        }
        
        .action-buttons {
            display: flex;
            gap: 12px;
            margin-top: 20px;
        }
        
        .action-btn {
            flex: 1;
            padding: 12px 16px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .action-btn.primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        
        .action-btn.secondary {
            background: #e9ecef;
            color: #495057;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <header class="header">
            <h1>WebSocket Connection Dashboard</h1>
            <p>Real-time monitoring and management of WebSocket connections</p>
        </header>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon connections">🔗</div>
                    <div>
                        <div class="stat-title">Active Connections</div>
                        <div class="stat-value" id="activeConnections">247</div>
                        <div class="stat-change positive">+12 from last hour</div>
                    </div>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon messages">💬</div>
                    <div>
                        <div class="stat-title">Messages/min</div>
                        <div class="stat-value" id="messagesPerMin">1,324</div>
                        <div class="stat-change positive">+8.2% avg</div>
                    </div>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon workflows">⚡</div>
                    <div>
                        <div class="stat-title">Active Workflows</div>
                        <div class="stat-value" id="activeWorkflows">89</div>
                        <div class="stat-change positive">Normal load</div>
                    </div>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon errors">⚠️</div>
                    <div>
                        <div class="stat-title">Error Rate</div>
                        <div class="stat-value" id="errorRate">0.8%</div>
                        <div class="stat-change negative">+0.2% from avg</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="main-content">
            <div class="connections-panel">
                <div class="panel-header">
                    <h2 class="panel-title">Live Connections</h2>
                </div>
                
                <div class="filter-controls">
                    <button class="filter-btn active" onclick="filterConnections('all')">All</button>
                    <button class="filter-btn" onclick="filterConnections('connected')">Connected</button>
                    <button class="filter-btn" onclick="filterConnections('workflows')">In Workflow</button>
                    <button class="filter-btn" onclick="filterConnections('errors')">Errors</button>
                </div>
                
                <div class="connection-list" id="connectionList">
                    <div class="connection-item">
                        <div class="connection-status connected"></div>
                        <div class="connection-info">
                            <div class="connection-user">user-profile-123</div>
                            <div class="connection-details">Session: client_session_a1b2c3d4 • Wheel Workflow</div>
                        </div>
                        <div class="connection-metrics">
                            <div>12 msg/min</div>
                            <div>Connected 45m</div>
                        </div>
                    </div>
                    
                    <div class="connection-item">
                        <div class="connection-status connected"></div>
                        <div class="connection-info">
                            <div class="connection-user">user-profile-456</div>
                            <div class="connection-details">Session: client_session_e5f6g7h8 • Chat Only</div>
                        </div>
                        <div class="connection-metrics">
                            <div>3 msg/min</div>
                            <div>Connected 2h</div>
                        </div>
                    </div>
                    
                    <div class="connection-item">
                        <div class="connection-status connected"></div>
                        <div class="connection-info">
                            <div class="connection-user">user-profile-789</div>
                            <div class="connection-details">Session: client_session_i9j0k1l2 • Discussion Workflow</div>
                        </div>
                        <div class="connection-metrics">
                            <div>8 msg/min</div>
                            <div>Connected 1h</div>
                        </div>
                    </div>
                    
                    <div class="connection-item">
                        <div class="connection-status disconnected"></div>
                        <div class="connection-info">
                            <div class="connection-user">user-profile-101</div>
                            <div class="connection-details">Session: client_session_m3n4o5p6 • Last seen 5m ago</div>
                        </div>
                        <div class="connection-metrics">
                            <div>Error</div>
                            <div>Reconnecting...</div>
                        </div>
                    </div>
                </div>
                
                <div class="action-buttons">
                    <button class="action-btn primary" onclick="refreshConnections()">Refresh Data</button>
                    <button class="action-btn secondary" onclick="exportData()">Export Logs</button>
                </div>
            </div>
            
            <div class="sidebar">
                <div class="activity-panel">
                    <div class="panel-header">
                        <h3 class="panel-title">Connection Activity</h3>
                    </div>
                    <div class="activity-chart">
                        📊 Real-time activity chart would go here
                    </div>
                </div>
                
                <div class="system-panel">
                    <div class="panel-header">
                        <h3 class="panel-title">System Health</h3>
                    </div>
                    <div class="system-status">
                        <div class="system-item">
                            <span class="system-label">Redis</span>
                            <span class="system-value good">Healthy</span>
                        </div>
                        <div class="system-item">
                            <span class="system-label">Celery Workers</span>
                            <span class="system-value good">8/8 Active</span>
                        </div>
                        <div class="system-item">
                            <span class="system-label">Database</span>
                            <span class="system-value warning">High Load</span>
                        </div>
                        <div class="system-item">
                            <span class="system-label">Memory Usage</span>
                            <span class="system-value good">67%</span>
                        </div>
                        <div class="system-item">
                            <span class="system-label">CPU Usage</span>
                            <span class="system-value good">45%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // WebSocket connection for real-time data
        let monitorSocket = null;
        let isConnected = false;
        let connectionData = [];
        let reconnectAttempts = 0;
        const maxReconnectAttempts = 5;

        // Initialize WebSocket connection
        function connectToMonitor() {
            const wsScheme = window.location.protocol === "https:" ? "wss" : "ws";
            const wsPath = wsScheme + '://' + window.location.host + "/ws/connection-monitor/";

            console.log('🔗 Connecting to:', wsPath);

            monitorSocket = new WebSocket(wsPath);

            monitorSocket.onopen = function(e) {
                console.log('✅ Monitor WebSocket connected');
                isConnected = true;
                reconnectAttempts = 0;

                // Update connection status
                updateConnectionStatus('Connected', 'good');

                // Request initial data
                requestConnectionData();
                requestSystemHealth();
                requestMessageStats();
            };

            monitorSocket.onmessage = function(e) {
                try {
                    const data = JSON.parse(e.data);
                    console.log('📦 Received data:', data.type, data);
                    handleMonitorMessage(data);
                } catch (error) {
                    console.error('❌ Error parsing monitor message:', error);
                }
            };

            monitorSocket.onerror = function(e) {
                console.error('❌ Monitor WebSocket error:', e);
                updateConnectionStatus('Error', 'error');
            };

            monitorSocket.onclose = function(e) {
                console.log('🔌 Monitor WebSocket closed:', e.code, e.reason);
                isConnected = false;
                updateConnectionStatus('Disconnected', 'warning');

                // Attempt to reconnect
                if (reconnectAttempts < maxReconnectAttempts) {
                    reconnectAttempts++;
                    console.log(`🔄 Reconnecting... (attempt ${reconnectAttempts}/${maxReconnectAttempts})`);
                    setTimeout(connectToMonitor, 2000 * reconnectAttempts);
                } else {
                    console.error('❌ Max reconnection attempts reached');
                    updateConnectionStatus('Failed', 'error');
                }
            };
        }

        // Handle incoming WebSocket messages
        function handleMonitorMessage(data) {
            switch (data.type) {
                case 'connection_data':
                    updateConnectionList(data.data);
                    updateConnectionCount(data.data.length);
                    break;
                case 'system_health':
                    updateSystemHealth(data.data);
                    break;
                case 'message_stats':
                    updateMessageStats(data.data);
                    break;
                default:
                    console.log('📦 Unknown message type:', data.type);
            }
        }

        // Request functions
        function requestConnectionData() {
            if (isConnected && monitorSocket) {
                console.log('📤 Requesting connection data');
                monitorSocket.send(JSON.stringify({ type: 'get_connections' }));
            }
        }

        function requestSystemHealth() {
            if (isConnected && monitorSocket) {
                console.log('📤 Requesting system health');
                monitorSocket.send(JSON.stringify({ type: 'get_system_health' }));
            }
        }

        function requestMessageStats() {
            if (isConnected && monitorSocket) {
                console.log('📤 Requesting message stats');
                monitorSocket.send(JSON.stringify({ type: 'get_message_stats' }));
            }
        }

        // Update functions
        function updateConnectionStatus(status, type) {
            // Add a status indicator to the header
            let statusEl = document.getElementById('connectionStatus');
            if (!statusEl) {
                statusEl = document.createElement('div');
                statusEl.id = 'connectionStatus';
                statusEl.style.cssText = 'position: absolute; top: 10px; right: 10px; padding: 8px 16px; border-radius: 8px; font-weight: 600; font-size: 0.9rem;';
                document.querySelector('.header').appendChild(statusEl);
            }

            statusEl.textContent = `WebSocket: ${status}`;
            statusEl.className = `system-value ${type}`;
        }

        function updateConnectionCount(count) {
            document.getElementById('activeConnections').textContent = count;
        }

        function updateConnectionList(connections) {
            connectionData = connections;
            const listEl = document.getElementById('connectionList');

            if (connections.length === 0) {
                listEl.innerHTML = '<div style="text-align: center; padding: 40px; color: #666; font-style: italic;">No active connections</div>';
                return;
            }

            listEl.innerHTML = connections.map(conn => `
                <div class="connection-item">
                    <div class="connection-status ${conn.status === 'connected' ? 'connected' : 'disconnected'}"></div>
                    <div class="connection-info">
                        <div class="connection-user">${conn.user_id}</div>
                        <div class="connection-details">Session: ${conn.session_id} • ${conn.current_workflow || 'No workflow'}</div>
                    </div>
                    <div class="connection-metrics">
                        <div>${conn.message_count} messages</div>
                        <div>Connected ${conn.duration}</div>
                    </div>
                </div>
            `).join('');
        }

        function updateSystemHealth(health) {
            // Update system health indicators
            console.log('🏥 System health:', health);
        }

        function updateMessageStats(stats) {
            document.getElementById('messagesPerMin').textContent = stats.messages_per_minute.toLocaleString();
            document.getElementById('activeWorkflows').textContent = stats.active_workflows;
            document.getElementById('errorRate').textContent = (stats.error_rate * 100).toFixed(1) + '%';
        }

        // Legacy functions for compatibility
        function filterConnections(filter) {
            // Remove active class from all buttons
            document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));

            // Add active class to clicked button
            event.target.classList.add('active');

            // Filter the connection list
            console.log('🔍 Filtering connections by:', filter);
            // Implementation would filter connectionData and update display
        }

        function refreshConnections() {
            console.log('🔄 Refreshing connection data...');
            requestConnectionData();
            requestSystemHealth();
            requestMessageStats();
        }

        function exportData() {
            console.log('📥 Exporting connection data...');
            const dataStr = JSON.stringify(connectionData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `connection-data-${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            URL.revokeObjectURL(url);
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Initializing WebSocket connection dashboard');
            connectToMonitor();

            // Set up periodic refresh as fallback
            setInterval(function() {
                if (isConnected) {
                    requestConnectionData();
                    requestMessageStats();
                }
            }, 10000); // Every 10 seconds
        });
    </script>
</body>
</html>