# 🏠 Goali Environment System - Comprehensive Documentation

## 🎯 Overview

The Goali Environment System is a sophisticated framework for modeling user environments and their impact on activity suitability. It captures the complex relationship between physical spaces, social contexts, and user psychology to enable highly personalized activity recommendations.

## 🏗️ Architecture

### Core Models

#### 1. UserEnvironment
The foundation representing a specific environment instance for a user.

**Key Fields:**
- `environment_name`: User's personal name for the environment
- `environment_description`: Detailed description of the environment
- `generic_environment`: Link to GenericEnvironment catalog
- `is_current`: <PERSON>olean indicating if this is the user's current environment
- `user_profile`: Owner of this environment

**Properties:**
- Hierarchical relationship with GenericEnvironment for shared characteristics
- User-specific customization and personalization
- Support for multiple environments per user

#### 2. UserEnvironmentPhysicalProperties
Captures detailed physical characteristics affecting activity suitability.

**Key Fields:**
- `rurality`: Urban (0) to rural (100) scale
- `noise_level`: Very quiet (0) to extremely loud (100)
- `light_quality`: Very dark (0) to very bright (100)
- `temperature_range`: Enum (cold, cool, moderate, warm, hot, variable)
- `accessibility`: Not accessible (0) to fully accessible (100)
- `air_quality`: Poor (0) to excellent (100)
- `has_natural_elements`: Boolean for plants, trees, natural features
- `surface_type`: Enum (hard, soft, mixed, outdoor, water)

#### 3. UserEnvironmentSocialContext
Describes social aspects affecting activity appropriateness.

**Key Fields:**
- `privacy_level`: Public (0) to extremely private (100)
- `typical_occupancy`: Empty (0) to extremely crowded (100)
- `social_interaction_level`: No interaction (0) to constant interaction (100)
- `formality_level`: Very casual (0) to very formal (100)
- `safety_level`: Unsafe (0) to very safe (100)
- `supervision_level`: Enum (none, minimal, moderate, high, constant)
- `cultural_diversity`: Homogeneous (0) to highly diverse (100)

#### 4. UserEnvironmentActivitySupport
Defines how well the environment supports different activities.

**Key Fields:**
- `digital_connectivity`: No connectivity (0) to excellent connectivity (100)
- `resource_availability`: No resources (0) to abundant resources (100)
- `time_availability`: JSON describing when environment is available
- `domain_specific_support`: JSON with user-specific domain support overrides

**Methods:**
- `update_domain_support(domain_code, support_level)`: Update support for specific domains
- `get_effective_domain_support()`: Combine generic and user-specific support levels

#### 5. UserEnvironmentPsychologicalQualities
Captures user-specific psychological responses to the environment.

**Key Fields:**
- `restorative_quality`: Not restorative (0) to highly restorative (100)
- `stimulation_level`: Under-stimulating (0) to over-stimulating (100)
- `aesthetic_appeal`: Unappealing (0) to highly appealing (100)
- `novelty_level`: Very familiar (0) to completely novel (100)
- `comfort_level`: Uncomfortable (0) to very comfortable (100)
- `personal_significance`: None (0) to deep significance (100)
- `emotional_associations`: JSON mapping emotions to intensity levels

## 🔄 Business Logic

### Environment Classification

#### By Physical Characteristics
1. **Urban vs Rural**: Affects noise, accessibility, resource availability
2. **Indoor vs Outdoor**: Influences weather dependency, equipment needs
3. **Private vs Public**: Determines activity appropriateness and social constraints
4. **Fixed vs Mobile**: Affects resource availability and setup requirements

#### By Social Context
1. **Solitary Environments**: High privacy, low interaction, personal control
2. **Collaborative Spaces**: Moderate privacy, high interaction, shared resources
3. **Public Venues**: Low privacy, variable interaction, external constraints
4. **Professional Settings**: High formality, structured interaction, specific resources

#### By Activity Support
1. **Creative Spaces**: High aesthetic appeal, moderate stimulation, abundant resources
2. **Focus Environments**: Low noise, minimal distractions, high comfort
3. **Physical Activity Areas**: Open space, appropriate surfaces, safety equipment
4. **Social Gathering Places**: High interaction support, flexible arrangements

### Psychological Impact Assessment

#### Restorative Qualities
- **High Restorative**: Natural elements, low noise, personal significance
- **Moderate Restorative**: Comfortable, familiar, aesthetically pleasing
- **Low Restorative**: Overstimulating, uncomfortable, impersonal

#### Stimulation Management
- **Optimal Stimulation**: Balanced sensory input, engaging but not overwhelming
- **Under-stimulation**: Monotonous, lacking variety, potentially boring
- **Over-stimulation**: Too much sensory input, distracting, potentially stressful

## 🔧 Implementation Details

### Environment Property Service
Helper service for creating and managing environment property models with graceful handling of None/missing data.

**Key Features:**
- Atomic transactions for property creation
- Graceful handling of missing data
- Validation and error handling
- Bulk operations support

### Domain Support Integration
Environments provide domain-specific support levels that influence activity recommendations.

**Domain Categories:**
- **Creative**: Art, music, writing, design activities
- **Technical**: Programming, engineering, analytical work
- **Physical**: Exercise, sports, movement activities
- **Social**: Group activities, communication, collaboration
- **Learning**: Study, research, skill development
- **Wellness**: Meditation, relaxation, self-care

### Time Availability Management
Flexible JSON structure for describing when environments are accessible.

**Example Structure:**
```json
{
  "weekdays": ["morning", "evening"],
  "weekends": ["all_day"],
  "specific_times": {
    "monday": ["09:00-12:00", "18:00-22:00"],
    "saturday": ["08:00-20:00"]
  }
}
```

## 📋 Usage Examples

### Environment Creation
```python
# Create user environment with all properties
user_env = UserEnvironment.objects.create(
    user_profile=user_profile,
    environment_name="Home Workshop",
    environment_description="Personal creative workspace",
    generic_environment=workshop_generic,
    is_current=True
)

# Add physical properties
physical_props = UserEnvironmentPhysicalProperties.objects.create(
    user_environment=user_env,
    noise_level=25,
    light_quality=75,
    temperature_range='moderate',
    accessibility=95,
    has_natural_elements=True
)
```

### Activity Matching
```python
def find_suitable_environments(user_profile, activity_requirements):
    """Find environments that meet activity requirements."""
    environments = user_profile.environments.all()
    
    suitable = []
    for env in environments:
        # Check physical requirements
        if activity_requirements.get('min_light', 0) <= env.physical_properties.light_quality:
            # Check domain support
            domain_support = env.activity_support.get_effective_domain_support()
            if domain_support.get(activity_requirements['domain'], 0) >= activity_requirements['min_support']:
                suitable.append(env)
    
    return suitable
```

### Psychological Assessment
```python
def assess_environment_mood_impact(user_environment, target_mood):
    """Assess how environment affects target mood."""
    psych_qualities = user_environment.psychological_qualities
    
    mood_factors = {
        'calm': psych_qualities.restorative_quality,
        'energetic': psych_qualities.stimulation_level,
        'creative': psych_qualities.aesthetic_appeal,
        'focused': 100 - psych_qualities.stimulation_level  # Inverse relationship
    }
    
    return mood_factors.get(target_mood, 50)  # Default neutral
```

## 🧪 Testing Strategy

### Test Coverage Areas
1. **Model Creation**: All environment models and relationships
2. **Property Validation**: Range validation, enum constraints
3. **Business Logic**: Domain support, time availability
4. **Integration**: Environment-activity matching
5. **Edge Cases**: Missing data, invalid values

### Test Files
- `test_environment_models.py`: Core model functionality
- `test_environment_properties.py`: Property model testing
- `test_environment_service.py`: Service layer testing
- `test_environment_integration.py`: Full system integration

## 🔮 Future Enhancements

### Planned Features
1. **Dynamic Environment Updates**: Real-time environment condition monitoring
2. **Environment Recommendations**: Suggest optimal environments for activities
3. **Shared Environments**: Support for environments shared between users
4. **Environment History**: Track how environment usage affects outcomes
5. **Smart Scheduling**: Optimize activity scheduling based on environment availability

### Integration Points
1. **Activity Recommendation**: Use environment data for activity filtering
2. **Wheel Generation**: Consider environment constraints in activity selection
3. **Mood Tracking**: Correlate environment usage with mood changes
4. **Resource Management**: Link environment resources to activity requirements

## 📚 Related Documentation
- `docs/backend/INVENTORY_SYSTEM_COMPREHENSIVE.md`: Resource management in environments
- `docs/backend/users/USER_PROFILE_IMPORT_SYSTEM.md`: Environment data import
- `backend/schemas/user_profile.schema.json`: Environment schema definitions
- `backend/apps/user/models.py`: Environment model implementations

## 🎯 Key Benefits

1. **Comprehensive Environment Modeling**: Capture all aspects affecting activity suitability
2. **User-Specific Customization**: Personalized environment characteristics
3. **Psychological Awareness**: Consider emotional and psychological impacts
4. **Activity Optimization**: Match activities to optimal environments
5. **Flexible Time Management**: Support complex availability patterns
6. **Scalable Architecture**: Support multiple environments per user
