# HistoryEvent Authoritative Guide

This document serves as the comprehensive, authoritative guide for the HistoryEvent model and its usage patterns throughout the Goali application. The HistoryEvent system provides comprehensive user behavior tracking, analytics foundation, and audit trail capabilities.

## Table of Contents
- [Model Overview](#model-overview)
- [Event Type Categories](#event-type-categories)
- [Implementation Patterns](#implementation-patterns)
- [Frontend Error Integration](#frontend-error-integration)
- [Analytics and Reporting](#analytics-and-reporting)
- [Best Practices](#best-practices)

## Model Overview

The HistoryEvent model serves as a comprehensive audit trail and analytics foundation for all significant user interactions and system events in the Goali application.

### Model Structure

```python
class HistoryEvent(models.Model):
    timestamp = models.DateTimeField(auto_now_add=True)
    event_type = models.Char<PERSON>ield(max_length=100)
    
    # Primary entity that triggered the event
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.CharField(max_length=255)
    related_object = GenericForeignKey('content_type', 'object_id')
    
    # Optional secondary entity
    secondary_content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE, null=True, blank=True, related_name='secondary_events')
    secondary_object_id = models.Char<PERSON>ield(max_length=255, null=True, blank=True)
    secondary_object = GenericForeignKey('secondary_content_type', 'secondary_object_id')
    
    # Event details and user context
    details = models.JSONField(default=dict)
    user_profile = models.ForeignKey('user.UserProfile', on_delete=models.CASCADE, null=True)
```

### Key Design Principles

1. **Generic Relations**: Uses Django's ContentType framework for flexible entity relationships
2. **Rich Metadata**: JSON details field for extensible event-specific data
3. **User Context**: Always linked to UserProfile for user-centric analytics
4. **Dual Entity Support**: Primary and secondary objects for complex relationships
5. **Temporal Ordering**: Automatic timestamp for chronological analysis

## Event Type Categories

### 1. Wheel-Related Events

#### `wheel_winner_detection_disagreement` ⭐ **NEW** (Session 39)
**Purpose**: Track when winner detection mechanisms disagree and collect user feedback for system improvement
```python
HistoryEvent.objects.create(
    event_type='wheel_winner_detection_disagreement',
    content_type=ContentType.objects.get_for_model(Wheel),
    object_id=wheel.id,
    user_profile=user_profile,
    details={
        'ball_position': {'x': 245.3, 'y': 187.2},
        'wheel_center': {'x': 200, 'y': 200},
        'detection_results': {
            'multi_point_angle': {'segment': 'Activity 2', 'confidence': 0.85},
            'collision_history': {'segment': 'Activity 3', 'confidence': 0.72},
            'radial_sector': {'segment': 'Activity 2', 'confidence': 0.91},
            'statistical_sampling': {'segment': 'Activity 2', 'confidence': 0.88},
            'distance_to_center': {'segment': 'Activity 3', 'confidence': 0.79}
        },
        'consensus_level': 0.6,  # 3/5 mechanisms agreed
        'agreement_count': 3,
        'disagreement_methods': ['collision_history', 'distance_to_center'],
        'majority_winner': 'Activity 2',
        'minority_winners': ['Activity 3'],
        'user_feedback_requested': True,
        'user_feedback_response': None,  # To be updated when user responds
        'actual_winner': None,  # To be updated when user confirms
        'learning_opportunity': True,
        'spin_timestamp': time.time(),
        'wheel_configuration': {
            'segment_count': 5,
            'segments': [
                {'id': 'item_1', 'text': 'Activity 1', 'percentage': 20},
                {'id': 'item_2', 'text': 'Activity 2', 'percentage': 20},
                {'id': 'item_3', 'text': 'Activity 3', 'percentage': 20},
                {'id': 'item_4', 'text': 'Activity 4', 'percentage': 20},
                {'id': 'item_5', 'text': 'Activity 5', 'percentage': 20}
            ]
        }
    }
)
```

#### `wheel_winner_detection_feedback` ⭐ **NEW** (Session 39)
**Purpose**: Track user feedback on winner detection accuracy for machine learning improvement
```python
HistoryEvent.objects.create(
    event_type='wheel_winner_detection_feedback',
    content_type=ContentType.objects.get_for_model(Wheel),
    object_id=wheel.id,
    user_profile=user_profile,
    details={
        'original_disagreement_event_id': disagreement_event.id,
        'user_confirmed_winner': 'Activity 3',
        'system_predicted_winner': 'Activity 2',
        'prediction_was_correct': False,
        'user_feedback_timestamp': time.time(),
        'user_confidence': 'very_confident',  # very_confident, confident, uncertain
        'feedback_method': 'modal_selection',  # modal_selection, voice_command, gesture
        'correct_methods': ['collision_history', 'distance_to_center'],
        'incorrect_methods': ['multi_point_angle', 'radial_sector', 'statistical_sampling'],
        'method_accuracy_update': {
            'collision_history': {'accuracy_increase': 0.05, 'new_weight': 1.1},
            'distance_to_center': {'accuracy_increase': 0.03, 'new_weight': 1.05},
            'multi_point_angle': {'accuracy_decrease': 0.02, 'new_weight': 0.95},
            'radial_sector': {'accuracy_decrease': 0.04, 'new_weight': 0.92},
            'statistical_sampling': {'accuracy_decrease': 0.01, 'new_weight': 0.98}
        },
        'learning_impact': {
            'training_data_point_created': True,
            'model_weights_updated': True,
            'consensus_threshold_adjusted': False
        }
    }
)
```

#### `wheel_winner_detection_success` ⭐ **NEW** (Session 39)
**Purpose**: Track successful winner detection with high consensus for performance monitoring
```python
HistoryEvent.objects.create(
    event_type='wheel_winner_detection_success',
    content_type=ContentType.objects.get_for_model(Wheel),
    object_id=wheel.id,
    user_profile=user_profile,
    details={
        'ball_position': {'x': 245.3, 'y': 187.2},
        'wheel_center': {'x': 200, 'y': 200},
        'detection_results': {
            'multi_point_angle': {'segment': 'Activity 2', 'confidence': 0.95},
            'collision_history': {'segment': 'Activity 2', 'confidence': 0.88},
            'radial_sector': {'segment': 'Activity 2', 'confidence': 0.92},
            'statistical_sampling': {'segment': 'Activity 2', 'confidence': 0.91},
            'distance_to_center': {'segment': 'Activity 2', 'confidence': 0.89}
        },
        'consensus_level': 1.0,  # 5/5 mechanisms agreed
        'agreement_count': 5,
        'final_confidence': 0.99,
        'winner_segment': 'Activity 2',
        'detection_time_ms': 15,
        'spin_timestamp': time.time(),
        'system_performance': 'excellent'
    }
)
```

#### `wheel_generated`
**Purpose**: Track when a new wheel is created for a user
```python
HistoryEvent.objects.create(
    event_type='wheel_generated',
    content_type=ContentType.objects.get_for_model(UserProfile),
    object_id=user_profile.id,
    secondary_content_type=ContentType.objects.get_for_model(Wheel),
    secondary_object_id=wheel.id,
    user_profile=user_profile,
    details={
        'workflow_id': workflow_id,
        'agent_flow': 'wheel_generation',
        'performance_metrics': {
            'generation_time_ms': 2500,
            'item_count': 5,
            'domain_count': 3
        },
        'wheel_summary': {
            'domains': ['physical', 'creative', 'social'],
            'total_duration_minutes': 120
        }
    }
)
```

#### `wheel_spin_initiated`
**Purpose**: Track when user starts spinning the wheel
```python
HistoryEvent.objects.create(
    event_type='wheel_spin_initiated',
    content_type=ContentType.objects.get_for_model(Wheel),
    object_id=wheel.id,
    user_profile=user_profile,
    details={
        'spin_timestamp': time.time(),
        'user_energy_level': 75,
        'time_available': 30,
        'wheel_items_count': 5
    }
)
```

#### `activity_removed_from_wheel`
**Purpose**: Track when activities are removed from wheels
```python
HistoryEvent.objects.create(
    event_type='activity_removed_from_wheel',
    content_type=ContentType.objects.get_for_model(Wheel),
    object_id=wheel.id,
    user_profile=user_profile,
    details={
        'activity_name': 'Morning Yoga',
        'activity_domain': 'physical',
        'wheel_items_before': 5,
        'wheel_items_after': 4,
        'removal_method': 'api_delete',
        'wheel_item_id': wheel_item_id
    }
)
```

#### `activity_added_to_wheel`
**Purpose**: Track when activities are added to wheels
```python
HistoryEvent.objects.create(
    event_type='activity_added_to_wheel',
    content_type=ContentType.objects.get_for_model(Wheel),
    object_id=wheel.id,
    user_profile=user_profile,
    details={
        'activity_name': 'Creative Writing',
        'activity_domain': 'creative',
        'activity_tailored_id': activity_tailored.id,
        'wheel_items_before': 4,
        'wheel_items_after': 5,
        'addition_method': 'api_post',
        'wheel_item_id': wheel_item_id
    }
)
```

### 2. User Interaction Events

#### `contract_displayed`
**Purpose**: Track when legal contract is shown to user
```python
HistoryEvent.objects.create(
    event_type='contract_displayed',
    content_type=ContentType.objects.get_for_model(UserProfile),
    object_id=user_profile.id,
    user_profile=user_profile,
    details={
        'contract_version': '1.0',
        'display_timestamp': time.time(),
        'user_session_id': session_id
    }
)
```

#### `contract_signed`
**Purpose**: Track when user accepts legal contract
```python
HistoryEvent.objects.create(
    event_type='contract_signed',
    content_type=ContentType.objects.get_for_model(UserProfile),
    object_id=user_profile.id,
    user_profile=user_profile,
    details={
        'contract_version': '1.0',
        'signature_timestamp': time.time(),
        'ip_address': request.META.get('REMOTE_ADDR'),
        'user_agent': request.META.get('HTTP_USER_AGENT')
    }
)
```

### 3. Custom Activity Events

#### `custom_activity_created`
**Purpose**: Track when users create custom activities
```python
HistoryEvent.objects.create(
    event_type='custom_activity_created',
    content_type=ContentType.objects.get_for_model(Wheel),
    object_id=wheel.id,
    user_profile=user_profile,
    details={
        'title': 'Learn Guitar',
        'description': 'Practice guitar for 30 minutes',
        'duration_range': '30-45 minutes',
        'challengingness': 60,
        'comfort_level': 70,
        'created_timestamp': time.time(),
        'activity_type': 'custom_user_created',
        'domain': 'creative'
    }
)
```

### 4. Workflow Events

#### `workflow_initiated`
**Purpose**: Track when workflows are started
```python
HistoryEvent.objects.create(
    event_type='workflow_initiated',
    content_type=ContentType.objects.get_for_model(UserProfile),
    object_id=user_profile.id,
    user_profile=user_profile,
    details={
        'workflow_type': 'wheel_generation',
        'workflow_id': workflow_id,
        'initial_context': {
            'time_available': 60,
            'energy_level': 80,
            'user_preferences': ['outdoor', 'social']
        }
    }
)
```

### 5. Error Events (Frontend Integration)

#### `frontend_error_temporary`
**Purpose**: Track temporary frontend errors (connection issues, etc.)
```python
HistoryEvent.objects.create(
    event_type='frontend_error_temporary',
    content_type=ContentType.objects.get_for_model(UserProfile),
    object_id=user_profile.id,
    user_profile=user_profile,
    details={
        'error_type': 'CONNECTION_ERROR',
        'error_message': 'WebSocket connection failed',
        'error_level': 'temporary',
        'component': 'websocket-manager',
        'timestamp': datetime.now().isoformat(),
        'user_agent': navigator.userAgent,
        'url': window.location.href,
        'retry_count': 3,
        'auto_resolved': True
    }
)
```

#### `frontend_error_critical`
**Purpose**: Track critical frontend errors requiring immediate attention
```python
HistoryEvent.objects.create(
    event_type='frontend_error_critical',
    content_type=ContentType.objects.get_for_model(UserProfile),
    object_id=user_profile.id,
    user_profile=user_profile,
    details={
        'error_type': 'WHEEL_RENDER_ERROR',
        'error_message': 'Failed to render wheel component',
        'error_level': 'critical',
        'component': 'wheel-component',
        'stack_trace': error.stack,
        'timestamp': datetime.now().isoformat(),
        'user_agent': navigator.userAgent,
        'url': window.location.href,
        'wheel_data': wheelData,
        'requires_support': True
    }
)
```

#### `frontend_error_non_critical`
**Purpose**: Track non-critical frontend errors (debug mode only)
```python
HistoryEvent.objects.create(
    event_type='frontend_error_non_critical',
    content_type=ContentType.objects.get_for_model(UserProfile),
    object_id=user_profile.id,
    user_profile=user_profile,
    details={
        'error_type': 'VALIDATION_ERROR',
        'error_message': 'Invalid input format',
        'error_level': 'non_critical',
        'component': 'input-validator',
        'timestamp': datetime.now().isoformat(),
        'debug_mode': True,
        'input_value': inputValue,
        'expected_format': expectedFormat
    }
)
```

## Implementation Patterns

### Standard Creation Pattern
```python
from apps.main.models import HistoryEvent
from django.contrib.contenttypes.models import ContentType

# Get content type for the primary object
content_type = ContentType.objects.get_for_model(primary_object.__class__)

# Create the event
HistoryEvent.objects.create(
    event_type='event_name',
    content_type=content_type,
    object_id=str(primary_object.id),
    user_profile=user_profile,
    details={
        # Event-specific metadata
        'key': 'value',
        'timestamp': time.time()
    }
)
```

### Dual Object Pattern
```python
# For events involving two objects (e.g., adding activity to wheel)
primary_content_type = ContentType.objects.get_for_model(Wheel)
secondary_content_type = ContentType.objects.get_for_model(ActivityTailored)

HistoryEvent.objects.create(
    event_type='activity_added_to_wheel',
    content_type=primary_content_type,
    object_id=str(wheel.id),
    secondary_content_type=secondary_content_type,
    secondary_object_id=str(activity.id),
    user_profile=user_profile,
    details={
        'operation_details': 'specific_metadata'
    }
)
```

### Error Handling Pattern
```python
try:
    # Create HistoryEvent
    HistoryEvent.objects.create(...)
except Exception as e:
    # Log the error but don't let it crash the main flow
    logger.error(f"Error creating HistoryEvent: {str(e)}", exc_info=True)
```

## Frontend Error Integration

### Error Level Classification
- **Temporary**: Connection issues, transient failures (auto-retry, top banner)
- **Non-Critical**: Validation errors, minor issues (popup in debug mode only)
- **Critical**: System failures, data corruption (always visible, support notification)

### Frontend-to-Backend Error Reporting
```javascript
// Frontend error reporting service
class ErrorReportingService {
    static async reportError(errorLevel, errorType, errorMessage, metadata = {}) {
        const errorData = {
            event_type: `frontend_error_${errorLevel}`,
            content_type: 'UserProfile',
            object_id: currentUser.id,
            details: {
                error_type: errorType,
                error_message: errorMessage,
                error_level: errorLevel,
                timestamp: new Date().toISOString(),
                user_agent: navigator.userAgent,
                url: window.location.href,
                ...metadata
            }
        };

        await fetch('/api/track-event/', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(errorData)
        });
    }
}
```

### Comprehensive Error Handling Architecture (2025-06-27 Update)

The frontend now implements a complete three-level error handling system with automatic HistoryEvent integration:

#### Error Classification Service
```typescript
// Automatic error type detection based on message content and context
enum ErrorType {
    API_ERROR = 'api_error',
    VALIDATION_ERROR = 'validation_error',
    AUTHENTICATION_ERROR = 'authentication_error',
    WEBSOCKET_ERROR = 'websocket_error',
    CONNECTION_ERROR = 'connection_error',
    UNKNOWN_ERROR = 'unknown_error'
}

class ErrorClassificationService {
    static classifyError(error: Error, context: ErrorContext): AppError {
        // Intelligent classification based on error patterns
        const errorType = this.detectErrorType(error.message, context);
        const errorLevel = this.determineErrorLevel(errorType, context);

        return new AppError(errorType, errorLevel, error.message, context);
    }
}
```

#### Enhanced Error Details Schema
```json
{
    "schema_version": "1.0",
    "error_type": "WEBSOCKET_ERROR",
    "error_message": "Connection failed",
    "error_level": "temporary",
    "component": "websocket-manager",
    "timestamp": "2024-01-15T10:30:00Z",
    "debug_mode": true,
    "user_agent": "Mozilla/5.0...",
    "url": "/dashboard",
    "stack_trace": "Error: Connection failed\n    at...",
    "retry_count": 2,
    "recovery_action": "websocket_reconnect",
    "user_context": {
        "session_id": "sess_123",
        "current_page": "/dashboard",
        "user_mode": "debug",
        "wheel_state": "populated"
    },
    "performance_metrics": {
        "error_time_ms": 1500,
        "memory_usage_mb": 45.2,
        "network_latency_ms": 250
    }
}
```

#### Error Handler Integration
```typescript
class ErrorHandler {
    async handleError(error: Error, context: ErrorContext): Promise<void> {
        // 1. Classify the error
        const classified = ErrorClassificationService.classifyError(error, context);

        // 2. Create HistoryEvent for tracking
        await this.reportToBackend(classified);

        // 3. Show appropriate UI notification
        this.notificationManager.showNotification(classified);

        // 4. Execute recovery actions if applicable
        if (classified.recoveryAction) {
            await this.executeRecoveryAction(classified);
        }
    }

    private async reportToBackend(error: AppError): Promise<void> {
        const eventData = {
            event_type: `frontend_error_${error.level}`,
            content_type: 'UserProfile',
            object_id: this.currentUser.id,
            details: {
                schema_version: '1.0',
                error_type: error.type,
                error_message: error.message,
                error_level: error.level,
                component: error.context.component,
                timestamp: new Date().toISOString(),
                retry_count: error.retryCount || 0,
                recovery_action: error.recoveryAction,
                user_context: this.getUserContext(),
                performance_metrics: this.getPerformanceMetrics(),
                ...error.context
            }
        };

        await HistoryEventService.createEvent(eventData);
    }
}
```

#### Error Recovery and Retry Logic
```typescript
class ErrorRetryService {
    private static readonly RETRY_CONFIG = {
        maxRetries: 3,
        baseDelay: 1000,
        maxDelay: 10000,
        exponentialBase: 2
    };

    static async executeWithRetry<T>(
        operation: () => Promise<T>,
        errorType: ErrorType
    ): Promise<T> {
        let lastError: Error;

        for (let attempt = 0; attempt <= this.RETRY_CONFIG.maxRetries; attempt++) {
            try {
                return await operation();
            } catch (error) {
                lastError = error;

                if (attempt < this.RETRY_CONFIG.maxRetries && this.isRetryable(errorType)) {
                    const delay = Math.min(
                        this.RETRY_CONFIG.baseDelay * Math.pow(this.RETRY_CONFIG.exponentialBase, attempt),
                        this.RETRY_CONFIG.maxDelay
                    );

                    await this.delay(delay);
                    continue;
                }

                break;
            }
        }

        throw lastError;
    }
}
```

## Analytics and Reporting

### Common Query Patterns
```python
# User activity timeline
user_events = HistoryEvent.objects.filter(
    user_profile=user_profile
).order_by('-timestamp')

# Wheel generation analytics
wheel_events = HistoryEvent.objects.filter(
    event_type__startswith='wheel_'
).select_related('user_profile')

# Error frequency analysis
error_events = HistoryEvent.objects.filter(
    event_type__contains='error'
).values('details__error_type').annotate(
    count=Count('id')
).order_by('-count')
```

## Schema System

### Centralized Schema Definitions

The HistoryEvent system now includes centralized schema definitions with versioning support. All event details are validated against Pydantic schemas to ensure consistency and maintainability.

#### Using the Schema System

```python
from apps.main.services.history_event_service import HistoryEventService

# Create events using the service (automatically validates against schema)
HistoryEventService.create_wheel_generated_event(
    user_profile=user_profile,
    wheel=wheel,
    workflow_id="workflow_123",
    performance_metrics={
        "generation_time_ms": 2500,
        "item_count": 5,
        "domain_count": 3
    },
    wheel_summary={
        "domains": ["physical", "creative", "social"],
        "total_duration_minutes": 120
    }
)
```

#### Schema Validation

```python
from apps.main.schemas.history_event_schemas import validate_event_details

# Validate details before creating event
validated_details = validate_event_details("wheel_generated", raw_details)
```

#### Available Schemas

- **Wheel Events**: `wheel_generated`, `wheel_spin_initiated`, `activity_added_to_wheel`, `activity_removed_from_wheel`
- **User Events**: `contract_displayed`, `contract_signed`, `custom_activity_created`
- **Workflow Events**: `workflow_initiated`
- **Error Events**: `frontend_error_temporary`, `frontend_error_non_critical`, `frontend_error_critical`
- **System Events**: `user_session_started`, `user_session_ended`

#### Schema Versioning

Each schema includes a `schema_version` field to support evolution over time:

```python
{
    "schema_version": "1.0",
    "workflow_id": "workflow_123",
    "performance_metrics": {...}
}
```

#### API Access

Get schema information via API:

```bash
# List all schemas
GET /api/history-event-schemas/

# Get specific schema
GET /api/history-event-schemas/?event_type=wheel_generated
```

## Best Practices

1. **Use HistoryEventService**: Always use the service for event creation to ensure schema validation
2. **Schema Compliance**: Follow the defined schemas for consistent data structure
3. **Version Management**: Include schema_version in details for future compatibility
4. **Always Include User Context**: Every event should be linked to a UserProfile
5. **Rich Details**: Use the details JSON field for comprehensive metadata
6. **Consistent Naming**: Use descriptive, consistent event_type names
7. **Error Handling**: Wrap HistoryEvent creation in try/catch blocks
8. **Performance**: Use bulk_create for multiple events when possible
9. **Privacy**: Avoid storing sensitive data in details field
10. **Indexing**: Leverage existing indexes for efficient queries
11. **Documentation**: Document new event types and update schemas accordingly

## Event Type Registry

Maintain this registry when adding new event types:

### Wheel Events
- `wheel_generated`, `wheel_spin_initiated`, `activity_added_to_wheel`, `activity_removed_from_wheel`
- `wheel_winner_detection_disagreement`, `wheel_winner_detection_feedback`, `wheel_winner_detection_success` ⭐ **NEW** (Session 39)

### User Events  
- `contract_displayed`, `contract_signed`, `custom_activity_created`

### Workflow Events
- `workflow_initiated`

### Error Events
- `frontend_error_temporary`, `frontend_error_non_critical`, `frontend_error_critical`

### System Events
- `user_session_started`, `user_session_ended`
