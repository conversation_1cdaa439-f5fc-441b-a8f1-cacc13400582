# Agent Tools Comprehensive Guide

**Last Updated**: June 13, 2025
**Status**: Comprehensive guide covering all agent tools and their integration patterns

## Overview

This document provides a comprehensive guide to the agent tools system in Goali, including findings from debugging tool call interception issues and solutions for ensuring proper tool execution tracking.

## 🔧 **Recent Critical Fixes (June 13, 2025)**

### **✅ RESOLVED: Mentor Agent Tool Integration Issues**

**Problem**: Mentor agent failed to use tools correctly, couldn't read user profiles, and didn't provide personalized responses.

**Root Cause Analysis**:
1. **Tool Parameter Wrapping Issue**: Tools expected parameters wrapped in `input_data` object
2. **LLM Prompt Missing Context**: Mentor prompts didn't include user ID or tool usage instructions
3. **Tool Result Processing**: No natural language processing of tool results

**Solutions Implemented**:

#### **1. Fixed Tool Call Parameter Structure**
**File**: `backend/apps/main/agents/mentor_agent.py`

```python
# BEFORE (Incorrect)
tool_input = {'user_profile_id': user_profile_id}

# AFTER (Correct)
tool_input = {'input_data': {'user_profile_id': user_profile_id}}
```

#### **2. Enhanced LLM Prompt with User Context**
**File**: `backend/apps/main/agents/mentor_agent.py`

```python
# Added to mentor prompt:
f"You are responding to user ID: {user_profile_id}"
f"Use the get_user_profile tool to personalize your response."
f"Convert tool results into natural, conversational language."
```

#### **3. Implemented Tool Result Processing**
**File**: `backend/apps/main/agents/mentor_agent.py`

```python
def _process_tool_results_to_response(self, tool_results, user_profile_id):
    """Convert tool results to natural language responses."""
    if 'get_user_profile' in tool_results:
        profile = tool_results['get_user_profile']
        if profile and 'name' in profile:
            return f"Your name is {profile['name']}! Nice to meet you, {profile['name']}."
    return "I'm here to help you with whatever you need."
```

#### **4. Fixed LLM Tool Call Parsing**
**File**: `backend/apps/main/agents/tools/tools_formatter.py`

```python
# Enhanced parsing for Mistral API response objects
if hasattr(response, 'choices') and response.choices:
    choice = response.choices[0]
    if hasattr(choice, 'message') and hasattr(choice.message, 'tool_calls'):
        return choice.message.tool_calls
```

**Test Results**:
- ✅ Mentor agent successfully reads user profile
- ✅ Natural language response: "Your name is PhiPhi Schmidt! Nice to meet you, PhiPhi."
- ✅ Tool calls work with proper parameter structure
- ✅ Complete conversation flow functional

**Impact**: Mentor agent now provides personalized, contextual responses based on user data from the database.

## Key Findings

### Issue: ResourceAgent Tool Outputs Empty in Benchmarks

**Problem**: ResourceAgent tool calls were being tracked by the tool interception system but showing empty `tool_output` in benchmark results, while other agents (Engagement, Psychological, Activity, Ethical) had properly populated tool outputs.

**Root Cause Analysis**:

1. **Tool Parameter Mismatch**: ResourceAgent tools expect parameters wrapped in an `input_data` object:
   ```python
   # Expected format for resource tools
   {
       "input_data": {
           "user_profile_id": "2",
           "reported_environment": ""
       }
   }
   ```
   
   But the ResourceAgent was calling tools with unwrapped parameters:
   ```python
   # Incorrect format being used
   {
       "user_profile_id": "2", 
       "reported_environment": ""
   }
   ```

2. **Missing Database Run Initialization**: Unlike other agents, ResourceAgent was not calling `self.db_service.start_run()` to create an AgentRun record and set `self.run_id`. This caused:
   - Tool execution to fail when trying to update AgentRun records
   - `run_id` to remain `None`, causing UUID validation errors
   - Tool outputs to be lost due to database update failures

**Solutions Implemented**:

1. **Fixed Tool Parameter Wrapping** in `ResourceAgent._call_tool()`:
   ```python
   async def _call_tool(self, tool_code, tool_input):
       """Call a tool and record the usage"""
       try:
           from apps.main.agents.tools.tools_util import execute_tool
           # Wrap tool_input in input_data parameter as expected by resource tools
           wrapped_input = {"input_data": tool_input}
           return await execute_tool(tool_code, wrapped_input, self.run_id)
       except Exception as e:
           return {}
   ```

2. **Added Missing Database Run Initialization** in `ResourceAgent.process()`:
   ```python
   # Convert user_profile_id to int for DB calls
   try:
       user_profile_id_int = int(self.user_profile_id)
   except ValueError:
       # Handle test/benchmark IDs
       user_profile_id_int = 1
   
   # Start a run in the database
   run = await self.db_service.start_run(
       agent_definition=self.agent_definition,
       user_profile_id=user_profile_id_int,
       input_data={"context_packet": context_packet},
       state={"workflow_id": workflow_id}
   )
   self.run_id = run.id if hasattr(run, 'id') else str(run.get('id', 'mock-run-id'))
   ```

## Tool Architecture

### Tool Parameter Patterns

Different agent tools use different parameter patterns:

1. **Resource Tools** (require `input_data` wrapper):
   - `get_environment_context`
   - `parse_time_availability` 
   - `get_available_resources`

2. **Other Agent Tools** (use direct parameters):
   - Engagement tools: `get_domain_preferences`, `get_completion_patterns`, etc.
   - Psychological tools: `analyze_psychological_state`, `get_trust_metrics`, etc.
   - Activity tools: `query_activity_catalog`, `tailor_activity`, etc.
   - Ethical tools: `validate_activity_ethics`, `validate_wheel_ethics`, etc.

### Tool Interception System

The tool interception system works correctly when:

1. **Agent has proper run_id**: Set via `self.db_service.start_run()`
2. **Tool parameters match expected format**: Varies by tool type
3. **Tool execution succeeds**: Returns proper data structures
4. **Database updates succeed**: AgentRun record can be updated with tool call info

### Agent Initialization Pattern

All agents should follow this pattern in their `process()` method:

```python
async def process(self, state: BaseModel) -> Dict[str, Any]:
    # 1. Load agent definition and tools
    await self._ensure_loaded()
    
    # 2. Convert user_profile_id to int for DB operations
    try:
        user_profile_id_int = int(self.user_profile_id)
    except ValueError:
        # Handle test/benchmark IDs
        user_profile_id_int = 1
    
    # 3. Start database run to get run_id
    run = await self.db_service.start_run(
        agent_definition=self.agent_definition,
        user_profile_id=user_profile_id_int,
        input_data=input_data,
        state={"workflow_id": workflow_id}
    )
    self.run_id = run.id if hasattr(run, 'id') else str(run.get('id', 'mock-run-id'))
    
    # 4. Execute agent logic with tool calls
    # ...
    
    # 5. Complete database run
    await self.db_service.complete_run(
        run_id=self.run_id,
        output_data=output_data,
        state=final_state,
        status='completed'
    )
```

## Debugging Tools

### Tool Call Verification

To verify tool calls are working correctly:

```python
# Check recent AgentRun records
from apps.main.models import AgentRun
recent_runs = AgentRun.objects.filter(started_at__gte=recent_time).order_by('-started_at')
for run in recent_runs:
    print(f'{run.id} - Agent: {run.agent.role} - User: {run.user_profile_id} - Status: {run.status}')

# Check benchmark tool outputs
from apps.main.models import BenchmarkRun
latest_run = BenchmarkRun.objects.latest('execution_date')
agents_data = latest_run.agent_communications.get('agents', [])
for agent in agents_data:
    tool_calls = agent.get('execution_context', {}).get('tool_calls', [])
    for tool_call in tool_calls:
        print(f"Tool: {tool_call.get('tool_name')}")
        print(f"Output keys: {list(tool_call.get('tool_output', {}).keys())}")
```

### Tool Execution Testing

To test tool execution directly:

```python
from apps.main.agents.tools.tools_util import execute_tool

# Test resource tools (require input_data wrapper)
result = await execute_tool(
    "get_environment_context",
    {
        "input_data": {
            "user_profile_id": "2",
            "reported_environment": ""
        }
    },
    run_id="valid-uuid-here"
)

# Test other tools (direct parameters)
result = await execute_tool(
    "get_domain_preferences", 
    {
        "user_profile_id": "2"
    },
    run_id="valid-uuid-here"
)
```

## Best Practices

1. **Always initialize run_id**: Call `start_run()` before making tool calls
2. **Use correct parameter format**: Check tool implementation for expected format
3. **Handle errors gracefully**: Provide fallback data if tools fail
4. **Verify tool outputs**: Check benchmark results to ensure tools are working
5. **Test in isolation**: Use debug scripts to test individual components
6. **Check database records**: Verify AgentRun records are being created

## Common Issues

1. **Empty tool outputs**: Usually caused by missing run_id or parameter format mismatch
2. **UUID validation errors**: Caused by invalid or missing run_id
3. **Silent tool failures**: Check exception handling in agent code, **nothing** here should go silent !
4. **Missing AgentRun records**: Agent not calling start_run() properly
5. **Tool interception not working**: Check if agent has _call_tool method and interception is applied

