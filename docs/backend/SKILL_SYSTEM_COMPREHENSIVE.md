# Skill System Comprehensive Documentation

## Overview

The Goali skill system is a sophisticated architecture designed to calculate the precise challengingness of activities for individual users. It models skills as compositions of fundamental attributes, influenced by personality traits, and applied across different domains with varying effectiveness.

## Core Architecture

### 1. Hierarchical Skill Model

The skill system uses a multi-layered approach:

```
SkillAttribute (Fundamental Components)
    ↓ (composed into)
SkillDefinition (Composite Skills)
    ↓ (applied to)
GenericDomain (Activity Domains)
    ↓ (used by)
GenericSkill (User-Facing Skills)
    ↓ (instantiated as)
Skill (User-Specific Skills)
```

### 2. Key Models and Relationships

#### SkillAttribute
**Purpose**: Fundamental cognitive, physical, social, emotional, creative, and technical components
**Examples**: Analytical Reasoning, Physical Strength, Empathic Understanding, Creative Thinking

**Key Fields**:
- `code`: Unique identifier (e.g., 'cog_analytical', 'phys_strength')
- `name`: Human-readable name
- `description`: Detailed explanation
- `base_decay_rate`: How quickly it deteriorates (0-100)
- `development_difficulty`: Inherent difficulty to develop (1-100)
- `development_timeframe`: Time needed to develop ('immediate', 'short', 'medium', 'long', 'lifetime')

#### SkillDefinition
**Purpose**: Composite skills made from weighted combinations of attributes
**Examples**: Creative Writing, Public Speaking, Programming, Leadership

**Key Fields**:
- `code`: Unique identifier (e.g., 'creative_writing', 'programming')
- `name`: Human-readable name
- `description`: Detailed explanation
- `tags`: Flexible categorization (JSON array)

**Relationships**:
- `attribute_compositions`: Many-to-many with SkillAttribute through SkillAttributeComposition
- `domain_applications`: Many-to-many with GenericDomain through SkillDomainApplication

#### GenericSkill
**Purpose**: User-facing skill definitions that reference SkillDefinitions
**Note**: Currently missing `name` field but referenced in `__str__` method (needs fixing)

**Key Fields**:
- `code`: Unique identifier
- `description`: Brief description
- `base_difficulty`: Baseline difficulty to acquire (1-100)
- `development_timeframe`: Time to develop
- `decay_rate`: Deterioration rate without practice (0-100)

**Relationships**:
- `domains`: Many-to-many with GenericDomain through SkillDomainRelationship
- `prerequisites`: Self-referencing many-to-many for skill dependencies
- `parent_skill`: Self-referencing foreign key for skill hierarchies
- `related_traits`: Many-to-many with GenericTrait through SkillTraitRelationship

#### Skill (User-Specific)
**Purpose**: Individual user's proficiency in specific skills

**Key Fields**:
- `level`: Proficiency level (0-100)
- `user_awareness`: User's awareness of their proficiency (0-100)
- `user_enjoyment`: Enjoyment level (0-100)
- `acquisition_date`: When skill was acquired
- `practice_frequency`: How often practiced
- `formal_training`: Whether formally trained
- `contexts`: JSON field for application contexts

### 3. Relationship Models

#### SkillAttributeComposition
**Purpose**: Defines how attributes combine to form skills with specific weights
**Key Field**: `weight` - Relative importance (0.1-10.0, higher = more important)

#### SkillDomainApplication
**Purpose**: Defines how skills apply to domains with contextual parameters
**Key Fields**:
- `relevance`: How relevant skill is to domain (10-100)
- `transfer_coefficient`: Effectiveness multiplier (0.1-2.0)
- `domain_specific_properties`: JSON field for domain-specific parameters

#### SkillTraitRelationship
**Purpose**: Maps how personality traits affect skill acquisition/application
**Key Field**: `impact` - Effect strength (-3 to +3, negative = hindering, positive = beneficial)

#### AttributeTraitInfluence
**Purpose**: Defines how traits influence fundamental attributes
**Key Field**: `impact` - Effect strength (-3 to +3)

## Challengingness Calculation System

### 1. Multi-Factor Analysis

The system calculates activity challengingness by considering:

1. **Required Skills**: What skills the activity demands
2. **User Proficiency**: User's current skill levels
3. **Trait Influences**: How user's personality affects skill application
4. **Domain Context**: How skills transfer to the activity's domain
5. **Skill Interactions**: How skills complement or conflict with each other

### 2. Calculation Flow

```
1. Activity Analysis
   ↓
2. Required Skills Identification
   ↓
3. User Skill Proficiency Lookup
   ↓
4. Trait Influence Application
   ↓
5. Domain Transfer Coefficient Application
   ↓
6. Skill Interaction Modeling
   ↓
7. Final Challengingness Score
```

### 3. Precision Factors

- **Attribute Composition**: Skills broken down to fundamental components
- **Trait Modifiers**: Personality influences on each attribute
- **Domain Specificity**: Context-aware skill effectiveness
- **Temporal Factors**: Skill decay and development timeframes
- **User Awareness**: Self-perception vs. actual ability

## Data Seeding Architecture

### Current Implementation (seed_db_50_skill_system.py)

The seeding system creates:

1. **Fundamental Attributes** (26 total):
   - Cognitive: Analytical, Creative, Spatial, Verbal, Numerical, Memory, Attention
   - Physical: Strength, Endurance, Flexibility, Balance, Fine Motor
   - Social: Empathy, Expression, Persuasion, Conflict Resolution, Leadership
   - Emotional: Awareness, Regulation, Resilience, Mindfulness, Expression
   - Creative: Visual, Verbal, Musical, Kinesthetic
   - Technical: Digital Literacy, Mechanical Aptitude, Project Organization

2. **Trait Influences** (40+ mappings):
   - HEXACO traits → Skill attributes
   - Impact values from -3 to +3

3. **Skill Definitions** (10 sample skills):
   - Creative Writing, Public Speaking, Problem Solving
   - Visual Art, Physical Fitness, Emotional Intelligence
   - Programming, Leadership, Meditation, Negotiation

4. **Domain Applications** (70+ mappings):
   - Skills → Domains with relevance and transfer coefficients

### Integration with Catalog Architecture

**Current Status**: Skills are NOT yet integrated into the catalog system
**Needed**: Extract skill data to JSON catalog following docs/backend/CATALOG_ARCHITECTURE.md

## Admin Interface Requirements

### 1. GenericSkill Management Needs

- **Comprehensive Form**: All fields with proper widgets
- **Relationship Display**: Clear visualization of related domains, traits, prerequisites
- **Hierarchy Visualization**: Parent/child skill relationships
- **Bulk Operations**: Import/export, batch updates
- **Validation**: Code uniqueness, relationship consistency

### 2. Related Model Integration

- **Inline Editing**: SkillDomainRelationship, SkillTraitRelationship
- **Attribute Composition**: If GenericSkill links to SkillDefinition
- **User Skills**: View related user-specific skills

### 3. User Experience Features

- **Search and Filter**: By domain, trait, difficulty, timeframe
- **Visual Relationships**: Graphical display of skill networks
- **Validation Feedback**: Real-time validation of relationships
- **Import/Export**: JSON-based data management

## Technical Issues Identified

### 1. GenericSkill Model Issues

- **Missing Name Field**: `__str__` method references `self.name` but field doesn't exist
- **Inconsistent Architecture**: Unclear relationship with SkillDefinition
- **Documentation Gap**: Model docstring doesn't match actual fields

### 2. Catalog Integration Gap

- **No JSON Catalog**: Skills still use hardcoded seeding data
- **Validation Missing**: No integration with CatalogValidationService
- **Inconsistent Pattern**: Other models use catalog architecture, skills don't

### 3. Admin Interface Gap

- **No Admin Entry**: GenericSkill not accessible in admin interface
- **Missing Templates**: No specialized admin templates for skill management
- **No Static Files**: No dedicated CSS/JS for skill admin interface

## Recommendations

### 1. Immediate Fixes

1. Add `name` field to GenericSkill model
2. Fix `__str__` method
3. Add GenericSkill to admin interface
4. Create basic admin templates

### 2. Catalog Integration

1. Extract skill data to JSON catalog
2. Update seeding command to use catalog
3. Integrate with CatalogValidationService
4. Follow established catalog patterns

### 3. Enhanced Admin Interface

1. Create comprehensive admin forms
2. Implement relationship visualization
3. Add bulk operations
4. Follow Django static file organization guidelines

### 4. Documentation Updates

1. Update model docstrings
2. Create admin interface documentation
3. Update AI-ENTRYPOINT.md with skill management tools
4. Document challengingness calculation algorithms

## Next Steps

1. **Fix Model Issues**: Add name field, fix __str__ method
2. **Create Admin Interface**: Basic functionality first
3. **Extract to Catalog**: Follow established patterns
4. **Enhance UX**: Rich admin interface with visualizations
5. **Test Integration**: Validate challengingness calculations
6. **Document Everything**: Comprehensive documentation updates

This skill system represents the core intelligence of Goali's activity recommendation engine, enabling precise matching of activities to user capabilities and preferences.
