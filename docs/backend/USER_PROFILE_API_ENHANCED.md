# Enhanced User Profile API Documentation

## Overview

The Enhanced User Profile API provides comprehensive access to user profile data, environments, and inventory through a robust, secure, and performant API system. This documentation covers the Phase 2 implementation that replaced placeholder data with real API integration.

## API Endpoints

### User Profile API

#### Get User Profile Details
```
GET /admin/user-profiles/api/{profile_id}/
```

**Enhanced Features:**
- Includes beliefs, traits, and trust level data
- Comprehensive statistics
- Optimized database queries
- 5-minute caching for performance
- Proper error handling

**Response Structure:**
```json
{
  "id": 1,
  "profile_name": "User Profile Name",
  "user": {...},
  "demographics": {...},
  "environments": [...],
  "skills": [...],
  "preferences": [...],
  "beliefs": [
    {
      "id": 1,
      "content": "Belief content",
      "strength": 85,
      "certainty": 90,
      "life_impact": 75,
      "emotionality": 60,
      "stability": 80,
      "user_awareness": 95,
      "last_updated": "2024-01-01T00:00:00Z",
      "generic_belief": {
        "name": "Generic Belief Name",
        "description": "Description"
      }
    }
  ],
  "traits": [
    {
      "id": 1,
      "strength": 75,
      "awareness": 80,
      "generic_trait": {
        "code": "TRAIT_CODE",
        "name": "Trait Name",
        "description": "Description",
        "trait_type": "PERSONALITY"
      }
    }
  ],
  "trust_level": {
    "value": 85,
    "aggregate_type": "OVERALL",
    "aggregate_id": null,
    "notes": "Trust level notes"
  },
  "statistics": {
    "total_environments": 3,
    "total_skills": 12,
    "total_resources": 47,
    "total_preferences": 8,
    "total_beliefs": 15,
    "total_traits": 20,
    "total_history_events": 156,
    "profile_completeness": 85
  }
}
```

### Environment API

#### Get All Environments
```
GET /admin/user-profiles/api/{profile_id}/environments/
```

#### Get Specific Environment
```
GET /admin/user-profiles/api/{profile_id}/environments/{environment_id}/
```

**Features:**
- Complete environment data including physical properties, social context, activity support, and psychological qualities
- Related resources and domains
- Optimized queries with select_related and prefetch_related

**Response Structure:**
```json
{
  "profile_id": 1,
  "environments": [
    {
      "id": 1,
      "environment_name": "Home Office",
      "environment_description": "Personal workspace",
      "is_current": true,
      "effective_start": "2024-01-01T00:00:00Z",
      "effective_end": null,
      "environment_details": {...},
      "generic_environment": {
        "name": "Office Space",
        "description": "Professional workspace"
      },
      "physical_properties": {
        "rurality": 20,
        "noise_level": 25,
        "light_quality": 85,
        "temperature_range": 70,
        "accessibility": 90,
        "air_quality": 80,
        "has_natural_elements": true,
        "surface_type": "HARD",
        "water_proximity": 10,
        "space_size": 75
      },
      "social_context": {
        "privacy_level": 90,
        "typical_occupancy": 1,
        "social_interaction_level": 20,
        "formality_level": 60,
        "safety_level": 95
      },
      "activity_support": {
        "equipment_availability": 85,
        "space_flexibility": 70,
        "interruption_likelihood": 15,
        "focus_conduciveness": 90,
        "collaboration_support": 40
      },
      "psychological_qualities": {
        "comfort_level": 95,
        "inspiration_level": 80,
        "stress_level": 10,
        "energy_impact": 85,
        "mood_impact": 90
      },
      "resources": [...],
      "domains": [...]
    }
  ],
  "total_environments": 1
}
```

### Inventory API

#### Get User Inventory
```
GET /admin/user-profiles/api/{profile_id}/inventory/
```

**Features:**
- Complete inventory data with resources categorized by type
- Mobile and temporary inventory indicators
- Resource condition and availability information
- Statistics and categorization

**Response Structure:**
```json
{
  "profile_id": 1,
  "inventories": [
    {
      "id": 1,
      "inventory_name": "Home Inventory",
      "inventory_description": "Personal items at home",
      "inventory_type": "Environment-based",
      "location_details": "Home Office",
      "is_mobile": false,
      "is_temporary": false,
      "effective_start": "2024-01-01T00:00:00Z",
      "effective_end": null,
      "resources": [...]
    }
  ],
  "resources_by_category": {
    "Tools": [...],
    "Electronics": [...],
    "Books": [...]
  },
  "statistics": {
    "total_inventories": 3,
    "total_resources": 47,
    "mobile_inventories": 1,
    "temporary_inventories": 0,
    "resource_categories": 8
  }
}
```

## Security Features

### Authentication & Authorization
- Staff-only access to all admin API endpoints
- Proper authentication checks in dispatch methods
- CSRF protection for state-changing operations

### Input Validation
- Profile ID parameter validation
- Type checking for all API parameters
- Proper URL parameter validation
- Input sanitization for security

## Performance Optimizations

### Caching
- 5-minute cache for profile data
- Cache refresh parameter (`?refresh=true`)
- Efficient cache key management

### Database Optimization
- `select_related` for foreign key relationships
- `prefetch_related` for many-to-many and reverse foreign key relationships
- Optimized queries to minimize database hits

## Error Handling

### HTTP Status Codes
- `200` - Success
- `400` - Bad Request (missing parameters)
- `404` - Not Found (invalid profile ID)
- `403` - Forbidden (insufficient permissions)
- `500` - Internal Server Error

### Error Response Format
```json
{
  "error": "User profile not found"
}
```

## Frontend Integration

### Modal System
The enhanced API integrates with three main modals:

1. **User Profile Detail Modal** - Displays comprehensive profile information
2. **Environment Detail Modal** - Shows detailed environment data
3. **Inventory Detail Modal** - Presents inventory and resource information

### JavaScript Integration
- Real API calls replacing placeholder data
- Proper loading states and error handling
- CSRF token handling
- Profile ID context sharing between modals

### Helper Functions
- `getCurrentProfileId()` - Retrieves profile ID from context
- `getCsrfToken()` - Gets CSRF token for API calls
- Description helpers for environment properties

## Testing

### Comprehensive Test Suite
The system includes a comprehensive test suite (`test_enhanced_api.py`) that validates:

- Enhanced profile API functionality
- Environment API endpoints
- Inventory API endpoints
- Error handling scenarios
- Authentication and permissions

### Test Results
All tests pass successfully, confirming:
- ✅ Enhanced profile API working correctly
- ✅ Environment API working correctly
- ✅ Inventory API working correctly
- ✅ Error handling working properly

## Usage Examples

### Fetching Profile Data
```javascript
const response = await fetch(`/admin/user-profiles/api/${profileId}/`, {
    method: 'GET',
    headers: {
        'X-CSRFToken': getCsrfToken(),
        'Content-Type': 'application/json',
    }
});

if (response.ok) {
    const profileData = await response.json();
    renderProfileData(profileData);
}
```

### Fetching Environment Data
```javascript
const response = await fetch(`/admin/user-profiles/api/${profileId}/environments/`, {
    method: 'GET',
    headers: {
        'X-CSRFToken': getCsrfToken(),
        'Content-Type': 'application/json',
    }
});

if (response.ok) {
    const environmentData = await response.json();
    renderEnvironmentData(environmentData);
}
```

## Future Enhancements

### Planned Improvements
1. **Advanced Caching** - Redis-based caching with cache invalidation
2. **Rate Limiting** - Per-user API throttling
3. **API Versioning** - Backward compatibility support
4. **Real-time Features** - WebSocket support for live updates
5. **Advanced Security** - OAuth2 authentication and field-level permissions
6. **Monitoring** - API usage analytics and performance monitoring

## Conclusion

The Enhanced User Profile API provides a robust, secure, and performant foundation for user profile management. With comprehensive error handling, performance optimizations, and real API integration, the system is production-ready and provides an excellent foundation for future enhancements.
