# Conversation State Management System

## Overview

The Conversation State Management System provides stateful conversation handling using browser session storage combined with return-based state updates from workflows to the ConversationDispatcher. This enables context-aware message processing and improved conversation flow.

## Architecture

### Frontend Components

#### ConversationState Utility (`frontend/src/utils/conversationState.js`)

A utility class that manages conversation state in browser session storage:

```javascript
import { ConversationState } from '../utils/conversationState.js';

// Basic operations
ConversationState.set(state);           // Set complete state
ConversationState.get();                // Get current state
ConversationState.update(updates);      // Partial updates
ConversationState.clear();              // Clear state

// Helper methods
ConversationState.getField(key, default);
ConversationState.setField(key, value);
ConversationState.isInPhase(phase);
ConversationState.isAwaitingResponse(type);
```

#### Message Handler Integration

The message handler automatically includes conversation state in outgoing messages and processes incoming state updates:

```javascript
// Outgoing messages include state
const metadata = {
    conversation_phase: conversationState.phase || 'initial',
    awaiting_response_type: conversationState.awaiting_response_type,
    last_workflow: conversationState.last_workflow,
    session_context: conversationState.context || {}
};

// Incoming state updates are processed
case 'conversation_state_update':
    ConversationState.update(data.updates);
    break;
```

### Backend Components

#### ConversationDispatcher Updates

Enhanced with state-aware message classification:

```python
# Extract conversation state from metadata
metadata = user_message.get('metadata', {})
conversation_phase = metadata.get('conversation_phase', 'initial')
awaiting_response = metadata.get('awaiting_response_type')
session_context = metadata.get('session_context', {})

# State-aware classification
if awaiting_response:
    classification = await self._handle_follow_up_message(...)
else:
    classification = await self._classify_message(...)
```

#### Follow-up Message Handling

The `_handle_follow_up_message` method routes messages based on expected response types:

- `profile_info` → `profile_completion` workflow
- `situation_info` → `discussion` workflow  
- `activity_selection` → `post_activity` workflow
- `activity_feedback` → `post_activity` workflow

#### WebSocket Consumer

Added `conversation_state_update` handler to send state updates to frontend:

```python
async def conversation_state_update(self, event):
    await self.send(text_data=json.dumps({
        'type': 'conversation_state_update',
        'updates': event['updates']
    }))
```

### Workflow Integration

#### State Updates in Workflow Results

Each workflow now includes `conversation_state_updates` in its return value:

```python
# Discussion workflow example
processed_result["conversation_state_updates"] = {
    'phase': 'awaiting_reflection',
    'awaiting_response_type': 'reflection_answer',
    'context': {
        'guidance_provided': True,
        'last_question': result.output_data.get('question')
    }
}
```

#### Workflow Result Handler

The workflow result handler automatically sends state updates via WebSocket:

```python
if 'conversation_state_updates' in enhanced_result:
    await self.channel_layer.group_send(
        user_ws_session,
        {
            'type': 'conversation_state_update',
            'updates': enhanced_result['conversation_state_updates']
        }
    )
```

## State Schema

### Core State Fields

- **`phase`**: Current conversation phase
  - `initial` - Starting state
  - `awaiting_profile_info` - Waiting for profile information
  - `awaiting_situation_info` - Waiting for situation details
  - `awaiting_activity_selection` - Waiting for activity choice
  - `awaiting_activity_feedback` - Waiting for activity feedback

- **`awaiting_response_type`**: Expected next input type
  - `profile_info` - Profile completion response
  - `situation_info` - Situation clarification
  - `activity_selection` - Activity choice
  - `activity_feedback` - Activity feedback

- **`last_workflow`**: Previously executed workflow
- **`context`**: Workflow-specific context data

### Phase Transitions

```mermaid
graph TD
    A[initial] --> B[awaiting_profile_info]
    B --> A
    A --> C[awaiting_situation_info]
    C --> A
    A --> D[awaiting_activity_selection]
    D --> E[awaiting_activity_feedback]
    E --> A
```

## Implementation Details

### State Validation

The system includes automatic state validation:

```javascript
static validateState(state) {
    const validPhases = [
        'initial', 'awaiting_profile_info', 'awaiting_situation_info',
        'awaiting_activity_selection', 'awaiting_activity_feedback'
    ];
    
    const validResponseTypes = [
        'profile_info', 'situation_info', 
        'activity_selection', 'activity_feedback'
    ];
    
    // Validation logic...
}
```

### Error Handling

- Invalid states fallback to `initial` phase
- Unknown response types trigger normal classification
- State format errors are logged and corrected
- WebSocket failures are handled gracefully

### Testing

#### Frontend Testing

Use `frontend/test_conversation_state.html` for interactive testing:

- Basic operations (set/get/update)
- State validation
- Phase transitions
- Conversation scenarios

#### Backend Testing

Run `backend/test_conversation_state_system.py` for automated testing:

- Follow-up message handling
- Normal message classification
- State validation and fallback

## Usage Examples

### Onboarding Flow

```javascript
// Initialize onboarding
ConversationState.initialize({
    phase: 'awaiting_profile_info',
    awaiting_response_type: 'profile_info',
    context: {
        completion_percentage: 0.3,
        next_question_category: 'basic_info'
    }
});

// Progress tracking
ConversationState.updateContext({
    completion_percentage: 0.8
});

// Complete onboarding
ConversationState.update({
    phase: 'initial',
    awaiting_response_type: null,
    context: { profile_completion: 1.0 }
});
```

### Activity Selection Flow

```javascript
// After wheel generation
ConversationState.update({
    phase: 'awaiting_activity_selection',
    awaiting_response_type: 'activity_selection',
    last_workflow: 'wheel_generation',
    context: {
        wheel_generated: true,
        activity_count: 4
    }
});

// After activity selection
ConversationState.update({
    phase: 'awaiting_activity_feedback',
    awaiting_response_type: 'activity_feedback'
});
```

## Best Practices

1. **Always validate state** before processing
2. **Use helper methods** for common operations
3. **Clear state** on authentication changes
4. **Log state transitions** for debugging
5. **Handle WebSocket failures** gracefully
6. **Test state transitions** thoroughly

## Troubleshooting

### Common Issues

1. **State not persisting**: Check session storage availability
2. **Invalid transitions**: Verify phase validation logic
3. **WebSocket failures**: Check connection and error handling
4. **Classification errors**: Verify follow-up message handling

### Debug Tools

- `ConversationState.debug()` - Log current state
- Browser dev tools - Inspect session storage
- WebSocket dashboard - Monitor state updates
- Backend logs - Track state processing

## Migration Notes

### From Legacy System

The new system replaces hardcoded conversation flows with dynamic state management:

- **Before**: Fixed message classification
- **After**: State-aware classification with follow-up handling

### Workflow Updates

All workflows now return conversation state updates:

- Discussion workflow: Reflection states
- Profile completion workflow: Progress tracking  
- Wheel generation workflow: Activity selection states
- Post-activity workflow: Feedback collection states

## Future Enhancements

1. **Persistent state storage** for cross-session continuity
2. **State analytics** for conversation flow optimization
3. **Advanced validation** with custom rules
4. **State machine visualization** for debugging
5. **Multi-user state management** for group conversations
