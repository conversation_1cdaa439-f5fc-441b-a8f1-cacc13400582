# Domain Harmonization Implementation Plan

## Overview

This document outlines the comprehensive plan to harmonize activity domain management across the Goali codebase, eliminating the current architectural inconsistencies and establishing the GenericDomain model as the single source of truth.

## Current Issues Summary

### 🚨 Critical Problems Identified

1. **Multiple Domain Definition Systems**
   - Pydantic ActivityDomain enum (8 domains)
   - GenericDomain model (101 domains across 10 categories)
   - Hardcoded mappings in converters and services

2. **Invalid Domain References**
   - ~70% of seeded activities use invalid domain codes
   - "wellness" domain doesn't exist in model
   - Inconsistent naming patterns

3. **No Validation Pipeline**
   - Domain codes not validated during seeding
   - Silent failures lead to missing relationships
   - No consistency checks across pipeline

## Implementation Strategy

### Phase 1: Foundation (COMPLETED ✅)
- [x] Create authoritative documentation
- [x] Build domain validation utility
- [x] Add validation to seeding process
- [x] Test validation system

### Phase 2: Remove Pydantic Enum Dependencies

#### 2.1 Update Activity Schemas
**File:** `backend/apps/main/schemas/activity_schemas.py`

**Actions:**
- Remove `ActivityDomain` enum entirely
- Update schema fields to use string validation against GenericDomain
- Create dynamic domain validation using database

**Implementation:**
```python
# BEFORE
class ActivityDomain(str, Enum):
    WELLNESS = "wellness"
    # ... other domains

# AFTER  
def get_valid_domain_codes() -> List[str]:
    """Get valid domain codes from GenericDomain model."""
    from apps.activity.models import GenericDomain
    return list(GenericDomain.objects.values_list('code', flat=True))

class GenericActivitySchema(BaseModel):
    domain: str = Field(default="general", 
                       description="Domain code - must exist in GenericDomain model")
    
    @validator('domain')
    def validate_domain(cls, v):
        valid_domains = get_valid_domain_codes()
        if v not in valid_domains:
            raise ValueError(f"Invalid domain code: {v}")
        return v
```

#### 2.2 Update Activity Converters
**File:** `backend/apps/main/schemas/activity_converters.py`

**Actions:**
- Remove hardcoded domain mappings (100+ lines)
- Use GenericDomain model for domain extraction
- Integrate with domain_validator utility

**Implementation:**
```python
# BEFORE
domain_mapping = {
    'wellness': ActivityDomain.WELLNESS,
    # ... 100+ hardcoded mappings
}

# AFTER
@staticmethod
def _extract_domain_from_generic_activity(generic_activity) -> str:
    """Extract domain code from GenericActivity."""
    primary_domain = generic_activity.get_primary_domain()
    return primary_domain.code if primary_domain else "general"
```

#### 2.3 Update Domain Management Service
**File:** `backend/apps/main/services/domain_management_service.py`

**Actions:**
- Remove ActivityDomain enum dependencies
- Use string domain codes throughout
- Integrate with domain_validator for validation

### Phase 3: Fix Seeding Data

#### 3.1 Automated Domain Correction
**Status:** Partially completed - validation added

**Remaining Actions:**
- Apply domain_validator corrections to all 99 activities
- Update invalid domain codes systematically
- Test seeding process with corrected data

#### 3.2 Seeding Data Patterns to Fix

**Leisure Domains:**
```python
# INVALID → VALID
'leisure_relaxation' → 'leisure_relax'
'leisure_entertainment' → 'leisure_entertain'  
'leisure_games' → 'leisure_play'
'leisure_digital' → 'explor_digital'
'leisure_crafting' → 'creative_craft'
```

**Productive Domains:**
```python
# INVALID → VALID
'productive_practical' → 'prod_organize'
'productive_planning' → 'prod_organize'
'productive_habits' → 'prod_habit'
'productive_skill' → 'prod_skill'
```

**Emotional Domains:**
```python
# INVALID → VALID
'emotional_awareness' → 'emot_aware'
'emotional_expression' → 'emot_express'
'emotional_regulation' → 'emot_regulate'
```

### Phase 4: Update Frontend Integration

#### 4.1 Domain Color Management
**Current Issue:** Hardcoded domain colors in frontend

**Solution:**
- Move domain color definitions to GenericDomain model
- Create API endpoint for domain metadata
- Update frontend to fetch domain colors dynamically

#### 4.2 Wheel Generation Pipeline
**Files to Update:**
- Wheel generation workflows
- Activity tailoring services
- Frontend wheel components

### Phase 5: Testing and Validation

#### 5.1 Comprehensive Testing
- Unit tests for domain validation
- Integration tests for seeding process
- End-to-end tests for wheel generation
- Performance tests for domain queries

#### 5.2 Data Migration Validation
- Verify all activities have valid domain relationships
- Check domain distribution across categories
- Validate wheel generation with new domain system

## Implementation Checklist

### Immediate Actions (Next Steps)
- [ ] Remove ActivityDomain enum from activity_schemas.py
- [ ] Update activity converters to use GenericDomain model
- [ ] Fix remaining invalid domain codes in seeding file
- [ ] Update domain management service
- [ ] Test complete seeding process

### Medium-term Actions
- [ ] Update frontend domain color management
- [ ] Create domain metadata API endpoints
- [ ] Update wheel generation pipeline
- [ ] Add comprehensive test coverage

### Long-term Actions
- [ ] Performance optimization for domain queries
- [ ] Domain analytics and reporting
- [ ] Domain recommendation system
- [ ] Advanced domain relationship features

## Success Metrics

### Technical Metrics
- ✅ 100% domain validation success rate
- ✅ Zero hardcoded domain mappings
- ✅ Single source of truth (GenericDomain model)
- [ ] All activities have valid domain relationships
- [ ] Wheel generation uses correct domains

### Quality Metrics
- [ ] Improved domain consistency across pipeline
- [ ] Reduced domain-related bugs
- [ ] Better wheel generation quality
- [ ] Cleaner, more maintainable code

## Risk Mitigation

### Potential Risks
1. **Breaking Changes:** Removing enum may break existing code
2. **Performance Impact:** Dynamic domain validation may be slower
3. **Data Loss:** Incorrect domain mappings could lose relationships

### Mitigation Strategies
1. **Gradual Migration:** Phase implementation to minimize disruption
2. **Caching:** Cache domain codes for performance
3. **Backup & Validation:** Backup data and validate all changes
4. **Rollback Plan:** Maintain ability to revert changes

## Timeline

### Week 1: Core Infrastructure
- Complete Pydantic enum removal
- Update converters and services
- Fix critical seeding data issues

### Week 2: Integration & Testing
- Update frontend integration
- Comprehensive testing
- Performance optimization

### Week 3: Validation & Deployment
- End-to-end validation
- Documentation updates
- Production deployment

## Conclusion

This harmonization plan will establish a robust, consistent domain management system that:
- Uses GenericDomain model as single source of truth
- Provides comprehensive validation throughout the pipeline
- Eliminates architectural inconsistencies
- Improves code maintainability and quality
- Ensures accurate wheel generation with proper domain relationships

The implementation follows best practices for large-scale refactoring while minimizing risk and maintaining system stability.
