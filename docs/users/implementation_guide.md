# User Profile Import Implementation Guide

## Architecture Overview

This implementation uses a clean, minimal schema approach focused on structural validation and straightforward JSON processing:

```
JSON Schema (Minimal) → Pydantic Business Objects → Import Service → Database Models
```

### Design Principles
- **Minimal Schema Complexity**: Direct JSON schema without OpenAPI wrapper overhead
- **Clear Validation**: Structural validation with descriptive error messages
- **Single Transaction**: Atomic profile creation with full rollback capability
- **Extensible Design**: Easy to add new profile components without breaking changes
- **Code Clarity**: Readable, maintainable code with clear data flow

## Layer 1: Schema and Business Objects

### File Structure
Create: `backend/apps/user/services/user_profile_business_objects.py`

This file contains Pydantic models that exactly mirror the JSON schema structure. Key design decisions:

#### Schema Simplification
- **No OpenAPI Wrapper**: Direct JSON schema eliminates validation complexity
- **Flat Component Structure**: All profile components at same level
- **Clear Field Descriptions**: Rich descriptions enable better validation error messages
- **Consistent Naming**: Field names match database model fields where possible

#### Business Object Strategy
- **Mirror Schema Exactly**: Each Pydantic model matches a schema object type
- **Nested Validation**: Complex objects like environment properties validated at each level
- **Array Validation**: Trait arrays, skill arrays, etc. validated both structurally and semantically
- **Cross-Field Validation**: Validate relationships between fields (e.g., effective_start < effective_end)

### Key Business Objects

#### UserProfileImportRequest (Root Object)
- Main container with all profile components
- Required: user_account, profile_name, demographics
- Optional: All other components (environment, traits, beliefs, etc.)
- Validation: Ensure internal consistency across components

#### UserAccountBO
- Username validation (alphanumeric + underscore, 3-150 chars)
- Email format validation
- Password strength validation (minimum 8 chars)
- Integration with Django User model fields

#### DemographicsBO
- Age range validation (13-120)
- Required string fields with length limits
- Validation of location formats and language codes

#### UserEnvironmentBO
- Complex nested structure with four sub-objects
- Date validation for effective periods
- Boolean validation for is_current flag
- Integration with generic environment codes

#### Array Component Objects
- **TraitInclinationBO**: HEXACO trait validation with strength/awareness ranges
- **BeliefBO**: Belief statements with strength/certainty/impact scores
- **AspirationBO/IntentionBO**: Goal objects with importance/commitment scoring
- **SkillBO**: Skill definitions with level/awareness/enjoyment scoring
- **ResourceBO/LimitationBO**: Asset and constraint definitions
- **PreferenceBO**: Preference patterns with strength ranges including negative values

#### State Objects
- **CurrentMoodBO**: Current psychological state with four required dimensions
- **TrustLevelBO**: Trust metrics with overall value and domain-specific scores

## Layer 2: Import Service Enhancement

### File Enhancement
Extend: `backend/apps/user/services/profile_import_service.py`

### Import Process Architecture

#### Phase 1: Validation and Parsing
1. **Schema Validation**: Use jsonschema library to validate against minimal schema
2. **Business Object Creation**: Parse validated JSON into Pydantic business objects
3. **Semantic Validation**: Check trait codes, skill codes, resource codes against database
4. **Consistency Validation**: Cross-component validation (e.g., environment supports stated activities)

#### Phase 2: Database Transaction Setup
1. **Transaction Initiation**: Begin atomic database transaction
2. **User Account Resolution**: Create Django User or validate existing
3. **Generic Entity Caching**: Cache lookups for traits, skills, resources, environments
4. **Dependency Preparation**: Prepare all foreign key relationships

#### Phase 3: Entity Creation Sequence
1. **Core Profile**: Create UserProfile with basic information
2. **Demographics**: Create Demographics linked to UserProfile
3. **Environment Setup**: Create UserEnvironment with all nested properties
4. **Trait System**: Create UserTraitInclination records for each trait
5. **Belief System**: Create Belief records with evidence and influences
6. **Goal Structure**: Create Aspiration and Intention records
7. **Capability Mapping**: Create Skill records with proficiency data
8. **Resource Inventory**: Create UserResource records linked to environment
9. **Constraint Mapping**: Create UserLimitation records
10. **Preference System**: Create Preference records across domains
11. **Current State**: Set CurrentMood and TrustLevel

#### Phase 4: Relationship Finalization
1. **Foreign Key Linking**: Establish all database relationships
2. **Current Environment Setting**: Link UserProfile.current_environment
3. **Validation Pass**: Final consistency checks
4. **Transaction Commit**: Commit all changes atomically

### Error Handling Strategy

#### Validation Error Types
- **Schema Violations**: Field type mismatches, missing required fields
- **Range Violations**: Values outside acceptable ranges (0-100 scales)
- **Reference Errors**: Invalid codes that don't exist in system
- **Consistency Errors**: Cross-field validation failures

#### Database Error Types
- **Constraint Violations**: Unique constraint failures, foreign key errors
- **Transaction Failures**: Deadlocks, timeout errors
- **Performance Issues**: Large dataset import problems

#### Error Response Format
- Structured error messages with field-level detail
- Clear indication of validation vs. database errors
- Actionable suggestions for fixing common errors
- Preservation of valid data when possible

## Layer 3: Database Integration Strategy

### Model Mapping Approach

#### Direct Field Mappings
- UserAccountBO → Django User model (username, email, first_name, last_name)
- DemographicsBO → Demographics model (all fields direct mapping)
- UserEnvironmentBO → UserEnvironment model (basic fields)

#### Complex Nested Mappings
- PhysicalProperties → UserEnvironmentPhysicalProperties model
- SocialContext → UserEnvironmentSocialContext model
- ActivitySupport → UserEnvironmentActivitySupport model
- PsychologicalQualities → UserEnvironmentPsychologicalQualities model

#### Array to Relation Mappings
- traits[] → Multiple UserTraitInclination records
- skills[] → Multiple Skill records
- resources[] → Multiple UserResource records
- limitations[] → Multiple UserLimitation records
- preferences[] → Multiple Preference records

#### Code Resolution Strategy
- trait_code → GenericTrait.objects.get(code=trait_code)
- skill_code → GenericSkill.objects.get(code=skill_code)
- generic_resource → GenericResource.objects.get(code=generic_resource)
- limitation_code → GenericUserLimitation.objects.get(code=limitation_code)

### Data Transformation Pipeline

#### Type Conversions
- String dates → Python date objects
- Integer scores → Appropriate database field types
- Boolean flags → BooleanField values
- Array fields → Related model creation

#### Validation Integration
- Use business object validation before database operations
- Leverage Django model validation as secondary check
- Custom validators for business rules (e.g., only one current environment)

## Layer 4: API Integration

### Endpoint Structure
- **POST /api/user-profiles/import**: Main import endpoint
- **GET /api/user-profiles/schema**: Serve current JSON schema
- **POST /api/user-profiles/validate**: Validation-only endpoint for testing

### Request/Response Handling
- Accept JSON matching schema exactly
- Return structured success/error responses
- Include created entity IDs in success responses
- Provide detailed field-level error information

### Authentication and Authorization
- Require staff permissions for profile import
- Log all import attempts with user attribution
- Rate limiting for import operations
- Input sanitization and validation

## Layer 5: Testing Strategy

### Unit Testing Approach
- **Business Object Validation**: Test all Pydantic model validation rules
- **Schema Compliance**: Test JSON schema validation edge cases
- **Import Logic**: Test individual import steps in isolation
- **Error Handling**: Test all error conditions with appropriate responses

### Integration Testing
- **Full Profile Import**: End-to-end import of complete profiles
- **Partial Profile Import**: Test handling of optional components
- **Database Consistency**: Verify all relationships created correctly
- **PhiPhi Profile Test**: Import existing PhiPhi seed data via new system

### Performance Testing
- **Large Profile Import**: Test complex profiles with many components
- **Bulk Import Simulation**: Test multiple profile imports
- **Memory Usage**: Monitor memory consumption during import
- **Database Performance**: Test query optimization and index usage

## Monitoring and Operational Considerations

### Logging Strategy
- **Import Events**: Log start, progress, completion of imports
- **Validation Failures**: Detailed logging of validation errors
- **Performance Metrics**: Track import duration and resource usage
- **Error Patterns**: Monitor for common import issues

### Metrics Collection
- **Success Rate**: Track successful vs. failed imports
- **Import Duration**: Monitor performance trends
- **Profile Complexity**: Track component counts and sizes
- **Error Categories**: Categorize and trend error types

### Deployment Considerations
- **Environment Configuration**: Different validation rules per environment
- **Database Migration**: Handle schema changes gracefully
- **Rollback Procedures**: Safe rollback for problematic imports
- **Performance Tuning**: Optimize for production load patterns

This implementation provides a robust foundation for user profile import while maintaining simplicity and extensibility for future enhancements.