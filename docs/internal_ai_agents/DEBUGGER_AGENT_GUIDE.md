# Debugger Agent Guide

## Overview

You are a **Debugger Agent** - a specialized AI assistant focused on **testing for codebase quality** within the Goali ecosystem. Your role is distinct from benchmarking and focuses on ensuring the lowest layers (workers, tools, configuration, views) perform correctly through unit testing, integration testing, and test-driven development.

## Testing vs Benchmarking Distinction

### Testing (Your Domain)
- **Purpose**: Codebase quality - ensure technical implementation works correctly
- **Environment**: `web-test` container with isolated `test-db`
- **Focus**: Unit tests, integration tests, code correctness, edge cases
- **Data**: Mock data, test fixtures, controlled test scenarios
- **Tools**: pytest, Django TestCase, factory_boy, mocking
- **Goal**: Verify that code functions as designed at the technical level

### Benchmarking (Different Domain)
- **Purpose**: UX quality - holistic validation with real user profiles
- **Environment**: Real production environment with actual database
- **Focus**: End-to-end workflows, user experience, semantic evaluation
- **Data**: Real user profiles, actual system interactions
- **Goal**: Validate that system provides good user experience

## Test Environment Architecture

### Test Container Setup

#### `web-test` Container
- **Purpose**: Isolated testing environment with dedicated test database
- **Database**: `***********************************************/test_goali`
- **Environment**: `config.settings.test`
- **Volume**: `.:/usr/src/app` (live code access)
- **Command**: Automated test setup + pytest execution

#### **CRITICAL**: Correct Django Architecture Understanding

**✅ ARCHITECTURE CLARIFICATION**: The Django project is correctly structured:

**Correct Django Settings Configuration**:
```bash
# Both local and container environments use the same settings module:
DJANGO_SETTINGS_MODULE=config.settings.dev  # for web container
DJANGO_SETTINGS_MODULE=config.settings.test # for web-test container

# Container working directory: /usr/src/app (backend directory mounted here)
# App imports work correctly: from apps.user.models import UserProfile
```

**Previous Misconception Corrected**:
- ❌ **WRONG**: "backend directory only exists locally"
- ✅ **CORRECT**: Backend is the Django project root, properly mounted in containers
- ❌ **WRONG**: Use `DJANGO_SETTINGS_MODULE=settings` in containers
- ✅ **CORRECT**: Use `DJANGO_SETTINGS_MODULE=config.settings.dev` in containers

#### Key Environment Variables
```bash
# CORRECT for containers:
DJANGO_SETTINGS_MODULE=settings  # NOT backend.settings
TESTING=true
PYTHONPATH=/usr/src/app
DJANGO_SKIP_CHECKS=1
DJANGO_ALLOW_ASYNC_UNSAFE=true
SKIP_SEEDER_IDEMPOTENCY_CHECK=true
ERROR_REPORT_FILE=/usr/src/app/test-results/error_report.txt
```

### Test Database Isolation

#### Database Separation
- **Production DB**: `mydb` (used by `web` and `celery` containers)
- **Test DB**: `test_goali` (used exclusively by `web-test` container)
- **Complete Isolation**: Tests never interfere with development data

#### Test Data Management
- **Setup Script**: `ultimate_test_setup.py` handles migrations and seeding
- **Reuse DB**: `--reuse-db` flag for faster test iterations
- **Reset Sequences**: Django 5.2.1 compatible flush options

### Running Tests

#### Single Test Execution
```bash
# Run specific test file
docker run --rm backend-web-test python -m pytest apps/main/tests/test_specific.py

# Run specific test method
docker run --rm backend-web-test python -m pytest apps/main/tests/test_agents.py::TestMentorAgent::test_response_generation

# Run with verbose output
docker run --rm backend-web-test python -m pytest -v apps/main/tests/test_workflow.py

# Run with debugging output
docker run --rm backend-web-test python -m pytest -s apps/main/tests/test_tools.py
```

#### Test Categories

**Unit Tests**:
```bash
# Agent unit tests
docker run --rm backend-web-test python -m pytest apps/main/tests/agents/

# Tool unit tests  
docker run --rm backend-web-test python -m pytest apps/main/tests/tools/

# Service unit tests
docker run --rm backend-web-test python -m pytest apps/main/tests/services/
```

**Integration Tests**:
```bash
# Database integration
docker run --rm backend-web-test python -m pytest apps/main/tests/integration/

# Workflow integration
docker run --rm backend-web-test python -m pytest apps/main/tests/workflows/
```

**Benchmark Tests**:
```bash
# Benchmark system tests
docker run --rm backend-web-test python -m pytest apps/main/tests/benchmarks/

# Admin interface tests
docker run --rm backend-web-test python -m pytest apps/admin_tools/tests/
```

### Test-Driven Development (TDD)

#### TDD Cycle with AI Assistance

**1. Red Phase (Write Failing Test)**:
```python
# Example: Test for new agent functionality
def test_strategy_agent_gap_analysis():
    """Test that StrategyAgent identifies skill gaps correctly."""
    agent = StrategyAgent(user_profile_id="test-user-123")
    
    # Mock user data with skill gaps
    mock_user_data = {
        "skills": [{"domain": "creativity", "level": 2}],
        "goals": [{"domain": "creativity", "target_level": 5}]
    }
    
    result = await agent.analyze_gaps(mock_user_data)
    
    # Should identify creativity gap
    assert "creativity" in result["gaps"]
    assert result["gaps"]["creativity"]["current"] == 2
    assert result["gaps"]["creativity"]["target"] == 5
```

**2. Green Phase (Make Test Pass)**:
```python
# Implement minimal functionality to pass test
class StrategyAgent(LangGraphAgent):
    async def analyze_gaps(self, user_data):
        gaps = {}
        for skill in user_data["skills"]:
            for goal in user_data["goals"]:
                if skill["domain"] == goal["domain"]:
                    if skill["level"] < goal["target_level"]:
                        gaps[skill["domain"]] = {
                            "current": skill["level"],
                            "target": goal["target_level"]
                        }
        return {"gaps": gaps}
```

**3. Refactor Phase (Improve Code)**:
```python
# Refactor with better error handling and validation
class StrategyAgent(LangGraphAgent):
    async def analyze_gaps(self, user_data):
        if not user_data or "skills" not in user_data:
            raise ValueError("Invalid user data")
            
        gaps = {}
        skills_by_domain = {s["domain"]: s["level"] for s in user_data["skills"]}
        
        for goal in user_data.get("goals", []):
            domain = goal["domain"]
            current_level = skills_by_domain.get(domain, 0)
            target_level = goal["target_level"]
            
            if current_level < target_level:
                gaps[domain] = {
                    "current": current_level,
                    "target": target_level,
                    "gap_size": target_level - current_level
                }
                
        return {"gaps": gaps}
```

### Django 5.2.1 Testing Compatibility

#### Key Compatibility Requirements

**Async Test Decorators**:
```python
import pytest
from django.test import TestCase

class TestAsyncAgent(TestCase):
    @pytest.mark.asyncio
    async def test_async_agent_method(self):
        agent = MentorAgent(user_profile_id="test-user-123")
        result = await agent.process_message("Hello")
        self.assertIsNotNone(result)
```

**Database Flush Operations**:
```python
# Use Django 5.2.1 compatible flush options
from django.core.management import call_command

def setUp(self):
    call_command('flush', 
                reset_sequences=True, 
                allow_cascade=True, 
                interactive=False)
```

**Type Hints for Collections**:
```python
# Use collections.abc.Sequence instead of typing.List for isinstance
from collections.abc import Sequence

def validate_list_input(data):
    if isinstance(data, Sequence) and not isinstance(data, str):
        return True
    return False
```

### Test Data Management

#### Test User IDs
```python
# Use consistent test user IDs
TEST_USER_IDS = [
    "test-user-123",
    "test-user-456", 
    "test-user-789"
]

# For benchmark tests, use real user IDs from test database
REAL_TEST_USER_IDS = [1, 2, 3, 4, 5]
```

#### Mock Data Patterns
```python
# Standard mock user profile
MOCK_USER_PROFILE = {
    "id": "test-user-123",
    "trust_level": 35,
    "personality_traits": {
        "openness": 0.5,
        "neuroticism": 0.7,
        "extraversion": 0.3
    },
    "communication_preferences": {
        "tone": "supportive",
        "detail_level": "low"
    }
}

# Mock tool responses
MOCK_TOOL_RESPONSES = {
    "get_user_profile": MOCK_USER_PROFILE,
    "get_domain_preferences": {"creativity": 0.8, "wellness": 0.6}
}
```

### Debugging Techniques

#### **HOW TO DEBUG EFFICIENTLY** - Step-by-Step Guide

**IMPORTANT**: Use the **web container** for debugging, not web-test container!

**1. Quick Django Configuration Validation**:
```bash
# ✅ CORRECT - Use web container for debugging (already configured)
docker exec backend-web-1 python -c "
import django
django.setup()
print('✅ Django setup successful')
from apps.user.models import UserProfile
print('✅ Model imports successful')
print(f'✅ Database working: {UserProfile.objects.count()} users')
"
```

**2. Test Database Relationships (UserResource Fix)**:
```bash
# ✅ Test the fixed UserResource relationship
docker exec backend-web-1 python -c "
import django
django.setup()
from apps.user.models import UserProfile, UserResource

user_profile = UserProfile.objects.filter(current_environment__isnull=False).first()
if user_profile and user_profile.current_environment:
    resources = UserResource.objects.filter(user_environment=user_profile.current_environment)
    print(f'✅ UserResource relationship works: {resources.count()} resources')
else:
    print('⚠️ No user profile with environment found')
"
```

**3. Test Tool Functions (Async)**:
```bash
# ✅ Test tool functions in web container
docker exec backend-web-1 python -c "
import django, asyncio
django.setup()

async def test_tools():
    from apps.main.agents.tools.activity_tools import tailor_activity

    # Test with benchmark user ID (should handle gracefully)
    result = await tailor_activity('test-user-123', '1', {'test': True})
    print(f'✅ tailor_activity with test ID: {result}')

    # Test with real user ID
    result = await tailor_activity('1', '1', {'test': True})
    print(f'✅ tailor_activity with real ID: {result}')

asyncio.run(test_tools())
"
```

**4. Run Comprehensive Architecture Validation**:
```bash
# ✅ Use our validation script
docker exec backend-web-1 python /usr/src/app/test_simple_validation.py
```

**5. Test Workflow Benchmarks**:
```bash
# ✅ Test complete workflow execution
docker exec backend-web-1 python /usr/src/app/test_workflow_benchmark.py
```

**6. Test Mock Tool Responses Fix (Critical Test)**:
```bash
# ✅ Test that mock_tool_responses are ignored in full real mode
docker exec backend-web-1 python /usr/src/app/real_condition_tests/test_quick_test_exact_path.py

# This test validates:
# - No timeout (completes in ~20 seconds instead of 15 minutes)
# - No "test-user-123" in final results (0 occurrences)
# - Real user ID "2" is used as primary identifier
# - Database contains real user traces (thousands of occurrences)
# - Any remaining "test-user-123" traces are in historical/logging data only
```

#### Debug Container
```bash
# Run tests with debugger
docker compose -f docker-compose.yml run --rm debug-tests python -m pytest apps/main/tests/test_specific.py

# Connect debugger on port 5681
# Set breakpoints in your IDE and connect to localhost:5681
```

#### Logging in Tests
```python
import logging
import pytest

# Enable debug logging for specific test
@pytest.mark.asyncio
async def test_with_debug_logging(caplog):
    with caplog.at_level(logging.DEBUG):
        agent = MentorAgent(user_profile_id="test-user-123")
        result = await agent.process_message("test")
        
    # Check logs
    assert "Processing message" in caplog.text
```

#### Test Database Inspection
```python
# Inspect test database state
from django.test import TestCase
from apps.user.models import UserProfile

class TestDatabaseState(TestCase):
    def test_user_profile_creation(self):
        # Create test data
        profile = UserProfile.objects.create(
            username="test_user",
            trust_level=50
        )
        
        # Verify state
        self.assertEqual(UserProfile.objects.count(), 1)
        self.assertEqual(profile.trust_level, 50)
```

### Common Testing Patterns

#### Agent Testing
```python
class TestMentorAgent(TestCase):
    def setUp(self):
        self.agent = MentorAgent(user_profile_id="test-user-123")
        
    @pytest.mark.asyncio
    async def test_response_generation(self):
        response = await self.agent.generate_response("I feel uncertain")
        self.assertIn("support", response.lower())
        
    def test_agent_initialization(self):
        self.assertEqual(self.agent.user_profile_id, "test-user-123")
        self.assertEqual(self.agent.agent_role, "mentor")
```

#### Tool Testing
```python
class TestEngagementTools(TestCase):
    @pytest.mark.asyncio
    async def test_get_domain_preferences(self):
        from apps.main.agents.tools.engagement_tools import get_domain_preferences
        
        result = await get_domain_preferences("test-user-123")
        
        self.assertIn("preferences", result)
        self.assertIsInstance(result["preferences"], dict)
```

#### Workflow Testing
```python
class TestWheelGenerationWorkflow(TestCase):
    @pytest.mark.asyncio
    async def test_complete_workflow(self):
        from apps.main.graphs.wheel_generation_graph import run_wheel_generation_workflow
        
        result = await run_wheel_generation_workflow(
            user_profile_id="test-user-123",
            initial_input="I want to try something creative"
        )
        
        self.assertTrue(result["completed"])
        self.assertIn("wheel_items", result["output_data"])
```

### Error Analysis and Reporting

#### Error Report File
- **Location**: `/usr/src/app/test-results/error_report.txt`
- **Content**: Detailed error traces and context
- **Usage**: Automatic generation during test failures

#### Common Error Patterns & Solutions

**1. Container Selection Errors**:
```
Container hangs or doesn't respond to simple Python commands
```
**Solution**: Use `backend-web-1` container for debugging, not `backend-web-test`

**2. Django Settings Module (Corrected)**:
```
Django configuration works correctly with config.settings.dev
```
**Solution**: The architecture is correct - use web container for debugging

**3. Database Field Relationship Errors**:
```
FieldError: Cannot resolve keyword 'user_profile_id' into field
FieldError: Cannot resolve keyword 'created_at' into field
FieldError: Cannot resolve keyword 'rating' into field
FieldError: Cannot resolve keyword 'trait_name' into field
AttributeError: 'HistoryEvent' object has no attribute 'event_data'
```
**Solution**: Check model relationships and field names:
- UserResource uses `user_environment`, not `user_profile_id`
- Use `created_on` instead of `created_at` for timestamps
- Use `details` instead of `event_data` for HistoryEvent
- Use `generic_trait__code` instead of `trait_name` for trait queries
- Check actual model field names in Django admin or model definitions

**4. User ID Format Issues**:
```
ValueError: invalid literal for int() with base 10: 'test-user-123'
```
**Solution**: Add proper handling for test user IDs vs real user IDs in tool functions

**5. Memory Storage Variable Scope Errors**:
```
UnboundLocalError: cannot access local variable 'created' where it is not associated with a value
```
**Solution**: Initialize variables before try/except blocks in database operations

**6. Container Code Synchronization Issues**:
```
Code changes not reflected in running containers despite file modifications
```
**Solution**:
- Clear Python bytecode cache: `docker exec backend-web-1 find /usr/src/app -name "*.pyc" -delete`
- Clear __pycache__ directories: `docker exec backend-web-1 find /usr/src/app -name "__pycache__" -type d -exec rm -rf {} +`
- Restart containers: `docker restart backend-web-1 backend-celery-1`
- Wait for containers to fully restart before testing
- you can use "backend/restart_containers_with_cache_clear.py" to clear all bytecode cache

**7. Schema Validation Errors**:
```
'response' is a required property (at mock_tool_responses/get_user_profile)
```
**Solution**: Use proper format: `{"response": "{\"key\": \"value\"}"}`

**8. Mock Tool Responses in Full Real Mode**:
```
Task hangs for 15 minutes and times out with TimeLimitExceeded(900,)
Celery logs show "Converting benchmark user ID test-user-123 to default ID"
```
**Solution**: Fixed in December 2025 - mock_tool_responses are now properly ignored when `use_real_tools=True`:
- Updated `async_workflow_manager.py` to skip `_prepare_mock_tools` when `use_real_tools=True`
- Fixed celery error handler to handle `None` task_kwargs during timeouts
- Added proper logging to distinguish real vs mock tool modes
- Test script `backend/real_condition_tests/test_quick_test_exact_path.py` validates the fix

**9. Container Path Issues**:
- **Local**: `backend/apps/main/models.py`
- **Container**: `apps/main/models.py` (no 'backend' prefix)

**9. Async/Await Issues**: Ensure proper async decorators
**10. Mock Data Mismatches**: Verify test data structure matches expected schemas

#### Critical Debugging Lesson: Testing Methodology Was Completely Wrong

**MAJOR ISSUE IDENTIFIED**: Previous debugging approach was fundamentally flawed because:

1. **❌ WRONG**: Testing benchmark "success" instead of actual error logs
2. **❌ WRONG**: Assuming container restarts applied code changes
3. **❌ WRONG**: Misinterpreting fallback behavior as fixes working
4. **❌ WRONG**: Not verifying that actual database calls were fixed

**ROOT CAUSE**: The benchmark system falls back to default/mocked behavior when database calls fail, making it appear successful even when all the database errors are still occurring.

#### Reliable Testing Methodology

** when using "web" container after restarting it, you have to wait 180s for it to be ready **
** when you read the logs of the "celery" container, you need the 480 last lines **

**1. Always Check Actual Error Logs First**:
```bash
# Check CELERY.txt for actual errors - this is the source of truth
cat CELERY.txt | grep -E "(FieldError|AttributeError|ValueError)" | head -10

# Check BM_RES.txt for fallback behavior indicators
cat BM_RES.txt | grep -E "(errors|fallback|default)"
```

**2. Test Individual Database Calls Directly**:
```bash
# Test specific database queries that are failing
docker exec backend-web-1 python -c "
import django
django.setup()
from apps.main.agents.tools.engagement_tools import get_domain_preferences
import asyncio

async def test():
    try:
        result = await get_domain_preferences('test-user-123')
        print('✅ Success:', result)
    except Exception as e:
        print('❌ Error:', e)

asyncio.run(test())
"
```

**3. Verify Code Changes Are Actually Applied**:
```bash
# Check that the actual running code has the fixes
docker exec backend-web-1 grep -n "created_at" /usr/src/app/apps/main/agents/tools/engagement_tools.py
# Should return NO results if fixed properly

docker exec backend-web-1 grep -n "created_on" /usr/src/app/apps/main/agents/tools/engagement_tools.py
# Should return results showing the correct field name
```

**4. Test Container Code Synchronization**:
```bash
# Make a small test change and verify it appears in container
echo "# Test change $(date)" >> backend/apps/main/agents/tools/engagement_tools.py
docker exec backend-web-1 tail -1 /usr/src/app/apps/main/agents/tools/engagement_tools.py
# Should show the test change - if not, volume mounting is broken
```

**5. Proper Fix Validation Sequence**:
```bash
# 1. Make code changes
# 2. Verify changes appear in container immediately (volume mount)
docker exec backend-web-1 grep -A5 -B5 "your_fix_pattern" /usr/src/app/path/to/file.py

# 3. Clear Python cache
docker exec backend-web-1 find /usr/src/app -name "*.pyc" -delete
docker exec backend-web-1 find /usr/src/app -name "__pycache__" -type d -exec rm -rf {} +

# 4. Restart containers
docker restart backend-web-1 backend-celery-1

# 5. Wait for containers to be fully ready (CRITICAL: web container takes 3+ minutes!)
sleep 180  # Wait at least 3 minutes for web container to be fully ready

# 6. Test specific failing function directly (not full benchmark)
docker exec backend-web-1 python -c "test_specific_function()"

# 7. Only then run full benchmark and check CELERY.txt for errors
docker exec backend-web-1 python /usr/src/app/test_workflow_benchmark.py
cat CELERY.txt | grep -E "(FieldError|AttributeError|ValueError)" | wc -l
# Should be 0 if fixes worked

# 8. Check for specific warnings that indicate remaining issues
docker logs backend-celery-1 --since 5m | grep -E "(Tailored activity is not a dict|WARNING|ERROR)"
```

**CRITICAL TIMING NOTE**: The web container takes **3+ minutes** to be fully ready after restart. Always wait at least 180 seconds before testing, or tests will hang/fail.

## Advanced Debugging Techniques

### Tool Registration and Activation Issues

**Problem**: Tools are registered in database but marked as `is_active=False`, causing "Tool not found or failed to load" errors.

**Detection**:
```bash
# Check tool registration status
docker exec backend-web-1 python -c "
import django
django.setup()
from apps.main.models import AgentTool
tools = AgentTool.objects.filter(code__in=['tailor_activity', 'query_activity_catalog'])
for tool in tools:
    print(f'{tool.code}: active={tool.is_active}')
"
```

**Solution**:
```bash
# Activate tools
docker exec backend-web-1 python -c "
import django
django.setup()
from apps.main.models import AgentTool
tools = AgentTool.objects.filter(code__in=['tailor_activity', 'query_activity_catalog', 'assign_wheel_probabilities', 'create_value_propositions'])
for tool in tools:
    tool.is_active = True
    tool.save()
    print(f'✅ Activated {tool.code}')
"
```

### Function vs Tool Execution Testing

**Problem**: Function works when called directly but fails when called through tool execution system.

**Direct Function Testing**:
```python
# test_function_direct.py
import django
django.setup()
from apps.main.agents.tools.activity_tools import tailor_activity

async def test():
    result = await tailor_activity(
        "benchmark-user-test123",
        "default-activity-1-creativity",  # Use enhanced format like benchmark
        {"time": {"reported_duration_minutes": 20}},
        {"reported_mood": "neutral"}
    )
    print(f"Result: {result}")
```

**Tool System Testing**:
```bash
# Test through tool execution system
docker exec backend-web-1 python /usr/src/app/test_workflow_benchmark.py
# Then check celery logs for tool execution results
docker logs backend-celery-1 --since 5m | grep -E "(tailor_activity|Tailored activity)"
```

### Debugging Tool Return Values

**Problem**: Tool returns `None` instead of expected dictionary structure.

**Key Insight**: The issue might be in tool execution system, not the function itself. Always test both:
1. Direct function call (to verify function logic)
2. Tool execution through benchmark (to verify tool system integration)

**Debug Pattern**:
```bash
# 1. Verify function works directly
docker exec backend-web-1 python /usr/src/app/test_function_direct.py

# 2. Check tool registration and activation
docker exec backend-web-1 python -c "from apps.main.models import AgentTool; print([(t.code, t.is_active) for t in AgentTool.objects.filter(code='tailor_activity')])"

# 3. Test through benchmark system
docker exec backend-web-1 python /usr/src/app/test_workflow_benchmark.py

# 4. Compare results and identify where the disconnect occurs
```

### Recent Debugging Session: User ID Consistency Fix (December 2024)

#### Problem Summary
When running benchmarks in "full real" mode with user ID "2", the system was incorrectly using "test-user-123" in 59 places throughout the workflow execution, instead of preserving the real user ID.

#### Root Cause Analysis
1. **Agent Conversion Logic**: Agents were converting real user IDs like "2" to test user IDs like "test-user-123" due to overly broad pattern matching
2. **Mock Tool Response Templates**: Scenario mock tool responses contained hardcoded "test-user-123" values that weren't being replaced with the actual user_profile_id
3. **Template Context Missing user_profile_id**: The template evaluation system didn't include user_profile_id in the substitution context

#### Fixes Implemented

**1. Agent User ID Conversion Logic Fix**:
```python
# ❌ WRONG - All agents (orchestrator_agent.py, mentor_agent.py, etc.)
if isinstance(self.user_profile_id, str) and (
    self.user_profile_id.startswith('test-user-') or
    self.user_profile_id.startswith('benchmark-user-')
):
    user_profile_id_int = 1  # Always converted real IDs to 1

# ✅ FIXED - All agents
try:
    # First, try to convert directly to int (for real user IDs like "2")
    user_profile_id_int = int(self.user_profile_id)
    logger.debug(f"Using real user_profile_id: {user_profile_id_int}")
except ValueError:
    # Only if conversion fails, check if it's a test/benchmark ID
    if isinstance(self.user_profile_id, str) and (
        self.user_profile_id.startswith('test-user-') or
        self.user_profile_id.startswith('benchmark-user-') or
        'test-user-' in self.user_profile_id or
        'benchmark' in self.user_profile_id.lower()
    ):
        user_profile_id_int = 1  # Use default for test/benchmark IDs
```

**2. Template Context Enhancement**:
```python
# ❌ WRONG - benchmark_manager.py template evaluation
eval_context = {
    "tool_input": tool_input or {},
    "call_count": call_count
}

# ✅ FIXED - benchmark_manager.py template evaluation
current_user_profile_id = (
    scenario.input_data.get('user_profile_id') or
    (params.get('user_profile_id') if params else None) or
    user_profile_id or
    'benchmark-user-unknown'
)

eval_context = {
    "tool_input": tool_input or {},
    "call_count": call_count,
    "user_profile_id": current_user_profile_id
}
```

**3. Template Substitution Enhancement**:
```python
# ✅ ADDED - benchmark_manager.py _evaluate_template method
# Add user_profile_id to template context if available
if context and 'user_profile_id' in context:
    flat_context["user_profile_id"] = str(context['user_profile_id'])

# Replace {user_profile_id} with the actual value
if '{user_profile_id}' in formatted_string and 'user_profile_id' in flat_context:
    formatted_string = formatted_string.replace('{user_profile_id}', flat_context['user_profile_id'])
```

**4. Fallback User ID Fix**:
```python
# ❌ WRONG - benchmark_manager.py hardcoded fallback
updated_input_data['user_profile_id'] = 'test-user-workflow-routing'

# ✅ FIXED - benchmark_manager.py dynamic fallback
fallback_user_id = params.get('user_profile_id') if params else None
if not fallback_user_id:
    fallback_user_id = f'benchmark-user-{uuid.uuid4().hex[:8]}'
updated_input_data['user_profile_id'] = fallback_user_id
```

#### Validation Results
After implementing all fixes:
- ✅ **Zero occurrences** of "test-user-123" in benchmark results
- ✅ **Real user ID "2"** correctly preserved throughout workflow
- ✅ **Wheel user_id** correctly set to "2" instead of "1"
- ✅ **Ethical validation user_id** correctly set to "2"
- ✅ **Mock tool responses** now use actual user_profile_id via {user_profile_id} template substitution

#### Testing Scripts Created
- `backend/test_user_id_consistency_fix.py` - Comprehensive verification script
- `backend/real_condition_tests/test_user_id_fix_verification.py` - Detailed test with user lookup

#### Key Lessons Learned
1. **Template systems need complete context** - Always include all relevant variables in template substitution
2. **Agent conversion logic should prioritize real IDs** - Try direct conversion before pattern matching
3. **Test with actual data flows** - Mock tool responses must use template variables, not hardcoded values
4. **Comprehensive testing required** - Check all output fields, not just final results

### Previous Debugging Session: Database Field Errors (December 2024)

#### Problem Summary
Benchmark workflows were failing with multiple database field errors in CELERY.txt logs:
- `Cannot resolve keyword 'created_at'` - Wrong field name usage
- `Cannot resolve keyword 'user_profile_id'` - Wrong relationship usage
- `'HistoryEvent' object has no attribute 'event_data'` - Wrong attribute access
- `Cannot resolve keyword 'rating'` - Wrong field name
- `Cannot resolve keyword 'trait_name'` - Wrong field name
- `cannot access local variable 'created'` - Variable scope error

#### Root Cause Analysis
1. **Field Name Mismatches**: Code was using incorrect field names that didn't match the actual database schema
2. **Relationship Errors**: UserResource relationship was incorrectly using `user_profile_id` instead of `user_environment`
3. **Container Code Synchronization**: Fixed code wasn't being applied due to Python bytecode caching
4. **Variable Scope Issues**: Database service had uninitialized variables in error handling paths

#### Fixes Implemented

**1. Database Field Name Corrections**:
```python
# ❌ WRONG - engagement_tools.py
events = HistoryEvent.objects.filter(created_at__gte=start_date)

# ✅ FIXED - engagement_tools.py
events = HistoryEvent.objects.filter(created_on__gte=start_date)

# ❌ WRONG - psychological_tools.py
event.event_data.get('mood')

# ✅ FIXED - psychological_tools.py
event.details.get('mood')
```

**2. Relationship Corrections**:
```python
# ❌ WRONG - activity_tools.py
user_resources = UserResource.objects.filter(user_profile_id=user_id)

# ✅ FIXED - activity_tools.py
user_resources = UserResource.objects.filter(
    user_environment=user_environment
) if user_environment else UserResource.objects.none()
```

**3. Variable Scope Fixes**:
```python
# ❌ WRONG - database_service.py
try:
    memory_obj, created = UserMemory.objects.get_or_create(...)
except Exception as e:
    if created:  # ❌ 'created' not defined in except block

# ✅ FIXED - database_service.py
created = False  # Initialize before try block
try:
    memory_obj, created = UserMemory.objects.get_or_create(...)
except Exception as e:
    if created:  # ✅ Now properly defined
```

#### Additional Fixes Required

**4. Model Field Access Errors**:
```python
# ❌ WRONG - activity_tools.py helper functions
domain = getattr(generic_activity, 'domain', 'general')

# ✅ FIXED - activity_tools.py helper functions
domain = 'general'
try:
    domain_rel = generic_activity.domain_relationships.first()
    if domain_rel:
        domain = domain_rel.domain.name
except Exception:
    domain = 'general'
```

### Systematic Debugging Workflow

#### Step 1: Container Architecture Understanding

**CRITICAL**: Use the correct container for debugging:

- **web container** (`backend-web-1`): ✅ Use for debugging and validation
  - Pre-configured with `config.settings.dev`
  - Connected to main database
  - Ready for immediate testing

- **web-test container** (`backend-web-test`): ❌ Don't use for simple debugging
  - Designed for full test suite execution
  - Runs complex setup scripts on startup
  - Will hang on simple Python commands

**Environment Verification**:
```bash
# ✅ CORRECT - Use web container for debugging
docker exec backend-web-1 python -c "
import os, django
print(f'Working dir: {os.getcwd()}')
print(f'Django settings: {os.environ.get(\"DJANGO_SETTINGS_MODULE\")}')
django.setup()
print('✅ Django setup successful')
from django.db import connection
with connection.cursor() as cursor:
    cursor.execute('SELECT 1')
    print('✅ Database connection successful')
"
```

#### Step 2: Model Relationship Testing
```bash
# Test specific model relationships that commonly fail
docker run --rm --network backend_default backend-web-test python -c "
import os, django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

# Test UserResource relationship
from apps.user.models import UserProfile, UserResource
print('Testing UserResource relationship...')
try:
    # This should work
    resources = UserResource.objects.all()
    print(f'✅ UserResource query successful: {resources.count()} resources')
except Exception as e:
    print(f'❌ UserResource query failed: {e}')
"
```

#### Step 3: Tool Function Isolation Testing
```bash
# Test individual tool functions
docker run --rm --network backend_default backend-web-test python -c "
import os, django, asyncio
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

async def test_tools():
    # Test engagement tools
    from apps.main.agents.tools.engagement_tools import get_domain_preferences
    try:
        result = await get_domain_preferences('test-user-123')
        print(f'✅ get_domain_preferences: {result}')
    except Exception as e:
        print(f'❌ get_domain_preferences failed: {e}')

asyncio.run(test_tools())
"
```

#### Step 4: Full Workflow Testing
```bash
# Test complete workflow execution
docker exec backend-web-1 python /usr/src/app/test_workflow_benchmark.py
```

### Best Practices for Debugger Role

1. **Use the web container for debugging** - `docker exec backend-web-1` not `docker run backend-web-test`
2. **Verify Django setup first** - ensure `config.settings.dev` is working
3. **Test database relationships in isolation** before complex operations
4. **Use our validation scripts** - `test_simple_validation.py` and `test_workflow_benchmark.py`
5. **Test tool functions with both test and real user IDs** for comprehensive coverage
6. **Use appropriate async decorators** for Django 5.2.1 compatibility
7. **Mock external dependencies** to ensure test reliability
8. **Write descriptive test names** that explain the expected behavior
9. **Use setUp/tearDown methods** for consistent test environments
10. **Verify both success and failure scenarios** in your tests
11. **Remember the web-test container is for full test suites** not individual debugging

## Recent Fixes and Improvements

### Agent Execution Detail Modal Enhancement (2025-06-09)

**Issue**: The agent execution detail modal in `backend/templates/admin_tools/benchmark_history.html` was displaying poor performance metrics instead of rich agent output data.

**Root Cause**: The modal's `renderInputOutput` function was using simple data extraction that missed the rich `output_data` structures produced by agents like:
- ResourceAgent: `combined_resource_context`
- EngagementAgent: `engagement_analysis`
- PsychologicalAgent: `psychological_assessment`
- StrategyAgent: `strategy_framework`
- ActivityAgent: `wheel` data
- EthicalAgent: `ethical_validation`

**Solution Implemented**:
1. **Enhanced Data Extraction**: Added comprehensive `extractAgentOutputData()` and `extractAgentInputData()` functions that check multiple data paths
2. **Rich Output Rendering**: Implemented agent-specific rendering functions for each agent type
3. **Improved UI**: Added structured display with collapsible JSON fallback
4. **CSS Styling**: Added comprehensive styles for rich output display

**Files Modified**:
- `backend/templates/admin_tools/modals/agent_execution_detail_modal.html`
- `docs/AGENTS_AND_WORKFLOWS_COMPREHENSIVE_GUIDE.md`

**Testing Approach**:
```bash
# Verify benchmark execution produces rich data
docker exec -it backend-web-1 python /usr/src/app/real_condition_tests/test_quick_test_exact_path.py

# Check celery logs for successful execution
docker logs backend-celery-1 --tail 20

# Verify in admin interface:
# 1. Navigate to benchmark history
# 2. Click on recent benchmark run
# 3. Click on individual agent execution details
# 4. Verify rich output data is displayed instead of generic metrics
```

**Verification**: The modal now displays meaningful agent data with structured sections for each agent type, making debugging and analysis much more effective.

### important considerations
- due to recent modifications of backend/docker-compose.yaml, the celery container now takes a very long time to restart (several minutes)

## Testing Agent Output Data Display

When fixing issues with agent output data not displaying properly in the benchmark history admin interface, use this systematic approach:

### 1. Verify Data Availability

First, check that the benchmark results contain the expected agent output data:

```bash
# Run a test benchmark and check the results
docker exec backend-web-1 python /usr/src/app/real_condition_tests/test_quick_test_exact_path.py

# Check the generated BM_RES.txt file for agent data
grep -A 10 "enhanced_debugging_data" BM_RES.txt
```

### 2. Test Agent Output Data Extraction

Use the dedicated test script to verify data extraction logic:

```bash
# Test the data extraction from benchmark results
docker exec backend-web-1 python /usr/src/app/test_agent_output_extraction.py
```

This script will:
- Find recent benchmark runs with rich agent data
- Test the extraction logic for each agent type
- Verify execution mode detection
- Simulate frontend data extraction patterns

### 3. Verify Admin Interface Changes

After making changes to the admin interface data extraction:

1. **Check JavaScript console** for errors in benchmark history page
2. **Test modal display** by clicking on agent execution details
3. **Verify execution mode** shows "Real Mode" instead of "Mock Mode" when appropriate
4. **Confirm rich data display** shows agent-specific output instead of generic performance metrics

### 4. Expected Agent Output Data

Each agent should display rich, meaningful data:

- **Resource Agent**: `combined_resource_context` with environment, time, and resource analysis
- **Engagement Agent**: `engagement_analysis` with historical patterns and recommendations
- **Psychological Agent**: `psychological_assessment` with current state and trust evaluation
- **Strategy Agent**: `strategy_framework` with comprehensive strategy synthesis
- **Activity Agent**: `wheel` with complete wheel structure and activities
- **Ethical Agent**: `ethical_validation` with validation results and safety considerations

## Summary

The Debugger Agent Guide provides comprehensive testing strategies for Goali's multi-agent system. Key testing approaches include:

1. **Unit Testing**: Isolated component testing using web-test container
2. **Integration Testing**: Real condition tests using web container with real database
3. **Benchmark Testing**: End-to-end workflow validation with comprehensive logging
4. **Error Analysis**: Systematic debugging using logs and test artifacts
5. **Agent Output Data Testing**: Verification of rich data display in admin interface

For specific testing scenarios, refer to the appropriate test files in `backend/real_condition_tests/` and follow the documented patterns for reliable validation of system changes.