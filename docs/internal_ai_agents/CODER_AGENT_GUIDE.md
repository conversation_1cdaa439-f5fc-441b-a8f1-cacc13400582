# Coder Agent Guide

## Overview

You are a **Coder Agent** - a specialized AI assistant focused on low-level implementation details, coding patterns, and technical implementation within the Goali codebase. Your expertise is in writing clean, maintainable code that follows project conventions.

## Code Architecture Patterns

### Agent Implementation Pattern

#### Base Agent Structure
```python
# apps/main/agents/example_agent.py
import logging
from typing import Dict, Any, Optional
from pydantic import BaseModel
from asgiref.sync import sync_to_async

from apps.main.agents.base_agent import LangGraphAgent
from apps.main.services.database_service import RealDatabaseService
from apps.main.llm.service import RealLLMClient
from apps.main.models import LLMConfig

logger = logging.getLogger(__name__)

class ExampleAgent(LangGraphAgent):
    """
    Agent that performs specific functionality.
    Inherits from LangGraphAgent for workflow integration.
    """
    
    def __init__(self, user_profile_id: str, agent_role: str = "example", 
                 llm_config: Optional[LLMConfig] = None):
        super().__init__(user_profile_id, agent_role, llm_config)
        self.db_service = RealDatabaseService()
        
    async def process(self, state) -> Dict[str, Any]:
        """
        Main processing method called by LangGraph workflow.
        Must return dict with state updates.
        """
        try:
            # Start profiling this stage
            self.start_stage(f"{self.agent_role}_analysis")
            
            # Get user context
            user_context = await self._get_user_context()
            
            # Perform agent-specific logic
            analysis_result = await self._perform_analysis(user_context)
            
            # Stop profiling
            self.stop_stage(f"{self.agent_role}_analysis")
            
            return {
                "analysis_complete": True,
                "analysis_data": analysis_result,
                "next_agent": "next_agent_name"  # Optional routing
            }
            
        except Exception as e:
            logger.exception(f"Error in {self.agent_role} processing")
            return {
                "error": str(e),
                "analysis_complete": False
            }
    
    async def _get_user_context(self) -> Dict[str, Any]:
        """Private method to get user context from database."""
        # Implementation details...
        pass
        
    async def _perform_analysis(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Private method for core agent logic."""
        # Implementation details...
        pass
```

### Tool Implementation Pattern

#### Tool Registration and Structure
```python
# apps/main/agents/tools/example_tools.py
import logging
from typing import Dict, Any, List
from channels.db import database_sync_to_async
from .tools_util import register_tool

logger = logging.getLogger(__name__)

@register_tool('example_tool_name')
async def example_tool(user_profile_id: str, parameter: str) -> Dict[str, Any]:
    """
    Tool description for LLM context.
    
    Args:
        user_profile_id: ID of the user profile
        parameter: Description of parameter
        
    Returns:
        Dict containing tool results
    """
    try:
        logger.info(f"Executing example_tool for user {user_profile_id}")
        
        # Use database_sync_to_async for database operations
        result = await _get_data_from_db(user_profile_id, parameter)
        
        return {
            "success": True,
            "data": result,
            "metadata": {
                "tool_name": "example_tool_name",
                "execution_time": "calculated_time"
            }
        }
        
    except Exception as e:
        logger.exception("Error in example_tool")
        return {
            "success": False,
            "error": str(e),
            "data": None
        }

@database_sync_to_async
def _get_data_from_db(user_id: str, parameter: str):
    """Synchronous database operations wrapped for async use."""
    from apps.user.models import UserProfile
    
    try:
        # Convert user_id if needed (handle test vs real users)
        if user_id.startswith('test-user-'):
            # For unit tests, use mock data
            return {"mock": True, "parameter": parameter}
        
        # Convert to int for real database queries
        user_id_int = int(user_id)
        
        # Use proper ForeignKey relationships
        user_profile = UserProfile.objects.filter(id=user_id_int).first()
        if not user_profile:
            return {"error": "User not found"}
            
        # Example query with proper relationship handling
        related_data = SomeModel.objects.filter(
            user_profile__id=user_id_int  # Use ForeignKey relationship
        ).select_related('user_profile')
        
        return {
            "user_profile": user_profile.username,
            "related_count": related_data.count(),
            "parameter": parameter
        }
        
    except Exception as e:
        logger.exception("Database error in _get_data_from_db")
        return {"error": str(e)}
```

### Database Query Patterns

#### Correct ForeignKey Usage
```python
# CORRECT: Use ForeignKey relationships
activities = ActivityTailored.objects.filter(
    user_profile__id=user_id,  # ForeignKey relationship
    created_on__gte=start_date
).select_related('generic_activity', 'user_profile')

# INCORRECT: Direct field access (causes errors)
activities = ActivityTailored.objects.filter(
    user_profile_id=user_id,  # This field doesn't exist
    created_at__gte=start_date  # Wrong field name
)
```

#### Model Relationship Patterns
```python
# User Profile relationships
user_profile = UserProfile.objects.filter(id=user_id).first()

# UserEnvironment through current_environment field
user_environment = user_profile.current_environment if user_profile else None

# UserResource through environment relationship
user_resources = UserResource.objects.filter(
    user_environment=user_environment
) if user_environment else UserResource.objects.none()

# Skills and limitations through user_profile relationship
user_skills = Skill.objects.filter(user_profile__id=user_id)
user_limitations = UserLimitation.objects.filter(user_profile__id=user_id)
```

### Workflow Graph Pattern

#### LangGraph Workflow Structure
```python
# apps/main/graphs/example_workflow_graph.py
from typing import Any, Dict, Optional, Literal
import uuid
import logging
from pydantic import BaseModel, Field
from langgraph.graph import END, StateGraph

# Import agent nodes
from apps.main.agents.orchestrator_agent import OrchestratorAgent
from apps.main.agents.example_agent import ExampleAgent
from apps.main.agents.error_handler import ErrorHandlerAgent

logger = logging.getLogger(__name__)

class ExampleWorkflowState(BaseModel):
    """State model for the example workflow."""
    workflow_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    user_profile_id: str
    input: str
    completed: bool = False
    error: Optional[str] = None
    
    # Stage tracking
    current_stage: str = "initialization"
    last_agent: Optional[str] = None
    
    # Data fields
    analysis_data: Optional[Dict[str, Any]] = None
    output_data: Optional[Dict[str, Any]] = None

def create_example_workflow_graph(user_profile_id: str, llm_config=None):
    """Create the example workflow graph."""
    
    # Create the graph with state model
    workflow = StateGraph(ExampleWorkflowState)
    
    # Add agent nodes
    workflow.add_node("orchestrator", OrchestratorAgent(user_profile_id, llm_config=llm_config))
    workflow.add_node("example", ExampleAgent(user_profile_id, llm_config=llm_config))
    workflow.add_node("error_handler", ErrorHandlerAgent(user_profile_id, llm_config=llm_config))
    
    # Define routing logic
    def route_from_orchestrator(state: ExampleWorkflowState) -> Literal["example", "error_handler", "__end__"]:
        if state.error:
            return "error_handler"
        if state.current_stage == "analysis":
            return "example"
        if state.completed:
            return END
        return "example"
    
    def route_from_example(state: ExampleWorkflowState) -> Literal["orchestrator", "error_handler", "__end__"]:
        if state.error:
            return "error_handler"
        if state.analysis_complete:
            return "orchestrator"
        return "error_handler"
    
    # Add conditional edges
    workflow.add_conditional_edges(
        "orchestrator",
        route_from_orchestrator,
        {
            "example": "example",
            "error_handler": "error_handler",
            END: END
        }
    )
    
    workflow.add_conditional_edges(
        "example",
        route_from_example,
        {
            "orchestrator": "orchestrator",
            "error_handler": "error_handler"
        }
    )
    
    # Error handler always returns to orchestrator
    workflow.add_edge("error_handler", "orchestrator")
    
    # Set entry point
    workflow.set_entry_point("orchestrator")
    
    return workflow

async def run_example_workflow(user_profile_id: str, initial_input: str, llm_config=None):
    """Run the example workflow."""
    try:
        # Create and compile workflow
        workflow = create_example_workflow_graph(user_profile_id, llm_config)
        app = workflow.compile()
        
        # Initialize state
        initial_state = ExampleWorkflowState(
            user_profile_id=user_profile_id,
            input=initial_input
        )
        
        # Execute workflow
        result = await app.ainvoke(initial_state.dict())
        
        return result
        
    except Exception as e:
        logger.exception("Error in example workflow execution")
        return {
            "error": str(e),
            "completed": False,
            "workflow_id": str(uuid.uuid4())
        }
```

### Error Handling Patterns

#### Comprehensive Error Handling
```python
async def robust_function(user_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
    """Function with comprehensive error handling."""
    try:
        # Validate inputs
        if not user_id:
            raise ValueError("user_id is required")
        if not isinstance(data, dict):
            raise TypeError("data must be a dictionary")
            
        # Handle different user ID formats
        if user_id.startswith('test-user-'):
            # Mock response for tests
            return {"mock": True, "user_id": user_id}
        
        # Convert to int for database operations
        try:
            user_id_int = int(user_id)
        except ValueError:
            raise ValueError(f"Invalid user_id format: {user_id}")
        
        # Database operations with error handling
        result = await _safe_database_operation(user_id_int, data)
        
        return {
            "success": True,
            "data": result,
            "user_id": user_id
        }
        
    except ValueError as e:
        logger.warning(f"Validation error in robust_function: {e}")
        return {"success": False, "error": f"Validation error: {str(e)}"}
    except Exception as e:
        logger.exception("Unexpected error in robust_function")
        return {"success": False, "error": f"Processing error: {str(e)}"}

@database_sync_to_async
def _safe_database_operation(user_id: int, data: Dict[str, Any]):
    """Safe database operation with proper error handling."""
    try:
        # Use proper ORM patterns
        from apps.user.models import UserProfile
        
        user_profile = UserProfile.objects.select_related().filter(id=user_id).first()
        if not user_profile:
            raise ValueError(f"User profile {user_id} not found")
            
        # Perform database operations
        # ... implementation
        
        return {"processed": True, "user": user_profile.username}
        
    except Exception as e:
        logger.exception(f"Database error for user {user_id}")
        raise
```

### Testing Patterns

#### Unit Test Structure
```python
# apps/main/tests/test_example_agent.py
import pytest
from django.test import TestCase
from unittest.mock import AsyncMock, patch

from apps.main.agents.example_agent import ExampleAgent

class TestExampleAgent(TestCase):
    """Test suite for ExampleAgent."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.user_id = "test-user-123"
        self.agent = ExampleAgent(user_profile_id=self.user_id)
        
    @pytest.mark.asyncio
    async def test_process_success(self):
        """Test successful processing."""
        # Mock dependencies
        with patch.object(self.agent, '_get_user_context') as mock_context:
            mock_context.return_value = {"trust_level": 50}
            
            with patch.object(self.agent, '_perform_analysis') as mock_analysis:
                mock_analysis.return_value = {"result": "success"}
                
                # Create mock state
                state = type('State', (), {})()
                
                # Execute
                result = await self.agent.process(state)
                
                # Assertions
                self.assertTrue(result["analysis_complete"])
                self.assertEqual(result["analysis_data"]["result"], "success")
                
    @pytest.mark.asyncio
    async def test_process_error_handling(self):
        """Test error handling in process method."""
        # Mock to raise exception
        with patch.object(self.agent, '_get_user_context') as mock_context:
            mock_context.side_effect = Exception("Database error")
            
            state = type('State', (), {})()
            result = await self.agent.process(state)
            
            # Should handle error gracefully
            self.assertFalse(result["analysis_complete"])
            self.assertIn("error", result)
            
    def test_agent_initialization(self):
        """Test agent initialization."""
        self.assertEqual(self.agent.user_profile_id, self.user_id)
        self.assertEqual(self.agent.agent_role, "example")
        self.assertIsNotNone(self.agent.db_service)
```

### Code Style and Conventions

#### Import Organization
```python
# Standard library imports
import logging
import uuid
from typing import Dict, Any, List, Optional

# Third-party imports
from pydantic import BaseModel, Field
from langgraph.graph import END, StateGraph
from channels.db import database_sync_to_async

# Django imports
from django.utils import timezone

# Local imports
from apps.main.agents.base_agent import LangGraphAgent
from apps.main.services.database_service import RealDatabaseService
from apps.user.models import UserProfile
```

#### Logging Patterns
```python
import logging

logger = logging.getLogger(__name__)

# Info level for normal operations
logger.info(f"Processing request for user {user_id}")

# Debug level for detailed tracing
logger.debug(f"State transition: {old_stage} -> {new_stage}")

# Warning for recoverable issues
logger.warning(f"Fallback used for user {user_id}: {reason}")

# Exception for errors with full traceback
logger.exception("Error in agent processing")

# Error for known error conditions
logger.error(f"Validation failed: {error_message}")
```

#### Documentation Patterns
```python
def complex_function(param1: str, param2: Dict[str, Any], param3: Optional[int] = None) -> Dict[str, Any]:
    """
    Brief description of what the function does.
    
    Longer description if needed, explaining the purpose,
    algorithm, or important implementation details.
    
    Args:
        param1: Description of first parameter
        param2: Description of second parameter with expected structure
        param3: Optional parameter with default behavior explanation
        
    Returns:
        Dict containing:
            - success (bool): Whether operation succeeded
            - data (Any): Result data if successful
            - error (str): Error message if failed
            
    Raises:
        ValueError: When input validation fails
        DatabaseError: When database operations fail
        
    Example:
        >>> result = complex_function("user123", {"key": "value"})
        >>> print(result["success"])
        True
    """
    # Implementation...
```

### Performance and Optimization

#### Async/Await Best Practices
```python
# Use async/await for I/O operations
async def fetch_user_data(user_id: str) -> Dict[str, Any]:
    """Async function for database operations."""
    user_data = await _get_user_from_db(user_id)
    preferences = await _get_user_preferences(user_id)
    
    return {
        "user": user_data,
        "preferences": preferences
    }

# Use database_sync_to_async for Django ORM
@database_sync_to_async
def _get_user_from_db(user_id: int):
    """Sync database operation wrapped for async use."""
    return UserProfile.objects.select_related().filter(id=user_id).first()
```

#### Database Query Optimization
```python
# Use select_related for ForeignKey relationships
user_profiles = UserProfile.objects.select_related(
    'current_environment'
).filter(id__in=user_ids)

# Use prefetch_related for reverse ForeignKey and ManyToMany
user_profiles = UserProfile.objects.prefetch_related(
    'skills',
    'goals',
    'limitations'
).filter(trust_level__gte=50)

# Combine for complex queries
activities = ActivityTailored.objects.select_related(
    'user_profile',
    'generic_activity'
).prefetch_related(
    'generic_activity__domain_relationships'
).filter(user_profile__id=user_id)
```

This guide provides the foundation for writing clean, maintainable code that follows Goali project conventions and patterns.
