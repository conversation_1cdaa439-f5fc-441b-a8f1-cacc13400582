# AI Agent Instructions: Lit + PixiJS + Matter.js Stack

## Core Stack Architecture

**Primary**: Lit 3.x + TypeScript 5.x + PixiJS 8.x + Matter.js 0.19.x
**Target**: Mobile WebViews (Android/iOS), high-performance 2D graphics with physics

## Lit Component Patterns

### Component Structure
```typescript
@customElement('game-wheel')
export class GameWheel extends LitElement {
  @property({ type: Array }) wheelData: WheelItem[] = [];
  @property({ type: Boolean, reflect: true }) spinning = false;
  @state() private _pixiApp?: Application;
  @query('canvas') private canvas!: HTMLCanvasElement;
}
```

### Best Practices
- Use `@property` for external API, `@state` for internal state
- Always type properties with interfaces
- Use `reflect: true` for CSS styling hooks
- Implement `willUpdate()` for property change reactions
- Use `@query()` for DOM element references

### Lifecycle Management
```typescript
firstUpdated() {
  this.initPixiApp();
}

disconnectedCallback() {
  super.disconnectedCallback();
  this._pixiApp?.destroy();
}
```

## PixiJS Integration

### Application Setup
```typescript
private async initPixiApp() {
  this._pixiApp = new Application();
  await this._pixiApp.init({
    canvas: this.canvas,
    width: 800,
    height: 600,
    antialias: true,
    resolution: window.devicePixelRatio,
    autoDensity: true
  });
}
```

### Performance Optimizations
- Set `resolution: window.devicePixelRatio` for crisp mobile displays
- Use `autoDensity: true` for automatic scaling
- Enable `antialias: true` for smooth graphics
- Implement texture atlases for multiple sprites
- Use `Container` for logical grouping
- Apply `cacheAsBitmap = true` for static elements

### Graphics Best Practices
```typescript
// Use Graphics for dynamic shapes
const wheel = new Graphics();
wheel.circle(0, 0, radius).fill(color);

// Use Sprite for static images
const ballSprite = Sprite.from('ball.png');
ballSprite.anchor.set(0.5);

// Optimize filters usage
wheel.filters = [new BlurFilter(2)]; // Apply sparingly
```

## Matter.js Physics Integration

### Engine Setup
```typescript
private initPhysics() {
  this.engine = Engine.create();
  this.world = this.engine.world;
  
  // Optimize for mobile
  this.engine.timing.timeScale = 1;
  this.engine.constraintIterations = 2;
  this.engine.positionIterations = 6;
}
```

### Physics Bodies
```typescript
// Static wheel segments
const wheelBody = Bodies.circle(400, 300, 200, {
  isStatic: true,
  render: { visible: false }
});

// Dynamic ball
const ball = Bodies.circle(400, 300, 10, {
  restitution: 0.8,
  friction: 0.001,
  frictionAir: 0.01
});
```

### Render Loop Synchronization
```typescript
private gameLoop = () => {
  Engine.update(this.engine, 16.67); // 60fps
  this.updateVisuals();
  requestAnimationFrame(this.gameLoop);
};

private updateVisuals() {
  // Sync PixiJS sprites with Matter.js bodies
  this.ballSprite.x = this.ballBody.position.x;
  this.ballSprite.y = this.ballBody.position.y;
  this.ballSprite.rotation = this.ballBody.angle;
}
```

## TypeScript Patterns

### Interfaces
```typescript
interface WheelItem {
  id: string;
  label: string;
  color: string;
  percentage: number;
  value?: any;
}

interface PhysicsConfig {
  restitution: number;
  friction: number;
  frictionAir: number;
}
```

### Type Guards
```typescript
function isWheelItem(obj: any): obj is WheelItem {
  return obj && typeof obj.id === 'string' && typeof obj.percentage === 'number';
}
```

## Mobile WebView Optimization

### Performance
- Use `will-change: transform` for animated elements
- Implement `passive: true` for touch event listeners
- Debounce resize events with `ResizeObserver`
- Use `transform3d(0,0,0)` to trigger hardware acceleration

### Touch Handling
```typescript
@eventOptions({ passive: true })
private handleTouch(e: TouchEvent) {
  e.preventDefault();
  const touch = e.touches[0];
  // Handle touch logic
}
```

### Memory Management
```typescript
private cleanup() {
  // Remove event listeners
  this.removeEventListener('touchstart', this.handleTouch);
  
  // Destroy PixiJS resources
  this._pixiApp?.destroy({ removeView: true });
  
  // Clear Matter.js world
  World.clear(this.world, false);
  Engine.clear(this.engine);
}
```

## API Integration Patterns

### Data Fetching
```typescript
private async loadWheelData(): Promise<WheelItem[]> {
  try {
    const response = await fetch('/api/wheel-items/');
    const data = await response.json();
    return data.filter(isWheelItem);
  } catch (error) {
    console.error('Failed to load wheel data:', error);
    return [];
  }
}
```

### Error Handling
```typescript
@state() private error: string | null = null;
@state() private loading = false;

private async fetchData() {
  this.loading = true;
  this.error = null;
  
  try {
    this.wheelData = await this.loadWheelData();
  } catch (err) {
    this.error = err instanceof Error ? err.message : 'Unknown error';
  } finally {
    this.loading = false;
  }
}
```

## Animation & Effects

### Smooth Transitions
```typescript
// Use GSAP or CSS animations for UI transitions
import { gsap } from 'gsap';

private animateWheelSpin(duration: number) {
  gsap.to(this.wheelContainer, {
    rotation: Math.PI * 8 + this.finalAngle,
    duration,
    ease: "power4.out"
  });
}
```

### Particle Effects
```typescript
// PixiJS particle system for visual flair
import { Emitter } from '@pixi/particle-emitter';

private createSparkles() {
  const emitter = new Emitter(this.effectsContainer, {
    lifetime: { min: 0.5, max: 1.0 },
    frequency: 0.008,
    maxParticles: 1000
  });
}
```

## Code Quality Standards

### Error Boundaries
```typescript
protected catch(error: Error) {
  console.error('Component error:', error);
  this.error = error.message;
  this.requestUpdate();
}
```

### Testing Patterns
```typescript
// Use @web/test-runner with Lit testing helpers
import { fixture, expect } from '@open-wc/testing';

it('should render wheel with data', async () => {
  const el = await fixture<GameWheel>(html`
    <game-wheel .wheelData=${mockData}></game-wheel>
  `);
  
  expect(el.shadowRoot?.querySelector('canvas')).to.exist;
});
```

### Performance Monitoring
```typescript
private measurePerformance() {
  const start = performance.now();
  // ... operation
  const end = performance.now();
  console.log(`Operation took ${end - start} milliseconds`);
}
```

## Build Configuration

### Rollup/Vite Config
```javascript
export default {
  build: {
    target: 'es2020',
    lib: {
      entry: 'src/index.ts',
      formats: ['es']
    },
    rollupOptions: {
      external: ['lit'],
      output: {
        globals: { lit: 'Lit' }
      }
    }
  }
};
```

### Bundle Optimization
- Tree-shake unused PixiJS modules
- Use dynamic imports for heavy physics calculations
- Compress textures appropriately for mobile
- Enable gzip/brotli compression

## Security & Validation

### Input Sanitization
```typescript
private sanitizeWheelData(data: any[]): WheelItem[] {
  return data
    .filter(isWheelItem)
    .map(item => ({
      ...item,
      label: this.sanitizeString(item.label),
      percentage: Math.max(0, Math.min(100, item.percentage))
    }));
}
```

### CSP Compliance
- Use nonce for inline styles if required
- Avoid `eval()` or `Function()` constructors
- Validate all external data sources

## Debugging & Development

### Debug Overlays
```typescript
private enableDebugMode() {
  if (process.env.NODE_ENV === 'development') {
    // Show Matter.js wireframes
    this.render.options.showDebug = true;
    
    // Add performance stats
    this.addPerformanceOverlay();
  }
}
```

### Logging Strategy
```typescript
private log(level: 'info' | 'warn' | 'error', message: string, data?: any) {
  if (this.debug) {
    console[level](`[GameWheel] ${message}`, data);
  }
}
```

Remember: Prioritize mobile performance, use TypeScript strictly, handle errors gracefully, and maintain clean separation between presentation (PixiJS) and logic (Matter.js).