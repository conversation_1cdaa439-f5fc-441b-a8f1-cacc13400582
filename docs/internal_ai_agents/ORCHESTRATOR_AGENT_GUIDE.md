# Orchestrator Agent Guide

## Overview

You are an **Orchestrator Agent** - a high-level AI assistant with comprehensive knowledge of the Goali codebase architecture. Your role is to coordinate development tasks, understand system-wide implications, and guide benchmark-driven development strategies.

## System Architecture Overview

### Core Technology Stack
- **Backend**: Django 5.2.1 with Python 3.12
- **Multi-Agent System**: LangGraph integrated within Django
- **Database**: PostgreSQL (production: `db`, testing: `test-db`)
- **Task Queue**: Celery with Redis broker
- **Containerization**: Docker with docker-compose
- **Frontend**: React with TypeScript (enhanced frontend)

### Container Architecture

#### Production Containers (`backend/docker-compose.yml`)
- **`web`**: Main Django application server
  - Port: 8000 (Django), 5678 (debugpy)
  - Volume: `.:/usr/src/app` (live code reloading)
  - Database: `************************************/mydb`
  - Environment: `config.settings.dev`

- **`web-test`**: Dedicated testing container
  - Volume: `.:/usr/src/app`
  - Database: `***********************************************/test_goali`
  - Environment: `config.settings.test`
  - Command: Runs `ultimate_test_setup.py` then pytest

- **`celery`**: Background task worker
  - Port: 5680 (debugpy)
  - Handles workflow execution and benchmarking
  - Depends on: redis, db, test-db

- **`db`**: PostgreSQL production database
- **`test-db`**: PostgreSQL testing database (isolated)
- **`redis`**: Celery broker and Channels layer backend

### Multi-Agent System Architecture

#### Agent Hierarchy
1. **ConversationDispatcher** - Entry point and workflow routing
2. **Orchestrator Agent** - Workflow coordination and state management
3. **Mentor Agent** - User-facing conversational interface
4. **Resource & Capacity Agent** - Environmental and resource analysis
5. **Engagement & Pattern Analytics Agent** - Historical pattern analysis
6. **Psychological Monitoring Agent** - Psychological state assessment
7. **Strategy Agent** - Decision synthesis and planning
8. **Wheel/Activity Agent** - Activity generation and customization
9. **Ethical Oversight Agent** - Safety and ethical validation

#### Workflow Types
- **Wheel Generation** (`wheel_generation_graph.py`): Primary workflow for activity recommendation
- **Discussion** (`discussion_graph.py`): Conversational interactions
- **Onboarding** (`onboarding_graph.py`): User initialization

### Development Strategy: Testing vs Benchmarking

#### Core Philosophy
The project uses a **dual-quality approach** with distinct purposes for testing and benchmarking:

#### Testing (Codebase Quality)
**Purpose**: Ensure lowest layers (workers, tools, configuration, views) perform correctly
- **Environment**: `web-test` container with isolated `test-db`
- **Focus**: Unit tests, integration tests, code correctness
- **Data**: Mock data, test fixtures, isolated test scenarios
- **Goal**: Verify technical implementation works as designed

#### Benchmarking (UX Quality)
**Purpose**: Holistic integration testing using real user profiles and environments
- **Environment**: Real production environment with actual database
- **Focus**: End-to-end workflows, user experience, semantic quality
- **Data**: Real user profiles, actual system interactions
- **Goal**: Validate that good benchmark scores = satisfied end users

#### Benchmark Types

**1. Agent Benchmarks**
- Test individual agent components with real user data
- Measure response quality, token usage, execution time
- Command: `python manage.py run_benchmarks --agent-role <role>`

**2. Workflow Benchmarks**
- Test complete LangGraph workflows end-to-end with real users
- Support real/mock execution modes for different validation needs
- Command: `python manage.py run_workflow_benchmarks --workflow-type <type>`

#### Execution Modes

**Mock Mode (Default - Safe)**:
```bash
# Safe for development, no real costs
python manage.py run_workflow_benchmarks --workflow-type wheel_generation
```

**Real Mode (Production-like)**:
```bash
# Full real mode - actual LLM costs and database operations
python manage.py run_workflow_benchmarks --workflow-type wheel_generation --use-real-llm --use-real-tools --use-real-db
```

**Granular Control**:
```bash
# Real LLM only (measure actual quality/costs)
python manage.py run_workflow_benchmarks --workflow-type wheel_generation --use-real-llm

# Real database only (test data integration)
python manage.py run_workflow_benchmarks --workflow-type wheel_generation --use-real-db
```

### Running Benchmarks

#### Using Web Container
```bash
# Quick workflow test
docker exec -it backend-web-1 python /usr/src/app/test_workflow_benchmark.py

# Full benchmark suite
docker exec -it backend-web-1 python manage.py run_benchmarks --agent-role mentor

# Workflow benchmarks with real mode
docker exec -it backend-web-1 python manage.py run_workflow_benchmarks --workflow-type wheel_generation --use-real-llm
```

#### Volume Mounts and Data Access
- **Code Volume**: `.:/usr/src/app` - Live code changes reflected immediately
- **Database Data**: Persistent volumes `postgres_data` and `postgres_test_data`
- **Test Results**: Available in container at `/usr/src/app/test-results/`

### Key Development Commands

#### Container Management
```bash
# Start all services
docker compose -f backend/docker-compose.yml up -d

# Check container status
docker compose -f backend/docker-compose.yml ps

# View logs
docker compose -f backend/docker-compose.yml logs celery
```

#### Database Operations
```bash
# Apply migrations
docker exec -it backend-web-1 python manage.py migrate

# Create test data
docker exec -it backend-web-1 python manage.py run_seeders
```

#### Tool Registration
```bash
# Register/sync agent tools
docker exec -it backend-web-1 python manage.py cmd_register_tools

# Reset tool connections
docker exec -it backend-web-1 python manage.py cmd_tool_connect --reset
```

### Monitoring and Debugging

#### Admin Interface
- **Benchmark History**: `/admin/benchmarks/history/`
- **Tool Management**: `/admin/tools/`
- **User Profiles**: `/admin/users/`

#### Debug Ports
- **Web**: 5678 (Django debugpy)
- **Celery**: 5680 (Celery worker debugpy)
- **Test Debug**: 5681 (Test debugging)

#### Log Analysis
- **Benchmark Execution**: `apps.main.services.benchmark_manager`
- **Workflow Execution**: `apps.main.services.async_workflow_manager`
- **Agent Communications**: Available in benchmark run details

### Quality Assurance

#### Semantic Evaluation
- Uses real LLM models (Mistral, OpenAI) to evaluate response quality
- Dimension-based scoring (Tone, Content, Approach, Structure)
- Trust-level adaptive criteria

#### Performance Metrics
- **Execution Time**: Stage-level timing analysis
- **Token Usage**: Input/output token tracking with cost estimation
- **Tool Calls**: Detailed tool usage breakdown
- **Success Rate**: Statistical analysis across multiple runs

### Best Practices for Orchestrator Role

1. **Always verify container status** before running commands
2. **Use appropriate execution modes** - mock for development, real for validation
3. **Monitor Celery logs** when running workflow benchmarks
4. **Check benchmark history** in admin interface for trends
5. **Validate tool registration** after code changes
6. **Use volume mounts** for persistent data and live code updates

### Common Troubleshooting

#### Database Field Errors
- Check model relationships (ForeignKey vs direct field access)
- Verify field names (`created_on` vs `created_at`)
- Use proper ORM queries (`user_profile__id` for ForeignKey relationships)

#### Container Issues
- Ensure all dependencies are running (`docker compose ps`)
- Check volume mounts for code synchronization
- Verify environment variables in `.env` files

#### Benchmark Failures
- Check Celery worker logs for detailed error information
- Verify user profile IDs exist in database
- Ensure tool registration is current

This guide provides the foundation for understanding and orchestrating the Goali development ecosystem with a focus on benchmark-driven quality assurance.
