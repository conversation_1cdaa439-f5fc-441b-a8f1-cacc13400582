# Debug Infrastructure Guide

## Overview

This document provides a comprehensive guide to Goali's debug infrastructure, including Docker configuration, container management, and debugging workflows.

## Current Docker Configuration

### Services Overview

Our Docker setup consists of 6 main services:

1. **web** - Main Django application server
2. **web-test** - Isolated testing environment
3. **debug-tests** - Test debugging with remote debugger
4. **db** - PostgreSQL production database
5. **test-db** - PostgreSQL testing database
6. **redis** - Redis for Celery and Channels
7. **celery** - Background task processor
8. **grafana** - Benchmark visualization
9. **prometheus** - Metrics collection

### Container Configuration Analysis

#### Web Service (Main Application)
```yaml
web:
  build: .
  ports:
    - "8000:8000"      # Django server
    - "5678:5678"      # Django debugging (debugpy)
  volumes:
    - .:/usr/src/app   # Live code reloading
  environment:
    - DJANGO_SETTINGS_MODULE=config.settings.dev
    - WAIT_FOR_DEBUGGER=${WAIT_FOR_DEBUGGER:-false}
  command: >
    bash -c "/usr/src/app/entrypoint.sh setup &&
            if [ \"${WAIT_FOR_DEBUGGER}\" = \"true\" ]; then
                exec python debug_django.py --wait-for-debugger runserver 0.0.0.0:8000
            else
                exec uvicorn config.asgi:application --host 0.0.0.0 --port 8000 --workers 1 --reload
            fi"
```

**Key Features:**
- Live code reloading via volume mount
- Conditional debugger support
- ASGI server with uvicorn for WebSocket support
- Graceful shutdown with 1-minute timeout

#### Celery Service (Background Tasks)
```yaml
celery:
  build: .
  ports:
    - "5680:5680"      # Celery debugging
  volumes:
    - .:/usr/src/app   # Live code reloading
  command: >
    bash -c "/usr/src/app/entrypoint.sh setup &&
            python -m debugpy --listen 0.0.0.0:5680 -m celery -A config worker --loglevel=info"
```

**Key Features:**
- Remote debugging support on port 5680
- Live code reloading
- Automatic restart on failure

#### Test Services
```yaml
web-test:
  build: .
  env_file: .env.test
  environment:
    - DJANGO_SETTINGS_MODULE=config.settings.test
    - TESTING=true
  command: >
    bash -c "python ultimate_test_setup.py &&
            python -Xfrozen_modules=off -m pytest -c pytest.ini --reuse-db"

debug-tests:
  extends: web-test
  ports:
    - "5681:5681"      # Test debugging
  command: >
    bash -c "bash scripts/setup_test_env.sh &&
            python -Xfrozen_modules=off -m debugpy --wait-for-client --listen 0.0.0.0:5681 $PYTEST_ARGS"
```

## Debugging Workflows

### 1. Web Application Debugging

#### Standard Development Mode
```bash
# Start all services
docker-compose up

# Access application at http://localhost:8000
# Logs are visible in terminal
```

#### Remote Debugging Mode
```bash
# Set environment variable
export WAIT_FOR_DEBUGGER=true

# Start services
docker-compose up

# Connect debugger to localhost:5678
# Application waits for debugger connection
```

### 2. Celery Task Debugging

```bash
# Celery runs with debugpy on port 5680
# Connect your IDE to localhost:5680 for task debugging
```

### 3. Test Debugging

#### Run Tests Normally
```bash
docker-compose run web-test
```

#### Debug Tests
```bash
# Start debug-tests service
docker-compose run debug-tests

# Connect debugger to localhost:5681
# Set PYTEST_ARGS for specific tests
export PYTEST_ARGS="-k test_specific_function"
```

## Container Lifecycle Management

### Startup Sequence

1. **Database Services** (db, test-db, redis) start first
2. **Health checks** ensure databases are ready
3. **Application services** (web, celery) start after dependencies
4. **Setup scripts** run via entrypoint.sh

### Entrypoint Script Analysis

The `entrypoint.sh` script handles:

1. **Database migrations** (activity → user → remaining)
2. **Static file collection** (production only)
3. **Idempotent seeding** (users, environments, tools)
4. **Tool registration and connection**
5. **Benchmark scenario creation**
6. **Template loading**

### Volume Mounts and Caching

#### Current Configuration
```yaml
volumes:
  - .:/usr/src/app  # Full source code mount
```

**Pros:**
- Immediate code changes without rebuild
- Easy debugging and development

**Cons:**
- No caching of Python packages
- Slower startup times
- Potential permission issues

## Performance Optimization Recommendations

### 1. Implement Multi-Stage Builds

```dockerfile
# Build stage
FROM python:3.12-slim as builder
COPY requirements.txt .
RUN pip install --user -r requirements.txt

# Runtime stage
FROM python:3.12-slim
COPY --from=builder /root/.local /root/.local
COPY . /usr/src/app
```

### 2. Optimize Volume Mounts

```yaml
volumes:
  # Mount only source code, not dependencies
  - ./apps:/usr/src/app/apps
  - ./config:/usr/src/app/config
  - ./templates:/usr/src/app/templates
  # Use named volume for dependencies
  - python_packages:/usr/local/lib/python3.12/site-packages
```

### 3. Implement Build Caching

```yaml
web:
  build:
    context: .
    cache_from:
      - goali-web:latest
    args:
      BUILDKIT_INLINE_CACHE: 1
```

## WebSocket Configuration

### Current Setup
- **ASGI Application**: `config.asgi:application`
- **Channel Layer**: Redis-backed
- **Available Endpoints**:
  - `ws/game/` - Main user session
  - `ws/admin-tester/` - Admin testing
  - `ws/benchmark-dashboard/` - Benchmark monitoring

### Common Issues
1. **Incorrect WebSocket paths** - Ensure clients connect to valid endpoints
2. **Redis connection issues** - Check redis service health
3. **Authentication middleware** - WebSocket connections need proper auth

## Monitoring and Logging

### Container Logs
```bash
# View specific service logs
docker logs backend-web-1 --tail 50

# Follow logs in real-time
docker logs -f backend-celery-1

# View all services
docker-compose logs
```

### Health Checks
```bash
# Check service status
docker-compose ps

# Check database connectivity
docker exec backend-db-1 pg_isready -U postgres

# Check Redis connectivity
docker exec backend-redis-1 redis-cli ping
```

## Troubleshooting Guide

### Common Issues

#### 1. Container Won't Start
```bash
# Check logs
docker logs backend-web-1

# Check dependencies
docker-compose ps

# Rebuild if needed
docker-compose build web
```

#### 2. Database Connection Issues
```bash
# Check database health
docker exec backend-db-1 pg_isready -U postgres

# Reset database
docker-compose down
docker volume rm backend_postgres_data
docker-compose up
```

#### 3. WebSocket Connection Errors
```bash
# Check routing configuration
grep -r "websocket_urlpatterns" backend/

# Verify Redis connection
docker exec backend-redis-1 redis-cli ping

# Check ASGI configuration
docker exec backend-web-1 python -c "from config.asgi import application; print('ASGI OK')"
```

#### 4. Code Changes Not Reflected
```bash
# Check volume mounts
docker inspect backend-web-1 | grep Mounts -A 10

# Restart with rebuild
docker-compose down
docker-compose up --build
```

## Security Considerations

### Development vs Production

#### Development (Current)
- Debug mode enabled
- Verbose logging
- Direct volume mounts
- Open debugger ports

#### Production Recommendations
- Disable debug mode
- Restricted logging
- Copy files instead of mounting
- Remove debugger ports
- Use secrets management

## Recommended Docker Configuration Improvements

### 1. Enhanced Dockerfile with Dependency Caching

```dockerfile
# Multi-stage build for better caching
FROM python:3.12-slim as base

# Install system dependencies
RUN apt-get update && apt-get install -y \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /usr/src/app

# Copy and install Python dependencies first (for better caching)
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Development stage
FROM base as development
# Copy source code
COPY . .
# Set development environment
ENV DJANGO_SETTINGS_MODULE=config.settings.dev
ENV PYTHONPATH=/usr/src/app
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# Production stage
FROM base as production
# Copy source code
COPY . .
# Collect static files
RUN python manage.py collectstatic --noinput
# Set production environment
ENV DJANGO_SETTINGS_MODULE=config.settings.prod
```

### 2. Improved Docker Compose Configuration

```yaml
# Enhanced docker-compose.yml
services:
  web:
    build:
      context: .
      target: development  # Use development stage
      cache_from:
        - goali-web:latest
    volumes:
      # Mount only source directories, not the entire project
      - ./apps:/usr/src/app/apps:cached
      - ./config:/usr/src/app/config:cached
      - ./templates:/usr/src/app/templates:cached
      - ./static:/usr/src/app/static:cached
      # Use named volume for dependencies to preserve caching
      - python_deps:/usr/local/lib/python3.12/site-packages
    environment:
      - PYTHONPATH=/usr/src/app
      - DJANGO_SETTINGS_MODULE=config.settings.dev
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_started

volumes:
  python_deps:  # Named volume for Python packages
  postgres_data:
  postgres_test_data:
  grafana_data:
  prometheus_data:
```

### 3. Development vs Production Compose Files

#### docker-compose.dev.yml
```yaml
# Development-specific overrides
services:
  web:
    build:
      target: development
    volumes:
      - ./apps:/usr/src/app/apps:cached
      - ./config:/usr/src/app/config:cached
    environment:
      - DJANGO_DEBUG=1
      - WAIT_FOR_DEBUGGER=${WAIT_FOR_DEBUGGER:-false}
    ports:
      - "5678:5678"  # Debug port
```

#### docker-compose.prod.yml
```yaml
# Production-specific overrides
services:
  web:
    build:
      target: production
    environment:
      - DJANGO_DEBUG=0
    # No debug ports or volume mounts
```

## Next Steps and Improvements

### Immediate Improvements (Priority 1)
1. **Fix WebSocket routing** ✅ (Completed)
2. **Implement dependency caching with named volumes**
3. **Add proper health checks for all services**
4. **Separate source code mounts from dependency caching**

### Short-term Goals (Priority 2)
1. **Multi-stage Docker builds for better caching**
2. **Development/production compose file separation**
3. **Container resource limits and monitoring**
4. **Automated container health monitoring**

### Medium-term Goals (Priority 3)
1. **Implement proper secrets management**
2. **Add automated testing in CI/CD pipeline**
3. **Performance monitoring and optimization**
4. **Backup and disaster recovery procedures**

### Long-term Vision (Priority 4)
1. **Kubernetes deployment preparation**
2. **Microservices architecture evaluation**
3. **Advanced monitoring and alerting systems**
4. **Auto-scaling capabilities implementation**

## Implementation Commands

### Apply Immediate Improvements
```bash
# 1. Create named volume for dependencies
docker volume create backend_python_deps

# 2. Update docker-compose.yml with recommended changes
# 3. Rebuild with caching
docker-compose build --no-cache web

# 4. Test the improved setup
docker-compose up
```

### Verify Improvements
```bash
# Check volume usage
docker volume ls
docker volume inspect backend_python_deps

# Monitor container performance
docker stats

# Test code change reflection
# Edit a Python file and verify hot reload works
```
