name: monkfish-app
region: lon
services:
- name: goali-backend
  environment_slug: python
  instance_size_slug: apps-s-1vcpu-1gb
  instance_count: 2
  source_dir: backend
  github:
    branch: gguine/prod-slow
    deploy_on_push: true
    repo: elgui/goali
  run_command: gunicorn --worker-tmp-dir /dev/shm --bind 0.0.0.0:8080 config.wsgi:application
  build_command: python manage.py migrate --noinput && python manage.py shell -c "from django.contrib.auth import get_user_model; import os; User = get_user_model(); User.objects.create_superuser('admin', '<EMAIL>', os.environ.get('DJANGO_ADMIN_PASSWORD', 'defaultpassword')) if not User.objects.filter(username='admin').exists() else None"
  http_port: 8080
  envs:
  - key: DJANGO_SETTINGS_MODULE
    scope: RUN_AND_BUILD_TIME
    value: config.settings.prod
  - key: DJANGO_SECRET_KEY
    scope: RUN_AND_BUILD_TIME
    type: SECRET
  - key: MISTRAL_API_KEY
    scope: RUN_AND_BUILD_TIME
    type: SECRET
  - key: DJANGO_ADMIN_PASSWORD
    scope: BUILD_TIME
    type: SECRET
  - key: STATIC_ROOT
    scope: RUN_AND_BUILD_TIME
    value: /app/staticfiles
  - key: DJANGO_ALLOWED_HOSTS
    scope: RUN_AND_BUILD_TIME
    value: 127.0.0.1,localhost,monkfish-app-jvgae.ondigitalocean.app
  - key: DATABASE_URL
    scope: RUN_AND_BUILD_TIME
    type: SECRET
  - key: REDIS_URL
    scope: RUN_AND_BUILD_TIME
    type: SECRET
- name: goali-worker
  environment_slug: python
  instance_size_slug: apps-s-1vcpu-1gb
  instance_count: 1
  source_dir: backend
  github:
    branch: gguine/prod-slow
    deploy_on_push: true
    repo: elgui/goali
  run_command: celery -A config worker --loglevel=info --concurrency=2
  envs:
  - key: DJANGO_SETTINGS_MODULE
    scope: RUN_AND_BUILD_TIME
    value: config.settings.prod
  - key: DJANGO_SECRET_KEY
    scope: RUN_AND_BUILD_TIME
    type: SECRET
  - key: MISTRAL_API_KEY
    scope: RUN_AND_BUILD_TIME
    type: SECRET
  - key: DATABASE_URL
    scope: RUN_AND_BUILD_TIME
    type: SECRET
  - key: REDIS_URL
    scope: RUN_AND_BUILD_TIME
    type: SECRET
static_sites:
- name: goali-frontend
  github:
    branch: gguine/prod-slow
    deploy_on_push: true
    repo: elgui/goali
  build_command: npm ci && npm run build:prod
  source_dir: frontend
  output_dir: /dist
ingress:
  rules:
  - component:
      name: goali-backend
      preserve_path_prefix: true
    match:
      path:
        prefix: /api
  - component:
      name: goali-backend
      preserve_path_prefix: true
    match:
      path:
        prefix: /health
  - component:
      name: goali-backend
      preserve_path_prefix: true
    match:
      path:
        prefix: /admin
  - component:
      name: goali-backend
      preserve_path_prefix: true
    match:
      path:
        prefix: /ws
  - component:
      name: goali-frontend
    match:
      path:
        prefix: /
alerts:
- rule: DEPLOYMENT_FAILED
- rule: DOMAIN_FAILED
features:
- buildpack-stack=ubuntu-22
