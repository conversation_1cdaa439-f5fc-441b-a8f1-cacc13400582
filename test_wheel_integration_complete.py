#!/usr/bin/env python3
"""
Complete Wheel Generation Integration Test

Tests the full wheel generation pipeline including:
- Activity selection and tailoring
- Wheel building and persistence
- Color assignment and domain mapping
- Frontend data structure validation
"""

import os
import sys
import django
import asyncio
import json
from datetime import datetime

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
sys.path.append('/usr/src/app')
django.setup()

from apps.main.domain.services.wheel_generation_service import WheelGenerationService
from apps.main.domain.models.wheel_models import WheelGenerationRequest, WheelConfiguration
from apps.main.domain.models.activity_models import ActivitySelectionCriteria
from apps.main.domain.enums.domain_enums import EnergyLevel, TrustPhase, DomainCode
from apps.user.models import UserProfile


async def test_complete_wheel_integration():
    """Test complete wheel generation with quality and color validation."""
    
    print("🧪 COMPLETE WHEEL GENERATION INTEGRATION TEST")
    print("=" * 60)
    
    # Test parameters
    user_id = 1
    time_available = 10  # minutes
    energy_level = 100   # percent
    
    print(f"📊 Test Parameters:")
    print(f"   - User ID: {user_id}")
    print(f"   - Time Available: {time_available} minutes")
    print(f"   - Energy Level: {energy_level}%")
    print()
    
    try:
        # Get user profile
        user_profile = await UserProfile.objects.aget(id=user_id)
        print(f"👤 User Profile: ID {user_profile.id}")
        
        # Create wheel generation request
        selection_criteria = ActivitySelectionCriteria(
            time_available=time_available,
            energy_level=energy_level,  # Use integer value
            trust_phase=TrustPhase.FOUNDATION
        )

        # Create wheel configuration
        wheel_config = WheelConfiguration(
            trust_phase=TrustPhase.FOUNDATION,
            domain_preferences={DomainCode.GENERAL: 1.0}
        )

        request = WheelGenerationRequest(
            user_profile_id=str(user_id),
            selection_criteria=selection_criteria,
            wheel_config=wheel_config
        )
        
        print(f"🚀 Starting wheel generation...")
        start_time = datetime.now()
        
        # Generate wheel
        service = WheelGenerationService()
        result = await service.generate_wheel(request)
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        print(f"⏱️  Generation completed in {duration:.2f} seconds")
        print()
        
        # Validate result
        if not result or not result.wheel:
            print("❌ FAILED: No wheel generated")
            return False
            
        wheel = result.wheel
        print(f"🎡 Wheel Generated Successfully!")
        print(f"   - Wheel ID: {wheel.id}")
        print(f"   - Wheel Name: {wheel.name}")
        print(f"   - Items Count: {len(wheel.items)}")
        print()
        
        # Validate wheel items
        if len(wheel.items) == 0:
            print("❌ FAILED: Wheel has no items")
            return False
            
        print(f"📋 Wheel Items Analysis:")
        domains_found = set()
        colors_found = set()
        
        for i, item in enumerate(wheel.items, 1):
            domain = getattr(item, 'domain', 'unknown')
            color = getattr(item, 'color', '#UNKNOWN')
            percentage = getattr(item, 'percentage', 0)
            
            domains_found.add(str(domain))
            colors_found.add(color)
            
            print(f"   {i}. {item.name}")
            print(f"      - Domain: {domain}")
            print(f"      - Color: {color}")
            print(f"      - Percentage: {percentage:.1f}%")
            print(f"      - Challenge: {getattr(item, 'challenge_rating', 'N/A')}")
            print()
        
        # Quality Analysis
        print(f"🔍 Quality Analysis:")
        print(f"   - Total Items: {len(wheel.items)}")
        print(f"   - Unique Domains: {len(domains_found)} ({', '.join(sorted(domains_found))})")
        print(f"   - Unique Colors: {len(colors_found)} ({', '.join(sorted(colors_found))})")
        print()
        
        # Validation checks
        checks_passed = 0
        total_checks = 6
        
        # Check 1: Minimum items
        if len(wheel.items) >= 3:
            print("✅ CHECK 1: Minimum 3 items")
            checks_passed += 1
        else:
            print("❌ CHECK 1: Less than 3 items")
            
        # Check 2: Domain diversity
        if len(domains_found) >= 2:
            print("✅ CHECK 2: Domain diversity (2+ domains)")
            checks_passed += 1
        else:
            print("❌ CHECK 2: Poor domain diversity")
            
        # Check 3: Color assignment
        if len(colors_found) >= 2 and '#UNKNOWN' not in colors_found:
            print("✅ CHECK 3: Color assignment working")
            checks_passed += 1
        else:
            print("❌ CHECK 3: Color assignment issues")
            
        # Check 4: Percentage totals
        total_percentage = sum(getattr(item, 'percentage', 0) for item in wheel.items)
        if 95 <= total_percentage <= 105:  # Allow some rounding tolerance
            print("✅ CHECK 4: Percentage totals valid")
            checks_passed += 1
        else:
            print(f"❌ CHECK 4: Percentage totals invalid ({total_percentage:.1f}%)")
            
        # Check 5: Activity names
        named_items = [item for item in wheel.items if item.name and item.name.strip()]
        if len(named_items) == len(wheel.items):
            print("✅ CHECK 5: All items have names")
            checks_passed += 1
        else:
            print("❌ CHECK 5: Some items missing names")
            
        # Check 6: Performance
        if duration < 30:
            print("✅ CHECK 6: Performance acceptable (<30s)")
            checks_passed += 1
        else:
            print("❌ CHECK 6: Performance too slow (>30s)")
        
        print()
        print(f"📊 FINAL SCORE: {checks_passed}/{total_checks} checks passed")
        
        if checks_passed == total_checks:
            print("🎉 INTEGRATION TEST: COMPLETE SUCCESS!")
            return True
        elif checks_passed >= 4:
            print("⚠️  INTEGRATION TEST: PARTIAL SUCCESS")
            return True
        else:
            print("❌ INTEGRATION TEST: FAILED")
            return False
            
    except Exception as e:
        print(f"❌ INTEGRATION TEST ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = asyncio.run(test_complete_wheel_integration())
    sys.exit(0 if success else 1)
