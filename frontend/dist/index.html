<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no" />
  <meta name="description" content="Goali - Life coaching app with personalized activity recommendations" />
  <meta name="theme-color" content="#4A90E2" />
  
  <!-- Preconnect to improve performance -->
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  
  <!-- Favicon -->
  <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
  <link rel="icon" type="image/png" href="/favicon.png" />
  
  <!-- Apple Touch Icon for iOS -->
  <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
  
  <!-- Manifest for PWA (disabled for demo) -->
  <!-- <link rel="manifest" href="/manifest.json" /> -->
  
  <!-- Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
  
  <title>Goali - Your Life Coaching Companion</title>
  
  <!-- Critical CSS for initial render -->
  <style>
    /* Reset and base styles */
    *, *::before, *::after {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }
    
    html {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      line-height: 1.5;
      -webkit-text-size-adjust: 100%;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
    
    body {
      margin: 0;
      padding: 0;
      min-height: 100vh;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: #333;
      overflow-x: hidden;
      /* Prevent zoom on iOS */
      touch-action: manipulation;
    }
    
    /* Loading screen */
    .loading-screen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      transition: opacity 0.5s ease-out;
    }
    
    .loading-screen.hidden {
      opacity: 0;
      pointer-events: none;
    }
    
    .loading-logo {
      width: 80px;
      height: 80px;
      margin-bottom: 24px;
      animation: pulse 2s ease-in-out infinite;
    }
    
    .loading-text {
      color: white;
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 16px;
    }
    
    .loading-spinner {
      width: 32px;
      height: 32px;
      border: 3px solid rgba(255, 255, 255, 0.3);
      border-top: 3px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    @keyframes pulse {
      0%, 100% { transform: scale(1); opacity: 1; }
      50% { transform: scale(1.05); opacity: 0.8; }
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    /* App container */
    #app {
      width: 100%;
      min-height: 100vh;
      position: relative;
    }
    
    /* Hide app initially */
    app-shell {
      opacity: 0;
      transition: opacity 0.5s ease-in;
    }
    
    app-shell.loaded {
      opacity: 1;
    }
    
    /* Error fallback */
    .error-fallback {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      color: white;
      padding: 20px;
      max-width: 400px;
    }
    
    .error-fallback h1 {
      font-size: 24px;
      margin-bottom: 16px;
    }
    
    .error-fallback p {
      font-size: 16px;
      margin-bottom: 20px;
      opacity: 0.9;
    }
    
    .error-fallback button {
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: white;
      padding: 12px 24px;
      border-radius: 8px;
      font-size: 16px;
      cursor: pointer;
      transition: background 0.2s ease;
    }
    
    .error-fallback button:hover {
      background: rgba(255, 255, 255, 0.3);
    }

    /* Debug Panel Styles */
    .debug-panel {
      position: fixed;
      top: 50px;
      left: 10px;
      width: 320px;
      max-height: 80vh;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border-radius: 12px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      z-index: 10000;
      font-family: 'Inter', sans-serif;
      overflow: hidden;
    }

    .debug-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }

    .debug-header h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }

    .debug-close {
      background: none;
      border: none;
      color: white;
      font-size: 24px;
      cursor: pointer;
      padding: 0;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: background 0.2s;
    }

    .debug-close:hover {
      background: rgba(255, 255, 255, 0.2);
    }

    .debug-content {
      padding: 20px;
      max-height: calc(80vh - 60px);
      overflow-y: auto;
    }

    .debug-section {
      margin-bottom: 20px;
    }

    .debug-section:last-child {
      margin-bottom: 0;
    }

    .debug-section h4 {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 600;
      color: #333;
      border-bottom: 1px solid #eee;
      padding-bottom: 8px;
    }

    .debug-btn {
      display: block;
      width: 100%;
      padding: 10px 16px;
      margin-bottom: 8px;
      background: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 6px;
      color: #495057;
      font-size: 13px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;
      text-align: left;
    }

    .debug-btn:hover {
      background: #e9ecef;
      border-color: #adb5bd;
      transform: translateY(-1px);
    }

    .debug-btn.primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-color: transparent;
    }

    .debug-btn.primary:hover {
      background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
      transform: translateY(-1px);
    }


  </style>
  <script type="module" crossorigin src="./assets/main-DhKaCUXn.js"></script>
  <link rel="modulepreload" crossorigin href="./assets/pixi-4Bm1fAf-.js">
  <link rel="modulepreload" crossorigin href="./assets/lit-KavRT6Ey.js">
  <link rel="modulepreload" crossorigin href="./assets/matter-oWEGo7PC.js">
  <link rel="stylesheet" crossorigin href="./assets/main-Dd2MvPK5.css">
</head>
<body>
  <!-- Loading screen -->
  <div id="loading-screen" class="loading-screen">
    <div class="loading-logo">
      <!-- Placeholder for logo SVG -->
      <svg viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="40" cy="40" r="35" stroke="white" stroke-width="3" fill="none" opacity="0.3"/>
        <circle cx="40" cy="40" r="25" stroke="white" stroke-width="2" fill="none"/>
        <circle cx="40" cy="40" r="8" fill="white"/>
      </svg>
    </div>
    <div class="loading-text">Loading Goali...</div>
    <div class="loading-spinner"></div>
  </div>

  <!-- Debug Panel (only visible for staff/development) -->
  <div id="debug-panel" class="debug-panel" style="display: none;">
    <div class="debug-header">
      <h3>🔧 Debug Panel</h3>
      <button id="debug-close" class="debug-close">×</button>
    </div>
    <div class="debug-content">
      <div class="debug-section">
        <h4>Cache Management</h4>
        <button id="clear-app-cache" class="debug-btn">Clear App Cache</button>
        <button id="clear-local-storage" class="debug-btn">Clear Local Storage</button>
        <button id="clear-session-storage" class="debug-btn">Clear Session Storage</button>
        <button id="clear-all-cache" class="debug-btn primary">Clear All Cache</button>
      </div>
      <div class="debug-section">
        <h4>Wheel Generation</h4>
        <button id="force-wheel-regeneration" class="debug-btn">Force Wheel Regeneration</button>
        <button id="test-energy-distribution" class="debug-btn">Test Energy Distribution</button>
      </div>
      <div class="debug-section">
        <h4>Domain & Color Testing</h4>
        <button id="test-domain-colors" class="debug-btn">Test Domain Colors</button>
        <button id="validate-wheel-data" class="debug-btn">Validate Wheel Data</button>
      </div>
    </div>
  </div>



  <!-- Main application -->
  <div id="app">
    <app-shell></app-shell>
  </div>

  <!-- Error fallback (hidden by default) -->
  <div id="error-fallback" class="error-fallback" style="display: none;">
    <h1>🎯 Goali Demo Mode</h1>
    <p>The backend server is not available, but you can still try the spinning wheel with sample activities!</p>
    <button onclick="window.location.reload()">Try Again</button>
  </div>

  <!-- Debug Panel Script -->
  <script>
    // Debug Panel Functionality
    document.addEventListener('DOMContentLoaded', function() {
      const debugPanel = document.getElementById('debug-panel');
      const debugClose = document.getElementById('debug-close');

      // Close debug panel
      debugClose?.addEventListener('click', () => {
        debugPanel.style.display = 'none';
      });

      // Cache Management Functions
      async function clearAppCache() {
        try {
          // Clear browser cache
          if ('caches' in window) {
            const cacheNames = await caches.keys();
            await Promise.all(cacheNames.map(name => caches.delete(name)));
          }

          // Clear service worker cache
          if ('serviceWorker' in navigator) {
            const registrations = await navigator.serviceWorker.getRegistrations();
            await Promise.all(registrations.map(reg => reg.unregister()));
          }

          console.log('✅ App cache cleared');
          alert('✅ App cache cleared successfully!');
        } catch (error) {
          console.error('❌ Error clearing app cache:', error);
          alert('❌ Error clearing app cache: ' + error.message);
        }
      }

      function clearLocalStorage() {
        try {
          localStorage.clear();
          console.log('✅ Local storage cleared');
          alert('✅ Local storage cleared successfully!');
        } catch (error) {
          console.error('❌ Error clearing local storage:', error);
          alert('❌ Error clearing local storage: ' + error.message);
        }
      }

      function clearSessionStorage() {
        try {
          sessionStorage.clear();
          console.log('✅ Session storage cleared');
          alert('✅ Session storage cleared successfully!');
        } catch (error) {
          console.error('❌ Error clearing session storage:', error);
          alert('❌ Error clearing session storage: ' + error.message);
        }
      }

      async function clearAllCache() {
        try {
          await clearAppCache();
          clearLocalStorage();
          clearSessionStorage();

          // Force reload to ensure fresh start
          setTimeout(() => {
            window.location.reload(true);
          }, 1000);

          console.log('✅ All cache cleared, reloading...');
          alert('✅ All cache cleared! Page will reload in 1 second.');
        } catch (error) {
          console.error('❌ Error clearing all cache:', error);
          alert('❌ Error clearing all cache: ' + error.message);
        }
      }

      // Wheel Generation Functions
      function forceWheelRegeneration() {
        try {
          const appShell = document.querySelector('app-shell');
          if (appShell && appShell.generateWheel) {
            // Clear any cached wheel data
            if (appShell.wheelData) {
              appShell.wheelData = null;
            }

            // Force regeneration with unique timestamp
            const uniqueContext = {
              energy_level: 100,
              time_available: 10,
              force_regeneration: true,
              debug_timestamp: Date.now()
            };

            appShell.generateWheel(uniqueContext);
            console.log('✅ Forced wheel regeneration');
            alert('✅ Forced wheel regeneration started!');
          } else {
            throw new Error('App shell or generateWheel method not found');
          }
        } catch (error) {
          console.error('❌ Error forcing wheel regeneration:', error);
          alert('❌ Error forcing wheel regeneration: ' + error.message);
        }
      }

      function testEnergyDistribution() {
        try {
          const appShell = document.querySelector('app-shell');
          if (appShell) {
            // Test with 100% energy to verify physical activity distribution
            appShell.energyLevel = 100;
            appShell.timeAvailable = 10;

            if (appShell.generateWheel) {
              appShell.generateWheel({
                energy_level: 100,
                time_available: 10,
                test_energy_distribution: true
              });
            }

            console.log('✅ Testing energy distribution with 100% energy');
            alert('✅ Testing energy distribution with 100% energy!');
          } else {
            throw new Error('App shell not found');
          }
        } catch (error) {
          console.error('❌ Error testing energy distribution:', error);
          alert('❌ Error testing energy distribution: ' + error.message);
        }
      }

      // Domain & Color Testing Functions
      function testDomainColors() {
        try {
          // Test the domain color service
          const testDomains = ['physical', 'productive_practical', 'emotional', 'creative', 'social', 'wellness'];
          const results = [];

          testDomains.forEach(domain => {
            if (window.getDomainColor) {
              const color = window.getDomainColor(domain);
              results.push(`${domain}: ${color}`);
              console.log(`✅ ${domain} → ${color}`);
            }
          });

          alert('✅ Domain color test results:\\n' + results.join('\\n'));
        } catch (error) {
          console.error('❌ Error testing domain colors:', error);
          alert('❌ Error testing domain colors: ' + error.message);
        }
      }

      function validateWheelData() {
        try {
          const appShell = document.querySelector('app-shell');
          if (appShell && appShell.wheelData) {
            const wheelData = appShell.wheelData;
            const items = wheelData.items || [];

            const validation = {
              itemCount: items.length,
              domains: [...new Set(items.map(item => item.domain))],
              physicalCount: items.filter(item => item.domain === 'physical' || item.domain?.startsWith('phys')).length,
              colorsAssigned: items.filter(item => item.color && item.color !== '#95A5A6').length
            };

            const physicalPercentage = (validation.physicalCount / validation.itemCount * 100).toFixed(1);

            const report = [
              `Items: ${validation.itemCount}`,
              `Domains: ${validation.domains.join(', ')}`,
              `Physical activities: ${validation.physicalCount}/${validation.itemCount} (${physicalPercentage}%)`,
              `Colors assigned: ${validation.colorsAssigned}/${validation.itemCount}`
            ];

            console.log('✅ Wheel validation:', validation);
            alert('✅ Wheel Validation Results:\\n' + report.join('\\n'));
          } else {
            throw new Error('No wheel data found');
          }
        } catch (error) {
          console.error('❌ Error validating wheel data:', error);
          alert('❌ Error validating wheel data: ' + error.message);
        }
      }

      // Bind event listeners
      document.getElementById('clear-app-cache')?.addEventListener('click', clearAppCache);
      document.getElementById('clear-local-storage')?.addEventListener('click', clearLocalStorage);
      document.getElementById('clear-session-storage')?.addEventListener('click', clearSessionStorage);
      document.getElementById('clear-all-cache')?.addEventListener('click', clearAllCache);
      document.getElementById('force-wheel-regeneration')?.addEventListener('click', forceWheelRegeneration);
      document.getElementById('test-energy-distribution')?.addEventListener('click', testEnergyDistribution);
      document.getElementById('test-domain-colors')?.addEventListener('click', testDomainColors);
      document.getElementById('validate-wheel-data')?.addEventListener('click', validateWheelData);
    });
  </script>

  <!-- Main application script -->
  
  <!-- Service worker registration (disabled for demo) -->
  <!--
  <script>
    // Register service worker for PWA functionality
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
          .then((registration) => {
            console.log('SW registered: ', registration);
          })
          .catch((registrationError) => {
            console.log('SW registration failed: ', registrationError);
          });
      });
    }
  </script>
  -->
</body>
</html>
