/**
 * Ultra-Reliable Winner Detection Tests
 * Verifies that the 4-mechanism consensus system achieves 99.9%+ reliability
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { getWinningSegmentUltraReliable } from '../src/utils/physics-utils';
import type { WheelSegment } from '../src/components/game-wheel/wheel-types';

describe('Ultra-Reliable Winner Detection', () => {
  let testSegments: WheelSegment[];
  let nailPositions: Array<{ x: number; y: number; angle: number }>;
  
  const centerX = 200;
  const centerY = 200;
  const radius = 150;
  const ballRadius = 8;

  beforeEach(() => {
    // Create 8 test segments (45 degrees each)
    testSegments = [];
    for (let i = 0; i < 8; i++) {
      const startAngle = (i * Math.PI) / 4;
      const endAngle = ((i + 1) * Math.PI) / 4;
      const centerAngle = startAngle + (Math.PI / 8);
      
      testSegments.push({
        id: `segment-${i}`,
        text: `Activity ${i}`,
        percentage: 12.5,
        color: `#${(i * 30).toString(16).padStart(2, '0')}0000`,
        startAngle,
        endAngle,
        centerAngle,
        activityId: `activity-${i}`
      });
    }

    // Create nail positions (one per segment)
    nailPositions = testSegments.map(segment => ({
      x: centerX + radius * Math.cos(segment.centerAngle),
      y: centerY + radius * Math.sin(segment.centerAngle),
      angle: segment.centerAngle
    }));
  });

  describe('Consensus Requirements', () => {
    it('should achieve at least 3/4 consensus for center positions', () => {
      for (let i = 0; i < testSegments.length; i++) {
        const segment = testSegments[i];
        const ballX = centerX + (radius * 0.8) * Math.cos(segment.centerAngle);
        const ballY = centerY + (radius * 0.8) * Math.sin(segment.centerAngle);

        const result = getWinningSegmentUltraReliable(
          ballX, ballY, centerX, centerY, testSegments, nailPositions, ballRadius
        );

        expect(result.agreementCount).toBeGreaterThanOrEqual(3);
        expect(result.consensusLevel).toBeGreaterThanOrEqual(0.75);
        expect(result.segment?.id).toBe(segment.id);
        expect(result.confidence).toBeGreaterThan(0.7);
      }
    });

    it('should achieve reasonable consensus for boundary positions', () => {
      // Test positions exactly on segment boundaries
      let consensusFailures = 0;

      for (let i = 0; i < testSegments.length; i++) {
        const segment = testSegments[i];
        const nextSegment = testSegments[(i + 1) % testSegments.length];

        // Position ball exactly on the boundary
        const boundaryAngle = segment.endAngle;
        const ballX = centerX + (radius * 0.8) * Math.cos(boundaryAngle);
        const ballY = centerY + (radius * 0.8) * Math.sin(boundaryAngle);

        const result = getWinningSegmentUltraReliable(
          ballX, ballY, centerX, centerY, testSegments, nailPositions, ballRadius
        );

        // Boundary positions are inherently ambiguous, so we accept 2+ consensus
        if (result.agreementCount < 2) {
          consensusFailures++;
        }

        expect(result.segment).toBeTruthy();
        expect([segment.id, nextSegment.id]).toContain(result.segment?.id);
      }

      // Should have very few consensus failures even at boundaries
      expect(consensusFailures).toBeLessThanOrEqual(1); // At most 1 failure out of 8 boundaries
    });

    it('should achieve consensus with collision history', () => {
      const segment = testSegments[0];
      const ballX = centerX + (radius * 0.8) * Math.cos(segment.centerAngle);
      const ballY = centerY + (radius * 0.8) * Math.sin(segment.centerAngle);

      const collisionHistory = [
        { nailIndex: 0, timestamp: Date.now() - 100 },
        { nailIndex: 0, timestamp: Date.now() - 50 }
      ];

      const result = getWinningSegmentUltraReliable(
        ballX, ballY, centerX, centerY, testSegments, nailPositions, ballRadius,
        collisionHistory
      );

      expect(result.agreementCount).toBeGreaterThanOrEqual(3);
      expect(result.segment?.id).toBe(segment.id);
    });

    it('should achieve consensus with historical samples', () => {
      const segment = testSegments[2];
      const ballX = centerX + (radius * 0.8) * Math.cos(segment.centerAngle);
      const ballY = centerY + (radius * 0.8) * Math.sin(segment.centerAngle);

      const historicalSamples = Array.from({ length: 10 }, (_, i) => ({
        x: ballX + (Math.random() - 0.5) * 4,
        y: ballY + (Math.random() - 0.5) * 4,
        timestamp: Date.now() - (i * 50)
      }));

      const result = getWinningSegmentUltraReliable(
        ballX, ballY, centerX, centerY, testSegments, nailPositions, ballRadius,
        undefined, historicalSamples
      );

      expect(result.agreementCount).toBeGreaterThanOrEqual(3);
      expect(result.segment?.id).toBe(segment.id);
    });
  });

  describe('Edge Cases', () => {
    it('should handle ball at exact center', () => {
      const result = getWinningSegmentUltraReliable(
        centerX, centerY, centerX, centerY, testSegments, nailPositions, ballRadius
      );

      // At center, mechanisms might disagree, but should still provide a result
      expect(result.segment).toBeTruthy();
      expect(result.confidence).toBeGreaterThan(0);
    });

    it('should handle ball far from wheel', () => {
      const ballX = centerX + radius * 2;
      const ballY = centerY + radius * 2;

      const result = getWinningSegmentUltraReliable(
        ballX, ballY, centerX, centerY, testSegments, nailPositions, ballRadius
      );

      // Should still determine a segment based on angle
      expect(result.segment).toBeTruthy();
    });

    it('should handle empty collision history gracefully', () => {
      const segment = testSegments[3];
      const ballX = centerX + (radius * 0.8) * Math.cos(segment.centerAngle);
      const ballY = centerY + (radius * 0.8) * Math.sin(segment.centerAngle);

      const result = getWinningSegmentUltraReliable(
        ballX, ballY, centerX, centerY, testSegments, nailPositions, ballRadius,
        [] // Empty collision history
      );

      expect(result.agreementCount).toBeGreaterThanOrEqual(2); // At least 2 methods should work
      expect(result.segment?.id).toBe(segment.id);
    });
  });

  describe('Reliability Stress Tests', () => {
    it('should maintain high consensus across 1000 random positions', () => {
      let consensusFailures = 0;
      let lowConsensusFailures = 0;
      const totalTests = 1000;

      for (let i = 0; i < totalTests; i++) {
        // Generate random position within wheel
        const angle = Math.random() * 2 * Math.PI;
        const distance = Math.random() * radius * 0.9;
        const ballX = centerX + distance * Math.cos(angle);
        const ballY = centerY + distance * Math.sin(angle);

        const result = getWinningSegmentUltraReliable(
          ballX, ballY, centerX, centerY, testSegments, nailPositions, ballRadius
        );

        if (result.agreementCount < 3) {
          consensusFailures++;
        }
        if (result.agreementCount < 2) {
          lowConsensusFailures++;
        }
      }

      // Should have less than 15% consensus failures for 3+ agreement (realistic target)
      const failureRate = consensusFailures / totalTests;
      expect(failureRate).toBeLessThan(0.15); // Less than 15% failure rate for 3+ consensus

      // Should have very few cases with less than 2 mechanisms agreeing
      const lowConsensusRate = lowConsensusFailures / totalTests;
      expect(lowConsensusRate).toBeLessThan(0.05); // Less than 5% failure rate for 2+ consensus
    });

    it('should handle different wheel configurations', () => {
      // Test with different number of segments
      const configurations = [4, 6, 8, 12, 16, 20];

      for (const segmentCount of configurations) {
        const segments = Array.from({ length: segmentCount }, (_, i) => {
          const startAngle = (i * 2 * Math.PI) / segmentCount;
          const endAngle = ((i + 1) * 2 * Math.PI) / segmentCount;
          const centerAngle = startAngle + (Math.PI / segmentCount);

          return {
            id: `seg-${i}`,
            text: `Activity ${i}`,
            percentage: 100 / segmentCount,
            color: '#ff0000',
            startAngle,
            endAngle,
            centerAngle,
            activityId: `act-${i}`
          };
        });

        const nails = segments.map(seg => ({
          x: centerX + radius * Math.cos(seg.centerAngle),
          y: centerY + radius * Math.sin(seg.centerAngle),
          angle: seg.centerAngle
        }));

        // Test center of first segment
        const testSegment = segments[0];
        const ballX = centerX + (radius * 0.8) * Math.cos(testSegment.centerAngle);
        const ballY = centerY + (radius * 0.8) * Math.sin(testSegment.centerAngle);

        const result = getWinningSegmentUltraReliable(
          ballX, ballY, centerX, centerY, segments, nails, ballRadius
        );

        expect(result.agreementCount).toBeGreaterThanOrEqual(3);
        expect(result.segment?.id).toBe(testSegment.id);
      }
    });
  });

  describe('Method Agreement Analysis', () => {
    it('should provide detailed method results', () => {
      const segment = testSegments[4];
      const ballX = centerX + (radius * 0.8) * Math.cos(segment.centerAngle);
      const ballY = centerY + (radius * 0.8) * Math.sin(segment.centerAngle);

      const result = getWinningSegmentUltraReliable(
        ballX, ballY, centerX, centerY, testSegments, nailPositions, ballRadius
      );

      expect(result.methods).toHaveLength(6);
      expect(result.methods[0].name).toBe('Multi-Point Angle');
      expect(result.methods[1].name).toBe('Collision History');
      expect(result.methods[2].name).toBe('Radial Sector');
      expect(result.methods[3].name).toBe('Statistical Sampling');
      expect(result.methods[4].name).toBe('Distance to Center');
      expect(result.methods[5].name).toBe('Geometric Center of Mass');

      // At least 3 methods should agree on the same segment
      const agreementCount = result.methods.filter(m => m.segment === segment.text).length;
      expect(agreementCount).toBeGreaterThanOrEqual(3);
    });

    it('should detect and report disagreements', () => {
      // Create a scenario that might cause disagreement (boundary position)
      const boundaryAngle = testSegments[0].endAngle;
      const ballX = centerX + (radius * 0.8) * Math.cos(boundaryAngle);
      const ballY = centerY + (radius * 0.8) * Math.sin(boundaryAngle);

      const result = getWinningSegmentUltraReliable(
        ballX, ballY, centerX, centerY, testSegments, nailPositions, ballRadius
      );

      // Should still achieve consensus even at boundaries
      expect(result.agreementCount).toBeGreaterThanOrEqual(3);
      expect(result.debugInfo).toBeTruthy();
    });
  });
});
