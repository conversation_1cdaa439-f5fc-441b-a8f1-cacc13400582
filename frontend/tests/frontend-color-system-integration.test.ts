/**
 * Frontend Color System Integration Tests
 * 
 * Tests that validate the complete frontend color system works correctly
 * without any backend dependencies, including edge cases and performance.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { 
  getDomainColor, 
  getAllDomainColors, 
  applyColorsToWheel,
  validateWheelDomains
} from '../src/services/domainColorService.js';

describe('Frontend Color System Integration', () => {
  describe('No Backend Dependencies', () => {
    it('should work without any network requests', () => {
      // Mock fetch to ensure no network calls are made
      const fetchSpy = vi.spyOn(global, 'fetch');
      
      // Test various color operations
      const color1 = getDomainColor('physical');
      const color2 = getDomainColor('creative');
      const colors = getAllDomainColors();
      
      const wheelData = {
        items: [
          { domain: 'physical', name: 'Exercise', color: '' },
          { domain: 'creative', name: 'Art', color: '' }
        ]
      };
      applyColorsToWheel(wheelData);
      
      // Verify no network calls were made
      expect(fetchSpy).not.toHaveBeenCalled();
      
      // Verify colors are valid
      expect(color1).toMatch(/^#[0-9A-Fa-f]{6}$/);
      expect(color2).toMatch(/^#[0-9A-Fa-f]{6}$/);
      expect(typeof colors).toBe('object');
      expect(wheelData.items[0].color).toBeDefined();
      
      fetchSpy.mockRestore();
    });

    it('should be synchronous and fast', () => {
      const startTime = performance.now();
      
      // Perform multiple color operations
      for (let i = 0; i < 1000; i++) {
        getDomainColor('physical');
        getDomainColor('creative');
        getDomainColor('unknown_domain');
      }
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // Should complete very quickly (under 100ms for 1000 operations)
      expect(duration).toBeLessThan(100);
    });
  });

  describe('Comprehensive Domain Coverage', () => {
    it('should handle all documented domain families', () => {
      const domainFamilies = {
        physical: ['physical', 'phys_strength', 'phys_cardio', 'phys_flexibility', 'phys_dance'],
        creative: ['creative', 'creative_visual', 'creative_writing', 'creative_music'],
        social: ['social', 'soc_family', 'soc_friends', 'soc_leadership'],
        intellectual: ['intellectual', 'intel_learn', 'intel_strategic'],
        reflective: ['reflective', 'refl_meditate', 'refl_journal'],
        productive: ['productive_practical', 'prod_health', 'prod_time'],
        emotional: ['emotional', 'emot_aware', 'emot_regulation']
      };

      Object.entries(domainFamilies).forEach(([family, domains]) => {
        domains.forEach(domain => {
          const color = getDomainColor(domain);
          
          // Should return a valid color
          expect(color).toMatch(/^#[0-9A-Fa-f]{6}$/);
          
          // Should not be the general fallback for known domains
          if (domain !== 'general') {
            expect(color).not.toBe('#52C41A');
          }
        });
      });
    });

    it('should provide consistent colors within domain families', () => {
      // Physical domain family should all map to red-family colors
      const physicalDomains = ['physical', 'phys_strength', 'phys_cardio'];
      const physicalColors = physicalDomains.map(domain => getDomainColor(domain));
      
      // All should start with similar hex values (red family)
      physicalColors.forEach(color => {
        const redValue = parseInt(color.slice(1, 3), 16);
        expect(redValue).toBeGreaterThan(150); // Should be red-ish
      });
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle malformed domain codes gracefully', () => {
      const malformedDomains = [
        null,
        undefined,
        '',
        '   ',
        '\n\t',
        '___',
        'domain_with_many_underscores_that_is_very_long',
        'DOMAIN_WITH_CAPS',
        'domain-with-dashes',
        'domain.with.dots',
        'domain with spaces',
        '123numeric',
        'special!@#$%^&*()',
        'unicode_émojis_🎨',
        'very_long_domain_name_that_exceeds_normal_length_expectations_and_might_cause_issues'
      ];

      malformedDomains.forEach(domain => {
        expect(() => {
          const color = getDomainColor(domain as any);
          expect(color).toMatch(/^#[0-9A-Fa-f]{6}$/);
        }).not.toThrow();
      });
    });

    it('should handle large wheel datasets efficiently', () => {
      const largeWheelData = {
        items: Array.from({ length: 1000 }, (_, i) => ({
          domain: ['physical', 'creative', 'social', 'intellectual', 'reflective'][i % 5],
          name: `Activity ${i}`,
          id: `activity-${i}`,
          color: ''
        }))
      };

      const startTime = performance.now();
      applyColorsToWheel(largeWheelData);
      const endTime = performance.now();

      // Should handle large datasets quickly
      expect(endTime - startTime).toBeLessThan(50);

      // All items should have colors
      largeWheelData.items.forEach(item => {
        expect(item.color).toMatch(/^#[0-9A-Fa-f]{6}$/);
      });
    });
  });

  describe('Color Quality and Accessibility', () => {
    it('should provide colors with sufficient contrast ratios', () => {
      const testDomains = ['physical', 'creative', 'social', 'intellectual', 'reflective'];
      
      testDomains.forEach(domain => {
        const color = getDomainColor(domain);
        
        // Convert hex to RGB for luminance calculation
        const r = parseInt(color.slice(1, 3), 16) / 255;
        const g = parseInt(color.slice(3, 5), 16) / 255;
        const b = parseInt(color.slice(5, 7), 16) / 255;
        
        // Calculate relative luminance (simplified)
        const luminance = 0.299 * r + 0.587 * g + 0.114 * b;
        
        // Should not be too dark or too light for good contrast
        expect(luminance).toBeGreaterThan(0.1);
        expect(luminance).toBeLessThan(0.9);
      });
    });

    it('should provide visually distinct colors for common domain combinations', () => {
      const commonCombinations = [
        ['physical', 'creative', 'social'],
        ['intellectual', 'reflective', 'emotional'],
        ['phys_strength', 'phys_cardio', 'phys_flexibility'],
        ['creative_visual', 'creative_writing', 'creative_music']
      ];

      commonCombinations.forEach(domains => {
        const colors = domains.map(domain => getDomainColor(domain));
        
        // Calculate color distances (simplified)
        for (let i = 0; i < colors.length; i++) {
          for (let j = i + 1; j < colors.length; j++) {
            const color1 = colors[i];
            const color2 = colors[j];
            
            // Extract RGB values
            const r1 = parseInt(color1.slice(1, 3), 16);
            const g1 = parseInt(color1.slice(3, 5), 16);
            const b1 = parseInt(color1.slice(5, 7), 16);
            
            const r2 = parseInt(color2.slice(1, 3), 16);
            const g2 = parseInt(color2.slice(3, 5), 16);
            const b2 = parseInt(color2.slice(5, 7), 16);
            
            // Calculate Euclidean distance in RGB space
            const distance = Math.sqrt(
              Math.pow(r2 - r1, 2) + 
              Math.pow(g2 - g1, 2) + 
              Math.pow(b2 - b1, 2)
            );
            
            // Colors should be sufficiently different (at least 40 units apart)
            // Reduced from 50 to 40 to account for naturally similar colors in same families
            expect(distance).toBeGreaterThan(40);
          }
        }
      });
    });
  });

  describe('Wheel Validation', () => {
    it('should validate complex wheel configurations', () => {
      const complexWheel = {
        name: 'Complex Activity Wheel',
        items: [
          { domain: 'phys_strength', name: 'Weight Training', percentage: 20, color: '' },
          { domain: 'creative_visual', name: 'Digital Art', percentage: 15, color: '' },
          { domain: 'soc_family', name: 'Family Time', percentage: 25, color: '' },
          { domain: 'intel_learn', name: 'Online Course', percentage: 20, color: '' },
          { domain: 'refl_meditate', name: 'Meditation', percentage: 10, color: '' },
          { domain: 'prod_health', name: 'Meal Prep', percentage: 10, color: '' }
        ]
      };

      const validation = validateWheelDomains(complexWheel);
      expect(validation.isValid).toBe(true);
      expect(validation.issues).toHaveLength(0);

      // Apply colors and verify
      applyColorsToWheel(complexWheel);
      complexWheel.items.forEach(item => {
        expect(item.color).toMatch(/^#[0-9A-Fa-f]{6}$/);
        expect(item.color).not.toBe('#52C41A'); // Should not use fallback
      });
    });

    it('should handle mixed valid and invalid domains', () => {
      const mixedWheel = {
        items: [
          { domain: 'physical', name: 'Valid Domain', color: '' },
          { domain: 'invalid_domain', name: 'Invalid Domain', color: '' },
          { domain: '', name: 'Empty Domain', color: '' },
          { domain: 'creative', name: 'Another Valid Domain', color: '' }
        ]
      };

      const validation = validateWheelDomains(mixedWheel);
      expect(validation.isValid).toBe(false);
      expect(validation.issues.length).toBeGreaterThan(0);

      // Should still apply colors (with fallbacks for invalid domains)
      applyColorsToWheel(mixedWheel);
      mixedWheel.items.forEach(item => {
        expect(item.color).toMatch(/^#[0-9A-Fa-f]{6}$/);
      });
    });
  });

  describe('Memory and Performance', () => {
    it('should not leak memory with repeated operations', () => {
      const initialMemory = (performance as any).memory?.usedJSHeapSize || 0;
      
      // Perform many operations
      for (let i = 0; i < 10000; i++) {
        getDomainColor(`test_domain_${i % 100}`);
        getAllDomainColors();
        
        const wheelData = {
          items: [{ domain: 'physical', name: 'Test' }]
        };
        applyColorsToWheel(wheelData);
      }
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }
      
      const finalMemory = (performance as any).memory?.usedJSHeapSize || 0;
      
      // Memory usage should not increase significantly (allow for some variance)
      if (initialMemory > 0 && finalMemory > 0) {
        const memoryIncrease = finalMemory - initialMemory;
        expect(memoryIncrease).toBeLessThan(1024 * 1024); // Less than 1MB increase
      }
    });

    it('should maintain consistent performance under load', () => {
      const iterations = 1000;
      const times: number[] = [];
      
      for (let i = 0; i < iterations; i++) {
        const start = performance.now();
        getDomainColor('physical');
        getDomainColor('creative');
        getDomainColor('unknown_domain');
        const end = performance.now();
        
        times.push(end - start);
      }
      
      // Calculate average and max times
      const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
      const maxTime = Math.max(...times);
      
      // Performance should be consistent (adjusted for CI environments and logging overhead)
      expect(avgTime).toBeLessThan(1); // Average under 1ms
      expect(maxTime).toBeLessThan(10); // Max under 10ms
    });
  });
});
