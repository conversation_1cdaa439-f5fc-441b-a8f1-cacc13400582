/**
 * Tests that reproduce the current runtime errors
 * These tests should fail initially and pass after fixes are implemented
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { WheelPixiRenderer } from '../src/components/game-wheel/wheel-renderer.js';
import { GameWheel } from '../src/components/game-wheel/game-wheel.js';
import { WebSocketManager } from '../src/services/websocket-manager.js';
import { DEFAULT_WHEEL_CONFIG } from '../src/components/game-wheel/wheel-types.js';

// Mock PixiJS Application
const mockApp = {
  renderer: {
    resize: vi.fn()
  },
  init: vi.fn().mockResolvedValue(undefined),
  stage: {
    addChild: vi.fn()
  },
  ticker: {
    stop: vi.fn(),
    add: vi.fn()
  },
  destroy: vi.fn()
};

// <PERSON><PERSON>
const mockCanvas = document.createElement('canvas');

describe('Error Reproduction Tests', () => {
  describe('PixiJS Renderer Resize Error', () => {
    let renderer: WheelPixiRenderer;

    beforeEach(() => {
      renderer = new WheelPixiRenderer(DEFAULT_WHEEL_CONFIG);
      // Simulate uninitialized state
      (renderer as any).app = null;
      (renderer as any).isInitialized = false;
    });

    it('should handle resize when renderer is not initialized', () => {
      // This test reproduces: "Cannot read properties of undefined (reading 'resize')"
      expect(() => {
        renderer.resize(400, 400);
      }).not.toThrow();
    });

    it('should handle resize when app.renderer is undefined', () => {
      // Simulate partially initialized state
      (renderer as any).app = { renderer: null };
      (renderer as any).isInitialized = false;

      expect(() => {
        renderer.resize(400, 400);
      }).not.toThrow();
    });

    it('should handle resize with valid app but no renderer', () => {
      // Simulate app without renderer
      (renderer as any).app = mockApp;
      (renderer as any).isInitialized = true;
      mockApp.renderer = null as any;

      expect(() => {
        renderer.resize(400, 400);
      }).not.toThrow();
    });
  });

  describe('GameWheel Resize Error', () => {
    let gameWheel: GameWheel;

    beforeEach(() => {
      // Create game wheel element
      gameWheel = new GameWheel();
      document.body.appendChild(gameWheel);
    });

    afterEach(() => {
      if (gameWheel.parentNode) {
        gameWheel.parentNode.removeChild(gameWheel);
      }
    });

    it('should handle resize when renderer is null', () => {
      // Simulate uninitialized renderer
      (gameWheel as any).renderer = null;

      expect(() => {
        (gameWheel as any).handleResize();
      }).not.toThrow();
    });

    it('should handle resize with zero dimensions', () => {
      // Mock getBoundingClientRect to return zero dimensions
      const mockGetBoundingClientRect = vi.fn().mockReturnValue({
        width: 0,
        height: 0,
        top: 0,
        left: 0
      });
      
      gameWheel.getBoundingClientRect = mockGetBoundingClientRect;
      
      // Create mock renderer
      const mockRenderer = {
        resize: vi.fn()
      };
      (gameWheel as any).renderer = mockRenderer;

      expect(() => {
        (gameWheel as any).handleResize();
      }).not.toThrow();

      // Should not call resize with zero dimensions
      expect(mockRenderer.resize).not.toHaveBeenCalled();
    });

    it('should handle resize observer errors gracefully', () => {
      // Mock ResizeObserver to throw error
      const originalResizeObserver = window.ResizeObserver;
      window.ResizeObserver = class {
        constructor(callback: ResizeObserverCallback) {
          // Simulate immediate callback with error
          setTimeout(() => {
            try {
              callback([], this);
            } catch (error) {
              // This should be caught by the component
            }
          }, 0);
        }
        observe() {}
        disconnect() {}
        unobserve() {}
      };

      expect(() => {
        (gameWheel as any).setupResizeObserver();
      }).not.toThrow();

      // Restore original
      window.ResizeObserver = originalResizeObserver;
    });
  });

  describe('WebSocket Error Handling', () => {
    let wsManager: WebSocketManager;

    beforeEach(() => {
      wsManager = WebSocketManager.getInstance();
    });

    afterEach(() => {
      // Clean up WebSocket connections
      try {
        wsManager.disconnect();
      } catch (error) {
        // Ignore cleanup errors
      }
    });

    it('should handle WebSocket connection failure gracefully', async () => {
      // This test reproduces: "Failed to establish WebSocket connection"
      // Mock WebSocket constructor to simulate connection failure
      const originalWebSocket = global.WebSocket;

      global.WebSocket = class MockWebSocket {
        constructor() {
          // Simulate immediate connection failure
          setTimeout(() => {
            if (this.onerror) {
              this.onerror(new Event('error'));
            }
            if (this.onclose) {
              this.onclose(new CloseEvent('close', { code: 1006, reason: 'Connection failed' }));
            }
          }, 0);
        }

        addEventListener = vi.fn();
        removeEventListener = vi.fn();
        close = vi.fn();
        send = vi.fn();
        readyState = WebSocket.CONNECTING;
        onerror: ((event: Event) => void) | null = null;
        onopen: ((event: Event) => void) | null = null;
        onclose: ((event: CloseEvent) => void) | null = null;
        onmessage: ((event: MessageEvent) => void) | null = null;
      } as any;

      const config = {
        url: 'ws://nonexistent:9999/ws',
        reconnectAttempts: 1,
        reconnectDelay: 100,
        heartbeatInterval: 1000,
        messageTimeout: 1000,
        maxQueueSize: 10
      };

      // Should handle error gracefully without throwing
      try {
        await wsManager.initialize(config);
      } catch (error) {
        // Expected to fail, but should be handled gracefully
      }

      // The error should be contained and not crash the app
      expect(wsManager.getState().isConnected).toBe(false);

      // Restore original WebSocket
      global.WebSocket = originalWebSocket;
    });

    it('should handle WebSocket error events without throwing', () => {
      // Mock WebSocket that immediately errors
      const mockWebSocket = {
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        close: vi.fn(),
        send: vi.fn(),
        readyState: WebSocket.CONNECTING,
        onerror: null,
        onopen: null,
        onclose: null,
        onmessage: null
      };

      // Simulate WebSocket error
      expect(() => {
        if (mockWebSocket.onerror) {
          (mockWebSocket.onerror as any)(new Event('error'));
        }
      }).not.toThrow();
    });

    it('should handle multiple connection attempts without memory leaks', async () => {
      // Mock WebSocket constructor to simulate connection failure
      const originalWebSocket = global.WebSocket;
      let connectionAttempts = 0;

      global.WebSocket = class MockWebSocket {
        constructor() {
          connectionAttempts++;
          // Simulate immediate connection failure
          setTimeout(() => {
            if (this.onerror) {
              this.onerror(new Event('error'));
            }
            if (this.onclose) {
              this.onclose(new CloseEvent('close', { code: 1006, reason: 'Connection failed' }));
            }
          }, 0);
        }

        addEventListener = vi.fn();
        removeEventListener = vi.fn();
        close = vi.fn();
        send = vi.fn();
        readyState = WebSocket.CONNECTING;
        onerror: ((event: Event) => void) | null = null;
        onopen: ((event: Event) => void) | null = null;
        onclose: ((event: CloseEvent) => void) | null = null;
        onmessage: ((event: MessageEvent) => void) | null = null;
      } as any;

      const config = {
        url: 'ws://localhost:9999/ws',
        reconnectAttempts: 2,
        reconnectDelay: 10,
        heartbeatInterval: 1000,
        messageTimeout: 1000,
        maxQueueSize: 10
      };

      // Multiple failed connection attempts should not accumulate errors
      const promises = Array.from({ length: 3 }, () =>
        wsManager.initialize(config).catch(() => {})
      );

      await Promise.allSettled(promises);

      // Should not have memory leaks or accumulated errors
      expect(wsManager.getState().isConnected).toBe(false);
      expect(connectionAttempts).toBeGreaterThan(0);

      // Restore original WebSocket
      global.WebSocket = originalWebSocket;
    });
  });

  describe('Service Worker and Manifest Errors', () => {
    it('should handle missing manifest.json gracefully', () => {
      // This test reproduces: "Manifest: Line: 1, column: 1, Syntax error"
      // The error should not crash the app
      
      // Mock fetch to return 404 for manifest
      const originalFetch = (globalThis as any).fetch;
      (globalThis as any).fetch = vi.fn().mockRejectedValue(new Error('Not found'));

      // App should continue to work even if manifest fails to load
      expect(() => {
        // Simulate manifest request
        fetch('/manifest.json').catch(() => {
          // Error should be handled gracefully
        });
      }).not.toThrow();

      (globalThis as any).fetch = originalFetch;
    });

    it('should handle service worker registration failure', () => {
      // This test reproduces: "SW registration failed: SecurityError"
      
      // Mock navigator.serviceWorker
      const mockServiceWorker = {
        register: vi.fn().mockRejectedValue(new Error('SecurityError'))
      };

      Object.defineProperty(navigator, 'serviceWorker', {
        value: mockServiceWorker,
        writable: true
      });

      expect(() => {
        // Simulate service worker registration
        navigator.serviceWorker.register('/sw.js').catch(() => {
          // Error should be handled gracefully
        });
      }).not.toThrow();
    });
  });

  describe('Global Error Handling', () => {
    it('should catch and handle unhandled promise rejections', () => {
      const originalHandler = window.onunhandledrejection;
      let caughtError: any = null;

      window.onunhandledrejection = (event) => {
        caughtError = event.reason;
        event.preventDefault(); // Prevent console error
      };

      // Create unhandled promise rejection and trigger the handler manually
      // In test environment, we need to simulate the event
      const rejectionError = new Error('Test unhandled rejection');

      // Create a mock PromiseRejectionEvent since it's not available in JSDOM
      const rejectionEvent = {
        type: 'unhandledrejection',
        promise: Promise.reject(rejectionError),
        reason: rejectionError,
        preventDefault: vi.fn()
      } as any;

      // Trigger the handler directly since JSDOM doesn't always trigger it automatically
      if (window.onunhandledrejection) {
        window.onunhandledrejection(rejectionEvent);
      }

      // Verify the error was caught
      expect(caughtError).toBeInstanceOf(Error);
      expect(caughtError.message).toBe('Test unhandled rejection');

      window.onunhandledrejection = originalHandler;
    });

    it('should catch and handle global errors', () => {
      const originalHandler = window.onerror;
      let caughtError: any = null;

      window.onerror = (message, source, lineno, colno, error) => {
        caughtError = error || new Error(message as string);
        return true; // Prevent console error
      };

      // Simulate global error by calling the handler directly
      // In test environment, we need to trigger the handler manually
      const testError = new Error('Test global error');
      if (window.onerror) {
        window.onerror('Test global error', 'test.js', 1, 1, testError);
      }

      // Verify the error was caught
      expect(caughtError).toBeInstanceOf(Error);
      expect(caughtError.message).toBe('Test global error');

      window.onerror = originalHandler;
    });
  });
});
