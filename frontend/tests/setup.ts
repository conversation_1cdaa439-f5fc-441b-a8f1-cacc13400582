/**
 * Test setup file for Vitest
 * Configures global mocks and test environment
 */

import { vi } from 'vitest';

// Mock PixiJS
vi.mock('pixi.js', () => ({
  Application: vi.fn().mockImplementation(() => ({
    init: vi.fn().mockResolvedValue(undefined),
    renderer: {
      resize: vi.fn(),
    },
    stage: {
      addChild: vi.fn(),
      removeChild: vi.fn(),
    },
    ticker: {
      add: vi.fn(),
      remove: vi.fn(),
      stop: vi.fn(),
    },
    destroy: vi.fn(),
  })),
  Container: vi.fn().mockImplementation(() => ({
    addChild: vi.fn(),
    removeChild: vi.fn(),
    removeChildren: vi.fn(),
  })),
  Graphics: vi.fn().mockImplementation(() => ({
    circle: vi.fn().mockReturnThis(),
    fill: vi.fn().mockReturnThis(),
    stroke: vi.fn().mockReturnThis(),
    moveTo: vi.fn().mockReturnThis(),
    arc: vi.fn().mockReturnThis(),
    closePath: vi.fn().mockReturnThis(),
    clear: vi.fn().mockReturnThis(),
  })),
  Text: vi.fn().mockImplementation(() => ({
    anchor: { set: vi.fn() },
    x: 0,
    y: 0,
    rotation: 0,
  })),
  TextStyle: vi.fn(),
  Assets: {
    load: vi.fn().mockResolvedValue({}),
  },
}));

// Mock Matter.js
vi.mock('matter-js', () => ({
  Engine: {
    create: vi.fn(() => ({
      world: {
        gravity: { x: 0, y: 0 },
      },
    })),
    clear: vi.fn(),
  },
  World: {
    add: vi.fn(),
    clear: vi.fn(),
  },
  Bodies: {
    circle: vi.fn(() => ({ id: Math.random() })),
    rectangle: vi.fn(() => ({ id: Math.random() })),
  },
  Body: {
    applyForce: vi.fn(),
    setPosition: vi.fn(),
    setVelocity: vi.fn(),
    setAngularVelocity: vi.fn(),
  },
  Events: {
    on: vi.fn(),
  },
  Vector: {
    magnitude: vi.fn(() => 1),
    mult: vi.fn(() => ({ x: 0, y: 0 })),
    normalise: vi.fn(() => ({ x: 0, y: 0 })),
  },
  Runner: {
    create: vi.fn(() => ({})),
    run: vi.fn(),
    stop: vi.fn(),
  },
}));

// Mock WebSocket
(globalThis as any).WebSocket = vi.fn().mockImplementation(() => ({
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  close: vi.fn(),
  send: vi.fn(),
  readyState: 0, // WebSocket.CONNECTING
  CONNECTING: 0,
  OPEN: 1,
  CLOSING: 2,
  CLOSED: 3,
})) as any;

// Mock ResizeObserver
(globalThis as any).ResizeObserver = vi.fn().mockImplementation((callback) => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock requestAnimationFrame
(globalThis as any).requestAnimationFrame = vi.fn((cb) => setTimeout(cb, 16));
(globalThis as any).cancelAnimationFrame = vi.fn();

// Mock performance
(globalThis as any).performance = {
  ...(globalThis as any).performance,
  now: vi.fn(() => Date.now()),
};

// Mock canvas context
HTMLCanvasElement.prototype.getContext = vi.fn(() => ({
  fillRect: vi.fn(),
  clearRect: vi.fn(),
  getImageData: vi.fn(() => ({ data: new Array(4) })),
  putImageData: vi.fn(),
  createImageData: vi.fn(() => ({ data: new Array(4) })),
  setTransform: vi.fn(),
  drawImage: vi.fn(),
  save: vi.fn(),
  fillText: vi.fn(),
  restore: vi.fn(),
  beginPath: vi.fn(),
  moveTo: vi.fn(),
  lineTo: vi.fn(),
  closePath: vi.fn(),
  stroke: vi.fn(),
  translate: vi.fn(),
  scale: vi.fn(),
  rotate: vi.fn(),
  arc: vi.fn(),
  fill: vi.fn(),
  measureText: vi.fn(() => ({ width: 0 })),
  transform: vi.fn(),
  rect: vi.fn(),
  clip: vi.fn(),
})) as any;

// Mock getBoundingClientRect
Element.prototype.getBoundingClientRect = vi.fn(() => ({
  width: 400,
  height: 400,
  top: 0,
  left: 0,
  bottom: 400,
  right: 400,
  x: 0,
  y: 0,
  toJSON: vi.fn(),
}));

// Mock setPointerCapture and releasePointerCapture
Element.prototype.setPointerCapture = vi.fn();
Element.prototype.releasePointerCapture = vi.fn();

// Suppress console warnings in tests
const originalWarn = console.warn;
console.warn = vi.fn();

// Restore console.warn after tests if needed
(globalThis as any).afterAll(() => {
  console.warn = originalWarn;
});
