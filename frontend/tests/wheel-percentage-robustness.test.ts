/**
 * Wheel Percentage Robustness Tests
 * Tests that the wheel system can handle imperfect percentages and normalize them properly
 */

import { describe, it, expect } from 'vitest';
import { calculateSegmentAngles } from '../src/utils/physics-utils.js';
import { getWinningSegmentUltraReliable } from '../src/utils/physics-utils.js';

describe('Wheel Percentage Robustness', () => {
  
  it('should handle imperfect percentages that do not sum to exactly 100', () => {
    // Test with percentages that sum to 96 (4% short)
    const segments = [
      { id: '1', text: 'Activity 1', percentage: 16, color: '#ff0000' },
      { id: '2', text: 'Activity 2', percentage: 16, color: '#00ff00' },
      { id: '3', text: 'Activity 3', percentage: 16, color: '#0000ff' },
      { id: '4', text: 'Activity 4', percentage: 16, color: '#ffff00' },
      { id: '5', text: 'Activity 5', percentage: 16, color: '#ff00ff' },
      { id: '6', text: 'Activity 6', percentage: 16, color: '#00ffff' }
    ];

    const totalInput = segments.reduce((sum, s) => sum + s.percentage, 0);
    expect(totalInput).toBe(96); // Verify our test data is imperfect

    const result = calculateSegmentAngles(segments);
    
    // After normalization, percentages should sum to 100
    const totalOutput = result.reduce((sum, s) => sum + s.percentage, 0);
    expect(totalOutput).toBeCloseTo(100, 10); // Should be exactly 100

    // Each segment should be approximately 16.67% (100/6)
    result.forEach(segment => {
      expect(segment.percentage).toBeCloseTo(16.666666666666668, 10);
    });
  });

  it('should handle percentages that sum to more than 100', () => {
    // Test with percentages that sum to 120 (20% over)
    const segments = [
      { id: '1', text: 'Activity 1', percentage: 20, color: '#ff0000' },
      { id: '2', text: 'Activity 2', percentage: 20, color: '#00ff00' },
      { id: '3', text: 'Activity 3', percentage: 20, color: '#0000ff' },
      { id: '4', text: 'Activity 4', percentage: 20, color: '#ffff00' },
      { id: '5', text: 'Activity 5', percentage: 20, color: '#ff00ff' },
      { id: '6', text: 'Activity 6', percentage: 20, color: '#00ffff' }
    ];

    const totalInput = segments.reduce((sum, s) => sum + s.percentage, 0);
    expect(totalInput).toBe(120); // Verify our test data is over 100

    const result = calculateSegmentAngles(segments);
    
    // After normalization, percentages should sum to 100
    const totalOutput = result.reduce((sum, s) => sum + s.percentage, 0);
    expect(totalOutput).toBeCloseTo(100, 10);

    // Each segment should be approximately 16.67% (100/6)
    result.forEach(segment => {
      expect(segment.percentage).toBeCloseTo(16.666666666666668, 10);
    });
  });

  it('should handle mixed imperfect percentages', () => {
    // Test with mixed percentages that sum to 97.5
    const segments = [
      { id: '1', text: 'Activity 1', percentage: 15.5, color: '#ff0000' },
      { id: '2', text: 'Activity 2', percentage: 17.2, color: '#00ff00' },
      { id: '3', text: 'Activity 3', percentage: 14.8, color: '#0000ff' },
      { id: '4', text: 'Activity 4', percentage: 18.1, color: '#ffff00' },
      { id: '5', text: 'Activity 5', percentage: 15.9, color: '#ff00ff' },
      { id: '6', text: 'Activity 6', percentage: 16.0, color: '#00ffff' }
    ];

    const totalInput = segments.reduce((sum, s) => sum + s.percentage, 0);
    expect(totalInput).toBe(97.5); // Verify our test data is imperfect

    const result = calculateSegmentAngles(segments);
    
    // After normalization, percentages should sum to 100
    const totalOutput = result.reduce((sum, s) => sum + s.percentage, 0);
    expect(totalOutput).toBeCloseTo(100, 10);

    // Verify proportions are maintained
    const inputRatio = segments[0].percentage / segments[1].percentage; // 15.5 / 17.2
    const outputRatio = result[0].percentage / result[1].percentage;
    expect(outputRatio).toBeCloseTo(inputRatio, 10);
  });

  it('should work with mock wheel data using simple 16% percentages', () => {
    // This test should FAIL initially because mock data uses exact 16.666666666666668
    // After fix, it should PASS with simple 16% values
    const mockSegments = [
      { id: 'item_mock_1_86', text: 'Pomodoro Focus Session', percentage: 16, color: '#ff0000' },
      { id: 'item_mock_2_155', text: 'Advanced Urban Parkour', percentage: 16, color: '#00ff00' },
      { id: 'item_mock_3_124', text: 'App-Guided Movement Session', percentage: 16, color: '#0000ff' },
      { id: 'item_mock_4_84', text: 'Deliberate Practice Session', percentage: 16, color: '#ffff00' },
      { id: 'item_mock_5_64', text: 'Emotional Anchoring Technique', percentage: 16, color: '#ff00ff' },
      { id: 'item_mock_6_121', text: 'Intentional Break Transition', percentage: 16, color: '#00ffff' }
    ];

    const totalInput = mockSegments.reduce((sum, s) => sum + s.percentage, 0);
    expect(totalInput).toBe(96); // 6 * 16 = 96, not 100

    const result = calculateSegmentAngles(mockSegments);
    
    // After normalization, should work perfectly
    const totalOutput = result.reduce((sum, s) => sum + s.percentage, 0);
    expect(totalOutput).toBeCloseTo(100, 10);

    // Each segment should be evenly distributed
    result.forEach(segment => {
      expect(segment.percentage).toBeCloseTo(16.666666666666668, 10);
    });

    // Verify that all segments have valid angles
    result.forEach(segment => {
      expect(segment.startAngle).toBeGreaterThanOrEqual(0);
      expect(segment.startAngle).toBeLessThan(2 * Math.PI);
      expect(segment.endAngle).toBeGreaterThanOrEqual(0);
      expect(segment.endAngle).toBeLessThan(2 * Math.PI);
      expect(segment.centerAngle).toBeGreaterThanOrEqual(0);
      expect(segment.centerAngle).toBeLessThan(2 * Math.PI);
    });
  });

  it('should maintain winning segment detection reliability with imperfect percentages', () => {
    // Test that winner detection still works with normalized percentages
    const segments = [
      { id: '1', text: 'Activity 1', percentage: 15, color: '#ff0000' },
      { id: '2', text: 'Activity 2', percentage: 17, color: '#00ff00' },
      { id: '3', text: 'Activity 3', percentage: 16, color: '#0000ff' },
      { id: '4', text: 'Activity 4', percentage: 18, color: '#ffff00' },
      { id: '5', text: 'Activity 5', percentage: 14, color: '#ff00ff' },
      { id: '6', text: 'Activity 6', percentage: 16, color: '#00ffff' }
    ];

    const normalizedSegments = calculateSegmentAngles(segments);
    
    // Test winner detection at the center of first segment
    const firstSegmentCenter = normalizedSegments[0].centerAngle;
    const ballX = 200 + 100 * Math.cos(firstSegmentCenter); // 100px from center
    const ballY = 200 + 100 * Math.sin(firstSegmentCenter);

    const result = getWinningSegmentUltraReliable(
      ballX, ballY, 200, 200, normalizedSegments, [], 8
    );

    expect(result.segment).toBeTruthy();
    expect(result.segment?.id).toBe('1');
    expect(result.confidence).toBeGreaterThan(0.8);
    expect(result.agreementCount).toBeGreaterThanOrEqual(3); // At least 3/4 consensus
  });

  it('should handle edge case with very small percentages', () => {
    const segments = [
      { id: '1', text: 'Activity 1', percentage: 0.1, color: '#ff0000' },
      { id: '2', text: 'Activity 2', percentage: 0.2, color: '#00ff00' },
      { id: '3', text: 'Activity 3', percentage: 0.3, color: '#0000ff' },
      { id: '4', text: 'Activity 4', percentage: 0.4, color: '#ffff00' }
    ];

    const result = calculateSegmentAngles(segments);
    
    const totalOutput = result.reduce((sum, s) => sum + s.percentage, 0);
    expect(totalOutput).toBeCloseTo(100, 10);

    // Verify proportions are maintained
    expect(result[0].percentage).toBeCloseTo(10, 10); // 0.1/1.0 * 100
    expect(result[1].percentage).toBeCloseTo(20, 10); // 0.2/1.0 * 100
    expect(result[2].percentage).toBeCloseTo(30, 10); // 0.3/1.0 * 100
    expect(result[3].percentage).toBeCloseTo(40, 10); // 0.4/1.0 * 100
  });

  it('should handle zero percentages gracefully', () => {
    const segments = [
      { id: '1', text: 'Activity 1', percentage: 0, color: '#ff0000' },
      { id: '2', text: 'Activity 2', percentage: 50, color: '#00ff00' },
      { id: '3', text: 'Activity 3', percentage: 0, color: '#0000ff' },
      { id: '4', text: 'Activity 4', percentage: 50, color: '#ffff00' }
    ];

    const result = calculateSegmentAngles(segments);
    
    const totalOutput = result.reduce((sum, s) => sum + s.percentage, 0);
    expect(totalOutput).toBeCloseTo(100, 10);

    // Zero percentage items should remain zero
    expect(result[0].percentage).toBe(0);
    expect(result[2].percentage).toBe(0);
    
    // Non-zero items should be normalized
    expect(result[1].percentage).toBeCloseTo(50, 10);
    expect(result[3].percentage).toBeCloseTo(50, 10);
  });

  it('should work with the updated mock wheel data from app-shell.ts', () => {
    // Test with the exact mock data structure from app-shell.ts (now using simple 16% values)
    const mockWheelSegments = [
      { id: 'item_mock_1_86', text: 'Pomodoro Focus Session', percentage: 16, color: '#ff0000', domain: 'productive_practical' },
      { id: 'item_mock_2_155', text: 'Advanced Urban Parkour', percentage: 16, color: '#00ff00', domain: 'general' },
      { id: 'item_mock_3_124', text: 'App-Guided Movement Session', percentage: 16, color: '#0000ff', domain: 'productive_practical' },
      { id: 'item_mock_4_84', text: 'Deliberate Practice Session', percentage: 16, color: '#ffff00', domain: 'productive_practical' },
      { id: 'item_mock_5_64', text: 'Emotional Anchoring Technique', percentage: 16, color: '#ff00ff', domain: 'emotional' },
      { id: 'item_mock_6_121', text: 'Intentional Break Transition', percentage: 16, color: '#00ffff', domain: 'productive_practical' }
    ];

    const totalInput = mockWheelSegments.reduce((sum, s) => sum + s.percentage, 0);
    expect(totalInput).toBe(96); // 6 * 16 = 96, not 100 - this is the robustness we're testing

    const result = calculateSegmentAngles(mockWheelSegments);

    // After normalization, should work perfectly
    const totalOutput = result.reduce((sum, s) => sum + s.percentage, 0);
    expect(totalOutput).toBeCloseTo(100, 10);

    // Each segment should be evenly distributed (16.67% each)
    result.forEach(segment => {
      expect(segment.percentage).toBeCloseTo(16.666666666666668, 10);
    });

    // Verify that domain information is preserved
    expect(result[0].domain).toBe('productive_practical');
    expect(result[1].domain).toBe('general');
    expect(result[4].domain).toBe('emotional');

    console.log('✅ Mock wheel data with simple 16% percentages works perfectly!');
    console.log(`Input total: ${totalInput}%, Output total: ${totalOutput.toFixed(2)}%`);
    console.log('Each segment normalized to:', result[0].percentage.toFixed(10) + '%');
  });

  it('should have enhanced winning segment detection with 6 mechanisms', () => {
    // Test the enhanced 6-mechanism detection system
    const segments = [
      { id: '1', text: 'Activity 1', percentage: 20, color: '#ff0000' },
      { id: '2', text: 'Activity 2', percentage: 20, color: '#00ff00' },
      { id: '3', text: 'Activity 3', percentage: 20, color: '#0000ff' },
      { id: '4', text: 'Activity 4', percentage: 20, color: '#ffff00' },
      { id: '5', text: 'Activity 5', percentage: 20, color: '#ff00ff' }
    ];

    const normalizedSegments = calculateSegmentAngles(segments);

    // Test winner detection at the center of first segment
    const firstSegmentCenter = normalizedSegments[0].centerAngle;
    const ballX = 200 + 100 * Math.cos(firstSegmentCenter);
    const ballY = 200 + 100 * Math.sin(firstSegmentCenter);

    const result = getWinningSegmentUltraReliable(
      ballX, ballY, 200, 200, normalizedSegments, [], 8
    );

    expect(result.segment).toBeTruthy();
    expect(result.segment?.id).toBe('1');
    expect(result.confidence).toBeGreaterThan(0.8);
    expect(result.agreementCount).toBeGreaterThanOrEqual(3); // At least 3/6 consensus
    expect(result.methods).toHaveLength(6); // Should have 6 methods now

    // Test that we get better consensus with 6 mechanisms
    expect(result.consensusLevel).toBeGreaterThanOrEqual(0.5); // At least 50% consensus

    console.log('✅ Enhanced 6-mechanism detection system working!');
    console.log(`Consensus: ${result.agreementCount}/6 mechanisms (${(result.consensusLevel * 100).toFixed(1)}%)`);
    console.log(`Confidence: ${(result.confidence * 100).toFixed(1)}%`);
    console.log('Methods:', result.methods.map(m => `${m.name}: ${m.segment || 'null'} (${(m.confidence * 100).toFixed(1)}%)`));
  });
});
