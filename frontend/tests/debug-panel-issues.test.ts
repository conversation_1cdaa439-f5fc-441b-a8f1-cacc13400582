/**
 * Debug Panel Issues Test Suite
 * Tests for the specific issues identified in console and Celery logs
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';

// Mock localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {};
  
  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value.toString();
    },
    removeItem: (key: string) => {
      delete store[key];
    },
    clear: () => {
      store = {};
    },
    get length() {
      return Object.keys(store).length;
    },
    key: (index: number) => Object.keys(store)[index] || null
  };
})();

// Mock WebSocket
class MockWebSocket {
  public readyState: 0 | 1 | 2 | 3 = 0;
  public onopen: ((event: Event) => void) | null = null;
  public onmessage: ((event: MessageEvent) => void) | null = null;
  public onerror: ((event: Event) => void) | null = null;
  public onclose: ((event: CloseEvent) => void) | null = null;

  constructor(public url: string) {
    // Simulate connection after a short delay
    setTimeout(() => {
      this.readyState = 1; // WebSocket.OPEN
      if (this.onopen) {
        this.onopen(new Event('open'));
      }
    }, 10);
  }

  send(data: string) {
    // Mock send - could be extended to simulate responses
    console.log('Mock WebSocket send:', data);
  }

  close() {
    this.readyState = 3; // WebSocket.CLOSED
    if (this.onclose) {
      this.onclose(new CloseEvent('close'));
    }
  }
}

// Debug panel storage keys
const DEBUG_STORAGE_KEYS = {
  USER_ID: 'debug_selected_user_id',
  LLM_CONFIG_ID: 'debug_selected_llm_config_id',
  BACKEND_URL: 'debug_backend_url',
  GOALI_STATE: 'goali-state'
};

describe('Debug Panel Storage Issues', () => {
  beforeEach(() => {
    // Setup mocks
    Object.defineProperty(window, 'localStorage', {
      value: localStorageMock,
      writable: true
    });
    
    Object.defineProperty(window, 'WebSocket', {
      value: MockWebSocket,
      writable: true
    });
    
    // Clear storage before each test
    localStorageMock.clear();
  });

  afterEach(() => {
    localStorageMock.clear();
  });

  describe('Storage Persistence', () => {
    it('should save and retrieve user ID correctly', () => {
      const testUserId = '2';
      
      localStorageMock.setItem(DEBUG_STORAGE_KEYS.USER_ID, testUserId);
      const retrieved = localStorageMock.getItem(DEBUG_STORAGE_KEYS.USER_ID);
      
      expect(retrieved).toBe(testUserId);
    });

    it('should save and retrieve LLM config ID correctly', () => {
      const testLLMConfigId = '5';
      
      localStorageMock.setItem(DEBUG_STORAGE_KEYS.LLM_CONFIG_ID, testLLMConfigId);
      const retrieved = localStorageMock.getItem(DEBUG_STORAGE_KEYS.LLM_CONFIG_ID);
      
      expect(retrieved).toBe(testLLMConfigId);
    });

    it('should handle missing values with defaults', () => {
      // Simulate debug panel initialization logic
      const userId = localStorageMock.getItem(DEBUG_STORAGE_KEYS.USER_ID) || '2';
      const llmConfigId = localStorageMock.getItem(DEBUG_STORAGE_KEYS.LLM_CONFIG_ID) || '';
      
      expect(userId).toBe('2');
      expect(llmConfigId).toBe('');
    });

    it('should persist values across simulated page reload', () => {
      // Set values
      localStorageMock.setItem(DEBUG_STORAGE_KEYS.USER_ID, '2');
      localStorageMock.setItem(DEBUG_STORAGE_KEYS.LLM_CONFIG_ID, '5');
      
      // Simulate page reload by creating new instance (storage persists)
      const userIdAfterReload = localStorageMock.getItem(DEBUG_STORAGE_KEYS.USER_ID);
      const llmConfigIdAfterReload = localStorageMock.getItem(DEBUG_STORAGE_KEYS.LLM_CONFIG_ID);
      
      expect(userIdAfterReload).toBe('2');
      expect(llmConfigIdAfterReload).toBe('5');
    });
  });

  describe('Message Structure Issues', () => {
    it('should create correct message structure with LLM config', () => {
      // Set debug panel values
      localStorageMock.setItem(DEBUG_STORAGE_KEYS.USER_ID, '2');
      localStorageMock.setItem(DEBUG_STORAGE_KEYS.LLM_CONFIG_ID, '5');
      
      // Simulate message creation logic from app-shell.ts
      const userId = localStorageMock.getItem(DEBUG_STORAGE_KEYS.USER_ID) || '2';
      const llmConfigId = localStorageMock.getItem(DEBUG_STORAGE_KEYS.LLM_CONFIG_ID);
      
      const messageContent: any = {
        message: 'Test message',
        user_profile_id: userId,
        timestamp: new Date().toISOString()
      };

      if (llmConfigId) {
        messageContent.metadata = {
          llm_config_id: llmConfigId
        };
      }
      
      const fullMessage = {
        type: 'chat_message',
        content: messageContent
      };
      
      expect(fullMessage.content.user_profile_id).toBe('2');
      expect(fullMessage.content.metadata?.llm_config_id).toBe('5');
      expect(fullMessage.type).toBe('chat_message');
    });

    it('should identify missing LLM config issue', () => {
      // Set only user ID, not LLM config (simulating the error condition)
      localStorageMock.setItem(DEBUG_STORAGE_KEYS.USER_ID, '2');
      // LLM config is missing
      
      const userId = localStorageMock.getItem(DEBUG_STORAGE_KEYS.USER_ID) || '2';
      const llmConfigId = localStorageMock.getItem(DEBUG_STORAGE_KEYS.LLM_CONFIG_ID);
      
      const messageContent: any = {
        message: 'Test message',
        user_profile_id: userId,
        timestamp: new Date().toISOString()
      };

      // This is the problematic condition - no LLM config
      if (llmConfigId) {
        messageContent.metadata = {
          llm_config_id: llmConfigId
        };
      }

      expect(llmConfigId).toBeNull();
      expect(messageContent.metadata).toBeUndefined();
      // This would cause "No LLMConfig provided" error in backend
    });
  });

  describe('Error Handling', () => {
    it('should handle localStorage errors gracefully', () => {
      // Mock localStorage to throw error
      const originalSetItem = localStorageMock.setItem;
      localStorageMock.setItem = vi.fn(() => {
        throw new Error('Storage quota exceeded');
      });
      
      // Simulate debug panel save logic with error handling
      let errorOccurred = false;
      try {
        localStorageMock.setItem(DEBUG_STORAGE_KEYS.USER_ID, '2');
      } catch (error) {
        errorOccurred = true;
        console.warn('Failed to save to localStorage:', error);
      }
      
      expect(errorOccurred).toBe(true);
      
      // Restore original function
      localStorageMock.setItem = originalSetItem;
    });

    it('should validate user ID format', () => {
      const validUserIds = ['1', '2', '3'];
      const invalidUserIds = ['', 'invalid', 'test-user-123', null, undefined];
      
      validUserIds.forEach(userId => {
        expect(userId).toMatch(/^\d+$/);
      });
      
      invalidUserIds.forEach(userId => {
        if (userId) {
          expect(userId).not.toMatch(/^\d+$/);
        }
      });
    });
  });

  describe('WebSocket Message Handling', () => {
    it('should handle debug_info messages', () => {
      const mockMessage = {
        type: 'debug_info',
        content: {
          message: 'Debug information',
          timestamp: new Date().toISOString()
        }
      };
      
      // Simulate message handler logic
      let debugInfoReceived = false;
      if (mockMessage.type === 'debug_info') {
        debugInfoReceived = true;
        console.log('Debug info:', mockMessage.content);
      }
      
      expect(debugInfoReceived).toBe(true);
    });

    it('should handle workflow_status messages', () => {
      const mockMessage = {
        type: 'workflow_status',
        workflow_id: '516d63d6-6aee-4d16-ae8e-f7eb845fd798',
        status: 'initiated'
      };
      
      // Simulate message handler logic
      let workflowStatusReceived = false;
      if (mockMessage.type === 'workflow_status') {
        workflowStatusReceived = true;
        console.log('Workflow status:', mockMessage);
      }
      
      expect(workflowStatusReceived).toBe(true);
      expect(mockMessage.status).toBe('initiated');
    });

    it('should handle error messages with proper structure', () => {
      const mockErrorMessage = {
        type: 'error',
        content: 'An unexpected error occurred while retrieving your profile.'
      };
      
      // Simulate error handler logic
      let errorHandled = false;
      let errorContent = '';
      
      if (mockErrorMessage.type === 'error') {
        errorHandled = true;
        errorContent = typeof mockErrorMessage.content === 'string'
          ? mockErrorMessage.content
          : (mockErrorMessage.content as any).content;
      }
      
      expect(errorHandled).toBe(true);
      expect(errorContent).toBe('An unexpected error occurred while retrieving your profile.');
    });
  });

  describe('Integration Issues', () => {
    it('should reproduce the exact error scenario from logs', () => {
      // Reproduce the exact scenario from the logs:
      // 1. User selects User ID: 2, LLM Config: 5
      // 2. Message is sent but LLM config is not included properly
      // 3. Backend fails with "No LLMConfig provided"
      
      // Step 1: Set debug panel selections
      localStorageMock.setItem(DEBUG_STORAGE_KEYS.USER_ID, '2');
      localStorageMock.setItem(DEBUG_STORAGE_KEYS.LLM_CONFIG_ID, '5');
      
      // Step 2: Simulate message sending (from app-shell.ts logic)
      const userId = localStorageMock.getItem(DEBUG_STORAGE_KEYS.USER_ID) || '2';
      const llmConfigId = localStorageMock.getItem(DEBUG_STORAGE_KEYS.LLM_CONFIG_ID);
      
      const messageContent: any = {
        message: 'Test message',
        user_profile_id: userId,
        timestamp: new Date().toISOString()
      };

      // This is where the bug might be - LLM config not being added properly
      if (llmConfigId && llmConfigId !== '') {
        messageContent.metadata = {
          llm_config_id: llmConfigId
        };
      }
      
      // Step 3: Verify the message structure
      expect(messageContent.user_profile_id).toBe('2');
      expect(messageContent.metadata?.llm_config_id).toBe('5');
      
      // If this test passes, the issue is elsewhere (possibly in the WebSocket sending logic)
      // If this test fails, the issue is in the message construction logic
    });
  });
});
