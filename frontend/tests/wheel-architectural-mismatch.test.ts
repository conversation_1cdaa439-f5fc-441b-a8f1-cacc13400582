/**
 * Wheel Architectural Mismatch Test
 * 
 * This test reproduces the EXACT architectural issue found in the console logs:
 * - Segments have IDs like "item_249_1_86"
 * - generateWheelColors() receives activity_tailored_id like "86"
 * - Mapping fails because "item_249_1_86" cannot find color for activity ID "86"
 */

import { describe, it, expect } from 'vitest';
import { generateWheelColors } from '../src/services/domainColorService.js';

// REAL segments from console logs (what the wheel actually has)
const REAL_SEGMENTS = [
  { id: "item_249_1_86", text: "Pomodoro Focus Session (Tailored for 125min)", domain: "productive_practical", activity_tailored_id: "86" },
  { id: "item_249_2_155", text: "Advanced Urban Parkour (Tailored for 125min)", domain: "general", activity_tailored_id: "155" },
  { id: "item_249_3_124", text: "App-Guided Movement Session (Tailored for 125min)", domain: "productive_practical", activity_tailored_id: "124" },
  { id: "item_249_4_84", text: "Deliberate Practice Session (Tailored for 125min)", domain: "productive_practical", activity_tailored_id: "84" },
  { id: "item_249_5_64", text: "Emotional Anchoring Technique (Tailored for 125min)", domain: "emotional", activity_tailored_id: "64" },
  { id: "item_249_6_121", text: "Intentional Break Transition (Tailored for 125min)", domain: "productive_practical", activity_tailored_id: "121" }
];

describe('Wheel Architectural Mismatch', () => {

  describe('🚨 ARCHITECTURAL ISSUE: ID Mismatch', () => {
    it('should FAIL because of ID mismatch between segments and color generation', () => {
      console.log('=== REPRODUCING ARCHITECTURAL MISMATCH ===');
      
      // Step 1: Simulate the WRONG way (current broken system)
      // This is what app-shell.ts was doing: using activity_tailored_id for color generation
      const activitiesWithWrongIds = REAL_SEGMENTS.map((segment, index) => ({
        id: segment.activity_tailored_id, // WRONG: Using activity_tailored_id (86, 155, etc.)
        name: segment.text,
        domain: segment.domain
      }));
      
      console.log('WRONG: Activities prepared for color generation (using activity_tailored_id):');
      activitiesWithWrongIds.forEach((activity, index) => {
        console.log(`  Activity ${index}: id="${activity.id}", name="${activity.name}"`);
      });
      
      // Step 2: Generate colors with wrong IDs
      const wheelColors = generateWheelColors(activitiesWithWrongIds, { dualColorMode: true });
      
      console.log('Generated wheel colors with WRONG IDs:');
      wheelColors.forEach((colorInfo, index) => {
        console.log(`  Color ${index}: activityId="${colorInfo.activityId}"`);
      });
      
      // Step 3: Try to map segments to colors (this will FAIL)
      const mappingResults = REAL_SEGMENTS.map((segment, index) => {
        const activityId = segment.id; // Segments have full IDs like "item_249_1_86"
        const colorInfo = wheelColors.find(color => color.activityId === activityId);
        
        console.log(`Segment ${index}: "${segment.id}" -> looking for activityId "${activityId}" -> found: ${!!colorInfo}`);
        
        return {
          segmentId: segment.id,
          activityId,
          found: !!colorInfo
        };
      });
      
      // Step 4: Count failures
      const failureCount = mappingResults.filter(r => !r.found).length;
      console.log(`RESULT: ${failureCount} out of ${mappingResults.length} segments failed to find colors`);
      
      // This should FAIL (all 6 segments should fail to find colors)
      expect(failureCount).toBe(0); // This will fail, proving the architectural issue
    });
  });

  describe('✅ ARCHITECTURAL FIX: Correct ID Usage', () => {
    it('should PASS when using correct segment IDs for color generation', () => {
      console.log('=== TESTING ARCHITECTURAL FIX ===');
      
      // Step 1: Simulate the CORRECT way (fixed system)
      // This is what app-shell.ts should do: using segment.id for color generation
      const activitiesWithCorrectIds = REAL_SEGMENTS.map((segment, index) => ({
        id: segment.id, // CORRECT: Using segment.id (item_249_1_86, etc.)
        name: segment.text,
        domain: segment.domain
      }));
      
      console.log('CORRECT: Activities prepared for color generation (using segment.id):');
      activitiesWithCorrectIds.forEach((activity, index) => {
        console.log(`  Activity ${index}: id="${activity.id}", name="${activity.name}"`);
      });
      
      // Step 2: Generate colors with correct IDs
      const wheelColors = generateWheelColors(activitiesWithCorrectIds, { dualColorMode: true });
      
      console.log('Generated wheel colors with CORRECT IDs:');
      wheelColors.forEach((colorInfo, index) => {
        console.log(`  Color ${index}: activityId="${colorInfo.activityId}"`);
      });
      
      // Step 3: Try to map segments to colors (this should SUCCEED)
      const mappingResults = REAL_SEGMENTS.map((segment, index) => {
        const activityId = segment.id; // Segments have full IDs like "item_249_1_86"
        const colorInfo = wheelColors.find(color => color.activityId === activityId);
        
        console.log(`Segment ${index}: "${segment.id}" -> looking for activityId "${activityId}" -> found: ${!!colorInfo}`);
        
        return {
          segmentId: segment.id,
          activityId,
          found: !!colorInfo,
          colorInfo
        };
      });
      
      // Step 4: Verify success
      const successCount = mappingResults.filter(r => r.found).length;
      console.log(`RESULT: ${successCount} out of ${mappingResults.length} segments successfully found colors`);
      
      // This should PASS (all 6 segments should find colors)
      expect(successCount).toBe(REAL_SEGMENTS.length);
      
      // Verify colors are correct
      mappingResults.forEach((result, index) => {
        expect(result.found).toBe(true);
        expect(result.colorInfo).toBeDefined();
        if (result.colorInfo) {
          expect(result.colorInfo.centerColor).toBeDefined();
          expect(result.colorInfo.extremityColor).toBeDefined();
        }
      });
    });
  });

  describe('🔍 ARCHITECTURAL ANALYSIS', () => {
    it('should demonstrate the exact ID mismatch problem', () => {
      console.log('=== ARCHITECTURAL ANALYSIS ===');
      
      REAL_SEGMENTS.forEach((segment, index) => {
        console.log(`Segment ${index}:`);
        console.log(`  - Full segment ID: "${segment.id}"`);
        console.log(`  - Activity tailored ID: "${segment.activity_tailored_id}"`);
        console.log(`  - Mismatch: ${segment.id !== segment.activity_tailored_id}`);
        
        // The architectural issue
        const idMismatch = segment.id !== segment.activity_tailored_id;
        expect(idMismatch).toBe(true); // This proves there's an ID mismatch
      });
      
      console.log('CONCLUSION: Segments have full IDs but color generation was using activity_tailored_id');
    });
  });

  describe('🎯 VERIFY CONSOLE LOG REPRODUCTION', () => {
    it('should reproduce the exact console log scenario', () => {
      console.log('=== REPRODUCING EXACT CONSOLE LOG SCENARIO ===');
      
      // From console logs:
      // Line 482: wheelColors activityIds: ['121', '124', '155', '64', '84', '86']
      // Line 485: segment IDs: ['item_249_1_86', 'item_249_2_155', 'item_249_3_124']
      
      const expectedColorActivityIds = ['121', '124', '155', '64', '84', '86'];
      const expectedSegmentIds = ['item_249_1_86', 'item_249_2_155', 'item_249_3_124', 'item_249_4_84', 'item_249_5_64', 'item_249_6_121'];
      
      // Simulate wrong ID preparation (what was happening)
      const wrongActivities = REAL_SEGMENTS.map(segment => ({
        id: segment.activity_tailored_id,
        name: segment.text,
        domain: segment.domain
      }));
      
      const wheelColors = generateWheelColors(wrongActivities, { dualColorMode: true });
      const actualColorActivityIds = wheelColors.map(c => c.activityId);
      
      console.log('Expected color activity IDs (from console):', expectedColorActivityIds);
      console.log('Actual color activity IDs (reproduced):', actualColorActivityIds);
      console.log('Expected segment IDs (from console):', expectedSegmentIds);
      console.log('Actual segment IDs (reproduced):', REAL_SEGMENTS.map(s => s.id));
      
      // Verify we reproduced the exact same scenario
      expect(actualColorActivityIds.sort()).toEqual(expectedColorActivityIds.sort());
      expect(REAL_SEGMENTS.map(s => s.id)).toEqual(expectedSegmentIds);
      
      // Verify the mismatch
      const canMapAny = expectedSegmentIds.some(segmentId => 
        actualColorActivityIds.includes(segmentId)
      );
      
      expect(canMapAny).toBe(false); // This proves no segment can find its color
      console.log('✅ Successfully reproduced the exact architectural mismatch from console logs');
    });
  });
});
