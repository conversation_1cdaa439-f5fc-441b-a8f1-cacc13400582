/**
 * Test to verify the ID mapping fix works correctly
 */

import { describe, it, expect } from 'vitest';

describe('Wheel ID Mapping Fix', () => {
  it('should correctly extract activity ID from segment ID', () => {
    // Test the ID extraction logic from app-shell.ts
    const testCases = [
      {
        segmentId: 'item_245_1_86-seg-0',
        expectedActivityId: 'item_245_1_86'
      },
      {
        segmentId: 'item_245_2_155-seg-15',
        expectedActivityId: 'item_245_2_155'
      },
      {
        segmentId: 'item_245_3_124',
        expectedActivityId: 'item_245_3_124' // No -seg- suffix
      }
    ];

    testCases.forEach(({ segmentId, expectedActivityId }) => {
      // This is the logic from app-shell.ts
      const extractedActivityId = segmentId.includes('-seg-') ? segmentId.split('-seg-')[0] : segmentId;
      
      expect(extractedActivityId).toBe(expectedActivityId);
      console.log(`✅ ${segmentId} -> ${extractedActivityId}`);
    });
  });

  it('should handle edge cases in ID extraction', () => {
    const edgeCases = [
      {
        segmentId: '',
        expectedActivityId: ''
      },
      {
        segmentId: 'simple-id',
        expectedActivityId: 'simple-id'
      },
      {
        segmentId: 'complex-id-with-seg-in-middle-seg-99',
        expectedActivityId: 'complex-id-with-seg-in-middle'
      }
    ];

    edgeCases.forEach(({ segmentId, expectedActivityId }) => {
      const extractedActivityId = segmentId.includes('-seg-') ? segmentId.split('-seg-')[0] : segmentId;
      
      expect(extractedActivityId).toBe(expectedActivityId);
      console.log(`✅ Edge case: ${segmentId} -> ${extractedActivityId}`);
    });
  });
});
