/**
 * Domain Color Service Tests
 * 
 * Comprehensive tests for the frontend-only domain color system.
 * These tests validate that colors are properly assigned based on domain codes
 * without any backend dependencies.
 */

import { describe, it, expect } from 'vitest';
import { 
  getDomainColor, 
  getAllDomainColors, 
  getDomainColorWithOpacity,
  getContrastingTextColor,
  validateWheelDomains,
  applyColorsToWheel
} from '../src/services/domainColorService.js';

describe('Domain Color Service', () => {
  describe('getDomainColor', () => {
    it('should return correct colors for main domain categories', () => {
      // Physical domain - Red family
      expect(getDomainColor('physical')).toBe('#E74C3C');
      expect(getDomainColor('phys_strength')).toBe('#E74C3C');
      expect(getDomainColor('phys_cardio')).toBe('#C0392B');
      
      // Creative domain - Orange family
      expect(getDomainColor('creative')).toBe('#FF8C00');
      expect(getDomainColor('creative_visual')).toBe('#E67E22');
      expect(getDomainColor('creative_writing')).toBe('#D35400');
      
      // Social domain - Yellow family
      expect(getDomainColor('social')).toBe('#FFD700');
      expect(getDomainColor('soc_family')).toBe('#F39C12');
      expect(getDomainColor('soc_leadership')).toBe('#E67E22');
      
      // Mental/Learning domain - Blue family
      expect(getDomainColor('intellectual')).toBe('#3498DB');
      expect(getDomainColor('intel_learn')).toBe('#2980B9');
      expect(getDomainColor('intel_strategic')).toBe('#1ABC9C');
      
      // Reflective domain - Indigo family
      expect(getDomainColor('reflective')).toBe('#6C5CE7');
      expect(getDomainColor('refl_meditate')).toBe('#9B59B6');
      expect(getDomainColor('refl_journal')).toBe('#8E44AD');
    });

    it('should handle case insensitive domain codes', () => {
      expect(getDomainColor('PHYSICAL')).toBe('#E74C3C');
      expect(getDomainColor('Physical')).toBe('#E74C3C');
      expect(getDomainColor('pHySiCaL')).toBe('#E74C3C');
    });

    it('should fallback to main domain for unknown sub-domains', () => {
      expect(getDomainColor('phys_unknown')).toBe('#E74C3C'); // Falls back to 'physical'
      expect(getDomainColor('creative_unknown')).toBe('#FF8C00'); // Falls back to 'creative'
      expect(getDomainColor('social_unknown')).toBe('#FFD700'); // Falls back to 'social'
    });

    it('should return general color for completely unknown domains', () => {
      expect(getDomainColor('unknown_domain')).toBe('#52C41A');
      expect(getDomainColor('xyz_abc')).toBe('#52C41A');
      expect(getDomainColor('')).toBe('#52C41A');
      expect(getDomainColor(null as any)).toBe('#52C41A');
      expect(getDomainColor(undefined as any)).toBe('#52C41A');
    });

    it('should handle edge cases gracefully', () => {
      expect(getDomainColor('_')).toBe('#52C41A');
      expect(getDomainColor('__')).toBe('#52C41A');
      expect(getDomainColor('123')).toBe('#52C41A');
      expect(getDomainColor('   ')).toBe('#52C41A');
    });
  });

  describe('getAllDomainColors', () => {
    it('should return all available domain colors', () => {
      const colors = getAllDomainColors();
      
      // Check that it returns an object
      expect(typeof colors).toBe('object');
      expect(colors).not.toBeNull();
      
      // Check that it contains expected main domains
      expect(colors.physical).toBe('#E74C3C');
      expect(colors.creative).toBe('#FF8C00');
      expect(colors.social).toBe('#FFD700');
      expect(colors.intellectual).toBe('#3498DB');
      expect(colors.reflective).toBe('#6C5CE7');
      expect(colors.general).toBe('#52C41A');
    });

    it('should return a copy of the color map (not reference)', () => {
      const colors1 = getAllDomainColors();
      const colors2 = getAllDomainColors();
      
      // Should be equal but not the same reference
      expect(colors1).toEqual(colors2);
      expect(colors1).not.toBe(colors2);
      
      // Modifying one should not affect the other
      colors1.test = '#000000';
      expect(colors2.test).toBeUndefined();
    });
  });

  describe('getDomainColorWithOpacity', () => {
    it('should return RGBA color with specified opacity', () => {
      const rgbaColor = getDomainColorWithOpacity('physical', 0.5);
      expect(rgbaColor).toMatch(/^rgba\(\d+, \d+, \d+, 0\.5\)$/);
      
      // Test with physical color #E74C3C (231, 76, 60)
      expect(rgbaColor).toBe('rgba(231, 76, 60, 0.5)');
    });

    it('should use default opacity of 0.1 when not specified', () => {
      const rgbaColor = getDomainColorWithOpacity('physical');
      expect(rgbaColor).toBe('rgba(231, 76, 60, 0.1)');
    });

    it('should work with different opacity values', () => {
      expect(getDomainColorWithOpacity('creative', 0)).toBe('rgba(255, 140, 0, 0)');
      expect(getDomainColorWithOpacity('creative', 1)).toBe('rgba(255, 140, 0, 1)');
      expect(getDomainColorWithOpacity('creative', 0.25)).toBe('rgba(255, 140, 0, 0.25)');
    });
  });

  describe('getContrastingTextColor', () => {
    it('should return white for dark domain colors', () => {
      expect(getContrastingTextColor('physical')).toBe('#FFFFFF'); // Dark red
      expect(getContrastingTextColor('intellectual')).toBe('#FFFFFF'); // Blue
      expect(getContrastingTextColor('reflective')).toBe('#FFFFFF'); // Dark purple
    });

    it('should return black for light domain colors', () => {
      expect(getContrastingTextColor('social')).toBe('#000000'); // Gold/Yellow
    });

    it('should handle unknown domains', () => {
      const textColor = getContrastingTextColor('unknown');
      expect(textColor).toMatch(/^#(000000|FFFFFF)$/);
    });
  });

  describe('validateWheelDomains', () => {
    it('should validate wheel with all known domains', () => {
      const wheelData = {
        items: [
          { domain: 'physical', name: 'Exercise' },
          { domain: 'creative', name: 'Art' },
          { domain: 'social', name: 'Friends' }
        ]
      };

      const result = validateWheelDomains(wheelData);
      expect(result.isValid).toBe(true);
      expect(result.issues).toHaveLength(0);
    });

    it('should identify unknown domains', () => {
      const wheelData = {
        items: [
          { domain: 'physical', name: 'Exercise' },
          { domain: 'unknown_domain', name: 'Mystery' },
          { domain: '', name: 'Empty' }
        ]
      };

      const result = validateWheelDomains(wheelData);
      expect(result.isValid).toBe(false);
      expect(result.issues.length).toBeGreaterThan(0);
      expect(result.issues.some(issue => issue.includes('unknown_domain'))).toBe(true);
    });

    it('should provide suggestions for similar domains', () => {
      const wheelData = {
        items: [
          { domain: 'physica', name: 'Exercise' }, // Typo
          { domain: 'creativ', name: 'Art' } // Typo
        ]
      };

      const result = validateWheelDomains(wheelData);
      expect(result.suggestions.length).toBeGreaterThan(0);
    });
  });

  describe('applyColorsToWheel', () => {
    it('should apply colors to wheel items without colors', () => {
      const wheelData = {
        items: [
          { domain: 'physical', name: 'Exercise' },
          { domain: 'creative', name: 'Art' },
          { domain: 'social', name: 'Friends' }
        ]
      };

      const result = applyColorsToWheel(wheelData);

      expect(result.items).toBeDefined();
      expect(result.items!.length).toBe(3);
      expect(result.items![0].color).toBe('#E74C3C'); // Physical
      expect(result.items![1].color).toBe('#FF8C00'); // Creative
      expect(result.items![2].color).toBe('#FFD700'); // Social
    });

    it('should not override existing colors', () => {
      const wheelData = {
        items: [
          { domain: 'physical', name: 'Exercise', color: '#CUSTOM' },
          { domain: 'creative', name: 'Art' }
        ]
      };

      const result = applyColorsToWheel(wheelData);

      expect(result.items).toBeDefined();
      expect(result.items!.length).toBe(2);
      expect(result.items![0].color).toBe('#CUSTOM'); // Preserved
      expect(result.items![1].color).toBe('#FF8C00'); // Applied
    });

    it('should handle empty or invalid wheel data', () => {
      expect(applyColorsToWheel({} as any)).toEqual({});
      expect(applyColorsToWheel({ items: [] })).toEqual({ items: [] });
    });
  });

  describe('Color Psychology and Accessibility', () => {
    it('should use psychologically appropriate colors for domains', () => {
      // Physical activities should use energetic colors (reds)
      const physicalColor = getDomainColor('physical');
      expect(physicalColor).toMatch(/^#[E-F][0-9A-F]/); // Should start with E or F (bright)
      
      // Reflective activities should use calming colors (purples/blues)
      const reflectiveColor = getDomainColor('reflective');
      expect(['#6C5CE7', '#9B59B6', '#8E44AD']).toContain(reflectiveColor);
    });

    it('should provide sufficient color contrast for accessibility', () => {
      const domains = ['physical', 'creative', 'social', 'intellectual', 'reflective'];
      
      domains.forEach(domain => {
        const color = getDomainColor(domain);
        const textColor = getContrastingTextColor(domain);
        
        // Should return either black or white for good contrast
        expect(['#000000', '#FFFFFF']).toContain(textColor);
      });
    });

    it('should provide visually distinct colors for different domains', () => {
      const mainDomains = ['physical', 'creative', 'social', 'intellectual', 'reflective'];
      const colors = mainDomains.map(domain => getDomainColor(domain));
      
      // All colors should be different
      const uniqueColors = new Set(colors);
      expect(uniqueColors.size).toBe(colors.length);
      
      // Colors should be sufficiently different (basic check)
      for (let i = 0; i < colors.length; i++) {
        for (let j = i + 1; j < colors.length; j++) {
          expect(colors[i]).not.toBe(colors[j]);
        }
      }
    });
  });

  describe('Integration with Real Domain Codes', () => {
    it('should handle real domain codes from backend', () => {
      // Test with actual domain codes that might come from the backend
      const realDomainCodes = [
        'phys_strength', 'phys_cardio', 'phys_flexibility', 'phys_dance',
        'creative_visual', 'creative_writing', 'creative_music',
        'soc_family', 'soc_friends', 'soc_leadership', 'soc_comm',
        'intel_learn', 'intel_strategic', 'intel_problem_solving',
        'refl_meditate', 'refl_journal', 'refl_gratitude',
        'prod_health', 'prod_time', 'prod_organization',
        'emot_aware', 'emot_regulation'
      ];

      realDomainCodes.forEach(domain => {
        const color = getDomainColor(domain);
        
        // Should return a valid hex color
        expect(color).toMatch(/^#[0-9A-Fa-f]{6}$/);
        
        // Should not be the fallback color for known domains
        if (domain.includes('_')) {
          const mainDomain = domain.split('_')[0];
          if (['phys', 'creative', 'soc', 'intel', 'refl', 'prod', 'emot'].includes(mainDomain)) {
            expect(color).not.toBe('#52C41A'); // Should not fallback to general
          }
        }
      });
    });
  });
});
