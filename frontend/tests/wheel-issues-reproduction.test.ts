/**
 * Wheel Issues Reproduction Tests
 * 
 * This test suite reproduces the three critical issues identified:
 * 1. Excessive caching messages ("Wheel data unchanged, skipping processing")
 * 2. "User profile not found" error when adding activities to wheel
 * 3. Greyish items after wheel item removal
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { fixture, html } from '@open-wc/testing';
import '../src/components/app-shell.ts';
import '../src/components/game-wheel/game-wheel.ts';
import type { AppShell } from '../src/components/app-shell.ts';
import type { GameWheel } from '../src/components/game-wheel/game-wheel.ts';

// Mock fetch for API calls
const mockFetch = vi.fn();
global.fetch = mockFetch;

// Mock console.log to capture caching messages
const mockConsoleLog = vi.fn();
const originalConsoleLog = console.log;

describe('Wheel Issues Reproduction', () => {
  let appShell: AppShell;
  let gameWheel: GameWheel;
  
  beforeEach(async () => {
    // Reset mocks
    vi.clearAllMocks();
    mockConsoleLog.mockClear();
    
    // Mock console.log to capture caching messages
    console.log = mockConsoleLog;
    
    // Create app-shell component
    appShell = await fixture(html`<app-shell></app-shell>`);
    await appShell.updateComplete;
    
    // Create game-wheel component for caching tests
    gameWheel = await fixture(html`<game-wheel></game-wheel>`);
    await gameWheel.updateComplete;
    
    // Set up initial wheel data
    const initialWheelData = {
      segments: [
        {
          id: 'item_210_1_124',
          text: 'App-Guided Movement Session (Tailored for 19min)',
          name: 'App-Guided Movement Session (Tailored for 19min)',
          description: 'Tailored for your current context: Use a smartphone app to guide a short physical exercise or stretching session.',
          percentage: 16.666666666666668,
          color: '#27AE60',
          domain: 'productive_practical',
          base_challenge_rating: 50,
          activity_tailored_id: '124',
          wheel_item_id: 'item_210_1_124'
        },
        {
          id: 'item_210_2_146',
          text: 'Balance Challenge (Tailored for 10min)',
          name: 'Balance Challenge (Tailored for 10min)',
          description: 'Tailored for your current context: Quick balance exercises to improve stability and core strength',
          percentage: 16.666666666666668,
          color: '#95A5A6',
          domain: 'general',
          base_challenge_rating: 50,
          activity_tailored_id: '146',
          wheel_item_id: 'item_210_2_146'
        },
        {
          id: 'item_210_3_64',
          text: 'Emotional Anchoring Technique (Tailored for 19min)',
          name: 'Emotional Anchoring Technique (Tailored for 19min)',
          description: 'Tailored for your current context: A practice for developing an emotional "anchor" to help regulate intense emotions',
          percentage: 16.666666666666668,
          color: '#9B59B6',
          domain: 'emotional',
          base_challenge_rating: 50,
          activity_tailored_id: '64',
          wheel_item_id: 'item_210_3_64'
        }
      ],
      wheelId: '210',
      createdAt: '2025-06-26T17:32:25.014Z'
    };
    
    // Set wheel data in state machine
    (appShell as any).wheelStateMachine.setWheelData(initialWheelData);
    await appShell.updateComplete;
  });

  afterEach(() => {
    vi.clearAllMocks();
    console.log = originalConsoleLog;
  });

  describe('Issue 1: Excessive Caching Messages', () => {
    it('should reproduce excessive "Wheel data unchanged, skipping processing" messages', async () => {
      // Set the same wheel data multiple times to trigger caching logic
      const wheelData = {
        segments: [
          {
            id: 'item_1',
            text: 'Test Activity',
            percentage: 100,
            color: '#FF0000'
          }
        ],
        wheelId: 'test-wheel',
        createdAt: new Date().toISOString()
      };

      // Set wheel data multiple times
      gameWheel.wheelData = wheelData;
      await gameWheel.updateComplete;
      
      gameWheel.wheelData = wheelData; // Same data - should trigger caching message
      await gameWheel.updateComplete;
      
      gameWheel.wheelData = wheelData; // Same data again - should trigger caching message
      await gameWheel.updateComplete;

      // Check that caching messages were logged
      const cachingMessages = mockConsoleLog.mock.calls.filter(call => 
        call[0] && call[0].includes('Wheel data unchanged, skipping processing')
      );
      
      // This test should FAIL initially - showing the excessive caching issue
      expect(cachingMessages.length).toBeGreaterThan(0);
      console.log(`Found ${cachingMessages.length} caching messages - this indicates the issue exists`);
    });

    it('should not have environment variable to control caching (current issue)', () => {
      // Check that there's no environment variable to control caching
      const viteCacheEnabled = import.meta.env.VITE_WHEEL_CACHE_ENABLED;
      const viteDisableCache = import.meta.env.VITE_DISABLE_WHEEL_CACHE;
      
      // This test should FAIL initially - no env var exists
      expect(viteCacheEnabled).toBeUndefined();
      expect(viteDisableCache).toBeUndefined();
      console.log('No caching environment variable found - this is the issue');
    });
  });

  describe('Issue 2: User Profile Not Found Error', () => {
    it('should reproduce "User profile not found" error when adding activities', async () => {
      // Mock the API to return the actual error we see in logs
      mockFetch.mockResolvedValue({
        ok: false,
        status: 400,
        json: () => Promise.resolve({
          success: false,
          error: 'User profile not found'
        })
      });

      const mockActivity = {
        id: 'test-activity-123',
        name: 'Fun Activity',
        type: 'generic'
      };

      // Try to add activity - this should fail with user profile error
      await (appShell as any).addActivityToWheel(mockActivity);

      // Verify the API was called
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/wheel-items/'),
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json'
          }),
          credentials: 'include',
          body: expect.stringContaining('test-activity-123')
        })
      );

      // This test should FAIL initially - showing the authentication issue
      const lastCall = mockFetch.mock.calls[mockFetch.mock.calls.length - 1];
      const requestBody = JSON.parse(lastCall[1].body);
      expect(requestBody.activity_id).toBe('test-activity-123');
      console.log('API call made but will fail with user profile error - this is the issue');
    });

    it('should not include proper authentication headers (current issue)', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ success: true })
      });

      const mockActivity = {
        id: 'test-activity-123',
        name: 'Fun Activity',
        type: 'generic'
      };

      await (appShell as any).addActivityToWheel(mockActivity);

      // Check if proper authentication headers are missing
      const lastCall = mockFetch.mock.calls[mockFetch.mock.calls.length - 1];
      const headers = lastCall[1].headers;
      
      // This test should FAIL initially - missing proper auth headers
      expect(headers['x-debug-user-id']).toBeUndefined();
      expect(headers['Authorization']).toBeUndefined();
      console.log('Missing authentication headers - this is the issue');
    });
  });

  describe('Issue 3: Greyish Items After Removal', () => {
    it('should reproduce greyish items after wheel item removal', async () => {
      // Mock API response that returns items with grey colors (the actual issue)
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          message: 'Activity "Emotional Anchoring Technique" removed successfully',
          wheel_data: {
            id: '210',
            name: 'Foundation Wheel - 17:32',
            segments: [
              {
                id: 'item_210_1_124',
                name: 'App-Guided Movement Session',
                domain: 'reflective',
                color: '#95A5A6', // Grey color - this is the issue!
                percentage: 20,
                position: 0,
                activity_id: null,
                activity_tailored_id: 124,
                description: 'A guided movement session using a mobile app',
                base_challenge_rating: 50
              },
              {
                id: 'item_210_2_146',
                name: 'Balance Challenge (Tailored for 10min)',
                domain: 'general',
                color: '#95A5A6', // Grey color - this is the issue!
                percentage: 20,
                position: 1,
                activity_id: null,
                activity_tailored_id: 105,
                description: 'Tailored for your current context: Quick balance exercises to improve stability and core strength',
                base_challenge_rating: 35
              }
            ],
            metadata: {},
            timestamp: '2025-06-26T17:36:46.087742'
          },
          wheel_id: 210
        })
      });

      // Remove an item
      await (appShell as any).removeWheelItem('item_210_3_64');

      // Get the updated wheel data
      const updatedData = (appShell as any).wheelStateMachine.wheelData;
      expect(updatedData).toBeTruthy();
      expect(updatedData.segments).toHaveLength(2);

      // Check that items turned grey (this is the issue we're reproducing)
      const greyItems = updatedData.segments.filter((s: any) => s.color === '#95A5A6');

      // This test should FAIL initially - showing the greyish color issue
      expect(greyItems.length).toBeGreaterThan(0);
      console.log(`Found ${greyItems.length} grey items after removal - this indicates the issue exists`);

      // All items should NOT be grey - this is what we want to fix
      expect(greyItems.length).toBe(updatedData.segments.length); // Currently all items turn grey
    });

    it('should not apply domain colors after removal (current issue)', async () => {
      // Mock API response with backend data that lacks proper colors
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          wheel_data: {
            segments: [
              {
                id: 'item_210_1_124',
                name: 'App-Guided Movement Session',
                domain: 'productive_practical', // Has domain but no color
                percentage: 50,
                activity_tailored_id: 124
              },
              {
                id: 'item_210_2_146',
                name: 'Balance Challenge',
                domain: 'general', // Has domain but no color
                percentage: 50,
                activity_tailored_id: 146
              }
            ]
          }
        })
      });

      await (appShell as any).removeWheelItem('item_210_3_64');

      const updatedData = (appShell as any).wheelStateMachine.wheelData;

      // Check that domain colors are not being applied properly
      const itemsWithDomainColors = updatedData.segments.filter((s: any) =>
        s.color && s.color !== '#95A5A6' && s.domain
      );

      // This test should FAIL initially - domain colors not applied after removal
      expect(itemsWithDomainColors.length).toBe(0);
      console.log('Domain colors not applied after removal - this is the issue');
    });
  });

  describe('Integration Test: All Issues Combined', () => {
    it('should demonstrate all three issues in a realistic scenario', async () => {
      console.log('=== REPRODUCING ALL THREE ISSUES ===');

      // Issue 1: Trigger excessive caching
      const sameData = (appShell as any).wheelStateMachine.wheelData;
      gameWheel.wheelData = sameData;
      await gameWheel.updateComplete;
      gameWheel.wheelData = sameData; // Should trigger caching message
      await gameWheel.updateComplete;

      // Issue 2: Try to add activity (will fail with user profile error)
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: () => Promise.resolve({ success: false, error: 'User profile not found' })
      });

      await (appShell as any).addActivityToWheel({ id: 'test', name: 'Test', type: 'generic' });

      // Issue 3: Remove item and get grey colors
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          wheel_data: {
            segments: [
              { id: 'item_1', name: 'Activity 1', domain: 'physical', color: '#95A5A6', percentage: 50 },
              { id: 'item_2', name: 'Activity 2', domain: 'mental', color: '#95A5A6', percentage: 50 }
            ]
          }
        })
      });

      await (appShell as any).removeWheelItem('item_210_3_64');

      // Verify all issues are present
      const cachingMessages = mockConsoleLog.mock.calls.filter(call =>
        call[0] && call[0].includes('Wheel data unchanged, skipping processing')
      );
      expect(cachingMessages.length).toBeGreaterThan(0);

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/wheel-items/'),
        expect.objectContaining({ method: 'POST' })
      );

      const finalData = (appShell as any).wheelStateMachine.wheelData;
      const greyItems = finalData.segments.filter((s: any) => s.color === '#95A5A6');
      expect(greyItems.length).toBeGreaterThan(0);

      console.log('All three issues reproduced successfully');
    });
  });
});
