/**
 * Comprehensive architecture validation tests for the frontend wheel system.
 * 
 * This test suite validates the architectural improvements made to fix:
 * 1. Color detection and assignment from backend data
 * 2. Data flow integrity from backend to frontend
 * 3. Wheel component state management
 * 4. Error handling and fallback strategies
 * 5. Frontend-backend integration consistency
 * 
 * These tests ensure that the frontend correctly processes backend data
 * and maintains architectural consistency.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { GameWheel } from '../src/components/game-wheel/game-wheel'
import { getDomainColor } from '../src/services/domainColorService.js'
import type { WheelData } from '../src/components/game-wheel/wheel-types.js'

// Mock data that matches the GameWheel WheelData interface
const mockWheelData: WheelData = {
  segments: [
    {
      id: 'item-1',
      text: 'Physical Exercise',
      name: 'Physical Exercise',
      description: 'A physical activity',
      domain: 'physical',
      color: '#E74C3C',
      percentage: 25.0,
      activity_tailored_id: 'activity-1',
      base_challenge_rating: 50
    },
    {
      id: 'item-2',
      text: 'Creative Writing',
      name: 'Creative Writing',
      description: 'A creative activity',
      domain: 'creative',
      color: '#FF8C00',
      percentage: 25.0,
      activity_tailored_id: 'activity-2',
      base_challenge_rating: 50
    },
    {
      id: 'item-3',
      text: 'Problem Solving',
      name: 'Problem Solving',
      description: 'An intellectual activity',
      domain: 'intellectual',
      color: '#3498DB',
      percentage: 25.0,
      activity_tailored_id: 'activity-3',
      base_challenge_rating: 50
    },
    {
      id: 'item-4',
      text: 'Social Connection',
      name: 'Social Connection',
      description: 'A social activity',
      domain: 'social',
      color: '#FFD700',
      percentage: 25.0,
      activity_tailored_id: 'activity-4',
      base_challenge_rating: 50
    }
  ],
  wheelId: 'test-wheel-1',
  createdAt: '2025-06-23T15:00:00Z'
}

describe('Frontend Architecture Validation', () => {
  let gameWheel: GameWheel

  beforeEach(() => {
    // Reset DOM
    document.body.innerHTML = ''

    // Create fresh instances
    gameWheel = new GameWheel()
    
    // Mock console methods to avoid noise in tests
    vi.spyOn(console, 'log').mockImplementation(() => {})
    vi.spyOn(console, 'warn').mockImplementation(() => {})
    vi.spyOn(console, 'error').mockImplementation(() => {})
  })

  describe('Color Detection and Assignment', () => {
    it('should correctly detect colors from backend wheel data', () => {
      // Test that frontend can extract colors from backend data
      const segments = mockWheelData.segments

      for (const segment of segments) {
        expect(segment.color).toBeDefined()
        expect(typeof segment.color).toBe('string')
        expect(segment.color).toMatch(/^#[0-9A-Fa-f]{6}$/)
      }
    })

    it('should maintain color consistency for same domains', () => {
      // Test that same domains get same colors
      const physicalSegments = mockWheelData.segments.filter(segment => segment.domain === 'physical')
      const creativeSegments = mockWheelData.segments.filter(segment => segment.domain === 'creative')
      
      if (physicalSegments.length > 1) {
        const firstColor = physicalSegments[0].color
        physicalSegments.forEach(segment => {
          expect(segment.color).toBe(firstColor)
        })
      }

      if (creativeSegments.length > 1) {
        const firstColor = creativeSegments[0].color
        creativeSegments.forEach(segment => {
          expect(segment.color).toBe(firstColor)
        })
      }
    })

    it('should provide fallback colors for unknown domains', async () => {
      const unknownDomainSegment = {
        ...mockWheelData.segments[0],
        domain: 'unknown_domain_xyz',
        color: undefined
      }

      // Domain color service should provide fallback
      const fallbackColor = getDomainColor('unknown_domain_xyz')
      expect(fallbackColor).toBeDefined()
      expect(typeof fallbackColor).toBe('string')
      expect(fallbackColor).toMatch(/^#[0-9A-Fa-f]{6}$/)
    })
  })

  describe('Data Flow Integrity', () => {
    it('should correctly process backend wheel data structure', () => {
      // Test that frontend can handle backend data format
      expect(mockWheelData).toHaveProperty('wheelId')
      expect(mockWheelData).toHaveProperty('segments')
      expect(mockWheelData).toHaveProperty('createdAt')

      expect(Array.isArray(mockWheelData.segments)).toBe(true)
      expect(mockWheelData.segments.length).toBeGreaterThan(0)
    })

    it('should validate wheel segment data structure', () => {
      for (const segment of mockWheelData.segments) {
        // Required fields from backend
        expect(segment).toHaveProperty('id')
        expect(segment).toHaveProperty('text')
        expect(segment).toHaveProperty('color')
        expect(segment).toHaveProperty('percentage')

        // Data type validation
        expect(typeof segment.id).toBe('string')
        expect(typeof segment.text).toBe('string')
        expect(typeof segment.color).toBe('string')
        expect(typeof segment.percentage).toBe('number')
      }
    })

    it('should handle missing or malformed data gracefully', () => {
      const malformedData: Partial<WheelData> = {
        wheelId: 'test-wheel',
        createdAt: new Date().toISOString(),
        segments: [
          {
            id: 'item-1',
            text: 'Test Activity',
            name: 'Test Activity',
            description: 'Test description',
            domain: 'general',
            color: '#52C41A',
            percentage: 100,
            activity_tailored_id: 'activity1',
            base_challenge_rating: 50
          }
        ]
      }

      // Should not throw errors when processing malformed data
      expect(() => {
        gameWheel.wheelData = malformedData as WheelData
      }).not.toThrow()
    })
  })

  describe('Wheel Component State Management', () => {
    it('should correctly initialize wheel component', () => {
      document.body.appendChild(gameWheel)

      expect(gameWheel).toBeDefined()
      expect(gameWheel.tagName.toLowerCase()).toBe('game-wheel')
    })

    it('should update wheel display when data changes', () => {
      document.body.appendChild(gameWheel)

      // Load initial data
      gameWheel.wheelData = mockWheelData

      // Verify wheel data is set
      expect(gameWheel.wheelData).toBeDefined()
      expect(gameWheel.wheelData?.segments.length).toBe(mockWheelData.segments.length)
    })

    it('should maintain state consistency during updates', () => {
      document.body.appendChild(gameWheel)

      // Load data
      gameWheel.wheelData = mockWheelData

      // Get initial state
      const initialSegmentCount = gameWheel.wheelData?.segments.length

      // Update with new data
      const updatedData = {
        ...mockWheelData,
        segments: mockWheelData.segments.slice(0, 2) // Reduce to 2 segments
      }

      gameWheel.wheelData = updatedData

      // Verify state updated correctly
      const updatedSegmentCount = gameWheel.wheelData?.segments.length
      expect(updatedSegmentCount).toBe(2)
      expect(updatedSegmentCount).not.toBe(initialSegmentCount)
    })
  })

  describe('Error Handling and Resilience', () => {
    it('should handle empty wheel data', () => {
      const emptyData: WheelData = {
        wheelId: 'empty-wheel',
        createdAt: new Date().toISOString(),
        segments: []
      }

      expect(() => {
        gameWheel.wheelData = emptyData
      }).not.toThrow()
    })

    it('should handle network errors gracefully', () => {
      // Mock fetch to simulate network error
      global.fetch = vi.fn().mockRejectedValue(new Error('Network error'))
      
      expect(() => {
        // Simulate network error by setting invalid data
        gameWheel.wheelData = null
      }).not.toThrow()
    })

    it('should provide meaningful error messages', () => {
      // Test that the component handles null data gracefully without crashing
      expect(() => {
        gameWheel.wheelData = null
      }).not.toThrow()

      // Verify the component is still functional
      expect(gameWheel.wheelData).toBeNull()
    })
  })

  describe('Frontend-Backend Integration', () => {
    it('should correctly map backend domain codes to frontend colors', async () => {
      const backendDomains = [
        'physical', 'creative', 'intellectual', 'social', 'emotional',
        'phys_cardio', 'creative_visual', 'intel_problem', 'soc_comm'
      ]
      
      for (const domain of backendDomains) {
        const color = getDomainColor(domain)
        expect(color).toBeDefined()
        expect(color).toMatch(/^#[0-9A-Fa-f]{6}$/)
      }
    })

    it('should handle backend schema evolution', () => {
      // Test with legacy data format converted to current format
      const legacyData: WheelData = {
        wheelId: 'legacy-wheel',
        createdAt: new Date().toISOString(),
        segments: [
          {
            id: 'legacy-item',
            text: 'Legacy Activity',
            name: 'Legacy Activity',
            description: 'Legacy activity description',
            domain: 'physical',
            color: '#E74C3C',
            percentage: 100,
            activity_tailored_id: 'legacy-activity',
            base_challenge_rating: 50
          }
        ]
      }

      // Should handle gracefully
      expect(() => {
        gameWheel.wheelData = legacyData
      }).not.toThrow()
    })

    it('should validate data contracts between frontend and backend', () => {
      // Ensure frontend expects the same data structure as backend provides
      const requiredFields = ['wheelId', 'segments', 'createdAt']

      for (const field of requiredFields) {
        expect(mockWheelData).toHaveProperty(field)
      }

      // Ensure wheel segments have required fields
      const requiredSegmentFields = ['id', 'text', 'color', 'percentage']

      for (const segment of mockWheelData.segments) {
        for (const field of requiredSegmentFields) {
          expect(segment).toHaveProperty(field)
        }
      }
    })
  })
})
