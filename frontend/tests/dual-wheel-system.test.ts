/**
 * Dual Wheel System Tests
 * 
 * Tests the complete dual wheel system implementation:
 * 1. Inner wheel with domain colors
 * 2. Outer wheel with distinct colors
 * 3. Proper nail spacing and configuration
 * 4. Color generation and assignment
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { generateWheelColors } from '../src/services/domainColorService.js';
import { DEFAULT_WHEEL_CONFIG } from '../src/components/game-wheel/wheel-types.js';

describe('Dual Wheel System', () => {
  
  describe('Wheel Configuration', () => {
    it('should have proper dual wheel configuration', () => {
      expect(DEFAULT_WHEEL_CONFIG).toHaveProperty('radius');
      expect(DEFAULT_WHEEL_CONFIG).toHaveProperty('innerWheelRadius');
      expect(DEFAULT_WHEEL_CONFIG).toHaveProperty('nailCount');
      expect(DEFAULT_WHEEL_CONFIG).toHaveProperty('innerNailCount');

      // Inner wheel should be half the size of outer wheel
      expect(DEFAULT_WHEEL_CONFIG.innerWheelRadius).toBe(DEFAULT_WHEEL_CONFIG.radius / 2);

      // Inner wheel should have doubled nails (more than 8, less than outer wheel)
      expect(DEFAULT_WHEEL_CONFIG.innerNailCount).toBeLessThan(DEFAULT_WHEEL_CONFIG.nailCount);
      expect(DEFAULT_WHEEL_CONFIG.innerNailCount).toBeGreaterThan(8);
      expect(DEFAULT_WHEEL_CONFIG.innerNailCount).toBeLessThanOrEqual(25);

      // Ball should be positioned to collide with inner wheel
      expect(DEFAULT_WHEEL_CONFIG.ballStartOffset).toBeLessThan(DEFAULT_WHEEL_CONFIG.innerWheelRadius);
      expect(DEFAULT_WHEEL_CONFIG.ballStartOffset).toBe(90); // Should be 90 for collision with inner wheel at radius 100
    });

    it('should calculate proper nail spacing for inner wheel', () => {
      const ballDiameter = DEFAULT_WHEEL_CONFIG.ballRadius * 2;
      const expectedSpacing = ballDiameter * 1.5;
      const circumference = 2 * Math.PI * DEFAULT_WHEEL_CONFIG.innerWheelRadius;
      const maxNails = Math.floor(circumference / expectedSpacing);
      
      // Inner nail count should not exceed what fits with proper spacing
      expect(DEFAULT_WHEEL_CONFIG.innerNailCount).toBeLessThanOrEqual(maxNails);
      
      // Spacing should be 1.5x ball diameter
      expect(expectedSpacing).toBe(ballDiameter * 1.5);
    });
  });

  describe('Color System Integration', () => {
    it('should generate dual colors for same domain activities', () => {
      const physicalActivities = [
        { id: 'act1', name: 'Running', domain: 'physical' },
        { id: 'act2', name: 'Swimming', domain: 'physical' },
        { id: 'act3', name: 'Cycling', domain: 'physical' },
        { id: 'act4', name: 'Yoga', domain: 'physical' },
        { id: 'act5', name: 'Dancing', domain: 'physical' }
      ];

      const wheelColors = generateWheelColors(physicalActivities, { dualColorMode: true });
      
      expect(wheelColors).toHaveLength(5);
      
      wheelColors.forEach(colorInfo => {
        // Should have dual color properties
        expect(colorInfo).toHaveProperty('centerColor');
        expect(colorInfo).toHaveProperty('extremityColor');
        expect(colorInfo).toHaveProperty('listColor');
        expect(colorInfo).toHaveProperty('backgroundColor');
        
        // Center color should be domain color (red for physical)
        expect(colorInfo.centerColor).toBe('#E74C3C');
        
        // Extremity color should be distinct
        expect(colorInfo.extremityColor).not.toBe('#E74C3C');
        expect(colorInfo.extremityColor).toMatch(/^#[0-9A-F]{6}$/i);
        
        // List color should match extremity
        expect(colorInfo.listColor).toBe(colorInfo.extremityColor);
        
        // Background should be domain color with opacity
        expect(colorInfo.backgroundColor).toContain('rgba');
        expect(colorInfo.backgroundColor).toContain('231, 76, 60'); // RGB of #E74C3C
      });
    });

    it('should generate single colors for mixed domain activities', () => {
      const mixedActivities = [
        { id: 'act1', name: 'Running', domain: 'physical' },
        { id: 'act2', name: 'Reading', domain: 'intellectual' },
        { id: 'act3', name: 'Painting', domain: 'creative' }
      ];

      // Use single color mode explicitly
      const wheelColors = generateWheelColors(mixedActivities, { dualColorMode: false });

      expect(wheelColors).toHaveLength(3);

      wheelColors.forEach(colorInfo => {
        // Should have single color properties
        expect(colorInfo).toHaveProperty('color');
        expect(colorInfo).not.toHaveProperty('centerColor');
        expect(colorInfo).not.toHaveProperty('extremityColor');
      });

      // Should use fixed outer wheel colors (not domain colors)
      const fixedColors = ['#e6194B', '#3cb44b', '#ffe119', '#4363d8', '#f58231', '#911eb4', '#f032e6', '#9A6324', '#a9a9a9', '#ffffff'];
      wheelColors.forEach(colorInfo => {
        expect(fixedColors).toContain(colorInfo.color);
      });
    });

    it('should handle domain detection from activity names', () => {
      // Test the fallback domain detection logic
      const activitiesWithoutDomain = [
        { id: 'act1', name: 'Stair Climbing Sprint', domain: undefined },
        { id: 'act2', name: 'Standing Desk Stretches', domain: undefined },
        { id: 'act3', name: 'Balance Challenge', domain: undefined },
        { id: 'act4', name: 'Power Walk Around Block', domain: undefined },
        { id: 'act5', name: 'Desk Push-ups', domain: undefined },
        { id: 'act6', name: 'Energizing Dance Break', domain: undefined }
      ];

      // Simulate the domain detection logic from app-shell.ts
      const activitiesWithDetectedDomain = activitiesWithoutDomain.map(activity => {
        let domain: string = activity.domain || 'general';
        
        if (!domain || domain === 'general') {
          const activityName = activity.name.toLowerCase();
          if (activityName.includes('climb') || activityName.includes('walk') || activityName.includes('push') || 
              activityName.includes('stretch') || activityName.includes('balance') || activityName.includes('dance') ||
              activityName.includes('exercise') || activityName.includes('workout') || activityName.includes('physical')) {
            domain = 'physical';
          } else {
            domain = 'general';
          }
        }
        
        return { ...activity, domain };
      });

      // All activities should be detected as physical
      activitiesWithDetectedDomain.forEach(activity => {
        expect(activity.domain).toBe('physical');
      });

      // Should enable dual color mode
      const domains = [...new Set(activitiesWithDetectedDomain.map(a => a.domain))];
      const useDualColorMode = domains.length === 1 && activitiesWithDetectedDomain.length > 1;
      expect(useDualColorMode).toBe(true);
    });
  });

  describe('Wheel Rendering Integration', () => {
    it('should support dual color segment rendering', () => {
      // Test that segments can have dual color properties
      const dualColorSegment = {
        id: 'test-segment',
        text: 'Test Activity',
        percentage: 20,
        startAngle: 0,
        endAngle: Math.PI / 3,
        centerColor: '#E74C3C',
        extremityColor: '#FF0000',
        listColor: '#FF0000',
        backgroundColor: 'rgba(231, 76, 60, 0.1)'
      };

      // Verify dual color properties exist
      expect(dualColorSegment).toHaveProperty('centerColor');
      expect(dualColorSegment).toHaveProperty('extremityColor');
      expect(dualColorSegment.centerColor).toBe('#E74C3C');
      expect(dualColorSegment.extremityColor).toBe('#FF0000');
    });

    it('should maintain backward compatibility with single color segments', () => {
      // Test that segments can still use single color
      const singleColorSegment = {
        id: 'test-segment',
        text: 'Test Activity',
        percentage: 20,
        startAngle: 0,
        endAngle: Math.PI / 3,
        color: '#E74C3C'
      };

      // Verify single color property exists
      expect(singleColorSegment).toHaveProperty('color');
      expect(singleColorSegment).not.toHaveProperty('centerColor');
      expect(singleColorSegment).not.toHaveProperty('extremityColor');
    });
  });

  describe('Color Distribution and Visual Quality', () => {
    it('should distribute colors evenly across the wheel', () => {
      const activities = Array.from({ length: 6 }, (_, i) => ({
        id: `act_${i}`,
        name: `Activity ${i}`,
        domain: 'physical'
      }));

      const wheelColors = generateWheelColors(activities, { dualColorMode: true });
      const extremityColors = wheelColors.map(c => c.extremityColor);

      // Colors should be different from each other
      const uniqueColors = new Set(extremityColors);
      expect(uniqueColors.size).toBe(6);

      // Colors should be well-distributed (not clustered)
      // Check that consecutive colors are different
      for (let i = 0; i < extremityColors.length - 1; i++) {
        expect(extremityColors[i]).not.toBe(extremityColors[i + 1]);
      }
    });

    it('should ensure no borders between segments for direct color contact', () => {
      // This is a conceptual test - in actual implementation,
      // segments should render without stroke/border
      const segmentRenderingConfig = {
        hasBorder: false,
        strokeWidth: 0,
        directColorContact: true
      };

      expect(segmentRenderingConfig.hasBorder).toBe(false);
      expect(segmentRenderingConfig.strokeWidth).toBe(0);
      expect(segmentRenderingConfig.directColorContact).toBe(true);
    });

    it('should maintain color consistency across wheel operations', () => {
      const activities = [
        { id: 'act1', name: 'Running', domain: 'physical' },
        { id: 'act2', name: 'Swimming', domain: 'physical' },
        { id: 'act3', name: 'Cycling', domain: 'physical' }
      ];

      // Generate colors multiple times - should be identical
      const colors1 = generateWheelColors(activities, { dualColorMode: true });
      const colors2 = generateWheelColors(activities, { dualColorMode: true });

      expect(colors1).toEqual(colors2);
    });
  });

  describe('Performance and Scalability', () => {
    it('should handle maximum number of activities efficiently', () => {
      // Generate 10 activities (maximum supported) of same domain
      const manyActivities = Array.from({ length: 10 }, (_, i) => ({
        id: `phys_${i}`,
        name: `Physical Activity ${i}`,
        domain: 'physical'
      }));

      const startTime = performance.now();
      const wheelColors = generateWheelColors(manyActivities, { dualColorMode: true });
      const endTime = performance.now();

      // Should complete quickly
      expect(endTime - startTime).toBeLessThan(50);

      // Should generate 10 activities (system limits to 10)
      expect(wheelColors).toHaveLength(10);

      // Should generate unique extremity colors from fixed palette
      const extremityColors = wheelColors.map(c => c.extremityColor);
      const uniqueColors = new Set(extremityColors);
      expect(uniqueColors.size).toBe(10); // All 10 should be unique from fixed palette

      // All should have same center color
      wheelColors.forEach(colorInfo => {
        expect(colorInfo.centerColor).toBe('#E74C3C');
      });
    });
  });

  describe('Inner Wheel Physics Integration', () => {
    it('should configure inner wheel for proper ball collision', () => {
      const config = DEFAULT_WHEEL_CONFIG;

      // Calculate expected nail spacing
      const ballDiameter = config.ballRadius * 2;
      const expectedSpacing = ballDiameter * 1.5;
      const circumference = 2 * Math.PI * config.innerWheelRadius;
      const maxPossibleNails = Math.floor(circumference / expectedSpacing);

      // Inner nail count should not exceed what fits with proper spacing
      expect(config.innerNailCount).toBeLessThanOrEqual(maxPossibleNails);

      // Ball should start close enough to inner wheel to collide
      const ballToInnerWheelDistance = config.innerWheelRadius - config.ballStartOffset;
      expect(ballToInnerWheelDistance).toBeGreaterThan(0);
      expect(ballToInnerWheelDistance).toBeLessThan(config.ballRadius * 3); // Close enough for collision
    });

    it('should ensure inner nails are properly spaced for ball interaction', () => {
      const config = DEFAULT_WHEEL_CONFIG;
      const ballDiameter = config.ballRadius * 2; // 16px
      const requiredSpacing = ballDiameter * 1.5; // 24px

      // Calculate actual spacing with current nail count
      const circumference = 2 * Math.PI * config.innerWheelRadius;
      const actualSpacing = circumference / config.innerNailCount;

      // Actual spacing should be at least the required spacing
      expect(actualSpacing).toBeGreaterThanOrEqual(requiredSpacing);

      // Should have doubled the nails (more than 8)
      expect(config.innerNailCount).toBeGreaterThan(8);
    });
  });
});
