import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { html, fixture, expect as wcExpect } from '@open-wc/testing';

describe('App Shell Component Robustness', () => {
  let element: any;
  let mockWebSocket: any;
  let mockFetch: any;

  beforeEach(async () => {
    // Mock WebSocket
    mockWebSocket = {
      send: vi.fn(),
      close: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      readyState: 1 // WebSocket.OPEN
    };

    // Properly mock WebSocket constructor
    const MockWebSocket = vi.fn(() => mockWebSocket) as any;
    MockWebSocket.CONNECTING = 0;
    MockWebSocket.OPEN = 1;
    MockWebSocket.CLOSING = 2;
    MockWebSocket.CLOSED = 3;
    global.WebSocket = MockWebSocket;

    // Mock fetch
    mockFetch = vi.fn();
    global.fetch = mockFetch;

    // Mock localStorage
    const localStorageMock = {
      getItem: vi.fn(() => null),
      setItem: vi.fn(),
      removeItem: vi.fn(),
      clear: vi.fn()
    };
    Object.defineProperty(window, 'localStorage', { value: localStorageMock });

    // Mock console to reduce noise
    vi.spyOn(console, 'log').mockImplementation(() => {});
    vi.spyOn(console, 'error').mockImplementation(() => {});
    vi.spyOn(console, 'warn').mockImplementation(() => {});

    // Import and create the app-shell element
    await import('../src/components/app-shell.js');
    element = await fixture(html`<app-shell></app-shell>`);
  });

  afterEach(() => {
    vi.restoreAllMocks();
    if (element) {
      element.remove();
    }
  });

  describe('Initialization Robustness', () => {
    it('should initialize without throwing errors', () => {
      expect(element).toBeDefined();
      expect(element.tagName.toLowerCase()).toBe('app-shell');
    });

    it('should handle missing configuration gracefully', async () => {
      // Mock missing config
      (window.localStorage.getItem as any).mockReturnValue(null);
      
      const newElement = await fixture(html`<app-shell></app-shell>`);
      
      // Should initialize with defaults
      expect(newElement).toBeDefined();
    });

    it('should handle corrupted localStorage data', async () => {
      // Mock corrupted data
      (window.localStorage.getItem as any).mockReturnValue('invalid-json');
      
      const newElement = await fixture(html`<app-shell></app-shell>`);
      
      // Should handle gracefully
      expect(newElement).toBeDefined();
    });
  });

  describe('WebSocket Integration Robustness', () => {
    it('should handle WebSocket connection failures', () => {
      // Simulate connection failure
      const errorHandler = mockWebSocket.addEventListener.mock.calls.find(
        (call: any[]) => call[0] === 'error'
      )?.[1];
      
      expect(() => {
        errorHandler?.(new Error('WebSocket connection failed'));
      }).not.toThrow();
    });

    it('should handle malformed WebSocket messages', () => {
      // Get message handler
      const messageHandler = mockWebSocket.addEventListener.mock.calls.find(
        (call: any[]) => call[0] === 'message'
      )?.[1];
      
      const malformedMessages = [
        { data: 'invalid-json' },
        { data: '{"type": null}' },
        { data: '{"incomplete":' },
        { data: '' },
        { data: null }
      ];
      
      malformedMessages.forEach(msg => {
        expect(() => messageHandler?.(msg)).not.toThrow();
      });
    });

    it('should handle WebSocket reconnection scenarios', () => {
      // Simulate disconnect and reconnect
      const closeHandler = mockWebSocket.addEventListener.mock.calls.find(
        (call: any[]) => call[0] === 'close'
      )?.[1];

      const openHandler = mockWebSocket.addEventListener.mock.calls.find(
        (call: any[]) => call[0] === 'open'
      )?.[1];
      
      expect(() => {
        closeHandler?.({ code: 1006, reason: 'Connection lost' });
        openHandler?.({});
      }).not.toThrow();
    });
  });

  describe('Wheel Data Processing Robustness', () => {
    it('should handle empty wheel data', () => {
      const emptyWheelData = {
        items: [],
        wheelId: 'empty-wheel',
        createdAt: new Date().toISOString()
      };
      
      expect(() => {
        element.wheelData = emptyWheelData;
      }).not.toThrow();
    });

    it('should handle malformed wheel data', () => {
      const malformedData = [
        null,
        undefined,
        { items: null },
        { items: [{ id: null }] },
        { items: [{ name: '', percentage: -1 }] },
        { wheelId: null, items: [] }
      ];
      
      malformedData.forEach(data => {
        expect(() => {
          element.wheelData = data;
        }).not.toThrow();
      });
    });

    it('should handle wheel data with missing required fields', () => {
      const incompleteData = {
        items: [
          { id: 'item1' }, // missing name, percentage
          { name: 'Item 2' }, // missing id, percentage
          { percentage: 25 } // missing id, name
        ]
      };
      
      expect(() => {
        element.wheelData = incompleteData;
      }).not.toThrow();
    });

    it('should handle wheel data with invalid percentages', () => {
      const invalidPercentageData = {
        items: [
          { id: 'item1', name: 'Item 1', percentage: -10 },
          { id: 'item2', name: 'Item 2', percentage: 150 },
          { id: 'item3', name: 'Item 3', percentage: NaN },
          { id: 'item4', name: 'Item 4', percentage: Infinity }
        ],
        wheelId: 'invalid-percentages',
        createdAt: new Date().toISOString()
      };
      
      expect(() => {
        element.wheelData = invalidPercentageData;
      }).not.toThrow();
    });
  });

  describe('Error Handling Robustness', () => {
    it('should handle API failures gracefully', async () => {
      // Mock API failure
      mockFetch.mockRejectedValue(new Error('API Error'));
      
      // Trigger an API call
      expect(() => {
        element.dispatchEvent(new CustomEvent('wheel-generation-request', {
          detail: { userInput: 'test input' }
        }));
      }).not.toThrow();
    });

    it('should handle network timeouts', async () => {
      // Mock timeout
      mockFetch.mockImplementation(() => 
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Timeout')), 100)
        )
      );
      
      expect(() => {
        element.dispatchEvent(new CustomEvent('wheel-generation-request', {
          detail: { userInput: 'test input' }
        }));
      }).not.toThrow();
    });

    it('should handle DOM manipulation errors', () => {
      // Mock DOM error
      const originalQuerySelector = element.querySelector;
      element.querySelector = vi.fn(() => {
        throw new Error('DOM Error');
      });
      
      expect(() => {
        element.requestUpdate();
      }).not.toThrow();
      
      // Restore
      element.querySelector = originalQuerySelector;
    });
  });

  describe('State Management Robustness', () => {
    it('should handle rapid state changes', () => {
      const states = [
        'idle',
        'loading',
        'processing',
        'complete',
        'error'
      ];
      
      // Rapidly change states
      states.forEach(state => {
        expect(() => {
          element.currentState = state;
          element.requestUpdate();
        }).not.toThrow();
      });
    });

    it('should handle concurrent state updates', async () => {
      // Simulate concurrent updates
      const updates = Array.from({ length: 10 }, (_, i) => 
        Promise.resolve().then(() => {
          element.currentState = `state-${i}`;
          element.requestUpdate();
        })
      );
      
      await expect(Promise.all(updates)).resolves.toBeDefined();
    });

    it('should maintain state consistency during errors', () => {
      // Trigger an error and ensure it doesn't crash the component
      expect(() => {
        element.dispatchEvent(new CustomEvent('error', {
          detail: new Error('Test error')
        }));
      }).not.toThrow();

      // Component should remain functional after error
      expect(element.tagName.toLowerCase()).toBe('app-shell');
    });
  });

  describe('Memory Management', () => {
    it('should clean up event listeners on disconnect', () => {
      const removeEventListenerSpy = vi.spyOn(window, 'removeEventListener');
      
      // Disconnect the element
      element.disconnectedCallback?.();
      
      // Should clean up listeners
      expect(removeEventListenerSpy).toHaveBeenCalled();
    });

    it('should handle large wheel datasets', () => {
      // Create large dataset
      const largeWheelData = {
        items: Array.from({ length: 1000 }, (_, i) => ({
          id: `item-${i}`,
          name: `Item ${i}`,
          percentage: 0.1,
          domain: 'test'
        })),
        wheelId: 'large-wheel',
        createdAt: new Date().toISOString()
      };
      
      expect(() => {
        element.wheelData = largeWheelData;
      }).not.toThrow();
    });

    it('should handle memory pressure scenarios', () => {
      // Simulate memory pressure by creating many elements
      const elements = Array.from({ length: 100 }, () => 
        document.createElement('div')
      );
      
      elements.forEach(el => document.body.appendChild(el));
      
      expect(() => {
        element.requestUpdate();
      }).not.toThrow();
      
      // Cleanup
      elements.forEach(el => el.remove());
    });
  });

  describe('Performance Robustness', () => {
    it('should handle frequent updates efficiently', () => {
      const startTime = performance.now();
      
      // Perform many updates
      for (let i = 0; i < 100; i++) {
        element.requestUpdate();
      }
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // Should complete within reasonable time (1 second)
      expect(duration).toBeLessThan(1000);
    });

    it('should throttle expensive operations', () => {
      const operationSpy = vi.fn();
      element.expensiveOperation = operationSpy;
      
      // Trigger many operations rapidly
      for (let i = 0; i < 50; i++) {
        element.dispatchEvent(new CustomEvent('expensive-operation'));
      }
      
      // Should be throttled (not called 50 times)
      expect(operationSpy.mock.calls.length).toBeLessThan(50);
    });
  });
});
