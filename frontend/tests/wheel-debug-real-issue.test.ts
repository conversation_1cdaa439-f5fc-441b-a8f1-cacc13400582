/**
 * Debug the Real Wheel Color Issue
 * 
 * Based on the latest console logs, I need to understand why generateWheelColors()
 * is being called but the activityId doesn't match the segment IDs.
 */

import { describe, it, expect } from 'vitest';
import { generateWheelColors } from '../src/services/domainColorService.js';

// Real data from the latest console logs (item_248_*)
const REAL_CURRENT_ACTIVITIES = [
  {
    id: "item_248_1_86",
    name: "Pomodoro Focus Session (Tailored for 125min)",
    percentage: 16.666666666666668,
    domain: "productive_practical"
  },
  {
    id: "item_248_2_155", 
    name: "Advanced Urban Parkour (Tailored for 125min)",
    percentage: 16.666666666666668,
    domain: "general"
  },
  {
    id: "item_248_3_124",
    name: "App-Guided Movement Session (Tailored for 125min)", 
    percentage: 16.666666666666668,
    domain: "productive_practical"
  },
  {
    id: "item_248_4_84",
    name: "Deliberate Practice Session (Tailored for 125min)",
    percentage: 16.666666666666668,
    domain: "productive_practical"
  },
  {
    id: "item_248_5_64",
    name: "Emotional Anchoring Technique (Tailored for 125min)",
    percentage: 16.666666666666668,
    domain: "emotional"
  },
  {
    id: "item_248_6_121",
    name: "Intentional Break Transition (Tailored for 125min)",
    percentage: 16.666666666666668,
    domain: "productive_practical"
  }
];

// Real segment IDs from console logs (no -seg- suffix)
const REAL_SEGMENT_IDS = [
  'item_248_1_86',
  'item_248_2_155', 
  'item_248_3_124',
  'item_248_4_84',
  'item_248_5_64',
  'item_248_6_121'
];

describe('Debug Real Wheel Color Issue', () => {

  describe('🔍 Investigate generateWheelColors() Output', () => {
    it('should show what generateWheelColors() actually returns', () => {
      const wheelColors = generateWheelColors(REAL_CURRENT_ACTIVITIES, { dualColorMode: true });
      
      console.log('=== DEBUGGING generateWheelColors() OUTPUT ===');
      console.log(`Generated ${wheelColors.length} color entries`);
      
      wheelColors.forEach((colorInfo, index) => {
        console.log(`Color ${index}:`);
        console.log(`  - activityId: "${colorInfo.activityId}"`);
        console.log(`  - domain: "${colorInfo.domain}"`);
        console.log(`  - centerColor: "${colorInfo.centerColor}"`);
        console.log(`  - extremityColor: "${colorInfo.extremityColor}"`);
      });
      
      console.log('=== DEBUGGING SEGMENT IDs ===');
      REAL_SEGMENT_IDS.forEach((segmentId, index) => {
        console.log(`Segment ${index}: "${segmentId}"`);
      });
      
      console.log('=== DEBUGGING ID MATCHING ===');
      REAL_SEGMENT_IDS.forEach((segmentId, index) => {
        const colorInfo = wheelColors.find(color => color.activityId === segmentId);
        console.log(`Segment "${segmentId}" -> Color found: ${!!colorInfo}`);
        if (colorInfo) {
          console.log(`  ✅ Match: ${colorInfo.activityId}`);
        } else {
          console.log(`  ❌ No match found`);
          console.log(`  Available activityIds: ${wheelColors.map(c => c.activityId).join(', ')}`);
        }
      });
      
      // This test should reveal the real issue
      const allSegmentsHaveColors = REAL_SEGMENT_IDS.every(segmentId => 
        wheelColors.some(color => color.activityId === segmentId)
      );
      
      expect(allSegmentsHaveColors).toBe(true);
    });
  });

  describe('🔍 Investigate Activity ID Format', () => {
    it('should check if activity IDs are being modified', () => {
      console.log('=== DEBUGGING ACTIVITY ID PROCESSING ===');
      
      REAL_CURRENT_ACTIVITIES.forEach((activity, index) => {
        console.log(`Activity ${index}:`);
        console.log(`  - Original ID: "${activity.id}"`);
        console.log(`  - Name: "${activity.name}"`);
        console.log(`  - Domain: "${activity.domain}"`);
        
        // Check if the ID gets modified during processing
        const expectedSegmentId = activity.id;
        const actualSegmentId = REAL_SEGMENT_IDS[index];
        
        console.log(`  - Expected segment ID: "${expectedSegmentId}"`);
        console.log(`  - Actual segment ID: "${actualSegmentId}"`);
        console.log(`  - IDs match: ${expectedSegmentId === actualSegmentId}`);
      });
      
      // All activity IDs should match their corresponding segment IDs
      const allIdsMatch = REAL_CURRENT_ACTIVITIES.every((activity, index) => 
        activity.id === REAL_SEGMENT_IDS[index]
      );
      
      expect(allIdsMatch).toBe(true);
    });
  });

  describe('🔍 Investigate Color Generation Process', () => {
    it('should simulate the exact app-shell.ts process', () => {
      console.log('=== SIMULATING APP-SHELL.TS PROCESS ===');
      
      // Step 1: Generate colors (as done in app-shell.ts)
      const wheelColors = generateWheelColors(REAL_CURRENT_ACTIVITIES, { dualColorMode: true });
      console.log(`Step 1: Generated ${wheelColors.length} colors`);
      
      // Step 2: Simulate segments (as they appear in console logs)
      const mockSegments = REAL_SEGMENT_IDS.map(id => ({ id, domain: 'test' }));
      console.log(`Step 2: Created ${mockSegments.length} mock segments`);
      
      // Step 3: Apply the app-shell.ts mapping logic
      const mappingResults = mockSegments.map((segment, index) => {
        // This is the exact logic from app-shell.ts
        const activityId = segment.id.includes('-seg-') ? segment.id.split('-seg-')[0] : segment.id;
        const colorInfo = wheelColors.find((color: any) => color.activityId === activityId);
        
        console.log(`Segment ${index}: "${segment.id}"`);
        console.log(`  - Extracted activityId: "${activityId}"`);
        console.log(`  - Color found: ${!!colorInfo}`);
        
        if (!colorInfo) {
          console.log(`  ❌ WOULD SHOW: "Color info not found for segment ${segment.id} (activity: ${activityId})"`);
        } else {
          console.log(`  ✅ Color found: outer=${colorInfo.extremityColor}, inner=${colorInfo.centerColor}`);
        }
        
        return {
          segmentId: segment.id,
          activityId,
          found: !!colorInfo,
          colorInfo
        };
      });
      
      // Count failures
      const failureCount = mappingResults.filter(r => !r.found).length;
      console.log(`=== RESULT: ${failureCount} out of ${mappingResults.length} segments would fail ===`);
      
      // This should be 0 if the system is working
      expect(failureCount).toBe(0);
    });
  });

  describe('🔍 Check for Data Type Issues', () => {
    it('should check if there are data type mismatches', () => {
      const wheelColors = generateWheelColors(REAL_CURRENT_ACTIVITIES, { dualColorMode: true });
      
      console.log('=== CHECKING DATA TYPES ===');
      
      wheelColors.forEach((colorInfo, index) => {
        console.log(`Color ${index}:`);
        console.log(`  - activityId type: ${typeof colorInfo.activityId}`);
        console.log(`  - activityId value: "${colorInfo.activityId}"`);
        console.log(`  - activityId length: ${colorInfo.activityId?.length || 'undefined'}`);
      });
      
      REAL_SEGMENT_IDS.forEach((segmentId, index) => {
        console.log(`Segment ${index}:`);
        console.log(`  - segmentId type: ${typeof segmentId}`);
        console.log(`  - segmentId value: "${segmentId}"`);
        console.log(`  - segmentId length: ${segmentId.length}`);
      });
      
      // Check for exact string matching
      const firstColorActivityId = wheelColors[0]?.activityId;
      const firstSegmentId = REAL_SEGMENT_IDS[0];
      
      console.log('=== EXACT COMPARISON ===');
      console.log(`First color activityId: "${firstColorActivityId}"`);
      console.log(`First segment ID: "${firstSegmentId}"`);
      console.log(`Exact match: ${firstColorActivityId === firstSegmentId}`);
      console.log(`Loose match: ${firstColorActivityId == firstSegmentId}`);
      
      // This should pass if data types are correct
      expect(typeof firstColorActivityId).toBe('string');
      expect(typeof firstSegmentId).toBe('string');
    });
  });
});
