import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { 
  generateWheelColors, 
  applyColorsToWheel, 
  getDomainColor,
  getDistinctColorsForSameDomain 
} from '../src/services/domainColorService.js';

describe('Domain Color Service Integration Tests', () => {
  beforeEach(() => {
    // Mock console to reduce noise
    vi.spyOn(console, 'log').mockImplementation(() => {});
    vi.spyOn(console, 'error').mockImplementation(() => {});
    vi.spyOn(console, 'warn').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Color Generation Robustness', () => {
    it('should generate colors for empty activity list', () => {
      const result = generateWheelColors([]);
      
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBe(0);
    });

    it('should handle activities with missing domains', () => {
      const activities = [
        { id: 'activity1', name: 'Activity 1' }, // missing domain
        { id: 'activity2', name: 'Activity 2', domain: undefined },
        { id: 'activity3', name: 'Activity 3', domain: undefined },
        { id: 'activity4', name: 'Activity 4', domain: '' }
      ];

      const result = generateWheelColors(activities as any);
      
      expect(result).toBeDefined();
      expect(result.length).toBe(4);
      
      // Should assign fallback colors
      result.forEach(colorInfo => {
        expect(colorInfo.color).toBeDefined();
        expect(colorInfo.color).toMatch(/^#[0-9A-Fa-f]{6}$/);
      });
    });

    it('should handle activities with invalid percentages', () => {
      const activities = [
        { id: 'activity1', name: 'Activity 1', domain: 'health', percentage: -10 },
        { id: 'activity2', name: 'Activity 2', domain: 'work', percentage: 150 },
        { id: 'activity3', name: 'Activity 3', domain: 'social', percentage: NaN },
        { id: 'activity4', name: 'Activity 4', domain: 'learning', percentage: Infinity }
      ];

      // Main test: should not crash with invalid percentages
      expect(() => {
        const result = generateWheelColors(activities);
        expect(result).toBeDefined();
        expect(result.length).toBeGreaterThan(0);
      }).not.toThrow();
    });

    it('should generate distinct colors for same domain activities', () => {
      const activities = [
        { id: 'health1', name: 'Exercise', domain: 'health', percentage: 25 },
        { id: 'health2', name: 'Meditation', domain: 'health', percentage: 25 },
        { id: 'health3', name: 'Sleep', domain: 'health', percentage: 25 },
        { id: 'health4', name: 'Nutrition', domain: 'health', percentage: 25 }
      ];
      
      const result = generateWheelColors(activities);
      
      expect(result).toBeDefined();
      expect(result.length).toBe(4);
      
      // Colors should be distinct even for same domain
      const colors = result.map(r => r.color);
      const uniqueColors = new Set(colors);
      expect(uniqueColors.size).toBeGreaterThan(1);
    });

    it('should handle dual color mode correctly', () => {
      const activities = [
        { id: 'activity1', name: 'Activity 1', domain: 'health', percentage: 50 },
        { id: 'activity2', name: 'Activity 2', domain: 'work', percentage: 50 }
      ];
      
      const result = generateWheelColors(activities, { dualColorMode: true });
      
      expect(result).toBeDefined();
      expect(result.length).toBe(2);
      
      // Should have both center and extremity colors in dual mode
      result.forEach(colorInfo => {
        expect(colorInfo.centerColor).toBeDefined();
        expect(colorInfo.extremityColor).toBeDefined();
        expect(colorInfo.centerColor).toMatch(/^#[0-9A-Fa-f]{6}$/);
        expect(colorInfo.extremityColor).toMatch(/^#[0-9A-Fa-f]{6}$/);
      });
    });

    it('should handle large numbers of activities', () => {
      const activities = Array.from({ length: 100 }, (_, i) => ({
        id: `activity${i}`,
        name: `Activity ${i}`,
        domain: `domain${i % 10}`, // 10 different domains
        percentage: 1
      }));
      
      const startTime = performance.now();
      const result = generateWheelColors(activities);
      const endTime = performance.now();
      
      expect(result).toBeDefined();
      expect(result.length).toBeGreaterThan(0); // May not return all 100 due to domain grouping
      
      // Should complete within reasonable time (500ms)
      expect(endTime - startTime).toBeLessThan(500);
      
      // All should have valid colors
      result.forEach(colorInfo => {
        expect(colorInfo.color).toMatch(/^#[0-9A-Fa-f]{6}$/);
      });
    });
  });

  describe('Wheel Color Application Robustness', () => {
    it('should handle wheel data with missing items', () => {
      const wheelData = {
        name: 'Test Wheel',
        wheelId: 'test-wheel',
        createdAt: new Date().toISOString()
        // missing items
      };
      
      const result = applyColorsToWheel(wheelData);
      
      expect(result).toBeDefined();
      // The service may or may not add items property - test that it doesn't crash
      expect(() => result.items).not.toThrow();
    });

    it('should handle wheel data with null items', () => {
      const wheelData = {
        name: 'Test Wheel',
        wheelId: 'test-wheel',
        createdAt: new Date().toISOString(),
        items: null
      };
      
      const result = applyColorsToWheel(wheelData as any);
      
      expect(result).toBeDefined();
      expect(result.items).toBeDefined();
    });

    it('should preserve existing colors when appropriate', () => {
      const wheelData = {
        name: 'Test Wheel',
        wheelId: 'test-wheel',
        createdAt: new Date().toISOString(),
        items: [
          { id: 'item1', name: 'Item 1', domain: 'health', color: '#FF0000' },
          { id: 'item2', name: 'Item 2', domain: 'work' } // no existing color
        ]
      };
      
      const result = applyColorsToWheel(wheelData);
      
      expect(result.items?.[0]?.color).toBe('#FF0000'); // preserved
      expect(result.items?.[1]?.color).toBeDefined(); // assigned
      expect(result.items?.[1]?.color).toMatch(/^#[0-9A-Fa-f]{6}$/);
    });

    it('should handle items with malformed color values', () => {
      const wheelData = {
        name: 'Test Wheel',
        wheelId: 'test-wheel',
        createdAt: new Date().toISOString(),
        items: [
          { id: 'item1', name: 'Item 1', domain: 'health', color: 'invalid-color' },
          { id: 'item2', name: 'Item 2', domain: 'work', color: '#ZZZ' },
          { id: 'item3', name: 'Item 3', domain: 'social', color: undefined }
        ]
      };
      
      const result = applyColorsToWheel(wheelData as any);
      
      // Should handle malformed colors gracefully (may preserve or fix them)
      if (result.items) {
        result.items.forEach(item => {
          expect(item.color).toBeDefined();
          // Color may be preserved as-is or corrected - test that it exists
          expect(typeof item.color).toBe('string');
        });
      }
    });
  });

  describe('Domain Color Consistency', () => {
    it('should return consistent colors for same domain', () => {
      const domain = 'health';
      
      const color1 = getDomainColor(domain);
      const color2 = getDomainColor(domain);
      const color3 = getDomainColor(domain);
      
      expect(color1).toBe(color2);
      expect(color2).toBe(color3);
      expect(color1).toMatch(/^#[0-9A-Fa-f]{6}$/);
    });

    it('should handle unknown domains gracefully', () => {
      const unknownDomains = [
        'unknown-domain',
        'non-existent',
        '',
        null,
        undefined
      ];
      
      unknownDomains.forEach(domain => {
        const color = getDomainColor(domain as any);
        expect(color).toBeDefined();
        expect(color).toMatch(/^#[0-9A-Fa-f]{6}$/);
      });
    });

    it('should generate distinct colors for same domain when requested', () => {
      const domain = 'health';
      const count = 5;
      
      const colors = getDistinctColorsForSameDomain(domain, count);
      
      expect(colors).toBeDefined();
      expect(colors.length).toBe(count);
      
      // All should be valid hex colors
      colors.forEach(color => {
        expect(color).toMatch(/^#[0-9A-Fa-f]{6}$/);
      });
      
      // Should be distinct
      const uniqueColors = new Set(colors);
      expect(uniqueColors.size).toBe(count);
    });

    it('should handle edge cases in distinct color generation', () => {
      const domain = 'test';
      
      // Test edge cases
      const edgeCases = [0, 1, 100, -1];
      
      edgeCases.forEach(count => {
        expect(() => {
          const colors = getDistinctColorsForSameDomain(domain, count);
          if (count > 0) {
            expect(colors.length).toBeGreaterThanOrEqual(0);
          }
        }).not.toThrow();
      });
    });
  });

  describe('Color Integration with Wheel Components', () => {
    it('should maintain color consistency across wheel updates', () => {
      const initialActivities = [
        { id: 'activity1', name: 'Activity 1', domain: 'health', percentage: 50 },
        { id: 'activity2', name: 'Activity 2', domain: 'work', percentage: 50 }
      ];
      
      const initialColors = generateWheelColors(initialActivities);
      
      // Update activities (add one)
      const updatedActivities = [
        ...initialActivities,
        { id: 'activity3', name: 'Activity 3', domain: 'social', percentage: 33.33 }
      ];
      
      const updatedColors = generateWheelColors(updatedActivities);
      
      // Original activities should maintain their colors
      expect(updatedColors[0].color).toBe(initialColors[0].color);
      expect(updatedColors[1].color).toBe(initialColors[1].color);
      
      // New activity should have a new color
      expect(updatedColors[2].color).toBeDefined();
    });

    it('should handle color conflicts gracefully', () => {
      // Create scenario where color conflicts might occur
      const activities = Array.from({ length: 20 }, (_, i) => ({
        id: `activity${i}`,
        name: `Activity ${i}`,
        domain: 'same-domain', // All same domain
        percentage: 5
      }));
      
      const result = generateWheelColors(activities);
      
      expect(result.length).toBeGreaterThan(0); // May group by domain, so less than 20
      
      // Should generate distinct colors even for same domain
      const colors = result.map(r => r.color);
      const uniqueColors = new Set(colors);
      expect(uniqueColors.size).toBeGreaterThan(1);
    });

    it('should handle rapid color updates', () => {
      const activities = [
        { id: 'activity1', name: 'Activity 1', domain: 'health', percentage: 100 }
      ];
      
      // Rapidly generate colors multiple times
      const results = [];
      for (let i = 0; i < 50; i++) {
        results.push(generateWheelColors(activities));
      }
      
      // Should be consistent
      const firstColor = results[0][0].color;
      results.forEach(result => {
        expect(result[0].color).toBe(firstColor);
      });
    });
  });

  describe('Performance and Memory', () => {
    it('should not leak memory with repeated color generation', () => {
      const activities = [
        { id: 'activity1', name: 'Activity 1', domain: 'health', percentage: 50 },
        { id: 'activity2', name: 'Activity 2', domain: 'work', percentage: 50 }
      ];
      
      // Generate colors many times
      for (let i = 0; i < 1000; i++) {
        generateWheelColors(activities);
      }
      
      // Should not crash or consume excessive memory
      expect(true).toBe(true);
    });

    it('should handle concurrent color generation', async () => {
      const activities = [
        { id: 'activity1', name: 'Activity 1', domain: 'health', percentage: 100 }
      ];
      
      // Generate colors concurrently
      const promises = Array.from({ length: 20 }, () => 
        Promise.resolve().then(() => generateWheelColors(activities))
      );
      
      const results = await Promise.all(promises);
      
      // All should succeed and be consistent
      expect(results.length).toBe(20);
      const firstColor = results[0][0].color;
      results.forEach(result => {
        expect(result[0].color).toBe(firstColor);
      });
    });
  });
});
