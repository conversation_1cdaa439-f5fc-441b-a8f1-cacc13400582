# Goali Frontend Production Mode Configuration
# Configured for Digital Ocean App Platform deployment

# Application Mode
VITE_APP_MODE=production

# Digital Ocean App URLs
VITE_WS_URL=wss://monkfish-app-jvgae.ondigitalocean.app/ws/game/
VITE_API_BASE_URL=https://monkfish-app-jvgae.ondigitalocean.app

# Debug Configuration (disabled in production)
VITE_DEBUG_ENABLED=false
VITE_DEBUG_SHOW_PERFORMANCE=false
VITE_DEBUG_SHOW_NETWORK_LOGS=false
VITE_DEBUG_SHOW_STATE_INSPECTOR=false
VITE_DEBUG_ALLOW_USER_SELECTION=false
VITE_DEBUG_ALLOW_LLM_CONFIG_SELECTION=false
VITE_DEBUG_ALLOW_BACKEND_URL_CHANGE=false
VITE_DEBUG_MOCK_DATA_ENABLED=false

# Security Configuration (enabled for production)
VITE_SECURITY_REQUIRE_AUTH=true
VITE_SECURITY_TOKEN_VALIDATION=true
VITE_SECURITY_SESSION_TIMEOUT=1800000
# Force rebuild trigger

# Wheel Configuration
VITE_WHEEL_CACHE_ENABLED=false
VITE_SECURITY_ALLOWED_ORIGINS=https://monkfish-app-jvgae.ondigitalocean.app


# Logging Configuration
VITE_LOGGING_ENABLED=true
VITE_LOGGING_LEVEL=warn
VITE_LOGGING_ENDPOINT=https://monkfish-app-jvgae.ondigitalocean.app/api/logs

# Performance optimizations for production
VITE_BUILD_SOURCEMAP=false
VITE_BUILD_MINIFY=true
