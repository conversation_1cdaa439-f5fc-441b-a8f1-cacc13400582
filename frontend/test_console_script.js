// Console test script for wheel debugging
// Run this in the browser console to test the wheel

console.log('=== WHEEL DEBUG TEST STARTING ===');

// Find the wheel component
const wheel = document.querySelector('game-wheel');
if (!wheel) {
    console.error('No game-wheel component found!');
} else {
    console.log('Found wheel component:', wheel);

    // Check if wheel has data
    console.log('Wheel data:', wheel.wheelData);

    // Use the new getWheelState method
    const wheelState = wheel.getWheelState();
    console.log('Wheel state:', wheelState);

    // Try to access physics engine
    try {
        const physicsEngine = wheel.physicsEngine;
        if (physicsEngine) {
            console.log('Physics engine found:', physicsEngine);
            console.log('Physics running:', physicsEngine.isRunning);
            console.log('Ball body exists:', !!physicsEngine.ballBody);
            console.log('Nail bodies count:', physicsEngine.nailBodies?.length);
            console.log('Boundary bodies count:', physicsEngine.boundaryBodies?.length);

            if (physicsEngine.ballBody) {
                const pos = physicsEngine.ballBody.position;
                console.log('Ball position:', pos.x.toFixed(1), pos.y.toFixed(1));

                // Check if ball is within expected bounds
                const centerX = wheelState.config.centerX;
                const centerY = wheelState.config.centerY;
                const radius = wheelState.config.radius;
                const distance = Math.sqrt(Math.pow(pos.x - centerX, 2) + Math.pow(pos.y - centerY, 2));
                console.log(`Ball distance from center: ${distance.toFixed(1)} (max expected: ${radius + 50})`);

                if (distance > radius + 50) {
                    console.warn('Ball is outside expected bounds!');
                } else {
                    console.log('Ball position looks good');
                }
            }

            console.log('Wheel rotation:', physicsEngine.getWheelRotation?.()?.toFixed(3));
            console.log('Wheel velocity:', physicsEngine.getWheelVelocity?.()?.toFixed(3));
        } else {
            console.error('Physics engine not found!');
        }
    } catch (error) {
        console.error('Error accessing physics engine:', error);
    }

    // Try to access renderer
    try {
        const renderer = wheel.renderer;
        if (renderer) {
            console.log('Renderer found:', renderer);
            console.log('Renderer initialized:', renderer.isInitialized);

            // Check canvas size
            const app = renderer.getApp?.();
            if (app) {
                console.log('Canvas size:', app.canvas.width, 'x', app.canvas.height);
            }
        } else {
            console.error('Renderer not found!');
        }
    } catch (error) {
        console.error('Error accessing renderer:', error);
    }

    // Test spinning with enhanced ball tracking
    console.log('Testing wheel spin with ball movement tracking...');
    try {
        // Start ball tracking first
        wheel.startBallTracking();

        // Then spin
        wheel.spin();
        console.log('Spin method called successfully');

        // Monitor for ball movement detection
        let monitorCount = 0;
        let lastPosition = null;
        let movementDetected = false;

        const monitor = setInterval(() => {
            monitorCount++;
            if (monitorCount > 20) {
                clearInterval(monitor);
                console.log(`=== FINAL RESULT ===`);
                console.log(`Ball movement detected: ${movementDetected ? '✅ YES' : '❌ NO'}`);
                if (!movementDetected) {
                    console.error('🚨 CRITICAL: Ball is not moving! Physics issue detected.');
                }
                return;
            }

            if (wheel.physicsEngine?.ballBody) {
                const pos = wheel.physicsEngine.ballBody.position;
                const vel = wheel.physicsEngine.getBallVelocity();

                // Check for movement
                if (lastPosition) {
                    const movement = Math.sqrt(
                        Math.pow(pos.x - lastPosition.x, 2) +
                        Math.pow(pos.y - lastPosition.y, 2)
                    );
                    if (movement > 0.1) {
                        movementDetected = true;
                    }
                }

                console.log(`Monitor ${monitorCount}: Ball at (${pos.x.toFixed(1)}, ${pos.y.toFixed(1)}) speed: ${vel.magnitude.toFixed(3)} movement: ${movementDetected ? '✅' : '❌'}`);
                lastPosition = { x: pos.x, y: pos.y };
            }
        }, 250);

    } catch (error) {
        console.error('Error calling spin:', error);
    }
}

console.log('=== WHEEL DEBUG TEST COMPLETE ===');
