#!/usr/bin/env node

// Quick test to verify debug message handling fix
import WebSocket from 'ws';

class DebugTestSimulator {
  constructor() {
    this.colors = {
      reset: '\x1b[0m',
      gray: '\x1b[90m',
      green: '\x1b[32m',
      blue: '\x1b[34m'
    };
  }

  async start() {
    console.log('🧪 Testing debug message fix...');
    
    try {
      this.socket = new WebSocket('ws://localhost:8000/ws/game/');
      
      this.socket.on('open', () => {
        console.log('✅ Connected to backend');
        
        // Send a simple message to trigger debug messages
        const message = {
          type: 'chat_message',
          content: {
            message: 'Hello',
            user_profile_id: '2',
            timestamp: new Date().toISOString()
          }
        };
        
        this.socket.send(JSON.stringify(message));
      });

      this.socket.on('message', (data) => {
        try {
          const message = JSON.parse(data.toString());
          this.handleMessage(message);
        } catch (error) {
          console.error('❌ Failed to parse message:', error.message);
        }
      });

      this.socket.on('error', (error) => {
        console.error('❌ WebSocket error:', error.message);
      });

      this.socket.on('close', () => {
        console.log('🔌 Connection closed');
        process.exit(0);
      });

      // Auto-close after 10 seconds
      setTimeout(() => {
        console.log('⏰ Test timeout - closing connection');
        this.socket.close();
      }, 10000);

    } catch (error) {
      console.error('❌ Failed to start test:', error.message);
      process.exit(1);
    }
  }

  handleMessage(message) {
    console.log(`\n${this.colors.blue}📨 Received: ${message.type}${this.colors.reset}`);
    
    if (message.type === 'debug_info') {
      // Test the fixed debug logic
      let debugMessage = 'Unknown debug message';
      
      if (message.content && typeof message.content === 'object') {
        if (message.content.message) {
          debugMessage = message.content.message;
        } else if (message.content.source) {
          debugMessage = `${message.content.source}: ${message.content.level || 'info'}`;
        } else {
          debugMessage = JSON.stringify(message.content);
        }
      } else if (message.content && typeof message.content === 'string') {
        debugMessage = message.content;
      }
      
      console.log(`${this.colors.gray}🔧 Debug: ${debugMessage}${this.colors.reset}`);
      
      // Also test what the old logic would have produced
      const oldResult = message.content.message;
      console.log(`${this.colors.gray}🔧 Old logic would show: ${oldResult}${this.colors.reset}`);
      
    } else if (message.type === 'system_message') {
      console.log(`${this.colors.green}🔔 System: ${message.content}${this.colors.reset}`);
    } else if (message.type === 'chat_message') {
      console.log(`${this.colors.green}💬 Chat: ${message.content}${this.colors.reset}`);
    } else {
      console.log(`${this.colors.gray}📦 Other: ${message.type}${this.colors.reset}`);
    }
  }
}

// Run the test
const simulator = new DebugTestSimulator();
simulator.start().catch(console.error);
