#!/usr/bin/env node

/**
 * Playwright UI Issues Analyzer
 * 
 * Specifically designed to analyze and fix the reported UI issues:
 * 1. Unstable connection
 * 2. Impossibility to scroll in chat area
 * 3. Abnormally long time for mentor to answer simple questions
 */

const { chromium } = require('playwright');
const fs = require('fs');

class UIIssuesAnalyzer {
    constructor() {
        this.browser = null;
        this.context = null;
        this.page = null;
        this.issues = {
            connectionStability: {
                disconnections: 0,
                reconnections: 0,
                connectionErrors: [],
                timeline: []
            },
            chatScrolling: {
                scrollable: false,
                scrollHeight: 0,
                clientHeight: 0,
                scrollEvents: [],
                cssIssues: []
            },
            mentorResponseTime: {
                messages: [],
                averageResponseTime: 0,
                slowResponses: [],
                timeouts: []
            }
        };
        this.startTime = Date.now();
    }

    async initialize() {
        console.log('🔍 Initializing UI Issues Analyzer...');
        
        this.browser = await chromium.launch({
            headless: false,
            slowMo: 500,
            args: ['--disable-web-security']
        });

        this.context = await this.browser.newContext({
            viewport: { width: 1280, height: 720 }
        });

        this.page = await this.context.newPage();
        
        // Setup comprehensive logging
        this.page.on('console', msg => {
            const text = msg.text();
            const timestamp = Date.now() - this.startTime;
            
            console.log(`🖥️  [${msg.type()}] ${text}`);
            
            // Track connection-related messages
            if (text.includes('WebSocket') || text.includes('connection') || text.includes('Connected')) {
                this.issues.connectionStability.timeline.push({
                    timestamp,
                    type: msg.type(),
                    message: text
                });
            }
        });

        this.page.on('pageerror', error => {
            console.error('❌ Page Error:', error.message);
            this.issues.connectionStability.connectionErrors.push({
                timestamp: Date.now() - this.startTime,
                error: error.message
            });
        });

        await this.setupWebSocketMonitoring();
        console.log('✅ Analyzer initialized');
    }

    async setupWebSocketMonitoring() {
        console.log('🔌 Setting up WebSocket monitoring...');
        
        await this.page.routeWebSocket('**/ws/**', ws => {
            console.log(`🔗 WebSocket intercepted: ${ws.url()}`);
            
            const server = ws.connectToServer();
            let lastMessageTime = Date.now();
            
            // Monitor connection stability
            server.onMessage(message => {
                const now = Date.now();
                const responseTime = now - lastMessageTime;
                
                console.log(`📨 WebSocket ← Server (${responseTime}ms):`, message.substring(0, 100) + '...');
                
                try {
                    const parsed = JSON.parse(message);
                    if (parsed.type === 'ai_response') {
                        this.issues.mentorResponseTime.messages.push({
                            timestamp: now - this.startTime,
                            responseTime,
                            message: parsed
                        });
                        
                        if (responseTime > 10000) { // > 10 seconds
                            this.issues.mentorResponseTime.slowResponses.push({
                                timestamp: now - this.startTime,
                                responseTime,
                                message: parsed.content?.message || 'Unknown'
                            });
                        }
                    }
                } catch (e) {
                    // Not JSON, ignore
                }
            });

            ws.onMessage(message => {
                lastMessageTime = Date.now();
                console.log('📤 WebSocket → Server:', message.substring(0, 100) + '...');
                server.send(message);
            });

            server.onClose(() => {
                console.log('❌ WebSocket closed');
                this.issues.connectionStability.disconnections++;
            });
        });
    }

    async analyzeConnectionStability() {
        console.log('🔌 Analyzing connection stability...');
        
        // Navigate and wait for initial connection
        await this.page.goto('http://localhost:3001');
        await this.page.waitForLoadState('networkidle');
        
        // Wait for WebSocket connection
        await this.page.waitForTimeout(5000);
        
        // Check connection status
        const connectionStatus = await this.page.evaluate(() => {
            return {
                websocketExists: !!window.websocket,
                readyState: window.websocket?.readyState,
                url: window.websocket?.url,
                stateManager: !!window.stateManager,
                isConnected: window.stateManager?.getState()?.isConnected
            };
        });
        
        console.log('📊 Connection Status:', connectionStatus);
        
        // Monitor for 30 seconds to detect instability
        console.log('⏳ Monitoring connection stability for 30 seconds...');
        const startMonitoring = Date.now();
        
        while (Date.now() - startMonitoring < 30000) {
            const currentStatus = await this.page.evaluate(() => {
                return {
                    readyState: window.websocket?.readyState,
                    isConnected: window.stateManager?.getState()?.isConnected
                };
            });
            
            if (currentStatus.readyState !== 1) { // Not OPEN
                this.issues.connectionStability.disconnections++;
                console.log('⚠️  Connection instability detected');
            }
            
            await this.page.waitForTimeout(1000);
        }
        
        return this.issues.connectionStability;
    }

    async analyzeChatScrolling() {
        console.log('📜 Analyzing chat scrolling functionality...');
        
        // Find chat container
        const chatContainer = this.page.locator('.chat-container, .messages-container, [data-chat-container]').first();
        const chatExists = await chatContainer.count() > 0;
        
        if (!chatExists) {
            console.log('⚠️  Chat container not found');
            this.issues.chatScrolling.cssIssues.push('Chat container element not found');
            return this.issues.chatScrolling;
        }
        
        // Analyze scrolling properties
        const scrollInfo = await chatContainer.evaluate(element => {
            const styles = window.getComputedStyle(element);
            return {
                scrollHeight: element.scrollHeight,
                clientHeight: element.clientHeight,
                scrollTop: element.scrollTop,
                overflowY: styles.overflowY,
                overflowX: styles.overflowX,
                height: styles.height,
                maxHeight: styles.maxHeight,
                position: styles.position
            };
        });
        
        console.log('📊 Chat Scroll Info:', scrollInfo);
        
        this.issues.chatScrolling = {
            ...this.issues.chatScrolling,
            scrollable: scrollInfo.scrollHeight > scrollInfo.clientHeight,
            scrollHeight: scrollInfo.scrollHeight,
            clientHeight: scrollInfo.clientHeight,
            overflowY: scrollInfo.overflowY
        };
        
        // Test scrolling functionality
        if (this.issues.chatScrolling.scrollable) {
            try {
                await chatContainer.evaluate(element => {
                    element.scrollTop = element.scrollHeight;
                });
                
                const newScrollTop = await chatContainer.evaluate(element => element.scrollTop);
                console.log(`📜 Scroll test: scrolled to ${newScrollTop}`);
                
                if (newScrollTop === 0) {
                    this.issues.chatScrolling.cssIssues.push('Scrolling not working - scrollTop remains 0');
                }
            } catch (error) {
                console.error('❌ Scroll test failed:', error.message);
                this.issues.chatScrolling.cssIssues.push(`Scroll test error: ${error.message}`);
            }
        } else {
            this.issues.chatScrolling.cssIssues.push('Chat container not scrollable');
        }
        
        return this.issues.chatScrolling;
    }

    async testMentorResponseTime() {
        console.log('🤖 Testing mentor response time...');
        
        // First, enable debug panel to select user
        await this.page.keyboard.press('Control+Shift+D');
        await this.page.waitForTimeout(1000);
        
        // Try to select user 2 (phiphi)
        const userSelect = this.page.locator('select[name="user"]').first();
        const selectExists = await userSelect.count() > 0;
        
        if (selectExists) {
            await userSelect.selectOption('2');
            console.log('✅ Selected user 2 (phiphi)');
            await this.page.waitForTimeout(1000);
        } else {
            console.log('⚠️  Debug panel not accessible');
        }
        
        // Find chat input
        const chatInput = this.page.locator('input[type="text"], textarea').first();
        const inputExists = await chatInput.count() > 0;
        
        if (!inputExists) {
            console.log('❌ Chat input not found');
            return this.issues.mentorResponseTime;
        }
        
        // Check if input is enabled
        const isEnabled = await chatInput.evaluate(element => !element.disabled);
        console.log(`📝 Chat input enabled: ${isEnabled}`);
        
        if (!isEnabled) {
            console.log('❌ Chat input is disabled');
            this.issues.mentorResponseTime.timeouts.push({
                timestamp: Date.now() - this.startTime,
                reason: 'Chat input disabled'
            });
            return this.issues.mentorResponseTime;
        }
        
        // Test simple question response time
        const testMessage = "hey, do you recognize me ?";
        console.log(`💬 Sending test message: "${testMessage}"`);
        
        const messageStartTime = Date.now();
        
        await chatInput.fill(testMessage);
        await chatInput.press('Enter');
        
        // Wait for response (up to 60 seconds)
        console.log('⏳ Waiting for mentor response...');
        let responseReceived = false;
        let waitTime = 0;
        const maxWaitTime = 60000; // 60 seconds
        
        while (!responseReceived && waitTime < maxWaitTime) {
            await this.page.waitForTimeout(1000);
            waitTime += 1000;
            
            // Check for new messages
            const messageCount = await this.page.locator('.message, .chat-message, [data-message]').count();
            if (messageCount > 0) {
                responseReceived = true;
                const responseTime = Date.now() - messageStartTime;
                console.log(`✅ Response received in ${responseTime}ms`);
                
                this.issues.mentorResponseTime.messages.push({
                    timestamp: Date.now() - this.startTime,
                    responseTime,
                    question: testMessage
                });
                
                if (responseTime > 10000) {
                    this.issues.mentorResponseTime.slowResponses.push({
                        timestamp: Date.now() - this.startTime,
                        responseTime,
                        question: testMessage
                    });
                }
            }
        }
        
        if (!responseReceived) {
            console.log('❌ No response received within 60 seconds');
            this.issues.mentorResponseTime.timeouts.push({
                timestamp: Date.now() - this.startTime,
                question: testMessage,
                waitTime: maxWaitTime
            });
        }
        
        return this.issues.mentorResponseTime;
    }

    async generateDiagnosticReport() {
        const report = {
            timestamp: new Date().toISOString(),
            duration: Date.now() - this.startTime,
            issues: this.issues,
            recommendations: this.generateRecommendations()
        };

        const reportPath = `./test-results/ui-issues-analysis-${Date.now()}.json`;
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        
        console.log('\n🔍 UI ISSUES ANALYSIS REPORT');
        console.log('═'.repeat(50));
        console.log(`📁 Report saved to: ${reportPath}`);
        console.log(`⏱️  Analysis Duration: ${Math.round(report.duration / 1000)}s`);
        
        console.log('\n🔌 Connection Stability:');
        console.log(`  Disconnections: ${this.issues.connectionStability.disconnections}`);
        console.log(`  Connection Errors: ${this.issues.connectionStability.connectionErrors.length}`);
        
        console.log('\n📜 Chat Scrolling:');
        console.log(`  Scrollable: ${this.issues.chatScrolling.scrollable}`);
        console.log(`  CSS Issues: ${this.issues.chatScrolling.cssIssues.length}`);
        
        console.log('\n🤖 Mentor Response Time:');
        console.log(`  Messages Sent: ${this.issues.mentorResponseTime.messages.length}`);
        console.log(`  Slow Responses: ${this.issues.mentorResponseTime.slowResponses.length}`);
        console.log(`  Timeouts: ${this.issues.mentorResponseTime.timeouts.length}`);
        
        if (report.recommendations.length > 0) {
            console.log('\n💡 Recommendations:');
            report.recommendations.forEach((rec, i) => {
                console.log(`  ${i + 1}. ${rec}`);
            });
        }
        
        return report;
    }

    generateRecommendations() {
        const recommendations = [];
        
        if (this.issues.connectionStability.disconnections > 0) {
            recommendations.push('Fix WebSocket connection stability - investigate reconnection logic');
        }
        
        if (this.issues.chatScrolling.cssIssues.length > 0) {
            recommendations.push('Fix chat scrolling CSS - check overflow properties and container height');
        }
        
        if (this.issues.mentorResponseTime.slowResponses.length > 0) {
            recommendations.push('Optimize mentor response time - investigate backend processing delays');
        }
        
        if (this.issues.mentorResponseTime.timeouts.length > 0) {
            recommendations.push('Fix mentor response timeouts - check message routing and processing');
        }
        
        return recommendations;
    }

    async cleanup() {
        console.log('🧹 Cleaning up...');
        if (this.page) await this.page.close();
        if (this.context) await this.context.close();
        if (this.browser) await this.browser.close();
    }
}

// Main execution
async function main() {
    const analyzer = new UIIssuesAnalyzer();
    
    try {
        await analyzer.initialize();
        
        // Analyze each issue
        await analyzer.analyzeConnectionStability();
        await analyzer.analyzeChatScrolling();
        await analyzer.testMentorResponseTime();
        
        // Generate comprehensive report
        await analyzer.generateDiagnosticReport();
        
    } catch (error) {
        console.error('❌ Analysis failed:', error.message);
    } finally {
        await analyzer.cleanup();
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = UIIssuesAnalyzer;
