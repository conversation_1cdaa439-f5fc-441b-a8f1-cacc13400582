#!/usr/bin/env node

const net = require('net');
const crypto = require('crypto');

console.log('🚀 Testing raw WebSocket connection to /ws/connection-monitor/');

// Create WebSocket handshake
const key = crypto.randomBytes(16).toString('base64');
const handshake = [
    'GET /ws/connection-monitor/ HTTP/1.1',
    'Host: localhost:8000',
    'Upgrade: websocket',
    'Connection: Upgrade',
    `Sec-WebSocket-Key: ${key}`,
    'Sec-WebSocket-Version: 13',
    '',
    ''
].join('\r\n');

console.log('📤 Sending handshake:');
console.log(handshake);

const socket = net.createConnection(8000, 'localhost');

socket.on('connect', () => {
    console.log('✅ TCP connection established');
    socket.write(handshake);
});

socket.on('data', (data) => {
    console.log('📥 Received data:');
    console.log(data.toString());
    
    if (data.toString().includes('HTTP/1.1 101')) {
        console.log('✅ WebSocket handshake successful!');
    } else if (data.toString().includes('HTTP/1.1')) {
        console.log('❌ WebSocket handshake failed');
    }
    
    socket.end();
});

socket.on('error', (err) => {
    console.log(`❌ Socket error: ${err.message}`);
});

socket.on('close', () => {
    console.log('🔌 Connection closed');
});

// Timeout after 10 seconds
setTimeout(() => {
    console.log('⏰ Timeout - closing connection');
    socket.destroy();
}, 10000);
