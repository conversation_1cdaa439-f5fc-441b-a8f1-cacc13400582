#!/usr/bin/env node

/**
 * Comprehensive Wheel Domain Data Flow Test
 * 
 * Tests the complete data flow from frontend user preferences to backend wheel generation
 * to ensure domains are properly assigned and user preferences are transmitted correctly.
 * 
 * Usage: node test-wheel-domain-data-flow.cjs [port]
 * Example: node test-wheel-domain-data-flow.cjs 3001
 */

const { chromium } = require('playwright');

class WheelDomainDataFlowTester {
    constructor(port = 3001) {
        this.port = port;
        this.baseUrl = `http://localhost:${port}`;
        this.browser = null;
        this.page = null;
        this.testResults = {
            userPreferenceCapture: false,
            messageTransmission: false,
            wheelGeneration: false,
            domainAssignment: false,
            colorDifferentiation: false,
            overallSuccess: false
        };
        this.capturedMessages = [];
        this.wheelData = null;
    }

    async initialize() {
        console.log('🚀 Initializing Wheel Domain Data Flow Test...');
        
        this.browser = await chromium.launch({ 
            headless: false,
            slowMo: 100,
            args: ['--disable-web-security', '--disable-features=VizDisplayCompositor']
        });
        
        this.page = await this.browser.newPage();
        
        // Set up WebSocket message interception
        await this.setupWebSocketInterception();
        
        // Navigate to the application
        await this.page.goto(this.baseUrl);
        await this.page.waitForLoadState('networkidle');
        
        console.log('✅ Browser initialized and page loaded');
    }

    async setupWebSocketInterception() {
        // Intercept WebSocket messages to track data flow
        await this.page.addInitScript(() => {
            window.capturedWebSocketMessages = [];
            
            const originalWebSocket = window.WebSocket;
            window.WebSocket = function(url, protocols) {
                const ws = new originalWebSocket(url, protocols);
                
                const originalSend = ws.send;
                ws.send = function(data) {
                    try {
                        const parsedData = JSON.parse(data);
                        window.capturedWebSocketMessages.push({
                            type: 'outgoing',
                            timestamp: new Date().toISOString(),
                            data: parsedData
                        });
                        console.log('📤 WebSocket Outgoing:', parsedData);
                    } catch (e) {
                        console.log('📤 WebSocket Outgoing (raw):', data);
                    }
                    return originalSend.call(this, data);
                };
                
                ws.addEventListener('message', (event) => {
                    try {
                        const parsedData = JSON.parse(event.data);
                        window.capturedWebSocketMessages.push({
                            type: 'incoming',
                            timestamp: new Date().toISOString(),
                            data: parsedData
                        });
                        console.log('📥 WebSocket Incoming:', parsedData);
                    } catch (e) {
                        console.log('📥 WebSocket Incoming (raw):', event.data);
                    }
                });
                
                return ws;
            };
        });
    }

    async testUserPreferenceCapture() {
        console.log('\n🎯 Testing User Preference Capture...');
        
        try {
            // Wait for app to load
            await this.page.waitForSelector('app-shell', { timeout: 10000 });
            
            // Set specific energy level (75%)
            const energySlider = await this.page.locator('input[type="range"]').nth(1); // Energy level slider
            await energySlider.fill('75');
            
            // Set specific time available (45 minutes)
            const timeSlider = await this.page.locator('input[type="range"]').first(); // Time available slider
            await timeSlider.fill('45');
            
            // Verify the values are captured
            const energyValue = await this.page.evaluate(() => {
                const appShell = document.querySelector('app-shell');
                return appShell?.energyLevel;
            });
            
            const timeValue = await this.page.evaluate(() => {
                const appShell = document.querySelector('app-shell');
                return appShell?.timeAvailable;
            });
            
            console.log(`⚡ Energy Level captured: ${energyValue}%`);
            console.log(`🕒 Time Available captured: ${timeValue} (should be ~45)`);
            
            this.testResults.userPreferenceCapture = (energyValue === 75 && Math.abs(timeValue - 45) < 5);
            
            if (this.testResults.userPreferenceCapture) {
                console.log('✅ User preference capture: SUCCESS');
            } else {
                console.log('❌ User preference capture: FAILED');
            }
            
        } catch (error) {
            console.error('❌ Error testing user preference capture:', error);
            this.testResults.userPreferenceCapture = false;
        }
    }

    async testWheelGeneration() {
        console.log('\n🎡 Testing Wheel Generation with User Preferences...');
        
        try {
            // Clear captured messages
            await this.page.evaluate(() => {
                window.capturedWebSocketMessages = [];
            });
            
            // Click generate wheel button
            const generateButton = await this.page.locator('button:has-text("Generate")').first();
            await generateButton.click();
            
            console.log('🔄 Wheel generation initiated...');
            
            // Wait for wheel generation to complete (up to 60 seconds)
            let wheelGenerated = false;
            let attempts = 0;
            const maxAttempts = 60;
            
            while (!wheelGenerated && attempts < maxAttempts) {
                await this.page.waitForTimeout(1000);
                attempts++;
                
                // Check if wheel data is available
                const wheelData = await this.page.evaluate(() => {
                    const appShell = document.querySelector('app-shell');
                    return appShell?.wheelData;
                });
                
                if (wheelData && wheelData.segments && wheelData.segments.length > 0) {
                    wheelGenerated = true;
                    this.wheelData = wheelData;
                    console.log(`✅ Wheel generated after ${attempts} seconds with ${wheelData.segments.length} segments`);
                }
                
                if (attempts % 10 === 0) {
                    console.log(`⏳ Still waiting for wheel generation... (${attempts}s)`);
                }
            }
            
            this.testResults.wheelGeneration = wheelGenerated;
            
            if (!wheelGenerated) {
                console.log('❌ Wheel generation: TIMEOUT after 60 seconds');
            }
            
        } catch (error) {
            console.error('❌ Error testing wheel generation:', error);
            this.testResults.wheelGeneration = false;
        }
    }

    async testMessageTransmission() {
        console.log('\n📡 Testing Message Transmission...');
        
        try {
            // Get captured WebSocket messages
            const messages = await this.page.evaluate(() => {
                return window.capturedWebSocketMessages || [];
            });
            
            this.capturedMessages = messages;
            
            // Find wheel generation request message
            const wheelGenRequest = messages.find(msg => 
                msg.type === 'outgoing' && 
                msg.data.content && 
                (msg.data.content.energy_level !== undefined || msg.data.content.time_available !== undefined)
            );
            
            if (wheelGenRequest) {
                console.log('📤 Found wheel generation request with user preferences:');
                console.log(`   Energy Level: ${wheelGenRequest.data.content.energy_level}`);
                console.log(`   Time Available: ${wheelGenRequest.data.content.time_available}`);
                
                this.testResults.messageTransmission = (
                    wheelGenRequest.data.content.energy_level === 75 &&
                    wheelGenRequest.data.content.time_available === 45
                );
            } else {
                console.log('❌ No wheel generation request found with user preferences');
                this.testResults.messageTransmission = false;
            }
            
            if (this.testResults.messageTransmission) {
                console.log('✅ Message transmission: SUCCESS');
            } else {
                console.log('❌ Message transmission: FAILED');
            }
            
        } catch (error) {
            console.error('❌ Error testing message transmission:', error);
            this.testResults.messageTransmission = false;
        }
    }

    async testDomainAssignment() {
        console.log('\n🎯 Testing Domain Assignment...');
        
        try {
            if (!this.wheelData || !this.wheelData.segments) {
                console.log('❌ No wheel data available for domain testing');
                this.testResults.domainAssignment = false;
                return;
            }
            
            const segments = this.wheelData.segments;
            const domainCounts = {};
            const uniqueDomains = new Set();
            
            console.log('\n📊 Analyzing wheel segment domains:');
            
            segments.forEach((segment, index) => {
                const domain = segment.domain || 'unknown';
                domainCounts[domain] = (domainCounts[domain] || 0) + 1;
                uniqueDomains.add(domain);
                
                console.log(`   Segment ${index + 1}: "${segment.text || segment.name}" - Domain: ${domain}`);
            });
            
            console.log('\n📈 Domain distribution:');
            Object.entries(domainCounts).forEach(([domain, count]) => {
                const percentage = ((count / segments.length) * 100).toFixed(1);
                console.log(`   ${domain}: ${count} segments (${percentage}%)`);
            });
            
            // Check if we have domain diversity (not all "general")
            const hasGeneralOnly = uniqueDomains.size === 1 && uniqueDomains.has('general');
            const hasDiverseDomains = uniqueDomains.size > 2;
            
            this.testResults.domainAssignment = !hasGeneralOnly && hasDiverseDomains;
            
            if (this.testResults.domainAssignment) {
                console.log(`✅ Domain assignment: SUCCESS (${uniqueDomains.size} unique domains)`);
            } else {
                console.log(`❌ Domain assignment: FAILED (${hasGeneralOnly ? 'all general' : 'insufficient diversity'})`);
            }
            
        } catch (error) {
            console.error('❌ Error testing domain assignment:', error);
            this.testResults.domainAssignment = false;
        }
    }

    async testColorDifferentiation() {
        console.log('\n🎨 Testing Color Differentiation...');
        
        try {
            if (!this.wheelData || !this.wheelData.segments) {
                console.log('❌ No wheel data available for color testing');
                this.testResults.colorDifferentiation = false;
                return;
            }
            
            const segments = this.wheelData.segments;
            const colors = new Set();
            const colorCounts = {};
            
            console.log('\n🌈 Analyzing wheel segment colors:');
            
            segments.forEach((segment, index) => {
                const color = segment.color || '#CCCCCC';
                colors.add(color);
                colorCounts[color] = (colorCounts[color] || 0) + 1;
                
                console.log(`   Segment ${index + 1}: "${segment.text || segment.name}" - Color: ${color} (Domain: ${segment.domain})`);
            });
            
            console.log('\n🎨 Color distribution:');
            Object.entries(colorCounts).forEach(([color, count]) => {
                const percentage = ((count / segments.length) * 100).toFixed(1);
                console.log(`   ${color}: ${count} segments (${percentage}%)`);
            });
            
            // Check for color diversity (should have multiple colors, not all the same)
            const hasColorDiversity = colors.size > 1;
            const hasIdenticalColors = colors.size === 1;
            
            this.testResults.colorDifferentiation = hasColorDiversity && !hasIdenticalColors;
            
            if (this.testResults.colorDifferentiation) {
                console.log(`✅ Color differentiation: SUCCESS (${colors.size} unique colors)`);
            } else {
                console.log(`❌ Color differentiation: FAILED (${hasIdenticalColors ? 'all identical' : 'insufficient diversity'})`);
            }
            
        } catch (error) {
            console.error('❌ Error testing color differentiation:', error);
            this.testResults.colorDifferentiation = false;
        }
    }

    async generateReport() {
        console.log('\n📋 COMPREHENSIVE WHEEL DOMAIN DATA FLOW TEST REPORT');
        console.log('=' * 60);
        
        const results = this.testResults;
        const overallSuccess = Object.values(results).filter(Boolean).length >= 4; // At least 4/5 tests pass
        
        console.log('\n🎯 Test Results:');
        console.log(`   User Preference Capture: ${results.userPreferenceCapture ? '✅ PASS' : '❌ FAIL'}`);
        console.log(`   Message Transmission: ${results.messageTransmission ? '✅ PASS' : '❌ FAIL'}`);
        console.log(`   Wheel Generation: ${results.wheelGeneration ? '✅ PASS' : '❌ FAIL'}`);
        console.log(`   Domain Assignment: ${results.domainAssignment ? '✅ PASS' : '❌ FAIL'}`);
        console.log(`   Color Differentiation: ${results.colorDifferentiation ? '✅ PASS' : '❌ FAIL'}`);
        
        console.log(`\n🏆 Overall Result: ${overallSuccess ? '✅ SUCCESS' : '❌ FAILURE'}`);
        
        if (!overallSuccess) {
            console.log('\n🔍 Issues Identified:');
            if (!results.userPreferenceCapture) {
                console.log('   - User preferences not properly captured from UI sliders');
            }
            if (!results.messageTransmission) {
                console.log('   - User preferences not transmitted in WebSocket messages');
            }
            if (!results.wheelGeneration) {
                console.log('   - Wheel generation failed or timed out');
            }
            if (!results.domainAssignment) {
                console.log('   - Activities showing as "general" domain instead of diverse domains');
            }
            if (!results.colorDifferentiation) {
                console.log('   - All wheel segments have identical colors instead of diverse colors');
            }
        }
        
        // Save detailed data for analysis
        if (this.wheelData) {
            console.log('\n💾 Wheel data saved for analysis');
            console.log(`   Segments: ${this.wheelData.segments.length}`);
            console.log(`   Wheel ID: ${this.wheelData.wheelId || 'N/A'}`);
        }
        
        if (this.capturedMessages.length > 0) {
            console.log(`\n📡 WebSocket messages captured: ${this.capturedMessages.length}`);
        }
        
        this.testResults.overallSuccess = overallSuccess;
        return this.testResults;
    }

    async cleanup() {
        if (this.browser) {
            await this.browser.close();
        }
    }

    async run() {
        try {
            await this.initialize();
            await this.testUserPreferenceCapture();
            await this.testWheelGeneration();
            await this.testMessageTransmission();
            await this.testDomainAssignment();
            await this.testColorDifferentiation();
            return await this.generateReport();
        } catch (error) {
            console.error('❌ Test execution failed:', error);
            return { overallSuccess: false, error: error.message };
        } finally {
            await this.cleanup();
        }
    }
}

// Main execution
async function main() {
    const port = process.argv[2] || 3001;
    console.log(`🚀 Starting Wheel Domain Data Flow Test on port ${port}`);
    
    const tester = new WheelDomainDataFlowTester(port);
    const results = await tester.run();
    
    process.exit(results.overallSuccess ? 0 : 1);
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = { WheelDomainDataFlowTester };
