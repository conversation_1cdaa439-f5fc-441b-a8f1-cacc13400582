#!/usr/bin/env node

/**
 * Dashboard Packet Inspector
 * 
 * Interactive tool for inspecting WebSocket packets and debugging the dashboard.
 * Provides rich formatting and the ability to send custom JSON messages.
 */

import WebSocket from 'ws';
import { CONFIG } from './config.js';
import fs from 'fs';
import path from 'path';
import readline from 'readline';

class DashboardPacketInspector {
  constructor() {
    this.clients = new Map();
    this.adminSocket = null;
    this.packetHistory = [];
    this.isRunning = false;
    this.rl = null;
    
    this.logFile = path.join(CONFIG.logging.logDirectory, `packet-inspector-${Date.now()}.log`);
    
    // Ensure log directory exists
    if (!fs.existsSync(CONFIG.logging.logDirectory)) {
      fs.mkdirSync(CONFIG.logging.logDirectory, { recursive: true });
    }
  }

  async start() {
    console.log('🔍 Dashboard Packet Inspector');
    console.log('============================\n');
    
    this.isRunning = true;
    
    // Setup readline for interactive commands
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
      prompt: '📦 > '
    });
    
    try {
      // Start with some test clients
      await this.createTestClients(3);
      
      // Try to connect admin socket (will fail with auth, but we'll see the error)
      await this.tryAdminConnection();
      
      // Start interactive session
      this.startInteractiveSession();
      
    } catch (error) {
      console.error('❌ Failed to start inspector:', error.message);
    }
  }

  async createTestClients(count) {
    console.log(`🔗 Creating ${count} test clients...`);
    
    for (let i = 1; i <= count; i++) {
      try {
        await this.createClient(i);
        console.log(`✅ Client ${i} connected`);
      } catch (error) {
        console.error(`❌ Client ${i} failed:`, error.message);
      }
    }
  }

  async createClient(clientId) {
    return new Promise((resolve, reject) => {
      const socket = new WebSocket(CONFIG.backend.websocketUrl);
      const clientInfo = {
        id: clientId,
        socket: socket,
        connected: false,
        messageCount: 0
      };
      
      const timeout = setTimeout(() => {
        reject(new Error(`Client ${clientId} connection timeout`));
      }, 5000);

      socket.on('open', () => {
        clearTimeout(timeout);
        clientInfo.connected = true;
        this.clients.set(clientId, clientInfo);
        
        // Send initial message
        const message = {
          type: 'chat_message',
          content: {
            message: `Hello from inspector client ${clientId}`,
            user_profile_id: `inspector-user-${clientId}`,
            timestamp: new Date().toISOString()
          }
        };
        
        this.sendClientMessage(clientId, message);
        resolve();
      });

      socket.on('error', (error) => {
        clearTimeout(timeout);
        reject(error);
      });

      socket.on('message', (data) => {
        this.handlePacket(`CLIENT_${clientId}_RECEIVED`, data);
      });

      socket.on('close', (code, reason) => {
        console.log(`🔌 Client ${clientId} disconnected: ${code} ${reason}`);
        clientInfo.connected = false;
      });
    });
  }

  async tryAdminConnection() {
    console.log('🔌 Attempting admin WebSocket connection...');
    
    return new Promise((resolve) => {
      const adminWsUrl = 'ws://localhost:8000/ws/connection-monitor/';
      this.adminSocket = new WebSocket(adminWsUrl);
      
      const timeout = setTimeout(() => {
        console.log('⏰ Admin connection timeout (expected if not authenticated)');
        resolve();
      }, 5000);

      this.adminSocket.on('open', () => {
        clearTimeout(timeout);
        console.log('✅ Admin WebSocket connected successfully!');
        resolve();
      });

      this.adminSocket.on('error', (error) => {
        clearTimeout(timeout);
        console.log(`❌ Admin connection failed: ${error.message}`);
        if (error.message.includes('403')) {
          console.log('💡 This is expected - you need to be logged in as a staff user');
          console.log('💡 To test admin features, log in at http://localhost:8000/admin/');
        }
        resolve();
      });

      this.adminSocket.on('message', (data) => {
        this.handlePacket('ADMIN_RECEIVED', data);
      });

      this.adminSocket.on('close', (code, reason) => {
        console.log(`🔌 Admin socket closed: ${code} ${reason}`);
      });
    });
  }

  handlePacket(direction, data) {
    const timestamp = new Date().toISOString();
    let packet;
    
    try {
      packet = JSON.parse(data);
    } catch (error) {
      packet = { raw: data.toString(), parseError: error.message };
    }
    
    const entry = {
      timestamp,
      direction,
      packet
    };
    
    this.packetHistory.push(entry);
    
    // Keep only last 1000 packets
    if (this.packetHistory.length > 1000) {
      this.packetHistory.shift();
    }
    
    // Display packet with formatting
    this.displayPacket(entry);
    
    // Save to log file
    fs.appendFileSync(this.logFile, JSON.stringify(entry) + '\n');
  }

  displayPacket(entry) {
    const colors = {
      'CLIENT_': '\x1b[32m',         // Green
      'ADMIN_RECEIVED': '\x1b[35m',  // Magenta
      'ADMIN_SENT': '\x1b[34m',      // Blue
      'ERROR': '\x1b[31m'            // Red
    };
    
    let color = '\x1b[37m'; // White default
    for (const [prefix, c] of Object.entries(colors)) {
      if (entry.direction.startsWith(prefix)) {
        color = c;
        break;
      }
    }
    
    console.log(`\n${color}📦 [${entry.timestamp}] ${entry.direction}\x1b[0m`);
    console.log(this.formatJSON(entry.packet));
    console.log('─'.repeat(60));
  }

  formatJSON(obj) {
    return JSON.stringify(obj, null, 2)
      .replace(/("type":\s*"[^"]*")/g, '\x1b[33m$1\x1b[0m')  // Yellow for type
      .replace(/("user_profile_id":\s*"[^"]*")/g, '\x1b[36m$1\x1b[0m')  // Cyan for user_id
      .replace(/("message":\s*"[^"]*")/g, '\x1b[32m$1\x1b[0m')  // Green for message
      .replace(/("timestamp":\s*"[^"]*")/g, '\x1b[90m$1\x1b[0m');  // Gray for timestamp
  }

  startInteractiveSession() {
    console.log('\n🎮 Interactive Session Started');
    console.log('Available commands:');
    console.log('  📋 list - Show connected clients');
    console.log('  📦 packets [n] - Show last n packets (default: 10)');
    console.log('  📤 send <client_id> <json> - Send JSON to client');
    console.log('  📤 admin <json> - Send JSON to admin socket');
    console.log('  🔄 reconnect <client_id> - Reconnect a client');
    console.log('  🆕 new - Create a new test client');
    console.log('  💾 save - Save packet history to file');
    console.log('  🧹 clear - Clear packet history');
    console.log('  ❓ help - Show this help');
    console.log('  🚪 quit - Exit inspector\n');
    
    this.rl.prompt();
    
    this.rl.on('line', (input) => {
      this.handleCommand(input.trim());
      this.rl.prompt();
    });
    
    this.rl.on('close', () => {
      this.stop();
    });
  }

  handleCommand(input) {
    const parts = input.split(' ');
    const command = parts[0].toLowerCase();
    
    try {
      switch (command) {
        case 'list':
          this.listClients();
          break;
          
        case 'packets':
          const count = parseInt(parts[1]) || 10;
          this.showPackets(count);
          break;
          
        case 'send':
          if (parts.length < 3) {
            console.log('❌ Usage: send <client_id> <json>');
            break;
          }
          const clientId = parseInt(parts[1]);
          const clientJson = parts.slice(2).join(' ');
          this.sendToClient(clientId, clientJson);
          break;
          
        case 'admin':
          if (parts.length < 2) {
            console.log('❌ Usage: admin <json>');
            break;
          }
          const adminJson = parts.slice(1).join(' ');
          this.sendToAdmin(adminJson);
          break;
          
        case 'reconnect':
          const reconnectId = parseInt(parts[1]);
          this.reconnectClient(reconnectId);
          break;
          
        case 'new':
          this.createNewClient();
          break;
          
        case 'save':
          this.savePacketHistory();
          break;
          
        case 'clear':
          this.packetHistory = [];
          console.log('✅ Packet history cleared');
          break;
          
        case 'help':
          this.startInteractiveSession();
          break;
          
        case 'quit':
        case 'exit':
          this.stop();
          break;
          
        default:
          console.log('❓ Unknown command. Type "help" for available commands.');
      }
    } catch (error) {
      console.error('❌ Command error:', error.message);
    }
  }

  listClients() {
    console.log('\n📋 Connected Clients:');
    for (const [id, client] of this.clients) {
      const status = client.connected ? '✅ Connected' : '❌ Disconnected';
      console.log(`  Client ${id}: ${status} (${client.messageCount} messages sent)`);
    }
    
    const adminStatus = this.adminSocket && this.adminSocket.readyState === WebSocket.OPEN 
      ? '✅ Connected' : '❌ Disconnected';
    console.log(`  Admin Socket: ${adminStatus}`);
  }

  showPackets(count) {
    console.log(`\n📦 Last ${count} packets:`);
    const recent = this.packetHistory.slice(-count);
    
    for (const entry of recent) {
      this.displayPacket(entry);
    }
  }

  sendToClient(clientId, jsonString) {
    const client = this.clients.get(clientId);
    if (!client) {
      console.log(`❌ Client ${clientId} not found`);
      return;
    }
    
    if (!client.connected) {
      console.log(`❌ Client ${clientId} is not connected`);
      return;
    }
    
    try {
      const message = JSON.parse(jsonString);
      this.sendClientMessage(clientId, message);
      console.log(`✅ Message sent to client ${clientId}`);
    } catch (error) {
      console.log(`❌ Invalid JSON: ${error.message}`);
    }
  }

  sendToAdmin(jsonString) {
    if (!this.adminSocket || this.adminSocket.readyState !== WebSocket.OPEN) {
      console.log('❌ Admin socket not connected');
      return;
    }
    
    try {
      const message = JSON.parse(jsonString);
      this.adminSocket.send(JSON.stringify(message));
      this.handlePacket('ADMIN_SENT', JSON.stringify(message));
      console.log('✅ Message sent to admin socket');
    } catch (error) {
      console.log(`❌ Invalid JSON: ${error.message}`);
    }
  }

  sendClientMessage(clientId, message) {
    const client = this.clients.get(clientId);
    if (client && client.connected) {
      client.socket.send(JSON.stringify(message));
      client.messageCount++;
      this.handlePacket(`CLIENT_${clientId}_SENT`, JSON.stringify(message));
    }
  }

  async reconnectClient(clientId) {
    console.log(`🔄 Reconnecting client ${clientId}...`);
    
    // Close existing connection if any
    const existingClient = this.clients.get(clientId);
    if (existingClient && existingClient.socket) {
      existingClient.socket.close();
    }
    
    try {
      await this.createClient(clientId);
      console.log(`✅ Client ${clientId} reconnected`);
    } catch (error) {
      console.log(`❌ Failed to reconnect client ${clientId}: ${error.message}`);
    }
  }

  async createNewClient() {
    const newId = Math.max(...this.clients.keys()) + 1;
    console.log(`🆕 Creating new client ${newId}...`);
    
    try {
      await this.createClient(newId);
      console.log(`✅ Client ${newId} created`);
    } catch (error) {
      console.log(`❌ Failed to create client ${newId}: ${error.message}`);
    }
  }

  savePacketHistory() {
    const filename = path.join(CONFIG.logging.logDirectory, `packet-history-${Date.now()}.json`);
    fs.writeFileSync(filename, JSON.stringify(this.packetHistory, null, 2));
    console.log(`💾 Packet history saved to: ${filename}`);
  }

  stop() {
    console.log('\n🛑 Stopping packet inspector...');
    this.isRunning = false;
    
    // Close all connections
    for (const [id, client] of this.clients) {
      if (client.socket) {
        client.socket.close();
      }
    }
    
    if (this.adminSocket) {
      this.adminSocket.close();
    }
    
    if (this.rl) {
      this.rl.close();
    }
    
    console.log('✅ Packet inspector stopped');
    process.exit(0);
  }
}

// CLI interface
if (import.meta.url === `file://${process.argv[1]}`) {
  const inspector = new DashboardPacketInspector();
  
  // Handle graceful shutdown
  process.on('SIGINT', () => {
    inspector.stop();
  });
  
  inspector.start().catch((error) => {
    console.error('❌ Inspector failed:', error.message);
    process.exit(1);
  });
}

export default DashboardPacketInspector;
