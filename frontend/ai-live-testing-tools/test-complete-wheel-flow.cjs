#!/usr/bin/env node

/**
 * Complete end-to-end test of wheel generation and spin result flow
 * Tests: wheel generation -> wheel display -> wheel spin -> mentor response
 */

const WebSocket = require('ws');

class CompleteWheelFlowTester {
    constructor() {
        this.ws = null;
        this.connected = false;
        this.testResults = {
            connection: false,
            wheelGeneration: false,
            wheelData: false,
            wheelSpin: false,
            mentorResponse: false
        };
        this.receivedMessages = [];
        this.wheelData = null;
        this.wheelGenerationComplete = false;
    }

    async runTest() {
        console.log('🎯 Starting Complete Wheel Flow Test');
        console.log('=====================================');

        try {
            // Step 1: Connect to WebSocket
            await this.connectWebSocket();
            
            // Step 2: Request wheel generation
            await this.requestWheelGeneration();
            
            // Step 3: Wait for wheel data
            await this.waitForWheelData();
            
            // Step 4: Simulate wheel spin result
            await this.simulateWheelSpinResult();
            
            // Step 5: Wait for mentor response
            await this.waitForMentorResponse();
            
            // Step 6: Report results
            this.reportResults();
            
        } catch (error) {
            console.error('❌ Test failed:', error);
            process.exit(1);
        } finally {
            if (this.ws) {
                this.ws.close();
            }
        }
    }

    async connectWebSocket() {
        return new Promise((resolve, reject) => {
            console.log('🔗 Connecting to WebSocket...');
            
            this.ws = new WebSocket('ws://localhost:8000/ws/game/');
            
            this.ws.on('open', () => {
                console.log('✅ WebSocket connected');
                this.connected = true;
                this.testResults.connection = true;
                resolve();
            });
            
            this.ws.on('message', (data) => {
                try {
                    const message = JSON.parse(data.toString());
                    this.handleMessage(message);
                } catch (error) {
                    console.error('Error parsing message:', error);
                }
            });
            
            this.ws.on('error', (error) => {
                console.error('❌ WebSocket error:', error);
                reject(error);
            });
            
            this.ws.on('close', () => {
                console.log('🔌 WebSocket disconnected');
                this.connected = false;
            });
            
            // Timeout after 10 seconds
            setTimeout(() => {
                if (!this.connected) {
                    reject(new Error('Connection timeout'));
                }
            }, 10000);
        });
    }

    handleMessage(message) {
        this.receivedMessages.push(message);
        
        switch (message.type) {
            case 'system_message':
                console.log('📋 System:', message.content);
                break;
                
            case 'chat_message':
                console.log('💬 Chat:', message.content || 'undefined');
                // Check if this is a mentor response to our spin result
                if (message.content && message.content.includes('activity')) {
                    this.testResults.mentorResponse = true;
                    console.log('✅ Mentor response detected!');
                }
                break;
                
            case 'wheel_data':
                console.log('🎡 Wheel data received');
                this.wheelData = message.wheel;
                if (this.wheelData && this.wheelData.items && this.wheelData.items.length > 0) {
                    console.log(`✅ Wheel has ${this.wheelData.items.length} activities`);
                    this.testResults.wheelData = true;
                    this.wheelGenerationComplete = true;
                } else {
                    console.log('❌ Wheel data is empty or invalid');
                }
                break;
                
            case 'processing_status':
                console.log('⚙️ Processing:', message.status);
                if (message.status === 'completed' && !this.wheelGenerationComplete) {
                    // Mark wheel generation as complete when processing finishes
                    this.testResults.wheelGeneration = true;
                }
                break;
                
            case 'workflow_status':
                console.log('🔄 Workflow:', message.workflow_id, message.status);
                break;
                
            case 'heartbeat':
                // Ignore heartbeats
                break;
                
            default:
                console.log(`📋 ${message.type}:`, JSON.stringify(message).substring(0, 100) + '...');
        }
    }

    async requestWheelGeneration() {
        console.log('🎡 Requesting wheel generation...');

        const wheelRequest = {
            type: 'chat_message',
            content: {
                message: 'hey, I\'m feeling energetic and I have 2h free ahead of me. It\'s very hot outside though. Generate me the perfect wheel !',
                user_profile_id: '2',
                timestamp: new Date().toISOString()
            }
        };

        this.ws.send(JSON.stringify(wheelRequest));
        console.log('✅ Wheel generation request sent');
    }

    async waitForWheelData() {
        console.log('⏳ Waiting for wheel generation to complete...');
        
        return new Promise((resolve) => {
            const timeout = setTimeout(() => {
                console.log('⚠️ Timeout waiting for wheel data');
                resolve();
            }, 120000); // 2 minute timeout for wheel generation
            
            const checkForWheelData = () => {
                if (this.testResults.wheelData && this.wheelGenerationComplete) {
                    clearTimeout(timeout);
                    console.log('✅ Wheel generation completed!');
                    resolve();
                } else {
                    setTimeout(checkForWheelData, 1000);
                }
            };
            
            checkForWheelData();
        });
    }

    async simulateWheelSpinResult() {
        console.log('🎯 Simulating wheel spin result...');
        
        if (!this.wheelData || !this.wheelData.items || this.wheelData.items.length === 0) {
            throw new Error('No wheel data available for spinning');
        }
        
        // Pick a random activity as the "winner"
        const winningActivity = this.wheelData.items[Math.floor(Math.random() * this.wheelData.items.length)];
        console.log(`🏆 Simulated winner: ${winningActivity.name}`);
        
        // Send spin result to backend (this is what the frontend should do)
        const spinResultMessage = {
            type: 'spin_result',
            content: {
                activity_tailored_id: winningActivity.activity_tailored_id,
                name: winningActivity.name,
                description: winningActivity.description,
                user_profile_id: '2' // Use existing user ID
            }
        };
        
        console.log('📤 Sending spin result to backend...');
        this.ws.send(JSON.stringify(spinResultMessage));
        this.testResults.wheelSpin = true;
        console.log('✅ Spin result sent');
    }

    async waitForMentorResponse() {
        console.log('⏳ Waiting for mentor response...');
        
        return new Promise((resolve) => {
            const timeout = setTimeout(() => {
                console.log('⚠️ Timeout waiting for mentor response');
                resolve();
            }, 30000); // 30 second timeout
            
            const checkForResponse = () => {
                if (this.testResults.mentorResponse) {
                    clearTimeout(timeout);
                    console.log('✅ Mentor response received!');
                    resolve();
                } else {
                    setTimeout(checkForResponse, 1000);
                }
            };
            
            checkForResponse();
        });
    }

    reportResults() {
        console.log('\n📊 Complete Wheel Flow Test Results');
        console.log('====================================');
        console.log(`🔗 WebSocket Connection: ${this.testResults.connection ? '✅ PASS' : '❌ FAIL'}`);
        console.log(`🎡 Wheel Generation: ${this.testResults.wheelGeneration ? '✅ PASS' : '❌ FAIL'}`);
        console.log(`📊 Wheel Data Received: ${this.testResults.wheelData ? '✅ PASS' : '❌ FAIL'}`);
        console.log(`🎯 Wheel Spin Simulation: ${this.testResults.wheelSpin ? '✅ PASS' : '❌ FAIL'}`);
        console.log(`💬 Mentor Response: ${this.testResults.mentorResponse ? '✅ PASS' : '❌ FAIL'}`);
        
        const allPassed = Object.values(this.testResults).every(result => result);
        console.log(`\n🎯 Overall Result: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
        
        if (this.wheelData && this.wheelData.items) {
            console.log(`\n🎡 Wheel Summary: ${this.wheelData.items.length} activities generated`);
            this.wheelData.items.forEach((item, index) => {
                console.log(`  ${index + 1}. ${item.name} (${item.domain})`);
            });
        }
        
        if (!allPassed) {
            console.log('\n📋 Debug Info:');
            console.log(`Total messages received: ${this.receivedMessages.length}`);
            const messageTypes = [...new Set(this.receivedMessages.map(m => m.type))];
            console.log(`Message types: ${messageTypes.join(', ')}`);
        }
        
        process.exit(allPassed ? 0 : 1);
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Run the test
const tester = new CompleteWheelFlowTester();
tester.runTest().catch(console.error);
