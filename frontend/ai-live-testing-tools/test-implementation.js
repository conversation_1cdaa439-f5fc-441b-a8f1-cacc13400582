#!/usr/bin/env node

/**
 * Implementation Test
 * 
 * Tests the basic implementation of the connection dashboard
 * without requiring authentication.
 */

import WebSocket from 'ws';
import { CONFIG } from './config.js';

class ImplementationTest {
  constructor() {
    this.results = [];
  }

  async runTests() {
    console.log('🔧 Testing Connection Dashboard Implementation');
    console.log('==============================================\n');

    try {
      // Test 1: Check if backend is responding
      await this.testBackendHealth();
      
      // Test 2: Test client WebSocket connections
      await this.testClientConnections();
      
      // Test 3: Test Redis connection tracking (if available)
      await this.testRedisTracking();
      
      // Test 4: Verify admin URLs are configured
      await this.testAdminURLs();
      
      this.printResults();
      
    } catch (error) {
      console.error('❌ Implementation test failed:', error.message);
    }
  }

  async testBackendHealth() {
    console.log('🏥 Testing Backend Health...');
    
    try {
      const response = await fetch('http://localhost:8000/admin/', {
        method: 'HEAD'
      });
      
      if (response.status === 302 || response.status === 200) {
        console.log('✅ Backend is responding');
        this.results.push({ test: 'backend_health', status: 'passed' });
      } else {
        throw new Error(`Unexpected status: ${response.status}`);
      }
    } catch (error) {
      console.log('❌ Backend health check failed:', error.message);
      this.results.push({ test: 'backend_health', status: 'failed', error: error.message });
    }
  }

  async testClientConnections() {
    console.log('\n🔗 Testing Client WebSocket Connections...');
    
    return new Promise((resolve) => {
      const socket = new WebSocket(CONFIG.backend.websocketUrl);
      let connected = false;
      
      const timeout = setTimeout(() => {
        if (!connected) {
          console.log('❌ Client connection timeout');
          this.results.push({ test: 'client_connection', status: 'failed', error: 'timeout' });
          socket.close();
          resolve();
        }
      }, 10000);

      socket.on('open', () => {
        connected = true;
        clearTimeout(timeout);
        console.log('✅ Client WebSocket connection successful');
        
        // Send test message
        socket.send(JSON.stringify({
          type: 'chat_message',
          content: {
            message: 'Test message for connection tracking',
            user_profile_id: 'test-implementation-user',
            timestamp: new Date().toISOString()
          }
        }));
        
        this.results.push({ test: 'client_connection', status: 'passed' });
        
        setTimeout(() => {
          socket.close();
          resolve();
        }, 2000);
      });

      socket.on('error', (error) => {
        clearTimeout(timeout);
        console.log('❌ Client connection failed:', error.message);
        this.results.push({ test: 'client_connection', status: 'failed', error: error.message });
        resolve();
      });

      socket.on('message', (data) => {
        try {
          const message = JSON.parse(data);
          console.log(`📨 Received: ${message.type}`);
        } catch (error) {
          console.log('⚠️  Received non-JSON message');
        }
      });
    });
  }

  async testRedisTracking() {
    console.log('\n📊 Testing Redis Connection Tracking...');
    
    // This is a basic test - we can't directly test Redis without authentication
    // But we can verify the implementation exists
    try {
      // Check if the connection tracker service file exists
      const fs = await import('fs');
      const path = await import('path');
      
      const trackerPath = path.resolve('../../backend/apps/admin_tools/services/connection_tracker.py');
      
      if (fs.existsSync(trackerPath)) {
        console.log('✅ Connection tracker service file exists');
        this.results.push({ test: 'redis_tracking', status: 'passed' });
      } else {
        console.log('❌ Connection tracker service file not found');
        this.results.push({ test: 'redis_tracking', status: 'failed', error: 'file not found' });
      }
    } catch (error) {
      console.log('⚠️  Could not verify Redis tracking implementation');
      this.results.push({ test: 'redis_tracking', status: 'skipped', error: error.message });
    }
  }

  async testAdminURLs() {
    console.log('\n🔗 Testing Admin URL Configuration...');
    
    try {
      // Test if the connection dashboard URL exists (will redirect to login)
      const response = await fetch('http://localhost:8000/admin/connection-dashboard/', {
        method: 'HEAD',
        redirect: 'manual'
      });
      
      // We expect a redirect to login page for unauthenticated users
      if (response.status === 302 || response.status === 301) {
        console.log('✅ Connection dashboard URL is configured (redirects to login)');
        this.results.push({ test: 'admin_urls', status: 'passed' });
      } else if (response.status === 200) {
        console.log('✅ Connection dashboard URL is accessible');
        this.results.push({ test: 'admin_urls', status: 'passed' });
      } else {
        throw new Error(`Unexpected status: ${response.status}`);
      }
    } catch (error) {
      console.log('❌ Admin URL test failed:', error.message);
      this.results.push({ test: 'admin_urls', status: 'failed', error: error.message });
    }
  }

  printResults() {
    console.log('\n📊 IMPLEMENTATION TEST RESULTS:');
    console.log('================================');
    
    const passed = this.results.filter(r => r.status === 'passed').length;
    const failed = this.results.filter(r => r.status === 'failed').length;
    const skipped = this.results.filter(r => r.status === 'skipped').length;
    
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`⏭️  Skipped: ${skipped}`);
    
    console.log('\nDetailed Results:');
    for (const result of this.results) {
      const icon = result.status === 'passed' ? '✅' : result.status === 'failed' ? '❌' : '⏭️';
      console.log(`  ${icon} ${result.test}: ${result.status}`);
      if (result.error) {
        console.log(`     Error: ${result.error}`);
      }
    }
    
    if (failed === 0) {
      console.log('\n🎉 Implementation appears to be working correctly!');
      console.log('   To test the full dashboard, log in as a staff user and visit:');
      console.log('   http://localhost:8000/admin/connection-dashboard/');
    } else {
      console.log('\n⚠️  Some tests failed. Check the implementation.');
    }
  }
}

// Run the test
if (import.meta.url === `file://${process.argv[1]}`) {
  const tester = new ImplementationTest();
  tester.runTests().catch(console.error);
}

export default ImplementationTest;
