/**
 * Production Login and Beta Signup Test
 * 
 * Comprehensive test for production-ready authentication system with beta signup functionality.
 * Tests both login form for existing users and beta signup form for interested visitors.
 * 
 * Usage: node test-production-login-beta-signup.cjs [port]
 * Example: node test-production-login-beta-signup.cjs 5173
 */

const puppeteer = require('puppeteer');

class ProductionLoginBetaSignupTest {
    constructor(port = 5173) {
        this.port = port;
        this.baseUrl = `http://localhost:${port}`;
        this.browser = null;
        this.page = null;
        this.testResults = {
            loginFormPresent: false,
            betaSignupVisible: false,
            betaSignupFormFunctional: false,
            loginFormFunctional: false,
            validationWorking: false,
            apiIntegration: false,
            uiResponsive: false
        };
    }

    async initialize() {
        console.log('🚀 Starting Production Login & Beta Signup Test...');
        console.log(`📍 Testing URL: ${this.baseUrl}`);
        
        this.browser = await puppeteer.launch({
            headless: false,
            defaultViewport: { width: 1280, height: 720 },
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        
        this.page = await this.browser.newPage();

        // Add waitForTimeout method if not available
        if (!this.page.waitForTimeout) {
            this.page.waitForTimeout = (ms) => new Promise(resolve => setTimeout(resolve, ms));
        }

        // Set up console logging
        this.page.on('console', msg => {
            if (msg.type() === 'error') {
                console.log('❌ Browser Error:', msg.text());
            }
        });
        
        // Set up request/response monitoring
        this.page.on('response', response => {
            if (response.url().includes('/api/auth/beta-signup/')) {
                console.log(`📡 Beta Signup API Response: ${response.status()}`);
            }
            if (response.url().includes('/api/auth/login/')) {
                console.log(`📡 Login API Response: ${response.status()}`);
            }
        });
    }

    async testLoginFormPresence() {
        console.log('\n🔍 Testing Login Form Presence...');

        try {
            await this.page.goto(this.baseUrl, { waitUntil: 'networkidle0' });

            // Debug: Log what's actually on the page
            const pageContent = await this.page.content();
            console.log('📄 Page contains login-form tag:', pageContent.includes('<login-form'));
            console.log('📄 Page contains app-shell tag:', pageContent.includes('<app-shell'));

            // Check for JavaScript errors
            const errors = await this.page.evaluate(() => {
                return window.console.errors || [];
            });
            if (errors.length > 0) {
                console.log('❌ JavaScript errors found:', errors);
            }

            // Check authentication state
            const authState = await this.page.evaluate(() => {
                const appShell = document.querySelector('app-shell');
                if (appShell && appShell.shadowRoot) {
                    const loginForm = appShell.shadowRoot.querySelector('login-form');
                    return {
                        appShellExists: !!appShell,
                        shadowRootExists: !!appShell.shadowRoot,
                        loginFormInShadow: !!loginForm,
                        shadowRootContent: appShell.shadowRoot.innerHTML.substring(0, 500)
                    };
                }
                return {
                    appShellExists: !!appShell,
                    shadowRootExists: false,
                    loginFormInShadow: false,
                    shadowRootContent: 'No shadow root'
                };
            });
            console.log('🔍 Auth state debug:', authState);

            // Wait a bit for components to load
            await this.page.waitForTimeout(2000);

            // Check if login form is present (inside shadow DOM)
            const loginForm = await this.page.evaluate(() => {
                const appShell = document.querySelector('app-shell');
                if (appShell && appShell.shadowRoot) {
                    return appShell.shadowRoot.querySelector('login-form');
                }
                return null;
            });
            if (loginForm) {
                console.log('✅ Login form component found');
                this.testResults.loginFormPresent = true;
                
                // Check for form elements inside shadow DOM
                const formElements = await this.page.evaluate(() => {
                    const appShell = document.querySelector('app-shell');
                    if (appShell && appShell.shadowRoot) {
                        const loginForm = appShell.shadowRoot.querySelector('login-form');
                        if (loginForm && loginForm.shadowRoot) {
                            return {
                                usernameField: !!loginForm.shadowRoot.querySelector('input[type="text"]#username'),
                                passwordField: !!loginForm.shadowRoot.querySelector('input[type="password"]#password'),
                                loginButton: !!loginForm.shadowRoot.querySelector('button[type="submit"]')
                            };
                        }
                    }
                    return { usernameField: false, passwordField: false, loginButton: false };
                });
                const { usernameField, passwordField, loginButton } = formElements;
                
                if (usernameField && passwordField && loginButton) {
                    console.log('✅ All login form elements present');
                } else {
                    console.log('❌ Missing login form elements');
                }
            } else {
                console.log('❌ Login form component not found');
            }
        } catch (error) {
            console.log('❌ Error testing login form presence:', error.message);
        }
    }

    async testBetaSignupVisibility() {
        console.log('\n🔍 Testing Beta Signup Visibility...');
        
        try {
            // Check for beta notice section inside shadow DOM
            const betaNotice = await this.page.evaluate(() => {
                const appShell = document.querySelector('app-shell');
                if (appShell && appShell.shadowRoot) {
                    const loginForm = appShell.shadowRoot.querySelector('login-form');
                    if (loginForm && loginForm.shadowRoot) {
                        return loginForm.shadowRoot.querySelector('.beta-notice');
                    }
                }
                return null;
            });
            if (betaNotice) {
                console.log('✅ Beta notice section found');
                this.testResults.betaSignupVisible = true;
                
                // Check for beta signup button inside shadow DOM
                const betaButton = await this.page.evaluate(() => {
                    const appShell = document.querySelector('app-shell');
                    if (appShell && appShell.shadowRoot) {
                        const loginForm = appShell.shadowRoot.querySelector('login-form');
                        if (loginForm && loginForm.shadowRoot) {
                            return loginForm.shadowRoot.querySelector('.beta-signup-button');
                        }
                    }
                    return null;
                });
                if (betaButton) {
                    console.log('✅ Beta signup button found');
                    
                    // Check beta notice text inside shadow DOM
                    const noticeText = await this.page.evaluate(() => {
                        const appShell = document.querySelector('app-shell');
                        if (appShell && appShell.shadowRoot) {
                            const loginForm = appShell.shadowRoot.querySelector('login-form');
                            if (loginForm && loginForm.shadowRoot) {
                                const betaNotice = loginForm.shadowRoot.querySelector('.beta-notice');
                                return betaNotice ? betaNotice.textContent : '';
                            }
                        }
                        return '';
                    });
                    if (noticeText.includes('Closed Beta') && noticeText.includes('Join the Waitlist')) {
                        console.log('✅ Beta notice contains appropriate messaging');
                    } else {
                        console.log('❌ Beta notice messaging incomplete');
                    }
                } else {
                    console.log('❌ Beta signup button not found');
                }
            } else {
                console.log('❌ Beta notice section not found');
            }
        } catch (error) {
            console.log('❌ Error testing beta signup visibility:', error.message);
        }
    }

    async testBetaSignupForm() {
        console.log('\n🔍 Testing Beta Signup Form Functionality...');
        
        try {
            // Click the beta signup button
            const betaButton = await this.page.$('.beta-signup-button');
            if (betaButton) {
                await betaButton.click();
                await this.page.waitForTimeout(500);
                
                // Check if form appears
                const betaForm = await this.page.$('.beta-form');
                if (betaForm) {
                    console.log('✅ Beta signup form appears on click');
                    this.testResults.betaSignupFormFunctional = true;
                    
                    // Test form elements
                    const emailField = await this.page.$('#beta-email');
                    const messageField = await this.page.$('#beta-message');
                    const submitButton = await this.page.$('.beta-submit-button');
                    const cancelButton = await this.page.$('.beta-cancel-button');
                    
                    if (emailField && messageField && submitButton && cancelButton) {
                        console.log('✅ All beta form elements present');
                        
                        // Test cancel functionality
                        await cancelButton.click();
                        await this.page.waitForSelector('.beta-form', { hidden: true, timeout: 1000 }).catch(() => {});
                        
                        const formHidden = await this.page.$('.beta-form') === null;
                        if (formHidden) {
                            console.log('✅ Cancel button hides form');
                        } else {
                            console.log('❌ Cancel button does not hide form');
                        }
                    } else {
                        console.log('❌ Missing beta form elements');
                    }
                } else {
                    console.log('❌ Beta signup form does not appear');
                }
            }
        } catch (error) {
            console.log('❌ Error testing beta signup form:', error.message);
        }
    }

    async testFormValidation() {
        console.log('\n🔍 Testing Form Validation...');
        
        try {
            // Open beta form again
            const betaButton = await this.page.$('.beta-signup-button');
            if (betaButton) {
                await betaButton.click();
                await this.page.waitForTimeout(500);
                
                // Test empty email submission
                const submitButton = await this.page.$('.beta-submit-button');
                if (submitButton) {
                    await submitButton.click();
                    await this.page.waitForTimeout(1000);
                    
                    // Check for error message
                    const errorMessage = await this.page.$('.error-message');
                    if (errorMessage) {
                        const errorText = await this.page.$eval('.error-message', el => el.textContent);
                        if (errorText.includes('email')) {
                            console.log('✅ Email validation working');
                            this.testResults.validationWorking = true;
                        } else {
                            console.log('❌ Email validation message incorrect');
                        }
                    } else {
                        console.log('❌ No validation error shown for empty email');
                    }
                    
                    // Test invalid email format
                    await this.page.type('#beta-email', 'invalid-email');
                    await submitButton.click();
                    await this.page.waitForTimeout(1000);
                    
                    const invalidEmailError = await this.page.$('.error-message');
                    if (invalidEmailError) {
                        console.log('✅ Invalid email format validation working');
                    }
                }
            }
        } catch (error) {
            console.log('❌ Error testing form validation:', error.message);
        }
    }

    async testApiIntegration() {
        console.log('\n🔍 Testing API Integration...');
        
        try {
            // Clear previous input and enter valid email
            await this.page.evaluate(() => {
                const emailField = document.querySelector('#beta-email');
                if (emailField) emailField.value = '';
            });
            
            const testEmail = `test-${Date.now()}@example.com`;
            await this.page.type('#beta-email', testEmail);
            await this.page.type('#beta-message', 'This is a test message for beta signup functionality.');
            
            // Submit the form
            const submitButton = await this.page.$('.beta-submit-button');
            if (submitButton) {
                await submitButton.click();
                
                // Wait for response
                await this.page.waitForTimeout(3000);
                
                // Check for success message
                const successMessage = await this.page.$('.beta-success');
                if (successMessage) {
                    console.log('✅ Beta signup API integration working');
                    this.testResults.apiIntegration = true;
                    
                    const successText = await this.page.$eval('.beta-success', el => el.textContent);
                    if (successText.includes('Thank You')) {
                        console.log('✅ Success message displayed correctly');
                    }
                } else {
                    // Check for error message
                    const errorMessage = await this.page.$('.error-message');
                    if (errorMessage) {
                        const errorText = await this.page.$eval('.error-message', el => el.textContent);
                        console.log(`⚠️ API returned error: ${errorText}`);
                        
                        // This might be expected if email already exists
                        if (errorText.includes('already been registered')) {
                            console.log('✅ Duplicate email validation working');
                            this.testResults.apiIntegration = true;
                        }
                    } else {
                        console.log('❌ No response from beta signup API');
                    }
                }
            }
        } catch (error) {
            console.log('❌ Error testing API integration:', error.message);
        }
    }

    async testResponsiveDesign() {
        console.log('\n🔍 Testing Responsive Design...');
        
        try {
            // Test mobile viewport
            await this.page.setViewport({ width: 375, height: 667 });
            await this.page.waitForTimeout(500);
            
            const loginContainer = await this.page.$('.login-container');
            if (loginContainer) {
                const containerStyles = await this.page.evaluate(() => {
                    const container = document.querySelector('.login-container');
                    const styles = window.getComputedStyle(container);
                    return {
                        width: styles.width,
                        padding: styles.padding,
                        margin: styles.margin
                    };
                });
                
                console.log('✅ Mobile layout applied');
                this.testResults.uiResponsive = true;
            }
            
            // Reset to desktop viewport
            await this.page.setViewport({ width: 1280, height: 720 });
        } catch (error) {
            console.log('❌ Error testing responsive design:', error.message);
        }
    }

    async generateReport() {
        console.log('\n📊 Test Results Summary:');
        console.log('================================');
        
        const results = [
            { test: 'Login Form Present', result: this.testResults.loginFormPresent },
            { test: 'Beta Signup Visible', result: this.testResults.betaSignupVisible },
            { test: 'Beta Form Functional', result: this.testResults.betaSignupFormFunctional },
            { test: 'Form Validation', result: this.testResults.validationWorking },
            { test: 'API Integration', result: this.testResults.apiIntegration },
            { test: 'Responsive Design', result: this.testResults.uiResponsive }
        ];
        
        let passedTests = 0;
        results.forEach(({ test, result }) => {
            const status = result ? '✅ PASS' : '❌ FAIL';
            console.log(`${status} ${test}`);
            if (result) passedTests++;
        });
        
        const successRate = Math.round((passedTests / results.length) * 100);
        console.log(`\n🎯 Overall Success Rate: ${successRate}% (${passedTests}/${results.length})`);
        
        if (successRate >= 80) {
            console.log('🎉 Production login and beta signup system is ready!');
        } else {
            console.log('⚠️ Some issues need to be addressed before production deployment.');
        }
    }

    async cleanup() {
        if (this.browser) {
            await this.browser.close();
        }
    }

    async run() {
        try {
            await this.initialize();
            await this.testLoginFormPresence();
            await this.testBetaSignupVisibility();
            await this.testBetaSignupForm();
            await this.testFormValidation();
            await this.testApiIntegration();
            await this.testResponsiveDesign();
            await this.generateReport();
        } catch (error) {
            console.error('❌ Test execution failed:', error);
        } finally {
            await this.cleanup();
        }
    }
}

// Run the test
const port = process.argv[2] || 5173;
const test = new ProductionLoginBetaSignupTest(port);
test.run().catch(console.error);
