const { chromium } = require('playwright');

class RealModeUserStoryTest {
    constructor() {
        this.browser = null;
        this.page = null;
        this.wsMessages = [];
        this.testResults = {
            realModeConnection: false,
            userRecognition: false,
            wheelGeneration: false,
            backendConnectivity: false,
            demoModeFallback: false,
            testDuration: 0
        };
    }

    async initialize() {
        console.log('🚀 Initializing Real Mode User Story Test...');
        
        this.browser = await chromium.launch({
            headless: false,  // Visual debugging
            slowMo: 100,      // Slow down for observation
            args: ['--disable-web-security', '--disable-features=VizDisplayCompositor']
        });
        
        this.page = await this.browser.newPage();
        
        // Enhanced WebSocket monitoring with timeout detection
        this.page.routeWebSocket('**/ws/game/', ws => {
            console.log('🔌 WebSocket route intercepted');
            
            ws.onMessage(message => {
                try {
                    const parsed = JSON.parse(message);
                    this.wsMessages.push({
                        timestamp: Date.now(),
                        direction: 'server->client',
                        parsed: parsed,
                        raw: message
                    });
                    console.log('📨 WebSocket message received:', parsed.type || 'unknown');
                } catch (e) {
                    console.log('📨 WebSocket raw message:', message);
                }
            });
            
            ws.onClose((code, reason) => {
                console.log(`🔌 WebSocket closed: ${code} - ${reason}`);
            });
            
            ws.connectToServer();
        });
        
        // Monitor console for demo mode indicators
        this.page.on('console', msg => {
            const text = msg.text();
            if (text.includes('demo mode') || text.includes('Demo Mode') || text.includes('DEMO')) {
                console.log('🎭 Demo mode detected:', text);
                this.testResults.demoModeFallback = true;
            }
            if (text.includes('WebSocket') || text.includes('websocket')) {
                console.log('🔌 WebSocket activity:', text);
            }
        });
    }

    async testRealModeConnection() {
        console.log('🔍 Testing real mode connection...');
        
        const startTime = Date.now();
        
        try {
            // Load frontend with extended timeout
            console.log('📱 Loading frontend...');
            await this.page.goto('http://localhost:3003', {
                waitUntil: 'networkidle',
                timeout: 30000  // Extended timeout
            });
            
            // Wait for potential WebSocket connection with longer timeout
            console.log('⏳ Waiting for WebSocket connection (30 seconds)...');
            await this.page.waitForTimeout(30000);
            
            // Check if we're in demo mode
            const demoModeIndicators = await this.page.locator('text=/demo mode/i').count();
            const realModeIndicators = this.wsMessages.length > 0;
            
            if (realModeIndicators && !this.testResults.demoModeFallback) {
                console.log('✅ Real mode connection established');
                this.testResults.realModeConnection = true;
                this.testResults.backendConnectivity = true;
            } else if (this.testResults.demoModeFallback || demoModeIndicators > 0) {
                console.log('🎭 Demo mode fallback detected');
                this.testResults.demoModeFallback = true;
                this.testResults.realModeConnection = false;
            } else {
                console.log('⚠️  Connection status unclear');
            }
            
        } catch (error) {
            console.error('❌ Real mode connection test failed:', error.message);
            this.testResults.realModeConnection = false;
        }
        
        this.testResults.testDuration = Date.now() - startTime;
    }

    async testUserRecognitionInRealMode() {
        if (!this.testResults.realModeConnection) {
            console.log('⏭️  Skipping user recognition test (not in real mode)');
            return;
        }
        
        console.log('👤 Testing user recognition in real mode...');
        
        try {
            // Find chat input
            const chatInput = await this.findChatInput();
            if (!chatInput) {
                console.log('❌ Chat input not found');
                return;
            }
            
            // Send user recognition message
            await chatInput.fill('hey! do you recognize me?');
            await chatInput.press('Enter');
            
            console.log('📤 Sent user recognition message');
            
            // Wait for AI response with user-specific data
            await this.page.waitForTimeout(15000);  // Wait for backend processing
            
            // Check for user-specific data in WebSocket messages
            const userDataFound = this.wsMessages.some(msg => {
                const content = JSON.stringify(msg.parsed || {}).toLowerCase();
                return content.includes('user_profile') || 
                       content.includes('phiphi') || 
                       content.includes('user_id') ||
                       content.includes('trust_level');
            });
            
            if (userDataFound) {
                console.log('✅ User recognition successful in real mode');
                this.testResults.userRecognition = true;
            } else {
                console.log('❌ No user-specific data detected');
            }
            
        } catch (error) {
            console.error('❌ User recognition test failed:', error.message);
        }
    }

    async findChatInput() {
        const selectors = [
            'input[placeholder*="message"]',
            'input[placeholder*="chat"]',
            'textarea[placeholder*="message"]',
            'input[type="text"]',
            '.chat-input input',
            '#chat-input'
        ];
        
        for (const selector of selectors) {
            try {
                const element = this.page.locator(selector);
                if (await element.isVisible({ timeout: 2000 })) {
                    return element;
                }
            } catch (e) {
                // Continue to next selector
            }
        }
        
        return null;
    }

    async testWheelGenerationInRealMode() {
        if (!this.testResults.realModeConnection) {
            console.log('⏭️  Skipping wheel generation test (not in real mode)');
            return;
        }
        
        console.log('🎡 Testing wheel generation in real mode...');
        
        try {
            // Look for wheel generation trigger
            const wheelButton = await this.findWheelGenerationButton();
            if (wheelButton) {
                await wheelButton.click();
                console.log('🎡 Clicked wheel generation button');
                
                // Wait for wheel generation workflow
                await this.page.waitForTimeout(20000);  // Extended wait for real backend
                
                // Check for wheel generation in WebSocket messages
                const wheelGenerated = this.wsMessages.some(msg => {
                    const content = JSON.stringify(msg.parsed || {}).toLowerCase();
                    return content.includes('wheel') || 
                           content.includes('activity') ||
                           content.includes('workflow_status');
                });
                
                if (wheelGenerated) {
                    console.log('✅ Wheel generation successful in real mode');
                    this.testResults.wheelGeneration = true;
                } else {
                    console.log('❌ No wheel generation detected');
                }
            } else {
                console.log('⚠️  Wheel generation button not found');
            }
            
        } catch (error) {
            console.error('❌ Wheel generation test failed:', error.message);
        }
    }

    async findWheelGenerationButton() {
        const selectors = [
            'button:has-text("Generate")',
            'button:has-text("Create")',
            'button:has-text("Wheel")',
            '[data-testid="generate-wheel"]',
            '.generate-button',
            'button[type="submit"]'
        ];
        
        for (const selector of selectors) {
            try {
                const element = this.page.locator(selector);
                if (await element.isVisible({ timeout: 2000 })) {
                    return element;
                }
            } catch (e) {
                // Continue to next selector
            }
        }
        
        return null;
    }

    generateReport() {
        console.log('\n📊 REAL MODE TEST RESULTS:');
        console.log('=====================================');
        console.log(`Real Mode Connection: ${this.testResults.realModeConnection ? '✅ PASSED' : '❌ FAILED'}`);
        console.log(`Backend Connectivity: ${this.testResults.backendConnectivity ? '✅ PASSED' : '❌ FAILED'}`);
        console.log(`Demo Mode Fallback: ${this.testResults.demoModeFallback ? '⚠️  DETECTED' : '✅ AVOIDED'}`);
        console.log(`User Recognition: ${this.testResults.userRecognition ? '✅ PASSED' : '❌ FAILED'}`);
        console.log(`Wheel Generation: ${this.testResults.wheelGeneration ? '✅ PASSED' : '❌ FAILED'}`);
        console.log(`Test Duration: ${Math.round(this.testResults.testDuration / 1000)}s`);
        console.log(`WebSocket Messages: ${this.wsMessages.length}`);
        
        const passedTests = Object.values(this.testResults).filter(result => result === true).length;
        const totalTests = Object.keys(this.testResults).length - 2; // Exclude duration and demoModeFallback
        const successRate = Math.round((passedTests / totalTests) * 100);
        
        console.log(`\nOverall Success Rate: ${successRate}% (${passedTests}/${totalTests})`);
        
        if (this.testResults.realModeConnection) {
            console.log('\n🎉 SUCCESS: Real mode connection established!');
            console.log('The frontend successfully connected to the backend without demo mode fallback.');
        } else if (this.testResults.demoModeFallback) {
            console.log('\n🎭 DEMO MODE: Frontend fell back to demo mode');
            console.log('This indicates WebSocket connection timeout, but backend is functional.');
        } else {
            console.log('\n❌ FAILURE: Unable to establish any connection');
        }
        
        console.log('\n📋 RECOMMENDATIONS:');
        if (!this.testResults.realModeConnection && this.testResults.demoModeFallback) {
            console.log('- Increase frontend WebSocket connection timeout');
            console.log('- Optimize backend startup time');
            console.log('- Implement connection retry logic');
        }
        if (this.testResults.realModeConnection && !this.testResults.userRecognition) {
            console.log('- Check user authentication flow');
            console.log('- Verify user profile data transmission');
        }
        
        return this.testResults;
    }

    async cleanup() {
        if (this.browser) {
            await this.browser.close();
        }
    }

    async run() {
        try {
            await this.initialize();
            await this.testRealModeConnection();
            await this.testUserRecognitionInRealMode();
            await this.testWheelGenerationInRealMode();
            return this.generateReport();
        } catch (error) {
            console.error('❌ Test execution failed:', error);
            return this.testResults;
        } finally {
            await this.cleanup();
        }
    }
}

// Run the test
if (require.main === module) {
    const test = new RealModeUserStoryTest();
    test.run().then(results => {
        process.exit(results.realModeConnection ? 0 : 1);
    }).catch(error => {
        console.error('Fatal error:', error);
        process.exit(1);
    });
}

module.exports = RealModeUserStoryTest;
