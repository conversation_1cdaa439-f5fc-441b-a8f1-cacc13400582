#!/usr/bin/env node

/**
 * RACE CONDITION DETECTOR - Dual-Wheel Issue Investigation
 * 
 * This test specifically looks for race conditions that might cause the dual-wheel issue:
 * 1. Monitor ALL wheel data changes with timestamps
 * 2. Track API calls and WebSocket messages
 * 3. Detect if wheel data gets overwritten after API operations
 * 4. Identify the exact source of grey segments
 */

const puppeteer = require('puppeteer');

class RaceConditionDetector {
    constructor(port = 3002) {
        this.port = port;
        this.baseUrl = `http://localhost:${port}`;
        this.browser = null;
        this.page = null;
        this.wheelDataTimeline = [];
        this.apiCalls = [];
        this.websocketMessages = [];
    }

    async runDetection() {
        console.log('🔍 ===== RACE CONDITION DETECTION FOR DUAL-WHEEL ISSUE =====');
        console.log(`🌐 URL: ${this.baseUrl}`);
        console.log();

        try {
            await this.setupBrowser();
            await this.setupMonitoring();
            await this.setupAuthentication();
            await this.waitForManualTesting();
        } catch (error) {
            console.error('❌ Detection failed:', error);
        } finally {
            if (this.browser) {
                console.log('\n📊 ===== RACE CONDITION ANALYSIS =====');
                this.analyzeRaceConditions();
                console.log('\n⏸️ Browser will remain open for inspection...');
                await new Promise(() => {}); // Keep open
            }
        }
    }

    async setupBrowser() {
        console.log('🚀 Setting up browser...');
        this.browser = await puppeteer.launch({
            headless: false,
            defaultViewport: { width: 1600, height: 1000 },
            args: ['--no-sandbox', '--disable-setuid-sandbox'],
            slowMo: 50
        });
        this.page = await this.browser.newPage();
        
        await this.page.goto(this.baseUrl);
        await new Promise(resolve => setTimeout(resolve, 3000));
        console.log('   ✅ Browser ready');
    }

    async setupMonitoring() {
        console.log('📊 Setting up comprehensive monitoring...');

        // Monitor ALL console messages
        this.page.on('console', msg => {
            const text = msg.text();
            const timestamp = Date.now();
            
            // Log wheel-related messages
            if (text.includes('wheel') || text.includes('WHEEL') || text.includes('🎡') || 
                text.includes('🗑️') || text.includes('➕') || text.includes('segments') ||
                text.includes('color') || text.includes('grey') || text.includes('gray')) {
                console.log(`   🔍 [${new Date(timestamp).toISOString().substr(14, 9)}] ${text}`);
            }
        });

        // Monitor ALL network requests
        this.page.on('response', response => {
            const timestamp = Date.now();
            const url = response.url();
            const method = response.request().method();
            const status = response.status();
            
            if (url.includes('wheel') || url.includes('api/')) {
                console.log(`   📡 [${new Date(timestamp).toISOString().substr(14, 9)}] ${method} ${status} ${url}`);
                
                this.apiCalls.push({
                    timestamp,
                    method,
                    url,
                    status,
                    type: url.includes('wheel-items') ? 'WHEEL_ITEM_API' : 'OTHER_API'
                });
                
                // Capture response data for wheel-related APIs
                if (url.includes('wheel-items') && method === 'DELETE') {
                    response.json().then(data => {
                        console.log(`   📊 [${new Date(timestamp).toISOString().substr(14, 9)}] DELETE Response:`, JSON.stringify(data, null, 2));
                    }).catch(() => {});
                }
            }
        });

        // Monitor WebSocket messages by injecting monitoring code
        await this.page.evaluateOnNewDocument(() => {
            // Override WebSocket to monitor messages
            const originalWebSocket = window.WebSocket;
            window.WebSocket = function(url, protocols) {
                const ws = new originalWebSocket(url, protocols);
                
                const originalOnMessage = ws.onmessage;
                ws.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        const timestamp = Date.now();
                        
                        if (data.type === 'wheel_data') {
                            console.log(`🔌 [${new Date(timestamp).toISOString().substr(14, 9)}] WebSocket wheel_data received`);
                            console.log(`🔌 Wheel items: ${data.wheel?.items?.length || 0}`);
                            
                            // Store in global for analysis
                            window.websocketMessages = window.websocketMessages || [];
                            window.websocketMessages.push({
                                timestamp,
                                type: 'wheel_data',
                                itemCount: data.wheel?.items?.length || 0,
                                data: data
                            });
                        }
                    } catch (e) {
                        // Ignore parsing errors
                    }
                    
                    if (originalOnMessage) {
                        return originalOnMessage.call(this, event);
                    }
                };
                
                return ws;
            };
        });

        // Set up periodic wheel data monitoring
        setInterval(async () => {
            try {
                const wheelDataInfo = await this.page.evaluate(() => {
                    const appShell = document.querySelector('app-shell');
                    if (appShell && appShell.wheelData && appShell.wheelData.segments) {
                        return {
                            timestamp: Date.now(),
                            segmentCount: appShell.wheelData.segments.length,
                            segments: appShell.wheelData.segments.map(s => ({
                                id: s.id,
                                name: s.name || s.text,
                                color: s.color
                            }))
                        };
                    }
                    return null;
                });
                
                if (wheelDataInfo) {
                    const coloredCount = wheelDataInfo.segments.filter(s => s.color && s.color !== '#95A5A6').length;
                    const greyCount = wheelDataInfo.segments.length - coloredCount;
                    
                    // Check if this is a significant change
                    const lastEntry = this.wheelDataTimeline[this.wheelDataTimeline.length - 1];
                    const isSignificantChange = !lastEntry || 
                        lastEntry.segmentCount !== wheelDataInfo.segmentCount ||
                        lastEntry.coloredCount !== coloredCount ||
                        lastEntry.greyCount !== greyCount;
                    
                    if (isSignificantChange) {
                        const entry = {
                            ...wheelDataInfo,
                            coloredCount,
                            greyCount,
                            source: 'PERIODIC_CHECK'
                        };
                        
                        this.wheelDataTimeline.push(entry);
                        
                        const timeStr = new Date(wheelDataInfo.timestamp).toISOString().substr(14, 9);
                        if (greyCount > 0) {
                            console.log(`   ❌ [${timeStr}] GREY SEGMENTS: ${greyCount}/${wheelDataInfo.segmentCount} segments`);
                        } else {
                            console.log(`   ✅ [${timeStr}] All ${wheelDataInfo.segmentCount} segments colored`);
                        }
                    }
                }
            } catch (error) {
                // Ignore monitoring errors
            }
        }, 500); // Check every 500ms for rapid detection
        
        console.log('   ✅ Monitoring setup complete');
    }

    async setupAuthentication() {
        console.log('🔐 Setting up authentication...');
        
        try {
            // Handle login if needed
            const loginModal = await this.page.$('.modal');
            if (loginModal) {
                console.log('   🔑 Handling login...');
                const usernameInput = await this.page.$('input[type="text"], input[name="username"]');
                const passwordInput = await this.page.$('input[type="password"], input[name="password"]');
                const loginButton = await this.page.$('button[type="submit"], .btn-primary');
                
                if (usernameInput && passwordInput && loginButton) {
                    await usernameInput.type('admin');
                    await passwordInput.type('admin123');
                    await loginButton.click();
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    console.log('   ✅ Logged in');
                }
            }

            // Open debug panel
            console.log('   🐛 Opening debug panel...');
            await this.page.keyboard.down('Control');
            await this.page.keyboard.down('Shift');
            await this.page.keyboard.press('KeyD');
            await this.page.keyboard.up('Shift');
            await this.page.keyboard.up('Control');
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Select PhiPhi user
            const userSelect = await this.page.$('select[data-testid="user-select"]');
            if (userSelect) {
                await userSelect.selectOption('2'); // PhiPhi
                console.log('   ✅ Selected PhiPhi user');
            }

            // Select LLM
            const llmSelect = await this.page.$('select[data-testid="llm-select"]');
            if (llmSelect) {
                await llmSelect.selectOption('mistral-small-latest');
                console.log('   ✅ Selected LLM');
            }

            // Apply settings
            const applyButton = await this.page.$('button[data-testid="apply-settings"]');
            if (applyButton) {
                await applyButton.click();
                await new Promise(resolve => setTimeout(resolve, 2000));
                console.log('   ✅ Applied debug settings');
            }

        } catch (error) {
            console.log('   ⚠️ Authentication setup failed, continuing...');
        }
    }

    async waitForManualTesting() {
        console.log();
        console.log('👤 ===== RACE CONDITION DETECTION ACTIVE =====');
        console.log('🔍 Please reproduce the dual-wheel issue:');
        console.log('   1. Generate a wheel (via chat or button)');
        console.log('   2. Remove an item from the wheel');
        console.log('   3. Close any modals');
        console.log('   4. Observe if wheel changes to grey segments');
        console.log();
        console.log('📊 All wheel data changes, API calls, and WebSocket messages are being monitored');
        console.log('⏰ Timestamps show exact timing to detect race conditions');
        console.log();
        console.log('⏸️ Press Ctrl+C when done to see the race condition analysis');
        
        // Wait indefinitely for manual testing
        await new Promise(() => {});
    }

    analyzeRaceConditions() {
        console.log('📈 TIMELINE ANALYSIS:');
        
        // Combine all events into a single timeline
        const allEvents = [
            ...this.wheelDataTimeline.map(e => ({ ...e, eventType: 'WHEEL_DATA_CHANGE' })),
            ...this.apiCalls.map(e => ({ ...e, eventType: 'API_CALL' })),
        ];
        
        // Sort by timestamp
        allEvents.sort((a, b) => a.timestamp - b.timestamp);
        
        // Display timeline
        allEvents.forEach((event, index) => {
            const timeStr = new Date(event.timestamp).toISOString().substr(14, 9);
            
            if (event.eventType === 'WHEEL_DATA_CHANGE') {
                const status = event.greyCount > 0 ? '❌ GREY' : '✅ OK';
                console.log(`   ${index + 1}. [${timeStr}] WHEEL: ${event.segmentCount} segments (${event.coloredCount} colored, ${event.greyCount} grey) ${status}`);
            } else if (event.eventType === 'API_CALL') {
                console.log(`   ${index + 1}. [${timeStr}] API: ${event.method} ${event.url} (${event.status})`);
            }
        });
        
        console.log();
        console.log('🔍 RACE CONDITION ANALYSIS:');
        
        // Look for patterns
        const removeApiCalls = this.apiCalls.filter(call => 
            call.url.includes('wheel-items') && call.method === 'DELETE'
        );
        
        if (removeApiCalls.length > 0) {
            console.log(`   📊 Found ${removeApiCalls.length} wheel item removal API calls`);
            
            removeApiCalls.forEach((apiCall, index) => {
                console.log(`   🗑️ Removal ${index + 1}: ${new Date(apiCall.timestamp).toISOString().substr(14, 9)}`);
                
                // Find wheel data changes within 5 seconds after this API call
                const subsequentChanges = this.wheelDataTimeline.filter(change => 
                    change.timestamp > apiCall.timestamp && 
                    change.timestamp < apiCall.timestamp + 5000
                );
                
                if (subsequentChanges.length > 0) {
                    console.log(`      📈 ${subsequentChanges.length} wheel data changes within 5 seconds:`);
                    subsequentChanges.forEach((change, changeIndex) => {
                        const delay = change.timestamp - apiCall.timestamp;
                        const status = change.greyCount > 0 ? '❌ GREY' : '✅ OK';
                        console.log(`         ${changeIndex + 1}. +${delay}ms: ${change.segmentCount} segments ${status}`);
                    });
                    
                    // Check if any change introduced grey segments
                    const greyIntroduced = subsequentChanges.find(change => change.greyCount > 0);
                    if (greyIntroduced) {
                        const delay = greyIntroduced.timestamp - apiCall.timestamp;
                        console.log(`      ❌ RACE CONDITION DETECTED: Grey segments appeared ${delay}ms after API call!`);
                    }
                } else {
                    console.log(`      📊 No wheel data changes detected within 5 seconds`);
                }
            });
        } else {
            console.log('   📊 No wheel item removal API calls detected');
        }
        
        // Check for grey segments
        const greyEvents = this.wheelDataTimeline.filter(event => event.greyCount > 0);
        if (greyEvents.length > 0) {
            console.log(`   ❌ GREY SEGMENTS DETECTED: ${greyEvents.length} times`);
            greyEvents.forEach((event, index) => {
                const timeStr = new Date(event.timestamp).toISOString().substr(14, 9);
                console.log(`      ${index + 1}. [${timeStr}] ${event.greyCount}/${event.segmentCount} segments grey`);
            });
        } else {
            console.log('   ✅ No grey segments detected during monitoring');
        }
    }
}

// Run the race condition detector
const port = process.argv[2] || 3002;
const detector = new RaceConditionDetector(port);
detector.runDetection().catch(console.error);
