#!/usr/bin/env node

/**
 * Browser Dashboard Tester
 * 
 * Simulates browser-like behavior to test the WebSocket connection dashboard UI.
 * This tool behaves exactly like a browser would, testing:
 * - Dashboard page loading
 * - WebSocket connections
 * - Real-time updates
 * - UI interactions
 * - Message flow validation
 */

import WebSocket from 'ws';
import { spawn } from 'child_process';
import fs from 'fs';
import path from 'path';

// Configuration
const CONFIG = {
  dashboard: {
    url: 'http://localhost:8000/admin/admin_tools/connection_dashboard/',
    websocketUrl: 'ws://localhost:8000/ws/connection-monitor/',
    gameWebsocketUrl: 'ws://localhost:8000/ws/game/'
  },
  users: {
    real: { id: '1', name: 'Test User', is_real: true },
    fake: { id: '2', name: 'Phi<PERSON>hi', is_real: false }
  },
  testing: {
    maxTestDuration: 120000, // 2 minutes
    celeryLogInterval: 3000, // Check logs every 3 seconds
    messageTimeout: 15000,
    uiUpdateTimeout: 5000
  }
};

class BrowserDashboardTester {
  constructor() {
    this.adminSocket = null;
    this.clientSockets = [];
    this.messageLog = [];
    this.celeryLogProcess = null;
    this.uiState = {
      connectionsDisplayed: 0,
      lastUpdate: null,
      messagesReceived: 0,
      errorsDetected: []
    };
    this.testResults = {
      dashboardConnectivity: false,
      connectionTracking: false,
      realTimeUpdates: false,
      messageInspector: false,
      sessionFocus: false,
      errorHandling: false,
      celeryIssues: [],
      uiResponsiveness: false
    };
    this.startTime = Date.now();
  }

  async runBrowserLikeTest() {
    console.log('🌐 Browser Dashboard Comprehensive Test');
    console.log('=======================================\n');

    try {
      // Start monitoring
      this.startCeleryLogMonitoring();

      // Phase 1: Simulate browser opening dashboard
      await this.simulateBrowserDashboardAccess();

      // Phase 2: Test WebSocket connections like a browser would
      await this.testWebSocketConnections();

      // Phase 3: Simulate user interactions
      await this.simulateUserInteractions();

      // Phase 4: Test real-time features
      await this.testRealTimeFeatures();

      // Phase 5: Test error scenarios
      await this.testErrorScenarios();

      // Generate comprehensive report
      this.generateBrowserTestReport();

    } catch (error) {
      console.error('❌ Browser test failed:', error.message);
      this.testResults.error = error.message;
    } finally {
      this.cleanup();
    }
  }

  startCeleryLogMonitoring() {
    console.log('📋 Starting Celery log monitoring (browser-style)...');
    
    this.celeryLogProcess = spawn('docker', ['logs', '-f', '--tail', '20', 'backend-celery-1'], {
      stdio: ['ignore', 'pipe', 'pipe']
    });

    this.celeryLogProcess.stdout.on('data', (data) => {
      const logLine = data.toString();
      this.analyzeCeleryLogForUIIssues(logLine);
    });

    this.celeryLogProcess.stderr.on('data', (data) => {
      const logLine = data.toString();
      this.analyzeCeleryLogForUIIssues(logLine);
    });

    console.log('✅ Celery monitoring started (browser-focused)\n');
  }

  analyzeCeleryLogForUIIssues(logLine) {
    // Focus on issues that would affect the dashboard UI
    const uiCriticalPatterns = [
      /Invalid user_profile_id format/,
      /Error recording workflow history/,
      /Connection refused/,
      /WebSocket.*error/,
      /Redis.*error/,
      /Database.*error/,
      /AttributeError.*object has no attribute/
    ];

    for (const pattern of uiCriticalPatterns) {
      if (pattern.test(logLine)) {
        const issue = {
          timestamp: new Date().toISOString(),
          category: 'ui_critical',
          message: logLine.trim(),
          impact: 'Dashboard functionality may be affected'
        };
        this.testResults.celeryIssues.push(issue);
        console.log(`🚨 UI-CRITICAL ISSUE: ${issue.message.substring(0, 100)}...`);
      }
    }
  }

  async simulateBrowserDashboardAccess() {
    console.log('📋 Phase 1: Simulating Browser Dashboard Access');
    console.log('------------------------------------------------');

    // Simulate browser connecting to admin dashboard WebSocket
    console.log('🌐 Browser: Opening dashboard page...');
    console.log(`🔗 Browser: Connecting to ${CONFIG.dashboard.websocketUrl}`);

    return new Promise((resolve, reject) => {
      this.adminSocket = new WebSocket(CONFIG.dashboard.websocketUrl);
      
      this.adminSocket.on('open', () => {
        console.log('✅ Browser: Dashboard WebSocket connected');
        this.testResults.dashboardConnectivity = true;
        
        // Simulate browser requesting initial data (like the dashboard would)
        console.log('📤 Browser: Requesting initial dashboard data...');
        this.adminSocket.send(JSON.stringify({ type: 'get_connections' }));
        this.adminSocket.send(JSON.stringify({ type: 'get_system_health' }));
        this.adminSocket.send(JSON.stringify({ type: 'get_message_stats' }));
        
        resolve();
      });

      this.adminSocket.on('error', (error) => {
        console.log('❌ Browser: Dashboard connection failed:', error.message);
        this.testResults.dashboardConnectivity = false;
        reject(error);
      });

      this.adminSocket.on('message', (data) => {
        this.handleDashboardMessage(data);
      });

      // Timeout
      setTimeout(() => {
        if (!this.testResults.dashboardConnectivity) {
          reject(new Error('Dashboard connection timeout'));
        }
      }, CONFIG.testing.messageTimeout);
    });
  }

  handleDashboardMessage(data) {
    try {
      const message = JSON.parse(data);
      this.messageLog.push({
        timestamp: Date.now(),
        direction: 'dashboard_received',
        type: message.type,
        message: message
      });

      // Simulate browser UI updates based on message type
      switch (message.type) {
        case 'connection_data':
          this.simulateConnectionListUpdate(message.data);
          break;
        case 'system_health':
          this.simulateSystemHealthUpdate(message.data);
          break;
        case 'message_stats':
          this.simulateMessageStatsUpdate(message.data);
          break;
        case 'message_flow':
          this.simulateMessageInspectorUpdate(message.data);
          break;
      }

      this.uiState.messagesReceived++;
      this.uiState.lastUpdate = new Date().toISOString();

    } catch (error) {
      console.log('❌ Browser: Failed to parse dashboard message:', error.message);
      this.uiState.errorsDetected.push({
        type: 'parse_error',
        message: error.message,
        timestamp: Date.now()
      });
    }
  }

  simulateConnectionListUpdate(connections) {
    console.log(`🔄 Browser UI: Updating connection list (${connections.length} connections)`);
    this.uiState.connectionsDisplayed = connections.length;
    
    // Simulate browser checking for expected UI elements
    if (connections.length > 0) {
      console.log('✅ Browser UI: Connection list populated');
      this.testResults.connectionTracking = true;
      
      // Log connection details like the browser would display
      connections.forEach((conn, index) => {
        console.log(`   ${index + 1}. User ${conn.user_id} - Session ${conn.session_id.substring(0, 8)}... (${conn.duration})`);
      });
    } else {
      console.log('⚠️ Browser UI: Connection list is empty');
    }
  }

  simulateSystemHealthUpdate(health) {
    console.log('🔄 Browser UI: Updating system health indicators');
    // Simulate browser updating health status indicators
    if (health.redis) {
      console.log(`   Redis: ${health.redis}`);
    }
    if (health.celery_workers) {
      console.log(`   Celery: ${health.celery_workers}`);
    }
  }

  simulateMessageStatsUpdate(stats) {
    console.log('🔄 Browser UI: Updating message statistics');
    console.log(`   Messages/min: ${stats.messages_per_minute || 0}`);
    console.log(`   Active workflows: ${stats.active_workflows || 0}`);
    console.log(`   Error rate: ${stats.error_rate || 0}%`);
  }

  simulateMessageInspectorUpdate(messageData) {
    console.log('🔄 Browser UI: Message inspector received new data');
    this.testResults.messageInspector = true;
  }

  async testWebSocketConnections() {
    console.log('\n📋 Phase 2: Testing WebSocket Connections (Browser-style)');
    console.log('----------------------------------------------------------');

    // Simulate browser creating game WebSocket connections
    console.log('🌐 Browser: User opens game interface...');
    
    const clientSocket = new WebSocket(CONFIG.dashboard.gameWebsocketUrl);
    
    return new Promise((resolve) => {
      clientSocket.on('open', () => {
        console.log('✅ Browser: Game WebSocket connected');
        
        // Simulate user sending a message (like typing in chat)
        const userMessage = {
          type: 'chat_message',
          content: {
            message: 'Browser test: Hello from simulated user',
            user_profile_id: CONFIG.users.fake.id,
            timestamp: new Date().toISOString(),
            metadata: {
              requested_workflow: 'discussion'
            }
          }
        };
        
        console.log('📤 Browser: User sends chat message...');
        clientSocket.send(JSON.stringify(userMessage));
        this.messageLog.push({
          timestamp: Date.now(),
          direction: 'game_sent',
          message: userMessage
        });
      });

      clientSocket.on('message', (data) => {
        try {
          const message = JSON.parse(data);
          console.log(`📨 Browser: Game received ${message.type}`);
          this.messageLog.push({
            timestamp: Date.now(),
            direction: 'game_received',
            type: message.type,
            message: message
          });
        } catch (error) {
          console.log('❌ Browser: Failed to parse game message:', error.message);
        }
      });

      clientSocket.on('error', (error) => {
        console.log('❌ Browser: Game WebSocket error:', error.message);
      });

      this.clientSockets.push(clientSocket);
      
      // Wait for potential responses
      setTimeout(() => {
        resolve();
      }, 8000);
    });
  }

  async simulateUserInteractions() {
    console.log('\n📋 Phase 3: Simulating User Interactions');
    console.log('----------------------------------------');

    // Simulate user clicking refresh button
    console.log('🖱️ Browser: User clicks "Refresh Data" button...');
    if (this.adminSocket && this.adminSocket.readyState === WebSocket.OPEN) {
      this.adminSocket.send(JSON.stringify({ type: 'get_connections' }));
    }

    // Wait for UI update
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Simulate user enabling message inspector
    console.log('🖱️ Browser: User clicks "Message Inspector" button...');
    if (this.adminSocket && this.adminSocket.readyState === WebSocket.OPEN) {
      this.adminSocket.send(JSON.stringify({ type: 'start_message_monitoring' }));
    }

    // Wait for response
    await new Promise(resolve => setTimeout(resolve, 2000));

    console.log('✅ Browser: User interactions simulated');
    this.testResults.uiResponsiveness = true;
  }

  async testRealTimeFeatures() {
    console.log('\n📋 Phase 4: Testing Real-time Features');
    console.log('--------------------------------------');

    const initialMessageCount = this.uiState.messagesReceived;
    
    // Create additional client to trigger real-time updates
    console.log('🔄 Browser: Simulating new user connection...');
    const newClientSocket = new WebSocket(CONFIG.dashboard.gameWebsocketUrl);
    
    return new Promise((resolve) => {
      newClientSocket.on('open', () => {
        console.log('✅ Browser: New user connected');
        
        // Send message to trigger dashboard update
        const message = {
          type: 'chat_message',
          content: {
            message: 'Real-time test message',
            user_profile_id: CONFIG.users.real.id,
            timestamp: new Date().toISOString()
          }
        };
        
        newClientSocket.send(JSON.stringify(message));
        this.clientSockets.push(newClientSocket);
        
        // Check if dashboard receives real-time updates
        setTimeout(() => {
          const newMessageCount = this.uiState.messagesReceived;
          if (newMessageCount > initialMessageCount) {
            console.log('✅ Browser: Real-time updates working');
            this.testResults.realTimeUpdates = true;
          } else {
            console.log('⚠️ Browser: Real-time updates may not be working');
            this.testResults.realTimeUpdates = false;
          }
          resolve();
        }, 5000);
      });

      newClientSocket.on('error', (error) => {
        console.log('❌ Browser: New client connection failed:', error.message);
        resolve();
      });
    });
  }

  async testErrorScenarios() {
    console.log('\n📋 Phase 5: Testing Error Scenarios');
    console.log('-----------------------------------');

    // Test invalid message handling
    if (this.clientSockets.length > 0) {
      console.log('🧪 Browser: Sending invalid message to test error handling...');
      const invalidMessage = {
        type: 'invalid_test_message',
        content: { test: 'invalid data' }
      };
      
      this.clientSockets[0].send(JSON.stringify(invalidMessage));
    }

    // Wait for potential error responses
    await new Promise(resolve => setTimeout(resolve, 3000));

    const errorMessages = this.messageLog.filter(log => 
      log.message && log.message.type === 'error'
    );

    if (errorMessages.length > 0 || this.uiState.errorsDetected.length > 0) {
      console.log('✅ Browser: Error handling detected');
      this.testResults.errorHandling = true;
    } else {
      console.log('⚠️ Browser: No error handling observed');
      this.testResults.errorHandling = false;
    }
  }

  generateBrowserTestReport() {
    console.log('\n🌐 BROWSER DASHBOARD TEST REPORT');
    console.log('================================');
    
    const duration = Date.now() - this.startTime;
    console.log(`⏱️ Test Duration: ${Math.round(duration / 1000)}s`);
    console.log(`📨 Total Messages: ${this.messageLog.length}`);
    console.log(`🔄 UI Updates: ${this.uiState.messagesReceived}`);
    console.log(`🚨 Celery Issues: ${this.testResults.celeryIssues.length}`);
    console.log(`⚠️ UI Errors: ${this.uiState.errorsDetected.length}`);
    
    console.log('\n🔍 Browser Test Results:');
    console.log(`  Dashboard Connectivity: ${this.testResults.dashboardConnectivity ? '✅' : '❌'}`);
    console.log(`  Connection Tracking: ${this.testResults.connectionTracking ? '✅' : '❌'}`);
    console.log(`  Real-time Updates: ${this.testResults.realTimeUpdates ? '✅' : '❌'}`);
    console.log(`  Message Inspector: ${this.testResults.messageInspector ? '✅' : '❌'}`);
    console.log(`  UI Responsiveness: ${this.testResults.uiResponsiveness ? '✅' : '❌'}`);
    console.log(`  Error Handling: ${this.testResults.errorHandling ? '✅' : '❌'}`);

    // Calculate overall score
    const totalTests = 6;
    const passedTests = Object.values(this.testResults).filter(result => result === true).length;
    const score = Math.round((passedTests / totalTests) * 100);
    
    console.log(`\n📊 Overall Score: ${score}% (${passedTests}/${totalTests} tests passed)`);

    if (this.testResults.celeryIssues.length > 0) {
      console.log('\n🚨 Critical Backend Issues Affecting UI:');
      this.testResults.celeryIssues.slice(0, 5).forEach((issue, index) => {
        console.log(`  ${index + 1}. ${issue.message.substring(0, 80)}...`);
      });
    }

    // Save detailed report
    this.saveBrowserTestReport(duration, score);
  }

  saveBrowserTestReport(duration, score) {
    const reportPath = path.join('logs', `browser-dashboard-test-${Date.now()}.json`);
    const report = {
      timestamp: new Date().toISOString(),
      testType: 'browser_dashboard_simulation',
      duration,
      score,
      results: this.testResults,
      uiState: this.uiState,
      messageLog: this.messageLog.slice(-50), // Last 50 messages
      summary: {
        totalMessages: this.messageLog.length,
        uiUpdates: this.uiState.messagesReceived,
        celeryIssues: this.testResults.celeryIssues.length,
        uiErrors: this.uiState.errorsDetected.length
      }
    };

    try {
      if (!fs.existsSync('logs')) {
        fs.mkdirSync('logs');
      }
      fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
      console.log(`\n💾 Browser test report saved: ${reportPath}`);
    } catch (error) {
      console.log(`❌ Failed to save browser test report: ${error.message}`);
    }
  }

  cleanup() {
    console.log('\n🧹 Browser: Cleaning up connections...');
    
    // Close all WebSocket connections
    if (this.adminSocket) {
      this.adminSocket.close();
    }
    
    this.clientSockets.forEach(socket => {
      if (socket.readyState === WebSocket.OPEN) {
        socket.close();
      }
    });

    // Stop Celery monitoring
    if (this.celeryLogProcess) {
      this.celeryLogProcess.kill();
    }

    console.log('✅ Browser: Cleanup completed');
  }
}

// Run the browser-like test
const tester = new BrowserDashboardTester();
tester.runBrowserLikeTest().then(() => {
  process.exit(0);
}).catch((error) => {
  console.error('❌ Browser test failed:', error);
  process.exit(1);
});

// Auto-exit after max duration
setTimeout(() => {
  console.log('\n⏰ Browser test maximum duration reached - exiting');
  process.exit(1);
}, CONFIG.testing.maxTestDuration);
