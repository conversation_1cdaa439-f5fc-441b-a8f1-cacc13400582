/**
 * End-to-End Integration Test
 * 
 * Comprehensive testing of complete wheel generation flow:
 * Frontend Request → Backend Processing → Database Storage → Frontend Display
 */

console.log('🔄 End-to-End Integration Test');
console.log('==============================');

// Test configuration
const E2E_CONFIG = {
    testTimeout: 60000, // 60 seconds for complete flow
    energyLevel: 100,   // High energy for physical activity testing
    timeAvailable: 10,  // 10 minutes
    expectedMinItems: 5,
    expectedMaxItems: 8,
    expectedMinDomains: 3,
    expectedPhysicalPercentage: 0.7 // 70% for 100% energy
};

// Test state tracking
let e2eResults = {
    startTime: null,
    phases: {
        frontendRequest: { completed: false, timestamp: null, duration: null },
        backendProcessing: { completed: false, timestamp: null, duration: null },
        databaseStorage: { completed: false, timestamp: null, duration: null },
        websocketTransmission: { completed: false, timestamp: null, duration: null },
        frontendDisplay: { completed: false, timestamp: null, duration: null }
    },
    wheelData: null,
    qualityMetrics: {},
    errors: []
};

/**
 * Main end-to-end test execution
 */
async function runEndToEndIntegrationTest() {
    try {
        console.log('🚀 Starting end-to-end integration test...');
        e2eResults.startTime = Date.now();
        
        // Phase 1: Frontend Request
        await testFrontendRequest();
        
        // Phase 2: Backend Processing (monitored via WebSocket)
        await testBackendProcessing();
        
        // Phase 3: Database Storage (verified via API)
        await testDatabaseStorage();
        
        // Phase 4: WebSocket Transmission
        await testWebSocketTransmission();
        
        // Phase 5: Frontend Display
        await testFrontendDisplay();
        
        // Final validation
        await validateEndToEndFlow();
        
        // Generate comprehensive report
        generateE2EReport();
        
    } catch (error) {
        console.error('❌ End-to-end test failed:', error);
        e2eResults.errors.push({
            phase: 'general',
            error: error.message,
            timestamp: Date.now()
        });
        generateE2EReport();
    }
}

/**
 * Phase 1: Test frontend request initiation
 */
async function testFrontendRequest() {
    console.log('\n📤 Phase 1: Frontend Request');
    const phaseStart = Date.now();
    
    try {
        const appShell = document.querySelector('app-shell');
        if (!appShell) {
            throw new Error('App-shell component not found');
        }
        
        // Set test parameters
        appShell.energyLevel = E2E_CONFIG.energyLevel;
        appShell.timeAvailable = E2E_CONFIG.timeAvailable;
        
        console.log(`⚡ Energy: ${E2E_CONFIG.energyLevel}%`);
        console.log(`⏰ Time: ${E2E_CONFIG.timeAvailable} minutes`);
        
        // Monitor WebSocket messages to track request
        const wsManager = appShell.websocketManager;
        let requestSent = false;
        
        // Override send method to monitor outgoing messages
        const originalSend = wsManager.send.bind(wsManager);
        wsManager.send = function(message) {
            console.log('📤 Outgoing WebSocket message:', message);
            if (message.type === 'chat_message' && 
                (message.content?.message?.includes('wheel') || 
                 message.content?.metadata?.forced_wheel_generation)) {
                requestSent = true;
                console.log('✅ Wheel generation request detected');
            }
            return originalSend(message);
        };
        
        // Trigger wheel generation
        const generateButton = appShell.shadowRoot?.querySelector('.generate-wheel-button');
        if (generateButton) {
            generateButton.click();
        } else if (appShell.generateWheel) {
            await appShell.generateWheel();
        } else {
            throw new Error('Could not trigger wheel generation');
        }
        
        // Wait for request to be sent
        await new Promise((resolve, reject) => {
            const timeout = setTimeout(() => reject(new Error('Request timeout')), 5000);
            const checkInterval = setInterval(() => {
                if (requestSent) {
                    clearInterval(checkInterval);
                    clearTimeout(timeout);
                    resolve();
                }
            }, 100);
        });
        
        e2eResults.phases.frontendRequest = {
            completed: true,
            timestamp: Date.now(),
            duration: Date.now() - phaseStart
        };
        
        console.log('✅ Frontend request phase completed');
        
    } catch (error) {
        e2eResults.errors.push({
            phase: 'frontendRequest',
            error: error.message,
            timestamp: Date.now()
        });
        throw error;
    }
}

/**
 * Phase 2: Monitor backend processing
 */
async function testBackendProcessing() {
    console.log('\n⚙️ Phase 2: Backend Processing');
    const phaseStart = Date.now();
    
    try {
        const appShell = document.querySelector('app-shell');
        const wsManager = appShell.websocketManager;
        
        let processingStarted = false;
        let processingCompleted = false;
        
        // Monitor processing status messages
        const originalOnMessage = wsManager.onMessage.bind(wsManager);
        wsManager.onMessage = function(type, handler) {
            if (type === 'processing_status') {
                return originalOnMessage(type, (data) => {
                    console.log('⚙️ Processing status:', data);
                    if (data.status === 'processing' || data.status === 'started') {
                        processingStarted = true;
                    } else if (data.status === 'completed') {
                        processingCompleted = true;
                    }
                    handler(data);
                });
            }
            return originalOnMessage(type, handler);
        };
        
        // Wait for processing to complete
        await new Promise((resolve, reject) => {
            const timeout = setTimeout(() => reject(new Error('Backend processing timeout')), 45000);
            const checkInterval = setInterval(() => {
                if (processingCompleted) {
                    clearInterval(checkInterval);
                    clearTimeout(timeout);
                    resolve();
                }
            }, 500);
        });
        
        e2eResults.phases.backendProcessing = {
            completed: true,
            timestamp: Date.now(),
            duration: Date.now() - phaseStart
        };
        
        console.log('✅ Backend processing phase completed');
        
    } catch (error) {
        e2eResults.errors.push({
            phase: 'backendProcessing',
            error: error.message,
            timestamp: Date.now()
        });
        throw error;
    }
}

/**
 * Phase 3: Verify database storage
 */
async function testDatabaseStorage() {
    console.log('\n💾 Phase 3: Database Storage');
    const phaseStart = Date.now();
    
    try {
        // Wait a moment for database operations to complete
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Check if wheel data includes database IDs
        const appShell = document.querySelector('app-shell');
        const wheelData = appShell.wheelData;
        
        if (!wheelData) {
            throw new Error('No wheel data available for database verification');
        }
        
        // Check for database indicators
        const hasDatabaseId = wheelData.wheelId || wheelData.id;
        const hasWheelItemIds = wheelData.segments?.every(s => s.id && s.activity_tailored_id);
        
        if (!hasDatabaseId) {
            console.log('⚠️ No database wheel ID found');
        }
        
        if (!hasWheelItemIds) {
            console.log('⚠️ Some wheel items missing database IDs');
        }
        
        console.log(`📊 Wheel ID: ${hasDatabaseId || 'N/A'}`);
        console.log(`📊 Items with IDs: ${wheelData.segments?.filter(s => s.id).length || 0}/${wheelData.segments?.length || 0}`);
        
        e2eResults.phases.databaseStorage = {
            completed: true,
            timestamp: Date.now(),
            duration: Date.now() - phaseStart,
            hasDatabaseId,
            hasWheelItemIds
        };
        
        console.log('✅ Database storage phase completed');
        
    } catch (error) {
        e2eResults.errors.push({
            phase: 'databaseStorage',
            error: error.message,
            timestamp: Date.now()
        });
        throw error;
    }
}

/**
 * Phase 4: Test WebSocket transmission
 */
async function testWebSocketTransmission() {
    console.log('\n📡 Phase 4: WebSocket Transmission');
    const phaseStart = Date.now();
    
    try {
        const appShell = document.querySelector('app-shell');
        const wsManager = appShell.websocketManager;
        
        let wheelDataReceived = false;
        let receivedWheelData = null;
        
        // Monitor wheel_data messages
        const originalOnMessage = wsManager.onMessage.bind(wsManager);
        wsManager.onMessage = function(type, handler) {
            if (type === 'wheel_data') {
                return originalOnMessage(type, (data) => {
                    console.log('📡 Wheel data received via WebSocket');
                    wheelDataReceived = true;
                    receivedWheelData = data;
                    e2eResults.wheelData = data;
                    handler(data);
                });
            }
            return originalOnMessage(type, handler);
        };
        
        // Wait for wheel data (it might already be received)
        if (appShell.wheelData && appShell.wheelData.segments) {
            wheelDataReceived = true;
            receivedWheelData = appShell.wheelData;
            e2eResults.wheelData = appShell.wheelData;
        } else {
            await new Promise((resolve, reject) => {
                const timeout = setTimeout(() => reject(new Error('WebSocket transmission timeout')), 10000);
                const checkInterval = setInterval(() => {
                    if (wheelDataReceived) {
                        clearInterval(checkInterval);
                        clearTimeout(timeout);
                        resolve();
                    }
                }, 500);
            });
        }
        
        // Validate received data structure
        if (receivedWheelData) {
            const hasValidStructure = receivedWheelData.segments && 
                                    Array.isArray(receivedWheelData.segments) &&
                                    receivedWheelData.segments.length > 0;
            
            if (!hasValidStructure) {
                throw new Error('Invalid wheel data structure received');
            }
            
            console.log(`📊 Received ${receivedWheelData.segments.length} wheel segments`);
        }
        
        e2eResults.phases.websocketTransmission = {
            completed: true,
            timestamp: Date.now(),
            duration: Date.now() - phaseStart,
            dataReceived: wheelDataReceived,
            segmentCount: receivedWheelData?.segments?.length || 0
        };
        
        console.log('✅ WebSocket transmission phase completed');
        
    } catch (error) {
        e2eResults.errors.push({
            phase: 'websocketTransmission',
            error: error.message,
            timestamp: Date.now()
        });
        throw error;
    }
}

/**
 * Phase 5: Test frontend display
 */
async function testFrontendDisplay() {
    console.log('\n🎨 Phase 5: Frontend Display');
    const phaseStart = Date.now();
    
    try {
        const appShell = document.querySelector('app-shell');
        const gameWheel = appShell.shadowRoot?.querySelector('game-wheel');
        
        if (!gameWheel) {
            throw new Error('Game wheel component not found');
        }
        
        // Check wheel state machine
        const wheelState = appShell.wheelStateMachine?.getState();
        const isPopulated = wheelState === 'POPULATED';
        
        // Check wheel data
        const hasWheelData = gameWheel.wheelData && gameWheel.wheelData.segments;
        const segmentCount = gameWheel.wheelData?.segments?.length || 0;
        
        // Check rendering
        const canvas = gameWheel.shadowRoot?.querySelector('canvas');
        const isRendered = !!canvas;
        
        console.log(`🎡 Wheel state: ${wheelState}`);
        console.log(`📊 Segments: ${segmentCount}`);
        console.log(`🖼️ Rendered: ${isRendered}`);
        
        if (!isPopulated || !hasWheelData || !isRendered) {
            throw new Error('Frontend display incomplete');
        }
        
        e2eResults.phases.frontendDisplay = {
            completed: true,
            timestamp: Date.now(),
            duration: Date.now() - phaseStart,
            wheelState,
            segmentCount,
            isRendered
        };
        
        console.log('✅ Frontend display phase completed');
        
    } catch (error) {
        e2eResults.errors.push({
            phase: 'frontendDisplay',
            error: error.message,
            timestamp: Date.now()
        });
        throw error;
    }
}

/**
 * Validate complete end-to-end flow
 */
async function validateEndToEndFlow() {
    console.log('\n🔍 Final Validation');
    
    if (!e2eResults.wheelData || !e2eResults.wheelData.segments) {
        throw new Error('No wheel data available for validation');
    }
    
    const segments = e2eResults.wheelData.segments;
    
    // Quality metrics
    const domains = new Set(segments.map(s => s.domain || 'unknown'));
    const physicalSegments = segments.filter(s => (s.domain || '').startsWith('phys'));
    
    e2eResults.qualityMetrics = {
        totalSegments: segments.length,
        domainDiversity: domains.size,
        domains: Array.from(domains),
        physicalCount: physicalSegments.length,
        physicalPercentage: physicalSegments.length / segments.length,
        itemCountValid: segments.length >= E2E_CONFIG.expectedMinItems && segments.length <= E2E_CONFIG.expectedMaxItems,
        domainDiversityValid: domains.size >= E2E_CONFIG.expectedMinDomains,
        physicalDistributionValid: (physicalSegments.length / segments.length) >= E2E_CONFIG.expectedPhysicalPercentage
    };
    
    console.log('✅ End-to-end flow validation completed');
}

/**
 * Generate comprehensive E2E test report
 */
function generateE2EReport() {
    console.log('\n📋 END-TO-END INTEGRATION TEST REPORT');
    console.log('====================================');
    
    const totalDuration = Date.now() - e2eResults.startTime;
    
    // Phase completion status
    console.log('\n🔄 Phase Completion:');
    Object.entries(e2eResults.phases).forEach(([phase, data]) => {
        const status = data.completed ? '✅' : '❌';
        const duration = data.duration ? `(${data.duration}ms)` : '';
        console.log(`  ${phase}: ${status} ${duration}`);
    });
    
    // Quality metrics
    if (e2eResults.qualityMetrics.totalSegments) {
        const metrics = e2eResults.qualityMetrics;
        console.log('\n📊 Quality Metrics:');
        console.log(`  Total Segments: ${metrics.totalSegments} ${metrics.itemCountValid ? '✅' : '❌'}`);
        console.log(`  Domain Diversity: ${metrics.domainDiversity} ${metrics.domainDiversityValid ? '✅' : '❌'}`);
        console.log(`  Physical Activities: ${metrics.physicalCount}/${metrics.totalSegments} (${(metrics.physicalPercentage * 100).toFixed(1)}%) ${metrics.physicalDistributionValid ? '✅' : '❌'}`);
        console.log(`  Domains: ${metrics.domains.join(', ')}`);
    }
    
    // Errors
    if (e2eResults.errors.length > 0) {
        console.log('\n❌ Errors:');
        e2eResults.errors.forEach(error => {
            console.log(`  [${error.phase}] ${error.error}`);
        });
    }
    
    // Overall result
    const allPhasesCompleted = Object.values(e2eResults.phases).every(phase => phase.completed);
    const qualityValid = e2eResults.qualityMetrics.itemCountValid && 
                        e2eResults.qualityMetrics.domainDiversityValid;
    const noErrors = e2eResults.errors.length === 0;
    
    const overallSuccess = allPhasesCompleted && qualityValid && noErrors;
    
    console.log('\n🎯 OVERALL RESULT:');
    console.log(`${overallSuccess ? '✅ END-TO-END INTEGRATION TEST PASSED' : '❌ END-TO-END INTEGRATION TEST FAILED'}`);
    console.log(`Total Duration: ${totalDuration}ms`);
    
    return overallSuccess;
}

// Export for manual execution
if (typeof window !== 'undefined') {
    window.runEndToEndIntegrationTest = runEndToEndIntegrationTest;
}

// Auto-run test when script is loaded
if (typeof window !== 'undefined') {
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(runEndToEndIntegrationTest, 2000);
        });
    } else {
        setTimeout(runEndToEndIntegrationTest, 2000);
    }
}
