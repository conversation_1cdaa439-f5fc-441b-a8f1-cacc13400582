#!/usr/bin/env node

/**
 * Robust Profile Completion Test
 * 
 * This test uses the new robust testing framework to reliably test
 * profile completion workflow with enhanced error handling and debugging.
 * 
 * Usage: node test-profile-completion-robust.cjs [port]
 * Example: node test-profile-completion-robust.cjs 3001
 */

const { TestingFramework } = require('./testing-framework.cjs');

async function testProfileCompletion(port = 3001) {
    const testId = Math.random().toString(36).substring(7);
    console.log('🚀 Robust Profile Completion Test');
    console.log(`   Port: ${port}`);
    console.log(`   URL: http://localhost:${port}`);
    console.log(`   Test ID: ${testId}`);
    
    const framework = new TestingFramework({
        headless: false,
        slowMo: 500,
        timeout: 30000
    });
    
    const results = {
        phases: [],
        totalDuration: 0,
        success: false,
        issues: []
    };
    
    const startTime = Date.now();
    
    try {
        // Phase 1: Initialize and Setup
        console.log('\n📱 Phase 1: Application setup and initialization');
        const phaseStartTime = Date.now();
        
        const page = await framework.initialize(`http://localhost:${port}`);
        console.log('   ✓ Application loaded');
        
        // Setup debug panel
        const debugPanelSuccess = await framework.setupDebugPanel();
        if (debugPanelSuccess) {
            console.log('   ✓ Debug panel opened');
        } else {
            console.log('   ⚠️  Debug panel setup failed, continuing');
            results.issues.push('Debug panel setup failed');
        }
        
        // Select User 191 (our test user with profile gaps)
        const userSelectionSuccess = await framework.selectUser('191');
        if (userSelectionSuccess) {
            console.log('   ✓ User 191 selected (test user with profile gaps)');
        } else {
            console.log('   ⚠️  User selection failed, using default');
            results.issues.push('User 191 selection failed');
        }
        
        // Select LLM configuration
        const llmSelectionSuccess = await framework.selectLLM('mistral-small-latest');
        if (llmSelectionSuccess) {
            console.log('   ✓ LLM configuration applied');
        } else {
            console.log('   ⚠️  LLM configuration failed, using default');
            results.issues.push('LLM configuration failed');
        }
        
        const phase1Duration = Date.now() - phaseStartTime;
        results.phases.push({
            name: 'APPLICATION SETUP',
            duration: phase1Duration,
            success: true
        });
        console.log(`   ✓ Application setup completed in ${phase1Duration}ms`);
        
        // Phase 2: Profile Completion Workflow Test
        console.log('\n🎯 Phase 2: Profile completion workflow test');
        const phase2StartTime = Date.now();
        
        // Send the wheel request message
        const messageSuccess = await framework.sendMessage('make me a wheel');
        if (!messageSuccess) {
            throw new Error('Failed to send message');
        }
        console.log('   ✓ Message "make me a wheel" sent');
        
        // Wait for response with hanging detection
        console.log('   ⏳ Waiting for assistant response...');
        const responseSuccess = await framework.waitForResponse(30000);
        
        const phase2Duration = Date.now() - phase2StartTime;
        
        if (responseSuccess) {
            console.log(`   ✅ Response received in ${phase2Duration}ms`);
            
            // Analyze the response quality
            try {
                const messages = await page.locator('.message, .chat-message').all();
                if (messages.length > 0) {
                    const lastMessage = messages[messages.length - 1];
                    const responseText = await lastMessage.textContent();
                    
                    console.log('   📝 Response analysis:');
                    console.log(`      Length: ${responseText.length} characters`);
                    
                    // Check for profile completion indicators
                    const profileIndicators = [
                        'tell me about',
                        'current environment',
                        'situation',
                        'more information',
                        'profile',
                        'personalized'
                    ];
                    
                    const foundIndicators = profileIndicators.filter(indicator => 
                        responseText.toLowerCase().includes(indicator)
                    );
                    
                    if (foundIndicators.length > 0) {
                        console.log(`      ✅ Profile completion indicators found: ${foundIndicators.join(', ')}`);
                        console.log('      ✅ Response appears to be asking for profile information');
                    } else {
                        console.log('      ⚠️  No clear profile completion indicators found');
                        results.issues.push('Response lacks profile completion indicators');
                    }
                    
                    // Check response time quality
                    if (phase2Duration < 10000) {
                        console.log('      ✅ Response time excellent (<10s)');
                    } else if (phase2Duration < 20000) {
                        console.log('      ⚠️  Response time acceptable (10-20s)');
                    } else {
                        console.log('      ❌ Response time poor (>20s)');
                        results.issues.push('Poor response time');
                    }
                }
            } catch (analysisError) {
                console.log(`      ⚠️  Response analysis failed: ${analysisError.message}`);
                results.issues.push('Response analysis failed');
            }
            
            results.phases.push({
                name: 'PROFILE COMPLETION WORKFLOW',
                duration: phase2Duration,
                success: true
            });
            
            results.success = true;
            
        } else {
            console.log(`   ❌ HANGING DETECTED: No response after ${phase2Duration}ms`);
            results.phases.push({
                name: 'PROFILE COMPLETION WORKFLOW',
                duration: phase2Duration,
                success: false
            });
            results.issues.push('Hanging detected - no response received');
        }
        
    } catch (error) {
        console.error(`❌ Test error: ${error.message}`);
        results.issues.push(`Test error: ${error.message}`);
    } finally {
        await framework.cleanup();
    }
    
    // Generate comprehensive report
    results.totalDuration = Date.now() - startTime;
    
    console.log('\n📊 Robust Profile Completion Test Report');
    console.log('=' * 50);
    console.log(`Port: ${port}`);
    console.log(`Total Test Phases: ${results.phases.length}`);
    console.log(`Successful Phases: ${results.phases.filter(p => p.success).length}`);
    console.log(`Failed Phases: ${results.phases.filter(p => !p.success).length}`);
    console.log(`Success Rate: ${((results.phases.filter(p => p.success).length / results.phases.length) * 100).toFixed(1)}%`);
    console.log(`Total Test Duration: ${results.totalDuration}ms`);
    
    console.log('\nPhase Results:');
    console.log('----------------------------------------');
    results.phases.forEach(phase => {
        const status = phase.success ? '✓' : '❌';
        console.log(`${status} ${phase.name}`);
        console.log(`    Duration: ${phase.duration}ms`);
        if (!phase.success) {
            console.log(`    ⚠️  FAILED`);
        }
    });
    
    if (results.issues.length > 0) {
        console.log('\nIssues Detected:');
        console.log('----------------------------------------');
        results.issues.forEach((issue, index) => {
            console.log(`${index + 1}. ${issue}`);
        });
    }
    
    console.log('\nAssessment:');
    console.log('----------------------------------------');
    console.log(`- Hanging Issues: ${results.issues.some(i => i.includes('hanging')) ? '❌ DETECTED' : '✅ NONE'}`);
    console.log(`- Response Time Issues: ${results.issues.some(i => i.includes('response time')) ? '❌ DETECTED' : '✅ NONE'}`);
    console.log(`- Overall Status: ${results.success ? '✅ PASS' : '❌ FAIL'}`);
    
    return results;
}

// Get port from command line argument
const port = process.argv[2] ? parseInt(process.argv[2]) : 3001;

testProfileCompletion(port).catch(console.error);
