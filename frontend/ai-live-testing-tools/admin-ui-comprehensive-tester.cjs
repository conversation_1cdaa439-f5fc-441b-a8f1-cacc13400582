#!/usr/bin/env node

/**
 * Admin UI Comprehensive Tester
 * 
 * Tests admin interface functionality including:
 * - Modal display and interaction
 * - Tab navigation
 * - Form functionality
 * - API integration
 * - JavaScript execution
 * 
 * Usage:
 *   node admin-ui-comprehensive-tester.cjs
 *   node admin-ui-comprehensive-tester.cjs --modal-focus
 *   node admin-ui-comprehensive-tester.cjs --tab-focus
 */

const { chromium } = require('playwright');

class AdminUITester {
    constructor() {
        this.browser = null;
        this.page = null;
        this.results = {
            modalTests: [],
            tabTests: [],
            formTests: [],
            apiTests: [],
            jsTests: [],
            overall: { passed: 0, failed: 0, score: 0 }
        };
    }

    async initialize() {
        console.log('🚀 Initializing Admin UI Comprehensive Tester...');
        
        this.browser = await chromium.launch({ 
            headless: false,
            slowMo: 100 // Slow down for visibility
        });
        
        this.page = await this.browser.newPage();
        
        // Set up console monitoring
        this.page.on('console', msg => {
            const type = msg.type();
            const text = msg.text();
            
            if (type === 'error') {
                console.log(`❌ Console Error: ${text}`);
                this.results.jsTests.push({
                    test: 'Console Error Check',
                    status: 'failed',
                    error: text
                });
            } else if (text.includes('QuickTest') || text.includes('Benchmark')) {
                console.log(`📝 JS Log: ${text}`);
                this.results.jsTests.push({
                    test: 'JavaScript Execution',
                    status: 'passed',
                    message: text
                });
            }
        });
        
        // Set up network monitoring
        this.page.on('response', response => {
            const url = response.url();
            const status = response.status();
            
            if (url.includes('/admin/') && status >= 400) {
                console.log(`❌ Network Error: ${url} - ${status}`);
                this.results.apiTests.push({
                    test: `API Endpoint: ${url}`,
                    status: 'failed',
                    error: `HTTP ${status}`
                });
            }
        });
    }

    async testAdminPageLoad() {
        console.log('🧪 Testing admin page load...');
        
        try {
            await this.page.goto('http://localhost:8000/admin/benchmarks/manage/', {
                waitUntil: 'networkidle',
                timeout: 30000
            });
            
            // Wait for page to be fully loaded
            await this.page.waitForSelector('body', { timeout: 10000 });
            
            const title = await this.page.title();
            console.log(`✅ Page loaded: ${title}`);
            
            this.results.overall.passed++;
            return true;
        } catch (error) {
            console.log(`❌ Page load failed: ${error.message}`);
            this.results.overall.failed++;
            return false;
        }
    }

    async testModalFunctionality() {
        console.log('🧪 Testing modal functionality...');
        
        try {
            // Look for configure button
            const configureBtn = await this.page.locator('#configure-quick-test-btn');
            
            if (await configureBtn.count() === 0) {
                throw new Error('Configure button not found');
            }
            
            console.log('✅ Configure button found');
            
            // Click configure button
            await configureBtn.click();
            
            // Wait for modal to appear
            await this.page.waitForSelector('#quick-test-config-modal', { 
                state: 'visible',
                timeout: 5000 
            });
            
            console.log('✅ Modal appeared after button click');
            
            // Test modal content
            const modalTitle = await this.page.locator('#quick-test-config-modal h2').textContent();
            console.log(`✅ Modal title: ${modalTitle}`);
            
            // Test close button
            const closeBtn = await this.page.locator('#quick-test-config-modal .close');
            await closeBtn.click();
            
            // Verify modal is hidden
            await this.page.waitForSelector('#quick-test-config-modal', { 
                state: 'hidden',
                timeout: 5000 
            });
            
            console.log('✅ Modal closed successfully');
            
            this.results.modalTests.push({
                test: 'Modal Display and Close',
                status: 'passed',
                details: 'Modal opens and closes correctly'
            });
            
            this.results.overall.passed++;
            return true;
            
        } catch (error) {
            console.log(`❌ Modal test failed: ${error.message}`);
            
            this.results.modalTests.push({
                test: 'Modal Display and Close',
                status: 'failed',
                error: error.message
            });
            
            this.results.overall.failed++;
            return false;
        }
    }

    async testTabNavigation() {
        console.log('🧪 Testing tab navigation...');
        
        try {
            // Open modal first
            await this.page.locator('#configure-quick-test-btn').click();
            await this.page.waitForSelector('#quick-test-config-modal', { state: 'visible' });
            
            // Test each tab
            const tabs = ['basic', 'advanced', 'preview'];
            
            for (const tabName of tabs) {
                console.log(`🔄 Testing ${tabName} tab...`);
                
                // Click tab button
                const tabButton = await this.page.locator(`[data-tab="${tabName}"]`);
                await tabButton.click();
                
                // Verify tab content is visible
                const tabContent = await this.page.locator(`#${tabName}-tab`);
                const isVisible = await tabContent.isVisible();
                
                if (isVisible) {
                    console.log(`✅ ${tabName} tab content visible`);
                    this.results.tabTests.push({
                        test: `${tabName} Tab Navigation`,
                        status: 'passed'
                    });
                } else {
                    throw new Error(`${tabName} tab content not visible`);
                }
            }
            
            // Close modal
            await this.page.locator('#quick-test-config-modal .close').click();
            
            this.results.overall.passed++;
            return true;
            
        } catch (error) {
            console.log(`❌ Tab navigation test failed: ${error.message}`);
            
            this.results.tabTests.push({
                test: 'Tab Navigation',
                status: 'failed',
                error: error.message
            });
            
            this.results.overall.failed++;
            return false;
        }
    }

    async testJavaScriptExecution() {
        console.log('🧪 Testing JavaScript execution...');
        
        try {
            // Check if QuickTest class is available
            const quickTestAvailable = await this.page.evaluate(() => {
                return typeof QuickTest !== 'undefined';
            });
            
            if (quickTestAvailable) {
                console.log('✅ QuickTest class available');
                this.results.jsTests.push({
                    test: 'QuickTest Class Availability',
                    status: 'passed'
                });
            } else {
                throw new Error('QuickTest class not available');
            }
            
            // Check if global functions are available
            const globalFunctionsAvailable = await this.page.evaluate(() => {
                return typeof window.showQuickTestModal === 'function' && 
                       typeof window.hideQuickTestModal === 'function';
            });
            
            if (globalFunctionsAvailable) {
                console.log('✅ Global modal functions available');
                this.results.jsTests.push({
                    test: 'Global Modal Functions',
                    status: 'passed'
                });
            } else {
                console.log('⚠️ Global modal functions not available');
                this.results.jsTests.push({
                    test: 'Global Modal Functions',
                    status: 'failed',
                    error: 'Functions not globally accessible'
                });
            }
            
            this.results.overall.passed++;
            return true;
            
        } catch (error) {
            console.log(`❌ JavaScript execution test failed: ${error.message}`);
            
            this.results.jsTests.push({
                test: 'JavaScript Execution',
                status: 'failed',
                error: error.message
            });
            
            this.results.overall.failed++;
            return false;
        }
    }

    async testFormFunctionality() {
        console.log('🧪 Testing form functionality...');
        
        try {
            // Open modal
            await this.page.locator('#configure-quick-test-btn').click();
            await this.page.waitForSelector('#quick-test-config-modal', { state: 'visible' });
            
            // Test form elements
            const scenarioSelect = await this.page.locator('#quick-scenario-select');
            const runsInput = await this.page.locator('#quick-runs-input');
            
            // Check if elements are interactive
            await scenarioSelect.click();
            await runsInput.fill('2');
            
            console.log('✅ Form elements are interactive');
            
            this.results.formTests.push({
                test: 'Form Element Interaction',
                status: 'passed'
            });
            
            // Close modal
            await this.page.locator('#quick-test-config-modal .close').click();
            
            this.results.overall.passed++;
            return true;
            
        } catch (error) {
            console.log(`❌ Form functionality test failed: ${error.message}`);
            
            this.results.formTests.push({
                test: 'Form Element Interaction',
                status: 'failed',
                error: error.message
            });
            
            this.results.overall.failed++;
            return false;
        }
    }

    async generateReport() {
        const totalTests = this.results.overall.passed + this.results.overall.failed;
        const score = totalTests > 0 ? Math.round((this.results.overall.passed / totalTests) * 100) : 0;
        
        this.results.overall.score = score;
        
        console.log('\n' + '='.repeat(80));
        console.log('📊 ADMIN UI COMPREHENSIVE TEST REPORT');
        console.log('='.repeat(80));
        
        console.log(`\n🎯 Overall Score: ${score}/100`);
        console.log(`✅ Passed: ${this.results.overall.passed}`);
        console.log(`❌ Failed: ${this.results.overall.failed}`);
        
        // Modal Tests
        if (this.results.modalTests.length > 0) {
            console.log('\n🪟 Modal Tests:');
            this.results.modalTests.forEach(test => {
                const icon = test.status === 'passed' ? '✅' : '❌';
                console.log(`  ${icon} ${test.test}`);
                if (test.error) console.log(`     Error: ${test.error}`);
            });
        }
        
        // Tab Tests
        if (this.results.tabTests.length > 0) {
            console.log('\n📑 Tab Navigation Tests:');
            this.results.tabTests.forEach(test => {
                const icon = test.status === 'passed' ? '✅' : '❌';
                console.log(`  ${icon} ${test.test}`);
                if (test.error) console.log(`     Error: ${test.error}`);
            });
        }
        
        // JavaScript Tests
        if (this.results.jsTests.length > 0) {
            console.log('\n⚡ JavaScript Execution Tests:');
            this.results.jsTests.forEach(test => {
                const icon = test.status === 'passed' ? '✅' : '❌';
                console.log(`  ${icon} ${test.test}`);
                if (test.error) console.log(`     Error: ${test.error}`);
                if (test.message) console.log(`     Message: ${test.message}`);
            });
        }
        
        // Form Tests
        if (this.results.formTests.length > 0) {
            console.log('\n📝 Form Functionality Tests:');
            this.results.formTests.forEach(test => {
                const icon = test.status === 'passed' ? '✅' : '❌';
                console.log(`  ${icon} ${test.test}`);
                if (test.error) console.log(`     Error: ${test.error}`);
            });
        }
        
        console.log('\n' + '='.repeat(80));
        
        if (score >= 90) {
            console.log('🎉 EXCELLENT: Admin UI is functioning perfectly!');
        } else if (score >= 70) {
            console.log('✅ GOOD: Admin UI is mostly functional with minor issues.');
        } else if (score >= 50) {
            console.log('⚠️ FAIR: Admin UI has significant issues that need attention.');
        } else {
            console.log('❌ POOR: Admin UI has critical issues that must be fixed.');
        }
        
        return this.results;
    }

    async runAllTests() {
        try {
            await this.initialize();
            
            // Run all tests
            await this.testAdminPageLoad();
            await this.testJavaScriptExecution();
            await this.testModalFunctionality();
            await this.testTabNavigation();
            await this.testFormFunctionality();
            
            // Generate report
            const results = await this.generateReport();
            
            return results;
            
        } catch (error) {
            console.log(`❌ Critical error during testing: ${error.message}`);
            return null;
        } finally {
            if (this.browser) {
                await this.browser.close();
            }
        }
    }
}

// Main execution
async function main() {
    const args = process.argv.slice(2);
    const modalFocus = args.includes('--modal-focus');
    const tabFocus = args.includes('--tab-focus');
    
    console.log('🎯 Admin UI Comprehensive Tester Starting...');
    
    if (modalFocus) {
        console.log('🎯 Focus Mode: Modal Testing');
    } else if (tabFocus) {
        console.log('🎯 Focus Mode: Tab Navigation Testing');
    } else {
        console.log('🎯 Mode: Complete Admin UI Testing');
    }
    
    const tester = new AdminUITester();
    const results = await tester.runAllTests();
    
    if (results && results.overall.score >= 70) {
        process.exit(0);
    } else {
        process.exit(1);
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = AdminUITester;
