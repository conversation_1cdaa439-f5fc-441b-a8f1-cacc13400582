#!/usr/bin/env node

/**
 * Quick Wheel Validation - Simple test to check if fixes are working
 */

const puppeteer = require('puppeteer');

async function quickValidation() {
    console.log('🔍 Quick Wheel Physics Validation...\n');
    
    const browser = await puppeteer.launch({
        headless: false,
        defaultViewport: { width: 1200, height: 800 }
    });

    try {
        // Test Debug Environment
        console.log('📋 Testing Debug Environment (http://localhost:3006/)...');
        const debugPage = await browser.newPage();
        await debugPage.goto('http://localhost:3006/', { waitUntil: 'networkidle0' });
        
        // Check if wheel component exists
        const debugWheelExists = await debugPage.evaluate(() => {
            return !!document.getElementById('debugWheel');
        });
        console.log(`   Wheel Component: ${debugWheelExists ? '✅' : '❌'}`);

        // Check if load button exists
        const loadButtonExists = await debugPage.evaluate(() => {
            return !!document.getElementById('loadWheelBtn');
        });
        console.log(`   Load Button: ${loadButtonExists ? '✅' : '❌'}`);

        if (loadButtonExists) {
            // Try to load wheel
            await debugPage.click('#loadWheelBtn');
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            const wheelLoaded = await debugPage.evaluate(() => {
                const wheel = document.getElementById('debugWheel');
                return wheel && wheel.wheelData && wheel.wheelData.segments && wheel.wheelData.segments.length > 0;
            });
            console.log(`   Wheel Loaded: ${wheelLoaded ? '✅' : '❌'}`);

            // Check if spin button is enabled
            const spinEnabled = await debugPage.evaluate(() => {
                const btn = document.getElementById('spinWheelBtn');
                return btn && !btn.disabled;
            });
            console.log(`   Spin Enabled: ${spinEnabled ? '✅' : '❌'}`);
        }

        // Test Production App
        console.log('\n📋 Testing Production App (http://localhost:3002/)...');
        const prodPage = await browser.newPage();
        await prodPage.goto('http://localhost:3002/', { waitUntil: 'networkidle0' });
        
        // Wait for app to initialize
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // Check if app shell loaded
        const appShellExists = await prodPage.evaluate(() => {
            return !!document.querySelector('app-shell');
        });
        console.log(`   App Shell: ${appShellExists ? '✅' : '❌'}`);

        // Check if wheel component exists
        const prodWheelExists = await prodPage.evaluate(() => {
            return !!document.querySelector('game-wheel');
        });
        console.log(`   Wheel Component: ${prodWheelExists ? '✅' : '❌'}`);

        // Check if wheel has data
        const wheelHasData = await prodPage.evaluate(() => {
            const wheel = document.querySelector('game-wheel');
            return wheel && wheel.wheelData && wheel.wheelData.segments && wheel.wheelData.segments.length > 0;
        });
        console.log(`   Wheel Data: ${wheelHasData ? '✅' : '❌'}`);

        // Check if wheel is visible (not placeholder)
        const wheelVisible = await prodPage.evaluate(() => {
            const wheel = document.querySelector('game-wheel');
            const placeholder = document.querySelector('.wheel-placeholder');
            return wheel && wheel.offsetWidth > 0 && (!placeholder || placeholder.style.display === 'none');
        });
        console.log(`   Wheel Visible: ${wheelVisible ? '✅' : '❌'}`);

        // Check if spin button exists and is enabled
        const spinButtonEnabled = await prodPage.evaluate(() => {
            const btn = document.querySelector('.spin-button');
            return btn && !btn.disabled;
        });
        console.log(`   Spin Button: ${spinButtonEnabled ? '✅' : '❌'}`);

        console.log('\n🎯 SUMMARY:');
        console.log('Debug Environment: Check the browser window to see if wheel is circular and activities are displayed');
        console.log('Production App: Check the browser window to see if wheel appears instead of placeholder');
        console.log('\nManual Test: Try spinning the wheel in both environments to verify physics are working');
        console.log('Expected: Ball should fall due to gravity and bounce around the wheel');

    } catch (error) {
        console.error('❌ Validation error:', error.message);
    } finally {
        // Don't close browser automatically so user can inspect
        console.log('\n📝 Browser windows left open for manual inspection');
        console.log('Close them manually when done testing');
    }
}

quickValidation().catch(console.error);
