const { chromium } = require('playwright');

async function debugWheelData() {
    console.log('🔍 Debugging Wheel Data and Component State');
    console.log('═══════════════════════════════════════════');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 500
    });
    
    const context = await browser.newContext();
    const page = await context.newPage();
    
    // Capture all WebSocket messages
    const messages = [];
    let wheelData = null;
    
    page.on('websocket', ws => {
        console.log(`🔌 WebSocket connection: ${ws.url()}`);
        ws.on('framereceived', event => {
            try {
                const data = JSON.parse(event.payload);
                messages.push({ type: 'received', data, timestamp: Date.now() });
                
                if (data.type === 'wheel_data') {
                    wheelData = data;
                    console.log('🎡 Wheel data received!');
                    console.log('📊 Wheel data structure:', JSON.stringify(data, null, 2));
                }
                
                console.log(`📨 ← Server: ${JSON.stringify(data).substring(0, 100)}...`);
            } catch (e) {
                console.log(`📨 ← Server (raw): ${event.payload.substring(0, 100)}...`);
            }
        });
        ws.on('framesent', event => {
            try {
                const data = JSON.parse(event.payload);
                messages.push({ type: 'sent', data, timestamp: Date.now() });
                console.log(`📤 → Client: ${JSON.stringify(data).substring(0, 100)}...`);
            } catch (e) {
                console.log(`📤 → Client (raw): ${event.payload.substring(0, 100)}...`);
            }
        });
    });
    
    try {
        // Load the frontend
        console.log('🌐 Loading frontend...');
        await page.goto('http://localhost:3000/', { waitUntil: 'networkidle' });
        console.log('✅ Frontend loaded');
        
        // Wait for connection
        await page.waitForTimeout(3000);
        
        // Send wheel generation request
        const chatInput = await page.locator('textarea').first();
        await chatInput.waitFor({ state: 'visible', timeout: 10000 });
        await chatInput.click();
        
        const message = "I'm restless and need to do things physical. I have 2 hours. Make me a wheel";
        console.log(`📝 Sending message: "${message}"`);
        await chatInput.fill(message);
        await page.keyboard.press('Enter');
        
        // Wait for wheel data
        console.log('⏳ Waiting for wheel data...');
        let attempts = 0;
        while (!wheelData && attempts < 60) {
            await page.waitForTimeout(1000);
            attempts++;
        }
        
        if (wheelData) {
            console.log('\n🎡 WHEEL DATA ANALYSIS');
            console.log('═══════════════════════════════════════');
            console.log('✅ Wheel data received');
            
            // Analyze wheel structure
            const wheel = wheelData.wheel;
            if (wheel) {
                console.log(`📊 Wheel name: ${wheel.metadata?.name || wheel.name || 'Unknown'}`);
                console.log(`📊 Items count: ${wheel.items?.length || 0}`);
                
                if (wheel.items && wheel.items.length > 0) {
                    console.log('\n🎯 WHEEL ITEMS:');
                    wheel.items.forEach((item, index) => {
                        console.log(`  ${index + 1}. ${item.name || item.title || 'Unnamed'}`);
                        console.log(`     ID: ${item.id || item.activity_tailored_id || 'No ID'}`);
                        console.log(`     Color: ${item.color || 'No color'}`);
                        console.log(`     Domain: ${item.domain || 'No domain'}`);
                        console.log(`     Description: ${(item.description || 'No description').substring(0, 100)}...`);
                    });
                }
            }
            
            // Check if wheel component is properly initialized
            console.log('\n🔍 FRONTEND COMPONENT ANALYSIS');
            console.log('═══════════════════════════════════════');
            
            // Wait a bit more for component to initialize
            await page.waitForTimeout(3000);
            
            // Check for game-wheel element
            const gameWheelElement = await page.locator('game-wheel').first();
            const gameWheelExists = await gameWheelElement.count() > 0;
            console.log(`🎡 game-wheel element exists: ${gameWheelExists}`);
            
            if (gameWheelExists) {
                // Check wheel component properties
                const wheelProperties = await page.evaluate(() => {
                    const gameWheel = document.querySelector('game-wheel');
                    if (gameWheel) {
                        return {
                            hasWheelData: !!gameWheel.wheelData,
                            segments: gameWheel.segments ? gameWheel.segments.length : 0,
                            isSpinning: gameWheel.isSpinning,
                            errorMessage: gameWheel.errorMessage,
                            statusMessage: gameWheel.statusMessage
                        };
                    }
                    return null;
                });
                
                console.log('🎡 Wheel component properties:', wheelProperties);
                
                // Check for spin button specifically
                const spinButton = await page.locator('game-wheel button.spin-button').first();
                const spinButtonExists = await spinButton.count() > 0;
                console.log(`🎯 Spin button exists: ${spinButtonExists}`);
                
                if (spinButtonExists) {
                    const spinButtonState = await page.evaluate(() => {
                        const button = document.querySelector('game-wheel button.spin-button');
                        if (button) {
                            return {
                                visible: !button.hidden && button.offsetParent !== null,
                                enabled: !button.disabled,
                                text: button.textContent.trim(),
                                style: button.style.cssText,
                                computedStyle: window.getComputedStyle(button).display
                            };
                        }
                        return null;
                    });
                    console.log('🎯 Spin button state:', spinButtonState);
                } else {
                    // Check for any buttons inside game-wheel
                    const allButtons = await page.locator('game-wheel button').all();
                    console.log(`🔍 Found ${allButtons.length} buttons inside game-wheel`);
                    
                    for (let i = 0; i < allButtons.length; i++) {
                        const button = allButtons[i];
                        const text = await button.textContent().catch(() => 'N/A');
                        const classes = await button.getAttribute('class').catch(() => 'N/A');
                        console.log(`   Button ${i}: "${text.trim()}" (classes: ${classes})`);
                    }
                }
                
                // Check canvas element
                const canvas = await page.locator('game-wheel canvas').first();
                const canvasExists = await canvas.count() > 0;
                console.log(`🎨 Canvas element exists: ${canvasExists}`);
                
                if (canvasExists) {
                    const canvasState = await page.evaluate(() => {
                        const canvas = document.querySelector('game-wheel canvas');
                        if (canvas) {
                            return {
                                width: canvas.width,
                                height: canvas.height,
                                visible: !canvas.hidden && canvas.offsetParent !== null
                            };
                        }
                        return null;
                    });
                    console.log('🎨 Canvas state:', canvasState);
                }
            }
            
            // Check console errors
            const consoleErrors = [];
            page.on('console', msg => {
                if (msg.type() === 'error') {
                    consoleErrors.push(msg.text());
                }
            });
            
            await page.waitForTimeout(2000);
            
            if (consoleErrors.length > 0) {
                console.log('\n❌ CONSOLE ERRORS:');
                consoleErrors.forEach(error => {
                    console.log(`   ${error}`);
                });
            } else {
                console.log('\n✅ No console errors detected');
            }
            
        } else {
            console.log('❌ No wheel data received');
        }
        
        console.log('\n🔍 Browser kept open for manual inspection...');
        console.log('Press Ctrl+C to close when done.');
        
        // Keep browser open for inspection
        await new Promise(() => {});
        
    } catch (error) {
        console.error('❌ Test failed:', error);
    }
}

debugWheelData().catch(console.error);
