#!/usr/bin/env node

/**
 * WHEEL DISPLAY VERIFICATION TEST
 * 
 * This test verifies that wheel items from the backend are properly displayed in the frontend.
 * It tests the complete flow: authentication → wheel generation → wheel display → item verification.
 */

const puppeteer = require('puppeteer');

const CONFIG = {
  testUrl: 'http://localhost:3001',
  adminUser: { username: 'admin', password: 'admin123' },
  timeout: 60000
};

class WheelDisplayTester {
  constructor() {
    this.browser = null;
    this.page = null;
    this.wheelData = null;
  }

  async initialize() {
    console.log('🔍 Initializing Wheel Display Tester...');
    
    this.browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1200, height: 800 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    this.page = await this.browser.newPage();
    
    // Enable console logging
    this.page.on('console', msg => {
      const type = msg.type();
      if (type === 'error') {
        console.log(`🖥️  [error] ${msg.text()}`);
      } else if (type === 'warn') {
        console.log(`🖥️  [warn] ${msg.text()}`);
      } else {
        console.log(`🖥️  [log] ${msg.text()}`);
      }
    });
    
    console.log('✅ Tester initialized');
  }

  async navigateToApp() {
    console.log('🌐 Navigating to app...');
    await this.page.goto(CONFIG.testUrl, { waitUntil: 'networkidle0' });
    
    // Wait for app to load
    await this.page.waitForSelector('app-shell', { timeout: 10000 });
    console.log('✅ App loaded');
  }

  async login() {
    console.log('🔐 Logging in as admin...');

    // Check authentication status and user data
    const authStatus = await this.page.evaluate(() => {
      const appShell = document.querySelector('app-shell');
      return {
        isAuthenticated: appShell?.isAuthenticated || false,
        currentUser: appShell?.authService?.getCurrentUser(),
        hasToken: !!appShell?.authService?.getToken()
      };
    });

    console.log('🔍 Current auth status:', authStatus);

    if (authStatus.isAuthenticated && authStatus.currentUser?.id) {
      console.log('✅ Already logged in with user ID:', authStatus.currentUser.id);
      return;
    }

    console.log('🔄 Need to perform login...');

    // Force logout first to clear any stale state
    await this.page.evaluate(() => {
      const appShell = document.querySelector('app-shell');
      if (appShell?.authService) {
        appShell.authService.logout();
      }
    });

    // Wait a moment for logout to complete
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Wait for login form to appear
    await this.page.waitForSelector('login-form', { timeout: 10000 });

    // Fill in credentials
    await this.page.type('login-form input[type="text"]', CONFIG.adminUser.username);
    await this.page.type('login-form input[type="password"]', CONFIG.adminUser.password);

    // Submit login
    await this.page.click('login-form button[type="submit"]');

    // Wait for successful login with user data
    await this.page.waitForFunction(() => {
      const appShell = document.querySelector('app-shell');
      const user = appShell?.authService?.getCurrentUser();
      return appShell?.isAuthenticated === true && user?.id;
    }, { timeout: 15000 });

    // Verify login was successful
    const finalAuthStatus = await this.page.evaluate(() => {
      const appShell = document.querySelector('app-shell');
      return {
        isAuthenticated: appShell?.isAuthenticated || false,
        currentUser: appShell?.authService?.getCurrentUser(),
        hasToken: !!appShell?.authService?.getToken()
      };
    });

    console.log('✅ Login successful:', finalAuthStatus);
  }

  async generateWheel() {
    console.log('🎡 Generating wheel...');

    // Check current wheel state
    const wheelState = await this.page.evaluate(() => {
      const appShell = document.querySelector('app-shell');
      return {
        state: appShell?.wheelStateMachine?.context?.state,
        hasData: appShell?.wheelData?.segments?.length > 0,
        shouldShowSpinButton: appShell?.wheelStateMachine?.shouldShowSpinButton,
        shouldShowGenerateButton: appShell?.wheelStateMachine?.shouldShowGenerateButton
      };
    });

    console.log('🔍 Current wheel state:', wheelState);

    // If wheel is populated with mock data, clear it first
    if (wheelState.shouldShowSpinButton && !wheelState.shouldShowGenerateButton) {
      console.log('🧹 Clearing existing wheel data to show generate button...');

      // Clear the wheel by resetting the state machine
      await this.page.evaluate(() => {
        const appShell = document.querySelector('app-shell');
        if (appShell?.wheelStateMachine) {
          appShell.wheelStateMachine.clearWheel();
        }
      });

      // Wait for the generate button to appear
      await this.page.waitForFunction(() => {
        const appShell = document.querySelector('app-shell');
        return appShell?.wheelStateMachine?.shouldShowGenerateButton === true;
      }, { timeout: 5000 });

      console.log('✅ Wheel cleared, generate button should now be visible');
    }

    // Debug: Check what's actually in the DOM
    const domStructure = await this.page.evaluate(() => {
      const appShell = document.querySelector('app-shell');
      const actionContainer = appShell?.shadowRoot?.querySelector('.action-button-container') ||
                             document.querySelector('.action-button-container');

      if (actionContainer) {
        const buttons = Array.from(actionContainer.querySelectorAll('button'));
        return {
          found: true,
          buttons: buttons.map(btn => ({
            text: btn.textContent?.trim(),
            className: btn.className,
            disabled: btn.disabled,
            outerHTML: btn.outerHTML
          }))
        };
      } else {
        // Look for any buttons in the entire document
        const allButtons = Array.from(document.querySelectorAll('button'));
        return {
          found: false,
          allButtons: allButtons.map(btn => ({
            text: btn.textContent?.trim(),
            className: btn.className,
            disabled: btn.disabled,
            outerHTML: btn.outerHTML
          }))
        };
      }
    });

    console.log('🔍 DOM structure analysis:', JSON.stringify(domStructure, null, 2));

    // Try to find the button in shadow DOM
    const generateButtonExists = await this.page.evaluate(() => {
      const appShell = document.querySelector('app-shell');
      if (appShell?.shadowRoot) {
        const button = appShell.shadowRoot.querySelector('.action-button.generate-button');
        return button ? {
          found: true,
          text: button.textContent?.trim(),
          className: button.className,
          disabled: button.disabled
        } : { found: false };
      }
      return { found: false, reason: 'No shadow root' };
    });

    console.log('🔍 Generate button in shadow DOM:', generateButtonExists);

    if (generateButtonExists.found) {
      // Click the button in shadow DOM
      await this.page.evaluate(() => {
        const appShell = document.querySelector('app-shell');
        const button = appShell?.shadowRoot?.querySelector('.action-button.generate-button');
        if (button) {
          button.click();
          return true;
        }
        return false;
      });
      console.log('✅ Clicked generate button in shadow DOM');
    } else {
      throw new Error('Generate button not found in shadow DOM');
    }


    console.log('🔄 Wheel generation started...');

    // Wait for wheel data to be received (real data, not mock)
    await this.page.waitForFunction(() => {
      const appShell = document.querySelector('app-shell');
      const wheelData = appShell?.wheelData;
      // Make sure we have real data (not the default mock data)
      return wheelData?.segments?.length > 0 &&
             wheelData.segments.some(segment => segment.name && segment.name !== 'Mock Activity');
    }, { timeout: CONFIG.timeout });

    // Get wheel data
    this.wheelData = await this.page.evaluate(() => {
      const appShell = document.querySelector('app-shell');
      return appShell?.wheelData;
    });

    console.log(`✅ Wheel generated with ${this.wheelData.segments.length} items`);
    return this.wheelData;
  }

  async verifyWheelDisplay() {
    console.log('🔍 Verifying wheel display...');
    
    if (!this.wheelData || !this.wheelData.segments) {
      throw new Error('No wheel data available for verification');
    }
    
    // Wait for wheel component to be rendered
    await this.page.waitForSelector('game-wheel', { timeout: 10000 });
    
    // Verify wheel component has the correct data
    const wheelComponentData = await this.page.evaluate(() => {
      const wheelComponent = document.querySelector('game-wheel');
      return wheelComponent?.wheelData;
    });
    
    if (!wheelComponentData) {
      throw new Error('Wheel component has no data');
    }
    
    console.log('✅ Wheel component has data');
    
    // Verify each wheel item
    for (let i = 0; i < this.wheelData.segments.length; i++) {
      const segment = this.wheelData.segments[i];
      console.log(`🔍 Verifying item ${i + 1}: ${segment.name}`);
      
      // Check that the segment has required properties
      if (!segment.name) {
        throw new Error(`Segment ${i} missing name`);
      }
      if (!segment.color) {
        throw new Error(`Segment ${i} missing color`);
      }
      if (typeof segment.percentage !== 'number') {
        throw new Error(`Segment ${i} missing or invalid percentage`);
      }
      
      console.log(`  ✅ Name: ${segment.name}`);
      console.log(`  ✅ Color: ${segment.color}`);
      console.log(`  ✅ Percentage: ${segment.percentage}%`);
      console.log(`  ✅ Description: ${segment.description || 'N/A'}`);
    }
    
    // Verify wheel is interactive (not mocked)
    const isInteractive = await this.page.evaluate(() => {
      const wheelComponent = document.querySelector('game-wheel');
      return !wheelComponent?.hasAttribute('disable-interaction');
    });
    
    if (!isInteractive) {
      throw new Error('Wheel is not interactive - may be showing mocked data');
    }
    
    console.log('✅ Wheel is interactive (not mocked)');
    
    // Verify wheel has proper colors (not all grey)
    const hasColors = this.wheelData.segments.some(segment => 
      segment.color && segment.color !== '#95A5A6' && segment.color !== '#cccccc'
    );
    
    if (!hasColors) {
      throw new Error('Wheel segments have no proper colors - may be using fallback data');
    }
    
    console.log('✅ Wheel has proper colors');
    
    return true;
  }

  async runFullTest() {
    try {
      await this.initialize();
      await this.navigateToApp();
      await this.login();
      await this.generateWheel();
      await this.verifyWheelDisplay();
      
      console.log('\n🎉 WHEEL DISPLAY TEST PASSED!');
      console.log('════════════════════════════════════════');
      console.log(`✅ Authentication: Working`);
      console.log(`✅ Wheel Generation: Working`);
      console.log(`✅ Wheel Display: Working`);
      console.log(`✅ Items Count: ${this.wheelData.segments.length}`);
      console.log(`✅ Interactive: Yes`);
      console.log(`✅ Proper Colors: Yes`);
      console.log('\n📋 Wheel Items:');
      this.wheelData.segments.forEach((segment, i) => {
        console.log(`  ${i + 1}. ${segment.name} (${segment.color})`);
      });
      
      return true;
      
    } catch (error) {
      console.error('\n❌ WHEEL DISPLAY TEST FAILED!');
      console.error('════════════════════════════════════════');
      console.error(`Error: ${error.message}`);
      return false;
    } finally {
      if (this.browser) {
        await this.browser.close();
      }
    }
  }
}

// Run the test
async function main() {
  console.log('🔍 WHEEL DISPLAY VERIFICATION TEST');
  console.log('════════════════════════════════════════');
  
  const tester = new WheelDisplayTester();
  const success = await tester.runFullTest();
  
  process.exit(success ? 0 : 1);
}

main().catch(console.error);
