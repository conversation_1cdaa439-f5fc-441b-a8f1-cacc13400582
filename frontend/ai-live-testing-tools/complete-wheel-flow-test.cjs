#!/usr/bin/env node

/**
 * Complete Wheel Flow Test - Test the entire wheel generation flow
 */

const { chromium } = require('playwright');

async function testCompleteWheelFlow() {
    console.log('🎡 Starting complete wheel flow test...');
    
    const browser = await chromium.launch({ headless: false });
    const page = await browser.newPage();
    
    const testResults = {
        pageLoaded: false,
        connectionEstablished: false,
        textareaFound: false,
        messageSent: false,
        wheelReceived: false,
        wheelRendered: false,
        errors: []
    };
    
    // Monitor console for important messages
    page.on('console', msg => {
        const text = msg.text();
        if (text.includes('wheel_data') || text.includes('Received wheel data') || text.includes('wheel generated')) {
            console.log(`🎡 WHEEL: ${text}`);
            testResults.wheelReceived = true;
        }
        if (text.includes('WebSocket') || text.includes('Connected')) {
            console.log(`🔌 CONNECTION: ${text}`);
        }
        if (text.includes('Sending message')) {
            console.log(`📤 MESSAGE: ${text}`);
            testResults.messageSent = true;
        }
        if (msg.type() === 'error') {
            console.log(`❌ ERROR: ${text}`);
            testResults.errors.push(text);
        }
    });
    
    try {
        // Load page
        console.log('🌐 Loading page...');
        await page.goto('http://localhost:3000/');
        await page.waitForTimeout(10000); // Wait for full initialization
        testResults.pageLoaded = true;
        
        // Check connection status
        const connectionStatus = await page.evaluate(() => {
            const appShell = document.querySelector('app-shell');
            if (!appShell || !appShell.shadowRoot) return 'no-app-shell';
            
            const chatInterface = appShell.shadowRoot.querySelector('chat-interface');
            if (!chatInterface) return 'no-chat-interface';
            
            return chatInterface.connectionStatus || 'unknown';
        });
        
        console.log(`🔌 Connection status: ${connectionStatus}`);
        if (connectionStatus === 'connected') {
            testResults.connectionEstablished = true;
        }
        
        // Configure debug mode (select user)
        const userSelected = await page.evaluate(() => {
            const appShell = document.querySelector('app-shell');
            if (!appShell || !appShell.shadowRoot) return false;
            
            const debugPanel = appShell.shadowRoot.querySelector('debug-panel');
            if (!debugPanel || !debugPanel.shadowRoot) return false;
            
            const userSelect = debugPanel.shadowRoot.querySelector('select[name="user"]');
            if (!userSelect) return false;
            
            userSelect.value = '2';
            userSelect.dispatchEvent(new Event('change', { bubbles: true }));
            return true;
        });
        
        if (userSelected) {
            console.log('✅ User 2 selected in debug mode');
            await page.waitForTimeout(1000);
        } else {
            console.log('⚠️  Could not select user in debug mode');
        }
        
        // Find and interact with textarea
        const textareaInteraction = await page.evaluate(() => {
            const appShell = document.querySelector('app-shell');
            if (!appShell || !appShell.shadowRoot) return { success: false, error: 'no-app-shell' };
            
            const chatInterface = appShell.shadowRoot.querySelector('chat-interface');
            if (!chatInterface || !chatInterface.shadowRoot) return { success: false, error: 'no-chat-interface' };
            
            const textarea = chatInterface.shadowRoot.querySelector('textarea');
            if (!textarea) return { success: false, error: 'no-textarea' };
            
            if (textarea.disabled) return { success: false, error: 'textarea-disabled' };
            
            // Type message
            const message = "I'm restless and need to do things physical. I have 2 hours. Make me a wheel";
            textarea.value = message;
            textarea.dispatchEvent(new Event('input', { bubbles: true }));
            
            // Trigger Enter key
            const enterEvent = new KeyboardEvent('keypress', {
                key: 'Enter',
                code: 'Enter',
                keyCode: 13,
                which: 13,
                bubbles: true
            });
            textarea.dispatchEvent(enterEvent);
            
            return { 
                success: true, 
                message: message,
                disabled: textarea.disabled,
                value: textarea.value
            };
        });
        
        console.log('📝 Textarea interaction:', textareaInteraction);
        if (textareaInteraction.success) {
            testResults.textareaFound = true;
            console.log('✅ Message typed and Enter pressed');
        } else {
            console.log(`❌ Textarea interaction failed: ${textareaInteraction.error}`);
        }
        
        // Wait for wheel generation (up to 2 minutes)
        console.log('⏳ Waiting for wheel generation (up to 120 seconds)...');
        let wheelCheckAttempts = 0;
        const maxAttempts = 24; // 24 * 5 seconds = 120 seconds
        
        while (wheelCheckAttempts < maxAttempts && !testResults.wheelReceived) {
            await page.waitForTimeout(5000);
            wheelCheckAttempts++;
            
            // Check for wheel data
            const wheelStatus = await page.evaluate(() => {
                const appShell = document.querySelector('app-shell');
                if (!appShell) return { hasWheel: false, messages: 0 };
                
                // Check if wheel component exists
                const gameWheel = appShell.shadowRoot?.querySelector('game-wheel');
                const wheelPlaceholder = appShell.shadowRoot?.querySelector('.wheel-placeholder');
                
                // Check messages
                const chatInterface = appShell.shadowRoot?.querySelector('chat-interface');
                const messageCount = chatInterface?.messages?.length || 0;
                
                return {
                    hasWheel: !!gameWheel,
                    hasPlaceholder: !!wheelPlaceholder,
                    messages: messageCount,
                    wheelData: !!appShell.wheelData
                };
            });
            
            console.log(`🔍 Attempt ${wheelCheckAttempts}/${maxAttempts}: Wheel=${wheelStatus.hasWheel}, Messages=${wheelStatus.messages}, WheelData=${wheelStatus.wheelData}`);
            
            if (wheelStatus.hasWheel || wheelStatus.wheelData) {
                testResults.wheelReceived = true;
                testResults.wheelRendered = wheelStatus.hasWheel;
                console.log('🎡 Wheel detected!');
                break;
            }
        }
        
        // Final status check
        const finalStatus = await page.evaluate(() => {
            const appShell = document.querySelector('app-shell');
            if (!appShell) return {};
            
            const chatInterface = appShell.shadowRoot?.querySelector('chat-interface');
            const gameWheel = appShell.shadowRoot?.querySelector('game-wheel');
            
            return {
                messages: chatInterface?.messages?.length || 0,
                hasWheel: !!gameWheel,
                wheelData: !!appShell.wheelData,
                connectionStatus: chatInterface?.connectionStatus || 'unknown'
            };
        });
        
        console.log('\n🎯 FINAL RESULTS:');
        console.log('════════════════════════════════════════════════════════════');
        console.log(`✅ Page Loaded: ${testResults.pageLoaded}`);
        console.log(`✅ Connection Established: ${testResults.connectionEstablished}`);
        console.log(`✅ Textarea Found: ${testResults.textareaFound}`);
        console.log(`✅ Message Sent: ${testResults.messageSent}`);
        console.log(`✅ Wheel Received: ${testResults.wheelReceived}`);
        console.log(`✅ Wheel Rendered: ${testResults.wheelRendered}`);
        console.log(`📊 Final Messages: ${finalStatus.messages}`);
        console.log(`🎡 Final Wheel Status: ${finalStatus.hasWheel ? 'Rendered' : 'Not rendered'}`);
        console.log(`🔌 Final Connection: ${finalStatus.connectionStatus}`);
        
        if (testResults.errors.length > 0) {
            console.log('\n❌ ERRORS:');
            testResults.errors.forEach((error, i) => {
                console.log(`  ${i+1}. ${error}`);
            });
        }
        
        const allTestsPassed = testResults.pageLoaded && 
                              testResults.connectionEstablished && 
                              testResults.textareaFound && 
                              testResults.wheelReceived;
        
        console.log(`\n🏆 OVERALL RESULT: ${allTestsPassed ? '✅ SUCCESS' : '❌ PARTIAL SUCCESS'}`);
        
        if (allTestsPassed) {
            console.log('🎉 Complete wheel generation flow is working!');
        } else {
            console.log('⚠️  Some issues detected - see details above');
        }
        
    } catch (error) {
        console.error('❌ Test failed:', error);
        testResults.errors.push(error.message);
    }
    
    await browser.close();
    return testResults;
}

testCompleteWheelFlow().catch(console.error);
