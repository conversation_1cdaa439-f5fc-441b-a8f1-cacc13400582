#!/usr/bin/env node

/**
 * Enhanced Dashboard Tester
 * 
 * Comprehensive testing tool for the WebSocket connection dashboard that:
 * - Uses real user IDs from the database
 * - Monitors Celery logs for backend issues
 * - Simulates browser-like behavior
 * - Validates message formats against MESSAGE_SPECIFICATIONS.md
 * - Provides detailed error analysis and reporting
 */

import WebSocket from 'ws';
import { spawn } from 'child_process';
import fs from 'fs';
import path from 'path';

// Configuration
const CONFIG = {
  backend: {
    websocketUrl: 'ws://localhost:8000/ws/game/',
    adminWebsocketUrl: 'ws://localhost:8000/ws/connection-monitor/',
    httpUrl: 'http://localhost:8000',
    timeout: 30000
  },
  users: {
    real: { id: '1', name: 'Test User', is_real: true },
    fake: { id: '2', name: 'Phi<PERSON>hi', is_real: false }
  },
  testing: {
    maxTestDuration: 60000, // 1 minute
    celeryLogInterval: 5000, // Check logs every 5 seconds
    messageTimeout: 10000
  }
};

class EnhancedDashboardTester {
  constructor() {
    this.adminSocket = null;
    this.clientSockets = [];
    this.messageLog = [];
    this.celeryLogProcess = null;
    this.testResults = {
      connectionTracking: false,
      messageFlow: false,
      dashboardUpdates: false,
      errorHandling: false,
      celeryIssues: []
    };
    this.startTime = Date.now();
  }

  async runComprehensiveTest() {
    console.log('🔍 Enhanced Dashboard Comprehensive Test');
    console.log('==========================================\n');

    try {
      // Start Celery log monitoring
      this.startCeleryLogMonitoring();

      // Test 1: Basic connectivity
      await this.testBasicConnectivity();

      // Test 2: Connection tracking
      await this.testConnectionTracking();

      // Test 3: Message flow validation
      await this.testMessageFlow();

      // Test 4: Dashboard real-time updates
      await this.testDashboardUpdates();

      // Test 5: Error handling
      await this.testErrorHandling();

      // Generate comprehensive report
      this.generateReport();

    } catch (error) {
      console.error('❌ Test suite failed:', error.message);
      this.testResults.error = error.message;
    } finally {
      this.cleanup();
    }
  }

  startCeleryLogMonitoring() {
    console.log('📋 Starting Celery log monitoring...');
    
    this.celeryLogProcess = spawn('docker', ['logs', '-f', 'backend-celery-1'], {
      stdio: ['ignore', 'pipe', 'pipe']
    });

    this.celeryLogProcess.stdout.on('data', (data) => {
      const logLine = data.toString();
      this.analyzeCeleryLog(logLine);
    });

    this.celeryLogProcess.stderr.on('data', (data) => {
      const logLine = data.toString();
      this.analyzeCeleryLog(logLine);
    });

    console.log('✅ Celery log monitoring started\n');
  }

  analyzeCeleryLog(logLine) {
    // Look for critical issues in Celery logs
    const criticalPatterns = [
      /Invalid user_profile_id format: (.+)/,
      /Error recording workflow history/,
      /Field 'id' expected a number but got/,
      /Cannot convert to int for DB/,
      /AttributeError: .* object has no attribute/,
      /ImportError: cannot import name/
    ];

    for (const pattern of criticalPatterns) {
      const match = logLine.match(pattern);
      if (match) {
        const issue = {
          timestamp: new Date().toISOString(),
          pattern: pattern.source,
          message: logLine.trim(),
          severity: 'critical'
        };
        this.testResults.celeryIssues.push(issue);
        console.log(`🚨 CELERY ISSUE: ${issue.message}`);
      }
    }
  }

  async testBasicConnectivity() {
    console.log('📋 Test 1: Basic Connectivity');
    console.log('------------------------------');

    return new Promise((resolve, reject) => {
      // Test admin socket
      this.adminSocket = new WebSocket(CONFIG.backend.adminWebsocketUrl);
      
      this.adminSocket.on('open', () => {
        console.log('✅ Admin WebSocket connected');
        
        // Test client socket
        const clientSocket = new WebSocket(CONFIG.backend.websocketUrl);
        
        clientSocket.on('open', () => {
          console.log('✅ Client WebSocket connected');
          this.clientSockets.push(clientSocket);
          resolve();
        });

        clientSocket.on('error', (error) => {
          console.log('❌ Client WebSocket error:', error.message);
          reject(error);
        });
      });

      this.adminSocket.on('error', (error) => {
        console.log('❌ Admin WebSocket error:', error.message);
        reject(error);
      });

      // Timeout
      setTimeout(() => {
        reject(new Error('Basic connectivity test timeout'));
      }, CONFIG.testing.messageTimeout);
    });
  }

  async testConnectionTracking() {
    console.log('\n📋 Test 2: Connection Tracking');
    console.log('-------------------------------');

    return new Promise((resolve) => {
      let initialConnections = 0;
      let finalConnections = 0;

      // Get initial connection count
      this.adminSocket.send(JSON.stringify({ type: 'get_connections' }));

      const messageHandler = (data) => {
        try {
          const message = JSON.parse(data);
          
          if (message.type === 'connection_data') {
            if (initialConnections === 0) {
              initialConnections = message.data.length;
              console.log(`📊 Initial connections: ${initialConnections}`);
              
              // Create test client with real user ID
              this.createTestClient();
              
              // Check again after client creation
              setTimeout(() => {
                this.adminSocket.send(JSON.stringify({ type: 'get_connections' }));
              }, 3000);
              
            } else {
              finalConnections = message.data.length;
              console.log(`📊 Final connections: ${finalConnections}`);
              
              if (finalConnections > initialConnections) {
                console.log('✅ Connection tracking is working!');
                this.testResults.connectionTracking = true;
              } else {
                console.log('❌ Connection tracking is NOT working');
                this.testResults.connectionTracking = false;
              }
              
              this.adminSocket.removeListener('message', messageHandler);
              resolve();
            }
          }
        } catch (error) {
          console.log('❌ Failed to parse admin message:', error.message);
        }
      };

      this.adminSocket.on('message', messageHandler);

      // Timeout
      setTimeout(() => {
        this.adminSocket.removeListener('message', messageHandler);
        console.log('⏰ Connection tracking test timeout');
        resolve();
      }, 15000);
    });
  }

  createTestClient() {
    const clientSocket = new WebSocket(CONFIG.backend.websocketUrl);
    
    clientSocket.on('open', () => {
      console.log('✅ Test client connected');
      
      // Send message with real user ID
      const message = {
        type: 'chat_message',
        content: {
          message: 'Enhanced dashboard test message',
          user_profile_id: CONFIG.users.fake.id, // Use PhiPhi (fake user)
          timestamp: new Date().toISOString(),
          metadata: {
            requested_workflow: 'discussion'
          }
        }
      };
      
      clientSocket.send(JSON.stringify(message));
      console.log('📤 Sent test message with real user ID');
      this.messageLog.push({ direction: 'sent', message, timestamp: Date.now() });
    });

    clientSocket.on('message', (data) => {
      try {
        const message = JSON.parse(data);
        console.log(`📨 Client received: ${message.type}`);
        this.messageLog.push({ direction: 'received', message, timestamp: Date.now() });
      } catch (error) {
        console.log('❌ Failed to parse client message:', error.message);
      }
    });

    clientSocket.on('error', (error) => {
      console.log('❌ Test client error:', error.message);
    });

    this.clientSockets.push(clientSocket);
  }

  async testMessageFlow() {
    console.log('\n📋 Test 3: Message Flow Validation');
    console.log('-----------------------------------');

    // Wait for some message exchange
    await new Promise(resolve => setTimeout(resolve, 5000));

    const expectedMessageTypes = ['system_message', 'chat_message', 'processing_status'];
    const receivedTypes = this.messageLog
      .filter(log => log.direction === 'received')
      .map(log => log.message.type);

    console.log('📊 Received message types:', [...new Set(receivedTypes)]);

    const hasExpectedMessages = expectedMessageTypes.some(type => 
      receivedTypes.includes(type)
    );

    if (hasExpectedMessages) {
      console.log('✅ Message flow is working');
      this.testResults.messageFlow = true;
    } else {
      console.log('❌ Message flow has issues');
      this.testResults.messageFlow = false;
    }
  }

  async testDashboardUpdates() {
    console.log('\n📋 Test 4: Dashboard Real-time Updates');
    console.log('--------------------------------------');

    return new Promise((resolve) => {
      let updateReceived = false;

      const updateHandler = (data) => {
        try {
          const message = JSON.parse(data);
          if (['connection_data', 'system_health', 'message_stats'].includes(message.type)) {
            console.log(`✅ Dashboard update received: ${message.type}`);
            updateReceived = true;
          }
        } catch (error) {
          // Ignore parsing errors for this test
        }
      };

      this.adminSocket.on('message', updateHandler);

      // Request various dashboard data
      this.adminSocket.send(JSON.stringify({ type: 'get_connections' }));
      this.adminSocket.send(JSON.stringify({ type: 'get_system_health' }));
      this.adminSocket.send(JSON.stringify({ type: 'get_message_stats' }));

      setTimeout(() => {
        this.adminSocket.removeListener('message', updateHandler);
        
        if (updateReceived) {
          console.log('✅ Dashboard updates are working');
          this.testResults.dashboardUpdates = true;
        } else {
          console.log('❌ Dashboard updates are NOT working');
          this.testResults.dashboardUpdates = false;
        }
        
        resolve();
      }, 5000);
    });
  }

  async testErrorHandling() {
    console.log('\n📋 Test 5: Error Handling');
    console.log('-------------------------');

    // Send invalid message to test error handling
    const invalidMessage = {
      type: 'invalid_message_type',
      content: { invalid: 'data' }
    };

    if (this.clientSockets.length > 0) {
      this.clientSockets[0].send(JSON.stringify(invalidMessage));
      console.log('📤 Sent invalid message to test error handling');
    }

    // Wait for potential error response
    await new Promise(resolve => setTimeout(resolve, 3000));

    const errorMessages = this.messageLog
      .filter(log => log.direction === 'received' && log.message.type === 'error');

    if (errorMessages.length > 0) {
      console.log('✅ Error handling is working');
      this.testResults.errorHandling = true;
    } else {
      console.log('⚠️ No error messages received (may be normal)');
      this.testResults.errorHandling = false;
    }
  }

  generateReport() {
    console.log('\n📊 COMPREHENSIVE TEST REPORT');
    console.log('============================');
    
    const duration = Date.now() - this.startTime;
    console.log(`⏱️ Test Duration: ${duration}ms`);
    console.log(`📨 Messages Logged: ${this.messageLog.length}`);
    console.log(`🚨 Celery Issues Found: ${this.testResults.celeryIssues.length}`);
    
    console.log('\n🔍 Test Results:');
    console.log(`  Connection Tracking: ${this.testResults.connectionTracking ? '✅' : '❌'}`);
    console.log(`  Message Flow: ${this.testResults.messageFlow ? '✅' : '❌'}`);
    console.log(`  Dashboard Updates: ${this.testResults.dashboardUpdates ? '✅' : '❌'}`);
    console.log(`  Error Handling: ${this.testResults.errorHandling ? '✅' : '❌'}`);

    if (this.testResults.celeryIssues.length > 0) {
      console.log('\n🚨 Critical Celery Issues:');
      this.testResults.celeryIssues.forEach((issue, index) => {
        console.log(`  ${index + 1}. ${issue.message}`);
      });
    }

    // Save detailed report
    const reportPath = path.join('logs', `enhanced-dashboard-test-${Date.now()}.json`);
    const report = {
      timestamp: new Date().toISOString(),
      duration,
      results: this.testResults,
      messageLog: this.messageLog
    };

    try {
      if (!fs.existsSync('logs')) {
        fs.mkdirSync('logs');
      }
      fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
      console.log(`\n💾 Detailed report saved to: ${reportPath}`);
    } catch (error) {
      console.log(`❌ Failed to save report: ${error.message}`);
    }
  }

  cleanup() {
    console.log('\n🧹 Cleaning up...');
    
    // Close WebSocket connections
    if (this.adminSocket) {
      this.adminSocket.close();
    }
    
    this.clientSockets.forEach(socket => {
      if (socket.readyState === WebSocket.OPEN) {
        socket.close();
      }
    });

    // Stop Celery log monitoring
    if (this.celeryLogProcess) {
      this.celeryLogProcess.kill();
    }

    console.log('✅ Cleanup completed');
  }
}

// Run the test
const tester = new EnhancedDashboardTester();
tester.runComprehensiveTest().then(() => {
  process.exit(0);
}).catch((error) => {
  console.error('❌ Test failed:', error);
  process.exit(1);
});

// Auto-exit after max duration
setTimeout(() => {
  console.log('\n⏰ Maximum test duration reached - exiting');
  process.exit(1);
}, CONFIG.testing.maxTestDuration);
