/**
 * Test Wheel ID Consistency
 * 
 * This script tests that wheel IDs remain consistent between:
 * 1. Initial wheel generation (WebSocket)
 * 2. Item removal (API)
 * 3. Item addition (API)
 * 
 * Run this in the browser console after loading the app.
 */

window.testWheelIdConsistency = function() {
  console.log('🔍 ===== WHEEL ID CONSISTENCY TEST =====');
  
  const appShell = document.querySelector('app-shell');
  if (!appShell) {
    console.error('❌ App shell not found');
    return;
  }
  
  let testResults = {
    initialWheelId: null,
    afterRemovalWheelId: null,
    afterAdditionWheelId: null,
    consistencyCheck: false,
    errors: []
  };
  
  // Capture initial wheel state
  function captureInitialState() {
    const wheelData = appShell.wheelData;
    if (wheelData && wheelData.wheelId) {
      testResults.initialWheelId = wheelData.wheelId;
      console.log(`📸 Initial wheel ID captured: ${testResults.initialWheelId}`);
      console.log(`📊 Initial segments: ${wheelData.segments?.length || 0}`);
      return true;
    } else {
      testResults.errors.push('No initial wheel data found');
      console.error('❌ No initial wheel data found');
      return false;
    }
  }
  
  // Monitor wheel data changes
  function monitorWheelChanges() {
    let changeCount = 0;
    const originalWheelData = appShell.wheelData;
    
    const observer = new MutationObserver(() => {
      const currentWheelData = appShell.wheelData;
      if (currentWheelData !== originalWheelData) {
        changeCount++;
        console.log(`🔄 Wheel data change #${changeCount} detected`);
        console.log(`   Wheel ID: ${currentWheelData?.wheelId || 'null'}`);
        console.log(`   Segments: ${currentWheelData?.segments?.length || 0}`);
        
        // Check for wheel ID changes
        if (testResults.initialWheelId && currentWheelData?.wheelId !== testResults.initialWheelId) {
          console.warn(`⚠️ Wheel ID changed from ${testResults.initialWheelId} to ${currentWheelData?.wheelId}`);
          testResults.errors.push(`Wheel ID changed from ${testResults.initialWheelId} to ${currentWheelData?.wheelId}`);
        }
      }
    });
    
    observer.observe(appShell, { 
      attributes: true, 
      childList: true, 
      subtree: true 
    });
    
    // Stop monitoring after 30 seconds
    setTimeout(() => {
      observer.disconnect();
      console.log('👁️ Wheel monitoring stopped');
    }, 30000);
    
    return observer;
  }
  
  // Test wheel item removal
  async function testItemRemoval() {
    console.log('\n🗑️ Testing wheel item removal...');
    
    const wheelData = appShell.wheelData;
    if (!wheelData || !wheelData.segments || wheelData.segments.length === 0) {
      testResults.errors.push('No wheel items to remove');
      console.error('❌ No wheel items to remove');
      return false;
    }
    
    const itemToRemove = wheelData.segments[0];
    const itemId = itemToRemove.id || itemToRemove.wheel_item_id;
    
    console.log(`🎯 Removing item: ${itemToRemove.name || itemToRemove.text} (ID: ${itemId})`);
    
    try {
      // Simulate the removal API call
      const response = await fetch(`/api/wheel-items/${itemId}/`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include'
      });
      
      if (response.ok) {
        const data = await response.json();
        console.log('✅ Removal API call successful');
        console.log(`📊 Response wheel ID: ${data.wheel_data?.id || data.wheel_id || 'not found'}`);
        
        testResults.afterRemovalWheelId = data.wheel_data?.id || data.wheel_id;
        
        // Wait for frontend to update
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Check if frontend wheel data updated correctly
        const updatedWheelData = appShell.wheelData;
        if (updatedWheelData) {
          console.log(`📊 Frontend wheel ID after removal: ${updatedWheelData.wheelId}`);
          console.log(`📊 Frontend segments after removal: ${updatedWheelData.segments?.length || 0}`);
          
          if (updatedWheelData.wheelId !== testResults.initialWheelId) {
            testResults.errors.push(`Frontend wheel ID changed after removal: ${testResults.initialWheelId} → ${updatedWheelData.wheelId}`);
            console.error(`❌ Frontend wheel ID changed after removal: ${testResults.initialWheelId} → ${updatedWheelData.wheelId}`);
          } else {
            console.log('✅ Frontend wheel ID remained consistent after removal');
          }
        }
        
        return true;
      } else {
        const errorData = await response.json();
        testResults.errors.push(`Removal API failed: ${errorData.error || 'Unknown error'}`);
        console.error('❌ Removal API failed:', errorData);
        return false;
      }
    } catch (error) {
      testResults.errors.push(`Removal API error: ${error.message}`);
      console.error('❌ Removal API error:', error);
      return false;
    }
  }
  
  // Analyze results
  function analyzeResults() {
    console.log('\n📊 ===== TEST RESULTS ANALYSIS =====');
    
    console.log(`Initial Wheel ID: ${testResults.initialWheelId}`);
    console.log(`After Removal Wheel ID: ${testResults.afterRemovalWheelId}`);
    console.log(`After Addition Wheel ID: ${testResults.afterAdditionWheelId}`);
    
    // Check consistency
    const idsMatch = testResults.initialWheelId === testResults.afterRemovalWheelId;
    testResults.consistencyCheck = idsMatch && testResults.errors.length === 0;
    
    if (testResults.consistencyCheck) {
      console.log('✅ WHEEL ID CONSISTENCY TEST PASSED!');
      console.log('   All wheel operations maintained the same wheel ID');
    } else {
      console.log('❌ WHEEL ID CONSISTENCY TEST FAILED!');
      if (!idsMatch) {
        console.log(`   Wheel ID changed: ${testResults.initialWheelId} → ${testResults.afterRemovalWheelId}`);
      }
      if (testResults.errors.length > 0) {
        console.log('   Errors encountered:');
        testResults.errors.forEach((error, index) => {
          console.log(`     ${index + 1}. ${error}`);
        });
      }
    }
    
    return testResults;
  }
  
  // Run the test
  async function runTest() {
    try {
      // Step 1: Capture initial state
      if (!captureInitialState()) {
        console.log('❌ Test aborted - no initial wheel data');
        return analyzeResults();
      }
      
      // Step 2: Start monitoring
      const observer = monitorWheelChanges();
      
      // Step 3: Test item removal
      await testItemRemoval();
      
      // Step 4: Analyze results
      return analyzeResults();
      
    } catch (error) {
      console.error('❌ Test execution error:', error);
      testResults.errors.push(`Test execution error: ${error.message}`);
      return analyzeResults();
    }
  }
  
  return runTest();
};

// Auto-run instructions
console.log('🚀 Wheel ID Consistency Test loaded.');
console.log('💡 To run the test, execute: window.testWheelIdConsistency()');
console.log('📋 Make sure you have a wheel with at least one item before running the test.');
