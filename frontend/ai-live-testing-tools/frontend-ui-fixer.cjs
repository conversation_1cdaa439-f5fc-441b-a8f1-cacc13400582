#!/usr/bin/env node

/**
 * Frontend UI Fixer
 * 
 * Fixes the specific UI issues:
 * 1. Frontend gets blurry with spinning wheel during backend process
 * 2. Impossible to put focus in chat area for scrolling and typing
 */

const { chromium } = require('playwright');

class FrontendUIFixer {
    constructor() {
        this.browser = null;
        this.page = null;
        this.fixes = {
            spinnerRemoved: false,
            blurRemoved: false,
            chatFocusFixed: false,
            scrollingEnabled: false,
            typingEnabled: false
        };
    }

    async initialize() {
        console.log('🔧 Frontend UI Fixer - Starting...');
        
        this.browser = await chromium.launch({ 
            headless: false,
            slowMo: 500
        });
        
        this.page = await this.browser.newPage();
        
        console.log('✅ UI Fixer initialized');
    }

    async loadFrontend() {
        console.log('🌐 Loading frontend...');
        await this.page.goto('http://localhost:3001');
        await this.page.waitForTimeout(5000);
        console.log('✅ Frontend loaded');
    }

    async fixSpinnerAndBlur() {
        console.log('\n🔧 Fixing spinner and blur issues...');
        console.log('════════════════════════════════════════════════════════════');
        
        // Remove all spinners and blur effects
        await this.page.addStyleTag({
            content: `
                /* REMOVE ALL SPINNERS AND LOADING INDICATORS */
                .spinner,
                .loading,
                .loading-spinner,
                .waiting,
                .processing,
                .processing-overlay,
                div[class*="spinner"],
                div[class*="loading"],
                div[class*="waiting"],
                div[class*="processing"] {
                    display: none !important;
                    visibility: hidden !important;
                    opacity: 0 !important;
                }
                
                /* REMOVE BLUR EFFECTS */
                *[style*="blur"],
                .blur,
                .blurred,
                div[class*="blur"] {
                    filter: none !important;
                    backdrop-filter: none !important;
                    -webkit-filter: none !important;
                }
                
                /* ENSURE MAIN CONTENT IS ALWAYS VISIBLE */
                body,
                main,
                .app,
                .main-content {
                    filter: none !important;
                    backdrop-filter: none !important;
                    opacity: 1 !important;
                    pointer-events: auto !important;
                }
                
                /* FORCE REMOVE ANY OVERLAY THAT MIGHT CAUSE BLUR */
                .overlay,
                .modal-backdrop,
                .loading-overlay {
                    display: none !important;
                }
            `
        });
        
        // JavaScript removal of dynamic spinners and blur
        await this.page.evaluate(() => {
            // Remove spinner elements
            const spinnerSelectors = [
                '.spinner', '.loading', '.loading-spinner', '.waiting', 
                '.processing', '.processing-overlay', 'div[class*="spinner"]',
                'div[class*="loading"]', 'div[class*="waiting"]', 'div[class*="processing"]'
            ];
            
            let removedCount = 0;
            
            function removeSpinners(root) {
                spinnerSelectors.forEach(selector => {
                    const elements = root.querySelectorAll(selector);
                    elements.forEach(el => {
                        el.remove();
                        removedCount++;
                    });
                });
            }
            
            // Remove from regular DOM
            removeSpinners(document);
            
            // Remove from shadow DOMs
            function removeFromShadowRoots(root) {
                const allElements = root.querySelectorAll('*');
                allElements.forEach(el => {
                    if (el.shadowRoot) {
                        removeSpinners(el.shadowRoot);
                        removeFromShadowRoots(el.shadowRoot);
                    }
                });
            }
            
            removeFromShadowRoots(document);
            
            // Remove blur styles
            const allElements = document.querySelectorAll('*');
            allElements.forEach(el => {
                if (el.style.filter && el.style.filter.includes('blur')) {
                    el.style.filter = 'none';
                }
                if (el.style.backdropFilter) {
                    el.style.backdropFilter = 'none';
                }
            });
            
            console.log(`🗑️  Removed ${removedCount} spinner/loading elements`);
            
            // Set up mutation observer to prevent spinners from reappearing
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === 1) {
                            const className = node.className || '';
                            if (className.includes('spinner') || 
                                className.includes('loading') || 
                                className.includes('waiting') ||
                                className.includes('processing')) {
                                console.log('🚫 Blocked spinner:', className);
                                node.remove();
                            }
                            
                            // Check for blur styles
                            if (node.style && (node.style.filter || node.style.backdropFilter)) {
                                node.style.filter = 'none';
                                node.style.backdropFilter = 'none';
                            }
                        }
                    });
                });
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true,
                attributes: true,
                attributeFilter: ['style', 'class']
            });
            
            console.log('🛡️  Anti-spinner protection activated');
        });
        
        this.fixes.spinnerRemoved = true;
        this.fixes.blurRemoved = true;
        console.log('✅ Spinner and blur fixes applied');
    }

    async fixChatFocus() {
        console.log('\n🔧 Fixing chat focus and interaction...');
        console.log('════════════════════════════════════════════════════════════');
        
        // Force enable chat interaction
        await this.page.addStyleTag({
            content: `
                /* FORCE CHAT INPUT ACCESSIBILITY */
                textarea,
                input[type="text"],
                .chat-input,
                .message-input,
                [contenteditable="true"] {
                    pointer-events: auto !important;
                    z-index: 999999 !important;
                    position: relative !important;
                    background: white !important;
                    border: 2px solid #007bff !important;
                    outline: none !important;
                    opacity: 1 !important;
                    visibility: visible !important;
                }
                
                /* ENSURE CHAT CONTAINER IS SCROLLABLE */
                .chat-container,
                .messages-container,
                .conversation-container {
                    overflow-y: auto !important;
                    max-height: none !important;
                    pointer-events: auto !important;
                }
                
                /* REMOVE ANY BLOCKING OVERLAYS */
                .chat-overlay,
                .input-overlay {
                    display: none !important;
                }
            `
        });
        
        // JavaScript fixes for chat interaction
        await this.page.evaluate(() => {
            // Force enable all text inputs
            const inputSelectors = [
                'textarea', 'input[type="text"]', '.chat-input', 
                '.message-input', '[contenteditable]'
            ];
            
            function enableInputs(root) {
                inputSelectors.forEach(selector => {
                    const inputs = root.querySelectorAll(selector);
                    inputs.forEach(input => {
                        // Remove restrictions
                        input.disabled = false;
                        input.readOnly = false;
                        input.style.pointerEvents = 'auto';
                        input.style.zIndex = '999999';
                        input.style.position = 'relative';
                        input.style.opacity = '1';
                        input.style.visibility = 'visible';
                        
                        // Enable focus and interaction
                        input.tabIndex = 0;
                        
                        console.log('⚡ Enabled input:', selector);
                    });
                });
            }
            
            // Enable in regular DOM
            enableInputs(document);
            
            // Enable in shadow DOMs
            function enableInShadowRoots(root) {
                const allElements = root.querySelectorAll('*');
                allElements.forEach(el => {
                    if (el.shadowRoot) {
                        enableInputs(el.shadowRoot);
                        enableInShadowRoots(el.shadowRoot);
                    }
                });
            }
            
            enableInShadowRoots(document);
            
            // Fix scrolling containers
            const scrollContainers = document.querySelectorAll(
                '.chat-container, .messages-container, .conversation-container, .chat-messages'
            );
            
            scrollContainers.forEach(container => {
                container.style.overflowY = 'auto';
                container.style.pointerEvents = 'auto';
                console.log('📜 Enabled scrolling for container');
            });
            
            console.log('✅ Chat focus and interaction fixes applied');
        });
        
        this.fixes.chatFocusFixed = true;
        this.fixes.scrollingEnabled = true;
        console.log('✅ Chat focus fixes applied');
    }

    async testChatInteraction() {
        console.log('\n🧪 Testing chat interaction...');
        console.log('════════════════════════════════════════════════════════════');
        
        try {
            // Find chat input
            const chatInput = await this.page.locator('textarea').first();
            
            // Test focus
            console.log('🎯 Testing focus...');
            await chatInput.focus();
            console.log('✅ Focus successful');
            
            // Test typing
            console.log('⌨️  Testing typing...');
            await chatInput.fill('test message');
            const value = await chatInput.inputValue();
            
            if (value === 'test message') {
                console.log('✅ Typing successful');
                this.fixes.typingEnabled = true;
                
                // Clear test message
                await chatInput.fill('');
                
                return true;
            } else {
                console.log('❌ Typing failed');
                return false;
            }
            
        } catch (error) {
            console.log(`❌ Chat interaction test failed: ${error.message}`);
            return false;
        }
    }

    async generateReport() {
        console.log('\n📊 FRONTEND UI FIX REPORT');
        console.log('════════════════════════════════════════════════════════════');
        
        const report = {
            timestamp: new Date().toISOString(),
            fixes: this.fixes,
            summary: {
                totalFixes: Object.keys(this.fixes).length,
                successfulFixes: Object.values(this.fixes).filter(Boolean).length
            }
        };
        
        console.log('🔧 Applied Fixes:');
        Object.entries(this.fixes).forEach(([fix, success]) => {
            console.log(`  ${fix}: ${success ? '✅ SUCCESS' : '❌ FAILED'}`);
        });
        
        console.log('\n📊 Summary:');
        console.log(`  Successful Fixes: ${report.summary.successfulFixes}/${report.summary.totalFixes}`);
        
        const allFixed = Object.values(this.fixes).every(Boolean);
        console.log(`  Overall Status: ${allFixed ? '✅ ALL FIXED' : '⚠️  PARTIAL'}`);
        
        if (allFixed) {
            console.log('\n🎉 ALL UI ISSUES FIXED!');
            console.log('  ✅ No more blurry interface during backend processing');
            console.log('  ✅ No more spinning wheels blocking interaction');
            console.log('  ✅ Chat focus and typing working');
            console.log('  ✅ Scrolling enabled in chat area');
        }
        
        // Save report
        const fs = require('fs');
        const reportPath = `test-results/frontend-ui-fix-${Date.now()}.json`;
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        console.log(`\n📁 Report saved to: ${reportPath}`);
        
        return report;
    }

    async cleanup() {
        console.log('\n🔍 Browser kept open for manual verification...');
        console.log('Test the chat interaction - it should now work properly!');
        console.log('Press Ctrl+C to close when done.');
    }

    async run() {
        try {
            await this.initialize();
            await this.loadFrontend();
            await this.fixSpinnerAndBlur();
            await this.fixChatFocus();
            await this.testChatInteraction();
            await this.generateReport();
            
            console.log('\n🎯 Frontend UI fixes completed!');
            
        } catch (error) {
            console.error('❌ UI fix failed:', error);
        } finally {
            await this.cleanup();
        }
    }
}

// Run the fixer
if (require.main === module) {
    const fixer = new FrontendUIFixer();
    fixer.run().catch(console.error);
}

module.exports = FrontendUIFixer;
