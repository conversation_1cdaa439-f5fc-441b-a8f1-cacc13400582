#!/usr/bin/env node

/**
 * Profile Completion Frontend Test - Fixed Version
 * 
 * This test validates the profile completion workflow from the frontend:
 * 1. Accepts port number as command line argument
 * 2. Creates new user with ~25% profile completion
 * 3. Sends "make me a wheel" message
 * 4. Validates response time (< 10 seconds)
 * 5. Checks for meaningful profile completion questions
 * 
 * Usage: node test-profile-completion-frontend-fixed.cjs [port]
 * Example: node test-profile-completion-frontend-fixed.cjs 3001
 */

const { chromium } = require('playwright');

class ProfileCompletionFrontendTest {
    constructor(port = 5173) {
        this.port = port;
        this.baseUrl = `http://localhost:${port}`;
        this.testId = Math.random().toString(36).substring(7);
        this.results = [];
        this.startTime = Date.now();
        this.responseTimeThreshold = 10000; // 10 seconds
        this.websocketMessages = [];
    }

    async runTest() {
        console.log('🚀 Profile Completion Frontend Test (Fixed)');
        console.log(`   Port: ${this.port}`);
        console.log(`   URL: ${this.baseUrl}`);
        console.log(`   Test ID: ${this.testId}`);
        console.log();

        let browser, page;
        
        try {
            // Launch browser
            browser = await chromium.launch({ 
                headless: false,  // Show browser for debugging
                slowMo: 500      // Slow down for observation
            });
            
            page = await browser.newPage();
            
            // Setup monitoring
            await this.setupPageMonitoring(page);
            
            // Phase 1: Load application and setup user
            await this.testApplicationSetup(page);
            
            // Phase 2: Test profile completion workflow
            await this.testProfileCompletionWorkflow(page);

            // Generate report
            this.generateTestReport();
            
        } catch (error) {
            console.error('❌ Test failed:', error);
            this.results.push({
                phase: 'test_execution',
                error: error.message,
                success: false
            });
        } finally {
            if (browser) {
                await browser.close();
            }
        }
    }

    async setupPageMonitoring(page) {
        console.log('📊 Setting up page monitoring...');
        
        // Monitor console logs
        page.on('console', msg => {
            if (msg.type() === 'error') {
                console.log(`   🔴 Console Error: ${msg.text()}`);
            }
        });
        
        // Monitor WebSocket messages
        page.on('websocket', ws => {
            console.log('   🔗 WebSocket connection established');
            
            ws.on('framereceived', event => {
                try {
                    const data = JSON.parse(event.payload);
                    this.websocketMessages.push({
                        timestamp: new Date().toISOString(),
                        direction: 'in',
                        type: data.type,
                        data: data
                    });
                } catch (e) {
                    // Ignore non-JSON messages
                }
            });
            
            ws.on('framesent', event => {
                try {
                    const data = JSON.parse(event.payload);
                    this.websocketMessages.push({
                        timestamp: new Date().toISOString(),
                        direction: 'out',
                        type: data.type,
                        data: data
                    });
                } catch (e) {
                    // Ignore non-JSON messages
                }
            });
        });
    }

    async testApplicationSetup(page) {
        console.log('📱 Phase 1: Application setup and user creation (Following exact user sequence)');
        const phaseStart = Date.now();

        try {
            // Navigate to application
            await page.goto(this.baseUrl, { waitUntil: 'networkidle' });
            console.log('   ✓ Application loaded');

            // Wait for app to initialize
            await page.waitForSelector('app-shell', { timeout: 10000 });
            console.log('   ✓ App shell loaded');

            // Step 1: Open debug panel
            await page.keyboard.press('Control+Shift+D');
            await page.waitForSelector('debug-panel', { timeout: 5000 });
            console.log('   ✓ Debug panel opened');

            // Wait for debug panel content to load
            await page.waitForTimeout(2000);
            console.log('   ✓ Waited for debug panel content to load');

            // Step 2: Look for user creation elements in the debug panel
            console.log('   🔍 Searching for user creation controls...');

            // Try to find buttons, dropdowns, or other interactive elements
            let createUserButton;
            const buttonTexts = ['New German Student', 'Create User', 'New User', 'German Student', 'Add User', 'Create', 'New'];

            // First, try to find buttons with specific text
            for (const buttonText of buttonTexts) {
                try {
                    createUserButton = await page.locator(`button:has-text("${buttonText}"), input[value*="${buttonText}"], [title*="${buttonText}"]`);
                    if (await createUserButton.isVisible({ timeout: 1000 })) {
                        console.log(`   ✓ Found user creation element: "${buttonText}"`);
                        break;
                    }
                } catch (e) {
                    // Try next button text
                }
            }

            // If no specific button found, look for any buttons in the debug panel
            if (!createUserButton || !(await createUserButton.isVisible())) {
                const debugButtons = await page.locator('debug-panel button, debug-panel input[type="button"], debug-panel input[type="submit"]').all();
                for (const button of debugButtons) {
                    if (await button.isVisible()) {
                        const text = await button.textContent() || await button.getAttribute('value') || '';
                        if (text.toLowerCase().includes('user') || text.toLowerCase().includes('german') || text.toLowerCase().includes('student') || text.toLowerCase().includes('create') || text.toLowerCase().includes('new')) {
                            createUserButton = button;
                            console.log(`   ✓ Found potential user creation button: "${text}"`);
                            break;
                        }
                    }
                }
            }

            if (!createUserButton || !(await createUserButton.isVisible())) {
                // List all available buttons for debugging
                console.log('   🔍 Available buttons in debug panel:');
                const allButtons = await page.locator('debug-panel button, button').all();
                for (const button of allButtons) {
                    if (await button.isVisible()) {
                        const text = await button.textContent();
                        console.log(`     - "${text}"`);
                    }
                }

                // Also check for any elements that might be user creation controls
                console.log('   🔍 Looking for other user creation elements...');
                const debugPanelContent = await page.locator('debug-panel').innerHTML();
                console.log('   📋 Debug panel HTML preview:', debugPanelContent.substring(0, 500));

                // Try to find any clickable elements with user-related text
                const userElements = await page.locator('*:has-text("user"), *:has-text("User"), *:has-text("German"), *:has-text("Student")').all();
                for (const element of userElements) {
                    if (await element.isVisible()) {
                        const text = await element.textContent();
                        const tagName = await element.evaluate(el => el.tagName);
                        console.log(`     - ${tagName}: "${text}"`);
                    }
                }

                console.log('   ⚠️  User creation button not found, proceeding with existing user');
            }

            if (createUserButton && await createUserButton.isVisible()) {
                await createUserButton.click();
                console.log('   ✓ Clicked user creation button');

                // Wait for user creation
                await page.waitForTimeout(3000);
                console.log('   ✓ New user created');
            } else {
                console.log('   ⚠️  Proceeding without creating new user');
            }

            // Step 3: Select User 191 (our test user with profile gaps) in the "User" dropdown
            const userDropdown = await page.locator('select[id*="user"], select:has(option:text-matches("User"))').first();
            if (await userDropdown.isVisible()) {
                // Look for User 191 specifically (our test user with critical gaps)
                const user191Option = await userDropdown.locator('option[value="191"]').first();
                if (await user191Option.isVisible()) {
                    await userDropdown.selectOption('191');
                    console.log('   ✓ Selected User 191 (test user with profile gaps)');
                } else {
                    // Fallback: Get the last option (newest user)
                    const userOptions = await userDropdown.locator('option').all();
                    if (userOptions.length > 1) {
                        const lastOption = userOptions[userOptions.length - 1];
                        const userId = await lastOption.getAttribute('value');
                        await userDropdown.selectOption(userId);
                        console.log(`   ⚠️  User 191 not found, selected user ${userId} instead`);
                    } else {
                        console.log('   ⚠️  Using default user selection');
                    }
                }
            }

            // Step 4: Select "mistral-small-latest" in the LLM config dropdown
            const llmDropdown = await page.locator('select[id*="llm"], select:has(option:text-matches("mistral"))').first();
            if (await llmDropdown.isVisible()) {
                await llmDropdown.selectOption({ label: 'mistral-small-latest' });
                console.log('   ✓ Selected "mistral-small-latest" in LLM config dropdown');
            } else {
                console.log('   ⚠️  LLM dropdown not found, using default');
            }

            // Step 5: Click "Apply" button
            const applyButton = await page.locator('button:has-text("Apply")').first();
            if (await applyButton.isVisible()) {
                await applyButton.click();
                console.log('   ✓ Clicked "Apply" button');
            } else {
                console.log('   ⚠️  Apply button not found');
            }

            // Step 6: Wait 3 seconds
            await page.waitForTimeout(3000);
            console.log('   ✓ Waited 3 seconds for configuration to apply');

            // Step 7: Close debug panel to access chat
            await page.keyboard.press('Control+Shift+D');
            console.log('   ✓ Debug panel closed, ready for chat interaction');

            const duration = Date.now() - phaseStart;
            this.results.push({
                phase: 'application_setup',
                duration: duration,
                success: true
            });

            console.log(`   ✓ Application setup completed in ${duration}ms`);

        } catch (error) {
            const duration = Date.now() - phaseStart;
            console.log(`   ❌ Application setup failed: ${error.message}`);
            this.results.push({
                phase: 'application_setup',
                duration: duration,
                error: error.message,
                success: false
            });
            throw error;
        }

        console.log();
    }

    async testProfileCompletionWorkflow(page) {
        console.log('🎯 Phase 2: Profile completion workflow test (Following exact chat sequence)');
        const phaseStart = Date.now();

        try {
            // Step 8: Click in the chat area where "type your message" is written
            console.log('   🔍 Looking for chat input area...');

            let chatInput;
            const chatSelectors = [
                'input[placeholder*="type your message"]',
                'input[placeholder*="Type your message"]',
                'textarea[placeholder*="type your message"]',
                'textarea[placeholder*="Type your message"]',
                'input[placeholder*="message"]',
                'textarea[placeholder*="message"]',
                '.chat-input input',
                '.chat-input textarea',
                'chat-interface input',
                'chat-interface textarea',
                '#message-input',
                '.message-input'
            ];

            for (const selector of chatSelectors) {
                try {
                    const element = await page.locator(selector).first();
                    if (await element.isVisible({ timeout: 2000 })) {
                        chatInput = element;
                        console.log(`   ✓ Chat input found with selector: ${selector}`);
                        break;
                    }
                } catch (e) {
                    // Try next selector
                }
            }

            if (!chatInput) {
                // Try to find any input or textarea that might be the chat
                console.log('   🔍 Trying to find any visible input/textarea...');
                const allInputs = await page.locator('input, textarea').all();
                for (const input of allInputs) {
                    if (await input.isVisible()) {
                        const placeholder = await input.getAttribute('placeholder') || '';
                        console.log(`   🔍 Found input with placeholder: "${placeholder}"`);
                        if (placeholder.toLowerCase().includes('message') || placeholder.toLowerCase().includes('type')) {
                            chatInput = input;
                            console.log(`   ✓ Using input with placeholder: "${placeholder}"`);
                            break;
                        }
                    }
                }
            }

            if (!chatInput || !(await chatInput.isVisible())) {
                throw new Error('Chat input not found - please check if chat interface is properly loaded');
            }

            // Step 9: Click on the chat input to focus it
            await chatInput.click();
            console.log('   ✓ Clicked on chat input area');

            // Step 10: Type "make me a wheel" and send
            const wheelRequestMessage = "make me a wheel";
            await chatInput.fill(wheelRequestMessage);
            console.log(`   ✓ Typed: "${wheelRequestMessage}"`);

            // Send the message (try Enter key first, then look for send button)
            try {
                await chatInput.press('Enter');
                console.log('   ✓ Sent message with Enter key');
            } catch (e) {
                // Try to find and click send button
                const sendButton = await page.locator('button:has-text("Send"), button[type="submit"], .send-button').first();
                if (await sendButton.isVisible()) {
                    await sendButton.click();
                    console.log('   ✓ Sent message with Send button');
                } else {
                    throw new Error('Could not send message - no Enter key response and no Send button found');
                }
            }

            // Monitor for response within threshold
            const responseStart = Date.now();
            let responseReceived = false;
            let hangingDetected = false;
            let responseContent = '';

            console.log('   ⏳ Waiting for assistant response...');

            // Wait for assistant response
            try {
                await page.waitForFunction(() => {
                    const messages = document.querySelectorAll('[data-message-type="assistant"], .message.assistant, .assistant-message, .chat-message.assistant, .message[data-role="assistant"]');
                    return messages.length > 0;
                }, { timeout: this.responseTimeThreshold });

                const responseTime = Date.now() - responseStart;
                responseReceived = true;
                console.log(`   ✅ Response received in ${responseTime}ms`);

                // Get response content
                const assistantMessages = await page.locator('[data-message-type="assistant"], .message.assistant, .assistant-message, .chat-message.assistant, .message[data-role="assistant"]').allTextContents();
                responseContent = assistantMessages[assistantMessages.length - 1] || '';

                // Analyze response quality
                const isProfileQuestion = this.analyzeResponseQuality(responseContent);
                console.log(`   ${isProfileQuestion ? '✅' : '⚠️'} Response quality: ${isProfileQuestion ? 'Good profile completion question' : 'May not be asking for profile info'}`);
                console.log(`   📝 Response preview: "${responseContent.substring(0, 100)}..."`);

            } catch (timeoutError) {
                const responseTime = Date.now() - responseStart;
                hangingDetected = true;
                console.log(`   ❌ HANGING DETECTED: No response after ${responseTime}ms`);
            }

            const duration = Date.now() - phaseStart;
            this.results.push({
                phase: 'profile_completion_workflow',
                duration: duration,
                responseReceived: responseReceived,
                hangingDetected: hangingDetected,
                responseContent: responseContent,
                success: responseReceived && !hangingDetected
            });

            console.log(`   ${responseReceived && !hangingDetected ? '✅' : '❌'} Profile completion workflow test completed in ${duration}ms`);

        } catch (error) {
            const duration = Date.now() - phaseStart;
            console.log(`   ❌ Profile completion workflow test failed: ${error.message}`);
            this.results.push({
                phase: 'profile_completion_workflow',
                duration: duration,
                error: error.message,
                success: false
            });
        }

        console.log();
    }

    analyzeResponseQuality(responseContent) {
        const profileKeywords = [
            'tell me', 'about yourself', 'information', 'share', 'help me understand',
            'what brings you', 'goals', 'preferences', 'interests', 'activities',
            'focus', 'wellness', 'productivity', 'feeling', 'mood'
        ];
        
        const lowerResponse = responseContent.toLowerCase();
        return profileKeywords.some(keyword => lowerResponse.includes(keyword));
    }

    generateTestReport() {
        console.log('📊 Profile Completion Frontend Test Report');
        console.log('=' * 60);
        
        const totalPhases = this.results.length;
        const successfulPhases = this.results.filter(r => r.success).length;
        const totalDuration = Date.now() - this.startTime;
        
        console.log(`Port: ${this.port}`);
        console.log(`Total Test Phases: ${totalPhases}`);
        console.log(`Successful Phases: ${successfulPhases}`);
        console.log(`Failed Phases: ${totalPhases - successfulPhases}`);
        console.log(`Success Rate: ${((successfulPhases / totalPhases) * 100).toFixed(1)}%`);
        console.log(`Total Test Duration: ${totalDuration}ms`);
        console.log();
        
        console.log('Phase Results:');
        console.log('-'.repeat(40));
        
        this.results.forEach(result => {
            const status = result.success ? '✓' : '❌';
            const phase = result.phase.replace(/_/g, ' ').toUpperCase();
            console.log(`${status} ${phase}`);
            
            if (result.duration) {
                console.log(`    Duration: ${result.duration}ms`);
            }
            
            if (result.hangingDetected) {
                console.log('    ⚠️  HANGING DETECTED');
            }
            
            if (result.responseContent) {
                console.log(`    Response: ${result.responseContent.substring(0, 100)}...`);
            }
            
            if (result.error) {
                console.log(`    Error: ${result.error}`);
            }
            
            console.log();
        });
        
        // Overall assessment
        const hangingIssues = this.results.filter(r => r.hangingDetected).length;
        const responseTimeIssues = this.results.filter(r => r.duration > this.responseTimeThreshold).length;
        
        console.log('Assessment:');
        console.log(`- Hanging Issues: ${hangingIssues > 0 ? '❌ DETECTED' : '✅ NONE'}`);
        console.log(`- Response Time Issues: ${responseTimeIssues > 0 ? '❌ DETECTED' : '✅ NONE'}`);
        console.log(`- Overall Status: ${successfulPhases === totalPhases ? '✅ PASS' : '❌ FAIL'}`);
    }
}

// Main execution
async function main() {
    // Get port from command line arguments
    const port = process.argv[2] ? parseInt(process.argv[2]) : 5173;
    
    if (isNaN(port) || port < 1000 || port > 65535) {
        console.error('❌ Invalid port number. Please provide a valid port (1000-65535)');
        console.log('Usage: node test-profile-completion-frontend-fixed.cjs [port]');
        console.log('Example: node test-profile-completion-frontend-fixed.cjs 3001');
        process.exit(1);
    }
    
    const test = new ProfileCompletionFrontendTest(port);
    await test.runTest();
}

if (require.main === module) {
    main().catch(console.error);
}
