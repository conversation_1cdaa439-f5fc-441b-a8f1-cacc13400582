#!/usr/bin/env node

/**
 * Debug Frontend Input - Check what happens when user types and presses Enter
 */

const { chromium } = require('playwright');

async function debugFrontendInput() {
    console.log('🚀 Starting frontend input debug...');
    
    const browser = await chromium.launch({ headless: false });
    const page = await browser.newPage();
    
    // Set up comprehensive event monitoring
    await page.addInitScript(() => {
        window.inputDebug = {
            events: [],
            elements: {},
            functions: {}
        };
        
        // Monitor all events on textarea
        document.addEventListener('DOMContentLoaded', () => {
            const textarea = document.querySelector('textarea');
            if (textarea) {
                window.inputDebug.elements.textarea = textarea;
                
                ['keydown', 'keyup', 'keypress', 'input', 'change', 'focus', 'blur'].forEach(eventType => {
                    textarea.addEventListener(eventType, (e) => {
                        window.inputDebug.events.push({
                            timestamp: Date.now(),
                            type: eventType,
                            key: e.key,
                            code: e.code,
                            value: textarea.value,
                            disabled: textarea.disabled
                        });
                        console.log(`🎹 ${eventType}: key=${e.key}, value="${textarea.value}", disabled=${textarea.disabled}`);
                    });
                });
            }
        });
        
        // Monitor form submissions
        document.addEventListener('submit', (e) => {
            window.inputDebug.events.push({
                timestamp: Date.now(),
                type: 'submit',
                target: e.target.tagName,
                prevented: e.defaultPrevented
            });
            console.log(`📝 Form submit: target=${e.target.tagName}, prevented=${e.defaultPrevented}`);
        });
        
        // Monitor button clicks
        document.addEventListener('click', (e) => {
            if (e.target.tagName === 'BUTTON' || e.target.type === 'submit') {
                window.inputDebug.events.push({
                    timestamp: Date.now(),
                    type: 'button_click',
                    text: e.target.textContent,
                    type_attr: e.target.type
                });
                console.log(`🔘 Button click: "${e.target.textContent}", type=${e.target.type}`);
            }
        });
        
        // Monitor WebSocket sends
        const originalWebSocket = window.WebSocket;
        window.WebSocket = function(url, protocols) {
            const ws = new originalWebSocket(url, protocols);
            
            const originalSend = ws.send;
            ws.send = function(data) {
                try {
                    const parsed = JSON.parse(data);
                    window.inputDebug.events.push({
                        timestamp: Date.now(),
                        type: 'websocket_send',
                        messageType: parsed.type,
                        data: parsed
                    });
                    console.log(`📤 WebSocket send: ${parsed.type}`);
                } catch (e) {
                    console.log(`📤 WebSocket send raw: ${data}`);
                }
                return originalSend.call(this, data);
            };
            
            return ws;
        };
    });
    
    // Load page
    console.log('🌐 Loading page...');
    await page.goto('http://localhost:3000/');
    await page.waitForTimeout(3000);
    
    // Configure debug mode
    try {
        const userSelect = await page.locator('select').first();
        if (await userSelect.isVisible({ timeout: 2000 })) {
            await userSelect.selectOption('2');
            console.log('✅ Selected user 2');
            await page.waitForTimeout(1000);
        }
    } catch (e) {
        console.log('⚠️  No user select found');
    }
    
    // Check textarea state
    const textareaInfo = await page.evaluate(() => {
        const textarea = document.querySelector('textarea');
        return {
            exists: !!textarea,
            disabled: textarea ? textarea.disabled : null,
            placeholder: textarea ? textarea.placeholder : null,
            value: textarea ? textarea.value : null,
            readOnly: textarea ? textarea.readOnly : null,
            className: textarea ? textarea.className : null,
            id: textarea ? textarea.id : null
        };
    });
    
    console.log('📝 Textarea state:', JSON.stringify(textareaInfo, null, 2));
    
    // Try to enable textarea
    await page.evaluate(() => {
        const textarea = document.querySelector('textarea');
        if (textarea) {
            textarea.disabled = false;
            textarea.readOnly = false;
            textarea.removeAttribute('disabled');
            textarea.removeAttribute('readonly');
        }
    });
    
    // Type message
    console.log('💬 Typing message...');
    await page.fill('textarea', "I'm restless and need to do things physical. I have 2 hours. Make me a wheel");
    
    await page.waitForTimeout(1000);
    
    // Press Enter
    console.log('⌨️  Pressing Enter...');
    await page.press('textarea', 'Enter');
    
    await page.waitForTimeout(5000);
    
    // Check for send button and try clicking it
    const sendButton = await page.locator('button[type="submit"], button:has-text("Send"), button:has-text("send"), .send-button').first();
    if (await sendButton.isVisible({ timeout: 2000 })) {
        console.log('🔘 Found send button, clicking...');
        await sendButton.click();
        await page.waitForTimeout(2000);
    } else {
        console.log('⚠️  No send button found');
    }
    
    // Analyze events
    const events = await page.evaluate(() => window.inputDebug.events);
    
    console.log('\n📊 INPUT EVENT ANALYSIS:');
    console.log('════════════════════════════════════════════════════════════');
    
    console.log(`Total events: ${events.length}`);
    
    const eventTypes = {};
    events.forEach(event => {
        eventTypes[event.type] = (eventTypes[event.type] || 0) + 1;
    });
    
    console.log('\nEvent summary:');
    Object.entries(eventTypes).forEach(([type, count]) => {
        console.log(`  ${type}: ${count}`);
    });
    
    console.log('\nDetailed events:');
    events.forEach((event, i) => {
        const time = new Date(event.timestamp).toISOString().substr(11, 12);
        if (event.type === 'websocket_send') {
            console.log(`${i+1}. [${time}] ${event.type}: ${event.messageType}`);
        } else if (event.type === 'keydown' && event.key === 'Enter') {
            console.log(`${i+1}. [${time}] ${event.type}: ENTER pressed, value="${event.value}"`);
        } else if (event.type === 'submit') {
            console.log(`${i+1}. [${time}] ${event.type}: target=${event.target}, prevented=${event.prevented}`);
        } else if (event.type === 'button_click') {
            console.log(`${i+1}. [${time}] ${event.type}: "${event.text}"`);
        }
    });
    
    console.log('\n🔍 DIAGNOSIS:');
    const hasEnterPress = events.some(e => e.type === 'keydown' && e.key === 'Enter');
    const hasSubmit = events.some(e => e.type === 'submit');
    const hasButtonClick = events.some(e => e.type === 'button_click');
    const hasWebSocketSend = events.some(e => e.type === 'websocket_send' && e.messageType === 'chat_message');
    
    if (!hasEnterPress) {
        console.log('❌ Enter key not detected - keyboard event not working');
    } else if (!hasSubmit && !hasButtonClick) {
        console.log('❌ No form submission or button click - UI not responding to Enter');
    } else if (!hasWebSocketSend) {
        console.log('❌ No chat message sent via WebSocket - message handling broken');
        console.log('💡 Check: Form submission handler, WebSocket send logic');
    } else {
        console.log('✅ Message sending flow working correctly');
    }
    
    await browser.close();
}

debugFrontendInput().catch(console.error);
