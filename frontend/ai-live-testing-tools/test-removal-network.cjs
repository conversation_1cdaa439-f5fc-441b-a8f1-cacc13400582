#!/usr/bin/env node

/**
 * Test wheel item removal by monitoring network requests
 * 
 * This test opens the frontend and monitors network requests to verify
 * that wheel item removal sends the correct wheel item IDs to the backend.
 */

const puppeteer = require('puppeteer');

async function testRemovalNetwork(port = 3002) {
    const browser = await puppeteer.launch({ 
        headless: false,
        defaultViewport: { width: 1400, height: 900 },
        args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // Monitor network requests
    const removalRequests = [];
    page.on('request', request => {
        if (request.url().includes('/api/wheel-items/') && request.method() === 'DELETE') {
            const wheelItemId = request.url().split('/api/wheel-items/')[1].replace('/', '');
            removalRequests.push({
                url: request.url(),
                wheelItemId: wheelItemId,
                timestamp: Date.now()
            });
            console.log(`🔍 DELETE request detected for wheel item: ${wheelItemId}`);
            
            // Analyze the ID format
            if (wheelItemId.startsWith('llm_tailored_') || wheelItemId.startsWith('generic-')) {
                console.log(`🚨 ERROR: Frontend still sending activity tailored ID: ${wheelItemId}`);
            } else if (wheelItemId.startsWith('wheel-item-') || wheelItemId.startsWith('item_')) {
                console.log(`✅ SUCCESS: Frontend sending proper wheel item ID: ${wheelItemId}`);
            } else {
                console.log(`⚠️  WARNING: Unexpected ID format: ${wheelItemId}`);
            }
        }
    });
    
    // Monitor responses
    page.on('response', response => {
        if (response.url().includes('/api/wheel-items/') && response.request().method() === 'DELETE') {
            if (response.status() === 400) {
                console.log(`🚨 ERROR: Wheel item removal failed with 400 Bad Request`);
            } else if (response.status() === 200) {
                console.log(`✅ SUCCESS: Wheel item removal succeeded`);
            } else {
                console.log(`⚠️  WARNING: Unexpected response status: ${response.status()}`);
            }
        }
    });
    
    // Capture console logs
    page.on('console', msg => {
        const text = msg.text();
        if (text.includes('🗑️') || text.includes('wheel') || text.includes('removal') || text.includes('ERROR')) {
            console.log(`🖥️  ${text}`);
        }
    });
    
    try {
        console.log(`🚀 Opening frontend on port ${port}`);
        await page.goto(`http://localhost:${port}`, { waitUntil: 'networkidle0' });
        
        // Wait for app to load
        await page.waitForSelector('app-shell', { timeout: 10000 });
        console.log('✅ App loaded successfully');
        
        console.log('');
        console.log('🎯 MANUAL TEST INSTRUCTIONS:');
        console.log('1. Generate a wheel using the chat interface');
        console.log('2. Wait for the wheel to appear');
        console.log('3. Try to remove an activity from the wheel (click ❌ button)');
        console.log('4. Confirm the removal in the feedback modal');
        console.log('5. Check the console output for network request analysis');
        console.log('6. Press Ctrl+C when done testing');
        console.log('');
        console.log('Expected behavior:');
        console.log('- Wheel item IDs should start with "wheel-item-" or "item_"');
        console.log('- Should NOT see "llm_tailored_" or "generic-" IDs');
        console.log('- Removal should succeed with 200 status');
        console.log('');
        
        // Keep the browser open for manual testing
        await new Promise(() => {}); // Wait indefinitely
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        return { success: false, error: error.message };
    } finally {
        await browser.close();
    }
}

// Run the test
if (require.main === module) {
    const port = process.argv[2] || 3002;
    testRemovalNetwork(parseInt(port))
        .catch(error => {
            console.error('💥 Test execution failed:', error);
            process.exit(1);
        });
}

module.exports = { testRemovalNetwork };
