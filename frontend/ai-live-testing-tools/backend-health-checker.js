#!/usr/bin/env node

/**
 * Backend Health Checker
 * Diagnoses backend issues and validates configuration
 */

import WebSocket from 'ws';
import { CONFIG } from './config.js';

// Use native fetch (Node.js 18+) or fallback
const fetch = globalThis.fetch || (await import('node-fetch')).default;

class BackendHealthChecker {
  constructor() {
    this.results = {
      websocket: { status: 'unknown', details: [] },
      http: { status: 'unknown', details: [] },
      database: { status: 'unknown', details: [] },
      llm: { status: 'unknown', details: [] },
      apis: { status: 'unknown', details: [] }
    };
  }

  async runAllChecks() {
    console.log('🏥 Backend Health Check Starting...\n');

    await this.checkWebSocketConnection();
    await this.checkHttpConnection();
    await this.checkApiEndpoints();
    await this.checkDatabaseConnection();
    await this.checkLLMConfiguration();

    this.printSummary();
    return this.results;
  }

  async checkWebSocketConnection() {
    console.log('🔌 Testing WebSocket Connection...');
    
    try {
      const ws = new WebSocket(CONFIG.backend.websocketUrl);
      
      const connectionPromise = new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Connection timeout'));
        }, CONFIG.testing.shortTimeout);

        ws.on('open', () => {
          clearTimeout(timeout);
          this.results.websocket.status = 'healthy';
          this.results.websocket.details.push('✅ WebSocket connection established');
          
          // Test message sending
          ws.send(JSON.stringify({
            type: 'chat_message',
            message: 'Health check test',
            user_profile_id: '2'
          }));
          
          resolve();
        });

        ws.on('message', (data) => {
          try {
            const message = JSON.parse(data);
            this.results.websocket.details.push(`📨 Received: ${message.type || 'unknown'}`);
          } catch (e) {
            this.results.websocket.details.push(`⚠️ Invalid JSON received: ${data}`);
          }
        });

        ws.on('error', (error) => {
          clearTimeout(timeout);
          reject(error);
        });
      });

      await connectionPromise;
      ws.close();
      
    } catch (error) {
      this.results.websocket.status = 'error';
      this.results.websocket.details.push(`❌ WebSocket error: ${error.message}`);
    }
  }

  async checkHttpConnection() {
    console.log('🌐 Testing HTTP Connection...');
    
    try {
      const response = await fetch(CONFIG.backend.httpUrl, {
        timeout: CONFIG.testing.shortTimeout
      });
      
      this.results.http.status = 'healthy';
      this.results.http.details.push(`✅ HTTP connection successful (${response.status})`);
      
    } catch (error) {
      this.results.http.status = 'error';
      this.results.http.details.push(`❌ HTTP error: ${error.message}`);
    }
  }

  async checkApiEndpoints() {
    console.log('🔗 Testing API Endpoints...');
    
    const allEndpoints = [
      ...CONFIG.apiEndpoints.debug,
      ...CONFIG.apiEndpoints.auth,
      ...CONFIG.apiEndpoints.logging
    ];

    let healthyCount = 0;
    
    for (const endpoint of allEndpoints) {
      try {
        const url = `${CONFIG.backend.httpUrl}${endpoint}`;
        const response = await fetch(url, {
          timeout: CONFIG.testing.shortTimeout,
          headers: {
            'Content-Type': 'application/json'
          }
        });
        
        if (response.status < 500) {
          healthyCount++;
          this.results.apis.details.push(`✅ ${endpoint} (${response.status})`);
        } else {
          this.results.apis.details.push(`⚠️ ${endpoint} (${response.status})`);
        }
        
      } catch (error) {
        this.results.apis.details.push(`❌ ${endpoint}: ${error.message}`);
      }
    }
    
    this.results.apis.status = healthyCount === allEndpoints.length ? 'healthy' : 
                               healthyCount > 0 ? 'partial' : 'error';
  }

  async checkDatabaseConnection() {
    console.log('🗄️ Testing Database Connection...');
    
    try {
      // Test debug users endpoint to check database
      const response = await fetch(`${CONFIG.backend.httpUrl}/api/debug/users/`, {
        timeout: CONFIG.testing.shortTimeout
      });
      
      if (response.ok) {
        const users = await response.json();
        this.results.database.status = 'healthy';
        this.results.database.details.push(`✅ Database accessible, ${users.length} users found`);
        
        // Check for test users
        const testUser = users.find(u => u.id === '2');
        if (testUser) {
          this.results.database.details.push(`✅ Test user found: ${testUser.name}`);
        } else {
          this.results.database.details.push(`⚠️ Test user (ID: 2) not found`);
        }
        
      } else if (response.status === 403) {
        this.results.database.status = 'partial';
        this.results.database.details.push(`⚠️ Debug endpoints disabled (production mode)`);
      } else {
        throw new Error(`HTTP ${response.status}`);
      }
      
    } catch (error) {
      this.results.database.status = 'error';
      this.results.database.details.push(`❌ Database error: ${error.message}`);
    }
  }

  async checkLLMConfiguration() {
    console.log('🤖 Testing LLM Configuration...');
    
    try {
      // Test LLM configs endpoint
      const response = await fetch(`${CONFIG.backend.httpUrl}/api/debug/llm-configs/`, {
        timeout: CONFIG.testing.shortTimeout
      });
      
      if (response.ok) {
        const configs = await response.json();
        this.results.llm.status = configs.length > 0 ? 'healthy' : 'warning';
        this.results.llm.details.push(`✅ LLM configs accessible, ${configs.length} configs found`);
        
        // Check for default config
        const defaultConfig = configs.find(c => c.is_default);
        if (defaultConfig) {
          this.results.llm.details.push(`✅ Default LLM config: ${defaultConfig.name}`);
        } else {
          this.results.llm.details.push(`⚠️ No default LLM config found`);
          this.results.llm.status = 'warning';
        }
        
      } else if (response.status === 403) {
        this.results.llm.status = 'partial';
        this.results.llm.details.push(`⚠️ LLM config endpoints disabled (production mode)`);
      } else {
        throw new Error(`HTTP ${response.status}`);
      }
      
    } catch (error) {
      this.results.llm.status = 'error';
      this.results.llm.details.push(`❌ LLM config error: ${error.message}`);
    }
  }

  printSummary() {
    console.log('\n📊 Health Check Summary:');
    console.log('========================');
    
    Object.entries(this.results).forEach(([category, result]) => {
      const icon = result.status === 'healthy' ? '✅' : 
                   result.status === 'partial' ? '⚠️' : 
                   result.status === 'warning' ? '🟡' : '❌';
      
      console.log(`\n${icon} ${category.toUpperCase()}: ${result.status}`);
      result.details.forEach(detail => console.log(`  ${detail}`));
    });

    // Overall status
    const statuses = Object.values(this.results).map(r => r.status);
    const hasErrors = statuses.includes('error');
    const hasWarnings = statuses.includes('warning') || statuses.includes('partial');
    
    console.log('\n🎯 Overall Status:');
    if (hasErrors) {
      console.log('❌ CRITICAL ISSUES DETECTED - Backend needs attention');
    } else if (hasWarnings) {
      console.log('⚠️ WARNINGS DETECTED - Some features may not work properly');
    } else {
      console.log('✅ ALL SYSTEMS HEALTHY - Ready for testing');
    }

    // Recommendations
    console.log('\n💡 Recommendations:');
    if (this.results.llm.status === 'error' || this.results.llm.status === 'warning') {
      console.log('  - Run: ./fix_backend_llm.sh to fix LLM configuration');
    }
    if (this.results.database.status === 'error') {
      console.log('  - Check database connection and migrations');
    }
    if (this.results.websocket.status === 'error') {
      console.log('  - Ensure backend server is running on port 8000');
    }
  }
}

// Run health check if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const checker = new BackendHealthChecker();
  checker.runAllChecks().catch(console.error);
}

export { BackendHealthChecker };
