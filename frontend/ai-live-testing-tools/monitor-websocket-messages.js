/**
 * Simple WebSocket message monitor that can be run in browser console
 * Copy and paste this into the browser console to monitor WebSocket messages
 */

console.log('🔍 Starting WebSocket message monitor...');

// Store original WebSocket
const OriginalWebSocket = window.WebSocket;

// Override WebSocket constructor
window.WebSocket = function(url, protocols) {
  console.log('🔌 Creating WebSocket connection to:', url);
  
  const ws = new OriginalWebSocket(url, protocols);
  
  // Store original methods
  const originalSend = ws.send;
  const originalOnMessage = ws.onmessage;
  
  // Override send method
  ws.send = function(data) {
    console.log('📤 SENDING:', data);
    try {
      const parsed = JSON.parse(data);
      console.log('📤 PARSED SEND:', parsed);
    } catch (e) {
      console.log('📤 RAW SEND:', data);
    }
    return originalSend.call(this, data);
  };
  
  // Override onmessage
  const originalOnMessageHandler = ws.onmessage;
  ws.onmessage = function(event) {
    console.log('📥 RECEIVED RAW:', event.data);
    try {
      const parsed = JSON.parse(event.data);
      console.log('📥 PARSED RECEIVED:', parsed);
      
      if (parsed.type === 'wheel_data') {
        console.log('🎡 WHEEL DATA DETECTED!');
        console.log('🎡 Wheel structure:', {
          hasWheel: !!parsed.wheel,
          hasItems: !!(parsed.wheel && parsed.wheel.items),
          itemCount: parsed.wheel && parsed.wheel.items ? parsed.wheel.items.length : 0,
          wheelKeys: parsed.wheel ? Object.keys(parsed.wheel) : []
        });
        
        if (parsed.wheel && parsed.wheel.items && parsed.wheel.items.length > 0) {
          console.log('🎡 First item:', parsed.wheel.items[0]);
        }
      }
    } catch (e) {
      console.log('📥 Failed to parse:', e);
    }
    
    // Call original handler
    if (originalOnMessageHandler) {
      return originalOnMessageHandler.call(this, event);
    }
  };
  
  return ws;
};

console.log('✅ WebSocket monitor installed. Send a wheel generation request to see messages.');
console.log('💡 To test: Type a message like "generate me a wheel please" in the chat.');

// Also monitor the app-shell wheel data handling
if (window.customElements && window.customElements.get('app-shell')) {
  console.log('🔍 Monitoring app-shell wheel data handling...');
  
  // Find app-shell element
  const appShell = document.querySelector('app-shell');
  if (appShell) {
    // Store original handleWheelGenerated method
    const originalHandleWheelGenerated = appShell.handleWheelGenerated;
    
    if (originalHandleWheelGenerated) {
      appShell.handleWheelGenerated = function(data) {
        console.log('🎡 APP-SHELL: handleWheelGenerated called with:', data);
        console.log('🎡 APP-SHELL: Data structure check:', {
          hasWheel: !!data.wheel,
          hasItems: !!(data.wheel && data.wheel.items),
          isItemsArray: Array.isArray(data.wheel && data.wheel.items),
          itemCount: data.wheel && data.wheel.items ? data.wheel.items.length : 0
        });
        
        const result = originalHandleWheelGenerated.call(this, data);
        
        console.log('🎡 APP-SHELL: After processing, wheelData:', this.wheelData);
        
        return result;
      };
      console.log('✅ App-shell wheel handler monitoring installed');
    } else {
      console.log('⚠️ Could not find handleWheelGenerated method on app-shell');
    }
  } else {
    console.log('⚠️ Could not find app-shell element');
  }
} else {
  console.log('⚠️ app-shell custom element not found');
}

// Monitor game-wheel component updates
function monitorGameWheel() {
  const gameWheel = document.querySelector('game-wheel');
  if (gameWheel) {
    console.log('🎮 Found game-wheel component');
    console.log('🎮 Current wheelData:', gameWheel.wheelData);
    console.log('🎮 Current segments:', gameWheel.segments);
  } else {
    console.log('🎮 No game-wheel component found yet');
  }
}

// Check for game-wheel periodically
setInterval(monitorGameWheel, 2000);

console.log('🎯 Monitor setup complete. Ready to track wheel generation!');
