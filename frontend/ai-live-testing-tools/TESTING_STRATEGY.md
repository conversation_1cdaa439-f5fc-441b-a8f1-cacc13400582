# Live Testing Strategy for Debug vs Production Mode Implementation

## Overview

This document outlines the comprehensive testing strategy to validate the frontend debug vs production mode implementation and identify/fix issues discovered in the backend logs.

## Issues Identified from Backend Logs

### 1. LLM Configuration Missing (HIGH PRIORITY)
```
ERROR: No LLMConfig provided and no default LLMConfig found
```
**Impact**: Prevents AI responses and wheel generation
**Fix**: Run LLM configuration setup scripts

### 2. Database Query Error (HIGH PRIORITY)
```
AttributeError: Cannot find 'current_environment__domain_relationships__domain' on UserProfile
```
**Impact**: User profile tool fails
**Fix**: Correct prefetch_related parameter in get_user_profile_tool.py

### 3. Missing Tool (MEDIUM PRIORITY)
```
Tool code 'get_user_state' not found in database
```
**Impact**: Some agent functionality unavailable
**Fix**: Implement and register missing tool

## Testing Strategy

### Phase 1: Quick Validation
**Goal**: Verify frontend changes are correctly implemented
**Tool**: `quick-frontend-test.js`
**Duration**: 2 minutes

```bash
cd frontend/ai-live-testing-tools
node quick-frontend-test.js
```

**Validates**:
- Required files exist
- Environment configuration is correct
- Type definitions are complete
- Component structure is valid
- Package scripts are available

### Phase 2: Backend Health Check
**Goal**: Diagnose backend issues and API availability
**Tool**: `backend-health-checker.js`
**Duration**: 5 minutes

```bash
node backend-health-checker.js
```

**Checks**:
- WebSocket connection
- HTTP API endpoints
- Database connectivity
- LLM configuration
- Debug API availability

### Phase 3: Real-time Monitoring
**Goal**: Monitor WebSocket communication during testing
**Tool**: `websocket-monitor.js`
**Duration**: Continuous during testing

```bash
node websocket-monitor.js
```

**Features**:
- Real-time message logging
- Error pattern detection
- Performance metrics
- Interactive commands (t=test, s=stats, q=quit)

### Phase 4: Backend Issue Resolution
**Goal**: Fix identified backend issues
**Tool**: `fix-backend-issues.py`
**Duration**: 10 minutes

```bash
python3 fix-backend-issues.py
```

**Fixes**:
- LLM configuration setup
- Database query corrections
- Missing tool identification
- API endpoint validation

### Phase 5: Integration Testing
**Goal**: End-to-end testing of both modes
**Tool**: `integration-test-suite.js`
**Duration**: 15 minutes

```bash
node integration-test-suite.js
```

**Tests**:
- Debug mode functionality
- Production mode authentication
- WebSocket communication
- API endpoint integration

## Manual Testing Procedures

### Debug Mode Testing

1. **Start Debug Mode**
   ```bash
   cd frontend
   npm run dev:debug
   ```

2. **Verify Debug Features**
   - Press `Ctrl+Shift+D` to open debug panel
   - Check user selection dropdown
   - Test LLM configuration selection
   - Try backend URL changes
   - Verify no mocked wheel on startup

3. **Test Messaging**
   - Send chat message: "I want to generate a wheel"
   - Monitor WebSocket traffic
   - Check for error patterns
   - Verify AI responses

### Production Mode Testing

1. **Start Production Mode**
   ```bash
   npm run dev:prod
   ```

2. **Verify Production Features**
   - Check login form appears
   - Test invalid credentials
   - Try demo mode option
   - Verify security features

3. **Test Authentication Flow**
   - Attempt login with test credentials
   - Check token validation
   - Test session management

## Error Detection and Monitoring

### Automated Error Detection
The monitoring tools automatically detect:
- WebSocket connection failures
- Message format violations
- Authentication errors
- API endpoint failures
- Database connectivity issues
- LLM configuration problems

### Error Patterns to Watch
```javascript
const errorPatterns = [
  'No LLMConfig provided',
  'AttributeError: Cannot find',
  'Tool code .* not found',
  'Connection refused',
  'CORS error',
  'Authentication failed',
  'Token expired'
];
```

### Performance Thresholds
```javascript
const thresholds = {
  websocketConnection: 2000, // ms
  apiResponse: 5000, // ms
  messageRoundTrip: 1000, // ms
  pageLoad: 10000 // ms
};
```

## Test Execution Order

### Recommended Sequence

1. **Quick Frontend Test** (2 min)
   - Validates implementation completeness
   - Identifies missing files or configuration

2. **Backend Health Check** (5 min)
   - Diagnoses backend connectivity
   - Identifies configuration issues

3. **Fix Backend Issues** (10 min)
   - Resolves LLM configuration
   - Fixes database queries
   - Addresses missing tools

4. **Start WebSocket Monitor** (continuous)
   - Provides real-time debugging
   - Captures all communication

5. **Manual Debug Mode Test** (10 min)
   - Validates debug panel functionality
   - Tests user/LLM selection
   - Verifies clean start experience

6. **Manual Production Mode Test** (10 min)
   - Tests authentication flow
   - Validates security features
   - Checks demo mode

7. **Integration Test Suite** (15 min)
   - Comprehensive automated testing
   - Generates detailed reports
   - Validates all scenarios

## Success Criteria

### Debug Mode Success
- ✅ Debug panel opens with `Ctrl+Shift+D`
- ✅ User selection dropdown populated from database
- ✅ LLM configuration selection available
- ✅ Backend URL can be changed
- ✅ No mocked wheel on startup
- ✅ Chat messages generate AI responses
- ✅ WebSocket communication functional

### Production Mode Success
- ✅ Login form appears on startup
- ✅ Invalid credentials rejected
- ✅ Demo mode option available
- ✅ Token validation working
- ✅ Session management functional
- ✅ Security features active

### Backend Integration Success
- ✅ All API endpoints accessible
- ✅ WebSocket connection stable
- ✅ LLM configuration available
- ✅ Database queries successful
- ✅ No critical errors in logs

## Troubleshooting Guide

### Common Issues and Solutions

**Issue**: WebSocket connection fails
**Solution**: Check backend server is running on port 8000

**Issue**: Debug panel not opening
**Solution**: Verify `VITE_DEBUG_ENABLED=true` in .env.development

**Issue**: User selection empty
**Solution**: Check `/api/debug/users/` endpoint accessibility

**Issue**: LLM responses missing
**Solution**: Run `./fix_backend_llm.sh` or setup LLM configuration

**Issue**: Authentication fails
**Solution**: Verify authentication endpoints and CORS settings

## Reporting and Documentation

### Automated Reports
- Test results saved to `./test-results/`
- WebSocket logs saved to `./logs/`
- Error patterns highlighted in reports

### Manual Documentation
- Update `BACKEND_LOGS.md` with new findings
- Document fixes in implementation summary
- Record performance metrics

## Continuous Monitoring

### During Development
- Keep WebSocket monitor running
- Watch for new error patterns
- Monitor performance metrics
- Track success rates

### Before Deployment
- Run full integration test suite
- Verify all success criteria met
- Generate comprehensive report
- Document any remaining issues

This testing strategy ensures comprehensive validation of the debug vs production mode implementation while providing tools to quickly identify and resolve issues.
