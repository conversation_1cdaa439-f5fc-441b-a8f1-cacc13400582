const { chromium } = require('playwright');

class WheelDataDebugTester {
    constructor() {
        this.browser = null;
        this.page = null;
        this.wheelDataReceived = null;
        this.frontendWheelData = null;
    }

    async initialize() {
        console.log('🚀 Initializing Wheel Data Debug Tester...');
        
        this.browser = await chromium.launch({ 
            headless: false,
            slowMo: 500
        });
        
        this.page = await this.browser.newPage();
        
        // Listen to console logs to capture wheel data processing
        this.page.on('console', msg => {
            const text = msg.text();
            if (text.includes('[WHEEL]') || text.includes('wheel') || text.includes('segments')) {
                console.log(`🖥️  [FRONTEND] ${text}`);
            }
        });

        console.log('✅ Tester initialized successfully');
    }

    async loadFrontendAndSendMessage() {
        console.log('\n🌐 Loading frontend and sending wheel request...');
        
        try {
            await this.page.goto('http://localhost:3000/', {
                waitUntil: 'networkidle',
                timeout: 30000
            });
            
            // Wait for the app to initialize
            await this.page.waitForTimeout(3000);
            
            // Send the message
            const chatInput = await this.page.locator('textarea, input[type="text"]').first();
            await chatInput.waitFor({ timeout: 10000 });
            
            await chatInput.fill("hey, would you generate me a wheel please ? I'm tired but not sleepy");
            await chatInput.press('Enter');
            
            console.log('✅ Message sent, waiting for wheel data...');
            
            // Wait for wheel data to be processed
            await this.page.waitForTimeout(20000);
            
            return true;
        } catch (error) {
            console.error('❌ Failed to load frontend or send message:', error.message);
            return false;
        }
    }

    async inspectWheelData() {
        console.log('\n🔍 Inspecting wheel data in frontend...');
        
        try {
            // Get the wheel data from the frontend state
            const wheelDataInfo = await this.page.evaluate(() => {
                // Access the app-shell component
                const appShell = document.querySelector('app-shell');
                if (!appShell) return { error: 'No app-shell found' };
                
                // Get wheel data
                const wheelData = appShell.wheelData;
                
                // Get game-wheel component
                const gameWheel = appShell.shadowRoot?.querySelector('game-wheel');
                
                return {
                    hasAppShell: !!appShell,
                    hasWheelData: !!wheelData,
                    wheelDataStructure: wheelData ? {
                        hasSegments: !!wheelData.segments,
                        segmentsLength: wheelData.segments?.length || 0,
                        wheelId: wheelData.wheelId,
                        createdAt: wheelData.createdAt,
                        firstSegment: wheelData.segments?.[0] || null
                    } : null,
                    hasGameWheel: !!gameWheel,
                    gameWheelInfo: gameWheel ? {
                        hasWheelData: !!gameWheel.wheelData,
                        segmentsLength: gameWheel.segments?.length || 0,
                        errorMessage: gameWheel.errorMessage || null,
                        isSpinning: gameWheel.isSpinning || false
                    } : null
                };
            });
            
            console.log('📊 Frontend Wheel Data Analysis:');
            console.log(`  App Shell Found: ${wheelDataInfo.hasAppShell ? '✅' : '❌'}`);
            console.log(`  Wheel Data Present: ${wheelDataInfo.hasWheelData ? '✅' : '❌'}`);
            
            if (wheelDataInfo.wheelDataStructure) {
                const wd = wheelDataInfo.wheelDataStructure;
                console.log(`  Segments Array: ${wd.hasSegments ? '✅' : '❌'}`);
                console.log(`  Segments Count: ${wd.segmentsLength}`);
                console.log(`  Wheel ID: ${wd.wheelId || 'none'}`);
                console.log(`  Created At: ${wd.createdAt || 'none'}`);
                
                if (wd.firstSegment) {
                    console.log(`  First Segment:`, wd.firstSegment);
                }
            }
            
            console.log(`  Game Wheel Component: ${wheelDataInfo.hasGameWheel ? '✅' : '❌'}`);
            
            if (wheelDataInfo.gameWheelInfo) {
                const gw = wheelDataInfo.gameWheelInfo;
                console.log(`  Game Wheel Has Data: ${gw.hasWheelData ? '✅' : '❌'}`);
                console.log(`  Game Wheel Segments: ${gw.segmentsLength}`);
                console.log(`  Error Message: ${gw.errorMessage || 'none'}`);
                console.log(`  Is Spinning: ${gw.isSpinning ? '✅' : '❌'}`);
            }
            
            return wheelDataInfo;
            
        } catch (error) {
            console.error('❌ Failed to inspect wheel data:', error.message);
            return null;
        }
    }

    async inspectWheelCanvas() {
        console.log('\n🎨 Inspecting wheel canvas rendering...');
        
        try {
            const canvasInfo = await this.page.evaluate(() => {
                const gameWheel = document.querySelector('app-shell')?.shadowRoot?.querySelector('game-wheel');
                if (!gameWheel) return { error: 'No game-wheel found' };
                
                const canvas = gameWheel.shadowRoot?.querySelector('canvas');
                if (!canvas) return { error: 'No canvas found' };
                
                return {
                    hasCanvas: true,
                    canvasWidth: canvas.width,
                    canvasHeight: canvas.height,
                    canvasStyle: canvas.style.cssText,
                    canvasVisible: canvas.offsetWidth > 0 && canvas.offsetHeight > 0
                };
            });
            
            console.log('🎨 Canvas Analysis:');
            if (canvasInfo.error) {
                console.log(`  ❌ ${canvasInfo.error}`);
            } else {
                console.log(`  Canvas Found: ✅`);
                console.log(`  Canvas Size: ${canvasInfo.canvasWidth}x${canvasInfo.canvasHeight}`);
                console.log(`  Canvas Visible: ${canvasInfo.canvasVisible ? '✅' : '❌'}`);
                console.log(`  Canvas Style: ${canvasInfo.canvasStyle || 'none'}`);
            }
            
            return canvasInfo;
            
        } catch (error) {
            console.error('❌ Failed to inspect canvas:', error.message);
            return null;
        }
    }

    async checkWheelContainer() {
        console.log('\n📦 Checking wheel container visibility...');
        
        try {
            const containerInfo = await this.page.evaluate(() => {
                const wheelContainer = document.querySelector('.wheel-container');
                const wheelSection = document.querySelector('.wheel-section');
                const gameWheel = document.querySelector('game-wheel');
                
                return {
                    hasWheelContainer: !!wheelContainer,
                    hasWheelSection: !!wheelSection,
                    hasGameWheelElement: !!gameWheel,
                    wheelContainerVisible: wheelContainer ? wheelContainer.offsetWidth > 0 : false,
                    wheelSectionVisible: wheelSection ? wheelSection.offsetWidth > 0 : false,
                    gameWheelVisible: gameWheel ? gameWheel.offsetWidth > 0 : false
                };
            });
            
            console.log('📦 Container Analysis:');
            console.log(`  Wheel Container: ${containerInfo.hasWheelContainer ? '✅' : '❌'} (visible: ${containerInfo.wheelContainerVisible})`);
            console.log(`  Wheel Section: ${containerInfo.hasWheelSection ? '✅' : '❌'} (visible: ${containerInfo.wheelSectionVisible})`);
            console.log(`  Game Wheel Element: ${containerInfo.hasGameWheelElement ? '✅' : '❌'} (visible: ${containerInfo.gameWheelVisible})`);
            
            return containerInfo;
            
        } catch (error) {
            console.error('❌ Failed to check containers:', error.message);
            return null;
        }
    }

    async runCompleteDebugTest() {
        console.log('\n🔬 Running Complete Wheel Data Debug Test');
        console.log('════════════════════════════════════════════════════════════');
        
        const success = await this.loadFrontendAndSendMessage();
        if (!success) {
            console.log('❌ Failed to load frontend or send message');
            return;
        }
        
        await this.inspectWheelData();
        await this.inspectWheelCanvas();
        await this.checkWheelContainer();
        
        console.log('\n💡 Debug Summary:');
        console.log('  1. Check if wheel data is being received and processed correctly');
        console.log('  2. Verify wheel component is rendering segments');
        console.log('  3. Ensure canvas is visible and properly sized');
        console.log('  4. Confirm wheel container elements are present');
    }

    async cleanup() {
        if (this.browser) {
            await this.browser.close();
        }
    }
}

// Run the debug test
async function main() {
    const tester = new WheelDataDebugTester();
    
    try {
        await tester.initialize();
        await tester.runCompleteDebugTest();
    } catch (error) {
        console.error('❌ Debug test execution failed:', error);
    } finally {
        await tester.cleanup();
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = WheelDataDebugTester;
