#!/usr/bin/env node

/**
 * Comprehensive test for the complete implementation including:
 * - Forced wheel generation
 * - Draggable debug panel
 * - Time slider (10min-4h)
 * - Activity modal enhancements
 * - Zoom effects and modal positioning
 */

const { chromium } = require('playwright');

const FRONTEND_URL = process.argv[2] || 'http://localhost:3001';

async function testCompleteImplementation() {
  console.log('🚀 Starting comprehensive implementation test...');
  console.log(`📍 Testing frontend at: ${FRONTEND_URL}`);

  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 1000 // Slow down for visual inspection
  });
  
  const context = await browser.newContext({
    viewport: { width: 1400, height: 900 }
  });
  
  const page = await context.newPage();

  try {
    // Navigate to the frontend
    console.log('🌐 Navigating to frontend...');
    await page.goto(FRONTEND_URL);
    await page.waitForTimeout(3000);

    // Test 1: Debug Panel Functionality
    console.log('\n📋 TEST 1: Debug Panel Functionality');

    // Open debug panel
    await page.keyboard.press('Control+Shift+D');
    await page.waitForTimeout(1000);

    const debugPanel = await page.locator('debug-panel').first();
    const isVisible = await debugPanel.isVisible();
    console.log(`   Debug panel visible: ${isVisible ? '✅' : '❌'}`);

    if (isVisible) {
      // Test draggability by getting initial position
      const initialBox = await debugPanel.boundingBox();
      console.log(`   Initial position: x=${initialBox.x}, y=${initialBox.y}`);

      // Try to drag the panel - drag from center to avoid clicking on interactive elements
      const centerX = initialBox.x + initialBox.width / 2;
      const centerY = initialBox.y + 20; // Near the top but not on close button

      await page.mouse.move(centerX, centerY);
      await page.mouse.down();
      await page.mouse.move(centerX - 100, centerY + 50);
      await page.mouse.up();
      await page.waitForTimeout(500);

      const newBox = await debugPanel.boundingBox();
      const moved = (Math.abs(newBox.x - initialBox.x) > 10 || Math.abs(newBox.y - initialBox.y) > 10);
      console.log(`   Panel draggable: ${moved ? '✅' : '❌'}`);
      console.log(`   New position: x=${newBox.x}, y=${newBox.y}`);

      // Select PhiPhi user (ID: 2)
      const userSelect = await page.locator('debug-panel select').first();
      if (await userSelect.isVisible()) {
        await userSelect.selectOption('2');
        console.log('   Selected PhiPhi user: ✅');
        await page.waitForTimeout(500);
      }
    }

    // Test 2: Time Slider Functionality
    console.log('\n⏰ TEST 2: Time Slider Functionality');
    
    const timeSlider = await page.locator('input[type="range"]').first();
    if (await timeSlider.isVisible()) {
      // Test different time values
      await timeSlider.fill('7'); // 7% should be around 26 minutes
      await page.waitForTimeout(500);
      
      const timeDisplay = await page.locator('.potentiometer-value').first().textContent();
      console.log(`   Time at 7%: ${timeDisplay}`);
      
      await timeSlider.fill('100'); // 100% should be 4h
      await page.waitForTimeout(500);
      
      const maxTimeDisplay = await page.locator('.potentiometer-value').first().textContent();
      console.log(`   Time at 100%: ${maxTimeDisplay}`);
      
      // Reset to test value
      await timeSlider.fill('7');
      console.log('   Time slider functionality: ✅');
    }

    // Test 3: Forced Wheel Generation
    console.log('\n🎡 TEST 3: Forced Wheel Generation');
    
    const generateButton = await page.locator('.generate-button');
    if (await generateButton.isVisible()) {
      console.log('   Generate button found: ✅');
      
      // Click generate button
      await generateButton.click();
      console.log('   Clicked generate button: ✅');
      
      // Wait for wheel to appear
      await page.waitForTimeout(10000); // Wait up to 10 seconds for wheel generation
      
      const wheelComponent = await page.locator('game-wheel');
      const wheelVisible = await wheelComponent.isVisible();
      console.log(`   Wheel generated: ${wheelVisible ? '✅' : '❌'}`);
      
      if (wheelVisible) {
        // Test 4: Activity List and Modal
        console.log('\n📝 TEST 4: Activity List and Modal');
        
        const activityItems = await page.locator('.activity-item');
        const activityCount = await activityItems.count();
        console.log(`   Activity items found: ${activityCount}`);
        
        if (activityCount > 0) {
          // Expand first activity
          await activityItems.first().click();
          await page.waitForTimeout(500);
          
          // Look for change button
          const changeButton = await page.locator('.activity-change-btn').first();
          if (await changeButton.isVisible()) {
            console.log('   Change button found: ✅');
            
            // Click change button to open modal
            await changeButton.click();
            await page.waitForTimeout(1000);
            
            const activityModal = await page.locator('.modal');
            const modalVisible = await activityModal.isVisible();
            console.log(`   Activity modal opened: ${modalVisible ? '✅' : '❌'}`);
            
            if (modalVisible) {
              // Test 5: Create New Activity Button
              console.log('\n➕ TEST 5: Create New Activity Feature');
              
              const createButton = await page.locator('.create-activity-btn');
              const createButtonVisible = await createButton.isVisible();
              console.log(`   Create activity button found: ${createButtonVisible ? '✅' : '❌'}`);
              
              if (createButtonVisible) {
                await createButton.click();
                await page.waitForTimeout(1000);
                
                const createModal = await page.locator('.modal').nth(1);
                const createModalVisible = await createModal.isVisible();
                console.log(`   Create activity modal opened: ${createModalVisible ? '✅' : '❌'}`);
                
                if (createModalVisible) {
                  // Fill out the form
                  await page.fill('#activity-name', 'Test Activity');
                  await page.fill('#activity-description', 'This is a test activity for validation');
                  await page.selectOption('#activity-domain', 'creative');
                  await page.fill('#activity-challenge', '75');
                  
                  console.log('   Form filled: ✅');
                  
                  // Submit the form
                  await page.click('.btn-primary');
                  await page.waitForTimeout(1000);
                  
                  console.log('   Activity creation attempted: ✅');
                }
              }
            }
          }
        }
        
        // Test 6: Wheel Zoom and Modal Positioning
        console.log('\n🔍 TEST 6: Wheel Zoom and Modal Positioning');

        // Get wheel dimensions and position
        const wheelBox = await wheelComponent.boundingBox();
        console.log(`   Wheel position: x=${wheelBox.x}, y=${wheelBox.y}`);
        console.log(`   Wheel size: ${wheelBox.width}x${wheelBox.height}`);

        // Calculate expected zoom center (bottom edge of wheel)
        const wheelCenterX = wheelBox.x + wheelBox.width / 2;
        const wheelBottomY = wheelBox.y + wheelBox.height;
        console.log(`   Expected zoom center: x=${wheelCenterX}, y=${wheelBottomY} (bottom edge)`);

        // Test spin functionality
        const spinButton = await page.locator('.spin-button');
        if (await spinButton.isVisible()) {
          console.log('   Spin button found: ✅');

          // Click spin button
          await spinButton.click();
          console.log('   Wheel spin initiated: ✅');

          // Monitor zoom during spin
          let maxZoom = 1.0;
          const zoomCheckInterval = setInterval(async () => {
            try {
              const canvas = await page.locator('game-wheel canvas').first();
              if (await canvas.isVisible()) {
                const transform = await canvas.evaluate(el => el.style.transform);
                const scaleMatch = transform.match(/scale\(([^)]+)\)/);
                if (scaleMatch) {
                  const currentZoom = parseFloat(scaleMatch[1]);
                  if (currentZoom > maxZoom) {
                    maxZoom = currentZoom;
                  }
                }
              }
            } catch (e) {
              // Ignore errors during zoom monitoring
            }
          }, 100);

          // Wait for spin to complete and check for winning modal
          await page.waitForTimeout(8000);
          clearInterval(zoomCheckInterval);

          console.log(`   Maximum zoom detected: ${maxZoom.toFixed(2)}x`);
          console.log(`   Progressive zoom working: ${maxZoom > 1.5 ? '✅' : '❌'}`);

          const winningModal = await page.locator('.winning-modal');
          const winningModalVisible = await winningModal.isVisible();
          console.log(`   Winning modal appeared: ${winningModalVisible ? '✅' : '❌'}`);

          if (winningModalVisible) {
            const modalBox = await winningModal.boundingBox();
            console.log(`   Modal position: x=${modalBox.x}, y=${modalBox.y}`);
            console.log(`   Modal size: ${modalBox.width}x${modalBox.height}`);

            // Check if modal is positioned relative to wheel (not viewport)
            const modalRelativeToWheel = (
              modalBox.x >= wheelBox.x &&
              modalBox.y >= wheelBox.y &&
              modalBox.x + modalBox.width <= wheelBox.x + wheelBox.width &&
              modalBox.y + modalBox.height <= wheelBox.y + wheelBox.height
            );

            console.log(`   Modal positioned relative to wheel: ${modalRelativeToWheel ? '✅' : '❌'}`);

            // Check if modal is centered within the wheel
            const wheelCenterX = wheelBox.x + wheelBox.width / 2;
            const wheelCenterY = wheelBox.y + wheelBox.height / 2;
            const modalCenterX = modalBox.x + modalBox.width / 2;
            const modalCenterY = modalBox.y + modalBox.height / 2;

            const xDiff = Math.abs(wheelCenterX - modalCenterX);
            const yDiff = Math.abs(wheelCenterY - modalCenterY);

            console.log(`   Modal centering - X diff: ${xDiff}px, Y diff: ${yDiff}px`);
            console.log(`   Modal properly centered on wheel: ${xDiff < 50 && yDiff < 50 ? '✅' : '❌'}`);
          }
        }
      }
    }

    console.log('\n🎉 Test completed! Check the browser window for visual validation.');
    console.log('Press any key to close the browser...');
    
    // Keep browser open for manual inspection
    await page.waitForTimeout(30000);

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await browser.close();
  }
}

// Run the test
testCompleteImplementation().catch(console.error);
