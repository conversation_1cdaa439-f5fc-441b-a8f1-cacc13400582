#!/usr/bin/env node

/**
 * Debug Wheel Workflow - Focused WebSocket Communication Test
 * 
 * This test focuses specifically on the WebSocket communication to debug
 * why the wheel generation workflow is not triggering properly.
 */

const WebSocket = require('ws');

class DebugWheelWorkflow {
    constructor() {
        this.ws = null;
        this.messages = [];
        this.startTime = Date.now();
    }

    log(message) {
        const timestamp = new Date().toISOString();
        const elapsed = ((Date.now() - this.startTime) / 1000).toFixed(1);
        console.log(`[${elapsed}s] ${message}`);
    }

    async connectWebSocket() {
        this.log('🔌 Connecting to WebSocket...');
        
        return new Promise((resolve, reject) => {
            this.ws = new WebSocket('ws://localhost:8000/ws/game/');
            
            this.ws.on('open', () => {
                this.log('✅ WebSocket connected');
                resolve();
            });
            
            this.ws.on('message', (data) => {
                try {
                    const message = JSON.parse(data);
                    this.messages.push({
                        timestamp: Date.now(),
                        type: message.type,
                        data: message
                    });
                    
                    this.log(`📥 Received: ${message.type}`);
                    
                    if (message.type === 'wheel_data') {
                        this.log('🎡 WHEEL DATA RECEIVED!');
                        if (message.wheel && message.wheel.items) {
                            this.log(`   📊 Activities: ${message.wheel.items.length}`);
                            message.wheel.items.forEach((item, i) => {
                                this.log(`   ${i+1}. ${item.name} (${item.color})`);
                            });
                        } else {
                            this.log('   ❌ Wheel data structure invalid');
                        }
                    } else if (message.type === 'chat_message') {
                        this.log(`   💬 Content: ${message.content}`);
                    } else if (message.type === 'processing_status') {
                        this.log(`   ⚙️ Status: ${message.status}`);
                    } else if (message.type === 'debug_info') {
                        if (message.content && message.content.source) {
                            this.log(`   🐛 ${message.content.source}: ${message.content.message}`);
                        }
                    }
                    
                } catch (e) {
                    this.log(`📥 Non-JSON message: ${data}`);
                }
            });
            
            this.ws.on('error', (error) => {
                this.log(`❌ WebSocket error: ${error.message}`);
                reject(error);
            });
            
            this.ws.on('close', (code, reason) => {
                this.log(`🔌 WebSocket closed: ${code} - ${reason}`);
            });
        });
    }

    async sendWheelRequest() {
        this.log('📤 Sending wheel generation request...');
        
        const message = {
            type: "chat_message",
            content: {
                message: "I'm restless and need to do things physical. I have 2 hours. Make me a wheel",
                user_profile_id: "2",
                timestamp: new Date().toISOString(),
                metadata: {
                    requested_workflow: "wheel_generation"
                }
            }
        };
        
        this.ws.send(JSON.stringify(message));
        this.log('✅ Message sent');
        this.log(`📋 Message content: ${JSON.stringify(message, null, 2)}`);
    }

    async waitForWorkflow() {
        this.log('⏳ Waiting for workflow completion (120 seconds)...');
        
        return new Promise((resolve) => {
            const timeout = setTimeout(() => {
                this.log('⏰ Timeout reached');
                resolve(false);
            }, 120000);
            
            const checkForCompletion = () => {
                const hasWheelData = this.messages.some(m => m.type === 'wheel_data');
                const hasCompleted = this.messages.some(m => 
                    m.type === 'processing_status' && m.data.status === 'completed'
                );
                
                if (hasWheelData || hasCompleted) {
                    clearTimeout(timeout);
                    this.log('✅ Workflow completed');
                    resolve(true);
                }
            };
            
            // Check every second
            const interval = setInterval(() => {
                checkForCompletion();
                if (this.messages.some(m => m.type === 'wheel_data')) {
                    clearInterval(interval);
                }
            }, 1000);
        });
    }

    async analyzeResults() {
        this.log('\n📊 WORKFLOW ANALYSIS REPORT');
        this.log('════════════════════════════════════════════════════════════');
        
        const messageTypes = {};
        this.messages.forEach(msg => {
            messageTypes[msg.type] = (messageTypes[msg.type] || 0) + 1;
        });
        
        this.log(`📨 Total messages: ${this.messages.length}`);
        Object.entries(messageTypes).forEach(([type, count]) => {
            this.log(`   ${type}: ${count}`);
        });
        
        // Check for workflow progression
        const hasProcessingStart = this.messages.some(m => 
            m.type === 'processing_status' && m.data.status === 'processing'
        );
        const hasWorkflowStatus = this.messages.some(m => m.type === 'workflow_status');
        const hasDebugInfo = this.messages.some(m => m.type === 'debug_info');
        const hasWheelData = this.messages.some(m => m.type === 'wheel_data');
        
        this.log('\n🔍 Workflow Progression:');
        this.log(`   Processing started: ${hasProcessingStart ? '✅' : '❌'}`);
        this.log(`   Workflow status: ${hasWorkflowStatus ? '✅' : '❌'}`);
        this.log(`   Debug info: ${hasDebugInfo ? '✅' : '❌'}`);
        this.log(`   Wheel data: ${hasWheelData ? '✅' : '❌'}`);
        
        if (!hasProcessingStart) {
            this.log('\n❌ ISSUE: No processing_status received - message may not be reaching backend');
        }
        
        if (hasDebugInfo) {
            this.log('\n🐛 Debug Messages:');
            this.messages
                .filter(m => m.type === 'debug_info')
                .slice(0, 10) // Show first 10
                .forEach(msg => {
                    if (msg.data.content) {
                        this.log(`   ${msg.data.content.source}: ${msg.data.content.message}`);
                    }
                });
        }
        
        if (hasWheelData) {
            const wheelMsg = this.messages.find(m => m.type === 'wheel_data');
            if (wheelMsg && wheelMsg.data.wheel) {
                this.log('\n🎡 Wheel Analysis:');
                this.log(`   Items: ${wheelMsg.data.wheel.items ? wheelMsg.data.wheel.items.length : 0}`);
                if (wheelMsg.data.wheel.items) {
                    const colors = [...new Set(wheelMsg.data.wheel.items.map(i => i.color))];
                    this.log(`   Unique colors: ${colors.length} (${colors.join(', ')})`);
                }
            }
        }
    }

    async run() {
        try {
            await this.connectWebSocket();
            
            // Wait for initial connection messages
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            await this.sendWheelRequest();
            await this.waitForWorkflow();
            await this.analyzeResults();
            
        } catch (error) {
            this.log(`❌ Test failed: ${error.message}`);
        } finally {
            if (this.ws) {
                this.ws.close();
            }
        }
    }
}

// Run the test
if (require.main === module) {
    const test = new DebugWheelWorkflow();
    test.run().catch(console.error);
}

module.exports = DebugWheelWorkflow;
