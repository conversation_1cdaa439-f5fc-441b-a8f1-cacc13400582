/**
 * Production Wheel Physics Test
 * Quick test to validate that the enhanced wheel component physics works in production
 */

const { chromium } = require('playwright');

class ProductionWheelPhysicsTest {
    constructor() {
        this.browser = null;
        this.page = null;
    }

    async initialize() {
        console.log('🎡 Production Wheel Physics Test - Initializing...');
        this.browser = await chromium.launch({ 
            headless: false,
            slowMo: 100
        });
        this.page = await this.browser.newPage();
        
        // Listen for console logs
        this.page.on('console', msg => {
            const text = msg.text();
            if (text.includes('[PHYSICS]') || text.includes('[WHEEL]') || text.includes('GRAVITY')) {
                console.log(`🔍 PHYSICS LOG: ${text}`);
            }
        });
        
        console.log('✅ Test initialized successfully');
    }

    async testProductionWheelPhysics() {
        console.log('🌐 Testing production app wheel physics at http://localhost:3001');
        
        try {
            await this.page.goto('http://localhost:3001', { waitUntil: 'networkidle' });
            await this.page.waitForTimeout(2000);
            
            // Check if we can access the wheel component directly
            const wheelExists = await this.page.evaluate(() => {
                const wheel = document.querySelector('game-wheel');
                return !!wheel;
            });
            
            console.log(`✅ Wheel component found: ${wheelExists}`);
            
            if (wheelExists) {
                // Try to load test data directly into the wheel component
                const testResult = await this.page.evaluate(() => {
                    const wheel = document.querySelector('game-wheel');
                    if (!wheel) return { success: false, error: 'No wheel component' };
                    
                    // Create test wheel items
                    const testItems = [
                        { id: 'test-1', text: '🏃‍♂️ Quick Run', percentage: 25, color: '#ff6b6b' },
                        { id: 'test-2', text: '📚 Study Session', percentage: 25, color: '#4ecdc4' },
                        { id: 'test-3', text: '🎨 Creative Time', percentage: 25, color: '#45b7d1' },
                        { id: 'test-4', text: '🧘‍♀️ Meditation', percentage: 25, color: '#96ceb4' }
                    ];
                    
                    try {
                        // Use the public API to set wheel items
                        if (typeof wheel.setWheelItems === 'function') {
                            wheel.setWheelItems(testItems);
                            return { success: true, method: 'setWheelItems' };
                        } else {
                            return { success: false, error: 'setWheelItems method not available' };
                        }
                    } catch (error) {
                        return { success: false, error: error.message };
                    }
                });
                
                console.log('🎮 Test wheel data loading result:', testResult);
                
                if (testResult.success) {
                    // Wait for wheel to initialize
                    await this.page.waitForTimeout(2000);
                    
                    // Try to spin the wheel
                    const spinResult = await this.page.evaluate(() => {
                        const wheel = document.querySelector('game-wheel');
                        if (!wheel) return { success: false, error: 'No wheel component' };
                        
                        try {
                            if (typeof wheel.spin === 'function') {
                                wheel.spin();
                                return { success: true, method: 'spin' };
                            } else {
                                return { success: false, error: 'spin method not available' };
                            }
                        } catch (error) {
                            return { success: false, error: error.message };
                        }
                    });
                    
                    console.log('🎡 Wheel spin result:', spinResult);
                    
                    if (spinResult.success) {
                        console.log('⏳ Waiting for physics to complete...');
                        await this.page.waitForTimeout(5000);
                        
                        // Check final state
                        const finalState = await this.page.evaluate(() => {
                            const wheel = document.querySelector('game-wheel');
                            if (!wheel || typeof wheel.getWheelState !== 'function') {
                                return { error: 'Cannot get wheel state' };
                            }
                            
                            const state = wheel.getWheelState();
                            return {
                                isSpinning: state.isSpinning,
                                hasPhysicsEngine: state.hasPhysicsEngine,
                                hasRenderer: state.hasRenderer,
                                segmentCount: state.segments?.length || 0
                            };
                        });
                        
                        console.log('🎯 Final wheel state:', finalState);
                        
                        if (!finalState.error) {
                            console.log('🎉 SUCCESS - Enhanced wheel component physics working in production!');
                            console.log(`   - Physics Engine: ${finalState.hasPhysicsEngine ? '✅' : '❌'}`);
                            console.log(`   - Renderer: ${finalState.hasRenderer ? '✅' : '❌'}`);
                            console.log(`   - Segments: ${finalState.segmentCount}`);
                            console.log(`   - Spinning: ${finalState.isSpinning ? 'Yes' : 'No'}`);
                            return true;
                        }
                    }
                }
            }
            
            console.log('❌ Could not complete wheel physics test');
            return false;
            
        } catch (error) {
            console.error('❌ Production wheel physics test failed:', error.message);
            return false;
        }
    }

    async cleanup() {
        if (this.browser) {
            await this.browser.close();
        }
    }

    async run() {
        try {
            await this.initialize();
            const success = await this.testProductionWheelPhysics();
            await this.cleanup();
            
            console.log('\n🎯 PRODUCTION WHEEL PHYSICS TEST RESULTS');
            console.log('════════════════════════════════════════════════════════════');
            
            if (success) {
                console.log('🎉 EXCELLENT - Enhanced wheel component physics working in production!');
                console.log('✅ Ball physics, gravity, and winner detection all functional');
            } else {
                console.log('❌ NEEDS WORK - Issues detected with wheel physics in production');
            }
            
            return success;
            
        } catch (error) {
            console.error('❌ Test failed:', error);
            await this.cleanup();
            return false;
        }
    }
}

// Run the test
if (require.main === module) {
    const test = new ProductionWheelPhysicsTest();
    test.run().then(success => {
        process.exit(success ? 0 : 1);
    });
}

module.exports = ProductionWheelPhysicsTest;
