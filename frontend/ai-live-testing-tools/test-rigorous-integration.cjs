#!/usr/bin/env node

/**
 * RIGOROUS INTEGRATION TEST
 * 
 * This test is INTRANSIGENT and will catch:
 * - Authentication failures during wheel generation
 * - False progress indicators
 * - Wheel item consistency issues
 * - Backend connection drops
 * - State management failures
 */

const puppeteer = require('puppeteer');

const CONFIG = {
  testUrl: 'http://localhost:3000',
  adminUser: { username: 'admin', password: 'admin123' },
  testUser: { username: 'phiphi', password: 'phiphi123' },
  testMessage: 'I want to improve my physical fitness and mental wellbeing',
  timeout: 45000
};

class RigorousIntegrationTest {
  constructor() {
    this.browser = null;
    this.page = null;
    this.testResults = {
      authenticationMaintained: false,
      wheelGenerated: false,
      wheelItemsConsistent: false,
      progressBarAccurate: false,
      backendConnectionStable: false,
      wheelItemRemovalWorking: false,
      finalWheelConsistent: false
    };
    this.consoleErrors = [];
    this.authErrors = [];
    this.wheelData = null;
    this.initialWheelItems = [];
    this.finalWheelItems = [];
  }

  async run() {
    console.log('🔥 RIGOROUS INTEGRATION TEST - ZERO TOLERANCE');
    console.log('=============================================');

    try {
      await this.setupBrowser();
      await this.navigateToApp();
      await this.loginAsTestUser();
      await this.verifyAuthentication();
      await this.generateWheelWithValidation();
      await this.validateWheelConsistency();
      await this.testWheelItemRemoval();
      await this.validateFinalConsistency();
      await this.generateFinalReport();
      
    } catch (error) {
      console.error('💥 CRITICAL TEST FAILURE:', error.message);
      await this.generateFailureReport();
    } finally {
      await this.cleanup();
    }
  }

  async setupBrowser() {
    console.log('🚀 Setting up browser with comprehensive monitoring...');
    this.browser = await puppeteer.launch({
      headless: false, // Show browser for debugging
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
      devtools: true
    });
    this.page = await this.browser.newPage();
    
    // Monitor ALL console messages
    this.page.on('console', (msg) => {
      const text = msg.text();
      console.log(`📝 CONSOLE: ${text}`);
      
      // Track authentication errors
      if (text.includes('No authenticated user found') || 
          text.includes('authentication') || 
          text.includes('login')) {
        this.authErrors.push(text);
      }
      
      // Track general errors
      if (msg.type() === 'error') {
        this.consoleErrors.push(text);
      }
    });

    // Monitor network failures
    this.page.on('requestfailed', (request) => {
      console.log(`🚨 NETWORK FAILURE: ${request.url()} - ${request.failure().errorText}`);
    });

    // Monitor response errors
    this.page.on('response', (response) => {
      if (response.status() >= 400) {
        console.log(`🚨 HTTP ERROR: ${response.status()} - ${response.url()}`);
      }
    });
  }

  async navigateToApp() {
    console.log('🌐 Navigating to app...');
    await this.page.goto(CONFIG.testUrl, { waitUntil: 'networkidle0' });
    await this.page.waitForSelector('app-shell', { timeout: CONFIG.timeout });
  }

  async loginAsTestUser() {
    console.log('🔐 Logging in as test user (phiphi)...');

    // Check if already logged in or in debug mode
    const loginForm = await this.page.$('.login-form');
    if (!loginForm) {
      console.log('✅ Already logged in or in debug mode');

      // In debug mode, we need to open debug panel and select user
      await this.setupDebugMode();
      return;
    }

    // Fill login form
    await this.page.type('input[name="username"]', CONFIG.testUser.username);
    await this.page.type('input[name="password"]', CONFIG.testUser.password);
    await this.page.click('button[type="submit"]');

    // Wait for login to complete
    await this.page.waitForSelector('.login-form', { hidden: true, timeout: 10000 });
    console.log('✅ Login completed');
  }

  async setupDebugMode() {
    console.log('🐛 Setting up debug mode...');

    try {
      // DIRECT APPROACH: Set debug user via localStorage
      const debugUserSet = await this.page.evaluate(() => {
        // Set debug user ID directly in localStorage
        localStorage.setItem('debug_selected_user_id', '2');

        // Trigger app-shell to reload debug selections
        const appShell = document.querySelector('app-shell');
        if (appShell && appShell.loadDebugSelections) {
          appShell.loadDebugSelections();
        }

        // Also trigger loadCurrentUser to update the current user
        if (appShell && appShell.loadCurrentUser) {
          appShell.loadCurrentUser();
        }

        // Force a re-render
        if (appShell && appShell.requestUpdate) {
          appShell.requestUpdate();
        }

        return {
          debugUserId: localStorage.getItem('debug_selected_user_id'),
          appShellExists: !!appShell
        };
      });

      console.log('🐛 Debug user set via localStorage:', debugUserSet);

      // Wait for changes to take effect
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Verify the debug user is now set
      const verificationResult = await this.page.evaluate(() => {
        const appShell = document.querySelector('app-shell');
        return {
          debugUserId: appShell?.debugUserId || null,
          currentUser: appShell?.currentUser || null,
          localStorage: localStorage.getItem('debug_selected_user_id')
        };
      });

      console.log('🔍 Debug mode verification:', verificationResult);

      if (verificationResult.debugUserId === '2' || verificationResult.currentUser?.id === '2') {
        console.log('✅ Debug mode setup successful');
        return true;
      } else {
        console.log('⚠️ Debug mode setup incomplete');
        return false;
      }

    } catch (error) {
      console.log('⚠️ Debug mode setup failed:', error.message);
      return false;
    }
  }

  async verifyAuthentication() {
    console.log('🔍 Verifying authentication state...');

    // Check for user info in the UI and auth service state
    const authInfo = await this.page.evaluate(() => {
      const appShell = document.querySelector('app-shell');

      // Check auth service state
      const authService = window.authService || appShell?.authService;
      const authServiceState = authService ? {
        isAuthenticated: authService.isAuthenticated(),
        currentUser: authService.getCurrentUser(),
        token: authService.getToken()
      } : null;

      return {
        appShell: {
          isAuthenticated: appShell?.isAuthenticated || false,
          currentUser: appShell?.currentUser || null,
          debugUserId: appShell?.debugUserId || null,
          configMode: appShell?.currentMode || null
        },
        authService: authServiceState,
        localStorage: {
          goaliAuthToken: !!localStorage.getItem('goali_auth_token'),
          goaliAuthUser: !!localStorage.getItem('goali_auth_user'),
          legacyAuthToken: !!localStorage.getItem('auth_token')
        }
      };
    });

    console.log('🔍 Detailed Auth Info:', JSON.stringify(authInfo, null, 2));

    // In debug mode, authentication might work differently
    const isDebugMode = authInfo.appShell.configMode === 'debug';

    if (isDebugMode) {
      console.log('🐛 Debug mode detected - checking debug authentication');

      // In debug mode, we might have a debug user ID instead of full auth
      if (authInfo.appShell.debugUserId || authInfo.appShell.currentUser) {
        console.log('✅ Debug authentication verified');
        this.testResults.authenticationMaintained = true;
        return;
      } else {
        // Try to setup debug mode if not already done
        console.log('🔧 Debug user not set, attempting to configure...');
        await this.setupDebugMode();

        // Re-check authentication after debug setup
        const updatedAuthInfo = await this.page.evaluate(() => {
          const appShell = document.querySelector('app-shell');
          return {
            debugUserId: appShell?.debugUserId || null,
            currentUser: appShell?.currentUser || null
          };
        });

        if (updatedAuthInfo.debugUserId || updatedAuthInfo.currentUser) {
          console.log('✅ Debug authentication verified after setup');
          this.testResults.authenticationMaintained = true;
          return;
        }
      }
    } else {
      // Production mode - require full authentication
      if (authInfo.appShell.isAuthenticated &&
          authInfo.appShell.currentUser &&
          (authInfo.localStorage.goaliAuthToken || authInfo.authService?.isAuthenticated)) {
        console.log('✅ Production authentication verified');
        this.testResults.authenticationMaintained = true;
        return;
      }
    }

    throw new Error(`❌ AUTHENTICATION VERIFICATION FAILED - Mode: ${authInfo.appShell.configMode}, Auth: ${authInfo.appShell.isAuthenticated}, User: ${!!authInfo.appShell.currentUser}`);
  }

  async generateWheelWithValidation() {
    console.log('🎡 Generating wheel with strict validation...');

    // Clear previous errors
    this.authErrors = [];
    this.consoleErrors = [];

    // First, let's see what's actually on the page
    const pageElements = await this.page.evaluate(() => {
      const elements = {
        appShell: !!document.querySelector('app-shell'),
        chatInterface: !!document.querySelector('chat-interface'),
        textareas: document.querySelectorAll('textarea').length,
        inputs: document.querySelectorAll('input').length,
        allElements: Array.from(document.querySelectorAll('*')).map(el => el.tagName.toLowerCase()).slice(0, 20)
      };

      // Check shadow DOM
      const appShell = document.querySelector('app-shell');
      if (appShell && appShell.shadowRoot) {
        elements.shadowElements = Array.from(appShell.shadowRoot.querySelectorAll('*')).map(el => el.tagName.toLowerCase()).slice(0, 20);
        elements.chatInterfaceInShadow = !!appShell.shadowRoot.querySelector('chat-interface');
      }

      return elements;
    });

    console.log('🔍 Page elements analysis:', pageElements);

    // Try multiple strategies to find chat input
    let chatInput = null;
    const strategies = [
      // Strategy 1: Direct selector
      () => this.page.$('textarea'),
      // Strategy 2: Chat interface selector
      () => this.page.$('chat-interface textarea'),
      // Strategy 3: Class-based selector
      () => this.page.$('.message-input'),
      // Strategy 4: Shadow DOM access
      async () => {
        return await this.page.evaluate(() => {
          const appShell = document.querySelector('app-shell');
          if (appShell && appShell.shadowRoot) {
            const chatInterface = appShell.shadowRoot.querySelector('chat-interface');
            if (chatInterface && chatInterface.shadowRoot) {
              return chatInterface.shadowRoot.querySelector('textarea');
            }
          }
          return null;
        });
      }
    ];

    for (let i = 0; i < strategies.length; i++) {
      try {
        console.log(`🔍 Trying strategy ${i + 1}...`);
        chatInput = await strategies[i]();
        if (chatInput) {
          console.log(`✅ Found chat input with strategy ${i + 1}`);
          break;
        }
      } catch (error) {
        console.log(`⚠️ Strategy ${i + 1} failed:`, error.message);
      }
    }
    
    if (!chatInput) {
      throw new Error('❌ Chat input not found');
    }

    // Send message
    await chatInput.type(CONFIG.testMessage);
    await this.page.keyboard.press('Enter');
    
    // Monitor progress bar
    console.log('📊 Monitoring progress bar...');
    let progressBarAppeared = false;
    let progressCompleted = false;
    
    // Wait for progress bar to appear
    try {
      await this.page.waitForSelector('.progress-bar', { timeout: 5000 });
      progressBarAppeared = true;
      console.log('✅ Progress bar appeared');
    } catch (e) {
      console.log('⚠️ Progress bar did not appear');
    }

    // Wait for wheel data
    console.log('⏳ Waiting for wheel generation...');
    let wheelReceived = false;
    let attempts = 0;
    const maxAttempts = 30; // 30 seconds

    while (!wheelReceived && attempts < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, 1000));
      attempts++;
      
      // Check for wheel data
      const wheelData = await this.page.evaluate(() => {
        const appShell = document.querySelector('app-shell');
        return appShell?.wheelData || null;
      });

      if (wheelData && wheelData.segments && wheelData.segments.length > 0) {
        wheelReceived = true;
        this.wheelData = wheelData;
        this.initialWheelItems = [...wheelData.segments];
        console.log(`✅ Wheel received with ${wheelData.segments.length} items`);
      }
      
      // Check for authentication errors during generation
      if (this.authErrors.length > 0) {
        console.log('🚨 Authentication errors detected:', this.authErrors);
        throw new Error(`❌ AUTHENTICATION LOST DURING GENERATION: ${this.authErrors.join(', ')}`);
      }

      // Also check console errors for authentication issues
      const authRelatedErrors = this.consoleErrors.filter(error =>
        error.includes('authenticated') ||
        error.includes('unauthorized') ||
        error.includes('permission')
      );

      if (authRelatedErrors.length > 0) {
        console.log('🚨 Auth-related console errors:', authRelatedErrors);
        throw new Error(`❌ AUTHENTICATION ERRORS IN CONSOLE: ${authRelatedErrors.join(', ')}`);
      }
    }

    if (!wheelReceived) {
      throw new Error('❌ WHEEL GENERATION TIMEOUT - No wheel data received');
    }

    // Verify progress bar completed accurately
    if (progressBarAppeared) {
      const progressHidden = await this.page.$('.progress-bar') === null;
      this.testResults.progressBarAccurate = progressHidden;
      if (!progressHidden) {
        console.log('⚠️ Progress bar still visible after completion');
      }
    }

    this.testResults.wheelGenerated = true;
    this.testResults.backendConnectionStable = this.authErrors.length === 0;
  }

  async validateWheelConsistency() {
    console.log('🔍 Validating wheel consistency...');
    
    if (!this.wheelData || !this.initialWheelItems.length) {
      throw new Error('❌ No wheel data to validate');
    }

    // Check wheel item IDs format
    const invalidIds = this.initialWheelItems.filter(item => 
      !item.id || !item.id.startsWith('wheel-item-')
    );

    if (invalidIds.length > 0) {
      throw new Error(`❌ INVALID WHEEL ITEM IDS: ${invalidIds.map(i => i.id).join(', ')}`);
    }

    // Check for duplicate IDs
    const ids = this.initialWheelItems.map(item => item.id);
    const uniqueIds = new Set(ids);
    if (ids.length !== uniqueIds.size) {
      throw new Error('❌ DUPLICATE WHEEL ITEM IDS DETECTED');
    }

    // Validate percentages sum to reasonable total
    const totalPercentage = this.initialWheelItems.reduce((sum, item) => sum + item.percentage, 0);
    if (Math.abs(totalPercentage - 100) > 5) {
      throw new Error(`❌ INVALID PERCENTAGE TOTAL: ${totalPercentage}%`);
    }

    this.testResults.wheelItemsConsistent = true;
    console.log('✅ Initial wheel consistency validated');
  }

  async testWheelItemRemoval() {
    console.log('🗑️ Testing wheel item removal...');
    
    if (this.initialWheelItems.length === 0) {
      throw new Error('❌ No wheel items to remove');
    }

    // Find the first wheel item remove button
    const removeButton = await this.page.$('.remove-activity-btn');
    if (!removeButton) {
      throw new Error('❌ Remove button not found');
    }

    const itemToRemove = this.initialWheelItems[0];
    console.log(`🗑️ Removing item: ${itemToRemove.name} (ID: ${itemToRemove.id})`);

    // Click remove button
    await removeButton.click();
    
    // Wait for removal to complete
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Get updated wheel data
    const updatedWheelData = await this.page.evaluate(() => {
      const appShell = document.querySelector('app-shell');
      return appShell?.wheelData || null;
    });

    if (!updatedWheelData) {
      throw new Error('❌ No wheel data after removal');
    }

    this.finalWheelItems = [...updatedWheelData.segments];
    
    // Verify item was actually removed
    const removedItemStillExists = this.finalWheelItems.some(item => item.id === itemToRemove.id);
    if (removedItemStillExists) {
      throw new Error(`❌ ITEM NOT REMOVED: ${itemToRemove.id} still exists`);
    }

    // Verify count decreased
    if (this.finalWheelItems.length !== this.initialWheelItems.length - 1) {
      throw new Error(`❌ INCORRECT ITEM COUNT: Expected ${this.initialWheelItems.length - 1}, got ${this.finalWheelItems.length}`);
    }

    this.testResults.wheelItemRemovalWorking = true;
    console.log('✅ Wheel item removal working');
  }

  async validateFinalConsistency() {
    console.log('🔍 Validating final wheel consistency...');
    
    // Same validation as initial, but for final state
    const invalidIds = this.finalWheelItems.filter(item => 
      !item.id || !item.id.startsWith('wheel-item-')
    );

    if (invalidIds.length > 0) {
      throw new Error(`❌ INVALID FINAL WHEEL ITEM IDS: ${invalidIds.map(i => i.id).join(', ')}`);
    }

    // Check percentages still sum correctly
    const totalPercentage = this.finalWheelItems.reduce((sum, item) => sum + item.percentage, 0);
    if (Math.abs(totalPercentage - 100) > 5) {
      throw new Error(`❌ INVALID FINAL PERCENTAGE TOTAL: ${totalPercentage}%`);
    }

    this.testResults.finalWheelConsistent = true;
    console.log('✅ Final wheel consistency validated');
  }

  async generateFinalReport() {
    console.log('\n🏁 RIGOROUS TEST RESULTS');
    console.log('========================');
    
    const allTestsPassed = Object.values(this.testResults).every(result => result === true);
    
    Object.entries(this.testResults).forEach(([test, passed]) => {
      const status = passed ? '✅ PASS' : '❌ FAIL';
      console.log(`${status} ${test}`);
    });

    console.log(`\n📊 SUMMARY:`);
    console.log(`Initial wheel items: ${this.initialWheelItems.length}`);
    console.log(`Final wheel items: ${this.finalWheelItems.length}`);
    console.log(`Authentication errors: ${this.authErrors.length}`);
    console.log(`Console errors: ${this.consoleErrors.length}`);

    if (allTestsPassed) {
      console.log('\n🎉 ALL TESTS PASSED - SYSTEM IS RELIABLE');
    } else {
      console.log('\n💥 TESTS FAILED - SYSTEM IS NOT RELIABLE');
      process.exit(1);
    }
  }

  async generateFailureReport() {
    console.log('\n💥 FAILURE ANALYSIS');
    console.log('===================');
    console.log('Authentication Errors:', this.authErrors);
    console.log('Console Errors:', this.consoleErrors);
    console.log('Test Results:', this.testResults);
    process.exit(1);
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
    }
  }
}

// Run the rigorous test
const test = new RigorousIntegrationTest();
test.run().catch(console.error);
