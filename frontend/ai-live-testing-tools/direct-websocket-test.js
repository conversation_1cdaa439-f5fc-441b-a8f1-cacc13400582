#!/usr/bin/env node

/**
 * Direct WebSocket Test
 * 
 * This tool directly tests the WebSocket connections without authentication
 * to verify that our dashboard fixes are working correctly.
 */

import WebSocket from 'ws';

const CONFIG = {
  websockets: {
    game: 'ws://localhost:8000/ws/game/',
    admin: 'ws://localhost:8000/ws/connection-monitor/'
  },
  user: { id: '2', name: '<PERSON><PERSON><PERSON>' }
};

class DirectWebSocketTest {
  constructor() {
    this.gameSocket = null;
    this.adminSocket = null;
    this.messageFlowReceived = [];
    this.connectionDataReceived = [];
    this.testResults = {
      gameConnection: false,
      adminConnection: false,
      messageFlowWorking: false,
      dashboardUpdates: false
    };
  }

  async runTest() {
    console.log('🔍 DIRECT WEBSOCKET TEST');
    console.log('========================\n');

    try {
      // Test 1: Connect to admin dashboard
      await this.testAdminConnection();
      
      // Test 2: Connect game client
      await this.testGameConnection();
      
      // Test 3: Send messages and monitor flow
      await this.testMessageFlow();
      
      // Test 4: Verify dashboard updates
      await this.verifyDashboardUpdates();
      
      // Generate report
      this.generateReport();
      
    } catch (error) {
      console.error('❌ Test failed:', error.message);
    } finally {
      this.cleanup();
    }
  }

  async testAdminConnection() {
    console.log('📋 Test 1: Admin Dashboard Connection');
    console.log('------------------------------------');

    return new Promise((resolve, reject) => {
      this.adminSocket = new WebSocket(CONFIG.websockets.admin);

      this.adminSocket.on('open', () => {
        console.log('✅ Admin WebSocket connected successfully');
        this.testResults.adminConnection = true;
        
        // Request initial data
        this.adminSocket.send(JSON.stringify({ type: 'get_connections' }));
        this.adminSocket.send(JSON.stringify({ type: 'start_message_monitoring' }));
        
        resolve();
      });

      this.adminSocket.on('message', (data) => {
        this.handleAdminMessage(data);
      });

      this.adminSocket.on('error', (error) => {
        console.log('❌ Admin WebSocket error:', error.message);
        this.testResults.adminConnection = false;
        reject(error);
      });

      // Timeout after 10 seconds
      setTimeout(() => {
        if (!this.testResults.adminConnection) {
          reject(new Error('Admin connection timeout'));
        }
      }, 10000);
    });
  }

  handleAdminMessage(data) {
    try {
      const message = JSON.parse(data);
      console.log(`📨 Admin received: ${message.type}`);
      
      if (message.type === 'connection_data') {
        this.connectionDataReceived.push(message);
        console.log(`   Connections: ${message.data.length}`);
        this.testResults.dashboardUpdates = true;
      }
      
      if (message.type === 'message_flow') {
        this.messageFlowReceived.push(message);
        console.log(`   Message flow: ${message.data.direction} - ${message.data.message?.type || 'unknown'}`);
        this.testResults.messageFlowWorking = true;
      }
      
      if (message.type === 'message_monitoring_started') {
        console.log('✅ Message monitoring activated');
      }
      
    } catch (error) {
      console.log('❌ Failed to parse admin message:', error.message);
    }
  }

  async testGameConnection() {
    console.log('\n📋 Test 2: Game Client Connection');
    console.log('---------------------------------');

    return new Promise((resolve, reject) => {
      this.gameSocket = new WebSocket(CONFIG.websockets.game);

      this.gameSocket.on('open', () => {
        console.log('✅ Game WebSocket connected successfully');
        this.testResults.gameConnection = true;
        resolve();
      });

      this.gameSocket.on('message', (data) => {
        try {
          const message = JSON.parse(data);
          console.log(`📨 Game received: ${message.type}`);
        } catch (error) {
          console.log('❌ Failed to parse game message');
        }
      });

      this.gameSocket.on('error', (error) => {
        console.log('❌ Game WebSocket error:', error.message);
        this.testResults.gameConnection = false;
        reject(error);
      });

      // Timeout after 10 seconds
      setTimeout(() => {
        if (!this.testResults.gameConnection) {
          reject(new Error('Game connection timeout'));
        }
      }, 10000);
    });
  }

  async testMessageFlow() {
    console.log('\n📋 Test 3: Message Flow Testing');
    console.log('-------------------------------');

    if (!this.gameSocket || !this.adminSocket) {
      console.log('❌ Cannot test message flow - connections not established');
      return;
    }

    // Send test message
    const testMessage = {
      type: 'chat_message',
      content: {
        message: 'Direct WebSocket test message',
        user_profile_id: CONFIG.user.id,
        timestamp: new Date().toISOString()
      }
    };

    console.log('📤 Sending test message...');
    this.gameSocket.send(JSON.stringify(testMessage));

    // Wait for message flow to be captured
    await new Promise(resolve => setTimeout(resolve, 5000));

    if (this.messageFlowReceived.length > 0) {
      console.log('✅ Message flow is working');
      this.testResults.messageFlowWorking = true;
    } else {
      console.log('❌ No message flow received');
      this.testResults.messageFlowWorking = false;
    }
  }

  async verifyDashboardUpdates() {
    console.log('\n📋 Test 4: Dashboard Updates Verification');
    console.log('-----------------------------------------');

    // Request fresh connection data
    if (this.adminSocket && this.adminSocket.readyState === WebSocket.OPEN) {
      this.adminSocket.send(JSON.stringify({ type: 'get_connections' }));
      this.adminSocket.send(JSON.stringify({ type: 'get_message_stats' }));
    }

    // Wait for updates
    await new Promise(resolve => setTimeout(resolve, 3000));

    if (this.connectionDataReceived.length > 0) {
      console.log('✅ Dashboard updates are working');
      this.testResults.dashboardUpdates = true;
      
      // Show latest connection data
      const latest = this.connectionDataReceived[this.connectionDataReceived.length - 1];
      console.log(`   Latest connections: ${latest.data.length}`);
      if (latest.data.length > 0) {
        const conn = latest.data[0];
        console.log(`   Session: ${conn.session_id?.substring(0, 8)}... User: ${conn.user_id} Messages: ${conn.message_count}`);
      }
    } else {
      console.log('❌ No dashboard updates received');
      this.testResults.dashboardUpdates = false;
    }
  }

  generateReport() {
    console.log('\n📊 TEST RESULTS SUMMARY');
    console.log('=======================');
    
    const results = [
      { name: 'Admin Connection', status: this.testResults.adminConnection },
      { name: 'Game Connection', status: this.testResults.gameConnection },
      { name: 'Message Flow', status: this.testResults.messageFlowWorking },
      { name: 'Dashboard Updates', status: this.testResults.dashboardUpdates }
    ];
    
    results.forEach(result => {
      const icon = result.status ? '✅' : '❌';
      console.log(`${icon} ${result.name}: ${result.status ? 'WORKING' : 'FAILED'}`);
    });
    
    const passedTests = results.filter(r => r.status).length;
    const totalTests = results.length;
    const score = Math.round((passedTests / totalTests) * 100);
    
    console.log(`\n📊 Overall Score: ${score}% (${passedTests}/${totalTests} tests passed)`);
    
    console.log('\n📋 Detailed Analysis:');
    console.log(`   Message Flow Events Received: ${this.messageFlowReceived.length}`);
    console.log(`   Connection Data Updates: ${this.connectionDataReceived.length}`);
    
    if (this.messageFlowReceived.length > 0) {
      console.log('\n📨 Message Flow Details:');
      this.messageFlowReceived.forEach((flow, index) => {
        const data = flow.data;
        console.log(`   ${index + 1}. ${data.direction} - ${data.message?.type || 'unknown'} (${data.session_id?.substring(0, 8)}...)`);
      });
    }
    
    // Diagnosis
    console.log('\n🔍 DIAGNOSIS:');
    if (this.testResults.adminConnection && this.testResults.gameConnection) {
      if (this.testResults.messageFlowWorking) {
        console.log('✅ All core functionality is working correctly');
        console.log('   The dashboard should now properly display chat messages');
      } else {
        console.log('❌ Message flow is not working');
        console.log('   Check if broadcast_message_flow is being called in UserSessionConsumer');
      }
    } else {
      console.log('❌ Basic WebSocket connectivity issues detected');
      console.log('   Check if backend containers are running and accessible');
    }
  }

  cleanup() {
    console.log('\n🧹 Cleaning up connections...');
    
    if (this.gameSocket) {
      this.gameSocket.close();
    }
    
    if (this.adminSocket) {
      this.adminSocket.close();
    }
    
    console.log('✅ Cleanup completed');
  }
}

// Run the test
const tester = new DirectWebSocketTest();
tester.runTest().then(() => {
  process.exit(0);
}).catch((error) => {
  console.error('❌ Test failed:', error);
  process.exit(1);
});

// Auto-exit after 60 seconds
setTimeout(() => {
  console.log('\n⏰ Test timeout - exiting');
  process.exit(1);
}, 60000);
