#!/usr/bin/env node

/**
 * Test Color Mapping Logic
 * 
 * This test verifies the color mapping logic that will be used in the frontend.
 */

// Domain color mapping (copied from domainColorService.js)
const DOMAIN_COLOR_MAP = {
  // Physical Domain - Red family (energy, action, strength)
  'physical': '#E74C3C',
  'phys_strength': '#E74C3C',
  'phys_cardio': '#C0392B',
  'phys_flexibility': '#E67E22',
  'phys_dance': '#F39C12',
  'phys_sports': '#D35400',
  'phys_outdoor': '#A0522D',

  // Creative Domain - Orange family (creativity, enthusiasm, innovation)
  'creativity': '#FF8C00',
  'creative_visual': '#FF8C00',
  'creative_writing': '#FF7F50',
  'creative_music': '#FFA500',
  'creative_culinary': '#FF6347',
  'creative_craft': '#CD853F',
  'creative_design': '#DEB887',

  // Learning/Intellectual Domain - Blue family (trust, wisdom, knowledge)
  'learning': '#3498DB',
  'intel_strategic': '#3498DB',
  'intel_debate': '#2980B9',
  'intel_language': '#5DADE2',
  'intel_research': '#85C1E9',
  'intel_analysis': '#AED6F1',
  'intel_problem': '#D6EAF8',

  // Social Domain - Gold/Yellow family (warmth, communication, joy)
  'social': '#FFD700',
  'soc_comm': '#FFD700',
  'soc_empathy': '#F1C40F',
  'soc_connecting': '#F4D03F',
  'soc_leadership': '#F7DC6F',
  'soc_collaboration': '#FCFC99',
  'soc_networking': '#FFFACD',

  // Emotional Domain - Purple family (introspection, emotion, spirituality)
  'emotional': '#9B59B6',
  'emot_aware': '#9B59B6',
  'emot_regulate': '#8E44AD',
  'emot_express': '#BB8FCE',
  'emot_healing': '#D2B4DE',
  'emot_mindful': '#E8DAEF',

  // Exploratory Domain - Teal family (discovery, adventure, growth)
  'exploratory': '#1ABC9C',
  'explor_sensory': '#1ABC9C',
  'explor_cultural': '#16A085',
  'explor_travel': '#48C9B0',
  'explor_nature': '#76D7C4',
  'explor_adventure': '#A3E4D7',

  // Reflective Domain - Indigo family (contemplation, wisdom, depth)
  'reflective': '#6C5CE7',
  'refl_meditate': '#6C5CE7',
  'refl_mindful': '#A29BFE',
  'refl_journal': '#74B9FF',
  'refl_spiritual': '#81ECEC',

  // Productive Domain - Green family (growth, productivity, balance)
  'productive': '#27AE60',
  'prod_health': '#27AE60',
  'prod_skill': '#2ECC71',
  'prod_time': '#58D68D',
  'prod_organization': '#82E0AA',
  'prod_transition': '#ABEBC6',

  // Leisure Domain - Neutral colors (relaxation, balance)
  'leisure': '#95A5A6',
  'leisure_relax': '#95A5A6',
  'leisure_entertainment': '#BDC3C7',
  'leisure_hobby': '#D5DBDB',

  // General/Default - Neutral gray
  'general': '#95A5A6',
  'unknown': '#95A5A6'
};

function getDomainColor(domainCode) {
  if (!domainCode || typeof domainCode !== 'string') {
    return DOMAIN_COLOR_MAP.general;
  }

  // Direct lookup
  const color = DOMAIN_COLOR_MAP[domainCode.toLowerCase()];
  if (color) {
    return color;
  }

  // Fallback to main domain category
  const mainDomain = domainCode.split('_')[0].toLowerCase();
  const mainColor = DOMAIN_COLOR_MAP[mainDomain];
  if (mainColor) {
    return mainColor;
  }

  // Final fallback
  return DOMAIN_COLOR_MAP.general;
}

console.log('🎨 Testing Color Mapping Logic');
console.log('=' .repeat(60));

function testBackendDomains() {
    console.log('\n🔍 Testing Domains from Backend Test Results');
    console.log('-'.repeat(40));
    
    // These are the actual domains from our backend test
    const backendDomains = [
        'explor_travel', 'refl_meditate', 'soc_empathy', 'creative_visual'
    ];
    
    let allValid = true;
    
    for (const domain of backendDomains) {
        const color = getDomainColor(domain);
        const isValid = color && color.startsWith('#') && color.length === 7 && color !== '#95A5A6';
        
        console.log(`  ${isValid ? '✅' : '❌'} ${domain.padEnd(20)} → ${color}`);
        
        if (!isValid) allValid = false;
    }
    
    return allValid;
}

function testCommonDomains() {
    console.log('\n🎯 Testing Common Domain Patterns');
    console.log('-'.repeat(40));
    
    const commonDomains = [
        'phys_dance', 'phys_strength', 'creative_culinary', 'creative_writing',
        'intel_strategic', 'intel_debate', 'soc_comm', 'soc_empathy',
        'emot_aware', 'explor_sensory', 'refl_meditate', 'prod_health'
    ];
    
    let allValid = true;
    
    for (const domain of commonDomains) {
        const color = getDomainColor(domain);
        const isValid = color && color.startsWith('#') && color.length === 7 && color !== '#95A5A6';
        
        console.log(`  ${isValid ? '✅' : '❌'} ${domain.padEnd(20)} → ${color}`);
        
        if (!isValid) allValid = false;
    }
    
    return allValid;
}

function testFallbackLogic() {
    console.log('\n🔧 Testing Fallback Logic');
    console.log('-'.repeat(40));
    
    // Test unknown specific domain with known main domain
    const unknownSpecific = getDomainColor('physical_unknown');
    const fallbackValid = unknownSpecific === DOMAIN_COLOR_MAP.physical;
    console.log(`  ${fallbackValid ? '✅' : '❌'} physical_unknown → ${unknownSpecific} (fallback to physical)`);
    
    // Test completely unknown domain
    const completelyUnknown = getDomainColor('totally_unknown');
    const unknownValid = completelyUnknown === DOMAIN_COLOR_MAP.general;
    console.log(`  ${unknownValid ? '✅' : '❌'} totally_unknown → ${completelyUnknown} (fallback to general)`);
    
    // Test null/undefined
    const nullColor = getDomainColor(null);
    const nullValid = nullColor === DOMAIN_COLOR_MAP.general;
    console.log(`  ${nullValid ? '✅' : '❌'} null → ${nullColor} (fallback to general)`);
    
    return fallbackValid && unknownValid && nullValid;
}

function testColorDistribution() {
    console.log('\n🌈 Testing Color Distribution');
    console.log('-'.repeat(40));
    
    const colorFamilies = {
        'Red (Physical)': ['#E74C3C', '#C0392B', '#E67E22', '#F39C12', '#D35400', '#A0522D'],
        'Orange (Creative)': ['#FF8C00', '#FF7F50', '#FFA500', '#FF6347', '#CD853F', '#DEB887'],
        'Blue (Learning)': ['#3498DB', '#2980B9', '#5DADE2', '#85C1E9', '#AED6F1', '#D6EAF8'],
        'Gold (Social)': ['#FFD700', '#F1C40F', '#F4D03F', '#F7DC6F', '#FCFC99', '#FFFACD'],
        'Purple (Emotional)': ['#9B59B6', '#8E44AD', '#BB8FCE', '#D2B4DE', '#E8DAEF'],
        'Teal (Exploratory)': ['#1ABC9C', '#16A085', '#48C9B0', '#76D7C4', '#A3E4D7'],
        'Indigo (Reflective)': ['#6C5CE7', '#A29BFE', '#74B9FF', '#81ECEC'],
        'Green (Productive)': ['#27AE60', '#2ECC71', '#58D68D', '#82E0AA', '#ABEBC6']
    };
    
    let distributionValid = true;
    
    for (const [family, colors] of Object.entries(colorFamilies)) {
        const uniqueColors = new Set(colors);
        const hasVariety = uniqueColors.size === colors.length;
        console.log(`  ${hasVariety ? '✅' : '❌'} ${family}: ${colors.length} unique colors`);
        
        if (!hasVariety) distributionValid = false;
    }
    
    return distributionValid;
}

async function runTests() {
    try {
        // Run all tests
        const backendDomainsWork = testBackendDomains();
        const commonDomainsWork = testCommonDomains();
        const fallbackLogicWorks = testFallbackLogic();
        const colorDistributionGood = testColorDistribution();
        
        // Final results
        console.log('\n' + '='.repeat(60));
        console.log('🏁 FINAL TEST RESULTS');
        console.log('='.repeat(60));
        console.log(`🔍 Backend Domains: ${backendDomainsWork ? '✅ PASS' : '❌ FAIL'}`);
        console.log(`🎯 Common Domains: ${commonDomainsWork ? '✅ PASS' : '❌ FAIL'}`);
        console.log(`🔧 Fallback Logic: ${fallbackLogicWorks ? '✅ PASS' : '❌ FAIL'}`);
        console.log(`🌈 Color Distribution: ${colorDistributionGood ? '✅ PASS' : '❌ FAIL'}`);
        
        const overallSuccess = backendDomainsWork && commonDomainsWork && 
                              fallbackLogicWorks && colorDistributionGood;
        
        console.log(`\n🎯 Overall Result: ${overallSuccess ? '✅ ALL TESTS PASS' : '❌ SOME TESTS FAILED'}`);
        
        if (overallSuccess) {
            console.log('\n🎉 Color mapping logic is working perfectly!');
            console.log('   - Backend domains map to proper colors');
            console.log('   - Common domain patterns work correctly');
            console.log('   - Fallback logic handles edge cases');
            console.log('   - Color distribution provides good variety');
            console.log('\n📋 Ready for integration with frontend wheel display!');
        } else {
            console.log('\n🔧 Some issues found that need to be addressed.');
        }
        
        process.exit(overallSuccess ? 0 : 1);
        
    } catch (error) {
        console.error('❌ Test execution failed:', error);
        process.exit(1);
    }
}

// Run the tests
runTests();
