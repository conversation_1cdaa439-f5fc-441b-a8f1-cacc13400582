#!/usr/bin/env node
/**
 * Frontend Test: Wheel Generation Database Fix Validation
 * 
 * This test validates that the wheel generation process works correctly with the new
 * ActivityTailored-UserEnvironment relationship and doesn't cause database constraint violations.
 * 
 * Key validations:
 * 1. <PERSON><PERSON><PERSON> (user_id: 2) can generate wheels without DB violations
 * 2. Multiple wheel generations reuse activities appropriately
 * 3. Frontend receives proper wheel data and can display it
 * 4. No backend errors occur during the process
 * 
 * Usage:
 *   node test-wheel-generation-db-fix.cjs [port]
 *   Example: node test-wheel-generation-db-fix.cjs 3001
 * 
 * Follows AI-ENTRYPOINT.md guidelines for frontend testing.
 */

const puppeteer = require('puppeteer');

class WheelGenerationDBFixTest {
    constructor(port = 5173) {
        this.port = port;
        this.baseUrl = `http://localhost:${port}`;
        this.browser = null;
        this.page = null;
        this.testResults = {
            total: 0,
            passed: 0,
            failed: 0,
            details: []
        };
    }

    async logResult(testName, passed, details = '') {
        this.testResults.total++;
        if (passed) {
            this.testResults.passed++;
            console.log(`✅ ${testName}: PASSED`);
        } else {
            this.testResults.failed++;
            console.log(`❌ ${testName}: FAILED - ${details}`);
        }
        
        this.testResults.details.push({
            name: testName,
            passed,
            details
        });
    }

    async setupBrowser() {
        console.log('🚀 Setting up browser...');
        
        this.browser = await puppeteer.launch({
            headless: false,
            defaultViewport: { width: 1200, height: 800 },
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        
        this.page = await this.browser.newPage();
        
        // Listen for console messages and errors
        this.page.on('console', msg => {
            if (msg.type() === 'error') {
                console.log(`🔴 Browser Error: ${msg.text()}`);
            }
        });
        
        this.page.on('pageerror', error => {
            console.log(`🔴 Page Error: ${error.message}`);
        });
        
        await this.page.goto(this.baseUrl);
        await this.page.waitForTimeout(2000);
    }

    async setupDebugPanel() {
        console.log('🔧 Setting up debug panel...');
        
        try {
            // Open debug panel
            await this.page.keyboard.down('Control');
            await this.page.keyboard.down('Shift');
            await this.page.keyboard.press('KeyD');
            await this.page.keyboard.up('Shift');
            await this.page.keyboard.up('Control');
            
            await this.page.waitForTimeout(1000);
            
            // Verify debug panel is open
            const debugPanel = await this.page.$('.debug-panel');
            if (!debugPanel) {
                throw new Error('Debug panel not found');
            }
            
            await this.logResult('Debug Panel Setup', true);
            return true;
            
        } catch (error) {
            await this.logResult('Debug Panel Setup', false, error.message);
            return false;
        }
    }

    async selectPhiPhiUser() {
        console.log('👤 Selecting PhiPhi user (ID: 2)...');
        
        try {
            // Select PhiPhi user in dropdown
            const userSelect = await this.page.$('#user-select');
            if (!userSelect) {
                throw new Error('User select dropdown not found');
            }
            
            await userSelect.select('2'); // PhiPhi's user ID
            await this.page.waitForTimeout(500);
            
            // Verify selection
            const selectedValue = await this.page.$eval('#user-select', el => el.value);
            if (selectedValue !== '2') {
                throw new Error(`Expected user ID 2, got ${selectedValue}`);
            }
            
            await this.logResult('PhiPhi User Selection', true);
            return true;
            
        } catch (error) {
            await this.logResult('PhiPhi User Selection', false, error.message);
            return false;
        }
    }

    async selectLLMConfig() {
        console.log('🤖 Selecting LLM configuration...');
        
        try {
            // Select mistral-small-latest
            const llmSelect = await this.page.$('#llm-config-select');
            if (!llmSelect) {
                throw new Error('LLM config select dropdown not found');
            }
            
            await llmSelect.select('mistral-small-latest');
            await this.page.waitForTimeout(500);
            
            // Apply configuration
            const applyButton = await this.page.$('#apply-config');
            if (!applyButton) {
                throw new Error('Apply config button not found');
            }
            
            await applyButton.click();
            await this.page.waitForTimeout(3000); // Wait for config to apply
            
            await this.logResult('LLM Configuration', true);
            return true;
            
        } catch (error) {
            await this.logResult('LLM Configuration', false, error.message);
            return false;
        }
    }

    async triggerWheelGeneration() {
        console.log('🎡 Triggering wheel generation...');
        
        try {
            // Find and click the chat input area
            const chatInput = await this.page.$('.chat-input') || 
                            await this.page.$('#message-input') ||
                            await this.page.$('input[placeholder*="message"]') ||
                            await this.page.$('textarea[placeholder*="message"]');
            
            if (!chatInput) {
                throw new Error('Chat input not found');
            }
            
            await chatInput.click();
            await this.page.waitForTimeout(500);
            
            // Type wheel generation request
            await chatInput.type('Generate a personalized activity wheel');
            await this.page.waitForTimeout(500);
            
            // Send message
            const sendButton = await this.page.$('.send-button') ||
                             await this.page.$('#send-button') ||
                             await this.page.$('button[type="submit"]');
            
            if (sendButton) {
                await sendButton.click();
            } else {
                // Try pressing Enter
                await this.page.keyboard.press('Enter');
            }
            
            await this.logResult('Wheel Generation Trigger', true);
            return true;
            
        } catch (error) {
            await this.logResult('Wheel Generation Trigger', false, error.message);
            return false;
        }
    }

    async waitForWheelGeneration() {
        console.log('⏳ Waiting for wheel generation to complete...');
        
        try {
            // Wait for wheel data to appear (up to 2 minutes)
            const wheelSelector = '.wheel-component, .wheel-container, #wheel-canvas';
            
            await this.page.waitForSelector(wheelSelector, { 
                timeout: 120000 // 2 minutes
            });
            
            // Additional wait for wheel to fully load
            await this.page.waitForTimeout(5000);
            
            // Check if wheel has data
            const wheelData = await this.page.evaluate(() => {
                const appShell = document.querySelector('app-shell');
                return appShell && appShell.wheelData ? appShell.wheelData : null;
            });
            
            if (!wheelData) {
                throw new Error('Wheel data not found in app-shell');
            }
            
            if (!wheelData.items || wheelData.items.length === 0) {
                throw new Error('Wheel has no items');
            }
            
            console.log(`🎯 Wheel generated with ${wheelData.items.length} items`);
            await this.logResult('Wheel Generation Completion', true, `${wheelData.items.length} items`);
            return wheelData;
            
        } catch (error) {
            await this.logResult('Wheel Generation Completion', false, error.message);
            return null;
        }
    }

    async validateWheelData(wheelData) {
        console.log('🔍 Validating wheel data structure...');
        
        try {
            // Validate basic structure
            if (!wheelData.id) {
                throw new Error('Wheel missing ID');
            }
            
            if (!wheelData.items || !Array.isArray(wheelData.items)) {
                throw new Error('Wheel missing items array');
            }
            
            if (wheelData.items.length < 2) {
                throw new Error(`Expected at least 2 items, got ${wheelData.items.length}`);
            }
            
            // Validate each item
            for (let i = 0; i < wheelData.items.length; i++) {
                const item = wheelData.items[i];
                
                if (!item.id) {
                    throw new Error(`Item ${i} missing ID`);
                }
                
                if (!item.activity_name && !item.title) {
                    throw new Error(`Item ${i} missing name/title`);
                }
                
                if (typeof item.percentage !== 'number') {
                    throw new Error(`Item ${i} missing or invalid percentage`);
                }
                
                if (!item.activity_id) {
                    throw new Error(`Item ${i} missing activity_id`);
                }
            }
            
            // Validate percentages sum to approximately 100
            const totalPercentage = wheelData.items.reduce((sum, item) => sum + item.percentage, 0);
            if (Math.abs(totalPercentage - 100) > 1) {
                throw new Error(`Percentages sum to ${totalPercentage}, expected ~100`);
            }
            
            await this.logResult('Wheel Data Validation', true, `${wheelData.items.length} valid items`);
            return true;
            
        } catch (error) {
            await this.logResult('Wheel Data Validation', false, error.message);
            return false;
        }
    }

    async testMultipleGenerations() {
        console.log('🔄 Testing multiple wheel generations...');
        
        try {
            // Generate second wheel
            const chatInput = await this.page.$('.chat-input') || 
                            await this.page.$('#message-input') ||
                            await this.page.$('input[placeholder*="message"]') ||
                            await this.page.$('textarea[placeholder*="message"]');
            
            if (!chatInput) {
                throw new Error('Chat input not found for second generation');
            }
            
            await chatInput.click();
            await this.page.waitForTimeout(500);
            await chatInput.type('Generate another wheel please');
            await this.page.keyboard.press('Enter');
            
            // Wait for second wheel
            await this.page.waitForTimeout(30000); // 30 seconds for second generation
            
            // Check if second wheel was generated
            const secondWheelData = await this.page.evaluate(() => {
                const appShell = document.querySelector('app-shell');
                return appShell && appShell.wheelData ? appShell.wheelData : null;
            });
            
            if (!secondWheelData) {
                throw new Error('Second wheel generation failed');
            }
            
            await this.logResult('Multiple Wheel Generations', true, 'Second wheel generated successfully');
            return true;
            
        } catch (error) {
            await this.logResult('Multiple Wheel Generations', false, error.message);
            return false;
        }
    }

    async checkForErrors() {
        console.log('🔍 Checking for backend errors...');
        
        try {
            // Check browser console for errors
            const logs = await this.page.evaluate(() => {
                return window.console._logs || [];
            });
            
            const errors = logs.filter(log => log.level === 'error');
            
            if (errors.length > 0) {
                throw new Error(`Found ${errors.length} console errors`);
            }
            
            // Check for error messages in the UI
            const errorElements = await this.page.$$('.error-message, .alert-danger, .error');
            
            if (errorElements.length > 0) {
                const errorTexts = await Promise.all(
                    errorElements.map(el => this.page.evaluate(element => element.textContent, el))
                );
                throw new Error(`Found UI errors: ${errorTexts.join(', ')}`);
            }
            
            await this.logResult('Error Check', true, 'No errors detected');
            return true;
            
        } catch (error) {
            await this.logResult('Error Check', false, error.message);
            return false;
        }
    }

    async runAllTests() {
        console.log('🚀 Starting Wheel Generation Database Fix Tests');
        console.log('=' * 80);
        
        try {
            await this.setupBrowser();
            
            if (!await this.setupDebugPanel()) return false;
            if (!await this.selectPhiPhiUser()) return false;
            if (!await this.selectLLMConfig()) return false;
            if (!await this.triggerWheelGeneration()) return false;
            
            const wheelData = await this.waitForWheelGeneration();
            if (!wheelData) return false;
            
            if (!await this.validateWheelData(wheelData)) return false;
            if (!await this.testMultipleGenerations()) return false;
            if (!await this.checkForErrors()) return false;
            
        } catch (error) {
            console.error(`❌ Test execution failed: ${error.message}`);
        } finally {
            if (this.browser) {
                await this.browser.close();
            }
        }
        
        // Print results
        console.log('=' * 80);
        console.log('📊 TEST RESULTS SUMMARY');
        console.log('=' * 80);
        console.log(`Total Tests: ${this.testResults.total}`);
        console.log(`Passed: ${this.testResults.passed}`);
        console.log(`Failed: ${this.testResults.failed}`);
        
        const successRate = this.testResults.total > 0 ? 
            (this.testResults.passed / this.testResults.total) * 100 : 0;
        console.log(`Success Rate: ${successRate.toFixed(1)}%`);
        
        if (this.testResults.failed > 0) {
            console.log('\n❌ FAILED TESTS:');
            this.testResults.details
                .filter(test => !test.passed)
                .forEach(test => console.log(`  - ${test.name}: ${test.details}`));
        }
        
        console.log('=' * 80);
        
        return this.testResults.failed === 0;
    }
}

async function main() {
    const port = process.argv[2] || 5173;
    console.log(`🎯 Testing wheel generation database fix on port ${port}`);
    
    const tester = new WheelGenerationDBFixTest(port);
    const success = await tester.runAllTests();
    
    if (success) {
        console.log('🎉 All tests passed! Wheel generation database fix is working correctly.');
        process.exit(0);
    } else {
        console.log('💥 Some tests failed. Please check the implementation.');
        process.exit(1);
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = { WheelGenerationDBFixTest };
