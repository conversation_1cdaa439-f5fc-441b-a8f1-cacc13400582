#!/usr/bin/env node

/**
 * Frontend Chat Interface Debugger
 * 
 * This tool specifically debugs the frontend chat interface to identify
 * why AI responses are not being displayed in the chat area.
 */

import WebSocket from 'ws';
import config from './config.js';

class FrontendChatDebugger {
  constructor() {
    this.ws = null;
    this.isConnected = false;
    this.receivedMessages = [];
    this.chatMessages = [];
    this.errors = [];
    this.warnings = [];
    this.wheelDataReceived = false;
    this.aiResponsesReceived = 0;
    this.aiResponsesDisplayed = 0;
  }

  /**
   * Connect to the backend WebSocket
   */
  async connectToBackend() {
    console.log('🔌 Connecting to backend WebSocket for chat debugging...');
    
    return new Promise((resolve, reject) => {
      this.ws = new WebSocket(config.backend.websocketUrl);
      
      this.ws.on('open', () => {
        console.log('✅ Connected to backend');
        this.isConnected = true;
        resolve();
      });

      this.ws.on('message', (data) => {
        try {
          const message = JSON.parse(data);
          this.handleMessage(message);
        } catch (error) {
          console.error('❌ Error parsing message:', error);
          this.errors.push(`Message parsing error: ${error.message}`);
        }
      });

      this.ws.on('error', (error) => {
        console.error('❌ WebSocket error:', error);
        this.errors.push(`WebSocket error: ${error.message}`);
        reject(error);
      });

      this.ws.on('close', () => {
        console.log('🔌 WebSocket connection closed');
        this.isConnected = false;
      });
    });
  }

  /**
   * Handle incoming WebSocket messages with chat focus
   */
  handleMessage(message) {
    this.receivedMessages.push(message);
    
    console.log(`📨 Received ${message.type}`);

    switch (message.type) {
      case 'chat_message':
        this.handleChatMessage(message);
        break;
      case 'wheel_data':
        this.handleWheelData(message);
        break;
      case 'debug_info':
        this.handleDebugInfo(message);
        break;
      case 'workflow_status':
        this.handleWorkflowStatus(message);
        break;
      case 'processing_status':
        this.handleProcessingStatus(message);
        break;
      case 'system_message':
        this.handleSystemMessage(message);
        break;
      case 'error':
        this.handleErrorMessage(message);
        break;
      default:
        console.log(`📝 Unhandled message type: ${message.type}`);
    }
  }

  /**
   * Handle chat messages specifically
   */
  handleChatMessage(message) {
    const isUser = message.is_user;
    const content = message.content;
    
    console.log(`💬 ${isUser ? 'User' : 'AI'}: ${content.substring(0, 100)}${content.length > 100 ? '...' : ''}`);
    
    this.chatMessages.push({
      type: 'chat',
      isUser: isUser,
      content: content,
      timestamp: new Date().toISOString(),
      displayed: false // Track if this would be displayed in frontend
    });

    if (!isUser) {
      this.aiResponsesReceived++;
      
      // Simulate frontend chat display logic
      if (this.simulateFrontendChatDisplay(message)) {
        this.aiResponsesDisplayed++;
        this.chatMessages[this.chatMessages.length - 1].displayed = true;
      } else {
        this.warnings.push(`AI response not displayed: ${content.substring(0, 50)}...`);
      }
    }
  }

  /**
   * Simulate how frontend would handle chat message display
   */
  simulateFrontendChatDisplay(message) {
    // Check common issues that prevent display
    
    // Issue 1: Missing content
    if (!message.content || message.content.trim() === '') {
      this.errors.push('Chat message has empty content');
      return false;
    }
    
    // Issue 2: Invalid is_user field
    if (typeof message.is_user !== 'boolean') {
      this.errors.push('Chat message missing or invalid is_user field');
      return false;
    }
    
    // Issue 3: Content is not a string
    if (typeof message.content !== 'string') {
      this.errors.push('Chat message content is not a string');
      return false;
    }
    
    // Issue 4: Message too long (might cause display issues)
    if (message.content.length > 10000) {
      this.warnings.push('Chat message is very long, might cause display issues');
    }
    
    // If all checks pass, message should display
    return true;
  }

  /**
   * Handle wheel data messages
   */
  handleWheelData(message) {
    console.log('🎡 Wheel data received');
    this.wheelDataReceived = true;
    
    // Check for wheel data issues that might affect chat
    if (!message.wheel) {
      this.errors.push('wheel_data message missing wheel property');
    } else {
      console.log(`📊 Wheel contains ${message.wheel.items?.length || 0} items`);
    }
  }

  /**
   * Handle debug info messages
   */
  handleDebugInfo(message) {
    // Don't log every debug message, just track them
    if (message.content && message.content.message) {
      // Check for specific debug issues
      if (message.content.message.includes('error') || message.content.message.includes('failed')) {
        this.warnings.push(`Debug warning: ${message.content.message}`);
      }
    }
  }

  /**
   * Handle workflow status
   */
  handleWorkflowStatus(message) {
    console.log(`📋 Workflow ${message.workflow_id}: ${message.status}`);
  }

  /**
   * Handle processing status
   */
  handleProcessingStatus(message) {
    console.log(`⚙️ Processing: ${message.status}`);
  }

  /**
   * Handle system messages
   */
  handleSystemMessage(message) {
    console.log(`🔔 System: ${message.content}`);
  }

  /**
   * Handle error messages
   */
  handleErrorMessage(message) {
    console.log('❌ Error message received:', message.content || message.message);
    this.errors.push(`Backend error: ${message.content || message.message || 'Unknown error'}`);
  }

  /**
   * Send a message to test chat functionality
   */
  sendMessage(content) {
    if (!this.isConnected) {
      console.log('❌ Cannot send message - not connected');
      return;
    }

    const message = {
      type: 'chat_message',
      content: {
        message: content,
        user_profile_id: '2',
        timestamp: new Date().toISOString(),
        metadata: {}
      }
    };

    console.log(`👤 Sending: "${content}"`);
    this.ws.send(JSON.stringify(message));
  }

  /**
   * Run chat debugging test
   */
  async runChatDebugging() {
    console.log('🎬 Starting frontend chat debugging...');
    
    try {
      // Connect to backend
      await this.connectToBackend();
      
      // Wait for connection to stabilize
      await this.sleep(1000);
      
      // Send test message
      this.sendMessage("Hello, can you help me?");
      
      // Wait for response
      console.log('⏳ Waiting for AI response...');
      await this.sleep(15000);
      
      // Analyze chat functionality
      this.analyzeChatFunctionality();
      
    } catch (error) {
      console.error('❌ Chat debugging failed:', error);
      this.errors.push(`Chat debugging failed: ${error.message}`);
    } finally {
      if (this.ws) {
        this.ws.close();
      }
    }
  }

  /**
   * Analyze chat functionality and identify issues
   */
  analyzeChatFunctionality() {
    console.log('\n🔍 FRONTEND CHAT DEBUGGING ANALYSIS');
    console.log('=====================================');
    
    console.log(`📊 Total messages received: ${this.receivedMessages.length}`);
    console.log(`💬 Chat messages received: ${this.chatMessages.length}`);
    console.log(`🤖 AI responses received: ${this.aiResponsesReceived}`);
    console.log(`📺 AI responses that would display: ${this.aiResponsesDisplayed}`);
    console.log(`🎡 Wheel data received: ${this.wheelDataReceived ? 'Yes' : 'No'}`);
    console.log(`❌ Errors detected: ${this.errors.length}`);
    console.log(`⚠️ Warnings detected: ${this.warnings.length}`);
    
    // Detailed chat message analysis
    console.log('\n💬 CHAT MESSAGE ANALYSIS:');
    this.chatMessages.forEach((msg, index) => {
      const status = msg.displayed ? '✅ Would display' : '❌ Would NOT display';
      const sender = msg.isUser ? 'User' : 'AI';
      console.log(`  ${index + 1}. ${sender}: "${msg.content.substring(0, 50)}..." - ${status}`);
    });
    
    if (this.errors.length > 0) {
      console.log('\n❌ ERRORS FOUND:');
      this.errors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error}`);
      });
    }
    
    if (this.warnings.length > 0) {
      console.log('\n⚠️ WARNINGS FOUND:');
      this.warnings.forEach((warning, index) => {
        console.log(`  ${index + 1}. ${warning}`);
      });
    }
    
    // Overall assessment
    console.log('\n🎯 CHAT FUNCTIONALITY ASSESSMENT:');
    if (this.aiResponsesReceived === 0) {
      console.log('❌ CRITICAL: No AI responses received from backend');
    } else if (this.aiResponsesDisplayed === 0) {
      console.log('❌ CRITICAL: AI responses received but none would display in frontend');
    } else if (this.aiResponsesDisplayed < this.aiResponsesReceived) {
      console.log('⚠️ WARNING: Some AI responses would not display in frontend');
    } else {
      console.log('✅ SUCCESS: All AI responses would display correctly');
    }
    
    // Specific recommendations
    console.log('\n🔧 RECOMMENDATIONS:');
    if (this.aiResponsesReceived === 0) {
      console.log('  - Check backend workflow execution');
      console.log('  - Verify message classification is working');
      console.log('  - Check for backend errors in logs');
    } else if (this.aiResponsesDisplayed < this.aiResponsesReceived) {
      console.log('  - Check frontend chat message handling logic');
      console.log('  - Verify message format validation');
      console.log('  - Check for JavaScript errors in browser console');
    }
  }

  /**
   * Sleep utility
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Run the debugging if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const chatDebugger = new FrontendChatDebugger();
  chatDebugger.runChatDebugging().then(() => {
    console.log('\n🎉 Frontend chat debugging completed!');
    process.exit(0);
  }).catch((error) => {
    console.error('❌ Debugging failed:', error);
    process.exit(1);
  });
}

export default FrontendChatDebugger;
