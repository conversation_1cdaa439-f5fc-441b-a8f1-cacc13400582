#!/usr/bin/env node

/**
 * Validate Fixes Script
 * Tests the CORS fixes and debug panel improvements
 */

import { CONFIG } from './config.js';

class FixValidator {
  constructor() {
    this.results = {
      cors: { passed: 0, failed: 0, details: [] },
      debugPanel: { passed: 0, failed: 0, details: [] },
      backend: { passed: 0, failed: 0, details: [] }
    };
  }

  async validateAllFixes() {
    console.log('🔧 Validating All Fixes...');
    console.log('==========================\n');

    await this.validateCORSFixes();
    await this.validateDebugPanelImprovements();
    await this.validateBackendFixes();

    this.printSummary();
    return this.results;
  }

  async validateCORSFixes() {
    console.log('🌐 Testing CORS Fixes...');
    
    const testPorts = [3000, 3002, 5173, 5174];
    
    for (const port of testPorts) {
      try {
        // Test debug users endpoint
        const usersResponse = await this.testCORSEndpoint(
          `${CONFIG.backend.httpUrl}/api/debug/users/`,
          `http://localhost:${port}`
        );
        
        if (usersResponse.success) {
          this.results.cors.passed++;
          this.results.cors.details.push(`✅ Port ${port}: Users API accessible`);
        } else {
          this.results.cors.failed++;
          this.results.cors.details.push(`❌ Port ${port}: Users API failed - ${usersResponse.error}`);
        }

        // Test LLM configs endpoint
        const llmResponse = await this.testCORSEndpoint(
          `${CONFIG.backend.httpUrl}/api/debug/llm-configs/`,
          `http://localhost:${port}`
        );
        
        if (llmResponse.success) {
          this.results.cors.passed++;
          this.results.cors.details.push(`✅ Port ${port}: LLM Configs API accessible`);
        } else {
          this.results.cors.failed++;
          this.results.cors.details.push(`❌ Port ${port}: LLM Configs API failed - ${llmResponse.error}`);
        }
        
      } catch (error) {
        this.results.cors.failed++;
        this.results.cors.details.push(`❌ Port ${port}: Test failed - ${error.message}`);
      }
    }
  }

  async testCORSEndpoint(url, origin) {
    try {
      // Use native fetch (Node.js 18+) or fallback
      const fetch = globalThis.fetch || (await import('node-fetch')).default;
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Origin': origin,
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        credentials: 'include'
      });

      if (response.ok) {
        const data = await response.json();
        return { success: true, data };
      } else {
        const errorText = await response.text();
        return { success: false, error: `HTTP ${response.status}: ${errorText}` };
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async validateDebugPanelImprovements() {
    console.log('🐛 Testing Debug Panel Improvements...');
    
    // Test 1: Check if debug panel component exists and has error handling
    try {
      const fs = await import('fs');
      const path = await import('path');
      
      const debugPanelPath = path.join(process.cwd(), '..', 'src', 'components', 'debug', 'debug-panel.ts');
      
      if (fs.existsSync(debugPanelPath)) {
        const content = fs.readFileSync(debugPanelPath, 'utf8');
        
        // Check for error handling improvements
        const hasErrorState = content.includes('@state() private errors');
        const hasErrorDisplay = content.includes('Latest Error');
        const hasDetailedErrorHandling = content.includes('addError');
        const hasWebSocketErrorHandling = content.includes('handleWebSocketError');
        const hasCORSHeaders = content.includes('credentials: \'include\'');
        
        if (hasErrorState) {
          this.results.debugPanel.passed++;
          this.results.debugPanel.details.push('✅ Error state management added');
        } else {
          this.results.debugPanel.failed++;
          this.results.debugPanel.details.push('❌ Missing error state management');
        }
        
        if (hasErrorDisplay) {
          this.results.debugPanel.passed++;
          this.results.debugPanel.details.push('✅ Error display UI added');
        } else {
          this.results.debugPanel.failed++;
          this.results.debugPanel.details.push('❌ Missing error display UI');
        }
        
        if (hasDetailedErrorHandling) {
          this.results.debugPanel.passed++;
          this.results.debugPanel.details.push('✅ Detailed error handling implemented');
        } else {
          this.results.debugPanel.failed++;
          this.results.debugPanel.details.push('❌ Missing detailed error handling');
        }
        
        if (hasWebSocketErrorHandling) {
          this.results.debugPanel.passed++;
          this.results.debugPanel.details.push('✅ WebSocket error handling added');
        } else {
          this.results.debugPanel.failed++;
          this.results.debugPanel.details.push('❌ Missing WebSocket error handling');
        }
        
        if (hasCORSHeaders) {
          this.results.debugPanel.passed++;
          this.results.debugPanel.details.push('✅ CORS credentials headers added');
        } else {
          this.results.debugPanel.failed++;
          this.results.debugPanel.details.push('❌ Missing CORS credentials headers');
        }
        
      } else {
        this.results.debugPanel.failed++;
        this.results.debugPanel.details.push('❌ Debug panel component not found');
      }
      
    } catch (error) {
      this.results.debugPanel.failed++;
      this.results.debugPanel.details.push(`❌ Error checking debug panel: ${error.message}`);
    }
  }

  async validateBackendFixes() {
    console.log('🔧 Testing Backend Fixes...');
    
    // Test 1: Check if CORS settings are updated
    try {
      const fs = await import('fs');
      const path = await import('path');
      
      const settingsPath = path.join(process.cwd(), '..', '..', 'backend', 'config', 'settings', 'base.py');
      
      if (fs.existsSync(settingsPath)) {
        const content = fs.readFileSync(settingsPath, 'utf8');
        
        const hasPort3002 = content.includes('localhost:3002');
        const hasAllowAllOrigins = content.includes('CORS_ALLOW_ALL_ORIGINS = DEBUG');
        const hasAllowMethods = content.includes('CORS_ALLOW_METHODS');
        
        if (hasPort3002) {
          this.results.backend.passed++;
          this.results.backend.details.push('✅ Port 3002 added to CORS origins');
        } else {
          this.results.backend.failed++;
          this.results.backend.details.push('❌ Port 3002 missing from CORS origins');
        }
        
        if (hasAllowAllOrigins) {
          this.results.backend.passed++;
          this.results.backend.details.push('✅ CORS_ALLOW_ALL_ORIGINS for debug mode added');
        } else {
          this.results.backend.failed++;
          this.results.backend.details.push('❌ CORS_ALLOW_ALL_ORIGINS for debug mode missing');
        }
        
        if (hasAllowMethods) {
          this.results.backend.passed++;
          this.results.backend.details.push('✅ CORS_ALLOW_METHODS configured');
        } else {
          this.results.backend.failed++;
          this.results.backend.details.push('❌ CORS_ALLOW_METHODS missing');
        }
        
      } else {
        this.results.backend.failed++;
        this.results.backend.details.push('❌ Backend settings file not found');
      }
      
    } catch (error) {
      this.results.backend.failed++;
      this.results.backend.details.push(`❌ Error checking backend settings: ${error.message}`);
    }

    // Test 2: Check if resource agent fix is applied
    try {
      const fs = await import('fs');
      const path = await import('path');
      
      const resourceAgentPath = path.join(process.cwd(), '..', '..', 'backend', 'apps', 'main', 'agents', 'resource_agent.py');
      
      if (fs.existsSync(resourceAgentPath)) {
        const content = fs.readFileSync(resourceAgentPath, 'utf8');
        
        const hasAnalyzedType = content.includes('"analyzed_type"');
        const hasDomainSupport = content.includes('"domain_support"');
        
        if (hasAnalyzedType) {
          this.results.backend.passed++;
          this.results.backend.details.push('✅ Resource agent analyzed_type field added');
        } else {
          this.results.backend.failed++;
          this.results.backend.details.push('❌ Resource agent analyzed_type field missing');
        }
        
        if (hasDomainSupport) {
          this.results.backend.passed++;
          this.results.backend.details.push('✅ Resource agent domain_support field added');
        } else {
          this.results.backend.failed++;
          this.results.backend.details.push('❌ Resource agent domain_support field missing');
        }
        
      } else {
        this.results.backend.failed++;
        this.results.backend.details.push('❌ Resource agent file not found');
      }
      
    } catch (error) {
      this.results.backend.failed++;
      this.results.backend.details.push(`❌ Error checking resource agent: ${error.message}`);
    }

    // Test 3: Check if get_user_profile_tool fix is applied
    try {
      const fs = await import('fs');
      const path = await import('path');
      
      const toolPath = path.join(process.cwd(), '..', '..', 'backend', 'apps', 'main', 'agents', 'tools', 'get_user_profile_tool.py');
      
      if (fs.existsSync(toolPath)) {
        const content = fs.readFileSync(toolPath, 'utf8');
        
        const hasCorrectPrefetch = content.includes('current_environment__domains');
        const hasIncorrectPrefetch = content.includes('current_environment__domain_relationships__domain');
        
        if (hasCorrectPrefetch && !hasIncorrectPrefetch) {
          this.results.backend.passed++;
          this.results.backend.details.push('✅ Database prefetch_related fix applied');
        } else if (hasIncorrectPrefetch) {
          this.results.backend.failed++;
          this.results.backend.details.push('❌ Old incorrect prefetch_related still present');
        } else {
          this.results.backend.failed++;
          this.results.backend.details.push('❌ Prefetch_related fix not found');
        }
        
      } else {
        this.results.backend.failed++;
        this.results.backend.details.push('❌ get_user_profile_tool file not found');
      }
      
    } catch (error) {
      this.results.backend.failed++;
      this.results.backend.details.push(`❌ Error checking user profile tool: ${error.message}`);
    }
  }

  printSummary() {
    console.log('\n📊 Fix Validation Summary:');
    console.log('==========================');
    
    Object.entries(this.results).forEach(([category, result]) => {
      const total = result.passed + result.failed;
      const percentage = total > 0 ? Math.round((result.passed / total) * 100) : 0;
      const status = result.failed === 0 ? '✅' : '⚠️';
      
      console.log(`\n${status} ${category.toUpperCase()}: ${result.passed}/${total} (${percentage}%)`);
      
      if (result.details.length > 0) {
        result.details.forEach(detail => console.log(`  ${detail}`));
      }
    });

    // Overall assessment
    const totalPassed = Object.values(this.results).reduce((sum, r) => sum + r.passed, 0);
    const totalFailed = Object.values(this.results).reduce((sum, r) => sum + r.failed, 0);
    const overallPercentage = totalPassed + totalFailed > 0 ? 
      Math.round((totalPassed / (totalPassed + totalFailed)) * 100) : 0;

    console.log('\n🎯 Overall Fix Status:');
    if (overallPercentage >= 90) {
      console.log('✅ EXCELLENT - All fixes applied successfully');
    } else if (overallPercentage >= 75) {
      console.log('⚠️ GOOD - Most fixes applied, minor issues remain');
    } else if (overallPercentage >= 50) {
      console.log('🟡 PARTIAL - Some fixes applied, several issues remain');
    } else {
      console.log('❌ POOR - Major issues with fix implementation');
    }

    console.log(`📈 Success Rate: ${overallPercentage}%`);

    // Next steps
    console.log('\n💡 Next Steps:');
    if (totalFailed === 0) {
      console.log('1. Test debug panel in browser: npm run dev:debug');
      console.log('2. Open debug panel with Ctrl+Shift+D');
      console.log('3. Verify user/LLM config loading works');
      console.log('4. Run the failing test to verify fix');
    } else {
      console.log('1. Review failed validations above');
      console.log('2. Apply missing fixes');
      console.log('3. Re-run this validation script');
    }
  }
}

// Run validation if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const validator = new FixValidator();
  validator.validateAllFixes().catch(console.error);
}

export { FixValidator };
