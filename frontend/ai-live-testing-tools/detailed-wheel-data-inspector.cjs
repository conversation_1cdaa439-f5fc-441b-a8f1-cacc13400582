#!/usr/bin/env node

/**
 * Detailed Wheel Data Inspector
 * 
 * This script captures and analyzes the exact wheel data structure being sent
 * from the backend to identify why activities are not being displayed.
 */

const { chromium } = require('playwright');

class DetailedWheelDataInspector {
    constructor() {
        this.browser = null;
        this.page = null;
        this.wheelData = null;
        this.messages = [];
    }

    log(message) {
        console.log(`[${new Date().toISOString()}] ${message}`);
    }

    async initialize() {
        this.log('🔍 Initializing Detailed Wheel Data Inspector...');
        
        this.browser = await chromium.launch({ 
            headless: false,
            devtools: true,
            args: ['--disable-web-security', '--disable-features=VizDisplayCompositor']
        });
        
        this.page = await this.browser.newPage();
        
        // Capture WebSocket messages
        this.page.on('websocket', ws => {
            this.log(`🔌 WebSocket connection: ${ws.url()}`);
            
            ws.on('framereceived', event => {
                try {
                    const data = JSON.parse(event.payload);
                    this.messages.push({ type: 'received', data, timestamp: Date.now() });
                    
                    if (data.type === 'wheel_data') {
                        this.wheelData = data;
                        this.log('🎡 WHEEL DATA RECEIVED!');
                        this.analyzeWheelData(data);
                    }
                } catch (e) {
                    // Ignore non-JSON messages
                }
            });
        });
    }

    analyzeWheelData(data) {
        console.log('\n🔍 DETAILED WHEEL DATA ANALYSIS');
        console.log('═══════════════════════════════════════════════════════════');
        
        // Log the complete structure
        console.log('📊 Complete wheel data structure:');
        console.log(JSON.stringify(data, null, 2));
        
        console.log('\n🎯 STRUCTURE ANALYSIS:');
        console.log(`- Type: ${data.type}`);
        console.log(`- Has wheel property: ${!!data.wheel}`);
        
        if (data.wheel) {
            const wheel = data.wheel;
            console.log(`- Wheel name: ${wheel.name || 'No name'}`);
            console.log(`- Wheel metadata: ${JSON.stringify(wheel.metadata || {})}`);
            console.log(`- Has items: ${!!wheel.items}`);
            console.log(`- Items type: ${Array.isArray(wheel.items) ? 'Array' : typeof wheel.items}`);
            console.log(`- Items length: ${wheel.items ? wheel.items.length : 0}`);
            
            if (wheel.items && Array.isArray(wheel.items) && wheel.items.length > 0) {
                console.log('\n🎯 ITEMS ANALYSIS:');
                wheel.items.forEach((item, index) => {
                    console.log(`\n  Item ${index + 1}:`);
                    console.log(`    - ID: ${item.id || 'No ID'}`);
                    console.log(`    - Name: ${item.name || 'No name'}`);
                    console.log(`    - Title: ${item.title || 'No title'}`);
                    console.log(`    - Description: ${(item.description || 'No description').substring(0, 100)}...`);
                    console.log(`    - Percentage: ${item.percentage || 'No percentage'}`);
                    console.log(`    - Color: ${item.color || 'No color'}`);
                    console.log(`    - Domain: ${item.domain || 'No domain'}`);
                    console.log(`    - Activity ID: ${item.activity_tailored_id || item.activity_id || 'No activity ID'}`);
                    console.log(`    - All properties: ${Object.keys(item).join(', ')}`);
                });
            } else {
                console.log('\n❌ NO ITEMS FOUND OR ITEMS NOT AN ARRAY');
                if (wheel.items) {
                    console.log(`   Items value: ${JSON.stringify(wheel.items)}`);
                }
            }
            
            // Check for alternative structures
            if (wheel.activities) {
                console.log(`\n🔍 Found 'activities' property: ${Array.isArray(wheel.activities) ? wheel.activities.length : typeof wheel.activities} items`);
            }
            
            if (wheel.segments) {
                console.log(`\n🔍 Found 'segments' property: ${Array.isArray(wheel.segments) ? wheel.segments.length : typeof wheel.segments} items`);
            }
        }
        
        console.log('\n═══════════════════════════════════════════════════════════');
    }

    async loadFrontendAndRequestWheel() {
        this.log('🌐 Loading frontend...');
        
        try {
            await this.page.goto('http://localhost:3001/', { 
                waitUntil: 'networkidle',
                timeout: 30000 
            });
            
            await this.page.waitForTimeout(3000);
            
            this.log('📝 Sending wheel request...');
            
            // Find and use textarea
            const textarea = this.page.locator('textarea').first();
            await textarea.click();
            await textarea.fill('I need a wheel with physical activities for 2 hours');
            await this.page.keyboard.press('Enter');
            
            this.log('✅ Wheel request sent');
            
            // Wait for wheel data
            this.log('⏳ Waiting for wheel data (up to 60 seconds)...');
            
            let attempts = 0;
            const maxAttempts = 60;
            
            while (attempts < maxAttempts && !this.wheelData) {
                await this.page.waitForTimeout(1000);
                attempts++;
                
                if (attempts % 10 === 0) {
                    this.log(`⏳ Still waiting... (${attempts}/${maxAttempts})`);
                }
            }
            
            if (this.wheelData) {
                this.log('✅ Wheel data received and analyzed!');
                
                // Check frontend wheel component state
                await this.checkFrontendWheelState();
            } else {
                this.log('❌ No wheel data received within timeout');
            }
            
            return true;
        } catch (error) {
            this.log(`❌ Failed to load frontend: ${error.message}`);
            return false;
        }
    }

    async checkFrontendWheelState() {
        this.log('\n🔍 CHECKING FRONTEND WHEEL STATE');
        console.log('═══════════════════════════════════════════════════════════');
        
        try {
            // Check wheel component
            const wheelComponent = await this.page.locator('game-wheel').first();
            const wheelExists = await wheelComponent.count() > 0;
            console.log(`🎡 Wheel component exists: ${wheelExists}`);
            
            if (wheelExists) {
                // Check if wheel has segments
                const segments = await this.page.evaluate(() => {
                    const wheel = document.querySelector('game-wheel');
                    if (wheel && wheel.wheelData) {
                        return {
                            hasWheelData: true,
                            segmentsCount: wheel.wheelData.segments ? wheel.wheelData.segments.length : 0,
                            wheelData: wheel.wheelData
                        };
                    }
                    return { hasWheelData: false };
                });
                
                console.log(`📊 Frontend wheel state:`, segments);
            }
            
            // Check for any wheel-related elements
            const wheelElements = await this.page.locator('.wheel, .wheel-container, [class*="wheel"]').count();
            const activityElements = await this.page.locator('.activity, .wheel-item, [class*="activity"]').count();
            
            console.log(`🔍 Wheel elements found: ${wheelElements}`);
            console.log(`🔍 Activity elements found: ${activityElements}`);
            
        } catch (error) {
            console.log(`❌ Error checking frontend state: ${error.message}`);
        }
        
        console.log('═══════════════════════════════════════════════════════════');
    }

    async runInspection() {
        try {
            await this.initialize();
            
            if (!await this.loadFrontendAndRequestWheel()) return;
            
            this.log('🎉 Inspection completed! Browser kept open for manual verification.');
            this.log('Press Ctrl+C to close when done.');
            
            // Keep browser open for manual inspection
            await new Promise(() => {});
            
        } catch (error) {
            this.log(`❌ Inspection failed: ${error.message}`);
        }
    }

    async cleanup() {
        if (this.browser) {
            await this.browser.close();
        }
    }
}

// Handle cleanup on exit
process.on('SIGINT', async () => {
    console.log('\n🛑 Inspection interrupted by user');
    process.exit(0);
});

// Run the inspection
const inspector = new DetailedWheelDataInspector();
inspector.runInspection().catch(console.error);
