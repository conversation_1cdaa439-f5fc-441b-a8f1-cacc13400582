<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Panel Storage Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #ffffff;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #444;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background: rgba(0, 255, 0, 0.1);
            border: 1px solid #00ff00;
            color: #00ff00;
        }
        .error {
            background: rgba(255, 0, 0, 0.1);
            border: 1px solid #ff0000;
            color: #ff0000;
        }
        .info {
            background: rgba(0, 150, 255, 0.1);
            border: 1px solid #0096ff;
            color: #0096ff;
        }
        button {
            background: #0096ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0080dd;
        }
        input, select {
            background: #333;
            color: white;
            border: 1px solid #555;
            padding: 8px;
            border-radius: 4px;
            margin: 5px;
        }
        .storage-display {
            background: #333;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🐛 Debug Panel Storage Test</h1>
    <p>This page tests the localStorage functionality for debug panel selections.</p>

    <div class="test-section">
        <h2>Current Storage State</h2>
        <div id="storageDisplay" class="storage-display"></div>
        <button onclick="refreshStorage()">🔄 Refresh</button>
        <button onclick="clearStorage()">🗑️ Clear All</button>
    </div>

    <div class="test-section">
        <h2>Test Storage Operations</h2>
        
        <h3>User ID Selection</h3>
        <input type="text" id="userIdInput" placeholder="Enter user ID (e.g., 2)" value="2">
        <button onclick="saveUserId()">Save User ID</button>
        <button onclick="loadUserId()">Load User ID</button>
        <div id="userIdResult" class="test-result info"></div>

        <h3>LLM Config Selection</h3>
        <input type="text" id="llmConfigInput" placeholder="Enter LLM config ID (e.g., 5)" value="5">
        <button onclick="saveLLMConfig()">Save LLM Config</button>
        <button onclick="loadLLMConfig()">Load LLM Config</button>
        <div id="llmConfigResult" class="test-result info"></div>

        <h3>Backend URL</h3>
        <input type="text" id="backendUrlInput" placeholder="Enter backend URL" value="ws://localhost:8000/ws/game/">
        <button onclick="saveBackendUrl()">Save Backend URL</button>
        <button onclick="loadBackendUrl()">Load Backend URL</button>
        <div id="backendUrlResult" class="test-result info"></div>
    </div>

    <div class="test-section">
        <h2>Automated Tests</h2>
        <button onclick="runAllTests()">🧪 Run All Tests</button>
        <div id="testResults"></div>
    </div>

    <div class="test-section">
        <h2>WebSocket Message Test</h2>
        <p>Test sending a message with debug selections:</p>
        <input type="text" id="testMessage" placeholder="Enter test message" value="Hello, I want to create a wheel">
        <button onclick="testWebSocketMessage()">📤 Send Test Message</button>
        <div id="websocketResult" class="test-result info"></div>
    </div>

    <script>
        // Storage keys used by debug panel
        const STORAGE_KEYS = {
            userId: 'debug_selected_user_id',
            llmConfig: 'debug_selected_llm_config_id',
            backendUrl: 'debug_backend_url'
        };

        function refreshStorage() {
            const storage = {};
            for (const [key, storageKey] of Object.entries(STORAGE_KEYS)) {
                storage[key] = localStorage.getItem(storageKey);
            }
            
            document.getElementById('storageDisplay').textContent = JSON.stringify(storage, null, 2);
        }

        function clearStorage() {
            Object.values(STORAGE_KEYS).forEach(key => localStorage.removeItem(key));
            refreshStorage();
            showResult('storageDisplay', 'All storage cleared', 'success');
        }

        function saveUserId() {
            const value = document.getElementById('userIdInput').value;
            localStorage.setItem(STORAGE_KEYS.userId, value);
            showResult('userIdResult', `User ID saved: ${value}`, 'success');
            refreshStorage();
        }

        function loadUserId() {
            const value = localStorage.getItem(STORAGE_KEYS.userId);
            document.getElementById('userIdInput').value = value || '';
            showResult('userIdResult', `User ID loaded: ${value || 'null'}`, value ? 'success' : 'error');
        }

        function saveLLMConfig() {
            const value = document.getElementById('llmConfigInput').value;
            localStorage.setItem(STORAGE_KEYS.llmConfig, value);
            showResult('llmConfigResult', `LLM Config saved: ${value}`, 'success');
            refreshStorage();
        }

        function loadLLMConfig() {
            const value = localStorage.getItem(STORAGE_KEYS.llmConfig);
            document.getElementById('llmConfigInput').value = value || '';
            showResult('llmConfigResult', `LLM Config loaded: ${value || 'null'}`, value ? 'success' : 'error');
        }

        function saveBackendUrl() {
            const value = document.getElementById('backendUrlInput').value;
            localStorage.setItem(STORAGE_KEYS.backendUrl, value);
            showResult('backendUrlResult', `Backend URL saved: ${value}`, 'success');
            refreshStorage();
        }

        function loadBackendUrl() {
            const value = localStorage.getItem(STORAGE_KEYS.backendUrl);
            document.getElementById('backendUrlInput').value = value || '';
            showResult('backendUrlResult', `Backend URL loaded: ${value || 'null'}`, value ? 'success' : 'error');
        }

        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `test-result ${type}`;
        }

        function runAllTests() {
            const results = document.getElementById('testResults');
            results.innerHTML = '<h3>Running Tests...</h3>';
            
            const tests = [
                { name: 'Save and Load User ID', test: testUserIdStorage },
                { name: 'Save and Load LLM Config', test: testLLMConfigStorage },
                { name: 'Save and Load Backend URL', test: testBackendUrlStorage },
                { name: 'Clear Storage', test: testClearStorage },
                { name: 'Persistence Across Page Reload', test: testPersistence }
            ];

            let passed = 0;
            let failed = 0;

            tests.forEach(({ name, test }) => {
                try {
                    const result = test();
                    if (result) {
                        results.innerHTML += `<div class="test-result success">✅ ${name}: PASSED</div>`;
                        passed++;
                    } else {
                        results.innerHTML += `<div class="test-result error">❌ ${name}: FAILED</div>`;
                        failed++;
                    }
                } catch (error) {
                    results.innerHTML += `<div class="test-result error">❌ ${name}: ERROR - ${error.message}</div>`;
                    failed++;
                }
            });

            results.innerHTML += `<div class="test-result info"><strong>Summary: ${passed} passed, ${failed} failed</strong></div>`;
        }

        function testUserIdStorage() {
            const testValue = 'test-user-123';
            localStorage.setItem(STORAGE_KEYS.userId, testValue);
            const retrieved = localStorage.getItem(STORAGE_KEYS.userId);
            return retrieved === testValue;
        }

        function testLLMConfigStorage() {
            const testValue = 'test-config-456';
            localStorage.setItem(STORAGE_KEYS.llmConfig, testValue);
            const retrieved = localStorage.getItem(STORAGE_KEYS.llmConfig);
            return retrieved === testValue;
        }

        function testBackendUrlStorage() {
            const testValue = 'ws://test.example.com/ws/game/';
            localStorage.setItem(STORAGE_KEYS.backendUrl, testValue);
            const retrieved = localStorage.getItem(STORAGE_KEYS.backendUrl);
            return retrieved === testValue;
        }

        function testClearStorage() {
            // Set some values
            localStorage.setItem(STORAGE_KEYS.userId, 'test');
            localStorage.setItem(STORAGE_KEYS.llmConfig, 'test');
            localStorage.setItem(STORAGE_KEYS.backendUrl, 'test');
            
            // Clear them
            Object.values(STORAGE_KEYS).forEach(key => localStorage.removeItem(key));
            
            // Check they're gone
            return Object.values(STORAGE_KEYS).every(key => localStorage.getItem(key) === null);
        }

        function testPersistence() {
            // This test just checks if localStorage is working
            const testKey = 'test-persistence';
            const testValue = 'persistence-test-value';
            localStorage.setItem(testKey, testValue);
            const retrieved = localStorage.getItem(testKey);
            localStorage.removeItem(testKey);
            return retrieved === testValue;
        }

        function testWebSocketMessage() {
            const message = document.getElementById('testMessage').value;
            const userId = localStorage.getItem(STORAGE_KEYS.userId) || '2';
            const llmConfigId = localStorage.getItem(STORAGE_KEYS.llmConfig);
            
            const messageData = {
                type: 'chat_message',
                content: {
                    message: message,
                    user_profile_id: userId,
                    timestamp: new Date().toISOString(),
                    metadata: llmConfigId ? { llm_config_id: llmConfigId } : undefined
                }
            };

            showResult('websocketResult', 
                `Message prepared with debug selections:\n${JSON.stringify(messageData, null, 2)}`, 
                'info'
            );

            // If WebSocket is available, try to send
            if (window.WebSocket && window.__GOALI_WS__) {
                try {
                    window.__GOALI_WS__.send(JSON.stringify(messageData));
                    showResult('websocketResult', 
                        `✅ Message sent successfully!\n${JSON.stringify(messageData, null, 2)}`, 
                        'success'
                    );
                } catch (error) {
                    showResult('websocketResult', 
                        `❌ Failed to send message: ${error.message}\n${JSON.stringify(messageData, null, 2)}`, 
                        'error'
                    );
                }
            } else {
                showResult('websocketResult', 
                    `⚠️ WebSocket not available (message prepared but not sent)\n${JSON.stringify(messageData, null, 2)}`, 
                    'info'
                );
            }
        }

        // Initialize display
        refreshStorage();
        
        // Auto-refresh storage display every 2 seconds
        setInterval(refreshStorage, 2000);
    </script>
</body>
</html>
