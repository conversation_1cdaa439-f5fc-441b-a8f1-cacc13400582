#!/usr/bin/env node

/**
 * Simple WebSocket Debug - Check what messages are actually received
 */

const { chromium } = require('playwright');

async function simpleWebSocketDebug() {
    console.log('🚀 Starting simple WebSocket debug...');
    
    const browser = await chromium.launch({ headless: false });
    const page = await browser.newPage();
    
    // Set up message capture
    await page.addInitScript(() => {
        window.wsMessages = [];
        
        const originalWebSocket = window.WebSocket;
        window.WebSocket = function(url, protocols) {
            const ws = new originalWebSocket(url, protocols);
            
            ws.addEventListener('message', (event) => {
                try {
                    const data = JSON.parse(event.data);
                    window.wsMessages.push({
                        timestamp: Date.now(),
                        type: data.type,
                        data: data
                    });
                    console.log(`📥 WebSocket: ${data.type}`);
                } catch (e) {
                    window.wsMessages.push({
                        timestamp: Date.now(),
                        type: 'raw',
                        data: event.data
                    });
                    console.log(`📥 WebSocket raw: ${event.data}`);
                }
            });
            
            return ws;
        };
    });
    
    // Load page
    await page.goto('http://localhost:3000/');
    await page.waitForTimeout(3000);
    
    // Configure debug mode
    try {
        const userSelect = await page.locator('select').first();
        if (await userSelect.isVisible({ timeout: 2000 })) {
            await userSelect.selectOption('2');
            console.log('✅ Selected user 2');
            await page.waitForTimeout(1000);
        }
    } catch (e) {
        console.log('⚠️  No user select found');
    }
    
    // Send message
    console.log('💬 Sending message...');
    await page.fill('textarea', "I'm restless and need to do things physical. I have 2 hours. Make me a wheel");
    await page.press('textarea', 'Enter');
    
    // Wait and check messages
    console.log('⏳ Waiting 60 seconds for messages...');
    await page.waitForTimeout(60000);
    
    const messages = await page.evaluate(() => window.wsMessages || []);
    
    console.log('\n📊 RECEIVED MESSAGES:');
    console.log(`Total: ${messages.length}`);
    
    messages.forEach((msg, i) => {
        console.log(`${i+1}. [${msg.type}] ${new Date(msg.timestamp).toISOString()}`);
        if (msg.type === 'wheel_data') {
            console.log(`   🎡 WHEEL DATA FOUND!`);
            if (msg.data.wheel && msg.data.wheel.items) {
                console.log(`   📊 Activities: ${msg.data.wheel.items.length}`);
            }
        }
    });
    
    await browser.close();
}

simpleWebSocketDebug().catch(console.error);
