#!/usr/bin/env node

/**
 * END-TO-END WHEEL TEST
 * 
 * This test verifies the complete wheel generation and item removal flow.
 * It tests: authentication → wheel generation → wheel display → item removal → verification.
 */

const puppeteer = require('puppeteer');

const CONFIG = {
  testUrl: 'http://localhost:3001',
  backendUrl: 'http://localhost:8000',
  adminUser: { username: 'admin', password: 'admin123' },
  timeout: 90000
};

class EndToEndWheelTester {
  constructor() {
    this.browser = null;
    this.page = null;
    this.wheelData = null;
    this.sessionCookies = null;
  }

  async initialize() {
    console.log('🔍 Initializing End-to-End Wheel Tester...');
    
    this.browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1200, height: 800 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    this.page = await this.browser.newPage();
    
    // Enable console logging
    this.page.on('console', msg => {
      const type = msg.type();
      if (type === 'error') {
        console.log(`🖥️  [error] ${msg.text()}`);
      } else if (type === 'warn') {
        console.log(`🖥️  [warn] ${msg.text()}`);
      }
    });
    
    console.log('✅ Tester initialized');
  }

  async authenticateWithBackend() {
    console.log('🔐 Authenticating with backend API...');
    
    // Login through backend API to get session cookies
    const response = await fetch(`${CONFIG.backendUrl}/api/auth/login/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify({
        username: CONFIG.adminUser.username,
        password: CONFIG.adminUser.password
      }),
    });

    if (!response.ok) {
      throw new Error(`Backend login failed: ${response.status}`);
    }

    const data = await response.json();
    console.log('✅ Backend authentication successful:', data.user.username);

    // Extract cookies from response
    const cookies = response.headers.get('set-cookie');
    if (cookies) {
      this.sessionCookies = cookies;
      console.log('✅ Session cookies obtained');
    }

    return data;
  }

  async navigateToApp() {
    console.log('🌐 Navigating to app...');

    await this.page.goto(CONFIG.testUrl, { waitUntil: 'networkidle0' });

    // Wait for app to load
    await this.page.waitForSelector('app-shell', { timeout: 10000 });
    console.log('✅ App loaded');
  }

  async loginThroughFrontend() {
    console.log('🔐 Checking authentication status...');

    // Check current authentication status
    const authStatus = await this.page.evaluate(() => {
      const appShell = document.querySelector('app-shell');
      return {
        isAuthenticated: appShell?.isAuthenticated || false,
        currentUser: appShell?.authService?.getCurrentUser(),
        hasToken: !!appShell?.authService?.getToken(),
        debugUserId: appShell?.debugUserId
      };
    });

    console.log('🔍 Current auth status:', authStatus);

    // If already authenticated with valid user data, we're good
    if (authStatus.isAuthenticated && authStatus.currentUser?.id) {
      console.log('✅ Already authenticated with user ID:', authStatus.currentUser.id);
      return authStatus;
    }

    // If in debug mode and has debugUserId, that's sufficient
    if (authStatus.debugUserId) {
      console.log('✅ Using debug user ID:', authStatus.debugUserId);
      return authStatus;
    }

    // Check if login form is present
    const hasLoginForm = await this.page.$('login-form') !== null;

    if (hasLoginForm) {
      console.log('🔐 Login form found, performing login...');

      // Fill in credentials
      await this.page.type('login-form input[type="text"]', CONFIG.adminUser.username);
      await this.page.type('login-form input[type="password"]', CONFIG.adminUser.password);

      // Submit login
      await this.page.click('login-form button[type="submit"]');

      // Wait for successful login with user data
      await this.page.waitForFunction(() => {
        const appShell = document.querySelector('app-shell');
        const user = appShell?.authService?.getCurrentUser();
        return appShell?.isAuthenticated === true && user?.id;
      }, { timeout: 15000 });

      // Verify login was successful
      const finalAuthStatus = await this.page.evaluate(() => {
        const appShell = document.querySelector('app-shell');
        return {
          isAuthenticated: appShell?.isAuthenticated || false,
          currentUser: appShell?.authService?.getCurrentUser(),
          hasToken: !!appShell?.authService?.getToken()
        };
      });

      console.log('✅ Frontend login successful:', finalAuthStatus);
      return finalAuthStatus;
    } else {
      // No login form, but not properly authenticated - this is an issue
      console.log('⚠️ No login form found, but authentication incomplete');

      // Try to force authentication by calling the auth service directly
      const forceAuthResult = await this.page.evaluate(async (credentials) => {
        const appShell = document.querySelector('app-shell');
        if (appShell?.authService) {
          try {
            const success = await appShell.authService.authenticate(credentials.username, credentials.password);
            return {
              success,
              currentUser: appShell.authService.getCurrentUser(),
              isAuthenticated: appShell.isAuthenticated
            };
          } catch (error) {
            return { success: false, error: error.message };
          }
        }
        return { success: false, error: 'Auth service not found' };
      }, CONFIG.adminUser);

      console.log('🔧 Force auth result:', forceAuthResult);

      if (forceAuthResult.success) {
        console.log('✅ Force authentication successful');
        return forceAuthResult;
      } else {
        throw new Error(`Authentication failed: ${forceAuthResult.error}`);
      }
    }
  }

  async generateWheel() {
    console.log('🎡 Generating wheel...');
    
    // Wait for the app to be ready
    await this.page.waitForFunction(() => {
      const appShell = document.querySelector('app-shell');
      return appShell?.wheelStateMachine?.shouldShowGenerateButton === true;
    }, { timeout: 10000 });
    
    // Debug button state
    const buttonState = await this.page.evaluate(() => {
      const appShell = document.querySelector('app-shell');
      const button = appShell?.shadowRoot?.querySelector('.action-button.generate-button');
      const spinButton = appShell?.shadowRoot?.querySelector('.action-button.spin-button');

      return {
        generateButton: button ? {
          exists: true,
          disabled: button.disabled,
          text: button.textContent?.trim(),
          className: button.className
        } : { exists: false },
        spinButton: spinButton ? {
          exists: true,
          disabled: spinButton.disabled,
          text: spinButton.textContent?.trim(),
          className: spinButton.className
        } : { exists: false },
        wheelState: {
          shouldShowGenerateButton: appShell?.wheelStateMachine?.shouldShowGenerateButton,
          shouldShowSpinButton: appShell?.wheelStateMachine?.shouldShowSpinButton,
          state: appShell?.wheelStateMachine?.context?.state,
          hasData: appShell?.wheelData?.segments?.length > 0
        }
      };
    });

    console.log('🔍 Button state debug:', JSON.stringify(buttonState, null, 2));

    // Clear wheel if it's populated with mock data
    if (buttonState.wheelState.shouldShowSpinButton && !buttonState.wheelState.shouldShowGenerateButton) {
      console.log('🧹 Clearing wheel to show generate button...');
      await this.page.evaluate(() => {
        const appShell = document.querySelector('app-shell');
        if (appShell?.wheelStateMachine) {
          appShell.wheelStateMachine.clearWheel();
        }
      });

      // Wait for generate button to appear
      await this.page.waitForFunction(() => {
        const appShell = document.querySelector('app-shell');
        return appShell?.wheelStateMachine?.shouldShowGenerateButton === true;
      }, { timeout: 5000 });

      console.log('✅ Wheel cleared, generate button should now be visible');
    }

    // Check if generation is already in progress
    if (buttonState.generateButton.disabled && buttonState.generateButton.text === 'Generating...') {
      console.log('🔄 Wheel generation already in progress, waiting for completion...');
    } else {
      // Click generate button in shadow DOM
      const clicked = await this.page.evaluate(() => {
        const appShell = document.querySelector('app-shell');
        const button = appShell?.shadowRoot?.querySelector('.action-button.generate-button');
        if (button && !button.disabled) {
          button.click();
          return true;
        }
        return false;
      });

      if (!clicked) {
        // Final debug before failing
        const finalButtonState = await this.page.evaluate(() => {
          const appShell = document.querySelector('app-shell');
          const button = appShell?.shadowRoot?.querySelector('.action-button.generate-button');
          return {
            buttonExists: !!button,
            buttonDisabled: button?.disabled,
            buttonText: button?.textContent?.trim(),
            wheelState: appShell?.wheelStateMachine?.context?.state,
            shouldShowGenerate: appShell?.wheelStateMachine?.shouldShowGenerateButton
          };
        });

        throw new Error(`Could not click generate button. Final state: ${JSON.stringify(finalButtonState)}`);
      }

      console.log('🔄 Wheel generation started...');
    }

    
    // Wait for wheel data to be received (real data, not mock)
    await this.page.waitForFunction(() => {
      const appShell = document.querySelector('app-shell');
      const wheelData = appShell?.wheelData;
      return wheelData?.segments?.length > 0 && 
             wheelData.segments.some(segment => 
               segment.name && 
               segment.name !== 'Mock Activity' && 
               segment.name.length > 5
             );
    }, { timeout: CONFIG.timeout });
    
    // Get wheel data
    this.wheelData = await this.page.evaluate(() => {
      const appShell = document.querySelector('app-shell');
      return appShell?.wheelData;
    });
    
    console.log(`✅ Wheel generated with ${this.wheelData.segments.length} items`);
    return this.wheelData;
  }

  async verifyWheelDisplay() {
    console.log('🔍 Verifying wheel display...');
    
    if (!this.wheelData || !this.wheelData.segments) {
      throw new Error('No wheel data available for verification');
    }
    
    // Verify wheel component has the correct data
    const wheelComponentData = await this.page.evaluate(() => {
      const wheelComponent = document.querySelector('app-shell')?.shadowRoot?.querySelector('game-wheel');
      return wheelComponent?.wheelData;
    });
    
    if (!wheelComponentData) {
      throw new Error('Wheel component has no data');
    }
    
    console.log('✅ Wheel component has data');
    
    // Verify each wheel item
    for (let i = 0; i < this.wheelData.segments.length; i++) {
      const segment = this.wheelData.segments[i];
      console.log(`🔍 Verifying item ${i + 1}: ${segment.name}`);
      
      if (!segment.name || segment.name.length < 3) {
        throw new Error(`Segment ${i} has invalid name: ${segment.name}`);
      }
      if (!segment.color) {
        throw new Error(`Segment ${i} missing color`);
      }
      
      console.log(`  ✅ Name: ${segment.name}`);
      console.log(`  ✅ Color: ${segment.color}`);
    }
    
    return true;
  }

  async testWheelItemRemoval() {
    console.log('🗑️ Testing wheel item removal...');
    
    if (!this.wheelData || this.wheelData.segments.length === 0) {
      throw new Error('No wheel items to remove');
    }
    
    const initialCount = this.wheelData.segments.length;
    const itemToRemove = this.wheelData.segments[0];
    
    console.log(`🎯 Removing item: ${itemToRemove.name}`);
    
    // Look for remove button (❌) for the first item
    const removeButtonClicked = await this.page.evaluate((itemName) => {
      const appShell = document.querySelector('app-shell');
      const activityList = appShell?.shadowRoot?.querySelector('.activity-list');
      
      if (!activityList) return false;
      
      // Find the activity item and its remove button
      const activityItems = activityList.querySelectorAll('.activity-item');
      for (const item of activityItems) {
        const nameElement = item.querySelector('.activity-name');
        if (nameElement && nameElement.textContent?.includes(itemName)) {
          const removeButton = item.querySelector('.remove-activity-btn');
          if (removeButton) {
            removeButton.click();
            return true;
          }
        }
      }
      return false;
    }, itemToRemove.name);
    
    if (!removeButtonClicked) {
      throw new Error('Could not find or click remove button');
    }
    
    console.log('🔄 Remove button clicked, waiting for update...');
    
    // Wait for the wheel data to be updated
    await this.page.waitForFunction((expectedCount) => {
      const appShell = document.querySelector('app-shell');
      const currentWheelData = appShell?.wheelData;
      return currentWheelData?.segments?.length === expectedCount;
    }, { timeout: 10000 }, initialCount - 1);
    
    // Get updated wheel data
    const updatedWheelData = await this.page.evaluate(() => {
      const appShell = document.querySelector('app-shell');
      return appShell?.wheelData;
    });
    
    if (updatedWheelData.segments.length !== initialCount - 1) {
      throw new Error(`Expected ${initialCount - 1} items, got ${updatedWheelData.segments.length}`);
    }
    
    // Verify the specific item was removed
    const itemStillExists = updatedWheelData.segments.some(segment => 
      segment.name === itemToRemove.name
    );
    
    if (itemStillExists) {
      throw new Error(`Item "${itemToRemove.name}" was not removed`);
    }
    
    console.log(`✅ Item removed successfully. Wheel now has ${updatedWheelData.segments.length} items`);
    
    // Check console for removal confirmation
    const consoleMessages = await this.page.evaluate(() => {
      return window.lastConsoleMessages || [];
    });
    
    console.log('📋 Recent console activity:', consoleMessages.slice(-5));
    
    return updatedWheelData;
  }

  async runFullTest() {
    try {
      await this.initialize();
      await this.navigateToApp();
      await this.loginThroughFrontend();
      await this.generateWheel();
      await this.verifyWheelDisplay();
      const finalWheelData = await this.testWheelItemRemoval();

      console.log('\n🎉 END-TO-END WHEEL TEST PASSED!');
      console.log('════════════════════════════════════════');
      console.log(`✅ Authentication: Working`);
      console.log(`✅ Wheel Generation: Working`);
      console.log(`✅ Wheel Display: Working`);
      console.log(`✅ Item Removal: Working`);
      console.log(`✅ Final Items Count: ${finalWheelData.segments.length}`);

      return true;

    } catch (error) {
      console.error('\n❌ END-TO-END WHEEL TEST FAILED!');
      console.error('════════════════════════════════════════');
      console.error(`Error: ${error.message}`);
      return false;
    } finally {
      if (this.browser) {
        await this.browser.close();
      }
    }
  }
}

// Run the test
async function main() {
  console.log('🔍 END-TO-END WHEEL TEST');
  console.log('════════════════════════════════════════');
  
  const tester = new EndToEndWheelTester();
  const success = await tester.runFullTest();
  
  process.exit(success ? 0 : 1);
}

main().catch(console.error);
