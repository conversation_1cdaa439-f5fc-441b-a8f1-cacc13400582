#!/usr/bin/env node

/**
 * Connection Tracking Test
 * 
 * Simple test to verify if the connection tracking is working
 */

import WebSocket from 'ws';

console.log('🔍 Connection Tracking Test');
console.log('===========================\n');

// Test 1: Connect admin socket and check initial state
console.log('📋 Test 1: Check initial connection state');
const adminSocket = new WebSocket('ws://localhost:8000/ws/connection-monitor/');

adminSocket.on('open', () => {
  console.log('✅ Admin socket connected');
  
  // Request initial connection data
  adminSocket.send(JSON.stringify({ type: 'get_connections' }));
  console.log('📤 Requested initial connection data');
});

adminSocket.on('message', (data) => {
  try {
    const message = JSON.parse(data);
    if (message.type === 'connection_data') {
      console.log(`📊 Initial connections: ${message.data.length}`);
      
      // Test 2: Create a client and check if it's tracked
      console.log('\n📋 Test 2: Create client and check tracking');
      createTestClient();
    }
  } catch (error) {
    console.log('❌ Failed to parse admin message:', error.message);
  }
});

adminSocket.on('error', (error) => {
  console.log('❌ Admin socket error:', error.message);
});

function createTestClient() {
  const clientSocket = new WebSocket('ws://localhost:8000/ws/game/');
  
  clientSocket.on('open', () => {
    console.log('✅ Test client connected');
    
    // Send identification message with real user ID
    const message = {
      type: 'chat_message',
      content: {
        message: 'Test connection tracking',
        user_profile_id: '2', // Use real user ID (PhiPhi - fake user for testing)
        timestamp: new Date().toISOString()
      }
    };
    
    clientSocket.send(JSON.stringify(message));
    console.log('📤 Sent identification message');
    
    // Wait a bit then check connections again
    setTimeout(() => {
      console.log('\n📋 Test 3: Check connections after client creation');
      adminSocket.send(JSON.stringify({ type: 'get_connections' }));
      console.log('📤 Requested updated connection data');
      
      // Set up listener for the response
      const responseHandler = (data) => {
        try {
          const message = JSON.parse(data);
          if (message.type === 'connection_data') {
            console.log(`📊 Connections after client: ${message.data.length}`);
            
            if (message.data.length > 0) {
              console.log('✅ Connection tracking is working!');
              console.log('📋 Connection details:', JSON.stringify(message.data, null, 2));
            } else {
              console.log('❌ Connection tracking is NOT working - still 0 connections');
            }
            
            // Cleanup
            clientSocket.close();
            adminSocket.close();
            process.exit(0);
          }
        } catch (error) {
          console.log('❌ Failed to parse response:', error.message);
        }
      };
      
      // Remove previous listeners and add new one
      adminSocket.removeAllListeners('message');
      adminSocket.on('message', responseHandler);
      
    }, 2000);
  });
  
  clientSocket.on('error', (error) => {
    console.log('❌ Test client error:', error.message);
  });
  
  clientSocket.on('message', (data) => {
    try {
      const message = JSON.parse(data);
      console.log(`📨 Client received: ${message.type}`);
    } catch (error) {
      console.log('❌ Failed to parse client message:', error.message);
    }
  });
}

// Auto-exit after 10 seconds
setTimeout(() => {
  console.log('\n⏰ Test timeout - exiting');
  process.exit(1);
}, 10000);
