#!/usr/bin/env node

/**
 * AUTHENTICATION STABILITY TEST
 * 
 * This test specifically targets the authentication loss issue during wheel generation.
 * It will catch the "No authenticated user found" error and validate that authentication
 * is maintained throughout the entire wheel generation process.
 */

const puppeteer = require('puppeteer');

const CONFIG = {
  testUrl: 'http://localhost:3000',
  adminUser: { username: 'admin', password: 'admin123' },
  testUser: { username: 'phiphi', password: 'phiphi123' },
  testMessage: 'Generate a wheel for me',
  timeout: 45000
};

class AuthenticationStabilityTest {
  constructor() {
    this.browser = null;
    this.page = null;
    this.authenticationEvents = [];
    this.progressEvents = [];
    this.wheelEvents = [];
    this.networkEvents = [];
  }

  async run() {
    console.log('🔐 AUTHENTICATION STABILITY TEST');
    console.log('================================');

    try {
      await this.setupBrowser();
      await this.navigateToApp();
      await this.loginAsAdmin();
      await this.monitorWheelGeneration();
      await this.analyzeAuthenticationStability();
      
    } catch (error) {
      console.error('💥 Authentication test failed:', error.message);
      await this.generateDetailedReport();
    } finally {
      await this.cleanup();
    }
  }

  async setupBrowser() {
    console.log('🚀 Setting up browser with authentication monitoring...');
    this.browser = await puppeteer.launch({
      headless: false,
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
      devtools: true
    });
    this.page = await this.browser.newPage();
    
    // Monitor authentication-related console messages
    this.page.on('console', (msg) => {
      const text = msg.text();
      const timestamp = new Date().toISOString();
      
      // Track authentication events
      if (text.includes('authenticated') || 
          text.includes('login') || 
          text.includes('auth') ||
          text.includes('user found') ||
          text.includes('No authenticated user')) {
        this.authenticationEvents.push({ timestamp, message: text, type: 'auth' });
        console.log(`🔐 AUTH EVENT: ${text}`);
      }
      
      // Track progress events
      if (text.includes('progress') || text.includes('generating') || text.includes('wheel')) {
        this.progressEvents.push({ timestamp, message: text, type: 'progress' });
        console.log(`📊 PROGRESS: ${text}`);
      }
      
      // Track wheel events
      if (text.includes('wheel_data') || text.includes('segments')) {
        this.wheelEvents.push({ timestamp, message: text, type: 'wheel' });
        console.log(`🎡 WHEEL: ${text}`);
      }
    });

    // Monitor network requests
    this.page.on('request', (request) => {
      if (request.url().includes('/api/') || request.url().includes('/ws/')) {
        this.networkEvents.push({
          timestamp: new Date().toISOString(),
          url: request.url(),
          method: request.method(),
          headers: request.headers(),
          type: 'request'
        });
      }
    });

    this.page.on('response', (response) => {
      if (response.url().includes('/api/') || response.url().includes('/ws/')) {
        this.networkEvents.push({
          timestamp: new Date().toISOString(),
          url: response.url(),
          status: response.status(),
          type: 'response'
        });
        
        if (response.status() === 401 || response.status() === 403) {
          console.log(`🚨 AUTH FAILURE: ${response.status()} - ${response.url()}`);
        }
      }
    });
  }

  async navigateToApp() {
    console.log('🌐 Navigating to app...');
    await this.page.goto(CONFIG.testUrl, { waitUntil: 'networkidle0' });
    await this.page.waitForSelector('app-shell', { timeout: CONFIG.timeout });
  }

  async loginAsAdmin() {
    console.log('🔐 Logging in as admin...');
    
    // Check if login form is present
    const loginForm = await this.page.$('.login-form');
    if (!loginForm) {
      console.log('✅ Already logged in');
      return;
    }

    // Fill login form
    await this.page.type('input[name="username"]', CONFIG.adminUser.username);
    await this.page.type('input[name="password"]', CONFIG.adminUser.password);
    await this.page.click('button[type="submit"]');
    
    // Wait for login to complete
    await this.page.waitForSelector('.login-form', { hidden: true, timeout: 10000 });
    
    // Verify authentication state
    const authState = await this.page.evaluate(() => {
      const appShell = document.querySelector('app-shell');
      return {
        isAuthenticated: appShell?.isAuthenticated || false,
        currentUser: appShell?.currentUser || null,
        authToken: localStorage.getItem('auth_token')
      };
    });

    console.log('👤 Post-login auth state:', authState);
    
    if (!authState.isAuthenticated) {
      throw new Error('❌ Login failed - not authenticated');
    }
    
    console.log('✅ Admin login successful');
  }

  async monitorWheelGeneration() {
    console.log('🎡 Starting wheel generation with authentication monitoring...');
    
    // Clear previous events
    this.authenticationEvents = [];
    this.progressEvents = [];
    this.wheelEvents = [];
    this.networkEvents = [];
    
    // Get initial authentication state
    const initialAuthState = await this.page.evaluate(() => {
      const appShell = document.querySelector('app-shell');
      return {
        isAuthenticated: appShell?.isAuthenticated || false,
        currentUser: appShell?.currentUser || null,
        authToken: localStorage.getItem('auth_token')
      };
    });
    
    console.log('🔍 Initial auth state:', initialAuthState);
    
    // Find and use chat input
    await this.page.waitForSelector('chat-interface input[type="text"]');
    const chatInput = await this.page.$('chat-interface input[type="text"]');
    
    if (!chatInput) {
      throw new Error('❌ Chat input not found');
    }

    // Send wheel generation request
    await chatInput.type(CONFIG.testMessage);
    await this.page.keyboard.press('Enter');
    
    console.log('📤 Wheel generation request sent');
    
    // Monitor for 30 seconds
    let monitoringTime = 0;
    const maxMonitoringTime = 30000; // 30 seconds
    const checkInterval = 1000; // 1 second
    
    while (monitoringTime < maxMonitoringTime) {
      await this.page.waitForTimeout(checkInterval);
      monitoringTime += checkInterval;
      
      // Check current authentication state
      const currentAuthState = await this.page.evaluate(() => {
        const appShell = document.querySelector('app-shell');
        return {
          isAuthenticated: appShell?.isAuthenticated || false,
          currentUser: appShell?.currentUser || null,
          authToken: localStorage.getItem('auth_token'),
          wheelData: appShell?.wheelData || null
        };
      });
      
      // Log authentication state changes
      if (currentAuthState.isAuthenticated !== initialAuthState.isAuthenticated) {
        console.log(`🚨 AUTH STATE CHANGED: ${initialAuthState.isAuthenticated} → ${currentAuthState.isAuthenticated}`);
      }
      
      // Check if wheel was received
      if (currentAuthState.wheelData && currentAuthState.wheelData.segments) {
        console.log(`✅ Wheel received with ${currentAuthState.wheelData.segments.length} items`);
        break;
      }
      
      // Check for authentication errors in events
      const authErrors = this.authenticationEvents.filter(event => 
        event.message.includes('No authenticated user') || 
        event.message.includes('authentication failed')
      );
      
      if (authErrors.length > 0) {
        console.log('🚨 AUTHENTICATION ERRORS DETECTED:', authErrors);
        break;
      }
    }
    
    console.log('📊 Monitoring completed');
  }

  async analyzeAuthenticationStability() {
    console.log('\n🔍 AUTHENTICATION STABILITY ANALYSIS');
    console.log('====================================');
    
    // Check for authentication errors
    const authErrors = this.authenticationEvents.filter(event => 
      event.message.includes('No authenticated user') || 
      event.message.includes('authentication failed') ||
      event.message.includes('unauthorized')
    );
    
    // Check for network authentication failures
    const networkAuthFailures = this.networkEvents.filter(event => 
      event.type === 'response' && (event.status === 401 || event.status === 403)
    );
    
    console.log(`📊 Total authentication events: ${this.authenticationEvents.length}`);
    console.log(`🚨 Authentication errors: ${authErrors.length}`);
    console.log(`🌐 Network auth failures: ${networkAuthFailures.length}`);
    console.log(`📈 Progress events: ${this.progressEvents.length}`);
    console.log(`🎡 Wheel events: ${this.wheelEvents.length}`);
    
    if (authErrors.length > 0) {
      console.log('\n💥 AUTHENTICATION ERRORS DETECTED:');
      authErrors.forEach(error => {
        console.log(`  - ${error.timestamp}: ${error.message}`);
      });
    }
    
    if (networkAuthFailures.length > 0) {
      console.log('\n🚨 NETWORK AUTHENTICATION FAILURES:');
      networkAuthFailures.forEach(failure => {
        console.log(`  - ${failure.timestamp}: ${failure.status} ${failure.url}`);
      });
    }
    
    // Determine if authentication was stable
    const authenticationStable = authErrors.length === 0 && networkAuthFailures.length === 0;
    
    if (authenticationStable) {
      console.log('\n✅ AUTHENTICATION STABLE - No issues detected');
    } else {
      console.log('\n❌ AUTHENTICATION UNSTABLE - Issues detected');
      throw new Error('Authentication stability test failed');
    }
  }

  async generateDetailedReport() {
    console.log('\n📋 DETAILED FAILURE REPORT');
    console.log('==========================');
    
    console.log('\n🔐 Authentication Events:');
    this.authenticationEvents.forEach(event => {
      console.log(`  ${event.timestamp}: ${event.message}`);
    });
    
    console.log('\n📊 Progress Events:');
    this.progressEvents.forEach(event => {
      console.log(`  ${event.timestamp}: ${event.message}`);
    });
    
    console.log('\n🌐 Network Events:');
    this.networkEvents.forEach(event => {
      if (event.type === 'response' && (event.status >= 400 || event.url.includes('auth'))) {
        console.log(`  ${event.timestamp}: ${event.status} ${event.url}`);
      }
    });
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
    }
  }
}

// Run the authentication stability test
const test = new AuthenticationStabilityTest();
test.run().catch(console.error);
