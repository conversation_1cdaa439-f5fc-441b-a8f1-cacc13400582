#!/usr/bin/env python3

"""
Test Basic User Story: User asks for wheel, gets quick response asking for more info
"""

import asyncio
import sys
import os
import time

# Add the app directory to Python path
sys.path.append('/usr/src/app')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')

import django
django.setup()

from apps.main.services.conversation_dispatcher import ConversationDispatcher

async def test_wheel_request():
    print("🎯 Testing Basic User Story: Wheel Request → Quick Response")
    
    # Test with the newly created German student (ID: 103)
    user_id = 103
    print(f"👤 Testing with user ID: {user_id} (<PERSON> - <PERSON> Student)")
    
    # Create dispatcher
    dispatcher = ConversationDispatcher(user_id)
    
    # Send wheel request
    message = {'text': 'Hi! I need a wheel with activities for my day. Can you help me?'}
    print(f"📤 Request: \"{message['text']}\"")
    
    # Measure response time
    start_time = time.time()
    response = await dispatcher.process_message(message)
    end_time = time.time()
    
    response_time = (end_time - start_time) * 1000  # Convert to milliseconds
    print(f"⏱️ Response time: {response_time:.0f}ms")
    
    # Analyze response
    print("\n📥 System Response Analysis:")
    print(f"🔍 Response Type: {type(response)}")
    
    if isinstance(response, dict):
        workflow_type = response.get('workflow_type', 'unknown')
        confidence = response.get('confidence', 0)
        status = response.get('status', 'unknown')
        
        print(f"🔄 Workflow: {workflow_type}")
        print(f"📊 Confidence: {confidence:.1%}")
        print(f"📋 Status: {status}")
        
        # Look for user-facing message
        user_message = None
        if 'direct_response' in response:
            user_message = response['direct_response']
        elif 'user_message' in response:
            user_message = response['user_message']
        elif 'message' in response:
            user_message = response['message']
        elif 'content' in response:
            user_message = response['content']
        elif 'text' in response:
            user_message = response['text']
        
        if user_message:
            print(f"💬 User Message: \"{user_message}\"")
        else:
            print("⚠️ No user-facing message found")
            print(f"📋 Full response keys: {list(response.keys())}")
        
        # Analyze response quality
        print("\n3️⃣ Response Quality Analysis:")
        
        # Check response time
        if response_time < 5000:
            print("✅ Response time: EXCELLENT (< 5 seconds)")
        elif response_time < 10000:
            print("⚠️ Response time: ACCEPTABLE (5-10 seconds)")
        else:
            print("❌ Response time: TOO SLOW (> 10 seconds)")
        
        # Check workflow routing
        if workflow_type == 'onboarding':
            print("✅ Workflow routing: CORRECT (onboarding for incomplete profile)")
        elif workflow_type == 'wheel_generation':
            print("⚠️ Workflow routing: DIRECT TO WHEEL (bypassing profile completion)")
        else:
            print(f"❌ Workflow routing: UNEXPECTED ({workflow_type})")
        
        # Check if system asks for more information
        if user_message:
            asks_for_info = any(phrase in user_message.lower() for phrase in [
                'tell me', 'more about', 'information', 'preferences', 
                'interests', 'goals', 'help me understand', 'would you like',
                'can you tell', 'what are', 'describe'
            ])
            
            if asks_for_info:
                print("✅ Information gathering: CORRECT (asks for more details)")
            else:
                print("⚠️ Information gathering: UNCLEAR (response exists but unclear if asking for info)")
        else:
            print("❌ Information gathering: MISSING (no user-facing response)")
        
        # Overall assessment
        print("\n🎯 BASIC USER STORY ASSESSMENT:")
        print("════════════════════════════════════════")
        
        is_quick_response = response_time < 10000
        is_correct_workflow = workflow_type in ['onboarding', 'wheel_generation']
        has_user_message = user_message is not None and len(str(user_message)) > 0
        
        if is_quick_response and is_correct_workflow and has_user_message:
            print("🎉 BASIC USER STORY: ✅ WORKING CORRECTLY")
            print("✅ Quick response time")
            print("✅ Appropriate workflow routing")
            print("✅ User-facing message provided")
        else:
            print("⚠️ BASIC USER STORY: NEEDS ATTENTION")
            
            if not is_quick_response:
                print("❌ Response time too slow")
            if not is_correct_workflow:
                print("❌ Unexpected workflow routing")
            if not has_user_message:
                print("❌ No user-facing message")
    
    else:
        print(f"❌ Unexpected response type: {type(response)}")
        print(f"📋 Response content: {response}")

if __name__ == "__main__":
    asyncio.run(test_wheel_request())
