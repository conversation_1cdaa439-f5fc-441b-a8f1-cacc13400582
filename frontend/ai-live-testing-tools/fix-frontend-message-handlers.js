#!/usr/bin/env node

/**
 * Fix Frontend Message Handlers
 * 
 * This script fixes the critical issue where the frontend doesn't handle
 * all message types defined in MESSAGE_SPECIFICATIONS.md
 */

import fs from 'fs';
import path from 'path';

class FrontendMessageHandlerFixer {
  constructor() {
    this.colors = {
      reset: '\x1b[0m',
      red: '\x1b[31m',
      green: '\x1b[32m',
      yellow: '\x1b[33m',
      blue: '\x1b[34m',
      cyan: '\x1b[36m'
    };
  }

  async fix() {
    console.log(`${this.colors.cyan}🔧 Frontend Message Handler Fixer${this.colors.reset}`);
    console.log(`${this.colors.cyan}===================================${this.colors.reset}\n`);

    try {
      // Fix app-shell.ts message handlers
      await this.fixAppShellHandlers();
      
      // Fix websocket-manager.ts message processing
      await this.fixWebSocketManagerHandlers();
      
      console.log(`\n${this.colors.green}✅ Frontend message handlers fixed!${this.colors.reset}`);
      console.log(`${this.colors.green}The frontend should now properly handle all message types.${this.colors.reset}`);
      
    } catch (error) {
      console.error(`${this.colors.red}❌ Error fixing handlers: ${error.message}${this.colors.reset}`);
    }
  }

  async fixAppShellHandlers() {
    console.log(`${this.colors.blue}📝 Fixing app-shell.ts message handlers...${this.colors.reset}`);
    
    const appShellPath = '../src/components/app-shell.ts';
    
    if (!fs.existsSync(appShellPath)) {
      console.log(`${this.colors.yellow}⚠️ app-shell.ts not found at ${appShellPath}${this.colors.reset}`);
      return;
    }

    let content = fs.readFileSync(appShellPath, 'utf8');
    
    // Check if handlers already exist
    if (content.includes('debug_info') && content.includes('workflow_status')) {
      console.log(`${this.colors.green}✅ Message handlers already exist in app-shell.ts${this.colors.reset}`);
      return;
    }

    // Add missing handlers
    const handlersToAdd = `
    this.websocketManager.onMessage('debug_info', (data) => {
      this.handleDebugInfo(data);
    });

    this.websocketManager.onMessage('workflow_status', (data) => {
      this.handleWorkflowStatus(data);
    });`;

    // Find the setupMessageHandlers method and add handlers
    const setupHandlersRegex = /(this\.websocketManager\.onMessage\('error'[^}]+}\);)/;
    
    if (setupHandlersRegex.test(content)) {
      content = content.replace(setupHandlersRegex, `$1${handlersToAdd}`);
      
      // Add handler methods
      const handlerMethods = `
  /**
   * Handle debug information messages
   */
  private handleDebugInfo(data: any): void {
    if (this.configService.isDebugMode()) {
      console.log('🔧 Debug Info:', data);
      // Could display in debug panel if needed
    }
  }

  /**
   * Handle workflow status updates
   */
  private handleWorkflowStatus(data: any): void {
    console.log('📋 Workflow Status:', data);
    // Could update UI workflow indicators
    this.dispatchEvent(new CustomEvent('workflow-status-change', { 
      detail: data 
    }));
  }`;

      // Add methods before the last closing brace
      const lastBraceIndex = content.lastIndexOf('}');
      content = content.slice(0, lastBraceIndex) + handlerMethods + '\n' + content.slice(lastBraceIndex);
      
      fs.writeFileSync(appShellPath, content);
      console.log(`${this.colors.green}✅ Added debug_info and workflow_status handlers to app-shell.ts${this.colors.reset}`);
    } else {
      console.log(`${this.colors.yellow}⚠️ Could not find setupMessageHandlers method in app-shell.ts${this.colors.reset}`);
    }
  }

  async fixWebSocketManagerHandlers() {
    console.log(`${this.colors.blue}📝 Checking websocket-manager.ts message validation...${this.colors.reset}`);
    
    const wsManagerPath = '../src/services/websocket-manager.ts';
    
    if (!fs.existsSync(wsManagerPath)) {
      console.log(`${this.colors.yellow}⚠️ websocket-manager.ts not found at ${wsManagerPath}${this.colors.reset}`);
      return;
    }

    let content = fs.readFileSync(wsManagerPath, 'utf8');
    
    // Check if message types are properly handled
    if (content.includes('debug_info') && content.includes('workflow_status')) {
      console.log(`${this.colors.green}✅ Message types already handled in websocket-manager.ts${this.colors.reset}`);
      return;
    }

    // Add message type validation if needed
    const messageTypes = ['debug_info', 'workflow_status'];
    
    for (const messageType of messageTypes) {
      if (!content.includes(`'${messageType}'`)) {
        console.log(`${this.colors.yellow}⚠️ Message type '${messageType}' not found in websocket-manager.ts${this.colors.reset}`);
        console.log(`${this.colors.yellow}   Consider adding it to the ServerMessage type definition${this.colors.reset}`);
      }
    }
  }

  async createTestScript() {
    console.log(`${this.colors.blue}📝 Creating test script for message handlers...${this.colors.reset}`);
    
    const testScript = `#!/usr/bin/env node

/**
 * Test Frontend Message Handlers
 * 
 * Tests that the frontend properly handles all message types
 */

import WebSocket from 'ws';

const testMessages = [
  {
    type: 'debug_info',
    content: {
      timestamp: new Date().toISOString(),
      source: 'Test',
      level: 'info',
      message: 'Test debug message',
      details: { test: true }
    }
  },
  {
    type: 'workflow_status',
    workflow_id: 'test-workflow-123',
    status: 'initiated'
  }
];

console.log('🧪 Testing frontend message handlers...');
console.log('Open browser console to see if messages are handled properly');

// This would need to be run with the frontend running
// and would send test messages to verify handling
`;

    fs.writeFileSync('test-message-handlers.js', testScript);
    console.log(`${this.colors.green}✅ Created test-message-handlers.js${this.colors.reset}`);
  }
}

// Run the fixer
const fixer = new FrontendMessageHandlerFixer();
fixer.fix().catch(console.error);
