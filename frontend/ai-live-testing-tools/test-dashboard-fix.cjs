#!/usr/bin/env node

/**
 * Test script to verify the dashboard fixes are working
 */

const WebSocket = require('ws');

// Configuration
const BACKEND_URL = 'ws://localhost:8000';
const ADMIN_WS_URL = `${BACKEND_URL}/ws/connection-monitor/`;
const CLIENT_WS_URL = `${BACKEND_URL}/ws/game/`;

console.log('🔧 Dashboard Fix Verification Test');
console.log('==================================\n');

let adminSocket = null;
let clientSocket = null;
let testResults = {
    adminConnection: false,
    clientConnection: false,
    connectionDataReceived: false,
    connectionCount: 0
};

// Test 1: Connect admin socket
function connectAdminSocket() {
    return new Promise((resolve, reject) => {
        console.log('📋 Test 1: Connecting admin socket...');
        
        adminSocket = new WebSocket(ADMIN_WS_URL);
        
        adminSocket.on('open', () => {
            console.log('✅ Admin socket connected');
            testResults.adminConnection = true;
            resolve();
        });
        
        adminSocket.on('message', (data) => {
            try {
                const message = JSON.parse(data);
                console.log(`📦 Admin received: ${message.type}`);
                
                if (message.type === 'connection_data') {
                    testResults.connectionDataReceived = true;
                    testResults.connectionCount = message.data.length;
                    console.log(`📊 Connection count: ${message.data.length}`);
                    
                    if (message.data.length > 0) {
                        console.log('📋 Connections:');
                        message.data.forEach((conn, i) => {
                            console.log(`   ${i+1}. Session: ${conn.session_id}, User: ${conn.user_id}, Status: ${conn.status}`);
                        });
                    }
                }
            } catch (e) {
                console.error('❌ Error parsing admin message:', e);
            }
        });
        
        adminSocket.on('error', (error) => {
            console.error('❌ Admin socket error:', error.message);
            reject(error);
        });
        
        adminSocket.on('close', () => {
            console.log('🔌 Admin socket closed');
        });
        
        // Timeout after 10 seconds
        setTimeout(() => {
            if (!testResults.adminConnection) {
                reject(new Error('Admin connection timeout'));
            }
        }, 10000);
    });
}

// Test 2: Connect client socket
function connectClientSocket() {
    return new Promise((resolve, reject) => {
        console.log('\n📋 Test 2: Connecting client socket...');
        
        clientSocket = new WebSocket(CLIENT_WS_URL);
        
        clientSocket.on('open', () => {
            console.log('✅ Client socket connected');
            testResults.clientConnection = true;
            
            // Send identification message
            const identMessage = {
                type: 'chat_message',
                content: {
                    message: 'Hello from test client',
                    user_profile_id: 'test-user-dashboard-fix'
                }
            };
            
            console.log('📤 Sending client identification...');
            clientSocket.send(JSON.stringify(identMessage));
            resolve();
        });
        
        clientSocket.on('message', (data) => {
            try {
                const message = JSON.parse(data);
                console.log(`📦 Client received: ${message.type}`);
            } catch (e) {
                console.error('❌ Error parsing client message:', e);
            }
        });
        
        clientSocket.on('error', (error) => {
            console.error('❌ Client socket error:', error.message);
            reject(error);
        });
        
        clientSocket.on('close', () => {
            console.log('🔌 Client socket closed');
        });
        
        // Timeout after 10 seconds
        setTimeout(() => {
            if (!testResults.clientConnection) {
                reject(new Error('Client connection timeout'));
            }
        }, 10000);
    });
}

// Test 3: Request connection data
function requestConnectionData() {
    return new Promise((resolve) => {
        console.log('\n📋 Test 3: Requesting connection data...');
        
        if (adminSocket && adminSocket.readyState === WebSocket.OPEN) {
            adminSocket.send(JSON.stringify({ type: 'get_connections' }));
            console.log('📤 Connection data request sent');
            
            // Wait for response
            setTimeout(() => {
                resolve();
            }, 3000);
        } else {
            console.error('❌ Admin socket not available');
            resolve();
        }
    });
}

// Cleanup function
function cleanup() {
    console.log('\n🧹 Cleaning up...');
    
    if (clientSocket) {
        clientSocket.close();
    }
    
    if (adminSocket) {
        adminSocket.close();
    }
}

// Main test function
async function runTest() {
    try {
        // Test 1: Admin connection
        await connectAdminSocket();
        
        // Wait a moment
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Test 2: Client connection
        await connectClientSocket();
        
        // Wait for connection to be registered
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Test 3: Request connection data
        await requestConnectionData();
        
        // Wait for final data
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Print results
        console.log('\n📊 Test Results:');
        console.log('================');
        console.log(`✅ Admin Connection: ${testResults.adminConnection ? 'PASS' : 'FAIL'}`);
        console.log(`✅ Client Connection: ${testResults.clientConnection ? 'PASS' : 'FAIL'}`);
        console.log(`✅ Connection Data Received: ${testResults.connectionDataReceived ? 'PASS' : 'FAIL'}`);
        console.log(`📊 Connection Count: ${testResults.connectionCount}`);
        
        const allPassed = testResults.adminConnection && 
                         testResults.clientConnection && 
                         testResults.connectionDataReceived;
        
        console.log(`\n🎯 Overall Result: ${allPassed ? '✅ PASS' : '❌ FAIL'}`);
        
        if (testResults.connectionCount > 0) {
            console.log('\n🎉 Dashboard fix appears to be working!');
            console.log('   - Connections are being tracked');
            console.log('   - Dashboard can retrieve connection data');
            console.log('   - No more "Loading..." or 0 connections issue');
        } else {
            console.log('\n⚠️  Dashboard may still have issues:');
            console.log('   - Connection tracking might not be working');
            console.log('   - Check backend logs for errors');
        }
        
    } catch (error) {
        console.error('\n❌ Test failed:', error.message);
    } finally {
        cleanup();
        process.exit(0);
    }
}

// Handle process termination
process.on('SIGINT', () => {
    console.log('\n⏹️  Test interrupted');
    cleanup();
    process.exit(0);
});

// Run the test
runTest();
