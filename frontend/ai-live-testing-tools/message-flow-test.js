#!/usr/bin/env node

/**
 * Message Flow Test
 * 
 * Tests the actual message flow between game client and admin dashboard
 */

import WebSocket from 'ws';

const CONFIG = {
  user: { id: '2', name: '<PERSON><PERSON><PERSON>' }
};

class MessageFlowTest {
  constructor() {
    this.gameSocket = null;
    this.adminSocket = null;
    this.messageFlowEvents = [];
    this.connectionEvents = [];
    this.adminConnected = false;
    this.gameConnected = false;
    this.messageMonitoringActive = false;
  }

  async runTest() {
    console.log('🔍 MESSAGE FLOW TEST');
    console.log('===================\n');

    try {
      // Step 1: Connect admin dashboard first
      await this.connectAdmin();
      
      // Step 2: Enable message monitoring
      await this.enableMessageMonitoring();
      
      // Step 3: Connect game client
      await this.connectGame();
      
      // Step 4: Send test messages
      await this.sendTestMessages();
      
      // Step 5: Analyze results
      this.analyzeResults();
      
    } catch (error) {
      console.error('❌ Test failed:', error.message);
    } finally {
      this.cleanup();
    }
  }

  async connectAdmin() {
    console.log('📋 Step 1: Connecting Admin Dashboard');
    console.log('------------------------------------');

    return new Promise((resolve, reject) => {
      this.adminSocket = new WebSocket('ws://localhost:8000/ws/connection-monitor/');

      this.adminSocket.on('open', () => {
        console.log('✅ Admin dashboard connected');
        this.adminConnected = true;
        resolve();
      });

      this.adminSocket.on('message', (data) => {
        this.handleAdminMessage(data);
      });

      this.adminSocket.on('error', (error) => {
        console.log('❌ Admin connection error:', error.message);
        reject(error);
      });

      setTimeout(() => {
        if (!this.adminConnected) {
          reject(new Error('Admin connection timeout'));
        }
      }, 10000);
    });
  }

  async enableMessageMonitoring() {
    console.log('\n📋 Step 2: Enabling Message Monitoring');
    console.log('--------------------------------------');

    if (!this.adminSocket || this.adminSocket.readyState !== WebSocket.OPEN) {
      throw new Error('Admin socket not connected');
    }

    // Send start message monitoring request
    this.adminSocket.send(JSON.stringify({ type: 'start_message_monitoring' }));
    console.log('📤 Sent start_message_monitoring request');

    // Wait for confirmation
    await new Promise(resolve => setTimeout(resolve, 2000));

    if (this.messageMonitoringActive) {
      console.log('✅ Message monitoring activated');
    } else {
      console.log('⚠️ Message monitoring activation not confirmed');
    }
  }

  async connectGame() {
    console.log('\n📋 Step 3: Connecting Game Client');
    console.log('---------------------------------');

    return new Promise((resolve, reject) => {
      this.gameSocket = new WebSocket('ws://localhost:8000/ws/game/');

      this.gameSocket.on('open', () => {
        console.log('✅ Game client connected');
        this.gameConnected = true;
        resolve();
      });

      this.gameSocket.on('message', (data) => {
        try {
          const message = JSON.parse(data);
          console.log(`📨 Game received: ${message.type}`);
        } catch (error) {
          console.log('📨 Game received raw data');
        }
      });

      this.gameSocket.on('error', (error) => {
        console.log('❌ Game connection error:', error.message);
        reject(error);
      });

      setTimeout(() => {
        if (!this.gameConnected) {
          reject(new Error('Game connection timeout'));
        }
      }, 10000);
    });
  }

  async sendTestMessages() {
    console.log('\n📋 Step 4: Sending Test Messages');
    console.log('--------------------------------');

    if (!this.gameSocket || this.gameSocket.readyState !== WebSocket.OPEN) {
      throw new Error('Game socket not connected');
    }

    const messages = [
      {
        type: 'chat_message',
        content: {
          message: 'Test message 1 - checking message flow',
          user_profile_id: CONFIG.user.id,
          timestamp: new Date().toISOString()
        }
      },
      {
        type: 'chat_message',
        content: {
          message: 'Test message 2 - verifying dashboard updates',
          user_profile_id: CONFIG.user.id,
          timestamp: new Date().toISOString()
        }
      }
    ];

    for (let i = 0; i < messages.length; i++) {
      const message = messages[i];
      console.log(`📤 Sending message ${i + 1}: "${message.content.message}"`);
      
      this.gameSocket.send(JSON.stringify(message));
      
      // Wait between messages
      await new Promise(resolve => setTimeout(resolve, 3000));
    }

    // Wait for final processing
    console.log('⏳ Waiting for message processing...');
    await new Promise(resolve => setTimeout(resolve, 5000));
  }

  handleAdminMessage(data) {
    try {
      const message = JSON.parse(data);
      
      switch (message.type) {
        case 'message_monitoring_started':
          console.log('✅ Message monitoring confirmed active');
          this.messageMonitoringActive = true;
          break;
          
        case 'message_flow':
          this.messageFlowEvents.push(message);
          const flowData = message.data;
          console.log(`📊 Message flow: ${flowData.direction} - ${flowData.message?.type || 'unknown'}`);
          if (flowData.message?.content?.message) {
            console.log(`   Content: "${flowData.message.content.message.substring(0, 50)}..."`);
          }
          break;
          
        case 'connection_data':
          this.connectionEvents.push(message);
          console.log(`📊 Connection update: ${message.data.length} connections`);
          break;
          
        case 'message_stats':
          console.log(`📊 Message stats: ${message.data.total_messages_today} total today`);
          break;
          
        default:
          console.log(`📨 Admin received: ${message.type}`);
      }
    } catch (error) {
      console.log('❌ Failed to parse admin message:', error.message);
    }
  }

  analyzeResults() {
    console.log('\n📊 ANALYSIS RESULTS');
    console.log('==================');

    console.log(`📈 Message Flow Events: ${this.messageFlowEvents.length}`);
    console.log(`📈 Connection Events: ${this.connectionEvents.length}`);
    console.log(`📈 Message Monitoring Active: ${this.messageMonitoringActive ? 'Yes' : 'No'}`);

    if (this.messageFlowEvents.length > 0) {
      console.log('\n✅ SUCCESS: Message flow is working!');
      console.log('\n📋 Message Flow Details:');
      this.messageFlowEvents.forEach((event, index) => {
        const data = event.data;
        const messageType = data.message?.type || 'unknown';
        const direction = data.direction || 'unknown';
        const sessionId = data.session_id?.substring(0, 8) || 'unknown';
        console.log(`   ${index + 1}. ${direction} ${messageType} (session: ${sessionId}...)`);
        
        if (data.message?.content?.message) {
          console.log(`      Content: "${data.message.content.message}"`);
        }
      });
      
      console.log('\n🎯 CONCLUSION:');
      console.log('The dashboard message flow is working correctly!');
      console.log('Chat messages should now appear in the admin dashboard.');
      
    } else {
      console.log('\n❌ ISSUE: No message flow events received');
      console.log('\n🔍 TROUBLESHOOTING:');
      console.log('1. Check if UserSessionConsumer is calling broadcast_message_flow()');
      console.log('2. Verify ConnectionMonitorConsumer._active_consumers list');
      console.log('3. Check if message_monitoring_enabled flag is set correctly');
      
      if (this.messageMonitoringActive) {
        console.log('4. Message monitoring is active, so the issue is in the broadcast logic');
      } else {
        console.log('4. Message monitoring is not active, check start_message_monitoring handler');
      }
    }

    if (this.connectionEvents.length > 0) {
      console.log('\n📊 Connection Tracking:');
      const latest = this.connectionEvents[this.connectionEvents.length - 1];
      console.log(`   Latest connection count: ${latest.data.length}`);
      if (latest.data.length > 0) {
        const conn = latest.data[0];
        console.log(`   Session: ${conn.session_id?.substring(0, 8)}...`);
        console.log(`   User: ${conn.user_id}`);
        console.log(`   Messages: ${conn.message_count}`);
      }
    }
  }

  cleanup() {
    console.log('\n🧹 Cleaning up...');
    
    if (this.gameSocket) {
      this.gameSocket.close();
    }
    
    if (this.adminSocket) {
      this.adminSocket.close();
    }
    
    console.log('✅ Cleanup completed');
  }
}

// Run the test
const tester = new MessageFlowTest();
tester.runTest().then(() => {
  process.exit(0);
}).catch((error) => {
  console.error('❌ Test failed:', error);
  process.exit(1);
});

// Auto-exit after 60 seconds
setTimeout(() => {
  console.log('\n⏰ Test timeout - exiting');
  process.exit(1);
}, 60000);
