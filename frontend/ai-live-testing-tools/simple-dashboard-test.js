#!/usr/bin/env node

/**
 * Simple Dashboard Test
 * 
 * Quick test to verify the dashboard is receiving data
 */

import WebSocket from 'ws';

console.log('🔍 Simple Dashboard Test');
console.log('========================\n');

// Test admin WebSocket connection
console.log('🔌 Testing admin WebSocket...');
const adminSocket = new WebSocket('ws://localhost:8000/ws/connection-monitor/');

adminSocket.on('open', () => {
  console.log('✅ Admin WebSocket connected');
  
  // Request data
  console.log('📤 Requesting connection data...');
  adminSocket.send(JSON.stringify({ type: 'get_connections' }));
  
  console.log('📤 Requesting system health...');
  adminSocket.send(JSON.stringify({ type: 'get_system_health' }));
  
  console.log('📤 Requesting message stats...');
  adminSocket.send(JSON.stringify({ type: 'get_message_stats' }));
});

adminSocket.on('message', (data) => {
  try {
    const message = JSON.parse(data);
    console.log(`📨 Received: ${message.type}`);
    console.log('   Data:', JSON.stringify(message.data, null, 2));
    console.log('');
  } catch (error) {
    console.log('❌ Failed to parse message:', data.toString());
  }
});

adminSocket.on('error', (error) => {
  console.log('❌ Admin WebSocket error:', error.message);
});

adminSocket.on('close', (code, reason) => {
  console.log(`🔌 Admin WebSocket closed: ${code} ${reason}`);
  process.exit(0);
});

// Create a test client
console.log('🔗 Creating test client...');
const clientSocket = new WebSocket('ws://localhost:8000/ws/game/');

clientSocket.on('open', () => {
  console.log('✅ Client WebSocket connected');
  
  // Send test message with real user ID
  const message = {
    type: 'chat_message',
    content: {
      message: 'Test message for dashboard',
      user_profile_id: '2', // Use real user ID (PhiPhi - fake user for testing)
      timestamp: new Date().toISOString()
    }
  };
  
  clientSocket.send(JSON.stringify(message));
  console.log('📤 Sent test message from client');
});

clientSocket.on('message', (data) => {
  try {
    const message = JSON.parse(data);
    console.log(`📨 Client received: ${message.type}`);
  } catch (error) {
    console.log('❌ Failed to parse client message:', data.toString());
  }
});

clientSocket.on('error', (error) => {
  console.log('❌ Client WebSocket error:', error.message);
});

// Auto-exit after 10 seconds
setTimeout(() => {
  console.log('⏰ Test timeout, closing connections...');
  adminSocket.close();
  clientSocket.close();
}, 10000);
