#!/usr/bin/env node

/**
 * Debug Console Check - Check browser console for errors
 */

const puppeteer = require('puppeteer');

async function checkConsole() {
    console.log('🔍 Checking browser console for errors...\n');
    
    const browser = await puppeteer.launch({
        headless: false,
        defaultViewport: { width: 1200, height: 800 }
    });

    try {
        // Test Debug Environment
        console.log('📋 Debug Environment Console Check (http://localhost:3004/)...');
        const debugPage = await browser.newPage();
        
        // Capture console messages
        debugPage.on('console', msg => {
            const type = msg.type();
            const text = msg.text();
            if (type === 'error') {
                console.log(`   ❌ ERROR: ${text}`);
            } else if (type === 'warning') {
                console.log(`   ⚠️  WARN: ${text}`);
            } else if (text.includes('✅') || text.includes('❌') || text.includes('WHEEL')) {
                console.log(`   📝 LOG: ${text}`);
            }
        });

        // Capture page errors
        debugPage.on('pageerror', error => {
            console.log(`   💥 PAGE ERROR: ${error.message}`);
        });

        await debugPage.goto('http://localhost:3004/', { waitUntil: 'networkidle0' });
        
        // Wait a bit for any async loading
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // Check what elements actually exist
        const elements = await debugPage.evaluate(() => {
            return {
                debugWheel: !!document.getElementById('debugWheel'),
                loadBtn: !!document.getElementById('loadWheelBtn'),
                gameWheelElements: document.querySelectorAll('game-wheel').length,
                customElementsDefined: !!window.customElements.get('game-wheel'),
                bodyContent: document.body.innerHTML.length
            };
        });
        
        console.log('   📊 Element Check:', elements);

        // Test Production App
        console.log('\n📋 Production App Console Check (http://localhost:3001/)...');
        const prodPage = await browser.newPage();
        
        // Capture console messages
        prodPage.on('console', msg => {
            const type = msg.type();
            const text = msg.text();
            if (type === 'error') {
                console.log(`   ❌ ERROR: ${text}`);
            } else if (type === 'warning') {
                console.log(`   ⚠️  WARN: ${text}`);
            } else if (text.includes('✅') || text.includes('❌') || text.includes('WHEEL') || text.includes('Demo mode')) {
                console.log(`   📝 LOG: ${text}`);
            }
        });

        // Capture page errors
        prodPage.on('pageerror', error => {
            console.log(`   💥 PAGE ERROR: ${error.message}`);
        });

        await prodPage.goto('http://localhost:3001/', { waitUntil: 'networkidle0' });
        
        // Wait for app initialization
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        // Check what elements actually exist
        const prodElements = await prodPage.evaluate(() => {
            return {
                appShell: !!document.querySelector('app-shell'),
                gameWheel: !!document.querySelector('game-wheel'),
                wheelPlaceholder: !!document.querySelector('.wheel-placeholder'),
                spinButton: !!document.querySelector('.spin-button'),
                wheelData: document.querySelector('game-wheel')?.wheelData ? 'present' : 'missing',
                customElementsDefined: !!window.customElements.get('game-wheel')
            };
        });
        
        console.log('   📊 Element Check:', prodElements);

        console.log('\n🎯 ANALYSIS:');
        console.log('Check the console output above for any errors or warnings');
        console.log('Browser windows are left open for manual inspection');

    } catch (error) {
        console.error('❌ Console check error:', error.message);
    }
    
    // Don't close browser for manual inspection
    console.log('\n📝 Browser windows left open for manual inspection');
}

checkConsole().catch(console.error);
