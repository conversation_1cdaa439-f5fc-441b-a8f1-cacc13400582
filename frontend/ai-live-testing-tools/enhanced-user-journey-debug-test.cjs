#!/usr/bin/env node

/**
 * Enhanced User Journey Debug Test
 * 
 * This test reproduces a new user scenario with ~25% profile completion
 * and validates that the system behaves correctly:
 * - Asks for more information when profile is incomplete
 * - <PERSON>les duplicate messages properly
 * - Shows proper content in chat (not "No content")
 * - Provides good debugging experience
 */

const { chromium } = require('playwright');

class EnhancedUserJourneyDebugTest {
  constructor() {
    this.browser = null;
    this.page = null;
    this.startTime = Date.now();
    this.messages = [];
    this.duplicates = [];
    this.debugInfo = [];
    this.userDetails = null;
    this.testResults = {
      profileCompletionCheck: false,
      duplicateMessages: false,
      noContentMessages: false,
      debugPanelWorking: false,
      systemAsksForMoreInfo: false,
      responseTime: 0,
      wheelGenerationBlocked: false
    };
  }

  async run() {
    console.log('🚀 Starting Enhanced User Journey Debug Test');
    console.log('════════════════════════════════════════════════════════════');
    
    try {
      await this.setup();
      await this.testNewUserScenario();
      await this.testDuplicateMessages();
      await this.testDebugPanel();
      await this.testProfileCompletionWorkflow();
      await this.generateReport();
    } catch (error) {
      console.error('❌ Test failed:', error);
      throw error;
    } finally {
      await this.cleanup();
    }
  }

  async setup() {
    console.log('🔧 Setting up test environment...');
    
    this.browser = await chromium.launch({ 
      headless: false,
      slowMo: 100,
      args: ['--disable-web-security', '--disable-features=VizDisplayCompositor']
    });
    
    this.page = await this.browser.newPage();
    
    // Enable console logging
    this.page.on('console', msg => {
      const text = msg.text();
      if (text.includes('duplicate') || text.includes('No content') || text.includes('DEBUG')) {
        console.log(`🖥️  Console: ${text}`);
      }
    });
    
    // Monitor WebSocket messages
    this.page.on('websocket', ws => {
      console.log('🔌 WebSocket connection established');
      
      ws.on('framereceived', event => {
        try {
          const message = JSON.parse(event.payload);
          this.analyzeMessage(message, 'received');
        } catch (e) {
          // Not JSON, ignore
        }
      });
      
      ws.on('framesent', event => {
        try {
          const message = JSON.parse(event.payload);
          this.analyzeMessage(message, 'sent');
        } catch (e) {
          // Not JSON, ignore
        }
      });
    });
    
    console.log('✅ Test environment ready');
  }

  async testNewUserScenario() {
    console.log('\n📋 Testing New User Scenario (Real User Journey)');
    console.log('────────────────────────────────────────────────────────────');

    // Navigate to the app
    await this.page.goto('http://localhost:3001');
    await this.page.waitForTimeout(2000);

    // Open debug panel with Ctrl+Shift+D
    await this.page.keyboard.press('Control+Shift+D');
    await this.page.waitForTimeout(1000);

    // Check if debug panel is visible
    const debugPanel = await this.page.$('debug-panel');
    if (debugPanel) {
      console.log('✅ Debug panel opened successfully');
      this.testResults.debugPanelWorking = true;

      // First, check current user details
      await this.extractUserDetails();

      // If we need a user with low profile completion, let's use user 2 directly
      console.log('🔄 Switching to user 2 (should have 0% profile completion)...');

      // Look for user selection or input field
      const userIdInput = await this.page.$('input[placeholder*="user"], input[placeholder*="User"], #user-id-input');
      if (userIdInput) {
        await userIdInput.clear();
        await userIdInput.type('2');

        // Look for apply/set button
        const applyBtn = await this.page.$('button:has-text("Apply"), button:has-text("Set"), button:has-text("Switch")');
        if (applyBtn) {
          await applyBtn.click();
          await this.page.waitForTimeout(2000);
          console.log('✅ Switched to user 2');

          // Re-extract user details after switch
          await this.extractUserDetails();
        }
      }
    } else {
      console.log('❌ Debug panel not found - continuing with default user');
    }

    // Test basic user interaction
    await this.testBasicUserInteraction();
  }

  async extractUserDetails() {
    try {
      // Try multiple selectors for user details
      const selectors = ['.user-details', '[class*="user-detail"]', '[class*="debug-info"]', '.debug-panel-content'];
      let userDetailsText = null;

      for (const selector of selectors) {
        try {
          userDetailsText = await this.page.textContent(selector);
          if (userDetailsText && userDetailsText.trim()) {
            console.log(`📊 User Details (from ${selector}):`, userDetailsText.trim());
            break;
          }
        } catch (e) {
          // Try next selector
        }
      }

      if (userDetailsText) {
        // Extract user ID
        const userIdMatch = userDetailsText.match(/User ID:\s*(\d+)/i) || userDetailsText.match(/user[_\s]*id[:\s]*(\d+)/i);
        if (userIdMatch) {
          const userId = userIdMatch[1];
          console.log(`👤 Current User ID: ${userId}`);
          this.userDetails = { userId };
        }

        // Extract profile completion percentage
        const completionMatch = userDetailsText.match(/Profile Completion:\s*(\d+(?:\.\d+)?)%/i) ||
                               userDetailsText.match(/completion[:\s]*(\d+(?:\.\d+)?)%/i);
        if (completionMatch) {
          const completion = parseFloat(completionMatch[1]);
          console.log(`📈 Profile Completion: ${completion}%`);

          if (this.userDetails) {
            this.userDetails.profileCompletion = completion;
          }

          if (completion < 50) {
            console.log('✅ Profile completion is below 50% - system should ask for more info');
            this.testResults.profileCompletionCheck = true;
          } else {
            console.log('⚠️  Profile completion is above 50% - may not trigger onboarding');
          }
        } else {
          console.log('⚠️  Could not extract profile completion percentage');
        }
      } else {
        console.log('⚠️  No user details found in debug panel');

        // Try to get user info from page title or other elements
        const pageTitle = await this.page.title();
        console.log(`📄 Page title: ${pageTitle}`);
      }
    } catch (error) {
      console.log('⚠️  Could not extract user details:', error.message);
    }
  }

  async testBasicUserInteraction() {
    console.log('\n💬 Testing Basic User Interaction (Real Journey)');
    console.log('────────────────────────────────────────────────────────────');

    // Clear previous messages
    this.messages = [];
    this.duplicates = [];

    // Find chat input - try multiple selectors
    const inputSelectors = [
      'input[type="text"]',
      'textarea',
      '[placeholder*="message"]',
      '[placeholder*="type"]',
      '.chat-input input',
      '.message-input',
      '#chat-input'
    ];

    let chatInput = null;
    for (const selector of inputSelectors) {
      try {
        chatInput = await this.page.$(selector);
        if (chatInput) {
          console.log(`✅ Found chat input with selector: ${selector}`);
          break;
        }
      } catch (e) {
        // Try next selector
      }
    }

    if (!chatInput) {
      console.log('❌ Chat input not found - trying to click on chat area');
      // Try to click on a chat area to focus it
      const chatArea = await this.page.$('.chat, .conversation, .messages, main');
      if (chatArea) {
        await chatArea.click();
        await this.page.waitForTimeout(500);
        // Try again
        chatInput = await this.page.$('input[type="text"], textarea');
      }
    }

    if (!chatInput) {
      console.log('❌ Still no chat input found - listing all inputs');
      const allInputs = await this.page.$$eval('input, textarea', elements =>
        elements.map(el => ({
          type: el.type,
          placeholder: el.placeholder,
          id: el.id,
          className: el.className,
          visible: el.offsetParent !== null
        }))
      );
      console.log('Available inputs:', allInputs);
      return;
    }

    // Send a simple message that should trigger profile completion check
    const testMessage = "Hi! I'm feeling a bit overwhelmed and could use some help.";
    console.log(`📝 Sending message: "${testMessage}"`);

    const startTime = Date.now();
    await chatInput.fill(testMessage);
    await this.page.keyboard.press('Enter');

    // Wait for response with progress updates
    console.log('⏳ Waiting for response...');
    for (let i = 0; i < 15; i++) {
      await this.page.waitForTimeout(1000);
      console.log(`   ${i + 1}s elapsed...`);

      // Check if we got a response
      const messages = await this.page.$$('.chat-message, [class*="message"]');
      if (messages.length > 1) { // More than just the user message
        break;
      }
    }

    const responseTime = Date.now() - startTime;
    this.testResults.responseTime = responseTime;

    console.log(`⏱️  Total response time: ${responseTime}ms`);

    // Check chat messages
    await this.analyzeChatMessages();
  }

  async analyzeChatMessages() {
    try {
      const chatMessages = await this.page.$$eval('.chat-message, [class*="message"], [class*="chat"]', 
        elements => elements.map(el => ({
          text: el.textContent?.trim() || '',
          isUser: el.classList.contains('user') || el.classList.contains('is-user'),
          classes: el.className
        }))
      );
      
      console.log(`📨 Found ${chatMessages.length} chat messages:`);
      
      let hasNoContentMessages = false;
      let hasSystemResponse = false;
      
      chatMessages.forEach((msg, index) => {
        console.log(`  ${index + 1}. ${msg.isUser ? '👤' : '🤖'} ${msg.text}`);
        
        if (msg.text.toLowerCase().includes('no content')) {
          hasNoContentMessages = true;
          console.log('    ⚠️  "No content" message detected!');
        }
        
        if (!msg.isUser && msg.text.length > 10) {
          hasSystemResponse = true;
          
          // Check if system asks for more information
          if (msg.text.toLowerCase().includes('tell me more') || 
              msg.text.toLowerCase().includes('more information') ||
              msg.text.toLowerCase().includes('help me understand')) {
            this.testResults.systemAsksForMoreInfo = true;
            console.log('    ✅ System asks for more information');
          }
        }
      });
      
      this.testResults.noContentMessages = hasNoContentMessages;
      
      if (!hasSystemResponse) {
        console.log('❌ No system response received');
      }
      
    } catch (error) {
      console.log('⚠️  Could not analyze chat messages:', error.message);
    }
  }

  analyzeMessage(message, direction) {
    const timestamp = Date.now();
    const messageData = {
      timestamp,
      direction,
      content: message,
      type: message.type || 'unknown'
    };
    
    this.messages.push(messageData);
    
    // Check for duplicates
    if (direction === 'received') {
      this.checkForDuplicate(messageData);
    }
    
    // Log important messages
    const elapsed = ((timestamp - this.startTime) / 1000).toFixed(1);
    if (message.type === 'chat_message' || message.type === 'error' || message.type === 'debug_info') {
      console.log(`[${elapsed}s] ${direction === 'received' ? '📨' : '📤'} ${message.type}: ${JSON.stringify(message).substring(0, 100)}...`);
    }
  }

  checkForDuplicate(messageData) {
    const content = JSON.stringify(messageData.content);
    const isDuplicate = this.messages.some(prev => 
      prev.direction === 'received' && 
      JSON.stringify(prev.content) === content &&
      (messageData.timestamp - prev.timestamp) < 5000 // Within 5 seconds
    );
    
    if (isDuplicate) {
      console.log('🚨 DUPLICATE MESSAGE DETECTED!');
      this.testResults.duplicateMessages = true;
      this.duplicates.push(messageData);
    }
  }

  async testDuplicateMessages() {
    console.log('\n🔍 Testing for Duplicate Messages');
    console.log('────────────────────────────────────────────────────────────');
    
    // Send another message to test for duplicates
    const chatInput = await this.page.$('input[type="text"], textarea');
    if (chatInput) {
      await chatInput.fill("Can you help me with activities?");
      await this.page.keyboard.press('Enter');
      await this.page.waitForTimeout(5000);
    }
    
    if (this.testResults.duplicateMessages) {
      console.log(`❌ Found ${this.duplicates.length} duplicate messages`);
    } else {
      console.log('✅ No duplicate messages detected');
    }
  }

  async testDebugPanel() {
    console.log('\n🐛 Testing Debug Panel Features');
    console.log('────────────────────────────────────────────────────────────');
    
    // Test debug panel visibility and functionality
    const debugPanel = await this.page.$('debug-panel');
    if (debugPanel) {
      // Check connection status
      const connectionStatus = await this.page.textContent('.status-indicator');
      console.log(`🔌 Connection status indicator found: ${connectionStatus ? 'Yes' : 'No'}`);
      
      // Check user details display
      const userDetails = await this.page.$('.user-details');
      console.log(`👤 User details displayed: ${userDetails ? 'Yes' : 'No'}`);
      
      // Check error display
      const errorDisplay = await this.page.$('[style*="color: #ff6b6b"]');
      console.log(`⚠️  Error display available: ${errorDisplay ? 'Yes' : 'No'}`);
      
      this.testResults.debugPanelWorking = true;
    }
  }

  async testProfileCompletionWorkflow() {
    console.log('\n📊 Testing Profile Completion Workflow');
    console.log('────────────────────────────────────────────────────────────');
    
    // Try to request a wheel directly
    const chatInput = await this.page.$('input[type="text"], textarea');
    if (chatInput) {
      console.log('🎯 Requesting wheel generation directly...');
      await chatInput.fill("Generate a wheel for me");
      await this.page.keyboard.press('Enter');
      await this.page.waitForTimeout(8000);
      
      // Check if wheel was generated or if system asked for more info
      const wheelElement = await this.page.$('.wheel, [class*="wheel"], canvas');
      if (wheelElement) {
        console.log('⚠️  Wheel was generated despite low profile completion');
        this.testResults.wheelGenerationBlocked = false;
      } else {
        console.log('✅ Wheel generation was blocked - system should ask for more info');
        this.testResults.wheelGenerationBlocked = true;
      }
    }
  }

  async generateReport() {
    console.log('\n📋 TEST RESULTS SUMMARY');
    console.log('════════════════════════════════════════════════════════════');
    
    const results = this.testResults;
    
    console.log(`✅ Debug Panel Working: ${results.debugPanelWorking ? 'PASS' : 'FAIL'}`);
    console.log(`✅ Profile Completion Check: ${results.profileCompletionCheck ? 'PASS' : 'FAIL'}`);
    console.log(`✅ System Asks for More Info: ${results.systemAsksForMoreInfo ? 'PASS' : 'FAIL'}`);
    console.log(`✅ Wheel Generation Blocked: ${results.wheelGenerationBlocked ? 'PASS' : 'FAIL'}`);
    console.log(`❌ Duplicate Messages: ${results.duplicateMessages ? 'DETECTED' : 'NONE'}`);
    console.log(`❌ "No Content" Messages: ${results.noContentMessages ? 'DETECTED' : 'NONE'}`);
    console.log(`⏱️  Response Time: ${results.responseTime}ms`);
    
    console.log(`\n📊 Messages Analyzed: ${this.messages.length}`);
    console.log(`🚨 Duplicates Found: ${this.duplicates.length}`);
    
    // Overall assessment
    const criticalIssues = [
      results.duplicateMessages,
      results.noContentMessages,
      !results.debugPanelWorking
    ].filter(Boolean).length;
    
    const positiveResults = [
      results.profileCompletionCheck,
      results.systemAsksForMoreInfo,
      results.wheelGenerationBlocked
    ].filter(Boolean).length;
    
    console.log('\n🎯 OVERALL ASSESSMENT:');
    if (criticalIssues === 0 && positiveResults >= 2) {
      console.log('✅ EXCELLENT - System working as expected');
    } else if (criticalIssues <= 1 && positiveResults >= 1) {
      console.log('⚠️  GOOD - Minor issues to address');
    } else {
      console.log('❌ NEEDS WORK - Critical issues detected');
    }
    
    console.log('\n🔧 RECOMMENDATIONS:');
    if (results.duplicateMessages) {
      console.log('- Fix duplicate message handling in WebSocket consumer or frontend');
    }
    if (results.noContentMessages) {
      console.log('- Fix "No content" display issue in conversation_dispatcher messages');
    }
    if (!results.systemAsksForMoreInfo && results.profileCompletionCheck) {
      console.log('- Ensure system asks for more information when profile is incomplete');
    }
    if (results.responseTime > 10000) {
      console.log('- Optimize response time (currently > 10 seconds)');
    }
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
    }
  }
}

// Run the test
if (require.main === module) {
  const test = new EnhancedUserJourneyDebugTest();
  test.run().catch(console.error);
}

module.exports = EnhancedUserJourneyDebugTest;
