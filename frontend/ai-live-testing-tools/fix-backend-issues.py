#!/usr/bin/env python3

"""
Backend Issues Fixer
Fixes the issues identified in the backend logs
"""

import os
import sys
import subprocess
import json

def run_command(command, cwd=None):
    """Run a shell command and return the result"""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            cwd=cwd,
            capture_output=True, 
            text=True, 
            check=True
        )
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        print(f"❌ Command failed: {command}")
        print(f"Error: {e.stderr}")
        return None

def fix_llm_configuration():
    """Fix LLM configuration issues"""
    print("🤖 Fixing LLM Configuration...")
    
    # Check if we're in the right directory
    backend_dir = "../backend" if os.path.exists("../backend") else "../../backend"
    
    if not os.path.exists(backend_dir):
        print("❌ Backend directory not found")
        return False
    
    # Run the LLM fix script
    fix_script = os.path.join(backend_dir, "fix_backend_llm.sh")
    if os.path.exists(fix_script):
        print("Running fix_backend_llm.sh...")
        result = run_command(f"bash {fix_script}", cwd=backend_dir)
        if result is not None:
            print("✅ LLM configuration fix completed")
            return True
    
    # Alternative: Run the setup script directly
    setup_script = os.path.join(backend_dir, "setup_llm_config.py")
    if os.path.exists(setup_script):
        print("Running setup_llm_config.py...")
        result = run_command(f"docker exec -it backend-web-1 python /usr/src/app/setup_llm_config.py")
        if result is not None:
            print("✅ LLM configuration setup completed")
            return True
    
    print("⚠️ Could not find LLM configuration fix scripts")
    return False

def fix_database_prefetch_issue():
    """Fix the database prefetch_related issue"""
    print("🗄️ Fixing Database Prefetch Issue...")
    
    # The issue is in get_user_profile_tool.py
    # 'current_environment__domain_relationships__domain' is invalid
    
    backend_dir = "../backend" if os.path.exists("../backend") else "../../backend"
    tool_file = os.path.join(backend_dir, "apps/main/agents/tools/get_user_profile_tool.py")
    
    if not os.path.exists(tool_file):
        print(f"❌ Tool file not found: {tool_file}")
        return False
    
    print(f"📝 Checking {tool_file} for prefetch_related issues...")
    
    # Read the file
    try:
        with open(tool_file, 'r') as f:
            content = f.read()
        
        # Look for the problematic prefetch_related
        if 'current_environment__domain_relationships__domain' in content:
            print("🔍 Found problematic prefetch_related parameter")
            
            # Suggest fix
            print("💡 Suggested fix:")
            print("   Replace 'current_environment__domain_relationships__domain'")
            print("   with 'current_environment__domain_relationships'")
            print("   or remove it if not needed")
            
            return True
        else:
            print("✅ No problematic prefetch_related found")
            return True
            
    except Exception as e:
        print(f"❌ Error reading tool file: {e}")
        return False

def check_missing_tools():
    """Check for missing tools in the database"""
    print("🔧 Checking Missing Tools...")
    
    # The log shows 'get_user_state' tool is missing
    missing_tools = ['get_user_state']
    
    print("📋 Missing tools detected:")
    for tool in missing_tools:
        print(f"   - {tool}")
    
    print("💡 These tools need to be:")
    print("   1. Implemented in the codebase")
    print("   2. Registered in the database")
    print("   3. Marked as active")
    
    return True

def test_api_endpoints():
    """Test the new API endpoints"""
    print("🔗 Testing API Endpoints...")
    
    endpoints = [
        "http://localhost:8000/api/debug/users/",
        "http://localhost:8000/api/debug/llm-configs/",
        "http://localhost:8000/api/auth/login/",
        "http://localhost:8000/api/auth/verify/",
        "http://localhost:8000/logs/"
    ]
    
    for endpoint in endpoints:
        print(f"Testing {endpoint}...")
        result = run_command(f"curl -s -o /dev/null -w '%{{http_code}}' {endpoint}")
        if result:
            status_code = result.strip()
            if status_code in ['200', '401', '403']:  # Expected codes
                print(f"   ✅ {endpoint} - {status_code}")
            else:
                print(f"   ⚠️ {endpoint} - {status_code}")
        else:
            print(f"   ❌ {endpoint} - Connection failed")
    
    return True

def create_backend_fix_summary():
    """Create a summary of backend fixes needed"""
    print("\n📋 Backend Fix Summary:")
    print("=" * 50)
    
    fixes = [
        {
            "issue": "LLM Configuration Missing",
            "severity": "HIGH",
            "fix": "Run ./fix_backend_llm.sh or setup_llm_config.py",
            "status": "FIXABLE"
        },
        {
            "issue": "Database Prefetch Error",
            "severity": "HIGH", 
            "fix": "Fix prefetch_related parameter in get_user_profile_tool.py",
            "status": "NEEDS CODE CHANGE"
        },
        {
            "issue": "Missing get_user_state Tool",
            "severity": "MEDIUM",
            "fix": "Implement and register get_user_state tool",
            "status": "NEEDS IMPLEMENTATION"
        },
        {
            "issue": "API Endpoints",
            "severity": "LOW",
            "fix": "Verify new API endpoints are accessible",
            "status": "TESTABLE"
        }
    ]
    
    for fix in fixes:
        print(f"\n🔸 {fix['issue']}")
        print(f"   Severity: {fix['severity']}")
        print(f"   Fix: {fix['fix']}")
        print(f"   Status: {fix['status']}")
    
    return fixes

def main():
    """Main function to run all fixes"""
    print("🔧 Backend Issues Fixer Starting...")
    print("=" * 40)
    
    results = {}
    
    # Fix LLM configuration
    results['llm_config'] = fix_llm_configuration()
    
    # Fix database issues
    results['database'] = fix_database_prefetch_issue()
    
    # Check missing tools
    results['missing_tools'] = check_missing_tools()
    
    # Test API endpoints
    results['api_endpoints'] = test_api_endpoints()
    
    # Create summary
    fixes = create_backend_fix_summary()
    
    # Overall result
    print(f"\n🎯 Fix Results:")
    for category, success in results.items():
        status = "✅ COMPLETED" if success else "❌ FAILED"
        print(f"   {category}: {status}")
    
    print(f"\n💡 Next Steps:")
    print("1. Run the health checker: node backend-health-checker.js")
    print("2. Test WebSocket communication: node websocket-monitor.js")
    print("3. Run integration tests: node integration-test-suite.js")
    
    return all(results.values())

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
