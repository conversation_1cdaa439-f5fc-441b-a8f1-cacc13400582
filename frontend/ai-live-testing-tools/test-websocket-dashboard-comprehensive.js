#!/usr/bin/env node

/**
 * Comprehensive WebSocket Dashboard Tester
 * Tests the actual dashboard functionality and identifies missing features
 */

import WebSocket from 'ws';
import { JSDOM } from 'jsdom';
import fs from 'fs';
import path from 'path';

class WebSocketDashboardComprehensiveTester {
  constructor() {
    this.errors = [];
    this.testResults = [];
    this.monitorSocket = null;
    this.gameSocket = null;
    this.receivedMessages = [];
    this.dashboardHtml = null;
    this.dom = null;
    this.window = null;
    this.document = null;
  }

  async runTests() {
    console.log('🔍 Comprehensive WebSocket Dashboard Testing');
    console.log('=============================================');
    
    try {
      await this.loadDashboardHTML();
      await this.testDashboardStructure();
      await this.testWebSocketConnections();
      await this.testMessageInspectorFunctionality();
      await this.testConnectionDetailsModal();
      await this.testMessageConformanceWithSpecs();
    } catch (error) {
      this.errors.push(`Test suite failed: ${error.message}`);
    } finally {
      if (this.monitorSocket) this.monitorSocket.close();
      if (this.gameSocket) this.gameSocket.close();
    }
    
    this.generateComprehensiveReport();
  }

  async loadDashboardHTML() {
    console.log('\n🔬 Test 1: Loading Dashboard HTML Structure');
    
    try {
      const dashboardPath = path.resolve('../../backend/apps/admin_tools/templates/admin_tools/connection_dashboard.html');
      this.dashboardHtml = fs.readFileSync(dashboardPath, 'utf8');
      
      // Create JSDOM environment with the actual dashboard HTML
      this.dom = new JSDOM(this.dashboardHtml, {
        url: 'http://localhost:8000/admin/admin_tools/connection_dashboard/',
        pretendToBeVisual: true,
        resources: 'usable',
        runScripts: 'dangerously'
      });
      
      this.window = this.dom.window;
      this.document = this.window.document;
      
      console.log('✅ PASSED: Dashboard HTML loaded successfully');
      this.testResults.push('Dashboard HTML loading: SUCCESS');
      
    } catch (error) {
      this.errors.push(`Failed to load dashboard HTML: ${error.message}`);
      console.log('❌ FAILED: Could not load dashboard HTML');
    }
  }

  async testDashboardStructure() {
    console.log('\n🔬 Test 2: Dashboard Structure and Components');
    
    try {
      // Test essential elements exist
      const essentialElements = [
        'connectionList',
        'messageInspector', 
        'messageFlow',
        'connectionModal',
        'connectionModalContent'
      ];
      
      let missingElements = [];
      essentialElements.forEach(id => {
        const element = this.document.getElementById(id);
        if (!element) {
          missingElements.push(id);
        } else {
          console.log(`✅ Found element: ${id}`);
        }
      });
      
      if (missingElements.length === 0) {
        console.log('✅ PASSED: All essential dashboard elements present');
        this.testResults.push('Dashboard structure: COMPLETE');
      } else {
        this.errors.push(`Missing dashboard elements: ${missingElements.join(', ')}`);
        console.log(`❌ FAILED: Missing elements: ${missingElements.join(', ')}`);
      }
      
      // Test JavaScript functions exist
      const essentialFunctions = [
        'toggleMessageInspector',
        'showConnectionDetails',
        'closeConnectionModal',
        'filterMessages',
        'clearMessageFlow'
      ];
      
      let missingFunctions = [];
      essentialFunctions.forEach(funcName => {
        if (typeof this.window[funcName] !== 'function') {
          missingFunctions.push(funcName);
        } else {
          console.log(`✅ Found function: ${funcName}`);
        }
      });
      
      if (missingFunctions.length === 0) {
        console.log('✅ PASSED: All essential JavaScript functions present');
      } else {
        this.errors.push(`Missing JavaScript functions: ${missingFunctions.join(', ')}`);
        console.log(`❌ FAILED: Missing functions: ${missingFunctions.join(', ')}`);
      }
      
    } catch (error) {
      this.errors.push(`Dashboard structure test failed: ${error.message}`);
      console.log('❌ FAILED: Exception occurred');
    }
  }

  async testWebSocketConnections() {
    console.log('\n🔬 Test 3: WebSocket Connection Capabilities');
    
    // Test connection monitor endpoint
    await this.testConnectionMonitorEndpoint();
    
    // Test game WebSocket endpoint (for message interception)
    await this.testGameWebSocketEndpoint();
  }

  async testConnectionMonitorEndpoint() {
    console.log('\n📡 Testing Connection Monitor Endpoint');
    
    return new Promise((resolve) => {
      try {
        const wsUrl = 'ws://localhost:8000/ws/connection-monitor/';
        console.log(`🔌 Connecting to: ${wsUrl}`);
        
        this.monitorSocket = new WebSocket(wsUrl);
        
        this.monitorSocket.on('open', () => {
          console.log('✅ PASSED: Connection monitor endpoint accessible');
          this.testResults.push('Connection monitor endpoint: ACCESSIBLE');
          
          // Test requesting different data types
          const requests = [
            {type: 'get_connections'},
            {type: 'get_system_health'},
            {type: 'get_message_stats'},
            {type: 'start_message_monitoring'},
            {type: 'get_connection_details', session_id: 'test-session-123'}
          ];
          
          requests.forEach(request => {
            console.log(`📤 Sending request: ${request.type}`);
            this.monitorSocket.send(JSON.stringify(request));
          });
          
          setTimeout(resolve, 3000);
        });

        this.monitorSocket.on('message', (data) => {
          try {
            const message = JSON.parse(data);
            this.receivedMessages.push(message);
            console.log(`📨 Monitor received: ${message.type}`);
            
            // Test specific response handling
            if (message.type === 'connection_details') {
              console.log('✅ PASSED: Connection details response received');
              this.testResults.push('Connection details: WORKING');
            }
            
          } catch (error) {
            this.errors.push(`Failed to parse monitor message: ${error.message}`);
          }
        });

        this.monitorSocket.on('error', (error) => {
          this.errors.push(`Connection monitor error: ${error.message}`);
          console.log('❌ FAILED: Connection monitor endpoint error');
          resolve();
        });

        setTimeout(() => {
          if (this.monitorSocket.readyState === WebSocket.CONNECTING) {
            this.errors.push('Connection monitor endpoint timeout');
            console.log('❌ FAILED: Connection timeout');
            resolve();
          }
        }, 5000);

      } catch (error) {
        this.errors.push(`Connection monitor test failed: ${error.message}`);
        console.log('❌ FAILED: Exception occurred');
        resolve();
      }
    });
  }

  async testGameWebSocketEndpoint() {
    console.log('\n🎮 Testing Game WebSocket Endpoint (for message interception)');
    
    return new Promise((resolve) => {
      try {
        const wsUrl = 'ws://localhost:8000/ws/game/';
        console.log(`🔌 Connecting to: ${wsUrl}`);
        
        this.gameSocket = new WebSocket(wsUrl);
        
        this.gameSocket.on('open', () => {
          console.log('✅ PASSED: Game WebSocket endpoint accessible');
          this.testResults.push('Game WebSocket endpoint: ACCESSIBLE');
          
          // Send a test message to generate traffic
          const testMessage = {
            type: 'chat_message',
            content: {
              message: 'Test message for dashboard monitoring',
              user_profile_id: '2',
              timestamp: new Date().toISOString()
            }
          };
          
          console.log('📤 Sending test message to generate traffic');
          this.gameSocket.send(JSON.stringify(testMessage));
          
          setTimeout(resolve, 2000);
        });

        this.gameSocket.on('message', (data) => {
          try {
            const message = JSON.parse(data);
            console.log(`📨 Game received: ${message.type}`);
            
            // This traffic should be visible in the dashboard
            if (message.type === 'debug_info') {
              console.log('✅ PASSED: Debug messages flowing (should appear in dashboard)');
            }
            
          } catch (error) {
            console.log('⚠️ Non-JSON message received (normal for some message types)');
          }
        });

        this.gameSocket.on('error', (error) => {
          this.errors.push(`Game WebSocket error: ${error.message}`);
          console.log('❌ FAILED: Game WebSocket endpoint error');
          resolve();
        });

        setTimeout(() => {
          if (this.gameSocket.readyState === WebSocket.CONNECTING) {
            this.errors.push('Game WebSocket endpoint timeout');
            console.log('❌ FAILED: Game WebSocket timeout');
            resolve();
          }
        }, 5000);

      } catch (error) {
        this.errors.push(`Game WebSocket test failed: ${error.message}`);
        console.log('❌ FAILED: Exception occurred');
        resolve();
      }
    });
  }

  async testMessageInspectorFunctionality() {
    console.log('\n🔬 Test 4: Message Inspector Functionality');
    
    try {
      // Test message inspector toggle
      if (typeof this.window.toggleMessageInspector === 'function') {
        console.log('✅ PASSED: Message inspector toggle function exists');
        
        // Test the function (simulate click)
        this.window.toggleMessageInspector();
        const inspector = this.document.getElementById('messageInspector');
        
        if (inspector && inspector.style.display !== 'none') {
          console.log('✅ PASSED: Message inspector can be shown');
          this.testResults.push('Message inspector toggle: WORKING');
        } else {
          this.errors.push('Message inspector toggle not working properly');
        }
      } else {
        this.errors.push('Message inspector toggle function missing');
      }
      
      // Test message filtering
      if (typeof this.window.filterMessages === 'function') {
        console.log('✅ PASSED: Message filtering function exists');
        this.testResults.push('Message filtering: AVAILABLE');
      } else {
        this.errors.push('Message filtering function missing');
      }
      
      // Test message clearing
      if (typeof this.window.clearMessageFlow === 'function') {
        console.log('✅ PASSED: Message clearing function exists');
        this.testResults.push('Message clearing: AVAILABLE');
      } else {
        this.errors.push('Message clearing function missing');
      }
      
    } catch (error) {
      this.errors.push(`Message inspector test failed: ${error.message}`);
      console.log('❌ FAILED: Exception occurred');
    }
  }

  async testConnectionDetailsModal() {
    console.log('\n🔬 Test 5: Connection Details Modal');
    
    try {
      // Test modal structure
      const modal = this.document.getElementById('connectionModal');
      const modalContent = this.document.getElementById('connectionModalContent');
      
      if (modal && modalContent) {
        console.log('✅ PASSED: Connection details modal structure exists');
        
        // Test showConnectionDetails function
        if (typeof this.window.showConnectionDetails === 'function') {
          console.log('✅ PASSED: showConnectionDetails function exists');
          
          // Test the function
          this.window.showConnectionDetails('test-session-123');
          
          // Check if modal is shown
          if (modal.classList.contains('show')) {
            console.log('✅ PASSED: Modal shows when connection clicked');
            this.testResults.push('Connection details modal: WORKING');
          } else {
            this.errors.push('Modal does not show when connection clicked');
            console.log('❌ FAILED: Modal not showing');
          }
          
        } else {
          this.errors.push('showConnectionDetails function missing');
          console.log('❌ FAILED: showConnectionDetails function missing');
        }
        
        // Test modal close function
        if (typeof this.window.closeConnectionModal === 'function') {
          console.log('✅ PASSED: closeConnectionModal function exists');
          
          this.window.closeConnectionModal();
          if (!modal.classList.contains('show')) {
            console.log('✅ PASSED: Modal closes correctly');
          } else {
            this.errors.push('Modal does not close properly');
          }
        } else {
          this.errors.push('closeConnectionModal function missing');
        }
        
      } else {
        this.errors.push('Connection details modal structure missing');
        console.log('❌ FAILED: Modal structure missing');
      }
      
    } catch (error) {
      this.errors.push(`Connection details modal test failed: ${error.message}`);
      console.log('❌ FAILED: Exception occurred');
    }
  }

  async testMessageConformanceWithSpecs() {
    console.log('\n🔬 Test 6: Message Conformance with Authoritative Specs');

    try {
      // Load message specifications
      const specsPath = path.resolve('../../tools/mock_server/MESSAGE_SPECIFICATIONS.md');
      let messageSpecs = '';

      try {
        messageSpecs = fs.readFileSync(specsPath, 'utf8');
        console.log('✅ PASSED: Message specifications loaded');

        // Parse specifications to extract message schemas
        const schemas = this.parseMessageSchemas(messageSpecs);
        console.log(`📋 Found ${Object.keys(schemas).length} message type schemas`);

      } catch (error) {
        this.errors.push('Could not load message specifications');
        console.log('❌ FAILED: Message specs not accessible');
        return;
      }
      
      // Test if dashboard can validate message conformance
      const testMessages = [
        {
          type: 'chat_message',
          content: 'Test message',
          is_user: false,
          timestamp: new Date().toISOString()
        },
        {
          type: 'debug_info',
          content: {
            source: 'MentorService',
            level: 'info',
            message: 'Test debug message',
            timestamp: new Date().toISOString()
          }
        },
        {
          type: 'wheel_data',
          wheel: {
            items: [
              { title: 'Test Activity', percentage: 25 }
            ]
          }
        }
      ];
      
      // Test message validation logic
      let validationWorking = true;
      testMessages.forEach((msg, index) => {
        try {
          // Test if dashboard can process these message types
          const messageSize = JSON.stringify(msg).length;
          const hasRequiredFields = msg.type && (msg.content || msg.wheel);
          
          if (!hasRequiredFields) {
            validationWorking = false;
            this.errors.push(`Test message ${index + 1} missing required fields`);
          } else {
            console.log(`✅ Test message ${index + 1} (${msg.type}): Valid structure`);
          }
          
        } catch (error) {
          validationWorking = false;
          this.errors.push(`Message validation failed for message ${index + 1}: ${error.message}`);
        }
      });
      
      if (validationWorking) {
        console.log('✅ PASSED: Message validation logic working');
        this.testResults.push('Message conformance validation: WORKING');
      } else {
        console.log('❌ FAILED: Message validation issues detected');
      }
      
    } catch (error) {
      this.errors.push(`Message conformance test failed: ${error.message}`);
      console.log('❌ FAILED: Exception occurred');
    }
  }

  parseMessageSchemas(specsContent) {
    const schemas = {};

    // Extract message type definitions from the specs
    const messageTypeRegex = /## (.+?)\n([\s\S]*?)(?=\n## |\n# |$)/g;
    let match;

    while ((match = messageTypeRegex.exec(specsContent)) !== null) {
      const messageType = match[1].toLowerCase().replace(/\s+/g, '_');
      const content = match[2];

      // Extract required fields
      const requiredFields = [];
      const fieldRegex = /- `(\w+)`: .+? \*\*required\*\*/gi;
      let fieldMatch;

      while ((fieldMatch = fieldRegex.exec(content)) !== null) {
        requiredFields.push(fieldMatch[1]);
      }

      schemas[messageType] = {
        requiredFields,
        content: content
      };
    }

    return schemas;
  }

  validateMessageAgainstSpecs(message, schemas) {
    const messageType = message.type;
    const schema = schemas[messageType];

    if (!schema) {
      return {
        valid: false,
        errors: [`Unknown message type: ${messageType}`]
      };
    }

    const errors = [];

    // Check required fields
    schema.requiredFields.forEach(field => {
      if (!(field in message)) {
        errors.push(`Missing required field: ${field}`);
      }
    });

    // Type-specific validation
    switch (messageType) {
      case 'chat_message':
        if (!message.content && !message.data?.content) {
          errors.push('chat_message must have content');
        }
        break;

      case 'wheel_data':
        if (!message.wheel?.items || !Array.isArray(message.wheel.items)) {
          errors.push('wheel_data must have wheel.items array');
        }
        break;

      case 'debug_info':
        if (!message.data?.message && !message.content?.message) {
          errors.push('debug_info must have message content');
        }
        break;
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  generateComprehensiveReport() {
    console.log('\n📊 COMPREHENSIVE WEBSOCKET DASHBOARD TEST REPORT');
    console.log('==================================================');
    
    console.log(`📨 Monitor messages received: ${this.receivedMessages.length}`);
    console.log(`✅ Tests passed: ${this.testResults.length}`);
    console.log(`❌ Errors found: ${this.errors.length}`);
    
    if (this.testResults.length > 0) {
      console.log('\n✅ PASSED TESTS:');
      this.testResults.forEach((result, index) => {
        console.log(`  ${index + 1}. ${result}`);
      });
    }
    
    if (this.errors.length > 0) {
      console.log('\n❌ ERRORS FOUND:');
      this.errors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error}`);
      });
    }

    if (this.receivedMessages.length > 0) {
      console.log('\n📨 RECEIVED MESSAGE TYPES:');
      const messageTypes = [...new Set(this.receivedMessages.map(m => m.type))];
      messageTypes.forEach(type => {
        const count = this.receivedMessages.filter(m => m.type === type).length;
        console.log(`  - ${type}: ${count} messages`);
      });
    }
    
    console.log('\n🎯 OVERALL ASSESSMENT:');
    if (this.errors.length === 0) {
      console.log('✅ WEBSOCKET DASHBOARD FULLY FUNCTIONAL - All features working!');
    } else if (this.errors.length <= 2) {
      console.log('⚠️ WEBSOCKET DASHBOARD MOSTLY FUNCTIONAL - Minor issues detected');
    } else {
      console.log('❌ WEBSOCKET DASHBOARD NEEDS ATTENTION - Multiple issues detected');
    }
    
    console.log('\n🔧 SPECIFIC ISSUES TO ADDRESS:');
    if (this.errors.some(e => e.includes('Connection details'))) {
      console.log('🔴 CRITICAL: Connection details modal stuck at "Loading..." - Backend not responding to get_connection_details requests');
    }
    if (this.errors.some(e => e.includes('timeout'))) {
      console.log('🔴 CRITICAL: WebSocket endpoints not accessible - Backend may not be running');
    }
    if (this.errors.some(e => e.includes('function missing'))) {
      console.log('🟡 MEDIUM: JavaScript functions missing - Dashboard functionality incomplete');
    }
    
    console.log('\n📋 RECOMMENDATIONS:');
    console.log('1. Ensure backend is running with WebSocket endpoints active');
    console.log('2. Implement get_connection_details handler in ConnectionMonitorConsumer');
    console.log('3. Test dashboard with real WebSocket traffic');
    console.log('4. Verify message conformance with authoritative specifications');
    console.log('5. Add comprehensive error handling for connection failures');
  }
}

// Run the test
const tester = new WebSocketDashboardComprehensiveTester();
tester.runTests().catch(console.error);
