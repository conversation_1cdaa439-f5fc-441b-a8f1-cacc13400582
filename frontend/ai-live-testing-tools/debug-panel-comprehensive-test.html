<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Panel Comprehensive Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .danger {
            background-color: #dc3545;
        }
        .danger:hover {
            background-color: #c82333;
        }
        .storage-display {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        input, select {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
        }
        .critical {
            background-color: #f8d7da;
            border: 2px solid #dc3545;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .critical h4 {
            color: #721c24;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Debug Panel Comprehensive Test</h1>
        <p>This tool comprehensively tests debug panel storage, state persistence, and identifies common issues that cause frontend errors.</p>

        <div class="test-section">
            <h3>📋 Current Storage State</h3>
            <button onclick="displayCurrentStorage()">Refresh Storage Display</button>
            <button onclick="exportStorage()">Export Storage</button>
            <button onclick="importStorage()">Import Storage</button>
            <div id="storageDisplay" class="storage-display">Click "Refresh Storage Display" to see current values</div>
        </div>

        <div class="test-section">
            <h3>🧪 Automated Tests</h3>
            <div class="test-grid">
                <div>
                    <button onclick="runAllTests()">Run All Tests</button>
                    <button onclick="runQuickTest()">Quick Test</button>
                    <button onclick="runStressTest()">Stress Test</button>
                </div>
                <div>
                    <button class="danger" onclick="clearAllStorage()">Clear All Storage</button>
                    <button onclick="resetToDefaults()">Reset to Defaults</button>
                </div>
            </div>
            <div id="testResults"></div>
        </div>

        <div class="test-section">
            <h3>🎯 Manual Testing</h3>
            <div class="two-column">
                <div>
                    <h4>Set Debug Panel Values:</h4>
                    <label>User ID: <input type="text" id="manualUserId" placeholder="2"></label><br>
                    <label>LLM Config ID: <input type="text" id="manualLLMConfigId" placeholder="5"></label><br>
                    <label>Backend URL: <input type="text" id="manualBackendUrl" placeholder="ws://localhost:8000/ws/game/"></label><br>
                    <button onclick="setManualValues()">Set Values</button>
                    <button onclick="testPersistence()">Test Persistence</button>
                </div>
                <div>
                    <h4>Simulate Frontend Behavior:</h4>
                    <button onclick="simulateDebugPanelUsage()">Simulate Normal Usage</button>
                    <button onclick="simulateErrorScenarios()">Simulate Error Scenarios</button>
                    <button onclick="simulatePageReload()">Simulate Page Reload</button>
                    <button onclick="testCrossTabSync()">Test Cross-Tab Sync</button>
                </div>
            </div>
            <div id="simulationResults"></div>
        </div>

        <div class="test-section">
            <h3>🔍 Issue Detection</h3>
            <button onclick="detectCommonIssues()">Detect Common Issues</button>
            <button onclick="validateStorageIntegrity()">Validate Storage Integrity</button>
            <button onclick="checkBrowserCompatibility()">Check Browser Compatibility</button>
            <div id="issueResults"></div>
        </div>

        <div class="test-section">
            <h3>📊 Performance Analysis</h3>
            <button onclick="measureStoragePerformance()">Measure Performance</button>
            <button onclick="testStorageQuota()">Test Storage Quota</button>
            <div id="performanceResults"></div>
        </div>

        <div class="test-section">
            <h3>🚨 Critical Issue Simulation</h3>
            <div class="critical">
                <h4>⚠️ These tests simulate the exact issues from your logs</h4>
                <button onclick="simulateLLMConfigError()">Simulate LLM Config Error</button>
                <button onclick="simulateProfileRetrievalError()">Simulate Profile Retrieval Error</button>
                <button onclick="simulateDebugPanelStateIssue()">Simulate State Persistence Issue</button>
                <button onclick="simulateWebSocketMessageError()">Simulate WebSocket Message Error</button>
            </div>
            <div id="criticalResults"></div>
        </div>
    </div>

    <script>
        // Debug panel storage keys (matching frontend implementation)
        const DEBUG_STORAGE_KEYS = {
            USER_ID: 'debug_selected_user_id',
            LLM_CONFIG_ID: 'debug_selected_llm_config_id',
            BACKEND_URL: 'debug_backend_url',
            GOALI_STATE: 'goali-state'
        };

        // Test data
        const TEST_DATA = {
            userIds: ['1', '2', '3', 'test-user-123'],
            llmConfigIds: ['1', '2', '3', '4', '5'],
            backendUrls: [
                'ws://localhost:8000/ws/game/',
                'ws://127.0.0.1:8000/ws/game/',
                'wss://production.example.com/ws/game/'
            ]
        };

        // Utility functions
        function createResultElement(title, success, message, type = 'info') {
            const div = document.createElement('div');
            div.className = `result ${success ? 'success' : 'error'}`;
            if (type) div.classList.add(type);
            div.innerHTML = `<strong>${title}:</strong> ${message}`;
            return div;
        }

        function logResult(containerId, title, success, message, type = 'info') {
            const container = document.getElementById(containerId);
            if (container) {
                container.appendChild(createResultElement(title, success, message, type));
            }
        }

        // Storage display functions
        function displayCurrentStorage() {
            const storageData = {
                localStorage: {},
                sessionStorage: {},
                debugPanelKeys: {},
                metadata: {
                    timestamp: new Date().toISOString(),
                    userAgent: navigator.userAgent,
                    storageQuota: 'checking...'
                }
            };

            // Get all localStorage items
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                storageData.localStorage[key] = localStorage.getItem(key);
            }

            // Get all sessionStorage items
            for (let i = 0; i < sessionStorage.length; i++) {
                const key = sessionStorage.key(i);
                storageData.sessionStorage[key] = sessionStorage.getItem(key);
            }

            // Get debug panel specific keys
            Object.entries(DEBUG_STORAGE_KEYS).forEach(([name, key]) => {
                storageData.debugPanelKeys[name] = localStorage.getItem(key);
            });

            // Check storage quota if available
            if ('storage' in navigator && 'estimate' in navigator.storage) {
                navigator.storage.estimate().then(estimate => {
                    storageData.metadata.storageQuota = {
                        quota: estimate.quota,
                        usage: estimate.usage,
                        usagePercentage: ((estimate.usage / estimate.quota) * 100).toFixed(2) + '%'
                    };
                    document.getElementById('storageDisplay').textContent = JSON.stringify(storageData, null, 2);
                });
            } else {
                storageData.metadata.storageQuota = 'Not available';
                document.getElementById('storageDisplay').textContent = JSON.stringify(storageData, null, 2);
            }
        }

        // Test functions
        function runAllTests() {
            const results = document.getElementById('testResults');
            results.innerHTML = '<h4>Running comprehensive tests...</h4>';

            const tests = [
                testBasicStorage,
                testDebugPanelKeys,
                testStorageRetrieval,
                testStorageOverwrite,
                testStorageErrors,
                testPersistenceAcrossReload,
                testInvalidData,
                testStorageEvents
            ];

            let passed = 0;
            let failed = 0;

            tests.forEach(test => {
                try {
                    const result = test();
                    logResult('testResults', result.name, result.success, result.message);
                    if (result.success) passed++;
                    else failed++;
                } catch (error) {
                    logResult('testResults', test.name, false, `Test failed: ${error.message}`);
                    failed++;
                }
            });

            logResult('testResults', 'Test Summary', passed > failed,
                `${passed} passed, ${failed} failed`, 'info');
        }

        function runQuickTest() {
            const results = document.getElementById('testResults');
            results.innerHTML = '<h4>Running quick test...</h4>';

            // Test the exact scenario from the logs
            const testUserId = '2';
            const testLLMConfigId = '5';

            // Set values
            localStorage.setItem(DEBUG_STORAGE_KEYS.USER_ID, testUserId);
            localStorage.setItem(DEBUG_STORAGE_KEYS.LLM_CONFIG_ID, testLLMConfigId);

            // Verify values
            const retrievedUserId = localStorage.getItem(DEBUG_STORAGE_KEYS.USER_ID);
            const retrievedLLMConfigId = localStorage.getItem(DEBUG_STORAGE_KEYS.LLM_CONFIG_ID);

            const success = retrievedUserId === testUserId && retrievedLLMConfigId === testLLMConfigId;

            logResult('testResults', 'Quick Test', success,
                success ? 'Basic storage working correctly' : 'Storage retrieval failed');

            if (success) {
                logResult('testResults', 'Debug Panel Ready', true,
                    'Debug panel should restore User ID: 2, LLM Config: 5');
            }
        }

        function testBasicStorage() {
            const testKey = 'test_key_' + Date.now();
            const testValue = 'test_value_' + Date.now();

            localStorage.setItem(testKey, testValue);
            const retrieved = localStorage.getItem(testKey);
            localStorage.removeItem(testKey);

            return {
                name: 'Basic Storage',
                success: retrieved === testValue,
                message: retrieved === testValue ? 'localStorage working correctly' : 'localStorage failed'
            };
        }

        function testDebugPanelKeys() {
            const testValues = {
                [DEBUG_STORAGE_KEYS.USER_ID]: '2',
                [DEBUG_STORAGE_KEYS.LLM_CONFIG_ID]: '5',
                [DEBUG_STORAGE_KEYS.BACKEND_URL]: 'ws://localhost:8000/ws/game/'
            };

            // Set all values
            Object.entries(testValues).forEach(([key, value]) => {
                localStorage.setItem(key, value);
            });

            // Verify all values
            let allCorrect = true;
            Object.entries(testValues).forEach(([key, expectedValue]) => {
                const actualValue = localStorage.getItem(key);
                if (actualValue !== expectedValue) {
                    allCorrect = false;
                }
            });

            return {
                name: 'Debug Panel Keys',
                success: allCorrect,
                message: allCorrect ? 'All debug panel keys stored correctly' : 'Some debug panel keys failed'
            };
        }

        function testStorageRetrieval() {
            // Test the exact pattern used in debug-panel.ts
            const savedUserId = localStorage.getItem(DEBUG_STORAGE_KEYS.USER_ID) || '2';
            const savedLLMConfigId = localStorage.getItem(DEBUG_STORAGE_KEYS.LLM_CONFIG_ID) || '';
            const savedBackendUrl = localStorage.getItem(DEBUG_STORAGE_KEYS.BACKEND_URL) || 'ws://localhost:8000/ws/game/';

            return {
                name: 'Storage Retrieval Pattern',
                success: true,
                message: `Retrieved - User: ${savedUserId}, LLM: ${savedLLMConfigId}, URL: ${savedBackendUrl}`
            };
        }

        function testStorageOverwrite() {
            const key = DEBUG_STORAGE_KEYS.USER_ID;

            localStorage.setItem(key, '1');
            localStorage.setItem(key, '2');
            localStorage.setItem(key, '3');

            const final = localStorage.getItem(key);

            return {
                name: 'Storage Overwrite',
                success: final === '3',
                message: final === '3' ? 'Overwrite working correctly' : `Expected '3', got '${final}'`
            };
        }

        function testStorageErrors() {
            try {
                // Test with null/undefined
                localStorage.setItem(DEBUG_STORAGE_KEYS.USER_ID, null);
                const nullResult = localStorage.getItem(DEBUG_STORAGE_KEYS.USER_ID);

                localStorage.setItem(DEBUG_STORAGE_KEYS.USER_ID, undefined);
                const undefinedResult = localStorage.getItem(DEBUG_STORAGE_KEYS.USER_ID);

                return {
                    name: 'Storage Error Handling',
                    success: true,
                    message: `null -> '${nullResult}', undefined -> '${undefinedResult}'`
                };
            } catch (error) {
                return {
                    name: 'Storage Error Handling',
                    success: false,
                    message: `Error: ${error.message}`
                };
            }
        }

        function testPersistenceAcrossReload() {
            // This test sets up data that should persist across page reload
            const timestamp = Date.now().toString();
            localStorage.setItem('persistence_test', timestamp);

            return {
                name: 'Persistence Setup',
                success: true,
                message: `Set persistence test value: ${timestamp}. Reload page to verify.`
            };
        }

        function testInvalidData() {
            try {
                // Test with various invalid data types
                const testCases = [
                    { value: '', expected: '' },
                    { value: '0', expected: '0' },
                    { value: 'false', expected: 'false' },
                    { value: JSON.stringify({test: 'object'}), expected: '{"test":"object"}' }
                ];

                let allPassed = true;
                testCases.forEach(testCase => {
                    localStorage.setItem('invalid_test', testCase.value);
                    const result = localStorage.getItem('invalid_test');
                    if (result !== testCase.expected) {
                        allPassed = false;
                    }
                });

                localStorage.removeItem('invalid_test');

                return {
                    name: 'Invalid Data Handling',
                    success: allPassed,
                    message: allPassed ? 'All edge cases handled correctly' : 'Some edge cases failed'
                };
            } catch (error) {
                return {
                    name: 'Invalid Data Handling',
                    success: false,
                    message: `Error: ${error.message}`
                };
            }
        }

        function testStorageEvents() {
            // Test if storage events are fired (for cross-tab sync)
            let eventFired = false;

            const handler = (e) => {
                if (e.key === 'event_test') {
                    eventFired = true;
                }
            };

            window.addEventListener('storage', handler);

            // This won't fire in the same tab, but we can test the setup
            localStorage.setItem('event_test', 'test_value');
            localStorage.removeItem('event_test');

            window.removeEventListener('storage', handler);

            return {
                name: 'Storage Events',
                success: true,
                message: 'Storage event listener setup correctly (cross-tab sync ready)'
            };
        }

        // Manual testing functions
        function setManualValues() {
            const userId = document.getElementById('manualUserId').value;
            const llmConfigId = document.getElementById('manualLLMConfigId').value;
            const backendUrl = document.getElementById('manualBackendUrl').value;

            if (userId) localStorage.setItem(DEBUG_STORAGE_KEYS.USER_ID, userId);
            if (llmConfigId) localStorage.setItem(DEBUG_STORAGE_KEYS.LLM_CONFIG_ID, llmConfigId);
            if (backendUrl) localStorage.setItem(DEBUG_STORAGE_KEYS.BACKEND_URL, backendUrl);

            logResult('simulationResults', 'Manual Values Set', true,
                `User: ${userId}, LLM: ${llmConfigId}, URL: ${backendUrl}`);

            displayCurrentStorage();
        }

        function testPersistence() {
            const userId = localStorage.getItem(DEBUG_STORAGE_KEYS.USER_ID);
            const llmConfigId = localStorage.getItem(DEBUG_STORAGE_KEYS.LLM_CONFIG_ID);
            const backendUrl = localStorage.getItem(DEBUG_STORAGE_KEYS.BACKEND_URL);

            const hasValues = userId || llmConfigId || backendUrl;

            logResult('simulationResults', 'Persistence Test', hasValues,
                hasValues ?
                `Values persisted - User: ${userId}, LLM: ${llmConfigId}` :
                'No values found in storage');
        }

        // Critical issue simulation functions
        function simulateLLMConfigError() {
            const results = document.getElementById('criticalResults');
            results.innerHTML = '<h4>🔥 Simulating LLM Config Error...</h4>';

            // Clear LLM config to simulate the error
            localStorage.removeItem(DEBUG_STORAGE_KEYS.LLM_CONFIG_ID);

            // Simulate the message that would be sent
            const messageWithoutLLMConfig = {
                type: 'chat_message',
                content: {
                    message: 'Test message',
                    user_profile_id: '2',
                    timestamp: new Date().toISOString()
                    // Note: no metadata.llm_config_id
                }
            };

            logResult('criticalResults', 'LLM Config Missing', false,
                'This simulates the "No LLMConfig provided" error from your logs');

            logResult('criticalResults', 'Message Structure', false,
                `Message would be sent without LLM config: ${JSON.stringify(messageWithoutLLMConfig, null, 2)}`);

            logResult('criticalResults', 'Fix Required', false,
                'Debug panel must restore LLM config selection and include it in message metadata');
        }

        function simulateProfileRetrievalError() {
            const results = document.getElementById('criticalResults');
            results.innerHTML = '<h4>🔥 Simulating Profile Retrieval Error...</h4>';

            // Test with invalid user ID
            localStorage.setItem(DEBUG_STORAGE_KEYS.USER_ID, 'invalid-user-id');

            const invalidUserId = localStorage.getItem(DEBUG_STORAGE_KEYS.USER_ID);

            logResult('criticalResults', 'Invalid User ID', false,
                `User ID "${invalidUserId}" may not exist in database`);

            logResult('criticalResults', 'Profile Retrieval', false,
                'This simulates "An unexpected error occurred while retrieving your profile"');

            logResult('criticalResults', 'Fix Required', false,
                'Validate user ID exists in database before sending messages');
        }

        function simulateDebugPanelStateIssue() {
            const results = document.getElementById('criticalResults');
            results.innerHTML = '<h4>🔥 Simulating Debug Panel State Issue...</h4>';

            // Clear all debug panel state
            Object.values(DEBUG_STORAGE_KEYS).forEach(key => {
                localStorage.removeItem(key);
            });

            // Try to load state (simulating debug panel initialization)
            const loadedUserId = localStorage.getItem(DEBUG_STORAGE_KEYS.USER_ID) || '2';
            const loadedLLMConfigId = localStorage.getItem(DEBUG_STORAGE_KEYS.LLM_CONFIG_ID) || '';
            const loadedBackendUrl = localStorage.getItem(DEBUG_STORAGE_KEYS.BACKEND_URL) || 'ws://localhost:8000/ws/game/';

            logResult('criticalResults', 'State Restoration', false,
                `Loaded defaults - User: ${loadedUserId}, LLM: ${loadedLLMConfigId}`);

            logResult('criticalResults', 'Issue Identified', false,
                'Debug panel not restoring previous selections properly');

            logResult('criticalResults', 'Fix Required', false,
                'Debug panel should save selections immediately when changed and restore on load');
        }

        function simulateWebSocketMessageError() {
            const results = document.getElementById('criticalResults');
            results.innerHTML = '<h4>🔥 Simulating WebSocket Message Error...</h4>';

            // Simulate the exact message structure that causes issues
            const problematicMessage = {
                type: 'chat_message',
                message: 'Test message', // Wrong structure - should be in content
                user_profile_id: '2',
                timestamp: new Date().toISOString()
                // Missing proper content structure
            };

            const correctMessage = {
                type: 'chat_message',
                content: {
                    message: 'Test message',
                    user_profile_id: '2',
                    timestamp: new Date().toISOString(),
                    metadata: {
                        llm_config_id: '5'
                    }
                }
            };

            logResult('criticalResults', 'Problematic Message', false,
                `Wrong structure: ${JSON.stringify(problematicMessage, null, 2)}`);

            logResult('criticalResults', 'Correct Message', true,
                `Correct structure: ${JSON.stringify(correctMessage, null, 2)}`);

            logResult('criticalResults', 'Fix Required', false,
                'Ensure message follows correct WebSocket message format with content wrapper');
        }

        // Utility functions
        function clearAllStorage() {
            localStorage.clear();
            sessionStorage.clear();
            logResult('testResults', 'Storage Cleared', true, 'All storage cleared');
            displayCurrentStorage();
        }

        function resetToDefaults() {
            localStorage.setItem(DEBUG_STORAGE_KEYS.USER_ID, '2');
            localStorage.setItem(DEBUG_STORAGE_KEYS.LLM_CONFIG_ID, '5');
            localStorage.setItem(DEBUG_STORAGE_KEYS.BACKEND_URL, 'ws://localhost:8000/ws/game/');

            logResult('testResults', 'Reset to Defaults', true, 'Default values restored');
            displayCurrentStorage();
        }

        function detectCommonIssues() {
            const results = document.getElementById('issueResults');
            results.innerHTML = '<h4>🔍 Detecting Common Issues...</h4>';

            const issues = [];

            // Check for missing debug panel values
            const userId = localStorage.getItem(DEBUG_STORAGE_KEYS.USER_ID);
            const llmConfigId = localStorage.getItem(DEBUG_STORAGE_KEYS.LLM_CONFIG_ID);

            if (!userId) {
                issues.push('Missing user ID - will default to "2"');
            }

            if (!llmConfigId) {
                issues.push('Missing LLM config ID - this causes "No LLMConfig provided" error');
            }

            // Check for invalid values
            if (userId && !['1', '2', '3'].includes(userId)) {
                issues.push(`User ID "${userId}" may not exist in database`);
            }

            // Check storage quota
            if (localStorage.length > 50) {
                issues.push('Large number of localStorage items - may affect performance');
            }

            if (issues.length === 0) {
                logResult('issueResults', 'Issue Detection', true, 'No common issues detected');
            } else {
                issues.forEach(issue => {
                    logResult('issueResults', 'Issue Found', false, issue);
                });
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            displayCurrentStorage();

            // Check for persistence test
            const persistenceTest = localStorage.getItem('persistence_test');
            if (persistenceTest) {
                logResult('testResults', 'Persistence Verified', true,
                    `Value persisted across reload: ${persistenceTest}`);
                localStorage.removeItem('persistence_test');
            }
        });
    </script>
</body>
</html>
