/**
 * Playwright Processing Overlay Fix
 * 
 * Targeted fix for the processing overlay that blocks all interactions
 */

const { chromium } = require('playwright');

class ProcessingOverlayFix {
    constructor() {
        this.browser = null;
        this.page = null;
    }

    async initialize() {
        console.log('🔧 Initializing Processing Overlay Fix...');
        
        this.browser = await chromium.launch({ 
            headless: false,
            slowMo: 1000
        });
        
        this.page = await this.browser.newPage();
        
        // Set up console logging
        this.page.on('console', msg => {
            console.log(`🖥️  Console: ${msg.text()}`);
        });
    }

    async loadAndFix() {
        console.log('🌐 Loading frontend...');
        
        await this.page.goto('http://localhost:3002/', { 
            waitUntil: 'domcontentloaded',
            timeout: 60000 
        });
        
        // Wait for main components
        await this.page.waitForSelector('app-shell', { timeout: 30000 });
        console.log('✅ Frontend loaded');
        
        // Wait for WebSocket connection
        await this.page.waitForTimeout(20000);
        
        // Now fix the processing overlay
        await this.fixProcessingOverlay();
        
        // Test the fix
        await this.testChatInteraction();
    }

    async fixProcessingOverlay() {
        console.log('🔧 Analyzing and fixing processing overlay...');
        
        // First, let's see what overlays exist
        const overlayInfo = await this.page.evaluate(() => {
            const overlays = document.querySelectorAll('.processing-overlay, [class*="overlay"], [class*="loading"]');
            return Array.from(overlays).map(overlay => ({
                className: overlay.className,
                id: overlay.id,
                display: window.getComputedStyle(overlay).display,
                visibility: window.getComputedStyle(overlay).visibility,
                pointerEvents: window.getComputedStyle(overlay).pointerEvents,
                zIndex: window.getComputedStyle(overlay).zIndex,
                position: window.getComputedStyle(overlay).position,
                opacity: window.getComputedStyle(overlay).opacity
            }));
        });

        console.log('📊 Found overlays:', JSON.stringify(overlayInfo, null, 2));

        // Remove all processing overlays
        await this.page.evaluate(() => {
            // Remove processing overlays
            const overlays = document.querySelectorAll('.processing-overlay, [class*="overlay"], [class*="loading"]');
            overlays.forEach(overlay => {
                console.log('Removing overlay:', overlay.className);
                overlay.style.display = 'none';
                overlay.style.pointerEvents = 'none';
                overlay.style.zIndex = '-9999';
                overlay.style.opacity = '0';
                overlay.style.visibility = 'hidden';
                // Also try removing it completely
                overlay.remove();
            });

            // Also check for any elements with high z-index that might be blocking
            const allElements = document.querySelectorAll('*');
            Array.from(allElements).forEach(el => {
                const style = window.getComputedStyle(el);
                const zIndex = parseInt(style.zIndex);
                if (zIndex > 1000 && style.pointerEvents !== 'none') {
                    console.log('High z-index element found:', el.className, 'z-index:', zIndex);
                    el.style.pointerEvents = 'none';
                }
            });
        });

        console.log('✅ Processing overlay removal attempted');
    }

    async testChatInteraction() {
        console.log('💬 Testing chat interaction after fix...');
        
        try {
            // Find chat input
            const chatInput = await this.page.waitForSelector('textarea', { timeout: 10000 });
            console.log('📝 Found chat textarea');

            // Force enable the textarea
            await this.page.evaluate(() => {
                const textarea = document.querySelector('textarea');
                if (textarea) {
                    textarea.disabled = false;
                    textarea.readOnly = false;
                    textarea.style.pointerEvents = 'auto';
                }
            });

            // Try clicking with force
            await chatInput.click({ force: true });
            console.log('🖱️  Clicked chat input (forced)');

            // Check if focused
            const isFocused = await this.page.evaluate(() => {
                return document.activeElement && document.activeElement.tagName === 'TEXTAREA';
            });

            console.log(`🎯 Chat input focused: ${isFocused}`);

            // Try typing
            await this.page.fill('textarea', 'hey! do you recognize me?');
            console.log('⌨️  Typed message');

            // Check if text was entered
            const textValue = await this.page.inputValue('textarea');
            console.log(`📝 Text in input: "${textValue}"`);

            // Try sending with Enter
            await this.page.press('textarea', 'Enter');
            console.log('📤 Pressed Enter to send');

            // Wait for response
            console.log('⏳ Waiting for response...');
            await this.page.waitForTimeout(15000);

            console.log('✅ Chat interaction test completed');

        } catch (error) {
            console.log(`❌ Chat interaction test failed: ${error.message}`);
        }
    }

    async testWheelSpinning() {
        console.log('🎡 Testing wheel spinning...');
        
        try {
            // Look for wheel elements
            const wheelInfo = await this.page.evaluate(() => {
                const selectors = ['svg', 'canvas', '.wheel', '[data-wheel]'];
                const found = [];
                
                selectors.forEach(selector => {
                    const elements = document.querySelectorAll(selector);
                    Array.from(elements).forEach((el, index) => {
                        const rect = el.getBoundingClientRect();
                        found.push({
                            selector: `${selector}[${index}]`,
                            visible: rect.width > 0 && rect.height > 0,
                            position: { x: rect.x, y: rect.y, width: rect.width, height: rect.height },
                            className: el.className,
                            id: el.id
                        });
                    });
                });
                
                return found;
            });

            console.log('🎡 Wheel elements found:', JSON.stringify(wheelInfo, null, 2));

            // Look for spin button
            const spinButtons = await this.page.evaluate(() => {
                const buttons = document.querySelectorAll('button');
                const spinButtons = [];
                
                Array.from(buttons).forEach(button => {
                    const text = button.textContent.toLowerCase();
                    if (text.includes('spin') || button.className.includes('spin')) {
                        spinButtons.push({
                            text: button.textContent,
                            className: button.className,
                            disabled: button.disabled,
                            visible: button.offsetParent !== null
                        });
                    }
                });
                
                return spinButtons;
            });

            console.log('🎯 Spin buttons found:', JSON.stringify(spinButtons, null, 2));

            // Try to click on the wheel or spin button
            if (wheelInfo.length > 0) {
                const wheel = wheelInfo[0];
                if (wheel.visible && wheel.position.width > 0) {
                    console.log('🖱️  Attempting to click on wheel...');
                    await this.page.click(`${wheel.selector}`, { 
                        position: { 
                            x: wheel.position.width / 2, 
                            y: wheel.position.height / 2 
                        },
                        force: true
                    });
                    console.log('✅ Clicked on wheel');
                    
                    // Wait to see if spinning animation occurs
                    await this.page.waitForTimeout(3000);
                    console.log('⏳ Waited for potential spinning animation');
                }
            }

        } catch (error) {
            console.log(`❌ Wheel spinning test failed: ${error.message}`);
        }
    }

    async cleanup() {
        if (this.browser) {
            await this.browser.close();
        }
    }
}

// Main execution
async function main() {
    const fixer = new ProcessingOverlayFix();
    
    try {
        await fixer.initialize();
        await fixer.loadAndFix();
        await fixer.testWheelSpinning();
        
        console.log('\n🎯 PROCESSING OVERLAY FIX COMPLETED');
        console.log('=====================================');
        console.log('✅ Processing overlay removal attempted');
        console.log('✅ Chat interaction tested');
        console.log('✅ Wheel spinning tested');
        console.log('\n📝 Manual verification recommended:');
        console.log('1. Check if chat input is now clickable');
        console.log('2. Verify typing works in chat');
        console.log('3. Test message sending');
        console.log('4. Test wheel interaction');
        
    } catch (error) {
        console.error(`❌ Fix process failed: ${error.message}`);
    } finally {
        await fixer.cleanup();
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = { ProcessingOverlayFix };
