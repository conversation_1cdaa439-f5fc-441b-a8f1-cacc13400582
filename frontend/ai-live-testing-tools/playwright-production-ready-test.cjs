#!/usr/bin/env node

/**
 * Production-Ready Playwright Test Suite
 * 
 * Comprehensive testing for production deployment readiness:
 * 1. Connection stability with robust error handling
 * 2. Chat functionality with precise error messages
 * 3. Mentor response time optimization
 * 4. User experience validation
 * 5. Error recovery mechanisms
 */

const { chromium } = require('playwright');
const fs = require('fs');

class ProductionReadyTester {
    constructor() {
        this.browser = null;
        this.context = null;
        this.page = null;
        this.testResults = {
            connectionStability: {
                status: 'unknown',
                wsConnected: false,
                apiAvailable: false,
                chatEnabled: false,
                errorHandling: false,
                reconnectionWorking: false,
                details: []
            },
            userExperience: {
                status: 'unknown',
                loadTime: 0,
                responsiveness: false,
                errorMessages: [],
                tooltips: false,
                accessibility: false,
                details: []
            },
            mentorPerformance: {
                status: 'unknown',
                averageResponseTime: 0,
                responses: [],
                timeouts: 0,
                errorRecovery: false,
                details: []
            },
            productionReadiness: {
                status: 'unknown',
                score: 0,
                criticalIssues: [],
                warnings: [],
                recommendations: []
            }
        };
        this.startTime = Date.now();
    }

    async initialize() {
        console.log('🚀 Initializing Production-Ready Test Suite...');
        
        this.browser = await chromium.launch({
            headless: false,
            slowMo: 500,
            args: ['--disable-web-security']
        });

        this.context = await this.browser.newContext({
            viewport: { width: 1280, height: 720 }
        });

        this.page = await this.context.newPage();
        
        // Comprehensive logging
        this.page.on('console', msg => {
            const text = msg.text();
            const timestamp = Date.now() - this.startTime;
            
            console.log(`🖥️  [${msg.type()}] ${text}`);
            
            // Track error messages for UX analysis
            if (msg.type() === 'error') {
                this.testResults.userExperience.errorMessages.push({
                    timestamp,
                    message: text
                });
            }
        });

        this.page.on('pageerror', error => {
            console.error('❌ Page Error:', error.message);
            this.testResults.productionReadiness.criticalIssues.push({
                type: 'page_error',
                message: error.message,
                timestamp: Date.now() - this.startTime
            });
        });

        await this.setupWebSocketMonitoring();
        console.log('✅ Production test suite initialized');
    }

    async setupWebSocketMonitoring() {
        await this.page.routeWebSocket('**/ws/**', ws => {
            console.log(`🔗 WebSocket intercepted: ${ws.url()}`);
            
            const server = ws.connectToServer();
            let messageCount = 0;
            
            server.onMessage(message => {
                messageCount++;
                const timestamp = Date.now() - this.startTime;
                console.log(`📨 WebSocket ← Server (#${messageCount}, ${timestamp}ms):`, message.substring(0, 100) + '...');
                
                try {
                    const parsed = JSON.parse(message);
                    if (parsed.type === 'ai_response') {
                        this.testResults.mentorPerformance.responses.push({
                            timestamp,
                            responseTime: timestamp,
                            messageNumber: messageCount,
                            contentLength: parsed.content?.length || 0
                        });
                    }
                } catch (e) {
                    // Not JSON, ignore
                }
            });

            ws.onMessage(message => {
                console.log('📤 WebSocket → Server:', message.substring(0, 100) + '...');
                server.send(message);
            });

            // Note: Playwright WebSocket server object doesn't have onClose/onError methods
            // Connection monitoring is handled through the main WebSocket events
        });
    }

    async testConnectionStability() {
        console.log('🔌 Testing connection stability and error handling...');
        
        const loadStartTime = Date.now();
        
        // Navigate to app
        await this.page.goto('http://localhost:3001');
        await this.page.waitForLoadState('networkidle');
        
        const loadTime = Date.now() - loadStartTime;
        this.testResults.userExperience.loadTime = loadTime;
        
        console.log(`📊 Page load time: ${loadTime}ms`);
        
        // Wait for initialization
        await this.page.waitForTimeout(5000);
        
        // Check connection status
        const connectionStatus = await this.page.evaluate(() => {
            return {
                wsExists: !!window.websocket,
                wsConnected: window.websocket?.readyState === 1,
                stateManager: !!window.stateManager,
                chatInput: {
                    exists: !!document.querySelector('.message-input'),
                    disabled: document.querySelector('.message-input')?.disabled || false,
                    tooltip: document.querySelector('.message-input')?.title || ''
                }
            };
        });
        
        console.log('📊 Connection Status:', connectionStatus);
        
        this.testResults.connectionStability.wsConnected = connectionStatus.wsConnected;
        this.testResults.connectionStability.chatEnabled = !connectionStatus.chatInput.disabled;
        this.testResults.userExperience.tooltips = !!connectionStatus.chatInput.tooltip;
        
        // Test API availability
        try {
            const apiResponse = await this.page.evaluate(async () => {
                const response = await fetch('/api/debug/users/', { method: 'HEAD' });
                return { status: response.status, ok: response.ok };
            });
            
            this.testResults.connectionStability.apiAvailable = apiResponse.ok;
            console.log(`📊 API Status: ${apiResponse.status} (${apiResponse.ok ? 'OK' : 'Failed'})`);
        } catch (error) {
            console.log('📊 API test failed:', error.message);
            this.testResults.connectionStability.apiAvailable = false;
        }
        
        // Determine connection stability status
        if (this.testResults.connectionStability.wsConnected && 
            this.testResults.connectionStability.chatEnabled) {
            this.testResults.connectionStability.status = 'EXCELLENT';
        } else if (this.testResults.connectionStability.wsConnected) {
            this.testResults.connectionStability.status = 'GOOD';
        } else {
            this.testResults.connectionStability.status = 'FAILED';
        }
        
        return this.testResults.connectionStability;
    }

    async testUserExperience() {
        console.log('👤 Testing user experience and accessibility...');
        
        // Test chat input responsiveness
        const chatInput = this.page.locator('.message-input').first();
        const inputExists = await chatInput.count() > 0;
        
        if (inputExists) {
            // Test input responsiveness
            const testText = "Test input responsiveness";
            await chatInput.fill(testText);
            
            const inputValue = await chatInput.inputValue();
            this.testResults.userExperience.responsiveness = inputValue === testText;
            
            // Clear input
            await chatInput.fill('');
        }
        
        // Test error message clarity
        const errorMessages = this.testResults.userExperience.errorMessages;
        const hasUserFriendlyErrors = errorMessages.every(msg => 
            !msg.message.includes('undefined') && 
            !msg.message.includes('null') &&
            !msg.message.includes('TypeError')
        );
        
        // Calculate UX score
        let uxScore = 0;
        if (this.testResults.userExperience.loadTime < 3000) uxScore += 25;
        if (this.testResults.userExperience.responsiveness) uxScore += 25;
        if (this.testResults.userExperience.tooltips) uxScore += 25;
        if (hasUserFriendlyErrors) uxScore += 25;
        
        if (uxScore >= 75) {
            this.testResults.userExperience.status = 'EXCELLENT';
        } else if (uxScore >= 50) {
            this.testResults.userExperience.status = 'GOOD';
        } else {
            this.testResults.userExperience.status = 'NEEDS_IMPROVEMENT';
        }
        
        return this.testResults.userExperience;
    }

    async testMentorPerformance() {
        console.log('🤖 Testing mentor performance and response times...');
        
        // Enable debug panel and select user
        await this.page.keyboard.press('Control+Shift+D');
        await this.page.waitForTimeout(1000);
        
        const userSelect = this.page.locator('select[name="user"]').first();
        const selectExists = await userSelect.count() > 0;
        
        if (selectExists) {
            await userSelect.selectOption('2');
            console.log('✅ Selected user 2 (phiphi)');
            await this.page.waitForTimeout(1000);
        }
        
        // Test mentor response with simple question
        const chatInput = this.page.locator('.message-input').first();
        const isEnabled = await chatInput.evaluate(el => !el.disabled);
        
        if (!isEnabled) {
            this.testResults.mentorPerformance.status = 'FAILED';
            this.testResults.mentorPerformance.details.push('Chat input disabled - cannot test mentor');
            return this.testResults.mentorPerformance;
        }
        
        const testQuestion = "hey, do you recognize me ?";
        console.log(`💬 Sending test question: "${testQuestion}"`);
        
        const startTime = Date.now();
        
        await chatInput.fill(testQuestion);
        await chatInput.press('Enter');
        
        // Wait for response with timeout
        let responseReceived = false;
        let waitTime = 0;
        const maxWaitTime = 30000; // 30 seconds
        
        while (!responseReceived && waitTime < maxWaitTime) {
            await this.page.waitForTimeout(1000);
            waitTime += 1000;
            
            // Check for AI response
            const aiMessages = await this.page.locator('message-bubble[data-type="ai"], .message.ai, .ai-message').count();
            if (aiMessages > 0) {
                responseReceived = true;
                const responseTime = Date.now() - startTime;
                console.log(`✅ Response received in ${responseTime}ms`);
                
                this.testResults.mentorPerformance.responses.push({
                    question: testQuestion,
                    responseTime,
                    timestamp: Date.now() - this.startTime
                });
                
                this.testResults.mentorPerformance.averageResponseTime = responseTime;
            }
        }
        
        if (!responseReceived) {
            console.log('❌ No response received within 30 seconds');
            this.testResults.mentorPerformance.timeouts++;
        }
        
        // Determine performance status
        const avgTime = this.testResults.mentorPerformance.averageResponseTime;
        if (avgTime > 0 && avgTime < 5000) {
            this.testResults.mentorPerformance.status = 'EXCELLENT';
        } else if (avgTime > 0 && avgTime < 15000) {
            this.testResults.mentorPerformance.status = 'GOOD';
        } else if (avgTime > 0) {
            this.testResults.mentorPerformance.status = 'SLOW';
        } else {
            this.testResults.mentorPerformance.status = 'FAILED';
        }
        
        return this.testResults.mentorPerformance;
    }

    async calculateProductionReadiness() {
        console.log('🎯 Calculating production readiness score...');
        
        let score = 0;
        const criticalIssues = [];
        const warnings = [];
        const recommendations = [];
        
        // Connection stability (30 points)
        if (this.testResults.connectionStability.status === 'EXCELLENT') {
            score += 30;
        } else if (this.testResults.connectionStability.status === 'GOOD') {
            score += 20;
            warnings.push('Chat input disabled despite WebSocket connection');
        } else {
            criticalIssues.push('WebSocket connection failed');
        }
        
        // User experience (25 points)
        if (this.testResults.userExperience.status === 'EXCELLENT') {
            score += 25;
        } else if (this.testResults.userExperience.status === 'GOOD') {
            score += 15;
            warnings.push('User experience could be improved');
        } else {
            criticalIssues.push('Poor user experience detected');
        }
        
        // Mentor performance (25 points)
        if (this.testResults.mentorPerformance.status === 'EXCELLENT') {
            score += 25;
        } else if (this.testResults.mentorPerformance.status === 'GOOD') {
            score += 15;
            warnings.push('Mentor response time could be faster');
        } else if (this.testResults.mentorPerformance.status === 'SLOW') {
            score += 5;
            criticalIssues.push('Mentor response time too slow for production');
        } else {
            criticalIssues.push('Mentor not responding');
        }
        
        // Error handling (20 points)
        const hasGoodErrorHandling = this.testResults.userExperience.errorMessages.length === 0 ||
                                   this.testResults.userExperience.errorMessages.every(msg => 
                                       !msg.message.includes('undefined') && 
                                       !msg.message.includes('TypeError')
                                   );
        
        if (hasGoodErrorHandling) {
            score += 20;
        } else {
            score += 10;
            warnings.push('Improve error message quality');
        }
        
        // Generate recommendations
        if (score < 70) {
            recommendations.push('Address critical issues before production deployment');
        }
        if (this.testResults.userExperience.loadTime > 3000) {
            recommendations.push('Optimize page load time');
        }
        if (this.testResults.mentorPerformance.averageResponseTime > 10000) {
            recommendations.push('Optimize mentor response time');
        }
        
        // Determine overall status
        let status;
        if (score >= 80 && criticalIssues.length === 0) {
            status = 'PRODUCTION_READY';
        } else if (score >= 60 && criticalIssues.length <= 1) {
            status = 'NEEDS_MINOR_FIXES';
        } else {
            status = 'NOT_READY';
        }
        
        this.testResults.productionReadiness = {
            status,
            score,
            criticalIssues,
            warnings,
            recommendations
        };
        
        return this.testResults.productionReadiness;
    }

    async generateProductionReport() {
        const report = {
            timestamp: new Date().toISOString(),
            duration: Date.now() - this.startTime,
            testResults: this.testResults,
            summary: {
                overallStatus: this.testResults.productionReadiness.status,
                score: this.testResults.productionReadiness.score,
                criticalIssues: this.testResults.productionReadiness.criticalIssues.length,
                warnings: this.testResults.productionReadiness.warnings.length
            }
        };

        const reportPath = `./test-results/production-ready-report-${Date.now()}.json`;
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        
        console.log('\n🎯 PRODUCTION READINESS REPORT');
        console.log('═'.repeat(60));
        console.log(`📁 Report saved to: ${reportPath}`);
        console.log(`⏱️  Test Duration: ${Math.round(report.duration / 1000)}s`);
        console.log(`🎯 Overall Score: ${report.summary.score}/100`);
        console.log(`📊 Status: ${this.getStatusEmoji(report.summary.overallStatus)} ${report.summary.overallStatus}`);
        
        console.log('\n📋 Detailed Results:');
        console.log(`🔌 Connection Stability: ${this.getStatusEmoji(this.testResults.connectionStability.status)} ${this.testResults.connectionStability.status}`);
        console.log(`👤 User Experience: ${this.getStatusEmoji(this.testResults.userExperience.status)} ${this.testResults.userExperience.status}`);
        console.log(`🤖 Mentor Performance: ${this.getStatusEmoji(this.testResults.mentorPerformance.status)} ${this.testResults.mentorPerformance.status}`);
        
        if (this.testResults.productionReadiness.criticalIssues.length > 0) {
            console.log('\n🚨 Critical Issues:');
            this.testResults.productionReadiness.criticalIssues.forEach((issue, i) => {
                console.log(`  ${i + 1}. ${issue}`);
            });
        }
        
        if (this.testResults.productionReadiness.warnings.length > 0) {
            console.log('\n⚠️  Warnings:');
            this.testResults.productionReadiness.warnings.forEach((warning, i) => {
                console.log(`  ${i + 1}. ${warning}`);
            });
        }
        
        if (this.testResults.productionReadiness.recommendations.length > 0) {
            console.log('\n💡 Recommendations:');
            this.testResults.productionReadiness.recommendations.forEach((rec, i) => {
                console.log(`  ${i + 1}. ${rec}`);
            });
        }
        
        return report;
    }

    getStatusEmoji(status) {
        const emojis = {
            'EXCELLENT': '🟢',
            'GOOD': '🟡',
            'NEEDS_IMPROVEMENT': '🟠',
            'SLOW': '🟠',
            'FAILED': '🔴',
            'PRODUCTION_READY': '🚀',
            'NEEDS_MINOR_FIXES': '⚠️',
            'NOT_READY': '🚫'
        };
        return emojis[status] || '❓';
    }

    async cleanup() {
        console.log('🧹 Cleaning up...');
        if (this.page) await this.page.close();
        if (this.context) await this.context.close();
        if (this.browser) await this.browser.close();
    }
}

// Main execution
async function main() {
    const tester = new ProductionReadyTester();
    
    try {
        await tester.initialize();
        
        // Run comprehensive production tests
        await tester.testConnectionStability();
        await tester.testUserExperience();
        await tester.testMentorPerformance();
        await tester.calculateProductionReadiness();
        
        // Generate production readiness report
        await tester.generateProductionReport();
        
    } catch (error) {
        console.error('❌ Production test failed:', error.message);
    } finally {
        await tester.cleanup();
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = ProductionReadyTester;
