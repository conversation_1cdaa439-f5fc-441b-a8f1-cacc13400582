#!/usr/bin/env node

/**
 * Live Demo of Ultra-Powerful Session-Focused Dashboard
 * Demonstrates real-time bidirectional message tracking
 */

import WebSocket from 'ws';

class SessionFocusLiveDemo {
  constructor() {
    this.monitorSocket = null;
    this.gameSocket = null;
    this.demoSessionId = `demo-session-${Date.now()}`;
    this.messageCount = 0;
  }

  async runDemo() {
    console.log('🎬 LIVE DEMO: Ultra-Powerful Session-Focused Dashboard');
    console.log('====================================================');
    console.log('Demonstrating real-time bidirectional message tracking...\n');
    
    await this.connectToMonitor();
    await this.sleep(1000);
    await this.setupSessionFocus();
    await this.sleep(1000);
    await this.connectToGame();
    await this.sleep(1000);
    await this.demonstrateBidirectionalFlow();
    
    console.log('\n🎯 Demo completed! Check the dashboard to see the chronological timeline in action.');
  }

  async connectToMonitor() {
    console.log('🔌 Connecting to WebSocket Monitor Dashboard...');
    
    return new Promise((resolve) => {
      this.monitorSocket = new WebSocket('ws://localhost:8000/ws/connection-monitor/');
      
      this.monitorSocket.on('open', () => {
        console.log('✅ Connected to monitor dashboard');
        resolve();
      });

      this.monitorSocket.on('message', (data) => {
        try {
          const message = JSON.parse(data);
          this.handleMonitorMessage(message);
        } catch (error) {
          // Non-JSON messages are normal
        }
      });

      this.monitorSocket.on('error', (error) => {
        console.log(`❌ Monitor connection error: ${error.message}`);
        resolve();
      });
    });
  }

  async setupSessionFocus() {
    console.log('🎯 Setting up session focus...');
    
    if (this.monitorSocket && this.monitorSocket.readyState === WebSocket.OPEN) {
      // Start session monitoring
      this.monitorSocket.send(JSON.stringify({
        type: 'start_session_monitoring'
      }));
      
      await this.sleep(500);
      
      // Focus on our demo session
      this.monitorSocket.send(JSON.stringify({
        type: 'focus_session',
        session_id: this.demoSessionId
      }));
      
      console.log(`✅ Focused on session: ${this.demoSessionId.substring(0, 12)}...`);
    }
  }

  async connectToGame() {
    console.log('🎮 Connecting to Game WebSocket (simulating frontend)...');
    
    return new Promise((resolve) => {
      this.gameSocket = new WebSocket('ws://localhost:8000/ws/game/');
      
      this.gameSocket.on('open', () => {
        console.log('✅ Connected to game WebSocket');
        resolve();
      });

      this.gameSocket.on('message', (data) => {
        try {
          const message = JSON.parse(data);
          this.handleGameMessage(message);
        } catch (error) {
          // Non-JSON messages are normal
          console.log('📨 Backend → Frontend: Non-JSON response (normal)');
        }
      });

      this.gameSocket.on('error', (error) => {
        console.log(`❌ Game connection error: ${error.message}`);
        resolve();
      });
    });
  }

  async demonstrateBidirectionalFlow() {
    console.log('\n🔄 Demonstrating Bidirectional Message Flow...');
    console.log('===============================================');
    
    // Simulate a realistic conversation flow
    const conversationFlow = [
      {
        delay: 1000,
        action: 'frontend',
        message: {
          type: 'chat_message',
          content: 'Hello! I\'m feeling bored today.',
          user_profile_id: '2',
          session_id: this.demoSessionId
        },
        description: 'User sends initial message'
      },
      {
        delay: 2000,
        action: 'frontend',
        message: {
          type: 'chat_message',
          content: 'Can you help me find something fun to do?',
          user_profile_id: '2',
          session_id: this.demoSessionId
        },
        description: 'User asks for help'
      },
      {
        delay: 3000,
        action: 'frontend',
        message: {
          type: 'wheel_request',
          content: 'Generate a wheel for me',
          user_profile_id: '2',
          session_id: this.demoSessionId
        },
        description: 'User requests wheel generation'
      },
      {
        delay: 1000,
        action: 'monitor',
        message: {
          type: 'debug_info',
          data: {
            source: 'MentorService',
            level: 'info',
            message: 'Processing wheel generation request',
            session_id: this.demoSessionId
          },
          session_id: this.demoSessionId
        },
        description: 'Backend debug information'
      },
      {
        delay: 2000,
        action: 'monitor',
        message: {
          type: 'workflow_status',
          data: {
            workflow_id: 'wheel_generation',
            status: 'running',
            session_id: this.demoSessionId
          },
          session_id: this.demoSessionId
        },
        description: 'Workflow status update'
      },
      {
        delay: 3000,
        action: 'monitor',
        message: {
          type: 'wheel_data',
          wheel: {
            items: [
              {title: 'Go for a walk in the park', percentage: 25},
              {title: 'Read a good book', percentage: 20},
              {title: 'Call a friend', percentage: 15},
              {title: 'Try a new recipe', percentage: 20},
              {title: 'Watch a documentary', percentage: 20}
            ]
          },
          session_id: this.demoSessionId
        },
        description: 'Wheel data generated and sent to frontend'
      },
      {
        delay: 1000,
        action: 'monitor',
        message: {
          type: 'workflow_status',
          data: {
            workflow_id: 'wheel_generation',
            status: 'completed',
            session_id: this.demoSessionId
          },
          session_id: this.demoSessionId
        },
        description: 'Workflow completion status'
      }
    ];

    // Execute the conversation flow
    for (const step of conversationFlow) {
      await this.sleep(step.delay);
      
      this.messageCount++;
      console.log(`\n📍 Step ${this.messageCount}: ${step.description}`);
      
      if (step.action === 'frontend' && this.gameSocket) {
        console.log(`📤 Frontend → Backend: ${step.message.type}`);
        console.log(`   Content: "${step.message.content || 'Structured data'}"`);
        this.gameSocket.send(JSON.stringify(step.message));
      } else if (step.action === 'monitor' && this.monitorSocket) {
        console.log(`📤 Backend → Frontend: ${step.message.type}`);
        console.log(`   Content: "${this.getMessagePreview(step.message)}"`);
        this.monitorSocket.send(JSON.stringify(step.message));
      }
      
      console.log(`   ⏰ Timestamp: ${new Date().toLocaleTimeString()}.${Date.now() % 1000}`);
      console.log(`   📊 Session: ${this.demoSessionId.substring(0, 8)}...`);
    }

    await this.sleep(2000);
    
    console.log('\n🎉 BIDIRECTIONAL FLOW DEMONSTRATION COMPLETE!');
    console.log('============================================');
    console.log('');
    console.log('🎯 What you should see in the dashboard:');
    console.log('  1. Session Focus Panel showing the demo session');
    console.log('  2. Chronological timeline with all messages in order');
    console.log('  3. Frontend messages (blue, →) on the right side');
    console.log('  4. Backend messages (pink, ←) on the left side');
    console.log('  5. System messages (gray, ↕) in the center');
    console.log('  6. Precise timestamps for each message');
    console.log('  7. Message statistics updating in real-time');
    console.log('  8. Expandable JSON content for each message');
    console.log('');
    console.log('🔍 Try these features:');
    console.log('  • Click on any message to expand JSON details');
    console.log('  • Use the filter buttons (All, Frontend, Backend, System, Errors)');
    console.log('  • Search for specific content in the search box');
    console.log('  • Export individual messages or the entire session');
    console.log('  • Toggle auto-scroll on/off');
    console.log('');
    console.log('📊 Session Statistics should show:');
    console.log(`  • Frontend Messages: 3 (chat messages + wheel request)`);
    console.log(`  • Backend Messages: 3 (debug info + workflow status + wheel data)`);
    console.log(`  • System Messages: 1 (workflow completion)`);
    console.log(`  • Total Messages: 7`);
    console.log(`  • Data Transferred: ~2-3 KB`);
  }

  handleMonitorMessage(message) {
    if (message.type === 'session_monitoring_started') {
      console.log('✅ Session monitoring activated');
    } else if (message.type === 'session_focused') {
      console.log('✅ Session focus confirmed');
    } else if (message.session_id === this.demoSessionId) {
      console.log(`📨 Dashboard received: ${message.type} for focused session`);
    }
  }

  handleGameMessage(message) {
    if (message.session_id === this.demoSessionId) {
      console.log(`📨 Game received: ${message.type} for demo session`);
    }
  }

  getMessagePreview(message) {
    switch (message.type) {
      case 'debug_info':
        return message.data?.message || 'Debug information';
      case 'workflow_status':
        return `${message.data?.workflow_id} is ${message.data?.status}`;
      case 'wheel_data':
        const itemCount = message.wheel?.items?.length || 0;
        return `Wheel with ${itemCount} activities generated`;
      default:
        return JSON.stringify(message).substring(0, 50) + '...';
    }
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async cleanup() {
    console.log('\n🧹 Cleaning up connections...');
    
    if (this.monitorSocket) {
      this.monitorSocket.close();
    }
    
    if (this.gameSocket) {
      this.gameSocket.close();
    }
    
    console.log('✅ Demo cleanup complete');
  }
}

// Run the live demo
const demo = new SessionFocusLiveDemo();

demo.runDemo()
  .then(() => demo.cleanup())
  .catch(error => {
    console.error('❌ Demo failed:', error);
    demo.cleanup();
  });

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Demo interrupted by user');
  demo.cleanup();
  process.exit(0);
});
