#!/usr/bin/env node

/**
 * Comprehensive Connection Dashboard Test Runner
 * 
 * Orchestrates all connection dashboard tests including:
 * - Dashboard functionality testing
 * - Client simulation for realistic load
 * - Real-time monitoring verification
 * - Performance and stress testing
 */

import ConnectionDashboardTester from './connection-dashboard-tester.js';
import WebSocketClientSimulator from './websocket-client-simulator.js';
import { CONFIG } from './config.js';
import fs from 'fs';
import path from 'path';

class ConnectionDashboardTestRunner {
  constructor() {
    this.testResults = [];
    this.logFile = path.join(CONFIG.logging.logDirectory, `dashboard-test-runner-${Date.now()}.log`);
    
    // Ensure log directory exists
    if (!fs.existsSync(CONFIG.logging.logDirectory)) {
      fs.mkdirSync(CONFIG.logging.logDirectory, { recursive: true });
    }
  }

  log(level, message, data = null) {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level,
      message,
      data
    };
    
    console.log(`[${timestamp}] ${level.toUpperCase()}: ${message}`);
    if (data) {
      console.log('  Data:', JSON.stringify(data, null, 2));
    }
    
    // Save to file
    if (CONFIG.logging.saveToFile) {
      fs.appendFileSync(this.logFile, JSON.stringify(logEntry) + '\n');
    }
  }

  async runAllTests() {
    this.log('info', '🚀 Starting Comprehensive Connection Dashboard Tests');
    
    try {
      // Phase 1: Basic functionality tests
      await this.runBasicFunctionalityTests();
      
      // Phase 2: Load testing with simulated clients
      await this.runLoadTests();
      
      // Phase 3: Stress testing
      await this.runStressTests();
      
      // Phase 4: Error scenario testing
      await this.runErrorScenarioTests();
      
      // Phase 5: Performance validation
      await this.runPerformanceTests();
      
      this.log('info', '✅ All Connection Dashboard Tests Completed Successfully');
      this.generateFinalReport();
      
    } catch (error) {
      this.log('error', '❌ Connection Dashboard Test Suite Failed', { error: error.message });
      throw error;
    }
  }

  async runBasicFunctionalityTests() {
    this.log('info', '🔧 Phase 1: Basic Functionality Tests');
    
    const tester = new ConnectionDashboardTester();
    
    try {
      await tester.runTests();
      this.testResults.push({
        phase: 'basic_functionality',
        status: 'passed',
        message: 'Basic functionality tests completed successfully'
      });
    } catch (error) {
      this.testResults.push({
        phase: 'basic_functionality',
        status: 'failed',
        message: `Basic functionality tests failed: ${error.message}`
      });
      throw error;
    }
  }

  async runLoadTests() {
    this.log('info', '📈 Phase 2: Load Testing with Simulated Clients');
    
    const loadTestScenarios = [
      { numClients: 5, duration: 30000, name: 'Light Load' },
      { numClients: 15, duration: 45000, name: 'Medium Load' },
      { numClients: 25, duration: 60000, name: 'Heavy Load' }
    ];
    
    for (const scenario of loadTestScenarios) {
      this.log('info', `🎯 Running ${scenario.name} Test`, scenario);
      
      try {
        await this.runLoadTestScenario(scenario);
        this.testResults.push({
          phase: 'load_testing',
          scenario: scenario.name,
          status: 'passed',
          message: `${scenario.name} test completed successfully`
        });
      } catch (error) {
        this.testResults.push({
          phase: 'load_testing',
          scenario: scenario.name,
          status: 'failed',
          message: `${scenario.name} test failed: ${error.message}`
        });
        this.log('error', `❌ ${scenario.name} test failed`, { error: error.message });
      }
      
      // Wait between scenarios
      await this.sleep(10000);
    }
  }

  async runLoadTestScenario(scenario) {
    return new Promise((resolve, reject) => {
      const simulator = new WebSocketClientSimulator({
        numClients: scenario.numClients,
        duration: scenario.duration,
        messageInterval: 3000,
        errorRate: 0.05
      });
      
      // Start simulation
      simulator.startSimulation();
      
      // Monitor for completion
      const checkCompletion = setInterval(() => {
        if (!simulator.isRunning) {
          clearInterval(checkCompletion);
          
          // Validate results
          if (simulator.stats.connectionsCreated >= scenario.numClients * 0.8) {
            resolve();
          } else {
            reject(new Error(`Insufficient connections created: ${simulator.stats.connectionsCreated}/${scenario.numClients}`));
          }
        }
      }, 1000);
      
      // Timeout protection
      setTimeout(() => {
        clearInterval(checkCompletion);
        simulator.stopSimulation();
        reject(new Error(`Load test scenario timeout: ${scenario.name}`));
      }, scenario.duration + 30000);
    });
  }

  async runStressTests() {
    this.log('info', '💪 Phase 3: Stress Testing');
    
    // Stress test with many connections and high message rate
    const stressScenario = {
      numClients: 50,
      duration: 30000,
      messageInterval: 1000,
      errorRate: 0.15
    };
    
    this.log('info', '🔥 Running High-Stress Test', stressScenario);
    
    try {
      await this.runLoadTestScenario(stressScenario);
      this.testResults.push({
        phase: 'stress_testing',
        status: 'passed',
        message: 'Stress testing completed successfully'
      });
    } catch (error) {
      this.testResults.push({
        phase: 'stress_testing',
        status: 'failed',
        message: `Stress testing failed: ${error.message}`
      });
      this.log('error', '❌ Stress testing failed', { error: error.message });
    }
  }

  async runErrorScenarioTests() {
    this.log('info', '🚨 Phase 4: Error Scenario Testing');
    
    // Test various error scenarios
    const errorScenarios = [
      {
        name: 'High Error Rate',
        config: { numClients: 10, duration: 20000, errorRate: 0.5 }
      },
      {
        name: 'Rapid Disconnections',
        config: { numClients: 15, duration: 15000, errorRate: 0.3 }
      }
    ];
    
    for (const scenario of errorScenarios) {
      this.log('info', `🎯 Running ${scenario.name} Test`);
      
      try {
        await this.runLoadTestScenario(scenario.config);
        this.testResults.push({
          phase: 'error_scenarios',
          scenario: scenario.name,
          status: 'passed',
          message: `${scenario.name} test completed`
        });
      } catch (error) {
        // Error scenarios are expected to have some failures
        this.testResults.push({
          phase: 'error_scenarios',
          scenario: scenario.name,
          status: 'partial',
          message: `${scenario.name} test completed with expected errors`
        });
      }
      
      await this.sleep(5000);
    }
  }

  async runPerformanceTests() {
    this.log('info', '⚡ Phase 5: Performance Validation');
    
    // Test dashboard responsiveness under load
    const performanceTest = {
      numClients: 20,
      duration: 30000,
      messageInterval: 2000,
      errorRate: 0.1
    };
    
    this.log('info', '📊 Testing Dashboard Performance Under Load');
    
    // Start load simulation
    const simulator = new WebSocketClientSimulator(performanceTest);
    simulator.startSimulation();
    
    // Test dashboard responsiveness
    const dashboardTester = new ConnectionDashboardTester();
    
    try {
      // Wait for load to build up
      await this.sleep(10000);
      
      // Test dashboard under load
      await dashboardTester.testRealTimeMonitoring();
      await dashboardTester.testSystemHealthMonitoring();
      
      this.testResults.push({
        phase: 'performance_testing',
        status: 'passed',
        message: 'Performance testing completed successfully'
      });
      
    } catch (error) {
      this.testResults.push({
        phase: 'performance_testing',
        status: 'failed',
        message: `Performance testing failed: ${error.message}`
      });
    } finally {
      simulator.stopSimulation();
      await dashboardTester.cleanup();
    }
  }

  async sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  generateFinalReport() {
    const report = {
      timestamp: new Date().toISOString(),
      testSuite: 'Connection Dashboard Comprehensive Tests',
      phases: this.testResults.reduce((acc, result) => {
        if (!acc[result.phase]) {
          acc[result.phase] = [];
        }
        acc[result.phase].push(result);
        return acc;
      }, {}),
      summary: {
        totalTests: this.testResults.length,
        passed: this.testResults.filter(t => t.status === 'passed').length,
        failed: this.testResults.filter(t => t.status === 'failed').length,
        partial: this.testResults.filter(t => t.status === 'partial').length
      },
      results: this.testResults
    };
    
    const reportFile = path.join(CONFIG.logging.logDirectory, `dashboard-comprehensive-report-${Date.now()}.json`);
    fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
    
    this.log('info', '📋 Comprehensive Test Report Generated', {
      file: reportFile,
      summary: report.summary
    });
    
    console.log('\n🎯 COMPREHENSIVE TEST SUMMARY:');
    console.log(`✅ Passed: ${report.summary.passed}`);
    console.log(`❌ Failed: ${report.summary.failed}`);
    console.log(`⚠️  Partial: ${report.summary.partial}`);
    console.log(`📊 Total: ${report.summary.totalTests}`);
    console.log(`📁 Report saved to: ${reportFile}`);
    
    // Print phase breakdown
    console.log('\n📋 PHASE BREAKDOWN:');
    for (const [phase, results] of Object.entries(report.phases)) {
      const passed = results.filter(r => r.status === 'passed').length;
      const total = results.length;
      console.log(`  ${phase}: ${passed}/${total} passed`);
    }
  }
}

// CLI interface
if (import.meta.url === `file://${process.argv[1]}`) {
  console.log('🎮 Connection Dashboard Comprehensive Test Runner');
  console.log('This will run all connection dashboard tests including load and stress testing.');
  console.log('Make sure the backend is running before starting tests.\n');
  
  const runner = new ConnectionDashboardTestRunner();
  
  // Handle graceful shutdown
  process.on('SIGINT', () => {
    console.log('\n🛑 Received SIGINT, stopping tests...');
    process.exit(0);
  });
  
  runner.runAllTests().catch((error) => {
    console.error('❌ Test suite failed:', error.message);
    process.exit(1);
  });
}

export default ConnectionDashboardTestRunner;
