/**
 * Test Domain Fix - Validate that domain and color fixes are working
 * 
 * This script tests the complete domain and color fix by:
 * 1. Triggering a new wheel generation
 * 2. Capturing the wheel data
 * 3. Validating domains and colors
 */

console.log('🔧 Testing Domain and Color Fix');
console.log('===============================');

// Test configuration
const EXPECTED_DOMAINS = ['physical', 'productive_practical', 'emotional'];
const EXPECTED_COLORS = {
    'physical': '#E74C3C',
    'productive_practical': '#27AE60', 
    'emotional': '#9B59B6'
};

let testResults = {
    wheelGenerated: false,
    domainsCorrect: false,
    colorsCorrect: false,
    wheelData: null,
    errors: []
};

/**
 * Main test execution
 */
async function testDomainFix() {
    try {
        console.log('🚀 Starting domain fix validation...');
        
        // Step 1: Wait for page to load
        await waitForPageLoad();
        
        // Step 2: Monitor wheel data
        setupWheelDataMonitoring();
        
        // Step 3: Trigger new wheel generation
        await triggerWheelGeneration();
        
        // Step 4: Wait for wheel data
        await waitForWheelData();
        
        // Step 5: Validate domains and colors
        validateDomainsAndColors();
        
        // Step 6: Generate report
        generateTestReport();
        
    } catch (error) {
        console.error('❌ Domain fix test failed:', error);
        testResults.errors.push(error.message);
        generateTestReport();
    }
}

/**
 * Wait for page to load
 */
async function waitForPageLoad() {
    console.log('⏳ Waiting for page to load...');
    
    return new Promise((resolve) => {
        if (document.readyState === 'complete') {
            resolve();
        } else {
            window.addEventListener('load', resolve);
        }
    });
}

/**
 * Set up monitoring for wheel data
 */
function setupWheelDataMonitoring() {
    console.log('🔍 Setting up wheel data monitoring...');
    
    // Monitor console logs for wheel data
    const originalLog = console.log;
    console.log = function(...args) {
        // Check for wheel data in console output
        const message = args.join(' ');
        if (message.includes('Full wheel data:') || message.includes('wheel_data')) {
            try {
                // Try to extract wheel data from the message
                const dataMatch = message.match(/\{.*\}/);
                if (dataMatch) {
                    const wheelData = JSON.parse(dataMatch[0]);
                    if (wheelData.wheel && wheelData.wheel.items) {
                        testResults.wheelData = wheelData.wheel;
                        testResults.wheelGenerated = true;
                        console.log('✅ Wheel data captured!');
                    }
                }
            } catch (e) {
                // Ignore parsing errors
            }
        }
        
        originalLog.apply(console, args);
    };
    
    // Also monitor WebSocket messages if available
    const appShell = document.querySelector('app-shell');
    if (appShell && appShell.websocketManager) {
        const wsManager = appShell.websocketManager;
        const originalOnMessage = wsManager.onMessage;
        
        wsManager.onMessage = function(type, handler) {
            if (type === 'wheel_data') {
                return originalOnMessage.call(this, type, (data) => {
                    console.log('🎡 WebSocket wheel data received:', data);
                    if (data.wheel && data.wheel.items) {
                        testResults.wheelData = data.wheel;
                        testResults.wheelGenerated = true;
                    }
                    handler(data);
                });
            }
            return originalOnMessage.call(this, type, handler);
        };
    }
}

/**
 * Trigger wheel generation
 */
async function triggerWheelGeneration() {
    console.log('🎲 Triggering wheel generation...');
    
    const appShell = document.querySelector('app-shell');
    if (!appShell) {
        throw new Error('App-shell not found');
    }
    
    // Set high energy for testing
    appShell.energyLevel = 100;
    appShell.timeAvailable = 10;
    
    // Try to trigger wheel generation
    const generateButton = appShell.shadowRoot?.querySelector('.generate-wheel-button');
    if (generateButton) {
        generateButton.click();
        console.log('✅ Wheel generation triggered via button');
    } else if (appShell.generateWheel) {
        await appShell.generateWheel();
        console.log('✅ Wheel generation triggered via method');
    } else {
        throw new Error('Could not trigger wheel generation');
    }
}

/**
 * Wait for wheel data to be received
 */
async function waitForWheelData() {
    console.log('⏳ Waiting for wheel data...');
    
    const startTime = Date.now();
    const timeout = 60000; // 60 seconds
    
    return new Promise((resolve, reject) => {
        const checkInterval = setInterval(() => {
            const elapsed = Date.now() - startTime;
            
            if (testResults.wheelGenerated && testResults.wheelData) {
                clearInterval(checkInterval);
                console.log(`✅ Wheel data received in ${elapsed}ms`);
                resolve();
            } else if (elapsed > timeout) {
                clearInterval(checkInterval);
                reject(new Error('Timeout waiting for wheel data'));
            } else {
                console.log(`⏳ Still waiting... (${elapsed}ms elapsed)`);
            }
        }, 2000);
    });
}

/**
 * Validate domains and colors
 */
function validateDomainsAndColors() {
    console.log('🔍 Validating domains and colors...');
    
    if (!testResults.wheelData || !testResults.wheelData.items) {
        testResults.errors.push('No wheel data available for validation');
        return;
    }
    
    const items = testResults.wheelData.items;
    console.log(`📊 Analyzing ${items.length} wheel items...`);
    
    let domainsCorrect = true;
    let colorsCorrect = true;
    const foundDomains = new Set();
    
    items.forEach((item, index) => {
        const domain = item.domain;
        const color = item.color;
        const name = item.name;
        
        foundDomains.add(domain);
        
        console.log(`${index + 1}. ${name}`);
        console.log(`   Domain: ${domain}`);
        console.log(`   Color: ${color}`);
        
        // Check if domain is one of the expected good domains
        if (!EXPECTED_DOMAINS.includes(domain) && domain !== 'general') {
            console.log(`   ⚠️ Unexpected domain: ${domain}`);
        }
        
        // Check if color is not the gray fallback
        if (color === '#95A5A6') {
            console.log(`   ❌ Gray fallback color detected`);
            colorsCorrect = false;
        } else {
            console.log(`   ✅ Non-gray color assigned`);
        }
        
        // Check for specific domain-color matches
        if (EXPECTED_COLORS[domain] && color !== EXPECTED_COLORS[domain]) {
            console.log(`   ⚠️ Color mismatch: expected ${EXPECTED_COLORS[domain]}, got ${color}`);
        }
    });
    
    // Check for problematic domains
    const problematicDomains = ['intel_language', 'intel_strategic'];
    const hasProblematicDomains = problematicDomains.some(domain => foundDomains.has(domain));
    
    if (hasProblematicDomains) {
        console.log('❌ Found problematic domains (intel_language, intel_strategic)');
        domainsCorrect = false;
    } else {
        console.log('✅ No problematic domains found');
    }
    
    // Check domain diversity
    const domainDiversity = foundDomains.size;
    if (domainDiversity >= 3) {
        console.log(`✅ Good domain diversity: ${domainDiversity} domains`);
    } else {
        console.log(`⚠️ Low domain diversity: ${domainDiversity} domains`);
    }
    
    testResults.domainsCorrect = domainsCorrect && !hasProblematicDomains;
    testResults.colorsCorrect = colorsCorrect;
}

/**
 * Generate test report
 */
function generateTestReport() {
    console.log('\n📋 DOMAIN FIX TEST REPORT');
    console.log('=========================');
    
    const results = testResults;
    
    console.log(`✅ Wheel Generated: ${results.wheelGenerated}`);
    console.log(`✅ Domains Correct: ${results.domainsCorrect}`);
    console.log(`✅ Colors Correct: ${results.colorsCorrect}`);
    
    if (results.wheelData && results.wheelData.items) {
        const items = results.wheelData.items;
        const domains = new Set(items.map(item => item.domain));
        const grayColors = items.filter(item => item.color === '#95A5A6').length;
        
        console.log(`\n📊 Statistics:`);
        console.log(`   Total Items: ${items.length}`);
        console.log(`   Unique Domains: ${domains.size}`);
        console.log(`   Gray Colors: ${grayColors}/${items.length}`);
        console.log(`   Domains: ${Array.from(domains).join(', ')}`);
    }
    
    if (results.errors.length > 0) {
        console.log(`\n❌ Errors:`);
        results.errors.forEach((error, i) => {
            console.log(`   ${i + 1}. ${error}`);
        });
    }
    
    // Overall result
    const overallSuccess = results.wheelGenerated && results.domainsCorrect && results.colorsCorrect;
    
    console.log(`\n🎯 OVERALL RESULT:`);
    console.log(`${overallSuccess ? '✅ DOMAIN FIX SUCCESSFUL' : '❌ DOMAIN FIX NEEDS WORK'}`);
    
    return overallSuccess;
}

// Auto-run test when script is loaded
if (typeof window !== 'undefined') {
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(testDomainFix, 2000);
        });
    } else {
        setTimeout(testDomainFix, 2000);
    }
}

// Export for manual execution
if (typeof window !== 'undefined') {
    window.testDomainFix = testDomainFix;
}
