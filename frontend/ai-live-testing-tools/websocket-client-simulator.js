#!/usr/bin/env node

/**
 * WebSocket Client Simulator
 * 
 * Simulates realistic WebSocket client behavior to test the connection dashboard:
 * - Multiple concurrent connections
 * - Various message patterns
 * - Connection lifecycle events
 * - Error scenarios
 */

import WebSocket from 'ws';
import { CONFIG } from './config.js';
import fs from 'fs';
import path from 'path';

class WebSocketClientSimulator {
  constructor(options = {}) {
    this.options = {
      numClients: options.numClients || 10,
      duration: options.duration || 60000, // 1 minute
      messageInterval: options.messageInterval || 5000, // 5 seconds
      errorRate: options.errorRate || 0.1, // 10% error rate
      ...options
    };
    
    this.clients = [];
    this.isRunning = false;
    this.stats = {
      connectionsCreated: 0,
      messagesSet: 0,
      errorsEncountered: 0,
      connectionsActive: 0
    };
    
    this.logFile = path.join(CONFIG.logging.logDirectory, `client-simulator-${Date.now()}.log`);
    
    // Ensure log directory exists
    if (!fs.existsSync(CONFIG.logging.logDirectory)) {
      fs.mkdirSync(CONFIG.logging.logDirectory, { recursive: true });
    }
  }

  log(level, message, data = null) {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level,
      message,
      data,
      stats: this.stats
    };
    
    console.log(`[${timestamp}] ${level.toUpperCase()}: ${message}`);
    if (data) {
      console.log('  Data:', JSON.stringify(data, null, 2));
    }
    
    // Save to file
    if (CONFIG.logging.saveToFile) {
      fs.appendFileSync(this.logFile, JSON.stringify(logEntry) + '\n');
    }
  }

  async startSimulation() {
    this.log('info', `🚀 Starting WebSocket Client Simulation`, {
      numClients: this.options.numClients,
      duration: this.options.duration,
      messageInterval: this.options.messageInterval
    });
    
    this.isRunning = true;
    
    // Create clients gradually
    for (let i = 0; i < this.options.numClients; i++) {
      setTimeout(() => {
        if (this.isRunning) {
          this.createClient(i + 1);
        }
      }, i * 1000); // Stagger connections by 1 second
    }
    
    // Run for specified duration
    setTimeout(() => {
      this.stopSimulation();
    }, this.options.duration);
    
    // Log stats periodically
    this.statsInterval = setInterval(() => {
      this.logStats();
    }, 10000); // Every 10 seconds
  }

  createClient(clientId) {
    const client = {
      id: clientId,
      userId: `sim-user-${clientId}`,
      socket: null,
      connected: false,
      messageCount: 0,
      lastActivity: Date.now(),
      messageTimer: null
    };
    
    try {
      client.socket = new WebSocket(CONFIG.backend.websocketUrl);
      
      client.socket.on('open', () => {
        client.connected = true;
        this.stats.connectionsCreated++;
        this.stats.connectionsActive++;
        
        this.log('debug', `🔗 Client ${clientId} connected`);
        
        // Send initial identification message
        this.sendMessage(client, {
          type: 'chat_message',
          content: {
            message: `Hello! I'm simulated user ${clientId}`,
            user_profile_id: client.userId,
            timestamp: new Date().toISOString()
          }
        });
        
        // Start sending periodic messages
        this.startMessageLoop(client);
      });
      
      client.socket.on('message', (data) => {
        try {
          const message = JSON.parse(data);
          client.lastActivity = Date.now();
          
          this.log('debug', `📨 Client ${clientId} received: ${message.type}`);
          
          // Simulate user interactions based on message type
          this.handleIncomingMessage(client, message);
          
        } catch (error) {
          this.log('error', `Failed to parse message for client ${clientId}`, { error: error.message });
          this.stats.errorsEncountered++;
        }
      });
      
      client.socket.on('error', (error) => {
        this.log('error', `❌ Client ${clientId} error`, { error: error.message });
        this.stats.errorsEncountered++;
        this.cleanupClient(client);
      });
      
      client.socket.on('close', (code, reason) => {
        this.log('debug', `🔌 Client ${clientId} disconnected`, { code, reason: reason.toString() });
        this.cleanupClient(client);
      });
      
      this.clients.push(client);
      
    } catch (error) {
      this.log('error', `Failed to create client ${clientId}`, { error: error.message });
      this.stats.errorsEncountered++;
    }
  }

  startMessageLoop(client) {
    const sendRandomMessage = () => {
      if (!client.connected || !this.isRunning) {
        return;
      }
      
      // Randomly decide message type and content
      const messageTypes = ['chat', 'wheel_request', 'activity_feedback'];
      const messageType = messageTypes[Math.floor(Math.random() * messageTypes.length)];
      
      let message;
      
      switch (messageType) {
        case 'chat':
          message = {
            type: 'chat_message',
            content: {
              message: this.generateRandomChatMessage(),
              user_profile_id: client.userId,
              timestamp: new Date().toISOString()
            }
          };
          break;
          
        case 'wheel_request':
          message = {
            type: 'chat_message',
            content: {
              message: "I'd like some activity suggestions please",
              user_profile_id: client.userId,
              timestamp: new Date().toISOString()
            }
          };
          break;
          
        case 'activity_feedback':
          message = {
            type: 'chat_message',
            content: {
              message: "I completed that activity and it was great!",
              user_profile_id: client.userId,
              timestamp: new Date().toISOString()
            }
          };
          break;
      }
      
      this.sendMessage(client, message);
      
      // Schedule next message with some randomness
      const nextInterval = this.options.messageInterval + (Math.random() * 5000 - 2500);
      client.messageTimer = setTimeout(sendRandomMessage, nextInterval);
    };
    
    // Start the message loop
    client.messageTimer = setTimeout(sendRandomMessage, this.options.messageInterval);
  }

  generateRandomChatMessage() {
    const messages = [
      "How are you doing today?",
      "I'm feeling creative and want to try something new",
      "What activities would you recommend for a rainy day?",
      "I have about 30 minutes free, what should I do?",
      "I'm looking for something relaxing",
      "Can you suggest something challenging?",
      "I want to learn a new skill",
      "What's a good outdoor activity?",
      "I'm feeling social today",
      "I need something to boost my energy"
    ];
    
    return messages[Math.floor(Math.random() * messages.length)];
  }

  handleIncomingMessage(client, message) {
    switch (message.type) {
      case 'wheel_data':
        // Simulate user spinning the wheel after a delay
        setTimeout(() => {
          if (client.connected && message.wheel && message.wheel.items && message.wheel.items.length > 0) {
            const randomItem = message.wheel.items[Math.floor(Math.random() * message.wheel.items.length)];
            this.sendMessage(client, {
              type: 'spin_result',
              content: {
                activity_tailored_id: randomItem.activity_tailored_id,
                name: randomItem.name,
                description: randomItem.description,
                user_profile_id: client.userId
              }
            });
          }
        }, 2000 + Math.random() * 3000); // 2-5 seconds delay
        break;
        
      case 'system_message':
        // Log system messages
        this.log('debug', `System message for client ${client.id}: ${message.content}`);
        break;
        
      case 'error':
        // Handle errors
        this.log('warn', `Error message for client ${client.id}: ${message.content}`);
        this.stats.errorsEncountered++;
        break;
    }
  }

  sendMessage(client, message) {
    if (!client.connected || !client.socket) {
      return;
    }
    
    try {
      // Simulate occasional errors
      if (Math.random() < this.options.errorRate) {
        // Send malformed message
        client.socket.send('{"invalid": json}');
        this.stats.errorsEncountered++;
        return;
      }
      
      client.socket.send(JSON.stringify(message));
      client.messageCount++;
      client.lastActivity = Date.now();
      this.stats.messagesSet++;
      
      this.log('debug', `📤 Client ${client.id} sent: ${message.type}`);
      
    } catch (error) {
      this.log('error', `Failed to send message for client ${client.id}`, { error: error.message });
      this.stats.errorsEncountered++;
    }
  }

  cleanupClient(client) {
    if (client.messageTimer) {
      clearTimeout(client.messageTimer);
    }
    
    if (client.connected) {
      this.stats.connectionsActive--;
      client.connected = false;
    }
    
    if (client.socket) {
      try {
        client.socket.close();
      } catch (error) {
        // Ignore close errors
      }
    }
  }

  logStats() {
    this.log('info', '📊 Simulation Statistics', {
      ...this.stats,
      activeClients: this.clients.filter(c => c.connected).length,
      totalClients: this.clients.length
    });
  }

  stopSimulation() {
    this.log('info', '🛑 Stopping WebSocket Client Simulation');
    this.isRunning = false;
    
    // Clear stats interval
    if (this.statsInterval) {
      clearInterval(this.statsInterval);
    }
    
    // Cleanup all clients
    for (const client of this.clients) {
      this.cleanupClient(client);
    }
    
    // Generate final report
    this.generateReport();
  }

  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      options: this.options,
      finalStats: this.stats,
      duration: this.options.duration,
      clients: this.clients.map(c => ({
        id: c.id,
        userId: c.userId,
        messageCount: c.messageCount,
        connected: c.connected
      }))
    };
    
    const reportFile = path.join(CONFIG.logging.logDirectory, `client-simulation-report-${Date.now()}.json`);
    fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
    
    this.log('info', '📋 Simulation Report Generated', {
      file: reportFile,
      summary: `${this.stats.connectionsCreated} connections, ${this.stats.messagesSet} messages, ${this.stats.errorsEncountered} errors`
    });
    
    console.log('\n📊 SIMULATION SUMMARY:');
    console.log(`🔗 Connections Created: ${this.stats.connectionsCreated}`);
    console.log(`📨 Messages Sent: ${this.stats.messagesSet}`);
    console.log(`❌ Errors Encountered: ${this.stats.errorsEncountered}`);
    console.log(`📁 Report saved to: ${reportFile}`);
  }
}

// CLI interface
if (import.meta.url === `file://${process.argv[1]}`) {
  const args = process.argv.slice(2);
  const options = {};
  
  // Parse command line arguments
  for (let i = 0; i < args.length; i += 2) {
    const key = args[i].replace('--', '');
    const value = args[i + 1];
    
    if (value !== undefined) {
      options[key] = isNaN(value) ? value : Number(value);
    }
  }
  
  console.log('🎮 WebSocket Client Simulator');
  console.log('Usage: node websocket-client-simulator.js [options]');
  console.log('Options:');
  console.log('  --numClients <number>     Number of clients to simulate (default: 10)');
  console.log('  --duration <ms>           Simulation duration in milliseconds (default: 60000)');
  console.log('  --messageInterval <ms>    Interval between messages (default: 5000)');
  console.log('  --errorRate <float>       Error rate 0-1 (default: 0.1)');
  console.log('');
  
  const simulator = new WebSocketClientSimulator(options);
  
  // Handle graceful shutdown
  process.on('SIGINT', () => {
    console.log('\n🛑 Received SIGINT, stopping simulation...');
    simulator.stopSimulation();
    process.exit(0);
  });
  
  simulator.startSimulation();
}

export default WebSocketClientSimulator;
