/**
 * Test wheel component debug page
 */

const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

async function testWheelDebug(port = 3000) {
    console.log('🎡 Testing wheel debug page...');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000
    });
    
    const page = await browser.newPage();
    
    // Set up console logging
    page.on('console', msg => {
        console.log(`[BROWSER] ${msg.text()}`);
    });
    
    // Set up error logging
    page.on('pageerror', error => {
        console.error(`[PAGE ERROR] ${error.message}`);
    });
    
    try {
        console.log(`📱 Navigating to http://localhost:${port}/debug/simple-wheel-test.html`);
        await page.goto(`http://localhost:${port}/debug/simple-wheel-test.html`);
        
        // Wait for page to load
        await page.waitForTimeout(3000);
        
        // Take initial screenshot
        const screenshotDir = path.join(__dirname, 'wheel-screenshots');
        if (!fs.existsSync(screenshotDir)) {
            fs.mkdirSync(screenshotDir, { recursive: true });
        }
        
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        await page.screenshot({ 
            path: path.join(screenshotDir, `${timestamp}_01_initial.png`),
            fullPage: true 
        });
        console.log('📸 Initial screenshot taken');
        
        // Wait for component to load
        await page.waitForSelector('game-wheel', { timeout: 10000 });
        console.log('✅ Wheel component found');
        
        // Click load simple data button
        console.log('🔄 Clicking load simple data button...');
        await page.click('#loadBtn');
        await page.waitForTimeout(2000);
        
        // Take screenshot after loading data
        await page.screenshot({ 
            path: path.join(screenshotDir, `${timestamp}_02_data_loaded.png`),
            fullPage: true 
        });
        console.log('📸 Data loaded screenshot taken');
        
        // Check if spin button is enabled
        const spinButtonEnabled = await page.isEnabled('#spinBtn');
        console.log(`🎯 Spin button enabled: ${spinButtonEnabled}`);
        
        if (spinButtonEnabled) {
            console.log('🎡 Clicking spin button...');
            await page.click('#spinBtn');
            await page.waitForTimeout(1000);
            
            // Take screenshot during spin
            await page.screenshot({ 
                path: path.join(screenshotDir, `${timestamp}_03_spinning.png`),
                fullPage: true 
            });
            console.log('📸 Spinning screenshot taken');
            
            // Wait for spin to complete
            await page.waitForTimeout(15000);
            
            // Take final screenshot
            await page.screenshot({ 
                path: path.join(screenshotDir, `${timestamp}_04_final.png`),
                fullPage: true 
            });
            console.log('📸 Final screenshot taken');
        }
        
        // Click debug button to get state
        console.log('🔍 Clicking debug button...');
        await page.click('#debugBtn');
        await page.waitForTimeout(1000);
        
        // Get log content
        const logContent = await page.textContent('#log');
        console.log('📋 Log content:');
        console.log(logContent);
        
        console.log('✅ Test completed successfully');
        
    } catch (error) {
        console.error('❌ Test failed:', error);
    } finally {
        await browser.close();
    }
}

// Get port from command line argument
const port = process.argv[2] || 3000;
testWheelDebug(port);
