#!/usr/bin/env node

/**
 * Simple wheel data debug test
 * 
 * This test opens the frontend and captures the exact wheel data being received
 * to identify the source of the ID mapping issue.
 */

const puppeteer = require('puppeteer');

async function debugWheelData(port = 3002) {
    const browser = await puppeteer.launch({ 
        headless: false,
        defaultViewport: { width: 1400, height: 900 },
        args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // Capture all console logs
    page.on('console', msg => {
        const text = msg.text();
        console.log(`🖥️  ${text}`);
    });
    
    try {
        console.log(`🚀 Opening frontend on port ${port}`);
        await page.goto(`http://localhost:${port}`, { waitUntil: 'networkidle0' });
        
        // Wait for app to load
        await page.waitForSelector('app-shell', { timeout: 10000 });
        console.log('✅ App loaded successfully');
        
        // Wait for user to manually generate a wheel and test removal
        console.log('');
        console.log('🎯 MANUAL TEST INSTRUCTIONS:');
        console.log('1. Generate a wheel using the interface');
        console.log('2. Try to remove an activity from the wheel');
        console.log('3. Check the console output for wheel data structure');
        console.log('4. Press Ctrl+C when done');
        console.log('');
        
        // Keep the browser open for manual testing
        await new Promise(() => {}); // Wait indefinitely
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
    } finally {
        await browser.close();
    }
}

// Run the test
if (require.main === module) {
    const port = process.argv[2] || 3002;
    debugWheelData(parseInt(port))
        .catch(error => {
            console.error('💥 Test execution failed:', error);
            process.exit(1);
        });
}

module.exports = { debugWheelData };
