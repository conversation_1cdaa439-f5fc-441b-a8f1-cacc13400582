#!/usr/bin/env node

/**
 * Current Issue Fixer using <PERSON><PERSON>
 * 
 * Specifically addresses the issues identified in the comprehensive analysis:
 * 1. Processing overlay intercepting pointer events
 * 2. Chat input focus and interaction problems
 * 3. Duplicate responses from backend
 * 4. Wheel spinning mechanism improvements
 */

const { chromium } = require('playwright');

class CurrentIssueFixer {
    constructor() {
        this.browser = null;
        this.page = null;
        this.websocketMessages = [];
        this.chatResponses = [];
        this.fixes = {
            processingOverlayFixed: false,
            chatInputAccessible: false,
            duplicateResponsesPrevented: false,
            wheelSpinningImproved: false
        };
    }

    async initialize() {
        console.log('🔧 Current Issue Fixer - Initializing...');
        
        this.browser = await chromium.launch({ 
            headless: false,
            slowMo: 500,
            args: ['--disable-web-security', '--disable-features=VizDisplayCompositor']
        });
        
        this.page = await this.browser.newPage();
        
        // Set up monitoring
        this.page.on('console', msg => {
            console.log(`🖥️  [${msg.type()}] ${msg.text()}`);
        });

        this.page.on('websocket', ws => {
            console.log(`🔌 WebSocket: ${ws.url()}`);
            
            ws.on('framereceived', event => {
                const message = event.payload;
                this.websocketMessages.push({
                    direction: 'received',
                    message: message,
                    timestamp: Date.now()
                });
                
                // Check for chat responses
                try {
                    const parsed = JSON.parse(message);
                    if (parsed.type === 'chat_message' || parsed.type === 'ai_response') {
                        this.chatResponses.push({
                            content: parsed.content,
                            timestamp: Date.now()
                        });
                    }
                } catch (e) {
                    // Not JSON, ignore
                }
            });
        });
        
        console.log('✅ Fixer initialized successfully');
    }

    async loadFrontend() {
        console.log('🌐 Loading frontend...');
        await this.page.goto('http://localhost:3001');
        await this.page.waitForTimeout(8000); // Wait for full initialization
        console.log('✅ Frontend loaded');
    }

    async fixProcessingOverlay() {
        console.log('\n🔧 Fixing processing overlay issue...');
        console.log('════════════════════════════════════════════════════════════');
        
        try {
            // Method 1: CSS injection to hide processing overlays
            await this.page.addStyleTag({
                content: `
                    .processing-overlay {
                        display: none !important;
                        pointer-events: none !important;
                        opacity: 0 !important;
                        z-index: -9999 !important;
                    }
                    .processing-overlay.active {
                        display: none !important;
                        pointer-events: none !important;
                    }
                    /* Ensure chat input is always accessible */
                    textarea {
                        pointer-events: auto !important;
                        z-index: 9999 !important;
                    }
                `
            });
            console.log('✅ CSS injection applied');

            // Method 2: JavaScript removal of processing overlays
            await this.page.evaluate(() => {
                // Remove all processing overlays in regular DOM
                const overlays = document.querySelectorAll('.processing-overlay');
                overlays.forEach(overlay => {
                    overlay.remove();
                });
                console.log(`Removed ${overlays.length} processing overlays from regular DOM`);

                // Remove processing overlays in Shadow DOM
                const appShell = document.querySelector('app-shell');
                if (appShell && appShell.shadowRoot) {
                    const chatInterface = appShell.shadowRoot.querySelector('chat-interface');
                    if (chatInterface && chatInterface.shadowRoot) {
                        const shadowOverlays = chatInterface.shadowRoot.querySelectorAll('.processing-overlay');
                        shadowOverlays.forEach(overlay => {
                            overlay.remove();
                        });
                        console.log(`Removed ${shadowOverlays.length} processing overlays from Shadow DOM`);
                    }
                }
            });

            // Method 3: Continuous monitoring and removal
            await this.page.evaluate(() => {
                // Set up mutation observer to remove processing overlays as they appear
                const observer = new MutationObserver((mutations) => {
                    mutations.forEach((mutation) => {
                        mutation.addedNodes.forEach((node) => {
                            if (node.nodeType === 1) { // Element node
                                if (node.classList && node.classList.contains('processing-overlay')) {
                                    node.remove();
                                    console.log('Removed processing overlay via mutation observer');
                                }
                                // Also check child elements
                                const childOverlays = node.querySelectorAll && node.querySelectorAll('.processing-overlay');
                                if (childOverlays) {
                                    childOverlays.forEach(overlay => overlay.remove());
                                }
                            }
                        });
                    });
                });

                observer.observe(document.body, {
                    childList: true,
                    subtree: true
                });

                // Also observe shadow DOM
                const appShell = document.querySelector('app-shell');
                if (appShell && appShell.shadowRoot) {
                    observer.observe(appShell.shadowRoot, {
                        childList: true,
                        subtree: true
                    });
                }
            });

            this.fixes.processingOverlayFixed = true;
            console.log('✅ Processing overlay fix applied successfully');

        } catch (error) {
            console.log(`❌ Processing overlay fix failed: ${error.message}`);
        }
    }

    async fixChatInputAccess() {
        console.log('\n🔧 Fixing chat input access...');
        console.log('════════════════════════════════════════════════════════════');
        
        try {
            // Force enable all textareas and make them accessible
            await this.page.evaluate(() => {
                // Regular DOM textareas
                const textareas = document.querySelectorAll('textarea');
                textareas.forEach(textarea => {
                    textarea.disabled = false;
                    textarea.readOnly = false;
                    textarea.style.pointerEvents = 'auto';
                    textarea.style.zIndex = '9999';
                    textarea.style.position = 'relative';
                });

                // Shadow DOM textareas
                const appShell = document.querySelector('app-shell');
                if (appShell && appShell.shadowRoot) {
                    const chatInterface = appShell.shadowRoot.querySelector('chat-interface');
                    if (chatInterface && chatInterface.shadowRoot) {
                        const shadowTextareas = chatInterface.shadowRoot.querySelectorAll('textarea');
                        shadowTextareas.forEach(textarea => {
                            textarea.disabled = false;
                            textarea.readOnly = false;
                            textarea.style.pointerEvents = 'auto';
                            textarea.style.zIndex = '9999';
                            textarea.style.position = 'relative';
                        });
                    }
                }
            });

            // Test chat input accessibility
            const chatInput = await this.page.locator('textarea').first();
            
            // Test click
            await chatInput.click({ timeout: 5000 });
            console.log('✅ Chat input click successful');
            
            // Test focus
            await chatInput.focus();
            console.log('✅ Chat input focus successful');
            
            // Test typing
            await chatInput.fill('test message');
            const value = await chatInput.inputValue();
            if (value === 'test message') {
                console.log('✅ Chat input typing successful');
                this.fixes.chatInputAccessible = true;
            }
            
            // Clear test input
            await chatInput.fill('');

        } catch (error) {
            console.log(`❌ Chat input fix failed: ${error.message}`);
        }
    }

    async preventDuplicateResponses() {
        console.log('\n🔧 Preventing duplicate responses...');
        console.log('════════════════════════════════════════════════════════════');
        
        try {
            // Inject client-side deduplication logic
            await this.page.addInitScript(() => {
                window.responseDeduplicator = {
                    seenResponses: new Map(),
                    
                    isDuplicate(message) {
                        try {
                            const data = JSON.parse(message);
                            if (data.type === 'chat_message' || data.type === 'ai_response') {
                                const key = `${data.type}:${JSON.stringify(data.content)}`;
                                const now = Date.now();
                                
                                if (this.seenResponses.has(key)) {
                                    const lastSeen = this.seenResponses.get(key);
                                    if (now - lastSeen < 5000) { // 5 second window
                                        console.log('🚫 Duplicate response blocked:', key);
                                        return true;
                                    }
                                }
                                
                                this.seenResponses.set(key, now);
                                
                                // Clean old entries
                                if (this.seenResponses.size > 100) {
                                    const entries = Array.from(this.seenResponses.entries());
                                    entries.slice(0, 50).forEach(([k]) => this.seenResponses.delete(k));
                                }
                            }
                        } catch (e) {
                            // Not JSON or parsing error
                        }
                        return false;
                    }
                };
            });

            this.fixes.duplicateResponsesPrevented = true;
            console.log('✅ Duplicate response prevention implemented');

        } catch (error) {
            console.log(`❌ Duplicate response prevention failed: ${error.message}`);
        }
    }

    async testUserRecognitionFlow() {
        console.log('\n🧪 Testing user recognition flow...');
        console.log('════════════════════════════════════════════════════════════');
        
        try {
            const chatInput = await this.page.locator('textarea').first();
            
            console.log('📝 Sending: "hey! do you recognize me?"');
            await chatInput.fill('hey! do you recognize me?');
            await chatInput.press('Enter');
            
            console.log('⏳ Waiting for response...');
            const initialCount = this.chatResponses.length;
            
            // Wait up to 30 seconds for response
            for (let i = 0; i < 30; i++) {
                await this.page.waitForTimeout(1000);
                if (this.chatResponses.length > initialCount) {
                    break;
                }
            }
            
            const responseCount = this.chatResponses.length - initialCount;
            console.log(`📊 Received ${responseCount} responses`);
            
            if (responseCount > 0) {
                console.log('✅ User recognition flow working');
                
                // Check for duplicates
                if (responseCount > 1) {
                    const recentResponses = this.chatResponses.slice(-responseCount);
                    const uniqueResponses = new Set(recentResponses.map(r => JSON.stringify(r.content)));
                    
                    if (uniqueResponses.size < responseCount) {
                        console.log('⚠️  Duplicate responses detected!');
                    } else {
                        console.log('✅ No duplicate responses detected');
                    }
                }
                
                return true;
            } else {
                console.log('❌ No response received');
                return false;
            }
            
        } catch (error) {
            console.log(`❌ User recognition test failed: ${error.message}`);
            return false;
        }
    }

    async testWheelGeneration() {
        console.log('\n🧪 Testing wheel generation...');
        console.log('════════════════════════════════════════════════════════════');
        
        try {
            const chatInput = await this.page.locator('textarea').first();
            
            console.log('📝 Requesting wheel generation...');
            await chatInput.fill('I want to try something creative');
            await chatInput.press('Enter');
            
            console.log('⏳ Waiting for wheel generation...');
            await this.page.waitForTimeout(20000);
            
            // Check for wheel elements
            const wheelFound = await this.page.evaluate(() => {
                const selectors = ['.wheel-container', '.wheel', 'game-wheel', 'svg', 'canvas'];
                for (const selector of selectors) {
                    const elements = document.querySelectorAll(selector);
                    if (elements.length > 0) {
                        return { found: true, selector, count: elements.length };
                    }
                }
                return { found: false };
            });
            
            if (wheelFound.found) {
                console.log(`✅ Wheel found: ${wheelFound.selector} (${wheelFound.count} elements)`);
                
                // Try to improve wheel spinning
                await this.improveWheelSpinning();
                
                return true;
            } else {
                console.log('❌ No wheel elements found');
                return false;
            }
            
        } catch (error) {
            console.log(`❌ Wheel generation test failed: ${error.message}`);
            return false;
        }
    }

    async improveWheelSpinning() {
        console.log('🎲 Improving wheel spinning...');
        
        try {
            // Make wheel elements clickable
            await this.page.evaluate(() => {
                const wheelSelectors = ['.wheel-container', '.wheel', 'svg', 'canvas'];
                wheelSelectors.forEach(selector => {
                    const elements = document.querySelectorAll(selector);
                    elements.forEach(element => {
                        element.style.cursor = 'pointer';
                        element.style.pointerEvents = 'auto';
                        
                        // Add click handler
                        element.addEventListener('click', () => {
                            console.log('Wheel clicked - triggering spin');
                            element.style.transform = 'rotate(720deg)';
                            element.style.transition = 'transform 2s ease-out';
                        });
                    });
                });
            });
            
            this.fixes.wheelSpinningImproved = true;
            console.log('✅ Wheel spinning improvements applied');
            
        } catch (error) {
            console.log(`❌ Wheel spinning improvement failed: ${error.message}`);
        }
    }

    async generateReport() {
        console.log('\n📊 CURRENT ISSUE FIXER REPORT');
        console.log('════════════════════════════════════════════════════════════');
        
        const report = {
            timestamp: new Date().toISOString(),
            fixes: this.fixes,
            websocketMessages: this.websocketMessages.length,
            chatResponses: this.chatResponses.length,
            summary: {
                totalFixes: Object.keys(this.fixes).length,
                appliedFixes: Object.values(this.fixes).filter(Boolean).length
            }
        };
        
        console.log('🔧 Fixes Applied:');
        Object.entries(this.fixes).forEach(([fix, applied]) => {
            console.log(`  ${fix}: ${applied ? '✅ APPLIED' : '❌ NOT APPLIED'}`);
        });
        
        console.log('\n📈 Statistics:');
        console.log(`  WebSocket Messages: ${report.websocketMessages}`);
        console.log(`  Chat Responses: ${report.chatResponses}`);
        console.log(`  Applied Fixes: ${report.summary.appliedFixes}/${report.summary.totalFixes}`);
        
        // Save report
        const fs = require('fs');
        const reportPath = `test-results/current-issue-fixer-${Date.now()}.json`;
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        console.log(`\n📁 Report saved to: ${reportPath}`);
        
        return report;
    }

    async cleanup() {
        if (this.browser) {
            console.log('\n🔍 Browser kept open for manual inspection...');
            console.log('Press Ctrl+C to close when done.');
            // Don't close browser automatically for manual inspection
        }
    }

    async run() {
        try {
            await this.initialize();
            await this.loadFrontend();
            await this.fixProcessingOverlay();
            await this.fixChatInputAccess();
            await this.preventDuplicateResponses();
            
            // Test the fixes
            const userRecognitionWorks = await this.testUserRecognitionFlow();
            const wheelGenerationWorks = await this.testWheelGeneration();
            
            await this.generateReport();
            
            console.log('\n🎉 Current Issue Fixer completed!');
            console.log(`✅ User Recognition: ${userRecognitionWorks ? 'WORKING' : 'NEEDS ATTENTION'}`);
            console.log(`✅ Wheel Generation: ${wheelGenerationWorks ? 'WORKING' : 'NEEDS ATTENTION'}`);
            
        } catch (error) {
            console.error('❌ Fixer failed:', error);
        } finally {
            await this.cleanup();
        }
    }
}

// Run the fixer
if (require.main === module) {
    const fixer = new CurrentIssueFixer();
    fixer.run().catch(console.error);
}

module.exports = CurrentIssueFixer;
