#!/usr/bin/env node

/**
 * Single Connection Test
 * 
 * Simple test to create one connection and check for debug messages
 */

import WebSocket from 'ws';

console.log('🔗 Single Connection Test');
console.log('=========================\n');

console.log('🔌 Creating single test connection...');

const socket = new WebSocket('ws://localhost:8000/ws/game/');

socket.on('open', () => {
  console.log('✅ Connected to /ws/game/');
  
  // Send a simple message
  const message = {
    type: 'chat_message',
    content: {
      message: 'Single connection test',
      user_profile_id: '2', // Use real user ID (PhiPhi - fake user for testing)
      timestamp: new Date().toISOString()
    }
  };
  
  socket.send(JSON.stringify(message));
  console.log('📤 Sent test message');
  
  // Keep connection alive for a bit
  setTimeout(() => {
    console.log('🔌 Closing connection...');
    socket.close();
  }, 5000);
});

socket.on('error', (error) => {
  console.log(`❌ Connection error: ${error.message}`);
});

socket.on('message', (data) => {
  try {
    const message = JSON.parse(data);
    console.log(`📨 Received: ${message.type}`);
  } catch (error) {
    console.log(`📨 Received raw: ${data.toString()}`);
  }
});

socket.on('close', (code, reason) => {
  console.log(`🔌 Connection closed: ${code} ${reason}`);
  console.log('✅ Test complete - check backend logs for debug messages');
  process.exit(0);
});

// Auto-exit after 10 seconds
setTimeout(() => {
  console.log('⏰ Test timeout');
  process.exit(1);
}, 10000);
