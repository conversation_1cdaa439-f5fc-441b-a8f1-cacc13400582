#!/usr/bin/env node

/**
 * Session Monitoring Issue Diagnostic Test
 * 
 * This test specifically identifies why session monitoring hangs and 
 * why the real-time message inspector is empty.
 */

const WebSocket = require('ws');

class SessionMonitoringDiagnostic {
    constructor() {
        this.adminSocket = null;
        this.gameSocket = null;
        this.testSessionId = null;
        this.receivedMessages = [];
        this.errors = [];
        this.startTime = Date.now();
    }

    async runDiagnostic() {
        console.log('🔍 Session Monitoring Issue Diagnostic');
        console.log('=====================================\n');

        try {
            await this.connectToAdmin();
            await this.connectToGame();
            await this.testSessionMonitoringFlow();
            await this.testMessageInspectorFlow();
            await this.generateDiagnosticReport();
        } catch (error) {
            console.error('❌ Diagnostic failed:', error.message);
            this.errors.push(`Diagnostic failure: ${error.message}`);
        } finally {
            await this.cleanup();
        }
    }

    async connectToAdmin() {
        console.log('🔬 Step 1: Connecting to Admin Dashboard');
        
        return new Promise((resolve) => {
            this.adminSocket = new WebSocket('ws://localhost:8000/ws/connection-monitor/');
            
            this.adminSocket.on('open', () => {
                console.log('✅ Admin dashboard connected');
                resolve();
            });
            
            this.adminSocket.on('message', (data) => {
                try {
                    const message = JSON.parse(data);
                    this.receivedMessages.push({
                        source: 'admin',
                        timestamp: Date.now(),
                        message: message
                    });
                    console.log(`📨 Admin received: ${message.type}`);
                } catch (error) {
                    console.log(`📨 Admin received non-JSON: ${data.toString().substring(0, 50)}...`);
                }
            });
            
            this.adminSocket.on('error', (error) => {
                console.log('❌ Admin connection failed:', error.message);
                this.errors.push(`Admin connection failed: ${error.message}`);
                resolve();
            });
            
            setTimeout(() => {
                if (this.adminSocket.readyState !== WebSocket.OPEN) {
                    this.errors.push('Admin connection timeout');
                }
                resolve();
            }, 5000);
        });
    }

    async connectToGame() {
        console.log('\n🔬 Step 2: Connecting to Game WebSocket');
        
        return new Promise((resolve) => {
            this.gameSocket = new WebSocket('ws://localhost:8000/ws/game/');
            
            this.gameSocket.on('open', () => {
                console.log('✅ Game WebSocket connected');
                resolve();
            });
            
            this.gameSocket.on('message', (data) => {
                try {
                    const message = JSON.parse(data);
                    this.receivedMessages.push({
                        source: 'game',
                        timestamp: Date.now(),
                        message: message
                    });
                    console.log(`📨 Game received: ${message.type}`);
                } catch (error) {
                    console.log(`📨 Game received non-JSON: ${data.toString().substring(0, 50)}...`);
                }
            });
            
            this.gameSocket.on('error', (error) => {
                console.log('❌ Game connection failed:', error.message);
                this.errors.push(`Game connection failed: ${error.message}`);
                resolve();
            });
            
            setTimeout(() => {
                if (this.gameSocket.readyState !== WebSocket.OPEN) {
                    this.errors.push('Game connection timeout');
                }
                resolve();
            }, 5000);
        });
    }

    async testSessionMonitoringFlow() {
        console.log('\n🔬 Step 3: Testing Session Monitoring Flow');
        
        if (!this.adminSocket || this.adminSocket.readyState !== WebSocket.OPEN) {
            this.errors.push('Admin socket not available for session monitoring test');
            return;
        }

        // Get connections first to find a session ID
        console.log('📤 Requesting connection list...');
        this.adminSocket.send(JSON.stringify({type: 'get_connections'}));
        
        await this.sleep(1000);
        
        // Find a session ID from the connections
        const connectionMessages = this.receivedMessages.filter(msg => 
            msg.source === 'admin' && msg.message.type === 'connection_data'
        );
        
        if (connectionMessages.length > 0 && connectionMessages[0].message.data.length > 0) {
            this.testSessionId = connectionMessages[0].message.data[0].session_id;
            console.log(`✅ Found session ID: ${this.testSessionId.substring(0, 12)}...`);
        } else {
            console.log('❌ No active sessions found for monitoring');
            this.errors.push('No active sessions available for monitoring test');
            return;
        }

        // Start session monitoring
        console.log('📤 Starting session monitoring...');
        this.adminSocket.send(JSON.stringify({type: 'start_session_monitoring'}));
        
        await this.sleep(500);
        
        // Focus on the session
        console.log(`📤 Focusing on session: ${this.testSessionId.substring(0, 12)}...`);
        this.adminSocket.send(JSON.stringify({
            type: 'focus_session',
            session_id: this.testSessionId
        }));
        
        await this.sleep(1000);
        
        // Check if we received session monitoring responses
        const sessionResponses = this.receivedMessages.filter(msg => 
            msg.source === 'admin' && (
                msg.message.type === 'session_monitoring_started' ||
                msg.message.type === 'session_focused' ||
                msg.message.type === 'session_history'
            )
        );
        
        if (sessionResponses.length > 0) {
            console.log(`✅ Session monitoring responses received: ${sessionResponses.length}`);
            sessionResponses.forEach(response => {
                console.log(`   - ${response.message.type}: ${response.message.message || 'OK'}`);
            });
        } else {
            console.log('❌ No session monitoring responses received');
            this.errors.push('Session monitoring not responding');
        }
        
        // Now send a test message to the game socket to see if it appears in monitoring
        console.log('📤 Sending test message to game socket...');
        if (this.gameSocket && this.gameSocket.readyState === WebSocket.OPEN) {
            this.gameSocket.send(JSON.stringify({
                type: 'chat_message',
                content: {
                    message: 'Test message for session monitoring',
                    user_profile_id: 'session-monitor-test-user'
                }
            }));
            
            await this.sleep(2000);
            
            // Check if the message appeared in admin monitoring
            const monitoredMessages = this.receivedMessages.filter(msg => 
                msg.source === 'admin' && 
                msg.timestamp > Date.now() - 3000 && // Last 3 seconds
                msg.message.session_id === this.testSessionId
            );
            
            if (monitoredMessages.length > 0) {
                console.log(`✅ Session messages being monitored: ${monitoredMessages.length}`);
            } else {
                console.log('❌ Session messages NOT appearing in monitoring');
                this.errors.push('Session messages not being broadcasted to monitoring dashboard');
            }
        }
    }

    async testMessageInspectorFlow() {
        console.log('\n🔬 Step 4: Testing Message Inspector Flow');
        
        if (!this.adminSocket || this.adminSocket.readyState !== WebSocket.OPEN) {
            this.errors.push('Admin socket not available for message inspector test');
            return;
        }

        // Start message monitoring
        console.log('📤 Starting message monitoring...');
        this.adminSocket.send(JSON.stringify({type: 'start_message_monitoring'}));
        
        await this.sleep(500);
        
        // Send multiple test messages
        console.log('📤 Sending test messages for inspector...');
        const testMessages = [
            {
                type: 'chat_message',
                content: {
                    message: 'Inspector test message 1',
                    user_profile_id: 'inspector-test-1'
                }
            },
            {
                type: 'chat_message',
                content: {
                    message: 'Inspector test message 2',
                    user_profile_id: 'inspector-test-2'
                }
            }
        ];
        
        if (this.gameSocket && this.gameSocket.readyState === WebSocket.OPEN) {
            for (let i = 0; i < testMessages.length; i++) {
                this.gameSocket.send(JSON.stringify(testMessages[i]));
                await this.sleep(500);
            }
            
            await this.sleep(2000);
            
            // Check if messages appeared in the inspector
            const inspectorMessages = this.receivedMessages.filter(msg => 
                msg.source === 'admin' && 
                msg.timestamp > Date.now() - 4000 && // Last 4 seconds
                (msg.message.type === 'message_flow' || 
                 msg.message.type === 'chat_message' ||
                 msg.message.type === 'debug_info')
            );
            
            if (inspectorMessages.length > 0) {
                console.log(`✅ Message inspector receiving messages: ${inspectorMessages.length}`);
            } else {
                console.log('❌ Message inspector NOT receiving messages');
                this.errors.push('Message inspector not receiving real-time messages');
            }
        }
    }

    async generateDiagnosticReport() {
        const duration = ((Date.now() - this.startTime) / 1000).toFixed(1);
        
        console.log('\n🎯 SESSION MONITORING DIAGNOSTIC REPORT');
        console.log('=======================================');
        console.log(`⏱️ Test duration: ${duration} seconds`);
        console.log(`📨 Total messages received: ${this.receivedMessages.length}`);
        console.log(`❌ Issues identified: ${this.errors.length}`);
        
        // Message breakdown by source
        const adminMessages = this.receivedMessages.filter(msg => msg.source === 'admin');
        const gameMessages = this.receivedMessages.filter(msg => msg.source === 'game');
        
        console.log('\n📊 MESSAGE BREAKDOWN:');
        console.log(`  Admin dashboard: ${adminMessages.length} messages`);
        console.log(`  Game WebSocket: ${gameMessages.length} messages`);
        
        // Admin message types
        if (adminMessages.length > 0) {
            console.log('\n📨 ADMIN MESSAGE TYPES:');
            const adminTypes = {};
            adminMessages.forEach(msg => {
                const type = msg.message.type || 'unknown';
                adminTypes[type] = (adminTypes[type] || 0) + 1;
            });
            Object.entries(adminTypes).forEach(([type, count]) => {
                console.log(`  - ${type}: ${count}`);
            });
        }
        
        // Issues found
        if (this.errors.length > 0) {
            console.log('\n❌ ISSUES IDENTIFIED:');
            this.errors.forEach((error, index) => {
                console.log(`  ${index + 1}. ${error}`);
            });
        }
        
        // Root cause analysis
        console.log('\n🔍 ROOT CAUSE ANALYSIS:');
        
        const hasSessionMonitoringStarted = adminMessages.some(msg => 
            msg.message.type === 'session_monitoring_started'
        );
        const hasSessionFocused = adminMessages.some(msg => 
            msg.message.type === 'session_focused'
        );
        const hasSessionMessages = adminMessages.some(msg => 
            msg.message.session_id && msg.message.session_id === this.testSessionId
        );
        const hasMessageMonitoringStarted = adminMessages.some(msg => 
            msg.message.type === 'message_monitoring_started'
        );
        const hasMessageFlow = adminMessages.some(msg => 
            msg.message.type === 'message_flow'
        );
        
        console.log(`  Session monitoring starts: ${hasSessionMonitoringStarted ? '✅' : '❌'}`);
        console.log(`  Session focus works: ${hasSessionFocused ? '✅' : '❌'}`);
        console.log(`  Session messages broadcasted: ${hasSessionMessages ? '✅' : '❌'}`);
        console.log(`  Message monitoring starts: ${hasMessageMonitoringStarted ? '✅' : '❌'}`);
        console.log(`  Message flow events: ${hasMessageFlow ? '✅' : '❌'}`);
        
        // Diagnosis
        console.log('\n🎯 DIAGNOSIS:');
        if (!hasSessionMessages && hasSessionFocused) {
            console.log('❌ CRITICAL: Session monitoring starts but messages are not broadcasted');
            console.log('   → UserSessionConsumer is not calling ConnectionMonitorConsumer.broadcast_session_message()');
            console.log('   → Integration between game consumer and monitoring consumer is incomplete');
        }
        
        if (!hasMessageFlow && hasMessageMonitoringStarted) {
            console.log('❌ CRITICAL: Message monitoring starts but no message flow events');
            console.log('   → Message inspector not receiving real-time message events');
            console.log('   → Message broadcasting mechanism is not implemented');
        }
        
        if (hasSessionMonitoringStarted && hasSessionFocused) {
            console.log('✅ Session monitoring backend is working correctly');
        }
        
        // Recommendations
        console.log('\n🔧 RECOMMENDED FIXES:');
        console.log('1. Modify UserSessionConsumer._send_to_client_and_admin() to call');
        console.log('   ConnectionMonitorConsumer.broadcast_session_message()');
        console.log('2. Add message flow broadcasting to the message inspector');
        console.log('3. Ensure all outgoing messages are broadcasted to monitoring dashboard');
        console.log('4. Test the integration with real message flow');
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async cleanup() {
        console.log('\n🧹 Cleaning up diagnostic environment...');
        
        if (this.adminSocket && this.adminSocket.readyState === WebSocket.OPEN) {
            this.adminSocket.close();
        }
        
        if (this.gameSocket && this.gameSocket.readyState === WebSocket.OPEN) {
            this.gameSocket.close();
        }
        
        console.log('✅ Diagnostic cleanup complete');
    }
}

// Run the diagnostic
if (require.main === module) {
    const diagnostic = new SessionMonitoringDiagnostic();
    diagnostic.runDiagnostic().catch(console.error);
}

module.exports = SessionMonitoringDiagnostic;
