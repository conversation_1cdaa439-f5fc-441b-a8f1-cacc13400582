/**
 * Enhanced Wheel Component Validation Test
 * Tests the complete lifecycle of the enhanced wheel component
 */

const { chromium } = require('playwright');

class EnhancedWheelTest {
    constructor() {
        this.browser = null;
        this.page = null;
        this.results = {
            componentLoaded: false,
            wheelVisible: false,
            wheelDataSet: false,
            spinExecuted: false,
            winnerDetected: false,
            confidenceScored: false,
            enhancedDetection: false,
            errors: []
        };
    }

    async initialize() {
        console.log('🎡 Enhanced Wheel Component Test - Initializing...');
        this.browser = await chromium.launch({ 
            headless: false,
            slowMo: 500
        });
        this.page = await this.browser.newPage();
        
        // Listen for console logs
        this.page.on('console', msg => {
            const text = msg.text();
            if (text.includes('[WHEEL]') || text.includes('Enhanced winner detection')) {
                console.log(`🔍 WHEEL LOG: ${text}`);
            }
        });
        
        console.log('✅ Test initialized successfully');
    }

    async testProductionApp() {
        console.log('🌐 Testing production app at http://localhost:3000');
        
        try {
            await this.page.goto('http://localhost:3000', { waitUntil: 'networkidle' });
            await this.page.waitForTimeout(2000);
            
            // Check if wheel component is loaded
            const wheelElement = await this.page.locator('game-wheel').first();
            const wheelExists = await wheelElement.count() > 0;
            this.results.componentLoaded = wheelExists;
            console.log(`✅ Wheel component loaded: ${wheelExists}`);
            
            if (!wheelExists) {
                throw new Error('Wheel component not found in production app');
            }
            
            // Wait for connection
            await this.page.waitForSelector('.status-indicator.connected', { timeout: 10000 });
            console.log('✅ Connected to backend');
            
            // Send a message to generate wheel
            const textarea = this.page.locator('textarea[placeholder*="message"]');
            await textarea.fill("I'm restless and need to do things physical. I have 2 hours. Make me a wheel");
            await textarea.press('Enter');
            console.log('✅ Message sent for wheel generation');
            
            // Wait for wheel to appear (up to 2 minutes)
            console.log('⏳ Waiting for wheel generation...');
            let wheelGenerated = false;
            for (let i = 0; i < 24; i++) {
                await this.page.waitForTimeout(5000);
                
                // Check if wheel is visible and has data
                const wheelVisible = await this.page.evaluate(() => {
                    const wheel = document.querySelector('game-wheel');
                    if (!wheel) return false;
                    
                    // Check if wheel has data and is visible
                    const hasData = wheel.wheelData && wheel.wheelData.segments && wheel.wheelData.segments.length > 0;
                    const isVisible = !wheel.invisible && wheel.offsetParent !== null;
                    
                    return hasData && isVisible;
                });
                
                if (wheelVisible) {
                    wheelGenerated = true;
                    this.results.wheelVisible = true;
                    this.results.wheelDataSet = true;
                    console.log('✅ Wheel generated and visible!');
                    break;
                }
                
                console.log(`   Attempt ${i + 1}/24: Still waiting...`);
            }
            
            if (!wheelGenerated) {
                throw new Error('Wheel was not generated within 2 minutes');
            }
            
            // Test wheel spinning
            await this.testWheelSpinning();
            
        } catch (error) {
            console.error('❌ Production app test failed:', error.message);
            this.results.errors.push(`Production app: ${error.message}`);
        }
    }

    async testDebugEnvironment() {
        console.log('🌐 Testing debug environment at http://localhost:3005');
        
        try {
            await this.page.goto('http://localhost:3005/wheel-debug.html', { waitUntil: 'networkidle' });
            await this.page.waitForTimeout(1000);
            
            // Check if wheel component is loaded
            const wheelElement = await this.page.locator('#debugWheel').first();
            const wheelExists = await wheelElement.count() > 0;
            this.results.componentLoaded = wheelExists;
            console.log(`✅ Debug wheel component loaded: ${wheelExists}`);
            
            if (!wheelExists) {
                throw new Error('Debug wheel component not found');
            }
            
            // Load mock wheel items
            const loadButton = this.page.locator('#loadWheelBtn');
            await loadButton.click();
            console.log('✅ Mock wheel items loaded');
            
            // Wait for wheel to become visible
            await this.page.waitForTimeout(2000);
            
            // Check if wheel is visible
            const wheelVisible = await this.page.evaluate(() => {
                const wheel = document.getElementById('debugWheel');
                return wheel && !wheel.invisible;
            });
            
            this.results.wheelVisible = wheelVisible;
            this.results.wheelDataSet = wheelVisible;
            console.log(`✅ Wheel visible: ${wheelVisible}`);
            
            if (wheelVisible) {
                // Test wheel spinning
                await this.testWheelSpinning();
            }
            
        } catch (error) {
            console.error('❌ Debug environment test failed:', error.message);
            this.results.errors.push(`Debug environment: ${error.message}`);
        }
    }

    async testWheelSpinning() {
        console.log('🎡 Testing wheel spinning functionality...');
        
        try {
            // Find and click spin button
            const spinButton = await this.page.locator('button:has-text("Spin")').first();
            const spinButtonExists = await spinButton.count() > 0;
            
            if (!spinButtonExists) {
                // Try to spin programmatically
                console.log('🔧 No spin button found, spinning programmatically...');
                await this.page.evaluate(() => {
                    const wheel = document.querySelector('game-wheel') || document.getElementById('debugWheel');
                    if (wheel && typeof wheel.spin === 'function') {
                        wheel.spin();
                        return true;
                    }
                    return false;
                });
            } else {
                await spinButton.click();
            }
            
            console.log('✅ Wheel spin initiated');
            this.results.spinExecuted = true;
            
            // Wait for spin to complete (up to 20 seconds)
            console.log('⏳ Waiting for spin to complete...');
            let spinCompleted = false;
            let winnerInfo = null;
            
            // Listen for wheel events
            await this.page.evaluate(() => {
                window.wheelEvents = [];
                const wheel = document.querySelector('game-wheel') || document.getElementById('debugWheel');
                if (wheel) {
                    wheel.addEventListener('wheel-spin-complete', (event) => {
                        window.wheelEvents.push({
                            type: 'spin-complete',
                            detail: event.detail,
                            timestamp: Date.now()
                        });
                    });
                    
                    wheel.addEventListener('wheel-result', (event) => {
                        window.wheelEvents.push({
                            type: 'result',
                            detail: event.detail,
                            timestamp: Date.now()
                        });
                    });
                }
            });
            
            // Wait for spin completion
            for (let i = 0; i < 40; i++) {
                await this.page.waitForTimeout(500);
                
                const events = await this.page.evaluate(() => window.wheelEvents || []);
                const spinCompleteEvent = events.find(e => e.type === 'spin-complete');
                
                if (spinCompleteEvent) {
                    spinCompleted = true;
                    winnerInfo = spinCompleteEvent.detail;
                    this.results.winnerDetected = true;
                    console.log('✅ Spin completed!');
                    console.log(`🎯 Winner: ${winnerInfo.winningSegment?.text || 'None'}`);
                    
                    // Check for enhanced detection features
                    if (winnerInfo.winningSegment) {
                        // Look for confidence scoring in status message or logs
                        const statusMessage = await this.page.evaluate(() => {
                            const wheel = document.querySelector('game-wheel') || document.getElementById('debugWheel');
                            return wheel ? wheel.statusMessage || '' : '';
                        });
                        
                        if (statusMessage.includes('%') || statusMessage.includes('confidence')) {
                            this.results.confidenceScored = true;
                            this.results.enhancedDetection = true;
                            console.log('✅ Enhanced winner detection with confidence scoring detected!');
                        }
                    }
                    break;
                }
                
                if (i % 4 === 0) {
                    console.log(`   Waiting for spin completion... ${i/2}s`);
                }
            }
            
            if (!spinCompleted) {
                throw new Error('Wheel spin did not complete within 20 seconds');
            }
            
        } catch (error) {
            console.error('❌ Wheel spinning test failed:', error.message);
            this.results.errors.push(`Wheel spinning: ${error.message}`);
        }
    }

    async generateReport() {
        console.log('\n🎯 ENHANCED WHEEL COMPONENT TEST RESULTS');
        console.log('════════════════════════════════════════════════════════════');
        
        const checkmark = (condition) => condition ? '✅' : '❌';
        
        console.log(`${checkmark(this.results.componentLoaded)} Component Loaded`);
        console.log(`${checkmark(this.results.wheelVisible)} Wheel Visible`);
        console.log(`${checkmark(this.results.wheelDataSet)} Wheel Data Set`);
        console.log(`${checkmark(this.results.spinExecuted)} Spin Executed`);
        console.log(`${checkmark(this.results.winnerDetected)} Winner Detected`);
        console.log(`${checkmark(this.results.confidenceScored)} Confidence Scoring`);
        console.log(`${checkmark(this.results.enhancedDetection)} Enhanced Detection`);
        
        if (this.results.errors.length > 0) {
            console.log('\n❌ Errors:');
            this.results.errors.forEach((error, index) => {
                console.log(`   ${index + 1}. ${error}`);
            });
        }
        
        const successCount = Object.values(this.results).filter(v => v === true).length;
        const totalTests = 7;
        const successRate = Math.round((successCount / totalTests) * 100);
        
        console.log(`\n🏆 OVERALL RESULT: ${successRate}% (${successCount}/${totalTests} tests passed)`);
        
        if (successRate >= 85) {
            console.log('🎉 EXCELLENT - Enhanced wheel component is working correctly!');
        } else if (successRate >= 70) {
            console.log('⚠️  GOOD - Minor issues detected, but core functionality works');
        } else {
            console.log('❌ NEEDS WORK - Significant issues detected');
        }
        
        return successRate >= 85;
    }

    async cleanup() {
        if (this.browser) {
            await this.browser.close();
        }
    }

    async run() {
        try {
            await this.initialize();
            
            // Test debug environment first (more reliable)
            await this.testDebugEnvironment();
            
            // Test production app if debug works
            if (this.results.wheelVisible) {
                await this.testProductionApp();
            }
            
            const success = await this.generateReport();
            await this.cleanup();
            
            return success;
            
        } catch (error) {
            console.error('❌ Test failed:', error);
            await this.cleanup();
            return false;
        }
    }
}

// Run the test
if (require.main === module) {
    const test = new EnhancedWheelTest();
    test.run().then(success => {
        process.exit(success ? 0 : 1);
    });
}

module.exports = EnhancedWheelTest;
