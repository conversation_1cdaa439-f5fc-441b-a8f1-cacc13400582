#!/usr/bin/env node

/**
 * Connection Dashboard Testing Tool
 * 
 * Tests the new WebSocket Connection Dashboard functionality including:
 * - Real-time connection monitoring
 * - System health indicators
 * - Message flow statistics
 * - Connection tracking and management
 */

import WebSocket from 'ws';
import { CONFIG } from './config.js';
import fs from 'fs';
import path from 'path';

class ConnectionDashboardTester {
  constructor() {
    this.testResults = [];
    this.connections = [];
    this.adminSocket = null;
    this.isRunning = false;
    this.logFile = path.join(CONFIG.logging.logDirectory, `connection-dashboard-test-${Date.now()}.log`);
    
    // Ensure log directory exists
    if (!fs.existsSync(CONFIG.logging.logDirectory)) {
      fs.mkdirSync(CONFIG.logging.logDirectory, { recursive: true });
    }
  }

  log(level, message, data = null) {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level,
      message,
      data
    };
    
    console.log(`[${timestamp}] ${level.toUpperCase()}: ${message}`);
    if (data) {
      console.log('  Data:', JSON.stringify(data, null, 2));
    }
    
    // Save to file
    if (CONFIG.logging.saveToFile) {
      fs.appendFileSync(this.logFile, JSON.stringify(logEntry) + '\n');
    }
  }

  async runTests() {
    this.log('info', '🚀 Starting Connection Dashboard Tests');
    this.isRunning = true;

    try {
      // Test 1: Connect to admin dashboard WebSocket
      await this.testAdminDashboardConnection();
      
      // Test 2: Create multiple client connections
      await this.testMultipleClientConnections();
      
      // Test 3: Test real-time monitoring
      await this.testRealTimeMonitoring();
      
      // Test 4: Test system health monitoring
      await this.testSystemHealthMonitoring();
      
      // Test 5: Test message flow tracking
      await this.testMessageFlowTracking();
      
      // Test 6: Test connection management
      await this.testConnectionManagement();
      
      // Test 7: Test dashboard UI accessibility
      await this.testDashboardUI();
      
      this.log('info', '✅ All Connection Dashboard Tests Completed');
      this.generateReport();
      
    } catch (error) {
      this.log('error', '❌ Connection Dashboard Tests Failed', { error: error.message });
    } finally {
      await this.cleanup();
    }
  }

  async testAdminDashboardConnection() {
    this.log('info', '🔌 Testing Admin Dashboard WebSocket Connection');
    
    return new Promise((resolve, reject) => {
      const adminWsUrl = 'ws://localhost:8000/ws/connection-monitor/';
      this.adminSocket = new WebSocket(adminWsUrl);
      
      const timeout = setTimeout(() => {
        reject(new Error('Admin dashboard connection timeout'));
      }, CONFIG.testing.defaultTimeout);

      this.adminSocket.on('open', () => {
        clearTimeout(timeout);
        this.log('info', '✅ Admin dashboard WebSocket connected');
        this.testResults.push({
          test: 'admin_dashboard_connection',
          status: 'passed',
          message: 'Successfully connected to admin dashboard WebSocket'
        });
        resolve();
      });

      this.adminSocket.on('error', (error) => {
        clearTimeout(timeout);
        this.log('error', '❌ Admin dashboard connection failed', { error: error.message });
        this.testResults.push({
          test: 'admin_dashboard_connection',
          status: 'failed',
          message: `Connection failed: ${error.message}`
        });
        reject(error);
      });

      this.adminSocket.on('message', (data) => {
        try {
          const message = JSON.parse(data);
          this.log('debug', '📨 Admin dashboard message received', { type: message.type });
          
          // Track different message types
          if (message.type === 'connection_data') {
            this.log('info', `📊 Connection data: ${message.data.length} active connections`);
          } else if (message.type === 'system_health') {
            this.log('info', '🏥 System health update received');
          } else if (message.type === 'message_stats') {
            this.log('info', '📈 Message statistics update received');
          }
        } catch (error) {
          this.log('error', 'Failed to parse admin dashboard message', { error: error.message });
        }
      });
    });
  }

  async testMultipleClientConnections() {
    this.log('info', '👥 Testing Multiple Client Connections');
    
    const connectionPromises = [];
    const numConnections = 5;
    
    for (let i = 0; i < numConnections; i++) {
      connectionPromises.push(this.createClientConnection(i + 1));
    }
    
    try {
      await Promise.all(connectionPromises);
      this.log('info', `✅ Successfully created ${numConnections} client connections`);
      this.testResults.push({
        test: 'multiple_client_connections',
        status: 'passed',
        message: `Created ${numConnections} client connections successfully`
      });
    } catch (error) {
      this.log('error', '❌ Failed to create multiple client connections', { error: error.message });
      this.testResults.push({
        test: 'multiple_client_connections',
        status: 'failed',
        message: `Failed to create connections: ${error.message}`
      });
    }
  }

  async createClientConnection(clientId) {
    return new Promise((resolve, reject) => {
      const clientWsUrl = CONFIG.backend.websocketUrl;
      const socket = new WebSocket(clientWsUrl);
      
      const timeout = setTimeout(() => {
        reject(new Error(`Client ${clientId} connection timeout`));
      }, CONFIG.testing.shortTimeout);

      socket.on('open', () => {
        clearTimeout(timeout);
        this.log('debug', `🔗 Client ${clientId} connected`);
        
        // Send initial message to identify user
        const initialMessage = {
          type: 'chat_message',
          content: {
            message: `Hello from test client ${clientId}`,
            user_profile_id: `test-user-${clientId}`,
            timestamp: new Date().toISOString()
          }
        };
        
        socket.send(JSON.stringify(initialMessage));
        
        this.connections.push({
          id: clientId,
          socket: socket,
          connected: true
        });
        
        resolve();
      });

      socket.on('error', (error) => {
        clearTimeout(timeout);
        this.log('error', `❌ Client ${clientId} connection failed`, { error: error.message });
        reject(error);
      });

      socket.on('message', (data) => {
        try {
          const message = JSON.parse(data);
          this.log('debug', `📨 Client ${clientId} received message`, { type: message.type });
        } catch (error) {
          this.log('error', `Failed to parse message for client ${clientId}`, { error: error.message });
        }
      });
    });
  }

  async testRealTimeMonitoring() {
    this.log('info', '⏱️ Testing Real-time Monitoring');
    
    if (!this.adminSocket) {
      throw new Error('Admin socket not connected');
    }
    
    // Request connection data
    this.adminSocket.send(JSON.stringify({ type: 'get_connections' }));
    
    // Wait for response
    await this.waitForMessage('connection_data', 5000);
    
    this.testResults.push({
      test: 'real_time_monitoring',
      status: 'passed',
      message: 'Real-time monitoring is working'
    });
  }

  async testSystemHealthMonitoring() {
    this.log('info', '🏥 Testing System Health Monitoring');
    
    if (!this.adminSocket) {
      throw new Error('Admin socket not connected');
    }
    
    // Request system health
    this.adminSocket.send(JSON.stringify({ type: 'get_system_health' }));
    
    // Wait for response
    await this.waitForMessage('system_health', 5000);
    
    this.testResults.push({
      test: 'system_health_monitoring',
      status: 'passed',
      message: 'System health monitoring is working'
    });
  }

  async testMessageFlowTracking() {
    this.log('info', '📊 Testing Message Flow Tracking');
    
    // Send messages from multiple clients
    for (const connection of this.connections) {
      if (connection.connected) {
        const message = {
          type: 'chat_message',
          content: {
            message: `Test message from client ${connection.id}`,
            user_profile_id: `test-user-${connection.id}`,
            timestamp: new Date().toISOString()
          }
        };
        connection.socket.send(JSON.stringify(message));
      }
    }
    
    // Request message stats
    if (this.adminSocket) {
      this.adminSocket.send(JSON.stringify({ type: 'get_message_stats' }));
      await this.waitForMessage('message_stats', 5000);
    }
    
    this.testResults.push({
      test: 'message_flow_tracking',
      status: 'passed',
      message: 'Message flow tracking is working'
    });
  }

  async testConnectionManagement() {
    this.log('info', '🔧 Testing Connection Management');
    
    // Test disconnecting a user (if implemented)
    if (this.adminSocket && this.connections.length > 0) {
      const testUserId = `test-user-${this.connections[0].id}`;
      this.adminSocket.send(JSON.stringify({ 
        type: 'disconnect_user', 
        user_id: testUserId 
      }));
      
      this.log('info', `Requested disconnection of user: ${testUserId}`);
    }
    
    this.testResults.push({
      test: 'connection_management',
      status: 'passed',
      message: 'Connection management features tested'
    });
  }

  async testDashboardUI() {
    this.log('info', '🖥️ Testing Dashboard UI Accessibility');
    
    // This would typically involve browser automation
    // For now, we'll just verify the endpoint is accessible
    try {
      const response = await fetch('http://localhost:8000/admin/connection-dashboard/');
      if (response.ok) {
        this.log('info', '✅ Dashboard UI is accessible');
        this.testResults.push({
          test: 'dashboard_ui',
          status: 'passed',
          message: 'Dashboard UI is accessible'
        });
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      this.log('error', '❌ Dashboard UI not accessible', { error: error.message });
      this.testResults.push({
        test: 'dashboard_ui',
        status: 'failed',
        message: `Dashboard UI not accessible: ${error.message}`
      });
    }
  }

  async waitForMessage(messageType, timeout = 5000) {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error(`Timeout waiting for ${messageType} message`));
      }, timeout);

      const messageHandler = (data) => {
        try {
          const message = JSON.parse(data);
          if (message.type === messageType) {
            clearTimeout(timer);
            this.adminSocket.off('message', messageHandler);
            resolve(message);
          }
        } catch (error) {
          // Ignore parsing errors
        }
      };

      this.adminSocket.on('message', messageHandler);
    });
  }

  async cleanup() {
    this.log('info', '🧹 Cleaning up connections');
    
    // Close all client connections
    for (const connection of this.connections) {
      if (connection.socket && connection.socket.readyState === WebSocket.OPEN) {
        connection.socket.close();
      }
    }
    
    // Close admin socket
    if (this.adminSocket && this.adminSocket.readyState === WebSocket.OPEN) {
      this.adminSocket.close();
    }
    
    this.isRunning = false;
  }

  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      totalTests: this.testResults.length,
      passed: this.testResults.filter(t => t.status === 'passed').length,
      failed: this.testResults.filter(t => t.status === 'failed').length,
      results: this.testResults
    };
    
    const reportFile = path.join(CONFIG.logging.logDirectory, `connection-dashboard-report-${Date.now()}.json`);
    fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
    
    this.log('info', '📋 Test Report Generated', {
      file: reportFile,
      summary: `${report.passed}/${report.totalTests} tests passed`
    });
    
    console.log('\n📊 TEST SUMMARY:');
    console.log(`✅ Passed: ${report.passed}`);
    console.log(`❌ Failed: ${report.failed}`);
    console.log(`📁 Report saved to: ${reportFile}`);
  }
}

// Run tests if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const tester = new ConnectionDashboardTester();
  tester.runTests().catch(console.error);
}

export default ConnectionDashboardTester;
