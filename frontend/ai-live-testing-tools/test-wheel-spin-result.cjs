#!/usr/bin/env node

/**
 * Test script to verify wheel spinning and result detection
 * Tests the complete flow from wheel spin to mentor response
 */

const WebSocket = require('ws');

class WheelSpinResultTester {
    constructor() {
        this.ws = null;
        this.connected = false;
        this.testResults = {
            connection: false,
            wheelDataSent: false,
            wheelSpinResult: false,
            mentorResponse: false
        };
        this.receivedMessages = [];
        this.wheelData = null;
    }

    async runTest() {
        console.log('🎯 Starting Wheel Spin Result Test');
        console.log('=====================================');

        try {
            // Step 1: Connect to WebSocket
            await this.connectWebSocket();
            
            // Step 2: Send mock wheel data
            await this.sendMockWheelData();
            
            // Step 3: Wait a moment for wheel to be ready
            await this.sleep(2000);
            
            // Step 4: Simulate wheel spin result
            await this.simulateWheelSpinResult();
            
            // Step 5: Wait for mentor response
            await this.waitForMentorResponse();
            
            // Step 6: Report results
            this.reportResults();
            
        } catch (error) {
            console.error('❌ Test failed:', error);
            process.exit(1);
        } finally {
            if (this.ws) {
                this.ws.close();
            }
        }
    }

    async connectWebSocket() {
        return new Promise((resolve, reject) => {
            console.log('🔗 Connecting to WebSocket...');
            
            this.ws = new WebSocket('ws://localhost:8000/ws/game/');
            
            this.ws.on('open', () => {
                console.log('✅ WebSocket connected');
                this.connected = true;
                this.testResults.connection = true;
                resolve();
            });
            
            this.ws.on('message', (data) => {
                try {
                    const message = JSON.parse(data.toString());
                    this.handleMessage(message);
                } catch (error) {
                    console.error('Error parsing message:', error);
                }
            });
            
            this.ws.on('error', (error) => {
                console.error('❌ WebSocket error:', error);
                reject(error);
            });
            
            this.ws.on('close', () => {
                console.log('🔌 WebSocket disconnected');
                this.connected = false;
            });
            
            // Timeout after 10 seconds
            setTimeout(() => {
                if (!this.connected) {
                    reject(new Error('Connection timeout'));
                }
            }, 10000);
        });
    }

    handleMessage(message) {
        this.receivedMessages.push(message);
        
        switch (message.type) {
            case 'system_message':
                console.log('📋 System:', message.content);
                break;
                
            case 'chat_message':
                console.log('💬 Chat:', message.content || 'undefined');
                // Check if this is a mentor response to our spin result
                if (message.content && message.content.includes('activity')) {
                    this.testResults.mentorResponse = true;
                    console.log('✅ Mentor response detected!');
                }
                break;
                
            case 'wheel_data':
                console.log('🎡 Wheel data received');
                this.wheelData = message.wheel;
                this.testResults.wheelDataSent = true;
                break;
                
            case 'processing_status':
                console.log('⚙️ Processing:', message.status);
                break;
                
            case 'heartbeat':
                // Ignore heartbeats
                break;
                
            default:
                console.log(`📋 ${message.type}:`, JSON.stringify(message).substring(0, 100) + '...');
        }
    }

    async sendMockWheelData() {
        console.log('🎡 Sending mock wheel data...');
        
        // Create mock wheel data with activities
        const mockWheelData = {
            type: 'wheel_data',
            wheel: {
                name: 'Test Wheel',
                items: [
                    {
                        id: 'activity-1',
                        name: 'Morning Meditation',
                        description: 'Start your day with mindfulness',
                        percentage: 25,
                        color: '#FF6B6B',
                        domain: 'wellness',
                        base_challenge_rating: 3,
                        activity_tailored_id: 'tailored-1'
                    },
                    {
                        id: 'activity-2', 
                        name: 'Creative Writing',
                        description: 'Express yourself through words',
                        percentage: 25,
                        color: '#4ECDC4',
                        domain: 'creativity',
                        base_challenge_rating: 4,
                        activity_tailored_id: 'tailored-2'
                    },
                    {
                        id: 'activity-3',
                        name: 'Nature Walk',
                        description: 'Connect with the outdoors',
                        percentage: 25,
                        color: '#45B7D1',
                        domain: 'physical',
                        base_challenge_rating: 2,
                        activity_tailored_id: 'tailored-3'
                    },
                    {
                        id: 'activity-4',
                        name: 'Learn Something New',
                        description: 'Expand your knowledge',
                        percentage: 25,
                        color: '#96CEB4',
                        domain: 'learning',
                        base_challenge_rating: 5,
                        activity_tailored_id: 'tailored-4'
                    }
                ]
            }
        };
        
        // Simulate receiving wheel data (as if from backend)
        this.handleMessage(mockWheelData);
        this.testResults.wheelDataSent = true;
        console.log('✅ Mock wheel data created with 4 activities');
    }

    async simulateWheelSpinResult() {
        console.log('🎯 Simulating wheel spin result...');
        
        if (!this.wheelData || !this.wheelData.items || this.wheelData.items.length === 0) {
            throw new Error('No wheel data available for spinning');
        }
        
        // Pick a random activity as the "winner"
        const winningActivity = this.wheelData.items[Math.floor(Math.random() * this.wheelData.items.length)];
        console.log(`🏆 Simulated winner: ${winningActivity.name}`);
        
        // Send spin result to backend (this is what the frontend should do)
        const spinResultMessage = {
            type: 'spin_result',
            content: {
                activity_tailored_id: winningActivity.activity_tailored_id,
                name: winningActivity.name,
                description: winningActivity.description,
                user_profile_id: '2' // Use existing user ID
            }
        };
        
        console.log('📤 Sending spin result to backend...');
        this.ws.send(JSON.stringify(spinResultMessage));
        this.testResults.wheelSpinResult = true;
        console.log('✅ Spin result sent');
    }

    async waitForMentorResponse() {
        console.log('⏳ Waiting for mentor response...');
        
        return new Promise((resolve) => {
            const timeout = setTimeout(() => {
                console.log('⚠️ Timeout waiting for mentor response');
                resolve();
            }, 30000); // 30 second timeout
            
            const checkForResponse = () => {
                if (this.testResults.mentorResponse) {
                    clearTimeout(timeout);
                    console.log('✅ Mentor response received!');
                    resolve();
                } else {
                    setTimeout(checkForResponse, 1000);
                }
            };
            
            checkForResponse();
        });
    }

    reportResults() {
        console.log('\n📊 Test Results');
        console.log('================');
        console.log(`🔗 WebSocket Connection: ${this.testResults.connection ? '✅ PASS' : '❌ FAIL'}`);
        console.log(`🎡 Wheel Data: ${this.testResults.wheelDataSent ? '✅ PASS' : '❌ FAIL'}`);
        console.log(`🎯 Spin Result Sent: ${this.testResults.wheelSpinResult ? '✅ PASS' : '❌ FAIL'}`);
        console.log(`💬 Mentor Response: ${this.testResults.mentorResponse ? '✅ PASS' : '❌ FAIL'}`);
        
        const allPassed = Object.values(this.testResults).every(result => result);
        console.log(`\n🎯 Overall Result: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
        
        if (!allPassed) {
            console.log('\n📋 Received Messages:');
            this.receivedMessages.forEach((msg, index) => {
                console.log(`${index + 1}. ${msg.type}: ${JSON.stringify(msg).substring(0, 100)}...`);
            });
        }
        
        process.exit(allPassed ? 0 : 1);
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Run the test
const tester = new WheelSpinResultTester();
tester.runTest().catch(console.error);
