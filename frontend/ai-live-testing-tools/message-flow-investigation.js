#!/usr/bin/env node

/**
 * Message Flow Investigation Tool
 * 
 * This tool specifically investigates why message inspector shows 0 chat messages
 * despite messages being sent. It focuses on:
 * 1. Checking if broadcast_message_flow is being called
 * 2. Verifying message format and content
 * 3. Testing message inspector activation
 * 4. Monitoring web container logs for broadcast calls
 */

import WebSocket from 'ws';
import { spawn } from 'child_process';

const CONFIG = {
  websockets: {
    game: 'ws://localhost:8000/ws/game/',
    admin: 'ws://localhost:8000/ws/connection-monitor/'
  },
  user: { id: '2', name: '<PERSON><PERSON><PERSON>' }
};

class MessageFlowInvestigator {
  constructor() {
    this.gameSocket = null;
    this.adminSocket = null;
    this.webLogProcess = null;
    this.messageFlowReceived = [];
    this.broadcastCallsDetected = [];
    this.messageInspectorActive = false;
  }

  async investigate() {
    console.log('🔍 MESSAGE FLOW INVESTIGATION');
    console.log('============================\n');

    try {
      // Start web log monitoring first
      await this.startWebLogMonitoring();
      
      // Connect to admin dashboard
      await this.connectToAdminDashboard();
      
      // Activate message inspector
      await this.activateMessageInspector();
      
      // Connect game client
      await this.connectGameClient();
      
      // Send test messages and monitor
      await this.sendTestMessagesAndMonitor();
      
      // Analyze results
      this.analyzeResults();
      
    } catch (error) {
      console.error('❌ Investigation failed:', error.message);
    } finally {
      this.cleanup();
    }
  }

  async startWebLogMonitoring() {
    console.log('📋 Starting Web Container Log Monitoring');
    console.log('----------------------------------------');

    this.webLogProcess = spawn('docker', ['logs', '-f', '--tail', '5', 'backend-web-1'], {
      stdio: ['ignore', 'pipe', 'pipe']
    });

    this.webLogProcess.stdout.on('data', (data) => {
      const logLine = data.toString();
      this.analyzeWebLogForBroadcasts(logLine);
    });

    this.webLogProcess.stderr.on('data', (data) => {
      const logLine = data.toString();
      this.analyzeWebLogForBroadcasts(logLine);
    });

    console.log('✅ Web log monitoring started\n');
    await new Promise(resolve => setTimeout(resolve, 2000));
  }

  analyzeWebLogForBroadcasts(logLine) {
    // Look for broadcast_message_flow calls
    if (logLine.includes('broadcast_message_flow') || logLine.includes('Broadcasting incoming message')) {
      const broadcastCall = {
        timestamp: new Date().toISOString(),
        content: logLine.trim(),
        type: 'broadcast_call'
      };
      this.broadcastCallsDetected.push(broadcastCall);
      console.log(`🔍 BROADCAST DETECTED: ${logLine.trim()}`);
    }

    // Look for ConnectionMonitorConsumer activity
    if (logLine.includes('ConnectionMonitorConsumer') || logLine.includes('message_flow')) {
      console.log(`📊 MONITOR ACTIVITY: ${logLine.trim()}`);
    }

    // Look for errors
    if (logLine.includes('ERROR') || logLine.includes('Exception')) {
      console.log(`🚨 ERROR: ${logLine.trim()}`);
    }
  }

  async connectToAdminDashboard() {
    console.log('📋 Connecting to Admin Dashboard');
    console.log('--------------------------------');

    return new Promise((resolve, reject) => {
      this.adminSocket = new WebSocket(CONFIG.websockets.admin);

      this.adminSocket.on('open', () => {
        console.log('✅ Admin dashboard connected');
        resolve();
      });

      this.adminSocket.on('message', (data) => {
        this.handleAdminMessage(data);
      });

      this.adminSocket.on('error', (error) => {
        console.log('❌ Admin connection error:', error.message);
        reject(error);
      });

      setTimeout(() => reject(new Error('Admin connection timeout')), 10000);
    });
  }

  handleAdminMessage(data) {
    try {
      const message = JSON.parse(data);
      
      if (message.type === 'message_flow') {
        this.messageFlowReceived.push({
          timestamp: Date.now(),
          data: message.data
        });
        console.log(`📨 MESSAGE FLOW RECEIVED: ${message.data.direction} - ${message.data.message?.type || 'unknown'}`);
      }

      if (message.type === 'message_monitoring_started') {
        this.messageInspectorActive = true;
        console.log('✅ Message inspector activated');
      }

      if (message.type === 'message_stats') {
        console.log(`📊 Message Stats: ${message.data.total_messages_today} total, ${message.data.messages_per_minute} per minute`);
      }

    } catch (error) {
      console.log('❌ Failed to parse admin message:', error.message);
    }
  }

  async activateMessageInspector() {
    console.log('📋 Activating Message Inspector');
    console.log('-------------------------------');

    if (this.adminSocket && this.adminSocket.readyState === WebSocket.OPEN) {
      this.adminSocket.send(JSON.stringify({ type: 'start_message_monitoring' }));
      console.log('📤 Sent start_message_monitoring request');
      
      // Wait for confirmation
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      if (this.messageInspectorActive) {
        console.log('✅ Message inspector is active\n');
      } else {
        console.log('❌ Message inspector activation failed\n');
      }
    }
  }

  async connectGameClient() {
    console.log('📋 Connecting Game Client');
    console.log('-------------------------');

    return new Promise((resolve, reject) => {
      this.gameSocket = new WebSocket(CONFIG.websockets.game);

      this.gameSocket.on('open', () => {
        console.log('✅ Game client connected');
        resolve();
      });

      this.gameSocket.on('message', (data) => {
        try {
          const message = JSON.parse(data);
          console.log(`📨 Game received: ${message.type}`);
        } catch (error) {
          console.log('❌ Failed to parse game message');
        }
      });

      this.gameSocket.on('error', (error) => {
        console.log('❌ Game connection error:', error.message);
        reject(error);
      });

      setTimeout(() => reject(new Error('Game connection timeout')), 10000);
    });
  }

  async sendTestMessagesAndMonitor() {
    console.log('📋 Sending Test Messages and Monitoring');
    console.log('---------------------------------------');

    // Send first test message
    const message1 = {
      type: 'chat_message',
      content: {
        message: 'Test message 1 - investigating message flow',
        user_profile_id: CONFIG.user.id,
        timestamp: new Date().toISOString()
      }
    };

    console.log('📤 Sending test message 1...');
    this.gameSocket.send(JSON.stringify(message1));
    
    // Wait and monitor
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Send second test message
    const message2 = {
      type: 'chat_message',
      content: {
        message: 'Test message 2 - checking broadcast calls',
        user_profile_id: CONFIG.user.id,
        timestamp: new Date().toISOString()
      }
    };

    console.log('📤 Sending test message 2...');
    this.gameSocket.send(JSON.stringify(message2));
    
    // Wait for processing
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Request message stats
    if (this.adminSocket && this.adminSocket.readyState === WebSocket.OPEN) {
      this.adminSocket.send(JSON.stringify({ type: 'get_message_stats' }));
      console.log('📤 Requested message stats');
    }

    // Final wait
    await new Promise(resolve => setTimeout(resolve, 3000));
  }

  analyzeResults() {
    console.log('\n🔍 INVESTIGATION RESULTS');
    console.log('========================');

    console.log(`📊 Message Flow Entries Received: ${this.messageFlowReceived.length}`);
    console.log(`🔍 Broadcast Calls Detected: ${this.broadcastCallsDetected.length}`);
    console.log(`📡 Message Inspector Active: ${this.messageInspectorActive ? 'Yes' : 'No'}`);

    console.log('\n📋 Detailed Analysis:');
    
    if (this.broadcastCallsDetected.length > 0) {
      console.log('✅ Broadcast calls are being made:');
      this.broadcastCallsDetected.forEach((call, index) => {
        console.log(`   ${index + 1}. ${call.content.substring(0, 100)}...`);
      });
    } else {
      console.log('❌ No broadcast calls detected in web logs');
    }

    if (this.messageFlowReceived.length > 0) {
      console.log('✅ Message flow entries received:');
      this.messageFlowReceived.forEach((flow, index) => {
        console.log(`   ${index + 1}. ${flow.data.direction} - ${flow.data.message?.type || 'unknown'}`);
      });
    } else {
      console.log('❌ No message flow entries received');
    }

    console.log('\n🎯 DIAGNOSIS:');
    
    if (this.broadcastCallsDetected.length > 0 && this.messageFlowReceived.length === 0) {
      console.log('❌ ISSUE: Broadcast calls are made but message flow not received');
      console.log('   This suggests an issue in ConnectionMonitorConsumer.broadcast_message_flow()');
    } else if (this.broadcastCallsDetected.length === 0) {
      console.log('❌ ISSUE: No broadcast calls detected');
      console.log('   This suggests UserSessionConsumer is not calling broadcast_message_flow()');
    } else if (this.messageFlowReceived.length > 0) {
      console.log('✅ Message flow is working correctly');
    }

    if (!this.messageInspectorActive) {
      console.log('❌ ISSUE: Message inspector not activated properly');
    }

    console.log('\n🔧 RECOMMENDED ACTIONS:');
    console.log('1. Check ConnectionMonitorConsumer._active_consumers list');
    console.log('2. Verify message_monitoring_enabled flag is set correctly');
    console.log('3. Check for exceptions in broadcast_message_flow method');
    console.log('4. Verify admin dashboard is properly registered as active consumer');
  }

  cleanup() {
    console.log('\n🧹 Cleaning up...');
    
    if (this.gameSocket) {
      this.gameSocket.close();
    }
    
    if (this.adminSocket) {
      this.adminSocket.close();
    }

    if (this.webLogProcess) {
      this.webLogProcess.kill();
    }

    console.log('✅ Cleanup completed');
  }
}

// Run the investigation
const investigator = new MessageFlowInvestigator();
investigator.investigate().then(() => {
  process.exit(0);
}).catch((error) => {
  console.error('❌ Investigation failed:', error);
  process.exit(1);
});

// Auto-exit after 60 seconds
setTimeout(() => {
  console.log('\n⏰ Investigation timeout - exiting');
  process.exit(1);
}, 60000);
