/**
 * Configuration for AI Live Testing Tools
 */

export const CONFIG = {
  // Backend Configuration
  backend: {
    websocketUrl: 'ws://localhost:8000/ws/game/',
    httpUrl: 'http://localhost:8000',
    timeout: 10000,
    reconnectAttempts: 3,
    reconnectDelay: 1000
  },

  // Frontend Configuration
  frontend: {
    debugUrl: 'http://localhost:5173',
    prodUrl: 'http://localhost:5173',
    debugMode: {
      envFile: '.env.development',
      expectedMode: 'debug'
    },
    prodMode: {
      envFile: '.env.production', 
      expectedMode: 'production'
    }
  },

  // Test Configuration
  testing: {
    defaultTimeout: 30000,
    shortTimeout: 5000,
    longTimeout: 60000,
    retryAttempts: 3,
    retryDelay: 2000
  },

  // Test Users (for debug mode testing)
  testUsers: [
    { id: '2', name: 'Test User 2', is_fake: true },
    { id: '3', name: 'Test User 3', is_fake: true },
    { id: '1', name: 'Admin User', is_fake: false }
  ],

  // Test Credentials (for production mode testing)
  testCredentials: {
    valid: {
      username: 'testuser',
      password: 'testpass123'
    },
    invalid: {
      username: 'wronguser',
      password: 'wrongpass'
    }
  },

  // API Endpoints to Test
  apiEndpoints: {
    debug: [
      '/api/debug/users/',
      '/api/debug/llm-configs/'
    ],
    auth: [
      '/api/auth/login/',
      '/api/auth/verify/',
      '/api/auth/logout/'
    ],
    logging: [
      '/logs/'
    ]
  },

  // Expected WebSocket Messages
  expectedMessages: {
    incoming: [
      'system_message',
      'chat_message', 
      'wheel_data',
      'processing_status',
      'error',
      'debug_info'
    ],
    outgoing: [
      'chat_message',
      'spin_result',
      'workflow_status_request'
    ]
  },

  // Test Scenarios
  scenarios: {
    debugMode: [
      'open_debug_panel',
      'select_user',
      'select_llm_config',
      'change_backend_url',
      'send_chat_message',
      'verify_response'
    ],
    prodMode: [
      'show_login_form',
      'test_invalid_login',
      'test_valid_login',
      'test_demo_mode',
      'send_chat_message',
      'verify_response'
    ]
  },

  // Logging Configuration
  logging: {
    level: 'debug', // debug, info, warn, error
    saveToFile: true,
    logDirectory: './logs',
    maxLogFiles: 10,
    maxLogSize: '10MB'
  },

  // Performance Thresholds
  performance: {
    websocketConnection: 2000, // ms
    apiResponse: 5000, // ms
    messageRoundTrip: 1000, // ms
    pageLoad: 10000 // ms
  },

  // Error Patterns to Watch For
  errorPatterns: [
    'No LLMConfig provided',
    'AttributeError: Cannot find',
    'Tool code .* not found',
    'Connection refused',
    'CORS error',
    'Authentication failed',
    'Token expired'
  ]
};

export default CONFIG;
