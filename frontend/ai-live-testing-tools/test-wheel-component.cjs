#!/usr/bin/env node

/**
 * Wheel Component Testing Tool
 * 
 * Tests the wheel component functionality including:
 * - Component loading and initialization
 * - Mock data loading
 * - Spin functionality
 * - Winner detection with 1-second delay
 * - Viewport controls (zoom, pan, reset)
 * 
 * Usage: node test-wheel-component.cjs [port]
 * Example: node test-wheel-component.cjs 3004
 */

const { chromium } = require('playwright');

// Default configuration
const DEFAULT_PORT = 3004;
const DEFAULT_TIMEOUT = 30000;

class WheelComponentTester {
    constructor(port = DEFAULT_PORT) {
        this.port = port;
        this.baseUrl = `http://localhost:${port}`;
        this.debugUrl = `${this.baseUrl}/wheel-debug.html`;
        this.browser = null;
        this.page = null;
        this.testResults = {
            passed: 0,
            failed: 0,
            errors: []
        };
    }

    async init() {
        console.log('🎡 Starting Wheel Component Testing Tool');
        console.log(`📍 Testing URL: ${this.debugUrl}`);
        console.log(`⏱️  Timeout: ${DEFAULT_TIMEOUT}ms`);
        console.log('');

        try {
            // Launch browser
            this.browser = await chromium.launch({ 
                headless: false,
                slowMo: 100 // Slow down for better visibility
            });
            
            this.page = await this.browser.newPage();
            
            // Set viewport size
            await this.page.setViewportSize({ width: 1400, height: 900 });
            
            // Enable console logging
            this.page.on('console', msg => {
                if (msg.type() === 'error') {
                    console.log(`🔴 Console Error: ${msg.text()}`);
                } else if (msg.text().includes('✅') || msg.text().includes('❌')) {
                    console.log(`📝 Console: ${msg.text()}`);
                }
            });

            // Navigate to debug page
            await this.page.goto(this.debugUrl, { waitUntil: 'networkidle' });
            console.log('✅ Debug page loaded successfully');

        } catch (error) {
            console.error('❌ Failed to initialize browser:', error.message);
            throw error;
        }
    }

    async runTests() {
        console.log('\n🧪 Running Wheel Component Tests...\n');

        try {
            await this.testComponentLoading();
            await this.testMockDataLoading();
            await this.testViewportControls();
            await this.testWheelSpin();
            await this.testWinnerDetection();
            await this.testDebugFeatures();

        } catch (error) {
            this.recordError('Test execution failed', error);
        }

        this.printResults();
    }

    async testComponentLoading() {
        console.log('🔍 Testing component loading...');

        try {
            // Wait for components to be defined
            await this.page.waitForFunction(() => {
                return customElements.get('game-wheel') && customElements.get('wheel-viewport');
            }, { timeout: 10000 });

            // Check if elements exist in DOM
            const wheelExists = await this.page.locator('#debugWheel').count() > 0;
            const viewportExists = await this.page.locator('#debugViewport').count() > 0;

            if (wheelExists && viewportExists) {
                console.log('✅ Components loaded and elements found in DOM');
                this.testResults.passed++;
            } else {
                throw new Error(`Missing elements - Wheel: ${wheelExists}, Viewport: ${viewportExists}`);
            }

        } catch (error) {
            this.recordError('Component loading test', error);
        }
    }

    async testMockDataLoading() {
        console.log('🔍 Testing mock data loading...');

        try {
            // Click load wheel button
            await this.page.click('#loadWheelBtn');
            console.log('📝 Clicked load wheel button');

            // Wait for wheel to be loaded
            await this.page.waitForFunction(() => {
                const statusDisplay = document.getElementById('statusDisplay');
                return statusDisplay && statusDisplay.textContent.includes('Mock wheel items loaded');
            }, { timeout: 5000 });

            // Check if spin button is enabled
            const spinButtonEnabled = await this.page.locator('#spinWheelBtn').isEnabled();
            
            if (spinButtonEnabled) {
                console.log('✅ Mock data loaded successfully, spin button enabled');
                this.testResults.passed++;
            } else {
                throw new Error('Spin button not enabled after loading mock data');
            }

        } catch (error) {
            this.recordError('Mock data loading test', error);
        }
    }

    async testViewportControls() {
        console.log('🔍 Testing viewport controls...');

        try {
            // Test zoom in
            await this.page.click('#zoomInBtn');
            await this.page.waitForTimeout(500);
            console.log('📝 Tested zoom in');

            // Test zoom out
            await this.page.click('#zoomOutBtn');
            await this.page.waitForTimeout(500);
            console.log('📝 Tested zoom out');

            // Test reset view
            await this.page.click('#resetViewBtn');
            await this.page.waitForTimeout(500);
            console.log('📝 Tested reset view');

            // Check if viewport state is being tracked
            const debugInfo = await this.page.textContent('#debugInfo');
            if (debugInfo.includes('Viewport State:')) {
                console.log('✅ Viewport controls working, state tracked in debug info');
                this.testResults.passed++;
            } else {
                throw new Error('Viewport state not found in debug info');
            }

        } catch (error) {
            this.recordError('Viewport controls test', error);
        }
    }

    async testWheelSpin() {
        console.log('🔍 Testing wheel spin functionality...');

        try {
            // Click spin button
            await this.page.click('#spinWheelBtn');
            console.log('📝 Clicked spin button');

            // Wait for spin to start
            await this.page.waitForFunction(() => {
                const statusDisplay = document.getElementById('statusDisplay');
                return statusDisplay && statusDisplay.textContent.includes('Starting wheel spin');
            }, { timeout: 3000 });

            console.log('✅ Wheel spin started successfully');
            this.testResults.passed++;

        } catch (error) {
            this.recordError('Wheel spin test', error);
        }
    }

    async testWinnerDetection() {
        console.log('🔍 Testing winner detection with 1-second delay...');

        try {
            // Wait for spin to complete (this should include the 1-second delay)
            await this.page.waitForFunction(() => {
                const statusDisplay = document.getElementById('statusDisplay');
                return statusDisplay && (
                    statusDisplay.textContent.includes('Spin complete') ||
                    statusDisplay.textContent.includes('Winner:')
                );
            }, { timeout: 15000 });

            // Check if winner was detected
            const winnerDisplayVisible = await this.page.locator('#winnerDisplay.visible').count() > 0;
            
            if (winnerDisplayVisible) {
                const winnerText = await this.page.textContent('#winnerText');
                console.log(`✅ Winner detected: ${winnerText}`);
                this.testResults.passed++;
            } else {
                throw new Error('Winner not detected or not displayed');
            }

        } catch (error) {
            this.recordError('Winner detection test', error);
        }
    }

    async testDebugFeatures() {
        console.log('🔍 Testing debug features...');

        try {
            // Test debug winner button
            await this.page.click('#debugWinnerBtn');
            await this.page.waitForTimeout(1000);
            console.log('📝 Tested debug winner button');

            // Test show segments button
            await this.page.click('#showSegmentsBtn');
            await this.page.waitForTimeout(1000);
            console.log('📝 Tested show segments button');

            // Check if debug info is being updated
            const debugInfo = await this.page.textContent('#debugInfo');
            if (debugInfo.includes('Wheel State:') && debugInfo.includes('Segments:')) {
                console.log('✅ Debug features working, detailed info displayed');
                this.testResults.passed++;
            } else {
                throw new Error('Debug info not properly displayed');
            }

        } catch (error) {
            this.recordError('Debug features test', error);
        }
    }

    recordError(testName, error) {
        console.log(`❌ ${testName} failed: ${error.message}`);
        this.testResults.failed++;
        this.testResults.errors.push({ test: testName, error: error.message });
    }

    printResults() {
        console.log('\n📊 Test Results Summary:');
        console.log('========================');
        console.log(`✅ Passed: ${this.testResults.passed}`);
        console.log(`❌ Failed: ${this.testResults.failed}`);
        console.log(`📈 Success Rate: ${((this.testResults.passed / (this.testResults.passed + this.testResults.failed)) * 100).toFixed(1)}%`);

        if (this.testResults.errors.length > 0) {
            console.log('\n🔍 Error Details:');
            this.testResults.errors.forEach((error, index) => {
                console.log(`${index + 1}. ${error.test}: ${error.error}`);
            });
        }

        console.log('\n🎯 Test Recommendations:');
        if (this.testResults.failed === 0) {
            console.log('🎉 All tests passed! Wheel component is working correctly.');
        } else {
            console.log('🔧 Some tests failed. Check the error details above and fix the issues.');
        }
    }

    async cleanup() {
        if (this.browser) {
            await this.browser.close();
            console.log('\n🧹 Browser closed');
        }
    }
}

// Main execution
async function main() {
    const port = process.argv[2] ? parseInt(process.argv[2]) : DEFAULT_PORT;
    
    if (isNaN(port) || port < 1000 || port > 65535) {
        console.error('❌ Invalid port number. Please provide a port between 1000 and 65535.');
        process.exit(1);
    }

    const tester = new WheelComponentTester(port);

    try {
        await tester.init();
        await tester.runTests();
    } catch (error) {
        console.error('❌ Testing failed:', error.message);
        process.exit(1);
    } finally {
        await tester.cleanup();
    }
}

// Handle process termination
process.on('SIGINT', async () => {
    console.log('\n⚠️  Test interrupted by user');
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log('\n⚠️  Test terminated');
    process.exit(0);
});

if (require.main === module) {
    main().catch(console.error);
}

module.exports = { WheelComponentTester };
