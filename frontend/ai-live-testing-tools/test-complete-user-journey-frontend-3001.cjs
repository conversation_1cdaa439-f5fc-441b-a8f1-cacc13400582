#!/usr/bin/env node

/**
 * Complete User Journey Frontend Test
 * 
 * This test simulates a complete user journey from the frontend perspective:
 * 1. New user with minimal profile completion
 * 2. User requests wheel ("make me a wheel")
 * 3. System asks for more information (no hanging)
 * 4. User provides profile information
 * 5. Profile completion increases
 * 6. User requests wheel again
 * 7. System generates wheel with activities
 * 8. User can interact with the wheel
 * 
 * This test validates the entire flow works from frontend to backend.
 */

const { chromium } = require('playwright');
const WebSocket = require('ws');

class CompleteUserJourneyFrontendTest {
    constructor() {
        this.testId = Math.random().toString(36).substring(7);
        this.results = [];
        this.startTime = Date.now();
        this.responseTimeThreshold = 10000; // 10 seconds
        this.websocketMessages = [];
        this.profileCompletionHistory = [];
    }

    async runCompleteJourney() {
        console.log('🚀 Complete User Journey Frontend Test');
        console.log(`   Test ID: ${this.testId}`);
        console.log('   Scenario: New user → Profile completion → Wheel generation');
        console.log();

        let browser, page;
        
        try {
            // Launch browser and setup
            browser = await chromium.launch({ 
                headless: false,  // Show browser for debugging
                slowMo: 1000     // Slow down for observation
            });
            
            page = await browser.newPage();
            
            // Setup monitoring
            await this.setupPageMonitoring(page);
            
            // Phase 1: Load application and setup new user
            await this.testApplicationLoad(page);
            
            // Phase 2: Test initial wheel request (should not hang)
            await this.testInitialWheelRequest(page);
            
            // Phase 3: Provide profile information
            await this.testProfileCompletion(page);
            
            // Phase 4: Test final wheel generation
            await this.testFinalWheelGeneration(page);
            
            // Phase 5: Test wheel interaction
            await this.testWheelInteraction(page);

            // Generate comprehensive report
            this.generateTestReport();
            
        } catch (error) {
            console.error('❌ Test failed:', error);
            this.results.push({
                phase: 'test_execution',
                error: error.message,
                success: false
            });
        } finally {
            if (browser) {
                await browser.close();
            }
        }
    }

    async setupPageMonitoring(page) {
        console.log('📊 Setting up page monitoring...');
        
        // Monitor console logs
        page.on('console', msg => {
            if (msg.type() === 'error') {
                console.log(`   🔴 Console Error: ${msg.text()}`);
            }
        });
        
        // Monitor WebSocket messages
        page.on('websocket', ws => {
            console.log('   🔗 WebSocket connection established');
            
            ws.on('framereceived', event => {
                try {
                    const data = JSON.parse(event.payload);
                    this.websocketMessages.push({
                        timestamp: new Date().toISOString(),
                        direction: 'in',
                        type: data.type,
                        data: data
                    });
                    
                    // Monitor profile completion updates
                    if (data.type === 'debug_info' && data.details?.profile_completion !== undefined) {
                        this.profileCompletionHistory.push({
                            timestamp: new Date().toISOString(),
                            completion: data.details.profile_completion
                        });
                    }
                } catch (e) {
                    // Ignore non-JSON messages
                }
            });
            
            ws.on('framesent', event => {
                try {
                    const data = JSON.parse(event.payload);
                    this.websocketMessages.push({
                        timestamp: new Date().toISOString(),
                        direction: 'out',
                        type: data.type,
                        data: data
                    });
                } catch (e) {
                    // Ignore non-JSON messages
                }
            });
        });
    }

    async testApplicationLoad(page) {
        console.log('📱 Phase 1: Testing application load and user setup');
        const phaseStart = Date.now();
        
        try {
            // Navigate to application
            await page.goto('http://localhost:3001', { waitUntil: 'networkidle' });
            console.log('   ✓ Application loaded');
            
            // Wait for app to initialize
            await page.waitForSelector('app-shell', { timeout: 10000 });
            console.log('   ✓ App shell loaded');
            
            // Open debug panel
            await page.keyboard.press('Control+Shift+D');
            await page.waitForSelector('debug-panel', { timeout: 5000 });
            console.log('   ✓ Debug panel opened');
            
            // Create new user with minimal profile
            const createUserButton = await page.locator('button:has-text("New German Student")');
            if (await createUserButton.isVisible()) {
                await createUserButton.click();
                console.log('   ✓ Creating new test user...');
                
                // Wait for user creation
                await page.waitForTimeout(3000);
                console.log('   ✓ New user created');
            }
            
            // Close debug panel
            await page.keyboard.press('Control+Shift+D');
            
            const duration = Date.now() - phaseStart;
            this.results.push({
                phase: 'application_load',
                duration: duration,
                success: true
            });
            
            console.log(`   ✓ Application load completed in ${duration}ms`);
            
        } catch (error) {
            const duration = Date.now() - phaseStart;
            console.log(`   ❌ Application load failed: ${error.message}`);
            this.results.push({
                phase: 'application_load',
                duration: duration,
                error: error.message,
                success: false
            });
            throw error;
        }
        
        console.log();
    }

    async testInitialWheelRequest(page) {
        console.log('🎯 Phase 2: Testing initial wheel request (hanging prevention)');
        const phaseStart = Date.now();
        
        try {
            // Find chat input
            const chatInput = await page.locator('input[placeholder*="message"], textarea[placeholder*="message"], input[type="text"]').first();
            await chatInput.waitFor({ timeout: 10000 });
            console.log('   ✓ Chat input found');
            
            // Send wheel request
            const wheelRequestMessage = "make me a wheel";
            await chatInput.fill(wheelRequestMessage);
            await chatInput.press('Enter');
            console.log(`   📤 Sent: "${wheelRequestMessage}"`);
            
            // Monitor for response within threshold
            const responseStart = Date.now();
            let responseReceived = false;
            let hangingDetected = false;
            
            // Wait for assistant response
            try {
                await page.waitForFunction(() => {
                    const messages = document.querySelectorAll('[data-message-type="assistant"], .message.assistant, .assistant-message');
                    return messages.length > 0;
                }, { timeout: this.responseTimeThreshold });
                
                const responseTime = Date.now() - responseStart;
                responseReceived = true;
                console.log(`   ✓ Response received in ${responseTime}ms`);
                
                // Check if response asks for more information
                const assistantMessages = await page.locator('[data-message-type="assistant"], .message.assistant, .assistant-message').allTextContents();
                const lastResponse = assistantMessages[assistantMessages.length - 1];
                
                if (lastResponse.toLowerCase().includes('tell me') || 
                    lastResponse.toLowerCase().includes('information') ||
                    lastResponse.toLowerCase().includes('about yourself')) {
                    console.log('   ✓ System correctly asks for more information');
                } else {
                    console.log('   ⚠️  Response may not be asking for profile information');
                }
                
            } catch (timeoutError) {
                const responseTime = Date.now() - responseStart;
                hangingDetected = true;
                console.log(`   ❌ HANGING DETECTED: No response after ${responseTime}ms`);
            }
            
            const duration = Date.now() - phaseStart;
            this.results.push({
                phase: 'initial_wheel_request',
                duration: duration,
                responseReceived: responseReceived,
                hangingDetected: hangingDetected,
                success: responseReceived && !hangingDetected
            });
            
            console.log(`   ${responseReceived && !hangingDetected ? '✓' : '❌'} Initial wheel request completed in ${duration}ms`);
            
        } catch (error) {
            const duration = Date.now() - phaseStart;
            console.log(`   ❌ Initial wheel request failed: ${error.message}`);
            this.results.push({
                phase: 'initial_wheel_request',
                duration: duration,
                error: error.message,
                success: false
            });
        }
        
        console.log();
    }

    async testProfileCompletion(page) {
        console.log('📝 Phase 3: Testing profile completion');
        const phaseStart = Date.now();
        
        try {
            // Get initial profile completion
            const initialCompletion = this.getLatestProfileCompletion();
            console.log(`   Initial profile completion: ${(initialCompletion * 100).toFixed(1)}%`);
            
            // Provide detailed profile information
            const profileMessages = [
                "I'm a 22-year-old computer science student in Berlin. I have ADHD and need help with focus and stress management.",
                "My goals are to improve concentration, reduce exam stress, and maintain work-life balance. I prefer creative and light physical activities.",
                "I like activities that are 10-30 minutes long, can be done indoors, and help me reset between study sessions."
            ];
            
            const chatInput = await page.locator('input[placeholder*="message"], textarea[placeholder*="message"], input[type="text"]').first();
            
            for (let i = 0; i < profileMessages.length; i++) {
                console.log(`   📤 Sending profile info ${i + 1}/3...`);
                
                await chatInput.fill(profileMessages[i]);
                await chatInput.press('Enter');
                
                // Wait for response
                await page.waitForTimeout(3000);
                
                // Check for profile completion update
                const currentCompletion = this.getLatestProfileCompletion();
                if (currentCompletion > initialCompletion) {
                    console.log(`   ✓ Profile completion increased to ${(currentCompletion * 100).toFixed(1)}%`);
                }
            }
            
            // Wait for all profile processing to complete
            await page.waitForTimeout(5000);
            
            const finalCompletion = this.getLatestProfileCompletion();
            const completionIncrease = finalCompletion - initialCompletion;
            
            const duration = Date.now() - phaseStart;
            this.results.push({
                phase: 'profile_completion',
                duration: duration,
                initialCompletion: initialCompletion,
                finalCompletion: finalCompletion,
                completionIncrease: completionIncrease,
                success: completionIncrease > 0
            });
            
            console.log(`   ✓ Profile completion: ${(initialCompletion * 100).toFixed(1)}% → ${(finalCompletion * 100).toFixed(1)}% (+${(completionIncrease * 100).toFixed(1)}%)`);
            console.log(`   ✓ Profile completion phase completed in ${duration}ms`);
            
        } catch (error) {
            const duration = Date.now() - phaseStart;
            console.log(`   ❌ Profile completion failed: ${error.message}`);
            this.results.push({
                phase: 'profile_completion',
                duration: duration,
                error: error.message,
                success: false
            });
        }
        
        console.log();
    }

    async testFinalWheelGeneration(page) {
        console.log('🎡 Phase 4: Testing final wheel generation');
        const phaseStart = Date.now();

        try {
            // Send final wheel request
            const chatInput = await page.locator('input[placeholder*="message"], textarea[placeholder*="message"], input[type="text"]').first();
            const finalWheelMessage = "Now I'm ready for my activity wheel! Please create one for me.";

            await chatInput.fill(finalWheelMessage);
            await chatInput.press('Enter');
            console.log(`   📤 Sent: "${finalWheelMessage}"`);

            // Wait for wheel generation (longer timeout)
            console.log('   ⏳ Waiting for wheel generation...');

            try {
                // Look for wheel component or wheel-related elements
                await page.waitForSelector('game-wheel, .wheel-container, .activity-wheel, [data-wheel]', {
                    timeout: 30000
                });
                console.log('   ✓ Wheel component appeared');

                // Check for activities in the wheel
                const wheelActivities = await page.locator('.activity, .wheel-item, [data-activity]').count();
                console.log(`   ✓ Wheel contains ${wheelActivities} activities`);

                const duration = Date.now() - phaseStart;
                this.results.push({
                    phase: 'final_wheel_generation',
                    duration: duration,
                    wheelActivities: wheelActivities,
                    success: wheelActivities >= 4
                });

                console.log(`   ✓ Wheel generation completed in ${duration}ms`);

            } catch (wheelError) {
                // Check if we got a response but no wheel
                const assistantMessages = await page.locator('[data-message-type="assistant"], .message.assistant, .assistant-message').allTextContents();
                const hasResponse = assistantMessages.length > 0;

                const duration = Date.now() - phaseStart;
                this.results.push({
                    phase: 'final_wheel_generation',
                    duration: duration,
                    hasResponse: hasResponse,
                    error: 'Wheel component not found',
                    success: false
                });

                console.log(`   ⚠️  Wheel generation issue: ${wheelError.message}`);
            }

        } catch (error) {
            const duration = Date.now() - phaseStart;
            console.log(`   ❌ Final wheel generation failed: ${error.message}`);
            this.results.push({
                phase: 'final_wheel_generation',
                duration: duration,
                error: error.message,
                success: false
            });
        }

        console.log();
    }

    async testWheelInteraction(page) {
        console.log('🎮 Phase 5: Testing wheel interaction');
        const phaseStart = Date.now();

        try {
            // Check if wheel is present and interactive
            const wheelElement = await page.locator('game-wheel, .wheel-container, .activity-wheel, [data-wheel]').first();

            if (await wheelElement.isVisible()) {
                console.log('   ✓ Wheel is visible');

                // Try to interact with the wheel (spin button, click, etc.)
                const spinButton = await page.locator('button:has-text("Spin"), .spin-button, [data-spin]').first();

                if (await spinButton.isVisible()) {
                    await spinButton.click();
                    console.log('   ✓ Wheel spin initiated');

                    // Wait for spin animation to complete
                    await page.waitForTimeout(3000);

                    // Check for spin result
                    const spinResult = await page.locator('.spin-result, .selected-activity, [data-spin-result]').first();
                    if (await spinResult.isVisible()) {
                        const resultText = await spinResult.textContent();
                        console.log(`   ✓ Spin result: ${resultText?.substring(0, 50)}...`);
                    }
                }

                const duration = Date.now() - phaseStart;
                this.results.push({
                    phase: 'wheel_interaction',
                    duration: duration,
                    wheelVisible: true,
                    success: true
                });

                console.log(`   ✓ Wheel interaction completed in ${duration}ms`);

            } else {
                console.log('   ⚠️  Wheel not visible for interaction');

                const duration = Date.now() - phaseStart;
                this.results.push({
                    phase: 'wheel_interaction',
                    duration: duration,
                    wheelVisible: false,
                    success: false
                });
            }

        } catch (error) {
            const duration = Date.now() - phaseStart;
            console.log(`   ❌ Wheel interaction failed: ${error.message}`);
            this.results.push({
                phase: 'wheel_interaction',
                duration: duration,
                error: error.message,
                success: false
            });
        }

        console.log();
    }

    getLatestProfileCompletion() {
        if (this.profileCompletionHistory.length === 0) return 0;
        return this.profileCompletionHistory[this.profileCompletionHistory.length - 1].completion;
    }

    generateTestReport() {
        console.log('📊 Complete User Journey Frontend Test Report');
        console.log('=' * 60);
        
        const totalPhases = this.results.length;
        const successfulPhases = this.results.filter(r => r.success).length;
        const totalDuration = Date.now() - this.startTime;
        
        console.log(`Total Test Phases: ${totalPhases}`);
        console.log(`Successful Phases: ${successfulPhases}`);
        console.log(`Failed Phases: ${totalPhases - successfulPhases}`);
        console.log(`Success Rate: ${((successfulPhases / totalPhases) * 100).toFixed(1)}%`);
        console.log(`Total Test Duration: ${totalDuration}ms`);
        console.log();
        
        console.log('Phase Results:');
        console.log('-'.repeat(40));
        
        this.results.forEach(result => {
            const status = result.success ? '✓' : '❌';
            const phase = result.phase.replace(/_/g, ' ').toUpperCase();
            console.log(`${status} ${phase}`);
            
            if (result.duration) {
                console.log(`    Duration: ${result.duration}ms`);
            }
            
            if (result.hangingDetected) {
                console.log('    ⚠️  HANGING DETECTED');
            }
            
            if (result.completionIncrease !== undefined) {
                console.log(`    Profile Completion Increase: +${(result.completionIncrease * 100).toFixed(1)}%`);
            }
            
            if (result.error) {
                console.log(`    Error: ${result.error}`);
            }
            
            console.log();
        });
        
        // Overall assessment
        const hangingFixed = !this.results.some(r => r.hangingDetected);
        const profileWorking = this.results.some(r => r.phase === 'profile_completion' && r.success);
        
        console.log('Overall Assessment:');
        console.log('-'.repeat(20));
        console.log(`✓ No Hanging Issues: ${hangingFixed ? '✓' : '❌'}`);
        console.log(`✓ Profile Completion Working: ${profileWorking ? '✓' : '❌'}`);
        console.log(`✓ Frontend-Backend Integration: ${hangingFixed && profileWorking ? '✓' : '❌'}`);
    }
}

// Main execution
async function main() {
    const test = new CompleteUserJourneyFrontendTest();
    await test.runCompleteJourney();
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = { CompleteUserJourneyFrontendTest };
