#!/usr/bin/env node

const WebSocket = require('ws');

console.log('🚀 Starting Simple WebSocket Wheel Test');

// Add more debugging
const ws = new WebSocket('ws://localhost:8000/ws/game/');

console.log('WebSocket state:', ws.readyState);
console.log('WebSocket URL:', ws.url);

let connected = false;
let wheelReceived = false;

// Set timeout for the test
const timeout = setTimeout(() => {
    console.log('❌ Test timeout after 120 seconds');
    if (!connected) {
        console.log('❌ WebSocket never connected');
    }
    if (!wheelReceived) {
        console.log('❌ Wheel data never received');
    }
    process.exit(1);
}, 120000);

ws.on('open', function open() {
    console.log('✅ WebSocket connected');
    console.log('WebSocket state after open:', ws.readyState);
    connected = true;
    
    // Send wheel generation request
    const message = {
        type: 'chat_message',
        content: {
            message: 'hey, I\'m feeling energetic and I have 2h free ahead of me. It\'s very hot outside though. Generate me the perfect wheel !',
            user_profile_id: '2',
            timestamp: new Date().toISOString()
        }
    };
    
    console.log('📤 Sending wheel generation request...');
    ws.send(JSON.stringify(message));
});

ws.on('message', function message(data) {
    try {
        const parsed = JSON.parse(data.toString());
        console.log(`📥 Received message type: ${parsed.type}`);
        
        if (parsed.type === 'wheel_data') {
            console.log('🎡 Wheel data received!');
            wheelReceived = true;
            
            if (parsed.wheel && parsed.wheel.activities) {
                console.log(`✅ Wheel has ${parsed.wheel.activities.length} activities`);
                
                // Check for distinct colors
                const colors = parsed.wheel.activities.map(activity => activity.color).filter(Boolean);
                const uniqueColors = [...new Set(colors)];
                console.log(`🎨 Found ${uniqueColors.length} unique colors: ${uniqueColors.join(', ')}`);
                
                if (uniqueColors.length >= 4) {
                    console.log('✅ Wheel has sufficient distinct colors');
                } else {
                    console.log('⚠️ Wheel needs more distinct colors');
                }
                
                // Log activity details
                parsed.wheel.activities.forEach((activity, index) => {
                    console.log(`  Activity ${index + 1}: ${activity.title} (${activity.color || 'no color'})`);
                });
                
                console.log('✅ Test completed successfully!');
                clearTimeout(timeout);
                ws.close();
                process.exit(0);
            } else {
                console.log('❌ Wheel data missing activities');
            }
        } else if (parsed.type === 'chat_message') {
            console.log(`💬 Chat message: ${parsed.message?.substring(0, 100)}...`);
        } else if (parsed.type === 'processing_status') {
            console.log(`⚙️ Processing status: ${parsed.status}`);
        } else {
            console.log(`📋 Other message: ${JSON.stringify(parsed).substring(0, 200)}...`);
        }
    } catch (error) {
        console.log(`❌ Error parsing message: ${error.message}`);
        console.log(`Raw data: ${data.toString().substring(0, 200)}...`);
    }
});

ws.on('error', function error(err) {
    console.log(`❌ WebSocket error: ${err.message}`);
    clearTimeout(timeout);
    process.exit(1);
});

ws.on('close', function close(code, reason) {
    console.log(`🔌 WebSocket closed: ${code} - ${reason}`);
    if (!wheelReceived) {
        console.log('❌ Test failed: Wheel data not received');
        clearTimeout(timeout);
        process.exit(1);
    }
});
