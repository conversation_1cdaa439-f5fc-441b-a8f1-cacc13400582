#!/usr/bin/env node

/**
 * Comprehensive test for wheel spin fix validation
 * Tests the complete flow: authentication, wheel generation, spin functionality, and winning modal
 */

const { chromium } = require('playwright');

const CONFIG = {
  frontendUrl: process.argv[2] || 'http://localhost:3002',
  timeout: 30000,
  testUser: {
    id: '2',
    name: '<PERSON><PERSON><PERSON>'
  }
};

async function runWheelSpinTest() {
  console.log('🎯 Starting Wheel Spin Fix Validation Test...');
  console.log(`Frontend URL: ${CONFIG.frontendUrl}`);

  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 1000 // Slow down for visibility
  });
  
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // Step 1: Navigate to frontend
    console.log('\n📍 Step 1: Loading frontend...');
    await page.goto(CONFIG.frontendUrl);
    await page.waitForTimeout(2000);

    // Step 2: Check authentication state
    console.log('\n🔐 Step 2: Checking authentication...');
    
    // Look for login form or authenticated content
    const loginForm = await page.locator('login-form').first();
    const isLoginVisible = await loginForm.isVisible().catch(() => false);
    
    if (isLoginVisible) {
      console.log('❌ Login form is visible - authentication system working');
      console.log('ℹ️  For this test, we need to be authenticated. Please implement demo mode or login.');
      
      // Try to find demo mode button
      const demoButton = await page.locator('button:has-text("Demo Mode")').first();
      const isDemoVisible = await demoButton.isVisible().catch(() => false);
      
      if (isDemoVisible) {
        console.log('🎮 Found demo mode button, clicking...');
        await demoButton.click();
        await page.waitForTimeout(2000);
      } else {
        console.log('⚠️  No demo mode found. Test requires authentication.');
        return false;
      }
    } else {
      console.log('✅ Already authenticated or no login required');
    }

    // Step 3: Check for debug panel access (staff users only)
    console.log('\n🐛 Step 3: Checking debug panel access...');
    const debugButton = await page.locator('button:has-text("Debug")').first();
    const isDebugVisible = await debugButton.isVisible().catch(() => false);
    
    if (isDebugVisible) {
      console.log('✅ Debug panel available (user has staff privileges)');
      await debugButton.click();
      await page.waitForTimeout(1000);
      
      // Select PhiPhi user
      const userSelect = await page.locator('select[data-testid="debug-user-select"]').first();
      const isUserSelectVisible = await userSelect.isVisible().catch(() => false);
      
      if (isUserSelectVisible) {
        console.log('👤 Selecting PhiPhi user...');
        await userSelect.selectOption('2');
        await page.waitForTimeout(1000);
      }
    } else {
      console.log('ℹ️  Debug panel not available (non-staff user)');
    }

    // Step 4: Set wheel generation parameters
    console.log('\n⚙️  Step 4: Setting wheel parameters...');
    
    // Set time available to 10min (minimum)
    const timeSlider = await page.locator('input[type="range"]').first();
    const isTimeSliderVisible = await timeSlider.isVisible().catch(() => false);
    
    if (isTimeSliderVisible) {
      console.log('⏱️  Setting time available to 10min...');
      await timeSlider.fill('0'); // 0% = 10min
      await page.waitForTimeout(500);
    }

    // Set energy level to 100%
    const energySlider = await page.locator('input[type="range"]').nth(1);
    const isEnergySliderVisible = await energySlider.isVisible().catch(() => false);
    
    if (isEnergySliderVisible) {
      console.log('⚡ Setting energy level to 100%...');
      await energySlider.fill('100');
      await page.waitForTimeout(500);
    }

    // Step 5: Generate wheel
    console.log('\n🎡 Step 5: Generating wheel...');
    const generateButton = await page.locator('button:has-text("Generate")').first();
    const isGenerateVisible = await generateButton.isVisible().catch(() => false);
    
    if (!isGenerateVisible) {
      console.log('❌ Generate button not found');
      return false;
    }

    await generateButton.click();
    console.log('🔄 Wheel generation started...');

    // Wait for wheel to be generated (button should change to "SPIN!")
    console.log('⏳ Waiting for wheel generation to complete...');
    await page.waitForFunction(() => {
      const button = document.querySelector('button');
      return button && button.textContent.includes('SPIN!');
    }, { timeout: 60000 });

    console.log('✅ Wheel generated successfully!');

    // Step 6: Test wheel spin functionality
    console.log('\n🎯 Step 6: Testing wheel spin...');
    
    const spinButton = await page.locator('button:has-text("SPIN!")').first();
    const isSpinVisible = await spinButton.isVisible().catch(() => false);
    
    if (!isSpinVisible) {
      console.log('❌ SPIN button not found');
      return false;
    }

    // Check console for any errors before spinning
    const consoleLogs = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleLogs.push(`ERROR: ${msg.text()}`);
      } else if (msg.text().includes('[WHEEL]')) {
        consoleLogs.push(`WHEEL: ${msg.text()}`);
      }
    });

    console.log('🎲 Clicking SPIN button...');
    await spinButton.click();

    // Wait for spin to complete and winning modal to appear
    console.log('⏳ Waiting for spin to complete...');
    
    try {
      // Wait for winning modal to appear
      await page.waitForSelector('.winning-modal-overlay', { timeout: 15000 });
      console.log('🎉 Winning modal appeared!');

      // Check modal content
      const modalContent = await page.locator('.winning-modal-content').first();
      const activityName = await modalContent.locator('.activity-name').textContent();
      const activityDescription = await modalContent.locator('.activity-description').textContent();
      
      console.log(`🏆 Winning activity: ${activityName}`);
      console.log(`📝 Description: ${activityDescription}`);

      // Test modal buttons
      const primaryButton = await modalContent.locator('.btn-primary').first();
      const secondaryButton = await modalContent.locator('.btn-secondary').first();
      
      const isPrimaryVisible = await primaryButton.isVisible();
      const isSecondaryVisible = await secondaryButton.isVisible();
      
      console.log(`✅ Primary button visible: ${isPrimaryVisible}`);
      console.log(`✅ Secondary button visible: ${isSecondaryVisible}`);

      // Close modal
      await primaryButton.click();
      console.log('✅ Modal closed successfully');

    } catch (error) {
      console.log('❌ Winning modal did not appear or had issues:', error.message);
      
      // Check console logs for debugging
      console.log('\n🔍 Console logs during spin:');
      consoleLogs.forEach(log => console.log(`  ${log}`));
      
      return false;
    }

    // Step 7: Test activity modal functionality
    console.log('\n📋 Step 7: Testing activity modal...');
    
    // Look for activity items in the wheel
    const wheelActivities = await page.locator('.wheel-item, .activity-item').all();
    
    if (wheelActivities.length > 0) {
      console.log(`Found ${wheelActivities.length} wheel activities`);
      
      // Try to click on first activity to open modal
      await wheelActivities[0].click();
      await page.waitForTimeout(1000);
      
      // Check if activity modal opened
      const activityModal = await page.locator('.modal-overlay').first();
      const isModalVisible = await activityModal.isVisible().catch(() => false);
      
      if (isModalVisible) {
        console.log('✅ Activity modal opened');
        
        // Check for activity catalog
        const catalogItems = await page.locator('.catalog-item').all();
        console.log(`📚 Found ${catalogItems.length} catalog activities`);
        
        // Check for tailored vs generic differentiation
        const tailoredItems = await page.locator('.catalog-item.tailored').all();
        const genericItems = await page.locator('.catalog-item:not(.tailored)').all();
        
        console.log(`⭐ Tailored activities: ${tailoredItems.length}`);
        console.log(`🎯 Generic activities: ${genericItems.length}`);
        
        // Close modal
        const closeButton = await page.locator('.modal-close').first();
        await closeButton.click();
        console.log('✅ Activity modal closed');
      } else {
        console.log('⚠️  Activity modal did not open');
      }
    }

    // Step 8: Final validation
    console.log('\n✅ Step 8: Final validation...');
    console.log('🎯 All wheel spin functionality tests completed successfully!');
    
    // Print summary
    console.log('\n📊 TEST SUMMARY:');
    console.log('✅ Authentication system working');
    console.log('✅ Wheel generation working');
    console.log('✅ Wheel spin functionality working');
    console.log('✅ Winning modal displaying correctly');
    console.log('✅ Activity modal system working');
    console.log('✅ Activity catalog with tailored/generic differentiation');

    return true;

  } catch (error) {
    console.error('❌ Test failed:', error);
    return false;
  } finally {
    await browser.close();
  }
}

// Run the test
runWheelSpinTest().then(success => {
  if (success) {
    console.log('\n🎉 WHEEL SPIN FIX VALIDATION: SUCCESS!');
    process.exit(0);
  } else {
    console.log('\n❌ WHEEL SPIN FIX VALIDATION: FAILED!');
    process.exit(1);
  }
}).catch(error => {
  console.error('💥 Test execution failed:', error);
  process.exit(1);
});
