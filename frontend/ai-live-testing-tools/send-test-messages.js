#!/usr/bin/env node

/**
 * Send Test Messages
 * 
 * Sends test messages to the game WebSocket while you have the dashboard open in browser
 */

import WebSocket from 'ws';

const CONFIG = {
  user: { id: '2', name: '<PERSON><PERSON><PERSON>' }
};

class TestMessageSender {
  constructor() {
    this.gameSocket = null;
    this.messageCount = 0;
  }

  async run() {
    console.log('🔍 TEST MESSAGE SENDER');
    console.log('=====================');
    console.log('This tool sends test messages to the game WebSocket.');
    console.log('Open the admin dashboard in your browser to see the messages appear.\n');
    console.log('Dashboard URL: http://localhost:8000/admin/admin_tools/connection_dashboard/');
    console.log('Make sure to enable "Message Inspector" in the dashboard!\n');

    try {
      await this.connectGame();
      await this.sendMessages();
    } catch (error) {
      console.error('❌ Error:', error.message);
    } finally {
      this.cleanup();
    }
  }

  async connectGame() {
    console.log('📋 Connecting to Game WebSocket...');
    
    return new Promise((resolve, reject) => {
      this.gameSocket = new WebSocket('ws://localhost:8000/ws/game/');

      this.gameSocket.on('open', () => {
        console.log('✅ Connected to game WebSocket');
        resolve();
      });

      this.gameSocket.on('message', (data) => {
        try {
          const message = JSON.parse(data);
          console.log(`📨 Received: ${message.type}`);
          if (message.content) {
            console.log(`   Content: ${message.content.substring(0, 100)}...`);
          }
        } catch (error) {
          console.log('📨 Received raw data');
        }
      });

      this.gameSocket.on('error', (error) => {
        console.log('❌ Game connection error:', error.message);
        reject(error);
      });

      setTimeout(() => reject(new Error('Connection timeout')), 10000);
    });
  }

  async sendMessages() {
    console.log('\n📋 Sending Test Messages...');
    console.log('---------------------------');
    console.log('Watch the admin dashboard for these messages to appear!\n');

    const messages = [
      'Hello! This is a test message to verify the dashboard.',
      'Testing message flow between frontend and backend.',
      'Can you see this message in the admin dashboard?',
      'Message flow debugging in progress...',
      'Final test message - dashboard should show all of these!'
    ];

    for (let i = 0; i < messages.length; i++) {
      const messageText = messages[i];
      this.messageCount++;
      
      const message = {
        type: 'chat_message',
        content: {
          message: messageText,
          user_profile_id: CONFIG.user.id,
          timestamp: new Date().toISOString()
        }
      };

      console.log(`📤 Message ${this.messageCount}: "${messageText}"`);
      this.gameSocket.send(JSON.stringify(message));
      
      // Wait between messages
      await new Promise(resolve => setTimeout(resolve, 3000));
    }

    console.log('\n✅ All test messages sent!');
    console.log('Check the admin dashboard to see if they appear in the Message Inspector.');
    console.log('\nIf messages appear:');
    console.log('  ✅ The message flow is working correctly');
    console.log('\nIf messages do NOT appear:');
    console.log('  ❌ There is an issue with the broadcast_message_flow implementation');
    
    // Keep connection alive for a bit longer
    console.log('\nKeeping connection alive for 10 more seconds...');
    await new Promise(resolve => setTimeout(resolve, 10000));
  }

  cleanup() {
    console.log('\n🧹 Closing connection...');
    
    if (this.gameSocket) {
      this.gameSocket.close();
    }
    
    console.log('✅ Connection closed');
  }
}

// Run the sender
const sender = new TestMessageSender();
sender.run().then(() => {
  process.exit(0);
}).catch((error) => {
  console.error('❌ Failed:', error);
  process.exit(1);
});
