#!/usr/bin/env node

/**
 * Debug Panel Inspector - Uses the frontend's debug panel to get detailed backend information
 * This tool opens the frontend, activates the debug panel, and extracts detailed connection info
 */

const { chromium } = require('playwright');

class DebugPanelInspector {
  constructor() {
    this.browser = null;
    this.page = null;
    this.debugInfo = {};
  }

  async initialize() {
    console.log('🔍 Initializing Debug Panel Inspector...');
    
    this.browser = await chromium.launch({
      headless: false,
      devtools: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    this.page = await this.browser.newPage();
    
    // Enable console logging
    this.page.on('console', msg => {
      const type = msg.type();
      const text = msg.text();
      console.log(`🖥️  [${type}] ${text}`);
    });
    
    // Enable error logging
    this.page.on('pageerror', error => {
      console.log(`❌ Page error: ${error.message}`);
    });
    
    console.log('✅ Debug Panel Inspector initialized');
  }

  async loadFrontend() {
    console.log('🌐 Loading frontend...');
    
    await this.page.goto('http://localhost:3000', { 
      waitUntil: 'domcontentloaded',
      timeout: 30000 
    });
    
    // Wait for app to initialize
    await this.page.waitForTimeout(5000);
    
    console.log('✅ Frontend loaded');
  }

  async activateDebugPanel() {
    console.log('🐛 Activating debug panel...');
    
    try {
      // Try to activate debug panel with Ctrl+Shift+D
      await this.page.keyboard.down('Control');
      await this.page.keyboard.down('Shift');
      await this.page.keyboard.press('KeyD');
      await this.page.keyboard.up('Shift');
      await this.page.keyboard.up('Control');
      
      // Wait for debug panel to appear
      await this.page.waitForTimeout(2000);
      
      // Check if debug panel is visible
      const debugPanel = await this.page.locator('[data-testid="debug-panel"], .debug-panel, #debug-panel').first();
      const isVisible = await debugPanel.isVisible().catch(() => false);
      
      if (isVisible) {
        console.log('✅ Debug panel activated');
        return true;
      } else {
        console.log('⚠️  Debug panel not visible, trying alternative methods...');
        
        // Try clicking on debug mode indicator if present
        const debugIndicator = await this.page.locator('text="Debug mode active"').first();
        const indicatorVisible = await debugIndicator.isVisible().catch(() => false);
        
        if (indicatorVisible) {
          await debugIndicator.click();
          await this.page.waitForTimeout(1000);
          console.log('✅ Debug panel activated via indicator');
          return true;
        }
        
        return false;
      }
    } catch (error) {
      console.log(`❌ Error activating debug panel: ${error.message}`);
      return false;
    }
  }

  async extractDebugInfo() {
    console.log('📊 Extracting debug information...');
    
    const debugInfo = await this.page.evaluate(() => {
      const info = {
        websocket: {},
        config: {},
        state: {},
        errors: [],
        performance: {}
      };
      
      // Check for global debug objects
      if (window.goaliApp) {
        info.websocket.isConnected = window.goaliApp.isConnected || false;
        info.websocket.connectionState = window.goaliApp.connectionState || 'unknown';
        info.websocket.lastError = window.goaliApp.lastError || null;
        info.websocket.reconnectAttempts = window.goaliApp.reconnectAttempts || 0;
      }
      
      // Check for WebSocket manager
      if (window.__GOALI_WS__) {
        info.websocket.readyState = window.__GOALI_WS__.readyState;
        info.websocket.url = window.__GOALI_WS__.url;
        info.websocket.protocol = window.__GOALI_WS__.protocol;
      }
      
      // Check for config service
      if (window.configService) {
        info.config.mode = window.configService.getMode ? window.configService.getMode() : 'unknown';
        info.config.websocketUrl = window.configService.getWebSocketUrl ? window.configService.getWebSocketUrl() : 'unknown';
        info.config.isDebugMode = window.configService.isDebugMode ? window.configService.isDebugMode() : false;
      }
      
      // Check for state manager
      if (window.stateManager) {
        info.state.currentState = window.stateManager.getState ? window.stateManager.getState() : 'unknown';
        info.state.isDemo = window.stateManager.isDemoMode ? window.stateManager.isDemoMode() : false;
      }
      
      // Check for error logs
      if (window.errorLog) {
        info.errors = window.errorLog.slice(-10); // Last 10 errors
      }
      
      // Check for performance data
      if (window.performance) {
        info.performance.timing = {
          domContentLoaded: window.performance.timing.domContentLoadedEventEnd - window.performance.timing.navigationStart,
          loadComplete: window.performance.timing.loadEventEnd - window.performance.timing.navigationStart
        };
      }
      
      // Check for debug panel data
      const debugPanel = document.querySelector('[data-testid="debug-panel"], .debug-panel, #debug-panel');
      if (debugPanel) {
        info.debugPanel = {
          visible: debugPanel.offsetParent !== null,
          innerHTML: debugPanel.innerHTML.substring(0, 500) // First 500 chars
        };
      }
      
      return info;
    });
    
    this.debugInfo = debugInfo;
    console.log('📋 Debug information extracted');
    return debugInfo;
  }

  async checkWebSocketConnection() {
    console.log('🔌 Checking WebSocket connection details...');
    
    const wsInfo = await this.page.evaluate(() => {
      const info = {
        attempts: [],
        errors: [],
        currentState: 'unknown'
      };
      
      // Check if there's a WebSocket connection attempt log
      if (window.wsConnectionLog) {
        info.attempts = window.wsConnectionLog;
      }
      
      // Check for WebSocket errors
      if (window.wsErrorLog) {
        info.errors = window.wsErrorLog;
      }
      
      // Try to get current WebSocket state
      if (window.__GOALI_WS__) {
        const ws = window.__GOALI_WS__;
        info.currentState = {
          readyState: ws.readyState,
          readyStateText: ['CONNECTING', 'OPEN', 'CLOSING', 'CLOSED'][ws.readyState],
          url: ws.url,
          protocol: ws.protocol,
          extensions: ws.extensions
        };
      }
      
      return info;
    });
    
    console.log('🔌 WebSocket connection info:');
    console.log(JSON.stringify(wsInfo, null, 2));
    
    return wsInfo;
  }

  async testBackendConnectivity() {
    console.log('🌐 Testing backend connectivity...');
    
    try {
      // Test HTTP endpoint
      const httpResponse = await this.page.evaluate(async () => {
        try {
          const response = await fetch('http://localhost:8000/admin/', { method: 'HEAD' });
          return {
            status: response.status,
            ok: response.ok,
            headers: Object.fromEntries(response.headers.entries())
          };
        } catch (error) {
          return { error: error.message };
        }
      });
      
      console.log('🌐 HTTP connectivity:', httpResponse);
      
      // Test WebSocket endpoint directly
      const wsTest = await this.page.evaluate(() => {
        return new Promise((resolve) => {
          const ws = new WebSocket('ws://localhost:8000/ws/game/');
          const timeout = setTimeout(() => {
            ws.close();
            resolve({ result: 'timeout', error: 'Connection timeout' });
          }, 5000);
          
          ws.onopen = () => {
            clearTimeout(timeout);
            ws.close();
            resolve({ result: 'success', message: 'WebSocket connection successful' });
          };
          
          ws.onerror = (error) => {
            clearTimeout(timeout);
            resolve({ result: 'error', error: error.message || 'WebSocket error' });
          };
          
          ws.onclose = (event) => {
            clearTimeout(timeout);
            resolve({ 
              result: 'closed', 
              code: event.code, 
              reason: event.reason,
              wasClean: event.wasClean
            });
          };
        });
      });
      
      console.log('🔌 Direct WebSocket test:', wsTest);
      
      return { http: httpResponse, websocket: wsTest };
    } catch (error) {
      console.log(`❌ Backend connectivity test failed: ${error.message}`);
      return { error: error.message };
    }
  }

  async generateReport() {
    console.log('📊 Generating comprehensive debug report...');
    
    const report = {
      timestamp: new Date().toISOString(),
      frontend: this.debugInfo,
      connectivity: await this.testBackendConnectivity(),
      websocket: await this.checkWebSocketConnection()
    };
    
    console.log('\n🎯 COMPREHENSIVE DEBUG REPORT');
    console.log('═'.repeat(50));
    console.log(JSON.stringify(report, null, 2));
    
    return report;
  }

  async runFullInspection() {
    try {
      await this.initialize();
      await this.loadFrontend();
      
      // Try to activate debug panel
      const debugPanelActive = await this.activateDebugPanel();
      
      // Extract debug information
      await this.extractDebugInfo();
      
      // Generate comprehensive report
      const report = await this.generateReport();
      
      console.log('\n🎯 INSPECTION SUMMARY:');
      console.log(`  Frontend loaded: ✅`);
      console.log(`  Debug panel active: ${debugPanelActive ? '✅' : '❌'}`);
      console.log(`  WebSocket connected: ${report.frontend.websocket.isConnected ? '✅' : '❌'}`);
      console.log(`  Demo mode: ${report.frontend.state.isDemo ? '⚠️  YES' : '✅ NO'}`);
      
      console.log('\n🔍 Browser kept open for manual inspection. Press Ctrl+C to exit.');
      
      // Keep browser open for manual inspection
      await new Promise(() => {});
      
    } catch (error) {
      console.error('❌ Inspection failed:', error);
    } finally {
      // Don't auto-cleanup to allow manual inspection
    }
  }
}

// Run the inspector
const inspector = new DebugPanelInspector();
inspector.runFullInspection().catch(console.error);
