#!/usr/bin/env node

/**
 * Comprehensive test for wheel item removal error detection and validation
 * 
 * This test specifically reproduces and validates the fix for the wheel item removal error:
 * "ERROR:apps.main.api_views:Wheel item llm_tailored_d0078602 not found in user's current wheel"
 * 
 * Usage: node test-wheel-item-removal-fix.cjs [port]
 */

const puppeteer = require('puppeteer');

async function testWheelItemRemovalFix(port = 5173) {
    const browser = await puppeteer.launch({ 
        headless: false,
        defaultViewport: { width: 1400, height: 900 },
        args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // Enable console logging
    page.on('console', msg => {
        const text = msg.text();
        if (text.includes('🗑️') || text.includes('wheel') || text.includes('removal') || text.includes('ERROR')) {
            console.log(`🖥️  ${text}`);
        }
    });
    
    // Monitor network requests for wheel item removal
    const removalRequests = [];
    page.on('request', request => {
        if (request.url().includes('/api/wheel-items/') && request.method() === 'DELETE') {
            const wheelItemId = request.url().split('/api/wheel-items/')[1].replace('/', '');
            removalRequests.push({
                url: request.url(),
                wheelItemId: wheelItemId,
                timestamp: Date.now()
            });
            console.log(`🔍 DELETE request detected for wheel item: ${wheelItemId}`);
        }
    });
    
    // Monitor responses for errors
    page.on('response', response => {
        if (response.url().includes('/api/wheel-items/') && response.request().method() === 'DELETE') {
            if (response.status() === 400) {
                console.log(`🚨 ERROR: Wheel item removal failed with 400 Bad Request`);
                console.log(`🚨 URL: ${response.url()}`);
            } else if (response.status() === 200) {
                console.log(`✅ SUCCESS: Wheel item removal succeeded`);
            }
        }
    });
    
    try {
        console.log(`🚀 Starting wheel item removal test on port ${port}`);
        await page.goto(`http://localhost:${port}`, { waitUntil: 'networkidle0' });
        
        // Wait for app to load
        await page.waitForSelector('app-shell', { timeout: 10000 });
        console.log('✅ App loaded successfully');
        
        // Open debug panel
        await page.keyboard.down('Control');
        await page.keyboard.down('Shift');
        await page.keyboard.press('KeyD');
        await page.keyboard.up('Shift');
        await page.keyboard.up('Control');
        
        await new Promise(resolve => setTimeout(resolve, 1000));
        console.log('✅ Debug panel opened');
        
        // Select PhiPhi user (user 2)
        const userSelect = await page.waitForSelector('select[data-testid="user-select"]', { timeout: 5000 });
        await userSelect.selectByValue('2');
        console.log('✅ Selected PhiPhi user (ID: 2)');
        
        // Select LLM
        const llmSelect = await page.waitForSelector('select[data-testid="llm-select"]', { timeout: 5000 });
        await llmSelect.selectByValue('mistral-small-latest');
        console.log('✅ Selected mistral-small-latest LLM');
        
        // Apply configuration
        const applyButton = await page.waitForSelector('button[data-testid="apply-config"]', { timeout: 5000 });
        await applyButton.click();
        console.log('✅ Applied configuration');
        
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Generate a wheel first
        console.log('🎡 Generating wheel...');
        const generateButton = await page.waitForSelector('button[data-testid="generate-wheel"]', { timeout: 10000 });
        await generateButton.click();
        
        // Wait for wheel generation to complete (up to 2 minutes)
        console.log('⏳ Waiting for wheel generation to complete...');
        await page.waitForFunction(() => {
            const appShell = document.querySelector('app-shell');
            return appShell && appShell.wheelData && appShell.wheelData.segments && appShell.wheelData.segments.length > 0;
        }, { timeout: 120000 });
        
        console.log('✅ Wheel generated successfully');
        
        // Get wheel data for analysis
        const wheelData = await page.evaluate(() => {
            const appShell = document.querySelector('app-shell');
            return appShell ? appShell.wheelData : null;
        });
        
        if (!wheelData || !wheelData.segments || wheelData.segments.length === 0) {
            throw new Error('No wheel data found after generation');
        }
        
        console.log(`📊 Wheel has ${wheelData.segments.length} segments`);
        
        // Analyze wheel data structure
        console.log('🔍 Analyzing wheel data structure...');
        wheelData.segments.forEach((segment, index) => {
            console.log(`   Segment ${index + 1}:`);
            console.log(`     - ID: ${segment.id}`);
            console.log(`     - Name: ${segment.name || segment.text}`);
            console.log(`     - Activity Tailored ID: ${segment.activity_tailored_id}`);
            
            // Check for potential ID mapping issues
            if (segment.id && segment.id.startsWith('llm_tailored_')) {
                console.log(`🚨 POTENTIAL ISSUE: Segment ID looks like activity tailored ID: ${segment.id}`);
            } else if (segment.id && segment.id.startsWith('item_')) {
                console.log(`✅ CORRECT: Segment ID looks like wheel item ID: ${segment.id}`);
            }
        });
        
        // Wait for activity list to be visible
        await page.waitForSelector('.activity-list', { timeout: 10000 });
        console.log('✅ Activity list is visible');
        
        // Find and click a remove button
        const removeButtons = await page.$$('.remove-activity-btn');
        if (removeButtons.length === 0) {
            throw new Error('No remove buttons found in activity list');
        }
        
        console.log(`🎯 Found ${removeButtons.length} remove buttons`);
        
        // Click the first remove button
        console.log('🗑️ Clicking remove button for first activity...');
        await removeButtons[0].click();
        
        // Wait for feedback modal to appear
        await page.waitForSelector('.modal-overlay', { timeout: 5000 });
        console.log('✅ Feedback modal appeared');
        
        // Click the "Remove Activity" button
        const removeActivityButton = await page.waitForSelector('button.btn-primary', { timeout: 5000 });
        const buttonText = await removeActivityButton.evaluate(el => el.textContent);
        
        if (buttonText.includes('Remove Activity')) {
            console.log('✅ Found "Remove Activity" button');
            await removeActivityButton.click();
            
            // Wait for the removal request to complete
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            // Analyze the results
            if (removalRequests.length > 0) {
                const lastRequest = removalRequests[removalRequests.length - 1];
                console.log(`📊 Removal request analysis:`);
                console.log(`   - Wheel Item ID sent: ${lastRequest.wheelItemId}`);
                
                if (lastRequest.wheelItemId.startsWith('llm_tailored_') || lastRequest.wheelItemId.startsWith('generic-')) {
                    console.log(`🚨 ERROR DETECTED: Frontend sent activity tailored ID instead of wheel item ID!`);
                    console.log(`🚨 This is the root cause of the backend error`);
                    return { success: false, error: 'Frontend sending wrong ID type', wheelItemId: lastRequest.wheelItemId };
                } else if (lastRequest.wheelItemId.startsWith('item_')) {
                    console.log(`✅ SUCCESS: Frontend sent correct wheel item ID format`);
                    return { success: true, wheelItemId: lastRequest.wheelItemId };
                } else {
                    console.log(`⚠️  WARNING: Unexpected wheel item ID format: ${lastRequest.wheelItemId}`);
                    return { success: false, error: 'Unexpected ID format', wheelItemId: lastRequest.wheelItemId };
                }
            } else {
                console.log(`🚨 ERROR: No removal requests detected`);
                return { success: false, error: 'No removal request sent' };
            }
        } else {
            throw new Error(`Expected "Remove Activity" button, found: ${buttonText}`);
        }
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        return { success: false, error: error.message };
    } finally {
        await browser.close();
    }
}

// Run the test
if (require.main === module) {
    const port = process.argv[2] || 5173;
    testWheelItemRemovalFix(parseInt(port))
        .then(result => {
            console.log('\n📊 TEST RESULTS:');
            console.log('================');
            if (result.success) {
                console.log('✅ WHEEL ITEM REMOVAL FIX VALIDATED');
                console.log(`✅ Correct wheel item ID sent: ${result.wheelItemId}`);
                process.exit(0);
            } else {
                console.log('❌ WHEEL ITEM REMOVAL FIX FAILED');
                console.log(`❌ Error: ${result.error}`);
                if (result.wheelItemId) {
                    console.log(`❌ Problematic ID: ${result.wheelItemId}`);
                }
                process.exit(1);
            }
        })
        .catch(error => {
            console.error('💥 Test execution failed:', error);
            process.exit(1);
        });
}

module.exports = { testWheelItemRemovalFix };
