#!/usr/bin/env node

/**
 * Simple UI Validator
 * 
 * This tool validates the UI layer by testing wheel data processing
 * and identifying common frontend issues without complex DOM simulation.
 */

import WebSocket from 'ws';
import config from './config.js';

class SimpleUIValidator {
  constructor() {
    this.ws = null;
    this.isConnected = false;
    this.receivedMessages = [];
    this.wheelData = null;
    this.errors = [];
    this.warnings = [];
  }

  /**
   * Connect to the backend WebSocket
   */
  async connectToBackend() {
    console.log('🔌 Connecting to backend WebSocket...');
    
    return new Promise((resolve, reject) => {
      this.ws = new WebSocket(config.backend.websocketUrl);
      
      this.ws.on('open', () => {
        console.log('✅ Connected to backend');
        this.isConnected = true;
        resolve();
      });

      this.ws.on('message', (data) => {
        try {
          const message = JSON.parse(data);
          this.handleMessage(message);
        } catch (error) {
          console.error('❌ Error parsing message:', error);
          this.errors.push(`Message parsing error: ${error.message}`);
        }
      });

      this.ws.on('error', (error) => {
        console.error('❌ WebSocket error:', error);
        this.errors.push(`WebSocket error: ${error.message}`);
        reject(error);
      });

      this.ws.on('close', () => {
        console.log('🔌 WebSocket connection closed');
        this.isConnected = false;
      });
    });
  }

  /**
   * Handle incoming WebSocket messages
   */
  handleMessage(message) {
    this.receivedMessages.push(message);

    // Safe content display
    let contentDisplay = 'No content';
    if (message.content) {
      if (typeof message.content === 'string') {
        contentDisplay = message.content.substring(0, 100) + '...';
      } else {
        contentDisplay = JSON.stringify(message.content).substring(0, 100) + '...';
      }
    } else {
      contentDisplay = JSON.stringify(message);
    }

    console.log(`📨 Received ${message.type}:`, contentDisplay);

    // Validate message structure
    this.validateMessage(message);

    // Handle specific message types
    switch (message.type) {
      case 'wheel_data':
        this.validateWheelData(message);
        break;
      case 'debug_info':
        this.validateDebugInfo(message);
        break;
      case 'workflow_status':
        this.validateWorkflowStatus(message);
        break;
      case 'chat_message':
        this.validateChatMessage(message);
        break;
      case 'error':
        this.handleErrorMessage(message);
        break;
      default:
        console.log(`📝 Unhandled message type: ${message.type}`);
    }
  }

  /**
   * Validate message structure
   */
  validateMessage(message) {
    if (!message.type) {
      this.errors.push('Message missing type field');
    }
  }

  /**
   * Validate wheel data message
   */
  validateWheelData(message) {
    console.log('🎡 Validating wheel_data message...');
    
    if (!message.wheel) {
      this.errors.push('wheel_data message missing wheel property');
      return;
    }

    this.wheelData = message.wheel;
    
    // Test frontend wheel data processing
    this.testFrontendWheelProcessing(message.wheel);
  }

  /**
   * Test how frontend would process wheel data
   */
  testFrontendWheelProcessing(wheelData) {
    console.log('🧪 Testing frontend wheel data processing...');
    
    // Extract items like frontend would
    let items = [];
    if (wheelData.items) {
      items = wheelData.items;
      console.log(`✅ Found ${items.length} items in wheelData.items`);
    } else if (wheelData.activities) {
      items = wheelData.activities;
      console.log(`✅ Found ${items.length} items in wheelData.activities`);
    } else if (Array.isArray(wheelData)) {
      items = wheelData;
      console.log(`✅ wheelData is array with ${items.length} items`);
    } else {
      this.errors.push('No items found in wheel data');
      return;
    }

    if (items.length === 0) {
      this.errors.push('Wheel data contains no items');
      return;
    }

    // Test segment mapping like frontend app-shell.ts does
    const segments = items.map((item, index) => {
      // Prioritize proper titles over generic names - ALWAYS use title first if available
      let displayText = `Activity ${index + 1}`; // fallback

      // Priority order: title (most specific) > activity_name > name (often generic)
      if (item.title && item.title.trim() && !item.title.match(/^Activity \d+$/)) {
        displayText = item.title;
      } else if (item.activity_name && item.activity_name.trim()) {
        displayText = item.activity_name;
      } else if (item.name && item.name.trim() && !item.name.match(/^Activity \d+$/)) {
        displayText = item.name;
      }

      return {
        id: item.id || `item-${index}`,
        text: displayText,
        percentage: item.percentage || (item.probability * 100) || (100 / items.length),
        color: item.color || this.generateWheelColor(index)
      };
    });

    console.log('📊 Generated segments:');
    segments.forEach((segment, index) => {
      console.log(`  ${index + 1}. "${segment.text}" (${segment.percentage.toFixed(1)}%) - ${segment.color}`);

      // Check for issues
      if (segment.text.startsWith('Activity ')) {
        this.warnings.push(`Segment ${index + 1} has generic name: "${segment.text}"`);
      }

      if (segment.percentage <= 0) {
        this.errors.push(`Segment ${index + 1} has invalid percentage: ${segment.percentage}`);
      }
    });

    // Test wheel data structure
    this.validateWheelStructure(wheelData);
  }

  /**
   * Validate wheel data structure
   */
  validateWheelStructure(wheelData) {
    console.log('🔍 Validating wheel data structure...');
    
    // Check metadata
    if (!wheelData.metadata) {
      this.warnings.push('Wheel data missing metadata');
    } else {
      if (!wheelData.metadata.name) {
        this.warnings.push('Wheel metadata missing name');
      }
      if (!wheelData.metadata.trust_phase) {
        this.warnings.push('Wheel metadata missing trust_phase');
      }
    }

    // Check activities array
    if (wheelData.activities && Array.isArray(wheelData.activities)) {
      console.log(`✅ Activities array contains ${wheelData.activities.length} activities`);
      
      wheelData.activities.forEach((activity, index) => {
        if (!activity.name && !activity.title) {
          this.warnings.push(`Activity ${index + 1} missing name/title`);
        }
        if (!activity.description) {
          this.warnings.push(`Activity ${index + 1} missing description`);
        }
        if (!activity.instructions) {
          this.warnings.push(`Activity ${index + 1} missing instructions`);
        }
      });
    }

    // Check value propositions
    if (wheelData.value_propositions) {
      console.log('✅ Value propositions present');
    } else {
      this.warnings.push('Wheel data missing value_propositions');
    }
  }

  /**
   * Validate debug info message
   */
  validateDebugInfo(message) {
    if (!message.content) {
      this.errors.push('debug_info message missing content');
      return;
    }

    // Check for undefined debug messages
    const debugMessage = message.content.message || message.content;
    if (debugMessage === 'undefined' || debugMessage.includes('undefined')) {
      this.errors.push('Debug message contains "undefined"');
    }
  }

  /**
   * Validate workflow status message
   */
  validateWorkflowStatus(message) {
    if (!message.workflow_id) {
      this.errors.push('workflow_status message missing workflow_id');
    }
    if (!message.status) {
      this.errors.push('workflow_status message missing status');
    }
  }

  /**
   * Validate chat message
   */
  validateChatMessage(message) {
    if (!message.content) {
      this.errors.push('chat_message missing content');
    }
    if (typeof message.is_user !== 'boolean') {
      this.warnings.push('chat_message missing or invalid is_user field');
    }
  }

  /**
   * Handle error messages
   */
  handleErrorMessage(message) {
    console.log('❌ Error message received:', message.content || message.message || JSON.stringify(message));
    this.errors.push(`Backend error: ${message.content || message.message || 'Unknown error'}`);
  }

  /**
   * Generate wheel colors
   */
  generateWheelColor(index) {
    const colors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
      '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
    ];
    return colors[index % colors.length];
  }

  /**
   * Send a message to the backend
   */
  sendMessage(content) {
    if (!this.isConnected) {
      console.log('❌ Cannot send message - not connected');
      return;
    }

    const message = {
      type: 'chat_message',
      content: {
        message: content,
        user_profile_id: '2', // Use test user ID
        timestamp: new Date().toISOString(),
        metadata: {
          // Let the system auto-classify the workflow
        }
      }
    };

    console.log(`👤 Sending: "${content}"`);
    this.ws.send(JSON.stringify(message));
  }

  /**
   * Run the validation test
   */
  async runValidation() {
    console.log('🎬 Starting UI validation test...');
    
    try {
      // Connect to backend
      await this.connectToBackend();
      
      // Wait for connection to stabilize
      await this.sleep(1000);
      
      // Send first message
      this.sendMessage("I'm bored");

      // Wait for response and wheel generation
      console.log('⏳ Waiting for first workflow to complete...');
      await this.sleep(30000);

      // Send second message
      this.sendMessage("I feel like doing exercise, what do you propose?");

      // Wait for final response
      console.log('⏳ Waiting for second workflow to complete...');
      await this.sleep(30000);
      
      // Analyze results
      this.analyzeResults();
      
    } catch (error) {
      console.error('❌ Validation failed:', error);
      this.errors.push(`Validation failed: ${error.message}`);
    } finally {
      if (this.ws) {
        this.ws.close();
      }
    }
  }

  /**
   * Analyze validation results
   */
  analyzeResults() {
    console.log('\n🔍 UI VALIDATION ANALYSIS');
    console.log('==========================');
    
    console.log(`📊 Total messages received: ${this.receivedMessages.length}`);
    console.log(`🎡 Wheel data received: ${this.wheelData ? 'Yes' : 'No'}`);
    console.log(`❌ Errors detected: ${this.errors.length}`);
    console.log(`⚠️ Warnings detected: ${this.warnings.length}`);
    
    if (this.errors.length > 0) {
      console.log('\n❌ ERRORS FOUND:');
      this.errors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error}`);
      });
    }
    
    if (this.warnings.length > 0) {
      console.log('\n⚠️ WARNINGS FOUND:');
      this.warnings.forEach((warning, index) => {
        console.log(`  ${index + 1}. ${warning}`);
      });
    }
    
    if (this.wheelData) {
      console.log('\n🎡 WHEEL DATA SUMMARY:');
      console.log(`  - Items: ${this.wheelData.items?.length || 0}`);
      console.log(`  - Activities: ${this.wheelData.activities?.length || 0}`);
      console.log(`  - Name: ${this.wheelData.name || this.wheelData.metadata?.name || 'N/A'}`);
      console.log(`  - Trust Phase: ${this.wheelData.metadata?.trust_phase || 'N/A'}`);
    }
    
    // Overall assessment
    console.log('\n🎯 OVERALL ASSESSMENT:');
    if (this.errors.length === 0 && this.wheelData) {
      console.log('✅ UI validation PASSED - All systems working correctly');
    } else if (this.wheelData && this.errors.length < 3) {
      console.log('⚠️ UI validation PARTIAL - Core functionality working with minor issues');
    } else {
      console.log('❌ UI validation FAILED - Significant issues detected');
    }
  }

  /**
   * Sleep utility
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Run the validation if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const validator = new SimpleUIValidator();
  validator.runValidation().then(() => {
    console.log('\n🎉 UI validation completed!');
    process.exit(0);
  }).catch((error) => {
    console.error('❌ Validation failed:', error);
    process.exit(1);
  });
}

export default SimpleUIValidator;
