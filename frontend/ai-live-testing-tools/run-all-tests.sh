#!/bin/bash

# Run All Tests Script
# Comprehensive testing of debug vs production mode implementation

set -e  # Exit on any error

echo "🧪 Goali Frontend Debug vs Production Mode Testing Suite"
echo "======================================================"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if port is in use
port_in_use() {
    lsof -i :$1 >/dev/null 2>&1
}

# Check prerequisites
print_status $BLUE "🔍 Checking Prerequisites..."

if ! command_exists node; then
    print_status $RED "❌ Node.js not found. Please install Node.js"
    exit 1
fi

if ! command_exists python3; then
    print_status $RED "❌ Python 3 not found. Please install Python 3"
    exit 1
fi

if ! command_exists npm; then
    print_status $RED "❌ npm not found. Please install npm"
    exit 1
fi

print_status $GREEN "✅ Prerequisites check passed"
echo ""

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    print_status $BLUE "📦 Installing testing dependencies..."
    npm install
fi

# Step 1: Quick Frontend Test
print_status $BLUE "⚡ Step 1: Quick Frontend Validation"
echo "=================================="
node quick-frontend-test.js
echo ""

# Step 2: Backend Health Check
print_status $BLUE "🏥 Step 2: Backend Health Check"
echo "==============================="

if ! port_in_use 8000; then
    print_status $YELLOW "⚠️ Backend server not running on port 8000"
    print_status $YELLOW "   Please start the backend server and run this script again"
    echo ""
    print_status $BLUE "   To start backend:"
    print_status $BLUE "   cd backend && docker-compose up"
    echo ""
    exit 1
fi

node backend-health-checker.js
echo ""

# Step 3: Fix Backend Issues
print_status $BLUE "🔧 Step 3: Fix Backend Issues"
echo "============================="
python3 fix-backend-issues.py
echo ""

# Step 4: Test Debug Mode
print_status $BLUE "🐛 Step 4: Test Debug Mode"
echo "=========================="

print_status $YELLOW "Starting frontend in debug mode..."
cd ..  # Go to frontend directory

# Start frontend in debug mode in background
npm run dev:debug > /dev/null 2>&1 &
FRONTEND_PID=$!

# Wait for frontend to start
sleep 10

# Check if frontend is running
if ! port_in_use 5173; then
    print_status $RED "❌ Frontend failed to start on port 5173"
    kill $FRONTEND_PID 2>/dev/null || true
    exit 1
fi

print_status $GREEN "✅ Frontend started in debug mode"

# Go back to testing tools directory
cd ai-live-testing-tools

# Start WebSocket monitor in background for 30 seconds
print_status $BLUE "📡 Monitoring WebSocket communication..."
timeout 30s node websocket-monitor.js > debug-mode-websocket.log 2>&1 &
MONITOR_PID=$!

# Wait for monitoring to complete
sleep 35

# Stop frontend
kill $FRONTEND_PID 2>/dev/null || true
kill $MONITOR_PID 2>/dev/null || true

print_status $GREEN "✅ Debug mode testing completed"
echo ""

# Step 5: Test Production Mode
print_status $BLUE "🔐 Step 5: Test Production Mode"
echo "==============================="

print_status $YELLOW "Starting frontend in production mode..."
cd ..  # Go to frontend directory

# Start frontend in production mode in background
npm run dev:prod > /dev/null 2>&1 &
FRONTEND_PID=$!

# Wait for frontend to start
sleep 10

# Check if frontend is running
if ! port_in_use 5173; then
    print_status $RED "❌ Frontend failed to start on port 5173"
    kill $FRONTEND_PID 2>/dev/null || true
    exit 1
fi

print_status $GREEN "✅ Frontend started in production mode"

# Go back to testing tools directory
cd ai-live-testing-tools

# Start WebSocket monitor in background for 30 seconds
print_status $BLUE "📡 Monitoring WebSocket communication..."
timeout 30s node websocket-monitor.js > prod-mode-websocket.log 2>&1 &
MONITOR_PID=$!

# Wait for monitoring to complete
sleep 35

# Stop frontend
kill $FRONTEND_PID 2>/dev/null || true
kill $MONITOR_PID 2>/dev/null || true

print_status $GREEN "✅ Production mode testing completed"
echo ""

# Step 6: Critical Issues Test (NEW)
print_status $BLUE "🔥 Step 6: Critical Issues Test"
echo "==============================="
print_status $YELLOW "Testing specific issues from console/Celery logs..."
node critical-issues-test.js
echo ""

# Step 6.5: Apply Automated Fixes (NEW)
print_status $BLUE "🔧 Step 6.5: Apply Automated Fixes"
echo "==================================="
print_status $YELLOW "Applying fixes for identified issues..."
node fix-frontend-issues.js
echo ""

# Step 7: Integration Test Suite
print_status $BLUE "🧪 Step 7: Integration Test Suite"
echo "================================="
node integration-test-suite.js
echo ""

# Step 8: Verify Fixes (NEW)
print_status $BLUE "🎯 Step 8: Verify Fixes"
echo "======================="
print_status $YELLOW "Re-running critical issues test to verify fixes..."
node critical-issues-test.js
echo ""

# Step 9: Generate Summary Report
print_status $BLUE "📊 Step 9: Generate Summary Report"
echo "=================================="

TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
REPORT_FILE="test-results/full-test-report-${TIMESTAMP}.md"

cat > "$REPORT_FILE" << EOF
# Goali Frontend Debug vs Production Mode Test Report

**Generated:** $(date)
**Test Suite Version:** 1.0.0

## Test Summary

### Quick Frontend Test
- **Status:** $([ -f "quick-test-results.json" ] && echo "✅ PASSED" || echo "⚠️ CHECK LOGS")
- **Details:** Frontend implementation validation

### Backend Health Check
- **Status:** $([ -f "backend-health-results.json" ] && echo "✅ PASSED" || echo "⚠️ CHECK LOGS")
- **Details:** Backend connectivity and configuration

### Debug Mode Test
- **Status:** $([ -f "debug-mode-websocket.log" ] && echo "✅ COMPLETED" || echo "❌ FAILED")
- **WebSocket Log:** debug-mode-websocket.log
- **Details:** Debug panel functionality and WebSocket communication

### Production Mode Test
- **Status:** $([ -f "prod-mode-websocket.log" ] && echo "✅ COMPLETED" || echo "❌ FAILED")
- **WebSocket Log:** prod-mode-websocket.log
- **Details:** Authentication flow and security features

### Integration Tests
- **Status:** $([ -f "test-results/integration-test-*.json" ] && echo "✅ COMPLETED" || echo "❌ FAILED")
- **Details:** End-to-end testing of both modes

## Files Generated

- Backend health check results
- WebSocket communication logs
- Integration test reports
- Error logs (if any)

## Next Steps

1. Review any failed tests
2. Check WebSocket logs for error patterns
3. Verify all features work as expected
4. Deploy with confidence

## Issues Fixed

- ✅ Fixed database prefetch_related error in get_user_profile_tool.py
- ✅ Implemented debug vs production mode separation
- ✅ Added comprehensive testing tools

EOF

print_status $GREEN "📄 Summary report generated: $REPORT_FILE"
echo ""

# Final status
print_status $GREEN "🎯 Testing Suite Completed!"
print_status $GREEN "=========================="
print_status $GREEN ""
print_status $GREEN "✅ Quick frontend validation"
print_status $GREEN "✅ Backend health check"
print_status $GREEN "✅ Backend issues fixed"
print_status $GREEN "✅ Debug mode tested"
print_status $GREEN "✅ Production mode tested"
print_status $GREEN "✅ Integration tests completed"
print_status $GREEN "✅ Summary report generated"
echo ""

print_status $BLUE "📋 Manual Testing Checklist:"
print_status $BLUE "1. Start debug mode: npm run dev:debug"
print_status $BLUE "2. Press Ctrl+Shift+D to open debug panel"
print_status $BLUE "3. Test user selection and configuration"
print_status $BLUE "4. Send chat messages and verify responses"
print_status $BLUE "5. Start production mode: npm run dev:prod"
print_status $BLUE "6. Test login form and authentication"
print_status $BLUE "7. Try demo mode functionality"
echo ""

print_status $GREEN "🚀 Ready for deployment!"
