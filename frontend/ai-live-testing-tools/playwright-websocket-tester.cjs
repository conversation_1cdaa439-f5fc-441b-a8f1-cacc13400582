#!/usr/bin/env node

/**
 * Playwright WebSocket Integration Tester
 * 
 * This tool integrates <PERSON><PERSON>'s powerful browser automation with your existing
 * WebSocket testing methodology to provide comprehensive end-to-end testing.
 * 
 * Features:
 * - Real browser WebSocket connections
 * - WebSocket message interception and mocking
 * - Complete user story automation
 * - Visual debugging capabilities
 * - Integration with existing testing tools
 */

const { chromium, firefox, webkit } = require('playwright');
const WebSocket = require('ws');

class PlaywrightWebSocketTester {
    constructor(options = {}) {
        this.options = {
            headless: false, // Show browser for debugging
            slowMo: 500,     // Slow down actions for visibility
            timeout: 30000,
            baseURL: 'http://localhost:3001', // Updated to correct frontend URL
            backendURL: 'http://localhost:8000',
            ...options
        };
        
        this.browser = null;
        this.context = null;
        this.page = null;
        this.wsMessages = [];
        this.testResults = {
            websocketConnection: false,
            userAuthentication: false,
            wheelGeneration: false,
            wheelSpinning: false,
            messageFlow: [],
            errors: []
        };
    }

    async initialize(browserType = 'chromium') {
        console.log('🚀 Initializing Playwright WebSocket Tester...');
        
        try {
            // Launch browser
            const browserEngine = browserType === 'firefox' ? firefox : 
                                browserType === 'webkit' ? webkit : chromium;
            
            this.browser = await browserEngine.launch({
                headless: this.options.headless,
                slowMo: this.options.slowMo,
                args: ['--disable-web-security', '--disable-features=VizDisplayCompositor']
            });

            // Create context with debugging capabilities
            this.context = await this.browser.newContext({
                viewport: { width: 1280, height: 720 },
                recordVideo: { dir: './test-results/videos/' },
                recordHar: { path: './test-results/network.har' }
            });

            // Create page
            this.page = await this.context.newPage();
            
            // Enable console logging
            this.page.on('console', msg => {
                console.log(`🖥️  Browser Console [${msg.type()}]:`, msg.text());
            });

            // Enable error tracking
            this.page.on('pageerror', error => {
                console.error('❌ Page Error:', error.message);
                this.testResults.errors.push({
                    type: 'page_error',
                    message: error.message,
                    timestamp: new Date().toISOString()
                });
            });

            console.log('✅ Playwright initialized successfully');
            return true;
        } catch (error) {
            console.error('❌ Failed to initialize Playwright:', error.message);
            this.testResults.errors.push({
                type: 'initialization_error',
                message: error.message,
                timestamp: new Date().toISOString()
            });
            return false;
        }
    }

    async setupWebSocketInterception() {
        console.log('🔌 Setting up WebSocket interception...');
        
        try {
            // Intercept WebSocket connections
            await this.page.routeWebSocket('**/ws/**', ws => {
                console.log('🔗 WebSocket connection intercepted:', ws.url());
                
                // Log all messages
                ws.onMessage(message => {
                    const msgData = {
                        direction: 'received',
                        data: message,
                        timestamp: new Date().toISOString(),
                        url: ws.url()
                    };
                    this.wsMessages.push(msgData);
                    console.log('📨 WebSocket Message Received:', message);
                    this.testResults.messageFlow.push(msgData);
                });

                // Connect to actual server (pass-through mode)
                const server = ws.connectToServer();
                
                // Log outgoing messages
                ws.routeSend(message => {
                    const msgData = {
                        direction: 'sent',
                        data: message,
                        timestamp: new Date().toISOString(),
                        url: ws.url()
                    };
                    this.wsMessages.push(msgData);
                    console.log('📤 WebSocket Message Sent:', message);
                    this.testResults.messageFlow.push(msgData);
                    
                    // Forward to server
                    server.send(message);
                });
            });

            console.log('✅ WebSocket interception configured');
            return true;
        } catch (error) {
            console.error('❌ Failed to setup WebSocket interception:', error.message);
            this.testResults.errors.push({
                type: 'websocket_setup_error',
                message: error.message,
                timestamp: new Date().toISOString()
            });
            return false;
        }
    }

    async testWebSocketConnection() {
        console.log('🔌 Testing WebSocket connection...');
        
        try {
            // Navigate to the application
            await this.page.goto(this.options.baseURL);
            await this.page.waitForLoadState('networkidle');

            // Wait for WebSocket connection
            await this.page.waitForTimeout(2000);

            // Check if WebSocket is connected
            const wsConnected = await this.page.evaluate(() => {
                return window.websocket && window.websocket.readyState === WebSocket.OPEN;
            });

            this.testResults.websocketConnection = wsConnected;
            
            if (wsConnected) {
                console.log('✅ WebSocket connection established');
            } else {
                console.log('❌ WebSocket connection failed');
            }

            return wsConnected;
        } catch (error) {
            console.error('❌ WebSocket connection test failed:', error.message);
            this.testResults.errors.push({
                type: 'websocket_connection_error',
                message: error.message,
                timestamp: new Date().toISOString()
            });
            return false;
        }
    }

    async authenticateAsUser(userId = 2) {
        console.log(`👤 Authenticating as user ${userId} (phiphi)...`);
        
        try {
            // Check if debug panel exists
            const debugPanelExists = await this.page.locator('[data-debug-panel]').count() > 0;
            
            if (debugPanelExists) {
                // Open debug panel
                await this.page.keyboard.press('Control+Shift+D');
                await this.page.waitForTimeout(1000);

                // Select user
                await this.page.selectOption('select[name="user"]', userId.toString());
                await this.page.waitForTimeout(500);

                console.log('✅ Debug mode authentication completed');
                this.testResults.userAuthentication = true;
            } else {
                // Production mode - would need actual login
                console.log('⚠️  Production mode detected - authentication not implemented');
                this.testResults.userAuthentication = false;
            }

            return this.testResults.userAuthentication;
        } catch (error) {
            console.error('❌ Authentication failed:', error.message);
            this.testResults.errors.push({
                type: 'authentication_error',
                message: error.message,
                timestamp: new Date().toISOString()
            });
            return false;
        }
    }

    async simulateUserStory() {
        console.log('🎭 Simulating complete user story...');
        
        try {
            // Step 1: Send initial message
            await this.sendChatMessage("I'm bored");
            await this.waitForResponse();

            // Step 2: Respond to clarification
            await this.sendChatMessage("I feel like doing exercise");
            await this.waitForWheelGeneration();

            // Step 3: Test wheel spinning
            await this.testWheelSpinning();

            console.log('✅ User story simulation completed');
            return true;
        } catch (error) {
            console.error('❌ User story simulation failed:', error.message);
            this.testResults.errors.push({
                type: 'user_story_error',
                message: error.message,
                timestamp: new Date().toISOString()
            });
            return false;
        }
    }

    async sendChatMessage(message) {
        console.log(`💬 Sending message: "${message}"`);
        
        // Find chat input
        const chatInput = this.page.locator('input[type="text"], textarea').first();
        await chatInput.fill(message);
        
        // Send message (Enter key or button click)
        await chatInput.press('Enter');
        await this.page.waitForTimeout(1000);
    }

    async waitForResponse(timeout = 10000) {
        console.log('⏳ Waiting for AI response...');
        
        try {
            // Wait for new message in chat
            await this.page.waitForFunction(() => {
                const messages = document.querySelectorAll('.message, .chat-message');
                return messages.length > 0;
            }, { timeout });
            
            console.log('✅ Response received');
            return true;
        } catch (error) {
            console.log('⚠️  Response timeout');
            return false;
        }
    }

    async waitForWheelGeneration(timeout = 30000) {
        console.log('🎡 Waiting for wheel generation...');
        
        try {
            // Wait for wheel to appear
            await this.page.waitForSelector('.wheel, [data-wheel], game-wheel', { timeout });
            
            this.testResults.wheelGeneration = true;
            console.log('✅ Wheel generated successfully');
            return true;
        } catch (error) {
            console.log('⚠️  Wheel generation timeout');
            this.testResults.wheelGeneration = false;
            return false;
        }
    }

    async testWheelSpinning() {
        console.log('🎯 Testing wheel spinning mechanics...');
        
        try {
            // Find spin button
            const spinButton = this.page.locator('button:has-text("Spin"), .spin-button, [data-spin]').first();
            
            if (await spinButton.count() > 0) {
                // Click spin button
                await spinButton.click();
                
                // Wait for spin animation
                await this.page.waitForTimeout(3000);
                
                // Check for winning segment
                const winner = await this.page.evaluate(() => {
                    // Try to find winning segment indicator
                    const winnerElement = document.querySelector('.winner, .selected, [data-winner]');
                    return winnerElement ? winnerElement.textContent : null;
                });

                if (winner) {
                    console.log(`🏆 Wheel spin completed! Winner: ${winner}`);
                    this.testResults.wheelSpinning = true;
                } else {
                    console.log('⚠️  Wheel spin completed but winner detection unclear');
                    this.testResults.wheelSpinning = false;
                }
            } else {
                console.log('⚠️  Spin button not found');
                this.testResults.wheelSpinning = false;
            }

            return this.testResults.wheelSpinning;
        } catch (error) {
            console.error('❌ Wheel spinning test failed:', error.message);
            this.testResults.errors.push({
                type: 'wheel_spinning_error',
                message: error.message,
                timestamp: new Date().toISOString()
            });
            return false;
        }
    }

    async generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            testResults: this.testResults,
            wsMessages: this.wsMessages,
            summary: {
                totalTests: 4,
                passed: Object.values(this.testResults).filter(v => v === true).length,
                failed: this.testResults.errors.length,
                websocketMessages: this.wsMessages.length
            }
        };

        // Save report
        const fs = require('fs');
        const reportPath = `./test-results/playwright-websocket-report-${Date.now()}.json`;
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        
        console.log('\n📊 Test Report Generated:');
        console.log(`📁 Report saved to: ${reportPath}`);
        console.log(`✅ Tests Passed: ${report.summary.passed}/${report.summary.totalTests}`);
        console.log(`❌ Errors: ${report.summary.failed}`);
        console.log(`📨 WebSocket Messages: ${report.summary.websocketMessages}`);

        return report;
    }

    async cleanup() {
        console.log('🧹 Cleaning up...');
        
        if (this.page) await this.page.close();
        if (this.context) await this.context.close();
        if (this.browser) await this.browser.close();
        
        console.log('✅ Cleanup completed');
    }
}

// Main execution
async function main() {
    const tester = new PlaywrightWebSocketTester({
        headless: false, // Show browser for debugging
        slowMo: 1000     // Slow down for visibility
    });

    try {
        // Initialize
        if (!await tester.initialize()) {
            process.exit(1);
        }

        // Setup WebSocket interception
        await tester.setupWebSocketInterception();

        // Test WebSocket connection
        await tester.testWebSocketConnection();

        // Authenticate as user 2 (phiphi)
        await tester.authenticateAsUser(2);

        // Run complete user story
        await tester.simulateUserStory();

        // Generate report
        await tester.generateReport();

    } catch (error) {
        console.error('❌ Test execution failed:', error.message);
    } finally {
        await tester.cleanup();
    }
}

// Run if called directly
if (require.main === module) {
    main().catch(console.error);
}

module.exports = PlaywrightWebSocketTester;
