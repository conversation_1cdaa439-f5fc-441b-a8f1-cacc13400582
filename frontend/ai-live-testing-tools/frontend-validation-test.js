#!/usr/bin/env node

/**
 * Frontend Validation Test
 * Tests the actual frontend application to verify our fixes work
 */

import puppeteer from 'puppeteer';
import { writeFileSync } from 'fs';

class FrontendValidationTest {
  constructor() {
    this.browser = null;
    this.page = null;
    this.results = {
      frontendRunning: false,
      debugSpamFixed: false,
      errorDisplayFixed: false,
      chatScrollingFixed: false,
      finalAnswerDisplayed: false,
      issues: [],
      screenshots: []
    };
  }

  async runValidation() {
    console.log('🧪 Frontend Validation Test Starting...');
    console.log('=====================================\n');

    try {
      await this.setupBrowser();
      await this.checkFrontendRunning();
      
      if (this.results.frontendRunning) {
        await this.testChatBehavior();
      }
      
      await this.generateReport();
      
    } catch (error) {
      console.error('❌ Validation error:', error);
      this.results.issues.push(`Validation error: ${error.message}`);
    } finally {
      await this.cleanup();
    }
  }

  async setupBrowser() {
    console.log('🌐 Setting up browser...');
    
    this.browser = await puppeteer.launch({
      headless: false, // Show browser for debugging
      defaultViewport: { width: 1200, height: 800 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    this.page = await this.browser.newPage();
    
    // Listen for console messages
    this.page.on('console', (msg) => {
      const text = msg.text();
      if (text.includes('🐛 Debug') && !text.includes('Debug message limit reached')) {
        this.results.issues.push(`Debug spam detected: ${text.substring(0, 100)}...`);
      }
    });
    
    console.log('   ✅ Browser ready');
  }

  async checkFrontendRunning() {
    console.log('🔍 Checking if frontend is running...');
    
    try {
      await this.page.goto('http://localhost:5173', { 
        waitUntil: 'networkidle0',
        timeout: 10000 
      });
      
      // Check if the app loaded
      const appShell = await this.page.$('app-shell');
      if (appShell) {
        this.results.frontendRunning = true;
        console.log('   ✅ Frontend is running');
        
        // Take screenshot
        await this.takeScreenshot('frontend-loaded');
      } else {
        throw new Error('App shell not found');
      }
      
    } catch (error) {
      console.log('   ❌ Frontend not running or not accessible');
      console.log('   💡 Make sure to run: npm run dev');
      this.results.issues.push(`Frontend not accessible: ${error.message}`);
    }
  }

  async testChatBehavior() {
    console.log('\n🧪 Testing chat behavior...');
    
    try {
      // Wait for chat interface to load
      await this.page.waitForSelector('chat-interface', { timeout: 5000 });
      console.log('   ✅ Chat interface found');
      
      // Check initial chat height
      const initialHeight = await this.page.evaluate(() => {
        const container = document.querySelector('chat-interface .messages-container');
        return container ? container.offsetHeight : 0;
      });
      
      console.log(`   📏 Initial chat height: ${initialHeight}px`);
      
      // Send test message
      await this.sendTestMessage();
      
      // Wait for response and check behavior
      await this.checkChatBehavior(initialHeight);
      
    } catch (error) {
      console.log('   ❌ Chat testing failed:', error.message);
      this.results.issues.push(`Chat test failed: ${error.message}`);
    }
  }

  async sendTestMessage() {
    console.log('   📤 Sending test message...');
    
    try {
      // Find and fill message input
      const messageInput = await this.page.waitForSelector('chat-interface .message-input', { timeout: 5000 });
      await messageInput.type("I'm bored");
      
      // Click send button
      const sendButton = await this.page.$('chat-interface .send-button');
      await sendButton.click();
      
      console.log('   ✅ Test message sent');
      
      // Take screenshot after sending
      await this.takeScreenshot('message-sent');
      
    } catch (error) {
      throw new Error(`Failed to send message: ${error.message}`);
    }
  }

  async checkChatBehavior(initialHeight) {
    console.log('   ⏳ Waiting for response and checking behavior...');
    
    // Wait for processing to start
    await this.page.waitForTimeout(2000);
    
    let debugMessageCount = 0;
    let errorMessageFound = false;
    let finalAnswerFound = false;
    let chatHeightGrew = false;
    
    // Monitor for 30 seconds
    for (let i = 0; i < 30; i++) {
      await this.page.waitForTimeout(1000);
      
      // Check for debug messages in console (already handled by console listener)
      
      // Check for error messages in chat
      const errorMessages = await this.page.$$eval('message-bubble[type="error"]', 
        elements => elements.length
      );
      
      if (errorMessages > 0) {
        errorMessageFound = true;
        console.log('   ✅ Error message properly displayed in chat');
      }
      
      // Check for AI response
      const aiMessages = await this.page.$$eval('message-bubble[type="ai"]', 
        elements => elements.length
      );
      
      if (aiMessages > 0) {
        finalAnswerFound = true;
        console.log('   ✅ AI response found in chat');
        break;
      }
      
      // Check chat height
      const currentHeight = await this.page.evaluate(() => {
        const container = document.querySelector('chat-interface .messages-container');
        return container ? container.offsetHeight : 0;
      });
      
      if (currentHeight > initialHeight + 100) { // Allow some growth but not excessive
        chatHeightGrew = true;
        console.log(`   ⚠️ Chat height grew from ${initialHeight}px to ${currentHeight}px`);
      }
    }
    
    // Update results
    this.results.errorDisplayFixed = errorMessageFound;
    this.results.finalAnswerDisplayed = finalAnswerFound;
    this.results.chatScrollingFixed = !chatHeightGrew;
    this.results.debugSpamFixed = this.results.issues.filter(i => i.includes('Debug spam')).length < 5;
    
    // Take final screenshot
    await this.takeScreenshot('final-state');
    
    console.log('\n   📊 Test Results:');
    console.log(`   Debug spam fixed: ${this.results.debugSpamFixed ? '✅' : '❌'}`);
    console.log(`   Error display fixed: ${this.results.errorDisplayFixed ? '✅' : '❌'}`);
    console.log(`   Chat scrolling fixed: ${this.results.chatScrollingFixed ? '✅' : '❌'}`);
    console.log(`   Final answer displayed: ${this.results.finalAnswerDisplayed ? '✅' : '❌'}`);
  }

  async takeScreenshot(name) {
    try {
      const filename = `./logs/frontend-test-${name}-${Date.now()}.png`;
      await this.page.screenshot({ path: filename, fullPage: true });
      this.results.screenshots.push(filename);
      console.log(`   📸 Screenshot saved: ${filename}`);
    } catch (error) {
      console.log(`   ⚠️ Screenshot failed: ${error.message}`);
    }
  }

  async generateReport() {
    console.log('\n📊 Frontend Validation Report');
    console.log('==============================');
    
    const allFixed = this.results.debugSpamFixed && 
                    this.results.errorDisplayFixed && 
                    this.results.chatScrollingFixed && 
                    this.results.finalAnswerDisplayed;
    
    console.log(`\n🎯 Overall Status: ${allFixed ? '✅ ALL ISSUES FIXED' : '❌ ISSUES REMAIN'}`);
    
    console.log('\n📋 Detailed Results:');
    console.log(`Frontend Running: ${this.results.frontendRunning ? '✅' : '❌'}`);
    console.log(`Debug Spam Fixed: ${this.results.debugSpamFixed ? '✅' : '❌'}`);
    console.log(`Error Display Fixed: ${this.results.errorDisplayFixed ? '✅' : '❌'}`);
    console.log(`Chat Scrolling Fixed: ${this.results.chatScrollingFixed ? '✅' : '❌'}`);
    console.log(`Final Answer Displayed: ${this.results.finalAnswerDisplayed ? '✅' : '❌'}`);
    
    if (this.results.issues.length > 0) {
      console.log('\n⚠️ Issues Found:');
      this.results.issues.forEach((issue, index) => {
        console.log(`${index + 1}. ${issue}`);
      });
    }
    
    if (this.results.screenshots.length > 0) {
      console.log('\n📸 Screenshots:');
      this.results.screenshots.forEach(screenshot => {
        console.log(`   ${screenshot}`);
      });
    }
    
    // Save detailed report
    const report = {
      timestamp: new Date().toISOString(),
      results: this.results,
      summary: {
        allIssuesFixed: allFixed,
        totalIssues: this.results.issues.length,
        screenshotCount: this.results.screenshots.length
      }
    };
    
    writeFileSync('./logs/frontend-validation-report.json', JSON.stringify(report, null, 2));
    console.log('\n📄 Detailed report saved to: logs/frontend-validation-report.json');
    
    if (!allFixed) {
      console.log('\n💡 Next Steps:');
      console.log('1. Check if frontend development server is running');
      console.log('2. Restart frontend if needed: npm run dev');
      console.log('3. Check browser console for additional errors');
      console.log('4. Review screenshots for visual issues');
    }
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
    }
  }
}

// Run validation if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const validator = new FrontendValidationTest();
  validator.runValidation().catch(console.error);
}

export { FrontendValidationTest };
