#!/usr/bin/env node

/**
 * Bidirectional WebSocket Debug - Monitor both sent and received messages
 */

const { chromium } = require('playwright');

async function bidirectionalWebSocketDebug() {
    console.log('🚀 Starting bidirectional WebSocket debug...');
    
    const browser = await chromium.launch({ headless: false });
    const page = await browser.newPage();
    
    // Set up comprehensive message capture
    await page.addInitScript(() => {
        window.wsTraffic = {
            sent: [],
            received: [],
            connection: null
        };
        
        const originalWebSocket = window.WebSocket;
        window.WebSocket = function(url, protocols) {
            const ws = new originalWebSocket(url, protocols);
            window.wsTraffic.connection = ws;
            
            // Capture sent messages
            const originalSend = ws.send;
            ws.send = function(data) {
                try {
                    const parsed = JSON.parse(data);
                    window.wsTraffic.sent.push({
                        timestamp: Date.now(),
                        type: parsed.type || 'unknown',
                        data: parsed,
                        raw: data
                    });
                    console.log(`📤 Sending: ${parsed.type || 'unknown'}`);
                } catch (e) {
                    window.wsTraffic.sent.push({
                        timestamp: Date.now(),
                        type: 'raw',
                        data: data,
                        raw: data
                    });
                    console.log(`📤 Sending raw: ${data}`);
                }
                return originalSend.call(this, data);
            };
            
            // Capture received messages
            ws.addEventListener('message', (event) => {
                try {
                    const data = JSON.parse(event.data);
                    window.wsTraffic.received.push({
                        timestamp: Date.now(),
                        type: data.type,
                        data: data
                    });
                    console.log(`📥 Received: ${data.type}`);
                } catch (e) {
                    window.wsTraffic.received.push({
                        timestamp: Date.now(),
                        type: 'raw',
                        data: event.data
                    });
                    console.log(`📥 Received raw: ${event.data}`);
                }
            });
            
            ws.addEventListener('open', () => {
                console.log('🔌 WebSocket opened');
            });
            
            ws.addEventListener('close', (event) => {
                console.log(`🔌 WebSocket closed: ${event.code} - ${event.reason}`);
            });
            
            ws.addEventListener('error', (error) => {
                console.log(`❌ WebSocket error:`, error);
            });
            
            return ws;
        };
    });
    
    // Load page
    console.log('🌐 Loading page...');
    await page.goto('http://localhost:3000/');
    await page.waitForTimeout(3000);
    
    // Configure debug mode
    try {
        const userSelect = await page.locator('select').first();
        if (await userSelect.isVisible({ timeout: 2000 })) {
            await userSelect.selectOption('2');
            console.log('✅ Selected user 2');
            await page.waitForTimeout(1000);
        }
    } catch (e) {
        console.log('⚠️  No user select found');
    }
    
    // Check initial state
    const initialTraffic = await page.evaluate(() => window.wsTraffic);
    console.log(`📊 Initial state: ${initialTraffic.received.length} received, ${initialTraffic.sent.length} sent`);
    
    // Send message
    console.log('💬 Sending wheel request...');
    
    // Make sure textarea is enabled
    await page.evaluate(() => {
        const textarea = document.querySelector('textarea');
        if (textarea) {
            textarea.disabled = false;
            textarea.removeAttribute('disabled');
        }
    });
    
    await page.fill('textarea', "I'm restless and need to do things physical. I have 2 hours. Make me a wheel");
    await page.press('textarea', 'Enter');
    
    console.log('⏳ Waiting 60 seconds for response...');
    await page.waitForTimeout(60000);
    
    // Analyze traffic
    const finalTraffic = await page.evaluate(() => window.wsTraffic);
    
    console.log('\n📊 WEBSOCKET TRAFFIC ANALYSIS:');
    console.log('════════════════════════════════════════════════════════════');
    
    console.log(`\n📤 SENT MESSAGES (${finalTraffic.sent.length}):`);
    finalTraffic.sent.forEach((msg, i) => {
        console.log(`${i+1}. [${msg.type}] ${new Date(msg.timestamp).toISOString()}`);
        if (msg.type === 'chat_message') {
            console.log(`   Content: ${msg.data.content?.message || 'N/A'}`);
            console.log(`   User ID: ${msg.data.content?.user_profile_id || 'N/A'}`);
        }
        console.log(`   Raw: ${msg.raw.substring(0, 200)}...`);
    });
    
    console.log(`\n📥 RECEIVED MESSAGES (${finalTraffic.received.length}):`);
    finalTraffic.received.forEach((msg, i) => {
        console.log(`${i+1}. [${msg.type}] ${new Date(msg.timestamp).toISOString()}`);
        if (msg.type === 'wheel_data') {
            console.log(`   🎡 WHEEL DATA FOUND!`);
            if (msg.data.wheel && msg.data.wheel.items) {
                console.log(`   📊 Activities: ${msg.data.wheel.items.length}`);
            }
        } else if (msg.type === 'processing_status') {
            console.log(`   Status: ${msg.data.status}`);
        } else if (msg.type === 'chat_message') {
            console.log(`   Content: ${msg.data.content?.substring(0, 100) || 'N/A'}...`);
        }
    });
    
    console.log('\n🔍 DIAGNOSIS:');
    if (finalTraffic.sent.length === 0) {
        console.log('❌ No messages sent - frontend not sending anything');
    } else if (finalTraffic.sent.length > 0 && finalTraffic.received.length <= 2) {
        console.log('❌ Messages sent but no workflow response - backend not processing');
        console.log('💡 Check: Message format, user authentication, backend logs');
    } else if (finalTraffic.received.some(m => m.type === 'processing_status')) {
        console.log('✅ Workflow started but may not have completed');
    } else if (finalTraffic.received.some(m => m.type === 'wheel_data')) {
        console.log('✅ Wheel data received - check frontend rendering');
    }
    
    await browser.close();
}

bidirectionalWebSocketDebug().catch(console.error);
