#!/usr/bin/env node

/**
 * User Story Simulator
 * 
 * Simulates the basic user story:
 * 1. User launches client and sends "I'm bored"
 * 2. App (via Mentor agent) asks for more information
 * 3. User writes "I feel like doing exercise, what do you propose?"
 * 4. App triggers wheel generation and sends result to client
 */

import WebSocket from 'ws';
import { CONFIG } from './config.js';

class UserStorySimulator {
  constructor() {
    this.socket = null;
    this.messages = [];
    this.step = 0;
    this.isConnected = false;
    this.userId = '2'; // PhiPhi profile - fake user for testing
    
    // User story steps
    this.userSteps = [
      "I'm bored",
      "I feel like doing exercise, what do you propose?"
    ];
    
    this.colors = {
      reset: '\x1b[0m',
      bright: '\x1b[1m',
      red: '\x1b[31m',
      green: '\x1b[32m',
      yellow: '\x1b[33m',
      blue: '\x1b[34m',
      magenta: '\x1b[35m',
      cyan: '\x1b[36m',
      gray: '\x1b[90m'  // FIXED: Added missing gray color
    };
  }

  async start() {
    console.log(`${this.colors.cyan}🎭 User Story Simulator${this.colors.reset}`);
    console.log(`${this.colors.cyan}======================${this.colors.reset}\n`);
    
    console.log(`${this.colors.yellow}📖 Simulating user story:${this.colors.reset}`);
    console.log(`${this.colors.yellow}1. User: "I'm bored"${this.colors.reset}`);
    console.log(`${this.colors.yellow}2. App asks for more information${this.colors.reset}`);
    console.log(`${this.colors.yellow}3. User: "I feel like doing exercise, what do you propose?"${this.colors.reset}`);
    console.log(`${this.colors.yellow}4. App triggers wheel generation${this.colors.reset}\n`);
    
    try {
      await this.connect();
      await this.runUserStory();
    } catch (error) {
      console.error(`${this.colors.red}❌ Error: ${error.message}${this.colors.reset}`);
    }
  }

  async connect() {
    console.log(`${this.colors.blue}🔌 Connecting to backend...${this.colors.reset}`);
    
    return new Promise((resolve, reject) => {
      this.socket = new WebSocket(CONFIG.backend.websocketUrl);
      
      const timeout = setTimeout(() => {
        reject(new Error('Connection timeout'));
      }, 10000);

      this.socket.on('open', () => {
        clearTimeout(timeout);
        this.isConnected = true;
        console.log(`${this.colors.green}✅ Connected to ${CONFIG.backend.websocketUrl}${this.colors.reset}`);
        resolve();
      });

      this.socket.on('error', (error) => {
        clearTimeout(timeout);
        reject(error);
      });

      this.socket.on('message', (data) => {
        this.handleMessage(data);
      });

      this.socket.on('close', (code, reason) => {
        this.isConnected = false;
        console.log(`${this.colors.yellow}🔌 Connection closed: ${code} ${reason}${this.colors.reset}`);
      });
    });
  }

  handleMessage(data) {
    let message;
    try {
      message = JSON.parse(data);
    } catch (error) {
      console.log(`${this.colors.red}❌ Failed to parse message: ${data}${this.colors.reset}`);
      return;
    }

    this.messages.push({
      timestamp: new Date().toISOString(),
      message: message
    });

    console.log(`\n${this.colors.green}📨 Received:${this.colors.reset}`);
    console.log(this.formatMessage(message));

    // Handle different message types
    this.processMessage(message);
  }

  formatMessage(message) {
    try {
      let formatted = JSON.stringify(message, null, 2);

      // FIXED: Ensure we have a valid string before applying replacements
      if (!formatted || typeof formatted !== 'string') {
        return 'Invalid message format';
      }

      // Apply syntax highlighting
      formatted = formatted
        .replace(/("type":\s*"[^"]*")/g, `${this.colors.yellow}$1${this.colors.reset}`)
        .replace(/("content":\s*"[^"]*")/g, `${this.colors.green}$1${this.colors.reset}`)
        .replace(/("is_user":\s*[^,}]*)/g, `${this.colors.cyan}$1${this.colors.reset}`)
        .replace(/("status":\s*"[^"]*")/g, `${this.colors.blue}$1${this.colors.reset}`);

      return formatted;
    } catch (error) {
      return `Error formatting message: ${error.message}`;
    }
  }

  processMessage(message) {
    const type = message.type;
    
    switch (type) {
      case 'system_message':
        console.log(`${this.colors.magenta}🔔 System: ${message.content}${this.colors.reset}`);
        // After system message, start the user story
        if (this.step === 0) {
          setTimeout(() => this.sendNextUserMessage(), 2000);
        }
        break;
        
      case 'chat_message':
        if (message.is_user) {
          console.log(`${this.colors.cyan}👤 User: ${message.content}${this.colors.reset}`);
        } else {
          console.log(`${this.colors.green}🤖 AI: ${message.content}${this.colors.reset}`);
          // After AI response, wait and send next user message
          if (this.step < this.userSteps.length) {
            setTimeout(() => this.sendNextUserMessage(), 3000);
          }
        }
        break;
        
      case 'processing_status':
        console.log(`${this.colors.yellow}⚙️ Processing: ${message.status}${this.colors.reset}`);
        break;
        
      case 'wheel_data':
        console.log(`${this.colors.magenta}🎡 Wheel Generated!${this.colors.reset}`);
        this.analyzeWheel(message.wheel);
        // End the simulation after wheel generation
        setTimeout(async () => await this.finish(), 2000);
        break;
        
      case 'error':
        console.log(`${this.colors.red}❌ Error: ${message.content}${this.colors.reset}`);
        break;

      case 'debug_info':
        // FIXED: Properly extract debug message content based on actual backend structure
        let debugMessage = 'Unknown debug message';

        if (message.content && typeof message.content === 'object') {
          // Priority order: message > source+level > stringify
          if (message.content.message && message.content.message !== 'undefined') {
            debugMessage = message.content.message;
          } else if (message.content.source && message.content.level) {
            debugMessage = `[${message.content.source}:${message.content.level}] ${message.content.message || 'debug info'}`;
          } else {
            debugMessage = JSON.stringify(message.content, null, 2);
          }
        } else if (message.content && typeof message.content === 'string') {
          debugMessage = message.content;
        }

        // FIXED: Ensure we never show "undefined"
        if (!debugMessage || debugMessage === 'undefined' || debugMessage.includes('undefined')) {
          const source = message.content?.source || 'unknown';
          const level = message.content?.level || 'info';
          const details = message.content?.details ? JSON.stringify(message.content.details) : 'no details';
          debugMessage = `[${source}:${level}] ${details}`;
        }

        // DEBUGGING: Add explicit debugging to track down the "undefined" issue
        console.log(`\n🔧 Debug: ${debugMessage}`);
        break;

      case 'workflow_status':
        console.log(`${this.colors.blue}📋 Workflow ${message.workflow_id}: ${message.status}${this.colors.reset}`);
        break;

      default:
        console.log(`${this.colors.gray}📦 Unknown message type: ${type}${this.colors.reset}`);
    }
  }

  analyzeWheel(wheel) {
    console.log(`\n${this.colors.cyan}🎡 Wheel Analysis:${this.colors.reset}`);
    console.log(`${this.colors.cyan}Name: ${wheel.name || 'Unnamed Wheel'}${this.colors.reset}`);
    console.log(`${this.colors.cyan}Items: ${wheel.items ? wheel.items.length : 0}${this.colors.reset}`);

    if (wheel.items && wheel.items.length > 0) {
      console.log(`${this.colors.cyan}Activities:${this.colors.reset}`);
      wheel.items.forEach((item, index) => {
        // FIXED: Prioritize item.title over item.name for proper activity names
        const activityName = item.title || item.name || 'Unnamed Activity';
        console.log(`  ${index + 1}. ${activityName} (${item.domain || 'unknown domain'})`);
        console.log(`     ${item.description || 'No description'}`);
      });
    }
  }

  async sendNextUserMessage() {
    if (this.step >= this.userSteps.length) {
      console.log(`${this.colors.yellow}✅ User story completed${this.colors.reset}`);
      return;
    }

    const userMessage = this.userSteps[this.step];
    console.log(`\n${this.colors.cyan}👤 Sending: "${userMessage}"${this.colors.reset}`);
    
    const message = {
      type: 'chat_message',
      content: {
        message: userMessage,
        user_profile_id: this.userId,
        timestamp: new Date().toISOString(),
        metadata: {
          // Let the system auto-classify the workflow
        }
      }
    };

    try {
      this.socket.send(JSON.stringify(message));
      this.step++;
    } catch (error) {
      console.error(`${this.colors.red}❌ Failed to send message: ${error.message}${this.colors.reset}`);
    }
  }

  async runUserStory() {
    console.log(`${this.colors.blue}🎬 Starting user story simulation...${this.colors.reset}`);
    
    // Wait for initial system message, then the story will proceed automatically
    // The processMessage method handles the flow
    
    // Keep the simulation running for a reasonable time
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve();
      }, 60000); // 1 minute timeout
    });
  }

  async finish() {
    console.log(`\n${this.colors.green}🎉 User story simulation completed!${this.colors.reset}`);
    console.log(`${this.colors.green}Total messages received: ${this.messages.length}${this.colors.reset}`);

    // Save the conversation log
    await this.saveConversationLog();

    if (this.socket) {
      this.socket.close();
    }

    process.exit(0);
  }

  async saveConversationLog() {
    const fs = await import('fs');
    const path = await import('path');

    const logFile = path.join('logs', `user-story-${Date.now()}.json`);
    const logData = {
      timestamp: new Date().toISOString(),
      userStory: this.userSteps,
      messages: this.messages,
      summary: {
        totalMessages: this.messages.length,
        stepsCompleted: this.step,
        wheelGenerated: this.messages.some(m => m.message.type === 'wheel_data')
      }
    };

    try {
      fs.writeFileSync(logFile, JSON.stringify(logData, null, 2));
      console.log(`${this.colors.blue}💾 Conversation log saved to: ${logFile}${this.colors.reset}`);
    } catch (error) {
      console.warn(`${this.colors.yellow}⚠️ Failed to save log: ${error.message}${this.colors.reset}`);
    }
  }
}

// Run the simulator
const simulator = new UserStorySimulator();
simulator.start().catch(console.error);
