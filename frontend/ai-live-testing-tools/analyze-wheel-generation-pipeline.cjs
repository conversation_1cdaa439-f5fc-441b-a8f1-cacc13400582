#!/usr/bin/env node

/**
 * Wheel Generation Pipeline Analyzer
 * 
 * Analyzes the complete wheel generation pipeline to identify where
 * domain diversity is lost and user preferences are not properly utilized.
 * 
 * This tool performs static analysis of the codebase to identify potential issues.
 * 
 * Usage: node analyze-wheel-generation-pipeline.cjs
 */

const fs = require('fs');
const path = require('path');

class WheelGenerationPipelineAnalyzer {
    constructor() {
        this.backendPath = path.join(__dirname, '..', '..', 'backend');
        this.frontendPath = path.join(__dirname, '..', 'src');
        this.issues = [];
        this.findings = [];
    }

    analyzeBackendWheelGeneration() {
        console.log('\n🔍 Analyzing Backend Wheel Generation Pipeline...');
        
        // Analyze wheel generation graph
        const graphPath = path.join(this.backendPath, 'apps/main/graphs/wheel_generation_graph.py');
        if (fs.existsSync(graphPath)) {
            const content = fs.readFileSync(graphPath, 'utf8');
            
            // Check for user preference handling
            const hasEnergyLevelHandling = content.includes('energy_level');
            const hasTimeAvailableHandling = content.includes('time_available');
            const hasUserInputContext = content.includes('user_input_context');
            
            console.log(`   Energy level handling: ${hasEnergyLevelHandling ? '✅' : '❌'}`);
            console.log(`   Time available handling: ${hasTimeAvailableHandling ? '✅' : '❌'}`);
            console.log(`   User input context: ${hasUserInputContext ? '✅' : '❌'}`);
            
            if (!hasEnergyLevelHandling) {
                this.issues.push('Wheel generation graph does not handle energy_level from user input');
            }
            if (!hasTimeAvailableHandling) {
                this.issues.push('Wheel generation graph does not handle time_available from user input');
            }
            
            // Check for fallback activity creation
            const fallbackActivityMatches = content.match(/fallback_activity.*=.*{/g);
            if (fallbackActivityMatches) {
                console.log(`   Fallback activity creation found: ${fallbackActivityMatches.length} instances`);
                this.findings.push(`Found ${fallbackActivityMatches.length} fallback activity creation points`);
            }
        }
        
        // Analyze activity tools
        const toolsPath = path.join(this.backendPath, 'apps/main/agents/tools/activity_tools.py');
        if (fs.existsSync(toolsPath)) {
            const content = fs.readFileSync(toolsPath, 'utf8');
            
            // Check domain assignment logic
            const domainAssignmentMatches = content.match(/domain.*=.*["']([^"']+)["']/g);
            if (domainAssignmentMatches) {
                console.log(`   Domain assignment patterns found: ${domainAssignmentMatches.length}`);
                
                // Check for "general" domain assignments
                const generalDomainMatches = content.match(/domain.*=.*["']general["']/g);
                if (generalDomainMatches) {
                    console.log(`   ⚠️ "General" domain assignments: ${generalDomainMatches.length}`);
                    this.issues.push(`Found ${generalDomainMatches.length} hardcoded "general" domain assignments`);
                }
            }
            
            // Check color assignment logic
            const colorAssignmentMatches = content.match(/_get_activity_color/g);
            if (colorAssignmentMatches) {
                console.log(`   Color assignment function calls: ${colorAssignmentMatches.length}`);
            } else {
                this.issues.push('No color assignment function calls found in activity tools');
            }
            
            // Check for enhanced catalog usage
            const enhancedCatalogMatches = content.match(/_get_enhanced_default_activity_catalog/g);
            if (enhancedCatalogMatches) {
                console.log(`   Enhanced catalog usage: ${enhancedCatalogMatches.length} calls`);
                this.findings.push('Enhanced activity catalog is being used');
            }
        }
    }

    analyzeFrontendWheelComponent() {
        console.log('\n🔍 Analyzing Frontend Wheel Component...');
        
        const wheelPath = path.join(this.frontendPath, 'components/game-wheel/game-wheel.ts');
        if (fs.existsSync(wheelPath)) {
            const content = fs.readFileSync(wheelPath, 'utf8');
            
            // Check for domain handling
            const domainHandling = content.includes('domain');
            const colorHandling = content.includes('color');
            const segmentProcessing = content.includes('processWheelData');
            
            console.log(`   Domain handling: ${domainHandling ? '✅' : '❌'}`);
            console.log(`   Color handling: ${colorHandling ? '✅' : '❌'}`);
            console.log(`   Segment processing: ${segmentProcessing ? '✅' : '❌'}`);
            
            // Check for color assignment logic
            const colorAssignmentMatches = content.match(/assignSegmentColors|getModulatedColor/g);
            if (colorAssignmentMatches) {
                console.log(`   Color assignment methods: ${colorAssignmentMatches.length}`);
            } else {
                this.issues.push('No color assignment methods found in wheel component');
            }
            
            // Check for segment normalization
            const normalizationMatches = content.match(/normalizedSegments|normalize/g);
            if (normalizationMatches) {
                console.log(`   Segment normalization: ${normalizationMatches.length} instances`);
            }
        }
    }

    analyzeAppShellComponent() {
        console.log('\n🔍 Analyzing App Shell Component...');
        
        const appShellPath = path.join(this.frontendPath, 'components/app-shell.ts');
        if (fs.existsSync(appShellPath)) {
            const content = fs.readFileSync(appShellPath, 'utf8');
            
            // Check for user preference capture
            const energyLevelCapture = content.includes('energyLevel');
            const timeAvailableCapture = content.includes('timeAvailable');
            const sliderHandling = content.includes('handleEnergyLevelChange') && content.includes('handleTimeAvailableChange');
            
            console.log(`   Energy level capture: ${energyLevelCapture ? '✅' : '❌'}`);
            console.log(`   Time available capture: ${timeAvailableCapture ? '✅' : '❌'}`);
            console.log(`   Slider handling: ${sliderHandling ? '✅' : '❌'}`);
            
            // Check for WebSocket message transmission
            const messageTransmission = content.includes('energy_level') && content.includes('time_available');
            console.log(`   Message transmission: ${messageTransmission ? '✅' : '❌'}`);
            
            if (!messageTransmission) {
                this.issues.push('User preferences may not be transmitted in WebSocket messages');
            }
            
            // Check for wheel data processing
            const wheelDataProcessing = content.includes('wheelData') && content.includes('segments');
            console.log(`   Wheel data processing: ${wheelDataProcessing ? '✅' : '❌'}`);
        }
    }

    analyzeDataFlowDocumentation() {
        console.log('\n🔍 Analyzing Data Flow Documentation...');
        
        const dataFlowPath = path.join(__dirname, '..', '..', 'docs/backend/DATA_FLOW_AUTHORITATIVE_SPECS.md');
        if (fs.existsSync(dataFlowPath)) {
            const content = fs.readFileSync(dataFlowPath, 'utf8');
            
            const hasEnergyLevelSpec = content.includes('energy_level');
            const hasTimeAvailableSpec = content.includes('time_available');
            const hasDomainSpec = content.includes('domain');
            
            console.log(`   Energy level specification: ${hasEnergyLevelSpec ? '✅' : '❌'}`);
            console.log(`   Time available specification: ${hasTimeAvailableSpec ? '✅' : '❌'}`);
            console.log(`   Domain specification: ${hasDomainSpec ? '✅' : '❌'}`);
            
            if (!hasEnergyLevelSpec || !hasTimeAvailableSpec) {
                this.issues.push('Data flow documentation missing user preference specifications');
            }
        } else {
            this.issues.push('Data flow documentation not found');
        }
        
        const messageSpecPath = path.join(__dirname, '..', '..', 'tools/mock_server/MESSAGE_SPECIFICATIONS.md');
        if (fs.existsSync(messageSpecPath)) {
            const content = fs.readFileSync(messageSpecPath, 'utf8');
            
            const hasEnergyLevelMsg = content.includes('energy_level');
            const hasTimeAvailableMsg = content.includes('time_available');
            const hasDomainMsg = content.includes('domain');
            
            console.log(`   Message energy level spec: ${hasEnergyLevelMsg ? '✅' : '❌'}`);
            console.log(`   Message time available spec: ${hasTimeAvailableMsg ? '✅' : '❌'}`);
            console.log(`   Message domain spec: ${hasDomainMsg ? '✅' : '❌'}`);
        }
    }

    identifyPotentialIssues() {
        console.log('\n🔍 Identifying Potential Issues...');
        
        // Common issues that could cause domain problems
        const potentialIssues = [
            {
                issue: 'Fallback activity generation overriding catalog activities',
                description: 'If the enhanced catalog fails, fallback activities may all use "general" domain',
                likelihood: 'High'
            },
            {
                issue: 'Domain mapping inconsistencies',
                description: 'Different domain names used in different parts of the pipeline',
                likelihood: 'Medium'
            },
            {
                issue: 'Color assignment happening in backend instead of frontend',
                description: 'Backend color assignment may not respect frontend color schemes',
                likelihood: 'Medium'
            },
            {
                issue: 'User preferences not reaching activity tailoring',
                description: 'Energy level and time available may not influence activity selection',
                likelihood: 'High'
            },
            {
                issue: 'Enhanced catalog not being used in production',
                description: 'Real users may be getting different catalog than test users',
                likelihood: 'Medium'
            }
        ];
        
        potentialIssues.forEach((issue, index) => {
            console.log(`   ${index + 1}. ${issue.issue} (${issue.likelihood} likelihood)`);
            console.log(`      ${issue.description}`);
        });
        
        return potentialIssues;
    }

    generateRecommendations() {
        console.log('\n💡 Recommendations...');
        
        const recommendations = [
            'Ensure user preferences (energy_level, time_available) are passed through entire pipeline',
            'Verify enhanced activity catalog is used for all users, not just test users',
            'Move color assignment logic to frontend for consistency',
            'Add logging to track domain assignment at each stage',
            'Implement domain validation to prevent "general" domain fallbacks',
            'Create integration tests that validate complete data flow',
            'Update documentation to reflect actual implementation'
        ];
        
        recommendations.forEach((rec, index) => {
            console.log(`   ${index + 1}. ${rec}`);
        });
        
        return recommendations;
    }

    generateReport() {
        console.log('\n📋 WHEEL GENERATION PIPELINE ANALYSIS REPORT');
        console.log('=' * 60);
        
        console.log(`\n🔍 Issues Identified (${this.issues.length}):`);
        this.issues.forEach((issue, index) => {
            console.log(`   ${index + 1}. ${issue}`);
        });
        
        console.log(`\n✅ Findings (${this.findings.length}):`);
        this.findings.forEach((finding, index) => {
            console.log(`   ${index + 1}. ${finding}`);
        });
        
        const potentialIssues = this.identifyPotentialIssues();
        const recommendations = this.generateRecommendations();
        
        console.log('\n🎯 Next Steps:');
        console.log('   1. Run backend domain flow test: python test-backend-domain-flow.py');
        console.log('   2. Run frontend domain flow test: node test-wheel-domain-data-flow.cjs');
        console.log('   3. Check Celery logs during wheel generation for domain assignment');
        console.log('   4. Verify enhanced catalog is used in production environment');
        console.log('   5. Add domain validation logging to wheel generation workflow');
        
        return {
            issues: this.issues,
            findings: this.findings,
            potentialIssues,
            recommendations,
            hasIssues: this.issues.length > 0
        };
    }

    run() {
        console.log('🚀 Starting Wheel Generation Pipeline Analysis...');
        
        this.analyzeBackendWheelGeneration();
        this.analyzeFrontendWheelComponent();
        this.analyzeAppShellComponent();
        this.analyzeDataFlowDocumentation();
        
        return this.generateReport();
    }
}

// Main execution
function main() {
    const analyzer = new WheelGenerationPipelineAnalyzer();
    const results = analyzer.run();
    
    process.exit(results.hasIssues ? 1 : 0);
}

if (require.main === module) {
    main();
}

module.exports = { WheelGenerationPipelineAnalyzer };
