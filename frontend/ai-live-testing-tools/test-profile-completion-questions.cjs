#!/usr/bin/env node

/**
 * Test Profile Completion Questions
 * 
 * This script tests the profile completion questions by sending non-explicit messages
 * to trigger the onboarding workflow for a user with low profile completion.
 */

const { chromium } = require('playwright');

class ProfileCompletionTester {
    constructor() {
        this.browser = null;
        this.page = null;
        this.testResults = {
            userSwitched: false,
            onboardingTriggered: false,
            profileQuestionsReceived: false,
            questionsRelevant: false,
            conversationProgressed: false,
            errors: []
        };
    }

    log(message) {
        console.log(`[${new Date().toISOString()}] ${message}`);
    }

    async initialize() {
        this.log('🚀 Initializing Profile Completion Questions Test...');
        
        this.browser = await chromium.launch({ 
            headless: false,
            devtools: true,
            args: ['--disable-web-security', '--disable-features=VizDisplayCompositor']
        });
        
        this.page = await this.browser.newPage();
        
        // Enable console logging
        this.page.on('console', msg => {
            if (msg.type() === 'error') {
                this.log(`❌ Console Error: ${msg.text()}`);
                this.testResults.errors.push(`Console: ${msg.text()}`);
            }
        });
    }

    async loadFrontendAndSwitchUser() {
        this.log('🌐 Loading frontend and switching to User 38...');
        
        try {
            await this.page.goto('http://localhost:3001/', { 
                waitUntil: 'networkidle',
                timeout: 30000 
            });
            
            await this.page.waitForTimeout(3000);
            
            // Open debug panel
            const debugButton = this.page.locator('button:has-text("Debug")');
            if (await debugButton.isVisible({ timeout: 5000 })) {
                await debugButton.click();
            } else {
                await this.page.keyboard.press('Control+Shift+D');
            }
            
            await this.page.waitForTimeout(2000);
            
            // Switch to User 38
            const userSelect = this.page.locator('select').filter({ hasText: /Select.*user/i }).first();
            if (await userSelect.isVisible({ timeout: 10000 })) {
                await userSelect.selectOption('38');
                this.log('✅ Selected User 38 (25% profile completion)');
                await this.page.waitForTimeout(3000);
                this.testResults.userSwitched = true;
            }
            
            // Close debug panel
            await this.page.keyboard.press('Escape');
            await this.page.waitForTimeout(1000);
            
            return true;
        } catch (error) {
            this.log(`❌ Failed to setup: ${error.message}`);
            this.testResults.errors.push(`Setup: ${error.message}`);
            return false;
        }
    }

    async testGeneralMessage() {
        this.log('💬 Testing general message to trigger onboarding...');
        
        try {
            // Send a general message that should trigger onboarding
            const chatInput = this.page.locator('textarea').first();
            await chatInput.click();
            await chatInput.fill('Hi there! I\'m feeling a bit overwhelmed today and could use some help.');
            await this.page.keyboard.press('Enter');
            
            this.log('✅ General message sent');
            
            // Wait for response
            await this.page.waitForTimeout(8000);
            
            // Check response content
            const chatMessages = await this.page.textContent('body');
            
            if (chatMessages.includes('tell me more') || 
                chatMessages.includes('more about') ||
                chatMessages.includes('help me understand') ||
                chatMessages.includes('share') ||
                chatMessages.includes('profile') ||
                chatMessages.includes('preferences')) {
                this.log('✅ System asked for profile information');
                this.testResults.onboardingTriggered = true;
                this.testResults.profileQuestionsReceived = true;
                
                // Check if questions are relevant
                if (chatMessages.includes('goals') || 
                    chatMessages.includes('interests') ||
                    chatMessages.includes('time') ||
                    chatMessages.includes('resources') ||
                    chatMessages.includes('environment')) {
                    this.log('✅ Questions appear relevant and contextual');
                    this.testResults.questionsRelevant = true;
                }
            } else {
                this.log('❌ System did not ask for profile information');
            }
            
            return true;
        } catch (error) {
            this.log(`❌ Failed to test general message: ${error.message}`);
            this.testResults.errors.push(`General message: ${error.message}`);
            return false;
        }
    }

    async testConversationProgression() {
        this.log('🔄 Testing conversation progression...');
        
        try {
            // Respond to the profile question
            const chatInput = this.page.locator('textarea').first();
            await chatInput.click();
            await chatInput.fill('I\'m a student studying computer science. I have about 2 hours free time today and I\'m interested in both physical activities and creative projects.');
            await this.page.keyboard.press('Enter');
            
            this.log('✅ Profile response sent');
            
            // Wait for follow-up
            await this.page.waitForTimeout(8000);
            
            const chatMessages = await this.page.textContent('body');
            
            if (chatMessages.includes('thank') || 
                chatMessages.includes('great') ||
                chatMessages.includes('helpful') ||
                chatMessages.includes('understand')) {
                this.log('✅ System acknowledged profile information');
                this.testResults.conversationProgressed = true;
            }
            
            return true;
        } catch (error) {
            this.log(`❌ Failed to test conversation progression: ${error.message}`);
            this.testResults.errors.push(`Conversation progression: ${error.message}`);
            return false;
        }
    }

    async generateReport() {
        this.log('📊 Generating test report...');
        
        const report = {
            timestamp: new Date().toISOString(),
            testResults: this.testResults,
            summary: {
                totalTests: 5,
                passed: Object.values(this.testResults).filter(v => v === true).length,
                failed: this.testResults.errors.length,
                success: this.testResults.userSwitched && 
                        this.testResults.onboardingTriggered && 
                        this.testResults.profileQuestionsReceived
            }
        };
        
        console.log('\n📊 PROFILE COMPLETION QUESTIONS TEST REPORT');
        console.log('════════════════════════════════════════════════════════════');
        console.log(`✅ User Switched to 38: ${this.testResults.userSwitched}`);
        console.log(`✅ Onboarding Triggered: ${this.testResults.onboardingTriggered}`);
        console.log(`✅ Profile Questions Received: ${this.testResults.profileQuestionsReceived}`);
        console.log(`✅ Questions Relevant: ${this.testResults.questionsRelevant}`);
        console.log(`✅ Conversation Progressed: ${this.testResults.conversationProgressed}`);
        
        if (this.testResults.errors.length > 0) {
            console.log('\n❌ Errors:');
            this.testResults.errors.forEach(error => console.log(`  - ${error}`));
        }
        
        console.log(`\n🎯 Overall Success: ${report.summary.success ? '✅ PASSED' : '❌ FAILED'}`);
        console.log('════════════════════════════════════════════════════════════');
        
        return report;
    }

    async runTest() {
        try {
            await this.initialize();
            
            if (!await this.loadFrontendAndSwitchUser()) return;
            if (!await this.testGeneralMessage()) return;
            await this.testConversationProgression();
            
            await this.generateReport();
            
            this.log('🎉 Test completed! Browser kept open for manual verification.');
            this.log('Press Ctrl+C to close when done.');
            
            // Keep browser open for manual inspection
            await new Promise(() => {});
            
        } catch (error) {
            this.log(`❌ Test failed: ${error.message}`);
            this.testResults.errors.push(`Test execution: ${error.message}`);
        }
    }

    async cleanup() {
        if (this.browser) {
            await this.browser.close();
        }
    }
}

// Handle cleanup on exit
process.on('SIGINT', async () => {
    console.log('\n🛑 Test interrupted by user');
    process.exit(0);
});

// Run the test
const tester = new ProfileCompletionTester();
tester.runTest().catch(console.error);
