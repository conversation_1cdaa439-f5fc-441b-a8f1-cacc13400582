#!/usr/bin/env node

/**
 * Connection Monitor Test - Test the admin dashboard WebSocket connection
 * 
 * This tool tests:
 * 1. Connection to /ws/connection-monitor/ endpoint
 * 2. Reception of statistics data (connection_data, system_health, message_stats)
 * 3. Proper message handling and display
 */

const WebSocket = require('ws');

const config = {
  adminWebSocketUrl: 'ws://localhost:8000/ws/connection-monitor/',
  testDuration: 30000, // 30 seconds
};

class ConnectionMonitorTest {
  constructor() {
    this.adminWs = null;
    this.receivedMessages = [];
    this.statisticsReceived = {
      connection_data: false,
      system_health: false,
      message_stats: false
    };
    this.startTime = Date.now();
  }

  async start() {
    console.log('🔍 Connection Monitor Test Starting...');
    console.log('📊 Testing admin dashboard WebSocket statistics');
    console.log('⏱️  Test duration: 30 seconds\n');

    try {
      await this.connectToMonitor();
      await this.runTest();
      this.generateReport();
    } catch (error) {
      console.error('❌ Test failed:', error.message);
    } finally {
      this.cleanup();
    }
  }

  async connectToMonitor() {
    return new Promise((resolve, reject) => {
      console.log('🔌 Connecting to connection monitor WebSocket...');
      
      this.adminWs = new WebSocket(config.adminWebSocketUrl);
      
      this.adminWs.on('open', () => {
        console.log('✅ Connection monitor WebSocket connected');
        
        // Request initial data
        this.requestData();
        
        resolve();
      });

      this.adminWs.on('message', (data) => {
        try {
          const message = JSON.parse(data.toString());
          this.handleMessage(message);
        } catch (error) {
          console.error('❌ Failed to parse message:', error.message);
          console.log('Raw data:', data.toString());
        }
      });

      this.adminWs.on('error', (error) => {
        console.error('❌ Connection monitor WebSocket error:', error.message);
        reject(error);
      });

      this.adminWs.on('close', (code, reason) => {
        console.log(`🔌 Connection monitor WebSocket closed: ${code} ${reason}`);
      });

      setTimeout(() => reject(new Error('Connection timeout')), 10000);
    });
  }

  requestData() {
    if (this.adminWs && this.adminWs.readyState === WebSocket.OPEN) {
      console.log('📡 Requesting initial data...');
      
      // Request connection data
      this.adminWs.send(JSON.stringify({type: 'get_connections'}));
      
      // Request system health
      this.adminWs.send(JSON.stringify({type: 'get_system_health'}));
      
      // Request message stats
      this.adminWs.send(JSON.stringify({type: 'get_message_stats'}));
    }
  }

  handleMessage(message) {
    this.receivedMessages.push({
      message,
      timestamp: new Date().toISOString()
    });

    console.log(`📨 Received: ${message.type}`);

    // Check for expected statistics messages
    if (message.type === 'connection_data') {
      this.statisticsReceived.connection_data = true;
      console.log(`  📊 Connection data: ${message.data ? message.data.length : 0} connections`);
    }

    if (message.type === 'system_health') {
      this.statisticsReceived.system_health = true;
      console.log(`  🏥 System health: ${JSON.stringify(message.data)}`);
    }

    if (message.type === 'message_stats') {
      this.statisticsReceived.message_stats = true;
      console.log(`  📈 Message stats: ${JSON.stringify(message.data)}`);
    }

    // Log other message types
    if (!['connection_data', 'system_health', 'message_stats'].includes(message.type)) {
      console.log(`  ℹ️  Other message: ${message.type}`);
    }
  }

  async runTest() {
    return new Promise(resolve => {
      // Request data periodically
      const requestInterval = setInterval(() => {
        this.requestData();
      }, 5000);

      setTimeout(() => {
        clearInterval(requestInterval);
        resolve();
      }, config.testDuration);
    });
  }

  generateReport() {
    const duration = Date.now() - this.startTime;
    
    console.log('\n🔍 Connection Monitor Test Report');
    console.log('=' .repeat(50));
    console.log(`⏱️  Test duration: ${Math.round(duration / 1000)}s`);
    console.log(`📨 Total messages received: ${this.receivedMessages.length}`);
    
    console.log('\n📊 Statistics Reception:');
    Object.entries(this.statisticsReceived).forEach(([type, received]) => {
      const status = received ? '✅' : '❌';
      console.log(`  ${status} ${type}: ${received ? 'Received' : 'Not received'}`);
    });

    console.log('\n📋 Message Types Received:');
    const messageTypes = {};
    this.receivedMessages.forEach(({message}) => {
      messageTypes[message.type] = (messageTypes[message.type] || 0) + 1;
    });
    
    Object.entries(messageTypes).forEach(([type, count]) => {
      console.log(`  - ${type}: ${count} messages`);
    });

    // Check if all expected statistics were received
    const allStatsReceived = Object.values(this.statisticsReceived).every(received => received);
    
    if (allStatsReceived) {
      console.log('\n✅ SUCCESS: All expected statistics received!');
    } else {
      console.log('\n❌ ISSUE: Some statistics not received');
      console.log('   This explains why dashboard shows "0" for all stats');
    }

    // Show sample messages
    if (this.receivedMessages.length > 0) {
      console.log('\n📄 Sample Messages:');
      this.receivedMessages.slice(0, 3).forEach(({message, timestamp}, index) => {
        console.log(`\n${index + 1}. ${message.type} (${timestamp}):`);
        console.log(JSON.stringify(message, null, 2));
      });
    }

    // Save detailed report
    const report = {
      testSummary: {
        duration,
        totalMessages: this.receivedMessages.length,
        statisticsReceived: this.statisticsReceived,
        allStatsReceived
      },
      messageTypes,
      messages: this.receivedMessages
    };

    const fs = require('fs');
    const filename = `logs/connection-monitor-test-${Date.now()}.json`;
    fs.writeFileSync(filename, JSON.stringify(report, null, 2));
    console.log(`\n📄 Detailed report saved: ${filename}`);
  }

  cleanup() {
    if (this.adminWs) {
      this.adminWs.close();
    }
  }
}

// Run the test
if (require.main === module) {
  const test = new ConnectionMonitorTest();
  test.start().catch(console.error);
}

module.exports = ConnectionMonitorTest;
