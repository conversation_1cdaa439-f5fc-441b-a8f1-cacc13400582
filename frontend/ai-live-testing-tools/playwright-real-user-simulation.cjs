#!/usr/bin/env node

/**
 * Real User Simulation - <PERSON><PERSON> Tool
 * 
 * This tool simulates EXACTLY what a real user experiences, including:
 * 1. Real mouse movements and clicks
 * 2. Actual typing behavior
 * 3. Waiting for real backend responses
 * 4. Detecting duplicate responses more accurately
 * 5. Testing chat area interaction like a human would
 */

const { chromium } = require('playwright');

class RealUserSimulation {
    constructor() {
        this.browser = null;
        this.page = null;
        this.allMessages = [];
        this.duplicateResponses = [];
        this.chatInteractionIssues = [];
        this.backendPerformance = {
            responseTime: [],
            slowQueries: [],
            errors: []
        };
        this.userActions = [];
    }

    async initialize() {
        console.log('👤 Real User Simulation - Initializing...');
        
        this.browser = await chromium.launch({ 
            headless: false,
            slowMo: 2000, // Slow down to human speed
            args: ['--disable-web-security', '--disable-features=VizDisplayCompositor']
        });
        
        this.page = await this.browser.newPage();
        
        // Set up comprehensive monitoring
        await this.setupRealTimeMonitoring();
        
        console.log('✅ Real user simulation initialized');
    }

    async setupRealTimeMonitoring() {
        console.log('🔍 Setting up real-time monitoring...');
        
        // Monitor ALL console messages (including backend logs)
        this.page.on('console', msg => {
            const text = msg.text();
            const type = msg.type();
            
            // Log everything for debugging
            console.log(`🖥️  [${type}] ${text}`);
            
            // Detect backend performance issues
            if (text.includes('slow query') || text.includes('timeout') || text.includes('error')) {
                this.backendPerformance.errors.push({
                    timestamp: Date.now(),
                    type: type,
                    message: text
                });
            }
        });

        // Monitor WebSocket with detailed message tracking
        this.page.on('websocket', ws => {
            console.log(`🔌 WebSocket connection: ${ws.url()}`);
            
            ws.on('framereceived', event => {
                const message = event.payload;
                const timestamp = Date.now();
                
                console.log(`📨 ← Server: ${message}`);
                
                try {
                    const parsed = JSON.parse(message);
                    this.allMessages.push({
                        direction: 'received',
                        timestamp: timestamp,
                        raw: message,
                        parsed: parsed,
                        type: parsed.type || 'unknown'
                    });
                    
                    // Check for duplicates with more sophisticated detection
                    this.checkForDuplicatesAdvanced(parsed, timestamp);
                    
                } catch (e) {
                    // Non-JSON message
                    this.allMessages.push({
                        direction: 'received',
                        timestamp: timestamp,
                        raw: message,
                        parsed: null,
                        type: 'raw'
                    });
                }
            });

            ws.on('framesent', event => {
                const message = event.payload;
                const timestamp = Date.now();
                
                console.log(`📤 → Client: ${message}`);
                
                this.allMessages.push({
                    direction: 'sent',
                    timestamp: timestamp,
                    raw: message,
                    parsed: null,
                    type: 'client'
                });
            });
        });

        // Monitor network requests for backend performance
        this.page.on('response', response => {
            const url = response.url();
            const status = response.status();

            if (url.includes('/api/') || url.includes('/ws/')) {
                console.log(`🌐 ${status} ${url}`);

                this.backendPerformance.responseTime.push({
                    url: url,
                    status: status,
                    timestamp: Date.now()
                });

                if (status >= 500) { // Server errors
                    this.backendPerformance.slowQueries.push({
                        url: url,
                        status: status,
                        timestamp: Date.now()
                    });
                }
            }
        });
    }

    checkForDuplicatesAdvanced(parsed, timestamp) {
        if (!parsed || !parsed.type) return;
        
        // Look for chat messages or AI responses
        if (parsed.type === 'chat_message' || parsed.type === 'ai_response' || 
            (parsed.content && typeof parsed.content === 'string')) {
            
            const content = parsed.content || parsed.message || JSON.stringify(parsed);
            
            // Check for duplicates in the last 30 seconds
            const recentMessages = this.allMessages.filter(msg => 
                msg.timestamp > (timestamp - 30000) && 
                msg.direction === 'received' &&
                msg.parsed
            );
            
            const duplicates = recentMessages.filter(msg => {
                if (!msg.parsed) return false;
                
                const msgContent = msg.parsed.content || msg.parsed.message || JSON.stringify(msg.parsed);
                return msgContent === content && msg.timestamp !== timestamp;
            });
            
            if (duplicates.length > 0) {
                console.log('🚨 DUPLICATE RESPONSE DETECTED!');
                console.log(`   Original: ${duplicates[0].timestamp}`);
                console.log(`   Duplicate: ${timestamp}`);
                console.log(`   Content: ${content.substring(0, 100)}...`);
                
                this.duplicateResponses.push({
                    original: duplicates[0],
                    duplicate: {
                        timestamp: timestamp,
                        content: content,
                        parsed: parsed
                    },
                    timeDiff: timestamp - duplicates[0].timestamp
                });
            }
        }
    }

    async loadFrontendLikeHuman() {
        console.log('\n👤 Loading frontend like a real user...');
        console.log('════════════════════════════════════════════════════════════');
        
        // Navigate like a human would
        await this.page.goto('http://localhost:3001');
        
        // Wait and observe like a human
        console.log('⏳ Waiting for page to load (like a human would)...');
        await this.page.waitForTimeout(5000);
        
        // Check if page loaded properly
        const title = await this.page.title();
        console.log(`📄 Page title: ${title}`);
        
        // Wait for WebSocket connection like a human would
        console.log('⏳ Waiting for connection (human patience)...');
        await this.page.waitForTimeout(10000);
        
        console.log('✅ Frontend loaded');
    }

    async testChatInteractionLikeHuman() {
        console.log('\n👤 Testing chat interaction like a real user...');
        console.log('════════════════════════════════════════════════════════════');
        
        // Try to find chat area like a human would
        console.log('👀 Looking for chat input area...');
        
        const chatSelectors = [
            'textarea',
            'input[type="text"]',
            '.chat-input',
            '.message-input',
            '[placeholder*="message"]',
            '[placeholder*="chat"]',
            '[placeholder*="type"]'
        ];
        
        let chatInput = null;
        let workingSelector = null;
        
        for (const selector of chatSelectors) {
            try {
                console.log(`🔍 Trying to find: ${selector}`);
                const element = this.page.locator(selector).first();
                
                // Check if element exists and is visible
                if (await element.isVisible({ timeout: 2000 })) {
                    console.log(`✅ Found visible element: ${selector}`);
                    chatInput = element;
                    workingSelector = selector;
                    break;
                } else {
                    console.log(`❌ Element not visible: ${selector}`);
                }
            } catch (e) {
                console.log(`❌ Element not found: ${selector}`);
            }
        }
        
        if (!chatInput) {
            console.log('🚨 CRITICAL: No chat input found - this confirms the user\'s issue!');
            this.chatInteractionIssues.push({
                issue: 'No chat input found',
                timestamp: Date.now(),
                selectorsAttempted: chatSelectors
            });
            return false;
        }
        
        // Try to interact like a human
        console.log(`🖱️  Attempting to click on chat input (${workingSelector})...`);
        
        try {
            // Human-like mouse movement and click
            await chatInput.hover();
            await this.page.waitForTimeout(500); // Human pause
            await chatInput.click();
            console.log('✅ Click successful');
            
            // Check if input is actually focused
            const isFocused = await this.page.evaluate(() => {
                const activeElement = document.activeElement;
                return activeElement && (
                    activeElement.tagName === 'TEXTAREA' || 
                    activeElement.tagName === 'INPUT'
                );
            });
            
            if (!isFocused) {
                console.log('🚨 CRITICAL: Click succeeded but input not focused!');
                this.chatInteractionIssues.push({
                    issue: 'Click succeeded but no focus',
                    timestamp: Date.now(),
                    selector: workingSelector
                });
            }
            
            // Try typing like a human
            console.log('⌨️  Attempting to type...');
            await this.page.waitForTimeout(1000); // Human pause before typing
            
            const testMessage = 'hey! do you recognize me?';
            await chatInput.type(testMessage, { delay: 100 }); // Human typing speed
            
            // Check if text actually appeared
            const inputValue = await chatInput.inputValue();
            if (inputValue !== testMessage) {
                console.log('🚨 CRITICAL: Typing failed - text not in input!');
                console.log(`   Expected: "${testMessage}"`);
                console.log(`   Actual: "${inputValue}"`);
                this.chatInteractionIssues.push({
                    issue: 'Typing failed',
                    timestamp: Date.now(),
                    expected: testMessage,
                    actual: inputValue
                });
                return false;
            }
            
            console.log('✅ Typing successful');
            return chatInput;
            
        } catch (error) {
            console.log('🚨 CRITICAL: Chat interaction completely failed!');
            console.log(`   Error: ${error.message}`);
            this.chatInteractionIssues.push({
                issue: 'Complete interaction failure',
                timestamp: Date.now(),
                error: error.message,
                selector: workingSelector
            });
            return false;
        }
    }

    async sendMessageAndWaitForResponse() {
        console.log('\n📤 Sending message and monitoring for duplicates...');
        console.log('════════════════════════════════════════════════════════════');
        
        const chatInput = await this.testChatInteractionLikeHuman();
        if (!chatInput) {
            console.log('❌ Cannot send message - chat input not accessible');
            return;
        }
        
        // Record initial message count
        const initialMessageCount = this.allMessages.filter(msg => 
            msg.direction === 'received' && msg.parsed
        ).length;
        
        console.log(`📊 Initial message count: ${initialMessageCount}`);
        
        // Send message like a human
        console.log('📤 Pressing Enter to send...');
        await chatInput.press('Enter');
        
        this.userActions.push({
            action: 'send_message',
            timestamp: Date.now(),
            message: 'hey! do you recognize me?'
        });
        
        // Wait and monitor for responses (longer wait to catch duplicates)
        console.log('⏳ Monitoring for responses (60 seconds)...');
        
        const startTime = Date.now();
        let lastMessageCount = initialMessageCount;
        
        for (let i = 0; i < 60; i++) {
            await this.page.waitForTimeout(1000);
            
            const currentMessageCount = this.allMessages.filter(msg => 
                msg.direction === 'received' && msg.parsed
            ).length;
            
            if (currentMessageCount > lastMessageCount) {
                const newMessages = currentMessageCount - lastMessageCount;
                const elapsed = ((Date.now() - startTime) / 1000).toFixed(1);
                console.log(`📨 +${newMessages} new message(s) at ${elapsed}s`);
                lastMessageCount = currentMessageCount;
            }
            
            // Check for duplicates in real-time
            if (this.duplicateResponses.length > 0) {
                console.log(`🚨 ${this.duplicateResponses.length} duplicate(s) detected so far!`);
            }
        }
        
        const finalMessageCount = this.allMessages.filter(msg => 
            msg.direction === 'received' && msg.parsed
        ).length;
        
        const totalNewMessages = finalMessageCount - initialMessageCount;
        console.log(`📊 Total new messages received: ${totalNewMessages}`);
        console.log(`🚨 Total duplicates detected: ${this.duplicateResponses.length}`);
    }

    async analyzeBackendPerformance() {
        console.log('\n⚡ Analyzing backend performance...');
        console.log('════════════════════════════════════════════════════════════');
        
        const avgResponseTime = this.backendPerformance.responseTime.length > 0 
            ? this.backendPerformance.responseTime.reduce((sum, r) => sum + r.time, 0) / this.backendPerformance.responseTime.length
            : 0;
        
        console.log(`📊 Average response time: ${avgResponseTime.toFixed(0)}ms`);
        console.log(`🐌 Slow queries (>5s): ${this.backendPerformance.slowQueries.length}`);
        console.log(`❌ Backend errors: ${this.backendPerformance.errors.length}`);
        
        if (this.backendPerformance.slowQueries.length > 0) {
            console.log('\n🐌 Slow queries detected:');
            this.backendPerformance.slowQueries.forEach((query, i) => {
                console.log(`   ${i + 1}. ${query.url} - ${query.time}ms`);
            });
        }
        
        if (this.backendPerformance.errors.length > 0) {
            console.log('\n❌ Backend errors detected:');
            this.backendPerformance.errors.forEach((error, i) => {
                console.log(`   ${i + 1}. [${error.type}] ${error.message}`);
            });
        }
    }

    async generateDetailedReport() {
        console.log('\n📊 REAL USER SIMULATION REPORT');
        console.log('════════════════════════════════════════════════════════════');
        
        const report = {
            timestamp: new Date().toISOString(),
            userExperience: {
                chatInteractionWorking: this.chatInteractionIssues.length === 0,
                duplicateResponsesDetected: this.duplicateResponses.length > 0,
                totalChatIssues: this.chatInteractionIssues.length,
                totalDuplicates: this.duplicateResponses.length
            },
            backendPerformance: {
                slowQueries: this.backendPerformance.slowQueries.length,
                errors: this.backendPerformance.errors.length,
                totalRequests: this.backendPerformance.responseTime.length
            },
            detailedData: {
                chatInteractionIssues: this.chatInteractionIssues,
                duplicateResponses: this.duplicateResponses,
                allMessages: this.allMessages,
                userActions: this.userActions,
                performanceData: this.backendPerformance
            }
        };
        
        console.log('👤 User Experience:');
        console.log(`  Chat Interaction Working: ${report.userExperience.chatInteractionWorking ? '✅ YES' : '❌ NO'}`);
        console.log(`  Duplicate Responses: ${report.userExperience.duplicateResponsesDetected ? '🚨 DETECTED' : '✅ NONE'}`);
        console.log(`  Chat Issues Found: ${report.userExperience.totalChatIssues}`);
        
        console.log('\n⚡ Backend Performance:');
        console.log(`  Slow Queries: ${report.backendPerformance.slowQueries}`);
        console.log(`  Errors: ${report.backendPerformance.errors}`);
        
        // Save detailed report
        const fs = require('fs');
        const reportPath = `test-results/real-user-simulation-${Date.now()}.json`;
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        console.log(`\n📁 Detailed report saved to: ${reportPath}`);
        
        // Critical issues summary
        if (report.userExperience.totalChatIssues > 0 || report.userExperience.duplicateResponsesDetected) {
            console.log('\n🚨 CRITICAL ISSUES CONFIRMED:');
            if (report.userExperience.totalChatIssues > 0) {
                console.log('   ❌ Chat interaction problems detected');
            }
            if (report.userExperience.duplicateResponsesDetected) {
                console.log('   ❌ Duplicate responses confirmed');
            }
        }
        
        return report;
    }

    async cleanup() {
        console.log('\n🔍 Browser kept open for manual inspection...');
        console.log('Press Ctrl+C to close when done.');
        // Keep browser open for manual verification
    }

    async run() {
        try {
            await this.initialize();
            await this.loadFrontendLikeHuman();
            await this.sendMessageAndWaitForResponse();
            await this.analyzeBackendPerformance();
            await this.generateDetailedReport();
            
            console.log('\n🎯 Real User Simulation completed!');
            console.log('Check the browser window to see the actual state.');
            
        } catch (error) {
            console.error('❌ Simulation failed:', error);
        } finally {
            await this.cleanup();
        }
    }
}

// Run the simulation
if (require.main === module) {
    const simulation = new RealUserSimulation();
    simulation.run().catch(console.error);
}

module.exports = RealUserSimulation;
