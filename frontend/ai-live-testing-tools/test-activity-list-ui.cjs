#!/usr/bin/env node

/**
 * Test script for activity list UI functionality
 * Tests the new activity list, expansion, and modal functionality
 */

const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

async function testActivityListUI(port = 3002) {
  console.log('🎡 Testing activity list UI functionality...');
  
  const browser = await chromium.launch({
    headless: false,
    slowMo: 500
  });

  try {
    const page = await browser.newPage();
    
    // Enable console logging
    page.on('console', msg => {
      console.log(`[BROWSER] ${msg.text()}`);
    });

    // Navigate to main app
    console.log(`📱 Navigating to http://localhost:${port}`);
    await page.goto(`http://localhost:${port}`, { waitUntil: 'networkidle0' });

    // Wait for app to load
    await page.waitForSelector('app-shell', { timeout: 10000 });
    console.log('✅ App shell loaded');

    // Wait for WebSocket connection
    await page.waitForTimeout(3000);

    // Inject sample wheel data to test the activity list
    console.log('🔄 Injecting sample wheel data...');
    await page.evaluate(() => {
      // Create sample wheel data
      const sampleWheelData = {
        segments: [
          {
            id: 'activity-1',
            name: 'Morning Yoga',
            text: 'Morning Yoga',
            description: 'Start your day with gentle stretching and mindfulness exercises',
            percentage: 25,
            color: '#FF6B6B',
            domain: 'physical',
            base_challenge_rating: 30,
            activity_tailored_id: 'tailored-1'
          },
          {
            id: 'activity-2',
            name: 'Creative Writing',
            text: 'Creative Writing',
            description: 'Express yourself through words and storytelling',
            percentage: 25,
            color: '#4ECDC4',
            domain: 'creative',
            base_challenge_rating: 50,
            activity_tailored_id: 'tailored-2'
          },
          {
            id: 'activity-3',
            name: 'Learn Spanish',
            text: 'Learn Spanish',
            description: 'Practice Spanish vocabulary and conversation skills',
            percentage: 25,
            color: '#45B7D1',
            domain: 'learning',
            base_challenge_rating: 60,
            activity_tailored_id: 'tailored-3'
          },
          {
            id: 'activity-4',
            name: 'Nature Walk',
            text: 'Nature Walk',
            description: 'Take a peaceful walk in nature to clear your mind',
            percentage: 25,
            color: '#96CEB4',
            domain: 'physical',
            base_challenge_rating: 20,
            activity_tailored_id: 'tailored-4'
          }
        ]
      };

      // Find the app-shell component and directly set the wheelData
      const appShell = document.querySelector('app-shell');
      if (appShell) {
        console.log('Found app-shell, setting wheelData...');

        // Access the internal property directly (Lit components expose their properties)
        try {
          // Try to access the private property through the component instance
          Object.defineProperty(appShell, 'wheelData', {
            value: sampleWheelData,
            writable: true,
            configurable: true
          });

          // Force a re-render by calling requestUpdate if available
          if (typeof appShell.requestUpdate === 'function') {
            appShell.requestUpdate();
          }

          console.log('WheelData set successfully');
        } catch (error) {
          console.error('Failed to set wheelData:', error);
        }
      } else {
        console.error('app-shell not found');
      }
    });

    await page.waitForTimeout(2000);

    // Take screenshot after injecting data
    await page.screenshot({ path: 'test-activity-list-with-data.png', fullPage: true });
    console.log('📸 Screenshot with data taken');

    // Look for the button bar
    console.log('🔍 Looking for button bar...');
    const buttonBar = await page.$('.button-bar');
    if (buttonBar) {
      console.log('✅ Button bar found');
      
      // Test potentiometer interactions
      const timeSlider = await buttonBar.$('input[type="range"]:first-of-type');
      const energySlider = await buttonBar.$('input[type="range"]:last-of-type');
      
      if (timeSlider && energySlider) {
        console.log('🎛️ Testing potentiometer interactions...');
        
        // Test time slider
        await timeSlider.fill('75');
        await page.waitForTimeout(500);
        
        // Test energy slider
        await energySlider.fill('25');
        await page.waitForTimeout(500);
        
        console.log('✅ Potentiometer interactions tested');
      }
    } else {
      console.log('❌ Button bar not found');
    }

    // Look for activity list
    console.log('🔍 Looking for activity list...');
    const activityList = await page.$('.activity-list');
    if (activityList) {
      console.log('✅ Activity list found');
      
      // Check for activity items
      const activityItems = await page.$$('.activity-item');
      console.log(`📋 Found ${activityItems.length} activity items`);
      
      if (activityItems.length > 0) {
        // Test expanding the first activity
        console.log('🔄 Testing activity expansion...');
        await activityItems[0].click();
        await page.waitForTimeout(1000);
        
        // Check if it expanded
        const expandedItem = await page.$('.activity-item.expanded');
        if (expandedItem) {
          console.log('✅ Activity item expanded successfully');
          
          // Take screenshot of expanded activity
          await page.screenshot({ path: 'test-activity-expanded.png', fullPage: true });
          console.log('📸 Expanded activity screenshot taken');
          
          // Look for change button
          const changeButton = await expandedItem.$('.activity-change-btn');
          if (changeButton) {
            console.log('🔄 Testing change button...');
            await changeButton.click();
            await page.waitForTimeout(1000);
            
            // Check if modal opened
            const modal = await page.$('.modal-overlay');
            if (modal) {
              console.log('✅ Activity change modal opened');
              
              // Take screenshot of modal
              await page.screenshot({ path: 'test-activity-modal.png', fullPage: true });
              console.log('📸 Modal screenshot taken');
              
              // Test search functionality
              const searchInput = await modal.$('.search-input');
              if (searchInput) {
                console.log('🔍 Testing search functionality...');
                await searchInput.fill('yoga');
                await page.waitForTimeout(1000);
                
                // Check for filtered results
                const catalogItems = await modal.$$('.catalog-item');
                console.log(`📋 Found ${catalogItems.length} catalog items after search`);
                
                // Test selecting an item
                if (catalogItems.length > 0) {
                  console.log('🔄 Testing catalog item selection...');
                  await catalogItems[0].click();
                  await page.waitForTimeout(500);
                  console.log('✅ Catalog item selected');
                }
              }
              
              // Close modal by clicking outside
              await page.click('.modal-overlay', { position: { x: 10, y: 10 } });
              await page.waitForTimeout(500);
              console.log('✅ Modal closed');
            } else {
              console.log('❌ Activity change modal not found');
            }
          } else {
            console.log('❌ Change button not found');
          }
        } else {
          console.log('❌ Activity item did not expand');
        }
        
        // Test expanding a different activity
        if (activityItems.length > 1) {
          console.log('🔄 Testing second activity expansion...');
          await activityItems[1].click();
          await page.waitForTimeout(1000);
          
          const secondExpanded = await page.$$('.activity-item.expanded');
          console.log(`📋 Found ${secondExpanded.length} expanded activities`);
        }
      }
    } else {
      console.log('❌ Activity list not found');
    }

    // Take final screenshot
    await page.screenshot({ path: 'test-activity-list-final.png', fullPage: true });
    console.log('📸 Final screenshot taken');

    console.log('✅ Activity list UI test completed');

  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

// Run the test
const port = process.argv[2] || 3002;
testActivityListUI(port).catch(console.error);
