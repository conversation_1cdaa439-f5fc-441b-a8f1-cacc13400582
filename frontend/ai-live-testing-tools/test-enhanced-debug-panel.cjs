#!/usr/bin/env node

/**
 * Enhanced Debug Panel Test
 * 
 * This test validates the enhanced debug panel functionality including:
 * - Reliable connection status with detailed WebSocket state
 * - Current user demographics data with profile completion details
 * - Raw message logs with bidirectional WebSocket traffic
 * - Enhanced debugging capabilities for better UX analysis
 */

const { chromium } = require('playwright');

class EnhancedDebugPanelTest {
  constructor() {
    this.browser = null;
    this.page = null;
    this.testResults = {
      connectionStatus: false,
      userDemographics: false,
      messageLogging: false,
      performanceMetrics: false,
      enhancedFeatures: false
    };
  }

  async initialize() {
    console.log('🚀 Initializing Enhanced Debug Panel Test...');
    
    this.browser = await chromium.launch({
      headless: false,
      devtools: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    this.page = await this.browser.newPage();
    
    // Enable console logging
    this.page.on('console', msg => {
      const type = msg.type();
      const text = msg.text();
      if (text.includes('Debug Panel') || text.includes('WebSocket')) {
        console.log(`🖥️  [${type}] ${text}`);
      }
    });
    
    // Enable error logging
    this.page.on('pageerror', error => {
      console.log(`❌ Page error: ${error.message}`);
    });
    
    console.log('✅ Enhanced Debug Panel Test initialized');
  }

  async loadFrontend() {
    console.log('📱 Loading frontend...');
    
    try {
      await this.page.goto('http://localhost:3001', {
        waitUntil: 'networkidle',
        timeout: 30000
      });
      
      // Wait for the app to load
      await this.page.waitForSelector('app-shell', { timeout: 10000 });
      console.log('✅ Frontend loaded successfully');
      
      return true;
    } catch (error) {
      console.log(`❌ Failed to load frontend: ${error.message}`);
      return false;
    }
  }

  async activateDebugPanel() {
    console.log('🐛 Activating debug panel...');

    try {
      // Try to activate debug panel with Ctrl+Shift+D
      await this.page.keyboard.down('Control');
      await this.page.keyboard.down('Shift');
      await this.page.keyboard.press('D');
      await this.page.keyboard.up('Shift');
      await this.page.keyboard.up('Control');

      // Wait a moment for the panel to appear
      await this.page.waitForTimeout(1000);

      // Check if debug panel is visible
      const debugPanel = await this.page.locator('debug-panel').first();
      const isVisible = await debugPanel.isVisible();

      if (isVisible) {
        console.log('✅ Debug panel activated');
        return true;
      } else {
        // Try alternative selector
        const debugPanelAlt = await this.page.locator('.debug-panel').first();
        const isVisibleAlt = await debugPanelAlt.isVisible();

        if (isVisibleAlt) {
          console.log('✅ Debug panel activated (alternative selector)');
          return true;
        } else {
          console.log('❌ Debug panel not visible after keyboard shortcut');
          return false;
        }
      }
    } catch (error) {
      console.log(`❌ Failed to activate debug panel: ${error.message}`);
      return false;
    }
  }

  async testConnectionStatus() {
    console.log('🔗 Testing enhanced connection status...');
    
    try {
      // Check for enhanced connection status section
      const connectionSection = await this.page.locator('.debug-section').filter({ hasText: '🔗 Connection Status' }).first();
      const isVisible = await connectionSection.isVisible();
      
      if (isVisible) {
        // Check for detailed connection info
        const hasReadyState = await connectionSection.locator('text=Ready State:').count() > 0;
        const hasUrl = await connectionSection.locator('text=URL:').count() > 0;
        const hasMessages = await connectionSection.locator('text=Messages:').count() > 0;
        
        console.log(`  ✅ Connection section visible: ${isVisible}`);
        console.log(`  📊 Ready state info: ${hasReadyState}`);
        console.log(`  🌐 URL info: ${hasUrl}`);
        console.log(`  📨 Message count: ${hasMessages}`);
        
        this.testResults.connectionStatus = isVisible && (hasReadyState || hasUrl || hasMessages);
      }
      
      return this.testResults.connectionStatus;
    } catch (error) {
      console.log(`❌ Connection status test failed: ${error.message}`);
      return false;
    }
  }

  async testUserDemographics() {
    console.log('👤 Testing user demographics display...');
    
    try {
      // Check for user selection section
      const userSection = await this.page.locator('.debug-section').filter({ hasText: 'User Selection' }).first();
      const isVisible = await userSection.isVisible();
      
      if (isVisible) {
        // Check for detailed user info
        const hasUserDetails = await userSection.locator('.user-details').count() > 0;
        const hasProfileCompletion = await userSection.locator('text=Profile Completion:').count() > 0;
        const hasDemographics = await userSection.locator('text=Age:').count() > 0;
        
        console.log(`  ✅ User section visible: ${isVisible}`);
        console.log(`  📋 User details: ${hasUserDetails}`);
        console.log(`  📊 Profile completion: ${hasProfileCompletion}`);
        console.log(`  🏠 Demographics: ${hasDemographics}`);
        
        this.testResults.userDemographics = isVisible && (hasUserDetails || hasProfileCompletion);
      }
      
      return this.testResults.userDemographics;
    } catch (error) {
      console.log(`❌ User demographics test failed: ${error.message}`);
      return false;
    }
  }

  async testMessageLogging() {
    console.log('📡 Testing WebSocket message logging...');
    
    try {
      // Check for message log section
      const messageSection = await this.page.locator('.debug-section').filter({ hasText: '📡 WebSocket Messages' }).first();
      const isVisible = await messageSection.isVisible();
      
      if (isVisible) {
        // Check for show/hide raw button
        const hasShowRawButton = await messageSection.locator('button', { hasText: 'Show' }).count() > 0 ||
                                  await messageSection.locator('button', { hasText: 'Hide' }).count() > 0;
        const hasClearButton = await messageSection.locator('button', { hasText: 'Clear' }).count() > 0;
        
        console.log(`  ✅ Message section visible: ${isVisible}`);
        console.log(`  🔍 Show/Hide raw button: ${hasShowRawButton}`);
        console.log(`  🗑️ Clear button: ${hasClearButton}`);
        
        // Try to click show raw to test functionality
        if (hasShowRawButton) {
          try {
            await messageSection.locator('button', { hasText: 'Show' }).click();
            await this.page.waitForTimeout(500);
            console.log(`  📝 Clicked show raw messages`);
          } catch (e) {
            console.log(`  ⚠️ Could not click show raw: ${e.message}`);
          }
        }
        
        this.testResults.messageLogging = isVisible && hasShowRawButton && hasClearButton;
      }
      
      return this.testResults.messageLogging;
    } catch (error) {
      console.log(`❌ Message logging test failed: ${error.message}`);
      return false;
    }
  }

  async testPerformanceMetrics() {
    console.log('⚡ Testing performance metrics...');
    
    try {
      // Check for performance section
      const perfSection = await this.page.locator('.debug-section').filter({ hasText: '⚡ Performance' }).first();
      const isVisible = await perfSection.isVisible();
      
      if (isVisible) {
        // Check for show/hide details button
        const hasDetailsButton = await perfSection.locator('button', { hasText: 'Show' }).count() > 0 ||
                                  await perfSection.locator('button', { hasText: 'Hide' }).count() > 0;
        
        console.log(`  ✅ Performance section visible: ${isVisible}`);
        console.log(`  📊 Details button: ${hasDetailsButton}`);
        
        // Try to click show details
        if (hasDetailsButton) {
          try {
            await perfSection.locator('button', { hasText: 'Show' }).click();
            await this.page.waitForTimeout(500);
            
            // Check for performance metrics
            const hasUptime = await perfSection.locator('text=Uptime:').count() > 0;
            const hasMemory = await perfSection.locator('text=Memory:').count() > 0;
            
            console.log(`  ⏱️ Uptime metric: ${hasUptime}`);
            console.log(`  💾 Memory metric: ${hasMemory}`);
            
            this.testResults.performanceMetrics = hasUptime || hasMemory;
          } catch (e) {
            console.log(`  ⚠️ Could not click show details: ${e.message}`);
          }
        }
      }
      
      return this.testResults.performanceMetrics;
    } catch (error) {
      console.log(`❌ Performance metrics test failed: ${error.message}`);
      return false;
    }
  }

  async testEnhancedFeatures() {
    console.log('🔧 Testing enhanced features...');
    
    try {
      // Check for enhanced actions section
      const actionsSection = await this.page.locator('.debug-section').filter({ hasText: '🔧 Actions' }).first();
      const isVisible = await actionsSection.isVisible();
      
      if (isVisible) {
        // Check for multiple action buttons
        const hasClearStorage = await actionsSection.locator('button', { hasText: 'Clear Storage' }).count() > 0;
        const hasClearMessages = await actionsSection.locator('button', { hasText: 'Clear Messages' }).count() > 0;
        const hasClearErrors = await actionsSection.locator('button', { hasText: 'Clear Errors' }).count() > 0;
        
        console.log(`  ✅ Actions section visible: ${isVisible}`);
        console.log(`  🗑️ Clear storage: ${hasClearStorage}`);
        console.log(`  📨 Clear messages: ${hasClearMessages}`);
        console.log(`  ❌ Clear errors: ${hasClearErrors}`);
        
        this.testResults.enhancedFeatures = hasClearStorage && hasClearMessages && hasClearErrors;
      }
      
      return this.testResults.enhancedFeatures;
    } catch (error) {
      console.log(`❌ Enhanced features test failed: ${error.message}`);
      return false;
    }
  }

  async runFullTest() {
    try {
      await this.initialize();
      
      const frontendLoaded = await this.loadFrontend();
      if (!frontendLoaded) {
        console.log('❌ Cannot proceed without frontend');
        return false;
      }
      
      const debugPanelActive = await this.activateDebugPanel();
      if (!debugPanelActive) {
        console.log('❌ Cannot proceed without debug panel');
        return false;
      }
      
      // Run all tests
      await this.testConnectionStatus();
      await this.testUserDemographics();
      await this.testMessageLogging();
      await this.testPerformanceMetrics();
      await this.testEnhancedFeatures();
      
      // Generate summary
      this.generateSummary();
      
      console.log('\n🔍 Browser kept open for manual inspection. Press Ctrl+C to exit.');
      
      // Keep browser open for manual inspection
      await new Promise(() => {}); // Keep alive
      
    } catch (error) {
      console.error('❌ Test failed:', error);
    }
  }

  generateSummary() {
    console.log('\n🎯 ENHANCED DEBUG PANEL TEST SUMMARY:');
    console.log('=' * 50);
    
    const results = this.testResults;
    const passed = Object.values(results).filter(Boolean).length;
    const total = Object.keys(results).length;
    
    console.log(`📊 Overall Score: ${passed}/${total} tests passed`);
    console.log(`🔗 Connection Status: ${results.connectionStatus ? '✅' : '❌'}`);
    console.log(`👤 User Demographics: ${results.userDemographics ? '✅' : '❌'}`);
    console.log(`📡 Message Logging: ${results.messageLogging ? '✅' : '❌'}`);
    console.log(`⚡ Performance Metrics: ${results.performanceMetrics ? '✅' : '❌'}`);
    console.log(`🔧 Enhanced Features: ${results.enhancedFeatures ? '✅' : '❌'}`);
    
    if (passed === total) {
      console.log('\n🎉 All enhanced debug panel features are working correctly!');
    } else {
      console.log(`\n⚠️ ${total - passed} features need attention.`);
    }
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
    }
  }
}

// Run the test
const test = new EnhancedDebugPanelTest();
test.runFullTest().catch(console.error);

// Handle cleanup on exit
process.on('SIGINT', async () => {
  console.log('\n🛑 Cleaning up...');
  await test.cleanup();
  process.exit(0);
});
