#!/usr/bin/env node

/**
 * Debug wheel generation issue
 * Checks backend connection, WebSocket status, and wheel generation workflow
 */

const { chromium } = require('playwright');

const CONFIG = {
  frontendUrl: process.argv[2] || 'http://localhost:3002',
  timeout: 10000
};

async function debugWheelGeneration() {
  console.log('🔍 Debugging Wheel Generation Issue...');
  console.log(`Frontend URL: ${CONFIG.frontendUrl}`);

  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 500
  });
  
  const context = await browser.newContext();
  const page = await context.newPage();

  // Capture all console messages
  const consoleLogs = [];
  page.on('console', msg => {
    const text = msg.text();
    consoleLogs.push(`[${msg.type().toUpperCase()}] ${text}`);
    console.log(`🖥️  [${msg.type().toUpperCase()}] ${text}`);
  });

  // Capture network requests
  const networkRequests = [];
  page.on('request', request => {
    if (request.url().includes('ws://') || request.url().includes('api/')) {
      networkRequests.push(`${request.method()} ${request.url()}`);
      console.log(`🌐 REQUEST: ${request.method()} ${request.url()}`);
    }
  });

  page.on('response', response => {
    if (response.url().includes('api/')) {
      console.log(`📡 RESPONSE: ${response.status()} ${response.url()}`);
    }
  });

  try {
    // Step 1: Load frontend
    console.log('\n📍 Loading frontend...');
    await page.goto(CONFIG.frontendUrl);
    await page.waitForTimeout(3000);

    // Step 2: Check initial state
    console.log('\n🔍 Checking initial state...');
    
    // Check if app-shell is loaded
    const appShell = await page.locator('app-shell').first();
    const isAppShellVisible = await appShell.isVisible().catch(() => false);
    console.log(`App Shell loaded: ${isAppShellVisible}`);

    // Check connection status
    const connectionStatus = await page.evaluate(() => {
      const appShell = document.querySelector('app-shell');
      if (appShell && appShell.shadowRoot) {
        const statusElement = appShell.shadowRoot.querySelector('.connection-status, .status-indicator');
        return statusElement ? statusElement.textContent : 'No status element found';
      }
      return 'No app-shell shadow root';
    });
    console.log(`Connection Status: ${connectionStatus}`);

    // Check for WebSocket connection
    const wsStatus = await page.evaluate(() => {
      return window.WebSocket ? 'WebSocket API available' : 'WebSocket API not available';
    });
    console.log(`WebSocket Status: ${wsStatus}`);

    // Step 3: Check authentication state
    console.log('\n🔐 Checking authentication...');
    const loginForm = await page.locator('login-form').first();
    const isLoginVisible = await loginForm.isVisible().catch(() => false);
    console.log(`Login form visible: ${isLoginVisible}`);

    if (isLoginVisible) {
      console.log('⚠️  User not authenticated - this might prevent wheel generation');
    }

    // Step 4: Check for generate button
    console.log('\n🎡 Checking generate button...');
    const generateButton = await page.locator('button').all();
    const buttonTexts = await Promise.all(
      generateButton.map(async (btn) => {
        try {
          return await btn.textContent();
        } catch {
          return 'N/A';
        }
      })
    );
    
    console.log(`Available buttons: ${buttonTexts.join(', ')}`);
    
    const generateBtn = await page.locator('button:has-text("Generate")').first();
    const isGenerateVisible = await generateBtn.isVisible().catch(() => false);
    console.log(`Generate button visible: ${isGenerateVisible}`);

    if (!isGenerateVisible) {
      console.log('❌ Generate button not found - checking for other wheel-related buttons...');
      const wheelButtons = buttonTexts.filter(text => 
        text && (text.includes('Generate') || text.includes('SPIN') || text.includes('Wheel'))
      );
      console.log(`Wheel-related buttons: ${wheelButtons.join(', ')}`);
    }

    // Step 5: Check sliders
    console.log('\n⚙️  Checking parameter sliders...');
    const sliders = await page.locator('input[type="range"]').all();
    console.log(`Found ${sliders.length} sliders`);

    for (let i = 0; i < sliders.length; i++) {
      try {
        const value = await sliders[i].getAttribute('value');
        const min = await sliders[i].getAttribute('min');
        const max = await sliders[i].getAttribute('max');
        console.log(`Slider ${i + 1}: value=${value}, range=${min}-${max}`);
      } catch (error) {
        console.log(`Slider ${i + 1}: Error reading attributes`);
      }
    }

    // Step 6: Try to trigger wheel generation if possible
    if (isGenerateVisible) {
      console.log('\n🚀 Attempting wheel generation...');
      
      // Set parameters first
      if (sliders.length >= 2) {
        console.log('Setting time to minimum (10min)...');
        await sliders[0].fill('0');
        await page.waitForTimeout(500);
        
        console.log('Setting energy to maximum (100%)...');
        await sliders[1].fill('100');
        await page.waitForTimeout(500);
      }

      console.log('Clicking Generate button...');
      await generateBtn.click();
      
      // Wait a bit and check for changes
      await page.waitForTimeout(5000);
      
      // Check if button text changed
      const newButtonText = await generateBtn.textContent().catch(() => 'Button not found');
      console.log(`Button text after click: ${newButtonText}`);
      
      // Check for loading state
      const isLoading = await page.evaluate(() => {
        const appShell = document.querySelector('app-shell');
        if (appShell && appShell.shadowRoot) {
          const loadingElement = appShell.shadowRoot.querySelector('.loading, .spinner, [data-loading]');
          return loadingElement ? loadingElement.style.display !== 'none' : false;
        }
        return false;
      });
      console.log(`Loading state active: ${isLoading}`);
    }

    // Step 7: Final diagnostics
    console.log('\n📊 Final Diagnostics:');
    console.log(`Total console messages: ${consoleLogs.length}`);
    console.log(`Network requests made: ${networkRequests.length}`);
    
    // Check for specific error patterns
    const errors = consoleLogs.filter(log => log.includes('[ERROR]'));
    const warnings = consoleLogs.filter(log => log.includes('[WARN]'));
    const wheelLogs = consoleLogs.filter(log => log.includes('wheel') || log.includes('WHEEL'));
    
    console.log(`Errors: ${errors.length}`);
    console.log(`Warnings: ${warnings.length}`);
    console.log(`Wheel-related logs: ${wheelLogs.length}`);
    
    if (errors.length > 0) {
      console.log('\n❌ ERRORS FOUND:');
      errors.forEach(error => console.log(`  ${error}`));
    }
    
    if (wheelLogs.length > 0) {
      console.log('\n🎡 WHEEL-RELATED LOGS:');
      wheelLogs.forEach(log => console.log(`  ${log}`));
    }

    // Wait a bit more to see if anything happens
    console.log('\n⏳ Waiting 10 more seconds for any delayed responses...');
    await page.waitForTimeout(10000);

    return true;

  } catch (error) {
    console.error('❌ Debug failed:', error);
    return false;
  } finally {
    await browser.close();
  }
}

// Run the debug
debugWheelGeneration().then(success => {
  console.log('\n🔍 Debug completed');
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('💥 Debug execution failed:', error);
  process.exit(1);
});
