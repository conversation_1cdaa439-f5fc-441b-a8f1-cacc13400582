const { chromium } = require('playwright');

class WebSocketRoutingDebugger {
    constructor() {
        this.browser = null;
        this.page = null;
    }

    async initialize() {
        console.log('🚀 Initializing WebSocket Routing Debugger...');
        
        this.browser = await chromium.launch({ 
            headless: false,
            slowMo: 500
        });
        
        this.page = await this.browser.newPage();
        
        // Inject debugging code into the page
        await this.page.addInitScript(() => {
            // Override WebSocket to log all messages
            const originalWebSocket = window.WebSocket;
            window.WebSocket = class extends originalWebSocket {
                constructor(url, protocols) {
                    super(url, protocols);
                    console.log('🔌 WebSocket created for:', url);
                    
                    this.addEventListener('message', (event) => {
                        try {
                            const data = JSON.parse(event.data);
                            console.log('📨 Raw WebSocket message received:', data.type, data);
                            
                            if (data.type === 'wheel_data') {
                                console.log('🎡 WHEEL_DATA MESSAGE DETECTED!', data);
                            }
                        } catch (e) {
                            console.log('📨 Non-JSON WebSocket message:', event.data);
                        }
                    });
                }
            };
        });

        console.log('✅ Debugger initialized successfully');
    }

    async loadFrontendAndMonitor() {
        console.log('\n🌐 Loading frontend and monitoring WebSocket routing...');
        
        try {
            await this.page.goto('http://localhost:3001/', { 
                waitUntil: 'networkidle',
                timeout: 30000 
            });
            
            // Wait for the app to initialize
            await this.page.waitForTimeout(3000);
            
            // Add debugging to the WebSocket manager
            await this.page.evaluate(() => {
                // Find the app-shell component
                const appShell = document.querySelector('app-shell');
                if (appShell && appShell.websocketManager) {
                    console.log('🔧 Adding WebSocket manager debugging...');
                    
                    // Override the onMessage method to log all registrations
                    const originalOnMessage = appShell.websocketManager.onMessage.bind(appShell.websocketManager);
                    appShell.websocketManager.onMessage = function(type, callback) {
                        console.log(`📝 Registering handler for message type: ${type}`);
                        return originalOnMessage(type, (data) => {
                            console.log(`🎯 Handler called for ${type}:`, data);
                            return callback(data);
                        });
                    };
                    
                    // Override handleWheelGenerated to add debugging
                    if (appShell.handleWheelGenerated) {
                        const originalHandler = appShell.handleWheelGenerated.bind(appShell);
                        appShell.handleWheelGenerated = function(data) {
                            console.log('🎡 handleWheelGenerated called with:', data);
                            return originalHandler(data);
                        };
                    }
                }
            });
            
            // Send the message
            const chatInput = await this.page.locator('textarea, input[type="text"]').first();
            await chatInput.waitFor({ timeout: 10000 });
            
            await chatInput.fill("hey, would you generate me a wheel please ? I'm tired but not sleepy");
            await chatInput.press('Enter');
            
            console.log('✅ Message sent, monitoring for 30 seconds...');
            
            // Monitor console logs for 30 seconds
            let messageCount = 0;
            let wheelDataReceived = false;
            let handlerCalled = false;
            
            this.page.on('console', msg => {
                const text = msg.text();
                messageCount++;
                
                if (text.includes('wheel_data') || text.includes('WHEEL_DATA')) {
                    wheelDataReceived = true;
                    console.log(`🎡 [${messageCount}] ${text}`);
                } else if (text.includes('handleWheelGenerated')) {
                    handlerCalled = true;
                    console.log(`🎯 [${messageCount}] ${text}`);
                } else if (text.includes('WebSocket') || text.includes('message')) {
                    console.log(`📨 [${messageCount}] ${text}`);
                }
            });
            
            await this.page.waitForTimeout(30000);
            
            console.log('\n📊 Monitoring Results:');
            console.log(`  Total console messages: ${messageCount}`);
            console.log(`  Wheel data received: ${wheelDataReceived ? '✅' : '❌'}`);
            console.log(`  Handler called: ${handlerCalled ? '✅' : '❌'}`);
            
            return { wheelDataReceived, handlerCalled };
            
        } catch (error) {
            console.error('❌ Failed to monitor WebSocket routing:', error.message);
            return { wheelDataReceived: false, handlerCalled: false };
        }
    }

    async checkWebSocketManagerState() {
        console.log('\n🔍 Checking WebSocket Manager State...');
        
        try {
            const wsState = await this.page.evaluate(() => {
                const appShell = document.querySelector('app-shell');
                if (!appShell) return { error: 'No app-shell found' };
                
                const wsManager = appShell.websocketManager;
                if (!wsManager) return { error: 'No websocketManager found' };
                
                return {
                    isConnected: wsManager.isConnected(),
                    state: wsManager.state,
                    hasEventListeners: !!wsManager.eventListeners,
                    messageHandlers: Object.keys(wsManager.messageHandlers || {}),
                };
            });
            
            console.log('🔍 WebSocket Manager Analysis:');
            if (wsState.error) {
                console.log(`  ❌ ${wsState.error}`);
            } else {
                console.log(`  Connected: ${wsState.isConnected ? '✅' : '❌'}`);
                console.log(`  State: ${JSON.stringify(wsState.state)}`);
                console.log(`  Message Handlers: ${wsState.messageHandlers.join(', ')}`);
            }
            
            return wsState;
            
        } catch (error) {
            console.error('❌ Failed to check WebSocket manager state:', error.message);
            return null;
        }
    }

    async runCompleteDebugTest() {
        console.log('\n🔬 Running Complete WebSocket Routing Debug Test');
        console.log('════════════════════════════════════════════════════════════');
        
        const results = await this.loadFrontendAndMonitor();
        await this.checkWebSocketManagerState();
        
        console.log('\n💡 Debug Summary:');
        if (!results.wheelDataReceived) {
            console.log('  ❌ wheel_data message not received - check backend WebSocket sending');
        } else if (!results.handlerCalled) {
            console.log('  ❌ wheel_data received but handler not called - check message routing');
        } else {
            console.log('  ✅ wheel_data received and handler called - check handler implementation');
        }
    }

    async cleanup() {
        if (this.browser) {
            await this.browser.close();
        }
    }
}

// Run the debug test
async function main() {
    const wsDebugger = new WebSocketRoutingDebugger();

    try {
        await wsDebugger.initialize();
        await wsDebugger.runCompleteDebugTest();
    } catch (error) {
        console.error('❌ Debug test execution failed:', error);
    } finally {
        await wsDebugger.cleanup();
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = WebSocketRoutingDebugger;
