#!/usr/bin/env python3

"""
Test Resource Agent Fix
Validates that the resource agent test now passes with the analyzed_type fix
"""

import subprocess
import sys
import os
import json
from datetime import datetime

def run_command(command, cwd=None):
    """Run a shell command and return the result"""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            cwd=cwd,
            capture_output=True, 
            text=True, 
            timeout=300  # 5 minute timeout
        )
        return {
            'success': result.returncode == 0,
            'stdout': result.stdout,
            'stderr': result.stderr,
            'returncode': result.returncode
        }
    except subprocess.TimeoutExpired:
        return {
            'success': False,
            'stdout': '',
            'stderr': 'Test timed out after 5 minutes',
            'returncode': -1
        }
    except Exception as e:
        return {
            'success': False,
            'stdout': '',
            'stderr': str(e),
            'returncode': -1
        }

def test_resource_agent_fix():
    """Test the specific failing resource agent test"""
    print("🧪 Testing Resource Agent Fix...")
    print("================================")
    
    # Find backend directory
    backend_dir = None
    possible_paths = [
        "../../backend",
        "../backend", 
        "./backend"
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            backend_dir = os.path.abspath(path)
            break
    
    if not backend_dir:
        print("❌ Backend directory not found")
        return False
    
    print(f"📁 Backend directory: {backend_dir}")
    
    # Test command
    test_command = "docker exec -it backend-web-1 python -m pytest apps/main/tests/test_agents/test_resource_agent.py::test_resource_agent_basic_functionality -v"
    
    print(f"🔧 Running test command...")
    print(f"Command: {test_command}")
    
    result = run_command(test_command, cwd=backend_dir)
    
    print(f"\n📊 Test Results:")
    print(f"Return Code: {result['returncode']}")
    print(f"Success: {result['success']}")
    
    if result['stdout']:
        print(f"\n📝 STDOUT:")
        print(result['stdout'])
    
    if result['stderr']:
        print(f"\n⚠️ STDERR:")
        print(result['stderr'])
    
    # Analyze results
    if result['success']:
        print("\n✅ TEST PASSED - Resource agent fix successful!")
        return True
    else:
        print("\n❌ TEST FAILED - Analyzing failure...")
        
        # Check for specific error patterns
        output = result['stdout'] + result['stderr']
        
        if "analyzed_type" in output and "is not None" in output:
            print("🔍 Still failing on analyzed_type assertion")
            print("💡 The fix may not be complete or the test needs to be re-run")
        elif "Environment type not correctly analyzed" in output:
            print("🔍 Environment type analysis still failing")
            print("💡 Check if the resource agent is properly mapping environment_type to analyzed_type")
        elif "Failed to load agent configuration" in output:
            print("🔍 Agent configuration loading error")
            print("💡 This might be a test environment issue, not related to our fix")
        elif "timeout" in output.lower():
            print("🔍 Test timed out")
            print("💡 Backend might be slow or unresponsive")
        else:
            print("🔍 Unknown test failure")
            print("💡 Check the full output above for clues")
        
        return False

def test_database_fix():
    """Test that the database prefetch fix is working"""
    print("\n🗄️ Testing Database Prefetch Fix...")
    print("===================================")
    
    # Find backend directory
    backend_dir = None
    possible_paths = [
        "../../backend",
        "../backend", 
        "./backend"
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            backend_dir = os.path.abspath(path)
            break
    
    if not backend_dir:
        print("❌ Backend directory not found")
        return False
    
    # Test the get_user_profile_tool specifically
    test_command = "docker exec -it backend-web-1 python -c \"from apps.main.agents.tools.get_user_profile_tool import get_user_profile; print('✅ Tool imports successfully')\""
    
    print(f"🔧 Testing tool import...")
    result = run_command(test_command, cwd=backend_dir)
    
    if result['success']:
        print("✅ Database prefetch fix appears to be working")
        return True
    else:
        print("❌ Database prefetch fix may have issues")
        print(f"Error: {result['stderr']}")
        return False

def generate_test_report():
    """Generate a comprehensive test report"""
    print("\n📊 Generating Test Report...")
    print("============================")
    
    report = {
        "timestamp": datetime.now().isoformat(),
        "test_results": {
            "resource_agent_fix": None,
            "database_fix": None
        },
        "summary": {
            "total_tests": 2,
            "passed": 0,
            "failed": 0
        }
    }
    
    # Test resource agent fix
    resource_agent_success = test_resource_agent_fix()
    report["test_results"]["resource_agent_fix"] = resource_agent_success
    
    # Test database fix
    database_success = test_database_fix()
    report["test_results"]["database_fix"] = database_success
    
    # Calculate summary
    if resource_agent_success:
        report["summary"]["passed"] += 1
    else:
        report["summary"]["failed"] += 1
        
    if database_success:
        report["summary"]["passed"] += 1
    else:
        report["summary"]["failed"] += 1
    
    # Save report
    try:
        os.makedirs("test-results", exist_ok=True)
        report_file = f"test-results/resource-agent-fix-test-{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        print(f"📄 Test report saved to: {report_file}")
    except Exception as e:
        print(f"⚠️ Could not save test report: {e}")
    
    # Print summary
    print(f"\n🎯 Test Summary:")
    print(f"================")
    print(f"Total Tests: {report['summary']['total_tests']}")
    print(f"Passed: {report['summary']['passed']}")
    print(f"Failed: {report['summary']['failed']}")
    print(f"Success Rate: {(report['summary']['passed'] / report['summary']['total_tests'] * 100):.1f}%")
    
    if report['summary']['failed'] == 0:
        print("\n✅ ALL TESTS PASSED - Fixes are working correctly!")
    else:
        print(f"\n⚠️ {report['summary']['failed']} TEST(S) FAILED - Review output above")
    
    return report['summary']['failed'] == 0

def main():
    """Main function"""
    print("🔧 Resource Agent Fix Validation")
    print("=================================")
    print("This script tests the fixes for:")
    print("1. Resource agent analyzed_type field")
    print("2. Database prefetch_related error")
    print("")
    
    success = generate_test_report()
    
    if success:
        print("\n🚀 All fixes validated successfully!")
        print("You can now proceed with confidence that the issues are resolved.")
    else:
        print("\n🔍 Some fixes need attention.")
        print("Review the test output above and apply any necessary corrections.")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
