#!/usr/bin/env node

/**
 * Enhanced Packet Debugger
 * 
 * Advanced debugging tool that provides:
 * - Real-time packet inspection for all clients
 * - Rich formatting and visualization
 * - Interactive JSON message sending
 * - Connection tracking diagnostics
 * - Client-specific packet history
 */

import WebSocket from 'ws';
import { CONFIG } from './config.js';
import fs from 'fs';
import path from 'path';
import readline from 'readline';

class EnhancedPacketDebugger {
  constructor() {
    this.clients = new Map();
    this.adminSocket = null;
    this.packetHistory = [];
    this.clientPacketHistory = new Map(); // Per-client packet history
    this.isRunning = false;
    this.rl = null;
    this.selectedClient = null;
    
    this.logFile = path.join(CONFIG.logging.logDirectory, `enhanced-debugger-${Date.now()}.log`);
    
    // Ensure log directory exists
    if (!fs.existsSync(CONFIG.logging.logDirectory)) {
      fs.mkdirSync(CONFIG.logging.logDirectory, { recursive: true });
    }
    
    // Color schemes
    this.colors = {
      reset: '\x1b[0m',
      bright: '\x1b[1m',
      dim: '\x1b[2m',
      red: '\x1b[31m',
      green: '\x1b[32m',
      yellow: '\x1b[33m',
      blue: '\x1b[34m',
      magenta: '\x1b[35m',
      cyan: '\x1b[36m',
      white: '\x1b[37m',
      gray: '\x1b[90m'
    };
  }

  async start() {
    console.log(`${this.colors.cyan}🔍 Enhanced Packet Debugger${this.colors.reset}`);
    console.log(`${this.colors.cyan}=============================${this.colors.reset}\n`);
    
    this.isRunning = true;
    
    // Setup readline for interactive commands
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
      prompt: `${this.colors.yellow}📦 > ${this.colors.reset}`
    });
    
    try {
      // Start with admin connection
      await this.connectAdmin();
      
      // Create initial test clients
      await this.createTestClients(3);
      
      // Start interactive session
      this.startInteractiveSession();
      
    } catch (error) {
      console.error(`${this.colors.red}❌ Failed to start debugger: ${error.message}${this.colors.reset}`);
    }
  }

  async connectAdmin() {
    console.log(`${this.colors.blue}🔌 Connecting to admin WebSocket...${this.colors.reset}`);
    
    return new Promise((resolve, reject) => {
      const adminWsUrl = 'ws://localhost:8000/ws/connection-monitor/';
      this.adminSocket = new WebSocket(adminWsUrl);
      
      const timeout = setTimeout(() => {
        reject(new Error('Admin connection timeout'));
      }, 10000);

      this.adminSocket.on('open', () => {
        clearTimeout(timeout);
        console.log(`${this.colors.green}✅ Admin WebSocket connected${this.colors.reset}`);
        
        // Request initial data
        this.sendAdminMessage({ type: 'get_connections' });
        this.sendAdminMessage({ type: 'get_system_health' });
        this.sendAdminMessage({ type: 'get_message_stats' });
        
        resolve();
      });

      this.adminSocket.on('error', (error) => {
        clearTimeout(timeout);
        console.error(`${this.colors.red}❌ Admin connection failed: ${error.message}${this.colors.reset}`);
        reject(error);
      });

      this.adminSocket.on('message', (data) => {
        this.handlePacket('ADMIN_RECEIVED', data, 'admin');
      });

      this.adminSocket.on('close', (code, reason) => {
        console.log(`${this.colors.yellow}🔌 Admin socket closed: ${code} ${reason}${this.colors.reset}`);
      });
    });
  }

  async createTestClients(count) {
    console.log(`${this.colors.blue}🔗 Creating ${count} test clients...${this.colors.reset}`);
    
    for (let i = 1; i <= count; i++) {
      try {
        await this.createClient(i);
        console.log(`${this.colors.green}✅ Client ${i} connected${this.colors.reset}`);
      } catch (error) {
        console.error(`${this.colors.red}❌ Client ${i} failed: ${error.message}${this.colors.reset}`);
      }
    }
  }

  async createClient(clientId) {
    return new Promise((resolve, reject) => {
      const socket = new WebSocket(CONFIG.backend.websocketUrl);
      const clientInfo = {
        id: clientId,
        socket: socket,
        connected: false,
        messageCount: 0,
        userId: `debugger-user-${clientId}`
      };
      
      // Initialize packet history for this client
      this.clientPacketHistory.set(clientId, []);
      
      const timeout = setTimeout(() => {
        reject(new Error(`Client ${clientId} connection timeout`));
      }, 5000);

      socket.on('open', () => {
        clearTimeout(timeout);
        clientInfo.connected = true;
        this.clients.set(clientId, clientInfo);
        
        // Send initial identification message
        const message = {
          type: 'chat_message',
          content: {
            message: `Hello from enhanced debugger client ${clientId}`,
            user_profile_id: clientInfo.userId,
            timestamp: new Date().toISOString()
          }
        };
        
        this.sendClientMessage(clientId, message);
        resolve();
      });

      socket.on('error', (error) => {
        clearTimeout(timeout);
        reject(error);
      });

      socket.on('message', (data) => {
        this.handlePacket(`CLIENT_${clientId}_RECEIVED`, data, clientId);
      });

      socket.on('close', (code, reason) => {
        console.log(`${this.colors.yellow}🔌 Client ${clientId} disconnected: ${code} ${reason}${this.colors.reset}`);
        clientInfo.connected = false;
      });
    });
  }

  handlePacket(direction, data, clientId) {
    const timestamp = new Date().toISOString();
    let packet;
    
    try {
      packet = JSON.parse(data);
    } catch (error) {
      packet = { raw: data.toString(), parseError: error.message };
    }
    
    const entry = {
      timestamp,
      direction,
      packet,
      clientId
    };
    
    // Add to global history
    this.packetHistory.push(entry);
    
    // Add to client-specific history
    if (clientId !== 'admin' && this.clientPacketHistory.has(clientId)) {
      this.clientPacketHistory.get(clientId).push(entry);
    }
    
    // Keep only last 1000 packets globally
    if (this.packetHistory.length > 1000) {
      this.packetHistory.shift();
    }
    
    // Keep only last 100 packets per client
    if (clientId !== 'admin' && this.clientPacketHistory.has(clientId)) {
      const clientHistory = this.clientPacketHistory.get(clientId);
      if (clientHistory.length > 100) {
        clientHistory.shift();
      }
    }
    
    // Display packet with rich formatting
    this.displayPacket(entry);
    
    // Save to log file
    fs.appendFileSync(this.logFile, JSON.stringify(entry) + '\n');
    
    // Special handling for admin connection data
    if (direction === 'ADMIN_RECEIVED' && packet.type === 'connection_data') {
      this.analyzeConnectionData(packet.data);
    }
  }

  displayPacket(entry) {
    const { timestamp, direction, packet, clientId } = entry;
    
    // Color coding based on direction
    let color = this.colors.white;
    let icon = '📦';
    
    if (direction.includes('ADMIN')) {
      color = this.colors.magenta;
      icon = '🔧';
    } else if (direction.includes('RECEIVED')) {
      color = this.colors.green;
      icon = '📨';
    } else if (direction.includes('SENT')) {
      color = this.colors.blue;
      icon = '📤';
    }
    
    // Only show if no client is selected, or if it matches the selected client
    if (this.selectedClient === null || clientId === this.selectedClient || clientId === 'admin') {
      console.log(`\n${color}${icon} [${timestamp}] ${direction}${this.colors.reset}`);
      console.log(this.formatJSON(packet));
      console.log(`${this.colors.gray}${'─'.repeat(60)}${this.colors.reset}`);
    }
  }

  formatJSON(obj) {
    let json = JSON.stringify(obj, null, 2);
    
    // Apply syntax highlighting
    json = json
      .replace(/("type":\s*"[^"]*")/g, `${this.colors.yellow}$1${this.colors.reset}`)
      .replace(/("user_profile_id":\s*"[^"]*")/g, `${this.colors.cyan}$1${this.colors.reset}`)
      .replace(/("message":\s*"[^"]*")/g, `${this.colors.green}$1${this.colors.reset}`)
      .replace(/("timestamp":\s*"[^"]*")/g, `${this.colors.gray}$1${this.colors.reset}`)
      .replace(/("error":\s*"[^"]*")/g, `${this.colors.red}$1${this.colors.reset}`)
      .replace(/("status":\s*"[^"]*")/g, `${this.colors.blue}$1${this.colors.reset}`);
    
    return json;
  }

  analyzeConnectionData(connectionData) {
    if (Array.isArray(connectionData) && connectionData.length === 0) {
      console.log(`${this.colors.red}⚠️  CONNECTION TRACKING ISSUE: Admin reports 0 connections but we have ${this.clients.size} active clients${this.colors.reset}`);
      console.log(`${this.colors.yellow}💡 This suggests the connection tracking integration is not working properly${this.colors.reset}`);
    } else {
      console.log(`${this.colors.green}✅ Connection tracking working: ${connectionData.length} connections reported${this.colors.reset}`);
    }
  }

  startInteractiveSession() {
    console.log(`\n${this.colors.cyan}🎮 Interactive Debugging Session${this.colors.reset}`);
    console.log(`${this.colors.cyan}=================================${this.colors.reset}`);
    this.showHelp();
    
    this.rl.prompt();
    
    this.rl.on('line', (input) => {
      this.handleCommand(input.trim());
      this.rl.prompt();
    });
    
    this.rl.on('close', () => {
      this.stop();
    });
  }

  showHelp() {
    console.log(`${this.colors.bright}Available commands:${this.colors.reset}`);
    console.log(`  ${this.colors.green}📋 list${this.colors.reset} - Show all connected clients`);
    console.log(`  ${this.colors.green}🔍 select <client_id>${this.colors.reset} - Focus on specific client packets`);
    console.log(`  ${this.colors.green}🔍 select admin${this.colors.reset} - Focus on admin packets only`);
    console.log(`  ${this.colors.green}🔍 select all${this.colors.reset} - Show all packets (default)`);
    console.log(`  ${this.colors.green}📦 packets [n]${this.colors.reset} - Show last n packets (default: 10)`);
    console.log(`  ${this.colors.green}📦 client-packets <client_id> [n]${this.colors.reset} - Show client-specific packets`);
    console.log(`  ${this.colors.green}📤 send <client_id> <json>${this.colors.reset} - Send JSON to client`);
    console.log(`  ${this.colors.green}📤 admin <json>${this.colors.reset} - Send JSON to admin socket`);
    console.log(`  ${this.colors.green}🔄 reconnect <client_id>${this.colors.reset} - Reconnect a client`);
    console.log(`  ${this.colors.green}🆕 new${this.colors.reset} - Create a new test client`);
    console.log(`  ${this.colors.green}🔧 diagnose${this.colors.reset} - Run connection tracking diagnostics`);
    console.log(`  ${this.colors.green}💾 save${this.colors.reset} - Save packet history to file`);
    console.log(`  ${this.colors.green}🧹 clear${this.colors.reset} - Clear packet history`);
    console.log(`  ${this.colors.green}❓ help${this.colors.reset} - Show this help`);
    console.log(`  ${this.colors.green}🚪 quit${this.colors.reset} - Exit debugger\n`);
  }

  handleCommand(input) {
    const parts = input.split(' ');
    const command = parts[0].toLowerCase();
    
    try {
      switch (command) {
        case 'list':
          this.listClients();
          break;
          
        case 'select':
          const target = parts[1];
          if (target === 'all') {
            this.selectedClient = null;
            console.log(`${this.colors.green}✅ Showing all packets${this.colors.reset}`);
          } else if (target === 'admin') {
            this.selectedClient = 'admin';
            console.log(`${this.colors.green}✅ Focusing on admin packets${this.colors.reset}`);
          } else {
            const clientId = parseInt(target);
            if (this.clients.has(clientId)) {
              this.selectedClient = clientId;
              console.log(`${this.colors.green}✅ Focusing on client ${clientId} packets${this.colors.reset}`);
            } else {
              console.log(`${this.colors.red}❌ Client ${clientId} not found${this.colors.reset}`);
            }
          }
          break;
          
        case 'packets':
          const count = parseInt(parts[1]) || 10;
          this.showPackets(count);
          break;
          
        case 'client-packets':
          const clientId = parseInt(parts[1]);
          const clientCount = parseInt(parts[2]) || 10;
          this.showClientPackets(clientId, clientCount);
          break;
          
        case 'send':
          if (parts.length < 3) {
            console.log(`${this.colors.red}❌ Usage: send <client_id> <json>${this.colors.reset}`);
            break;
          }
          const sendClientId = parseInt(parts[1]);
          const clientJson = parts.slice(2).join(' ');
          this.sendToClient(sendClientId, clientJson);
          break;
          
        case 'admin':
          if (parts.length < 2) {
            console.log(`${this.colors.red}❌ Usage: admin <json>${this.colors.reset}`);
            break;
          }
          const adminJson = parts.slice(1).join(' ');
          this.sendToAdmin(adminJson);
          break;
          
        case 'reconnect':
          const reconnectId = parseInt(parts[1]);
          this.reconnectClient(reconnectId);
          break;
          
        case 'new':
          this.createNewClient();
          break;
          
        case 'diagnose':
          this.runDiagnostics();
          break;
          
        case 'save':
          this.savePacketHistory();
          break;
          
        case 'clear':
          this.packetHistory = [];
          this.clientPacketHistory.clear();
          console.log(`${this.colors.green}✅ Packet history cleared${this.colors.reset}`);
          break;
          
        case 'help':
          this.showHelp();
          break;
          
        case 'quit':
        case 'exit':
          this.stop();
          break;
          
        default:
          console.log(`${this.colors.red}❓ Unknown command. Type "help" for available commands.${this.colors.reset}`);
      }
    } catch (error) {
      console.error(`${this.colors.red}❌ Command error: ${error.message}${this.colors.reset}`);
    }
  }

  listClients() {
    console.log(`\n${this.colors.cyan}📋 Connected Clients:${this.colors.reset}`);
    for (const [id, client] of this.clients) {
      const status = client.connected ? `${this.colors.green}✅ Connected${this.colors.reset}` : `${this.colors.red}❌ Disconnected${this.colors.reset}`;
      const packets = this.clientPacketHistory.get(id)?.length || 0;
      console.log(`  Client ${id}: ${status} (${client.messageCount} sent, ${packets} total packets)`);
      console.log(`    User ID: ${client.userId}`);
    }
    
    const adminStatus = this.adminSocket && this.adminSocket.readyState === WebSocket.OPEN 
      ? `${this.colors.green}✅ Connected${this.colors.reset}` : `${this.colors.red}❌ Disconnected${this.colors.reset}`;
    console.log(`  Admin Socket: ${adminStatus}`);
    
    if (this.selectedClient !== null) {
      console.log(`\n${this.colors.yellow}🔍 Currently focusing on: ${this.selectedClient === 'admin' ? 'Admin' : `Client ${this.selectedClient}`}${this.colors.reset}`);
    }
  }

  showPackets(count) {
    console.log(`\n${this.colors.cyan}📦 Last ${count} packets:${this.colors.reset}`);
    const recent = this.packetHistory.slice(-count);
    
    for (const entry of recent) {
      this.displayPacket(entry);
    }
  }

  showClientPackets(clientId, count) {
    if (!this.clientPacketHistory.has(clientId)) {
      console.log(`${this.colors.red}❌ No packet history for client ${clientId}${this.colors.reset}`);
      return;
    }
    
    console.log(`\n${this.colors.cyan}📦 Last ${count} packets for Client ${clientId}:${this.colors.reset}`);
    const clientHistory = this.clientPacketHistory.get(clientId);
    const recent = clientHistory.slice(-count);
    
    for (const entry of recent) {
      this.displayPacket(entry);
    }
  }

  sendToClient(clientId, jsonString) {
    const client = this.clients.get(clientId);
    if (!client) {
      console.log(`${this.colors.red}❌ Client ${clientId} not found${this.colors.reset}`);
      return;
    }
    
    if (!client.connected) {
      console.log(`${this.colors.red}❌ Client ${clientId} is not connected${this.colors.reset}`);
      return;
    }
    
    try {
      const message = JSON.parse(jsonString);
      this.sendClientMessage(clientId, message);
      console.log(`${this.colors.green}✅ Message sent to client ${clientId}${this.colors.reset}`);
    } catch (error) {
      console.log(`${this.colors.red}❌ Invalid JSON: ${error.message}${this.colors.reset}`);
    }
  }

  sendToAdmin(jsonString) {
    if (!this.adminSocket || this.adminSocket.readyState !== WebSocket.OPEN) {
      console.log(`${this.colors.red}❌ Admin socket not connected${this.colors.reset}`);
      return;
    }
    
    try {
      const message = JSON.parse(jsonString);
      this.sendAdminMessage(message);
      console.log(`${this.colors.green}✅ Message sent to admin socket${this.colors.reset}`);
    } catch (error) {
      console.log(`${this.colors.red}❌ Invalid JSON: ${error.message}${this.colors.reset}`);
    }
  }

  sendClientMessage(clientId, message) {
    const client = this.clients.get(clientId);
    if (client && client.connected) {
      client.socket.send(JSON.stringify(message));
      client.messageCount++;
      this.handlePacket(`CLIENT_${clientId}_SENT`, JSON.stringify(message), clientId);
    }
  }

  sendAdminMessage(message) {
    if (this.adminSocket && this.adminSocket.readyState === WebSocket.OPEN) {
      this.adminSocket.send(JSON.stringify(message));
      this.handlePacket('ADMIN_SENT', JSON.stringify(message), 'admin');
    }
  }

  async reconnectClient(clientId) {
    console.log(`${this.colors.blue}🔄 Reconnecting client ${clientId}...${this.colors.reset}`);
    
    // Close existing connection if any
    const existingClient = this.clients.get(clientId);
    if (existingClient && existingClient.socket) {
      existingClient.socket.close();
    }
    
    try {
      await this.createClient(clientId);
      console.log(`${this.colors.green}✅ Client ${clientId} reconnected${this.colors.reset}`);
    } catch (error) {
      console.log(`${this.colors.red}❌ Failed to reconnect client ${clientId}: ${error.message}${this.colors.reset}`);
    }
  }

  async createNewClient() {
    const newId = Math.max(...this.clients.keys(), 0) + 1;
    console.log(`${this.colors.blue}🆕 Creating new client ${newId}...${this.colors.reset}`);
    
    try {
      await this.createClient(newId);
      console.log(`${this.colors.green}✅ Client ${newId} created${this.colors.reset}`);
    } catch (error) {
      console.log(`${this.colors.red}❌ Failed to create client ${newId}: ${error.message}${this.colors.reset}`);
    }
  }

  runDiagnostics() {
    console.log(`\n${this.colors.cyan}🔧 Running Connection Tracking Diagnostics...${this.colors.reset}`);
    
    // Request fresh data from admin
    this.sendAdminMessage({ type: 'get_connections' });
    
    console.log(`${this.colors.yellow}📊 Current State:${this.colors.reset}`);
    console.log(`  - Active test clients: ${this.clients.size}`);
    console.log(`  - Admin socket: ${this.adminSocket?.readyState === WebSocket.OPEN ? 'Connected' : 'Disconnected'}`);
    console.log(`  - Total packets logged: ${this.packetHistory.length}`);
    
    // Check if we're receiving admin responses
    const adminPackets = this.packetHistory.filter(p => p.direction === 'ADMIN_RECEIVED').length;
    console.log(`  - Admin responses received: ${adminPackets}`);
    
    if (adminPackets === 0) {
      console.log(`${this.colors.red}⚠️  No admin responses - check backend logs${this.colors.reset}`);
    }
  }

  savePacketHistory() {
    const timestamp = Date.now();
    
    // Save global history
    const globalFile = path.join(CONFIG.logging.logDirectory, `packet-history-global-${timestamp}.json`);
    fs.writeFileSync(globalFile, JSON.stringify(this.packetHistory, null, 2));
    
    // Save per-client histories
    const clientHistories = {};
    for (const [clientId, history] of this.clientPacketHistory) {
      clientHistories[clientId] = history;
    }
    
    const clientFile = path.join(CONFIG.logging.logDirectory, `packet-history-clients-${timestamp}.json`);
    fs.writeFileSync(clientFile, JSON.stringify(clientHistories, null, 2));
    
    console.log(`${this.colors.green}💾 Packet history saved:${this.colors.reset}`);
    console.log(`  Global: ${globalFile}`);
    console.log(`  Clients: ${clientFile}`);
  }

  stop() {
    console.log(`\n${this.colors.yellow}🛑 Stopping enhanced packet debugger...${this.colors.reset}`);
    this.isRunning = false;
    
    // Close all connections
    for (const [id, client] of this.clients) {
      if (client.socket) {
        client.socket.close();
      }
    }
    
    if (this.adminSocket) {
      this.adminSocket.close();
    }
    
    if (this.rl) {
      this.rl.close();
    }
    
    console.log(`${this.colors.green}✅ Enhanced packet debugger stopped${this.colors.reset}`);
    process.exit(0);
  }
}

// CLI interface
if (import.meta.url === `file://${process.argv[1]}`) {
  const packetDebugger = new EnhancedPacketDebugger();

  // Handle graceful shutdown
  process.on('SIGINT', () => {
    packetDebugger.stop();
  });

  packetDebugger.start().catch((error) => {
    console.error('❌ Enhanced debugger failed:', error.message);
    process.exit(1);
  });
}

export default EnhancedPacketDebugger;
