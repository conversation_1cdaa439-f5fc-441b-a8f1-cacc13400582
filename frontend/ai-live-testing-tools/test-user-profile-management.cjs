#!/usr/bin/env node

/**
 * User Profile Management Page Test
 * 
 * Tests the new user profile management admin page functionality including:
 * - Page loading and display
 * - Search and filter functionality
 * - Profile modal opening and data display
 * - API endpoints for profile data
 */

const puppeteer = require('puppeteer');

async function testUserProfileManagement() {
    console.log('🧪 Testing User Profile Management Page...\n');
    
    let browser;
    let testResults = {
        pageLoad: false,
        profileList: false,
        searchFilter: false,
        modalOpen: false,
        apiEndpoint: false,
        overallSuccess: false
    };
    
    try {
        // Launch browser
        browser = await puppeteer.launch({
            headless: false,
            defaultViewport: { width: 1400, height: 900 },
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        
        const page = await browser.newPage();
        
        // Enable console logging
        page.on('console', msg => {
            if (msg.type() === 'error') {
                console.log('❌ Browser Console Error:', msg.text());
            }
        });
        
        // Test 1: Navigate to user profile management page
        console.log('📋 Test 1: Loading User Profile Management Page...');
        try {
            await page.goto('http://localhost:8000/admin/user-profiles/', {
                waitUntil: 'networkidle0',
                timeout: 30000
            });
            
            // Check if we're redirected to login
            const currentUrl = page.url();
            if (currentUrl.includes('/admin/login/')) {
                console.log('🔐 Need to login to admin...');
                
                // Try to login (assuming default admin credentials)
                await page.type('#id_username', 'admin');
                await page.type('#id_password', 'admin');
                await page.click('input[type="submit"]');
                
                // Wait for redirect and try again
                await page.waitForNavigation({ waitUntil: 'networkidle0' });
                await page.goto('http://localhost:8000/admin/user-profiles/', {
                    waitUntil: 'networkidle0',
                    timeout: 30000
                });
            }
            
            // Check if page loaded successfully
            const title = await page.title();
            const hasProfileManagement = title.includes('User Profile Management');
            
            if (hasProfileManagement) {
                console.log('✅ User Profile Management page loaded successfully');
                testResults.pageLoad = true;
            } else {
                console.log('❌ User Profile Management page not found');
                console.log('   Current title:', title);
                console.log('   Current URL:', page.url());
            }
            
        } catch (error) {
            console.log('❌ Failed to load User Profile Management page:', error.message);
        }
        
        // Test 2: Check profile list display
        console.log('\n📋 Test 2: Checking Profile List Display...');
        try {
            // Wait for profile table to load
            await page.waitForSelector('.profile-table', { timeout: 10000 });
            
            // Check for profile rows
            const profileRows = await page.$$('.profile-table tbody tr');
            console.log(`✅ Found ${profileRows.length} profile rows in table`);
            
            // Check for statistics cards
            const statCards = await page.$$('.stat-card');
            console.log(`✅ Found ${statCards.length} statistics cards`);
            
            if (profileRows.length > 0 && statCards.length > 0) {
                testResults.profileList = true;
            }
            
        } catch (error) {
            console.log('❌ Failed to find profile list elements:', error.message);
        }
        
        // Test 3: Test search and filter functionality
        console.log('\n🔍 Test 3: Testing Search and Filter Functionality...');
        try {
            // Test search box
            const searchBox = await page.$('#search');
            if (searchBox) {
                await searchBox.type('test');
                console.log('✅ Search box is functional');
            }
            
            // Test profile type filter
            const profileTypeSelect = await page.$('#profile_type');
            if (profileTypeSelect) {
                await profileTypeSelect.select('real');
                console.log('✅ Profile type filter is functional');
            }
            
            // Test filter button
            const filterButton = await page.$('button[type="submit"]');
            if (filterButton) {
                console.log('✅ Filter button found');
                testResults.searchFilter = true;
            }
            
        } catch (error) {
            console.log('❌ Failed to test search/filter functionality:', error.message);
        }
        
        // Test 4: Test profile modal opening
        console.log('\n👁️ Test 4: Testing Profile Modal Opening...');
        try {
            // Look for view profile buttons
            const viewButtons = await page.$$('.view-profile-btn');
            
            if (viewButtons.length > 0) {
                console.log(`✅ Found ${viewButtons.length} view profile buttons`);
                
                // Click the first view button
                await viewButtons[0].click();
                
                // Wait for modal to appear
                await page.waitForSelector('#user-profile-detail-modal', { 
                    visible: true, 
                    timeout: 5000 
                });
                
                console.log('✅ Profile modal opened successfully');
                testResults.modalOpen = true;
                
                // Check modal content
                const modalTitle = await page.$eval('#profile-modal-title', el => el.textContent);
                console.log('✅ Modal title:', modalTitle);
                
                // Close modal
                const closeButton = await page.$('#user-profile-detail-modal .close');
                if (closeButton) {
                    await closeButton.click();
                    console.log('✅ Modal closed successfully');
                }
                
            } else {
                console.log('❌ No view profile buttons found');
            }
            
        } catch (error) {
            console.log('❌ Failed to test profile modal:', error.message);
        }
        
        // Test 5: Test API endpoint
        console.log('\n🔌 Test 5: Testing User Profile API Endpoint...');
        try {
            // Test the API endpoint directly
            const response = await page.evaluate(async () => {
                try {
                    const res = await fetch('/admin/user-profiles/api/');
                    const data = await res.json();
                    return {
                        status: res.status,
                        hasProfiles: data.profiles && Array.isArray(data.profiles),
                        profileCount: data.profiles ? data.profiles.length : 0
                    };
                } catch (error) {
                    return { error: error.message };
                }
            });
            
            if (response.status === 200 && response.hasProfiles) {
                console.log(`✅ API endpoint working - returned ${response.profileCount} profiles`);
                testResults.apiEndpoint = true;
            } else {
                console.log('❌ API endpoint failed:', response);
            }
            
        } catch (error) {
            console.log('❌ Failed to test API endpoint:', error.message);
        }
        
        // Calculate overall success
        const successCount = Object.values(testResults).filter(result => result === true).length;
        const totalTests = Object.keys(testResults).length - 1; // Exclude overallSuccess
        testResults.overallSuccess = successCount >= totalTests * 0.8; // 80% success rate
        
        console.log('\n📊 Test Results Summary:');
        console.log('========================');
        console.log(`Page Load: ${testResults.pageLoad ? '✅' : '❌'}`);
        console.log(`Profile List: ${testResults.profileList ? '✅' : '❌'}`);
        console.log(`Search/Filter: ${testResults.searchFilter ? '✅' : '❌'}`);
        console.log(`Modal Opening: ${testResults.modalOpen ? '✅' : '❌'}`);
        console.log(`API Endpoint: ${testResults.apiEndpoint ? '✅' : '❌'}`);
        console.log(`Overall Success: ${testResults.overallSuccess ? '✅' : '❌'} (${successCount}/${totalTests})`);
        
        if (testResults.overallSuccess) {
            console.log('\n🎉 User Profile Management page is working correctly!');
        } else {
            console.log('\n⚠️ Some issues found with User Profile Management page');
        }
        
    } catch (error) {
        console.error('❌ Test execution failed:', error);
    } finally {
        if (browser) {
            await browser.close();
        }
    }
    
    return testResults;
}

// Run the test
if (require.main === module) {
    testUserProfileManagement()
        .then(results => {
            process.exit(results.overallSuccess ? 0 : 1);
        })
        .catch(error => {
            console.error('Test failed:', error);
            process.exit(1);
        });
}

module.exports = { testUserProfileManagement };
