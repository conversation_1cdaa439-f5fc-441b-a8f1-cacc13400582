#!/usr/bin/env node

/**
 * DUAL-WHEEL PERSISTENCE ISSUE TEST
 * 
 * This test validates that wheel item removal operations maintain wheel consistency
 * and don't return data from a completely different wheel.
 * 
 * CRITICAL ISSUE: After removing an item, the backend returns a completely different
 * set of wheel items from a different wheel, causing dual-wheel behavior.
 */

const WebSocket = require('ws');

const CONFIG = {
  wsUrl: 'ws://localhost:8000/ws/game/',
  apiUrl: 'http://localhost:8000',
  testUserId: '2',
  timeout: 90000
};

class DualWheelPersistenceTest {
  constructor() {
    this.ws = null;
    this.originalWheelData = null;
    this.updatedWheelData = null;
    this.messages = [];
    this.sessionCookies = null;
  }

  async authenticateWithBackend() {
    console.log('🔐 Authenticating with backend API...');
    
    const response = await fetch(`${CONFIG.apiUrl}/api/auth/login/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123'
      }),
    });

    if (!response.ok) {
      throw new Error(`Backend login failed: ${response.status}`);
    }

    const data = await response.json();
    console.log('✅ Backend authentication successful:', data.user.username);

    const cookies = response.headers.get('set-cookie');
    if (cookies) {
      this.sessionCookies = cookies;
      console.log('✅ Session cookies obtained');
    }

    return data;
  }

  async connectWebSocket() {
    console.log('🔌 Connecting to WebSocket...');
    
    return new Promise((resolve, reject) => {
      this.ws = new WebSocket(CONFIG.wsUrl);
      
      this.ws.on('open', () => {
        console.log('✅ WebSocket connected');
        resolve();
      });
      
      this.ws.on('error', (error) => {
        console.error('❌ WebSocket error:', error);
        reject(error);
      });
      
      this.ws.on('message', (data) => {
        try {
          const message = JSON.parse(data);
          this.messages.push(message);
          
          if (message.type === 'wheel_data') {
            console.log('🎡 Wheel data received via WebSocket');
            
            // Extract wheel data from message
            const wheelData = message.wheel || message.content || message;
            
            if (wheelData.items && wheelData.items.length > 0) {
              this.originalWheelData = {
                segments: wheelData.items.map(item => ({
                  id: item.id,
                  name: item.name,
                  description: item.description,
                  percentage: item.percentage,
                  color: item.color,
                  activity_tailored_id: item.activity_tailored_id,
                  domain: item.domain,
                  wheel_item_id: item.id
                })),
                wheelId: wheelData.database_id || wheelData.wheelId,
                metadata: wheelData.metadata
              };
              
              console.log(`✅ Original wheel captured: ${this.originalWheelData.segments.length} items, wheelId: ${this.originalWheelData.wheelId}`);
              console.log('📋 Original wheel items:', this.originalWheelData.segments.map(s => s.name));
            }
          }
        } catch (error) {
          console.error('❌ Error parsing WebSocket message:', error);
        }
      });
    });
  }

  async generateWheel() {
    console.log('🎡 Generating wheel...');
    
    const message = {
      type: 'chat_message',
      content: {
        message: 'I need a wheel with physical activities. I have 2 hours.',
        user_profile_id: CONFIG.testUserId,
        timestamp: new Date().toISOString(),
        metadata: {
          requested_workflow: 'wheel_generation'
        }
      }
    };
    
    this.ws.send(JSON.stringify(message));
    console.log('📤 Wheel generation request sent');
    
    // Wait for wheel data
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Timeout waiting for wheel data'));
      }, CONFIG.timeout);
      
      const checkForWheel = () => {
        if (this.originalWheelData && this.originalWheelData.segments?.length > 0) {
          clearTimeout(timeout);
          console.log(`✅ Wheel generated with ${this.originalWheelData.segments.length} items`);
          resolve(this.originalWheelData);
        } else {
          setTimeout(checkForWheel, 1000);
        }
      };
      
      checkForWheel();
    });
  }

  async removeWheelItem(itemId) {
    console.log(`🗑️ Removing wheel item: ${itemId}`);
    
    const response = await fetch(`${CONFIG.apiUrl}/api/wheel-items/${itemId}/`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': this.sessionCookies || ''
      },
      credentials: 'include'
    });
    
    if (!response.ok) {
      throw new Error(`Failed to remove wheel item: ${response.status} ${response.statusText}`);
    }
    
    const result = await response.json();
    console.log('✅ Wheel item removal API response received');
    
    // Extract updated wheel data
    if (result.wheel_data && result.wheel_data.segments) {
      this.updatedWheelData = {
        segments: result.wheel_data.segments.map(segment => ({
          id: segment.id,
          name: segment.name,
          description: segment.description,
          percentage: segment.percentage,
          color: segment.color,
          activity_tailored_id: segment.activity_tailored_id,
          domain: segment.domain,
          wheel_item_id: segment.id
        })),
        wheelId: result.wheel_data.wheelId || result.wheel_id,
        metadata: result.wheel_data.metadata
      };
      
      console.log(`📊 Updated wheel captured: ${this.updatedWheelData.segments.length} items, wheelId: ${this.updatedWheelData.wheelId}`);
      console.log('📋 Updated wheel items:', this.updatedWheelData.segments.map(s => s.name));
    }
    
    return result;
  }

  validateWheelConsistency() {
    console.log('\n🔍 VALIDATING WHEEL CONSISTENCY...');
    console.log('════════════════════════════════════════');
    
    if (!this.originalWheelData || !this.updatedWheelData) {
      throw new Error('Missing wheel data for comparison');
    }
    
    // Check 1: Wheel ID consistency
    console.log(`🆔 Original wheelId: ${this.originalWheelData.wheelId}`);
    console.log(`🆔 Updated wheelId: ${this.updatedWheelData.wheelId}`);
    
    if (this.originalWheelData.wheelId !== this.updatedWheelData.wheelId) {
      throw new Error(`DUAL-WHEEL ISSUE: Wheel ID changed from ${this.originalWheelData.wheelId} to ${this.updatedWheelData.wheelId}`);
    }
    
    // Check 2: Item count should be original count - 1
    const originalCount = this.originalWheelData.segments.length;
    const updatedCount = this.updatedWheelData.segments.length;
    
    console.log(`📊 Original item count: ${originalCount}`);
    console.log(`📊 Updated item count: ${updatedCount}`);
    
    if (updatedCount !== originalCount - 1) {
      throw new Error(`ITEM COUNT MISMATCH: Expected ${originalCount - 1} items, got ${updatedCount}`);
    }
    
    // Check 3: Remaining items should be from the original wheel
    const originalItemNames = new Set(this.originalWheelData.segments.map(s => s.name));
    const updatedItemNames = new Set(this.updatedWheelData.segments.map(s => s.name));
    
    console.log('📋 Original items:', Array.from(originalItemNames));
    console.log('📋 Updated items:', Array.from(updatedItemNames));
    
    // All updated items should be from the original set
    for (const updatedName of updatedItemNames) {
      if (!originalItemNames.has(updatedName)) {
        throw new Error(`DUAL-WHEEL ISSUE: New item "${updatedName}" appeared that wasn't in original wheel`);
      }
    }
    
    // Check 4: ID format consistency
    const originalIdFormat = this.originalWheelData.segments[0]?.id?.startsWith('wheel-item-') ? 'wheel-item-*' : 'item_*';
    const updatedIdFormat = this.updatedWheelData.segments[0]?.id?.startsWith('wheel-item-') ? 'wheel-item-*' : 'item_*';
    
    console.log(`🔗 Original ID format: ${originalIdFormat}`);
    console.log(`🔗 Updated ID format: ${updatedIdFormat}`);
    
    if (originalIdFormat !== updatedIdFormat) {
      throw new Error(`ID FORMAT INCONSISTENCY: Original format ${originalIdFormat}, updated format ${updatedIdFormat}`);
    }
    
    console.log('✅ Wheel consistency validation passed');
    return true;
  }

  async runTest() {
    try {
      console.log('🔍 DUAL-WHEEL PERSISTENCE ISSUE TEST');
      console.log('════════════════════════════════════════');
      
      await this.authenticateWithBackend();
      await this.connectWebSocket();
      
      const wheelData = await this.generateWheel();
      
      if (!wheelData.segments || wheelData.segments.length === 0) {
        throw new Error('No wheel items to remove');
      }
      
      const itemToRemove = wheelData.segments[0];
      const itemId = itemToRemove.id || itemToRemove.wheel_item_id;
      
      console.log(`🎯 Removing item: ${itemToRemove.name} (ID: ${itemId})`);
      
      await this.removeWheelItem(itemId);
      
      // Validate wheel consistency
      this.validateWheelConsistency();
      
      console.log('\n🎉 DUAL-WHEEL PERSISTENCE TEST PASSED!');
      console.log('════════════════════════════════════════');
      console.log(`✅ Wheel consistency maintained`);
      console.log(`✅ Item count reduced correctly`);
      console.log(`✅ No dual-wheel behavior detected`);
      
      return true;
      
    } catch (error) {
      console.error('\n❌ DUAL-WHEEL PERSISTENCE TEST FAILED!');
      console.error('════════════════════════════════════════');
      console.error(`Error: ${error.message}`);
      return false;
    } finally {
      if (this.ws) {
        this.ws.close();
      }
    }
  }
}

// Run the test
async function main() {
  const tester = new DualWheelPersistenceTest();
  const success = await tester.runTest();
  
  process.exit(success ? 0 : 1);
}

main().catch(console.error);
