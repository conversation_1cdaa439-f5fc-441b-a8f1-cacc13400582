#!/usr/bin/env node

/**
 * Modal Debug Test
 * 
 * Simple test to debug modal functionality
 */

const puppeteer = require('puppeteer');

async function testModalDebug() {
    console.log('🔍 Debugging Modal Functionality...\n');
    
    let browser;
    
    try {
        browser = await puppeteer.launch({
            headless: false,
            defaultViewport: { width: 1400, height: 900 },
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        
        const page = await browser.newPage();
        
        // Enable console logging
        page.on('console', msg => {
            console.log('🖥️ Browser Console:', msg.text());
        });
        
        // Navigate to the page
        await page.goto('http://localhost:8000/admin/user-profiles/', {
            waitUntil: 'networkidle0',
            timeout: 30000
        });
        
        // Check if we need to login
        const currentUrl = page.url();
        if (currentUrl.includes('/admin/login/')) {
            console.log('🔐 Logging in...');
            await page.type('#id_username', 'admin');
            await page.type('#id_password', 'admin');
            await page.click('input[type="submit"]');
            await page.waitForNavigation({ waitUntil: 'networkidle0' });
            await page.goto('http://localhost:8000/admin/user-profiles/', {
                waitUntil: 'networkidle0',
                timeout: 30000
            });
        }
        
        // Check if modal exists in DOM
        const modalExists = await page.evaluate(() => {
            const modal = document.getElementById('user-profile-detail-modal');
            return {
                exists: !!modal,
                display: modal ? window.getComputedStyle(modal).display : null,
                innerHTML: modal ? modal.innerHTML.substring(0, 200) + '...' : null
            };
        });
        
        console.log('📋 Modal DOM Check:', modalExists);
        
        // Check if view buttons exist
        const viewButtons = await page.$$('.view-profile-btn');
        console.log(`📋 Found ${viewButtons.length} view buttons`);
        
        if (viewButtons.length > 0) {
            console.log('🖱️ Clicking first view button...');
            
            // Add a click listener to see what happens
            await page.evaluate(() => {
                window.modalDebugInfo = [];
                const originalOpenModal = window.openProfileModal;
                window.openProfileModal = function(profileId, mode) {
                    window.modalDebugInfo.push(`openProfileModal called with profileId: ${profileId}, mode: ${mode}`);
                    console.log('openProfileModal called with:', profileId, mode);
                    if (originalOpenModal) {
                        return originalOpenModal(profileId, mode);
                    }
                };
            });
            
            // Click the button
            await viewButtons[0].click();
            
            // Wait a moment
            await page.waitForTimeout(2000);
            
            // Check what happened
            const debugInfo = await page.evaluate(() => {
                const modal = document.getElementById('user-profile-detail-modal');
                return {
                    modalDebugInfo: window.modalDebugInfo || [],
                    modalDisplay: modal ? window.getComputedStyle(modal).display : null,
                    modalVisible: modal ? modal.style.display : null,
                    openProfileModalExists: typeof window.openProfileModal === 'function'
                };
            });
            
            console.log('🔍 Debug Info:', debugInfo);
            
            // Try to manually show the modal
            console.log('🔧 Manually showing modal...');
            await page.evaluate(() => {
                const modal = document.getElementById('user-profile-detail-modal');
                if (modal) {
                    modal.style.display = 'block';
                    console.log('Modal display set to block');
                }
            });
            
            // Check if it's visible now
            const isVisible = await page.evaluate(() => {
                const modal = document.getElementById('user-profile-detail-modal');
                return modal ? window.getComputedStyle(modal).display !== 'none' : false;
            });
            
            console.log('👁️ Modal visible after manual show:', isVisible);
            
            if (isVisible) {
                console.log('✅ Modal can be shown manually!');
                
                // Try to call the API directly
                const apiTest = await page.evaluate(async () => {
                    try {
                        const response = await fetch('/admin/user-profiles/api/1/');
                        const data = await response.json();
                        return { success: true, hasData: !!data.id };
                    } catch (error) {
                        return { success: false, error: error.message };
                    }
                });
                
                console.log('🔌 API Test:', apiTest);
            }
        }
        
        // Keep browser open for manual inspection
        console.log('\n🔍 Browser will stay open for 30 seconds for manual inspection...');
        await page.waitForTimeout(30000);
        
    } catch (error) {
        console.error('❌ Test failed:', error);
    } finally {
        if (browser) {
            await browser.close();
        }
    }
}

// Run the test
testModalDebug().catch(console.error);
