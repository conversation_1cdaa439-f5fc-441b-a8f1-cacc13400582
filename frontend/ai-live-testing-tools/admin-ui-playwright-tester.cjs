#!/usr/bin/env node

/**
 * Admin UI Playwright Tester
 * 
 * Comprehensive testing tool for admin interface using Playwright
 * Tests modals, tabs, forms, and JavaScript functionality
 * 
 * Usage:
 *   node admin-ui-playwright-tester.cjs
 *   node admin-ui-playwright-tester.cjs --fix-mode
 *   node admin-ui-playwright-tester.cjs --debug
 */

const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

class AdminUIPlaywrightTester {
    constructor() {
        this.browser = null;
        this.page = null;
        this.context = null;
        this.results = {
            pageLoad: { status: 'pending', details: [] },
            jsExecution: { status: 'pending', details: [] },
            modalFunctionality: { status: 'pending', details: [] },
            tabNavigation: { status: 'pending', details: [] },
            formInteraction: { status: 'pending', details: [] },
            apiConnectivity: { status: 'pending', details: [] },
            overall: { score: 0, passed: 0, failed: 0 }
        };
        this.fixMode = process.argv.includes('--fix-mode');
        this.debugMode = process.argv.includes('--debug');
    }

    async initialize() {
        console.log('🚀 Initializing Admin UI Playwright Tester...');
        
        this.browser = await chromium.launch({ 
            headless: !this.debugMode,
            slowMo: this.debugMode ? 500 : 100,
            devtools: this.debugMode
        });
        
        this.context = await this.browser.newContext({
            viewport: { width: 1920, height: 1080 }
        });
        
        this.page = await this.context.newPage();
        
        // Set up comprehensive monitoring
        await this.setupMonitoring();
        
        console.log('✅ Playwright tester initialized');
    }

    async setupMonitoring() {
        // Console monitoring
        this.page.on('console', msg => {
            const type = msg.type();
            const text = msg.text();
            
            if (type === 'error') {
                console.log(`❌ Console Error: ${text}`);
                this.results.jsExecution.details.push({
                    type: 'error',
                    message: text,
                    timestamp: new Date().toISOString()
                });
            } else if (text.includes('QuickTest') || text.includes('Benchmark') || text.includes('Modal')) {
                console.log(`📝 JS Log: ${text}`);
                this.results.jsExecution.details.push({
                    type: 'info',
                    message: text,
                    timestamp: new Date().toISOString()
                });
            }
        });

        // Network monitoring
        this.page.on('response', response => {
            const url = response.url();
            const status = response.status();
            
            if (url.includes('/admin/') || url.includes('/static/')) {
                if (status >= 400) {
                    console.log(`❌ Network Error: ${url} - ${status}`);
                    this.results.apiConnectivity.details.push({
                        url,
                        status,
                        error: true,
                        timestamp: new Date().toISOString()
                    });
                } else if (this.debugMode) {
                    console.log(`✅ Network OK: ${url} - ${status}`);
                }
            }
        });

        // Page error monitoring
        this.page.on('pageerror', error => {
            console.log(`❌ Page Error: ${error.message}`);
            this.results.jsExecution.details.push({
                type: 'pageerror',
                message: error.message,
                stack: error.stack,
                timestamp: new Date().toISOString()
            });
        });
    }

    async loginAsAdmin() {
        console.log('🔐 Logging in as admin...');

        try {
            // Go to admin login page
            await this.page.goto('http://localhost:8000/admin/login/');

            // Fill login form
            await this.page.fill('#id_username', 'admin');
            await this.page.fill('#id_password', 'admin123');

            // Submit login form
            await this.page.click('input[type="submit"]');

            // Wait for redirect
            await this.page.waitForURL('**/admin/**', { timeout: 10000 });

            console.log('✅ Successfully logged in as admin');
            return true;

        } catch (error) {
            console.log(`❌ Login failed: ${error.message}`);
            return false;
        }
    }

    async testPageLoad() {
        console.log('🧪 Testing admin page load...');

        try {
            // First login as admin
            const loginSuccess = await this.loginAsAdmin();
            if (!loginSuccess) {
                throw new Error('Failed to login as admin');
            }

            const startTime = Date.now();

            await this.page.goto('http://localhost:8000/admin/benchmarks/manage/', {
                waitUntil: 'networkidle',
                timeout: 30000
            });

            const loadTime = Date.now() - startTime;
            
            // Wait for essential elements
            await this.page.waitForSelector('body', { timeout: 10000 });
            
            const title = await this.page.title();
            const url = this.page.url();
            
            console.log(`✅ Page loaded in ${loadTime}ms: ${title}`);
            
            this.results.pageLoad = {
                status: 'passed',
                details: [
                    { metric: 'Load Time', value: `${loadTime}ms` },
                    { metric: 'Title', value: title },
                    { metric: 'URL', value: url }
                ]
            };
            
            this.results.overall.passed++;
            return true;
            
        } catch (error) {
            console.log(`❌ Page load failed: ${error.message}`);
            
            this.results.pageLoad = {
                status: 'failed',
                details: [{ error: error.message }]
            };
            
            this.results.overall.failed++;
            return false;
        }
    }

    async testJavaScriptExecution() {
        console.log('🧪 Testing JavaScript execution...');
        
        try {
            // Test QuickTest class availability
            const quickTestAvailable = await this.page.evaluate(() => {
                return typeof QuickTest !== 'undefined';
            });
            
            // Test global functions
            const globalFunctions = await this.page.evaluate(() => {
                return {
                    showQuickTestModal: typeof window.showQuickTestModal === 'function',
                    hideQuickTestModal: typeof window.hideQuickTestModal === 'function',
                    quickTestInstance: typeof window.quickTest === 'object'
                };
            });
            
            // Test DOM elements
            const domElements = await this.page.evaluate(() => {
                return {
                    configureButton: !!document.getElementById('configure-quick-test-btn'),
                    modal: !!document.getElementById('quick-test-config-modal'),
                    tabButtons: document.querySelectorAll('.tab-button').length,
                    tabContents: document.querySelectorAll('.tab-content').length
                };
            });
            
            const allPassed = quickTestAvailable && 
                             Object.values(globalFunctions).every(Boolean) &&
                             domElements.configureButton && 
                             domElements.modal;
            
            this.results.jsExecution = {
                status: allPassed ? 'passed' : 'failed',
                details: [
                    { test: 'QuickTest Class', result: quickTestAvailable },
                    { test: 'Global Functions', result: globalFunctions },
                    { test: 'DOM Elements', result: domElements }
                ]
            };
            
            if (allPassed) {
                console.log('✅ JavaScript execution tests passed');
                this.results.overall.passed++;
            } else {
                console.log('❌ JavaScript execution tests failed');
                this.results.overall.failed++;
            }
            
            return allPassed;
            
        } catch (error) {
            console.log(`❌ JavaScript execution test failed: ${error.message}`);
            
            this.results.jsExecution = {
                status: 'failed',
                details: [{ error: error.message }]
            };
            
            this.results.overall.failed++;
            return false;
        }
    }

    async testModalFunctionality() {
        console.log('🧪 Testing modal functionality...');
        
        try {
            // Find and click configure button
            const configureBtn = this.page.locator('#configure-quick-test-btn');
            
            if (await configureBtn.count() === 0) {
                throw new Error('Configure button not found');
            }
            
            await configureBtn.click();
            console.log('✅ Configure button clicked');
            
            // Wait for modal to appear
            const modal = this.page.locator('#quick-test-config-modal');
            await modal.waitFor({ state: 'visible', timeout: 5000 });
            console.log('✅ Modal appeared');
            
            // Test modal content
            const modalTitle = await this.page.locator('#quick-test-config-modal h2').textContent();
            console.log(`✅ Modal title: ${modalTitle}`);
            
            // Test close functionality
            const closeBtn = this.page.locator('#quick-test-config-modal .close');
            await closeBtn.click();
            
            await modal.waitFor({ state: 'hidden', timeout: 5000 });
            console.log('✅ Modal closed successfully');
            
            this.results.modalFunctionality = {
                status: 'passed',
                details: [
                    { test: 'Button Click', result: true },
                    { test: 'Modal Display', result: true },
                    { test: 'Modal Close', result: true },
                    { test: 'Modal Title', result: modalTitle }
                ]
            };
            
            this.results.overall.passed++;
            return true;
            
        } catch (error) {
            console.log(`❌ Modal functionality test failed: ${error.message}`);
            
            this.results.modalFunctionality = {
                status: 'failed',
                details: [{ error: error.message }]
            };
            
            this.results.overall.failed++;
            return false;
        }
    }

    async testTabNavigation() {
        console.log('🧪 Testing tab navigation...');
        
        try {
            // Open modal first
            await this.page.locator('#configure-quick-test-btn').click();
            await this.page.locator('#quick-test-config-modal').waitFor({ state: 'visible' });
            
            const tabs = ['basic', 'advanced', 'preview'];
            const tabResults = [];
            
            for (const tabName of tabs) {
                console.log(`🔄 Testing ${tabName} tab...`);
                
                // Click tab button
                const tabButton = this.page.locator(`[data-tab="${tabName}"]`);
                await tabButton.click();
                
                // Wait a moment for transition
                await this.page.waitForTimeout(500);
                
                // Check if tab content is visible
                const tabContent = this.page.locator(`#${tabName}-tab`);
                const isVisible = await tabContent.isVisible();
                
                tabResults.push({
                    tab: tabName,
                    visible: isVisible,
                    result: isVisible ? 'passed' : 'failed'
                });
                
                if (isVisible) {
                    console.log(`✅ ${tabName} tab working`);
                } else {
                    console.log(`❌ ${tabName} tab not working`);
                }
            }
            
            // Close modal
            await this.page.locator('#quick-test-config-modal .close').click();
            
            const allTabsPassed = tabResults.every(tab => tab.visible);
            
            this.results.tabNavigation = {
                status: allTabsPassed ? 'passed' : 'failed',
                details: tabResults
            };
            
            if (allTabsPassed) {
                this.results.overall.passed++;
            } else {
                this.results.overall.failed++;
            }
            
            return allTabsPassed;
            
        } catch (error) {
            console.log(`❌ Tab navigation test failed: ${error.message}`);
            
            this.results.tabNavigation = {
                status: 'failed',
                details: [{ error: error.message }]
            };
            
            this.results.overall.failed++;
            return false;
        }
    }

    async applyFixes() {
        if (!this.fixMode) return;
        
        console.log('🔧 Applying UI fixes...');
        
        // Inject comprehensive CSS fixes
        await this.page.addStyleTag({
            content: `
                /* Ensure modals are properly displayed */
                .modal, #quick-test-config-modal {
                    display: block !important;
                    visibility: visible !important;
                    opacity: 1 !important;
                    pointer-events: auto !important;
                    z-index: 1000 !important;
                }
                
                /* Fix tab navigation */
                .tab-button {
                    pointer-events: auto !important;
                    cursor: pointer !important;
                    opacity: 1 !important;
                }
                
                .tab-content {
                    display: none !important;
                }
                
                .tab-content.active {
                    display: block !important;
                    visibility: visible !important;
                    opacity: 1 !important;
                }
                
                /* Ensure buttons are clickable */
                button, .btn {
                    pointer-events: auto !important;
                    cursor: pointer !important;
                }
            `
        });
        
        // Inject JavaScript fixes
        await this.page.evaluate(() => {
            // Force initialize QuickTest if not available
            if (typeof QuickTest !== 'undefined' && !window.quickTest) {
                try {
                    window.quickTest = new QuickTest();
                    console.log('✅ QuickTest instance created');
                } catch (e) {
                    console.log('❌ Failed to create QuickTest instance:', e);
                }
            }
            
            // Ensure global functions are available
            if (!window.showQuickTestModal) {
                window.showQuickTestModal = function() {
                    const modal = document.getElementById('quick-test-config-modal');
                    if (modal) {
                        modal.style.display = 'block';
                    }
                };
            }
            
            if (!window.hideQuickTestModal) {
                window.hideQuickTestModal = function() {
                    const modal = document.getElementById('quick-test-config-modal');
                    if (modal) {
                        modal.style.display = 'none';
                    }
                };
            }
            
            console.log('✅ UI fixes applied');
        });
        
        console.log('✅ Fixes applied successfully');
    }

    async generateReport() {
        const totalTests = this.results.overall.passed + this.results.overall.failed;
        const score = totalTests > 0 ? Math.round((this.results.overall.passed / totalTests) * 100) : 0;
        
        this.results.overall.score = score;
        
        console.log('\n' + '='.repeat(80));
        console.log('📊 ADMIN UI PLAYWRIGHT TEST REPORT');
        console.log('='.repeat(80));
        
        console.log(`\n🎯 Overall Score: ${score}/100`);
        console.log(`✅ Passed: ${this.results.overall.passed}`);
        console.log(`❌ Failed: ${this.results.overall.failed}`);
        
        // Display detailed results
        Object.entries(this.results).forEach(([category, result]) => {
            if (category === 'overall') return;
            
            const icon = result.status === 'passed' ? '✅' : '❌';
            console.log(`\n${icon} ${category.toUpperCase()}:`);
            
            if (result.details) {
                result.details.forEach(detail => {
                    if (detail.error) {
                        console.log(`   ❌ Error: ${detail.error}`);
                    } else if (detail.test) {
                        const testIcon = detail.result ? '✅' : '❌';
                        console.log(`   ${testIcon} ${detail.test}: ${JSON.stringify(detail.result)}`);
                    } else {
                        console.log(`   📊 ${JSON.stringify(detail)}`);
                    }
                });
            }
        });
        
        // Save report
        const reportDir = path.join(__dirname, 'test-results');
        if (!fs.existsSync(reportDir)) {
            fs.mkdirSync(reportDir, { recursive: true });
        }
        
        const reportPath = path.join(reportDir, `admin-ui-test-${Date.now()}.json`);
        fs.writeFileSync(reportPath, JSON.stringify(this.results, null, 2));
        
        console.log(`\n📁 Report saved to: ${reportPath}`);
        console.log('='.repeat(80));
        
        return this.results;
    }

    async runAllTests() {
        try {
            await this.initialize();
            
            // Apply fixes if in fix mode
            if (this.fixMode) {
                await this.applyFixes();
            }
            
            // Run all tests
            await this.testPageLoad();
            await this.testJavaScriptExecution();
            await this.testModalFunctionality();
            await this.testTabNavigation();
            
            // Generate report
            const results = await this.generateReport();
            
            return results;
            
        } catch (error) {
            console.log(`❌ Critical error during testing: ${error.message}`);
            return null;
        } finally {
            if (!this.debugMode && this.browser) {
                await this.browser.close();
            } else if (this.debugMode) {
                console.log('\n🔍 Debug mode: Browser kept open for inspection');
                console.log('Press Ctrl+C to close when done.');
            }
        }
    }
}

// Main execution
async function main() {
    console.log('🎯 Admin UI Playwright Tester Starting...');
    
    const tester = new AdminUIPlaywrightTester();
    const results = await tester.runAllTests();
    
    if (results && results.overall.score >= 70) {
        console.log('\n🎉 Admin UI tests passed!');
        process.exit(0);
    } else {
        console.log('\n💥 Admin UI tests failed!');
        process.exit(1);
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = AdminUIPlaywrightTester;
