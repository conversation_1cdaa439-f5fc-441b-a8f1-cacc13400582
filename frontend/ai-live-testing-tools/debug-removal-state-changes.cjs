/**
 * Debug Wheel Item Removal State Changes
 * Monitor exactly what happens to wheelD<PERSON> during the removal process
 */

const puppeteer = require('puppeteer');

async function debugRemovalStateChanges() {
  console.log('🔍 Debugging wheel item removal state changes...');
  
  const browser = await puppeteer.launch({ 
    headless: false,
    defaultViewport: { width: 1200, height: 800 }
  });
  
  try {
    const page = await browser.newPage();
    
    // Enable console logging
    page.on('console', msg => {
      console.log('🔍 BROWSER:', msg.text());
    });
    
    // Navigate to the app
    console.log('📱 Navigating to http://localhost:3001...');
    await page.goto('http://localhost:3001', { waitUntil: 'networkidle0' });
    
    // Wait for app to load
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('\n🎯 Step 1: Inject mock wheel data and set up monitoring...');
    
    // Inject mock wheel data and set up monitoring
    await page.evaluate(() => {
      const appShell = document.querySelector('app-shell');
      if (appShell) {
        // Create mock wheel data
        const mockWheelData = {
          segments: [
            {
              id: 'item_mock_1',
              text: 'Mock Activity 1',
              name: 'Mock Activity 1',
              description: 'First mock activity for testing',
              percentage: 33.33,
              color: '#FF6B6B',
              activity_tailored_id: 'activity_1',
              domain: 'physical',
              base_challenge_rating: 50,
              wheel_item_id: 'item_mock_1'
            },
            {
              id: 'item_mock_2',
              text: 'Mock Activity 2',
              name: 'Mock Activity 2',
              description: 'Second mock activity for testing',
              percentage: 33.33,
              color: '#4ECDC4',
              activity_tailored_id: 'activity_2',
              domain: 'mental',
              base_challenge_rating: 40,
              wheel_item_id: 'item_mock_2'
            },
            {
              id: 'item_mock_3',
              text: 'Mock Activity 3',
              name: 'Mock Activity 3',
              description: 'Third mock activity for testing',
              percentage: 33.34,
              color: '#45B7D1',
              activity_tailored_id: 'activity_3',
              domain: 'creative',
              base_challenge_rating: 60,
              wheel_item_id: 'item_mock_3'
            }
          ],
          wheelId: 'mock-wheel-123',
          createdAt: new Date().toISOString()
        };
        
        console.log('🎯 Setting up wheelData monitoring...');
        
        // Store original wheelData property
        let _wheelData = null;
        
        // Create a property descriptor that logs all changes
        Object.defineProperty(appShell, 'wheelData', {
          get() {
            return _wheelData;
          },
          set(value) {
            console.log('🔄 WHEEL DATA CHANGE DETECTED!');
            console.log('🔄 Previous value:', _wheelData ? `${_wheelData.segments?.length || 0} segments` : 'null/undefined');
            console.log('🔄 New value:', value ? `${value.segments?.length || 0} segments` : 'null/undefined');
            console.log('🔄 New value type:', typeof value);
            console.log('🔄 New value truthy:', !!value);
            
            if (value && value.segments) {
              console.log('🔄 New segments preview:', value.segments.slice(0, 2).map(s => ({ id: s.id, text: s.text || s.name })));
            }
            
            // Log stack trace to see what's calling this
            console.log('🔄 Stack trace:', new Error().stack);
            
            _wheelData = value;
            
            // Trigger re-render
            appShell.requestUpdate();
          },
          enumerable: true,
          configurable: true
        });
        
        // Set initial data
        console.log('🎯 Setting initial mock wheel data...');
        appShell.wheelData = mockWheelData;
        
        return { success: true };
      }
      return { success: false };
    });
    
    // Wait for re-render
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    console.log('\n🎯 Step 2: Check initial state...');
    
    const initialState = await page.evaluate(() => {
      const appShell = document.querySelector('app-shell');
      return {
        hasWheelData: !!(appShell && appShell.wheelData && appShell.wheelData.segments),
        segmentCount: appShell && appShell.wheelData ? appShell.wheelData.segments.length : 0
      };
    });
    
    console.log('Initial state:', initialState);
    
    console.log('\n🎯 Step 3: Mock the removeWheelItem API call...');
    
    // Mock the fetch API to simulate different scenarios
    await page.evaluateOnNewDocument(() => {
      const originalFetch = window.fetch;
      window.fetch = function(url, options) {
        console.log('🌐 FETCH INTERCEPTED:', url, options?.method);
        
        // If it's a DELETE request to wheel-items, simulate different responses
        if (url.includes('/api/wheel-items/') && options?.method === 'DELETE') {
          console.log('🗑️ SIMULATING WHEEL ITEM REMOVAL API CALL');
          
          // Simulate successful removal with updated wheel data
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({
              success: true,
              wheel_data: {
                segments: [
                  {
                    id: 'item_mock_2',
                    name: 'Mock Activity 2',
                    description: 'Second mock activity for testing',
                    percentage: 50,
                    color: '#4ECDC4',
                    activity_tailored_id: 'activity_2',
                    domain: 'mental',
                    base_challenge_rating: 40
                  },
                  {
                    id: 'item_mock_3',
                    name: 'Mock Activity 3',
                    description: 'Third mock activity for testing',
                    percentage: 50,
                    color: '#45B7D1',
                    activity_tailored_id: 'activity_3',
                    domain: 'creative',
                    base_challenge_rating: 60
                  }
                ],
                wheel_id: 'mock-wheel-123'
              }
            })
          });
        }
        
        // For all other requests, use original fetch
        return originalFetch.apply(this, arguments);
      };
    });
    
    console.log('\n🎯 Step 4: Trigger wheel item removal...');
    
    // Find and click a remove button
    const removeClicked = await page.evaluate(() => {
      const appShell = document.querySelector('app-shell');
      if (appShell && appShell.shadowRoot) {
        // Look for remove button
        const removeButton = appShell.shadowRoot.querySelector('.remove-activity-btn');
        if (removeButton) {
          console.log('🗑️ Found remove button, clicking...');
          removeButton.click();
          return true;
        } else {
          console.log('🗑️ No remove button found, trying to call removeWheelItem directly...');
          // Call removeWheelItem directly
          if (appShell.removeWheelItem) {
            appShell.removeWheelItem('item_mock_1');
            return true;
          }
        }
      }
      return false;
    });
    
    if (!removeClicked) {
      console.log('❌ Could not trigger removal');
      return;
    }
    
    console.log('🔄 Removal triggered, waiting for state changes...');
    
    // Wait for the removal to complete
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    console.log('\n🎯 Step 5: Check final state...');
    
    const finalState = await page.evaluate(() => {
      const appShell = document.querySelector('app-shell');
      if (appShell && appShell.shadowRoot) {
        const wheel = appShell.shadowRoot.querySelector('game-wheel');
        return {
          hasWheelData: !!(appShell.wheelData && appShell.wheelData.segments),
          segmentCount: appShell.wheelData ? appShell.wheelData.segments.length : 0,
          wheelDataType: typeof appShell.wheelData,
          wheelDataTruthy: !!appShell.wheelData,
          isUnpopulated: wheel ? wheel.classList.contains('wheel-unpopulated') : false,
          wheelDataPreview: appShell.wheelData && appShell.wheelData.segments ? 
            appShell.wheelData.segments.map(s => ({ id: s.id, text: s.text || s.name })) : null
        };
      }
      return { error: 'No app-shell or shadow root' };
    });
    
    console.log('Final state:', JSON.stringify(finalState, null, 2));
    
    console.log('\n🎯 Analysis:');
    console.log(`Initial segments: ${initialState.segmentCount}`);
    console.log(`Final segments: ${finalState.segmentCount}`);
    console.log(`Wheel became unpopulated: ${finalState.isUnpopulated}`);
    
    if (finalState.isUnpopulated) {
      console.log('🚨 ISSUE CONFIRMED: Wheel became unpopulated after removal!');
      console.log('This means wheelData became falsy during the removal process.');
    }
    
    // Wait for user to see the browser
    console.log('\n⏳ Browser will stay open for 15 seconds for manual inspection...');
    await new Promise(resolve => setTimeout(resolve, 15000));
    
  } catch (error) {
    console.error('❌ Debug failed:', error);
  } finally {
    await browser.close();
  }
}

// Run the debug
debugRemovalStateChanges().catch(console.error);
