/**
 * Comprehensive wheel component testing tool
 * Tests segment visibility, ball physics, and winner detection
 */

const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

async function testWheelComprehensive(port = 3002) {
    console.log('🎡 Starting comprehensive wheel component test...');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 500
    });
    
    const page = await browser.newPage();
    
    // Set up console logging
    page.on('console', msg => {
        const text = msg.text();
        if (text.includes('[WHEEL]') || text.includes('[RENDERER]') || text.includes('segment') || text.includes('ball')) {
            console.log(`[BROWSER] ${text}`);
        }
    });
    
    // Set up error logging
    page.on('pageerror', error => {
        console.error(`[PAGE ERROR] ${error.message}`);
    });
    
    try {
        console.log(`📱 Navigating to wheel debug page on port ${port}...`);
        await page.goto(`http://localhost:${port}/debug/wheel-debug.html`);
        
        // Wait for page to load
        await page.waitForTimeout(3000);
        
        // Create screenshots directory
        const screenshotDir = path.join(__dirname, 'wheel-screenshots');
        if (!fs.existsSync(screenshotDir)) {
            fs.mkdirSync(screenshotDir, { recursive: true });
        }
        
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        
        // Test 1: Component Loading
        console.log('\n🔍 TEST 1: Component Loading');
        await page.waitForSelector('game-wheel', { timeout: 10000 });
        console.log('✅ Wheel component found in DOM');
        
        await page.waitForSelector('wheel-viewport', { timeout: 5000 });
        console.log('✅ Viewport component found in DOM');
        
        // Take initial screenshot
        await page.screenshot({ 
            path: path.join(screenshotDir, `${timestamp}_01_initial.png`),
            fullPage: true 
        });
        console.log('📸 Initial screenshot taken');
        
        // Test 2: Load Mock Data
        console.log('\n🔍 TEST 2: Loading Mock Data');
        await page.click('#loadWheelBtn');
        await page.waitForTimeout(2000);
        
        // Check if segments are visible
        const segmentVisibility = await page.evaluate(() => {
            const wheel = document.querySelector('game-wheel');
            if (!wheel) return { error: 'No wheel component found' };
            
            const canvas = wheel.shadowRoot?.querySelector('canvas') || wheel.querySelector('canvas');
            if (!canvas) return { error: 'No canvas found' };
            
            // Get canvas context and check for drawn content
            const ctx = canvas.getContext('2d');
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            const data = imageData.data;
            
            // Count non-transparent pixels
            let nonTransparentPixels = 0;
            for (let i = 3; i < data.length; i += 4) {
                if (data[i] > 0) nonTransparentPixels++;
            }
            
            return {
                canvasSize: { width: canvas.width, height: canvas.height },
                nonTransparentPixels,
                hasContent: nonTransparentPixels > 1000
            };
        });
        
        console.log('🎨 Segment visibility check:', segmentVisibility);
        
        // Take screenshot after loading data
        await page.screenshot({ 
            path: path.join(screenshotDir, `${timestamp}_02_data_loaded.png`),
            fullPage: true 
        });
        console.log('📸 Data loaded screenshot taken');
        
        // Test 3: Check Wheel State
        console.log('\n🔍 TEST 3: Wheel State Analysis');
        const wheelState = await page.evaluate(() => {
            const wheel = document.querySelector('game-wheel');
            if (!wheel || !wheel.getWheelState) return { error: 'Wheel state not available' };
            
            return wheel.getWheelState();
        });
        
        console.log('🎯 Wheel state:', JSON.stringify(wheelState, null, 2));
        
        // Test 4: Ball Physics Test
        console.log('\n🔍 TEST 4: Ball Physics Test');
        const spinButtonEnabled = await page.isEnabled('#spinWheelBtn');
        console.log(`🎯 Spin button enabled: ${spinButtonEnabled}`);
        
        if (spinButtonEnabled) {
            console.log('🎡 Starting spin test...');
            await page.click('#spinWheelBtn');
            
            // Monitor ball movement
            let ballMovementDetected = false;
            let ballPositions = [];
            
            for (let i = 0; i < 20; i++) {
                await page.waitForTimeout(500);
                
                const ballInfo = await page.evaluate(() => {
                    const wheel = document.querySelector('game-wheel');
                    if (!wheel || !wheel.physicsEngine) return null;
                    
                    try {
                        const ballBody = wheel.physicsEngine.getBallBody();
                        if (!ballBody) return null;
                        
                        return {
                            position: { x: ballBody.position.x, y: ballBody.position.y },
                            velocity: wheel.physicsEngine.getBallVelocity()
                        };
                    } catch (error) {
                        return { error: error.message };
                    }
                });
                
                if (ballInfo && ballInfo.position) {
                    ballPositions.push(ballInfo.position);
                    
                    // Check for movement
                    if (ballPositions.length > 1) {
                        const lastPos = ballPositions[ballPositions.length - 1];
                        const prevPos = ballPositions[ballPositions.length - 2];
                        const movement = Math.sqrt(
                            Math.pow(lastPos.x - prevPos.x, 2) + 
                            Math.pow(lastPos.y - prevPos.y, 2)
                        );
                        
                        if (movement > 1) {
                            ballMovementDetected = true;
                            console.log(`🎱 Ball movement detected: ${movement.toFixed(2)} pixels`);
                        }
                    }
                    
                    console.log(`🎱 Ball position ${i}: (${ballInfo.position.x.toFixed(1)}, ${ballInfo.position.y.toFixed(1)}) vel: ${ballInfo.velocity?.magnitude?.toFixed(3) || 'N/A'}`);
                }
                
                // Take screenshot during spin
                if (i === 5) {
                    await page.screenshot({ 
                        path: path.join(screenshotDir, `${timestamp}_03_spinning.png`),
                        fullPage: true 
                    });
                    console.log('📸 Spinning screenshot taken');
                }
            }
            
            console.log(`🎱 Ball movement detected: ${ballMovementDetected}`);
            console.log(`🎱 Total ball positions recorded: ${ballPositions.length}`);
            
            // Wait for spin to complete
            console.log('⏳ Waiting for spin to complete...');
            await page.waitForTimeout(10000);
            
            // Take final screenshot
            await page.screenshot({ 
                path: path.join(screenshotDir, `${timestamp}_04_final.png`),
                fullPage: true 
            });
            console.log('📸 Final screenshot taken');
        }
        
        // Test 5: Winner Detection Test
        console.log('\n🔍 TEST 5: Winner Detection Test');
        const winnerInfo = await page.evaluate(() => {
            const winnerDisplay = document.querySelector('#winnerDisplay');
            const winnerText = document.querySelector('#winnerText');
            
            return {
                winnerDisplayVisible: winnerDisplay ? winnerDisplay.classList.contains('visible') : false,
                winnerText: winnerText ? winnerText.textContent : 'None',
                winnerDisplayExists: !!winnerDisplay
            };
        });
        
        console.log('🏆 Winner detection result:', winnerInfo);
        
        // Test 6: Debug Information
        console.log('\n🔍 TEST 6: Debug Information');
        await page.click('#debugWinnerBtn');
        await page.waitForTimeout(1000);
        
        const debugInfo = await page.textContent('#debugInfo');
        console.log('🔍 Debug info:', debugInfo);
        
        // Test 7: Segment Information
        console.log('\n🔍 TEST 7: Segment Information');
        await page.click('#showSegmentsBtn');
        await page.waitForTimeout(1000);
        
        // Final comprehensive report
        console.log('\n📊 COMPREHENSIVE TEST RESULTS:');
        console.log('================================');
        console.log(`✅ Component Loading: PASSED`);
        console.log(`${segmentVisibility.hasContent ? '✅' : '❌'} Segment Visibility: ${segmentVisibility.hasContent ? 'PASSED' : 'FAILED'}`);
        console.log(`${ballMovementDetected ? '✅' : '❌'} Ball Physics: ${ballMovementDetected ? 'PASSED' : 'FAILED'}`);
        console.log(`${winnerInfo.winnerDisplayExists ? '✅' : '❌'} Winner Detection UI: ${winnerInfo.winnerDisplayExists ? 'PASSED' : 'FAILED'}`);
        console.log(`${spinButtonEnabled ? '✅' : '❌'} Spin Functionality: ${spinButtonEnabled ? 'PASSED' : 'FAILED'}`);
        
        const overallSuccess = segmentVisibility.hasContent && ballMovementDetected && winnerInfo.winnerDisplayExists && spinButtonEnabled;
        console.log(`\n🎯 OVERALL RESULT: ${overallSuccess ? '✅ PASSED' : '❌ FAILED'}`);
        
        if (!overallSuccess) {
            console.log('\n🔧 ISSUES DETECTED:');
            if (!segmentVisibility.hasContent) console.log('- Segments not visible (rendering issue)');
            if (!ballMovementDetected) console.log('- Ball physics not working');
            if (!winnerInfo.winnerDisplayExists) console.log('- Winner detection UI missing');
            if (!spinButtonEnabled) console.log('- Spin button not enabled');
        }
        
    } catch (error) {
        console.error('❌ Test failed:', error);
    } finally {
        console.log('\n⏳ Keeping browser open for 30 seconds for manual inspection...');
        await page.waitForTimeout(30000);
        await browser.close();
    }
}

// Get port from command line argument
const port = process.argv[2] || 3002;
testWheelComprehensive(port);
