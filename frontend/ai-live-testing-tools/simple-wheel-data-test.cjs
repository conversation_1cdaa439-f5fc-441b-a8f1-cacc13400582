#!/usr/bin/env node

/**
 * Simple test to check wheel data structure
 */

const WebSocket = require('ws');

class SimpleWheelDataTest {
  constructor() {
    this.ws = null;
    this.receivedMessages = [];
  }

  async run() {
    console.log('🔍 Starting simple wheel data test...');
    
    try {
      // Connect to WebSocket
      this.ws = new WebSocket('ws://localhost:8000/ws/game/');
      
      this.ws.on('open', () => {
        console.log('✅ WebSocket connected');
        
        // Send wheel generation request
        const message = {
          type: 'user_message',
          content: "hey, I'm feeling energetic and I have 2h free ahead of me. It's very hot outside though. Generate me the perfect wheel !",
          debug_selections: {
            userId: 2,
            llmConfigId: ''
          }
        };
        
        console.log('💬 Sending wheel request...');
        this.ws.send(JSON.stringify(message));
      });
      
      this.ws.on('message', (data) => {
        try {
          const message = JSON.parse(data.toString());
          this.receivedMessages.push(message);
          
          console.log(`📨 Received: ${message.type}`);
          
          if (message.type === 'wheel_data') {
            console.log('\n🎡 WHEEL DATA RECEIVED!');
            console.log('📊 Full message structure:');
            console.log(JSON.stringify(message, null, 2));
            
            if (message.data && message.data.wheel) {
              const wheel = message.data.wheel;
              console.log('\n🔍 Wheel structure analysis:');
              console.log(`  - Has metadata: ${!!wheel.metadata}`);
              console.log(`  - Has items: ${!!wheel.items}`);
              console.log(`  - Items count: ${wheel.items ? wheel.items.length : 0}`);
              console.log(`  - Has activities: ${!!wheel.activities}`);
              console.log(`  - Activities count: ${wheel.activities ? wheel.activities.length : 0}`);
              
              if (wheel.items && wheel.items.length > 0) {
                console.log('\n📋 First item structure:');
                console.log(JSON.stringify(wheel.items[0], null, 2));
              }
              
              if (wheel.activities && wheel.activities.length > 0) {
                console.log('\n🎯 First activity structure:');
                console.log(JSON.stringify(wheel.activities[0], null, 2));
              }
            } else if (message.wheel) {
              console.log('\n⚠️  Wheel data is directly in message.wheel (not message.data.wheel)');
              const wheel = message.wheel;
              console.log(`  - Items count: ${wheel.items ? wheel.items.length : 0}`);
              console.log(`  - Activities count: ${wheel.activities ? wheel.activities.length : 0}`);
            } else {
              console.log('\n❌ No wheel data found in message');
              console.log('Available keys:', Object.keys(message));
            }
            
            // Close after receiving wheel data
            setTimeout(() => {
              this.ws.close();
              process.exit(0);
            }, 1000);
          }
          
        } catch (error) {
          console.error('❌ Error parsing message:', error);
        }
      });
      
      this.ws.on('error', (error) => {
        console.error('❌ WebSocket error:', error);
      });
      
      this.ws.on('close', () => {
        console.log('🔌 WebSocket closed');
      });
      
      // Timeout after 2 minutes
      setTimeout(() => {
        console.log('⏰ Test timeout - no wheel data received');
        if (this.ws) {
          this.ws.close();
        }
        process.exit(1);
      }, 120000);
      
    } catch (error) {
      console.error('❌ Test failed:', error);
      process.exit(1);
    }
  }
}

// Run the test
const test = new SimpleWheelDataTest();
test.run();
