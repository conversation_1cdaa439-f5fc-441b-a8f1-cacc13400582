const { chromium } = require('playwright');

async function testSpinResult() {
    console.log('🎯 Testing Spin Result Functionality');
    console.log('═══════════════════════════════════════');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 300
    });
    
    const context = await browser.newContext();
    const page = await context.newPage();
    
    // Capture console logs from the page
    page.on('console', msg => {
        const text = msg.text();
        if (text.includes('[WHEEL]') || text.includes('[APP-SHELL]') || text.includes('🎡') || text.includes('🎯')) {
            console.log(`🖥️  BROWSER: ${text}`);
        }
    });
    
    // Capture WebSocket messages
    let spinResultSent = false;
    
    page.on('websocket', ws => {
        ws.on('framesent', event => {
            try {
                const data = JSON.parse(event.payload);
                if (data.type === 'spin_result') {
                    spinResultSent = true;
                    console.log('✅ SPIN RESULT SENT TO BACKEND!');
                    console.log('📤 Spin result data:', JSON.stringify(data, null, 2));
                }
            } catch (e) {
                // Ignore non-JSON messages
            }
        });
        ws.on('framereceived', event => {
            try {
                const data = JSON.parse(event.payload);
                if (data.type === 'wheel_data') {
                    console.log('🎡 Wheel data received, waiting for user to spin...');
                }
            } catch (e) {
                // Ignore non-JSON messages
            }
        });
    });
    
    try {
        // Load the frontend
        console.log('🌐 Loading frontend...');
        await page.goto('http://localhost:3000/', { waitUntil: 'networkidle' });
        console.log('✅ Frontend loaded');
        
        // Wait for connection
        await page.waitForTimeout(3000);
        
        // Send wheel generation request
        const chatInput = await page.locator('textarea').first();
        await chatInput.waitFor({ state: 'visible', timeout: 10000 });
        await chatInput.click();
        
        const message = "I want to do something fun. Make me a wheel with 4 activities";
        console.log(`📝 Sending message: "${message}"`);
        await chatInput.fill(message);
        await page.keyboard.press('Enter');
        
        // Wait for wheel data (shorter timeout for simpler request)
        console.log('⏳ Waiting for wheel data...');
        let attempts = 0;
        let wheelVisible = false;
        
        while (!wheelVisible && attempts < 60) {
            const spinButton = await page.locator('game-wheel button.spin-button').first();
            const spinButtonExists = await spinButton.count() > 0;
            const isEnabled = spinButtonExists ? await spinButton.isEnabled() : false;
            
            if (spinButtonExists && isEnabled) {
                wheelVisible = true;
                console.log('✅ Wheel is ready for spinning!');
                break;
            }
            
            await page.waitForTimeout(1000);
            attempts++;
            
            if (attempts % 10 === 0) {
                console.log(`   Still waiting for wheel... ${attempts}/60 seconds`);
            }
        }
        
        if (!wheelVisible) {
            console.log('❌ Wheel not ready within 60 seconds');
            return;
        }
        
        // Now test the spin
        console.log('🎲 Clicking spin button...');
        const spinButton = await page.locator('game-wheel button.spin-button').first();
        await spinButton.click();
        
        // Wait for spin to complete and check for events
        console.log('⏳ Waiting for spin to complete and events to be dispatched...');
        let spinCompleted = false;
        let spinAttempts = 0;
        
        while (!spinCompleted && spinAttempts < 20) {
            await page.waitForTimeout(1000);
            spinAttempts++;
            
            // Check if wheel is still spinning
            const isSpinning = await page.evaluate(() => {
                const gameWheel = document.querySelector('game-wheel');
                return gameWheel ? gameWheel.isSpinning : false;
            });
            
            if (!isSpinning && spinAttempts > 3) {
                console.log('✅ Wheel has stopped spinning');
                spinCompleted = true;
                break;
            }
            
            if (spinAttempts % 3 === 0) {
                console.log(`   Still spinning... ${spinAttempts}/20 seconds`);
            }
        }
        
        // Wait a bit more for events to be processed
        console.log('⏳ Waiting for event processing...');
        await page.waitForTimeout(3000);
        
        // Check results
        console.log('\n📊 SPIN RESULT TEST RESULTS');
        console.log('═══════════════════════════════════════');
        console.log(`✅ Frontend loaded: YES`);
        console.log(`✅ Wheel generated: YES`);
        console.log(`✅ Spin button clicked: YES`);
        console.log(`✅ Spin completed: ${spinCompleted ? 'YES' : 'NO'}`);
        console.log(`${spinResultSent ? '✅' : '❌'} Spin result sent to backend: ${spinResultSent ? 'YES' : 'NO'}`);
        
        if (!spinResultSent) {
            console.log('\n🔍 DEBUGGING INFO:');
            console.log('Check the browser console logs above for any [WHEEL] or [APP-SHELL] messages');
            console.log('The wheel component should dispatch wheel-spin-complete event');
            console.log('The app-shell should handle this event and send spin_result to backend');
        }
        
        console.log('\n🔍 Browser kept open for manual inspection...');
        console.log('You can manually spin the wheel and check browser console for debugging');
        console.log('Press Ctrl+C to close when done.');
        
        // Keep browser open for inspection
        await new Promise(() => {});
        
    } catch (error) {
        console.error('❌ Test failed:', error);
    }
}

testSpinResult().catch(console.error);
