#!/usr/bin/env node

/**
 * Test Wheel Processing Optimization
 * 
 * This test verifies that the wheel processing optimization prevents
 * unnecessary processWheelData() calls when wheel data hasn't changed.
 */

const puppeteer = require('puppeteer');

const CONFIG = {
  testUrl: 'http://localhost:3000',
  testUserId: 'test-user-123',
  testMessage: 'I want to improve my physical fitness and mental wellbeing',
  timeout: 30000
};

class WheelProcessingOptimizationTest {
  constructor() {
    this.browser = null;
    this.page = null;
    this.testResults = {
      initialProcessingCount: 0,
      sliderChangeProcessingCount: 0,
      optimizationWorking: false
    };
  }

  async run() {
    console.log('🧪 Testing Wheel Processing Optimization');
    console.log('========================================');

    try {
      await this.setupBrowser();
      await this.navigateToApp();
      await this.waitForAppLoad();
      await this.generateWheel();
      await this.testSliderChanges();
      await this.analyzeResults();
      
      console.log('\n🏁 TEST COMPLETED');
      console.log('================');
      
      if (this.testResults.optimizationWorking) {
        console.log('🎉 OPTIMIZATION WORKING - No unnecessary wheel processing!');
      } else {
        console.log('❌ OPTIMIZATION FAILED - Unnecessary wheel processing detected');
      }
      
    } catch (error) {
      console.error('❌ Test failed:', error.message);
    } finally {
      await this.cleanup();
    }
  }

  async setupBrowser() {
    console.log('🚀 Setting up browser...');
    this.browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    this.page = await this.browser.newPage();
    
    // Monitor console logs for wheel processing
    this.page.on('console', (msg) => {
      const text = msg.text();
      if (text.includes('[WHEEL] 🔄 Wheel data changed, processing...')) {
        this.testResults.initialProcessingCount++;
        console.log('📊 Wheel processing detected:', text);
      }
      if (text.includes('[WHEEL] ⏭️ Wheel data unchanged, skipping processing')) {
        console.log('✅ Optimization working:', text);
      }
    });
  }

  async navigateToApp() {
    console.log('🌐 Navigating to app...');
    await this.page.goto(CONFIG.testUrl, { waitUntil: 'networkidle0' });
  }

  async waitForAppLoad() {
    console.log('⏳ Waiting for app to load...');
    await this.page.waitForSelector('app-shell', { timeout: CONFIG.timeout });
    await this.page.waitForTimeout(2000); // Allow for initialization
  }

  async generateWheel() {
    console.log('🎡 Generating wheel...');
    
    // Reset processing count
    this.testResults.initialProcessingCount = 0;
    
    // Find and click the chat input
    await this.page.waitForSelector('chat-interface');
    const chatInput = await this.page.$('chat-interface input[type="text"]');
    
    if (chatInput) {
      await chatInput.type(CONFIG.testMessage);
      await this.page.keyboard.press('Enter');
      
      // Wait for wheel generation
      await this.page.waitForTimeout(15000);
      
      console.log(`📊 Initial wheel processing count: ${this.testResults.initialProcessingCount}`);
    } else {
      throw new Error('Chat input not found');
    }
  }

  async testSliderChanges() {
    console.log('🎚️ Testing slider changes...');
    
    // Reset processing count for slider test
    const processingCountBefore = this.testResults.initialProcessingCount;
    
    // Find energy level slider
    const energySlider = await this.page.$('input[type="range"]');
    
    if (energySlider) {
      console.log('🔄 Changing energy level slider...');
      
      // Change slider value multiple times
      await energySlider.click({ clickCount: 3 });
      await this.page.waitForTimeout(1000);
      
      await energySlider.click({ clickCount: 5 });
      await this.page.waitForTimeout(1000);
      
      await energySlider.click({ clickCount: 2 });
      await this.page.waitForTimeout(1000);
      
      // Check if processing count increased
      const processingCountAfter = this.testResults.initialProcessingCount;
      this.testResults.sliderChangeProcessingCount = processingCountAfter - processingCountBefore;
      
      console.log(`📊 Processing count after slider changes: ${this.testResults.sliderChangeProcessingCount}`);
    } else {
      console.log('⚠️ Energy slider not found, skipping slider test');
    }
  }

  async analyzeResults() {
    console.log('\n📊 ANALYSIS RESULTS');
    console.log('==================');
    console.log(`Initial wheel processing: ${this.testResults.initialProcessingCount}`);
    console.log(`Slider change processing: ${this.testResults.sliderChangeProcessingCount}`);
    
    // Optimization is working if slider changes don't trigger wheel processing
    this.testResults.optimizationWorking = this.testResults.sliderChangeProcessingCount === 0;
    
    if (this.testResults.optimizationWorking) {
      console.log('✅ Optimization working: Slider changes do not trigger wheel processing');
    } else {
      console.log('❌ Optimization failed: Slider changes triggered wheel processing');
    }
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
    }
  }
}

// Run the test
const test = new WheelProcessingOptimizationTest();
test.run().catch(console.error);
