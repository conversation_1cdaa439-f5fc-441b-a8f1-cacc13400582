#!/usr/bin/env node

/**
 * Test Wheel Data Mapping
 * 
 * This script tests our wheel data mapping logic to ensure proper titles are used
 * instead of generic names like "Activity 1", "Activity 2", etc.
 */

// Sample wheel data from actual backend response
const sampleWheelData = {
  "metadata": {
    "name": "Activity Wheel - Foundation Phase",
    "created_at": "2025-06-11T09:23:32.164947",
    "trust_phase": "Foundation",
    "strategy_id": "strategy-unknown"
  },
  "items": [
    {
      "id": "physical_1",
      "title": "Personalized Gentle Movement",
      "probability": 0.25,
      "sector_start": 0,
      "sector_end": 90,
      "color": "#96CEB4",
      "domain": "physical",
      "percentage": 30.000000000000004,
      "position": 0,
      "name": "Activity 1",
      "description": "",
      "activity_tailored_id": "physical_1",
      "base_challenge_rating": 50
    },
    {
      "id": "physical_2",
      "title": "Personalized Gentle Movement",
      "probability": 0.25,
      "sector_start": 90,
      "sector_end": 180,
      "color": "#96CEB4",
      "domain": "physical",
      "percentage": 30.000000000000004,
      "position": 1,
      "name": "Activity 2",
      "description": "",
      "activity_tailored_id": "physical_2",
      "base_challenge_rating": 50
    },
    {
      "id": "social_1",
      "title": "Personalized Gratitude Practice",
      "probability": 0.25,
      "sector_start": 180,
      "sector_end": 270,
      "color": "#45B7D1",
      "domain": "personal_growth",
      "percentage": 30.000000000000004,
      "position": 2,
      "name": "Activity 3",
      "description": "",
      "activity_tailored_id": "social_1",
      "base_challenge_rating": 50
    },
    {
      "id": "social_2",
      "title": "Personalized Gratitude Practice",
      "probability": 0.25,
      "sector_start": 270,
      "sector_end": 360,
      "color": "#45B7D1",
      "domain": "personal_growth",
      "percentage": 30.000000000000004,
      "position": 3,
      "name": "Activity 4",
      "description": "",
      "activity_tailored_id": "social_2",
      "base_challenge_rating": 50
    }
  ]
};

/**
 * Generate wheel colors (same as frontend)
 */
function generateWheelColor(index) {
  const colors = [
    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
    '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
  ];
  return colors[index % colors.length];
}

/**
 * Test the wheel data mapping logic (same as frontend app-shell.ts)
 */
function testWheelDataMapping(wheelData) {
  console.log('🧪 Testing Wheel Data Mapping Logic');
  console.log('=====================================\n');
  
  // Extract items like frontend would
  let items = [];
  if (wheelData.items) {
    items = wheelData.items;
    console.log(`✅ Found ${items.length} items in wheelData.items`);
  } else if (wheelData.activities) {
    items = wheelData.activities;
    console.log(`✅ Found ${items.length} items in wheelData.activities`);
  } else if (Array.isArray(wheelData)) {
    items = wheelData;
    console.log(`✅ wheelData is array with ${items.length} items`);
  } else {
    console.log('❌ No items found in wheel data');
    return;
  }

  // Convert backend wheel format to frontend format (same logic as app-shell.ts)
  const segments = items.map((item, index) => {
    // Prioritize proper titles over generic names - ALWAYS use title first if available
    let displayText = `Activity ${index + 1}`; // fallback
    
    // Priority order: title (most specific) > activity_name > name (often generic)
    if (item.title && item.title.trim() && !item.title.match(/^Activity \d+$/)) {
      displayText = item.title;
    } else if (item.activity_name && item.activity_name.trim()) {
      displayText = item.activity_name;
    } else if (item.name && item.name.trim() && !item.name.match(/^Activity \d+$/)) {
      displayText = item.name;
    }
    
    return {
      id: item.id || `item-${index}`,
      text: displayText,
      percentage: item.percentage || (item.probability * 100) || (100 / items.length),
      color: item.color || generateWheelColor(index)
    };
  });

  console.log('\n📊 Generated Segments:');
  console.log('======================');
  segments.forEach((segment, index) => {
    const originalItem = items[index];
    console.log(`\n${index + 1}. Segment Analysis:`);
    console.log(`   📝 Final Display Text: "${segment.text}"`);
    console.log(`   🎯 Original Title: "${originalItem.title}"`);
    console.log(`   🏷️  Original Name: "${originalItem.name}"`);
    console.log(`   📊 Percentage: ${segment.percentage.toFixed(1)}%`);
    console.log(`   🎨 Color: ${segment.color}`);
    
    // Check for issues
    if (segment.text.startsWith('Activity ')) {
      console.log(`   ⚠️  WARNING: Using generic name instead of proper title!`);
    } else {
      console.log(`   ✅ SUCCESS: Using proper title!`);
    }
  });

  // Overall assessment
  console.log('\n🎯 OVERALL ASSESSMENT:');
  console.log('======================');
  const genericCount = segments.filter(s => s.text.match(/^Activity \d+$/)).length;
  const properCount = segments.length - genericCount;
  
  console.log(`✅ Segments with proper titles: ${properCount}/${segments.length}`);
  console.log(`⚠️  Segments with generic names: ${genericCount}/${segments.length}`);
  
  if (genericCount === 0) {
    console.log('🎉 PERFECT: All segments use proper titles!');
  } else if (properCount > genericCount) {
    console.log('👍 GOOD: Most segments use proper titles');
  } else {
    console.log('❌ ISSUE: Too many segments using generic names');
  }
  
  return segments;
}

// Run the test
console.log('🎭 Wheel Data Mapping Test');
console.log('==========================\n');

const segments = testWheelDataMapping(sampleWheelData);

console.log('\n💾 Final Wheel Data Structure:');
console.log('==============================');
console.log(JSON.stringify({
  segments: segments,
  wheelId: `wheel-${Date.now()}`,
  createdAt: new Date().toISOString()
}, null, 2));

console.log('\n🎉 Test completed!');
