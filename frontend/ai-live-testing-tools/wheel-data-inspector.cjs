#!/usr/bin/env node

/**
 * Wheel Data Inspector - Specifically monitors wheel_data messages and frontend processing
 */

const { chromium } = require('playwright');

class WheelDataInspector {
  constructor() {
    this.browser = null;
    this.page = null;
    this.wheelDataReceived = false;
    this.wheelData = null;
  }

  async initialize() {
    console.log('🎡 Initializing Wheel Data Inspector...');
    
    this.browser = await chromium.launch({
      headless: false,
      devtools: true
    });
    
    this.page = await this.browser.newPage();
    
    // Enable console logging
    this.page.on('console', msg => {
      const type = msg.type();
      const text = msg.text();
      console.log(`🖥️  [${type}] ${text}`);
    });
    
    console.log('✅ Wheel Data Inspector initialized');
  }

  async loadFrontend() {
    console.log('🌐 Loading frontend...');
    
    await this.page.goto('http://localhost:3000', { 
      waitUntil: 'domcontentloaded',
      timeout: 30000 
    });
    
    // Wait for app to initialize
    await this.page.waitForTimeout(5000);
    
    console.log('✅ Frontend loaded');
  }

  async setupWheelDataMonitoring() {
    console.log('🔍 Setting up wheel data monitoring...');
    
    await this.page.addInitScript(() => {
      window.wheelDataLog = [];
      window.wheelDataReceived = false;
      
      // Monitor WebSocket messages
      const originalWebSocket = window.WebSocket;
      window.WebSocket = function(url, protocols) {
        const ws = new originalWebSocket(url, protocols);
        
        const originalOnMessage = ws.onmessage;
        ws.onmessage = function(event) {
          try {
            const data = JSON.parse(event.data);
            
            if (data.type === 'wheel_data') {
              console.log('🎡 WHEEL_DATA MESSAGE INTERCEPTED!');
              window.wheelDataReceived = true;
              window.wheelDataLog.push({
                timestamp: new Date().toISOString(),
                data: data
              });
              console.log('🎡 Wheel data structure:', JSON.stringify(data, null, 2));
            }
          } catch (e) {
            // Ignore parsing errors
          }
          
          if (originalOnMessage) {
            originalOnMessage.call(this, event);
          }
        };
        
        return ws;
      };
    });
    
    console.log('✅ Wheel data monitoring setup');
  }

  async sendWheelRequest() {
    console.log('💬 Sending wheel generation request...');

    // Check what's available in window
    const windowInfo = await this.page.evaluate(() => {
      return {
        hasGoaliApp: !!window.goaliApp,
        goaliAppKeys: window.goaliApp ? Object.keys(window.goaliApp) : [],
        hasStateManager: !!window.stateManager,
        hasWebSocket: !!window.__GOALI_WS__,
        wsReadyState: window.__GOALI_WS__ ? window.__GOALI_WS__.readyState : null
      };
    });

    console.log('🔍 Window state:', windowInfo);

    // Wait for WebSocket to be connected (readyState 1 = OPEN)
    try {
      await this.page.waitForFunction(() => {
        return window.__GOALI_WS__ && window.__GOALI_WS__.readyState === 1;
      }, { timeout: 15000 });
      console.log('✅ WebSocket connection confirmed');
    } catch (error) {
      console.log('⚠️  WebSocket not ready, proceeding anyway...');
    }

    // Find and fill chat input
    const chatInput = await this.page.locator('textarea, input[type="text"]').first();
    await chatInput.waitFor({ timeout: 10000 });

    // Check if input is enabled
    const isEnabled = await chatInput.isEnabled();
    console.log(`🔍 Chat input enabled: ${isEnabled}`);

    if (!isEnabled) {
      // Wait for input to be enabled
      await this.page.waitForFunction(() => {
        const input = document.querySelector('textarea, input[type="text"]');
        return input && !input.disabled;
      }, { timeout: 10000 });
    }

    await chatInput.fill("hey, I'm feeling energetic and I have 2h free ahead of me. It's very hot outside though. Generate me the perfect wheel !");
    await chatInput.press('Enter');

    console.log('✅ Wheel request sent');
  }

  async waitForWheelData(timeoutMs = 120000) {
    console.log('⏳ Waiting for wheel_data message...');
    
    try {
      await this.page.waitForFunction(() => {
        return window.wheelDataReceived;
      }, { timeout: timeoutMs });
      
      console.log('✅ Wheel data received!');
      return true;
    } catch (error) {
      console.log('❌ Timeout waiting for wheel data');
      return false;
    }
  }

  async analyzeWheelData() {
    console.log('🔍 Analyzing wheel data...');
    
    const analysis = await this.page.evaluate(() => {
      const wheelLog = window.wheelDataLog || [];
      
      if (wheelLog.length === 0) {
        return { error: 'No wheel data received' };
      }
      
      const latestWheelData = wheelLog[wheelLog.length - 1].data;
      
      return {
        messageCount: wheelLog.length,
        latestData: {
          type: latestWheelData.type,
          hasWheel: !!latestWheelData.wheel,
          wheelKeys: latestWheelData.wheel ? Object.keys(latestWheelData.wheel) : [],
          hasItems: !!(latestWheelData.wheel && latestWheelData.wheel.items),
          itemCount: latestWheelData.wheel && latestWheelData.wheel.items ? latestWheelData.wheel.items.length : 0,
          hasActivities: !!(latestWheelData.wheel && latestWheelData.wheel.activities),
          activityCount: latestWheelData.wheel && latestWheelData.wheel.activities ? latestWheelData.wheel.activities.length : 0,
          firstItem: latestWheelData.wheel && latestWheelData.wheel.items && latestWheelData.wheel.items[0] ? latestWheelData.wheel.items[0] : null,
          firstActivity: latestWheelData.wheel && latestWheelData.wheel.activities && latestWheelData.wheel.activities[0] ? latestWheelData.wheel.activities[0] : null
        },
        fullData: latestWheelData
      };
    });
    
    console.log('📊 WHEEL DATA ANALYSIS:');
    console.log(JSON.stringify(analysis, null, 2));
    
    return analysis;
  }

  async checkFrontendProcessing() {
    console.log('🔍 Checking frontend wheel data processing...');
    
    const frontendState = await this.page.evaluate(() => {
      // Check app-shell state
      const appShell = document.querySelector('app-shell');
      const appShellState = appShell ? {
        hasWheelData: !!appShell.wheelData,
        wheelDataType: typeof appShell.wheelData,
        wheelDataKeys: appShell.wheelData ? Object.keys(appShell.wheelData) : [],
        segmentCount: appShell.wheelData && appShell.wheelData.segments ? appShell.wheelData.segments.length : 0
      } : null;
      
      // Check game-wheel component
      const gameWheel = document.querySelector('game-wheel');
      const gameWheelState = gameWheel ? {
        hasWheelData: !!gameWheel.wheelData,
        hasSegments: !!gameWheel.segments,
        segmentCount: gameWheel.segments ? gameWheel.segments.length : 0,
        isVisible: gameWheel.offsetParent !== null,
        innerHTML: gameWheel.innerHTML.substring(0, 200)
      } : null;
      
      // Check wheel container
      const wheelContainer = document.querySelector('.wheel-container');
      const wheelPlaceholder = document.querySelector('.wheel-placeholder');
      
      return {
        appShell: appShellState,
        gameWheel: gameWheelState,
        dom: {
          hasWheelContainer: !!wheelContainer,
          hasWheelPlaceholder: !!wheelPlaceholder,
          wheelPlaceholderVisible: wheelPlaceholder ? wheelPlaceholder.offsetParent !== null : false,
          wheelContainerVisible: wheelContainer ? wheelContainer.offsetParent !== null : false
        }
      };
    });
    
    console.log('🖼️  FRONTEND STATE:');
    console.log(JSON.stringify(frontendState, null, 2));
    
    return frontendState;
  }

  async runFullInspection() {
    try {
      await this.initialize();
      await this.setupWheelDataMonitoring();
      await this.loadFrontend();
      
      // Wait for connection
      await this.page.waitForTimeout(3000);
      
      await this.sendWheelRequest();
      
      const wheelDataReceived = await this.waitForWheelData();
      
      if (wheelDataReceived) {
        // Wait a bit for frontend processing
        await this.page.waitForTimeout(3000);
        
        const analysis = await this.analyzeWheelData();
        const frontendState = await this.checkFrontendProcessing();
        
        console.log('\n🎯 INSPECTION RESULTS:');
        console.log(`  Wheel data received: ✅`);
        console.log(`  Item count: ${analysis.latestData.itemCount}`);
        console.log(`  Activity count: ${analysis.latestData.activityCount}`);
        console.log(`  Frontend processed: ${frontendState.appShell && frontendState.appShell.hasWheelData ? '✅' : '❌'}`);
        console.log(`  Game wheel updated: ${frontendState.gameWheel && frontendState.gameWheel.hasWheelData ? '✅' : '❌'}`);
        console.log(`  Wheel visible: ${frontendState.dom.wheelContainerVisible ? '✅' : '❌'}`);
        
        if (analysis.latestData.firstItem) {
          console.log(`  First item: ${analysis.latestData.firstItem.title || analysis.latestData.firstItem.name || 'Unknown'}`);
        }
        
        if (analysis.latestData.firstActivity) {
          console.log(`  First activity: ${analysis.latestData.firstActivity.title || analysis.latestData.firstActivity.name || 'Unknown'}`);
        }
        
      } else {
        console.log('\n❌ No wheel data received - backend issue');
      }
      
      console.log('\n🔍 Browser kept open for manual inspection. Press Ctrl+C to exit.');
      
      // Keep browser open
      await new Promise(() => {});
      
    } catch (error) {
      console.error('❌ Inspection failed:', error);
    }
  }
}

// Run the inspector
const inspector = new WheelDataInspector();
inspector.runFullInspection().catch(console.error);
