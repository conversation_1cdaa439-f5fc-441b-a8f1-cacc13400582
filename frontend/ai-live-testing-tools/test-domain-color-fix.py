#!/usr/bin/env python3
"""
Test Domain Color Fix
====================

This test verifies that the domain-to-color mapping fix is working properly.
It tests both the domain management service and the wheel generation pipeline.
"""

import os
import sys
import django
import logging

# Add the backend directory to the Python path
sys.path.insert(0, '/usr/src/app')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from apps.main.services.domain_management_service import domain_management_service
from apps.main.agents.tools.activity_tools import _get_activity_color

logger = logging.getLogger(__name__)

def test_domain_color_mapping():
    """Test that domains map to proper colors."""
    print("🎨 Testing Domain Color Mapping")
    print("=" * 50)
    
    # Test domains that should appear in wheel generation
    test_domains = [
        'phys_dance', 'phys_strength', 'phys_flexibility',
        'creative_culinary', 'creative_visual', 'creative_writing',
        'intel_strategic', 'intel_debate', 'intel_language',
        'soc_comm', 'soc_empathy', 'soc_connecting',
        'explor_sensory', 'explor_cultural', 'explor_travel',
        'emot_aware', 'emot_regulate', 'emot_express',
        'refl_meditate', 'refl_mindful', 'refl_journal',
        'prod_health', 'prod_skill', 'prod_time'
    ]
    
    success_count = 0
    total_count = len(test_domains)
    
    for domain in test_domains:
        try:
            # Test domain management service
            color1 = domain_management_service.get_domain_color(domain)
            
            # Test activity tools function
            color2 = _get_activity_color(domain, 0)
            
            # Validate color format
            is_valid_color = (
                isinstance(color1, str) and 
                color1.startswith('#') and 
                len(color1) == 7 and
                color1 != '#9E9E9E'  # Not the old default gray
            )
            
            if is_valid_color and color1 == color2:
                print(f"✅ {domain:20} → {color1}")
                success_count += 1
            else:
                print(f"❌ {domain:20} → {color1} (invalid or inconsistent)")
                
        except Exception as e:
            print(f"❌ {domain:20} → ERROR: {e}")
    
    print(f"\n📊 Results: {success_count}/{total_count} domains working properly")
    return success_count == total_count

def test_wheel_generation_with_colors():
    """Test wheel generation includes proper colors."""
    print("\n🎡 Testing Wheel Generation with Colors")
    print("=" * 50)
    
    try:
        from apps.main.agents.tools.tools import generate_wheel
        from apps.user.models import UserProfile
        
        # Get a test user
        user = UserProfile.objects.first()
        if not user:
            print("❌ No user profiles found")
            return False
            
        print(f"🧪 Testing with user: {user.profile_name} (ID: {user.id})")
        
        # Generate a wheel
        input_data = {
            'user_profile_id': str(user.id),
            'strategy_framework': {
                'domain_distribution': {'physical': 0.3, 'creative': 0.3, 'learning': 0.4},
                'challenge_calibration': {'min_challenge': 40, 'max_challenge': 60},
                'selection_constraints': {'time_available': 30, 'energy_level': 70}
            },
            'activity_count': 4
        }
        
        # This is an async function, but we'll call it synchronously for testing
        import asyncio
        result = asyncio.run(generate_wheel(input_data))
        
        if 'error' in result:
            print(f"❌ Wheel generation failed: {result['error']}")
            return False
            
        wheel_data = result.get('wheel', {})
        items = wheel_data.get('items', [])
        
        if not items:
            print("❌ No wheel items generated")
            return False
            
        print(f"✅ Generated wheel with {len(items)} items:")
        
        colors_valid = True
        for i, item in enumerate(items):
            domain = item.get('domain', 'unknown')
            color = item.get('color', 'missing')
            name = item.get('name', f'Item {i+1}')
            
            is_valid_color = (
                isinstance(color, str) and 
                color.startswith('#') and 
                len(color) == 7 and
                color != '#95A5A6'  # Not fallback gray
            )
            
            status = "✅" if is_valid_color else "❌"
            print(f"   {status} {name[:30]:30} | {domain:15} | {color}")
            
            if not is_valid_color:
                colors_valid = False
        
        return colors_valid
        
    except Exception as e:
        print(f"❌ Error testing wheel generation: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all domain color tests."""
    print("🚀 Domain Color Fix Validation")
    print("=" * 50)
    
    # Test 1: Domain color mapping
    mapping_success = test_domain_color_mapping()
    
    # Test 2: Wheel generation with colors
    wheel_success = test_wheel_generation_with_colors()
    
    # Final results
    print("\n" + "=" * 50)
    print("🏁 FINAL RESULTS")
    print("=" * 50)
    print(f"🎨 Domain Color Mapping: {'✅ PASS' if mapping_success else '❌ FAIL'}")
    print(f"🎡 Wheel Generation Colors: {'✅ PASS' if wheel_success else '❌ FAIL'}")
    
    overall_success = mapping_success and wheel_success
    print(f"\n🎯 Overall: {'✅ ALL TESTS PASS' if overall_success else '❌ SOME TESTS FAILED'}")
    
    return 0 if overall_success else 1

if __name__ == "__main__":
    sys.exit(main())
