#!/usr/bin/env node

/**
 * Direct WebSocket Connection Test
 * Tests the WebSocket connection directly without browser overhead
 */

const WebSocket = require('ws');

class DirectWebSocketTest {
  constructor() {
    this.ws = null;
    this.connected = false;
    this.messages = [];
  }

  async testConnection() {
    console.log('🔍 Testing direct WebSocket connection...');
    
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        console.log('❌ Connection timeout');
        if (this.ws) {
          this.ws.close();
        }
        reject(new Error('Connection timeout'));
      }, 10000);

      try {
        this.ws = new WebSocket('ws://localhost:8000/ws/game/');
        
        this.ws.on('open', () => {
          console.log('✅ WebSocket connected successfully');
          this.connected = true;
          clearTimeout(timeout);
          
          // Send a test message
          const testMessage = {
            type: 'user_message',
            content: 'Hello, test connection',
            debug_selections: {
              userId: 2,
              llmConfigId: ''
            }
          };
          
          console.log('📤 Sending test message...');
          this.ws.send(JSON.stringify(testMessage));
          
          // Wait for response
          setTimeout(() => {
            resolve({
              connected: true,
              messagesReceived: this.messages.length
            });
          }, 5000);
        });
        
        this.ws.on('message', (data) => {
          try {
            const message = JSON.parse(data.toString());
            this.messages.push(message);
            console.log(`📥 Received: ${message.type}`);
            
            if (message.type === 'system_message') {
              console.log(`📋 System message: ${message.content}`);
            } else if (message.type === 'chat_message') {
              console.log(`💬 Chat message: ${message.content}`);
            } else if (message.type === 'wheel_data') {
              console.log('🎡 Wheel data received!');
              console.log(`   Items: ${message.wheel && message.wheel.items ? message.wheel.items.length : 0}`);
            } else if (message.type === 'error') {
              console.log(`❌ Error message: ${message.message || message.content || JSON.stringify(message)}`);
            }
          } catch (error) {
            console.log('📥 Raw message:', data.toString());
          }
        });
        
        this.ws.on('error', (error) => {
          console.log('❌ WebSocket error:', error.message);
          clearTimeout(timeout);
          reject(error);
        });
        
        this.ws.on('close', (code, reason) => {
          console.log(`🔌 WebSocket closed: ${code} - ${reason}`);
          this.connected = false;
        });
        
      } catch (error) {
        console.log('❌ Failed to create WebSocket:', error.message);
        clearTimeout(timeout);
        reject(error);
      }
    });
  }

  async testWheelGeneration() {
    if (!this.connected) {
      throw new Error('Not connected');
    }

    console.log('🎡 Testing wheel generation...');
    
    return new Promise((resolve) => {
      const wheelMessage = {
        type: 'user_message',
        content: "hey, I'm feeling energetic and I have 2h free ahead of me. It's very hot outside though. Generate me the perfect wheel !",
        debug_selections: {
          userId: 2,
          llmConfigId: ''
        }
      };
      
      console.log('📤 Sending wheel generation request...');
      this.ws.send(JSON.stringify(wheelMessage));
      
      // Wait for wheel data
      const startTime = Date.now();
      const checkForWheelData = () => {
        const wheelDataMessage = this.messages.find(msg => msg.type === 'wheel_data');
        
        if (wheelDataMessage) {
          console.log('✅ Wheel data received!');
          resolve({
            success: true,
            wheelData: wheelDataMessage,
            timeToReceive: Date.now() - startTime
          });
        } else if (Date.now() - startTime > 120000) {
          console.log('❌ Timeout waiting for wheel data');
          resolve({
            success: false,
            timeToReceive: Date.now() - startTime,
            messagesReceived: this.messages.length
          });
        } else {
          setTimeout(checkForWheelData, 1000);
        }
      };
      
      checkForWheelData();
    });
  }

  close() {
    if (this.ws) {
      this.ws.close();
    }
  }

  async runFullTest() {
    try {
      console.log('🚀 Starting Direct WebSocket Test');
      console.log('=' * 50);
      
      // Test connection
      const connectionResult = await this.testConnection();
      console.log('✅ Connection test passed:', connectionResult);
      
      // Test wheel generation
      const wheelResult = await this.testWheelGeneration();
      console.log('🎡 Wheel generation test result:', wheelResult);
      
      console.log('\n📊 FINAL RESULTS:');
      console.log(`  Connection: ${connectionResult.connected ? 'SUCCESS' : 'FAILED'}`);
      console.log(`  Messages received: ${this.messages.length}`);
      console.log(`  Wheel generation: ${wheelResult.success ? 'SUCCESS' : 'FAILED'}`);
      
      if (wheelResult.success) {
        console.log(`  Time to generate wheel: ${wheelResult.timeToReceive}ms`);
        if (wheelResult.wheelData && wheelResult.wheelData.wheel) {
          const wheel = wheelResult.wheelData.wheel;
          console.log(`  Wheel items: ${wheel.items ? wheel.items.length : 0}`);
          console.log(`  Wheel activities: ${wheel.activities ? wheel.activities.length : 0}`);
        }
      }
      
      this.close();
      
    } catch (error) {
      console.error('❌ Test failed:', error);
      this.close();
      process.exit(1);
    }
  }
}

// Run the test
const test = new DirectWebSocketTest();
test.runFullTest();
