#!/usr/bin/env node

/**
 * Comprehensive Issue Diagnosis Tool
 * 
 * Tests all the issues mentioned by the user:
 * 1. Duplicate messages in frontend
 * 2. Wheel spinning winner detection issues
 * 3. Post-spin workflow multiple messages
 * 4. Markdown auto-detection and line breaks
 */

const { chromium } = require('playwright');
const WebSocket = require('ws');

class ComprehensiveIssueDiagnosis {
    constructor() {
        this.browser = null;
        this.page = null;
        this.ws = null;
        this.messages = [];
        this.duplicates = [];
        this.issues = [];
    }

    async initialize() {
        console.log('🔧 Initializing Comprehensive Issue Diagnosis...');
        
        // Launch browser
        this.browser = await chromium.launch({ 
            headless: false,
            args: ['--disable-web-security', '--disable-features=VizDisplayCompositor']
        });
        this.page = await this.browser.newPage();
        
        // Set up console logging
        this.page.on('console', msg => {
            if (msg.type() === 'log' || msg.type() === 'error' || msg.type() === 'warning') {
                console.log(`🖥️  [${msg.type()}] ${msg.text()}`);
            }
        });

        console.log('✅ Browser initialized');
    }

    async testUserStory() {
        console.log('\n🎯 Testing Complete User Story');
        console.log('════════════════════════════════════════════════════════════');
        
        // Load frontend
        await this.page.goto('http://localhost:3000/');
        await this.page.waitForTimeout(3000);
        
        console.log('✅ Frontend loaded');
        
        // Wait for connection
        await this.page.waitForSelector('.connection-status', { timeout: 10000 });
        const connectionStatus = await this.page.textContent('.connection-status');
        console.log(`🔌 Connection status: ${connectionStatus}`);
        
        // Send the specific user message
        const userMessage = "I'm restless and need to do things physical. I have 2 hours. Make me a wheel";
        console.log(`📝 Sending message: "${userMessage}"`);
        
        // Find and fill textarea
        const textarea = await this.page.locator('textarea, input[type="text"]').first();
        await textarea.fill(userMessage);
        await this.page.keyboard.press('Enter');
        
        console.log('✅ Message sent');
        
        // Monitor messages for duplicates
        await this.monitorMessagesForDuplicates();
        
        // Wait for wheel generation
        await this.waitForWheelGeneration();
        
        // Test wheel spinning
        await this.testWheelSpinning();
        
        // Test post-spin workflow
        await this.testPostSpinWorkflow();
        
        // Test markdown detection
        await this.testMarkdownDetection();
    }

    async monitorMessagesForDuplicates() {
        console.log('\n🔍 Monitoring for duplicate messages...');
        
        let messageCount = 0;
        let lastMessageCount = 0;
        
        for (let i = 0; i < 30; i++) {
            await this.page.waitForTimeout(1000);
            
            // Count chat messages
            const messages = await this.page.locator('.chat-message, .message').count();
            
            if (messages > lastMessageCount) {
                console.log(`📨 Messages detected: ${messages} (was ${lastMessageCount})`);
                
                // Check for duplicates by examining message content
                const messageTexts = await this.page.locator('.chat-message, .message').allTextContents();
                const uniqueMessages = new Set(messageTexts);
                
                if (messageTexts.length > uniqueMessages.size) {
                    const duplicateCount = messageTexts.length - uniqueMessages.size;
                    console.log(`🚨 DUPLICATE MESSAGES DETECTED: ${duplicateCount} duplicates found`);
                    this.issues.push({
                        type: 'duplicate_messages',
                        count: duplicateCount,
                        total: messageTexts.length,
                        unique: uniqueMessages.size
                    });
                }
                
                lastMessageCount = messages;
            }
        }
    }

    async waitForWheelGeneration() {
        console.log('\n🎡 Waiting for wheel generation...');
        
        try {
            // Wait for wheel container
            await this.page.waitForSelector('.wheel-container, .wheel, [data-testid="wheel"]', { timeout: 60000 });
            console.log('✅ Wheel container found');
            
            // Count wheel segments
            const segments = await this.page.locator('.wheel-segment, .segment, .activity').count();
            console.log(`🎯 Wheel segments found: ${segments}`);
            
            if (segments < 4) {
                this.issues.push({
                    type: 'insufficient_activities',
                    count: segments,
                    expected: 4
                });
            }
            
            // Check for distinct colors
            const segmentColors = await this.page.locator('.wheel-segment, .segment, .activity').evaluateAll(elements => {
                return elements.map(el => {
                    const style = window.getComputedStyle(el);
                    return style.backgroundColor || style.color || el.style.backgroundColor;
                });
            });
            
            const uniqueColors = new Set(segmentColors.filter(color => color && color !== 'rgba(0, 0, 0, 0)'));
            console.log(`🎨 Unique colors found: ${uniqueColors.size} out of ${segments} segments`);
            
            if (uniqueColors.size < segments) {
                this.issues.push({
                    type: 'insufficient_distinct_colors',
                    unique: uniqueColors.size,
                    total: segments
                });
            }
            
        } catch (error) {
            console.log(`❌ Wheel generation failed: ${error.message}`);
            this.issues.push({
                type: 'wheel_generation_failed',
                error: error.message
            });
        }
    }

    async testWheelSpinning() {
        console.log('\n🎲 Testing wheel spinning...');
        
        try {
            // Look for spin button
            const spinButton = await this.page.locator('button:has-text("Spin"), .spin-button, [data-testid="spin"]').first();
            
            if (await spinButton.count() > 0) {
                console.log('✅ Spin button found');
                
                // Click spin button
                await spinButton.click();
                console.log('🎯 Spin button clicked');
                
                // Monitor for winner detection
                await this.monitorWinnerDetection();
                
            } else {
                console.log('❌ No spin button found');
                this.issues.push({
                    type: 'no_spin_button',
                    message: 'Spin button not found'
                });
            }
            
        } catch (error) {
            console.log(`❌ Wheel spinning failed: ${error.message}`);
            this.issues.push({
                type: 'wheel_spinning_failed',
                error: error.message
            });
        }
    }

    async monitorWinnerDetection() {
        console.log('🏆 Monitoring winner detection...');
        
        let winnerDetected = false;
        
        for (let i = 0; i < 20; i++) {
            await this.page.waitForTimeout(500);
            
            // Check for winner indication
            const winnerElements = await this.page.locator('.winner, .selected, .result, [data-testid="winner"]').count();
            
            if (winnerElements > 0) {
                const winnerText = await this.page.locator('.winner, .selected, .result, [data-testid="winner"]').first().textContent();
                console.log(`🏆 Winner detected: ${winnerText}`);
                winnerDetected = true;
                break;
            }
            
            // Check if wheel and ball are still moving
            const isSpinning = await this.page.evaluate(() => {
                const wheel = document.querySelector('.wheel, .wheel-container');
                if (wheel) {
                    const transform = window.getComputedStyle(wheel).transform;
                    return transform && transform !== 'none';
                }
                return false;
            });
            
            if (!isSpinning && i > 10) {
                console.log('⚠️  Wheel appears to have stopped but no winner detected');
                break;
            }
        }
        
        if (!winnerDetected) {
            this.issues.push({
                type: 'winner_detection_failed',
                message: 'Winner not detected after wheel spin'
            });
        }
    }

    async testPostSpinWorkflow() {
        console.log('\n🔄 Testing post-spin workflow...');
        
        // Monitor for multiple messages from post-spin
        const initialMessageCount = await this.page.locator('.chat-message, .message').count();
        
        // Wait for post-spin messages
        await this.page.waitForTimeout(5000);
        
        const finalMessageCount = await this.page.locator('.chat-message, .message').count();
        const newMessages = finalMessageCount - initialMessageCount;
        
        console.log(`📨 New messages after spin: ${newMessages}`);
        
        if (newMessages > 2) {
            console.log('⚠️  Multiple messages detected from post-spin workflow');
            this.issues.push({
                type: 'multiple_post_spin_messages',
                count: newMessages,
                expected: 1
            });
        }
    }

    async testMarkdownDetection() {
        console.log('\n📝 Testing markdown detection and line breaks...');
        
        // Send a message with markdown and line breaks
        const markdownMessage = "Here's a test with **bold text** and\n\nline breaks\n\n- List item 1\n- List item 2";
        
        const textarea = await this.page.locator('textarea, input[type="text"]').first();
        await textarea.fill(markdownMessage);
        await this.page.keyboard.press('Enter');
        
        await this.page.waitForTimeout(3000);
        
        // Check if markdown is rendered
        const hasMarkdown = await this.page.locator('strong, b, ul, ol').count() > 0;
        const hasLineBreaks = await this.page.locator('br, p').count() > 0;
        
        console.log(`📝 Markdown detected: ${hasMarkdown}`);
        console.log(`📝 Line breaks detected: ${hasLineBreaks}`);
        
        if (!hasMarkdown) {
            this.issues.push({
                type: 'markdown_not_detected',
                message: 'Markdown formatting not auto-detected'
            });
        }
        
        if (!hasLineBreaks) {
            this.issues.push({
                type: 'line_breaks_not_respected',
                message: 'Line breaks not properly displayed'
            });
        }
    }

    async generateReport() {
        console.log('\n📊 COMPREHENSIVE ISSUE DIAGNOSIS REPORT');
        console.log('════════════════════════════════════════════════════════════');
        
        if (this.issues.length === 0) {
            console.log('✅ NO ISSUES DETECTED - All systems working correctly!');
        } else {
            console.log(`❌ ${this.issues.length} ISSUES DETECTED:`);
            console.log('');
            
            this.issues.forEach((issue, index) => {
                console.log(`${index + 1}. ${issue.type.toUpperCase().replace(/_/g, ' ')}`);
                console.log(`   Details: ${JSON.stringify(issue, null, 2)}`);
                console.log('');
            });
        }
        
        console.log('🔧 RECOMMENDED FIXES:');
        console.log('');
        
        this.issues.forEach(issue => {
            switch (issue.type) {
                case 'duplicate_messages':
                    console.log('• Fix duplicate message handling in frontend message processing');
                    break;
                case 'winner_detection_failed':
                    console.log('• Implement proper winner detection after wheel stops spinning');
                    break;
                case 'multiple_post_spin_messages':
                    console.log('• Consolidate post-spin workflow into single mentor message');
                    break;
                case 'markdown_not_detected':
                    console.log('• Add markdown auto-detection to chat message display');
                    break;
                case 'line_breaks_not_respected':
                    console.log('• Preserve line breaks in chat message rendering');
                    break;
                case 'insufficient_distinct_colors':
                    console.log('• Ensure wheel activities have distinct colors');
                    break;
            }
        });
    }

    async cleanup() {
        if (this.ws) {
            this.ws.close();
        }
        if (this.browser) {
            await this.browser.close();
        }
    }

    async run() {
        try {
            await this.initialize();
            await this.testUserStory();
            await this.generateReport();
        } catch (error) {
            console.error('❌ Test failed:', error);
        } finally {
            await this.cleanup();
        }
    }
}

// Run the diagnosis
const diagnosis = new ComprehensiveIssueDiagnosis();
diagnosis.run().catch(console.error);
