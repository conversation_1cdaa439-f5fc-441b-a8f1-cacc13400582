#!/usr/bin/env node

/**
 * Direct User 191 Test
 * 
 * This test bypasses the debug panel and directly tests User 191
 * by sending a WebSocket message to verify our backend fix is working.
 */

const WebSocket = require('ws');

async function testUser191Direct(port = 3001) {
    console.log('🎯 Direct User 191 Test');
    console.log(`   Port: ${port}`);
    console.log(`   WebSocket URL: ws://localhost:${port}/ws/chat/`);
    
    return new Promise((resolve, reject) => {
        const ws = new WebSocket(`ws://localhost:${port}/ws/chat/`);
        let responseReceived = false;
        let startTime = null;
        
        const timeout = setTimeout(() => {
            if (!responseReceived) {
                console.log('❌ TIMEOUT: No response after 30 seconds');
                ws.close();
                resolve({
                    success: false,
                    reason: 'timeout',
                    duration: 30000
                });
            }
        }, 30000);
        
        ws.on('open', () => {
            console.log('✓ WebSocket connection established');
            
            // Send authentication/session setup message for User 191
            const authMessage = {
                type: 'auth',
                user_profile_id: '191',
                session_name: 'test_user_191_direct'
            };
            
            console.log('📤 Sending auth message for User 191...');
            ws.send(JSON.stringify(authMessage));
            
            // Wait a moment then send the wheel request
            setTimeout(() => {
                const wheelMessage = {
                    type: 'chat_message',
                    text: 'make me a wheel',
                    user_profile_id: '191'
                };
                
                console.log('📤 Sending wheel request: "make me a wheel"');
                startTime = Date.now();
                ws.send(JSON.stringify(wheelMessage));
            }, 1000);
        });
        
        ws.on('message', (data) => {
            try {
                const message = JSON.parse(data.toString());
                console.log(`📥 Received message: ${message.type}`);
                
                if (message.type === 'chat_message' && message.text) {
                    responseReceived = true;
                    const duration = Date.now() - startTime;
                    
                    console.log(`✅ Response received in ${duration}ms`);
                    console.log(`📝 Response: ${message.text.substring(0, 200)}...`);
                    
                    // Analyze response for profile completion indicators
                    const responseText = message.text.toLowerCase();
                    const profileIndicators = [
                        'tell me about',
                        'current environment',
                        'situation',
                        'more information',
                        'profile',
                        'personalized'
                    ];
                    
                    const foundIndicators = profileIndicators.filter(indicator => 
                        responseText.includes(indicator)
                    );
                    
                    console.log('\n📊 Response Analysis:');
                    if (foundIndicators.length > 0) {
                        console.log(`✅ Profile completion indicators found: ${foundIndicators.join(', ')}`);
                        console.log('✅ Backend fix working: Asking for specific profile information');
                    } else {
                        console.log('⚠️  No clear profile completion indicators found');
                    }
                    
                    clearTimeout(timeout);
                    ws.close();
                    
                    resolve({
                        success: true,
                        duration: duration,
                        responseText: message.text,
                        profileIndicators: foundIndicators,
                        backendFixWorking: foundIndicators.length > 0
                    });
                }
                
                if (message.type === 'processing_status') {
                    console.log(`🔄 Processing: ${message.status || 'unknown'}`);
                }
                
                if (message.type === 'wheel_data') {
                    console.log('🎡 Wheel data received - this means wheel generation happened instead of onboarding!');
                    console.log('❌ Backend fix NOT working - should have routed to onboarding');
                    
                    responseReceived = true;
                    clearTimeout(timeout);
                    ws.close();
                    
                    resolve({
                        success: false,
                        reason: 'wheel_generated_instead_of_onboarding',
                        duration: Date.now() - startTime,
                        backendFixWorking: false
                    });
                }
                
            } catch (error) {
                console.log(`⚠️  Error parsing message: ${error.message}`);
            }
        });
        
        ws.on('error', (error) => {
            console.log(`❌ WebSocket error: ${error.message}`);
            clearTimeout(timeout);
            resolve({
                success: false,
                reason: 'websocket_error',
                error: error.message
            });
        });
        
        ws.on('close', () => {
            console.log('🔌 WebSocket connection closed');
        });
    });
}

async function main() {
    const port = process.argv[2] ? parseInt(process.argv[2]) : 3001;
    
    console.log('🚀 Starting Direct User 191 Test');
    console.log('=' * 50);
    
    const result = await testUser191Direct(port);
    
    console.log('\n📊 Test Results:');
    console.log('=' * 30);
    console.log(`Success: ${result.success ? '✅ YES' : '❌ NO'}`);
    console.log(`Duration: ${result.duration}ms`);
    
    if (result.reason) {
        console.log(`Reason: ${result.reason}`);
    }
    
    if (result.backendFixWorking !== undefined) {
        console.log(`Backend Fix Working: ${result.backendFixWorking ? '✅ YES' : '❌ NO'}`);
    }
    
    if (result.profileIndicators && result.profileIndicators.length > 0) {
        console.log(`Profile Indicators: ${result.profileIndicators.join(', ')}`);
    }
    
    console.log('\n🎯 Conclusion:');
    if (result.success && result.backendFixWorking) {
        console.log('✅ SUCCESS: Backend fix is working correctly!');
        console.log('✅ User 191 is properly routed to onboarding workflow');
        console.log('✅ Specific profile completion questions are being asked');
    } else if (result.reason === 'wheel_generated_instead_of_onboarding') {
        console.log('❌ FAILURE: Backend fix not working');
        console.log('❌ User 191 was routed to wheel generation instead of onboarding');
    } else {
        console.log('⚠️  INCONCLUSIVE: Test failed due to technical issues');
    }
}

main().catch(console.error);
