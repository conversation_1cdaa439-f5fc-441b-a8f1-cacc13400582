#!/usr/bin/env node

/**
 * Test Robust Wheel Item Removal - Test the robust solution with existing wheel data
 * 
 * This test uses existing wheel items in the database and tests the robust removal API
 * that can handle both database IDs and workflow-generated IDs.
 */

const CONFIG = {
  apiBaseUrl: 'http://localhost:8000',
  testUserId: '2'
};

class RobustWheelRemovalTest {
  constructor() {
    this.testResults = {
      databaseItemsFound: false,
      workflowIdRemovalTested: false,
      databaseIdRemovalTested: false,
      removalSuccessful: false,
      errorMessage: null
    };
  }

  async runTest() {
    console.log('🧪 Testing Robust Wheel Item Removal Solution');
    console.log('==============================================');
    
    try {
      // Step 1: Check existing wheel items in database
      await this.checkExistingWheelItems();
      
      // Step 2: Test removal with workflow-generated ID format
      if (this.testResults.databaseItemsFound) {
        await this.testWorkflowIdRemoval();
      }
      
      return this.testResults;
    } catch (error) {
      console.error('💥 Test execution failed:', error);
      this.testResults.errorMessage = error.message;
      return this.testResults;
    }
  }

  async checkExistingWheelItems() {
    console.log('📊 Step 1: Checking existing wheel items in database...');
    
    try {
      // Use Django shell to check wheel items
      const { spawn } = require('child_process');
      
      const checkCommand = `
from apps.main.models import WheelItem, Wheel
from apps.user.models import UserProfile

user_profile = UserProfile.objects.get(id=2)
wheels = Wheel.objects.filter(name__icontains=user_profile.profile_name).order_by('-created_at')

if wheels.exists():
    wheel = wheels.first()
    items = WheelItem.objects.filter(wheel=wheel)
    print(f"WHEEL_FOUND:{wheel.name}")
    print(f"ITEM_COUNT:{items.count()}")
    for i, item in enumerate(items):
        print(f"ITEM_{i+1}:{item.id}:{item.activity_tailored.name}")
else:
    print("NO_WHEEL_FOUND")
`;

      return new Promise((resolve, reject) => {
        const process = spawn('docker', [
          'exec', 'backend-web-1', 'python', 'manage.py', 'shell', '-c', checkCommand
        ]);

        let output = '';
        process.stdout.on('data', (data) => {
          output += data.toString();
        });

        process.on('close', (code) => {
          if (code === 0) {
            this.parseWheelData(output);
            resolve();
          } else {
            reject(new Error(`Django shell command failed with code ${code}`));
          }
        });
      });
    } catch (error) {
      console.error('❌ Error checking wheel items:', error);
      throw error;
    }
  }

  parseWheelData(output) {
    const lines = output.split('\n');
    let wheelFound = false;
    let itemCount = 0;
    const items = [];

    for (const line of lines) {
      if (line.startsWith('WHEEL_FOUND:')) {
        wheelFound = true;
        const wheelName = line.split('WHEEL_FOUND:')[1];
        console.log(`✅ Found wheel: ${wheelName}`);
      } else if (line.startsWith('ITEM_COUNT:')) {
        itemCount = parseInt(line.split('ITEM_COUNT:')[1]);
        console.log(`📊 Wheel has ${itemCount} items`);
      } else if (line.startsWith('ITEM_')) {
        const parts = line.split(':');
        if (parts.length >= 3) {
          const itemId = parts[1];
          const itemName = parts.slice(2).join(':');
          items.push({ id: itemId, name: itemName });
          console.log(`   - ${itemId}: ${itemName}`);
        }
      } else if (line.includes('NO_WHEEL_FOUND')) {
        console.log('❌ No wheel found for user');
        return;
      }
    }

    if (wheelFound && itemCount > 0) {
      this.testResults.databaseItemsFound = true;
      this.wheelItems = items;
      console.log(`✅ Found ${itemCount} wheel items to test with`);
    } else {
      console.log('❌ No wheel items found to test with');
    }
  }

  async testWorkflowIdRemoval() {
    console.log('\n📤 Step 2: Testing removal with workflow-generated ID format...');
    
    if (!this.wheelItems || this.wheelItems.length === 0) {
      console.log('❌ No wheel items available for testing');
      return;
    }

    // Use the first wheel item for testing
    const testItem = this.wheelItems[0];
    console.log(`🎯 Testing with database item: ${testItem.id} (${testItem.name})`);

    // Generate a workflow-style ID for testing the robust solution
    // Format: wheel-item-{index}-{hash}
    const workflowStyleId = `wheel-item-1-abc123test`;
    console.log(`🔧 Testing robust solution with workflow-style ID: ${workflowStyleId}`);

    this.testResults.workflowIdRemovalTested = true;

    try {
      const response = await fetch(`${CONFIG.apiBaseUrl}/api/wheel-items/${workflowStyleId}/`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      console.log(`📡 API Response Status: ${response.status}`);

      if (response.ok) {
        const result = await response.json();
        console.log('✅ ROBUST SOLUTION SUCCESS!');
        console.log(`📊 Removed item: ${result.debug_info?.removed_item || 'Unknown'}`);
        console.log(`📊 Requested ID: ${result.debug_info?.requested_id || workflowStyleId}`);
        console.log(`📊 Database ID: ${result.debug_info?.database_id || 'Unknown'}`);
        console.log(`📊 Updated wheel has ${result.wheel_data.segments.length} segments`);
        this.testResults.removalSuccessful = true;
      } else {
        const errorText = await response.text();
        console.log('❌ ROBUST SOLUTION FAILED!');
        console.log(`📊 Error: ${response.status} - ${errorText}`);
        this.testResults.errorMessage = `${response.status} - ${errorText}`;
      }
    } catch (error) {
      console.log('❌ ROBUST SOLUTION FAILED!');
      console.log(`📊 Network Error: ${error.message}`);
      this.testResults.errorMessage = error.message;
    }
  }
}

// Run the test
if (require.main === module) {
  const test = new RobustWheelRemovalTest();
  test.runTest()
    .then(results => {
      console.log('\n🏁 TEST COMPLETED');
      console.log('================');
      console.log(`📊 Database items found: ${results.databaseItemsFound}`);
      console.log(`🧪 Workflow ID removal tested: ${results.workflowIdRemovalTested}`);
      console.log(`🎯 Removal successful: ${results.removalSuccessful}`);
      
      if (results.errorMessage) {
        console.log(`❌ Error: ${results.errorMessage}`);
      }
      
      if (results.databaseItemsFound && results.workflowIdRemovalTested && results.removalSuccessful) {
        console.log('\n🎉 ROBUST SOLUTION WORKS! Wheel item removal handles workflow-generated IDs correctly!');
        process.exit(0);
      } else {
        console.log('\n💥 ROBUST SOLUTION NEEDS WORK');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('💥 Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = { RobustWheelRemovalTest };
