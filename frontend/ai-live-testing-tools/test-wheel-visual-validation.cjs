#!/usr/bin/env node

/**
 * Wheel Component Visual Validation Tool
 * 
 * Takes screenshots at different stages to validate visual behavior:
 * 1. Before loading activities (greyed out wheel)
 * 2. After loading activities (colorful display)
 * 3. During spin (ball movement)
 * 4. Winner established (visual verification)
 * 
 * Usage: node test-wheel-visual-validation.cjs [port]
 * Example: node test-wheel-visual-validation.cjs 3004
 */

const { chromium } = require('playwright');
const path = require('path');
const fs = require('fs');

// Default configuration
const DEFAULT_PORT = 3004;
const DEFAULT_TIMEOUT = 30000;

class WheelVisualValidator {
    constructor(port = DEFAULT_PORT) {
        this.port = port;
        this.baseUrl = `http://localhost:${port}`;
        this.debugUrl = `${this.baseUrl}/wheel-debug.html`;
        this.browser = null;
        this.page = null;
        this.screenshotDir = path.join(__dirname, 'wheel-screenshots');
        this.testResults = {
            screenshots: [],
            issues: [],
            ballVisible: false,
            ballMoving: false,
            winnerDetected: false
        };
    }

    async init() {
        console.log('📸 Starting Wheel Visual Validation Tool');
        console.log(`📍 Testing URL: ${this.debugUrl}`);
        console.log(`📁 Screenshots will be saved to: ${this.screenshotDir}`);
        console.log('');

        // Create screenshots directory
        if (!fs.existsSync(this.screenshotDir)) {
            fs.mkdirSync(this.screenshotDir, { recursive: true });
        }

        try {
            // Launch browser
            this.browser = await chromium.launch({ 
                headless: false,
                slowMo: 200 // Slow down for better visibility
            });
            
            this.page = await this.browser.newPage();
            
            // Set viewport size for consistent screenshots
            await this.page.setViewportSize({ width: 1400, height: 900 });
            
            // Enable console logging
            this.page.on('console', msg => {
                if (msg.type() === 'error') {
                    console.log(`🔴 Console Error: ${msg.text()}`);
                    this.testResults.issues.push(`Console Error: ${msg.text()}`);
                } else if (msg.text().includes('BALL') || msg.text().includes('WHEEL') || msg.text().includes('PHYSICS')) {
                    console.log(`🎱 Physics: ${msg.text()}`);
                }
            });

            // Navigate to debug page
            await this.page.goto(this.debugUrl, { waitUntil: 'networkidle' });
            console.log('✅ Debug page loaded successfully');

        } catch (error) {
            console.error('❌ Failed to initialize browser:', error.message);
            throw error;
        }
    }

    async takeScreenshot(stage, description) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `${timestamp}_${stage}.png`;
        const filepath = path.join(this.screenshotDir, filename);
        
        await this.page.screenshot({ 
            path: filepath,
            fullPage: false // Just the viewport
        });
        
        console.log(`📸 Screenshot taken: ${stage} - ${description}`);
        console.log(`   Saved to: ${filename}`);
        
        this.testResults.screenshots.push({
            stage,
            description,
            filename,
            filepath,
            timestamp
        });
        
        return filepath;
    }

    async validateBallVisibility() {
        console.log('🔍 Checking ball visibility...');
        
        try {
            // Check if ball is rendered in canvas
            const ballInfo = await this.page.evaluate(() => {
                const wheel = document.getElementById('debugWheel');
                if (!wheel || !wheel.physicsEngine) {
                    return { exists: false, error: 'No physics engine' };
                }
                
                const ballBody = wheel.physicsEngine.getBallBody();
                if (!ballBody) {
                    return { exists: false, error: 'No ball body' };
                }
                
                const position = ballBody.position;
                const velocity = wheel.physicsEngine.getBallVelocity();
                
                return {
                    exists: true,
                    position: { x: position.x, y: position.y },
                    velocity: velocity.magnitude,
                    visible: true // We'll assume it should be visible if it exists
                };
            });
            
            if (ballInfo.exists) {
                console.log(`✅ Ball found at position (${ballInfo.position.x.toFixed(1)}, ${ballInfo.position.y.toFixed(1)})`);
                console.log(`   Velocity: ${ballInfo.velocity.toFixed(4)}`);
                this.testResults.ballVisible = true;
            } else {
                console.log(`❌ Ball not found: ${ballInfo.error}`);
                this.testResults.issues.push(`Ball not found: ${ballInfo.error}`);
            }
            
            return ballInfo;
        } catch (error) {
            console.log(`❌ Error checking ball visibility: ${error.message}`);
            this.testResults.issues.push(`Ball visibility check failed: ${error.message}`);
            return { exists: false, error: error.message };
        }
    }

    async runVisualValidation() {
        console.log('\n📸 Running Visual Validation Sequence...\n');

        try {
            // Stage 1: Before loading activities (greyed out wheel)
            console.log('🔍 Stage 1: Before loading activities');
            await this.page.waitForTimeout(2000); // Let page settle
            await this.takeScreenshot('01_before_loading', 'Greyed out wheel before activities loaded');
            
            // Check initial state
            const initialState = await this.page.evaluate(() => {
                const wheel = document.getElementById('debugWheel');
                return {
                    hasWheel: !!wheel,
                    isInvisible: wheel ? wheel.invisible : null,
                    segmentCount: wheel ? (wheel.getWheelState ? wheel.getWheelState().segments.length : 0) : 0
                };
            });
            
            console.log(`   Wheel element: ${initialState.hasWheel ? '✅' : '❌'}`);
            console.log(`   Invisible state: ${initialState.isInvisible}`);
            console.log(`   Segments loaded: ${initialState.segmentCount}`);

            // Stage 2: Load activities
            console.log('\n🔍 Stage 2: Loading activities');
            await this.page.click('#loadWheelBtn');
            console.log('   Clicked load wheel button');
            
            // Wait for activities to load
            await this.page.waitForFunction(() => {
                const statusDisplay = document.getElementById('statusDisplay');
                return statusDisplay && statusDisplay.textContent.includes('Mock wheel items loaded');
            }, { timeout: 10000 });
            
            await this.page.waitForTimeout(2000); // Let rendering complete
            await this.takeScreenshot('02_after_loading', 'Colorful wheel with activities loaded');
            
            // Check ball visibility after loading
            await this.validateBallVisibility();
            
            // Stage 3: Before spin
            console.log('\n🔍 Stage 3: Before spinning');
            await this.takeScreenshot('03_before_spin', 'Wheel ready to spin with ball visible');
            
            // Stage 4: Start spin
            console.log('\n🔍 Stage 4: Starting spin');
            await this.page.click('#spinWheelBtn');
            console.log('   Clicked spin button');
            
            // Wait a moment for spin to start
            await this.page.waitForTimeout(1000);
            await this.takeScreenshot('04_spin_start', 'Spin started - ball should be falling');
            
            // Monitor ball movement during spin
            console.log('   Monitoring ball movement...');
            let ballMovementDetected = false;
            let lastBallPosition = null;
            
            for (let i = 0; i < 10; i++) {
                const ballInfo = await this.validateBallVisibility();
                if (ballInfo.exists) {
                    if (lastBallPosition) {
                        const movement = Math.sqrt(
                            Math.pow(ballInfo.position.x - lastBallPosition.x, 2) +
                            Math.pow(ballInfo.position.y - lastBallPosition.y, 2)
                        );
                        if (movement > 1) {
                            ballMovementDetected = true;
                            console.log(`   ✅ Ball movement detected: ${movement.toFixed(2)} pixels`);
                            break;
                        }
                    }
                    lastBallPosition = ballInfo.position;
                }
                await this.page.waitForTimeout(500);
            }
            
            this.testResults.ballMoving = ballMovementDetected;
            if (!ballMovementDetected) {
                console.log('   ❌ No ball movement detected during spin');
                this.testResults.issues.push('Ball not moving during spin');
            }
            
            // Stage 5: During spin
            await this.page.waitForTimeout(2000);
            await this.takeScreenshot('05_during_spin', 'Wheel spinning with ball movement');
            
            // Stage 6: Wait for winner
            console.log('\n🔍 Stage 6: Waiting for winner detection');
            
            try {
                // Wait for spin to complete with winner detection
                await this.page.waitForFunction(() => {
                    const winnerDisplay = document.getElementById('winnerDisplay');
                    return winnerDisplay && winnerDisplay.classList.contains('visible');
                }, { timeout: 20000 });
                
                await this.page.waitForTimeout(1000); // Let winner animation complete
                await this.takeScreenshot('06_winner_detected', 'Winner detected and displayed');
                
                // Get winner information
                const winnerInfo = await this.page.evaluate(() => {
                    const winnerText = document.getElementById('winnerText');
                    return winnerText ? winnerText.textContent : 'Unknown';
                });
                
                console.log(`   🎉 Winner detected: ${winnerInfo}`);
                this.testResults.winnerDetected = true;
                
            } catch (error) {
                console.log('   ❌ Winner detection timeout or failed');
                this.testResults.issues.push('Winner detection failed or timed out');
                await this.takeScreenshot('06_no_winner', 'Spin completed but no winner detected');
            }
            
            // Final state screenshot
            await this.page.waitForTimeout(2000);
            await this.takeScreenshot('07_final_state', 'Final state after spin completion');

        } catch (error) {
            console.error('❌ Visual validation failed:', error.message);
            this.testResults.issues.push(`Validation failed: ${error.message}`);
        }

        this.printResults();
    }

    printResults() {
        console.log('\n📊 Visual Validation Results:');
        console.log('==============================');
        
        console.log(`📸 Screenshots taken: ${this.testResults.screenshots.length}`);
        this.testResults.screenshots.forEach((screenshot, index) => {
            console.log(`   ${index + 1}. ${screenshot.stage}: ${screenshot.description}`);
        });
        
        console.log(`\n🎱 Ball Status:`);
        console.log(`   Visible: ${this.testResults.ballVisible ? '✅' : '❌'}`);
        console.log(`   Moving: ${this.testResults.ballMoving ? '✅' : '❌'}`);
        console.log(`   Winner Detected: ${this.testResults.winnerDetected ? '✅' : '❌'}`);
        
        if (this.testResults.issues.length > 0) {
            console.log(`\n❌ Issues Found (${this.testResults.issues.length}):`);
            this.testResults.issues.forEach((issue, index) => {
                console.log(`   ${index + 1}. ${issue}`);
            });
        }
        
        console.log(`\n📁 Screenshots saved to: ${this.screenshotDir}`);
        
        // Recommendations
        console.log('\n🔧 Recommendations:');
        if (!this.testResults.ballVisible) {
            console.log('   • Fix ball rendering - ball should be visible on the wheel');
        }
        if (!this.testResults.ballMoving) {
            console.log('   • Fix ball physics - ball should fall when gravity is enabled');
        }
        if (!this.testResults.winnerDetected) {
            console.log('   • Fix winner detection - should detect winner after ball settles');
        }
        if (this.testResults.ballVisible && this.testResults.ballMoving && this.testResults.winnerDetected) {
            console.log('   🎉 All visual validation tests passed!');
        }
    }

    async cleanup() {
        if (this.browser) {
            await this.browser.close();
            console.log('\n🧹 Browser closed');
        }
    }
}

// Main execution
async function main() {
    const port = process.argv[2] ? parseInt(process.argv[2]) : DEFAULT_PORT;
    
    if (isNaN(port) || port < 1000 || port > 65535) {
        console.error('❌ Invalid port number. Please provide a port between 1000 and 65535.');
        process.exit(1);
    }

    const validator = new WheelVisualValidator(port);

    try {
        await validator.init();
        await validator.runVisualValidation();
    } catch (error) {
        console.error('❌ Visual validation failed:', error.message);
        process.exit(1);
    } finally {
        await validator.cleanup();
    }
}

// Handle process termination
process.on('SIGINT', async () => {
    console.log('\n⚠️  Validation interrupted by user');
    process.exit(0);
});

if (require.main === module) {
    main().catch(console.error);
}

module.exports = { WheelVisualValidator };
