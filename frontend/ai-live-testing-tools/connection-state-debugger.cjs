#!/usr/bin/env node

/**
 * Connection State Debugger - Check the actual connection state in the frontend
 */

const { chromium } = require('playwright');

async function debugConnectionState() {
    console.log('🔍 Starting connection state debug...');
    
    const browser = await chromium.launch({ headless: false });
    const page = await browser.newPage();
    
    // Monitor console for connection-related messages
    page.on('console', msg => {
        const text = msg.text();
        if (text.includes('WebSocket') || text.includes('Connected') || text.includes('Demo') || text.includes('debug mode') || text.includes('wsConnected')) {
            console.log(`🖥️  [${msg.type().toUpperCase()}] ${text}`);
        }
    });
    
    // Load page
    console.log('🌐 Loading page...');
    await page.goto('http://localhost:3000/');
    await page.waitForTimeout(10000); // Wait for full initialization
    
    // Check the actual state of the app-shell component
    const appState = await page.evaluate(() => {
        const appShell = document.querySelector('app-shell');
        if (!appShell) return { error: 'app-shell not found' };
        
        // Access the internal state (this is a hack but useful for debugging)
        const shadowRoot = appShell.shadowRoot;
        const statusIndicator = shadowRoot?.querySelector('.status-indicator');
        const statusText = shadowRoot?.querySelector('.connection-status span');
        const chatInterface = shadowRoot?.querySelector('chat-interface');
        
        return {
            // Try to access internal properties (may not work due to encapsulation)
            wsConnected: appShell.wsConnected || 'unknown',
            isLoading: appShell.isLoading || 'unknown',
            currentMode: appShell.currentMode || 'unknown',
            
            // Check DOM elements
            statusIndicatorClass: statusIndicator?.className || 'not found',
            statusText: statusText?.textContent || 'not found',
            chatInterfaceExists: !!chatInterface,
            chatConnectionStatus: chatInterface?.connectionStatus || 'unknown',
            
            // Check for demo mode indicators
            demoModeText: document.body.textContent.includes('Demo Mode'),
            debugModeText: document.body.textContent.includes('DEBUG'),
            
            // Check for chat elements
            textareaExists: !!shadowRoot?.querySelector('textarea') || !!document.querySelector('textarea'),
            formExists: !!shadowRoot?.querySelector('form') || !!document.querySelector('form'),
            
            // Check WebSocket manager state
            websocketManagerState: window.__GOALI_WS__ ? {
                readyState: window.__GOALI_WS__.readyState,
                url: window.__GOALI_WS__.url
            } : 'not available'
        };
    });
    
    console.log('\n📊 CONNECTION STATE ANALYSIS:');
    console.log('════════════════════════════════════════════════════════════');
    
    console.log('\n🔗 App Shell State:');
    console.log(`  wsConnected: ${appState.wsConnected}`);
    console.log(`  isLoading: ${appState.isLoading}`);
    console.log(`  currentMode: ${appState.currentMode}`);
    
    console.log('\n🎨 UI Elements:');
    console.log(`  Status Indicator Class: ${appState.statusIndicatorClass}`);
    console.log(`  Status Text: ${appState.statusText}`);
    console.log(`  Chat Interface Exists: ${appState.chatInterfaceExists}`);
    console.log(`  Chat Connection Status: ${appState.chatConnectionStatus}`);
    
    console.log('\n📝 Form Elements:');
    console.log(`  Textarea Exists: ${appState.textareaExists}`);
    console.log(`  Form Exists: ${appState.formExists}`);
    
    console.log('\n🎭 Mode Indicators:');
    console.log(`  Demo Mode Text: ${appState.demoModeText ? '❌ YES' : '✅ NO'}`);
    console.log(`  Debug Mode Text: ${appState.debugModeText ? '✅ YES' : '❌ NO'}`);
    
    console.log('\n🔌 WebSocket State:');
    if (appState.websocketManagerState !== 'not available') {
        console.log(`  Ready State: ${appState.websocketManagerState.readyState} (${getReadyStateText(appState.websocketManagerState.readyState)})`);
        console.log(`  URL: ${appState.websocketManagerState.url}`);
    } else {
        console.log(`  WebSocket Manager: ${appState.websocketManagerState}`);
    }
    
    console.log('\n🔍 DIAGNOSIS:');
    if (appState.wsConnected === true && appState.statusText === 'Demo Mode') {
        console.log('❌ INCONSISTENCY: wsConnected=true but UI shows Demo Mode');
        console.log('💡 This indicates a UI rendering issue - state is correct but UI not updating');
    } else if (appState.wsConnected === false && appState.statusText === 'Demo Mode') {
        console.log('✅ CONSISTENT: wsConnected=false and UI shows Demo Mode');
        console.log('💡 Connection state is correctly reflected in UI');
    } else if (appState.wsConnected === true && appState.statusText === 'Connected') {
        console.log('✅ PERFECT: wsConnected=true and UI shows Connected');
        console.log('💡 Everything working correctly');
    } else {
        console.log('❓ UNKNOWN STATE: Need to investigate further');
    }
    
    if (!appState.textareaExists) {
        console.log('❌ No textarea found - chat interface not rendered');
    }
    
    await browser.close();
}

function getReadyStateText(readyState) {
    switch (readyState) {
        case 0: return 'CONNECTING';
        case 1: return 'OPEN';
        case 2: return 'CLOSING';
        case 3: return 'CLOSED';
        default: return 'UNKNOWN';
    }
}

debugConnectionState().catch(console.error);
