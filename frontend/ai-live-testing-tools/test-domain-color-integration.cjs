/**
 * Test Domain Color Integration
 * Verifies that the domainColorService is properly integrated with wheel display components
 */

const { chromium } = require('playwright');

async function testDomainColorIntegration() {
  console.log('🎨 Testing Domain Color Integration');
  console.log('============================================================');

  let browser;
  let page;

  try {
    // Launch browser
    browser = await chromium.launch({ headless: false });
    page = await browser.newPage();

    // Navigate to the app
    console.log('🌐 Navigating to app...');
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle' });

    // Wait for app to load
    await page.waitForTimeout(3000);

    // Test 1: Check if domainColorService is available
    console.log('\n🔍 Test 1: Domain Color Service Availability');
    const domainColorServiceAvailable = await page.evaluate(() => {
      // Check if the service functions are available in the global scope or modules
      return typeof window !== 'undefined';
    });
    console.log(`✅ Browser environment ready: ${domainColorServiceAvailable}`);

    // Test 2: Check for wheel component
    console.log('\n🎡 Test 2: Wheel Component Detection');
    const wheelComponent = await page.locator('game-wheel').first();
    const wheelExists = await wheelComponent.count() > 0;
    console.log(`${wheelExists ? '✅' : '❌'} Wheel component found: ${wheelExists}`);

    // Test 3: Check for activity list
    console.log('\n📋 Test 3: Activity List Detection');
    const activityList = await page.locator('.activity-list').first();
    const activityListExists = await activityList.count() > 0;
    console.log(`${activityListExists ? '✅' : '❌'} Activity list found: ${activityListExists}`);

    // Test 4: Check for color dots in activity items
    console.log('\n🎨 Test 4: Activity Color Dots');
    const colorDots = await page.locator('.activity-color-dot').count();
    console.log(`${colorDots > 0 ? '✅' : '❌'} Color dots found: ${colorDots}`);

    if (colorDots > 0) {
      // Check if color dots have actual colors (not default)
      const colorDotStyles = await page.evaluate(() => {
        const dots = document.querySelectorAll('.activity-color-dot');
        return Array.from(dots).map(dot => {
          const style = window.getComputedStyle(dot);
          return {
            backgroundColor: style.backgroundColor,
            hasColor: style.backgroundColor !== 'rgba(0, 0, 0, 0)' && style.backgroundColor !== 'transparent'
          };
        });
      });

      const coloredDots = colorDotStyles.filter(dot => dot.hasColor).length;
      console.log(`   ✅ Colored dots: ${coloredDots}/${colorDots}`);
      
      if (coloredDots > 0) {
        console.log('   🎨 Sample colors:');
        colorDotStyles.slice(0, 3).forEach((dot, index) => {
          if (dot.hasColor) {
            console.log(`      ${index + 1}. ${dot.backgroundColor}`);
          }
        });
      }
    }

    // Test 5: Check for domain badges
    console.log('\n🏷️  Test 5: Domain Badges');
    const domainBadges = await page.locator('.domain-badge').count();
    console.log(`${domainBadges > 0 ? '✅' : '❌'} Domain badges found: ${domainBadges}`);

    // Test 6: Try to trigger wheel generation (if possible)
    console.log('\n🎯 Test 6: Wheel Generation Test');
    try {
      // Look for generate wheel button
      const generateButton = await page.locator('button:has-text("Generate"), button:has-text("Create")').first();
      const generateButtonExists = await generateButton.count() > 0;
      
      if (generateButtonExists) {
        console.log('✅ Generate button found, attempting to trigger wheel generation...');
        await generateButton.click();
        await page.waitForTimeout(2000);
        
        // Check if wheel segments appeared
        const wheelSegments = await page.evaluate(() => {
          const wheelComponent = document.querySelector('game-wheel');
          if (wheelComponent && wheelComponent.wheelData) {
            return wheelComponent.wheelData.segments ? wheelComponent.wheelData.segments.length : 0;
          }
          return 0;
        });
        
        console.log(`${wheelSegments > 0 ? '✅' : '⚠️'} Wheel segments generated: ${wheelSegments}`);
        
        if (wheelSegments > 0) {
          // Check if segments have colors
          const segmentColors = await page.evaluate(() => {
            const wheelComponent = document.querySelector('game-wheel');
            if (wheelComponent && wheelComponent.wheelData && wheelComponent.wheelData.segments) {
              return wheelComponent.wheelData.segments.map(segment => ({
                name: segment.text || segment.name,
                color: segment.color,
                domain: segment.domain
              }));
            }
            return [];
          });
          
          console.log('   🎨 Segment colors:');
          segmentColors.slice(0, 5).forEach((segment, index) => {
            console.log(`      ${index + 1}. ${segment.name}: ${segment.color} (${segment.domain})`);
          });
        }
      } else {
        console.log('⚠️ No generate button found - may need user authentication or specific state');
      }
    } catch (error) {
      console.log(`⚠️ Wheel generation test failed: ${error.message}`);
    }

    // Test 7: Check console for color-related logs
    console.log('\n📝 Test 7: Console Logs Analysis');
    const consoleLogs = [];
    page.on('console', msg => {
      if (msg.text().includes('color') || msg.text().includes('domain') || msg.text().includes('🎨')) {
        consoleLogs.push(msg.text());
      }
    });

    // Wait a bit to collect logs
    await page.waitForTimeout(1000);
    
    if (consoleLogs.length > 0) {
      console.log('✅ Color-related console logs found:');
      consoleLogs.slice(0, 3).forEach(log => {
        console.log(`   📝 ${log.substring(0, 100)}...`);
      });
    } else {
      console.log('⚠️ No color-related console logs detected');
    }

    console.log('\n============================================================');
    console.log('🏁 DOMAIN COLOR INTEGRATION TEST RESULTS');
    console.log('============================================================');
    
    const results = {
      wheelComponent: wheelExists,
      activityList: activityListExists,
      colorDots: colorDots > 0,
      domainBadges: domainBadges > 0,
      overallSuccess: wheelExists && activityListExists && (colorDots > 0 || domainBadges > 0)
    };

    console.log(`🎡 Wheel Component: ${results.wheelComponent ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`📋 Activity List: ${results.activityList ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`🎨 Color Dots: ${results.colorDots ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`🏷️ Domain Badges: ${results.domainBadges ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`\n🎯 Overall Result: ${results.overallSuccess ? '✅ INTEGRATION SUCCESSFUL' : '❌ INTEGRATION NEEDS WORK'}`);

    if (results.overallSuccess) {
      console.log('\n🎉 Domain color integration is working correctly!');
      console.log('   - Wheel components are present');
      console.log('   - Activity lists are displaying');
      console.log('   - Color elements are being rendered');
    } else {
      console.log('\n🔧 Issues found that need attention:');
      if (!results.wheelComponent) console.log('   - Wheel component not found');
      if (!results.activityList) console.log('   - Activity list not found');
      if (!results.colorDots) console.log('   - Color dots not found');
      if (!results.domainBadges) console.log('   - Domain badges not found');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Run the test
testDomainColorIntegration().catch(console.error);
