#!/usr/bin/env node

/**
 * Comprehensive Wheel Debug Test
 * Tests the complete wheel generation flow from backend to frontend
 */

const { chromium } = require('playwright');

class ComprehensiveWheelDebugger {
  constructor() {
    this.browser = null;
    this.page = null;
    this.receivedMessages = [];
  }

  async initialize() {
    console.log('🔍 Initializing Comprehensive Wheel Debugger...');
    
    this.browser = await chromium.launch({
      headless: false,
      devtools: true
    });
    
    this.page = await this.browser.newPage();
    
    // Enable console logging
    this.page.on('console', msg => {
      const type = msg.type();
      const text = msg.text();
      if (text.includes('wheel') || text.includes('WHEEL') || type === 'error') {
        console.log(`🖥️  [${type}] ${text}`);
      }
    });
    
    // Enable error logging
    this.page.on('pageerror', error => {
      console.log(`❌ Page error: ${error.message}`);
    });
    
    console.log('✅ Debugger initialized');
  }

  async loadFrontend() {
    console.log('🌐 Loading frontend...');
    
    await this.page.goto('http://localhost:3000', { 
      waitUntil: 'networkidle',
      timeout: 30000 
    });
    
    // Wait for app to initialize
    await this.page.waitForTimeout(3000);
    
    console.log('✅ Frontend loaded');
  }

  async setupWebSocketMonitoring() {
    console.log('🔌 Setting up WebSocket monitoring...');
    
    await this.page.addInitScript(() => {
      window.wheelDebugData = {
        messages: [],
        wheelDataReceived: false,
        appShellCalls: [],
        gameWheelUpdates: []
      };
      
      // Monitor WebSocket messages
      const originalWebSocket = window.WebSocket;
      window.WebSocket = function(url, protocols) {
        const ws = new originalWebSocket(url, protocols);
        
        const originalOnMessage = ws.onmessage;
        ws.onmessage = function(event) {
          try {
            const data = JSON.parse(event.data);
            window.wheelDebugData.messages.push({
              timestamp: new Date().toISOString(),
              type: data.type,
              data: data
            });
            
            if (data.type === 'wheel_data') {
              console.log('🎡 WHEEL_DATA MESSAGE RECEIVED!');
              window.wheelDebugData.wheelDataReceived = true;
              console.log('🎡 Message structure:', {
                hasWheel: !!data.wheel,
                hasItems: !!(data.wheel && data.wheel.items),
                itemCount: data.wheel && data.wheel.items ? data.wheel.items.length : 0,
                wheelKeys: data.wheel ? Object.keys(data.wheel) : []
              });
            }
          } catch (e) {
            console.log('📨 Failed to parse WebSocket message:', e);
          }
          
          if (originalOnMessage) {
            originalOnMessage.call(this, event);
          }
        };
        
        return ws;
      };
    });
    
    console.log('✅ WebSocket monitoring setup');
  }

  async sendWheelRequest() {
    console.log('💬 Sending wheel generation request...');
    
    // Find and fill chat input
    const chatInput = await this.page.locator('textarea, input[type="text"]').first();
    await chatInput.waitFor({ timeout: 10000 });
    
    await chatInput.fill("hey, I'm feeling energetic and I have 2h free ahead of me. It's very hot outside though. Generate me the perfect wheel !");
    await chatInput.press('Enter');
    
    console.log('✅ Message sent');
  }

  async waitForWheelData(timeoutMs = 120000) {
    console.log('⏳ Waiting for wheel data...');
    
    try {
      await this.page.waitForFunction(() => {
        return window.wheelDebugData && window.wheelDebugData.wheelDataReceived;
      }, { timeout: timeoutMs });
      
      console.log('✅ Wheel data received');
      return true;
    } catch (error) {
      console.log('❌ Timeout waiting for wheel data');
      return false;
    }
  }

  async analyzeResults() {
    console.log('🔍 Analyzing results...');
    
    const analysis = await this.page.evaluate(() => {
      const debugData = window.wheelDebugData || {};
      
      // Check messages
      const wheelMessages = debugData.messages.filter(msg => msg.type === 'wheel_data');
      
      // Check app-shell state
      const appShell = document.querySelector('app-shell');
      const appShellState = appShell ? {
        hasWheelData: !!appShell.wheelData,
        wheelDataSegments: appShell.wheelData && appShell.wheelData.segments ? appShell.wheelData.segments.length : 0
      } : null;
      
      // Check game-wheel component
      const gameWheel = document.querySelector('game-wheel');
      const gameWheelState = gameWheel ? {
        hasWheelData: !!gameWheel.wheelData,
        segmentCount: gameWheel.segments ? gameWheel.segments.length : 0,
        isVisible: gameWheel.offsetParent !== null
      } : null;
      
      // Check wheel container
      const wheelContainer = document.querySelector('.wheel-container');
      const wheelPlaceholder = document.querySelector('.wheel-placeholder');
      
      return {
        websocketMessages: {
          total: debugData.messages.length,
          wheelDataCount: wheelMessages.length,
          latestWheelMessage: wheelMessages.length > 0 ? wheelMessages[wheelMessages.length - 1] : null
        },
        appShell: appShellState,
        gameWheel: gameWheelState,
        dom: {
          hasWheelContainer: !!wheelContainer,
          hasWheelPlaceholder: !!wheelPlaceholder,
          wheelPlaceholderVisible: wheelPlaceholder ? wheelPlaceholder.offsetParent !== null : false
        }
      };
    });
    
    console.log('📊 ANALYSIS RESULTS:');
    console.log(JSON.stringify(analysis, null, 2));
    
    return analysis;
  }

  async checkBackendLogs() {
    console.log('📋 Checking backend logs...');
    
    // This would need to be implemented to check Docker logs
    // For now, just log that we should check manually
    console.log('💡 Check backend logs with: docker logs backend-celery-1 --tail 20');
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
    }
  }

  async runFullDebugSession() {
    try {
      await this.initialize();
      await this.setupWebSocketMonitoring();
      await this.loadFrontend();
      
      // Wait for connection
      await this.page.waitForTimeout(3000);
      
      await this.sendWheelRequest();
      
      const wheelDataReceived = await this.waitForWheelData();
      
      // Wait a bit more for processing
      await this.page.waitForTimeout(5000);
      
      const analysis = await this.analyzeResults();
      
      // Provide diagnosis
      console.log('\n🎯 DIAGNOSIS:');
      
      if (analysis.websocketMessages.wheelDataCount === 0) {
        console.log('❌ No wheel_data messages received - WebSocket or backend issue');
      } else if (!analysis.appShell || !analysis.appShell.hasWheelData) {
        console.log('❌ Wheel data received but not processed by app-shell');
      } else if (!analysis.gameWheel || !analysis.gameWheel.hasWheelData) {
        console.log('❌ App-shell has wheel data but game-wheel component not updated');
      } else if (analysis.dom.wheelPlaceholderVisible) {
        console.log('❌ Game-wheel has data but placeholder still visible - rendering issue');
      } else {
        console.log('✅ Wheel generation appears to be working correctly');
      }
      
      await this.checkBackendLogs();
      
      console.log('\n🔍 Debug session completed. Browser kept open for manual inspection.');
      console.log('Press Ctrl+C to exit.');
      
      // Keep browser open for manual inspection
      await new Promise(() => {});
      
    } catch (error) {
      console.error('❌ Debug session failed:', error);
    } finally {
      // Don't auto-cleanup to allow manual inspection
    }
  }
}

// Run the debugger
const wheelDebugger = new ComprehensiveWheelDebugger();
wheelDebugger.runFullDebugSession().catch(console.error);
