#!/usr/bin/env node

/**
 * Frontend test for wheel item management features
 * 
 * This test validates:
 * - Remove activity button functionality
 * - Add activity button and modal
 * - Feedback modal functionality
 * - Wheel redraw after modifications
 * 
 * Usage: node test-wheel-item-management.cjs [port]
 */

const { chromium } = require('playwright');

async function testWheelItemManagement(port = 5173) {
  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 1000 // Slow down for visibility
  });
  
  const context = await browser.newContext();
  const page = await context.newPage();
  
  console.log('🚀 Starting Wheel Item Management Test');
  console.log('=' + '='.repeat(50));
  
  try {
    // Navigate to the app
    console.log(`📱 Opening app at http://localhost:${port}`);
    await page.goto(`http://localhost:${port}`);
    await page.waitForLoadState('networkidle');
    
    // Wait for app to load
    await page.waitForSelector('app-shell', { timeout: 10000 });
    console.log('✅ App loaded successfully');
    
    // Check if we need to login
    const loginForm = await page.locator('login-form').first();
    if (await loginForm.isVisible()) {
      console.log('🔐 Login required, using demo mode...');
      
      // Click demo mode button
      const demoButton = await page.locator('button:has-text("Demo Mode")').first();
      if (await demoButton.isVisible()) {
        await demoButton.click();
        await page.waitForTimeout(2000);
        console.log('✅ Demo mode activated');
      } else {
        console.log('❌ Demo mode button not found');
        return false;
      }
    }
    
    // Open debug panel if available
    const debugButton = await page.locator('button:has-text("Debug")').first();
    if (await debugButton.isVisible()) {
      console.log('🐛 Opening debug panel...');
      await debugButton.click();
      await page.waitForTimeout(1000);
      
      // Select PhiPhi user
      const userSelect = await page.locator('select[data-testid="debug-user-select"]').first();
      if (await userSelect.isVisible()) {
        await userSelect.selectOption('2'); // PhiPhi user ID
        console.log('✅ Selected PhiPhi user');
        await page.waitForTimeout(1000);
      }
      
      // Apply debug settings
      const applyButton = await page.locator('button:has-text("Apply")').first();
      if (await applyButton.isVisible()) {
        await applyButton.click();
        await page.waitForTimeout(2000);
        console.log('✅ Debug settings applied');
      }
    }
    
    // Generate a wheel first
    console.log('🎡 Generating wheel...');
    const generateButton = await page.locator('button:has-text("Generate")').first();
    if (await generateButton.isVisible()) {
      await generateButton.click();
      console.log('⏳ Waiting for wheel generation...');
      
      // Wait for wheel to appear (up to 60 seconds)
      await page.waitForSelector('.activity-list', { timeout: 60000 });
      console.log('✅ Wheel generated successfully');
    } else {
      // Try to load mocked wheel if generate button not available
      console.log('🎭 Loading mocked wheel data...');
      await page.evaluate(() => {
        window.dispatchEvent(new CustomEvent('load-mocked-wheel'));
      });
      await page.waitForTimeout(3000);
    }
    
    // Test 1: Check if activity list is visible
    console.log('\n📋 Test 1: Activity List Visibility');
    const activityList = await page.locator('.activity-list').first();
    if (await activityList.isVisible()) {
      const activityCount = await page.locator('.activity-item').count();
      console.log(`✅ Activity list visible with ${activityCount} activities`);
    } else {
      console.log('❌ Activity list not visible');
      return false;
    }
    
    // Test 2: Check for add activity button
    console.log('\n➕ Test 2: Add Activity Button');
    const addButton = await page.locator('.add-activity-btn').first();
    if (await addButton.isVisible()) {
      console.log('✅ Add activity button visible');
      
      // Click add button to open modal
      await addButton.click();
      await page.waitForTimeout(1000);
      
      // Check if add activity modal opened
      const addModal = await page.locator('.modal:has(.modal-title:has-text("Add Activity"))').first();
      if (await addModal.isVisible()) {
        console.log('✅ Add activity modal opened');
        
        // Test search functionality
        const searchInput = await addModal.locator('.search-input').first();
        if (await searchInput.isVisible()) {
          await searchInput.fill('meditation');
          await page.waitForTimeout(1000);
          console.log('✅ Search input working');
        }
        
        // Close modal
        const closeButton = await addModal.locator('.modal-close').first();
        await closeButton.click();
        await page.waitForTimeout(500);
        console.log('✅ Add activity modal closed');
      } else {
        console.log('❌ Add activity modal did not open');
      }
    } else {
      console.log('❌ Add activity button not visible');
    }
    
    // Test 3: Check for remove activity buttons
    console.log('\n❌ Test 3: Remove Activity Buttons');
    const removeButtons = await page.locator('.remove-activity-btn');
    const removeButtonCount = await removeButtons.count();
    
    if (removeButtonCount > 0) {
      console.log(`✅ Found ${removeButtonCount} remove buttons`);
      
      // Click first remove button to test feedback modal
      const firstRemoveButton = removeButtons.first();
      await firstRemoveButton.click();
      await page.waitForTimeout(1000);
      
      // Check if feedback modal opened
      const feedbackModal = await page.locator('.modal:has(.modal-title:has-text("Don\'t like"))').first();
      if (await feedbackModal.isVisible()) {
        console.log('✅ Feedback modal opened');
        
        // Test feedback form
        const feedbackTextarea = await feedbackModal.locator('#feedback-comment').first();
        if (await feedbackTextarea.isVisible()) {
          await feedbackTextarea.fill('This activity is not suitable for my current mood');
          console.log('✅ Feedback form working');
        }
        
        // Close modal without submitting
        const cancelButton = await feedbackModal.locator('button:has-text("Cancel")').first();
        await cancelButton.click();
        await page.waitForTimeout(500);
        console.log('✅ Feedback modal closed');
      } else {
        console.log('❌ Feedback modal did not open');
      }
    } else {
      console.log('❌ No remove buttons found');
    }
    
    // Test 4: Check activity expansion
    console.log('\n📖 Test 4: Activity Expansion');
    const firstActivity = await page.locator('.activity-item').first();
    if (await firstActivity.isVisible()) {
      // Click to expand
      const activityHeader = await firstActivity.locator('.activity-item-header').first();
      await activityHeader.click();
      await page.waitForTimeout(500);
      
      // Check if expanded
      const isExpanded = await firstActivity.evaluate(el => el.classList.contains('expanded'));
      if (isExpanded) {
        console.log('✅ Activity expansion working');
        
        // Check that "Change" button is not present (as requested)
        const changeButton = await firstActivity.locator('button:has-text("Change")').first();
        const changeButtonVisible = await changeButton.isVisible().catch(() => false);
        if (!changeButtonVisible) {
          console.log('✅ Change button correctly removed');
        } else {
          console.log('❌ Change button still present (should be removed)');
        }
      } else {
        console.log('❌ Activity expansion not working');
      }
    }
    
    // Test 5: Wheel component interaction
    console.log('\n🎡 Test 5: Wheel Component');
    const wheelComponent = await page.locator('game-wheel').first();
    if (await wheelComponent.isVisible()) {
      console.log('✅ Wheel component visible');
      
      // Check if wheel has segments
      const wheelSegments = await page.evaluate(() => {
        const wheel = document.querySelector('game-wheel');
        return wheel && wheel.wheelData && wheel.wheelData.segments ? wheel.wheelData.segments.length : 0;
      });
      
      if (wheelSegments > 0) {
        console.log(`✅ Wheel has ${wheelSegments} segments`);
      } else {
        console.log('❌ Wheel has no segments');
      }
    } else {
      console.log('❌ Wheel component not visible');
    }
    
    // Test 6: UI Responsiveness
    console.log('\n📱 Test 6: UI Responsiveness');
    
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(1000);
    
    const activityListMobile = await page.locator('.activity-list').first();
    if (await activityListMobile.isVisible()) {
      console.log('✅ Activity list responsive on mobile');
    } else {
      console.log('❌ Activity list not responsive on mobile');
    }
    
    // Reset to desktop viewport
    await page.setViewportSize({ width: 1200, height: 800 });
    await page.waitForTimeout(1000);
    
    console.log('\n' + '='.repeat(50));
    console.log('🏁 Wheel Item Management Test Complete');
    console.log('✅ All major features tested successfully');
    
    // Keep browser open for manual inspection
    console.log('\n🔍 Browser will remain open for manual inspection...');
    console.log('Press Ctrl+C to close when done.');
    
    // Wait indefinitely until user closes
    await new Promise(() => {});
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
    return false;
  } finally {
    // Don't close browser automatically for inspection
    // await browser.close();
  }
}

// Get port from command line argument
const port = process.argv[2] ? parseInt(process.argv[2]) : 5173;

// Run the test
testWheelItemManagement(port).then(success => {
  if (!success) {
    process.exit(1);
  }
}).catch(error => {
  console.error('Test runner failed:', error);
  process.exit(1);
});
