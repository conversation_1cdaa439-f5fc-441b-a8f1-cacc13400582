#!/usr/bin/env node

/**
 * Simple debug test to isolate the "undefined" issue
 */

const colors = {
  reset: '\x1b[0m',
  gray: '\x1b[90m'
};

// Test 1: Simple console.log
console.log('Test 1: Simple console.log');
console.log(`${colors.gray}🔧 Debug: Test message${colors.reset}`);

// Test 2: Variable assignment
console.log('\nTest 2: Variable assignment');
const debugMessage = 'Test message';
console.log(`${colors.gray}🔧 Debug: ${debugMessage}${colors.reset}`);

// Test 3: Function return
console.log('\nTest 3: Function return');
function getDebugMessage() {
  return 'Test message';
}
console.log(`${colors.gray}🔧 Debug: ${getDebugMessage()}${colors.reset}`);

// Test 4: Potential issue - nested console.log
console.log('\nTest 4: Potential issue - nested console.log');
// This would cause the issue:
// console.log(console.log(`${colors.gray}🔧 Debug: Test message${colors.reset}`));

// Test 5: Check if colors.gray is undefined
console.log('\nTest 5: Check colors.gray');
console.log('colors.gray:', colors.gray);
console.log('typeof colors.gray:', typeof colors.gray);

// Test 6: Simulate the exact pattern from the user story simulator
console.log('\nTest 6: Simulate exact pattern');
const message = {
  content: {
    message: 'Message processed by MentorService',
    source: 'MentorService',
    level: 'info'
  }
};

let debugMessage2 = 'Unknown debug message';
if (message.content && typeof message.content === 'object') {
  if (message.content.message && message.content.message !== 'undefined') {
    debugMessage2 = message.content.message;
  }
}

console.log(`${colors.gray}🔧 Debug: ${debugMessage2}${colors.reset}`);
