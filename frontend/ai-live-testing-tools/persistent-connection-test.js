#!/usr/bin/env node

/**
 * Persistent Connection Test
 * 
 * Test that keeps connections alive and monitors dashboard data in real-time
 */

import WebSocket from 'ws';

console.log('🔗 Persistent Connection Test');
console.log('==============================\n');

let adminSocket = null;
let clientSockets = [];
let isRunning = true;

async function connectAdmin() {
  console.log('🔌 Connecting to admin dashboard...');
  
  return new Promise((resolve, reject) => {
    adminSocket = new WebSocket('ws://localhost:8000/ws/connection-monitor/');
    
    const timeout = setTimeout(() => {
      reject(new Error('Admin connection timeout'));
    }, 10000);

    adminSocket.on('open', () => {
      clearTimeout(timeout);
      console.log('✅ Admin dashboard connected');
      resolve();
    });

    adminSocket.on('error', (error) => {
      clearTimeout(timeout);
      console.log(`❌ Admin connection failed: ${error.message}`);
      reject(error);
    });

    adminSocket.on('message', (data) => {
      try {
        const message = JSON.parse(data);
        console.log(`📊 Admin received: ${message.type}`);
        
        if (message.type === 'connection_data') {
          console.log(`   📋 Connections: ${message.data.length}`);
          if (message.data.length > 0) {
            console.log('   📝 Connection details:');
            message.data.forEach((conn, index) => {
              console.log(`     ${index + 1}. User: ${conn.user_id}, Session: ${conn.session_id}, Status: ${conn.status}`);
            });
          }
        } else if (message.type === 'message_stats') {
          console.log(`   📈 Messages/min: ${message.data.messages_per_minute}`);
        } else if (message.type === 'system_health') {
          console.log(`   🏥 System health: Redis=${message.data.redis_status}, Celery=${message.data.celery_status}`);
        }
      } catch (error) {
        console.log(`❌ Failed to parse admin message: ${error.message}`);
      }
    });

    adminSocket.on('close', (code, reason) => {
      console.log(`🔌 Admin connection closed: ${code} ${reason}`);
    });
  });
}

async function createPersistentClient(clientId) {
  console.log(`🔗 Creating persistent client ${clientId}...`);
  
  return new Promise((resolve, reject) => {
    const socket = new WebSocket('ws://localhost:8000/ws/game/');
    
    const timeout = setTimeout(() => {
      reject(new Error(`Client ${clientId} connection timeout`));
    }, 5000);

    socket.on('open', () => {
      clearTimeout(timeout);
      console.log(`✅ Client ${clientId} connected`);
      
      // Send identification message
      const message = {
        type: 'chat_message',
        content: {
          message: `Hello from persistent client ${clientId}`,
          user_profile_id: `persistent-user-${clientId}`,
          timestamp: new Date().toISOString()
        }
      };
      
      socket.send(JSON.stringify(message));
      console.log(`📤 Client ${clientId} sent identification message`);
      
      // Store the socket
      clientSockets.push({ id: clientId, socket });
      resolve();
    });

    socket.on('error', (error) => {
      clearTimeout(timeout);
      console.log(`❌ Client ${clientId} error: ${error.message}`);
      reject(error);
    });

    socket.on('message', (data) => {
      try {
        const message = JSON.parse(data);
        console.log(`📨 Client ${clientId} received: ${message.type}`);
      } catch (error) {
        console.log(`📨 Client ${clientId} received raw: ${data.toString().substring(0, 50)}...`);
      }
    });

    socket.on('close', (code, reason) => {
      console.log(`🔌 Client ${clientId} closed: ${code} ${reason}`);
      // Remove from active sockets
      clientSockets = clientSockets.filter(c => c.id !== clientId);
    });
  });
}

function requestDashboardData() {
  if (adminSocket && adminSocket.readyState === WebSocket.OPEN) {
    console.log('\n📊 Requesting dashboard data...');
    adminSocket.send(JSON.stringify({ type: 'get_connections' }));
    adminSocket.send(JSON.stringify({ type: 'get_message_stats' }));
    adminSocket.send(JSON.stringify({ type: 'get_system_health' }));
  }
}

function sendPeriodicMessages() {
  clientSockets.forEach(client => {
    if (client.socket.readyState === WebSocket.OPEN) {
      const message = {
        type: 'chat_message',
        content: {
          message: `Periodic message from client ${client.id}`,
          user_profile_id: `persistent-user-${client.id}`,
          timestamp: new Date().toISOString()
        }
      };
      
      try {
        client.socket.send(JSON.stringify(message));
        console.log(`📤 Client ${client.id} sent periodic message`);
      } catch (error) {
        console.log(`❌ Client ${client.id} failed to send message: ${error.message}`);
      }
    }
  });
}

async function main() {
  try {
    // Connect to admin dashboard
    await connectAdmin();
    
    // Create persistent clients
    console.log('\n🔗 Creating persistent clients...');
    for (let i = 1; i <= 3; i++) {
      await createPersistentClient(i);
      await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second between connections
    }
    
    console.log(`\n✅ Created ${clientSockets.length} persistent clients`);
    
    // Request initial dashboard data
    setTimeout(() => {
      requestDashboardData();
    }, 2000);
    
    // Set up periodic data requests
    const dataRequestInterval = setInterval(() => {
      if (isRunning) {
        requestDashboardData();
      } else {
        clearInterval(dataRequestInterval);
      }
    }, 10000); // Every 10 seconds
    
    // Set up periodic message sending
    const messageInterval = setInterval(() => {
      if (isRunning && clientSockets.length > 0) {
        sendPeriodicMessages();
      } else {
        clearInterval(messageInterval);
      }
    }, 15000); // Every 15 seconds
    
    console.log('\n🔄 Test running... Press Ctrl+C to stop');
    console.log('📊 Dashboard data will be requested every 10 seconds');
    console.log('📤 Clients will send messages every 15 seconds');
    
    // Keep the test running
    const keepAlive = setInterval(() => {
      if (!isRunning) {
        clearInterval(keepAlive);
        return;
      }
      
      console.log(`\n⏰ Status: ${clientSockets.length} active clients, admin: ${adminSocket?.readyState === WebSocket.OPEN ? 'connected' : 'disconnected'}`);
    }, 30000); // Status every 30 seconds
    
  } catch (error) {
    console.error(`❌ Test failed: ${error.message}`);
    cleanup();
  }
}

function cleanup() {
  console.log('\n🧹 Cleaning up...');
  isRunning = false;
  
  // Close all client sockets
  clientSockets.forEach(client => {
    if (client.socket && client.socket.readyState === WebSocket.OPEN) {
      client.socket.close();
    }
  });
  
  // Close admin socket
  if (adminSocket && adminSocket.readyState === WebSocket.OPEN) {
    adminSocket.close();
  }
  
  console.log('✅ Cleanup complete');
  process.exit(0);
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Received SIGINT, stopping test...');
  cleanup();
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Received SIGTERM, stopping test...');
  cleanup();
});

main();
