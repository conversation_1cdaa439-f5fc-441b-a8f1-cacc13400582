/**
 * Domain Color System Integration Test
 * 
 * This script tests the domain color system with diverse domain codes
 * from backend wheel data to ensure proper color mapping and visual distinction.
 */

console.log('🎨 Domain Color System Integration Test');
console.log('======================================');

// Import domain color service (assuming it's available globally or via import)
let domainColorService;
try {
    // Try to get from global scope or import
    if (typeof getDomainColor !== 'undefined') {
        domainColorService = {
            getDomainColor,
            getAllDomainColors: typeof getAllDomainColors !== 'undefined' ? getAllDomainColors : null,
            getDomainColorWithOpacity: typeof getDomainColorWithOpacity !== 'undefined' ? getDomainColorWithOpacity : null
        };
    } else {
        // Try to import from module
        import('../src/services/domainColorService.js').then(module => {
            domainColorService = module;
            runDomainColorTest();
        });
    }
} catch (error) {
    console.error('❌ Could not load domain color service:', error);
}

// Test domain codes from recent backend wheel generation
const TEST_DOMAIN_CODES = [
    'physical',
    'phys_strength', 
    'phys_cardio',
    'phys_dance',
    'productive_practical',
    'emotional',
    'refl_grat',
    'soc_comm',
    'soc_connecting',
    'creative',
    'creative_writing',
    'creative_music',
    'mental',
    'mental_learning',
    'wellness',
    'wellness_mindfulness',
    'general'
];

// Expected color properties
const COLOR_VALIDATION = {
    hexPattern: /^#[0-9A-Fa-f]{6}$/,
    minDistinctColors: 10,
    maxSimilarityThreshold: 0.3 // Colors should be sufficiently different
};

let testResults = {
    colorMappings: {},
    validColors: 0,
    distinctColors: 0,
    fallbacksUsed: 0,
    visualDistinction: true,
    errors: []
};

/**
 * Main test execution
 */
function runDomainColorTest() {
    if (!domainColorService) {
        console.error('❌ Domain color service not available');
        return;
    }
    
    console.log('🚀 Starting domain color system test...');
    
    try {
        // Test 1: Basic color mapping
        testBasicColorMapping();
        
        // Test 2: Fallback behavior
        testFallbackBehavior();
        
        // Test 3: Visual distinction
        testVisualDistinction();
        
        // Test 4: Color consistency
        testColorConsistency();
        
        // Test 5: Integration with wheel data
        testWheelDataIntegration();
        
        // Generate report
        generateColorTestReport();
        
    } catch (error) {
        console.error('❌ Domain color test failed:', error);
        testResults.errors.push(error.message);
        generateColorTestReport();
    }
}

/**
 * Test basic color mapping for all domain codes
 */
function testBasicColorMapping() {
    console.log('\n🎯 Test 1: Basic Color Mapping');
    
    TEST_DOMAIN_CODES.forEach(domainCode => {
        try {
            const color = domainColorService.getDomainColor(domainCode);
            testResults.colorMappings[domainCode] = color;
            
            // Validate color format
            if (COLOR_VALIDATION.hexPattern.test(color)) {
                testResults.validColors++;
                console.log(`✅ ${domainCode}: ${color}`);
            } else {
                console.log(`❌ ${domainCode}: Invalid color format: ${color}`);
                testResults.errors.push(`Invalid color format for ${domainCode}: ${color}`);
            }
        } catch (error) {
            console.log(`❌ ${domainCode}: Error getting color: ${error.message}`);
            testResults.errors.push(`Error getting color for ${domainCode}: ${error.message}`);
        }
    });
    
    console.log(`✅ Valid colors: ${testResults.validColors}/${TEST_DOMAIN_CODES.length}`);
}

/**
 * Test fallback behavior for unknown domains
 */
function testFallbackBehavior() {
    console.log('\n🔄 Test 2: Fallback Behavior');
    
    const unknownDomains = [
        'unknown_domain',
        'invalid_code',
        '',
        null,
        undefined
    ];
    
    unknownDomains.forEach(domainCode => {
        try {
            const color = domainColorService.getDomainColor(domainCode);
            console.log(`🔄 ${domainCode || 'null/undefined'}: ${color} (fallback)`);
            
            if (COLOR_VALIDATION.hexPattern.test(color)) {
                testResults.fallbacksUsed++;
            } else {
                testResults.errors.push(`Invalid fallback color for ${domainCode}: ${color}`);
            }
        } catch (error) {
            testResults.errors.push(`Fallback error for ${domainCode}: ${error.message}`);
        }
    });
    
    console.log(`✅ Fallbacks working: ${testResults.fallbacksUsed}/${unknownDomains.length}`);
}

/**
 * Test visual distinction between colors
 */
function testVisualDistinction() {
    console.log('\n👁️ Test 3: Visual Distinction');
    
    const colors = Object.values(testResults.colorMappings);
    const uniqueColors = new Set(colors);
    testResults.distinctColors = uniqueColors.size;
    
    console.log(`🌈 Unique colors: ${testResults.distinctColors}/${colors.length}`);
    
    // Check color similarity (simplified RGB distance)
    const colorPairs = [];
    const colorArray = Array.from(uniqueColors);
    
    for (let i = 0; i < colorArray.length; i++) {
        for (let j = i + 1; j < colorArray.length; j++) {
            const similarity = calculateColorSimilarity(colorArray[i], colorArray[j]);
            colorPairs.push({
                color1: colorArray[i],
                color2: colorArray[j],
                similarity
            });
        }
    }
    
    // Find similar colors
    const similarColors = colorPairs.filter(pair => pair.similarity > COLOR_VALIDATION.maxSimilarityThreshold);
    
    if (similarColors.length > 0) {
        console.log(`⚠️ Similar color pairs found: ${similarColors.length}`);
        similarColors.forEach(pair => {
            console.log(`  ${pair.color1} ↔ ${pair.color2} (similarity: ${pair.similarity.toFixed(2)})`);
        });
        testResults.visualDistinction = false;
    } else {
        console.log('✅ All colors are visually distinct');
    }
}

/**
 * Test color consistency (same domain always returns same color)
 */
function testColorConsistency() {
    console.log('\n🔒 Test 4: Color Consistency');
    
    let consistencyErrors = 0;
    
    TEST_DOMAIN_CODES.forEach(domainCode => {
        const color1 = domainColorService.getDomainColor(domainCode);
        const color2 = domainColorService.getDomainColor(domainCode);
        const color3 = domainColorService.getDomainColor(domainCode);
        
        if (color1 !== color2 || color2 !== color3) {
            console.log(`❌ ${domainCode}: Inconsistent colors: ${color1}, ${color2}, ${color3}`);
            consistencyErrors++;
        }
    });
    
    if (consistencyErrors === 0) {
        console.log('✅ All colors are consistent across multiple calls');
    } else {
        console.log(`❌ ${consistencyErrors} domains have inconsistent colors`);
        testResults.errors.push(`${consistencyErrors} domains have inconsistent colors`);
    }
}

/**
 * Test integration with actual wheel data format
 */
function testWheelDataIntegration() {
    console.log('\n🎡 Test 5: Wheel Data Integration');
    
    // Simulate wheel data from backend
    const mockWheelData = {
        segments: [
            { id: 'item1', name: 'Push-ups', domain: 'physical', percentage: 16.67 },
            { id: 'item2', name: 'Dance Break', domain: 'phys_dance', percentage: 16.67 },
            { id: 'item3', name: 'Breathing Exercise', domain: 'wellness_mindfulness', percentage: 16.67 },
            { id: 'item4', name: 'Journal Writing', domain: 'creative_writing', percentage: 16.67 },
            { id: 'item5', name: 'Friend Call', domain: 'soc_connecting', percentage: 16.67 },
            { id: 'item6', name: 'Learning Session', domain: 'mental_learning', percentage: 16.65 }
        ]
    };
    
    // Test color application
    let integrationSuccess = true;
    
    mockWheelData.segments.forEach(segment => {
        try {
            const color = domainColorService.getDomainColor(segment.domain);
            segment.color = color;
            console.log(`✅ ${segment.name} (${segment.domain}): ${color}`);
        } catch (error) {
            console.log(`❌ ${segment.name} (${segment.domain}): Error: ${error.message}`);
            integrationSuccess = false;
        }
    });
    
    if (integrationSuccess) {
        console.log('✅ Wheel data integration successful');
        
        // Test applyColorsToWheel function if available
        if (domainColorService.applyColorsToWheel) {
            try {
                const coloredWheel = domainColorService.applyColorsToWheel({...mockWheelData});
                console.log('✅ applyColorsToWheel function working');
            } catch (error) {
                console.log(`❌ applyColorsToWheel error: ${error.message}`);
                testResults.errors.push(`applyColorsToWheel error: ${error.message}`);
            }
        }
    } else {
        testResults.errors.push('Wheel data integration failed');
    }
}

/**
 * Calculate color similarity (simplified RGB distance)
 */
function calculateColorSimilarity(color1, color2) {
    const rgb1 = hexToRgb(color1);
    const rgb2 = hexToRgb(color2);
    
    if (!rgb1 || !rgb2) return 0;
    
    const rDiff = Math.abs(rgb1.r - rgb2.r);
    const gDiff = Math.abs(rgb1.g - rgb2.g);
    const bDiff = Math.abs(rgb1.b - rgb2.b);
    
    const maxDiff = 255 * 3; // Maximum possible difference
    const actualDiff = rDiff + gDiff + bDiff;
    
    return 1 - (actualDiff / maxDiff); // Similarity score (0 = completely different, 1 = identical)
}

/**
 * Convert hex color to RGB
 */
function hexToRgb(hex) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16)
    } : null;
}

/**
 * Generate comprehensive test report
 */
function generateColorTestReport() {
    console.log('\n📋 DOMAIN COLOR SYSTEM TEST REPORT');
    console.log('==================================');
    
    const results = testResults;
    
    // Basic metrics
    console.log('\n📊 Basic Metrics:');
    console.log(`  Valid Colors: ${results.validColors}/${TEST_DOMAIN_CODES.length}`);
    console.log(`  Distinct Colors: ${results.distinctColors}`);
    console.log(`  Fallbacks Working: ${results.fallbacksUsed}/5`);
    console.log(`  Visual Distinction: ${results.visualDistinction ? '✅' : '❌'}`);
    
    // Color mappings
    console.log('\n🎨 Color Mappings:');
    Object.entries(results.colorMappings).forEach(([domain, color]) => {
        console.log(`  ${domain}: ${color}`);
    });
    
    // Errors
    if (results.errors.length > 0) {
        console.log('\n❌ Errors:');
        results.errors.forEach(error => {
            console.log(`  - ${error}`);
        });
    }
    
    // Overall assessment
    const overallSuccess = results.validColors === TEST_DOMAIN_CODES.length &&
                          results.distinctColors >= COLOR_VALIDATION.minDistinctColors &&
                          results.visualDistinction &&
                          results.errors.length === 0;
    
    console.log('\n🎯 OVERALL RESULT:');
    console.log(`${overallSuccess ? '✅ DOMAIN COLOR SYSTEM TEST PASSED' : '❌ DOMAIN COLOR SYSTEM TEST FAILED'}`);
    
    return overallSuccess;
}

// Auto-run test when script is loaded
if (typeof window !== 'undefined' && domainColorService) {
    setTimeout(runDomainColorTest, 500);
}

// Export for manual execution
if (typeof window !== 'undefined') {
    window.runDomainColorTest = runDomainColorTest;
}
