#!/usr/bin/env node

/**
 * Browser UI Simulator
 *
 * This tool simulates browser behavior to test the highest layers of the application,
 * including DOM manipulation, component rendering, and user interactions.
 * It provides visibility into UI errors that can't be detected from backend testing alone.
 */

import WebSocket from 'ws';
import { JSD<PERSON> } from 'jsdom';
import config from './config.js';

class BrowserUISimulator {
  constructor() {
    this.dom = null;
    this.window = null;
    this.document = null;
    this.ws = null;
    this.appShell = null;
    this.wheelComponent = null;
    this.isConnected = false;
    this.receivedMessages = [];
    this.wheelData = null;
    this.errors = [];
  }

  /**
   * Initialize the browser environment simulation
   */
  async initializeBrowserEnvironment() {
    console.log('🌐 Initializing browser environment simulation...');
    
    // Create a JSDOM instance with a basic HTML structure
    this.dom = new JSDOM(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Goali - Browser UI Simulator</title>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1">
        </head>
        <body>
          <app-shell id="app-shell">
            <div class="wheel-section">
              <div class="wheel-container">
                <div class="wheel-placeholder">
                  <div class="wheel-placeholder-icon">🎡</div>
                  <h3>Ready for Your Next Adventure?</h3>
                  <p>Start a conversation below to generate your personalized activity wheel!</p>
                </div>
              </div>
            </div>
            <div class="chat-section">
              <chat-interface id="chat-interface"></chat-interface>
            </div>
          </app-shell>
        </body>
      </html>
    `, {
      url: 'http://localhost:3000',
      pretendToBeVisual: true,
      resources: 'usable'
    });

    this.window = this.dom.window;
    this.document = this.window.document;
    
    // Add global objects that frontend expects
    this.window.WebSocket = WebSocket;
    this.window.console = console;
    
    // Mock web components
    this.setupWebComponentMocks();
    
    console.log('✅ Browser environment initialized');
  }

  /**
   * Setup mock web components to simulate frontend behavior
   */
  setupWebComponentMocks() {
    const self = this;
    
    // Mock app-shell component
    class MockAppShell {
      constructor() {
        this.wheelData = null;
        this.messages = [];
        this.isLoading = false;
        this.websocketManager = {
          onMessage: (type, handler) => {
            console.log(`📝 Registered handler for message type: ${type}`);
            self.messageHandlers = self.messageHandlers || {};
            self.messageHandlers[type] = handler;
          }
        };
      }

      handleWheelData(data) {
        console.log('🎡 MockAppShell.handleWheelData called with:', JSON.stringify(data, null, 2));
        this.wheelData = data;
        this.updateWheelDisplay();
      }

      updateWheelDisplay() {
        const wheelContainer = self.document.querySelector('.wheel-container');
        if (wheelContainer && this.wheelData) {
          console.log('🔄 Updating wheel display...');
          
          // Remove placeholder
          const placeholder = wheelContainer.querySelector('.wheel-placeholder');
          if (placeholder) {
            placeholder.remove();
            console.log('🗑️ Removed wheel placeholder');
          }
          
          // Add wheel component
          const wheelElement = self.document.createElement('game-wheel');
          wheelElement.setAttribute('id', 'game-wheel');
          wheelElement.wheelData = this.wheelData;
          wheelContainer.appendChild(wheelElement);
          
          console.log('✅ Wheel component added to DOM');
          self.wheelComponent = wheelElement;
        } else {
          console.log('⚠️ Cannot update wheel display - missing container or data');
        }
      }
    }

    // Mock game-wheel component
    class MockGameWheel {
      constructor() {
        this.wheelData = null;
        this.segments = [];
        this.isSpinning = false;
      }

      set wheelData(data) {
        console.log('🎡 MockGameWheel.wheelData setter called');
        this.wheelData = data;
        this.processWheelData(data);
      }

      processWheelData(data) {
        if (data && data.segments) {
          console.log(`🔄 Processing ${data.segments.length} wheel segments`);
          this.segments = data.segments.map((segment, index) => ({
            id: segment.id,
            text: segment.text || segment.title || segment.name || `Activity ${index + 1}`,
            percentage: segment.percentage,
            color: segment.color
          }));
          
          console.log('📊 Processed segments:', this.segments.map(s => `${s.text} (${s.percentage}%)`));
          this.renderWheel();
        }
      }

      renderWheel() {
        console.log('🎨 Rendering wheel with segments:', this.segments.length);
        // Simulate wheel rendering
        this.isRendered = true;
      }
    }

    // Register mock components
    this.window.customElements = {
      define: (name, constructor) => {
        console.log(`📝 Registered custom element: ${name}`);
      }
    };

    // Create mock instances
    this.appShell = new MockAppShell();
    this.window.appShell = this.appShell;
  }

  /**
   * Connect to the backend WebSocket
   */
  async connectToBackend() {
    console.log('🔌 Connecting to backend WebSocket...');
    
    return new Promise((resolve, reject) => {
      this.ws = new WebSocket(config.backend.websocketUrl);
      
      this.ws.on('open', () => {
        console.log('✅ Connected to backend');
        this.isConnected = true;
        resolve();
      });

      this.ws.on('message', (data) => {
        try {
          const message = JSON.parse(data);
          this.handleMessage(message);
        } catch (error) {
          console.error('❌ Error parsing message:', error);
          this.errors.push(`Message parsing error: ${error.message}`);
        }
      });

      this.ws.on('error', (error) => {
        console.error('❌ WebSocket error:', error);
        this.errors.push(`WebSocket error: ${error.message}`);
        reject(error);
      });

      this.ws.on('close', () => {
        console.log('🔌 WebSocket connection closed');
        this.isConnected = false;
      });
    });
  }

  /**
   * Handle incoming WebSocket messages
   */
  handleMessage(message) {
    this.receivedMessages.push(message);
    
    console.log(`📨 Received ${message.type}:`, message.content || message);

    // Simulate frontend message handling
    switch (message.type) {
      case 'wheel_data':
        this.handleWheelDataMessage(message);
        break;
      case 'debug_info':
        this.handleDebugInfoMessage(message);
        break;
      case 'workflow_status':
        this.handleWorkflowStatusMessage(message);
        break;
      case 'chat_message':
        this.handleChatMessage(message);
        break;
      default:
        console.log(`📝 Unhandled message type: ${message.type}`);
    }
  }

  /**
   * Handle wheel data messages
   */
  handleWheelDataMessage(message) {
    console.log('🎡 Processing wheel_data message...');
    
    if (message.wheel) {
      this.wheelData = message.wheel;
      console.log(`📊 Wheel data received: ${message.wheel.items?.length || 0} items`);
      
      // Test frontend wheel data processing
      this.testWheelDataProcessing(message.wheel);
      
      // Simulate app-shell handling
      if (this.appShell) {
        this.appShell.handleWheelData(message.wheel);
      }
    } else {
      console.log('⚠️ wheel_data message missing wheel property');
      this.errors.push('wheel_data message missing wheel property');
    }
  }

  /**
   * Test wheel data processing like the frontend would
   */
  testWheelDataProcessing(wheelData) {
    console.log('🧪 Testing wheel data processing...');
    
    // Test different data formats the frontend might receive
    let items = [];
    if (wheelData.items) {
      items = wheelData.items;
      console.log('✅ Found items in wheelData.items');
    } else if (wheelData.activities) {
      items = wheelData.activities;
      console.log('✅ Found items in wheelData.activities');
    } else if (Array.isArray(wheelData)) {
      items = wheelData;
      console.log('✅ wheelData is array');
    } else {
      console.log('❌ No items found in wheel data');
      this.errors.push('No items found in wheel data');
      return;
    }

    // Test segment mapping
    const segments = items.map((item, index) => ({
      id: item.id || `item-${index}`,
      text: item.title || item.name || item.activity_name || `Activity ${index + 1}`,
      percentage: item.percentage || (item.probability * 100) || (100 / items.length),
      color: item.color || this.generateWheelColor(index)
    }));

    console.log('📊 Generated segments:');
    segments.forEach((segment, index) => {
      console.log(`  ${index + 1}. ${segment.text} (${segment.percentage.toFixed(1)}%) - ${segment.color}`);
    });

    // Check for issues
    if (segments.some(s => s.text.startsWith('Activity '))) {
      console.log('⚠️ WARNING: Some segments have generic names (Activity 1, Activity 2, etc.)');
      this.errors.push('Segments have generic names instead of proper titles');
    }

    if (segments.length === 0) {
      console.log('❌ ERROR: No segments generated');
      this.errors.push('No segments generated from wheel data');
    }
  }

  /**
   * Handle debug info messages
   */
  handleDebugInfoMessage(message) {
    if (message.content) {
      const debugMessage = message.content.message || message.content || JSON.stringify(message);
      console.log(`🐛 Debug: ${debugMessage}`);
      
      // Test if debug message displays correctly
      if (debugMessage === 'undefined') {
        console.log('❌ ERROR: Debug message shows "undefined"');
        this.errors.push('Debug message displays "undefined"');
      }
    }
  }

  /**
   * Handle workflow status messages
   */
  handleWorkflowStatusMessage(message) {
    console.log(`🔄 Workflow ${message.workflow_id}: ${message.status}`);
  }

  /**
   * Handle chat messages
   */
  handleChatMessage(message) {
    const sender = message.is_user ? 'User' : 'AI';
    console.log(`💬 ${sender}: ${message.content.substring(0, 100)}...`);
  }

  /**
   * Generate wheel colors
   */
  generateWheelColor(index) {
    const colors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
      '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
    ];
    return colors[index % colors.length];
  }

  /**
   * Send a message to the backend
   */
  sendMessage(content) {
    if (!this.isConnected) {
      console.log('❌ Cannot send message - not connected');
      return;
    }

    const message = {
      type: 'chat_message',
      content: content,
      timestamp: new Date().toISOString()
    };

    console.log(`👤 Sending: "${content}"`);
    this.ws.send(JSON.stringify(message));
  }

  /**
   * Run the complete user story simulation
   */
  async runUserStorySimulation() {
    console.log('🎬 Starting browser UI simulation...');
    
    try {
      // Initialize browser environment
      await this.initializeBrowserEnvironment();
      
      // Connect to backend
      await this.connectToBackend();
      
      // Wait for connection to stabilize
      await this.sleep(1000);
      
      // Send first message
      this.sendMessage("I'm bored");
      
      // Wait for response and wheel generation
      await this.sleep(15000);
      
      // Send second message
      this.sendMessage("I feel like doing exercise, what do you propose?");
      
      // Wait for final response
      await this.sleep(15000);
      
      // Analyze results
      this.analyzeResults();
      
    } catch (error) {
      console.error('❌ Simulation failed:', error);
      this.errors.push(`Simulation failed: ${error.message}`);
    } finally {
      if (this.ws) {
        this.ws.close();
      }
    }
  }

  /**
   * Analyze simulation results
   */
  analyzeResults() {
    console.log('\n🔍 BROWSER UI SIMULATION ANALYSIS');
    console.log('=====================================');
    
    console.log(`📊 Total messages received: ${this.receivedMessages.length}`);
    console.log(`🎡 Wheel data received: ${this.wheelData ? 'Yes' : 'No'}`);
    console.log(`🎨 Wheel component created: ${this.wheelComponent ? 'Yes' : 'No'}`);
    console.log(`❌ Errors detected: ${this.errors.length}`);
    
    if (this.errors.length > 0) {
      console.log('\n❌ ERRORS FOUND:');
      this.errors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error}`);
      });
    }
    
    if (this.wheelData) {
      console.log('\n🎡 WHEEL DATA ANALYSIS:');
      console.log(`  - Items: ${this.wheelData.items?.length || 0}`);
      console.log(`  - Activities: ${this.wheelData.activities?.length || 0}`);
      console.log(`  - Name: ${this.wheelData.name || 'N/A'}`);
      console.log(`  - Trust Phase: ${this.wheelData.metadata?.trust_phase || 'N/A'}`);
    }
    
    // Check DOM state
    console.log('\n🌐 DOM STATE ANALYSIS:');
    const wheelContainer = this.document.querySelector('.wheel-container');
    const placeholder = this.document.querySelector('.wheel-placeholder');
    const gameWheel = this.document.querySelector('game-wheel');
    
    console.log(`  - Wheel container: ${wheelContainer ? 'Found' : 'Missing'}`);
    console.log(`  - Wheel placeholder: ${placeholder ? 'Still present' : 'Removed'}`);
    console.log(`  - Game wheel component: ${gameWheel ? 'Present' : 'Missing'}`);
    
    // Overall assessment
    console.log('\n🎯 OVERALL ASSESSMENT:');
    if (this.errors.length === 0 && this.wheelData && this.wheelComponent) {
      console.log('✅ UI simulation PASSED - All systems working correctly');
    } else if (this.wheelData && this.errors.length < 3) {
      console.log('⚠️ UI simulation PARTIAL - Core functionality working with minor issues');
    } else {
      console.log('❌ UI simulation FAILED - Significant issues detected');
    }
  }

  /**
   * Sleep utility
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Run the simulation if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const simulator = new BrowserUISimulator();
  simulator.runUserStorySimulation().then(() => {
    console.log('\n🎉 Browser UI simulation completed!');
    process.exit(0);
  }).catch((error) => {
    console.error('❌ Simulation failed:', error);
    process.exit(1);
  });
}

export default BrowserUISimulator;
