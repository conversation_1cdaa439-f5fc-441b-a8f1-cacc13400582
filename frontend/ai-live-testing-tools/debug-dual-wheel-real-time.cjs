#!/usr/bin/env node

/**
 * DEBUG DUAL-WHEEL ISSUE - Real-Time Interactive Test
 * 
 * This test will help us witness the dual-wheel issue by:
 * 1. Setting up proper authentication and user selection
 * 2. Generating a wheel through chat interface
 * 3. Monitoring wheel data changes in real-time
 * 4. Observing what happens during remove operations
 */

const puppeteer = require('puppeteer');

class DualWheelDebugger {
    constructor(port = 3002) {
        this.port = port;
        this.baseUrl = `http://localhost:${port}`;
        this.browser = null;
        this.page = null;
        this.wheelDataHistory = [];
    }

    async runDebugSession() {
        console.log('🔍 ===== DUAL-WHEEL ISSUE REAL-TIME DEBUGGING =====');
        console.log(`🌐 URL: ${this.baseUrl}`);
        console.log();

        try {
            await this.setupBrowser();
            await this.setupDebugMode();
            await this.monitorWheelData();
            await this.generateWheelViaChat();
            await this.waitForUserInteraction();
        } catch (error) {
            console.error('❌ Debug session failed:', error);
        } finally {
            console.log('\n🔍 Final wheel data history:');
            this.wheelDataHistory.forEach((entry, index) => {
                console.log(`   ${index + 1}. ${entry.event}: ${entry.segmentCount} segments, ${entry.coloredCount} colored, ${entry.greyCount} grey`);
            });
            
            if (this.browser) {
                console.log('\n⏸️ Browser will remain open for manual inspection...');
                console.log('   Press Ctrl+C to close when done');
                // Keep browser open for manual inspection
                await new Promise(() => {}); // Wait indefinitely
            }
        }
    }

    async setupBrowser() {
        console.log('🚀 Setting up browser...');
        this.browser = await puppeteer.launch({
            headless: false,
            defaultViewport: { width: 1600, height: 1000 },
            args: ['--no-sandbox', '--disable-setuid-sandbox'],
            slowMo: 50
        });
        this.page = await this.browser.newPage();
        
        // Monitor console for wheel-related messages
        this.page.on('console', msg => {
            const text = msg.text();
            if (text.includes('wheel') || text.includes('WHEEL') || text.includes('🎡') || 
                text.includes('🗑️') || text.includes('➕') || text.includes('segments')) {
                console.log(`   🔍 ${text}`);
            }
        });
        
        await this.page.goto(this.baseUrl);
        await new Promise(resolve => setTimeout(resolve, 3000));
        console.log('   ✅ Browser ready');
    }

    async setupDebugMode() {
        console.log('🔧 Setting up debug mode...');
        
        try {
            // Handle login if needed
            const loginModal = await this.page.$('.modal');
            if (loginModal) {
                console.log('   🔑 Handling login...');
                const usernameInput = await this.page.$('input[type="text"], input[name="username"]');
                const passwordInput = await this.page.$('input[type="password"], input[name="password"]');
                const loginButton = await this.page.$('button[type="submit"], .btn-primary');
                
                if (usernameInput && passwordInput && loginButton) {
                    await usernameInput.type('admin');
                    await passwordInput.type('admin123');
                    await loginButton.click();
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    console.log('   ✅ Logged in');
                }
            }

            // Open debug panel
            console.log('   🐛 Opening debug panel...');
            await this.page.keyboard.down('Control');
            await this.page.keyboard.down('Shift');
            await this.page.keyboard.press('KeyD');
            await this.page.keyboard.up('Shift');
            await this.page.keyboard.up('Control');
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Select PhiPhi user
            const userSelect = await this.page.$('select[data-testid="user-select"]');
            if (userSelect) {
                await userSelect.selectOption('2'); // PhiPhi
                console.log('   ✅ Selected PhiPhi user');
            }

            // Select LLM
            const llmSelect = await this.page.$('select[data-testid="llm-select"]');
            if (llmSelect) {
                await llmSelect.selectOption('mistral-small-latest');
                console.log('   ✅ Selected LLM');
            }

            // Apply settings
            const applyButton = await this.page.$('button[data-testid="apply-settings"]');
            if (applyButton) {
                await applyButton.click();
                await new Promise(resolve => setTimeout(resolve, 2000));
                console.log('   ✅ Applied debug settings');
            }

        } catch (error) {
            console.log('   ⚠️ Debug setup failed, continuing...');
        }
    }

    async monitorWheelData() {
        console.log('📊 Starting wheel data monitoring...');
        
        // Set up periodic wheel data monitoring
        setInterval(async () => {
            try {
                const wheelData = await this.page.evaluate(() => {
                    const appShell = document.querySelector('app-shell');
                    if (appShell && appShell.wheelData && appShell.wheelData.segments) {
                        return {
                            segmentCount: appShell.wheelData.segments.length,
                            segments: appShell.wheelData.segments.map(s => ({
                                id: s.id,
                                name: s.name || s.text,
                                color: s.color
                            }))
                        };
                    }
                    return null;
                });
                
                if (wheelData) {
                    const coloredCount = wheelData.segments.filter(s => s.color && s.color !== '#95A5A6').length;
                    const greyCount = wheelData.segments.length - coloredCount;
                    
                    // Only log if data changed
                    const lastEntry = this.wheelDataHistory[this.wheelDataHistory.length - 1];
                    if (!lastEntry || 
                        lastEntry.segmentCount !== wheelData.segmentCount ||
                        lastEntry.coloredCount !== coloredCount ||
                        lastEntry.greyCount !== greyCount) {
                        
                        const entry = {
                            timestamp: Date.now(),
                            event: 'WHEEL_DATA_CHANGE',
                            segmentCount: wheelData.segmentCount,
                            coloredCount,
                            greyCount,
                            segments: wheelData.segments
                        };
                        
                        this.wheelDataHistory.push(entry);
                        
                        if (greyCount > 0) {
                            console.log(`   ❌ GREY SEGMENTS DETECTED: ${greyCount}/${wheelData.segmentCount}`);
                        } else {
                            console.log(`   ✅ Wheel data: ${wheelData.segmentCount} segments, all colored`);
                        }
                    }
                }
            } catch (error) {
                // Ignore monitoring errors
            }
        }, 1000); // Check every second
        
        console.log('   ✅ Monitoring started');
    }

    async generateWheelViaChat() {
        console.log('💬 Generating wheel via chat...');
        
        try {
            // Find chat input
            const chatInput = await this.page.$('input[type="text"], textarea, .chat-input');
            if (chatInput) {
                console.log('   📝 Found chat input, sending wheel generation request...');
                await chatInput.type('Generate a wheel for me');
                
                // Find send button or press Enter
                const sendButton = await this.page.$('button[type="submit"], .send-button, .btn-primary');
                if (sendButton) {
                    await sendButton.click();
                } else {
                    await this.page.keyboard.press('Enter');
                }
                
                console.log('   🚀 Wheel generation request sent');
                console.log('   ⏳ Waiting for wheel generation...');
                
                // Wait for wheel to be generated (up to 60 seconds)
                await this.page.waitForFunction(() => {
                    const appShell = document.querySelector('app-shell');
                    return appShell && appShell.wheelData && appShell.wheelData.segments && appShell.wheelData.segments.length > 0;
                }, { timeout: 60000 });
                
                console.log('   ✅ Wheel generated successfully!');
                
            } else {
                console.log('   ❌ Chat input not found');
            }
            
        } catch (error) {
            console.log(`   ❌ Wheel generation failed: ${error.message}`);
        }
    }

    async waitForUserInteraction() {
        console.log();
        console.log('👤 ===== MANUAL INTERACTION PHASE =====');
        console.log('🔍 The browser is now ready for you to reproduce the dual-wheel issue:');
        console.log('   1. ✅ Wheel should be generated');
        console.log('   2. 🗑️ Try removing an item from the wheel');
        console.log('   3. 🔄 Close any modals that appear');
        console.log('   4. 👁️ Observe if the wheel changes to grey segments');
        console.log();
        console.log('📊 Wheel data changes will be logged in real-time below:');
        console.log('   ✅ = All segments have proper colors');
        console.log('   ❌ = Grey segments detected');
        console.log();
        console.log('⏸️ Browser will remain open for manual testing...');
        console.log('   Press Ctrl+C when done to see the complete analysis');
        
        // Set up enhanced monitoring for user interactions
        this.page.on('response', response => {
            if (response.url().includes('wheel-items') && response.request().method() === 'DELETE') {
                console.log(`   🗑️ REMOVE API CALL: ${response.status()} ${response.url()}`);
                this.wheelDataHistory.push({
                    timestamp: Date.now(),
                    event: 'REMOVE_API_CALL',
                    status: response.status(),
                    url: response.url()
                });
            }
        });
        
        // Wait indefinitely for manual testing
        await new Promise(() => {});
    }
}

// Run the debug session
const port = process.argv[2] || 3002;
const debugSession = new DualWheelDebugger(port);
debugSession.runDebugSession().catch(console.error);
