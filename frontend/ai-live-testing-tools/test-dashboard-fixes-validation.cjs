#!/usr/bin/env node

/**
 * Dashboard Fixes Validation Test
 * 
 * This test validates that the fixes for session monitoring and message inspector
 * are working correctly with proper timing and connection handling.
 */

const WebSocket = require('ws');

class DashboardFixesValidator {
    constructor() {
        this.adminSocket = null;
        this.gameSocket = null;
        this.testSessionId = null;
        this.receivedMessages = [];
        this.errors = [];
        this.startTime = Date.now();
    }

    async runValidation() {
        console.log('🔧 Dashboard Fixes Validation Test');
        console.log('==================================\n');

        try {
            await this.connectToAdmin();
            await this.connectToGame();
            await this.waitForInitialData();
            await this.testMessageInspectorFix();
            await this.testSessionMonitoringFix();
            await this.generateValidationReport();
        } catch (error) {
            console.error('❌ Validation failed:', error.message);
            this.errors.push(`Validation failure: ${error.message}`);
        } finally {
            await this.cleanup();
        }
    }

    async connectToAdmin() {
        console.log('🔬 Step 1: Connecting to Admin Dashboard');
        
        return new Promise((resolve) => {
            this.adminSocket = new WebSocket('ws://localhost:8000/ws/connection-monitor/');
            
            this.adminSocket.on('open', () => {
                console.log('✅ Admin dashboard connected');
                
                // Set up message handler
                this.adminSocket.on('message', (data) => {
                    try {
                        const message = JSON.parse(data);
                        this.receivedMessages.push({
                            source: 'admin',
                            timestamp: Date.now(),
                            message: message
                        });
                        console.log(`📨 Admin received: ${message.type}`);
                    } catch (error) {
                        console.log(`📨 Admin received non-JSON: ${data.toString().substring(0, 50)}...`);
                    }
                });
                
                resolve();
            });
            
            this.adminSocket.on('error', (error) => {
                console.log('❌ Admin connection failed:', error.message);
                this.errors.push(`Admin connection failed: ${error.message}`);
                resolve();
            });
            
            setTimeout(() => {
                if (this.adminSocket.readyState !== WebSocket.OPEN) {
                    this.errors.push('Admin connection timeout');
                }
                resolve();
            }, 5000);
        });
    }

    async connectToGame() {
        console.log('\n🔬 Step 2: Connecting to Game WebSocket');
        
        return new Promise((resolve) => {
            this.gameSocket = new WebSocket('ws://localhost:8000/ws/game/');
            
            this.gameSocket.on('open', () => {
                console.log('✅ Game WebSocket connected');
                
                // Set up message handler
                this.gameSocket.on('message', (data) => {
                    try {
                        const message = JSON.parse(data);
                        this.receivedMessages.push({
                            source: 'game',
                            timestamp: Date.now(),
                            message: message
                        });
                        console.log(`📨 Game received: ${message.type}`);
                    } catch (error) {
                        console.log(`📨 Game received non-JSON: ${data.toString().substring(0, 50)}...`);
                    }
                });
                
                resolve();
            });
            
            this.gameSocket.on('error', (error) => {
                console.log('❌ Game connection failed:', error.message);
                this.errors.push(`Game connection failed: ${error.message}`);
                resolve();
            });
            
            setTimeout(() => {
                if (this.gameSocket.readyState !== WebSocket.OPEN) {
                    this.errors.push('Game connection timeout');
                }
                resolve();
            }, 5000);
        });
    }

    async waitForInitialData() {
        console.log('\n🔬 Step 3: Waiting for Initial Dashboard Data');
        
        if (!this.adminSocket || this.adminSocket.readyState !== WebSocket.OPEN) {
            this.errors.push('Admin socket not available for initial data');
            return;
        }

        // Request initial data
        this.adminSocket.send(JSON.stringify({type: 'get_connections'}));
        this.adminSocket.send(JSON.stringify({type: 'get_system_health'}));
        this.adminSocket.send(JSON.stringify({type: 'get_message_stats'}));
        
        // Wait for responses
        await this.sleep(3000);
        
        const connectionMessages = this.receivedMessages.filter(msg => 
            msg.source === 'admin' && msg.message.type === 'connection_data'
        );
        
        if (connectionMessages.length > 0 && connectionMessages[0].message.data.length > 0) {
            this.testSessionId = connectionMessages[0].message.data[0].session_id;
            console.log(`✅ Found session ID: ${this.testSessionId.substring(0, 12)}...`);
            console.log(`✅ Dashboard receiving data: ${this.receivedMessages.filter(msg => msg.source === 'admin').length} admin messages`);
        } else {
            console.log('⚠️ No active sessions found, will create test session');
        }
    }

    async testMessageInspectorFix() {
        console.log('\n🔬 Step 4: Testing Message Inspector Fix');
        
        if (!this.adminSocket || this.adminSocket.readyState !== WebSocket.OPEN) {
            this.errors.push('Admin socket not available for message inspector test');
            return;
        }

        // Start message monitoring
        console.log('📤 Starting message monitoring...');
        this.adminSocket.send(JSON.stringify({type: 'start_message_monitoring'}));
        
        await this.sleep(1000);
        
        // Send test messages through game socket
        console.log('📤 Sending test messages for inspector...');
        const testMessages = [
            {
                type: 'chat_message',
                content: {
                    message: 'Message inspector test 1',
                    user_profile_id: 'inspector-test-1'
                }
            },
            {
                type: 'chat_message',
                content: {
                    message: 'Message inspector test 2',
                    user_profile_id: 'inspector-test-2'
                }
            }
        ];
        
        if (this.gameSocket && this.gameSocket.readyState === WebSocket.OPEN) {
            for (let i = 0; i < testMessages.length; i++) {
                this.gameSocket.send(JSON.stringify(testMessages[i]));
                await this.sleep(500);
            }
            
            await this.sleep(2000);
            
            // Check if message flow events are being received
            const messageFlowEvents = this.receivedMessages.filter(msg => 
                msg.source === 'admin' && 
                msg.timestamp > Date.now() - 4000 && // Last 4 seconds
                msg.message.type === 'message_flow'
            );
            
            if (messageFlowEvents.length > 0) {
                console.log(`✅ Message inspector fix working: ${messageFlowEvents.length} flow events received`);
                messageFlowEvents.forEach(event => {
                    console.log(`   - ${event.message.data.direction} message: ${event.message.data.message.type}`);
                });
            } else {
                console.log('❌ Message inspector fix NOT working: No message flow events received');
                this.errors.push('Message inspector not receiving message flow events');
            }
        }
    }

    async testSessionMonitoringFix() {
        console.log('\n🔬 Step 5: Testing Session Monitoring Fix');
        
        if (!this.adminSocket || this.adminSocket.readyState !== WebSocket.OPEN) {
            this.errors.push('Admin socket not available for session monitoring test');
            return;
        }

        if (!this.testSessionId) {
            console.log('⚠️ No session ID available for monitoring test');
            return;
        }

        // Start session monitoring
        console.log('📤 Starting session monitoring...');
        this.adminSocket.send(JSON.stringify({type: 'start_session_monitoring'}));
        
        await this.sleep(500);
        
        // Focus on the session
        console.log(`📤 Focusing on session: ${this.testSessionId.substring(0, 12)}...`);
        this.adminSocket.send(JSON.stringify({
            type: 'focus_session',
            session_id: this.testSessionId
        }));
        
        await this.sleep(1000);
        
        // Send a test message to trigger session monitoring
        console.log('📤 Sending test message to trigger session monitoring...');
        if (this.gameSocket && this.gameSocket.readyState === WebSocket.OPEN) {
            this.gameSocket.send(JSON.stringify({
                type: 'chat_message',
                content: {
                    message: 'Session monitoring test message',
                    user_profile_id: 'session-monitor-test'
                }
            }));
            
            await this.sleep(2000);
            
            // Check if session messages are being monitored
            const sessionMessages = this.receivedMessages.filter(msg => 
                msg.source === 'admin' && 
                msg.timestamp > Date.now() - 3000 && // Last 3 seconds
                msg.message.session_id === this.testSessionId
            );
            
            if (sessionMessages.length > 0) {
                console.log(`✅ Session monitoring fix working: ${sessionMessages.length} session messages received`);
                sessionMessages.forEach(msg => {
                    console.log(`   - Session message: ${msg.message.type}`);
                });
            } else {
                console.log('❌ Session monitoring fix NOT working: No session messages received');
                this.errors.push('Session monitoring not receiving session-specific messages');
            }
        }
    }

    async generateValidationReport() {
        const duration = ((Date.now() - this.startTime) / 1000).toFixed(1);
        
        console.log('\n🎯 DASHBOARD FIXES VALIDATION REPORT');
        console.log('====================================');
        console.log(`⏱️ Test duration: ${duration} seconds`);
        console.log(`📨 Total messages received: ${this.receivedMessages.length}`);
        console.log(`❌ Issues found: ${this.errors.length}`);
        
        // Message breakdown by source
        const adminMessages = this.receivedMessages.filter(msg => msg.source === 'admin');
        const gameMessages = this.receivedMessages.filter(msg => msg.source === 'game');
        
        console.log('\n📊 MESSAGE BREAKDOWN:');
        console.log(`  Admin dashboard: ${adminMessages.length} messages`);
        console.log(`  Game WebSocket: ${gameMessages.length} messages`);
        
        // Admin message types
        if (adminMessages.length > 0) {
            console.log('\n📨 ADMIN MESSAGE TYPES:');
            const adminTypes = {};
            adminMessages.forEach(msg => {
                const type = msg.message.type || 'unknown';
                adminTypes[type] = (adminTypes[type] || 0) + 1;
            });
            Object.entries(adminTypes).forEach(([type, count]) => {
                console.log(`  - ${type}: ${count}`);
            });
        }
        
        // Check specific fixes
        const hasMessageFlow = adminMessages.some(msg => msg.message.type === 'message_flow');
        const hasSessionMessages = adminMessages.some(msg => msg.message.session_id);
        const hasMonitoringStarted = adminMessages.some(msg => msg.message.type === 'message_monitoring_started');
        
        console.log('\n🔧 FIX VALIDATION RESULTS:');
        console.log(`  Message flow events: ${hasMessageFlow ? '✅ WORKING' : '❌ NOT WORKING'}`);
        console.log(`  Session message broadcasting: ${hasSessionMessages ? '✅ WORKING' : '❌ NOT WORKING'}`);
        console.log(`  Message monitoring activation: ${hasMonitoringStarted ? '✅ WORKING' : '❌ NOT WORKING'}`);
        
        // Issues found
        if (this.errors.length > 0) {
            console.log('\n❌ REMAINING ISSUES:');
            this.errors.forEach((error, index) => {
                console.log(`  ${index + 1}. ${error}`);
            });
        }
        
        // Overall assessment
        const fixesWorking = hasMessageFlow && hasSessionMessages;
        
        if (fixesWorking && this.errors.length === 0) {
            console.log('\n🎉 VALIDATION RESULT: ALL FIXES WORKING');
            console.log('✅ Session monitoring and message inspector are now functional');
            console.log('✅ Dashboard is ready for production use');
        } else if (fixesWorking) {
            console.log('\n👍 VALIDATION RESULT: MAJOR FIXES WORKING');
            console.log('✅ Core functionality restored with minor issues remaining');
        } else {
            console.log('\n⚠️ VALIDATION RESULT: FIXES NEED MORE WORK');
            console.log('❌ Core issues still present, additional debugging needed');
        }
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async cleanup() {
        console.log('\n🧹 Cleaning up validation environment...');
        
        if (this.adminSocket && this.adminSocket.readyState === WebSocket.OPEN) {
            this.adminSocket.close();
        }
        
        if (this.gameSocket && this.gameSocket.readyState === WebSocket.OPEN) {
            this.gameSocket.close();
        }
        
        console.log('✅ Validation cleanup complete');
    }
}

// Run the validation
if (require.main === module) {
    const validator = new DashboardFixesValidator();
    validator.runValidation().catch(console.error);
}

module.exports = DashboardFixesValidator;
