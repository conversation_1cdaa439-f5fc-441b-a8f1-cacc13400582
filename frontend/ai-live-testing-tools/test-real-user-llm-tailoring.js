#!/usr/bin/env node

/**
 * Test script to verify LLM-powered activity tailoring with a real user
 * This script connects as user ID 1 (real user) to test LLM tailoring functionality
 */

import WebSocket from 'ws';

const BACKEND_URL = 'ws://localhost:8000/ws/game/';
const REAL_USER_ID = '1'; // User ID 1 is a real user with is_real=True

console.log('🧪 LLM Tailoring Test');
console.log('====================');
console.log('');
console.log('📖 Testing LLM-powered activity tailoring with real user:');
console.log(`   User ID: ${REAL_USER_ID} (real user with is_real=True)`);
console.log('   Expected: LLM-tailored activities instead of fallback activities');
console.log('');

let messageCount = 0;
let wheelReceived = false;

function connectAsRealUser() {
    console.log('🔌 Connecting to backend as real user...');
    
    const ws = new WebSocket(BACKEND_URL, {
        headers: {
            'X-User-ID': REAL_USER_ID,
            'X-Debug-Mode': 'true'
        }
    });

    ws.on('open', function open() {
        console.log('✅ Connected to backend as real user');
        console.log('🎬 Starting LLM tailoring test...');
        console.log('');
        
        // Send a message that should trigger wheel generation
        setTimeout(() => {
            console.log('👤 Sending: "hey, would you generate me a wheel please ? I\'m tired but not sleepy"');
            ws.send(JSON.stringify({
                type: 'chat_message',
                content: "hey, would you generate me a wheel please ? I'm tired but not sleepy"
            }));
        }, 1000);
    });

    ws.on('message', function message(data) {
        messageCount++;
        
        try {
            const msg = JSON.parse(data);
            
            // Log key message types
            if (msg.type === 'system_message') {
                console.log(`🔔 System: ${msg.content}`);
            } else if (msg.type === 'chat_message' && !msg.is_user) {
                console.log(`🤖 AI: ${msg.content.substring(0, 100)}${msg.content.length > 100 ? '...' : ''}`);
            } else if (msg.type === 'wheel_data') {
                wheelReceived = true;
                console.log('');
                console.log('🎡 Wheel Generated!');
                console.log('');
                
                // Analyze the wheel for LLM tailoring evidence
                analyzeWheelForLLMTailoring(msg.wheel);
                
            } else if (msg.type === 'debug_info' && msg.content.message) {
                // Look for LLM tailoring related debug messages
                const message = msg.content.message;
                if (message.includes('LLM tailoring') || 
                    message.includes('real user') || 
                    message.includes('test user') ||
                    message.includes('benchmark user')) {
                    console.log(`🔧 Debug: ${message}`);
                }
            } else if (msg.type === 'workflow_status' && msg.status === 'completed') {
                console.log('📋 Workflow completed');
                
                // Give some time for final messages, then analyze results
                setTimeout(() => {
                    analyzeTestResults();
                    ws.close();
                }, 2000);
            }
            
        } catch (e) {
            console.error('Error parsing message:', e);
        }
    });

    ws.on('error', function error(err) {
        console.error('❌ WebSocket error:', err);
    });

    ws.on('close', function close() {
        console.log('');
        console.log('🔌 Connection closed');
    });
}

function analyzeWheelForLLMTailoring(wheel) {
    console.log('🔍 Analyzing wheel for LLM tailoring evidence...');
    console.log('');
    
    if (!wheel || !wheel.activities) {
        console.log('❌ No wheel activities found');
        return;
    }
    
    console.log(`📊 Wheel Analysis:`);
    console.log(`   Name: ${wheel.name || wheel.metadata?.name}`);
    console.log(`   Items: ${wheel.items?.length || 0}`);
    console.log(`   Activities: ${wheel.activities?.length || 0}`);
    console.log('');
    
    let llmTailoredCount = 0;
    let fallbackCount = 0;
    
    wheel.activities.forEach((activity, index) => {
        const isLLMTailored = !activity.id.startsWith('fallback_');
        const isFallback = activity.id.startsWith('fallback_');
        
        if (isLLMTailored) llmTailoredCount++;
        if (isFallback) fallbackCount++;
        
        console.log(`   ${index + 1}. ${activity.name || activity.title} (${activity.domain})`);
        console.log(`      ID: ${activity.id} ${isFallback ? '❌ FALLBACK' : '✅ LLM-TAILORED'}`);
        console.log(`      Description: ${activity.description || 'No description'}`);
        console.log('');
    });
    
    console.log('📈 Tailoring Analysis:');
    console.log(`   ✅ LLM-tailored activities: ${llmTailoredCount}`);
    console.log(`   ❌ Fallback activities: ${fallbackCount}`);
    console.log(`   🎯 Success rate: ${llmTailoredCount > 0 ? 'PASS' : 'FAIL'} (${Math.round(llmTailoredCount / wheel.activities.length * 100)}%)`);
    console.log('');
}

function analyzeTestResults() {
    console.log('');
    console.log('🎉 LLM Tailoring Test Results');
    console.log('=============================');
    console.log('');
    
    if (!wheelReceived) {
        console.log('❌ FAIL: No wheel was generated');
        console.log('   Expected: Wheel generation workflow to complete successfully');
        console.log('   Actual: No wheel_data message received');
    } else {
        console.log('✅ PASS: Wheel generation completed');
        console.log('   The test successfully triggered wheel generation');
        console.log('   Check the analysis above for LLM tailoring evidence');
    }
    
    console.log('');
    console.log(`📊 Total messages received: ${messageCount}`);
    console.log('');
    console.log('💡 Next Steps:');
    console.log('   1. Check celery logs for LLM tailoring debug messages');
    console.log('   2. Verify that real user gets LLM-tailored activities');
    console.log('   3. Compare with test user (PhiPhi) who should get fallbacks');
    console.log('');
}

// Start the test
connectAsRealUser();
