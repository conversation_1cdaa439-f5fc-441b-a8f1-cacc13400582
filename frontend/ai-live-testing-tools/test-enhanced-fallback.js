/**
 * Test Enhanced Fallback Mechanism
 * 
 * This script tests if the enhanced fallback mechanism is working correctly
 * by checking if high energy levels result in more physical activities.
 */

console.log('🎯 Testing Enhanced Fallback Mechanism');
console.log('=====================================');

// Test configuration
const TEST_CONFIG = {
    HIGH_ENERGY: 100,
    MEDIUM_ENERGY: 50,
    LOW_ENERGY: 20,
    TIME_AVAILABLE: 10,
    EXPECTED_PHYSICAL_PERCENTAGE_HIGH: 70, // 70%+ for high energy
    EXPECTED_PHYSICAL_PERCENTAGE_MEDIUM: 30, // 30% for medium energy
};

let testResults = {
    highEnergyTest: null,
    mediumEnergyTest: null,
    lowEnergyTest: null,
    enhancedFallbackWorking: false
};

/**
 * Main test execution
 */
async function testEnhancedFallback() {
    try {
        console.log('🚀 Starting enhanced fallback tests...');
        
        // Test 1: High Energy (100%) - Should get 75% physical activities
        console.log('\n🔥 Test 1: High Energy (100%)');
        testResults.highEnergyTest = await testEnergyLevel(TEST_CONFIG.HIGH_ENERGY, 'HIGH');
        
        // Test 2: Medium Energy (50%) - Should get ~30% physical activities
        console.log('\n⚡ Test 2: Medium Energy (50%)');
        testResults.mediumEnergyTest = await testEnergyLevel(TEST_CONFIG.MEDIUM_ENERGY, 'MEDIUM');
        
        // Test 3: Low Energy (20%) - Should get minimal physical activities
        console.log('\n🔋 Test 3: Low Energy (20%)');
        testResults.lowEnergyTest = await testEnergyLevel(TEST_CONFIG.LOW_ENERGY, 'LOW');
        
        // Analyze results
        analyzeResults();
        
        // Generate report
        generateTestReport();
        
    } catch (error) {
        console.error('❌ Enhanced fallback test failed:', error);
    }
}

/**
 * Test a specific energy level
 */
async function testEnergyLevel(energyLevel, energyType) {
    console.log(`⏳ Testing ${energyType} energy (${energyLevel}%)...`);
    
    try {
        const appShell = document.querySelector('app-shell');
        if (!appShell) {
            throw new Error('App shell not found');
        }
        
        // Set energy level and time
        appShell.energyLevel = energyLevel;
        appShell.timeAvailable = TEST_CONFIG.TIME_AVAILABLE;
        
        // Clear any existing wheel data
        appShell.wheelData = null;
        
        // Generate wheel with specific energy level
        const wheelPromise = new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Wheel generation timeout'));
            }, 30000); // 30 second timeout
            
            // Monitor for wheel data changes
            const checkInterval = setInterval(() => {
                if (appShell.wheelData && appShell.wheelData.items) {
                    clearTimeout(timeout);
                    clearInterval(checkInterval);
                    resolve(appShell.wheelData);
                }
            }, 500);
        });
        
        // Trigger wheel generation
        if (appShell.generateWheel) {
            await appShell.generateWheel({
                energy_level: energyLevel,
                time_available: TEST_CONFIG.TIME_AVAILABLE,
                test_enhanced_fallback: true,
                unique_id: Date.now()
            });
        } else {
            throw new Error('generateWheel method not found');
        }
        
        // Wait for wheel data
        const wheelData = await wheelPromise;
        
        // Analyze the wheel data
        const analysis = analyzeWheelData(wheelData, energyLevel, energyType);
        
        console.log(`✅ ${energyType} energy test completed`);
        return analysis;
        
    } catch (error) {
        console.error(`❌ ${energyType} energy test failed:`, error);
        return {
            energyLevel,
            energyType,
            error: error.message,
            success: false
        };
    }
}

/**
 * Analyze wheel data for energy distribution
 */
function analyzeWheelData(wheelData, energyLevel, energyType) {
    const items = wheelData.items || [];
    
    if (items.length === 0) {
        return {
            energyLevel,
            energyType,
            error: 'No wheel items found',
            success: false
        };
    }
    
    // Count physical activities
    const physicalCount = items.filter(item => 
        item.domain === 'physical' || 
        item.domain?.startsWith('phys')
    ).length;
    
    const physicalPercentage = (physicalCount / items.length) * 100;
    
    // Count domains
    const domains = [...new Set(items.map(item => item.domain))];
    
    // Check colors
    const colorsAssigned = items.filter(item => 
        item.color && item.color !== '#95A5A6'
    ).length;
    
    const analysis = {
        energyLevel,
        energyType,
        totalItems: items.length,
        physicalCount,
        physicalPercentage: Math.round(physicalPercentage * 10) / 10,
        domains,
        domainCount: domains.length,
        colorsAssigned,
        items: items.map(item => ({
            name: item.name,
            domain: item.domain,
            color: item.color
        })),
        success: true
    };
    
    console.log(`📊 ${energyType} Energy Analysis:`, analysis);
    
    return analysis;
}

/**
 * Analyze overall results
 */
function analyzeResults() {
    console.log('\n📊 ANALYZING OVERALL RESULTS');
    console.log('============================');
    
    const { highEnergyTest, mediumEnergyTest, lowEnergyTest } = testResults;
    
    // Check if enhanced fallback is working
    let enhancedFallbackWorking = true;
    const issues = [];
    
    // High energy should have high physical percentage
    if (highEnergyTest?.success) {
        if (highEnergyTest.physicalPercentage < TEST_CONFIG.EXPECTED_PHYSICAL_PERCENTAGE_HIGH) {
            enhancedFallbackWorking = false;
            issues.push(`High energy physical percentage too low: ${highEnergyTest.physicalPercentage}% (expected ≥${TEST_CONFIG.EXPECTED_PHYSICAL_PERCENTAGE_HIGH}%)`);
        } else {
            console.log(`✅ High energy test passed: ${highEnergyTest.physicalPercentage}% physical`);
        }
    } else {
        enhancedFallbackWorking = false;
        issues.push('High energy test failed');
    }
    
    // Medium energy should have moderate physical percentage
    if (mediumEnergyTest?.success) {
        if (mediumEnergyTest.physicalPercentage > highEnergyTest?.physicalPercentage) {
            issues.push('Medium energy has more physical activities than high energy (unexpected)');
        } else {
            console.log(`✅ Medium energy test reasonable: ${mediumEnergyTest.physicalPercentage}% physical`);
        }
    }
    
    // Check domain diversity
    [highEnergyTest, mediumEnergyTest, lowEnergyTest].forEach(test => {
        if (test?.success && test.domainCount < 2) {
            issues.push(`${test.energyType} energy has poor domain diversity: ${test.domainCount} domains`);
        }
    });
    
    testResults.enhancedFallbackWorking = enhancedFallbackWorking;
    testResults.issues = issues;
    
    if (enhancedFallbackWorking && issues.length === 0) {
        console.log('🎉 ENHANCED FALLBACK IS WORKING CORRECTLY!');
    } else {
        console.log('⚠️ Enhanced fallback has issues:', issues);
    }
}

/**
 * Generate comprehensive test report
 */
function generateTestReport() {
    console.log('\n📋 ENHANCED FALLBACK TEST REPORT');
    console.log('=================================');
    
    const { highEnergyTest, mediumEnergyTest, lowEnergyTest, enhancedFallbackWorking, issues } = testResults;
    
    console.log(`✅ Enhanced Fallback Working: ${enhancedFallbackWorking ? 'YES' : 'NO'}`);
    
    if (issues && issues.length > 0) {
        console.log(`\n❌ Issues Found:`);
        issues.forEach((issue, i) => {
            console.log(`   ${i + 1}. ${issue}`);
        });
    }
    
    console.log(`\n📊 Test Results Summary:`);
    
    [
        { test: highEnergyTest, label: 'High Energy (100%)' },
        { test: mediumEnergyTest, label: 'Medium Energy (50%)' },
        { test: lowEnergyTest, label: 'Low Energy (20%)' }
    ].forEach(({ test, label }) => {
        if (test?.success) {
            console.log(`   ${label}:`);
            console.log(`     Physical: ${test.physicalCount}/${test.totalItems} (${test.physicalPercentage}%)`);
            console.log(`     Domains: ${test.domainCount} (${test.domains.join(', ')})`);
            console.log(`     Colors: ${test.colorsAssigned}/${test.totalItems} assigned`);
        } else {
            console.log(`   ${label}: ❌ FAILED (${test?.error || 'Unknown error'})`);
        }
    });
    
    console.log(`\n🎯 OVERALL RESULT:`);
    console.log(`${enhancedFallbackWorking ? '✅ ENHANCED FALLBACK SUCCESSFUL' : '❌ ENHANCED FALLBACK NEEDS WORK'}`);
    
    return enhancedFallbackWorking;
}

// Auto-run test when script is loaded
if (typeof window !== 'undefined') {
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(testEnhancedFallback, 3000); // Wait 3 seconds for app to load
        });
    } else {
        setTimeout(testEnhancedFallback, 3000);
    }
}

// Export for manual execution
if (typeof window !== 'undefined') {
    window.testEnhancedFallback = testEnhancedFallback;
    window.testEnergyLevel = testEnergyLevel;
}
