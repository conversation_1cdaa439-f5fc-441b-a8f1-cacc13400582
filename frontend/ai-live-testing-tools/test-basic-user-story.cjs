#!/usr/bin/env node

/**
 * Test Basic User Story: User asks for wheel, gets quick response asking for more info
 * 
 * This tests the most fundamental user interaction:
 * 1. User asks for a wheel
 * 2. System quickly responds asking for more information
 * 3. Validates response time and content quality
 */

async function testBasicUserStory() {
  console.log('🎯 Testing Basic User Story: Wheel Request → Quick Response');
  
  try {
    // Test with the newly created German student (ID: 103)
    const userId = 103;
    
    console.log(`👤 Testing with user ID: ${userId} (<PERSON> - <PERSON> Student)`);
    
    // Step 1: Get user details to confirm profile state
    console.log('\n1️⃣ Checking user profile state...');
    const userResponse = await fetch(`http://localhost:8000/api/debug/users/${userId}/`);
    
    if (userResponse.ok) {
      const userDetails = await userResponse.json();
      console.log(`✅ User: ${userDetails.profile_name}`);
      console.log(`📊 Profile completion: ${userDetails.profile_completion_percentage}%`);
      console.log(`🏠 Location: ${userDetails.demographics?.location || 'N/A'}`);
      console.log(`💼 Occupation: ${userDetails.demographics?.occupation || 'N/A'}`);
    } else {
      throw new Error(`Failed to get user details: ${userResponse.status}`);
    }
    
    // Step 2: Send wheel request via conversation dispatcher
    console.log('\n2️⃣ Sending wheel request...');
    const startTime = Date.now();
    
    const wheelRequest = {
      text: 'Hi! I need a wheel with activities for my day. Can you help me?'
    };
    
    console.log(`📤 Request: "${wheelRequest.text}"`);
    
    // Use the backend test approach
    const testScript = `
import asyncio
import sys
import os
sys.path.append('/usr/src/app')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
import django
django.setup()

from apps.main.services.conversation_dispatcher import ConversationDispatcher

async def test_wheel_request():
    dispatcher = ConversationDispatcher(${userId})
    
    message = {'text': '${wheelRequest.text}'}
    response = await dispatcher.process_message(message)
    
    print(f"RESPONSE_TYPE:{type(response)}")
    print(f"RESPONSE_DATA:{response}")
    
    if isinstance(response, dict):
        print(f"WORKFLOW_TYPE:{response.get('workflow_type', 'unknown')}")
        print(f"CONFIDENCE:{response.get('confidence', 0)}")
        print(f"STATUS:{response.get('status', 'unknown')}")
        
        # Check for user-facing message
        if 'user_message' in response:
            print(f"USER_MESSAGE:{response['user_message']}")
        elif 'message' in response:
            print(f"USER_MESSAGE:{response['message']}")
        elif 'content' in response:
            print(f"USER_MESSAGE:{response['content']}")

asyncio.run(test_wheel_request())
`;
    
    // Execute the test in the backend container
    const { execSync } = require('child_process');
    const result = execSync(`docker exec -i backend-web-1 python -c "${testScript}"`, {
      encoding: 'utf8',
      timeout: 30000
    });
    
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    console.log(`⏱️ Response time: ${responseTime}ms`);
    
    // Parse the response
    const lines = result.split('\n');
    let responseType = 'unknown';
    let workflowType = 'unknown';
    let confidence = 0;
    let status = 'unknown';
    let userMessage = '';
    
    for (const line of lines) {
      if (line.startsWith('RESPONSE_TYPE:')) {
        responseType = line.replace('RESPONSE_TYPE:', '').trim();
      } else if (line.startsWith('WORKFLOW_TYPE:')) {
        workflowType = line.replace('WORKFLOW_TYPE:', '').trim();
      } else if (line.startsWith('CONFIDENCE:')) {
        confidence = parseFloat(line.replace('CONFIDENCE:', '').trim());
      } else if (line.startsWith('STATUS:')) {
        status = line.replace('STATUS:', '').trim();
      } else if (line.startsWith('USER_MESSAGE:')) {
        userMessage = line.replace('USER_MESSAGE:', '').trim();
      }
    }
    
    console.log('\n📥 System Response Analysis:');
    console.log(`🔍 Response Type: ${responseType}`);
    console.log(`🔄 Workflow: ${workflowType}`);
    console.log(`📊 Confidence: ${(confidence * 100).toFixed(1)}%`);
    console.log(`📋 Status: ${status}`);
    
    if (userMessage) {
      console.log(`💬 User Message: "${userMessage}"`);
    }
    
    // Step 3: Analyze the response quality
    console.log('\n3️⃣ Response Quality Analysis:');
    
    // Check response time (should be quick)
    if (responseTime < 5000) {
      console.log('✅ Response time: EXCELLENT (< 5 seconds)');
    } else if (responseTime < 10000) {
      console.log('⚠️ Response time: ACCEPTABLE (5-10 seconds)');
    } else {
      console.log('❌ Response time: TOO SLOW (> 10 seconds)');
    }
    
    // Check workflow routing
    if (workflowType === 'onboarding') {
      console.log('✅ Workflow routing: CORRECT (onboarding for incomplete profile)');
    } else if (workflowType === 'wheel_generation') {
      console.log('⚠️ Workflow routing: DIRECT TO WHEEL (bypassing profile completion)');
    } else {
      console.log(`❌ Workflow routing: UNEXPECTED (${workflowType})`);
    }
    
    // Check if system asks for more information
    const asksForInfo = userMessage && (
      userMessage.toLowerCase().includes('tell me') ||
      userMessage.toLowerCase().includes('more about') ||
      userMessage.toLowerCase().includes('information') ||
      userMessage.toLowerCase().includes('preferences') ||
      userMessage.toLowerCase().includes('interests') ||
      userMessage.toLowerCase().includes('goals') ||
      userMessage.toLowerCase().includes('help me understand')
    );
    
    if (asksForInfo) {
      console.log('✅ Information gathering: CORRECT (asks for more details)');
    } else if (userMessage) {
      console.log('⚠️ Information gathering: UNCLEAR (response exists but unclear if asking for info)');
    } else {
      console.log('❌ Information gathering: MISSING (no user-facing response)');
    }
    
    // Overall assessment
    console.log('\n🎯 BASIC USER STORY ASSESSMENT:');
    console.log('════════════════════════════════════════');
    
    const isQuickResponse = responseTime < 10000;
    const isCorrectWorkflow = workflowType === 'onboarding' || workflowType === 'wheel_generation';
    const hasUserMessage = userMessage.length > 0;
    
    if (isQuickResponse && isCorrectWorkflow && hasUserMessage) {
      console.log('🎉 BASIC USER STORY: ✅ WORKING CORRECTLY');
      console.log('✅ Quick response time');
      console.log('✅ Appropriate workflow routing');
      console.log('✅ User-facing message provided');
      
      if (asksForInfo) {
        console.log('✅ Correctly asks for more information');
      }
    } else {
      console.log('⚠️ BASIC USER STORY: NEEDS ATTENTION');
      
      if (!isQuickResponse) {
        console.log('❌ Response time too slow');
      }
      if (!isCorrectWorkflow) {
        console.log('❌ Unexpected workflow routing');
      }
      if (!hasUserMessage) {
        console.log('❌ No user-facing message');
      }
    }
    
    console.log('\n📋 Next Steps:');
    if (asksForInfo) {
      console.log('✅ System is ready for user to provide more information');
      console.log('✅ Profile completion workflow is functioning');
    } else {
      console.log('⚠️ May need to verify information gathering mechanism');
    }
    
  } catch (error) {
    console.error('❌ Basic user story test failed:', error.message);
    console.error('Full error:', error);
  }
}

// Check if we're in a Node.js environment with fetch
if (typeof fetch === 'undefined') {
  try {
    const fetch = require('node-fetch');
    global.fetch = fetch;
  } catch (e) {
    console.log('Installing node-fetch...');
    require('child_process').execSync('npm install node-fetch', { stdio: 'inherit' });
    const fetch = require('node-fetch');
    global.fetch = fetch;
  }
}

// Run the test
testBasicUserStory().catch(console.error);
