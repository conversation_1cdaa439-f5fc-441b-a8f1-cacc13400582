/**
 * Final Progress Bar Test - Comprehensive System Validation
 * 
 * This test validates the complete progress bar system end-to-end
 * 
 * Usage: node final-progress-bar-test.cjs [port]
 */

const { chromium } = require('playwright');

class FinalProgressBarTest {
    constructor(port = 3000) {
        this.port = port;
        this.baseUrl = `http://localhost:${port}`;
        this.browser = null;
        this.page = null;
        this.results = {
            websocketConnected: false,
            progressUpdatesReceived: 0,
            progressBarAppeared: false,
            progressBarUpdatedDuringWorkflow: false,
            wheelGenerationCompleted: false,
            wheelPopulatedAfterCompletion: false,
            errors: []
        };
    }

    async initialize() {
        console.log('🚀 Starting Final Progress Bar System Test...');
        
        this.browser = await chromium.launch({ 
            headless: false,
            devtools: false
        });
        
        this.page = await this.browser.newPage();
        
        // Monitor WebSocket messages
        this.page.on('websocket', ws => {
            this.results.websocketConnected = true;
            console.log('✅ WebSocket connection established');
            
            ws.on('framereceived', event => {
                try {
                    const data = JSON.parse(event.payload);

                    if (data.type === 'progress_update') {
                        this.results.progressUpdatesReceived++;
                        this.results.progressBarUpdatedDuringWorkflow = true;
                        const stageName = data.data?.stage_name || data.data?.stage || 'Unknown';
                        const progressPercent = data.data?.progress_percent || 0;
                        console.log(`📊 Progress update #${this.results.progressUpdatesReceived}: ${stageName} - ${progressPercent}%`);

                        // Log detailed progress data for debugging
                        if (data.data) {
                            console.log(`   Stage: ${data.data.stage || 'N/A'}, Workflow: ${data.data.workflow_type || 'N/A'}, Tracker: ${data.data.tracker_id || 'N/A'}`);
                        }
                    }

                    if (data.type === 'wheel_data') {
                        console.log('🎡 Wheel data received via WebSocket:', data);
                        if (data.wheel && data.wheel.items && Array.isArray(data.wheel.items) && data.wheel.items.length > 0) {
                            this.results.wheelPopulatedAfterCompletion = true;
                            console.log(`✅ Wheel populated with ${data.wheel.items.length} items`);
                        }
                    }

                    if (data.type === 'conversation_state_update' && data.updates?.chatMessages) {
                        const lastMessage = data.updates.chatMessages[data.updates.chatMessages.length - 1];
                        if (lastMessage?.content?.includes('wheel') && lastMessage?.type === 'assistant') {
                            console.log('💬 Wheel generation message received from assistant');
                        }
                    }
                } catch (e) {
                    // Not JSON, ignore
                }
            });
        });

        await this.page.goto(this.baseUrl);
        console.log(`✅ Navigated to ${this.baseUrl}`);
        
        // Wait for page to load
        await this.page.waitForTimeout(3000);
    }

    async testProgressBarSystem() {
        console.log('\n🧪 Testing Progress Bar System...');

        // Step 1: Login with admin credentials
        console.log('🔐 Logging in with admin credentials...');

        // Wait for login form to be visible
        await this.page.waitForSelector('login-form', { timeout: 10000 });

        // Find username and password inputs within the login form
        const usernameInput = await this.page.locator('login-form input[type="text"], login-form input[name="username"]').first();
        const passwordInput = await this.page.locator('login-form input[type="password"], login-form input[name="password"]').first();
        const loginButton = await this.page.locator('login-form button[type="submit"], login-form .login-button').first();

        if (await usernameInput.isVisible() && await passwordInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            console.log('✅ Login credentials entered and submitted');

            // Wait for login to complete and main interface to load
            await this.page.waitForTimeout(3000);
        } else {
            this.results.errors.push('Login form inputs not found');
            return;
        }

        // Step 2: Click the Generate button
        console.log('🎯 Looking for Generate button...');

        // Try multiple selectors for the generate button
        const generateButtonSelectors = [
            '.generate-button',
            'button:has-text("Generate")',
            'button[type="button"]:has-text("Generate")',
            '.btn:has-text("Generate")',
            '[data-testid="generate-button"]',
            'button:contains("Generate")',
            'input[type="button"][value*="Generate"]',
            'button'  // Fallback to any button
        ];

        let generateButton = null;
        let buttonFound = false;

        for (const selector of generateButtonSelectors) {
            try {
                generateButton = await this.page.locator(selector).first();
                if (await generateButton.isVisible({ timeout: 2000 })) {
                    const buttonText = await generateButton.textContent();
                    console.log(`✅ Found button with selector "${selector}": "${buttonText}"`);
                    if (buttonText && (buttonText.toLowerCase().includes('generate') || selector === 'button')) {
                        buttonFound = true;
                        break;
                    }
                }
            } catch (e) {
                // Continue to next selector
            }
        }

        if (buttonFound && generateButton) {
            await generateButton.click();
            console.log('📤 Generate button clicked - wheel generation started');
        } else {
            // List all buttons for debugging
            const allButtons = await this.page.locator('button').all();
            console.log(`🔍 Found ${allButtons.length} buttons on page:`);
            for (let i = 0; i < Math.min(allButtons.length, 10); i++) {
                const buttonText = await allButtons[i].textContent();
                const isVisible = await allButtons[i].isVisible();
                console.log(`  ${i + 1}. "${buttonText}" (visible: ${isVisible})`);
            }

            this.results.errors.push('Generate button not found');
            return;
        }
        
        // Monitor for progress bar appearance and wheel generation completion
        let progressBarFound = false;
        let wheelGenerationCompleted = false;
        let progressBarUpdating = false;
        let lastProgressCount = 0;
        let attempts = 0;
        const maxAttempts = 70; // 70 seconds to account for 50+ second wheel generation

        console.log('🔍 Starting comprehensive monitoring for 70 seconds...');

        while (attempts < maxAttempts && !wheelGenerationCompleted) {
            await this.page.waitForTimeout(1000);
            attempts++;

            // Check progress bar state
            const progressBarState = await this.page.evaluate(() => {
                const appShell = document.querySelector('app-shell');
                if (appShell && appShell.shadowRoot) {
                    const progressModal = appShell.shadowRoot.querySelector('.progress-modal-overlay');
                    const progressBar = appShell.shadowRoot.querySelector('real-time-progress-bar');
                    const showProgressBar = appShell.showProgressBar;
                    return {
                        modalExists: !!progressModal,
                        modalVisible: progressModal && !progressModal.hidden,
                        progressBarExists: !!progressBar,
                        showProgressBar: showProgressBar,
                        progressTitle: appShell.progressTitle || 'Unknown'
                    };
                }
                return { modalExists: false, modalVisible: false, progressBarExists: false, showProgressBar: false, progressTitle: 'Unknown' };
            });

            // Check wheel state
            const wheelState = await this.page.evaluate(() => {
                const appShell = document.querySelector('app-shell');
                if (appShell && appShell.shadowRoot) {
                    const wheelComponent = appShell.shadowRoot.querySelector('game-wheel');
                    const wheelData = appShell.wheelData;
                    return {
                        wheelComponentExists: !!wheelComponent,
                        wheelDataExists: !!wheelData,
                        wheelSegments: wheelData?.segments?.length || 0,
                        wheelDataSample: wheelData?.segments?.[0] || null
                    };
                }
                return { wheelComponentExists: false, wheelDataExists: false, wheelSegments: 0, wheelDataSample: null };
            });

            // Track progress bar appearance
            if (!progressBarFound && (progressBarState.modalExists || progressBarState.showProgressBar)) {
                progressBarFound = true;
                this.results.progressBarAppeared = true;
                console.log(`✅ Progress bar appeared after ${attempts} seconds`);
                console.log(`   Modal exists: ${progressBarState.modalExists}, Visible: ${progressBarState.modalVisible}`);
                console.log(`   Progress bar exists: ${progressBarState.progressBarExists}, Show: ${progressBarState.showProgressBar}`);
                console.log(`   Title: ${progressBarState.progressTitle}`);
            }

            // Track progress updates
            if (this.results.progressUpdatesReceived > lastProgressCount) {
                progressBarUpdating = true;
                lastProgressCount = this.results.progressUpdatesReceived;
                console.log(`📊 Progress update #${this.results.progressUpdatesReceived} received at ${attempts}s`);
            }

            // Check for wheel generation completion
            if (wheelState.wheelDataExists && wheelState.wheelSegments > 0) {
                wheelGenerationCompleted = true;
                this.results.wheelGenerationCompleted = true;
                console.log(`🎡 Wheel generation completed after ${attempts} seconds!`);
                console.log(`   Wheel segments: ${wheelState.wheelSegments}`);
                console.log(`   Sample segment:`, wheelState.wheelDataSample);
                break;
            }

            // Periodic status updates
            if (attempts % 10 === 0) {
                console.log(`⏳ Status at ${attempts}s:`);
                console.log(`   Progress updates received: ${this.results.progressUpdatesReceived}`);
                console.log(`   Progress bar appeared: ${progressBarFound}`);
                console.log(`   Progress bar updating: ${progressBarUpdating}`);
                console.log(`   Progress bar visible: ${progressBarState.showProgressBar}`);
                console.log(`   Wheel segments: ${wheelState.wheelSegments}`);

                // Check if progress bar stopped updating (potential issue)
                if (progressBarFound && !progressBarUpdating && this.results.progressUpdatesReceived === lastProgressCount && attempts > 20) {
                    console.warn(`⚠️  Progress bar not updating since ${attempts - 10}s - potential issue!`);
                }
                progressBarUpdating = false; // Reset for next interval
            }
        }

        // Final validation
        if (!progressBarFound) {
            this.results.errors.push('Progress bar never appeared during 70-second observation');
        }

        if (!wheelGenerationCompleted) {
            this.results.errors.push('Wheel generation did not complete within 70 seconds');
            console.log('❌ Wheel generation did not complete - checking final state...');

            // Final wheel state check
            const finalWheelState = await this.page.evaluate(() => {
                const appShell = document.querySelector('app-shell');
                if (appShell && appShell.shadowRoot) {
                    const wheelData = appShell.wheelData;
                    return {
                        wheelDataExists: !!wheelData,
                        wheelSegments: wheelData?.segments?.length || 0,
                        rawWheelData: wheelData
                    };
                }
                return { wheelDataExists: false, wheelSegments: 0, rawWheelData: null };
            });

            console.log('🔍 Final wheel state:', finalWheelState);
        }

        // Check if progress bar is still visible after completion
        const finalProgressState = await this.page.evaluate(() => {
            const appShell = document.querySelector('app-shell');
            if (appShell && appShell.shadowRoot) {
                const progressModal = appShell.shadowRoot.querySelector('.progress-modal-overlay');
                return {
                    modalStillVisible: progressModal && !progressModal.hidden,
                    showProgressBar: appShell.showProgressBar
                };
            }
            return { modalStillVisible: false, showProgressBar: false };
        });

        if (finalProgressState.modalStillVisible || finalProgressState.showProgressBar) {
            console.log('⚠️  Progress bar still visible after completion - this may be expected for 2-second delay');
        } else {
            console.log('✅ Progress bar properly hidden after completion');
        }
    }

    async generateReport() {
        console.log('\n📊 FINAL TEST RESULTS:');
        console.log('='.repeat(60));
        
        console.log(`✅ WebSocket Connected: ${this.results.websocketConnected}`);
        console.log(`📊 Progress Updates Received: ${this.results.progressUpdatesReceived}`);
        console.log(`📊 Progress Bar Appeared: ${this.results.progressBarAppeared}`);
        console.log(`📊 Progress Bar Updated During Workflow: ${this.results.progressBarUpdatedDuringWorkflow}`);
        console.log(`🎯 Wheel Generation Completed: ${this.results.wheelGenerationCompleted}`);
        console.log(`🎡 Wheel Populated After Completion: ${this.results.wheelPopulatedAfterCompletion}`);

        if (this.results.errors.length > 0) {
            console.log(`❌ Errors: ${this.results.errors.length}`);
            this.results.errors.forEach((error, index) => {
                console.log(`   ${index + 1}. ${error}`);
            });
        }

        // Overall assessment
        const score = [
            this.results.websocketConnected,
            this.results.progressUpdatesReceived > 0,
            this.results.progressBarAppeared,
            this.results.progressBarUpdatedDuringWorkflow,
            this.results.wheelGenerationCompleted,
            this.results.wheelPopulatedAfterCompletion
        ].filter(Boolean).length;
        
        console.log(`\n🎯 Overall Score: ${score}/6`);

        if (score === 6) {
            console.log('🎉 PERFECT! Progress bar system is fully functional!');
        } else if (score >= 4) {
            console.log('✅ GOOD: Most components working, minor issues to address');
        } else if (score >= 2) {
            console.log('⚠️  PARTIAL SUCCESS: Some components working, needs investigation');
        } else {
            console.log('❌ SYSTEM ISSUES: Major problems detected');
        }

        // Specific issue identification
        if (!this.results.progressBarUpdatedDuringWorkflow && this.results.progressUpdatesReceived === 0) {
            console.log('🔍 ISSUE IDENTIFIED: No progress updates during workflow - WebSocket or backend issue');
        }
        if (!this.results.wheelPopulatedAfterCompletion && this.results.wheelGenerationCompleted) {
            console.log('🔍 ISSUE IDENTIFIED: Wheel generation completed but wheel not populated - frontend refresh issue');
        }
        
        return score;
    }

    async cleanup() {
        if (this.browser) {
            await this.browser.close();
        }
    }

    async run() {
        try {
            await this.initialize();
            await this.testProgressBarSystem();
            const score = await this.generateReport();
            return score;
        } catch (error) {
            console.error('❌ Test failed:', error);
            return 0;
        } finally {
            await this.cleanup();
        }
    }
}

// Run the test
const port = process.argv[2] || 3000;
const test = new FinalProgressBarTest(port);
test.run().then(score => {
    process.exit(score >= 4 ? 0 : 1); // Exit 0 if score is 4+ out of 6
}).catch(console.error);
