#!/usr/bin/env node

/**
 * Test Frontend Domain Color Service
 * 
 * This test verifies that the frontend domain color service works correctly
 * with the domain codes produced by the clean architecture backend.
 */

import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Get the directory of this script
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Read and evaluate the domain color service
const serviceCode = readFileSync(join(__dirname, '../src/services/domainColorService.js'), 'utf8');

// Create a simple module system for the service
const module = { exports: {} };
const exports = module.exports;

// Evaluate the service code
eval(serviceCode);

// Extract the functions
const { 
    getDomainColor, 
    validateWheelDomains, 
    applyColorsToWheel,
    getDomainColorWithOpacity,
    getContrastingTextColor
} = module.exports;

console.log('🎨 Testing Frontend Domain Color Service');
console.log('=' .repeat(60));

function testDomainColorMapping() {
    console.log('\n🔍 Testing Domain Color Mapping');
    console.log('-'.repeat(40));
    
    // Test domains from the backend test results
    const testDomains = [
        'explor_travel', 'refl_meditate', 'soc_empathy', 'creative_visual',
        'phys_dance', 'intel_strategic', 'soc_comm', 'emot_aware',
        'prod_health', 'leisure_relax'
    ];
    
    let allValid = true;
    
    for (const domain of testDomains) {
        const color = getDomainColor(domain);
        const isValid = color && color.startsWith('#') && color.length === 7 && color !== '#95A5A6';
        
        console.log(`  ${isValid ? '✅' : '❌'} ${domain.padEnd(20)} → ${color}`);
        
        if (!isValid) allValid = false;
    }
    
    return allValid;
}

function testColorUtilities() {
    console.log('\n🛠️ Testing Color Utilities');
    console.log('-'.repeat(40));
    
    const testDomain = 'phys_dance';
    
    // Test opacity
    const colorWithOpacity = getDomainColorWithOpacity(testDomain, 0.3);
    const opacityValid = colorWithOpacity.startsWith('rgba(') && colorWithOpacity.includes('0.3');
    console.log(`  ${opacityValid ? '✅' : '❌'} Opacity: ${testDomain} → ${colorWithOpacity}`);
    
    // Test contrasting text
    const textColor = getContrastingTextColor(testDomain);
    const textValid = textColor === '#000000' || textColor === '#FFFFFF';
    console.log(`  ${textValid ? '✅' : '❌'} Text Color: ${testDomain} → ${textColor}`);
    
    return opacityValid && textValid;
}

function testWheelValidation() {
    console.log('\n🎡 Testing Wheel Validation');
    console.log('-'.repeat(40));
    
    // Test wheel data similar to backend output
    const testWheel = {
        items: [
            { name: 'Local Neighborhood Exploration', domain: 'explor_travel' },
            { name: 'Progressive Body Scan', domain: 'refl_meditate' },
            { name: 'Active Empathy Practice', domain: 'soc_empathy' },
            { name: 'Personal Time Audit', domain: 'creative_visual' }
        ]
    };
    
    const validation = validateWheelDomains(testWheel);
    console.log(`  ${validation.valid ? '✅' : '❌'} Validation: ${validation.valid ? 'PASS' : 'FAIL'}`);
    
    if (!validation.valid) {
        validation.issues.forEach(issue => console.log(`    - ${issue}`));
    }
    
    return validation.valid;
}

function testColorApplication() {
    console.log('\n🎨 Testing Color Application');
    console.log('-'.repeat(40));
    
    // Test wheel data without colors (like from backend)
    const testWheel = {
        items: [
            { name: 'Local Neighborhood Exploration', domain: 'explor_travel' },
            { name: 'Progressive Body Scan', domain: 'refl_meditate' },
            { name: 'Active Empathy Practice', domain: 'soc_empathy' },
            { name: 'Personal Time Audit', domain: 'creative_visual' }
        ]
    };
    
    // Apply colors
    const coloredWheel = applyColorsToWheel({ ...testWheel });
    
    let allColored = true;
    coloredWheel.items.forEach((item, index) => {
        const hasColor = item.color && item.color.startsWith('#') && item.color !== '#95A5A6';
        console.log(`  ${hasColor ? '✅' : '❌'} Item ${index + 1}: ${item.name} → ${item.color || 'missing'}`);
        
        if (!hasColor) allColored = false;
    });
    
    return allColored;
}

function testEdgeCases() {
    console.log('\n🔧 Testing Edge Cases');
    console.log('-'.repeat(40));
    
    // Test unknown domain
    const unknownColor = getDomainColor('unknown_domain');
    const unknownValid = unknownColor === '#95A5A6';
    console.log(`  ${unknownValid ? '✅' : '❌'} Unknown domain → ${unknownColor}`);
    
    // Test null/undefined
    const nullColor = getDomainColor(null);
    const nullValid = nullColor === '#95A5A6';
    console.log(`  ${nullValid ? '✅' : '❌'} Null domain → ${nullColor}`);
    
    // Test empty wheel
    const emptyValidation = validateWheelDomains({ items: [] });
    const emptyValid = !emptyValidation.valid;
    console.log(`  ${emptyValid ? '✅' : '❌'} Empty wheel validation → ${emptyValidation.valid ? 'PASS' : 'FAIL'}`);
    
    return unknownValid && nullValid && emptyValid;
}

async function runTests() {
    try {
        // Run all tests
        const domainMappingWorks = testDomainColorMapping();
        const colorUtilitiesWork = testColorUtilities();
        const wheelValidationWorks = testWheelValidation();
        const colorApplicationWorks = testColorApplication();
        const edgeCasesWork = testEdgeCases();
        
        // Final results
        console.log('\n' + '='.repeat(60));
        console.log('🏁 FINAL TEST RESULTS');
        console.log('='.repeat(60));
        console.log(`🔍 Domain Color Mapping: ${domainMappingWorks ? '✅ PASS' : '❌ FAIL'}`);
        console.log(`🛠️ Color Utilities: ${colorUtilitiesWork ? '✅ PASS' : '❌ FAIL'}`);
        console.log(`🎡 Wheel Validation: ${wheelValidationWorks ? '✅ PASS' : '❌ FAIL'}`);
        console.log(`🎨 Color Application: ${colorApplicationWorks ? '✅ PASS' : '❌ FAIL'}`);
        console.log(`🔧 Edge Cases: ${edgeCasesWork ? '✅ PASS' : '❌ FAIL'}`);
        
        const overallSuccess = domainMappingWorks && colorUtilitiesWork && 
                              wheelValidationWorks && colorApplicationWorks && edgeCasesWork;
        
        console.log(`\n🎯 Overall Result: ${overallSuccess ? '✅ ALL TESTS PASS' : '❌ SOME TESTS FAILED'}`);
        
        if (overallSuccess) {
            console.log('\n🎉 Frontend domain color service is working perfectly!');
            console.log('   - All domain codes map to proper colors');
            console.log('   - Color utilities work correctly');
            console.log('   - Wheel validation catches issues');
            console.log('   - Color application works on backend data');
            console.log('   - Edge cases are handled gracefully');
        } else {
            console.log('\n🔧 Some issues found that need to be addressed.');
        }
        
        process.exit(overallSuccess ? 0 : 1);
        
    } catch (error) {
        console.error('❌ Test execution failed:', error);
        process.exit(1);
    }
}

// Run the tests
runTests();
