#!/usr/bin/env node

/**
 * WITNESS DUAL-WHEEL ISSUE - Real-Time Observation Test
 * 
 * This test will actually witness the dual-wheel issue by:
 * 1. Generating a wheel and capturing its data
 * 2. Removing an item and capturing the new data
 * 3. Observing modal closing behavior
 * 4. Documenting exactly what happens to identify root cause
 */

const puppeteer = require('puppeteer');

class DualWheelIssueWitness {
    constructor(port = 3002) {
        this.port = port;
        this.baseUrl = `http://localhost:${port}`;
        this.browser = null;
        this.page = null;
        this.observations = [];
        this.wheelDataSnapshots = [];
    }

    async runWitnessTest() {
        console.log('👁️ ===== WITNESSING DUAL-WHEEL ISSUE IN REAL-TIME =====');
        console.log(`🔍 Observing on ${this.baseUrl}`);
        console.log();

        try {
            await this.setupBrowser();
            await this.setupAuthentication();
            await this.witnessWheelGeneration();
            await this.witnessItemRemoval();
            await this.witnessModalClosing();
            await this.analyzeObservations();
        } catch (error) {
            console.error('❌ Witness test failed:', error);
        } finally {
            if (this.browser) {
                await this.browser.close();
            }
        }
    }

    async setupBrowser() {
        console.log('🚀 Setting up browser for real-time observation...');
        this.browser = await puppeteer.launch({
            headless: false,
            defaultViewport: { width: 1400, height: 900 },
            args: ['--no-sandbox', '--disable-setuid-sandbox'],
            slowMo: 100 // Slow down for better observation
        });
        this.page = await this.browser.newPage();
        
        // Capture ALL console messages for debugging
        this.page.on('console', msg => {
            const text = msg.text();
            console.log(`   🔍 Console: ${text}`);
            this.observations.push({
                type: 'console',
                timestamp: Date.now(),
                message: text
            });
        });
        
        // Monitor network requests
        this.page.on('response', response => {
            if (response.url().includes('wheel-items') || response.url().includes('wheel')) {
                console.log(`   📡 Network: ${response.status()} ${response.url()}`);
                this.observations.push({
                    type: 'network',
                    timestamp: Date.now(),
                    url: response.url(),
                    status: response.status()
                });
            }
        });
        
        await this.page.goto(this.baseUrl);
        await new Promise(resolve => setTimeout(resolve, 3000));
        console.log('   ✅ Browser setup complete');
    }

    async setupAuthentication() {
        console.log('🔐 Setting up authentication...');
        
        try {
            // Check if login modal is present
            const loginModal = await this.page.$('.modal:has(.login-form), .login-modal, [data-testid="login-modal"]');
            if (loginModal) {
                console.log('   🔑 Login modal detected, performing admin login...');
                
                // Fill in admin credentials
                const usernameInput = await this.page.$('input[name="username"], input[type="text"], input[placeholder*="username" i]');
                if (usernameInput) {
                    await usernameInput.type('admin');
                    console.log('   ✅ Entered username: admin');
                }
                
                const passwordInput = await this.page.$('input[name="password"], input[type="password"], input[placeholder*="password" i]');
                if (passwordInput) {
                    await passwordInput.type('admin123');
                    console.log('   ✅ Entered password');
                }
                
                // Click login button
                const loginButton = await this.page.$('button[type="submit"], .btn-primary');
                if (loginButton) {
                    await loginButton.click();
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    console.log('   ✅ Clicked login button');
                }
            }

            // Open debug panel
            await this.page.keyboard.down('Control');
            await this.page.keyboard.down('Shift');
            await this.page.keyboard.press('KeyD');
            await this.page.keyboard.up('Shift');
            await this.page.keyboard.up('Control');
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Select PhiPhi user (ID 2)
            const userSelect = await this.page.$('select[data-testid="user-select"]');
            if (userSelect) {
                await userSelect.selectOption('2');
                console.log('   ✅ Selected PhiPhi user');
            }

            // Select LLM
            const llmSelect = await this.page.$('select[data-testid="llm-select"]');
            if (llmSelect) {
                await llmSelect.selectOption('mistral-small-latest');
                console.log('   ✅ Selected LLM');
            }

            // Apply settings
            const applyButton = await this.page.$('button[data-testid="apply-settings"]');
            if (applyButton) {
                await applyButton.click();
                await new Promise(resolve => setTimeout(resolve, 2000));
                console.log('   ✅ Applied settings');
            }

        } catch (error) {
            console.log('   ⚠️ Authentication setup failed, continuing with default user');
        }
    }

    async witnessWheelGeneration() {
        console.log('🎡 STEP 1: Witnessing wheel generation...');
        
        try {
            // Look for generate button
            let generateButton = await this.page.$('button[data-testid="generate-wheel"]');
            if (!generateButton) {
                generateButton = await this.page.$('button.btn-primary');
            }
            if (!generateButton) {
                generateButton = await this.page.evaluateHandle(() => {
                    const buttons = Array.from(document.querySelectorAll('button'));
                    return buttons.find(btn => btn.textContent.toLowerCase().includes('generate'));
                });
            }
            
            if (generateButton && generateButton.asElement) {
                await generateButton.asElement().click();
                console.log('   🔄 Wheel generation started...');
                
                // Wait for wheel generation to complete (up to 60 seconds)
                await this.page.waitForFunction(() => {
                    const appShell = document.querySelector('app-shell');
                    return appShell && appShell.wheelData && appShell.wheelData.segments && appShell.wheelData.segments.length > 0;
                }, { timeout: 60000 });
                
                // Capture initial wheel data
                const initialWheelData = await this.page.evaluate(() => {
                    const appShell = document.querySelector('app-shell');
                    return appShell && appShell.wheelData ? JSON.parse(JSON.stringify(appShell.wheelData)) : null;
                });
                
                if (initialWheelData && initialWheelData.segments.length > 0) {
                    this.wheelDataSnapshots.push({
                        step: 'INITIAL_GENERATION',
                        timestamp: Date.now(),
                        data: initialWheelData,
                        segmentCount: initialWheelData.segments.length,
                        colors: initialWheelData.segments.map(s => ({ name: s.name || s.text, color: s.color }))
                    });
                    
                    console.log(`   ✅ INITIAL WHEEL: ${initialWheelData.segments.length} segments generated`);
                    console.log(`   🎨 INITIAL COLORS:`);
                    initialWheelData.segments.forEach((segment, index) => {
                        console.log(`      ${index + 1}. ${segment.name || segment.text}: ${segment.color}`);
                    });
                } else {
                    console.log('   ❌ Wheel generation failed or no segments');
                }
                
            } else {
                console.log('   ❌ Generate button not found');
            }
            
        } catch (error) {
            console.log(`   ❌ Wheel generation witness failed: ${error.message}`);
        }
    }

    async witnessItemRemoval() {
        console.log('🗑️ STEP 2: Witnessing item removal...');
        
        try {
            // Wait a moment for UI to stabilize
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // Find a remove button
            const removeButton = await this.page.$('.remove-activity-btn, .activity-item .btn-danger, button[title*="Remove"]');
            if (removeButton) {
                // Capture wheel data BEFORE removal
                const beforeRemoval = await this.page.evaluate(() => {
                    const appShell = document.querySelector('app-shell');
                    return appShell && appShell.wheelData ? JSON.parse(JSON.stringify(appShell.wheelData)) : null;
                });
                
                if (beforeRemoval) {
                    this.wheelDataSnapshots.push({
                        step: 'BEFORE_REMOVAL',
                        timestamp: Date.now(),
                        data: beforeRemoval,
                        segmentCount: beforeRemoval.segments.length,
                        colors: beforeRemoval.segments.map(s => ({ name: s.name || s.text, color: s.color }))
                    });
                    
                    console.log(`   📊 BEFORE REMOVAL: ${beforeRemoval.segments.length} segments`);
                }
                
                console.log('   🗑️ Clicking remove button...');
                await removeButton.click();
                
                // Wait for removal to process
                await new Promise(resolve => setTimeout(resolve, 3000));
                
                // Capture wheel data AFTER removal
                const afterRemoval = await this.page.evaluate(() => {
                    const appShell = document.querySelector('app-shell');
                    return appShell && appShell.wheelData ? JSON.parse(JSON.stringify(appShell.wheelData)) : null;
                });
                
                if (afterRemoval) {
                    this.wheelDataSnapshots.push({
                        step: 'AFTER_REMOVAL',
                        timestamp: Date.now(),
                        data: afterRemoval,
                        segmentCount: afterRemoval.segments.length,
                        colors: afterRemoval.segments.map(s => ({ name: s.name || s.text, color: s.color }))
                    });
                    
                    console.log(`   📊 AFTER REMOVAL: ${afterRemoval.segments.length} segments`);
                    console.log(`   🎨 COLORS AFTER REMOVAL:`);
                    afterRemoval.segments.forEach((segment, index) => {
                        const colorStatus = segment.color && segment.color !== '#95A5A6' ? '✅' : '❌ GREY';
                        console.log(`      ${index + 1}. ${segment.name || segment.text}: ${segment.color} ${colorStatus}`);
                    });
                }
                
            } else {
                console.log('   ⚠️ No remove button found');
            }
            
        } catch (error) {
            console.log(`   ❌ Item removal witness failed: ${error.message}`);
        }
    }

    async witnessModalClosing() {
        console.log('🔄 STEP 3: Witnessing modal closing behavior...');
        
        try {
            // Capture wheel data before any modal operations
            const beforeModal = await this.page.evaluate(() => {
                const appShell = document.querySelector('app-shell');
                return appShell && appShell.wheelData ? JSON.parse(JSON.stringify(appShell.wheelData)) : null;
            });
            
            if (beforeModal) {
                this.wheelDataSnapshots.push({
                    step: 'BEFORE_MODAL_CLOSE',
                    timestamp: Date.now(),
                    data: beforeModal,
                    segmentCount: beforeModal.segments.length,
                    colors: beforeModal.segments.map(s => ({ name: s.name || s.text, color: s.color }))
                });
            }
            
            // Close any open modals
            const closeButtons = await this.page.$$('.modal-close, .close, button[aria-label="Close"]');
            for (const closeButton of closeButtons) {
                try {
                    console.log('   🔄 Closing modal...');
                    await closeButton.click();
                    await new Promise(resolve => setTimeout(resolve, 1000));
                } catch (e) {
                    // Ignore if button is not clickable
                }
            }
            
            // Wait for any state changes to complete
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            // Capture wheel data AFTER modal closing
            const afterModal = await this.page.evaluate(() => {
                const appShell = document.querySelector('app-shell');
                return appShell && appShell.wheelData ? JSON.parse(JSON.stringify(appShell.wheelData)) : null;
            });
            
            if (afterModal) {
                this.wheelDataSnapshots.push({
                    step: 'AFTER_MODAL_CLOSE',
                    timestamp: Date.now(),
                    data: afterModal,
                    segmentCount: afterModal.segments.length,
                    colors: afterModal.segments.map(s => ({ name: s.name || s.text, color: s.color }))
                });
                
                console.log(`   📊 AFTER MODAL CLOSE: ${afterModal.segments.length} segments`);
                console.log(`   🎨 FINAL COLORS:`);
                afterModal.segments.forEach((segment, index) => {
                    const colorStatus = segment.color && segment.color !== '#95A5A6' ? '✅' : '❌ GREY';
                    console.log(`      ${index + 1}. ${segment.name || segment.text}: ${segment.color} ${colorStatus}`);
                });
            }
            
        } catch (error) {
            console.log(`   ❌ Modal closing witness failed: ${error.message}`);
        }
    }

    async analyzeObservations() {
        console.log();
        console.log('🔍 ===== DUAL-WHEEL ISSUE ANALYSIS =====');
        
        console.log('📊 WHEEL DATA EVOLUTION:');
        this.wheelDataSnapshots.forEach((snapshot, index) => {
            console.log(`   ${index + 1}. ${snapshot.step}:`);
            console.log(`      - Segments: ${snapshot.segmentCount}`);
            console.log(`      - Timestamp: ${new Date(snapshot.timestamp).toISOString()}`);
            
            if (snapshot.colors.length > 0) {
                const greyCount = snapshot.colors.filter(c => !c.color || c.color === '#95A5A6').length;
                const coloredCount = snapshot.colors.length - greyCount;
                console.log(`      - Colors: ${coloredCount} colored, ${greyCount} grey`);
                
                if (greyCount > 0) {
                    console.log(`      ❌ GREY SEGMENTS DETECTED!`);
                }
            }
        });
        
        console.log();
        console.log('🔍 ROOT CAUSE ANALYSIS:');
        
        // Compare initial vs final
        if (this.wheelDataSnapshots.length >= 2) {
            const initial = this.wheelDataSnapshots[0];
            const final = this.wheelDataSnapshots[this.wheelDataSnapshots.length - 1];
            
            console.log(`   📊 Segment count change: ${initial.segmentCount} → ${final.segmentCount}`);
            
            // Check if it's a completely different wheel
            const initialNames = initial.colors.map(c => c.name).sort();
            const finalNames = final.colors.map(c => c.name).sort();
            const isDifferentWheel = JSON.stringify(initialNames) !== JSON.stringify(finalNames);
            
            if (isDifferentWheel) {
                console.log(`   ❌ COMPLETELY DIFFERENT WHEEL DETECTED!`);
                console.log(`   📋 Initial activities: ${initialNames.join(', ')}`);
                console.log(`   📋 Final activities: ${finalNames.join(', ')}`);
            }
            
            // Check for grey segments
            const finalGreyCount = final.colors.filter(c => !c.color || c.color === '#95A5A6').length;
            if (finalGreyCount > 0) {
                console.log(`   ❌ GREY SEGMENTS IN FINAL WHEEL: ${finalGreyCount}/${final.segmentCount}`);
            }
        }
        
        console.log();
        console.log('📝 OBSERVATIONS SUMMARY:');
        const consoleMessages = this.observations.filter(o => o.type === 'console');
        const networkRequests = this.observations.filter(o => o.type === 'network');
        
        console.log(`   📨 Console messages: ${consoleMessages.length}`);
        console.log(`   📡 Network requests: ${networkRequests.length}`);
        
        // Look for specific patterns
        const wheelDataMessages = consoleMessages.filter(o => 
            o.message.includes('wheel') || o.message.includes('WHEEL') || o.message.includes('🎡')
        );
        
        if (wheelDataMessages.length > 0) {
            console.log(`   🎡 Wheel-related messages: ${wheelDataMessages.length}`);
            wheelDataMessages.slice(-5).forEach(msg => {
                console.log(`      - ${msg.message}`);
            });
        }
        
        console.log();
        console.log('🎯 CONCLUSION:');
        if (this.wheelDataSnapshots.length > 0) {
            const hasIssue = this.wheelDataSnapshots.some(snapshot => 
                snapshot.colors.some(c => !c.color || c.color === '#95A5A6')
            );
            
            if (hasIssue) {
                console.log('❌ DUAL-WHEEL ISSUE CONFIRMED: Grey segments detected');
                console.log('🔧 NEXT STEPS: Analyze the exact moment when wheel data changes');
            } else {
                console.log('✅ No dual-wheel issue detected in this test run');
            }
        }
    }
}

// Run the witness test
const port = process.argv[2] || 3002;
const witness = new DualWheelIssueWitness(port);
witness.runWitnessTest().catch(console.error);
