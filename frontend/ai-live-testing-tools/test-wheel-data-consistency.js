/**
 * Test Wheel Data Consistency
 * 
 * This script can be run in the browser console to test wheel data consistency
 * during item removal operations.
 */

window.testWheelDataConsistency = function() {
  console.log('🔍 ===== WHEEL DATA CONSISTENCY TEST =====');
  
  const appShell = document.querySelector('app-shell');
  if (!appShell) {
    console.error('❌ App shell not found');
    return;
  }
  
  // Capture initial state
  function captureWheelState(label) {
    const state = {
      label,
      timestamp: new Date().toISOString(),
      appShellWheelData: appShell.wheelData ? JSON.parse(JSON.stringify(appShell.wheelData)) : null,
      stateMachineState: appShell.wheelStateMachine ? appShell.wheelStateMachine.currentState : null,
      stateMachineData: appShell.wheelStateMachine?.wheelData ? JSON.parse(JSON.stringify(appShell.wheelStateMachine.wheelData)) : null,
      gameWheelData: (() => {
        const gameWheel = document.querySelector('game-wheel');
        return gameWheel?.wheelData ? JSON.parse(JSON.stringify(gameWheel.wheelData)) : null;
      })()
    };
    
    console.log(`📸 ${label}:`);
    console.log(`   App Shell: ${state.appShellWheelData?.segments?.length || 0} segments`);
    console.log(`   State Machine: ${state.stateMachineData?.segments?.length || 0} segments (${state.stateMachineState})`);
    console.log(`   Game Wheel: ${state.gameWheelData?.segments?.length || 0} segments`);
    
    if (state.appShellWheelData) {
      console.log(`   Wheel ID: ${state.appShellWheelData.wheelId}`);
      state.appShellWheelData.segments.forEach((seg, i) => {
        console.log(`     ${i + 1}. ${seg.name || seg.text} (${seg.id})`);
      });
    }
    
    return state;
  }
  
  // Test wheel data consistency
  const initialState = captureWheelState('INITIAL_STATE');
  
  if (!initialState.appShellWheelData || !initialState.appShellWheelData.segments || initialState.appShellWheelData.segments.length === 0) {
    console.log('⚠️ No wheel data found. Generate a wheel first.');
    return;
  }
  
  // Check for inconsistencies between data sources
  console.log('\n🔍 CHECKING DATA CONSISTENCY:');
  
  const appShellCount = initialState.appShellWheelData?.segments?.length || 0;
  const stateMachineCount = initialState.stateMachineData?.segments?.length || 0;
  const gameWheelCount = initialState.gameWheelData?.segments?.length || 0;
  
  if (appShellCount !== stateMachineCount || appShellCount !== gameWheelCount) {
    console.log('❌ INCONSISTENCY DETECTED:');
    console.log(`   App Shell: ${appShellCount} segments`);
    console.log(`   State Machine: ${stateMachineCount} segments`);
    console.log(`   Game Wheel: ${gameWheelCount} segments`);
  } else {
    console.log('✅ All data sources have consistent segment counts');
  }
  
  // Check wheel IDs
  const appShellWheelId = initialState.appShellWheelData?.wheelId;
  const stateMachineWheelId = initialState.stateMachineData?.wheelId;
  const gameWheelWheelId = initialState.gameWheelData?.wheelId;
  
  if (appShellWheelId !== stateMachineWheelId || appShellWheelId !== gameWheelWheelId) {
    console.log('❌ WHEEL ID INCONSISTENCY:');
    console.log(`   App Shell: ${appShellWheelId}`);
    console.log(`   State Machine: ${stateMachineWheelId}`);
    console.log(`   Game Wheel: ${gameWheelWheelId}`);
  } else {
    console.log('✅ All data sources have consistent wheel IDs');
  }
  
  // Test removal simulation
  console.log('\n🗑️ SIMULATING ITEM REMOVAL:');
  
  if (initialState.appShellWheelData.segments.length > 1) {
    const itemToRemove = initialState.appShellWheelData.segments[0];
    console.log(`🎯 Would remove: ${itemToRemove.name || itemToRemove.text} (${itemToRemove.id})`);
    
    // Find remove buttons
    const removeButtons = document.querySelectorAll('.remove-activity-btn, button[title*="Remove"], button[data-action="remove"]');
    console.log(`🔍 Found ${removeButtons.length} remove buttons`);
    
    if (removeButtons.length > 0) {
      console.log('✅ Remove buttons are available for testing');
      console.log('💡 To test removal, run: document.querySelector(".remove-activity-btn").click()');
    } else {
      console.log('⚠️ No remove buttons found');
    }
  } else {
    console.log('⚠️ Only one item in wheel, cannot test removal');
  }
  
  console.log('\n🔧 DEBUGGING HELPERS:');
  console.log('💡 To monitor wheel data changes, run:');
  console.log('   window.monitorWheelDataChanges()');
  console.log('💡 To capture state after removal, run:');
  console.log('   window.captureWheelState("AFTER_REMOVAL")');
  
  // Store capture function globally for easy access
  window.captureWheelState = captureWheelState;
  
  console.log('🔍 ===== END WHEEL DATA CONSISTENCY TEST =====');
};

window.monitorWheelDataChanges = function() {
  console.log('👁️ Starting wheel data change monitoring...');
  
  const appShell = document.querySelector('app-shell');
  if (!appShell) {
    console.error('❌ App shell not found');
    return;
  }
  
  let lastWheelData = null;
  let changeCount = 0;
  
  const monitor = setInterval(() => {
    const currentWheelData = appShell.wheelData;
    
    if (currentWheelData !== lastWheelData) {
      changeCount++;
      console.log(`🔄 Wheel data change #${changeCount} detected:`);
      
      if (currentWheelData) {
        console.log(`   New data: ${currentWheelData.segments?.length || 0} segments, wheelId: ${currentWheelData.wheelId}`);
        if (currentWheelData.segments) {
          currentWheelData.segments.forEach((seg, i) => {
            console.log(`     ${i + 1}. ${seg.name || seg.text} (${seg.id})`);
          });
        }
      } else {
        console.log('   New data: null');
      }
      
      lastWheelData = currentWheelData;
    }
  }, 500);
  
  // Stop monitoring after 30 seconds
  setTimeout(() => {
    clearInterval(monitor);
    console.log('👁️ Wheel data monitoring stopped');
  }, 30000);
  
  console.log('👁️ Monitoring wheel data changes for 30 seconds...');
  return monitor;
};

// Auto-run the test
console.log('🚀 Wheel Data Consistency Test loaded. Run window.testWheelDataConsistency() to start.');
