#!/usr/bin/env node

/**
 * Dashboard UI Interaction Tester
 * 
 * This tool simulates real browser interactions with the WebSocket dashboard
 * to identify UI issues, unresponsive features, and missing data.
 */

const WebSocket = require('ws');
const { JSDOM } = require('jsdom');
const fs = require('fs');
const path = require('path');

class DashboardUITester {
    constructor() {
        this.adminSocket = null;
        this.testClients = [];
        this.dom = null;
        this.window = null;
        this.document = null;
        this.errors = [];
        this.testResults = [];
    }

    async runTests() {
        console.log('🎮 Dashboard UI Interaction Testing');
        console.log('====================================\n');

        try {
            await this.setupDOMEnvironment();
            await this.connectToAdmin();
            await this.createTestClients();
            await this.testUIInteractions();
            await this.testMessageInspector();
            await this.testSessionFocus();
            await this.generateReport();
        } catch (error) {
            console.error('❌ Test suite failed:', error.message);
            this.errors.push(`Test suite failure: ${error.message}`);
        } finally {
            await this.cleanup();
        }
    }

    async setupDOMEnvironment() {
        console.log('🔬 Test 1: Setting up DOM environment');
        
        try {
            // Load the dashboard HTML
            const dashboardPath = path.join(__dirname, '../../backend/apps/admin_tools/templates/admin_tools/connection_dashboard.html');
            const htmlContent = fs.readFileSync(dashboardPath, 'utf8');
            
            // Create JSDOM environment
            this.dom = new JSDOM(htmlContent, {
                url: 'http://localhost:8000',
                pretendToBeVisual: true,
                resources: 'usable'
            });
            
            this.window = this.dom.window;
            this.document = this.window.document;
            
            // Mock WebSocket in the DOM environment
            this.window.WebSocket = WebSocket;
            
            // Mock browser APIs
            this.window.navigator = {
                clipboard: {
                    writeText: (text) => Promise.resolve()
                }
            };
            
            console.log('✅ PASSED: DOM environment setup complete');
            this.testResults.push('DOM environment: WORKING');
            
        } catch (error) {
            console.log('❌ FAILED: DOM environment setup failed');
            this.errors.push(`DOM setup failed: ${error.message}`);
        }
    }

    async connectToAdmin() {
        console.log('\n🔬 Test 2: Admin WebSocket connection');
        
        return new Promise((resolve) => {
            this.adminSocket = new WebSocket('ws://localhost:8000/ws/connection-monitor/');
            
            this.adminSocket.on('open', () => {
                console.log('✅ PASSED: Admin WebSocket connected');
                this.testResults.push('Admin WebSocket: CONNECTED');
                resolve();
            });
            
            this.adminSocket.on('error', (error) => {
                console.log('❌ FAILED: Admin WebSocket connection failed');
                this.errors.push(`Admin WebSocket failed: ${error.message}`);
                resolve();
            });
            
            setTimeout(() => {
                if (this.adminSocket.readyState !== WebSocket.OPEN) {
                    console.log('❌ FAILED: Admin WebSocket connection timeout');
                    this.errors.push('Admin WebSocket connection timeout');
                }
                resolve();
            }, 5000);
        });
    }

    async createTestClients() {
        console.log('\n🔬 Test 3: Creating test clients for UI data');
        
        const clientPromises = [];
        
        for (let i = 1; i <= 3; i++) {
            const promise = new Promise((resolve) => {
                const client = new WebSocket('ws://localhost:8000/ws/game/');
                
                client.on('open', () => {
                    // Send identification message
                    client.send(JSON.stringify({
                        type: 'chat_message',
                        content: {
                            message: `Test message from UI test client ${i}`,
                            user_profile_id: `ui-test-user-${i}`
                        }
                    }));
                    
                    this.testClients.push(client);
                    console.log(`✅ Test client ${i} connected`);
                    resolve();
                });
                
                client.on('error', () => {
                    console.log(`❌ Test client ${i} failed to connect`);
                    resolve();
                });
                
                setTimeout(resolve, 2000); // Timeout after 2 seconds
            });
            
            clientPromises.push(promise);
        }
        
        await Promise.all(clientPromises);
        console.log(`✅ PASSED: Created ${this.testClients.length} test clients`);
        this.testResults.push(`Test clients: ${this.testClients.length} CREATED`);
    }

    async testUIInteractions() {
        console.log('\n🔬 Test 4: UI Element Interactions');
        
        // Test connection list functionality
        await this.testConnectionList();
        
        // Test modal functionality
        await this.testModalInteractions();
        
        // Test filter buttons
        await this.testFilterButtons();
        
        // Test action buttons
        await this.testActionButtons();
    }

    async testConnectionList() {
        console.log('📋 Testing connection list interactions...');
        
        try {
            const connectionList = this.document.getElementById('connectionList');
            if (!connectionList) {
                throw new Error('Connection list element not found');
            }
            
            // Simulate connection data update
            const mockConnections = [
                {
                    session_id: 'test-session-1',
                    user_id: 'ui-test-user-1',
                    status: 'connected',
                    duration: '0:02:30',
                    message_count: 5,
                    current_workflow: 'wheel_generation'
                },
                {
                    session_id: 'test-session-2',
                    user_id: 'ui-test-user-2',
                    status: 'connected',
                    duration: '0:01:15',
                    message_count: 3,
                    current_workflow: null
                }
            ];
            
            // Test connection list rendering
            connectionList.innerHTML = mockConnections.map(conn => `
                <div class="connection-item" data-session-id="${conn.session_id}">
                    <div class="connection-status ${conn.status}"></div>
                    <div class="connection-info">
                        <div class="connection-user">User ${conn.user_id}</div>
                        <div class="connection-details">
                            Session: ${conn.session_id.substring(0, 8)}... •
                            ${conn.current_workflow || 'No workflow'}
                        </div>
                    </div>
                    <div class="connection-metrics">
                        <div>${conn.message_count} messages</div>
                        <div>Connected ${conn.duration}</div>
                    </div>
                </div>
            `).join('');
            
            const connectionItems = connectionList.querySelectorAll('.connection-item');
            if (connectionItems.length === mockConnections.length) {
                console.log('✅ Connection list rendering: WORKING');
                this.testResults.push('Connection list rendering: WORKING');
            } else {
                throw new Error('Connection list rendering failed');
            }
            
        } catch (error) {
            console.log('❌ Connection list test failed:', error.message);
            this.errors.push(`Connection list: ${error.message}`);
        }
    }

    async testModalInteractions() {
        console.log('🔍 Testing modal interactions...');
        
        try {
            const modal = this.document.getElementById('connectionModal');
            if (!modal) {
                throw new Error('Connection modal not found');
            }
            
            // Test modal show/hide
            modal.classList.add('show');
            if (modal.classList.contains('show')) {
                console.log('✅ Modal show functionality: WORKING');
                this.testResults.push('Modal show: WORKING');
            }
            
            modal.classList.remove('show');
            if (!modal.classList.contains('show')) {
                console.log('✅ Modal hide functionality: WORKING');
                this.testResults.push('Modal hide: WORKING');
            }
            
        } catch (error) {
            console.log('❌ Modal test failed:', error.message);
            this.errors.push(`Modal interactions: ${error.message}`);
        }
    }

    async testFilterButtons() {
        console.log('🔽 Testing filter button functionality...');
        
        try {
            const filterButtons = this.document.querySelectorAll('.filter-btn');
            if (filterButtons.length === 0) {
                throw new Error('Filter buttons not found');
            }
            
            // Test filter button state changes
            filterButtons.forEach((btn, index) => {
                btn.classList.remove('active');
                if (index === 0) {
                    btn.classList.add('active');
                }
            });
            
            const activeButtons = this.document.querySelectorAll('.filter-btn.active');
            if (activeButtons.length === 1) {
                console.log('✅ Filter button states: WORKING');
                this.testResults.push('Filter buttons: WORKING');
            } else {
                throw new Error('Filter button state management failed');
            }
            
        } catch (error) {
            console.log('❌ Filter button test failed:', error.message);
            this.errors.push(`Filter buttons: ${error.message}`);
        }
    }

    async testActionButtons() {
        console.log('⚡ Testing action button availability...');
        
        const requiredButtons = [
            'refreshConnections',
            'exportData',
            'toggleMessageInspector',
            'toggleSessionFocus'
        ];
        
        let workingButtons = 0;
        
        requiredButtons.forEach(buttonId => {
            const button = this.document.querySelector(`[onclick*="${buttonId}"]`);
            if (button) {
                console.log(`✅ ${buttonId} button: FOUND`);
                workingButtons++;
            } else {
                console.log(`❌ ${buttonId} button: MISSING`);
                this.errors.push(`Missing button: ${buttonId}`);
            }
        });
        
        if (workingButtons === requiredButtons.length) {
            console.log('✅ All action buttons: AVAILABLE');
            this.testResults.push('Action buttons: ALL AVAILABLE');
        } else {
            console.log(`⚠️ Action buttons: ${workingButtons}/${requiredButtons.length} available`);
            this.testResults.push(`Action buttons: ${workingButtons}/${requiredButtons.length} AVAILABLE`);
        }
    }

    async testMessageInspector() {
        console.log('\n🔬 Test 5: Message Inspector UI');
        
        try {
            const messageInspector = this.document.getElementById('messageInspector');
            const messageFlow = this.document.getElementById('messageFlow');
            
            if (!messageInspector || !messageFlow) {
                throw new Error('Message inspector elements not found');
            }
            
            // Test message inspector visibility toggle
            messageInspector.style.display = 'block';
            if (messageInspector.style.display === 'block') {
                console.log('✅ Message inspector show: WORKING');
                this.testResults.push('Message inspector show: WORKING');
            }
            
            // Test message flow content
            messageFlow.innerHTML = `
                <div class="message-item chat_message">
                    <span class="message-timestamp">12:34:56</span>
                    <span class="message-type">chat_message</span>
                    <span class="message-content">Test message content</span>
                    <span class="message-size">1.2 KB</span>
                </div>
            `;
            
            const messageItems = messageFlow.querySelectorAll('.message-item');
            if (messageItems.length > 0) {
                console.log('✅ Message flow rendering: WORKING');
                this.testResults.push('Message flow: WORKING');
            }
            
        } catch (error) {
            console.log('❌ Message inspector test failed:', error.message);
            this.errors.push(`Message inspector: ${error.message}`);
        }
    }

    async testSessionFocus() {
        console.log('\n🔬 Test 6: Session Focus UI');
        
        try {
            const sessionFocusPanel = this.document.getElementById('sessionFocusPanel');
            const messageTimeline = this.document.getElementById('messageTimeline');
            
            if (!sessionFocusPanel || !messageTimeline) {
                throw new Error('Session focus elements not found');
            }
            
            // Test session focus panel visibility
            sessionFocusPanel.style.display = 'block';
            if (sessionFocusPanel.style.display === 'block') {
                console.log('✅ Session focus panel show: WORKING');
                this.testResults.push('Session focus panel: WORKING');
            }
            
            // Test timeline rendering
            messageTimeline.innerHTML = `
                <div class="timeline-line"></div>
                <div class="timeline-message frontend">
                    <div class="timeline-dot frontend"></div>
                    <div class="message-bubble frontend">
                        <div class="message-type-badge chat_message">chat_message</div>
                        <div class="message-content-preview">Test timeline message</div>
                    </div>
                </div>
            `;
            
            const timelineMessages = messageTimeline.querySelectorAll('.timeline-message');
            if (timelineMessages.length > 0) {
                console.log('✅ Timeline rendering: WORKING');
                this.testResults.push('Timeline rendering: WORKING');
            }
            
        } catch (error) {
            console.log('❌ Session focus test failed:', error.message);
            this.errors.push(`Session focus: ${error.message}`);
        }
    }

    async generateReport() {
        console.log('\n📊 DASHBOARD UI INTERACTION TEST REPORT');
        console.log('=========================================');
        console.log(`✅ Tests passed: ${this.testResults.length}`);
        console.log(`❌ Errors found: ${this.errors.length}`);
        
        if (this.testResults.length > 0) {
            console.log('\n✅ PASSED TESTS:');
            this.testResults.forEach((result, index) => {
                console.log(`  ${index + 1}. ${result}`);
            });
        }
        
        if (this.errors.length > 0) {
            console.log('\n❌ ERRORS FOUND:');
            this.errors.forEach((error, index) => {
                console.log(`  ${index + 1}. ${error}`);
            });
        }
        
        const overallStatus = this.errors.length === 0 ? 
            '✅ DASHBOARD UI FULLY FUNCTIONAL' : 
            '⚠️ DASHBOARD UI HAS MINOR ISSUES';
        
        console.log(`\n🎯 OVERALL ASSESSMENT:`);
        console.log(overallStatus);
        
        if (this.errors.length === 0) {
            console.log('\n🎉 All UI interactions working correctly!');
            console.log('🔍 Dashboard ready for production use');
        } else {
            console.log('\n🔧 RECOMMENDATIONS:');
            console.log('1. Fix missing UI elements');
            console.log('2. Test interactive features in browser');
            console.log('3. Verify WebSocket message handling');
        }
    }

    async cleanup() {
        console.log('\n🧹 Cleaning up test environment...');
        
        if (this.adminSocket) {
            this.adminSocket.close();
        }
        
        this.testClients.forEach(client => {
            if (client.readyState === WebSocket.OPEN) {
                client.close();
            }
        });
        
        if (this.dom) {
            this.dom.window.close();
        }
        
        console.log('✅ Cleanup complete');
    }
}

// Run the tests
if (require.main === module) {
    const tester = new DashboardUITester();
    tester.runTests().catch(console.error);
}

module.exports = DashboardUITester;
