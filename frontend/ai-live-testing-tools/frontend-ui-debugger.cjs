#!/usr/bin/env node

/**
 * Frontend UI Debugger - Comprehensive testing for frontend chat UI issues
 * 
 * This tool specifically tests:
 * 1. Chat message display issues (empty bubbles, undefined errors)
 * 2. Connection dashboard statistics showing "0"
 * 3. Message handling and rendering pipeline
 */

const WebSocket = require('ws');
const fs = require('fs');

const config = {
  gameWebSocketUrl: 'ws://localhost:8000/ws/game/',
  adminWebSocketUrl: 'ws://localhost:8000/ws/connection-monitor/',
  testDuration: 30000, // 30 seconds
  messageInterval: 3000 // Send message every 3 seconds
};

class FrontendUIDebugger {
  constructor() {
    this.gameWs = null;
    this.adminWs = null;
    this.messages = [];
    this.dashboardStats = {};
    this.issues = [];
    this.startTime = Date.now();
  }

  async start() {
    console.log('🔍 Frontend UI Debugger Starting...');
    console.log('📋 Testing chat UI and dashboard statistics issues');
    console.log('⏱️  Test duration: 30 seconds\n');

    try {
      // Connect to both WebSocket endpoints
      await this.connectGameWebSocket();
      await this.connectAdminWebSocket();

      // Start message flow simulation
      this.startMessageFlow();

      // Monitor for issues
      this.monitorIssues();

      // Wait for test completion
      await this.waitForCompletion();

      // Generate comprehensive report
      this.generateReport();

    } catch (error) {
      console.error('❌ Test failed:', error.message);
      this.issues.push({
        type: 'CRITICAL_ERROR',
        message: error.message,
        timestamp: new Date().toISOString()
      });
    } finally {
      this.cleanup();
    }
  }

  async connectGameWebSocket() {
    return new Promise((resolve, reject) => {
      console.log('🔌 Connecting to game WebSocket...');
      
      this.gameWs = new WebSocket(config.gameWebSocketUrl);
      
      this.gameWs.on('open', () => {
        console.log('✅ Game WebSocket connected');
        
        // Send identification message
        this.gameWs.send(JSON.stringify({
          type: 'chat_message',
          content: {
            message: 'Frontend UI Debug Test',
            user_profile_id: '2',
            timestamp: new Date().toISOString()
          }
        }));
        
        resolve();
      });

      this.gameWs.on('message', (data) => {
        try {
          const message = JSON.parse(data.toString());
          this.handleGameMessage(message);
        } catch (error) {
          this.issues.push({
            type: 'MESSAGE_PARSE_ERROR',
            message: `Failed to parse game message: ${error.message}`,
            rawData: data.toString(),
            timestamp: new Date().toISOString()
          });
        }
      });

      this.gameWs.on('error', (error) => {
        console.error('❌ Game WebSocket error:', error.message);
        reject(error);
      });

      setTimeout(() => reject(new Error('Game WebSocket connection timeout')), 10000);
    });
  }

  async connectAdminWebSocket() {
    return new Promise((resolve, reject) => {
      console.log('🔌 Connecting to admin WebSocket...');
      
      this.adminWs = new WebSocket(config.adminWebSocketUrl);
      
      this.adminWs.on('open', () => {
        console.log('✅ Admin WebSocket connected');
        resolve();
      });

      this.adminWs.on('message', (data) => {
        try {
          const message = JSON.parse(data.toString());
          this.handleAdminMessage(message);
        } catch (error) {
          this.issues.push({
            type: 'ADMIN_MESSAGE_PARSE_ERROR',
            message: `Failed to parse admin message: ${error.message}`,
            rawData: data.toString(),
            timestamp: new Date().toISOString()
          });
        }
      });

      this.adminWs.on('error', (error) => {
        console.error('❌ Admin WebSocket error:', error.message);
        reject(error);
      });

      setTimeout(() => reject(new Error('Admin WebSocket connection timeout')), 10000);
    });
  }

  handleGameMessage(message) {
    this.messages.push({
      source: 'game',
      message,
      timestamp: new Date().toISOString()
    });

    console.log(`📨 Game message: ${message.type}`);

    // Check for chat message issues
    if (message.type === 'chat_message') {
      this.analyzeChatMessage(message);
    }

    // Check for wheel data
    if (message.type === 'wheel_data') {
      this.analyzeWheelData(message);
    }

    // Check for errors
    if (message.type === 'error') {
      this.issues.push({
        type: 'BACKEND_ERROR',
        message: message.content || 'Unknown error',
        timestamp: new Date().toISOString()
      });
    }
  }

  handleAdminMessage(message) {
    this.messages.push({
      source: 'admin',
      message,
      timestamp: new Date().toISOString()
    });

    console.log(`📊 Admin message: ${message.type || 'unknown'}`);

    // Extract dashboard statistics
    if (message.connections !== undefined) {
      this.dashboardStats.connections = message.connections;
    }
    if (message.total_messages !== undefined) {
      this.dashboardStats.totalMessages = message.total_messages;
    }
    if (message.active_sessions !== undefined) {
      this.dashboardStats.activeSessions = message.active_sessions;
    }
  }

  analyzeChatMessage(message) {
    // Check for empty content
    if (!message.content || message.content.trim() === '') {
      this.issues.push({
        type: 'EMPTY_CHAT_MESSAGE',
        message: 'Chat message has empty content',
        data: message,
        timestamp: new Date().toISOString()
      });
    }

    // Check for undefined values
    if (message.content === undefined || message.content === null) {
      this.issues.push({
        type: 'UNDEFINED_CHAT_CONTENT',
        message: 'Chat message content is undefined/null',
        data: message,
        timestamp: new Date().toISOString()
      });
    }

    // Check message structure
    if (typeof message.content !== 'string') {
      this.issues.push({
        type: 'INVALID_CONTENT_TYPE',
        message: `Chat message content is not string: ${typeof message.content}`,
        data: message,
        timestamp: new Date().toISOString()
      });
    }
  }

  analyzeWheelData(message) {
    if (!message.wheel || !message.wheel.items) {
      this.issues.push({
        type: 'INVALID_WHEEL_DATA',
        message: 'Wheel data missing required fields',
        data: message,
        timestamp: new Date().toISOString()
      });
      return;
    }

    // Check for generic activity names
    const genericNames = message.wheel.items.filter(item => 
      item.name && item.name.match(/^Activity \d+$/)
    );

    if (genericNames.length > 0) {
      this.issues.push({
        type: 'GENERIC_ACTIVITY_NAMES',
        message: `Found ${genericNames.length} activities with generic names`,
        data: genericNames,
        timestamp: new Date().toISOString()
      });
    }
  }

  startMessageFlow() {
    const messages = [
      "I'm feeling bored today",
      "I want to do some exercise",
      "What activities do you recommend?",
      "Show me my wheel"
    ];

    let messageIndex = 0;
    
    const sendMessage = () => {
      if (messageIndex < messages.length && this.gameWs && this.gameWs.readyState === WebSocket.OPEN) {
        const message = {
          type: 'chat_message',
          content: {
            message: messages[messageIndex],
            user_profile_id: '2',
            timestamp: new Date().toISOString()
          }
        };

        console.log(`👤 Sending: "${messages[messageIndex]}"`);
        this.gameWs.send(JSON.stringify(message));
        messageIndex++;
      }
    };

    // Send first message immediately
    sendMessage();

    // Send subsequent messages at intervals
    this.messageInterval = setInterval(sendMessage, config.messageInterval);
  }

  monitorIssues() {
    this.monitorInterval = setInterval(() => {
      // Check dashboard stats
      if (Object.keys(this.dashboardStats).length === 0) {
        console.log('⚠️  No dashboard statistics received yet');
      } else {
        const allZero = Object.values(this.dashboardStats).every(val => val === 0);
        if (allZero) {
          this.issues.push({
            type: 'DASHBOARD_STATS_ALL_ZERO',
            message: 'All dashboard statistics are showing 0',
            data: this.dashboardStats,
            timestamp: new Date().toISOString()
          });
        }
      }
    }, 5000);
  }

  async waitForCompletion() {
    return new Promise(resolve => {
      setTimeout(resolve, config.testDuration);
    });
  }

  generateReport() {
    const report = {
      testSummary: {
        duration: Date.now() - this.startTime,
        totalMessages: this.messages.length,
        gameMessages: this.messages.filter(m => m.source === 'game').length,
        adminMessages: this.messages.filter(m => m.source === 'admin').length,
        issuesFound: this.issues.length
      },
      dashboardStats: this.dashboardStats,
      issues: this.issues,
      messages: this.messages
    };

    // Save detailed report
    const filename = `logs/frontend-ui-debug-${Date.now()}.json`;
    fs.writeFileSync(filename, JSON.stringify(report, null, 2));

    // Display summary
    console.log('\n🔍 Frontend UI Debug Report');
    console.log('=' .repeat(50));
    console.log(`📊 Total messages: ${report.testSummary.totalMessages}`);
    console.log(`🎮 Game messages: ${report.testSummary.gameMessages}`);
    console.log(`📈 Admin messages: ${report.testSummary.adminMessages}`);
    console.log(`⚠️  Issues found: ${report.testSummary.issuesFound}`);
    console.log(`📊 Dashboard stats: ${JSON.stringify(this.dashboardStats)}`);

    if (this.issues.length > 0) {
      console.log('\n🚨 Issues Identified:');
      this.issues.forEach((issue, index) => {
        console.log(`${index + 1}. ${issue.type}: ${issue.message}`);
      });
    } else {
      console.log('\n✅ No issues detected!');
    }

    console.log(`\n📄 Detailed report saved: ${filename}`);
  }

  cleanup() {
    if (this.messageInterval) clearInterval(this.messageInterval);
    if (this.monitorInterval) clearInterval(this.monitorInterval);
    if (this.gameWs) this.gameWs.close();
    if (this.adminWs) this.adminWs.close();
  }
}

// Run the debugger
if (require.main === module) {
  const uiDebugger = new FrontendUIDebugger();
  uiDebugger.start().catch(console.error);
}

module.exports = FrontendUIDebugger;
