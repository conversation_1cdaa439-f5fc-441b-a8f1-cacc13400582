#!/usr/bin/env node

/**
 * WebSocket Monitor
 * Real-time monitoring of WebSocket communication for debugging
 */

import WebSocket from 'ws';
import { CONFIG } from './config.js';
import { writeFileSync, mkdirSync } from 'fs';
import { join } from 'path';

class WebSocketMonitor {
  constructor() {
    this.ws = null;
    this.isConnected = false;
    this.messageLog = [];
    this.errorLog = [];
    this.startTime = Date.now();
    this.messageCount = { sent: 0, received: 0 };
    
    // Ensure logs directory exists
    try {
      mkdirSync('./logs', { recursive: true });
    } catch (e) {
      // Directory already exists
    }
  }

  async start() {
    console.log('🔍 WebSocket Monitor Starting...');
    console.log(`📡 Connecting to: ${CONFIG.backend.websocketUrl}`);
    console.log('📝 Press Ctrl+C to stop monitoring\n');

    await this.connect();
    this.setupMessageHandlers();
    this.startPeriodicReporting();
    
    // Keep the process alive
    process.on('SIGINT', () => {
      this.stop();
    });
  }

  async connect() {
    return new Promise((resolve, reject) => {
      this.ws = new WebSocket(CONFIG.backend.websocketUrl);
      
      const timeout = setTimeout(() => {
        reject(new Error('Connection timeout'));
      }, CONFIG.testing.shortTimeout);

      this.ws.on('open', () => {
        clearTimeout(timeout);
        this.isConnected = true;
        this.log('🟢 WebSocket connected', 'info');
        resolve();
      });

      this.ws.on('error', (error) => {
        clearTimeout(timeout);
        this.log(`🔴 WebSocket error: ${error.message}`, 'error');
        reject(error);
      });

      this.ws.on('close', (code, reason) => {
        this.isConnected = false;
        this.log(`🟡 WebSocket closed: ${code} ${reason}`, 'warn');
      });
    });
  }

  setupMessageHandlers() {
    this.ws.on('message', (data) => {
      this.messageCount.received++;
      
      try {
        const message = JSON.parse(data);
        this.logMessage('📥 RECEIVED', message);
        this.validateMessage(message, 'incoming');
      } catch (error) {
        this.log(`❌ Invalid JSON received: ${data}`, 'error');
      }
    });
  }

  sendTestMessage(type = 'chat_message', content = 'Test message from monitor') {
    if (!this.isConnected) {
      this.log('❌ Cannot send message - not connected', 'error');
      return;
    }

    const message = {
      type,
      message: content,
      user_profile_id: '2',
      timestamp: new Date().toISOString()
    };

    this.messageCount.sent++;
    this.ws.send(JSON.stringify(message));
    this.logMessage('📤 SENT', message);
  }

  logMessage(direction, message) {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      direction,
      type: message.type || 'unknown',
      message: message,
      size: JSON.stringify(message).length
    };

    this.messageLog.push(logEntry);
    
    // Console output with color coding
    const typeColor = this.getTypeColor(message.type);
    console.log(`${direction} [${timestamp}] ${typeColor}${message.type || 'unknown'}${'\x1b[0m'}`);
    
    // Show message content for important types
    if (['error', 'system_message', 'wheel_data'].includes(message.type)) {
      console.log(`  Content: ${JSON.stringify(message, null, 2).substring(0, 200)}...`);
    }
  }

  validateMessage(message, direction) {
    const expectedTypes = CONFIG.expectedMessages[direction];

    if (!message.type) {
      this.log('⚠️ Message missing type field', 'warn');
      return;
    }

    if (!expectedTypes.includes(message.type)) {
      this.log(`⚠️ Unexpected message type: ${message.type}`, 'warn');
    }

    // Check for error patterns
    const messageStr = JSON.stringify(message);
    CONFIG.errorPatterns.forEach(pattern => {
      if (messageStr.includes(pattern)) {
        this.log(`🚨 Error pattern detected: ${pattern}`, 'error');
      }
    });

    // Specific issue detection
    this.detectSpecificIssues(message);
  }

  detectSpecificIssues(message) {
    // Detect LLM Config issues
    if (message.type === 'error' && message.content) {
      const content = typeof message.content === 'string' ? message.content : JSON.stringify(message.content);

      if (content.includes('No LLMConfig provided')) {
        this.log('🔥 CRITICAL: LLM Config not being passed properly from frontend', 'error');
        this.log('💡 Check debug panel selections and message metadata', 'warn');
      }

      if (content.includes('retrieving your profile')) {
        this.log('🔥 CRITICAL: User profile retrieval failed', 'error');
        this.log('💡 Check user_profile_id in message and database', 'warn');
      }
    }

    // Detect debug info patterns
    if (message.type === 'debug_info') {
      this.log(`🐛 Debug Info: ${JSON.stringify(message.content || message, null, 2)}`, 'info');
    }

    // Detect workflow status issues
    if (message.type === 'workflow_status') {
      if (message.status === 'failed' || message.status === 'error') {
        this.log(`🔥 Workflow failed: ${message.workflow_id}`, 'error');
        if (message.error) {
          this.log(`   Error: ${message.error}`, 'error');
        }
      }
    }
  }

  getTypeColor(type) {
    const colors = {
      'error': '\x1b[31m',        // Red
      'system_message': '\x1b[36m', // Cyan
      'chat_message': '\x1b[32m',   // Green
      'wheel_data': '\x1b[35m',     // Magenta
      'debug_info': '\x1b[33m',     // Yellow
      'processing_status': '\x1b[34m' // Blue
    };
    return colors[type] || '\x1b[37m'; // Default white
  }

  log(message, level = 'info') {
    const timestamp = new Date().toISOString();
    const logEntry = { timestamp, level, message };
    
    if (level === 'error') {
      this.errorLog.push(logEntry);
    }
    
    console.log(`[${timestamp}] ${message}`);
  }

  startPeriodicReporting() {
    setInterval(() => {
      this.printStats();
    }, 10000); // Every 10 seconds
  }

  printStats() {
    const uptime = Math.round((Date.now() - this.startTime) / 1000);
    const errorCount = this.errorLog.length;
    
    console.log('\n📊 Monitor Stats:');
    console.log(`⏱️  Uptime: ${uptime}s`);
    console.log(`📨 Messages: ${this.messageCount.received} received, ${this.messageCount.sent} sent`);
    console.log(`❌ Errors: ${errorCount}`);
    console.log(`🔗 Status: ${this.isConnected ? 'Connected' : 'Disconnected'}`);
    console.log('─'.repeat(50));
  }

  saveLogsToFile() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    
    // Save message log
    const messageLogFile = join('./logs', `websocket-messages-${timestamp}.json`);
    writeFileSync(messageLogFile, JSON.stringify(this.messageLog, null, 2));
    
    // Save error log
    if (this.errorLog.length > 0) {
      const errorLogFile = join('./logs', `websocket-errors-${timestamp}.json`);
      writeFileSync(errorLogFile, JSON.stringify(this.errorLog, null, 2));
    }
    
    console.log(`💾 Logs saved to ./logs/`);
  }

  stop() {
    console.log('\n🛑 Stopping WebSocket Monitor...');
    
    if (this.ws) {
      this.ws.close();
    }
    
    this.printStats();
    this.saveLogsToFile();
    
    console.log('👋 Monitor stopped');
    process.exit(0);
  }

  // Interactive commands
  setupInteractiveCommands() {
    process.stdin.setRawMode(true);
    process.stdin.resume();
    process.stdin.setEncoding('utf8');

    console.log('\n🎮 Interactive Commands:');
    console.log('  t - Send test message');
    console.log('  s - Show stats');
    console.log('  q - Quit');
    console.log('');

    process.stdin.on('data', (key) => {
      switch (key) {
        case 't':
          this.sendTestMessage();
          break;
        case 's':
          this.printStats();
          break;
        case 'q':
        case '\u0003': // Ctrl+C
          this.stop();
          break;
      }
    });
  }
}

// Run monitor if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const monitor = new WebSocketMonitor();
  monitor.start()
    .then(() => {
      monitor.setupInteractiveCommands();
    })
    .catch(console.error);
}

export { WebSocketMonitor };
