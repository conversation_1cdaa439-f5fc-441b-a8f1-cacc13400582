/**
 * Playwright Shadow DOM Fix
 * 
 * Comprehensive solution that properly handles Shadow DOM elements
 * in the APP-SHELL component where the chat interface is located
 */

const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

class ShadowDOMFix {
    constructor() {
        this.browser = null;
        this.page = null;
        this.chatMessages = [];
        this.testResults = {
            shadowDOMAccessed: false,
            chatInterfaceFound: false,
            textareaFound: false,
            textareaEnabled: false,
            messagesSent: false,
            responsesReceived: false,
            wheelElementsFound: false
        };
    }

    async initialize() {
        console.log('🌑 Initializing Shadow DOM Fix...');
        
        this.browser = await chromium.launch({ 
            headless: false,
            slowMo: 1000
        });
        
        this.page = await this.browser.newPage();
        
        this.page.on('console', msg => {
            console.log(`🖥️  Console: ${msg.text()}`);
        });

        // WebSocket message tracking
        await this.page.routeWebSocket(/ws:\/\/.*/, ws => {
            ws.onMessage(message => {
                try {
                    const parsed = JSON.parse(message.toString());
                    if (parsed.type === 'chat_message') {
                        this.chatMessages.push(parsed);
                        console.log(`📨 Chat response: ${JSON.stringify(parsed.content).substring(0, 100)}...`);
                    }
                } catch (e) {}
            });
            ws.connectToServer();
        });
    }

    async loadAndWait() {
        console.log('🌐 Loading frontend...');
        
        await this.page.goto('http://localhost:3002/');
        await this.page.waitForSelector('app-shell');
        
        // Wait for full initialization
        await this.page.waitForTimeout(25000);
        console.log('✅ Frontend loaded');
    }

    async exploreShadowDOM() {
        console.log('🌑 Exploring Shadow DOM structure...');
        
        const shadowStructure = await this.page.evaluate(() => {
            const appShell = document.querySelector('app-shell');
            if (!appShell || !appShell.shadowRoot) {
                return { error: 'No shadow root found' };
            }

            const exploreShadow = (root, depth = 0) => {
                if (depth > 3) return '...';
                
                const children = Array.from(root.children).map(child => ({
                    tagName: child.tagName,
                    id: child.id,
                    className: child.className,
                    textContent: child.textContent ? child.textContent.substring(0, 50) + '...' : '',
                    hasShadowRoot: !!child.shadowRoot,
                    children: child.shadowRoot ? exploreShadow(child.shadowRoot, depth + 1) : 
                             (child.children.length > 0 && depth < 2) ? 
                             Array.from(child.children).map(grandchild => ({
                                 tagName: grandchild.tagName,
                                 id: grandchild.id,
                                 className: grandchild.className
                             })) : []
                }));
                
                return children;
            };

            return {
                success: true,
                structure: exploreShadow(appShell.shadowRoot)
            };
        });

        console.log('🌑 Shadow DOM structure:', JSON.stringify(shadowStructure, null, 2));
        
        if (shadowStructure.success) {
            this.testResults.shadowDOMAccessed = true;
        }
        
        return shadowStructure;
    }

    async findChatElements() {
        console.log('💬 Finding chat elements in Shadow DOM...');
        
        const chatElements = await this.page.evaluate(() => {
            const appShell = document.querySelector('app-shell');
            if (!appShell || !appShell.shadowRoot) {
                return { error: 'No shadow root' };
            }

            const shadowRoot = appShell.shadowRoot;
            const elements = {
                chatInterface: null,
                textarea: null,
                sendButton: null,
                processingOverlay: null,
                allInputs: [],
                allButtons: []
            };

            // Look for chat-interface
            const chatInterface = shadowRoot.querySelector('chat-interface');
            if (chatInterface) {
                elements.chatInterface = {
                    found: true,
                    hasShadowRoot: !!chatInterface.shadowRoot,
                    properties: {
                        isProcessing: chatInterface.isProcessing,
                        connectionStatus: chatInterface.connectionStatus,
                        inputValue: chatInterface.inputValue
                    }
                };

                // If chat-interface has its own shadow root, explore it
                if (chatInterface.shadowRoot) {
                    const chatShadow = chatInterface.shadowRoot;
                    elements.textarea = chatShadow.querySelector('textarea');
                    elements.sendButton = chatShadow.querySelector('button.send-button, [data-send]');
                    elements.processingOverlay = chatShadow.querySelector('.processing-overlay');
                }
            }

            // Also look directly in app-shell shadow root
            if (!elements.textarea) {
                elements.textarea = shadowRoot.querySelector('textarea');
            }
            if (!elements.sendButton) {
                elements.sendButton = shadowRoot.querySelector('button');
            }

            // Find all inputs and buttons for comprehensive analysis
            elements.allInputs = Array.from(shadowRoot.querySelectorAll('input, textarea')).map(input => ({
                tagName: input.tagName,
                type: input.type || 'N/A',
                id: input.id,
                className: input.className,
                disabled: input.disabled,
                readOnly: input.readOnly,
                placeholder: input.placeholder || '',
                value: input.value || ''
            }));

            elements.allButtons = Array.from(shadowRoot.querySelectorAll('button')).map(button => ({
                tagName: button.tagName,
                id: button.id,
                className: button.className,
                disabled: button.disabled,
                textContent: button.textContent.trim()
            }));

            return elements;
        });

        console.log('💬 Chat elements found:', JSON.stringify(chatElements, null, 2));
        
        if (chatElements.chatInterface) {
            this.testResults.chatInterfaceFound = true;
        }
        if (chatElements.textarea) {
            this.testResults.textareaFound = true;
        }
        
        return chatElements;
    }

    async fixChatInterface() {
        console.log('🔧 Fixing chat interface in Shadow DOM...');
        
        const fixResult = await this.page.evaluate(() => {
            const appShell = document.querySelector('app-shell');
            if (!appShell || !appShell.shadowRoot) {
                return { error: 'No shadow root' };
            }

            const results = {
                chatInterfaceFixed: false,
                textareaFixed: false,
                overlayRemoved: false,
                errors: []
            };

            try {
                // Fix chat-interface component
                const chatInterface = appShell.shadowRoot.querySelector('chat-interface');
                if (chatInterface) {
                    console.log('Fixing chat-interface properties');
                    chatInterface.isProcessing = false;
                    chatInterface.connectionStatus = 'connected';
                    chatInterface.requestUpdate();
                    results.chatInterfaceFixed = true;

                    // Fix elements in chat-interface shadow root
                    if (chatInterface.shadowRoot) {
                        const textarea = chatInterface.shadowRoot.querySelector('textarea');
                        if (textarea) {
                            console.log('Fixing textarea in chat-interface shadow root');
                            textarea.disabled = false;
                            textarea.readOnly = false;
                            results.textareaFixed = true;
                        }

                        const overlay = chatInterface.shadowRoot.querySelector('.processing-overlay');
                        if (overlay) {
                            console.log('Removing processing overlay');
                            overlay.style.display = 'none';
                            overlay.style.opacity = '0';
                            overlay.classList.remove('visible');
                            results.overlayRemoved = true;
                        }
                    }
                }

                // Also fix any direct textarea in app-shell shadow root
                const directTextarea = appShell.shadowRoot.querySelector('textarea');
                if (directTextarea) {
                    console.log('Fixing direct textarea in app-shell shadow root');
                    directTextarea.disabled = false;
                    directTextarea.readOnly = false;
                    results.textareaFixed = true;
                }

            } catch (error) {
                results.errors.push(error.message);
            }

            return results;
        });

        console.log('🔧 Fix result:', JSON.stringify(fixResult, null, 2));
        
        if (fixResult.textareaFixed) {
            this.testResults.textareaEnabled = true;
        }
        
        return fixResult;
    }

    async testChatInteraction() {
        console.log('💬 Testing chat interaction with Shadow DOM...');
        
        try {
            // Wait for fixes to take effect
            await this.page.waitForTimeout(3000);

            // Test interaction using Shadow DOM access
            const interactionResult = await this.page.evaluate(() => {
                const appShell = document.querySelector('app-shell');
                if (!appShell || !appShell.shadowRoot) {
                    return { error: 'No shadow root' };
                }

                let textarea = null;
                let chatInterface = null;

                // Find textarea in chat-interface shadow root
                const chatInterfaceEl = appShell.shadowRoot.querySelector('chat-interface');
                if (chatInterfaceEl) {
                    chatInterface = chatInterfaceEl;
                    if (chatInterfaceEl.shadowRoot) {
                        textarea = chatInterfaceEl.shadowRoot.querySelector('textarea');
                    }
                }

                // Fallback to direct textarea in app-shell
                if (!textarea) {
                    textarea = appShell.shadowRoot.querySelector('textarea');
                }

                if (!textarea) {
                    return { error: 'No textarea found in shadow DOM' };
                }

                try {
                    // Set the message
                    const message = 'hey! do you recognize me?';
                    
                    // Focus and set value
                    textarea.focus();
                    textarea.value = message;
                    
                    // Update chat interface component if available
                    if (chatInterface) {
                        chatInterface.inputValue = message;
                    }
                    
                    // Trigger input event
                    textarea.dispatchEvent(new Event('input', { bubbles: true }));
                    
                    // Trigger keypress event (Enter)
                    const enterEvent = new KeyboardEvent('keypress', {
                        key: 'Enter',
                        code: 'Enter',
                        keyCode: 13,
                        which: 13,
                        bubbles: true
                    });
                    textarea.dispatchEvent(enterEvent);

                    return {
                        success: true,
                        textareaValue: textarea.value,
                        focused: document.activeElement === textarea,
                        chatInterfaceValue: chatInterface ? chatInterface.inputValue : 'N/A'
                    };
                } catch (error) {
                    return { error: error.message };
                }
            });

            console.log('💬 Interaction result:', JSON.stringify(interactionResult, null, 2));

            if (interactionResult.success) {
                this.testResults.messagesSent = true;
                console.log('✅ Message sent successfully');

                // Wait for response
                console.log('⏳ Waiting for response...');
                const initialCount = this.chatMessages.length;
                
                for (let i = 0; i < 30; i++) {
                    await this.page.waitForTimeout(1000);
                    if (this.chatMessages.length > initialCount) {
                        break;
                    }
                }

                const responseCount = this.chatMessages.length - initialCount;
                if (responseCount > 0) {
                    this.testResults.responsesReceived = true;
                    console.log(`✅ Received ${responseCount} responses`);
                    
                    if (responseCount > 1) {
                        console.log('⚠️  Multiple responses detected - duplicate issue confirmed');
                    }
                } else {
                    console.log('❌ No response received');
                }
            } else {
                console.log('❌ Message sending failed');
            }

        } catch (error) {
            console.log(`❌ Chat interaction test failed: ${error.message}`);
        }
    }

    async testWheelElements() {
        console.log('🎡 Testing wheel elements in Shadow DOM...');
        
        const wheelResult = await this.page.evaluate(() => {
            const appShell = document.querySelector('app-shell');
            if (!appShell || !appShell.shadowRoot) {
                return { error: 'No shadow root' };
            }

            const wheelElements = [];
            const shadowRoot = appShell.shadowRoot;

            // Look for wheel-related elements
            const selectors = ['game-wheel', 'svg', 'canvas', '.wheel', '[data-wheel]'];
            
            selectors.forEach(selector => {
                const elements = shadowRoot.querySelectorAll(selector);
                Array.from(elements).forEach(el => {
                    const rect = el.getBoundingClientRect();
                    if (rect.width > 0 && rect.height > 0) {
                        wheelElements.push({
                            selector,
                            tagName: el.tagName,
                            id: el.id,
                            className: el.className,
                            dimensions: { width: rect.width, height: rect.height },
                            visible: el.offsetParent !== null
                        });
                    }
                });
            });

            return { success: true, elements: wheelElements };
        });

        if (wheelResult.success && wheelResult.elements.length > 0) {
            this.testResults.wheelElementsFound = true;
            console.log(`✅ Found ${wheelResult.elements.length} wheel elements`);
            console.log('🎡 Wheel elements:', JSON.stringify(wheelResult.elements, null, 2));
        } else {
            console.log('❌ No wheel elements found');
        }

        return wheelResult;
    }

    async generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            testResults: this.testResults,
            chatMessages: this.chatMessages,
            summary: {
                testsPassedCount: Object.values(this.testResults).filter(Boolean).length,
                totalTests: Object.keys(this.testResults).length,
                shadowDOMWorking: this.testResults.shadowDOMAccessed,
                chatFunctional: this.testResults.messagesSent && this.testResults.responsesReceived,
                duplicateResponses: this.chatMessages.length > 1
            }
        };

        const reportPath = path.join('logs', `shadow-dom-fix-report-${Date.now()}.json`);
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

        console.log('\n🌑 SHADOW DOM FIX REPORT');
        console.log('========================');
        console.log(`✅ Tests Passed: ${report.summary.testsPassedCount}/${report.summary.totalTests}`);
        console.log(`🌑 Shadow DOM Access: ${report.summary.shadowDOMWorking ? 'SUCCESS' : 'FAILED'}`);
        console.log(`💬 Chat Functional: ${report.summary.chatFunctional ? 'SUCCESS' : 'FAILED'}`);
        console.log(`📨 Chat Messages: ${this.chatMessages.length}`);
        
        if (report.summary.duplicateResponses) {
            console.log('⚠️  DUPLICATE RESPONSES DETECTED');
        }

        console.log(`📄 Detailed report: ${reportPath}`);
        return report;
    }

    async cleanup() {
        if (this.browser) {
            await this.browser.close();
        }
    }
}

// Main execution
async function main() {
    const fixer = new ShadowDOMFix();
    
    try {
        await fixer.initialize();
        await fixer.loadAndWait();
        
        const shadowStructure = await fixer.exploreShadowDOM();
        const chatElements = await fixer.findChatElements();
        const fixResult = await fixer.fixChatInterface();
        
        await fixer.testChatInteraction();
        await fixer.testWheelElements();
        
        const report = await fixer.generateReport();
        
        console.log('\n🎯 SHADOW DOM MISSION SUMMARY');
        console.log('=============================');
        
        if (report.summary.chatFunctional) {
            console.log('✅ SUCCESS: Chat functionality working with Shadow DOM');
        } else {
            console.log('❌ PARTIAL: Shadow DOM accessed but chat issues remain');
        }
        
        console.log('\n📋 KEY FINDINGS:');
        console.log('1. Frontend uses Shadow DOM in APP-SHELL component');
        console.log('2. Chat interface elements are in nested shadow roots');
        console.log('3. Standard DOM selectors cannot access shadow elements');
        console.log('4. Custom shadow DOM handling required for automation');
        
    } catch (error) {
        console.error(`❌ Shadow DOM fix failed: ${error.message}`);
    } finally {
        await fixer.cleanup();
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = { ShadowDOMFix };
