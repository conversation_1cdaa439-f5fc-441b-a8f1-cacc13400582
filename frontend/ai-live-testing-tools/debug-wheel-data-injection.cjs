/**
 * Debug Wheel Data Injection and Removal
 * Test the wheel data management by manually injecting data and testing removal
 */

const puppeteer = require('puppeteer');

async function debugWheelDataInjection() {
  console.log('🔍 Debugging wheel data injection and removal...');
  
  const browser = await puppeteer.launch({ 
    headless: false,
    defaultViewport: { width: 1200, height: 800 }
  });
  
  try {
    const page = await browser.newPage();
    
    // Enable console logging
    page.on('console', msg => {
      if (msg.text().includes('wheel') || msg.text().includes('REMOVING') || msg.text().includes('wheelData')) {
        console.log('🔍 BROWSER:', msg.text());
      }
    });
    
    // Navigate to the app
    console.log('📱 Navigating to http://localhost:3001...');
    await page.goto('http://localhost:3001', { waitUntil: 'networkidle0' });
    
    // Wait for app to load
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('\n🎯 Step 1: Inject mock wheel data...');
    
    // Inject mock wheel data
    const injectionResult = await page.evaluate(() => {
      const appShell = document.querySelector('app-shell');
      if (appShell) {
        // Create mock wheel data similar to what the backend would send
        const mockWheelData = {
          segments: [
            {
              id: 'item_mock_1',
              text: 'Mock Activity 1',
              name: 'Mock Activity 1',
              description: 'First mock activity for testing',
              percentage: 25,
              color: '#FF6B6B',
              activity_tailored_id: 'activity_1',
              domain: 'physical',
              base_challenge_rating: 50,
              wheel_item_id: 'item_mock_1'
            },
            {
              id: 'item_mock_2',
              text: 'Mock Activity 2',
              name: 'Mock Activity 2',
              description: 'Second mock activity for testing',
              percentage: 25,
              color: '#4ECDC4',
              activity_tailored_id: 'activity_2',
              domain: 'mental',
              base_challenge_rating: 40,
              wheel_item_id: 'item_mock_2'
            },
            {
              id: 'item_mock_3',
              text: 'Mock Activity 3',
              name: 'Mock Activity 3',
              description: 'Third mock activity for testing',
              percentage: 25,
              color: '#45B7D1',
              activity_tailored_id: 'activity_3',
              domain: 'creative',
              base_challenge_rating: 60,
              wheel_item_id: 'item_mock_3'
            },
            {
              id: 'item_mock_4',
              text: 'Mock Activity 4',
              name: 'Mock Activity 4',
              description: 'Fourth mock activity for testing',
              percentage: 25,
              color: '#96CEB4',
              activity_tailored_id: 'activity_4',
              domain: 'social',
              base_challenge_rating: 30,
              wheel_item_id: 'item_mock_4'
            }
          ],
          wheelId: 'mock-wheel-123',
          createdAt: new Date().toISOString()
        };
        
        console.log('Injecting mock wheel data:', mockWheelData);
        appShell.wheelData = mockWheelData;
        appShell.requestUpdate();
        
        return {
          success: true,
          segmentCount: mockWheelData.segments.length,
          wheelId: mockWheelData.wheelId
        };
      }
      return { success: false, error: 'No app-shell found' };
    });
    
    console.log('Injection result:', injectionResult);
    
    if (!injectionResult.success) {
      console.log('❌ Failed to inject wheel data');
      return;
    }
    
    // Wait for re-render
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    console.log('\n🎯 Step 2: Check wheel state after injection...');
    
    // Check wheel state after injection
    const afterInjectionState = await page.evaluate(() => {
      const appShell = document.querySelector('app-shell');
      if (appShell && appShell.shadowRoot) {
        const wheel = appShell.shadowRoot.querySelector('game-wheel');
        return {
          hasWheelData: !!(appShell.wheelData && appShell.wheelData.segments),
          wheelDataSegmentCount: appShell.wheelData ? appShell.wheelData.segments.length : 0,
          wheelDataPreview: appShell.wheelData ? appShell.wheelData.segments.map(s => ({ 
            id: s.id, 
            text: s.text || s.name, 
            color: s.color
          })) : null,
          wheelId: appShell.wheelData ? appShell.wheelData.wheelId : null,
          isUnpopulated: wheel ? wheel.classList.contains('wheel-unpopulated') : false,
          wheelDataTruthy: !!appShell.wheelData,
          wheelDataType: typeof appShell.wheelData
        };
      }
      return { error: 'No app-shell or shadow root' };
    });
    
    console.log('After injection state:', JSON.stringify(afterInjectionState, null, 2));
    
    console.log('\n🎯 Step 3: Test template logic by setting wheelData to null...');
    
    // Test what happens when wheelData becomes null
    const nullTestResult = await page.evaluate(() => {
      const appShell = document.querySelector('app-shell');
      if (appShell) {
        console.log('Setting wheelData to null...');
        appShell.wheelData = null;
        appShell.requestUpdate();
        return { success: true };
      }
      return { success: false };
    });
    
    // Wait for re-render
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Check state after setting to null
    const afterNullState = await page.evaluate(() => {
      const appShell = document.querySelector('app-shell');
      if (appShell && appShell.shadowRoot) {
        const wheel = appShell.shadowRoot.querySelector('game-wheel');
        return {
          hasWheelData: !!(appShell.wheelData && appShell.wheelData.segments),
          wheelDataSegmentCount: appShell.wheelData ? appShell.wheelData.segments.length : 0,
          wheelDataPreview: appShell.wheelData ? appShell.wheelData.segments.map(s => ({ 
            id: s.id, 
            text: s.text || s.name, 
            color: s.color
          })) : null,
          isUnpopulated: wheel ? wheel.classList.contains('wheel-unpopulated') : false,
          wheelDataTruthy: !!appShell.wheelData,
          wheelDataType: typeof appShell.wheelData,
          wheelDataValue: appShell.wheelData
        };
      }
      return { error: 'No app-shell or shadow root' };
    });
    
    console.log('After setting to null:', JSON.stringify(afterNullState, null, 2));
    
    console.log('\n🎯 Step 4: Test template logic by setting wheelData to undefined...');
    
    // Test what happens when wheelData becomes undefined
    await page.evaluate(() => {
      const appShell = document.querySelector('app-shell');
      if (appShell) {
        console.log('Setting wheelData to undefined...');
        appShell.wheelData = undefined;
        appShell.requestUpdate();
      }
    });
    
    // Wait for re-render
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Check state after setting to undefined
    const afterUndefinedState = await page.evaluate(() => {
      const appShell = document.querySelector('app-shell');
      if (appShell && appShell.shadowRoot) {
        const wheel = appShell.shadowRoot.querySelector('game-wheel');
        return {
          hasWheelData: !!(appShell.wheelData && appShell.wheelData.segments),
          isUnpopulated: wheel ? wheel.classList.contains('wheel-unpopulated') : false,
          wheelDataTruthy: !!appShell.wheelData,
          wheelDataType: typeof appShell.wheelData,
          wheelDataValue: appShell.wheelData
        };
      }
      return { error: 'No app-shell or shadow root' };
    });
    
    console.log('After setting to undefined:', JSON.stringify(afterUndefinedState, null, 2));
    
    console.log('\n🎯 Step 5: Test with empty segments array...');
    
    // Test what happens when wheelData has empty segments
    await page.evaluate(() => {
      const appShell = document.querySelector('app-shell');
      if (appShell) {
        console.log('Setting wheelData to empty segments...');
        appShell.wheelData = {
          segments: [],
          wheelId: 'empty-wheel',
          createdAt: new Date().toISOString()
        };
        appShell.requestUpdate();
      }
    });
    
    // Wait for re-render
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Check state after setting to empty
    const afterEmptyState = await page.evaluate(() => {
      const appShell = document.querySelector('app-shell');
      if (appShell && appShell.shadowRoot) {
        const wheel = appShell.shadowRoot.querySelector('game-wheel');
        return {
          hasWheelData: !!(appShell.wheelData && appShell.wheelData.segments),
          wheelDataSegmentCount: appShell.wheelData ? appShell.wheelData.segments.length : 0,
          isUnpopulated: wheel ? wheel.classList.contains('wheel-unpopulated') : false,
          wheelDataTruthy: !!appShell.wheelData,
          templateCondition: !!(appShell.wheelData) // This is what the template checks
        };
      }
      return { error: 'No app-shell or shadow root' };
    });
    
    console.log('After setting to empty segments:', JSON.stringify(afterEmptyState, null, 2));
    
    console.log('\n🎯 Analysis:');
    console.log('The template condition `this.wheelData` checks for truthiness of the wheelData object.');
    console.log('If wheelData becomes null/undefined, it shows the background wheel.');
    console.log('If wheelData exists but has empty segments, it still shows the populated wheel (which would be empty).');
    
    // Wait for user to see the browser
    console.log('\n⏳ Browser will stay open for 10 seconds for manual inspection...');
    await new Promise(resolve => setTimeout(resolve, 10000));
    
  } catch (error) {
    console.error('❌ Debug failed:', error);
  } finally {
    await browser.close();
  }
}

// Run the debug
debugWheelDataInjection().catch(console.error);
