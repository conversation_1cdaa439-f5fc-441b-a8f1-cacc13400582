# Frontend Wheel Generation Sequence Diagram

## 🎯 **Purpose**
Detailed sequence diagram showing frontend component interactions and state management during wheel generation, from user interaction to wheel display.

## 📊 **Mermaid Sequence Diagram**

```mermaid
sequenceDiagram
    participant User as 👤 User
    participant AppShell as 🏠 AppShell<br/>(app-shell.ts)
    participant <PERSON><PERSON><PERSON><PERSON> as 📨 MessageHandler<br/>(message-handler.ts)
    participant WebSocketManager as 🔌 WebSocketManager<br/>(websocket-manager.ts)
    participant WheelStateMachine as 🎰 WheelStateMachine<br/>(wheel-state-machine.ts)
    participant GameWheel as 🎡 GameWheel<br/>(game-wheel.ts)
    participant <PERSON><PERSON><PERSON><PERSON> as 🎨 WheelRenderer<br/>(wheel-renderer.ts)
    participant WheelPhysics as ⚙️ WheelPhysics<br/>(wheel-physics.ts)
    participant ProgressBar as 📊 ProgressBar<br/>(progress-bar.ts)
    participant Backend as 🖥️ Backend<br/>(WebSocket Server)

    %% 1. User Initiates Wheel Generation
    User->>AppShell: Click "Generate Wheel"<br/>Energy: 80%, Time: 10min
    Note over AppShell: app-shell.ts<br/>handleGenerateWheel()
    
    %% 2. UI State Updates
    AppShell->>AppShell: Set loading state<br/>isLoading = true
    AppShell->>ProgressBar: Show progress bar<br/>showProgressBar = true
    AppShell->>WheelStateMachine: startLoading()<br/>Transition to LOADING state
    Note over WheelStateMachine: wheel-state-machine.ts<br/>State: EMPTY → LOADING
    
    %% 3. Message Preparation
    AppShell->>MessageHandler: sendChatMessage()<br/>workflow: 'wheel_generation'
    Note over MessageHandler: message-handler.ts<br/>Prepare WebSocket message
    MessageHandler->>MessageHandler: Build message payload<br/>Include energy_level, time_available
    
    %% 4. WebSocket Communication
    MessageHandler->>WebSocketManager: sendMessage()<br/>type: 'chat_message'
    Note over WebSocketManager: websocket-manager.ts<br/>WebSocket connection management
    WebSocketManager->>Backend: WebSocket Message<br/>{"type": "chat_message", "workflow": "wheel_generation"}
    
    %% 5. Backend Processing (Simplified)
    Note over Backend: Backend processes request<br/>(See Backend Sequence Diagram)
    Backend->>WebSocketManager: Processing Status<br/>{"type": "processing_status", "stage": "activity_selection"}
    
    %% 6. Progress Updates
    WebSocketManager->>AppShell: onMessage('processing_status')<br/>Progress updates
    AppShell->>ProgressBar: Update progress<br/>dispatchEvent('progress-update')
    ProgressBar->>ProgressBar: Animate progress bar<br/>Show current stage
    AppShell->>WheelStateMachine: updateProgress()<br/>Update loading percentage
    
    %% 7. Wheel Data Reception
    Backend->>WebSocketManager: Wheel Data<br/>{"type": "wheel_data", "wheel": {...}}
    WebSocketManager->>AppShell: onMessage('wheel_data')<br/>Complete wheel data
    Note over AppShell: app-shell.ts<br/>handleWheelGenerated()
    
    %% 8. Data Validation & Processing
    AppShell->>AppShell: Validate wheel data<br/>Check structure & required fields
    AppShell->>AppShell: Transform data format<br/>Backend format → Frontend format
    
    alt Valid wheel data
        AppShell->>AppShell: Create WheelData object<br/>wheelId, segments, metadata
        Note over AppShell: Preserve wheel ID from backend<br/>Prevent ID inconsistency
        
        %% 9. State Machine Update
        AppShell->>WheelStateMachine: setWheelData(wheelData)<br/>Transition to POPULATED
        Note over WheelStateMachine: wheel-state-machine.ts<br/>State: LOADING → POPULATED
        WheelStateMachine->>WheelStateMachine: Validate data structure<br/>Check segments, percentages
        WheelStateMachine->>WheelStateMachine: hasWheelDataChanged()<br/>Prevent unnecessary updates
        
        %% 10. Wheel Component Initialization
        WheelStateMachine->>GameWheel: Notify state change<br/>New wheel data available
        Note over GameWheel: game-wheel.ts<br/>Lit component lifecycle
        GameWheel->>GameWheel: updated() lifecycle<br/>Property change detected
        GameWheel->>GameWheel: initializeWheel()<br/>Set up wheel components
        
        %% 11. Renderer Setup
        GameWheel->>WheelRenderer: Initialize renderer<br/>PIXI.js setup
        Note over WheelRenderer: wheel-renderer.ts<br/>PIXI.js graphics engine
        WheelRenderer->>WheelRenderer: Create canvas context<br/>Set up graphics pipeline
        WheelRenderer->>WheelRenderer: Apply domain colors<br/>Color system integration
        
        %% 12. Physics Engine Setup
        GameWheel->>WheelPhysics: Initialize physics<br/>Wheel mechanics
        Note over WheelPhysics: wheel-physics.ts<br/>Physics simulation
        WheelPhysics->>WheelPhysics: Set up physics properties<br/>Friction, momentum, boundaries
        
        %% 13. Wheel Rendering
        GameWheel->>WheelRenderer: renderWheel()<br/>Draw wheel segments
        WheelRenderer->>WheelRenderer: Draw segments<br/>Apply colors, text, borders
        WheelRenderer->>WheelRenderer: Calculate positions<br/>Segment angles, text placement
        WheelRenderer->>GameWheel: Rendering complete<br/>Visual wheel ready
        
        %% 14. Physics Simulation Start
        GameWheel->>WheelPhysics: start()<br/>Begin physics simulation
        WheelPhysics->>WheelPhysics: Animation loop<br/>Continuous physics updates
        
        %% 15. UI State Cleanup
        AppShell->>ProgressBar: Hide progress bar<br/>showProgressBar = false
        AppShell->>AppShell: Clear loading state<br/>isLoading = false
        AppShell->>AppShell: Add system message<br/>"Wheel generated successfully"
        AppShell->>AppShell: requestUpdate()<br/>Force re-render
        
    else Invalid wheel data
        AppShell->>AppShell: Handle error<br/>Log validation failure
        AppShell->>WheelStateMachine: setError()<br/>Transition to ERROR state
        Note over WheelStateMachine: wheel-state-machine.ts<br/>State: LOADING → ERROR
        AppShell->>AppShell: Show error message<br/>User-friendly error display
    end
    
    %% 16. User Interaction Ready
    GameWheel->>User: Wheel ready for interaction<br/>Visual feedback, hover effects
    Note over User: User can now spin wheel<br/>or remove items
    
    %% 17. Wheel Item Removal Flow (Optional)
    User->>GameWheel: Click remove item<br/>Remove specific activity
    GameWheel->>AppShell: Dispatch remove event<br/>activity_tailored_id
    AppShell->>WebSocketManager: Send removal request<br/>DELETE API call
    WebSocketManager->>Backend: HTTP DELETE<br/>/api/wheel-items/{id}
    Backend->>WebSocketManager: Removal confirmation<br/>Success response
    WebSocketManager->>AppShell: Removal complete<br/>Update UI state
    AppShell->>WheelStateMachine: Update wheel data<br/>Remove item from state
    WheelStateMachine->>GameWheel: Notify data change<br/>Re-render wheel
    GameWheel->>WheelRenderer: Re-render wheel<br/>Updated segments
    WheelRenderer->>WheelRenderer: Recalculate percentages<br/>Maintain visual balance
    WheelRenderer->>User: Updated wheel display<br/>Item removed, colors preserved
```

## 🏗️ **Component Architecture**

### **1. Presentation Layer**
- **`app-shell.ts`**: Main application container, orchestrates all components
- **`game-wheel.ts`**: Interactive wheel component with physics and rendering
- **`progress-bar.ts`**: Real-time progress visualization
- **Role**: User interface, visual feedback, user interaction handling

### **2. State Management Layer**
- **`wheel-state-machine.ts`**: Centralized wheel state management
- **`message-handler.ts`**: Message processing and routing
- **Role**: Application state consistency, data flow control

### **3. Communication Layer**
- **`websocket-manager.ts`**: WebSocket connection and message handling
- **Role**: Backend communication, real-time updates

### **4. Rendering Layer**
- **`wheel-renderer.ts`**: PIXI.js-based graphics rendering
- **`wheel-physics.ts`**: Physics simulation and animation
- **Role**: Visual presentation, user interaction physics

## 🔍 **Key Mechanics**

### **State Transitions**
1. **EMPTY** → User clicks generate → **LOADING**
2. **LOADING** → Wheel data received → **POPULATED**
3. **POPULATED** → Item removed → **POPULATED** (updated)
4. **Any State** → Error occurs → **ERROR**

### **Data Flow**
1. **User Input** → AppShell event handlers
2. **AppShell** → MessageHandler → WebSocketManager
3. **Backend Response** → WebSocketManager → AppShell
4. **AppShell** → WheelStateMachine → GameWheel
5. **GameWheel** → Renderer + Physics → Visual Display

### **Error Handling**
- **WebSocket errors**: Connection loss, message failures
- **Data validation**: Invalid wheel structure, missing fields
- **Rendering errors**: Canvas issues, PIXI.js failures
- **State corruption**: Invalid state transitions

### **Performance Optimizations**
- **State change detection**: Only re-render when data actually changes
- **Canvas optimization**: Efficient PIXI.js rendering pipeline
- **Memory management**: Proper cleanup of physics and rendering resources
- **Debounced updates**: Prevent excessive re-renders during rapid updates
