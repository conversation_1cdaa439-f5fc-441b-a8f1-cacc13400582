#!/usr/bin/env python3

"""
Backend Duplicate Response Fix

This script investigates and fixes the duplicate response issue where
the frontend receives 2 different responses from backend for one simple question.
"""

import os
import sys
import django
from django.conf import settings

def setup_django():
    """Setup Django environment."""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
    if not settings.configured:
        django.setup()

def analyze_duplicate_issue():
    """
    Analyze the duplicate response issue by examining the code flow.
    """
    print("🔍 ANALYZING DUPLICATE RESPONSE ISSUE")
    print("════════════════════════════════════════════════════════════")
    
    print("📋 Issue Analysis:")
    print("1. Frontend sends one chat message")
    print("2. Backend processes message through ConversationDispatcher")
    print("3. Frontend receives 2 different responses")
    print("4. This causes slow performance and confusion")
    
    print("\n🔍 Code Flow Analysis:")
    print("1. UserSessionConsumer.handle_chat_message() receives message")
    print("2. Creates new ConversationDispatcher instance")
    print("3. Calls dispatcher.process_message()")
    print("4. ConversationDispatcher routes through MentorService")
    print("5. Workflow is initiated (discussion, wheel_generation, etc.)")
    print("6. Workflow sends response via EventService")
    
    print("\n🚨 POTENTIAL DUPLICATE SOURCES:")
    print("A. Multiple workflow executions for same message")
    print("B. MentorService processing message multiple times")
    print("C. EventService sending multiple responses")
    print("D. WebSocket consumer echoing messages")
    print("E. Frontend processing same response multiple times")
    
    return True

def check_mentor_service_singleton():
    """
    Check if MentorService is properly implemented as singleton.
    """
    print("\n🔍 CHECKING MENTOR SERVICE SINGLETON PATTERN")
    print("════════════════════════════════════════════════════════════")
    
    try:
        from apps.main.services.mentor_service import MentorService
        
        # Test singleton behavior
        instance1 = MentorService.get_instance(user_profile_id='2')
        instance2 = MentorService.get_instance(user_profile_id='2')
        
        if instance1 is instance2:
            print("✅ MentorService singleton working correctly")
            return True
        else:
            print("❌ MentorService singleton BROKEN - multiple instances created")
            print("   This could cause duplicate processing!")
            return False
            
    except Exception as e:
        print(f"❌ MentorService check failed: {e}")
        return False

def check_event_service_broadcasting():
    """
    Check EventService for potential duplicate broadcasting.
    """
    print("\n🔍 CHECKING EVENT SERVICE BROADCASTING")
    print("════════════════════════════════════════════════════════════")
    
    try:
        from apps.main.services.event_service import EventService
        
        print("✅ EventService imported successfully")
        print("📋 EventService is responsible for sending responses to frontend")
        print("⚠️  Check if EventService.send_message() is called multiple times")
        
        return True
        
    except Exception as e:
        print(f"❌ EventService check failed: {e}")
        return False

def check_conversation_dispatcher_flow():
    """
    Check ConversationDispatcher for duplicate processing.
    """
    print("\n🔍 CHECKING CONVERSATION DISPATCHER FLOW")
    print("════════════════════════════════════════════════════════════")
    
    try:
        from apps.main.services.conversation_dispatcher import ConversationDispatcher
        
        print("✅ ConversationDispatcher imported successfully")
        print("📋 ConversationDispatcher.process_message() flow:")
        print("   1. Routes through MentorService.process_incoming_message()")
        print("   2. Extracts context from message")
        print("   3. Classifies workflow type")
        print("   4. Initiates workflow")
        print("⚠️  Each step could potentially cause duplicates")
        
        return True
        
    except Exception as e:
        print(f"❌ ConversationDispatcher check failed: {e}")
        return False

def identify_duplicate_sources():
    """
    Identify the most likely sources of duplicate responses.
    """
    print("\n🎯 IDENTIFYING DUPLICATE SOURCES")
    print("════════════════════════════════════════════════════════════")
    
    print("🔍 Most Likely Sources (in order of probability):")
    print()
    print("1. 🚨 WORKFLOW EXECUTION DUPLICATES")
    print("   - Same workflow triggered multiple times")
    print("   - Multiple Celery tasks for same message")
    print("   - Race conditions in workflow initiation")
    print()
    print("2. 🚨 MENTOR SERVICE PROCESSING")
    print("   - MentorService.process_incoming_message() called multiple times")
    print("   - Singleton pattern not working correctly")
    print("   - State management issues")
    print()
    print("3. 🚨 EVENT SERVICE BROADCASTING")
    print("   - EventService.send_message() called multiple times")
    print("   - Multiple WebSocket group sends")
    print("   - Channel layer duplication")
    print()
    print("4. 🚨 WEBSOCKET CONSUMER ECHOING")
    print("   - Consumer echoing user message AND sending response")
    print("   - Multiple message handlers triggered")
    print("   - Group send duplication")

def create_duplicate_prevention_strategy():
    """
    Create a strategy to prevent duplicate responses.
    """
    print("\n🛡️  DUPLICATE PREVENTION STRATEGY")
    print("════════════════════════════════════════════════════════════")
    
    print("📋 Implementation Strategy:")
    print()
    print("1. 🔒 MESSAGE DEDUPLICATION")
    print("   - Add message ID tracking in ConversationDispatcher")
    print("   - Prevent processing same message ID twice")
    print("   - Use Redis or in-memory cache for tracking")
    print()
    print("2. 🔒 WORKFLOW EXECUTION LOCKS")
    print("   - Add workflow execution locks per user")
    print("   - Prevent multiple workflows for same user simultaneously")
    print("   - Use distributed locks for Celery tasks")
    print()
    print("3. 🔒 RESPONSE TRACKING")
    print("   - Track sent responses per message")
    print("   - Prevent sending duplicate responses")
    print("   - Add response fingerprinting")
    print()
    print("4. 🔒 FRONTEND DEDUPLICATION")
    print("   - Add client-side response deduplication")
    print("   - Track received message IDs")
    print("   - Filter duplicate responses in WebSocket handler")

def generate_fix_recommendations():
    """
    Generate specific fix recommendations.
    """
    print("\n💡 SPECIFIC FIX RECOMMENDATIONS")
    print("════════════════════════════════════════════════════════════")
    
    print("🎯 IMMEDIATE FIXES (High Impact, Low Risk):")
    print()
    print("1. 📝 ADD MESSAGE ID TRACKING")
    print("   File: apps/main/services/conversation_dispatcher.py")
    print("   Action: Add message_id field and duplicate checking")
    print("   Code: Track processed message IDs in Redis/memory")
    print()
    print("2. 🔒 ADD WORKFLOW EXECUTION LOCKS")
    print("   File: apps/main/services/conversation_dispatcher.py")
    print("   Action: Add user-based workflow locks")
    print("   Code: Use Redis locks to prevent concurrent workflows")
    print()
    print("3. 🛡️  FRONTEND RESPONSE DEDUPLICATION")
    print("   File: frontend WebSocket handler")
    print("   Action: Add client-side duplicate filtering")
    print("   Code: Track response IDs and filter duplicates")
    print()
    print("🎯 MEDIUM-TERM FIXES (Medium Impact, Medium Risk):")
    print()
    print("4. 🔧 MENTOR SERVICE SINGLETON VALIDATION")
    print("   File: apps/main/services/mentor_service.py")
    print("   Action: Ensure proper singleton implementation")
    print("   Code: Add singleton validation and logging")
    print()
    print("5. 📊 EVENT SERVICE RESPONSE TRACKING")
    print("   File: apps/main/services/event_service.py")
    print("   Action: Add response tracking and deduplication")
    print("   Code: Track sent responses per user/session")

def main():
    """
    Main function to analyze and fix duplicate response issue.
    """
    print("🚀 BACKEND DUPLICATE RESPONSE ANALYSIS & FIX")
    print("════════════════════════════════════════════════════════════")
    print("Investigating why frontend receives 2 different responses")
    print("from backend for one simple question.")
    print()
    
    try:
        # Setup Django
        setup_django()
        
        # Analyze the issue
        analyze_duplicate_issue()
        
        # Check components
        mentor_ok = check_mentor_service_singleton()
        event_ok = check_event_service_broadcasting()
        dispatcher_ok = check_conversation_dispatcher_flow()
        
        # Identify sources
        identify_duplicate_sources()
        
        # Create prevention strategy
        create_duplicate_prevention_strategy()
        
        # Generate recommendations
        generate_fix_recommendations()
        
        print("\n📊 ANALYSIS SUMMARY")
        print("════════════════════════════════════════════════════════════")
        print(f"MentorService Singleton: {'✅ OK' if mentor_ok else '❌ ISSUE'}")
        print(f"EventService: {'✅ OK' if event_ok else '❌ ISSUE'}")
        print(f"ConversationDispatcher: {'✅ OK' if dispatcher_ok else '❌ ISSUE'}")
        
        print("\n🎯 NEXT STEPS:")
        print("1. Implement message ID tracking in ConversationDispatcher")
        print("2. Add workflow execution locks per user")
        print("3. Implement frontend response deduplication")
        print("4. Monitor for duplicate reduction")
        print("5. Validate fix with WebSocket monitoring tools")
        
        print("\n✅ Analysis completed successfully!")
        return True
        
    except Exception as e:
        print(f"\n❌ ANALYSIS FAILED: {e}")
        print("Manual investigation required.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
