const WebSocket = require('ws');

class WebSocketStabilityTest {
    constructor() {
        this.ws = null;
        this.connectionTime = null;
        this.messageCount = 0;
        this.isConnected = false;
    }

    async testConnection() {
        console.log('🔌 Testing WebSocket connection stability...');
        
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            
            this.ws = new WebSocket('ws://localhost:8000/ws/game/');
            
            this.ws.on('open', () => {
                this.connectionTime = Date.now() - startTime;
                this.isConnected = true;
                console.log(`✅ WebSocket connected in ${this.connectionTime}ms`);
                
                // Send a test message
                this.sendTestMessage();
                
                // Keep connection alive for 60 seconds
                setTimeout(() => {
                    console.log(`🔍 Connection stable for 60 seconds`);
                    console.log(`📊 Messages received: ${this.messageCount}`);
                    this.ws.close(1000, 'Test completed');
                    resolve({
                        success: true,
                        connectionTime: this.connectionTime,
                        messageCount: this.messageCount
                    });
                }, 60000);
            });
            
            this.ws.on('message', (data) => {
                this.messageCount++;
                try {
                    const message = JSON.parse(data);
                    console.log(`📨 Received message ${this.messageCount}: ${message.type || 'unknown'}`);
                } catch (e) {
                    console.log(`📨 Received raw message ${this.messageCount}`);
                }
            });
            
            this.ws.on('error', (error) => {
                console.error('❌ WebSocket error:', error.message);
                reject({
                    success: false,
                    error: error.message,
                    connectionTime: this.connectionTime
                });
            });
            
            this.ws.on('close', (code, reason) => {
                const duration = Date.now() - startTime;
                console.log(`🔌 WebSocket closed: ${code} - ${reason} (after ${duration}ms)`);
                
                if (code !== 1000) {
                    reject({
                        success: false,
                        error: `Unexpected close: ${code} - ${reason}`,
                        connectionTime: this.connectionTime,
                        duration: duration
                    });
                }
            });
            
            // Timeout after 70 seconds
            setTimeout(() => {
                if (this.ws.readyState !== WebSocket.CLOSED) {
                    console.log('⏰ Test timeout - closing connection');
                    this.ws.close(1000, 'Test timeout');
                }
            }, 70000);
        });
    }
    
    sendTestMessage() {
        if (this.isConnected && this.ws.readyState === WebSocket.OPEN) {
            const message = {
                type: 'chat_message',
                content: {
                    message: 'Hello from stability test',
                    user_profile_id: '2',
                    timestamp: new Date().toISOString()
                }
            };
            
            console.log('📤 Sending test message...');
            this.ws.send(JSON.stringify(message));
        }
    }
}

async function runTest() {
    const test = new WebSocketStabilityTest();
    
    try {
        const result = await test.testConnection();
        console.log('\n📊 TEST RESULTS:');
        console.log('================');
        console.log(`Success: ${result.success}`);
        console.log(`Connection Time: ${result.connectionTime}ms`);
        console.log(`Messages Received: ${result.messageCount}`);
        console.log('\n✅ WebSocket connection is stable!');
        
        if (result.connectionTime > 10000) {
            console.log('⚠️  Connection time is slow (>10s) - this may cause frontend timeouts');
        }
        
    } catch (error) {
        console.log('\n📊 TEST RESULTS:');
        console.log('================');
        console.log(`Success: ${error.success}`);
        console.log(`Error: ${error.error}`);
        console.log(`Connection Time: ${error.connectionTime || 'N/A'}ms`);
        console.log(`Duration: ${error.duration || 'N/A'}ms`);
        console.log('\n❌ WebSocket connection is unstable!');
        
        if (error.connectionTime && error.connectionTime > 25000) {
            console.log('🔧 RECOMMENDATION: Connection takes too long - increase frontend timeouts');
        } else if (error.error && error.error.includes('timeout')) {
            console.log('🔧 RECOMMENDATION: Backend WebSocket endpoint may be unresponsive');
        } else {
            console.log('🔧 RECOMMENDATION: Check backend WebSocket configuration');
        }
    }
}

if (require.main === module) {
    runTest().catch(console.error);
}

module.exports = WebSocketStabilityTest;
