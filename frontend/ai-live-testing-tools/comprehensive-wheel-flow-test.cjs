#!/usr/bin/env node

/**
 * Comprehensive Wheel Flow Test - Robust Testing with Proper Timeouts
 * 
 * This test systematically validates the complete wheel generation and spinning flow
 * with proper timeouts (up to 120 seconds for wheel generation) and detailed logging.
 */

const { chromium } = require('playwright');
const WebSocket = require('ws');

class ComprehensiveWheelFlowTest {
    constructor() {
        this.browser = null;
        this.page = null;
        this.ws = null;
        this.wheelData = null;
        this.testResults = {
            frontendLoaded: false,
            backendConnected: false,
            wheelGenerated: false,
            wheelHasActivities: false,
            activitiesHaveColors: false,
            wheelSpinning: false,
            winnerDetection: false,
            postSpinWorkflow: false
        };
        this.messages = [];
        this.startTime = Date.now();
    }

    log(message) {
        const timestamp = new Date().toISOString();
        const elapsed = ((Date.now() - this.startTime) / 1000).toFixed(1);
        console.log(`[${elapsed}s] ${message}`);
    }

    async initialize() {
        this.log('🚀 Initializing Comprehensive Wheel Flow Test...');
        
        // Launch browser with proper settings
        this.browser = await chromium.launch({ 
            headless: false,
            slowMo: 100,
            args: ['--disable-web-security', '--disable-features=VizDisplayCompositor']
        });
        
        this.page = await this.browser.newPage();
        
        // Set up console logging
        this.page.on('console', msg => {
            if (msg.type() === 'error') {
                this.log(`🖥️  [ERROR] ${msg.text()}`);
            } else if (msg.type() === 'log') {
                this.log(`🖥️  [LOG] ${msg.text()}`);
            }
        });

        this.log('✅ Test environment initialized');
    }

    async setupWebSocketMonitoring() {
        this.log('🔌 Setting up WebSocket monitoring...');
        
        // Monitor WebSocket messages
        await this.page.route('ws://localhost:8000/ws/game/', route => {
            this.log('🔗 WebSocket connection intercepted');
            route.continue();
        });

        // Set up WebSocket listener in the page
        await this.page.addInitScript(() => {
            window.wheelTestData = {
                messages: [],
                wheelReceived: false,
                wheelData: null
            };

            // Override WebSocket to capture messages
            const originalWebSocket = window.WebSocket;
            window.WebSocket = function(url, protocols) {
                const ws = new originalWebSocket(url, protocols);
                
                ws.addEventListener('message', (event) => {
                    try {
                        const data = JSON.parse(event.data);
                        window.wheelTestData.messages.push({
                            type: data.type,
                            timestamp: Date.now(),
                            data: data
                        });
                        
                        if (data.type === 'wheel_data') {
                            window.wheelTestData.wheelReceived = true;
                            window.wheelTestData.wheelData = data;
                            console.log('🎡 WHEEL DATA RECEIVED:', JSON.stringify(data, null, 2));
                        }
                    } catch (e) {
                        console.log('📥 Non-JSON message:', event.data);
                    }
                });
                
                return ws;
            };
        });

        this.log('✅ WebSocket monitoring setup complete');
    }

    async loadFrontend() {
        this.log('🌐 Loading frontend...');
        
        try {
            await this.page.goto('http://localhost:3000/', { 
                waitUntil: 'networkidle',
                timeout: 30000 
            });
            
            // Wait for frontend to initialize
            await this.page.waitForFunction(() => {
                return window.document.title.includes('Goali');
            }, { timeout: 30000 });

            this.testResults.frontendLoaded = true;
            this.log('✅ Frontend loaded successfully');
            
            // Wait for WebSocket connection
            await this.page.waitForFunction(() => {
                return window.wheelTestData && window.wheelTestData.messages.length > 0;
            }, { timeout: 30000 });

            this.testResults.backendConnected = true;
            this.log('✅ Backend connection established');
            
        } catch (error) {
            this.log(`❌ Frontend loading failed: ${error.message}`);
            throw error;
        }
    }

    async sendWheelGenerationRequest() {
        this.log('💬 Sending wheel generation request...');

        try {
            // Check if we're in debug mode and need to select user/LLM
            const debugMode = await this.page.evaluate(() => {
                return document.querySelector('.debug-panel, [class*="debug"]') !== null;
            });

            if (debugMode) {
                this.log('🐛 Debug mode detected, configuring...');

                // Try to select a user (PhiPhi - user ID 2)
                const userSelect = await this.page.locator('select[id*="user"], select[name*="user"]').first();
                if (await userSelect.isVisible({ timeout: 5000 })) {
                    await userSelect.selectOption('2');
                    this.log('✅ Selected user ID 2 (PhiPhi)');
                }

                // Wait a moment for the selection to take effect
                await this.page.waitForTimeout(1000);
            }

            // Wait for chat input to be available and enabled
            await this.page.waitForSelector('textarea', { timeout: 30000 });

            // Check if input is enabled
            const isEnabled = await this.page.evaluate(() => {
                const textarea = document.querySelector('textarea');
                return textarea && !textarea.disabled;
            });

            if (!isEnabled) {
                this.log('⏳ Waiting for chat input to be enabled...');

                // Try clicking on the textarea to enable it
                await this.page.click('textarea');
                await this.page.waitForTimeout(1000);

                // Check again
                const nowEnabled = await this.page.evaluate(() => {
                    const textarea = document.querySelector('textarea');
                    return textarea && !textarea.disabled;
                });

                if (!nowEnabled) {
                    // Try removing disabled attribute directly
                    await this.page.evaluate(() => {
                        const textarea = document.querySelector('textarea');
                        if (textarea) {
                            textarea.disabled = false;
                            textarea.removeAttribute('disabled');
                        }
                    });
                    this.log('🔧 Manually enabled textarea');
                }
            }

            // Send the message
            const message = "I'm restless and need to do things physical. I have 2 hours. Make me a wheel";
            await this.page.fill('textarea', message);
            await this.page.press('textarea', 'Enter');

            this.log('✅ Wheel generation request sent');

        } catch (error) {
            this.log(`❌ Failed to send wheel request: ${error.message}`);

            // Debug info
            const textareaInfo = await this.page.evaluate(() => {
                const textarea = document.querySelector('textarea');
                return {
                    exists: !!textarea,
                    disabled: textarea ? textarea.disabled : null,
                    placeholder: textarea ? textarea.placeholder : null,
                    value: textarea ? textarea.value : null
                };
            });
            this.log(`🔍 Textarea info: ${JSON.stringify(textareaInfo)}`);

            throw error;
        }
    }

    async waitForWheelGeneration() {
        this.log('⏳ Waiting for wheel generation (up to 120 seconds)...');
        
        try {
            // Wait for wheel_data message with extended timeout
            await this.page.waitForFunction(() => {
                return window.wheelTestData && window.wheelTestData.wheelReceived;
            }, { timeout: 120000 }); // 120 seconds timeout

            // Get the wheel data
            const wheelData = await this.page.evaluate(() => {
                return window.wheelTestData.wheelData;
            });

            this.wheelData = wheelData;
            this.testResults.wheelGenerated = true;
            this.log('✅ Wheel data received');
            
            // Analyze wheel data
            if (wheelData && wheelData.wheel && wheelData.wheel.items) {
                const itemCount = wheelData.wheel.items.length;
                this.log(`📊 Wheel has ${itemCount} activities`);
                
                if (itemCount >= 4) {
                    this.testResults.wheelHasActivities = true;
                    this.log('✅ Wheel has sufficient activities');
                    
                    // Check for distinct colors
                    const colors = wheelData.wheel.items.map(item => item.color);
                    const uniqueColors = [...new Set(colors)];
                    this.log(`🎨 Found ${uniqueColors.length} unique colors: ${uniqueColors.join(', ')}`);
                    
                    if (uniqueColors.length >= 3) {
                        this.testResults.activitiesHaveColors = true;
                        this.log('✅ Activities have distinct colors');
                    } else {
                        this.log('⚠️  Activities do not have sufficient color diversity');
                    }
                } else {
                    this.log('❌ Insufficient activities in wheel');
                }
            } else {
                this.log('❌ Wheel data structure is invalid');
            }
            
        } catch (error) {
            this.log(`❌ Wheel generation timeout or failed: ${error.message}`);
            
            // Get debug info
            const messages = await this.page.evaluate(() => {
                return window.wheelTestData ? window.wheelTestData.messages : [];
            });
            
            this.log(`📊 Received ${messages.length} WebSocket messages`);
            messages.forEach((msg, i) => {
                this.log(`  ${i+1}. ${msg.type} (${new Date(msg.timestamp).toISOString()})`);
            });
        }
    }

    async testWheelSpinning() {
        this.log('🎲 Testing wheel spinning mechanics...');
        
        try {
            // Look for wheel container
            const wheelContainer = await this.page.locator('.wheel-container, .wheel, [class*="wheel"]').first();
            
            if (await wheelContainer.isVisible({ timeout: 10000 })) {
                this.log('✅ Wheel container found');
                
                // Look for spin button or clickable wheel
                const spinTrigger = await this.page.locator('button[class*="spin"], .spin-button, [data-testid="spin"], .wheel').first();
                
                if (await spinTrigger.isVisible({ timeout: 5000 })) {
                    this.log('🎯 Spin trigger found, attempting to spin...');
                    await spinTrigger.click();
                    
                    // Wait for spin animation or result
                    await this.page.waitForTimeout(3000);
                    this.testResults.wheelSpinning = true;
                    this.log('✅ Wheel spinning initiated');
                    
                } else {
                    this.log('❌ No spin button or clickable wheel found');
                }
            } else {
                this.log('❌ Wheel container not visible');
            }
            
        } catch (error) {
            this.log(`❌ Wheel spinning test failed: ${error.message}`);
        }
    }

    async testWinnerDetection() {
        this.log('🏆 Testing winner detection...');
        
        try {
            // Look for winner indication
            const winnerElements = await this.page.locator('.winner, .selected, [class*="winner"], [class*="selected"]').all();
            
            if (winnerElements.length > 0) {
                this.testResults.winnerDetection = true;
                this.log('✅ Winner indication found');
            } else {
                this.log('❌ No winner indication found');
            }
            
        } catch (error) {
            this.log(`❌ Winner detection test failed: ${error.message}`);
        }
    }

    async testPostSpinWorkflow() {
        this.log('🤖 Testing post-spin workflow...');
        
        try {
            // Wait for post-spin messages
            await this.page.waitForFunction(() => {
                const messages = window.wheelTestData ? window.wheelTestData.messages : [];
                return messages.some(msg => msg.type === 'chat_message' && 
                    msg.data.content && msg.data.content.includes('activity'));
            }, { timeout: 30000 });
            
            this.testResults.postSpinWorkflow = true;
            this.log('✅ Post-spin workflow detected');
            
        } catch (error) {
            this.log(`❌ Post-spin workflow test failed: ${error.message}`);
        }
    }

    async generateReport() {
        const duration = ((Date.now() - this.startTime) / 1000).toFixed(1);
        const passedTests = Object.values(this.testResults).filter(Boolean).length;
        const totalTests = Object.keys(this.testResults).length;
        
        this.log('\n📊 COMPREHENSIVE WHEEL FLOW TEST REPORT');
        this.log('════════════════════════════════════════════════════════════');
        this.log(`⏱️  Duration: ${duration}s`);
        this.log(`✅ Tests Passed: ${passedTests}/${totalTests}`);
        this.log('');
        
        Object.entries(this.testResults).forEach(([test, passed]) => {
            const icon = passed ? '✅' : '❌';
            this.log(`  ${icon} ${test}: ${passed ? 'PASSED' : 'FAILED'}`);
        });
        
        if (this.wheelData) {
            this.log('\n🎡 Wheel Data Analysis:');
            if (this.wheelData.wheel && this.wheelData.wheel.items) {
                this.log(`  📊 Activities: ${this.wheelData.wheel.items.length}`);
                this.log(`  🎨 Colors: ${[...new Set(this.wheelData.wheel.items.map(i => i.color))].join(', ')}`);
            }
        }
        
        this.log('\n💡 Next Steps:');
        if (!this.testResults.wheelHasActivities) {
            this.log('  1. Fix wheel generation to include proper activities');
        }
        if (!this.testResults.activitiesHaveColors) {
            this.log('  2. Implement distinct colors for wheel activities');
        }
        if (!this.testResults.wheelSpinning) {
            this.log('  3. Add proper wheel spinning mechanics');
        }
        if (!this.testResults.winnerDetection) {
            this.log('  4. Implement winner detection after spin');
        }
        if (!this.testResults.postSpinWorkflow) {
            this.log('  5. Ensure post-spin workflow triggers properly');
        }
    }

    async cleanup() {
        if (this.browser) {
            await this.browser.close();
        }
    }

    async run() {
        try {
            await this.initialize();
            await this.setupWebSocketMonitoring();
            await this.loadFrontend();
            await this.sendWheelGenerationRequest();
            await this.waitForWheelGeneration();
            await this.testWheelSpinning();
            await this.testWinnerDetection();
            await this.testPostSpinWorkflow();
            await this.generateReport();
            
        } catch (error) {
            this.log(`❌ Test failed: ${error.message}`);
        } finally {
            await this.cleanup();
        }
    }
}

// Run the test
if (require.main === module) {
    const test = new ComprehensiveWheelFlowTest();
    test.run().catch(console.error);
}

module.exports = ComprehensiveWheelFlowTest;
