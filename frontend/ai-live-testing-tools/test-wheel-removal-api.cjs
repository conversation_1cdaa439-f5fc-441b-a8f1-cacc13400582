#!/usr/bin/env node

/**
 * Test Wheel Item Removal API - Test if the API can handle current wheel item IDs
 * 
 * This test generates a wheel and then tries to remove a wheel item using the API
 * to see if the current wheel item IDs work with the removal endpoint.
 */

const WebSocket = require('ws');

const CONFIG = {
  gameWebSocketUrl: 'ws://localhost:8000/ws/game/',
  apiBaseUrl: 'http://localhost:8000',
  testUserId: '2',
  testMessage: 'hey, I\'m feeling energetic and I have 2h free ahead of me. It\'s very hot outside though. Generate me the perfect wheel !'
};

class WheelRemovalAPITest {
  constructor() {
    this.ws = null;
    this.wheelData = null;
    this.testResults = {
      wheelReceived: false,
      removalAttempted: false,
      removalSuccessful: false,
      errorMessage: null
    };
  }

  async runTest() {
    console.log('🧪 Testing Wheel Item Removal API');
    console.log('==================================');
    
    try {
      // Step 1: Generate a wheel
      await this.generateWheel();
      
      // Step 2: Try to remove a wheel item
      if (this.wheelData && this.wheelData.wheel && this.wheelData.wheel.items.length > 0) {
        await this.testWheelItemRemoval();
      } else {
        console.log('❌ No wheel items to test removal');
      }
      
      return this.testResults;
    } catch (error) {
      console.error('💥 Test execution failed:', error);
      this.testResults.errorMessage = error.message;
      return this.testResults;
    }
  }

  async generateWheel() {
    console.log('📤 Step 1: Generating wheel...');
    
    return new Promise((resolve, reject) => {
      this.ws = new WebSocket(CONFIG.gameWebSocketUrl);
      
      this.ws.on('open', () => {
        console.log('✅ WebSocket connected');
        this.sendWheelRequest();
      });
      
      this.ws.on('message', (data) => {
        try {
          const message = JSON.parse(data.toString());
          this.handleMessage(message, resolve);
        } catch (e) {
          // Ignore non-JSON messages
        }
      });
      
      this.ws.on('error', (error) => {
        console.error(`❌ WebSocket error: ${error.message}`);
        reject(error);
      });
      
      this.ws.on('close', () => {
        console.log('🔌 WebSocket closed');
        if (!this.testResults.wheelReceived) {
          reject(new Error('WebSocket closed without receiving wheel data'));
        }
      });
      
      // Timeout after 2 minutes
      setTimeout(() => {
        if (this.ws.readyState === WebSocket.OPEN) {
          this.ws.close();
        }
        if (!this.testResults.wheelReceived) {
          reject(new Error('Timeout waiting for wheel data'));
        }
      }, 120000);
    });
  }

  sendWheelRequest() {
    const message = {
      type: 'chat_message',
      content: {
        message: CONFIG.testMessage,
        user_profile_id: CONFIG.testUserId,
        timestamp: new Date().toISOString()
      }
    };
    
    this.ws.send(JSON.stringify(message));
    console.log('✅ Wheel generation request sent');
  }

  handleMessage(message, resolve) {
    if (message.type === 'wheel_data') {
      console.log('🎡 WHEEL DATA RECEIVED!');
      console.log(`📊 Wheel: ${message.wheel.name}`);
      console.log(`📊 Items: ${message.wheel.items.length}`);
      
      this.wheelData = message;
      this.testResults.wheelReceived = true;
      
      // Close WebSocket and proceed to removal test
      this.ws.close();
      resolve();
    }
  }

  async testWheelItemRemoval() {
    console.log('\n📤 Step 2: Testing wheel item removal...');
    
    const firstItem = this.wheelData.wheel.items[0];
    console.log(`🎯 Attempting to remove item: ${firstItem.id} (${firstItem.name})`);
    
    this.testResults.removalAttempted = true;
    
    try {
      const response = await fetch(`${CONFIG.apiBaseUrl}/api/wheel-items/${firstItem.id}/`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        }
      });
      
      console.log(`📡 API Response Status: ${response.status}`);
      
      if (response.ok) {
        const result = await response.json();
        console.log('✅ REMOVAL SUCCESSFUL!');
        console.log(`📊 Updated wheel has ${result.wheel_data.segments.length} segments`);
        this.testResults.removalSuccessful = true;
      } else {
        const errorText = await response.text();
        console.log('❌ REMOVAL FAILED!');
        console.log(`📊 Error: ${response.status} - ${errorText}`);
        this.testResults.errorMessage = `${response.status} - ${errorText}`;
      }
    } catch (error) {
      console.log('❌ REMOVAL FAILED!');
      console.log(`📊 Network Error: ${error.message}`);
      this.testResults.errorMessage = error.message;
    }
  }
}

// Run the test
if (require.main === module) {
  const test = new WheelRemovalAPITest();
  test.runTest()
    .then(results => {
      console.log('\n🏁 TEST COMPLETED');
      console.log('================');
      console.log(`✅ Wheel received: ${results.wheelReceived}`);
      console.log(`📤 Removal attempted: ${results.removalAttempted}`);
      console.log(`🎯 Removal successful: ${results.removalSuccessful}`);
      
      if (results.errorMessage) {
        console.log(`❌ Error: ${results.errorMessage}`);
      }
      
      if (results.wheelReceived && results.removalAttempted && results.removalSuccessful) {
        console.log('\n🎉 ALL TESTS PASSED - WHEEL ITEM REMOVAL WORKS!');
        process.exit(0);
      } else {
        console.log('\n💥 SOME TESTS FAILED');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('💥 Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = { WheelRemovalAPITest };
