#!/usr/bin/env node

/**
 * Direct WebSocket Message Test
 * 
 * This tool connects directly to the backend WebSocket and captures
 * the exact message structure being sent to verify the duplicate content issue.
 */

const WebSocket = require('ws');

const CONFIG = {
  gameWebSocketUrl: 'ws://localhost:8000/ws/game/',
  testUserId: '2',
  testMessage: 'What is my name?'
};

class DirectWebSocketMessageTest {
  constructor() {
    this.ws = null;
    this.receivedMessages = [];
    this.startTime = Date.now();
  }

  async run() {
    console.log('🧪 Direct WebSocket Message Test');
    console.log('=' * 60);
    console.log(`Connecting to: ${CONFIG.gameWebSocketUrl}`);
    console.log(`Test User ID: ${CONFIG.testUserId}`);
    console.log(`Test Message: "${CONFIG.testMessage}"`);
    
    try {
      await this.connectWebSocket();
      await this.sendTestMessage();
      await this.waitForResponse();
      this.analyzeMessages();
    } catch (error) {
      console.error('❌ Test failed:', error);
    } finally {
      if (this.ws) {
        this.ws.close();
      }
    }
  }

  async connectWebSocket() {
    return new Promise((resolve, reject) => {
      this.ws = new WebSocket(CONFIG.gameWebSocketUrl);
      
      const timeout = setTimeout(() => {
        reject(new Error('Connection timeout'));
      }, 10000);

      this.ws.on('open', () => {
        clearTimeout(timeout);
        console.log('✅ WebSocket connected');
        resolve();
      });

      this.ws.on('error', (error) => {
        clearTimeout(timeout);
        console.error('❌ WebSocket error:', error);
        reject(error);
      });

      this.ws.on('message', (data) => {
        this.handleMessage(data);
      });

      this.ws.on('close', (code, reason) => {
        console.log(`🔌 WebSocket closed: ${code} ${reason}`);
      });
    });
  }

  handleMessage(data) {
    const timestamp = Date.now();
    const elapsed = ((timestamp - this.startTime) / 1000).toFixed(1);
    
    try {
      const message = JSON.parse(data.toString());
      
      console.log(`\n📨 [${elapsed}s] RECEIVED MESSAGE:`);
      console.log('Raw data:', data.toString());
      console.log('Parsed structure:');
      console.log(JSON.stringify(message, null, 2));
      
      // Check for duplicate content issue
      if (message.type === 'chat_message') {
        this.analyzeMessageStructure(message);
      }
      
      this.receivedMessages.push({
        timestamp,
        elapsed,
        raw: data.toString(),
        parsed: message
      });
      
    } catch (error) {
      console.error('❌ Failed to parse message:', error);
      console.log('Raw data:', data.toString());
    }
  }

  analyzeMessageStructure(message) {
    console.log('\n🔍 ANALYZING MESSAGE STRUCTURE:');
    
    // Check for expected clean structure
    const hasType = message.hasOwnProperty('type');
    const hasContent = message.hasOwnProperty('content');
    const hasIsUser = message.hasOwnProperty('is_user');
    
    console.log(`- Has 'type': ${hasType} (${message.type})`);
    console.log(`- Has 'content': ${hasContent}`);
    console.log(`- Has 'is_user': ${hasIsUser} (${message.is_user})`);
    
    // Check for problematic fields
    const hasData = message.hasOwnProperty('data');
    const hasDirection = message.hasOwnProperty('direction');
    const hasSessionId = message.hasOwnProperty('session_id');
    const hasUserId = message.hasOwnProperty('user_id');
    const hasTimestamp = message.hasOwnProperty('timestamp');
    
    console.log(`- Has 'data': ${hasData}`);
    console.log(`- Has 'direction': ${hasDirection}`);
    console.log(`- Has 'session_id': ${hasSessionId}`);
    console.log(`- Has 'user_id': ${hasUserId}`);
    console.log(`- Has 'timestamp': ${hasTimestamp}`);
    
    // Check for duplicate content
    if (hasData && message.data) {
      const dataHasType = message.data.hasOwnProperty('type');
      const dataHasContent = message.data.hasOwnProperty('content');
      
      console.log(`- data.type: ${dataHasType} (${message.data.type})`);
      console.log(`- data.content: ${dataHasContent}`);
      
      if (dataHasContent && hasContent) {
        const contentMatch = message.content === message.data.content;
        console.log(`- DUPLICATE CONTENT: ${contentMatch}`);
        
        if (contentMatch) {
          console.log('🚨 DUPLICATE CONTENT DETECTED!');
          console.log('This confirms the frontend duplicate content issue.');
        }
      }
    }
    
    // Determine message type
    if (hasType && hasContent && hasIsUser && !hasData && !hasDirection) {
      console.log('✅ CLEAN CLIENT MESSAGE (expected)');
    } else if (hasData || hasDirection || hasSessionId) {
      console.log('❌ ADMIN MONITORING MESSAGE (unexpected for client!)');
    } else {
      console.log('⚠️ UNKNOWN MESSAGE STRUCTURE');
    }
  }

  async sendTestMessage() {
    const message = {
      type: 'chat_message',
      content: {
        message: CONFIG.testMessage,
        user_profile_id: CONFIG.testUserId,
        timestamp: new Date().toISOString(),
        metadata: {
          requested_workflow: 'discussion'
        }
      }
    };
    
    console.log('\n📤 SENDING TEST MESSAGE:');
    console.log(JSON.stringify(message, null, 2));
    
    this.ws.send(JSON.stringify(message));
  }

  async waitForResponse() {
    console.log('\n⏳ Waiting for responses...');
    
    // Wait for responses (up to 30 seconds)
    await new Promise(resolve => {
      setTimeout(resolve, 30000);
    });
  }

  analyzeMessages() {
    console.log('\n📊 MESSAGE ANALYSIS SUMMARY');
    console.log('=' * 60);
    
    console.log(`Total messages received: ${this.receivedMessages.length}`);
    
    const chatMessages = this.receivedMessages.filter(msg => 
      msg.parsed && msg.parsed.type === 'chat_message'
    );
    
    console.log(`Chat messages: ${chatMessages.length}`);
    
    const duplicateContentMessages = chatMessages.filter(msg => {
      const message = msg.parsed;
      return message.data && 
             message.content && 
             message.data.content === message.content;
    });
    
    console.log(`Messages with duplicate content: ${duplicateContentMessages.length}`);
    
    if (duplicateContentMessages.length > 0) {
      console.log('\n🚨 DUPLICATE CONTENT ISSUE CONFIRMED!');
      console.log('The frontend is receiving messages with duplicate content.');
      console.log('This explains why the frontend displays duplicate messages.');
      
      console.log('\nExample problematic message:');
      console.log(JSON.stringify(duplicateContentMessages[0].parsed, null, 2));
    } else {
      console.log('\n✅ No duplicate content detected.');
      console.log('The issue might be in frontend message processing.');
    }
    
    // Check message types
    const messageTypes = {};
    this.receivedMessages.forEach(msg => {
      if (msg.parsed && msg.parsed.type) {
        messageTypes[msg.parsed.type] = (messageTypes[msg.parsed.type] || 0) + 1;
      }
    });
    
    console.log('\nMessage types received:');
    Object.entries(messageTypes).forEach(([type, count]) => {
      console.log(`  ${type}: ${count}`);
    });
    
    console.log('\n🎯 CONCLUSION:');
    if (duplicateContentMessages.length > 0) {
      console.log('The backend is sending messages with duplicate content structure.');
      console.log('This is the root cause of the frontend duplicate message issue.');
    } else {
      console.log('The backend is sending clean messages.');
      console.log('The issue might be in frontend message handling or display logic.');
    }
  }
}

// Run the test
const test = new DirectWebSocketMessageTest();
test.run().catch(console.error);
