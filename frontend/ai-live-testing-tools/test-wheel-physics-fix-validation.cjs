#!/usr/bin/env node

/**
 * Comprehensive Wheel Physics Fix Validation Test
 * Tests both debug environment and production app to ensure physics are working
 */

const puppeteer = require('puppeteer');
const path = require('path');

class WheelPhysicsValidator {
    constructor() {
        this.browser = null;
        this.debugPage = null;
        this.prodPage = null;
        this.results = {
            debug: { passed: 0, failed: 0, tests: [] },
            production: { passed: 0, failed: 0, tests: [] }
        };
    }

    async initialize() {
        console.log('🚀 Starting Wheel Physics Fix Validation...\n');
        
        this.browser = await puppeteer.launch({
            headless: false,
            defaultViewport: { width: 1200, height: 800 },
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });

        // Open debug environment
        this.debugPage = await this.browser.newPage();
        await this.debugPage.goto('http://localhost:3006/', { waitUntil: 'networkidle0' });
        console.log('✅ Debug environment loaded');

        // Open production app
        this.prodPage = await this.browser.newPage();
        await this.prodPage.goto('http://localhost:3002/', { waitUntil: 'networkidle0' });
        console.log('✅ Production app loaded\n');
    }

    async testDebugEnvironment() {
        console.log('🔍 Testing Debug Environment...');
        
        try {
            // Test 1: Wheel component visibility
            const wheelVisible = await this.debugPage.evaluate(() => {
                const wheel = document.getElementById('debugWheel');
                return wheel && wheel.offsetWidth > 0 && wheel.offsetHeight > 0;
            });
            this.recordTest('debug', 'Wheel Component Visible', wheelVisible);

            // Test 2: Load wheel items
            await this.debugPage.click('#loadWheelBtn');
            await new Promise(resolve => setTimeout(resolve, 2000));

            const itemsLoaded = await this.debugPage.evaluate(() => {
                const wheel = document.getElementById('debugWheel');
                return wheel && wheel.wheelData && wheel.wheelData.segments && wheel.wheelData.segments.length > 0;
            });
            this.recordTest('debug', 'Wheel Items Loaded', itemsLoaded);

            // Test 3: Activities displayed in legend
            const legendVisible = await this.debugPage.evaluate(() => {
                const legend = document.querySelector('.wheel-legend');
                return legend && legend.offsetWidth > 0;
            });
            this.recordTest('debug', 'Activities Legend Displayed', legendVisible);

            // Test 4: Spin button enabled
            const spinEnabled = await this.debugPage.evaluate(() => {
                const spinBtn = document.getElementById('spinWheelBtn');
                return spinBtn && !spinBtn.disabled;
            });
            this.recordTest('debug', 'Spin Button Enabled', spinEnabled);

            // Test 5: Physics engine working
            if (spinEnabled) {
                console.log('   🎡 Testing wheel spin physics...');
                
                // Start ball tracking
                await this.debugPage.click('#trackBallBtn');
                await new Promise(resolve => setTimeout(resolve, 500));

                // Spin the wheel
                await this.debugPage.click('#spinWheelBtn');

                // Wait for spin to start and monitor ball movement
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                const ballMovement = await this.debugPage.evaluate(() => {
                    const trackingInfo = document.getElementById('ballTrackingInfo');
                    const trackingText = trackingInfo ? trackingInfo.textContent : '';
                    
                    // Check if ball tracking shows movement
                    return trackingText.includes('Velocity:') && 
                           trackingText.includes('Position:') &&
                           trackingText.includes('Spinning: ✅');
                });
                this.recordTest('debug', 'Ball Physics Movement', ballMovement);

                // Wait for spin completion
                console.log('   ⏳ Waiting for spin completion...');
                await new Promise(resolve => setTimeout(resolve, 12000));

                // Check if winner was detected
                const winnerDetected = await this.debugPage.evaluate(() => {
                    const winnerDisplay = document.getElementById('winnerDisplay');
                    return winnerDisplay && winnerDisplay.classList.contains('visible');
                });
                this.recordTest('debug', 'Winner Detection', winnerDetected);
            }

        } catch (error) {
            console.error('❌ Debug environment test error:', error.message);
            this.recordTest('debug', 'Test Execution', false);
        }
    }

    async testProductionApp() {
        console.log('\n🔍 Testing Production App...');
        
        try {
            // Test 1: App shell loaded
            const appLoaded = await this.prodPage.evaluate(() => {
                const appShell = document.querySelector('app-shell');
                return appShell && appShell.offsetWidth > 0;
            });
            this.recordTest('production', 'App Shell Loaded', appLoaded);

            // Test 2: Wheel data present (mock data should be enabled)
            await new Promise(resolve => setTimeout(resolve, 3000)); // Wait for initialization
            
            const wheelDataPresent = await this.prodPage.evaluate(() => {
                const wheelContainer = document.querySelector('.wheel-container game-wheel');
                return wheelContainer && wheelContainer.wheelData && wheelContainer.wheelData.segments;
            });
            this.recordTest('production', 'Wheel Data Present', wheelDataPresent);

            // Test 3: Wheel component visible
            const wheelVisible = await this.prodPage.evaluate(() => {
                const wheel = document.querySelector('game-wheel');
                return wheel && wheel.offsetWidth > 0 && wheel.offsetHeight > 0;
            });
            this.recordTest('production', 'Wheel Component Visible', wheelVisible);

            // Test 4: Activities legend visible
            const legendVisible = await this.prodPage.evaluate(() => {
                const legend = document.querySelector('.wheel-legend');
                return legend && legend.offsetWidth > 0;
            });
            this.recordTest('production', 'Activities Legend Visible', legendVisible);

            // Test 5: Spin button present and enabled
            const spinButtonEnabled = await this.prodPage.evaluate(() => {
                const spinBtn = document.querySelector('.spin-button');
                return spinBtn && !spinBtn.disabled;
            });
            this.recordTest('production', 'Spin Button Enabled', spinButtonEnabled);

            // Test 6: Wheel physics working
            if (spinButtonEnabled) {
                console.log('   🎡 Testing production wheel spin...');
                
                // Click spin button
                await this.prodPage.click('.spin-button');
                
                // Wait for spin to start
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Check if wheel is spinning
                const isSpinning = await this.prodPage.evaluate(() => {
                    const wheel = document.querySelector('game-wheel');
                    return wheel && wheel.isSpinning;
                });
                this.recordTest('production', 'Wheel Spinning State', isSpinning);

                // Wait for completion
                console.log('   ⏳ Waiting for production spin completion...');
                await new Promise(resolve => setTimeout(resolve, 12000));

                // Check for completion event or status
                const spinCompleted = await this.prodPage.evaluate(() => {
                    const wheel = document.querySelector('game-wheel');
                    return wheel && !wheel.isSpinning;
                });
                this.recordTest('production', 'Spin Completion', spinCompleted);
            }

        } catch (error) {
            console.error('❌ Production app test error:', error.message);
            this.recordTest('production', 'Test Execution', false);
        }
    }

    recordTest(environment, testName, passed) {
        const result = { name: testName, passed };
        this.results[environment].tests.push(result);
        
        if (passed) {
            this.results[environment].passed++;
            console.log(`   ✅ ${testName}`);
        } else {
            this.results[environment].failed++;
            console.log(`   ❌ ${testName}`);
        }
    }

    async generateReport() {
        console.log('\n📊 WHEEL PHYSICS FIX VALIDATION REPORT');
        console.log('=====================================\n');

        // Debug Environment Results
        console.log('🔧 DEBUG ENVIRONMENT:');
        console.log(`   Passed: ${this.results.debug.passed}`);
        console.log(`   Failed: ${this.results.debug.failed}`);
        console.log(`   Success Rate: ${((this.results.debug.passed / (this.results.debug.passed + this.results.debug.failed)) * 100).toFixed(1)}%`);
        
        this.results.debug.tests.forEach(test => {
            console.log(`   ${test.passed ? '✅' : '❌'} ${test.name}`);
        });

        // Production App Results
        console.log('\n🚀 PRODUCTION APP:');
        console.log(`   Passed: ${this.results.production.passed}`);
        console.log(`   Failed: ${this.results.production.failed}`);
        console.log(`   Success Rate: ${((this.results.production.passed / (this.results.production.passed + this.results.production.failed)) * 100).toFixed(1)}%`);
        
        this.results.production.tests.forEach(test => {
            console.log(`   ${test.passed ? '✅' : '❌'} ${test.name}`);
        });

        // Overall Assessment
        const totalPassed = this.results.debug.passed + this.results.production.passed;
        const totalTests = this.results.debug.tests.length + this.results.production.tests.length;
        const overallSuccess = (totalPassed / totalTests) * 100;

        console.log('\n🎯 OVERALL ASSESSMENT:');
        console.log(`   Total Tests: ${totalTests}`);
        console.log(`   Total Passed: ${totalPassed}`);
        console.log(`   Overall Success Rate: ${overallSuccess.toFixed(1)}%`);

        if (overallSuccess >= 90) {
            console.log('   🎉 EXCELLENT! Physics fixes are working properly.');
        } else if (overallSuccess >= 75) {
            console.log('   ✅ GOOD! Most fixes are working, minor issues remain.');
        } else if (overallSuccess >= 50) {
            console.log('   ⚠️  PARTIAL! Some fixes working, significant issues remain.');
        } else {
            console.log('   ❌ CRITICAL! Major issues still present, fixes need review.');
        }

        console.log('\n📝 RECOMMENDATIONS:');
        if (this.results.debug.failed > 0) {
            console.log('   - Review debug environment configuration');
            console.log('   - Check wheel component initialization in debug mode');
        }
        if (this.results.production.failed > 0) {
            console.log('   - Review production app wheel data loading');
            console.log('   - Check mock data configuration');
        }
        if (overallSuccess < 100) {
            console.log('   - Monitor browser console for physics engine errors');
            console.log('   - Verify ball movement and gravity settings');
        }
    }

    async cleanup() {
        if (this.browser) {
            await this.browser.close();
        }
    }

    async run() {
        try {
            await this.initialize();
            await this.testDebugEnvironment();
            await this.testProductionApp();
            await this.generateReport();
        } catch (error) {
            console.error('❌ Validation failed:', error);
        } finally {
            await this.cleanup();
        }
    }
}

// Run the validation
const validator = new WheelPhysicsValidator();
validator.run().catch(console.error);
