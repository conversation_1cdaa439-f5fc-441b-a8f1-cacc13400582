/**
 * Debug Wheel DOM Structure
 * Simple script to check the actual DOM structure and wheel component loading
 */

const puppeteer = require('puppeteer');

async function debugWheelDOM() {
  console.log('🔍 Debugging wheel DOM structure...');
  
  const browser = await puppeteer.launch({ 
    headless: false,
    defaultViewport: { width: 1200, height: 800 }
  });
  
  try {
    const page = await browser.newPage();
    
    // Navigate to the app
    console.log('📱 Navigating to http://localhost:3001...');
    await page.goto('http://localhost:3001', { waitUntil: 'networkidle0' });
    
    // Wait for app to load
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Take screenshot
    await page.screenshot({ path: 'wheel-debug.png', fullPage: true });
    console.log('📸 Screenshot saved as wheel-debug.png');
    
    // Check DOM structure
    const structure = await page.evaluate(() => {
      const appShell = document.querySelector('app-shell');
      const wheels = document.querySelectorAll('game-wheel');
      const wheelContainer = document.querySelector('.wheel-container');
      const wheelSection = document.querySelector('.wheel-section');
      
      return {
        hasAppShell: !!appShell,
        wheelCount: wheels.length,
        hasWheelContainer: !!wheelContainer,
        hasWheelSection: !!wheelSection,
        wheelContainerHTML: wheelContainer ? wheelContainer.innerHTML.substring(0, 500) : 'Not found',
        wheelSectionHTML: wheelSection ? wheelSection.innerHTML.substring(0, 500) : 'Not found',
        appShellWheelData: appShell && appShell.wheelData ? 'Has data' : 'No data',
        appShellHTML: appShell ? appShell.shadowRoot ? 'Has shadow root' : 'No shadow root' : 'No app-shell'
      };
    });
    
    console.log('\n🔍 DOM Structure Analysis:');
    console.log('App Shell:', structure.hasAppShell ? '✅' : '❌');
    console.log('Wheel Count:', structure.wheelCount);
    console.log('Wheel Container:', structure.hasWheelContainer ? '✅' : '❌');
    console.log('Wheel Section:', structure.hasWheelSection ? '✅' : '❌');
    console.log('App Shell Wheel Data:', structure.appShellWheelData);
    console.log('App Shell Shadow Root:', structure.appShellHTML);
    
    if (structure.hasWheelContainer) {
      console.log('\n📋 Wheel Container HTML (first 500 chars):');
      console.log(structure.wheelContainerHTML);
    }
    
    if (structure.hasWheelSection) {
      console.log('\n📋 Wheel Section HTML (first 500 chars):');
      console.log(structure.wheelSectionHTML);
    }
    
    // Check shadow DOM
    const shadowDOMStructure = await page.evaluate(() => {
      const appShell = document.querySelector('app-shell');
      if (appShell && appShell.shadowRoot) {
        const shadowWheels = appShell.shadowRoot.querySelectorAll('game-wheel');
        const shadowWheelContainer = appShell.shadowRoot.querySelector('.wheel-container');
        const shadowWheelSection = appShell.shadowRoot.querySelector('.wheel-section');
        
        return {
          shadowWheelCount: shadowWheels.length,
          hasShadowWheelContainer: !!shadowWheelContainer,
          hasShadowWheelSection: !!shadowWheelSection,
          shadowWheelContainerHTML: shadowWheelContainer ? shadowWheelContainer.innerHTML.substring(0, 500) : 'Not found'
        };
      }
      return { error: 'No shadow root found' };
    });
    
    console.log('\n🌑 Shadow DOM Analysis:');
    if (shadowDOMStructure.error) {
      console.log('❌', shadowDOMStructure.error);
    } else {
      console.log('Shadow Wheel Count:', shadowDOMStructure.shadowWheelCount);
      console.log('Shadow Wheel Container:', shadowDOMStructure.hasShadowWheelContainer ? '✅' : '❌');
      console.log('Shadow Wheel Section:', shadowDOMStructure.hasShadowWheelSection ? '✅' : '❌');
      
      if (shadowDOMStructure.hasShadowWheelContainer) {
        console.log('\n📋 Shadow Wheel Container HTML (first 500 chars):');
        console.log(shadowDOMStructure.shadowWheelContainerHTML);
      }
    }
    
    // Wait for user to see the browser
    console.log('\n⏳ Browser will stay open for 10 seconds for manual inspection...');
    await new Promise(resolve => setTimeout(resolve, 10000));
    
  } catch (error) {
    console.error('❌ Debug failed:', error);
  } finally {
    await browser.close();
  }
}

// Run the debug
debugWheelDOM().catch(console.error);
