/**
 * Playwright Frontend Issue Analyzer
 * 
 * Comprehensive analysis of current frontend issues:
 * 1. Duplicate/verbose backend responses
 * 2. Chat area focus and interaction problems
 * 3. User recognition testing
 * 4. Wheel generation and spinning validation
 */

const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

class FrontendIssueAnalyzer {
    constructor() {
        this.browser = null;
        this.page = null;
        this.websocketMessages = [];
        this.chatMessages = [];
        this.issues = [];
        this.testResults = {
            frontendLoaded: false,
            websocketConnected: false,
            chatFocusWorking: false,
            userRecognitionWorking: false,
            duplicateResponses: false,
            wheelGenerationWorking: false,
            wheelSpinningWorking: false,
            winnerDetectionWorking: false
        };
    }

    async initialize() {
        console.log('🎭 Initializing Playwright Frontend Issue Analyzer...');
        
        this.browser = await chromium.launch({ 
            headless: false,
            slowMo: 1000,
            args: ['--disable-web-security', '--disable-features=VizDisplayCompositor']
        });
        
        this.page = await this.browser.newPage();
        
        // Set up console logging
        this.page.on('console', msg => {
            const text = msg.text();
            console.log(`🖥️  Console: ${text}`);
            if (text.includes('WebSocket') || text.includes('connection')) {
                this.websocketMessages.push({
                    timestamp: new Date().toISOString(),
                    type: 'console',
                    message: text
                });
            }
        });

        // Set up error logging
        this.page.on('pageerror', error => {
            console.log(`❌ Page Error: ${error.message}`);
            this.issues.push({
                type: 'page_error',
                message: error.message,
                timestamp: new Date().toISOString()
            });
        });

        // Set up WebSocket interception
        await this.setupWebSocketInterception();
    }

    async setupWebSocketInterception() {
        console.log('🔌 Setting up WebSocket interception...');
        
        await this.page.routeWebSocket(/ws:\/\/.*/, ws => {
            console.log('🔗 WebSocket connection intercepted');
            
            ws.onMessage(message => {
                const data = message.toString();
                console.log(`📨 WebSocket Message Received: ${data.substring(0, 200)}...`);
                
                try {
                    const parsed = JSON.parse(data);
                    this.websocketMessages.push({
                        timestamp: new Date().toISOString(),
                        direction: 'received',
                        type: parsed.type || 'unknown',
                        data: parsed,
                        raw: data
                    });

                    // Analyze for duplicate responses
                    if (parsed.type === 'chat_message') {
                        this.analyzeChatMessage(parsed);
                    }
                } catch (e) {
                    console.log(`⚠️  Failed to parse WebSocket message: ${e.message}`);
                }
            });

            ws.onClose(() => {
                console.log('🔌 WebSocket connection closed');
            });

            ws.connectToServer();
        });
    }

    analyzeChatMessage(message) {
        this.chatMessages.push({
            timestamp: new Date().toISOString(),
            content: message.content,
            sender: message.sender || 'unknown'
        });

        // Check for duplicate responses
        const recentMessages = this.chatMessages.slice(-5);
        const duplicates = recentMessages.filter(msg => 
            msg.content === message.content && 
            msg.timestamp !== message.timestamp
        );

        if (duplicates.length > 0) {
            this.testResults.duplicateResponses = true;
            this.issues.push({
                type: 'duplicate_response',
                message: 'Duplicate chat message detected',
                content: message.content,
                timestamp: new Date().toISOString()
            });
        }

        // Check for verbose responses
        if (message.content && message.content.length > 500) {
            this.issues.push({
                type: 'verbose_response',
                message: 'Verbose response detected',
                length: message.content.length,
                timestamp: new Date().toISOString()
            });
        }
    }

    async testFrontendLoading() {
        console.log('🌐 Testing frontend loading...');
        
        try {
            await this.page.goto('http://localhost:3002/', { 
                waitUntil: 'networkidle',
                timeout: 30000 
            });
            
            // Wait for main components to load
            await this.page.waitForSelector('app-shell', { timeout: 10000 });
            
            this.testResults.frontendLoaded = true;
            console.log('✅ Frontend loaded successfully');
            
            // Take screenshot for documentation
            await this.page.screenshot({ 
                path: 'logs/frontend-loaded.png',
                fullPage: true 
            });
            
        } catch (error) {
            console.log(`❌ Frontend loading failed: ${error.message}`);
            this.issues.push({
                type: 'frontend_loading',
                message: error.message,
                timestamp: new Date().toISOString()
            });
        }
    }

    async testWebSocketConnection() {
        console.log('🔌 Testing WebSocket connection...');
        
        // Wait for WebSocket connection
        await this.page.waitForTimeout(15000);
        
        // Check for connection status in console or UI
        const connectionStatus = await this.page.evaluate(() => {
            // Check if there's a connection status indicator
            const statusElement = document.querySelector('[data-connection-status]');
            if (statusElement) {
                return statusElement.textContent || statusElement.getAttribute('data-connection-status');
            }
            
            // Check console for connection messages
            return window.console ? 'unknown' : 'no_console';
        });

        if (this.websocketMessages.length > 0) {
            this.testResults.websocketConnected = true;
            console.log('✅ WebSocket connection detected');
        } else {
            console.log('❌ No WebSocket messages detected');
            this.issues.push({
                type: 'websocket_connection',
                message: 'No WebSocket messages detected',
                timestamp: new Date().toISOString()
            });
        }
    }

    async testChatFocusAndInteraction() {
        console.log('💬 Testing chat focus and interaction...');
        
        try {
            // Look for chat input element
            const chatSelectors = [
                'input[type="text"]',
                'textarea',
                '[data-chat-input]',
                '.chat-input',
                '#chat-input',
                'input[placeholder*="message"]',
                'input[placeholder*="chat"]'
            ];

            let chatInput = null;
            for (const selector of chatSelectors) {
                try {
                    chatInput = await this.page.waitForSelector(selector, { timeout: 2000 });
                    if (chatInput) {
                        console.log(`📝 Found chat input with selector: ${selector}`);
                        break;
                    }
                } catch (e) {
                    // Continue to next selector
                }
            }

            if (!chatInput) {
                console.log('❌ No chat input element found');
                this.issues.push({
                    type: 'chat_input_missing',
                    message: 'Chat input element not found',
                    timestamp: new Date().toISOString()
                });
                return;
            }

            // Test clicking on chat input
            await chatInput.click();
            await this.page.waitForTimeout(1000);

            // Test if input is focused
            const isFocused = await this.page.evaluate(() => {
                const activeElement = document.activeElement;
                return activeElement && (
                    activeElement.tagName === 'INPUT' || 
                    activeElement.tagName === 'TEXTAREA'
                );
            });

            if (isFocused) {
                this.testResults.chatFocusWorking = true;
                console.log('✅ Chat input focus working');
            } else {
                console.log('❌ Chat input focus not working');
                this.issues.push({
                    type: 'chat_focus_issue',
                    message: 'Chat input does not receive focus after click',
                    timestamp: new Date().toISOString()
                });
            }

            // Test typing
            await this.page.type('input, textarea', 'test message');
            await this.page.waitForTimeout(1000);

            const inputValue = await this.page.evaluate(() => {
                const input = document.querySelector('input, textarea');
                return input ? input.value : '';
            });

            if (inputValue.includes('test message')) {
                console.log('✅ Chat typing working');
            } else {
                console.log('❌ Chat typing not working');
                this.issues.push({
                    type: 'chat_typing_issue',
                    message: 'Unable to type in chat input',
                    timestamp: new Date().toISOString()
                });
            }

        } catch (error) {
            console.log(`❌ Chat interaction test failed: ${error.message}`);
            this.issues.push({
                type: 'chat_interaction_error',
                message: error.message,
                timestamp: new Date().toISOString()
            });
        }
    }

    async testUserRecognition() {
        console.log('👤 Testing user recognition scenario...');
        
        try {
            // Clear any existing text and type the test message
            await this.page.fill('input, textarea', '');
            await this.page.type('input, textarea', 'hey! do you recognize me?');
            
            // Send the message (look for send button or press Enter)
            const sendButton = await this.page.locator('button:has-text("Send"), [data-send], .send-button').first();
            if (await sendButton.isVisible()) {
                await sendButton.click();
            } else {
                await this.page.press('input, textarea', 'Enter');
            }

            console.log('📤 User recognition message sent');

            // Wait for response and analyze
            await this.page.waitForTimeout(10000);

            // Count responses received
            const responseCount = this.chatMessages.filter(msg => 
                msg.timestamp > new Date(Date.now() - 15000).toISOString()
            ).length;

            if (responseCount > 1) {
                this.testResults.duplicateResponses = true;
                console.log(`⚠️  Multiple responses detected: ${responseCount}`);
            }

            if (responseCount > 0) {
                this.testResults.userRecognitionWorking = true;
                console.log('✅ User recognition response received');
            } else {
                console.log('❌ No user recognition response received');
            }

        } catch (error) {
            console.log(`❌ User recognition test failed: ${error.message}`);
            this.issues.push({
                type: 'user_recognition_error',
                message: error.message,
                timestamp: new Date().toISOString()
            });
        }
    }

    async generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            testResults: this.testResults,
            issues: this.issues,
            websocketMessages: this.websocketMessages,
            chatMessages: this.chatMessages,
            summary: {
                totalIssues: this.issues.length,
                criticalIssues: this.issues.filter(i => 
                    ['chat_focus_issue', 'duplicate_response', 'websocket_connection'].includes(i.type)
                ).length,
                testsPassed: Object.values(this.testResults).filter(Boolean).length,
                totalTests: Object.keys(this.testResults).length
            }
        };

        // Save detailed report
        const reportPath = path.join('logs', `frontend-issue-analysis-${Date.now()}.json`);
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

        console.log('\n📊 FRONTEND ISSUE ANALYSIS REPORT');
        console.log('=====================================');
        console.log(`✅ Tests Passed: ${report.summary.testsPassed}/${report.summary.totalTests}`);
        console.log(`❌ Total Issues: ${report.summary.totalIssues}`);
        console.log(`🚨 Critical Issues: ${report.summary.criticalIssues}`);
        console.log(`📄 Detailed report saved to: ${reportPath}`);

        return report;
    }

    async cleanup() {
        if (this.browser) {
            await this.browser.close();
        }
    }
}

// Main execution
async function main() {
    const analyzer = new FrontendIssueAnalyzer();
    
    try {
        await analyzer.initialize();
        await analyzer.testFrontendLoading();
        await analyzer.testWebSocketConnection();
        await analyzer.testChatFocusAndInteraction();
        await analyzer.testUserRecognition();
        
        const report = await analyzer.generateReport();
        
        console.log('\n🎯 NEXT STEPS BASED ON ANALYSIS:');
        if (report.summary.criticalIssues > 0) {
            console.log('1. Address critical issues identified in the report');
            console.log('2. Re-run analysis to validate fixes');
        }
        console.log('3. Proceed with wheel generation testing');
        console.log('4. Update documentation with findings');
        
    } catch (error) {
        console.error(`❌ Analysis failed: ${error.message}`);
    } finally {
        await analyzer.cleanup();
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = { FrontendIssueAnalyzer };
