#!/usr/bin/env node

/**
 * Quick Connection Dashboard Test
 * 
 * A simple, fast test to verify the connection dashboard is working.
 * Perfect for quick validation during development.
 */

import WebSocket from 'ws';
import { CONFIG } from './config.js';

class QuickDashboardTest {
  constructor() {
    this.adminSocket = null;
    this.clientSockets = [];
    this.testsPassed = 0;
    this.testsFailed = 0;
  }

  async runQuickTest() {
    console.log('🚀 Quick Connection Dashboard Test');
    console.log('=====================================\n');

    try {
      // Test 1: Admin dashboard connection
      await this.testAdminConnection();
      
      // Test 2: Create a few client connections
      await this.testClientConnections();
      
      // Test 3: Test real-time data
      await this.testRealTimeData();
      
      // Test 4: Test dashboard UI
      await this.testDashboardUI();
      
      console.log('\n📊 QUICK TEST RESULTS:');
      console.log(`✅ Passed: ${this.testsPassed}`);
      console.log(`❌ Failed: ${this.testsFailed}`);
      
      if (this.testsFailed === 0) {
        console.log('\n🎉 All tests passed! Connection dashboard is working correctly.');
      } else {
        console.log('\n⚠️  Some tests failed. Check the logs above for details.');
      }
      
    } catch (error) {
      console.error('❌ Quick test failed:', error.message);
      this.testsFailed++;
    } finally {
      await this.cleanup();
    }
  }

  async testAdminConnection() {
    console.log('🔌 Testing admin dashboard WebSocket connection...');
    
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Admin connection timeout'));
      }, 10000);

      this.adminSocket = new WebSocket('ws://localhost:8000/ws/connection-monitor/');
      
      this.adminSocket.on('open', () => {
        clearTimeout(timeout);
        console.log('✅ Admin dashboard connected successfully');
        this.testsPassed++;
        resolve();
      });

      this.adminSocket.on('error', (error) => {
        clearTimeout(timeout);
        console.error('❌ Admin dashboard connection failed:', error.message);
        this.testsFailed++;
        reject(error);
      });

      this.adminSocket.on('message', (data) => {
        try {
          const message = JSON.parse(data);
          console.log(`📨 Admin received: ${message.type}`);
        } catch (error) {
          console.error('Failed to parse admin message:', error.message);
        }
      });
    });
  }

  async testClientConnections() {
    console.log('\n👥 Testing client connections...');
    
    const numClients = 3;
    const promises = [];
    
    for (let i = 1; i <= numClients; i++) {
      promises.push(this.createTestClient(i));
    }
    
    try {
      await Promise.all(promises);
      console.log(`✅ Created ${numClients} client connections successfully`);
      this.testsPassed++;
    } catch (error) {
      console.error('❌ Failed to create client connections:', error.message);
      this.testsFailed++;
      throw error;
    }
  }

  async createTestClient(clientId) {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error(`Client ${clientId} connection timeout`));
      }, 5000);

      const socket = new WebSocket(CONFIG.backend.websocketUrl);
      
      socket.on('open', () => {
        clearTimeout(timeout);
        console.log(`🔗 Client ${clientId} connected`);
        
        // Send identification message
        socket.send(JSON.stringify({
          type: 'chat_message',
          content: {
            message: `Hello from test client ${clientId}`,
            user_profile_id: '2', // Use real user ID (PhiPhi - fake user for testing)
            timestamp: new Date().toISOString()
          }
        }));
        
        this.clientSockets.push(socket);
        resolve();
      });

      socket.on('error', (error) => {
        clearTimeout(timeout);
        reject(error);
      });

      socket.on('message', (data) => {
        try {
          const message = JSON.parse(data);
          console.log(`📨 Client ${clientId} received: ${message.type}`);
        } catch (error) {
          console.error(`Failed to parse message for client ${clientId}:`, error.message);
        }
      });
    });
  }

  async testRealTimeData() {
    console.log('\n📊 Testing real-time data...');
    
    if (!this.adminSocket) {
      console.error('❌ Admin socket not available for real-time data test');
      this.testsFailed++;
      return;
    }
    
    try {
      // Request connection data
      this.adminSocket.send(JSON.stringify({ type: 'get_connections' }));
      
      // Request system health
      this.adminSocket.send(JSON.stringify({ type: 'get_system_health' }));
      
      // Request message stats
      this.adminSocket.send(JSON.stringify({ type: 'get_message_stats' }));
      
      // Wait a bit for responses
      await this.sleep(3000);
      
      console.log('✅ Real-time data requests sent successfully');
      this.testsPassed++;
      
    } catch (error) {
      console.error('❌ Real-time data test failed:', error.message);
      this.testsFailed++;
    }
  }

  async testDashboardUI() {
    console.log('\n🖥️  Testing dashboard UI accessibility...');
    
    try {
      const response = await fetch('http://localhost:8000/admin/connection-dashboard/');
      
      if (response.ok) {
        console.log('✅ Dashboard UI is accessible');
        this.testsPassed++;
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      console.error('❌ Dashboard UI test failed:', error.message);
      console.log('   Make sure you are logged in as a staff user');
      this.testsFailed++;
    }
  }

  async sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async cleanup() {
    console.log('\n🧹 Cleaning up...');
    
    // Close client sockets
    for (const socket of this.clientSockets) {
      if (socket.readyState === WebSocket.OPEN) {
        socket.close();
      }
    }
    
    // Close admin socket
    if (this.adminSocket && this.adminSocket.readyState === WebSocket.OPEN) {
      this.adminSocket.close();
    }
    
    console.log('✅ Cleanup completed');
  }
}

// Run the quick test
if (import.meta.url === `file://${process.argv[1]}`) {
  const tester = new QuickDashboardTest();
  
  // Handle graceful shutdown
  process.on('SIGINT', () => {
    console.log('\n🛑 Received SIGINT, cleaning up...');
    tester.cleanup().then(() => process.exit(0));
  });
  
  tester.runQuickTest().catch((error) => {
    console.error('Quick test failed:', error.message);
    process.exit(1);
  });
}

export default QuickDashboardTest;
