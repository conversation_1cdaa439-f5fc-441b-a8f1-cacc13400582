#!/usr/bin/env node

/**
 * Test Clean Architecture Domain Flow
 * 
 * This test verifies that:
 * 1. Backend produces wheel data with proper domain codes
 * 2. Frontend can apply colors using the domain color service
 * 3. No color assignment happens in backend business logic
 */

const WebSocket = require('ws');
const { getDomainColor, validateWheelDomains, applyColorsToWheel } = require('../src/services/domainColorService.js');

// Test configuration
const WS_URL = 'ws://localhost:8000/ws/chat/1/';
const TEST_TIMEOUT = 30000; // 30 seconds

console.log('🧪 Testing Clean Architecture Domain Flow');
console.log('=' .repeat(60));

async function testDomainColorService() {
    console.log('\n🎨 Testing Frontend Domain Color Service');
    console.log('-'.repeat(40));
    
    // Test domain color mapping
    const testDomains = [
        'phys_dance', 'creative_culinary', 'intel_strategic', 
        'soc_comm', 'explor_sensory', 'emot_aware'
    ];
    
    let allValid = true;
    
    for (const domain of testDomains) {
        const color = getDomainColor(domain);
        const isValid = color && color.startsWith('#') && color.length === 7;
        
        console.log(`  ${isValid ? '✅' : '❌'} ${domain.padEnd(20)} → ${color}`);
        
        if (!isValid) allValid = false;
    }
    
    return allValid;
}

async function testWheelGeneration() {
    console.log('\n🎡 Testing Wheel Generation with Clean Architecture');
    console.log('-'.repeat(40));
    
    return new Promise((resolve, reject) => {
        const ws = new WebSocket(WS_URL);
        let testPassed = false;
        
        const timeout = setTimeout(() => {
            if (!testPassed) {
                console.log('❌ Test timed out');
                ws.close();
                resolve(false);
            }
        }, TEST_TIMEOUT);
        
        ws.on('open', () => {
            console.log('🔌 Connected to WebSocket');
            
            // Send wheel generation request
            const message = {
                type: 'user_message',
                message: 'Generate a wheel for me',
                user_profile_id: 1
            };
            
            ws.send(JSON.stringify(message));
        });
        
        ws.on('message', (data) => {
            try {
                const response = JSON.parse(data.toString());
                
                if (response.type === 'wheel_generated' && response.wheel) {
                    console.log('🎡 Wheel received from backend');
                    
                    const wheel = response.wheel;
                    const items = wheel.items || [];
                    
                    console.log(`📊 Wheel has ${items.length} items`);
                    
                    // Check domains (should exist, no colors from backend)
                    let domainsValid = true;
                    let backendColorsFound = false;
                    
                    items.forEach((item, index) => {
                        const domain = item.domain;
                        const color = item.color;
                        
                        if (!domain || domain === 'general') {
                            console.log(`  ❌ Item ${index + 1}: Missing or generic domain (${domain})`);
                            domainsValid = false;
                        } else {
                            console.log(`  ✅ Item ${index + 1}: ${item.name} → ${domain}`);
                        }
                        
                        if (color && color !== '#95A5A6') {
                            console.log(`  ⚠️  Item ${index + 1}: Backend assigned color ${color} (should be frontend)`);
                            backendColorsFound = true;
                        }
                    });
                    
                    // Test frontend color application
                    console.log('\n🎨 Testing Frontend Color Application');
                    const validation = validateWheelDomains(wheel);
                    console.log(`  Domain validation: ${validation.valid ? '✅ PASS' : '❌ FAIL'}`);
                    
                    if (!validation.valid) {
                        validation.issues.forEach(issue => console.log(`    - ${issue}`));
                    }
                    
                    // Apply colors using frontend service
                    const coloredWheel = applyColorsToWheel({ ...wheel });
                    let frontendColorsValid = true;
                    
                    coloredWheel.items.forEach((item, index) => {
                        if (!item.color || item.color === '#95A5A6') {
                            console.log(`  ❌ Item ${index + 1}: No color applied by frontend`);
                            frontendColorsValid = false;
                        } else {
                            console.log(`  ✅ Item ${index + 1}: Frontend applied ${item.color}`);
                        }
                    });
                    
                    // Final assessment
                    const architectureClean = domainsValid && !backendColorsFound && frontendColorsValid;
                    
                    console.log('\n📋 Test Results:');
                    console.log(`  Domains from backend: ${domainsValid ? '✅ PASS' : '❌ FAIL'}`);
                    console.log(`  No backend colors: ${!backendColorsFound ? '✅ PASS' : '❌ FAIL'}`);
                    console.log(`  Frontend colors work: ${frontendColorsValid ? '✅ PASS' : '❌ FAIL'}`);
                    console.log(`  Clean architecture: ${architectureClean ? '✅ PASS' : '❌ FAIL'}`);
                    
                    testPassed = true;
                    clearTimeout(timeout);
                    ws.close();
                    resolve(architectureClean);
                }
            } catch (error) {
                console.error('❌ Error parsing WebSocket message:', error);
            }
        });
        
        ws.on('error', (error) => {
            console.error('❌ WebSocket error:', error);
            clearTimeout(timeout);
            resolve(false);
        });
        
        ws.on('close', () => {
            if (!testPassed) {
                console.log('🔌 WebSocket closed without receiving wheel');
                clearTimeout(timeout);
                resolve(false);
            }
        });
    });
}

async function runTests() {
    try {
        // Test 1: Frontend domain color service
        const colorServiceWorks = await testDomainColorService();
        
        // Test 2: Wheel generation with clean architecture
        const wheelGenerationWorks = await testWheelGeneration();
        
        // Final results
        console.log('\n' + '='.repeat(60));
        console.log('🏁 FINAL TEST RESULTS');
        console.log('='.repeat(60));
        console.log(`🎨 Frontend Color Service: ${colorServiceWorks ? '✅ PASS' : '❌ FAIL'}`);
        console.log(`🎡 Clean Architecture Flow: ${wheelGenerationWorks ? '✅ PASS' : '❌ FAIL'}`);
        
        const overallSuccess = colorServiceWorks && wheelGenerationWorks;
        console.log(`\n🎯 Overall Result: ${overallSuccess ? '✅ ALL TESTS PASS' : '❌ SOME TESTS FAILED'}`);
        
        if (overallSuccess) {
            console.log('\n🎉 Clean architecture is working correctly!');
            console.log('   - Backend provides domain codes without colors');
            console.log('   - Frontend applies colors using domain color service');
            console.log('   - Separation of concerns is maintained');
        } else {
            console.log('\n🔧 Issues found that need to be addressed:');
            if (!colorServiceWorks) {
                console.log('   - Frontend domain color service needs fixes');
            }
            if (!wheelGenerationWorks) {
                console.log('   - Backend-frontend domain flow needs fixes');
            }
        }
        
        process.exit(overallSuccess ? 0 : 1);
        
    } catch (error) {
        console.error('❌ Test execution failed:', error);
        process.exit(1);
    }
}

// Run the tests
runTests();
