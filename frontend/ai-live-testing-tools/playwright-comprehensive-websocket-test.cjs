#!/usr/bin/env node

/**
 * Comprehensive Playwright WebSocket Test & Fix
 * 
 * This tool integrates Playwright with your existing testing methodology to:
 * 1. Diagnose WebSocket connection issues
 * 2. Test complete user stories with real browser automation
 * 3. Simulate user "2" (phiphi) interactions
 * 4. Test wheel generation and spinning
 * 5. Document findings and limitations
 */

const { chromium } = require('playwright');
const fs = require('fs');

class ComprehensiveWebSocketTester {
    constructor() {
        this.browser = null;
        this.context = null;
        this.page = null;
        this.wsConnections = [];
        this.testResults = {
            frontendLoaded: false,
            backendConnected: false,
            websocketConnected: false,
            userAuthenticated: false,
            chatFunctional: false,
            wheelGenerated: false,
            wheelSpinnable: false,
            winnerDetectable: false,
            errors: [],
            messages: [],
            limitations: []
        };
        this.startTime = Date.now();
    }

    async initialize() {
        console.log('🚀 Initializing Comprehensive WebSocket Tester...');
        
        try {
            this.browser = await chromium.launch({
                headless: false,
                slowMo: 1000,
                args: ['--disable-web-security']
            });

            this.context = await this.browser.newContext({
                viewport: { width: 1280, height: 720 }
            });

            this.page = await this.context.newPage();
            
            // Setup logging
            this.page.on('console', msg => {
                const text = msg.text();
                console.log(`🖥️  [${msg.type()}] ${text}`);
                this.testResults.messages.push({
                    type: msg.type(),
                    text: text,
                    timestamp: Date.now() - this.startTime
                });
            });

            this.page.on('pageerror', error => {
                console.error('❌ Page Error:', error.message);
                this.testResults.errors.push({
                    type: 'page_error',
                    message: error.message,
                    timestamp: Date.now() - this.startTime
                });
            });

            await this.setupWebSocketInterception();
            console.log('✅ Tester initialized');
            return true;
        } catch (error) {
            console.error('❌ Initialization failed:', error.message);
            this.testResults.errors.push({
                type: 'init_error',
                message: error.message,
                timestamp: Date.now() - this.startTime
            });
            return false;
        }
    }

    async setupWebSocketInterception() {
        console.log('🔌 Setting up WebSocket interception...');
        
        await this.page.routeWebSocket('**/ws/**', ws => {
            const wsInfo = {
                url: ws.url(),
                connected: false,
                messages: [],
                startTime: Date.now()
            };
            
            this.wsConnections.push(wsInfo);
            console.log(`🔗 WebSocket intercepted: ${ws.url()}`);

            // Connect to server and monitor
            const server = ws.connectToServer();
            
            server.onMessage(message => {
                console.log('📨 WebSocket ← Server:', message.substring(0, 100) + '...');
                wsInfo.messages.push({
                    direction: 'from_server',
                    data: message,
                    timestamp: Date.now() - this.startTime
                });
                wsInfo.connected = true;
                this.testResults.websocketConnected = true;
            });

            ws.onMessage(message => {
                console.log('📤 WebSocket → Server:', message.substring(0, 100) + '...');
                wsInfo.messages.push({
                    direction: 'to_server',
                    data: message,
                    timestamp: Date.now() - this.startTime
                });
                server.send(message);
            });

            server.onClose(() => {
                console.log('❌ WebSocket closed:', ws.url());
                wsInfo.connected = false;
            });
        });
    }

    async testFrontendLoad() {
        console.log('🌐 Testing frontend load...');
        
        try {
            await this.page.goto('http://localhost:3001');
            await this.page.waitForLoadState('domcontentloaded', { timeout: 10000 });
            
            // Check if the page loaded properly
            const title = await this.page.title();
            console.log(`📄 Page title: ${title}`);
            
            // Wait for frontend initialization
            await this.page.waitForTimeout(3000);
            
            this.testResults.frontendLoaded = true;
            console.log('✅ Frontend loaded successfully');
            return true;
        } catch (error) {
            console.error('❌ Frontend load failed:', error.message);
            this.testResults.errors.push({
                type: 'frontend_load_error',
                message: error.message,
                timestamp: Date.now() - this.startTime
            });
            return false;
        }
    }

    async testBackendConnectivity() {
        console.log('🔗 Testing backend connectivity...');
        
        try {
            // Check if backend is available by looking at console messages
            const backendMessages = this.testResults.messages.filter(msg => 
                msg.text.includes('Backend server not available') || 
                msg.text.includes('demo mode')
            );
            
            if (backendMessages.length > 0) {
                console.log('⚠️  Backend not available - frontend in demo mode');
                this.testResults.backendConnected = false;
                this.testResults.limitations.push('Backend server not responding - testing in demo mode');
            } else {
                console.log('✅ Backend appears to be connected');
                this.testResults.backendConnected = true;
            }
            
            return this.testResults.backendConnected;
        } catch (error) {
            console.error('❌ Backend connectivity test failed:', error.message);
            this.testResults.errors.push({
                type: 'backend_test_error',
                message: error.message,
                timestamp: Date.now() - this.startTime
            });
            return false;
        }
    }

    async authenticateAsPhiphi() {
        console.log('👤 Authenticating as user 2 (phiphi)...');
        
        try {
            // Check if debug panel is available
            await this.page.keyboard.press('Control+Shift+D');
            await this.page.waitForTimeout(1000);
            
            // Look for user selection dropdown
            const userSelect = this.page.locator('select[name="user"], select:has(option:text("phiphi"))').first();
            const selectCount = await userSelect.count();
            
            if (selectCount > 0) {
                await userSelect.selectOption('2');
                console.log('✅ Selected user 2 (phiphi)');
                this.testResults.userAuthenticated = true;
            } else {
                console.log('⚠️  Debug panel not found - may be in production mode');
                this.testResults.userAuthenticated = false;
                this.testResults.limitations.push('Debug panel not available - cannot select specific user');
            }
            
            return this.testResults.userAuthenticated;
        } catch (error) {
            console.error('❌ Authentication failed:', error.message);
            this.testResults.errors.push({
                type: 'auth_error',
                message: error.message,
                timestamp: Date.now() - this.startTime
            });
            return false;
        }
    }

    async testChatFunctionality() {
        console.log('💬 Testing chat functionality...');
        
        try {
            // Find chat input
            const chatInput = this.page.locator('input[type="text"], textarea, [data-chat-input]').first();
            const inputCount = await chatInput.count();
            
            if (inputCount === 0) {
                console.log('⚠️  No chat input found');
                this.testResults.chatFunctional = false;
                this.testResults.limitations.push('Chat input element not found');
                return false;
            }
            
            // Send test message
            console.log('📝 Sending: "I\'m bored"');
            await chatInput.fill("I'm bored");
            await chatInput.press('Enter');
            
            // Wait for response
            await this.page.waitForTimeout(3000);
            
            // Check for AI response
            const messages = await this.page.locator('.message, .chat-message, [data-message]').count();
            if (messages > 0) {
                console.log(`✅ Chat functional - found ${messages} messages`);
                this.testResults.chatFunctional = true;
            } else {
                console.log('⚠️  No chat messages visible');
                this.testResults.chatFunctional = false;
                this.testResults.limitations.push('Chat messages not visible in UI');
            }
            
            return this.testResults.chatFunctional;
        } catch (error) {
            console.error('❌ Chat test failed:', error.message);
            this.testResults.errors.push({
                type: 'chat_error',
                message: error.message,
                timestamp: Date.now() - this.startTime
            });
            return false;
        }
    }

    async testWheelGeneration() {
        console.log('🎡 Testing wheel generation...');
        
        try {
            // Send message that should trigger wheel generation
            const chatInput = this.page.locator('input[type="text"], textarea, [data-chat-input]').first();
            const inputCount = await chatInput.count();
            
            if (inputCount > 0) {
                console.log('📝 Sending: "I feel like doing exercise"');
                await chatInput.fill("I feel like doing exercise");
                await chatInput.press('Enter');
                
                // Wait for wheel generation
                console.log('⏳ Waiting for wheel generation...');
                await this.page.waitForTimeout(10000);
                
                // Look for wheel element
                const wheelSelectors = [
                    '.wheel',
                    '[data-wheel]',
                    'game-wheel',
                    '.wheel-container',
                    '#wheel'
                ];
                
                let wheelFound = false;
                for (const selector of wheelSelectors) {
                    const count = await this.page.locator(selector).count();
                    if (count > 0) {
                        console.log(`✅ Wheel found with selector: ${selector}`);
                        wheelFound = true;
                        break;
                    }
                }
                
                this.testResults.wheelGenerated = wheelFound;
                
                if (!wheelFound) {
                    console.log('⚠️  Wheel element not found');
                    this.testResults.limitations.push('Wheel element not visible or not generated');
                }
            } else {
                console.log('⚠️  Cannot test wheel generation - no chat input');
                this.testResults.limitations.push('Cannot trigger wheel generation without chat input');
            }
            
            return this.testResults.wheelGenerated;
        } catch (error) {
            console.error('❌ Wheel generation test failed:', error.message);
            this.testResults.errors.push({
                type: 'wheel_generation_error',
                message: error.message,
                timestamp: Date.now() - this.startTime
            });
            return false;
        }
    }

    async testWheelSpinning() {
        console.log('🎯 Testing wheel spinning mechanics...');
        
        if (!this.testResults.wheelGenerated) {
            console.log('⚠️  Cannot test spinning - wheel not generated');
            this.testResults.limitations.push('Wheel spinning test skipped - wheel not available');
            return false;
        }
        
        try {
            // Look for spin button
            const spinSelectors = [
                'button:has-text("Spin")',
                '.spin-button',
                '[data-spin]',
                'button[onclick*="spin"]',
                '.wheel-spin-btn'
            ];
            
            let spinButton = null;
            for (const selector of spinSelectors) {
                const button = this.page.locator(selector).first();
                if (await button.count() > 0) {
                    spinButton = button;
                    console.log(`🎯 Found spin button: ${selector}`);
                    break;
                }
            }
            
            if (spinButton) {
                console.log('🎲 Clicking spin button...');
                await spinButton.click();
                
                // Wait for spin animation
                await this.page.waitForTimeout(5000);
                
                this.testResults.wheelSpinnable = true;
                console.log('✅ Wheel spinning initiated');
                
                // Try to detect winner
                await this.testWinnerDetection();
            } else {
                console.log('⚠️  Spin button not found');
                this.testResults.wheelSpinnable = false;
                this.testResults.limitations.push('Spin button not found or not accessible');
            }
            
            return this.testResults.wheelSpinnable;
        } catch (error) {
            console.error('❌ Wheel spinning test failed:', error.message);
            this.testResults.errors.push({
                type: 'wheel_spinning_error',
                message: error.message,
                timestamp: Date.now() - this.startTime
            });
            return false;
        }
    }

    async testWinnerDetection() {
        console.log('🏆 Testing winner detection...');
        
        try {
            // Look for winner indicators
            const winnerSelectors = [
                '.winner',
                '.selected',
                '[data-winner]',
                '.wheel-result',
                '.winning-segment'
            ];
            
            let winnerFound = false;
            let winnerText = '';
            
            for (const selector of winnerSelectors) {
                const element = this.page.locator(selector).first();
                if (await element.count() > 0) {
                    winnerText = await element.textContent();
                    if (winnerText && winnerText.trim()) {
                        console.log(`🏆 Winner detected: "${winnerText}"`);
                        winnerFound = true;
                        break;
                    }
                }
            }
            
            if (!winnerFound) {
                // Try to detect winner through JavaScript evaluation
                const jsWinner = await this.page.evaluate(() => {
                    // Look for any element that might indicate a winner
                    const indicators = document.querySelectorAll('[class*="winner"], [class*="selected"], [data-result]');
                    for (const indicator of indicators) {
                        if (indicator.textContent && indicator.textContent.trim()) {
                            return indicator.textContent.trim();
                        }
                    }
                    return null;
                });
                
                if (jsWinner) {
                    console.log(`🏆 Winner detected via JS: "${jsWinner}"`);
                    winnerFound = true;
                    winnerText = jsWinner;
                }
            }
            
            this.testResults.winnerDetectable = winnerFound;
            
            if (!winnerFound) {
                console.log('⚠️  Winner detection unclear');
                this.testResults.limitations.push('Winner detection mechanism not clearly identifiable');
            }
            
            return winnerFound;
        } catch (error) {
            console.error('❌ Winner detection test failed:', error.message);
            this.testResults.errors.push({
                type: 'winner_detection_error',
                message: error.message,
                timestamp: Date.now() - this.startTime
            });
            return false;
        }
    }

    async generateComprehensiveReport() {
        const duration = Date.now() - this.startTime;
        
        const report = {
            timestamp: new Date().toISOString(),
            duration: duration,
            testResults: this.testResults,
            wsConnections: this.wsConnections,
            summary: {
                totalTests: 8,
                passed: Object.values(this.testResults).filter(v => v === true).length,
                failed: this.testResults.errors.length,
                limitations: this.testResults.limitations.length,
                websocketConnections: this.wsConnections.length
            },
            recommendations: this.generateRecommendations()
        };

        // Save detailed report
        const reportPath = `./test-results/playwright-comprehensive-report-${Date.now()}.json`;
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        
        // Display summary
        console.log('\n📊 COMPREHENSIVE TEST REPORT');
        console.log('═'.repeat(50));
        console.log(`📁 Report saved to: ${reportPath}`);
        console.log(`⏱️  Duration: ${Math.round(duration / 1000)}s`);
        console.log(`✅ Tests Passed: ${report.summary.passed}/${report.summary.totalTests}`);
        console.log(`❌ Errors: ${report.summary.failed}`);
        console.log(`⚠️  Limitations: ${report.summary.limitations}`);
        console.log(`🔌 WebSocket Connections: ${report.summary.websocketConnections}`);
        
        console.log('\n🎯 Test Results:');
        console.log(`  Frontend Loaded: ${this.testResults.frontendLoaded ? '✅' : '❌'}`);
        console.log(`  Backend Connected: ${this.testResults.backendConnected ? '✅' : '❌'}`);
        console.log(`  WebSocket Connected: ${this.testResults.websocketConnected ? '✅' : '❌'}`);
        console.log(`  User Authenticated: ${this.testResults.userAuthenticated ? '✅' : '❌'}`);
        console.log(`  Chat Functional: ${this.testResults.chatFunctional ? '✅' : '❌'}`);
        console.log(`  Wheel Generated: ${this.testResults.wheelGenerated ? '✅' : '❌'}`);
        console.log(`  Wheel Spinnable: ${this.testResults.wheelSpinnable ? '✅' : '❌'}`);
        console.log(`  Winner Detectable: ${this.testResults.winnerDetectable ? '✅' : '❌'}`);
        
        if (this.testResults.limitations.length > 0) {
            console.log('\n⚠️  Limitations Found:');
            this.testResults.limitations.forEach((limitation, i) => {
                console.log(`  ${i + 1}. ${limitation}`);
            });
        }
        
        if (report.recommendations.length > 0) {
            console.log('\n💡 Recommendations:');
            report.recommendations.forEach((rec, i) => {
                console.log(`  ${i + 1}. ${rec}`);
            });
        }
        
        return report;
    }

    generateRecommendations() {
        const recommendations = [];
        
        if (!this.testResults.backendConnected) {
            recommendations.push('Fix backend connectivity - check Docker containers and port 8000');
        }
        
        if (!this.testResults.websocketConnected) {
            recommendations.push('Investigate WebSocket connection issues - check ws://localhost:8000/ws/game/');
        }
        
        if (!this.testResults.userAuthenticated) {
            recommendations.push('Ensure debug panel is accessible for user selection');
        }
        
        if (!this.testResults.wheelGenerated) {
            recommendations.push('Verify wheel generation workflow and UI elements');
        }
        
        if (!this.testResults.winnerDetectable) {
            recommendations.push('Improve winner detection mechanism with clear UI indicators');
        }
        
        return recommendations;
    }

    async cleanup() {
        console.log('🧹 Cleaning up...');
        if (this.page) await this.page.close();
        if (this.context) await this.context.close();
        if (this.browser) await this.browser.close();
    }
}

// Main execution
async function main() {
    const tester = new ComprehensiveWebSocketTester();
    
    try {
        if (!await tester.initialize()) {
            process.exit(1);
        }
        
        // Run comprehensive tests
        await tester.testFrontendLoad();
        await tester.testBackendConnectivity();
        await tester.authenticateAsPhiphi();
        await tester.testChatFunctionality();
        await tester.testWheelGeneration();
        await tester.testWheelSpinning();
        
        // Generate comprehensive report
        await tester.generateComprehensiveReport();
        
        console.log('\n🎉 Comprehensive testing completed!');
        
    } catch (error) {
        console.error('❌ Test execution failed:', error.message);
    } finally {
        await tester.cleanup();
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = ComprehensiveWebSocketTester;
