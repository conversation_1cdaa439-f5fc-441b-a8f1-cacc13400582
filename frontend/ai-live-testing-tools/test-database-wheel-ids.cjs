#!/usr/bin/env node

/**
 * Test Database Wheel IDs - Verify that WebSocket sends actual database wheel item IDs
 * 
 * This test generates a wheel and compares the WebSocket wheel item IDs 
 * with the actual database wheel item IDs to ensure they match.
 */

const WebSocket = require('ws');

const CONFIG = {
  gameWebSocketUrl: 'ws://localhost:8000/ws/game/',
  testUserId: '2',
  testMessage: 'hey, I\'m feeling energetic and I have 2h free ahead of me. It\'s very hot outside though. Generate me the perfect wheel !'
};

class DatabaseWheelIDTest {
  constructor() {
    this.ws = null;
    this.wheelData = null;
    this.testResults = {
      wheelReceived: false,
      databaseIDsMatch: false,
      properIDFormat: false
    };
  }

  async runTest() {
    console.log('🧪 Testing Database Wheel ID Consistency');
    console.log('========================================');
    
    return new Promise((resolve, reject) => {
      this.ws = new WebSocket(CONFIG.gameWebSocketUrl);
      
      this.ws.on('open', () => {
        console.log('✅ WebSocket connected');
        this.sendWheelRequest();
      });
      
      this.ws.on('message', (data) => {
        try {
          const message = JSON.parse(data.toString());
          this.handleMessage(message);
        } catch (e) {
          console.log(`📥 Non-JSON message: ${data}`);
        }
      });
      
      this.ws.on('error', (error) => {
        console.error(`❌ WebSocket error: ${error.message}`);
        reject(error);
      });
      
      this.ws.on('close', () => {
        console.log('🔌 WebSocket closed');
        resolve(this.testResults);
      });
      
      // Timeout after 2 minutes
      setTimeout(() => {
        if (this.ws.readyState === WebSocket.OPEN) {
          this.ws.close();
        }
        resolve(this.testResults);
      }, 120000);
    });
  }

  sendWheelRequest() {
    console.log('📤 Sending wheel generation request...');
    
    const message = {
      type: 'chat_message',
      content: {
        message: CONFIG.testMessage,
        user_profile_id: CONFIG.testUserId,
        timestamp: new Date().toISOString()
      }
    };
    
    this.ws.send(JSON.stringify(message));
    console.log('✅ Request sent');
  }

  handleMessage(message) {
    console.log(`📥 Received: ${message.type}`);
    
    if (message.type === 'wheel_data') {
      console.log('🎡 WHEEL DATA RECEIVED!');
      console.log('======================');
      
      this.wheelData = message;
      this.testResults.wheelReceived = true;
      
      // Analyze wheel data structure
      this.analyzeWheelData(message.wheel);
      
      // Close connection after analysis
      setTimeout(() => {
        this.ws.close();
      }, 1000);
    }
  }

  analyzeWheelData(wheel) {
    if (!wheel || !wheel.items || !Array.isArray(wheel.items)) {
      console.log('❌ Invalid wheel structure');
      return;
    }

    console.log(`📊 Wheel: ${wheel.name}`);
    console.log(`📊 Items: ${wheel.items.length}`);
    console.log('');

    let properIDFormat = true;
    let databaseIDsMatch = true;

    wheel.items.forEach((item, index) => {
      console.log(`🔍 Item ${index + 1}:`);
      console.log(`   - ID: ${item.id}`);
      console.log(`   - Name: ${item.name}`);
      console.log(`   - Activity Tailored ID: ${item.activity_tailored_id}`);
      
      // Check if wheel item ID has proper database format
      if (item.id && item.id.startsWith('item_')) {
        console.log(`   ✅ GOOD: Database wheel item ID format: ${item.id}`);
      } else if (item.id && (item.id.startsWith('wheel-item-') || item.id.startsWith('item-'))) {
        console.log(`   ⚠️  WARNING: Generated wheel item ID (not from database): ${item.id}`);
        databaseIDsMatch = false;
      } else if (item.id && (item.id.startsWith('llm_tailored_') || item.id.startsWith('generic-'))) {
        console.log(`   ❌ ERROR: Activity tailored ID used as wheel item ID: ${item.id}`);
        properIDFormat = false;
        databaseIDsMatch = false;
      } else {
        console.log(`   ❌ ERROR: Unexpected wheel item ID format: ${item.id}`);
        properIDFormat = false;
        databaseIDsMatch = false;
      }
      
      console.log('');
    });

    this.testResults.properIDFormat = properIDFormat;
    this.testResults.databaseIDsMatch = databaseIDsMatch;

    // Summary
    console.log('📊 ANALYSIS RESULTS:');
    console.log('===================');
    console.log(`✅ Wheel received: ${this.testResults.wheelReceived}`);
    console.log(`${properIDFormat ? '✅' : '❌'} Proper ID format (no activity IDs): ${properIDFormat}`);
    console.log(`${databaseIDsMatch ? '✅' : '❌'} Database IDs used (item_* format): ${databaseIDsMatch}`);
    
    const allGood = properIDFormat && databaseIDsMatch;
    console.log('');
    if (allGood) {
      console.log(`🎯 OVERALL RESULT: ✅ DATABASE WHEEL ID FIX SUCCESSFUL`);
      console.log(`🎯 WebSocket now sends actual database wheel item IDs`);
    } else {
      console.log(`🎯 OVERALL RESULT: ❌ ISSUES REMAIN`);
      if (!properIDFormat) {
        console.log(`❌ Still sending activity tailored IDs instead of wheel item IDs`);
      }
      if (!databaseIDsMatch) {
        console.log(`❌ Not using actual database wheel item IDs`);
      }
    }
  }
}

// Run the test
if (require.main === module) {
  const test = new DatabaseWheelIDTest();
  test.runTest()
    .then(results => {
      console.log('\n🏁 TEST COMPLETED');
      console.log('================');
      
      const success = results.wheelReceived && 
                     results.properIDFormat && 
                     results.databaseIDsMatch;
      
      if (success) {
        console.log('🎉 ALL TESTS PASSED - DATABASE WHEEL ID FIX SUCCESSFUL!');
        process.exit(0);
      } else {
        console.log('💥 SOME TESTS FAILED - ISSUES REMAIN');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('💥 Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = { DatabaseWheelIDTest };
