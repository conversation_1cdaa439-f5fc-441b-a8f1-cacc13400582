#!/usr/bin/env node

// Complete integration test to verify frontend-backend integration
import WebSocket from 'ws';

class CompleteIntegrationTest {
  constructor() {
    this.colors = {
      reset: '\x1b[0m',
      gray: '\x1b[90m',
      green: '\x1b[32m',
      blue: '\x1b[34m',
      yellow: '\x1b[33m',
      cyan: '\x1b[36m',
      magenta: '\x1b[35m',
      red: '\x1b[31m'
    };
    
    this.messages = [];
    this.wheelData = null;
    this.userSteps = [
      "I'm bored",
      "I feel like doing exercise, what do you propose?"
    ];
    this.step = 0;
    this.userId = '2';
  }

  async start() {
    console.log(`${this.colors.blue}🧪 Complete Integration Test${this.colors.reset}`);
    console.log(`${this.colors.blue}================================${this.colors.reset}`);
    console.log(`${this.colors.blue}Testing complete user story with frontend data processing${this.colors.reset}\n`);
    
    try {
      this.socket = new WebSocket('ws://localhost:8000/ws/game/');
      
      this.socket.on('open', () => {
        console.log(`${this.colors.green}✅ Connected to backend${this.colors.reset}`);
      });

      this.socket.on('message', (data) => {
        try {
          const message = JSON.parse(data.toString());
          this.handleMessage(message);
        } catch (error) {
          console.error(`${this.colors.red}❌ Failed to parse message: ${error.message}${this.colors.reset}`);
        }
      });

      this.socket.on('error', (error) => {
        console.error(`${this.colors.red}❌ WebSocket error: ${error.message}${this.colors.reset}`);
      });

      this.socket.on('close', () => {
        console.log(`${this.colors.gray}🔌 Connection closed${this.colors.reset}`);
        this.generateReport();
        process.exit(0);
      });

      // Auto-close after 60 seconds
      setTimeout(() => {
        console.log(`${this.colors.yellow}⏰ Test timeout - generating report${this.colors.reset}`);
        this.socket.close();
      }, 60000);

    } catch (error) {
      console.error(`${this.colors.red}❌ Failed to start test: ${error.message}${this.colors.reset}`);
      process.exit(1);
    }
  }

  handleMessage(message) {
    this.messages.push(message);
    
    switch (message.type) {
      case 'system_message':
        console.log(`${this.colors.magenta}🔔 System: ${message.content}${this.colors.reset}`);
        // Start user story after system message
        if (this.step === 0) {
          setTimeout(() => this.sendNextMessage(), 2000);
        }
        break;
        
      case 'chat_message':
        if (message.is_user) {
          console.log(`${this.colors.cyan}👤 User: ${message.content}${this.colors.reset}`);
        } else {
          console.log(`${this.colors.green}🤖 AI: ${message.content.substring(0, 100)}...${this.colors.reset}`);
          // Continue user story after AI response
          if (this.step < this.userSteps.length) {
            setTimeout(() => this.sendNextMessage(), 3000);
          }
        }
        break;
        
      case 'wheel_data':
        console.log(`${this.colors.magenta}🎡 Wheel data received!${this.colors.reset}`);
        this.wheelData = message.wheel;
        this.testFrontendWheelProcessing(message.wheel);
        // End test after wheel generation
        setTimeout(() => this.socket.close(), 3000);
        break;
        
      case 'debug_info':
        // Test the fixed debug logic
        let debugMessage = 'Unknown debug message';
        if (message.content && typeof message.content === 'object') {
          if (message.content.message) {
            debugMessage = message.content.message;
          } else if (message.content.source) {
            debugMessage = `${message.content.source}: ${message.content.level || 'info'}`;
          } else {
            debugMessage = JSON.stringify(message.content);
          }
        }
        console.log(`${this.colors.gray}🔧 Debug: ${debugMessage}${this.colors.reset}`);
        break;
        
      case 'processing_status':
        console.log(`${this.colors.yellow}⚙️ Processing: ${message.status}${this.colors.reset}`);
        break;
        
      case 'workflow_status':
        console.log(`${this.colors.blue}📋 Workflow ${message.workflow_id}: ${message.status}${this.colors.reset}`);
        break;
        
      default:
        console.log(`${this.colors.gray}📦 ${message.type}${this.colors.reset}`);
    }
  }

  sendNextMessage() {
    if (this.step >= this.userSteps.length) {
      console.log(`${this.colors.yellow}✅ User story completed${this.colors.reset}`);
      return;
    }

    const userMessage = this.userSteps[this.step];
    console.log(`\n${this.colors.cyan}👤 Sending: "${userMessage}"${this.colors.reset}`);
    
    const message = {
      type: 'chat_message',
      content: {
        message: userMessage,
        user_profile_id: this.userId,
        timestamp: new Date().toISOString()
      }
    };

    try {
      this.socket.send(JSON.stringify(message));
      this.step++;
    } catch (error) {
      console.error(`${this.colors.red}❌ Failed to send message: ${error.message}${this.colors.reset}`);
    }
  }

  testFrontendWheelProcessing(wheelData) {
    console.log(`\n${this.colors.blue}🧪 Testing Frontend Wheel Processing${this.colors.reset}`);
    console.log(`${this.colors.blue}======================================${this.colors.reset}`);
    
    // Simulate the frontend wheel data processing logic from app-shell.ts
    let items = [];
    if (wheelData.items) {
      items = wheelData.items;
    } else if (wheelData.activities) {
      items = wheelData.activities;
    } else if (Array.isArray(wheelData)) {
      items = wheelData;
    }

    if (items.length > 0) {
      const processedWheelData = {
        segments: items.map((item, index) => {
          // Priority order: title (most specific) > activity_name > name (often generic)
          let displayText = `Activity ${index + 1}`; // fallback

          if (item.title && item.title.trim() && !item.title.match(/^Activity \d+$/)) {
            displayText = item.title;
          } else if (item.activity_name && item.activity_name.trim()) {
            displayText = item.activity_name;
          } else if (item.name && item.name.trim() && !item.name.match(/^Activity \d+$/)) {
            displayText = item.name;
          }

          return {
            id: item.id || `item-${index}`,
            text: displayText,
            percentage: item.percentage || (item.probability * 100) || (100 / items.length),
            color: item.color || this.generateWheelColor(index)
          };
        }),
        wheelId: `wheel-${Date.now()}`,
        createdAt: new Date().toISOString()
      };

      console.log(`${this.colors.green}✅ Wheel processing successful!${this.colors.reset}`);
      console.log(`${this.colors.green}   Segments: ${processedWheelData.segments.length}${this.colors.reset}`);
      
      processedWheelData.segments.forEach((segment, index) => {
        const isGeneric = segment.text.match(/^Activity \d+$/);
        const status = isGeneric ? `${this.colors.red}❌ GENERIC` : `${this.colors.green}✅ PROPER`;
        console.log(`${this.colors.green}   ${index + 1}. ${segment.text} ${status}${this.colors.reset}`);
      });
      
      return processedWheelData;
    } else {
      console.log(`${this.colors.red}❌ No wheel items found${this.colors.reset}`);
      return null;
    }
  }

  generateWheelColor(index) {
    const colors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
      '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
    ];
    return colors[index % colors.length];
  }

  generateReport() {
    console.log(`\n${this.colors.blue}📊 Integration Test Report${this.colors.reset}`);
    console.log(`${this.colors.blue}===========================${this.colors.reset}`);
    
    const systemMessages = this.messages.filter(m => m.type === 'system_message');
    const chatMessages = this.messages.filter(m => m.type === 'chat_message');
    const wheelMessages = this.messages.filter(m => m.type === 'wheel_data');
    const debugMessages = this.messages.filter(m => m.type === 'debug_info');
    
    console.log(`${this.colors.green}✅ Total messages: ${this.messages.length}${this.colors.reset}`);
    console.log(`${this.colors.green}✅ System messages: ${systemMessages.length}${this.colors.reset}`);
    console.log(`${this.colors.green}✅ Chat messages: ${chatMessages.length}${this.colors.reset}`);
    console.log(`${this.colors.green}✅ Debug messages: ${debugMessages.length}${this.colors.reset}`);
    
    if (wheelMessages.length > 0) {
      console.log(`${this.colors.green}✅ Wheel generated: YES${this.colors.reset}`);
      console.log(`${this.colors.green}✅ Wheel items: ${this.wheelData?.items?.length || 0}${this.colors.reset}`);
    } else {
      console.log(`${this.colors.red}❌ Wheel generated: NO${this.colors.reset}`);
    }
    
    console.log(`${this.colors.green}✅ User story steps completed: ${this.step}/${this.userSteps.length}${this.colors.reset}`);
    
    if (this.step === this.userSteps.length && wheelMessages.length > 0) {
      console.log(`\n${this.colors.green}🎉 INTEGRATION TEST PASSED!${this.colors.reset}`);
    } else {
      console.log(`\n${this.colors.red}❌ INTEGRATION TEST FAILED!${this.colors.reset}`);
    }
  }
}

// Run the test
const test = new CompleteIntegrationTest();
test.start().catch(console.error);
