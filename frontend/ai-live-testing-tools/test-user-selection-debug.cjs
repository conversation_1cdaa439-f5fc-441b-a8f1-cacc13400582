#!/usr/bin/env node

/**
 * Debug User Selection Test
 * 
 * This test specifically checks which user is being selected in the frontend
 * and verifies the dropdown selection logic.
 */

const { chromium } = require('playwright');

async function debugUserSelection(port = 3001) {
    console.log('🔍 Debug User Selection Test');
    console.log(`   Port: ${port}`);
    console.log(`   URL: http://localhost:${port}`);
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000  // Slow down for debugging
    });
    
    try {
        const page = await browser.newPage();
        
        // Navigate to the application
        await page.goto(`http://localhost:${port}`);
        await page.waitForLoadState('networkidle');
        
        console.log('✓ Application loaded');
        
        // Open debug panel
        const debugButton = await page.locator('button:has-text("Debug")').first();
        if (await debugButton.isVisible()) {
            await debugButton.click();
            await page.waitForTimeout(2000);
            console.log('✓ Debug panel opened');
        } else {
            console.log('❌ Debug button not found');
            return;
        }
        
        // Check user dropdown
        console.log('\n🔍 Analyzing user dropdown...');
        const userDropdown = await page.locator('select[id*="user"], select:has(option:text-matches("User"))').first();
        
        if (await userDropdown.isVisible()) {
            console.log('✓ User dropdown found');
            
            // Get all options
            const userOptions = await userDropdown.locator('option').all();
            console.log(`   Total options: ${userOptions.length}`);
            
            // List all options
            for (let i = 0; i < userOptions.length; i++) {
                const option = userOptions[i];
                const value = await option.getAttribute('value');
                const text = await option.textContent();
                const isSelected = await option.isSelected();
                
                console.log(`   Option ${i}: value="${value}", text="${text}", selected=${isSelected}`);
            }
            
            // Get currently selected option
            const selectedValue = await userDropdown.inputValue();
            console.log(`\n   Currently selected: ${selectedValue}`);
            
            // Select the last option (what the test should do)
            if (userOptions.length > 1) {
                const lastOption = userOptions[userOptions.length - 1];
                const lastValue = await lastOption.getAttribute('value');
                const lastText = await lastOption.textContent();
                
                console.log(`\n🎯 Last option details:`);
                console.log(`   Value: ${lastValue}`);
                console.log(`   Text: ${lastText}`);
                
                // Select the last option
                await userDropdown.selectOption(lastValue);
                await page.waitForTimeout(1000);
                
                const newSelectedValue = await userDropdown.inputValue();
                console.log(`   After selection: ${newSelectedValue}`);
                
                if (newSelectedValue === lastValue) {
                    console.log('✅ Successfully selected last user');
                } else {
                    console.log('❌ Failed to select last user');
                }
            }
        } else {
            console.log('❌ User dropdown not found');
        }
        
        // Check if there are any other user-related elements
        console.log('\n🔍 Looking for other user-related elements...');
        const allSelects = await page.locator('select').all();
        console.log(`   Total select elements: ${allSelects.length}`);
        
        for (let i = 0; i < allSelects.length; i++) {
            const select = allSelects[i];
            const id = await select.getAttribute('id');
            const name = await select.getAttribute('name');
            const options = await select.locator('option').all();
            
            console.log(`   Select ${i}: id="${id}", name="${name}", options=${options.length}`);
            
            if (options.length > 0) {
                const firstOption = await options[0].textContent();
                console.log(`     First option: "${firstOption}"`);
            }
        }
        
        console.log('\n✅ User selection debug completed');
        
    } catch (error) {
        console.error('❌ Error during user selection debug:', error);
    } finally {
        await browser.close();
    }
}

// Get port from command line argument
const port = process.argv[2] ? parseInt(process.argv[2]) : 3001;

debugUserSelection(port).catch(console.error);
