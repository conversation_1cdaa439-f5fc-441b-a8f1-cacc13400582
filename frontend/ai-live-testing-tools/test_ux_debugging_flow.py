#!/usr/bin/env python3
"""
Comprehensive UX Debugging Flow Test

This script tests the complete high-level UX debugging flow with PhiPhi user (id:2):
1. Authentication and connection
2. User profile modal functionality
3. Activity catalog loading and auto-tailoring
4. Wheel generation and spinning
5. Winning modal information display
6. Activity replacement functionality

Usage:
    python test_ux_debugging_flow.py [port]
    
Example:
    python test_ux_debugging_flow.py 5173
"""

import asyncio
import sys
import time
from pathlib import Path

# Add the parent directory to the path to import test utilities
sys.path.append(str(Path(__file__).parent))

from test_utils import (
    setup_browser, 
    wait_for_element, 
    wait_for_text,
    click_element,
    type_text,
    get_element_text,
    take_screenshot,
    log_test_step,
    log_test_result
)

class UXDebuggingFlowTest:
    """Comprehensive UX debugging flow test"""
    
    def __init__(self, port=5173):
        self.port = port
        self.base_url = f"http://localhost:{port}"
        self.browser = None
        self.page = None
        self.test_results = []
        
    async def setup(self):
        """Setup browser and navigate to app"""
        log_test_step("Setting up browser and navigating to app")
        self.browser, self.page = await setup_browser()
        await self.page.goto(self.base_url)
        await self.page.wait_for_load_state('networkidle')
        
    async def test_authentication_flow(self):
        """Test authentication and initial connection"""
        log_test_step("Testing authentication flow")
        
        try:
            # Check if login form is present
            login_form = await wait_for_element(self.page, 'login-form', timeout=5000)
            if login_form:
                log_test_step("Login form detected, performing authentication")
                
                # Fill in login credentials (using PhiPhi's credentials)
                await type_text(self.page, 'input[type="email"]', '<EMAIL>')
                await type_text(self.page, 'input[type="password"]', 'password123')
                
                # Click login button
                await click_element(self.page, 'button[type="submit"]')
                
                # Wait for authentication to complete
                await self.page.wait_for_timeout(2000)
            
            # Check connection status
            connection_status = await wait_for_element(self.page, '.connection-status', timeout=10000)
            status_text = await get_element_text(self.page, '.connection-status')
            
            if 'connected' in status_text.lower():
                self.test_results.append(("Authentication & Connection", "PASS", "Successfully connected to backend"))
            else:
                self.test_results.append(("Authentication & Connection", "FAIL", f"Connection status: {status_text}"))
                
        except Exception as e:
            self.test_results.append(("Authentication & Connection", "ERROR", str(e)))
            
    async def test_user_profile_modal(self):
        """Test user profile modal functionality"""
        log_test_step("Testing user profile modal")
        
        try:
            # Click user profile button
            await click_element(self.page, '.user-icon-btn')
            
            # Wait for profile modal to appear
            profile_modal = await wait_for_element(self.page, '.modal', timeout=5000)
            
            # Check if basic information is displayed outside accordion
            basic_info = await wait_for_element(self.page, '.profile-static-section', timeout=3000)
            if basic_info:
                # Check for name and description
                name_element = await self.page.query_selector('.profile-static-section .profile-value')
                name_text = await name_element.inner_text() if name_element else "Not found"
                
                if name_text and name_text != "Not found":
                    self.test_results.append(("User Profile Modal - Basic Info", "PASS", f"Name displayed: {name_text}"))
                else:
                    self.test_results.append(("User Profile Modal - Basic Info", "FAIL", "Name not displayed properly"))
            
            # Check demographics section
            demographics = await self.page.query_selector_all('.profile-static-section')
            if len(demographics) >= 2:
                self.test_results.append(("User Profile Modal - Demographics", "PASS", "Demographics section present"))
            else:
                self.test_results.append(("User Profile Modal - Demographics", "FAIL", "Demographics section missing"))
            
            # Check that Preferences section is removed
            preferences_section = await self.page.query_selector('text=Preferences')
            if not preferences_section:
                self.test_results.append(("User Profile Modal - Preferences Removed", "PASS", "Preferences section successfully removed"))
            else:
                self.test_results.append(("User Profile Modal - Preferences Removed", "FAIL", "Preferences section still present"))
            
            # Close modal
            await click_element(self.page, '.modal-close')
            await self.page.wait_for_timeout(1000)
            
        except Exception as e:
            self.test_results.append(("User Profile Modal", "ERROR", str(e)))
            
    async def test_wheel_generation(self):
        """Test wheel generation functionality"""
        log_test_step("Testing wheel generation")
        
        try:
            # Set energy level and time available
            energy_slider = await self.page.query_selector('input[type="range"]')
            if energy_slider:
                await energy_slider.fill('75')  # Set energy to 75%
            
            # Click generate button
            generate_button = await wait_for_element(self.page, '.generate-button', timeout=5000)
            await generate_button.click()
            
            # Wait for wheel to be generated (up to 100 seconds as mentioned in requirements)
            wheel_component = await wait_for_element(self.page, 'game-wheel', timeout=120000)
            
            if wheel_component:
                self.test_results.append(("Wheel Generation", "PASS", "Wheel generated successfully"))
                
                # Check if wheel has activities
                await self.page.wait_for_timeout(3000)  # Wait for wheel to fully load
                activities = await self.page.query_selector_all('.activity-item')
                
                if len(activities) >= 2:  # Should have minimum 2 activities as per requirements
                    self.test_results.append(("Wheel Activities", "PASS", f"Found {len(activities)} activities"))
                else:
                    self.test_results.append(("Wheel Activities", "FAIL", f"Only {len(activities)} activities found, expected minimum 2"))
            else:
                self.test_results.append(("Wheel Generation", "FAIL", "Wheel not generated within timeout"))
                
        except Exception as e:
            self.test_results.append(("Wheel Generation", "ERROR", str(e)))
            
    async def test_activity_modal_functionality(self):
        """Test activity modal catalog loading and auto-tailoring"""
        log_test_step("Testing activity modal functionality")
        
        try:
            # Click on first activity change button
            change_button = await wait_for_element(self.page, '.activity-change-btn', timeout=5000)
            await change_button.click()
            
            # Wait for activity modal
            activity_modal = await wait_for_element(self.page, '.modal', timeout=5000)
            
            # Check if catalog is loaded
            catalog_items = await self.page.query_selector_all('.catalog-item')
            
            if len(catalog_items) > 0:
                self.test_results.append(("Activity Catalog Loading", "PASS", f"Found {len(catalog_items)} catalog items"))
                
                # Check for tailored and generic activities
                tailored_items = await self.page.query_selector_all('.catalog-item.tailored')
                generic_items = await self.page.query_selector_all('.catalog-item.generic')
                
                self.test_results.append(("Activity Types", "INFO", f"Tailored: {len(tailored_items)}, Generic: {len(generic_items)}"))
                
                # Test auto-tailoring by clicking a generic activity
                if len(generic_items) > 0:
                    log_test_step("Testing auto-tailoring functionality")
                    await generic_items[0].click()
                    
                    # Check for loading spinner
                    loading_state = await self.page.query_selector('.loading-state')
                    if loading_state:
                        self.test_results.append(("Auto-tailoring Spinner", "PASS", "Loading spinner displayed during tailoring"))
                    
                    # Wait for tailoring to complete
                    await self.page.wait_for_timeout(5000)
                    
            else:
                self.test_results.append(("Activity Catalog Loading", "FAIL", "No catalog items found"))
            
            # Close modal
            close_button = await self.page.query_selector('.modal-close')
            if close_button:
                await close_button.click()
                
        except Exception as e:
            self.test_results.append(("Activity Modal", "ERROR", str(e)))
            
    async def test_wheel_spinning_and_winning_modal(self):
        """Test wheel spinning and winning modal display"""
        log_test_step("Testing wheel spinning and winning modal")
        
        try:
            # Click spin button
            spin_button = await wait_for_element(self.page, '.spin-button', timeout=5000)
            await spin_button.click()
            
            # Wait for spin to complete and winning modal to appear (with 2-second delay)
            winning_modal = await wait_for_element(self.page, '.winning-modal-content', timeout=15000)
            
            if winning_modal:
                self.test_results.append(("Wheel Spinning", "PASS", "Wheel spun and winning modal appeared"))
                
                # Check winning modal content
                activity_name = await self.page.query_selector('.activity-name')
                activity_description = await self.page.query_selector('.activity-description')
                activity_details = await self.page.query_selector('.activity-details')
                
                if activity_name and activity_description and activity_details:
                    name_text = await activity_name.inner_text()
                    desc_text = await activity_description.inner_text()
                    
                    if name_text and desc_text and "No description available" not in desc_text:
                        self.test_results.append(("Winning Modal Content", "PASS", f"Complete activity info displayed: {name_text}"))
                    else:
                        self.test_results.append(("Winning Modal Content", "FAIL", "Incomplete or missing activity information"))
                else:
                    self.test_results.append(("Winning Modal Content", "FAIL", "Missing activity information elements"))
                
                # Close winning modal
                close_button = await self.page.query_selector('.modal-close')
                if close_button:
                    await close_button.click()
                    
            else:
                self.test_results.append(("Wheel Spinning", "FAIL", "Winning modal did not appear"))
                
        except Exception as e:
            self.test_results.append(("Wheel Spinning", "ERROR", str(e)))
            
    async def test_logout_functionality(self):
        """Test logout functionality"""
        log_test_step("Testing logout functionality")
        
        try:
            # Click logout button
            logout_button = await wait_for_element(self.page, '.logout-btn', timeout=5000)
            await logout_button.click()
            
            # Wait for logout to complete
            await self.page.wait_for_timeout(2000)
            
            # Check if login form appears (indicating successful logout)
            login_form = await self.page.query_selector('login-form')
            
            if login_form:
                self.test_results.append(("Logout Functionality", "PASS", "Successfully logged out"))
            else:
                self.test_results.append(("Logout Functionality", "FAIL", "Still appears to be logged in"))
                
        except Exception as e:
            self.test_results.append(("Logout Functionality", "ERROR", str(e)))
            
    async def run_all_tests(self):
        """Run all tests in sequence"""
        log_test_step("Starting comprehensive UX debugging flow test")
        
        try:
            await self.setup()
            await self.test_authentication_flow()
            await self.test_user_profile_modal()
            await self.test_wheel_generation()
            await self.test_activity_modal_functionality()
            await self.test_wheel_spinning_and_winning_modal()
            await self.test_logout_functionality()
            
        except Exception as e:
            log_test_result("CRITICAL ERROR", str(e))
            
        finally:
            if self.browser:
                await self.browser.close()
                
        # Print test results
        self.print_test_results()
        
    def print_test_results(self):
        """Print comprehensive test results"""
        print("\n" + "="*80)
        print("UX DEBUGGING FLOW TEST RESULTS")
        print("="*80)
        
        passed = 0
        failed = 0
        errors = 0
        
        for test_name, status, message in self.test_results:
            status_symbol = {
                "PASS": "✅",
                "FAIL": "❌", 
                "ERROR": "💥",
                "INFO": "ℹ️"
            }.get(status, "❓")
            
            print(f"{status_symbol} {test_name}: {message}")
            
            if status == "PASS":
                passed += 1
            elif status == "FAIL":
                failed += 1
            elif status == "ERROR":
                errors += 1
                
        print("\n" + "-"*80)
        print(f"SUMMARY: {passed} passed, {failed} failed, {errors} errors")
        print("-"*80)
        
        # Overall assessment
        if errors > 0:
            print("🚨 CRITICAL ISSUES DETECTED - System needs immediate attention")
        elif failed > 0:
            print("⚠️  SOME TESTS FAILED - UX improvements needed")
        else:
            print("🎉 ALL TESTS PASSED - UX flow is working correctly!")

async def main():
    """Main test execution"""
    port = int(sys.argv[1]) if len(sys.argv) > 1 else 5173
    
    test_runner = UXDebuggingFlowTest(port)
    await test_runner.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
