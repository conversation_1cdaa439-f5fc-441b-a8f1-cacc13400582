/**
 * Test Template Logic Fix
 * Comprehensive test to verify the template logic fix for wheel item removal
 */

const puppeteer = require('puppeteer');

class TemplateLogicTester {
    constructor() {
        this.browser = null;
        this.page = null;
        this.testResults = {
            normalRemoval: false,
            emptySegmentsHandling: false,
            noBackgroundWheelFallback: false,
            properStateTransitions: false,
            overallSuccess: false
        };
    }

    async run(port = 3001) {
        console.log('🔍 ===== TEMPLATE LOGIC FIX VALIDATION =====');
        console.log(`Testing on http://localhost:${port}`);
        
        try {
            await this.setupBrowser(port);
            await this.testNormalRemoval();
            await this.testEmptySegmentsHandling();
            await this.testNoBackgroundWheelFallback();
            await this.testProperStateTransitions();
            await this.generateReport();
        } catch (error) {
            console.error('❌ Test suite failed:', error);
        } finally {
            if (this.browser) {
                await this.browser.close();
            }
        }
    }

    async setupBrowser(port) {
        console.log('\n🚀 Setting up browser...');
        
        this.browser = await puppeteer.launch({ 
            headless: false,
            defaultViewport: { width: 1200, height: 800 }
        });
        
        this.page = await this.browser.newPage();
        
        // Navigate to the app
        await this.page.goto(`http://localhost:${port}`, { waitUntil: 'networkidle0' });
        
        // Wait for app to load
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        console.log('   ✅ Browser setup complete');
    }

    async testNormalRemoval() {
        console.log('🎯 Testing normal wheel item removal...');
        
        try {
            // Inject mock wheel data with 3 items
            await this.page.evaluate(() => {
                const appShell = document.querySelector('app-shell');
                if (appShell) {
                    const mockWheelData = {
                        segments: [
                            {
                                id: 'item_1',
                                text: 'Activity 1',
                                name: 'Activity 1',
                                percentage: 33.33,
                                color: '#FF6B6B',
                                wheel_item_id: 'item_1'
                            },
                            {
                                id: 'item_2',
                                text: 'Activity 2',
                                name: 'Activity 2',
                                percentage: 33.33,
                                color: '#4ECDC4',
                                wheel_item_id: 'item_2'
                            },
                            {
                                id: 'item_3',
                                text: 'Activity 3',
                                name: 'Activity 3',
                                percentage: 33.34,
                                color: '#45B7D1',
                                wheel_item_id: 'item_3'
                            }
                        ],
                        wheelId: 'test-wheel-1'
                    };
                    
                    appShell.wheelData = mockWheelData;
                    return true;
                }
                return false;
            });
            
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Mock successful removal API
            await this.page.evaluate(() => {
                window.fetch = function(url, options) {
                    if (url.includes('/api/wheel-items/') && options?.method === 'DELETE') {
                        return Promise.resolve({
                            ok: true,
                            json: () => Promise.resolve({
                                success: true,
                                wheel_data: {
                                    segments: [
                                        {
                                            id: 'item_2',
                                            name: 'Activity 2',
                                            percentage: 50,
                                            color: '#4ECDC4'
                                        },
                                        {
                                            id: 'item_3',
                                            name: 'Activity 3',
                                            percentage: 50,
                                            color: '#45B7D1'
                                        }
                                    ],
                                    wheel_id: 'test-wheel-1'
                                }
                            })
                        });
                    }
                    return Promise.reject(new Error('Not mocked'));
                };
            });
            
            // Trigger removal
            await this.page.evaluate(() => {
                const appShell = document.querySelector('app-shell');
                if (appShell && appShell.removeWheelItem) {
                    appShell.removeWheelItem('item_1');
                }
            });
            
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // Check result
            const result = await this.page.evaluate(() => {
                const appShell = document.querySelector('app-shell');
                if (appShell && appShell.shadowRoot) {
                    const wheel = appShell.shadowRoot.querySelector('game-wheel');
                    return {
                        hasWheelData: !!(appShell.wheelData && appShell.wheelData.segments && appShell.wheelData.segments.length > 0),
                        segmentCount: appShell.wheelData ? appShell.wheelData.segments.length : 0,
                        isUnpopulated: wheel ? wheel.classList.contains('wheel-unpopulated') : false
                    };
                }
                return { hasWheelData: false, segmentCount: 0, isUnpopulated: true };
            });
            
            if (result.hasWheelData && result.segmentCount === 2 && !result.isUnpopulated) {
                console.log('   ✅ Normal removal works correctly');
                this.testResults.normalRemoval = true;
            } else {
                console.log(`   ❌ Normal removal failed: hasData=${result.hasWheelData}, count=${result.segmentCount}, unpopulated=${result.isUnpopulated}`);
            }
            
        } catch (error) {
            console.log(`   ❌ Normal removal test failed: ${error.message}`);
        }
    }

    async testEmptySegmentsHandling() {
        console.log('🎯 Testing empty segments handling...');
        
        try {
            // Mock API to return empty segments
            await this.page.evaluate(() => {
                window.fetch = function(url, options) {
                    if (url.includes('/api/wheel-items/') && options?.method === 'DELETE') {
                        return Promise.resolve({
                            ok: true,
                            json: () => Promise.resolve({
                                success: true,
                                wheel_data: {
                                    segments: [], // Empty segments!
                                    wheel_id: 'test-wheel-1'
                                }
                            })
                        });
                    }
                    return Promise.reject(new Error('Not mocked'));
                };
            });
            
            // Trigger removal
            await this.page.evaluate(() => {
                const appShell = document.querySelector('app-shell');
                if (appShell && appShell.removeWheelItem) {
                    appShell.removeWheelItem('item_2');
                }
            });
            
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // Check result - should show unpopulated wheel
            const result = await this.page.evaluate(() => {
                const appShell = document.querySelector('app-shell');
                if (appShell && appShell.shadowRoot) {
                    const wheel = appShell.shadowRoot.querySelector('game-wheel');
                    return {
                        wheelDataExists: !!appShell.wheelData,
                        segmentCount: appShell.wheelData ? appShell.wheelData.segments.length : 0,
                        isUnpopulated: wheel ? wheel.classList.contains('wheel-unpopulated') : false,
                        templateCondition: !!(appShell.wheelData && appShell.wheelData.segments && appShell.wheelData.segments.length > 0)
                    };
                }
                return { wheelDataExists: false, segmentCount: 0, isUnpopulated: true, templateCondition: false };
            });
            
            if (result.wheelDataExists && result.segmentCount === 0 && result.isUnpopulated && !result.templateCondition) {
                console.log('   ✅ Empty segments correctly trigger unpopulated state');
                this.testResults.emptySegmentsHandling = true;
            } else {
                console.log(`   ❌ Empty segments handling failed: exists=${result.wheelDataExists}, count=${result.segmentCount}, unpopulated=${result.isUnpopulated}, condition=${result.templateCondition}`);
            }
            
        } catch (error) {
            console.log(`   ❌ Empty segments test failed: ${error.message}`);
        }
    }

    async testNoBackgroundWheelFallback() {
        console.log('🎯 Testing no background wheel fallback...');
        
        try {
            // Check that the unpopulated wheel shows background data, not corrupted data
            const result = await this.page.evaluate(() => {
                const appShell = document.querySelector('app-shell');
                if (appShell && appShell.shadowRoot) {
                    const wheel = appShell.shadowRoot.querySelector('game-wheel');
                    
                    // Get the wheel data that's actually being passed to the wheel component
                    const wheelData = wheel ? wheel.wheelData : null;
                    
                    return {
                        isUnpopulated: wheel ? wheel.classList.contains('wheel-unpopulated') : false,
                        wheelComponentData: wheelData ? {
                            segmentCount: wheelData.segments ? wheelData.segments.length : 0,
                            hasBackgroundData: wheelData.segments && wheelData.segments.some(s => 
                                s.text && (s.text.includes('Exercise') || s.text.includes('Learning') || s.text.includes('Creative'))
                            )
                        } : null
                    };
                }
                return { isUnpopulated: false, wheelComponentData: null };
            });
            
            if (result.isUnpopulated && result.wheelComponentData && result.wheelComponentData.hasBackgroundData) {
                console.log('   ✅ Unpopulated wheel correctly shows background data');
                this.testResults.noBackgroundWheelFallback = true;
            } else {
                console.log(`   ❌ Background wheel fallback test failed: unpopulated=${result.isUnpopulated}, data=${JSON.stringify(result.wheelComponentData)}`);
            }
            
        } catch (error) {
            console.log(`   ❌ Background wheel fallback test failed: ${error.message}`);
        }
    }

    async testProperStateTransitions() {
        console.log('🎯 Testing proper state transitions...');
        
        try {
            // Test transition from unpopulated back to populated
            await this.page.evaluate(() => {
                const appShell = document.querySelector('app-shell');
                if (appShell) {
                    const newWheelData = {
                        segments: [
                            {
                                id: 'new_item_1',
                                text: 'New Activity 1',
                                name: 'New Activity 1',
                                percentage: 100,
                                color: '#FF6B6B',
                                wheel_item_id: 'new_item_1'
                            }
                        ],
                        wheelId: 'new-wheel'
                    };
                    
                    appShell.wheelData = newWheelData;
                }
            });
            
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Check that it's now populated
            const result = await this.page.evaluate(() => {
                const appShell = document.querySelector('app-shell');
                if (appShell && appShell.shadowRoot) {
                    const wheel = appShell.shadowRoot.querySelector('game-wheel');
                    return {
                        hasWheelData: !!(appShell.wheelData && appShell.wheelData.segments && appShell.wheelData.segments.length > 0),
                        segmentCount: appShell.wheelData ? appShell.wheelData.segments.length : 0,
                        isUnpopulated: wheel ? wheel.classList.contains('wheel-unpopulated') : false
                    };
                }
                return { hasWheelData: false, segmentCount: 0, isUnpopulated: true };
            });
            
            if (result.hasWheelData && result.segmentCount === 1 && !result.isUnpopulated) {
                console.log('   ✅ State transition from unpopulated to populated works correctly');
                this.testResults.properStateTransitions = true;
            } else {
                console.log(`   ❌ State transition failed: hasData=${result.hasWheelData}, count=${result.segmentCount}, unpopulated=${result.isUnpopulated}`);
            }
            
        } catch (error) {
            console.log(`   ❌ State transitions test failed: ${error.message}`);
        }
    }

    async generateReport() {
        console.log('\n📋 ===== TEMPLATE LOGIC FIX RESULTS =====');
        
        const passedTests = Object.values(this.testResults).filter(result => result === true).length;
        const totalTests = Object.keys(this.testResults).length - 1; // Exclude overallSuccess
        
        this.testResults.overallSuccess = passedTests >= totalTests * 0.8; // 80% pass rate
        
        console.log(`✅ Passed: ${passedTests}/${totalTests} tests`);
        console.log();
        
        Object.entries(this.testResults).forEach(([test, result]) => {
            if (test !== 'overallSuccess') {
                const status = result ? '✅' : '❌';
                const testName = test.replace(/([A-Z])/g, ' $1').toLowerCase();
                console.log(`${status} ${testName}: ${result ? 'PASS' : 'FAIL'}`);
            }
        });
        
        console.log();
        if (this.testResults.overallSuccess) {
            console.log('🎉 TEMPLATE LOGIC FIX: SUCCESS');
            console.log('   The template logic fix has resolved the wheel item removal issue');
            console.log('   No more grey wheel fallback after item removal');
            console.log('   Proper handling of empty segments and state transitions');
        } else {
            console.log('⚠️ TEMPLATE LOGIC FIX: NEEDS ATTENTION');
            console.log('   Some tests failed - template logic issue may still exist');
            console.log('   Review failed tests and check template condition implementation');
        }
        
        console.log();
        console.log('🔧 Root Cause Analysis:');
        console.log('   The issue was in the template condition: ${this.wheelData ? ... : ...}');
        console.log('   This only checked if wheelData was truthy, not if it had valid segments');
        console.log('   Fixed with: ${this.wheelData && this.wheelData.segments && this.wheelData.segments.length > 0 ? ... : ...}');
        console.log('   Now empty segments correctly trigger the unpopulated state');
    }
}

// Run the test
if (require.main === module) {
    const port = process.argv[2] || 3001;
    const tester = new TemplateLogicTester();
    tester.run(port).then(() => {
        process.exit(tester.testResults.overallSuccess ? 0 : 1);
    });
}

module.exports = { TemplateLogicTester };
