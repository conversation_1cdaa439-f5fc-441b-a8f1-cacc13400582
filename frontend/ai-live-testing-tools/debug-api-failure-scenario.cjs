/**
 * Debug API Failure Scenario
 * Test what happens when the removeWheelItem API call fails
 */

const puppeteer = require('puppeteer');

async function debugAPIFailureScenario() {
  console.log('🔍 Debugging API failure scenario...');
  
  const browser = await puppeteer.launch({ 
    headless: false,
    defaultViewport: { width: 1200, height: 800 }
  });
  
  try {
    const page = await browser.newPage();
    
    // Enable console logging
    page.on('console', msg => {
      console.log('🔍 BROWSER:', msg.text());
    });
    
    // Navigate to the app
    console.log('📱 Navigating to http://localhost:3001...');
    await page.goto('http://localhost:3001', { waitUntil: 'networkidle0' });
    
    // Wait for app to load
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('\n🎯 Step 1: Inject mock wheel data and set up monitoring...');
    
    // Inject mock wheel data and set up monitoring
    await page.evaluate(() => {
      const appShell = document.querySelector('app-shell');
      if (appShell) {
        // Create mock wheel data
        const mockWheelData = {
          segments: [
            {
              id: 'item_mock_1',
              text: 'Mock Activity 1',
              name: 'Mock Activity 1',
              description: 'First mock activity for testing',
              percentage: 50,
              color: '#FF6B6B',
              activity_tailored_id: 'activity_1',
              domain: 'physical',
              base_challenge_rating: 50,
              wheel_item_id: 'item_mock_1'
            },
            {
              id: 'item_mock_2',
              text: 'Mock Activity 2',
              name: 'Mock Activity 2',
              description: 'Second mock activity for testing',
              percentage: 50,
              color: '#4ECDC4',
              activity_tailored_id: 'activity_2',
              domain: 'mental',
              base_challenge_rating: 40,
              wheel_item_id: 'item_mock_2'
            }
          ],
          wheelId: 'mock-wheel-123',
          createdAt: new Date().toISOString()
        };
        
        console.log('🎯 Setting up wheelData monitoring...');
        
        // Store original wheelData property
        let _wheelData = null;
        
        // Create a property descriptor that logs all changes
        Object.defineProperty(appShell, 'wheelData', {
          get() {
            return _wheelData;
          },
          set(value) {
            console.log('🔄 WHEEL DATA CHANGE DETECTED!');
            console.log('🔄 Previous value:', _wheelData ? `${_wheelData.segments?.length || 0} segments` : 'null/undefined');
            console.log('🔄 New value:', value ? `${value.segments?.length || 0} segments` : 'null/undefined');
            console.log('🔄 New value type:', typeof value);
            console.log('🔄 New value truthy:', !!value);
            
            if (value === null) {
              console.log('🚨 WHEEL DATA SET TO NULL!');
              console.log('🚨 Stack trace:', new Error().stack);
            }
            
            if (value === undefined) {
              console.log('🚨 WHEEL DATA SET TO UNDEFINED!');
              console.log('🚨 Stack trace:', new Error().stack);
            }
            
            _wheelData = value;
            
            // Trigger re-render
            appShell.requestUpdate();
          },
          enumerable: true,
          configurable: true
        });
        
        // Set initial data
        console.log('🎯 Setting initial mock wheel data...');
        appShell.wheelData = mockWheelData;
        
        return { success: true };
      }
      return { success: false };
    });
    
    // Wait for re-render
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    console.log('\n🎯 Step 2: Test Scenario 1 - API returns 404...');
    
    // Mock fetch to return 404
    await page.evaluate(() => {
      const originalFetch = window.fetch;
      window.fetch = function(url, options) {
        console.log('🌐 FETCH INTERCEPTED:', url, options?.method);
        
        if (url.includes('/api/wheel-items/') && options?.method === 'DELETE') {
          console.log('🗑️ SIMULATING 404 ERROR');
          return Promise.resolve({
            ok: false,
            status: 404,
            json: () => Promise.resolve({ error: 'Wheel item not found' })
          });
        }
        
        return originalFetch.apply(this, arguments);
      };
    });
    
    // Trigger removal
    await page.evaluate(() => {
      const appShell = document.querySelector('app-shell');
      if (appShell && appShell.removeWheelItem) {
        console.log('🗑️ Calling removeWheelItem with 404 scenario...');
        appShell.removeWheelItem('item_mock_1');
      }
    });
    
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('\n🎯 Step 3: Test Scenario 2 - API returns success but no wheel_data...');
    
    // Mock fetch to return success but no wheel_data
    await page.evaluate(() => {
      const originalFetch = window.fetch;
      window.fetch = function(url, options) {
        console.log('🌐 FETCH INTERCEPTED:', url, options?.method);
        
        if (url.includes('/api/wheel-items/') && options?.method === 'DELETE') {
          console.log('🗑️ SIMULATING SUCCESS BUT NO WHEEL_DATA');
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({
              success: true,
              message: 'Item removed'
              // No wheel_data field
            })
          });
        }
        
        return originalFetch.apply(this, arguments);
      };
    });
    
    // Trigger removal
    await page.evaluate(() => {
      const appShell = document.querySelector('app-shell');
      if (appShell && appShell.removeWheelItem) {
        console.log('🗑️ Calling removeWheelItem with no wheel_data scenario...');
        appShell.removeWheelItem('item_mock_1');
      }
    });
    
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('\n🎯 Step 4: Test Scenario 3 - API throws exception...');
    
    // Mock fetch to throw exception
    await page.evaluate(() => {
      const originalFetch = window.fetch;
      window.fetch = function(url, options) {
        console.log('🌐 FETCH INTERCEPTED:', url, options?.method);
        
        if (url.includes('/api/wheel-items/') && options?.method === 'DELETE') {
          console.log('🗑️ SIMULATING NETWORK ERROR');
          return Promise.reject(new Error('Network error'));
        }
        
        return originalFetch.apply(this, arguments);
      };
    });
    
    // Trigger removal
    await page.evaluate(() => {
      const appShell = document.querySelector('app-shell');
      if (appShell && appShell.removeWheelItem) {
        console.log('🗑️ Calling removeWheelItem with network error scenario...');
        appShell.removeWheelItem('item_mock_1');
      }
    });
    
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('\n🎯 Step 5: Test Scenario 4 - API returns empty segments...');
    
    // Mock fetch to return empty segments
    await page.evaluate(() => {
      const originalFetch = window.fetch;
      window.fetch = function(url, options) {
        console.log('🌐 FETCH INTERCEPTED:', url, options?.method);
        
        if (url.includes('/api/wheel-items/') && options?.method === 'DELETE') {
          console.log('🗑️ SIMULATING EMPTY SEGMENTS RESPONSE');
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve({
              success: true,
              wheel_data: {
                segments: [], // Empty segments!
                wheel_id: 'mock-wheel-123'
              }
            })
          });
        }
        
        return originalFetch.apply(this, arguments);
      };
    });
    
    // Trigger removal
    await page.evaluate(() => {
      const appShell = document.querySelector('app-shell');
      if (appShell && appShell.removeWheelItem) {
        console.log('🗑️ Calling removeWheelItem with empty segments scenario...');
        appShell.removeWheelItem('item_mock_1');
      }
    });
    
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('\n🎯 Step 6: Check final state...');
    
    const finalState = await page.evaluate(() => {
      const appShell = document.querySelector('app-shell');
      if (appShell && appShell.shadowRoot) {
        const wheel = appShell.shadowRoot.querySelector('game-wheel');
        return {
          hasWheelData: !!(appShell.wheelData && appShell.wheelData.segments),
          segmentCount: appShell.wheelData ? appShell.wheelData.segments.length : 0,
          wheelDataType: typeof appShell.wheelData,
          wheelDataTruthy: !!appShell.wheelData,
          isUnpopulated: wheel ? wheel.classList.contains('wheel-unpopulated') : false,
          wheelDataPreview: appShell.wheelData && appShell.wheelData.segments ? 
            appShell.wheelData.segments.map(s => ({ id: s.id, text: s.text || s.name })) : null
        };
      }
      return { error: 'No app-shell or shadow root' };
    });
    
    console.log('Final state:', JSON.stringify(finalState, null, 2));
    
    if (finalState.isUnpopulated) {
      console.log('🚨 ISSUE FOUND: Wheel became unpopulated!');
    } else {
      console.log('✅ Wheel remained populated through all error scenarios');
    }
    
    // Wait for user to see the browser
    console.log('\n⏳ Browser will stay open for 10 seconds for manual inspection...');
    await new Promise(resolve => setTimeout(resolve, 10000));
    
  } catch (error) {
    console.error('❌ Debug failed:', error);
  } finally {
    await browser.close();
  }
}

// Run the debug
debugAPIFailureScenario().catch(console.error);
