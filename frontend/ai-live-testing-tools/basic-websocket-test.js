#!/usr/bin/env node

/**
 * Basic WebSocket Test
 * 
 * Tests basic WebSocket connectivity to identify connection issues
 */

import WebSocket from 'ws';

const ENDPOINTS = {
  game: 'ws://localhost:8000/ws/game/',
  admin: 'ws://localhost:8000/ws/connection-monitor/'
};

async function testEndpoint(name, url) {
  console.log(`\n📋 Testing ${name} WebSocket`);
  console.log(`URL: ${url}`);
  console.log('-----------------------------------');

  return new Promise((resolve) => {
    const socket = new WebSocket(url);
    let connected = false;

    const timeout = setTimeout(() => {
      if (!connected) {
        console.log('❌ Connection timeout (10s)');
        socket.close();
        resolve({ success: false, error: 'timeout' });
      }
    }, 10000);

    socket.on('open', () => {
      console.log('✅ Connection successful');
      connected = true;
      clearTimeout(timeout);
      
      // Send a test message
      try {
        socket.send(JSON.stringify({ type: 'test', message: 'ping' }));
        console.log('📤 Test message sent');
      } catch (error) {
        console.log('❌ Failed to send test message:', error.message);
      }
      
      setTimeout(() => {
        socket.close();
        resolve({ success: true });
      }, 2000);
    });

    socket.on('message', (data) => {
      try {
        const message = JSON.parse(data);
        console.log('📨 Received:', message.type || 'unknown');
      } catch (error) {
        console.log('📨 Received raw data:', data.toString().substring(0, 100));
      }
    });

    socket.on('error', (error) => {
      console.log('❌ WebSocket error:', error.message);
      clearTimeout(timeout);
      resolve({ success: false, error: error.message });
    });

    socket.on('close', (code, reason) => {
      console.log(`🔌 Connection closed: ${code} ${reason || ''}`);
      if (!connected) {
        clearTimeout(timeout);
        resolve({ success: false, error: `closed with code ${code}` });
      }
    });
  });
}

async function runTests() {
  console.log('🔍 BASIC WEBSOCKET CONNECTIVITY TEST');
  console.log('====================================');

  const results = {};

  // Test game WebSocket
  results.game = await testEndpoint('Game', ENDPOINTS.game);

  // Test admin WebSocket
  results.admin = await testEndpoint('Admin Dashboard', ENDPOINTS.admin);

  // Generate report
  console.log('\n📊 TEST RESULTS');
  console.log('===============');
  
  Object.entries(results).forEach(([name, result]) => {
    const icon = result.success ? '✅' : '❌';
    const status = result.success ? 'SUCCESS' : `FAILED (${result.error})`;
    console.log(`${icon} ${name.toUpperCase()}: ${status}`);
  });

  const successCount = Object.values(results).filter(r => r.success).length;
  const totalCount = Object.keys(results).length;
  
  console.log(`\n📊 Overall: ${successCount}/${totalCount} endpoints working`);

  if (results.game.success && !results.admin.success) {
    console.log('\n🔍 DIAGNOSIS:');
    console.log('Game WebSocket works but Admin Dashboard fails.');
    console.log('This suggests an authentication or routing issue with the admin endpoint.');
    console.log('Check if the admin endpoint requires authentication or has different middleware.');
  } else if (!results.game.success && !results.admin.success) {
    console.log('\n🔍 DIAGNOSIS:');
    console.log('Both WebSocket endpoints are failing.');
    console.log('This suggests a general WebSocket configuration issue.');
    console.log('Check if the backend is properly configured for WebSocket connections.');
  } else if (results.game.success && results.admin.success) {
    console.log('\n🔍 DIAGNOSIS:');
    console.log('Both WebSocket endpoints are working correctly.');
    console.log('The issue might be in the message flow logic or dashboard handling.');
  }
}

runTests().then(() => {
  process.exit(0);
}).catch((error) => {
  console.error('❌ Test failed:', error);
  process.exit(1);
});
