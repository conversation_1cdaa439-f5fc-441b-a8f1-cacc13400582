const WebSocket = require('ws');

class WebSocketMessageMonitor {
    constructor() {
        this.ws = null;
        this.messages = [];
        this.isConnected = false;
    }

    async connect() {
        console.log('🔌 Connecting to WebSocket...');
        
        return new Promise((resolve, reject) => {
            this.ws = new WebSocket('ws://localhost:8000/ws/game/');
            
            this.ws.on('open', () => {
                console.log('✅ WebSocket connected');
                this.isConnected = true;
                resolve();
            });
            
            this.ws.on('message', (data) => {
                try {
                    const message = JSON.parse(data.toString());
                    this.messages.push({
                        timestamp: new Date().toISOString(),
                        message: message
                    });
                    
                    console.log(`📨 [${new Date().toISOString()}] Received message:`);
                    console.log(`   Type: ${message.type || 'unknown'}`);
                    
                    if (message.type === 'wheel_data') {
                        console.log('🎡 WHEEL DATA RECEIVED!');
                        console.log(`   Wheel name: ${message.wheel?.name || 'unknown'}`);
                        console.log(`   Items count: ${message.wheel?.items?.length || 0}`);
                        console.log(`   Activities count: ${message.wheel?.activities?.length || 0}`);
                        
                        if (message.wheel?.items?.length > 0) {
                            console.log('   Items:');
                            message.wheel.items.forEach((item, index) => {
                                console.log(`     ${index + 1}. ${item.name || item.title || 'Unnamed'} (${item.domain || 'no domain'})`);
                            });
                        }
                        
                        if (message.wheel?.activities?.length > 0) {
                            console.log('   Activities:');
                            message.wheel.activities.forEach((activity, index) => {
                                console.log(`     ${index + 1}. ${activity.name || activity.title || 'Unnamed'} (${activity.domain || 'no domain'})`);
                            });
                        }
                    } else if (message.type === 'chat_message') {
                        console.log(`   Content: ${message.content?.substring(0, 100)}...`);
                    } else if (message.type === 'workflow_status') {
                        console.log(`   Workflow: ${message.workflow_id} - Status: ${message.status}`);
                    } else if (message.type === 'debug_info') {
                        console.log(`   Debug: ${message.content?.message || 'No message'}`);
                    } else {
                        console.log(`   Data: ${JSON.stringify(message).substring(0, 200)}...`);
                    }
                    
                } catch (error) {
                    console.error('❌ Error parsing message:', error);
                    console.log('Raw message:', data.toString());
                }
            });
            
            this.ws.on('error', (error) => {
                console.error('❌ WebSocket error:', error);
                reject(error);
            });
            
            this.ws.on('close', () => {
                console.log('🔌 WebSocket connection closed');
                this.isConnected = false;
            });
        });
    }

    async sendMessage(message) {
        if (!this.isConnected) {
            console.error('❌ WebSocket not connected');
            return;
        }
        
        console.log(`📤 Sending message: ${JSON.stringify(message)}`);
        this.ws.send(JSON.stringify(message));
    }

    async sendWheelRequest() {
        await this.sendMessage({
            type: 'chat_message',
            content: {
                message: "hey, I'm feeling energetic and I have 2h free ahead ! give me a wheel !",
                user_profile_id: '2',
                timestamp: new Date().toISOString(),
                metadata: {
                    requested_workflow: 'wheel_generation'
                }
            }
        });
    }

    async monitorForDuration(durationMs = 90000) {
        console.log(`⏱️  Monitoring WebSocket messages for ${durationMs / 1000} seconds...`);

        await this.connect();

        // Send the wheel request
        setTimeout(() => {
            this.sendWheelRequest();
        }, 2000);

        // Monitor for the specified duration
        await new Promise(resolve => setTimeout(resolve, durationMs));

        this.disconnect();
        this.generateReport();
    }

    disconnect() {
        if (this.ws) {
            this.ws.close();
        }
    }

    generateReport() {
        console.log('\n📊 WEBSOCKET MESSAGE MONITORING REPORT');
        console.log('════════════════════════════════════════════════════════════');
        
        const messageTypes = {};
        let wheelDataFound = false;
        let wheelWithActivities = false;
        
        this.messages.forEach(({ message }) => {
            const type = message.type || 'unknown';
            messageTypes[type] = (messageTypes[type] || 0) + 1;
            
            if (type === 'wheel_data') {
                wheelDataFound = true;
                if (message.wheel?.items?.length > 0 || message.wheel?.activities?.length > 0) {
                    wheelWithActivities = true;
                }
            }
        });
        
        console.log(`📈 Total messages received: ${this.messages.length}`);
        console.log('\n📋 Message types:');
        Object.entries(messageTypes).forEach(([type, count]) => {
            console.log(`  ${type}: ${count}`);
        });
        
        console.log('\n🎡 Wheel Data Analysis:');
        console.log(`  Wheel data received: ${wheelDataFound ? '✅ YES' : '❌ NO'}`);
        console.log(`  Wheel has activities: ${wheelWithActivities ? '✅ YES' : '❌ NO'}`);
        
        if (wheelDataFound) {
            const wheelMessages = this.messages.filter(m => m.message.type === 'wheel_data');
            wheelMessages.forEach((msg, index) => {
                const wheel = msg.message.wheel;
                console.log(`\n🎡 Wheel Data #${index + 1}:`);
                console.log(`  Name: ${wheel?.name || 'unknown'}`);
                console.log(`  Items: ${wheel?.items?.length || 0}`);
                console.log(`  Activities: ${wheel?.activities?.length || 0}`);
                
                if (wheel?.items?.length > 0) {
                    console.log('  Item details:');
                    wheel.items.forEach((item, i) => {
                        console.log(`    ${i + 1}. ${item.name || item.title || 'Unnamed'}`);
                        console.log(`       Domain: ${item.domain || 'none'}`);
                        console.log(`       Color: ${item.color || 'none'}`);
                        console.log(`       Percentage: ${item.percentage || 'none'}`);
                    });
                }
            });
        }
        
        console.log('\n💡 Recommendations:');
        if (!wheelDataFound) {
            console.log('  ❌ No wheel data received - check workflow execution');
        } else if (!wheelWithActivities) {
            console.log('  ❌ Wheel data received but no activities - check data structure');
        } else {
            console.log('  ✅ Wheel data with activities received successfully');
        }
    }
}

// Run the monitor
async function main() {
    const monitor = new WebSocketMessageMonitor();
    
    try {
        await monitor.monitorForDuration(60000); // Monitor for 60 seconds
    } catch (error) {
        console.error('❌ Monitoring failed:', error);
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = WebSocketMessageMonitor;
