#!/usr/bin/env node

/**
 * Debug Frontend DOM - Check what's actually being rendered
 */

const { chromium } = require('playwright');

async function debugFrontendDOM() {
    console.log('🚀 Starting frontend DOM debug...');
    
    const browser = await chromium.launch({ headless: false });
    const page = await browser.newPage();
    
    // Load page
    console.log('🌐 Loading page...');
    await page.goto('http://localhost:3000/');
    await page.waitForTimeout(5000);
    
    // Take screenshot
    await page.screenshot({ path: 'frontend-debug.png', fullPage: true });
    console.log('📸 Screenshot saved as frontend-debug.png');
    
    // Analyze DOM structure
    const domAnalysis = await page.evaluate(() => {
        return {
            title: document.title,
            bodyClasses: document.body.className,
            bodyId: document.body.id,
            
            // Look for chat-related elements
            chatElements: Array.from(document.querySelectorAll('*')).filter(el => 
                el.className && typeof el.className === 'string' && 
                (el.className.includes('chat') || el.className.includes('message') || el.className.includes('input'))
            ).map(el => ({
                tagName: el.tagName,
                className: el.className,
                id: el.id,
                textContent: el.textContent.substring(0, 100)
            })),
            
            // Look for form elements
            formElements: Array.from(document.querySelectorAll('form, input, textarea, button')).map(el => ({
                tagName: el.tagName,
                type: el.type,
                className: el.className,
                id: el.id,
                disabled: el.disabled,
                placeholder: el.placeholder,
                value: el.value
            })),
            
            // Look for debug elements
            debugElements: Array.from(document.querySelectorAll('*')).filter(el => 
                el.className && typeof el.className === 'string' && 
                el.className.includes('debug')
            ).map(el => ({
                tagName: el.tagName,
                className: el.className,
                id: el.id,
                textContent: el.textContent.substring(0, 100)
            })),
            
            // Look for select elements (user selection)
            selectElements: Array.from(document.querySelectorAll('select')).map(el => ({
                tagName: el.tagName,
                className: el.className,
                id: el.id,
                options: Array.from(el.options).map(opt => ({
                    value: opt.value,
                    text: opt.text,
                    selected: opt.selected
                }))
            })),
            
            // Get all visible text
            visibleText: document.body.innerText.substring(0, 500),
            
            // Check for error messages
            errorElements: Array.from(document.querySelectorAll('.error, .alert, .warning, [class*="error"]')).map(el => ({
                tagName: el.tagName,
                className: el.className,
                textContent: el.textContent
            }))
        };
    });
    
    console.log('\n📊 FRONTEND DOM ANALYSIS:');
    console.log('════════════════════════════════════════════════════════════');
    
    console.log(`\n📄 Page Info:`);
    console.log(`  Title: ${domAnalysis.title}`);
    console.log(`  Body classes: ${domAnalysis.bodyClasses}`);
    console.log(`  Body ID: ${domAnalysis.bodyId}`);
    
    console.log(`\n💬 Chat Elements (${domAnalysis.chatElements.length}):`);
    domAnalysis.chatElements.forEach((el, i) => {
        console.log(`  ${i+1}. <${el.tagName}> class="${el.className}" id="${el.id}"`);
        if (el.textContent) console.log(`     Text: "${el.textContent}"`);
    });
    
    console.log(`\n📝 Form Elements (${domAnalysis.formElements.length}):`);
    domAnalysis.formElements.forEach((el, i) => {
        console.log(`  ${i+1}. <${el.tagName}> type="${el.type}" class="${el.className}" disabled=${el.disabled}`);
        if (el.placeholder) console.log(`     Placeholder: "${el.placeholder}"`);
        if (el.value) console.log(`     Value: "${el.value}"`);
    });
    
    console.log(`\n🐛 Debug Elements (${domAnalysis.debugElements.length}):`);
    domAnalysis.debugElements.forEach((el, i) => {
        console.log(`  ${i+1}. <${el.tagName}> class="${el.className}" id="${el.id}"`);
        if (el.textContent) console.log(`     Text: "${el.textContent}"`);
    });
    
    console.log(`\n📋 Select Elements (${domAnalysis.selectElements.length}):`);
    domAnalysis.selectElements.forEach((el, i) => {
        console.log(`  ${i+1}. <${el.tagName}> class="${el.className}" id="${el.id}"`);
        el.options.forEach((opt, j) => {
            console.log(`     Option ${j+1}: "${opt.text}" (value: ${opt.value}) ${opt.selected ? '[SELECTED]' : ''}`);
        });
    });
    
    console.log(`\n❌ Error Elements (${domAnalysis.errorElements.length}):`);
    domAnalysis.errorElements.forEach((el, i) => {
        console.log(`  ${i+1}. <${el.tagName}> class="${el.className}"`);
        console.log(`     Text: "${el.textContent}"`);
    });
    
    console.log(`\n📄 Visible Text (first 500 chars):`);
    console.log(`"${domAnalysis.visibleText}"`);
    
    console.log('\n🔍 DIAGNOSIS:');
    if (domAnalysis.formElements.length === 0) {
        console.log('❌ No form elements found - chat interface not loaded');
    } else if (domAnalysis.formElements.every(el => el.disabled)) {
        console.log('❌ All form elements disabled - chat interface not ready');
    } else if (domAnalysis.selectElements.length > 0 && !domAnalysis.selectElements.some(el => el.options.some(opt => opt.selected && opt.value !== ''))) {
        console.log('❌ User not selected in debug mode - chat disabled until user selection');
    } else if (domAnalysis.errorElements.length > 0) {
        console.log('❌ Error elements found - check error messages');
    } else {
        console.log('✅ Frontend appears to be loaded correctly');
    }
    
    await browser.close();
}

debugFrontendDOM().catch(console.error);
