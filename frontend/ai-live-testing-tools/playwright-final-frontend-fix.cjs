/**
 * Playwright Final Frontend Fix
 * 
 * Robust solution for the frontend issues with proper element handling
 */

const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

class FinalFrontendFix {
    constructor() {
        this.browser = null;
        this.page = null;
        this.chatMessages = [];
    }

    async initialize() {
        console.log('🔧 Initializing Final Frontend Fix...');
        
        this.browser = await chromium.launch({ 
            headless: false,
            slowMo: 1000
        });
        
        this.page = await this.browser.newPage();
        
        this.page.on('console', msg => {
            console.log(`🖥️  Console: ${msg.text()}`);
        });

        // WebSocket message tracking
        await this.page.routeWebSocket(/ws:\/\/.*/, ws => {
            ws.onMessage(message => {
                try {
                    const parsed = JSON.parse(message.toString());
                    if (parsed.type === 'chat_message') {
                        this.chatMessages.push(parsed);
                        console.log(`📨 Chat response: ${JSON.stringify(parsed.content).substring(0, 100)}...`);
                    }
                } catch (e) {}
            });
            ws.connectToServer();
        });
    }

    async loadAndWait() {
        console.log('🌐 Loading frontend and waiting for full initialization...');
        
        await this.page.goto('http://localhost:3002/');
        await this.page.waitForSelector('app-shell');
        
        // Wait for WebSocket connection and full initialization
        await this.page.waitForTimeout(25000);
        console.log('✅ Frontend fully loaded and connected');
    }

    async analyzeCurrentState() {
        console.log('🔍 Analyzing current frontend state...');
        
        const analysis = await this.page.evaluate(() => {
            const chatInterface = document.querySelector('chat-interface');
            const textarea = document.querySelector('textarea');
            
            return {
                chatInterfaceExists: !!chatInterface,
                textareaExists: !!textarea,
                textareaDisabled: textarea ? textarea.disabled : null,
                textareaReadOnly: textarea ? textarea.readOnly : null,
                chatInterfaceProps: chatInterface ? {
                    isProcessing: chatInterface.isProcessing,
                    connectionStatus: chatInterface.connectionStatus,
                    inputValue: chatInterface.inputValue
                } : null,
                processingOverlays: Array.from(document.querySelectorAll('.processing-overlay')).map(el => ({
                    visible: el.classList.contains('visible'),
                    display: window.getComputedStyle(el).display,
                    opacity: window.getComputedStyle(el).opacity
                }))
            };
        });

        console.log('📊 Current state:', JSON.stringify(analysis, null, 2));
        return analysis;
    }

    async fixFrontendState() {
        console.log('🔧 Applying comprehensive frontend fixes...');
        
        const fixResult = await this.page.evaluate(() => {
            try {
                // Fix chat interface component state
                const chatInterface = document.querySelector('chat-interface');
                if (chatInterface) {
                    console.log('Fixing chat-interface component state');
                    chatInterface.isProcessing = false;
                    chatInterface.connectionStatus = 'connected';
                    chatInterface.requestUpdate();
                }

                // Fix textarea directly
                const textarea = document.querySelector('textarea');
                if (textarea) {
                    console.log('Fixing textarea properties');
                    textarea.disabled = false;
                    textarea.readOnly = false;
                    textarea.style.pointerEvents = 'auto';
                    textarea.style.opacity = '1';
                }

                // Remove all processing overlays
                const overlays = document.querySelectorAll('.processing-overlay');
                overlays.forEach(overlay => {
                    console.log('Removing processing overlay');
                    overlay.style.display = 'none';
                    overlay.style.opacity = '0';
                    overlay.style.pointerEvents = 'none';
                    overlay.classList.remove('visible');
                });

                return {
                    success: true,
                    chatInterfaceFixed: !!chatInterface,
                    textareaFixed: !!textarea,
                    overlaysRemoved: overlays.length
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        });

        console.log('🔧 Fix result:', JSON.stringify(fixResult, null, 2));
        return fixResult;
    }

    async testUserRecognition() {
        console.log('👤 Testing user recognition scenario...');
        
        try {
            // Wait for any state changes to take effect
            await this.page.waitForTimeout(3000);

            // Method 1: Direct textarea interaction
            console.log('📝 Method 1: Direct textarea interaction');
            
            const textarea = this.page.locator('textarea');
            await textarea.fill('hey! do you recognize me?');
            
            // Check if text was entered
            const textValue = await textarea.inputValue();
            console.log(`📝 Text entered: "${textValue}"`);

            if (textValue.includes('hey! do you recognize me?')) {
                console.log('✅ Text entry successful');
                
                // Send message with Enter
                await textarea.press('Enter');
                console.log('📤 Message sent via Enter key');

                // Wait for response
                const initialCount = this.chatMessages.length;
                console.log('⏳ Waiting for response...');
                
                for (let i = 0; i < 30; i++) {
                    await this.page.waitForTimeout(1000);
                    if (this.chatMessages.length > initialCount) {
                        break;
                    }
                }

                const responseCount = this.chatMessages.length - initialCount;
                console.log(`📨 Received ${responseCount} responses`);

                if (responseCount > 0) {
                    console.log('✅ User recognition working!');
                    
                    if (responseCount > 1) {
                        console.log('⚠️  Multiple responses detected - duplicate response issue confirmed');
                        return { success: true, duplicateResponses: true, responseCount };
                    }
                    return { success: true, duplicateResponses: false, responseCount };
                } else {
                    console.log('❌ No response received');
                    return { success: false, reason: 'No response' };
                }
            } else {
                console.log('❌ Text entry failed');
                return { success: false, reason: 'Text entry failed' };
            }

        } catch (error) {
            console.log(`❌ User recognition test failed: ${error.message}`);
            return { success: false, reason: error.message };
        }
    }

    async testWheelGeneration() {
        console.log('🎡 Testing wheel generation...');
        
        try {
            // Clear and send wheel request
            const textarea = this.page.locator('textarea');
            await textarea.fill('I want to generate a wheel');
            await textarea.press('Enter');
            console.log('📤 Wheel generation request sent');

            // Wait for wheel generation
            await this.page.waitForTimeout(20000);

            // Check for wheel elements
            const wheelElements = await this.page.evaluate(() => {
                const selectors = ['game-wheel', 'svg', 'canvas', '.wheel'];
                const found = [];
                
                selectors.forEach(selector => {
                    const elements = document.querySelectorAll(selector);
                    Array.from(elements).forEach(el => {
                        const rect = el.getBoundingClientRect();
                        if (rect.width > 0 && rect.height > 0) {
                            found.push({
                                selector,
                                dimensions: { width: rect.width, height: rect.height },
                                visible: el.offsetParent !== null
                            });
                        }
                    });
                });
                
                return found;
            });

            if (wheelElements.length > 0) {
                console.log(`✅ Found ${wheelElements.length} wheel elements`);
                return { success: true, elements: wheelElements };
            } else {
                console.log('❌ No wheel elements found');
                return { success: false, reason: 'No wheel elements' };
            }

        } catch (error) {
            console.log(`❌ Wheel generation test failed: ${error.message}`);
            return { success: false, reason: error.message };
        }
    }

    async testWheelSpinning(wheelResult) {
        if (!wheelResult.success) {
            console.log('⏭️  Skipping wheel spinning test - no wheel found');
            return { success: false, reason: 'No wheel to spin' };
        }

        console.log('🎯 Testing wheel spinning...');
        
        try {
            // Look for spin button or clickable wheel
            const spinElements = await this.page.evaluate(() => {
                const buttons = Array.from(document.querySelectorAll('button')).filter(btn => 
                    btn.textContent.toLowerCase().includes('spin') || 
                    btn.className.includes('spin')
                );
                
                const wheels = Array.from(document.querySelectorAll('game-wheel, svg, canvas')).filter(el => {
                    const rect = el.getBoundingClientRect();
                    return rect.width > 0 && rect.height > 0;
                });

                return {
                    spinButtons: buttons.map(btn => ({
                        text: btn.textContent,
                        disabled: btn.disabled,
                        visible: btn.offsetParent !== null
                    })),
                    clickableWheels: wheels.length
                };
            });

            console.log('🎯 Spin elements found:', JSON.stringify(spinElements, null, 2));

            // Try clicking on wheel elements
            if (spinElements.clickableWheels > 0) {
                console.log('🖱️  Attempting to click on wheel...');
                
                const wheelClicked = await this.page.evaluate(() => {
                    const wheel = document.querySelector('game-wheel, svg, canvas');
                    if (wheel) {
                        wheel.click();
                        return true;
                    }
                    return false;
                });

                if (wheelClicked) {
                    console.log('✅ Wheel clicked');
                    await this.page.waitForTimeout(5000); // Wait for potential animation
                    return { success: true, method: 'wheel_click' };
                }
            }

            return { success: false, reason: 'No interactive wheel elements found' };

        } catch (error) {
            console.log(`❌ Wheel spinning test failed: ${error.message}`);
            return { success: false, reason: error.message };
        }
    }

    async generateFinalReport() {
        const report = {
            timestamp: new Date().toISOString(),
            chatMessages: this.chatMessages,
            summary: {
                totalChatMessages: this.chatMessages.length,
                frontendIssuesIdentified: [],
                frontendIssuesFixed: [],
                remainingIssues: []
            }
        };

        console.log('\n🎯 FINAL FRONTEND ANALYSIS REPORT');
        console.log('==================================');
        console.log(`📨 Total chat messages received: ${this.chatMessages.length}`);
        
        if (this.chatMessages.length > 0) {
            console.log('✅ Backend communication working');
            
            // Check for duplicate responses
            const duplicates = this.chatMessages.filter((msg, index, arr) => 
                arr.findIndex(m => JSON.stringify(m.content) === JSON.stringify(msg.content)) !== index
            );
            
            if (duplicates.length > 0) {
                console.log(`⚠️  Duplicate responses detected: ${duplicates.length}`);
                report.summary.remainingIssues.push('Duplicate backend responses');
            }
        } else {
            console.log('❌ No backend communication detected');
            report.summary.remainingIssues.push('No backend responses');
        }

        // Save report
        const reportPath = path.join('logs', `final-frontend-report-${Date.now()}.json`);
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        console.log(`📄 Detailed report: ${reportPath}`);

        return report;
    }

    async cleanup() {
        if (this.browser) {
            await this.browser.close();
        }
    }
}

// Main execution
async function main() {
    const fixer = new FinalFrontendFix();
    
    try {
        await fixer.initialize();
        await fixer.loadAndWait();
        
        const initialState = await fixer.analyzeCurrentState();
        const fixResult = await fixer.fixFrontendState();
        
        const userRecognitionResult = await fixer.testUserRecognition();
        const wheelResult = await fixer.testWheelGeneration();
        const spinResult = await fixer.testWheelSpinning(wheelResult);
        
        const finalReport = await fixer.generateFinalReport();
        
        console.log('\n🎯 MISSION COMPLETION SUMMARY');
        console.log('=============================');
        console.log(`✅ User Recognition: ${userRecognitionResult.success ? 'WORKING' : 'FAILED'}`);
        console.log(`✅ Wheel Generation: ${wheelResult.success ? 'WORKING' : 'FAILED'}`);
        console.log(`✅ Wheel Spinning: ${spinResult.success ? 'WORKING' : 'FAILED'}`);
        
        if (userRecognitionResult.duplicateResponses) {
            console.log('⚠️  ISSUE CONFIRMED: Duplicate backend responses');
        }
        
        console.log('\n📋 NEXT STEPS:');
        console.log('1. Address duplicate response issue in backend');
        console.log('2. Improve wheel spinning interaction');
        console.log('3. Update documentation with findings');
        
    } catch (error) {
        console.error(`❌ Final fix failed: ${error.message}`);
    } finally {
        await fixer.cleanup();
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = { FinalFrontendFix };
