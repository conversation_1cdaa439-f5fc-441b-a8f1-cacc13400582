#!/usr/bin/env python3

"""
Backend Django Fix Script

This script addresses the critical Django initialization issue:
"AppRegistryNotReady: Apps aren't loaded yet."

The issue occurs when tools are executed outside the main Django context,
causing all agent tools to fail and resulting in slow responses and errors.
"""

import os
import sys
import django
from django.conf import settings
from django.core.management import execute_from_command_line

def setup_django():
    """
    Properly initialize Django apps to fix the AppRegistryNotReady error.
    """
    print("🔧 Setting up Django environment...")
    
    # Set the Django settings module
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
    
    # Configure Django
    if not settings.configured:
        django.setup()
        print("✅ Django apps loaded successfully")
    else:
        print("✅ Django already configured")

def test_django_initialization():
    """
    Test that Django is properly initialized by importing models.
    """
    print("🧪 Testing Django initialization...")
    
    try:
        from apps.main.models import Use<PERSON><PERSON><PERSON><PERSON>le, LLMConfig, AgentTool
        print("✅ Successfully imported models")
        
        # Test database connection
        user_count = UserProfile.objects.count()
        print(f"✅ Database connection working - {user_count} users found")
        
        # Test tool loading
        tool_count = AgentTool.objects.count()
        print(f"✅ Tool loading working - {tool_count} tools found")
        
        return True
        
    except Exception as e:
        print(f"❌ Django initialization test failed: {e}")
        return False

def test_conversation_dispatcher():
    """
    Test the ConversationDispatcher with proper Django setup.
    """
    print("🧪 Testing ConversationDispatcher...")
    
    try:
        from apps.main.services.conversation_dispatcher import ConversationDispatcher
        
        # Initialize dispatcher
        dispatcher = ConversationDispatcher(user_profile_id='2')
        print("✅ ConversationDispatcher initialized successfully")
        
        # Test message processing (this should not fail with Django apps loaded)
        print("📝 Testing message processing...")
        
        # Note: This is a sync test, so we'll test the initialization only
        print("✅ ConversationDispatcher ready for message processing")
        
        return True
        
    except Exception as e:
        print(f"❌ ConversationDispatcher test failed: {e}")
        return False

def test_agent_tools():
    """
    Test that agent tools can be loaded and executed.
    """
    print("🧪 Testing agent tools...")
    
    try:
        from apps.main.agents.tools.tools_util import execute_tool
        
        print("✅ Tool utilities imported successfully")
        
        # Test tool loading (without execution to avoid async issues)
        from apps.main.models import AgentTool
        
        tools = AgentTool.objects.all()[:3]
        for tool in tools:
            print(f"✅ Tool available: {tool.code}")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent tools test failed: {e}")
        return False

def apply_django_fix():
    """
    Apply the Django initialization fix to the backend.
    """
    print("\n🔧 APPLYING DJANGO INITIALIZATION FIX")
    print("════════════════════════════════════════════════════════════")
    
    # Step 1: Setup Django
    setup_django()
    
    # Step 2: Test initialization
    django_ok = test_django_initialization()
    
    # Step 3: Test ConversationDispatcher
    dispatcher_ok = test_conversation_dispatcher()
    
    # Step 4: Test agent tools
    tools_ok = test_agent_tools()
    
    # Summary
    print("\n📊 DJANGO FIX RESULTS")
    print("════════════════════════════════════════════════════════════")
    print(f"Django Initialization: {'✅ WORKING' if django_ok else '❌ FAILED'}")
    print(f"ConversationDispatcher: {'✅ WORKING' if dispatcher_ok else '❌ FAILED'}")
    print(f"Agent Tools: {'✅ WORKING' if tools_ok else '❌ FAILED'}")
    
    if django_ok and dispatcher_ok and tools_ok:
        print("\n🎉 Django initialization fix SUCCESSFUL!")
        print("Backend should now work properly without 'Apps aren't loaded yet' errors.")
        return True
    else:
        print("\n❌ Django initialization fix FAILED!")
        print("Manual intervention required to fix Django setup.")
        return False

def create_django_fix_patch():
    """
    Create a patch file to fix the Django initialization issue permanently.
    """
    print("\n📝 Creating Django fix patch...")
    
    patch_content = '''
# Django Initialization Fix
# Add this to the beginning of any script that uses Django models outside the main app

import os
import django
from django.conf import settings

def ensure_django_setup():
    """Ensure Django is properly initialized."""
    if not settings.configured:
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
        django.setup()

# Call this before any Django model imports
ensure_django_setup()
'''
    
    try:
        with open('/tmp/django_fix_patch.py', 'w') as f:
            f.write(patch_content)
        print("✅ Django fix patch created at /tmp/django_fix_patch.py")
        print("📋 Apply this patch to scripts that use Django models")
        return True
    except Exception as e:
        print(f"❌ Failed to create patch: {e}")
        return False

def main():
    """
    Main function to run the Django fix.
    """
    print("🚀 BACKEND DJANGO INITIALIZATION FIX")
    print("════════════════════════════════════════════════════════════")
    print("This script fixes the 'Apps aren't loaded yet' error")
    print("that prevents agent tools from working properly.")
    print()
    
    try:
        # Apply the fix
        success = apply_django_fix()
        
        # Create patch for future use
        create_django_fix_patch()
        
        if success:
            print("\n🎯 NEXT STEPS:")
            print("1. Test the backend with a real user message")
            print("2. Verify that agent tools now execute successfully")
            print("3. Check that response times are improved")
            print("4. Apply the patch to other scripts as needed")
        else:
            print("\n🔧 TROUBLESHOOTING:")
            print("1. Check Django settings configuration")
            print("2. Verify database connection")
            print("3. Ensure all required apps are in INSTALLED_APPS")
            print("4. Check for circular import issues")
        
        return success
        
    except Exception as e:
        print(f"\n❌ CRITICAL ERROR: {e}")
        print("Manual Django configuration required.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
