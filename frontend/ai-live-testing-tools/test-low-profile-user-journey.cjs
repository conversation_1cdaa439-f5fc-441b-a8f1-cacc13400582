#!/usr/bin/env node

/**
 * Test Low Profile User Journey
 * 
 * This script tests the user journey for a user with low profile completion (25%)
 * to verify that the system asks for more information instead of immediately generating a wheel.
 */

const { chromium } = require('playwright');

class LowProfileUserJourneyTester {
    constructor() {
        this.browser = null;
        this.page = null;
        this.testResults = {
            userSwitched: false,
            profileCompletionChecked: false,
            wheelRequestMade: false,
            systemAskedForMoreInfo: false,
            profileQuestionsReceived: false,
            wheelGeneratedAfterCompletion: false,
            wheelVisible: false,
            winnerCorrect: false,
            errors: []
        };
    }

    log(message) {
        console.log(`[${new Date().toISOString()}] ${message}`);
    }

    async initialize() {
        this.log('🚀 Initializing Low Profile User Journey Test...');
        
        this.browser = await chromium.launch({ 
            headless: false,
            devtools: true,
            args: ['--disable-web-security', '--disable-features=VizDisplayCompositor']
        });
        
        this.page = await this.browser.newPage();
        
        // Enable console logging
        this.page.on('console', msg => {
            if (msg.type() === 'error') {
                this.log(`❌ Console Error: ${msg.text()}`);
                this.testResults.errors.push(`Console: ${msg.text()}`);
            }
        });
        
        // Enable network monitoring
        this.page.on('response', response => {
            if (response.url().includes('/api/') || response.url().includes('/ws/')) {
                this.log(`🌐 API Response: ${response.status()} ${response.url()}`);
            }
        });
    }

    async loadFrontend() {
        this.log('🌐 Loading frontend...');
        
        try {
            await this.page.goto('http://localhost:3001/', { 
                waitUntil: 'networkidle',
                timeout: 30000 
            });
            
            this.log('✅ Frontend loaded');
            
            // Wait for initial setup
            await this.page.waitForTimeout(3000);
            
            return true;
        } catch (error) {
            this.log(`❌ Failed to load frontend: ${error.message}`);
            this.testResults.errors.push(`Frontend load: ${error.message}`);
            return false;
        }
    }

    async openDebugPanel() {
        this.log('🐛 Opening debug panel...');
        
        try {
            // Try clicking the debug button
            const debugButton = this.page.locator('button:has-text("Debug")');
            if (await debugButton.isVisible({ timeout: 5000 })) {
                await debugButton.click();
                this.log('✅ Debug panel opened via button');
            } else {
                // Try keyboard shortcut
                await this.page.keyboard.press('Control+Shift+D');
                this.log('✅ Debug panel opened via keyboard shortcut');
            }
            
            await this.page.waitForTimeout(2000);
            return true;
        } catch (error) {
            this.log(`❌ Failed to open debug panel: ${error.message}`);
            this.testResults.errors.push(`Debug panel: ${error.message}`);
            return false;
        }
    }

    async switchToLowProfileUser() {
        this.log('👤 Switching to User 38 (25% profile completion)...');
        
        try {
            // Look for user selection dropdown
            const userSelect = this.page.locator('select').filter({ hasText: /Select.*user/i }).first();
            
            if (await userSelect.isVisible({ timeout: 10000 })) {
                // Select User 38
                await userSelect.selectOption('38');
                this.log('✅ Selected User 38');
                
                // Wait for user details to load
                await this.page.waitForTimeout(3000);
                
                // Verify user details show 25% completion
                const userDetails = await this.page.textContent('.debug-panel, [class*="debug"]');
                if (userDetails && userDetails.includes('25')) {
                    this.log('✅ User 38 profile completion verified (25%)');
                    this.testResults.profileCompletionChecked = true;
                }
                
                this.testResults.userSwitched = true;
                return true;
            } else {
                this.log('❌ User selection dropdown not found');
                return false;
            }
        } catch (error) {
            this.log(`❌ Failed to switch user: ${error.message}`);
            this.testResults.errors.push(`User switch: ${error.message}`);
            return false;
        }
    }

    async testInitialWheelRequest() {
        this.log('🎯 Testing initial wheel request behavior...');
        
        try {
            // Close debug panel first
            await this.page.keyboard.press('Escape');
            await this.page.waitForTimeout(1000);
            
            // Find chat input
            const chatInput = this.page.locator('textarea, input[type="text"]').filter({ hasText: /message/i }).first();
            if (!await chatInput.isVisible({ timeout: 5000 })) {
                // Try alternative selectors
                const altInput = this.page.locator('textarea').first();
                if (await altInput.isVisible({ timeout: 5000 })) {
                    await altInput.click();
                    await altInput.fill('I want to create a wheel with activities');
                } else {
                    throw new Error('Chat input not found');
                }
            } else {
                await chatInput.click();
                await chatInput.fill('I want to create a wheel with activities');
            }
            
            // Send message
            await this.page.keyboard.press('Enter');
            this.log('✅ Wheel request sent');
            this.testResults.wheelRequestMade = true;
            
            // Wait for response
            await this.page.waitForTimeout(5000);
            
            // Check if system asks for more information instead of generating wheel
            const chatMessages = await this.page.textContent('body');
            
            if (chatMessages.includes('more information') || 
                chatMessages.includes('tell me more') || 
                chatMessages.includes('profile') ||
                chatMessages.includes('preferences')) {
                this.log('✅ System correctly asked for more information');
                this.testResults.systemAskedForMoreInfo = true;
            } else if (chatMessages.includes('wheel') && chatMessages.includes('activities')) {
                this.log('❌ System generated wheel immediately (should ask for more info)');
            } else {
                this.log('⚠️ Unclear system response');
            }
            
            return true;
        } catch (error) {
            this.log(`❌ Failed to test wheel request: ${error.message}`);
            this.testResults.errors.push(`Wheel request: ${error.message}`);
            return false;
        }
    }

    async generateReport() {
        this.log('📊 Generating test report...');
        
        const report = {
            timestamp: new Date().toISOString(),
            testResults: this.testResults,
            summary: {
                totalTests: 6,
                passed: Object.values(this.testResults).filter(v => v === true).length,
                failed: this.testResults.errors.length,
                success: this.testResults.userSwitched && 
                        this.testResults.profileCompletionChecked && 
                        this.testResults.wheelRequestMade && 
                        this.testResults.systemAskedForMoreInfo
            }
        };
        
        console.log('\n📊 LOW PROFILE USER JOURNEY TEST REPORT');
        console.log('════════════════════════════════════════════════════════════');
        console.log(`✅ User Switched to 38: ${this.testResults.userSwitched}`);
        console.log(`✅ Profile Completion Checked (25%): ${this.testResults.profileCompletionChecked}`);
        console.log(`✅ Wheel Request Made: ${this.testResults.wheelRequestMade}`);
        console.log(`✅ System Asked for More Info: ${this.testResults.systemAskedForMoreInfo}`);
        console.log(`⏳ Profile Questions Received: ${this.testResults.profileQuestionsReceived}`);
        console.log(`⏳ Wheel Generated After Completion: ${this.testResults.wheelGeneratedAfterCompletion}`);
        console.log(`⏳ Wheel Visible: ${this.testResults.wheelVisible}`);
        console.log(`⏳ Winner Correct: ${this.testResults.winnerCorrect}`);
        
        if (this.testResults.errors.length > 0) {
            console.log('\n❌ Errors:');
            this.testResults.errors.forEach(error => console.log(`  - ${error}`));
        }
        
        console.log(`\n🎯 Overall Success: ${report.summary.success ? '✅ PASSED' : '❌ FAILED'}`);
        console.log('════════════════════════════════════════════════════════════');
        
        return report;
    }

    async runTest() {
        try {
            await this.initialize();
            
            if (!await this.loadFrontend()) return;
            if (!await this.openDebugPanel()) return;
            if (!await this.switchToLowProfileUser()) return;
            await this.testInitialWheelRequest();
            
            await this.generateReport();
            
            this.log('🎉 Test completed! Browser kept open for manual verification.');
            this.log('Press Ctrl+C to close when done.');
            
            // Keep browser open for manual inspection
            await new Promise(() => {});
            
        } catch (error) {
            this.log(`❌ Test failed: ${error.message}`);
            this.testResults.errors.push(`Test execution: ${error.message}`);
        }
    }

    async cleanup() {
        if (this.browser) {
            await this.browser.close();
        }
    }
}

// Handle cleanup on exit
process.on('SIGINT', async () => {
    console.log('\n🛑 Test interrupted by user');
    process.exit(0);
});

// Run the test
const tester = new LowProfileUserJourneyTester();
tester.runTest().catch(console.error);
