#!/usr/bin/env node

/**
 * Test Enjoy Overlay Implementation
 * 
 * This test validates the new enjoy overlay functionality that shows after
 * a winning activity modal is closed, stores the last wheel spin time in
 * localStorage, and provides a 10-minute countdown with app reset capability.
 * 
 * Usage: node test-enjoy-overlay-implementation.cjs [port]
 * Example: node test-enjoy-overlay-implementation.cjs 3001
 */

const puppeteer = require('puppeteer');

class EnjoyOverlayTester {
  constructor(port = 5173) {
    this.port = port;
    this.baseUrl = `http://localhost:${port}`;
    this.browser = null;
    this.page = null;
  }

  async initialize() {
    console.log('🚀 Starting Enjoy Overlay Implementation Test...');
    
    this.browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1200, height: 800 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    this.page = await this.browser.newPage();
    
    // Enable console logging
    this.page.on('console', msg => {
      if (msg.text().includes('🎯') || msg.text().includes('Enjoy') || msg.text().includes('overlay')) {
        console.log('🖥️  BROWSER:', msg.text());
      }
    });
    
    await this.page.goto(this.baseUrl);
    await this.page.waitForTimeout(2000);
  }

  async testEnjoyOverlayFlow() {
    console.log('\n📋 Testing Complete Enjoy Overlay Flow...');
    
    try {
      // Step 1: Check if app loads correctly
      await this.page.waitForSelector('app-shell', { timeout: 10000 });
      console.log('✅ App shell loaded');

      // Step 2: Open debug panel and set up user
      await this.page.keyboard.down('Control');
      await this.page.keyboard.down('Shift');
      await this.page.keyboard.press('KeyD');
      await this.page.keyboard.up('Shift');
      await this.page.keyboard.up('Control');
      await this.page.waitForTimeout(1000);

      // Step 3: Load mock wheel data
      const loadMockButton = await this.page.$('button[title*="Load Mock Wheel"]');
      if (loadMockButton) {
        await loadMockButton.click();
        console.log('✅ Mock wheel data loaded');
        await this.page.waitForTimeout(2000);
      }

      // Step 4: Check if wheel is visible
      const wheelElement = await this.page.$('game-wheel');
      if (!wheelElement) {
        console.log('❌ Wheel component not found');
        return false;
      }
      console.log('✅ Wheel component found');

      // Step 5: Click spin button
      const spinButton = await this.page.$('.spin-button');
      if (!spinButton) {
        console.log('❌ Spin button not found');
        return false;
      }

      await spinButton.click();
      console.log('✅ Spin button clicked');
      await this.page.waitForTimeout(1000);

      // Step 6: Handle contract modal if it appears
      const contractModal = await this.page.$('contract-disclaimer-modal');
      if (contractModal) {
        console.log('📝 Contract modal appeared, accepting...');
        const acceptButton = await this.page.$('contract-disclaimer-modal button[type="submit"]');
        if (acceptButton) {
          await acceptButton.click();
          await this.page.waitForTimeout(1000);
        }
      }

      // Step 7: Wait for wheel spin to complete and winning modal to appear
      console.log('⏳ Waiting for wheel spin to complete...');
      await this.page.waitForTimeout(8000); // Wait for spin animation

      // Step 8: Check for winning modal
      const winningModal = await this.page.$('.winning-modal-overlay');
      if (!winningModal) {
        console.log('❌ Winning modal not found');
        return false;
      }
      console.log('✅ Winning modal appeared');

      // Step 9: Close winning modal to trigger enjoy overlay
      const closeButton = await this.page.$('.winning-modal-overlay .modal-close, .winning-modal-overlay .btn-primary');
      if (!closeButton) {
        console.log('❌ Close button not found in winning modal');
        return false;
      }

      await closeButton.click();
      console.log('✅ Winning modal closed');
      await this.page.waitForTimeout(1000);

      // Step 10: Check if enjoy overlay appears
      const enjoyOverlay = await this.page.$('.enjoy-overlay');
      if (!enjoyOverlay) {
        console.log('❌ Enjoy overlay not found');
        return false;
      }
      console.log('✅ Enjoy overlay appeared');

      // Step 11: Validate overlay content
      const enjoyTitle = await this.page.$eval('.enjoy-title', el => el.textContent);
      if (!enjoyTitle.includes('Enjoy')) {
        console.log('❌ Enjoy title not correct:', enjoyTitle);
        return false;
      }
      console.log('✅ Enjoy title correct:', enjoyTitle);

      // Step 12: Check activity details in overlay
      const activityName = await this.page.$eval('.enjoy-activity-name', el => el.textContent);
      console.log('✅ Activity name in overlay:', activityName);

      const activityDescription = await this.page.$eval('.enjoy-activity-description', el => el.textContent);
      console.log('✅ Activity description in overlay:', activityDescription);

      // Step 13: Check hourglass animation
      const hourglass = await this.page.$('.enjoy-hourglass');
      if (!hourglass) {
        console.log('❌ Hourglass not found');
        return false;
      }
      console.log('✅ Hourglass found');

      // Step 14: Check timer display
      const timer = await this.page.$eval('.enjoy-timer', el => el.textContent);
      console.log('✅ Timer display:', timer);

      // Step 15: Check localStorage storage
      const localStorageData = await this.page.evaluate(() => {
        return localStorage.getItem('goali_last_wheel_spin');
      });
      
      if (!localStorageData) {
        console.log('❌ localStorage data not found');
        return false;
      }
      
      const spinData = JSON.parse(localStorageData);
      console.log('✅ localStorage data stored:', {
        timestamp: new Date(spinData.timestamp).toISOString(),
        activityName: spinData.activity.name
      });

      // Step 16: Test reset functionality
      const resetButton = await this.page.$('.enjoy-close-btn');
      if (!resetButton) {
        console.log('❌ Reset button not found');
        return false;
      }

      await resetButton.click();
      console.log('✅ Reset button clicked');
      await this.page.waitForTimeout(1000);

      // Step 17: Verify overlay is hidden and app is reset
      const overlayAfterReset = await this.page.$('.enjoy-overlay');
      if (overlayAfterReset) {
        console.log('❌ Overlay still visible after reset');
        return false;
      }
      console.log('✅ Overlay hidden after reset');

      // Step 18: Check localStorage cleanup
      const localStorageAfterReset = await this.page.evaluate(() => {
        return localStorage.getItem('goali_last_wheel_spin');
      });
      
      if (localStorageAfterReset) {
        console.log('❌ localStorage not cleaned up after reset');
        return false;
      }
      console.log('✅ localStorage cleaned up after reset');

      return true;

    } catch (error) {
      console.error('❌ Error during enjoy overlay test:', error);
      return false;
    }
  }

  async testPersistenceAcrossReload() {
    console.log('\n🔄 Testing Persistence Across Page Reload...');
    
    try {
      // First, create a mock localStorage entry
      await this.page.evaluate(() => {
        const mockSpinData = {
          timestamp: Date.now() - (2 * 60 * 1000), // 2 minutes ago
          activity: {
            id: 'test-activity',
            name: '🏃‍♂️ Test Running',
            description: 'A test running activity',
            domain: 'fitness',
            type: 'tailored',
            icon: '🏃‍♂️',
            base_challenge_rating: 75,
            duration_range: '20-30 minutes'
          }
        };
        localStorage.setItem('goali_last_wheel_spin', JSON.stringify(mockSpinData));
      });

      // Reload the page
      await this.page.reload();
      await this.page.waitForTimeout(3000);

      // Check if overlay appears automatically
      const enjoyOverlay = await this.page.$('.enjoy-overlay');
      if (!enjoyOverlay) {
        console.log('❌ Enjoy overlay not restored after reload');
        return false;
      }
      console.log('✅ Enjoy overlay restored after reload');

      // Verify the activity data is correct
      const activityName = await this.page.$eval('.enjoy-activity-name', el => el.textContent);
      if (!activityName.includes('Test Running')) {
        console.log('❌ Activity name not restored correctly:', activityName);
        return false;
      }
      console.log('✅ Activity data restored correctly:', activityName);

      return true;

    } catch (error) {
      console.error('❌ Error during persistence test:', error);
      return false;
    }
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
    }
  }

  async runAllTests() {
    try {
      await this.initialize();
      
      const flowTest = await this.testEnjoyOverlayFlow();
      const persistenceTest = await this.testPersistenceAcrossReload();
      
      console.log('\n📊 Test Results Summary:');
      console.log(`✅ Enjoy Overlay Flow: ${flowTest ? 'PASSED' : 'FAILED'}`);
      console.log(`✅ Persistence Test: ${persistenceTest ? 'PASSED' : 'FAILED'}`);
      
      const allPassed = flowTest && persistenceTest;
      console.log(`\n🎯 Overall Result: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
      
      return allPassed;
      
    } catch (error) {
      console.error('❌ Test execution failed:', error);
      return false;
    } finally {
      await this.cleanup();
    }
  }
}

// Main execution
async function main() {
  const port = process.argv[2] || 5173;
  console.log(`🎯 Testing Enjoy Overlay Implementation on port ${port}`);
  
  const tester = new EnjoyOverlayTester(port);
  const success = await tester.runAllTests();
  
  process.exit(success ? 0 : 1);
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { EnjoyOverlayTester };
