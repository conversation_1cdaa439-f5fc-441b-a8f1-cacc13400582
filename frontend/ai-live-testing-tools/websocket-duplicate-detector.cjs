#!/usr/bin/env node

/**
 * WebSocket Duplicate Response Detector
 * 
 * Monitors WebSocket traffic to identify and analyze duplicate responses
 * from the backend that are causing performance issues.
 */

const { chromium } = require('playwright');

class WebSocketDuplicateDetector {
    constructor() {
        this.browser = null;
        this.page = null;
        this.messages = [];
        this.duplicates = [];
        this.messageHashes = new Map();
        this.startTime = Date.now();
    }

    async initialize() {
        console.log('🔍 WebSocket Duplicate Response Detector - Starting...');
        
        this.browser = await chromium.launch({ 
            headless: false,
            slowMo: 500
        });
        
        this.page = await this.browser.newPage();
        await this.setupWebSocketMonitoring();
        
        console.log('✅ WebSocket monitoring initialized');
    }

    async setupWebSocketMonitoring() {
        this.page.on('websocket', ws => {
            console.log(`🔌 WebSocket connected: ${ws.url()}`);
            
            ws.on('framereceived', event => {
                const timestamp = Date.now();
                const message = event.payload;
                
                this.analyzeMessage(message, timestamp, 'received');
            });

            ws.on('framesent', event => {
                const timestamp = Date.now();
                const message = event.payload;
                
                this.analyzeMessage(message, timestamp, 'sent');
            });
        });
    }

    analyzeMessage(message, timestamp, direction) {
        const messageData = {
            timestamp,
            direction,
            content: message,
            size: message.length
        };

        try {
            const parsed = JSON.parse(message);
            messageData.parsed = parsed;
            messageData.type = parsed.type || 'unknown';
        } catch (e) {
            messageData.type = 'raw';
        }

        this.messages.push(messageData);

        // Check for duplicates
        if (direction === 'received' && messageData.parsed) {
            this.checkForDuplicate(messageData);
        }

        // Real-time logging
        const elapsed = ((timestamp - this.startTime) / 1000).toFixed(1);
        console.log(`[${elapsed}s] ${direction === 'received' ? '📨' : '📤'} ${messageData.type}: ${message.substring(0, 100)}${message.length > 100 ? '...' : ''}`);
    }

    checkForDuplicate(messageData) {
        const content = messageData.content;
        const contentHash = this.hashMessage(content);
        
        if (this.messageHashes.has(contentHash)) {
            const originalMessage = this.messageHashes.get(contentHash);
            const timeDiff = messageData.timestamp - originalMessage.timestamp;
            
            console.log(`🚨 DUPLICATE DETECTED! Time diff: ${timeDiff}ms`);
            
            this.duplicates.push({
                original: originalMessage,
                duplicate: messageData,
                timeDifference: timeDiff,
                contentHash
            });
        } else {
            this.messageHashes.set(contentHash, messageData);
        }
    }

    hashMessage(content) {
        // Simple hash for duplicate detection
        try {
            const parsed = JSON.parse(content);
            // Hash based on message type and content, ignoring timestamps
            const hashableContent = {
                type: parsed.type,
                content: parsed.content || parsed.message || parsed.data
            };
            return JSON.stringify(hashableContent);
        } catch (e) {
            return content;
        }
    }

    async testUserMessage() {
        console.log('\n🧪 Testing user message for duplicates...');
        console.log('════════════════════════════════════════════════════════════');
        
        await this.page.goto('http://localhost:3001');
        await this.page.waitForTimeout(5000);
        
        // Clear previous messages
        this.messages = [];
        this.duplicates = [];
        this.messageHashes.clear();
        this.startTime = Date.now();
        
        console.log('📝 Sending test message...');
        
        // Try to find and use chat input
        try {
            const chatInput = await this.page.locator('textarea').first();
            await chatInput.fill('hey! do you recognize me?');
            await chatInput.press('Enter');
            
            console.log('✅ Message sent, monitoring for duplicates...');
            
            // Monitor for 60 seconds
            for (let i = 0; i < 60; i++) {
                await this.page.waitForTimeout(1000);
                
                if (this.duplicates.length > 0) {
                    console.log(`⚠️  ${this.duplicates.length} duplicate(s) detected so far`);
                }
                
                // Show progress every 10 seconds
                if ((i + 1) % 10 === 0) {
                    console.log(`⏳ Monitoring... ${i + 1}/60 seconds`);
                }
            }
            
        } catch (error) {
            console.log(`❌ Could not send message: ${error.message}`);
            console.log('🔍 Monitoring existing WebSocket traffic instead...');
            
            // Just monitor for 30 seconds
            await this.page.waitForTimeout(30000);
        }
    }

    generateReport() {
        console.log('\n📊 DUPLICATE RESPONSE ANALYSIS REPORT');
        console.log('════════════════════════════════════════════════════════════');
        
        const report = {
            timestamp: new Date().toISOString(),
            totalMessages: this.messages.length,
            duplicatesFound: this.duplicates.length,
            messagesByType: this.getMessagesByType(),
            duplicateAnalysis: this.analyzeDuplicates(),
            recommendations: this.generateRecommendations()
        };
        
        console.log(`📈 Total Messages: ${report.totalMessages}`);
        console.log(`🚨 Duplicates Found: ${report.duplicatesFound}`);
        
        console.log('\n📋 Messages by Type:');
        Object.entries(report.messagesByType).forEach(([type, count]) => {
            console.log(`  ${type}: ${count}`);
        });
        
        if (report.duplicatesFound > 0) {
            console.log('\n🔍 Duplicate Analysis:');
            report.duplicateAnalysis.forEach((dup, i) => {
                console.log(`  ${i + 1}. Type: ${dup.type}, Time diff: ${dup.timeDifference}ms`);
                console.log(`     Content: ${dup.contentPreview}`);
            });
        }
        
        console.log('\n💡 Recommendations:');
        report.recommendations.forEach((rec, i) => {
            console.log(`  ${i + 1}. ${rec}`);
        });
        
        // Save detailed report
        const fs = require('fs');
        const reportPath = `test-results/websocket-duplicate-analysis-${Date.now()}.json`;
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        console.log(`\n📁 Detailed report saved to: ${reportPath}`);
        
        return report;
    }

    getMessagesByType() {
        const types = {};
        this.messages.forEach(msg => {
            const type = msg.type || 'unknown';
            types[type] = (types[type] || 0) + 1;
        });
        return types;
    }

    analyzeDuplicates() {
        return this.duplicates.map(dup => ({
            type: dup.original.type,
            timeDifference: dup.timeDifference,
            contentPreview: dup.original.content.substring(0, 100),
            originalTimestamp: dup.original.timestamp,
            duplicateTimestamp: dup.duplicate.timestamp
        }));
    }

    generateRecommendations() {
        const recommendations = [];
        
        if (this.duplicates.length > 0) {
            recommendations.push('CRITICAL: Duplicate responses detected - implement response deduplication');
            recommendations.push('Investigate backend workflow execution - may be running multiple times');
            recommendations.push('Check for race conditions in message processing');
        }
        
        const responseMessages = this.messages.filter(m => 
            m.direction === 'received' && 
            (m.type === 'chat_message' || m.type === 'ai_response')
        );
        
        if (responseMessages.length > 2) {
            recommendations.push('Multiple response messages detected - optimize response flow');
        }
        
        const avgMessageSize = this.messages.reduce((sum, m) => sum + m.size, 0) / this.messages.length;
        if (avgMessageSize > 1000) {
            recommendations.push('Large message sizes detected - consider message compression');
        }
        
        if (recommendations.length === 0) {
            recommendations.push('No critical issues detected in WebSocket traffic');
        }
        
        return recommendations;
    }

    async cleanup() {
        if (this.browser) {
            await this.browser.close();
        }
    }

    async run() {
        try {
            await this.initialize();
            await this.testUserMessage();
            this.generateReport();
            
            console.log('\n🎯 WebSocket duplicate detection completed!');
            
        } catch (error) {
            console.error('❌ Detection failed:', error);
        } finally {
            await this.cleanup();
        }
    }
}

// Run the detector
if (require.main === module) {
    const detector = new WebSocketDuplicateDetector();
    detector.run().catch(console.error);
}

module.exports = WebSocketDuplicateDetector;
