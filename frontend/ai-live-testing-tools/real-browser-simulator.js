#!/usr/bin/env node

/**
 * Real Browser Simulator
 * 
 * This tool simulates the actual frontend behavior to detect UI issues
 * that can't be caught by backend-only testing.
 */

import WebSocket from 'ws';
import { JSDOM } from 'jsdom';
import config from './config.js';

class RealBrowserSimulator {
  constructor() {
    this.dom = null;
    this.window = null;
    this.document = null;
    this.ws = null;
    this.isConnected = false;
    this.messages = [];
    this.chatMessages = [];
    this.wheelData = null;
    this.errors = [];
    this.chatInterface = null;
    this.appShell = null;
  }

  async start() {
    console.log('🌐 Starting Real Browser Simulator...');
    console.log('=====================================');
    
    try {
      await this.initializeBrowserEnvironment();
      await this.connectToBackend();
      await this.runUserStory();
      this.analyzeResults();
    } catch (error) {
      console.error('❌ Simulation failed:', error);
      this.errors.push(`Simulation failed: ${error.message}`);
    } finally {
      if (this.ws) {
        this.ws.close();
      }
    }
  }

  async initializeBrowserEnvironment() {
    console.log('🔧 Initializing browser environment...');
    
    // Create JSDOM with realistic HTML structure
    this.dom = new JSDOM(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Goali</title>
          <meta charset="utf-8">
        </head>
        <body>
          <app-shell id="app-shell">
            <div class="chat-section">
              <chat-interface id="chat-interface">
                <div class="chat-messages" id="chat-messages">
                  <!-- Chat messages will be added here -->
                </div>
                <div class="chat-input-area">
                  <input type="text" id="chat-input" placeholder="Type your message...">
                  <button id="send-button">Send</button>
                </div>
              </chat-interface>
            </div>
            <div class="wheel-section">
              <div class="wheel-container" id="wheel-container">
                <div class="wheel-placeholder">
                  <p>Ready for your next adventure?</p>
                </div>
              </div>
            </div>
          </app-shell>
        </body>
      </html>
    `, {
      url: 'http://localhost:3000',
      pretendToBeVisual: true,
      resources: 'usable'
    });

    this.window = this.dom.window;
    this.document = this.window.document;
    
    // Setup mock components that simulate real frontend behavior
    this.setupMockComponents();
    
    console.log('✅ Browser environment initialized');
  }

  setupMockComponents() {
    const self = this;
    
    // Mock ChatInterface component
    class MockChatInterface {
      constructor() {
        this.messages = [];
        this.chatMessagesContainer = self.document.getElementById('chat-messages');
      }

      addMessage(message) {
        console.log(`💬 Adding message to chat: ${message.type} - "${message.content?.substring(0, 50)}..."`);
        
        // Test for the chat display issue
        if (!message.content || message.content === 'undefined' || message.content.trim() === '') {
          console.log('❌ CRITICAL ERROR: Chat message has undefined/empty content!');
          self.errors.push('Chat message content is undefined or empty');
          return;
        }

        this.messages.push(message);
        
        // Simulate DOM update
        const messageElement = self.document.createElement('div');
        messageElement.className = `chat-message ${message.type}`;
        messageElement.innerHTML = `
          <div class="message-content">${message.content}</div>
          <div class="message-timestamp">${new Date().toLocaleTimeString()}</div>
        `;
        
        if (this.chatMessagesContainer) {
          this.chatMessagesContainer.appendChild(messageElement);
          console.log(`✅ Message added to DOM: ${this.messages.length} total messages`);
        } else {
          console.log('❌ ERROR: Chat messages container not found in DOM!');
          self.errors.push('Chat messages container missing from DOM');
        }
      }

      getDisplayedMessages() {
        const messageElements = this.chatMessagesContainer?.querySelectorAll('.chat-message') || [];
        return Array.from(messageElements).map(el => ({
          content: el.querySelector('.message-content')?.textContent || '',
          timestamp: el.querySelector('.message-timestamp')?.textContent || ''
        }));
      }
    }

    // Mock AppShell component
    class MockAppShell {
      constructor() {
        this.chatInterface = new MockChatInterface();
        self.chatInterface = this.chatInterface;
      }

      handleChatMessage(data) {
        console.log('🔄 AppShell processing chat message...');
        
        // Simulate the exact logic from app-shell.ts
        if (data.is_user) {
          // User message echo - add to chat
          const userMessage = {
            id: `msg-${Date.now()}`,
            type: 'user',
            content: data.content,
            timestamp: new Date()
          };
          this.chatInterface.addMessage(userMessage);
        } else {
          // AI response - add to chat
          const aiMessage = {
            id: `msg-${Date.now()}`,
            type: 'ai',
            content: data.content,
            timestamp: new Date()
          };
          this.chatInterface.addMessage(aiMessage);
        }
      }

      handleWheelData(wheelData) {
        console.log('🎡 AppShell processing wheel data...');
        self.wheelData = wheelData;
        
        // Add system message about wheel generation
        const systemMessage = {
          id: `msg-${Date.now()}`,
          type: 'system',
          content: 'Your personalized activity wheel has been generated! Give it a spin to choose your next adventure.',
          timestamp: new Date()
        };
        this.chatInterface.addMessage(systemMessage);
      }
    }

    this.appShell = new MockAppShell();
  }

  async connectToBackend() {
    console.log('🔌 Connecting to backend...');
    
    return new Promise((resolve, reject) => {
      this.ws = new WebSocket(config.backend.websocketUrl);
      
      this.ws.on('open', () => {
        console.log('✅ Connected to backend');
        this.isConnected = true;
        resolve();
      });

      this.ws.on('message', (data) => {
        try {
          const message = JSON.parse(data);
          this.handleMessage(message);
        } catch (error) {
          console.error('❌ Error parsing message:', error);
          this.errors.push(`Message parsing error: ${error.message}`);
        }
      });

      this.ws.on('error', (error) => {
        console.error('❌ WebSocket error:', error);
        reject(error);
      });

      this.ws.on('close', () => {
        console.log('🔌 Connection closed');
        this.isConnected = false;
      });
    });
  }

  handleMessage(message) {
    this.messages.push(message);
    console.log(`📨 Received: ${message.type}`);

    switch (message.type) {
      case 'chat_message':
        this.appShell.handleChatMessage(message);
        break;
      case 'wheel_data':
        this.appShell.handleWheelData(message.wheel);
        break;
      case 'system_message':
        const systemMsg = {
          id: `msg-${Date.now()}`,
          type: 'system',
          content: message.content,
          timestamp: new Date()
        };
        this.chatInterface.addMessage(systemMsg);
        break;
      default:
        console.log(`📝 Unhandled message type: ${message.type}`);
    }
  }

  async runUserStory() {
    console.log('🎬 Running user story simulation...');

    // Send first message
    console.log('👤 Step 1: User says "I\'m bored"');
    await this.sendMessage("I'm bored");
    await this.sleep(8000);

    // Check if we received AI response
    this.checkChatDisplay('Step 1');

    // Send second message
    console.log('👤 Step 2: User asks for exercise suggestions');
    await this.sendMessage("I feel like doing exercise, what do you propose?");
    await this.sleep(20000);

    // Check final chat display
    this.checkChatDisplay('Step 2');
  }

  checkChatDisplay(step) {
    console.log(`\n🔍 Checking chat display after ${step}:`);

    if (this.chatInterface) {
      const displayedMessages = this.chatInterface.getDisplayedMessages();
      console.log(`📱 Messages in DOM: ${displayedMessages.length}`);

      displayedMessages.forEach((msg, index) => {
        if (!msg.content || msg.content.trim() === '') {
          console.log(`❌ CRITICAL: Message ${index + 1} has empty content!`);
          this.errors.push(`${step}: Message ${index + 1} has empty content in chat display`);
        } else {
          console.log(`✅ Message ${index + 1}: "${msg.content.substring(0, 50)}..."`);
        }
      });

      // Check if AI responses are being displayed
      const aiMessages = this.chatInterface.messages.filter(m => m.type === 'ai');
      const displayedAiMessages = displayedMessages.filter(m => m.content && m.content.includes('AI') || m.content.includes('wheel') || m.content.includes('activity'));

      console.log(`🤖 AI messages in memory: ${aiMessages.length}`);
      console.log(`📺 AI messages displayed: ${displayedAiMessages.length}`);

      if (aiMessages.length > displayedAiMessages.length) {
        console.log(`❌ CRITICAL: ${aiMessages.length - displayedAiMessages.length} AI messages not displayed!`);
        this.errors.push(`${step}: AI messages not appearing in chat display`);
      }
    } else {
      console.log('❌ CRITICAL: Chat interface not found!');
      this.errors.push(`${step}: Chat interface not accessible`);
    }
  }

  async sendMessage(content) {
    if (!this.isConnected) {
      console.log('❌ Cannot send message - not connected');
      return;
    }

    console.log(`👤 Sending: "${content}"`);
    
    const message = {
      type: 'chat_message',
      content: {
        message: content,
        user_profile_id: '2',
        timestamp: new Date().toISOString()
      }
    };

    this.ws.send(JSON.stringify(message));
  }

  analyzeResults() {
    console.log('\n🔍 REAL BROWSER SIMULATION ANALYSIS');
    console.log('====================================');
    
    console.log(`📊 Total messages received: ${this.messages.length}`);
    console.log(`💬 Chat messages in interface: ${this.chatInterface?.messages.length || 0}`);
    console.log(`🎡 Wheel data received: ${this.wheelData ? 'Yes' : 'No'}`);
    console.log(`❌ Errors detected: ${this.errors.length}`);
    
    // Analyze chat display
    if (this.chatInterface) {
      const displayedMessages = this.chatInterface.getDisplayedMessages();
      console.log(`📱 Messages displayed in DOM: ${displayedMessages.length}`);
      
      // Check for empty or undefined content
      const emptyMessages = displayedMessages.filter(msg => !msg.content || msg.content.trim() === '');
      if (emptyMessages.length > 0) {
        console.log(`❌ CRITICAL: ${emptyMessages.length} messages have empty content in chat display!`);
        this.errors.push(`${emptyMessages.length} messages with empty content in chat display`);
      }
    }
    
    if (this.errors.length > 0) {
      console.log('\n❌ ERRORS FOUND:');
      this.errors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error}`);
      });
    }
    
    // Overall assessment
    console.log('\n🎯 OVERALL ASSESSMENT:');
    if (this.errors.length === 0) {
      console.log('✅ SIMULATION PASSED - No UI issues detected');
    } else {
      console.log('❌ SIMULATION FAILED - UI issues detected');
    }
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const simulator = new RealBrowserSimulator();
  simulator.start().then(() => {
    console.log('\n🎉 Real browser simulation completed!');
    process.exit(0);
  }).catch((error) => {
    console.error('❌ Simulation failed:', error);
    process.exit(1);
  });
}

export default RealBrowserSimulator;
