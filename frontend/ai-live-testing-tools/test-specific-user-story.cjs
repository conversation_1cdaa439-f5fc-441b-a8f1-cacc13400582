const { chromium } = require('playwright');
const WebSocket = require('ws');

async function testSpecificUserStory() {
    console.log('🚀 Testing Specific User Story: "I\'m restless and need to do things physical. I have 2 hours. Make me a wheel"');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000 // Slow down for better observation
    });
    
    const context = await browser.newContext();
    const page = await context.newPage();
    
    // Monitor WebSocket messages
    const messages = [];
    page.on('websocket', ws => {
        console.log(`🔌 WebSocket connection: ${ws.url()}`);
        ws.on('framereceived', event => {
            try {
                const data = JSON.parse(event.payload);
                messages.push({ type: 'received', data, timestamp: Date.now() });
                console.log(`📨 ← Server: ${JSON.stringify(data).substring(0, 100)}...`);
            } catch (e) {
                console.log(`📨 ← Server (raw): ${event.payload.substring(0, 100)}...`);
            }
        });
        ws.on('framesent', event => {
            try {
                const data = JSON.parse(event.payload);
                messages.push({ type: 'sent', data, timestamp: Date.now() });
                console.log(`📤 → Client: ${JSON.stringify(data).substring(0, 100)}...`);
            } catch (e) {
                console.log(`📤 → Client (raw): ${event.payload.substring(0, 100)}...`);
            }
        });
    });
    
    try {
        // Load the frontend
        console.log('🌐 Loading frontend...');
        await page.goto('http://localhost:3000/', { waitUntil: 'networkidle' });
        console.log('✅ Frontend loaded');
        
        // Wait for connection
        await page.waitForTimeout(3000);
        
        // Find and click the chat input
        console.log('🔍 Looking for chat input...');
        const chatInput = await page.locator('textarea').first();
        await chatInput.waitFor({ state: 'visible', timeout: 10000 });
        
        console.log('🖱️ Clicking chat input...');
        await chatInput.click();
        
        // Send the specific message
        const message = "I'm restless and need to do things physical. I have 2 hours. Make me a wheel";
        console.log(`📝 Typing message: "${message}"`);
        await chatInput.fill(message);
        
        console.log('📤 Sending message...');
        await page.keyboard.press('Enter');
        
        // Wait for wheel generation
        console.log('⏳ Waiting for wheel generation...');
        let wheelFound = false;
        let wheelData = null;
        let mentorResponse = null;
        
        // Monitor for 60 seconds
        const startTime = Date.now();
        while (Date.now() - startTime < 60000) {
            // Check for wheel element
            const wheelElement = await page.locator('.wheel-container, .wheel, [class*="wheel"]').first();
            if (await wheelElement.isVisible().catch(() => false)) {
                console.log('🎡 Wheel element found!');
                wheelFound = true;
                
                // Try to get wheel data from messages
                const wheelMessages = messages.filter(m => 
                    m.data && (
                        m.data.type === 'wheel_data' || 
                        (m.data.content && typeof m.data.content === 'object' && m.data.content.activities)
                    )
                );
                
                if (wheelMessages.length > 0) {
                    wheelData = wheelMessages[wheelMessages.length - 1].data;
                    console.log('📊 Wheel data found:', JSON.stringify(wheelData, null, 2));
                }
                break;
            }
            
            // Check for mentor response
            const chatMessages = messages.filter(m => 
                m.data && m.data.type === 'chat_message' && !m.data.is_user
            );
            if (chatMessages.length > 0) {
                mentorResponse = chatMessages[chatMessages.length - 1].data.content;
            }
            
            await page.waitForTimeout(1000);
        }
        
        // Test wheel spinning if wheel is found
        if (wheelFound) {
            console.log('🎲 Testing wheel spinning...');

            // Wait a bit more for wheel to fully render
            await page.waitForTimeout(2000);

            // Look for spin button with more specific selectors
            const spinSelectors = [
                'button.spin-button',
                'game-wheel button',
                '.wheel-container button',
                'button:has-text("Spin")',
                'button:has-text("Spin!")'
            ];

            let spinSuccess = false;
            for (const selector of spinSelectors) {
                try {
                    console.log(`🔍 Trying selector: ${selector}`);
                    const element = await page.locator(selector).first();
                    const isVisible = await element.isVisible().catch(() => false);
                    const isEnabled = await element.isEnabled().catch(() => false);

                    console.log(`   Visible: ${isVisible}, Enabled: ${isEnabled}`);

                    if (isVisible && isEnabled) {
                        console.log(`🎯 Found spin button: ${selector}`);
                        await element.click();
                        console.log('✅ Wheel spin initiated');
                        spinSuccess = true;

                        // Wait for spin animation and completion
                        console.log('⏳ Waiting for spin to complete...');
                        await page.waitForTimeout(8000); // Wait longer for spin to complete

                        // Check for spin result messages
                        const spinResults = messages.filter(m =>
                            m.data && m.data.type === 'spin_result'
                        );

                        // Check for post-spin mentor responses
                        const postSpinResponses = messages.filter(m =>
                            m.data && m.data.type === 'chat_message' &&
                            !m.data.is_user &&
                            m.timestamp > Date.now() - 10000 // Last 10 seconds
                        );

                        if (spinResults.length > 0) {
                            console.log('🏆 Spin result found:', JSON.stringify(spinResults[spinResults.length - 1].data, null, 2));
                        }

                        if (postSpinResponses.length > 0) {
                            console.log('💬 Post-spin mentor response found:', postSpinResponses[postSpinResponses.length - 1].data.content.substring(0, 100) + '...');
                        }

                        break;
                    }
                } catch (e) {
                    console.log(`   Error with ${selector}:`, e.message);
                }
            }

            if (!spinSuccess) {
                console.log('❌ Could not find clickable spin button');

                // Debug: List all buttons on the page
                const allButtons = await page.locator('button').all();
                console.log(`🔍 Found ${allButtons.length} buttons on page:`);
                for (let i = 0; i < Math.min(allButtons.length, 5); i++) {
                    const button = allButtons[i];
                    const text = await button.textContent().catch(() => 'N/A');
                    const classes = await button.getAttribute('class').catch(() => 'N/A');
                    console.log(`   Button ${i}: "${text}" (classes: ${classes})`);
                }
            }
        }
        
        // Analyze results
        console.log('\n📊 ANALYSIS RESULTS');
        console.log('════════════════════════════════════════');
        console.log(`✅ Frontend loaded: YES`);
        console.log(`✅ Message sent: YES`);
        console.log(`${wheelFound ? '✅' : '❌'} Wheel generated: ${wheelFound ? 'YES' : 'NO'}`);
        
        if (wheelData) {
            const activities = wheelData.content?.activities || wheelData.activities || [];
            console.log(`✅ Activities found: ${activities.length}`);
            
            if (activities.length > 0) {
                const colors = activities.map(a => a.color).filter(c => c);
                const uniqueColors = [...new Set(colors)];
                console.log(`${uniqueColors.length > 1 ? '✅' : '❌'} Distinct colors: ${uniqueColors.length}/${activities.length}`);
                console.log(`Colors found: ${uniqueColors.join(', ')}`);
            }
        }
        
        console.log(`${mentorResponse ? '✅' : '❌'} Mentor response: ${mentorResponse ? 'YES' : 'NO'}`);
        
        if (mentorResponse) {
            console.log('📝 Mentor response preview:', mentorResponse.substring(0, 200) + '...');
        }
        
        console.log(`\n📨 Total WebSocket messages: ${messages.length}`);
        
        // Keep browser open for manual inspection
        console.log('\n🔍 Browser kept open for manual inspection...');
        console.log('Press Ctrl+C to close when done.');
        
        // Wait indefinitely
        await new Promise(() => {});
        
    } catch (error) {
        console.error('❌ Test failed:', error);
    } finally {
        // Don't close browser automatically for inspection
        // await browser.close();
    }
}

testSpecificUserStory().catch(console.error);
