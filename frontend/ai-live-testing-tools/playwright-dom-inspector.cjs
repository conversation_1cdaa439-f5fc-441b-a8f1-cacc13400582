/**
 * Playwright DOM Inspector
 * 
 * Comprehensive inspection of the actual DOM structure to understand
 * why chat-interface and textarea elements are not found
 */

const { chromium } = require('playwright');

class DOMInspector {
    constructor() {
        this.browser = null;
        this.page = null;
    }

    async initialize() {
        console.log('🔍 Initializing DOM Inspector...');
        
        this.browser = await chromium.launch({ 
            headless: false,
            slowMo: 1000
        });
        
        this.page = await this.browser.newPage();
        
        this.page.on('console', msg => {
            console.log(`🖥️  Console: ${msg.text()}`);
        });
    }

    async loadAndInspect() {
        console.log('🌐 Loading frontend...');
        
        await this.page.goto('http://localhost:3002/');
        await this.page.waitForSelector('app-shell');
        
        // Wait for full initialization
        await this.page.waitForTimeout(25000);
        console.log('✅ Frontend loaded');
        
        await this.inspectDOMStructure();
        await this.inspectShadowDOM();
        await this.findInputElements();
        await this.inspectChatElements();
    }

    async inspectDOMStructure() {
        console.log('\n📋 INSPECTING MAIN DOM STRUCTURE');
        console.log('================================');
        
        const structure = await this.page.evaluate(() => {
            const getElementInfo = (element, depth = 0) => {
                if (depth > 3) return '...'; // Limit depth
                
                const info = {
                    tagName: element.tagName,
                    id: element.id || '',
                    className: element.className || '',
                    childCount: element.children.length,
                    textContent: element.textContent ? element.textContent.substring(0, 50) + '...' : ''
                };
                
                if (element.children.length > 0 && depth < 3) {
                    info.children = Array.from(element.children).map(child => 
                        getElementInfo(child, depth + 1)
                    );
                }
                
                return info;
            };
            
            return getElementInfo(document.body);
        });
        
        console.log('🏗️  DOM Structure:', JSON.stringify(structure, null, 2));
    }

    async inspectShadowDOM() {
        console.log('\n🌑 INSPECTING SHADOW DOM');
        console.log('========================');
        
        const shadowInfo = await this.page.evaluate(() => {
            const elementsWithShadow = [];
            
            const findShadowRoots = (element) => {
                if (element.shadowRoot) {
                    const shadowContent = {
                        host: element.tagName,
                        hostId: element.id,
                        hostClass: element.className,
                        shadowChildren: Array.from(element.shadowRoot.children).map(child => ({
                            tagName: child.tagName,
                            id: child.id,
                            className: child.className,
                            textContent: child.textContent ? child.textContent.substring(0, 50) + '...' : ''
                        }))
                    };
                    elementsWithShadow.push(shadowContent);
                }
                
                Array.from(element.children).forEach(child => findShadowRoots(child));
            };
            
            findShadowRoots(document.body);
            return elementsWithShadow;
        });
        
        console.log('🌑 Shadow DOM elements:', JSON.stringify(shadowInfo, null, 2));
    }

    async findInputElements() {
        console.log('\n📝 FINDING INPUT ELEMENTS');
        console.log('=========================');
        
        const inputInfo = await this.page.evaluate(() => {
            const inputs = [];
            
            // Regular DOM inputs
            const regularInputs = document.querySelectorAll('input, textarea, [contenteditable]');
            Array.from(regularInputs).forEach(input => {
                inputs.push({
                    type: 'regular',
                    tagName: input.tagName,
                    inputType: input.type || 'N/A',
                    id: input.id,
                    className: input.className,
                    placeholder: input.placeholder || '',
                    disabled: input.disabled,
                    readOnly: input.readOnly,
                    visible: input.offsetParent !== null,
                    value: input.value || ''
                });
            });
            
            // Shadow DOM inputs
            const elementsWithShadow = document.querySelectorAll('*');
            Array.from(elementsWithShadow).forEach(element => {
                if (element.shadowRoot) {
                    const shadowInputs = element.shadowRoot.querySelectorAll('input, textarea, [contenteditable]');
                    Array.from(shadowInputs).forEach(input => {
                        inputs.push({
                            type: 'shadow',
                            host: element.tagName,
                            tagName: input.tagName,
                            inputType: input.type || 'N/A',
                            id: input.id,
                            className: input.className,
                            placeholder: input.placeholder || '',
                            disabled: input.disabled,
                            readOnly: input.readOnly,
                            visible: input.offsetParent !== null,
                            value: input.value || ''
                        });
                    });
                }
            });
            
            return inputs;
        });
        
        console.log('📝 Input elements found:', JSON.stringify(inputInfo, null, 2));
        return inputInfo;
    }

    async inspectChatElements() {
        console.log('\n💬 INSPECTING CHAT-RELATED ELEMENTS');
        console.log('====================================');
        
        const chatInfo = await this.page.evaluate(() => {
            const chatElements = [];
            
            // Look for chat-related elements
            const selectors = [
                'chat-interface', 'app-shell', 'game-wheel',
                '[class*="chat"]', '[id*="chat"]',
                '[class*="message"]', '[id*="message"]',
                '[class*="input"]', '[id*="input"]'
            ];
            
            selectors.forEach(selector => {
                try {
                    const elements = document.querySelectorAll(selector);
                    Array.from(elements).forEach(element => {
                        chatElements.push({
                            selector,
                            tagName: element.tagName,
                            id: element.id,
                            className: element.className,
                            hasShadowRoot: !!element.shadowRoot,
                            visible: element.offsetParent !== null,
                            textContent: element.textContent ? element.textContent.substring(0, 100) + '...' : ''
                        });
                    });
                } catch (e) {
                    // Invalid selector, skip
                }
            });
            
            return chatElements;
        });
        
        console.log('💬 Chat-related elements:', JSON.stringify(chatInfo, null, 2));
        return chatInfo;
    }

    async testDirectInteraction() {
        console.log('\n🖱️  TESTING DIRECT INTERACTION');
        console.log('==============================');
        
        const inputElements = await this.findInputElements();
        
        for (const input of inputElements) {
            if (input.visible && !input.disabled) {
                console.log(`🎯 Testing interaction with: ${input.tagName} (${input.type})`);
                
                try {
                    if (input.type === 'shadow') {
                        // For shadow DOM elements, we need a different approach
                        const result = await this.page.evaluate((inputInfo) => {
                            const host = document.querySelector(inputInfo.host.toLowerCase());
                            if (host && host.shadowRoot) {
                                const shadowInput = host.shadowRoot.querySelector('textarea, input');
                                if (shadowInput) {
                                    shadowInput.focus();
                                    shadowInput.value = 'test message';
                                    shadowInput.dispatchEvent(new Event('input', { bubbles: true }));
                                    return {
                                        success: true,
                                        value: shadowInput.value,
                                        focused: document.activeElement === shadowInput
                                    };
                                }
                            }
                            return { success: false, reason: 'Shadow input not found' };
                        }, input);
                        
                        console.log(`🎯 Shadow DOM interaction result:`, result);
                    } else {
                        // Regular DOM element
                        const selector = input.id ? `#${input.id}` : 
                                       input.className ? `.${input.className.split(' ')[0]}` : 
                                       input.tagName.toLowerCase();
                        
                        await this.page.fill(selector, 'test message');
                        const value = await this.page.inputValue(selector);
                        console.log(`✅ Regular DOM interaction successful: "${value}"`);
                    }
                } catch (error) {
                    console.log(`❌ Interaction failed: ${error.message}`);
                }
            }
        }
    }

    async cleanup() {
        if (this.browser) {
            await this.browser.close();
        }
    }
}

// Main execution
async function main() {
    const inspector = new DOMInspector();
    
    try {
        await inspector.initialize();
        await inspector.loadAndInspect();
        await inspector.testDirectInteraction();
        
        console.log('\n🎯 DOM INSPECTION COMPLETE');
        console.log('===========================');
        console.log('✅ Full DOM structure analyzed');
        console.log('✅ Shadow DOM elements inspected');
        console.log('✅ Input elements catalogued');
        console.log('✅ Direct interaction tested');
        
        console.log('\n📋 NEXT STEPS:');
        console.log('1. Use findings to create targeted fixes');
        console.log('2. Focus on actual DOM structure found');
        console.log('3. Test with correct element selectors');
        
    } catch (error) {
        console.error(`❌ DOM inspection failed: ${error.message}`);
    } finally {
        await inspector.cleanup();
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = { DOMInspector };
