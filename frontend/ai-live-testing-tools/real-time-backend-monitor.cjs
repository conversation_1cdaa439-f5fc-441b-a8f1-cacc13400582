#!/usr/bin/env node

/**
 * Real-Time Backend Monitor
 * 
 * This script monitors backend logs in real-time during user journey tests
 * to provide immediate visibility into what's happening on the backend.
 */

const { spawn } = require('child_process');
const fs = require('fs');

class RealTimeBackendMonitor {
  constructor() {
    this.webLogs = [];
    this.celeryLogs = [];
    this.profileCompletionEvents = [];
    this.messageEvents = [];
    this.startTime = Date.now();
    this.isMonitoring = false;
  }

  async start() {
    console.log('🔍 Starting Real-Time Backend Monitor');
    console.log('════════════════════════════════════════════════════════════');
    console.log('This monitor will track:');
    console.log('  📊 Profile completion calculations');
    console.log('  💬 Message processing flow');
    console.log('  🔧 Tool execution details');
    console.log('  ⚠️  Errors and warnings');
    console.log('');
    
    this.isMonitoring = true;
    
    // Start monitoring web container logs
    this.startWebLogMonitoring();
    
    // Start monitoring celery container logs
    this.startCeleryLogMonitoring();
    
    // Keep the process alive
    process.on('SIGINT', () => {
      console.log('\n🛑 Stopping monitor...');
      this.stop();
      process.exit(0);
    });
    
    console.log('✅ Monitor started. Press Ctrl+C to stop.');
    console.log('📡 Listening for backend events...\n');
  }

  startWebLogMonitoring() {
    const webLogProcess = spawn('docker', ['logs', '-f', 'backend-web-1'], {
      stdio: ['ignore', 'pipe', 'pipe']
    });

    webLogProcess.stdout.on('data', (data) => {
      if (!this.isMonitoring) return;
      
      const logLine = data.toString().trim();
      this.processWebLogLine(logLine);
    });

    webLogProcess.stderr.on('data', (data) => {
      if (!this.isMonitoring) return;
      
      const logLine = data.toString().trim();
      this.processWebLogLine(logLine);
    });

    this.webLogProcess = webLogProcess;
  }

  startCeleryLogMonitoring() {
    const celeryLogProcess = spawn('docker', ['logs', '-f', 'backend-celery-1'], {
      stdio: ['ignore', 'pipe', 'pipe']
    });

    celeryLogProcess.stdout.on('data', (data) => {
      if (!this.isMonitoring) return;
      
      const logLine = data.toString().trim();
      this.processCeleryLogLine(logLine);
    });

    celeryLogProcess.stderr.on('data', (data) => {
      if (!this.isMonitoring) return;
      
      const logLine = data.toString().trim();
      this.processCeleryLogLine(logLine);
    });

    this.celeryLogProcess = celeryLogProcess;
  }

  processWebLogLine(logLine) {
    const timestamp = this.getRelativeTimestamp();
    
    // Profile completion events
    if (logLine.includes('Profile analysis completed') || 
        logLine.includes('completion_percentage') ||
        logLine.includes('profile_completion')) {
      console.log(`[${timestamp}] 📊 PROFILE: ${logLine}`);
      this.profileCompletionEvents.push({ timestamp, line: logLine });
    }
    
    // Message processing events
    else if (logLine.includes('Processing message for user') ||
             logLine.includes('Message classified') ||
             logLine.includes('chat_message')) {
      console.log(`[${timestamp}] 💬 MESSAGE: ${logLine}`);
      this.messageEvents.push({ timestamp, line: logLine });
    }
    
    // Tool execution events
    else if (logLine.includes('Executing tool:') ||
             logLine.includes('Tool') && logLine.includes('executed in')) {
      console.log(`[${timestamp}] 🔧 TOOL: ${logLine}`);
    }
    
    // Workflow events
    else if (logLine.includes('Launching workflow') ||
             logLine.includes('workflow_status') ||
             logLine.includes('Agent') && logLine.includes('process')) {
      console.log(`[${timestamp}] 🔄 WORKFLOW: ${logLine}`);
    }
    
    // Error events
    else if (logLine.includes('ERROR') || logLine.includes('Exception')) {
      console.log(`[${timestamp}] ❌ ERROR: ${logLine}`);
    }
    
    // Warning events
    else if (logLine.includes('WARNING') || logLine.includes('Warning')) {
      console.log(`[${timestamp}] ⚠️  WARNING: ${logLine}`);
    }
    
    // WebSocket events
    else if (logLine.includes('WebSocket') || logLine.includes('connection')) {
      console.log(`[${timestamp}] 🔌 WEBSOCKET: ${logLine}`);
    }
  }

  processCeleryLogLine(logLine) {
    const timestamp = this.getRelativeTimestamp();
    
    // Workflow execution in Celery
    if (logLine.includes('execute_graph_workflow') ||
        logLine.includes('discussion') ||
        logLine.includes('wheel_generation')) {
      console.log(`[${timestamp}] 🎯 CELERY-WORKFLOW: ${logLine}`);
    }
    
    // Celery errors
    else if (logLine.includes('ERROR') || logLine.includes('Exception')) {
      console.log(`[${timestamp}] ❌ CELERY-ERROR: ${logLine}`);
    }
  }

  getRelativeTimestamp() {
    const elapsed = Date.now() - this.startTime;
    const seconds = (elapsed / 1000).toFixed(1);
    return `${seconds}s`;
  }

  stop() {
    this.isMonitoring = false;
    
    if (this.webLogProcess) {
      this.webLogProcess.kill();
    }
    
    if (this.celeryLogProcess) {
      this.celeryLogProcess.kill();
    }
    
    this.generateSummary();
  }

  generateSummary() {
    console.log('\n📋 MONITORING SUMMARY');
    console.log('════════════════════════════════════════════════════════════');
    
    console.log(`📊 Profile Completion Events: ${this.profileCompletionEvents.length}`);
    this.profileCompletionEvents.forEach((event, index) => {
      console.log(`  ${index + 1}. [${event.timestamp}] ${event.line.substring(0, 100)}...`);
    });
    
    console.log(`\n💬 Message Processing Events: ${this.messageEvents.length}`);
    this.messageEvents.forEach((event, index) => {
      console.log(`  ${index + 1}. [${event.timestamp}] ${event.line.substring(0, 100)}...`);
    });
    
    console.log('\n🎯 KEY INSIGHTS:');
    
    // Analyze profile completion patterns
    const profileCompletionValues = this.profileCompletionEvents
      .map(event => {
        const match = event.line.match(/completion_percentage['":\s]*(\d+(?:\.\d+)?)/);
        return match ? parseFloat(match[1]) : null;
      })
      .filter(val => val !== null);
    
    if (profileCompletionValues.length > 0) {
      const uniqueValues = [...new Set(profileCompletionValues)];
      console.log(`  📈 Profile completion values seen: ${uniqueValues.join(', ')}`);
      
      if (uniqueValues.includes(1.0) && uniqueValues.includes(0.0)) {
        console.log('  ⚠️  INCONSISTENCY: Both 0% and 100% completion detected!');
      }
    }
    
    // Check for duplicate messages
    const messageCount = this.messageEvents.filter(e => 
      e.line.includes('chat_message') || e.line.includes('Processing message')
    ).length;
    
    if (messageCount > 2) {
      console.log(`  🚨 Potential duplicate processing: ${messageCount} message events`);
    }
  }
}

// Run the monitor
if (require.main === module) {
  const monitor = new RealTimeBackendMonitor();
  monitor.start().catch(console.error);
}
