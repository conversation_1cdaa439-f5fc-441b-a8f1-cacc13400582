/**
 * Progress Bar Issue Investigation Tool
 * 
 * This tool specifically tests the progress bar functionality during wheel generation
 * to identify why the progress bar is not showing up in the frontend.
 * 
 * Usage: node test-progress-bar-issue.cjs [port]
 * Example: node test-progress-bar-issue.cjs 3001
 */

const { chromium } = require('playwright');

class ProgressBarTester {
    constructor(port = 5173) {
        this.port = port;
        this.baseUrl = `http://localhost:${port}`;
        this.browser = null;
        this.page = null;
        this.progressUpdates = [];
        this.websocketMessages = [];
    }

    async initialize() {
        console.log('🚀 Initializing Progress Bar Tester...');
        
        this.browser = await chromium.launch({ 
            headless: false,
            devtools: true,
            args: ['--disable-web-security', '--disable-features=VizDisplayCompositor']
        });
        
        this.page = await this.browser.newPage();
        
        // Enable console logging
        this.page.on('console', msg => {
            const text = msg.text();
            if (text.includes('Progress') || text.includes('progress') || text.includes('📊') || text.includes('🔄')) {
                console.log(`📋 Console: ${text}`);
            }
        });

        // Monitor WebSocket messages
        this.page.on('websocket', ws => {
            console.log('🔌 WebSocket connection established');
            
            ws.on('framereceived', event => {
                try {
                    const data = JSON.parse(event.payload);
                    this.websocketMessages.push(data);
                    
                    if (data.type === 'progress_update') {
                        console.log('📊 Progress Update Received:', JSON.stringify(data, null, 2));
                        this.progressUpdates.push(data);
                    }
                    
                    if (data.type === 'workflow_progress') {
                        console.log('🔄 Workflow Progress Received:', JSON.stringify(data, null, 2));
                    }
                    
                    if (data.type === 'performance_metrics') {
                        console.log('⚡ Performance Metrics Received:', JSON.stringify(data, null, 2));
                    }
                } catch (e) {
                    // Not JSON, ignore
                }
            });
        });

        await this.page.goto(this.baseUrl);
        console.log(`✅ Navigated to ${this.baseUrl}`);

        // Force hard refresh to ensure latest code
        await this.page.reload({ waitUntil: 'networkidle' });
        console.log(`🔄 Hard refresh completed`);
    }

    async setupDebugMode() {
        console.log('🔧 Setting up debug mode...');
        
        // Open debug panel
        await this.page.keyboard.press('Control+Shift+D');
        await this.page.waitForTimeout(1000);
        
        // Select a user
        const userSelect = await this.page.locator('select[id*="user"], select[name*="user"], .debug-panel select').first();
        if (await userSelect.isVisible()) {
            await userSelect.selectOption({ index: 1 }); // Select first real user
            console.log('👤 User selected');
        }
        
        // Select LLM
        const llmSelect = await this.page.locator('select[id*="llm"], select[name*="llm"], .debug-panel select').nth(1);
        if (await llmSelect.isVisible()) {
            await llmSelect.selectOption('mistral-small-latest');
            console.log('🤖 LLM selected: mistral-small-latest');
        }
        
        // Apply settings
        const applyButton = await this.page.locator('button:has-text("Apply"), .debug-panel button').first();
        if (await applyButton.isVisible()) {
            await applyButton.click();
            console.log('✅ Debug settings applied');
        }
        
        await this.page.waitForTimeout(2000);
    }

    async monitorProgressBarElement() {
        console.log('👀 Monitoring progress bar element...');
        
        // Check if progress bar container exists
        const progressContainer = this.page.locator('.progress-bar-container');
        const progressBar = this.page.locator('real-time-progress-bar');
        
        const containerExists = await progressContainer.count() > 0;
        const progressBarExists = await progressBar.count() > 0;
        
        console.log(`📊 Progress container exists: ${containerExists}`);
        console.log(`📊 Progress bar component exists: ${progressBarExists}`);
        
        if (progressBarExists) {
            const isVisible = await progressBar.isVisible();
            console.log(`📊 Progress bar visible: ${isVisible}`);
            
            if (isVisible) {
                const boundingBox = await progressBar.boundingBox();
                console.log(`📊 Progress bar position:`, boundingBox);
            }
        }
        
        return { containerExists, progressBarExists };
    }

    async checkAppShellState() {
        console.log('🏠 Checking app-shell state...');
        
        // Check showProgressBar state
        const showProgressBar = await this.page.evaluate(() => {
            const appShell = document.querySelector('app-shell');
            if (appShell && appShell.shadowRoot) {
                // Try to access the showProgressBar property
                return appShell.showProgressBar || false;
            }
            return null;
        });
        
        console.log(`📊 showProgressBar state: ${showProgressBar}`);
        
        // Check if progress bar is in the DOM
        const progressBarInDOM = await this.page.evaluate(() => {
            const appShell = document.querySelector('app-shell');
            if (appShell && appShell.shadowRoot) {
                const progressBar = appShell.shadowRoot.querySelector('real-time-progress-bar');
                const progressContainer = appShell.shadowRoot.querySelector('.progress-bar-container');
                return {
                    progressBar: !!progressBar,
                    progressContainer: !!progressContainer,
                    progressBarVisible: progressBar ? !progressBar.hidden : false
                };
            }
            return null;
        });
        
        console.log(`📊 Progress bar in DOM:`, progressBarInDOM);
        
        return { showProgressBar, progressBarInDOM };
    }

    async sendWheelGenerationRequest() {
        console.log('🎯 Sending wheel generation request...');

        // Clear previous data
        this.progressUpdates = [];
        this.websocketMessages = [];

        // Wait for page to load completely
        await this.page.waitForTimeout(3000);

        // Try multiple selectors for chat input
        const chatSelectors = [
            'input[placeholder*="message"]',
            'textarea[placeholder*="message"]',
            '.chat-input input',
            '.chat-input textarea',
            'input[type="text"]',
            'textarea',
            '[contenteditable="true"]',
            '.message-input',
            '#message-input',
            'chat-interface input',
            'chat-interface textarea'
        ];

        let chatInput = null;
        for (const selector of chatSelectors) {
            try {
                const element = this.page.locator(selector).first();
                if (await element.isVisible({ timeout: 1000 })) {
                    chatInput = element;
                    console.log(`✅ Found chat input with selector: ${selector}`);
                    break;
                }
            } catch (e) {
                // Continue to next selector
            }
        }

        if (chatInput) {
            await chatInput.click();
            await chatInput.fill('make me a wheel');
            await this.page.keyboard.press('Enter');
            console.log('📤 Wheel generation request sent');
            
            // Monitor progress bar appearance
            let progressBarAppeared = false;
            let checkCount = 0;
            const maxChecks = 30; // 30 seconds
            
            while (checkCount < maxChecks && !progressBarAppeared) {
                await this.page.waitForTimeout(1000);
                checkCount++;
                
                const { progressBarInDOM } = await this.checkAppShellState();
                const { progressBarExists } = await this.monitorProgressBarElement();
                
                if (progressBarExists || (progressBarInDOM && progressBarInDOM.progressBar)) {
                    progressBarAppeared = true;
                    console.log(`✅ Progress bar appeared after ${checkCount} seconds`);
                    break;
                }
                
                if (checkCount % 5 === 0) {
                    console.log(`⏳ Still waiting for progress bar... (${checkCount}/${maxChecks})`);
                    console.log(`📊 Progress updates received: ${this.progressUpdates.length}`);
                    console.log(`📨 WebSocket messages received: ${this.websocketMessages.length}`);
                }
            }
            
            if (!progressBarAppeared) {
                console.log('❌ Progress bar never appeared');
            }
            
        } else {
            console.log('❌ Chat input not found');

            // Debug: Show what elements are available
            console.log('🔍 Available input elements:');
            const allInputs = await this.page.locator('input, textarea, [contenteditable]').all();
            for (let i = 0; i < allInputs.length; i++) {
                const input = allInputs[i];
                const tagName = await input.evaluate(el => el.tagName);
                const placeholder = await input.getAttribute('placeholder') || '';
                const id = await input.getAttribute('id') || '';
                const className = await input.getAttribute('class') || '';
                const isVisible = await input.isVisible();
                console.log(`  ${i+1}. ${tagName} - placeholder:"${placeholder}" id:"${id}" class:"${className}" visible:${isVisible}`);
            }
        }
    }

    async analyzeResults() {
        console.log('\n📊 ANALYSIS RESULTS:');
        console.log('='.repeat(50));
        
        console.log(`📨 Total WebSocket messages: ${this.websocketMessages.length}`);
        console.log(`📊 Progress updates received: ${this.progressUpdates.length}`);
        
        if (this.progressUpdates.length > 0) {
            console.log('\n📊 Progress Updates Details:');
            this.progressUpdates.forEach((update, index) => {
                console.log(`  ${index + 1}. Stage: ${update.data?.stage_name || 'Unknown'}`);
                console.log(`     Progress: ${update.data?.progress_percent || 0}%`);
                console.log(`     Message: ${update.data?.message || 'No message'}`);
                console.log(`     Tracker ID: ${update.data?.tracker_id || 'No ID'}`);
            });
        } else {
            console.log('❌ No progress updates received');
        }
        
        // Check for other relevant messages
        const relevantMessages = this.websocketMessages.filter(msg => 
            msg.type && (
                msg.type.includes('progress') || 
                msg.type.includes('workflow') || 
                msg.type.includes('status')
            )
        );
        
        if (relevantMessages.length > 0) {
            console.log('\n📨 Other Relevant Messages:');
            relevantMessages.forEach((msg, index) => {
                console.log(`  ${index + 1}. Type: ${msg.type}`);
                console.log(`     Data: ${JSON.stringify(msg.data || msg, null, 2).substring(0, 200)}...`);
            });
        }
        
        // Final state check
        const finalState = await this.checkAppShellState();
        console.log('\n🏠 Final App Shell State:', finalState);
        
        const finalProgressBar = await this.monitorProgressBarElement();
        console.log('📊 Final Progress Bar State:', finalProgressBar);
    }

    async cleanup() {
        if (this.browser) {
            await this.browser.close();
        }
    }

    async run() {
        try {
            await this.initialize();
            await this.setupDebugMode();
            
            // Initial state check
            console.log('\n🔍 Initial State Check:');
            await this.checkAppShellState();
            await this.monitorProgressBarElement();
            
            await this.sendWheelGenerationRequest();
            
            // Wait a bit more for any delayed updates
            await this.page.waitForTimeout(5000);
            
            await this.analyzeResults();
            
        } catch (error) {
            console.error('❌ Test failed:', error);
        } finally {
            await this.cleanup();
        }
    }
}

// Run the test
const port = process.argv[2] || 5173;
const tester = new ProgressBarTester(port);
tester.run().catch(console.error);
