#!/usr/bin/env node

/**
 * Complete Wheel ID Solution Test - End-to-end test of the robust wheel item ID solution
 * 
 * This test:
 * 1. Generates a wheel via WebSocket (gets workflow-generated IDs)
 * 2. Tests removal using those exact IDs
 * 3. Verifies the robust solution works with real workflow data
 */

const WebSocket = require('ws');

const CONFIG = {
  gameWebSocketUrl: 'ws://localhost:8000/ws/game/',
  apiBaseUrl: 'http://localhost:8000',
  testUserId: '2',
  testMessage: 'I want to do something creative and energizing for 1 hour. Generate me a wheel!'
};

class CompleteWheelIdSolutionTest {
  constructor() {
    this.ws = null;
    this.wheelData = null;
    this.testResults = {
      wheelGenerated: false,
      wheelItemsReceived: false,
      removalTested: false,
      removalSuccessful: false,
      errorMessage: null
    };
  }

  async runTest() {
    console.log('🧪 Complete Wheel ID Solution Test');
    console.log('===================================');
    console.log('Testing end-to-end wheel generation → removal with robust ID handling');
    
    try {
      // Step 1: Generate a wheel and get workflow IDs
      await this.generateWheel();
      
      // Step 2: Test removal with the actual workflow IDs
      if (this.wheelData && this.wheelData.wheel && this.wheelData.wheel.items.length > 0) {
        await this.testRemovalWithWorkflowIds();
      } else {
        console.log('❌ No wheel items to test removal');
      }
      
      return this.testResults;
    } catch (error) {
      console.error('💥 Test execution failed:', error);
      this.testResults.errorMessage = error.message;
      return this.testResults;
    }
  }

  async generateWheel() {
    console.log('📤 Step 1: Generating wheel via WebSocket...');
    
    return new Promise((resolve, reject) => {
      this.ws = new WebSocket(CONFIG.gameWebSocketUrl);
      
      this.ws.on('open', () => {
        console.log('✅ WebSocket connected');
        this.sendWheelRequest();
      });
      
      this.ws.on('message', (data) => {
        try {
          const message = JSON.parse(data.toString());
          this.handleMessage(message, resolve);
        } catch (e) {
          // Ignore non-JSON messages
        }
      });
      
      this.ws.on('error', (error) => {
        console.error(`❌ WebSocket error: ${error.message}`);
        reject(error);
      });
      
      this.ws.on('close', () => {
        console.log('🔌 WebSocket closed');
        if (!this.testResults.wheelGenerated) {
          reject(new Error('WebSocket closed without receiving wheel data'));
        }
      });
      
      // Timeout after 3 minutes (workflow can take up to 2 minutes)
      setTimeout(() => {
        if (this.ws.readyState === WebSocket.OPEN) {
          this.ws.close();
        }
        if (!this.testResults.wheelGenerated) {
          reject(new Error('Timeout waiting for wheel data'));
        }
      }, 180000);
    });
  }

  sendWheelRequest() {
    const message = {
      type: 'chat_message',
      content: {
        message: CONFIG.testMessage,
        user_profile_id: CONFIG.testUserId,
        timestamp: new Date().toISOString()
      }
    };
    
    this.ws.send(JSON.stringify(message));
    console.log('✅ Wheel generation request sent');
  }

  handleMessage(message, resolve) {
    if (message.type === 'wheel_data') {
      console.log('🎡 WHEEL DATA RECEIVED!');
      console.log(`📊 Wheel: ${message.wheel.name}`);
      console.log(`📊 Items: ${message.wheel.items.length}`);
      
      this.wheelData = message;
      this.testResults.wheelGenerated = true;
      this.testResults.wheelItemsReceived = true;
      
      // Log the workflow-generated IDs
      console.log('\n🔍 Workflow-generated wheel item IDs:');
      message.wheel.items.forEach((item, index) => {
        console.log(`   ${index + 1}. ${item.id} - ${item.name}`);
      });
      
      // Close WebSocket and proceed to removal test
      this.ws.close();
      resolve();
    }
  }

  async testRemovalWithWorkflowIds() {
    console.log('\n📤 Step 2: Testing removal with actual workflow-generated IDs...');
    
    const firstItem = this.wheelData.wheel.items[0];
    console.log(`🎯 Testing removal of: ${firstItem.id} (${firstItem.name})`);
    console.log(`🔧 This ID was generated by the workflow, not the database`);
    
    this.testResults.removalTested = true;
    
    try {
      const response = await fetch(`${CONFIG.apiBaseUrl}/api/wheel-items/${firstItem.id}/`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        }
      });
      
      console.log(`📡 API Response Status: ${response.status}`);
      
      if (response.ok) {
        const result = await response.json();
        console.log('✅ ROBUST SOLUTION SUCCESS!');
        console.log(`📊 Message: ${result.message}`);
        
        if (result.debug_info) {
          console.log(`📊 Requested ID: ${result.debug_info.requested_id}`);
          console.log(`📊 Database ID: ${result.debug_info.database_id}`);
          console.log(`📊 Removed item: ${result.debug_info.removed_item}`);
        }
        
        console.log(`📊 Updated wheel has ${result.wheel_data.segments.length} segments`);
        this.testResults.removalSuccessful = true;
        
        // Verify the correct item was removed
        const remainingItems = result.wheel_data.segments;
        const removedItemStillExists = remainingItems.some(item => 
          item.name === firstItem.name
        );
        
        if (!removedItemStillExists) {
          console.log(`✅ Confirmed: "${firstItem.name}" was successfully removed from wheel`);
        } else {
          console.log(`⚠️ Warning: "${firstItem.name}" still appears in wheel`);
        }
        
      } else {
        const errorText = await response.text();
        console.log('❌ REMOVAL FAILED!');
        console.log(`📊 Error: ${response.status} - ${errorText}`);
        this.testResults.errorMessage = `${response.status} - ${errorText}`;
      }
    } catch (error) {
      console.log('❌ REMOVAL FAILED!');
      console.log(`📊 Network Error: ${error.message}`);
      this.testResults.errorMessage = error.message;
    }
  }
}

// Run the test
if (require.main === module) {
  const test = new CompleteWheelIdSolutionTest();
  test.runTest()
    .then(results => {
      console.log('\n🏁 COMPLETE TEST RESULTS');
      console.log('========================');
      console.log(`🎡 Wheel generated: ${results.wheelGenerated}`);
      console.log(`📊 Wheel items received: ${results.wheelItemsReceived}`);
      console.log(`🧪 Removal tested: ${results.removalTested}`);
      console.log(`🎯 Removal successful: ${results.removalSuccessful}`);
      
      if (results.errorMessage) {
        console.log(`❌ Error: ${results.errorMessage}`);
      }
      
      const allTestsPassed = results.wheelGenerated && 
                           results.wheelItemsReceived && 
                           results.removalTested && 
                           results.removalSuccessful;
      
      if (allTestsPassed) {
        console.log('\n🎉 COMPLETE SOLUTION SUCCESS!');
        console.log('🎉 Wheel generation → workflow IDs → robust removal ALL WORKING!');
        console.log('🎉 The wheel item ID consistency issue is SOLVED!');
        process.exit(0);
      } else {
        console.log('\n💥 SOME TESTS FAILED - Solution needs more work');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('💥 Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = { CompleteWheelIdSolutionTest };
