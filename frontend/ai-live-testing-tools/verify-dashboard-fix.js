#!/usr/bin/env node

/**
 * Verify Dashboard Fix
 * 
 * Final verification that the dashboard message flow fix is working correctly
 */

import WebSocket from 'ws';

console.log('🎯 DASHBOARD FIX VERIFICATION');
console.log('=============================');
console.log('This script verifies that the dashboard message flow fix is working.\n');

console.log('📋 VERIFICATION STEPS:');
console.log('1. Open the admin dashboard in your browser');
console.log('2. Navigate to: http://localhost:8000/admin/admin_tools/connection_dashboard/');
console.log('3. Enable the "Message Inspector" toggle');
console.log('4. Watch for chat messages to appear in real-time\n');

console.log('🚀 Starting message flow test...\n');

const gameSocket = new WebSocket('ws://localhost:8000/ws/game/');

gameSocket.on('open', () => {
  console.log('✅ Connected to game WebSocket');
  console.log('📤 Sending test messages...\n');
  
  let messageCount = 0;
  
  const sendMessage = () => {
    messageCount++;
    const message = {
      type: 'chat_message',
      content: {
        message: `Dashboard verification message #${messageCount}`,
        user_profile_id: '2',
        timestamp: new Date().toISOString()
      }
    };
    
    console.log(`📨 Message ${messageCount}: "${message.content.message}"`);
    gameSocket.send(JSON.stringify(message));
    
    if (messageCount < 3) {
      setTimeout(sendMessage, 3000);
    } else {
      console.log('\n✅ All test messages sent!');
      console.log('\n🔍 CHECK THE DASHBOARD:');
      console.log('- Messages should appear in the Message Inspector');
      console.log('- Message type should be "chat_message" (not "message_flow")');
      console.log('- Content should be visible and readable');
      console.log('- Chat filter should show these messages');
      
      setTimeout(() => {
        gameSocket.close();
        console.log('\n🎯 VERIFICATION COMPLETE');
        console.log('If messages appeared correctly in the dashboard, the fix is working! ✅');
        process.exit(0);
      }, 5000);
    }
  };
  
  sendMessage();
});

gameSocket.on('error', (error) => {
  console.error('❌ Connection error:', error.message);
  process.exit(1);
});

// Auto-exit after 30 seconds
setTimeout(() => {
  console.log('\n⏰ Verification timeout');
  process.exit(1);
}, 30000);
