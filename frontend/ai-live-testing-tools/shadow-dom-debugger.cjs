#!/usr/bin/env node

/**
 * Shadow DOM Debugger - Check elements inside shadow DOM components
 */

const { chromium } = require('playwright');

async function debugShadowDOM() {
    console.log('🔍 Starting shadow DOM debug...');
    
    const browser = await chromium.launch({ headless: false });
    const page = await browser.newPage();
    
    // Load page
    console.log('🌐 Loading page...');
    await page.goto('http://localhost:3000/');
    await page.waitForTimeout(10000); // Wait for full initialization
    
    // Check shadow DOM elements
    const shadowDOMAnalysis = await page.evaluate(() => {
        const results = {
            appShell: null,
            chatInterface: null,
            textarea: null,
            form: null,
            sendButton: null
        };
        
        // Find app-shell
        const appShell = document.querySelector('app-shell');
        if (appShell && appShell.shadowRoot) {
            results.appShell = {
                exists: true,
                shadowRoot: true,
                children: appShell.shadowRoot.children.length
            };
            
            // Find chat-interface inside app-shell shadow root
            const chatInterface = appShell.shadowRoot.querySelector('chat-interface');
            if (chatInterface) {
                results.chatInterface = {
                    exists: true,
                    shadowRoot: !!chatInterface.shadowRoot,
                    connectionStatus: chatInterface.connectionStatus || 'unknown',
                    isProcessing: chatInterface.isProcessing || false,
                    messages: chatInterface.messages ? chatInterface.messages.length : 'unknown'
                };
                
                // Look inside chat-interface shadow root
                if (chatInterface.shadowRoot) {
                    const textarea = chatInterface.shadowRoot.querySelector('textarea');
                    const form = chatInterface.shadowRoot.querySelector('form');
                    const sendButton = chatInterface.shadowRoot.querySelector('.send-button');
                    const inputContainer = chatInterface.shadowRoot.querySelector('.input-container');
                    
                    results.textarea = textarea ? {
                        exists: true,
                        disabled: textarea.disabled,
                        placeholder: textarea.placeholder,
                        value: textarea.value,
                        className: textarea.className,
                        style: textarea.style.cssText
                    } : { exists: false };
                    
                    results.form = form ? {
                        exists: true,
                        action: form.action,
                        method: form.method
                    } : { exists: false };
                    
                    results.sendButton = sendButton ? {
                        exists: true,
                        disabled: sendButton.disabled,
                        textContent: sendButton.textContent,
                        className: sendButton.className
                    } : { exists: false };
                    
                    results.inputContainer = inputContainer ? {
                        exists: true,
                        children: inputContainer.children.length,
                        className: inputContainer.className
                    } : { exists: false };
                }
            } else {
                results.chatInterface = { exists: false };
            }
        } else {
            results.appShell = { exists: false };
        }
        
        return results;
    });
    
    console.log('\n📊 SHADOW DOM ANALYSIS:');
    console.log('════════════════════════════════════════════════════════════');
    
    console.log('\n🏠 App Shell:');
    if (shadowDOMAnalysis.appShell.exists) {
        console.log(`  ✅ Exists with shadow root`);
        console.log(`  Children: ${shadowDOMAnalysis.appShell.children}`);
    } else {
        console.log(`  ❌ Not found`);
    }
    
    console.log('\n💬 Chat Interface:');
    if (shadowDOMAnalysis.chatInterface.exists) {
        console.log(`  ✅ Exists`);
        console.log(`  Shadow Root: ${shadowDOMAnalysis.chatInterface.shadowRoot ? '✅ YES' : '❌ NO'}`);
        console.log(`  Connection Status: ${shadowDOMAnalysis.chatInterface.connectionStatus}`);
        console.log(`  Is Processing: ${shadowDOMAnalysis.chatInterface.isProcessing}`);
        console.log(`  Messages: ${shadowDOMAnalysis.chatInterface.messages}`);
    } else {
        console.log(`  ❌ Not found`);
    }
    
    console.log('\n📝 Textarea:');
    if (shadowDOMAnalysis.textarea.exists) {
        console.log(`  ✅ Exists`);
        console.log(`  Disabled: ${shadowDOMAnalysis.textarea.disabled ? '❌ YES' : '✅ NO'}`);
        console.log(`  Placeholder: "${shadowDOMAnalysis.textarea.placeholder}"`);
        console.log(`  Value: "${shadowDOMAnalysis.textarea.value}"`);
        console.log(`  Class: "${shadowDOMAnalysis.textarea.className}"`);
    } else {
        console.log(`  ❌ Not found in chat-interface shadow root`);
    }
    
    console.log('\n🔘 Send Button:');
    if (shadowDOMAnalysis.sendButton.exists) {
        console.log(`  ✅ Exists`);
        console.log(`  Disabled: ${shadowDOMAnalysis.sendButton.disabled ? '❌ YES' : '✅ NO'}`);
        console.log(`  Class: "${shadowDOMAnalysis.sendButton.className}"`);
    } else {
        console.log(`  ❌ Not found in chat-interface shadow root`);
    }
    
    console.log('\n📋 Form:');
    if (shadowDOMAnalysis.form.exists) {
        console.log(`  ✅ Exists`);
        console.log(`  Action: "${shadowDOMAnalysis.form.action}"`);
        console.log(`  Method: "${shadowDOMAnalysis.form.method}"`);
    } else {
        console.log(`  ❌ Not found (this is normal - chat uses button click, not form submit)`);
    }
    
    console.log('\n🔍 DIAGNOSIS:');
    if (shadowDOMAnalysis.chatInterface.exists && shadowDOMAnalysis.textarea.exists) {
        if (shadowDOMAnalysis.textarea.disabled) {
            console.log('❌ Textarea exists but is DISABLED');
            console.log(`💡 Connection status: ${shadowDOMAnalysis.chatInterface.connectionStatus}`);
            if (shadowDOMAnalysis.chatInterface.connectionStatus !== 'connected') {
                console.log('💡 Textarea disabled because connection status is not "connected"');
            }
        } else {
            console.log('✅ Textarea exists and is ENABLED - ready for input!');
        }
    } else if (!shadowDOMAnalysis.chatInterface.exists) {
        console.log('❌ Chat interface component not found');
    } else if (!shadowDOMAnalysis.textarea.exists) {
        console.log('❌ Textarea not found in chat interface shadow root');
    }
    
    await browser.close();
}

debugShadowDOM().catch(console.error);
