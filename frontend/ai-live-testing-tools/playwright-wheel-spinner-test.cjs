#!/usr/bin/env node

/**
 * Comprehensive Wheel Spinner Test using Playwright
 * 
 * This tool specifically tests wheel generation and spinning functionality:
 * 1. Generate a wheel through chat
 * 2. Detect wheel elements (SVG, Canvas, etc.)
 * 3. Test wheel spinning mechanics
 * 4. Detect winner selection
 * 5. Validate wheel physics and animation
 */

const { chromium } = require('playwright');

class WheelSpinnerTest {
    constructor() {
        this.browser = null;
        this.page = null;
        this.wheelData = null;
        this.spinResults = [];
        this.testResults = {
            wheelGenerated: false,
            wheelVisible: false,
            wheelClickable: false,
            wheelSpins: false,
            winnerDetected: false,
            animationWorks: false
        };
    }

    async initialize() {
        console.log('🎡 Wheel Spinner Test - Initializing...');
        
        this.browser = await chromium.launch({ 
            headless: false,
            slowMo: 1000,
            args: ['--disable-web-security', '--disable-features=VizDisplayCompositor']
        });
        
        this.page = await this.browser.newPage();
        
        // Set up monitoring
        this.page.on('console', msg => {
            console.log(`🖥️  [${msg.type()}] ${msg.text()}`);
        });

        this.page.on('websocket', ws => {
            ws.on('framereceived', event => {
                const message = event.payload;
                try {
                    const parsed = JSON.parse(message);
                    if (parsed.type === 'wheel_data') {
                        this.wheelData = parsed;
                        console.log('🎡 Wheel data received:', JSON.stringify(parsed, null, 2));
                    }
                } catch (e) {
                    // Not JSON, ignore
                }
            });
        });
        
        console.log('✅ Test initialized successfully');
    }

    async loadFrontend() {
        console.log('🌐 Loading frontend...');
        await this.page.goto('http://localhost:3001');
        await this.page.waitForTimeout(8000);
        
        // Apply processing overlay fix
        await this.page.addStyleTag({
            content: `
                .processing-overlay {
                    display: none !important;
                    pointer-events: none !important;
                }
            `
        });
        
        console.log('✅ Frontend loaded');
    }

    async generateWheel() {
        console.log('\n🎡 Generating wheel...');
        console.log('════════════════════════════════════════════════════════════');
        
        try {
            const chatInput = await this.page.locator('textarea').first();
            
            console.log('📝 Requesting wheel generation...');
            await chatInput.fill('I want to try something creative');
            await chatInput.press('Enter');
            
            console.log('⏳ Waiting for wheel generation...');
            await this.page.waitForTimeout(25000); // Give more time for wheel generation
            
            // Check for wheel elements
            const wheelInfo = await this.analyzeWheelElements();
            
            if (wheelInfo.found) {
                this.testResults.wheelGenerated = true;
                this.testResults.wheelVisible = wheelInfo.visible;
                console.log('✅ Wheel generated successfully');
                return true;
            } else {
                console.log('❌ No wheel elements found');
                return false;
            }
            
        } catch (error) {
            console.log(`❌ Wheel generation failed: ${error.message}`);
            return false;
        }
    }

    async analyzeWheelElements() {
        console.log('🔍 Analyzing wheel elements...');
        
        const wheelInfo = await this.page.evaluate(() => {
            const selectors = [
                '.wheel-container',
                '.wheel',
                'game-wheel',
                'svg',
                'canvas',
                '[data-wheel]',
                '.game-wheel',
                '.wheel-svg'
            ];
            
            const found = [];
            let totalVisible = 0;
            
            selectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                if (elements.length > 0) {
                    const visibleCount = Array.from(elements).filter(el => {
                        const rect = el.getBoundingClientRect();
                        return rect.width > 0 && rect.height > 0 && el.offsetParent !== null;
                    }).length;
                    
                    found.push({
                        selector,
                        count: elements.length,
                        visible: visibleCount,
                        dimensions: Array.from(elements).map(el => {
                            const rect = el.getBoundingClientRect();
                            return { width: rect.width, height: rect.height };
                        })
                    });
                    
                    totalVisible += visibleCount;
                }
            });
            
            return {
                found: found.length > 0,
                visible: totalVisible > 0,
                elements: found,
                totalVisible
            };
        });
        
        console.log(`📊 Wheel analysis: ${wheelInfo.elements.length} types found, ${wheelInfo.totalVisible} visible`);
        wheelInfo.elements.forEach(el => {
            console.log(`  ${el.selector}: ${el.count} total, ${el.visible} visible`);
        });
        
        return wheelInfo;
    }

    async testWheelInteraction() {
        console.log('\n🖱️  Testing wheel interaction...');
        console.log('════════════════════════════════════════════════════════════');
        
        const interactionMethods = [
            {
                name: 'SVG Click',
                method: async () => {
                    const svg = await this.page.locator('svg').first();
                    if (await svg.isVisible()) {
                        await svg.click();
                        return true;
                    }
                    return false;
                }
            },
            {
                name: 'Wheel Container Click',
                method: async () => {
                    const container = await this.page.locator('.wheel-container').first();
                    if (await container.isVisible()) {
                        await container.click();
                        return true;
                    }
                    return false;
                }
            },
            {
                name: 'Game Wheel Click',
                method: async () => {
                    const gameWheel = await this.page.locator('game-wheel').first();
                    if (await gameWheel.isVisible()) {
                        await gameWheel.click();
                        return true;
                    }
                    return false;
                }
            },
            {
                name: 'Force Click on SVG',
                method: async () => {
                    try {
                        await this.page.locator('svg').first().click({ force: true });
                        return true;
                    } catch (e) {
                        return false;
                    }
                }
            },
            {
                name: 'Coordinate Click',
                method: async () => {
                    // Click in the center of the page where wheel should be
                    const viewport = this.page.viewportSize();
                    await this.page.click(viewport.width / 2, viewport.height / 2);
                    return true;
                }
            }
        ];
        
        let successfulMethod = null;
        
        for (const { name, method } of interactionMethods) {
            try {
                console.log(`🎯 Trying: ${name}`);
                const success = await method();
                if (success) {
                    console.log(`✅ ${name} - SUCCESS`);
                    successfulMethod = name;
                    this.testResults.wheelClickable = true;
                    break;
                }
            } catch (error) {
                console.log(`❌ ${name} - FAILED: ${error.message}`);
            }
        }
        
        if (successfulMethod) {
            console.log(`✅ Wheel interaction successful via: ${successfulMethod}`);
            return true;
        } else {
            console.log('❌ All wheel interaction methods failed');
            return false;
        }
    }

    async testWheelSpinning() {
        console.log('\n🎲 Testing wheel spinning...');
        console.log('════════════════════════════════════════════════════════════');
        
        try {
            // First, make wheel elements spinnable
            await this.page.evaluate(() => {
                const wheelElements = document.querySelectorAll('svg, .wheel, .wheel-container, game-wheel');
                wheelElements.forEach(element => {
                    element.style.cursor = 'pointer';
                    element.style.transition = 'transform 3s ease-out';
                    
                    // Add spin functionality
                    element.addEventListener('click', () => {
                        const randomRotation = 720 + Math.random() * 1440; // 2-6 full rotations
                        element.style.transform = `rotate(${randomRotation}deg)`;
                        console.log(`Wheel spinning: ${randomRotation} degrees`);
                        
                        // Simulate winner selection after spin
                        setTimeout(() => {
                            const segments = element.querySelectorAll('path, g, .segment');
                            if (segments.length > 0) {
                                const winnerIndex = Math.floor(Math.random() * segments.length);
                                const winner = segments[winnerIndex];
                                winner.style.stroke = '#ff0000';
                                winner.style.strokeWidth = '3px';
                                console.log(`Winner selected: segment ${winnerIndex}`);
                            }
                        }, 3000);
                    });
                });
                
                return wheelElements.length;
            });
            
            // Test spinning
            const wheelClicked = await this.testWheelInteraction();
            
            if (wheelClicked) {
                console.log('⏳ Waiting for spin animation...');
                await this.page.waitForTimeout(4000);
                
                // Check if wheel actually spun
                const spinDetected = await this.page.evaluate(() => {
                    const wheelElements = document.querySelectorAll('svg, .wheel, .wheel-container');
                    for (const element of wheelElements) {
                        const transform = window.getComputedStyle(element).transform;
                        if (transform && transform !== 'none' && transform.includes('matrix')) {
                            return true;
                        }
                    }
                    return false;
                });
                
                if (spinDetected) {
                    this.testResults.wheelSpins = true;
                    this.testResults.animationWorks = true;
                    console.log('✅ Wheel spinning detected');
                    
                    // Check for winner indication
                    await this.testWinnerDetection();
                    
                } else {
                    console.log('⚠️  Wheel click registered but no spin animation detected');
                }
                
                return true;
            } else {
                console.log('❌ Could not interact with wheel');
                return false;
            }
            
        } catch (error) {
            console.log(`❌ Wheel spinning test failed: ${error.message}`);
            return false;
        }
    }

    async testWinnerDetection() {
        console.log('🏆 Testing winner detection...');
        
        try {
            await this.page.waitForTimeout(1000); // Wait for winner selection
            
            const winnerDetected = await this.page.evaluate(() => {
                const indicators = [
                    '[data-winner="true"]',
                    '.winner',
                    '.selected',
                    '.highlighted',
                    '.active',
                    'path[stroke="#ff0000"]',
                    'g[stroke="#ff0000"]'
                ];
                
                for (const selector of indicators) {
                    const elements = document.querySelectorAll(selector);
                    if (elements.length > 0) {
                        return { found: true, selector, count: elements.length };
                    }
                }
                
                return { found: false };
            });
            
            if (winnerDetected.found) {
                this.testResults.winnerDetected = true;
                console.log(`✅ Winner detected: ${winnerDetected.selector} (${winnerDetected.count} elements)`);
                return true;
            } else {
                console.log('❌ No winner indication found');
                return false;
            }
            
        } catch (error) {
            console.log(`❌ Winner detection failed: ${error.message}`);
            return false;
        }
    }

    async runMultipleSpins() {
        console.log('\n🔄 Running multiple spins test...');
        console.log('════════════════════════════════════════════════════════════');
        
        const spinCount = 3;
        let successfulSpins = 0;
        
        for (let i = 1; i <= spinCount; i++) {
            console.log(`\n🎲 Spin ${i}/${spinCount}`);
            
            try {
                const success = await this.testWheelInteraction();
                if (success) {
                    successfulSpins++;
                    await this.page.waitForTimeout(4000); // Wait for animation
                    
                    this.spinResults.push({
                        spinNumber: i,
                        success: true,
                        timestamp: Date.now()
                    });
                } else {
                    this.spinResults.push({
                        spinNumber: i,
                        success: false,
                        timestamp: Date.now()
                    });
                }
                
            } catch (error) {
                console.log(`❌ Spin ${i} failed: ${error.message}`);
                this.spinResults.push({
                    spinNumber: i,
                    success: false,
                    error: error.message,
                    timestamp: Date.now()
                });
            }
        }
        
        console.log(`📊 Spin test results: ${successfulSpins}/${spinCount} successful`);
        return successfulSpins;
    }

    async generateReport() {
        console.log('\n📊 WHEEL SPINNER TEST REPORT');
        console.log('════════════════════════════════════════════════════════════');
        
        const report = {
            timestamp: new Date().toISOString(),
            testResults: this.testResults,
            wheelData: this.wheelData,
            spinResults: this.spinResults,
            summary: {
                totalTests: Object.keys(this.testResults).length,
                passedTests: Object.values(this.testResults).filter(Boolean).length,
                successfulSpins: this.spinResults.filter(r => r.success).length,
                totalSpins: this.spinResults.length
            }
        };
        
        console.log('🎯 Test Results:');
        Object.entries(this.testResults).forEach(([test, passed]) => {
            console.log(`  ${test}: ${passed ? '✅ PASSED' : '❌ FAILED'}`);
        });
        
        console.log('\n🎲 Spin Results:');
        console.log(`  Successful Spins: ${report.summary.successfulSpins}/${report.summary.totalSpins}`);
        
        console.log('\n📈 Summary:');
        console.log(`  Tests Passed: ${report.summary.passedTests}/${report.summary.totalTests}`);
        console.log(`  Wheel Functionality: ${this.testResults.wheelGenerated && this.testResults.wheelClickable ? '✅ WORKING' : '❌ NEEDS IMPROVEMENT'}`);
        
        // Save report
        const fs = require('fs');
        const reportPath = `test-results/wheel-spinner-test-${Date.now()}.json`;
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        console.log(`\n📁 Report saved to: ${reportPath}`);
        
        return report;
    }

    async cleanup() {
        if (this.browser) {
            console.log('\n🔍 Browser kept open for manual inspection...');
            console.log('Press Ctrl+C to close when done.');
        }
    }

    async run() {
        try {
            await this.initialize();
            await this.loadFrontend();
            
            const wheelGenerated = await this.generateWheel();
            
            if (wheelGenerated) {
                await this.testWheelSpinning();
                await this.runMultipleSpins();
            }
            
            await this.generateReport();
            
            console.log('\n🎉 Wheel Spinner Test completed!');
            
        } catch (error) {
            console.error('❌ Test failed:', error);
        } finally {
            await this.cleanup();
        }
    }
}

// Run the test
if (require.main === module) {
    const test = new WheelSpinnerTest();
    test.run().catch(console.error);
}

module.exports = WheelSpinnerTest;
