#!/usr/bin/env node

/**
 * Test Status Bar and Complete Data Flow
 * 
 * This test validates:
 * 1. Status bar displays correct connection states (disconnected → connecting → connected)
 * 2. User information is displayed correctly with staff badge
 * 3. Button states change appropriately (Connecting... → Generate → SPIN!)
 * 4. Energy level and time available data flows to backend
 * 5. Complete end-to-end wheel generation workflow
 */

const puppeteer = require('puppeteer');
const path = require('path');

class StatusBarDataFlowTest {
  constructor(port = 3000) {
    this.port = port;
    this.browser = null;
    this.page = null;
    this.testResults = {};
  }

  async run() {
    console.log('🧪 Testing Status Bar and Complete Data Flow');
    console.log('=' * 60);

    try {
      // Launch browser
      this.browser = await puppeteer.launch({
        headless: false,
        defaultViewport: { width: 1400, height: 900 },
        args: ['--no-sandbox', '--disable-setuid-sandbox']
      });

      this.page = await this.browser.newPage();
      
      // Enable console logging
      this.page.on('console', msg => {
        if (msg.type() === 'error') {
          console.log('❌ Browser Error:', msg.text());
        } else if (msg.text().includes('🎯') || msg.text().includes('✅') || msg.text().includes('❌')) {
          console.log('📱 Frontend:', msg.text());
        }
      });

      // Navigate to frontend
      const url = `http://localhost:${this.port}`;
      console.log(`🌐 Navigating to ${url}`);
      await this.page.goto(url, { waitUntil: 'networkidle0' });

      // Wait for app to load
      await this.page.waitForSelector('app-shell', { timeout: 10000 });
      console.log('✅ App shell loaded');

      // Test sequence
      await this.testInitialConnectionState();
      await this.testStatusBarElements();
      await this.testUserInformation();
      await this.testButtonStates();
      await this.testEnergyTimeControls();
      await this.testCompleteDataFlow();

      console.log('\n📊 Test Results Summary:');
      for (const [test, result] of Object.entries(this.testResults)) {
        const status = result.success ? '✅' : '❌';
        console.log(`${status} ${test}: ${result.message}`);
      }

      const allPassed = Object.values(this.testResults).every(r => r.success);
      if (allPassed) {
        console.log('\n🎉 ALL TESTS PASSED - Status bar and data flow working correctly!');
        return true;
      } else {
        console.log('\n❌ SOME TESTS FAILED - Check results above');
        return false;
      }

    } catch (error) {
      console.error('❌ Test failed:', error);
      return false;
    } finally {
      if (this.browser) {
        await this.browser.close();
      }
    }
  }

  async testInitialConnectionState() {
    console.log('\n🔍 Testing Initial Connection State...');
    
    try {
      // Check for status indicator
      const statusIndicator = await this.page.waitForSelector('.status-indicator', { timeout: 5000 });
      const indicatorClass = await statusIndicator.getAttribute('class');
      
      // Should start as connecting or disconnected
      const isValidInitialState = indicatorClass.includes('connecting') || indicatorClass.includes('disconnected');
      
      this.testResults['initial_connection_state'] = {
        success: isValidInitialState,
        message: `Status indicator class: ${indicatorClass}`
      };

      console.log(`📊 Initial status indicator: ${indicatorClass}`);
      
    } catch (error) {
      this.testResults['initial_connection_state'] = {
        success: false,
        message: `Failed to find status indicator: ${error.message}`
      };
    }
  }

  async testStatusBarElements() {
    console.log('\n🔍 Testing Status Bar Elements...');
    
    try {
      // Check for status bar
      const statusBar = await this.page.waitForSelector('.status-bar', { timeout: 5000 });
      
      // Check for connection status
      const connectionStatus = await this.page.$('.connection-status');
      const hasConnectionStatus = !!connectionStatus;
      
      // Check for user info
      const userInfo = await this.page.$('.user-info');
      const hasUserInfo = !!userInfo;
      
      this.testResults['status_bar_elements'] = {
        success: hasConnectionStatus && hasUserInfo,
        message: `Connection status: ${hasConnectionStatus}, User info: ${hasUserInfo}`
      };

      console.log(`📊 Status bar elements - Connection: ${hasConnectionStatus}, User: ${hasUserInfo}`);
      
    } catch (error) {
      this.testResults['status_bar_elements'] = {
        success: false,
        message: `Failed to find status bar elements: ${error.message}`
      };
    }
  }

  async testUserInformation() {
    console.log('\n🔍 Testing User Information Display...');
    
    try {
      // Wait for user info to load
      await this.page.waitForSelector('.user-info', { timeout: 5000 });
      
      // Check user name
      const userName = await this.page.$eval('.user-name', el => el.textContent);
      const hasUserName = userName && userName.trim().length > 0;
      
      // Check for user icon button
      const userIconBtn = await this.page.$('.user-icon-btn');
      const hasUserIcon = !!userIconBtn;
      
      // Check for staff badge (if present)
      const staffBadge = await this.page.$('.staff-badge');
      const hasStaffBadge = !!staffBadge;
      
      this.testResults['user_information'] = {
        success: hasUserName && hasUserIcon,
        message: `User: ${userName}, Icon: ${hasUserIcon}, Staff: ${hasStaffBadge}`
      };

      console.log(`📊 User info - Name: "${userName}", Icon: ${hasUserIcon}, Staff: ${hasStaffBadge}`);
      
    } catch (error) {
      this.testResults['user_information'] = {
        success: false,
        message: `Failed to get user information: ${error.message}`
      };
    }
  }

  async testButtonStates() {
    console.log('\n🔍 Testing Button States...');
    
    try {
      // Wait for action button
      const actionButton = await this.page.waitForSelector('.action-button', { timeout: 5000 });
      
      // Check initial button text
      const initialText = await actionButton.textContent();
      console.log(`📊 Initial button text: "${initialText}"`);
      
      // Wait for connection to establish (button should change from "Connecting..." to "Generate")
      let finalText = initialText;
      let attempts = 0;
      const maxAttempts = 20; // 20 seconds max wait
      
      while (finalText.includes('Connecting') && attempts < maxAttempts) {
        await this.page.waitForTimeout(1000);
        finalText = await actionButton.textContent();
        attempts++;
      }
      
      console.log(`📊 Final button text: "${finalText}"`);
      
      // Check if button is enabled when connected
      const isDisabled = await actionButton.evaluate(el => el.disabled);
      
      this.testResults['button_states'] = {
        success: finalText === 'Generate' && !isDisabled,
        message: `Button text: "${finalText}", Disabled: ${isDisabled}`
      };
      
    } catch (error) {
      this.testResults['button_states'] = {
        success: false,
        message: `Failed to test button states: ${error.message}`
      };
    }
  }

  async testEnergyTimeControls() {
    console.log('\n🔍 Testing Energy and Time Controls...');
    
    try {
      // Find energy level slider
      const energySlider = await this.page.waitForSelector('input[type="range"]', { timeout: 5000 });
      
      // Set energy level to 80%
      await energySlider.click();
      await this.page.evaluate(() => {
        const sliders = document.querySelectorAll('input[type="range"]');
        if (sliders.length >= 2) {
          sliders[1].value = 80; // Energy level slider (second one)
          sliders[1].dispatchEvent(new Event('input', { bubbles: true }));
        }
      });
      
      // Set time available to 30 minutes
      await this.page.evaluate(() => {
        const sliders = document.querySelectorAll('input[type="range"]');
        if (sliders.length >= 1) {
          sliders[0].value = 30; // Time available slider (first one)
          sliders[0].dispatchEvent(new Event('input', { bubbles: true }));
        }
      });
      
      // Verify values are set
      const energyValue = await this.page.$eval('.potentiometer-value', el => el.textContent);
      
      this.testResults['energy_time_controls'] = {
        success: true, // If we got here without errors, controls are working
        message: `Energy/Time controls functional, sample value: ${energyValue}`
      };

      console.log(`📊 Energy/Time controls working, sample value: ${energyValue}`);
      
    } catch (error) {
      this.testResults['energy_time_controls'] = {
        success: false,
        message: `Failed to test energy/time controls: ${error.message}`
      };
    }
  }

  async testCompleteDataFlow() {
    console.log('\n🔍 Testing Complete Data Flow...');
    
    try {
      // Wait for Generate button to be ready
      await this.page.waitForFunction(() => {
        const button = document.querySelector('.action-button');
        return button && button.textContent === 'Generate' && !button.disabled;
      }, { timeout: 10000 });
      
      // Click Generate button
      const generateButton = await this.page.$('.action-button');
      await generateButton.click();
      
      console.log('📤 Clicked Generate button');
      
      // Wait for button text to change to "Generating..."
      await this.page.waitForFunction(() => {
        const button = document.querySelector('.action-button');
        return button && button.textContent.includes('Generating');
      }, { timeout: 5000 });
      
      console.log('📊 Button changed to "Generating..." - request sent');
      
      // Wait for wheel data to be generated (button should change to "SPIN!")
      let wheelGenerated = false;
      try {
        await this.page.waitForFunction(() => {
          const button = document.querySelector('.action-button');
          return button && button.textContent === 'SPIN!';
        }, { timeout: 60000 }); // 60 second timeout for wheel generation
        
        wheelGenerated = true;
        console.log('🎡 Wheel generated successfully - button changed to "SPIN!"');
        
      } catch (timeoutError) {
        console.log('⏰ Wheel generation timeout - checking if workflow is still running...');
      }
      
      this.testResults['complete_data_flow'] = {
        success: wheelGenerated,
        message: wheelGenerated ? 'Complete data flow successful' : 'Wheel generation in progress (async)'
      };
      
    } catch (error) {
      this.testResults['complete_data_flow'] = {
        success: false,
        message: `Failed to test complete data flow: ${error.message}`
      };
    }
  }
}

// Main execution
async function main() {
  const port = process.argv[2] || 3000;
  console.log(`🚀 Starting Status Bar and Data Flow Test on port ${port}`);
  
  const test = new StatusBarDataFlowTest(port);
  const success = await test.run();
  
  process.exit(success ? 0 : 1);
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = StatusBarDataFlowTest;
