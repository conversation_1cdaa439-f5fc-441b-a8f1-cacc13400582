#!/usr/bin/env python3

"""
Backend Domain Data Flow Test

Tests the backend wheel generation workflow to ensure:
1. User preferences (energy_level, time_available) are properly received
2. Activity domains are correctly assigned and diverse
3. Wheel items maintain domain information
4. Color assignment works correctly

Usage: python test-backend-domain-flow.py
"""

import asyncio
import json
import logging
import sys
import os
from typing import Dict, Any, List

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'backend'))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
import django
django.setup()

from apps.main.graphs.wheel_generation_graph import create_wheel_generation_graph, WheelGenerationState
from apps.main.agents.tools.activity_tools import _get_enhanced_default_activity_catalog, tailor_activity, assign_wheel_probabilities

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BackendDomainFlowTester:
    def __init__(self):
        self.test_results = {
            'user_preference_processing': False,
            'activity_catalog_diversity': False,
            'domain_assignment': False,
            'wheel_item_generation': False,
            'color_differentiation': False,
            'overall_success': False
        }
        self.test_data = {}

    async def test_user_preference_processing(self):
        """Test that user preferences are properly processed in the workflow"""
        print("\n🎯 Testing User Preference Processing...")
        
        try:
            # Create test context packet with user preferences
            context_packet = {
                "user_id": "test-user-123",
                "session_timestamp": "2025-01-27T14:35:00Z",
                "reported_mood": "focused",
                "reported_environment": "home",
                "user_input_context": {
                    "energy_level": 75,  # High energy
                    "time_available": 45  # 45 minutes
                }
            }
            
            # Create wheel generation state
            state = WheelGenerationState(
                user_profile_id="test-user-123",
                context_packet=context_packet,
                use_real_llm=False,
                use_real_tools=False,
                use_real_db=False
            )
            
            print(f"⚡ Energy Level: {context_packet['user_input_context']['energy_level']}%")
            print(f"🕒 Time Available: {context_packet['user_input_context']['time_available']} minutes")
            
            self.test_data['context_packet'] = context_packet
            self.test_results['user_preference_processing'] = True
            print("✅ User preference processing: SUCCESS")
            
        except Exception as e:
            print(f"❌ User preference processing: FAILED - {e}")
            self.test_results['user_preference_processing'] = False

    async def test_activity_catalog_diversity(self):
        """Test that the activity catalog provides diverse domains"""
        print("\n📚 Testing Activity Catalog Diversity...")
        
        try:
            # Test enhanced default activity catalog
            domains = ["creativity", "wellness", "physical", "learning", "social"]
            catalog = _get_enhanced_default_activity_catalog(domains=domains, limit=10)
            
            activities = catalog.get("candidate_activities", [])
            
            if not activities:
                print("❌ No activities returned from catalog")
                self.test_results['activity_catalog_diversity'] = False
                return
            
            # Analyze domain diversity
            domain_counts = {}
            for activity in activities:
                domain = activity.get("domain", "unknown")
                domain_counts[domain] = domain_counts.get(domain, 0) + 1
            
            print(f"📊 Activity catalog returned {len(activities)} activities")
            print("🎯 Domain distribution:")
            for domain, count in domain_counts.items():
                percentage = (count / len(activities)) * 100
                print(f"   {domain}: {count} activities ({percentage:.1f}%)")
            
            # Check for diversity (should have at least 3 different domains)
            unique_domains = len(domain_counts)
            has_diversity = unique_domains >= 3
            
            self.test_data['activity_catalog'] = {
                'activities': activities,
                'domain_counts': domain_counts,
                'unique_domains': unique_domains
            }
            
            self.test_results['activity_catalog_diversity'] = has_diversity
            
            if has_diversity:
                print(f"✅ Activity catalog diversity: SUCCESS ({unique_domains} unique domains)")
            else:
                print(f"❌ Activity catalog diversity: FAILED (only {unique_domains} domains)")
                
        except Exception as e:
            print(f"❌ Activity catalog diversity: FAILED - {e}")
            self.test_results['activity_catalog_diversity'] = False

    async def test_domain_assignment(self):
        """Test that activity tailoring preserves and assigns domains correctly"""
        print("\n🎯 Testing Domain Assignment in Activity Tailoring...")
        
        try:
            if 'activity_catalog' not in self.test_data:
                print("❌ No activity catalog data available")
                self.test_results['domain_assignment'] = False
                return
            
            activities = self.test_data['activity_catalog']['activities']
            context_packet = self.test_data.get('context_packet', {})
            
            # Test tailoring for a few activities
            tailored_activities = []
            
            for i, activity in enumerate(activities[:5]):  # Test first 5 activities
                try:
                    activity_id = activity.get("id", f"test-activity-{i}")
                    original_domain = activity.get("domain", "unknown")
                    
                    print(f"🔧 Tailoring activity: {activity_id} (domain: {original_domain})")
                    
                    # Call tailor_activity function
                    result = await tailor_activity(
                        user_profile_id="test-user-123",
                        generic_activity_id=activity_id,
                        resource_context={},
                        context_packet=context_packet,
                        activity_index=i
                    )
                    
                    tailored_activity = result.get("tailored_activity", {})
                    tailored_domain = tailored_activity.get("domain", "unknown")
                    
                    print(f"   Original domain: {original_domain} → Tailored domain: {tailored_domain}")
                    
                    tailored_activities.append({
                        'original': activity,
                        'tailored': tailored_activity,
                        'original_domain': original_domain,
                        'tailored_domain': tailored_domain
                    })
                    
                except Exception as e:
                    print(f"   ⚠️ Failed to tailor activity {activity_id}: {e}")
                    continue
            
            # Analyze domain preservation
            domain_preserved = 0
            domain_changed = 0
            
            for item in tailored_activities:
                if item['original_domain'] == item['tailored_domain']:
                    domain_preserved += 1
                else:
                    domain_changed += 1
            
            print(f"\n📊 Domain assignment results:")
            print(f"   Domain preserved: {domain_preserved}")
            print(f"   Domain changed: {domain_changed}")
            print(f"   Total tailored: {len(tailored_activities)}")
            
            # Success if we have tailored activities and domains are reasonable
            has_tailored_activities = len(tailored_activities) > 0
            reasonable_domain_handling = domain_preserved >= domain_changed
            
            self.test_data['tailored_activities'] = tailored_activities
            self.test_results['domain_assignment'] = has_tailored_activities and reasonable_domain_handling
            
            if self.test_results['domain_assignment']:
                print("✅ Domain assignment: SUCCESS")
            else:
                print("❌ Domain assignment: FAILED")
                
        except Exception as e:
            print(f"❌ Domain assignment: FAILED - {e}")
            self.test_results['domain_assignment'] = False

    async def test_wheel_item_generation(self):
        """Test that wheel items are generated with proper domain information"""
        print("\n🎡 Testing Wheel Item Generation...")
        
        try:
            if 'tailored_activities' not in self.test_data:
                print("❌ No tailored activities data available")
                self.test_results['wheel_item_generation'] = False
                return
            
            tailored_activities = [item['tailored'] for item in self.test_data['tailored_activities']]
            
            # Test wheel probability assignment
            result = await assign_wheel_probabilities(
                user_profile_id="test-user-123",
                activities=tailored_activities,
                domain_distribution={
                    "creativity": 25,
                    "wellness": 25,
                    "physical": 25,
                    "learning": 25
                }
            )
            
            wheel_items = result.get("wheel_items", [])
            
            if not wheel_items:
                print("❌ No wheel items generated")
                self.test_results['wheel_item_generation'] = False
                return
            
            print(f"🎡 Generated {len(wheel_items)} wheel items")
            
            # Analyze wheel item domains
            wheel_domain_counts = {}
            for item in wheel_items:
                domain = item.get("domain", "unknown")
                wheel_domain_counts[domain] = wheel_domain_counts.get(domain, 0) + 1
                
                print(f"   Item: {item.get('title', 'Unknown')} - Domain: {domain} - Probability: {item.get('probability', 0):.2f}")
            
            print(f"\n📊 Wheel item domain distribution:")
            for domain, count in wheel_domain_counts.items():
                percentage = (count / len(wheel_items)) * 100
                print(f"   {domain}: {count} items ({percentage:.1f}%)")
            
            # Success if we have wheel items with domain diversity
            has_wheel_items = len(wheel_items) > 0
            has_domain_diversity = len(wheel_domain_counts) > 1
            
            self.test_data['wheel_items'] = wheel_items
            self.test_data['wheel_domain_counts'] = wheel_domain_counts
            
            self.test_results['wheel_item_generation'] = has_wheel_items and has_domain_diversity
            
            if self.test_results['wheel_item_generation']:
                print(f"✅ Wheel item generation: SUCCESS ({len(wheel_domain_counts)} unique domains)")
            else:
                print("❌ Wheel item generation: FAILED")
                
        except Exception as e:
            print(f"❌ Wheel item generation: FAILED - {e}")
            self.test_results['wheel_item_generation'] = False

    async def test_color_differentiation(self):
        """Test that wheel items have different colors based on domains"""
        print("\n🎨 Testing Color Differentiation...")
        
        try:
            if 'wheel_items' not in self.test_data:
                print("❌ No wheel items data available")
                self.test_results['color_differentiation'] = False
                return
            
            wheel_items = self.test_data['wheel_items']
            
            # Analyze colors
            color_counts = {}
            domain_color_mapping = {}
            
            for item in wheel_items:
                color = item.get("color", "#CCCCCC")
                domain = item.get("domain", "unknown")
                
                color_counts[color] = color_counts.get(color, 0) + 1
                
                if domain not in domain_color_mapping:
                    domain_color_mapping[domain] = set()
                domain_color_mapping[domain].add(color)
                
                print(f"   {item.get('title', 'Unknown')}: {color} (domain: {domain})")
            
            print(f"\n🌈 Color distribution:")
            for color, count in color_counts.items():
                percentage = (count / len(wheel_items)) * 100
                print(f"   {color}: {count} items ({percentage:.1f}%)")
            
            print(f"\n🎯 Domain-color mapping:")
            for domain, colors in domain_color_mapping.items():
                print(f"   {domain}: {list(colors)}")
            
            # Success if we have color diversity (not all the same color)
            unique_colors = len(color_counts)
            has_color_diversity = unique_colors > 1
            
            self.test_results['color_differentiation'] = has_color_diversity
            
            if has_color_diversity:
                print(f"✅ Color differentiation: SUCCESS ({unique_colors} unique colors)")
            else:
                print("❌ Color differentiation: FAILED (all items have same color)")
                
        except Exception as e:
            print(f"❌ Color differentiation: FAILED - {e}")
            self.test_results['color_differentiation'] = False

    def generate_report(self):
        """Generate comprehensive test report"""
        print("\n📋 BACKEND DOMAIN DATA FLOW TEST REPORT")
        print("=" * 60)
        
        results = self.test_results
        passed_tests = sum(1 for result in results.values() if result and isinstance(result, bool))
        total_tests = len([k for k, v in results.items() if k != 'overall_success'])
        
        overall_success = passed_tests >= 4  # At least 4/5 tests pass
        
        print(f"\n🎯 Test Results ({passed_tests}/{total_tests} passed):")
        print(f"   User Preference Processing: {'✅ PASS' if results['user_preference_processing'] else '❌ FAIL'}")
        print(f"   Activity Catalog Diversity: {'✅ PASS' if results['activity_catalog_diversity'] else '❌ FAIL'}")
        print(f"   Domain Assignment: {'✅ PASS' if results['domain_assignment'] else '❌ FAIL'}")
        print(f"   Wheel Item Generation: {'✅ PASS' if results['wheel_item_generation'] else '❌ FAIL'}")
        print(f"   Color Differentiation: {'✅ PASS' if results['color_differentiation'] else '❌ FAIL'}")
        
        print(f"\n🏆 Overall Result: {'✅ SUCCESS' if overall_success else '❌ FAILURE'}")
        
        if not overall_success:
            print("\n🔍 Issues Identified:")
            if not results['user_preference_processing']:
                print("   - User preferences not properly processed in backend workflow")
            if not results['activity_catalog_diversity']:
                print("   - Activity catalog lacks domain diversity")
            if not results['domain_assignment']:
                print("   - Domain assignment in activity tailoring is failing")
            if not results['wheel_item_generation']:
                print("   - Wheel item generation not preserving domain information")
            if not results['color_differentiation']:
                print("   - All wheel items have identical colors")
        
        self.test_results['overall_success'] = overall_success
        return self.test_results

    async def run(self):
        """Run all tests"""
        try:
            await self.test_user_preference_processing()
            await self.test_activity_catalog_diversity()
            await self.test_domain_assignment()
            await self.test_wheel_item_generation()
            await self.test_color_differentiation()
            return self.generate_report()
        except Exception as e:
            print(f"❌ Test execution failed: {e}")
            return {'overall_success': False, 'error': str(e)}

async def main():
    print("🚀 Starting Backend Domain Data Flow Test")
    
    tester = BackendDomainFlowTester()
    results = await tester.run()
    
    return 0 if results['overall_success'] else 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
