#!/usr/bin/env node

/**
 * Playwright WebSocket Debugger
 * 
 * Advanced WebSocket debugging tool using <PERSON><PERSON>'s WebSocket routing capabilities.
 * This tool can intercept, mock, and analyze WebSocket traffic in real browser environments.
 */

const { chromium } = require('playwright');
const fs = require('fs');

class PlaywrightWebSocketDebugger {
    constructor(options = {}) {
        this.options = {
            headless: false,
            slowMo: 500,
            baseURL: 'http://localhost:3000',
            mockMode: false, // Set to true to mock WebSocket responses
            ...options
        };
        
        this.browser = null;
        this.context = null;
        this.page = null;
        this.wsConnections = new Map();
        this.messageLog = [];
        this.mockResponses = new Map();
    }

    async initialize() {
        console.log('🔧 Initializing Playwright WebSocket Debugger...');
        
        this.browser = await chromium.launch({
            headless: this.options.headless,
            slowMo: this.options.slowMo,
            devtools: true // Open DevTools for additional debugging
        });

        this.context = await this.browser.newContext({
            viewport: { width: 1280, height: 720 }
        });

        this.page = await this.context.newPage();
        
        // Enable console logging
        this.page.on('console', msg => {
            if (msg.type() === 'error') {
                console.log(`🔴 Console Error: ${msg.text()}`);
            } else {
                console.log(`🖥️  Console: ${msg.text()}`);
            }
        });

        await this.setupWebSocketRouting();
        console.log('✅ Debugger initialized');
    }

    async setupWebSocketRouting() {
        console.log('🔌 Setting up WebSocket routing...');
        
        // Setup mock responses for testing
        this.setupMockResponses();
        
        await this.page.routeWebSocket('**/ws/**', ws => {
            const wsId = `ws_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            const wsInfo = {
                id: wsId,
                url: ws.url(),
                connected: false,
                messageCount: 0,
                startTime: new Date()
            };
            
            this.wsConnections.set(wsId, wsInfo);
            console.log(`🔗 WebSocket intercepted: ${ws.url()} (ID: ${wsId})`);

            if (this.options.mockMode) {
                // Mock mode - don't connect to server
                this.setupMockWebSocket(ws, wsId);
            } else {
                // Debug mode - connect to server and intercept
                this.setupDebugWebSocket(ws, wsId);
            }
        });
    }

    setupMockResponses() {
        // Define mock responses for different message types
        this.mockResponses.set('chat_message', {
            type: 'ai_response',
            content: {
                message: 'This is a mocked AI response for testing purposes.',
                timestamp: new Date().toISOString()
            }
        });

        this.mockResponses.set('wheel_request', {
            type: 'wheel_data',
            content: {
                segments: [
                    { name: 'Mock Activity 1', probability: 0.3, color: '#FF6B6B' },
                    { name: 'Mock Activity 2', probability: 0.4, color: '#4ECDC4' },
                    { name: 'Mock Activity 3', probability: 0.3, color: '#45B7D1' }
                ],
                total_probability: 1.0
            }
        });
    }

    setupMockWebSocket(ws, wsId) {
        console.log(`🎭 Setting up mock WebSocket: ${wsId}`);
        
        ws.onMessage(message => {
            this.logMessage(wsId, 'received', message);
            
            try {
                const parsedMessage = JSON.parse(message);
                const mockResponse = this.generateMockResponse(parsedMessage);
                
                if (mockResponse) {
                    setTimeout(() => {
                        ws.send(JSON.stringify(mockResponse));
                        this.logMessage(wsId, 'sent_mock', JSON.stringify(mockResponse));
                    }, 1000); // Simulate network delay
                }
            } catch (error) {
                console.log(`⚠️  Could not parse message as JSON: ${message}`);
            }
        });

        // Simulate connection established
        setTimeout(() => {
            const wsInfo = this.wsConnections.get(wsId);
            wsInfo.connected = true;
            console.log(`✅ Mock WebSocket connected: ${wsId}`);
        }, 500);
    }

    setupDebugWebSocket(ws, wsId) {
        console.log(`🔍 Setting up debug WebSocket: ${wsId}`);
        
        // Connect to actual server
        const server = ws.connectToServer();
        
        // Intercept messages from page to server
        ws.onMessage(message => {
            this.logMessage(wsId, 'page_to_server', message);
            
            // Forward to server (with optional modification)
            const modifiedMessage = this.modifyOutgoingMessage(message);
            server.send(modifiedMessage);
            
            if (modifiedMessage !== message) {
                console.log(`🔄 Message modified: ${message} -> ${modifiedMessage}`);
            }
        });

        // Intercept messages from server to page
        server.onMessage(message => {
            this.logMessage(wsId, 'server_to_page', message);
            
            // Forward to page (with optional modification)
            const modifiedMessage = this.modifyIncomingMessage(message);
            ws.send(modifiedMessage);
            
            if (modifiedMessage !== message) {
                console.log(`🔄 Response modified: ${message} -> ${modifiedMessage}`);
            }
        });

        // Handle connection events
        server.onClose(() => {
            const wsInfo = this.wsConnections.get(wsId);
            wsInfo.connected = false;
            console.log(`❌ WebSocket closed: ${wsId}`);
        });

        // Mark as connected
        const wsInfo = this.wsConnections.get(wsId);
        wsInfo.connected = true;
        console.log(`✅ Debug WebSocket connected: ${wsId}`);
    }

    generateMockResponse(message) {
        // Generate appropriate mock responses based on message type
        if (message.type === 'chat_message') {
            return this.mockResponses.get('chat_message');
        } else if (message.type === 'wheel_request' || 
                   (message.content && message.content.message && 
                    message.content.message.includes('exercise'))) {
            return this.mockResponses.get('wheel_request');
        }
        
        // Default response
        return {
            type: 'debug_response',
            content: {
                message: `Mock response for: ${JSON.stringify(message)}`,
                timestamp: new Date().toISOString()
            }
        };
    }

    modifyOutgoingMessage(message) {
        // Add debugging information to outgoing messages
        try {
            const parsed = JSON.parse(message);
            parsed._debug = {
                intercepted: true,
                timestamp: new Date().toISOString(),
                direction: 'outgoing'
            };
            return JSON.stringify(parsed);
        } catch {
            return message; // Return as-is if not JSON
        }
    }

    modifyIncomingMessage(message) {
        // Add debugging information to incoming messages
        try {
            const parsed = JSON.parse(message);
            parsed._debug = {
                intercepted: true,
                timestamp: new Date().toISOString(),
                direction: 'incoming'
            };
            return JSON.stringify(parsed);
        } catch {
            return message; // Return as-is if not JSON
        }
    }

    logMessage(wsId, direction, message) {
        const logEntry = {
            wsId,
            direction,
            message,
            timestamp: new Date().toISOString()
        };
        
        this.messageLog.push(logEntry);
        
        const wsInfo = this.wsConnections.get(wsId);
        wsInfo.messageCount++;
        
        const directionEmoji = {
            'received': '📨',
            'sent_mock': '🎭',
            'page_to_server': '📤',
            'server_to_page': '📥'
        };
        
        console.log(`${directionEmoji[direction] || '📋'} [${wsId}] ${direction}: ${message}`);
    }

    async navigateAndTest(url = null) {
        const targetUrl = url || this.options.baseURL;
        console.log(`🌐 Navigating to: ${targetUrl}`);
        
        await this.page.goto(targetUrl);
        await this.page.waitForLoadState('networkidle');
        
        // Wait a bit for WebSocket connections to establish
        await this.page.waitForTimeout(3000);
        
        console.log('📊 WebSocket Connections:');
        for (const [id, info] of this.wsConnections) {
            console.log(`  ${info.connected ? '✅' : '❌'} ${id}: ${info.url} (${info.messageCount} messages)`);
        }
    }

    async sendTestMessage(message = "I'm bored") {
        console.log(`💬 Sending test message: "${message}"`);
        
        try {
            // Try to find chat input
            const chatInput = await this.page.locator('input[type="text"], textarea').first();
            await chatInput.fill(message);
            await chatInput.press('Enter');
            
            // Wait for response
            await this.page.waitForTimeout(5000);
            
            console.log('✅ Test message sent');
        } catch (error) {
            console.error('❌ Failed to send test message:', error.message);
        }
    }

    async generateDebugReport() {
        const report = {
            timestamp: new Date().toISOString(),
            options: this.options,
            connections: Object.fromEntries(this.wsConnections),
            messageLog: this.messageLog,
            summary: {
                totalConnections: this.wsConnections.size,
                totalMessages: this.messageLog.length,
                activeConnections: Array.from(this.wsConnections.values()).filter(c => c.connected).length
            }
        };

        const reportPath = `./test-results/playwright-websocket-debug-${Date.now()}.json`;
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        
        console.log('\n📊 Debug Report:');
        console.log(`📁 Report saved to: ${reportPath}`);
        console.log(`🔗 Total Connections: ${report.summary.totalConnections}`);
        console.log(`📨 Total Messages: ${report.summary.totalMessages}`);
        console.log(`✅ Active Connections: ${report.summary.activeConnections}`);
        
        return report;
    }

    async cleanup() {
        console.log('🧹 Cleaning up...');
        if (this.page) await this.page.close();
        if (this.context) await this.context.close();
        if (this.browser) await this.browser.close();
    }
}

// CLI interface
async function main() {
    const args = process.argv.slice(2);
    const mockMode = args.includes('--mock');
    const headless = args.includes('--headless');
    const url = args.find(arg => arg.startsWith('--url='))?.split('=')[1];

    console.log('🚀 Starting Playwright WebSocket Debugger...');
    console.log(`🎭 Mock Mode: ${mockMode ? 'ON' : 'OFF'}`);
    console.log(`👁️  Headless: ${headless ? 'ON' : 'OFF'}`);

    const debugger = new PlaywrightWebSocketDebugger({
        mockMode,
        headless,
        baseURL: url || 'http://localhost:3000'
    });

    try {
        await debugger.initialize();
        await debugger.navigateAndTest();
        
        // Send test message
        await debugger.sendTestMessage("I'm bored");
        
        // Wait for interactions
        console.log('\n⏳ Waiting 30 seconds for WebSocket activity...');
        console.log('💡 You can interact with the page in the browser window');
        await debugger.page.waitForTimeout(30000);
        
        // Generate report
        await debugger.generateDebugReport();
        
    } catch (error) {
        console.error('❌ Debugger failed:', error.message);
    } finally {
        await debugger.cleanup();
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = PlaywrightWebSocketDebugger;
