#!/usr/bin/env node

/**
 * Comprehensive Dashboard End-to-End Test
 * 
 * This test validates the complete flow from backend WebSocket consumers
 * to the admin dashboard UI, ensuring all fixes work properly.
 */

const WebSocket = require('ws');
const http = require('http');

class ComprehensiveDashboardTest {
    constructor() {
        this.adminSocket = null;
        this.gameSocket = null;
        this.testResults = {
            backendHealth: false,
            adminConnection: false,
            gameConnection: false,
            messageInspector: false,
            sessionMonitoring: false,
            uiIntegration: false
        };
        this.receivedMessages = [];
        this.errors = [];
        this.startTime = Date.now();
    }

    async runComprehensiveTest() {
        console.log('🚀 Comprehensive Dashboard End-to-End Test');
        console.log('==========================================\n');

        try {
            await this.testBackendHealth();
            await this.testWebSocketConnections();
            await this.testMessageInspectorFlow();
            await this.testSessionMonitoringFlow();
            await this.testUIIntegration();
            await this.generateComprehensiveReport();
        } catch (error) {
            console.error('❌ Test suite failed:', error.message);
            this.errors.push(`Test suite failure: ${error.message}`);
        } finally {
            await this.cleanup();
        }
    }

    async testBackendHealth() {
        console.log('🔍 Step 1: Testing Backend Health');
        
        return new Promise((resolve) => {
            const req = http.request({
                hostname: 'localhost',
                port: 8000,
                path: '/admin/',
                method: 'GET',
                timeout: 5000
            }, (res) => {
                console.log(`✅ Backend responding: HTTP ${res.statusCode}`);
                this.testResults.backendHealth = true;
                resolve();
            });

            req.on('error', (error) => {
                console.log(`❌ Backend health check failed: ${error.message}`);
                this.errors.push(`Backend not accessible: ${error.message}`);
                resolve();
            });

            req.on('timeout', () => {
                console.log('❌ Backend health check timeout');
                this.errors.push('Backend health check timeout');
                req.destroy();
                resolve();
            });

            req.end();
        });
    }

    async testWebSocketConnections() {
        console.log('\n🔍 Step 2: Testing WebSocket Connections');
        
        // Test admin dashboard connection
        await this.connectToAdmin();
        
        // Test game WebSocket connection
        await this.connectToGame();
        
        // Wait for initial handshakes
        await this.sleep(2000);
    }

    async connectToAdmin() {
        console.log('📡 Connecting to Admin Dashboard...');
        
        return new Promise((resolve) => {
            this.adminSocket = new WebSocket('ws://localhost:8000/ws/connection-monitor/');
            
            this.adminSocket.on('open', () => {
                console.log('✅ Admin dashboard connected');
                this.testResults.adminConnection = true;
                
                this.adminSocket.on('message', (data) => {
                    try {
                        const message = JSON.parse(data);
                        this.receivedMessages.push({
                            source: 'admin',
                            timestamp: Date.now(),
                            message: message
                        });
                        console.log(`📨 Admin: ${message.type} ${message.data ? `(${Object.keys(message.data).join(',')})` : ''}`);
                    } catch (error) {
                        console.log(`📨 Admin: [non-JSON] ${data.toString().substring(0, 50)}...`);
                    }
                });
                
                resolve();
            });
            
            this.adminSocket.on('error', (error) => {
                console.log(`❌ Admin connection failed: ${error.message}`);
                this.errors.push(`Admin connection failed: ${error.message}`);
                resolve();
            });
            
            setTimeout(() => {
                if (this.adminSocket.readyState !== WebSocket.OPEN) {
                    this.errors.push('Admin connection timeout');
                    console.log('❌ Admin connection timeout');
                }
                resolve();
            }, 5000);
        });
    }

    async connectToGame() {
        console.log('🎮 Connecting to Game WebSocket...');
        
        return new Promise((resolve) => {
            this.gameSocket = new WebSocket('ws://localhost:8000/ws/game/');
            
            this.gameSocket.on('open', () => {
                console.log('✅ Game WebSocket connected');
                this.testResults.gameConnection = true;
                
                this.gameSocket.on('message', (data) => {
                    try {
                        const message = JSON.parse(data);
                        this.receivedMessages.push({
                            source: 'game',
                            timestamp: Date.now(),
                            message: message
                        });
                        console.log(`📨 Game: ${message.type}`);
                    } catch (error) {
                        console.log(`📨 Game: [non-JSON] ${data.toString().substring(0, 50)}...`);
                    }
                });
                
                resolve();
            });
            
            this.gameSocket.on('error', (error) => {
                console.log(`❌ Game connection failed: ${error.message}`);
                this.errors.push(`Game connection failed: ${error.message}`);
                resolve();
            });
            
            setTimeout(() => {
                if (this.gameSocket.readyState !== WebSocket.OPEN) {
                    this.errors.push('Game connection timeout');
                    console.log('❌ Game connection timeout');
                }
                resolve();
            }, 5000);
        });
    }

    async testMessageInspectorFlow() {
        console.log('\n🔍 Step 3: Testing Message Inspector Flow');
        
        if (!this.adminSocket || this.adminSocket.readyState !== WebSocket.OPEN) {
            console.log('❌ Admin socket not available for message inspector test');
            this.errors.push('Admin socket not available for message inspector test');
            return;
        }

        if (!this.gameSocket || this.gameSocket.readyState !== WebSocket.OPEN) {
            console.log('❌ Game socket not available for message inspector test');
            this.errors.push('Game socket not available for message inspector test');
            return;
        }

        // Clear previous messages
        this.receivedMessages = [];
        
        // Start message monitoring
        console.log('📤 Starting message monitoring...');
        this.adminSocket.send(JSON.stringify({type: 'start_message_monitoring'}));
        
        await this.sleep(1000);
        
        // Send test messages
        console.log('📤 Sending test messages...');
        const testMessages = [
            {
                type: 'chat_message',
                content: {
                    message: 'Test message for inspector 1',
                    user_profile_id: 'test-user-123'
                }
            },
            {
                type: 'chat_message',
                content: {
                    message: 'Test message for inspector 2',
                    user_profile_id: 'test-user-123'
                }
            }
        ];
        
        for (let i = 0; i < testMessages.length; i++) {
            this.gameSocket.send(JSON.stringify(testMessages[i]));
            await this.sleep(1000);
        }
        
        // Wait for message flow events
        await this.sleep(3000);
        
        // Check for message flow events
        const messageFlowEvents = this.receivedMessages.filter(msg => 
            msg.source === 'admin' && 
            msg.message.type === 'message_flow'
        );
        
        if (messageFlowEvents.length > 0) {
            console.log(`✅ Message inspector working: ${messageFlowEvents.length} flow events received`);
            this.testResults.messageInspector = true;
            messageFlowEvents.forEach(event => {
                const data = event.message.data;
                console.log(`   - ${data.direction} message: ${data.message.type} (session: ${data.session_id.substring(0, 8)}...)`);
            });
        } else {
            console.log('❌ Message inspector NOT working: No message flow events received');
            this.errors.push('Message inspector not receiving message flow events');
        }
    }

    async testSessionMonitoringFlow() {
        console.log('\n🔍 Step 4: Testing Session Monitoring Flow');
        
        if (!this.adminSocket || this.adminSocket.readyState !== WebSocket.OPEN) {
            console.log('❌ Admin socket not available for session monitoring test');
            this.errors.push('Admin socket not available for session monitoring test');
            return;
        }

        // Get connection data to find a session ID
        console.log('📤 Requesting connection data...');
        this.adminSocket.send(JSON.stringify({type: 'get_connections'}));
        
        await this.sleep(2000);
        
        // Find a session ID from connection data
        const connectionMessages = this.receivedMessages.filter(msg => 
            msg.source === 'admin' && msg.message.type === 'connection_data'
        );
        
        let sessionId = null;
        if (connectionMessages.length > 0 && connectionMessages[0].message.data.length > 0) {
            sessionId = connectionMessages[0].message.data[0].session_id;
            console.log(`📍 Found session ID: ${sessionId.substring(0, 12)}...`);
        } else {
            console.log('⚠️ No active sessions found for monitoring test');
            return;
        }

        // Start session monitoring
        console.log('📤 Starting session monitoring...');
        this.adminSocket.send(JSON.stringify({type: 'start_session_monitoring'}));
        
        await this.sleep(500);
        
        // Focus on the session
        console.log(`📤 Focusing on session: ${sessionId.substring(0, 12)}...`);
        this.adminSocket.send(JSON.stringify({
            type: 'focus_session',
            session_id: sessionId
        }));
        
        await this.sleep(1000);
        
        // Clear previous messages and send a test message
        const messagesBefore = this.receivedMessages.length;
        
        console.log('📤 Sending test message to trigger session monitoring...');
        if (this.gameSocket && this.gameSocket.readyState === WebSocket.OPEN) {
            this.gameSocket.send(JSON.stringify({
                type: 'chat_message',
                content: {
                    message: 'Session monitoring test message',
                    user_profile_id: 'test-user-123'
                }
            }));
            
            await this.sleep(3000);
            
            // Check for session-specific messages
            const newMessages = this.receivedMessages.slice(messagesBefore);
            const sessionMessages = newMessages.filter(msg => 
                msg.source === 'admin' && 
                (msg.message.session_id === sessionId || 
                 (msg.message.data && msg.message.data.session_id === sessionId))
            );
            
            if (sessionMessages.length > 0) {
                console.log(`✅ Session monitoring working: ${sessionMessages.length} session messages received`);
                this.testResults.sessionMonitoring = true;
                sessionMessages.forEach(msg => {
                    console.log(`   - Session message: ${msg.message.type}`);
                });
            } else {
                console.log('❌ Session monitoring NOT working: No session-specific messages received');
                this.errors.push('Session monitoring not receiving session-specific messages');
            }
        }
    }

    async testUIIntegration() {
        console.log('\n🔍 Step 5: Testing UI Integration');
        
        // Test if the admin dashboard page is accessible
        return new Promise((resolve) => {
            const req = http.request({
                hostname: 'localhost',
                port: 8000,
                path: '/admin/admin_tools/connection_dashboard/',
                method: 'GET',
                timeout: 5000
            }, (res) => {
                let data = '';
                res.on('data', chunk => data += chunk);
                res.on('end', () => {
                    if (res.statusCode === 200 && data.includes('Connection Dashboard')) {
                        console.log('✅ Admin dashboard UI accessible');
                        this.testResults.uiIntegration = true;
                    } else {
                        console.log(`❌ Admin dashboard UI issue: HTTP ${res.statusCode}`);
                        this.errors.push(`Admin dashboard UI not accessible: HTTP ${res.statusCode}`);
                    }
                    resolve();
                });
            });

            req.on('error', (error) => {
                console.log(`❌ UI integration test failed: ${error.message}`);
                this.errors.push(`UI integration test failed: ${error.message}`);
                resolve();
            });

            req.on('timeout', () => {
                console.log('❌ UI integration test timeout');
                this.errors.push('UI integration test timeout');
                req.destroy();
                resolve();
            });

            req.end();
        });
    }

    async generateComprehensiveReport() {
        const duration = ((Date.now() - this.startTime) / 1000).toFixed(1);
        
        console.log('\n🎯 COMPREHENSIVE TEST REPORT');
        console.log('============================');
        console.log(`⏱️ Test duration: ${duration} seconds`);
        console.log(`📨 Total messages received: ${this.receivedMessages.length}`);
        console.log(`❌ Issues found: ${this.errors.length}`);
        
        console.log('\n🧪 TEST RESULTS:');
        Object.entries(this.testResults).forEach(([test, passed]) => {
            const status = passed ? '✅ PASS' : '❌ FAIL';
            const testName = test.replace(/([A-Z])/g, ' $1').toLowerCase();
            console.log(`  ${testName}: ${status}`);
        });
        
        // Message breakdown
        const adminMessages = this.receivedMessages.filter(msg => msg.source === 'admin');
        const gameMessages = this.receivedMessages.filter(msg => msg.source === 'game');
        
        console.log('\n📊 MESSAGE BREAKDOWN:');
        console.log(`  Admin dashboard: ${adminMessages.length} messages`);
        console.log(`  Game WebSocket: ${gameMessages.length} messages`);
        
        // Admin message types
        if (adminMessages.length > 0) {
            console.log('\n📨 ADMIN MESSAGE TYPES:');
            const adminTypes = {};
            adminMessages.forEach(msg => {
                const type = msg.message.type || 'unknown';
                adminTypes[type] = (adminTypes[type] || 0) + 1;
            });
            Object.entries(adminTypes).forEach(([type, count]) => {
                console.log(`  - ${type}: ${count}`);
            });
        }
        
        // Issues found
        if (this.errors.length > 0) {
            console.log('\n❌ ISSUES FOUND:');
            this.errors.forEach((error, index) => {
                console.log(`  ${index + 1}. ${error}`);
            });
        }
        
        // Overall assessment
        const passedTests = Object.values(this.testResults).filter(Boolean).length;
        const totalTests = Object.keys(this.testResults).length;
        
        console.log('\n🎯 OVERALL ASSESSMENT:');
        console.log(`Tests passed: ${passedTests}/${totalTests}`);
        
        if (passedTests === totalTests && this.errors.length === 0) {
            console.log('🎉 ALL TESTS PASSED - Dashboard is fully functional!');
            console.log('✅ Session monitoring and message inspector are working');
            console.log('✅ UI integration is successful');
            console.log('✅ Ready for production use');
        } else if (passedTests >= totalTests * 0.8) {
            console.log('👍 MOSTLY WORKING - Minor issues remain');
            console.log('✅ Core functionality is operational');
            console.log('⚠️ Some edge cases need attention');
        } else if (passedTests >= totalTests * 0.5) {
            console.log('⚠️ PARTIALLY WORKING - Significant issues remain');
            console.log('✅ Basic connectivity established');
            console.log('❌ Core features need debugging');
        } else {
            console.log('❌ MAJOR ISSUES - Extensive debugging needed');
            console.log('❌ Core functionality not working');
            console.log('❌ Requires immediate attention');
        }
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async cleanup() {
        console.log('\n🧹 Cleaning up test environment...');
        
        if (this.adminSocket && this.adminSocket.readyState === WebSocket.OPEN) {
            this.adminSocket.close();
        }
        
        if (this.gameSocket && this.gameSocket.readyState === WebSocket.OPEN) {
            this.gameSocket.close();
        }
        
        console.log('✅ Test cleanup complete');
    }
}

// Run the comprehensive test
if (require.main === module) {
    const tester = new ComprehensiveDashboardTest();
    tester.runComprehensiveTest().catch(console.error);
}

module.exports = ComprehensiveDashboardTest;
