#!/usr/bin/env node

/**
 * Comprehensive Issue Analyzer using Playwright
 * 
 * This tool specifically tests the issues mentioned by the user:
 * 1. Duplicate responses from backend (too verbose)
 * 2. Frontend disconnects during backend process
 * 3. Impossible to put focus in chat area (no scrolling/typing after mouse click)
 * 4. Wheel generation and spinning validation
 */

const { chromium } = require('playwright');

class ComprehensiveIssueAnalyzer {
    constructor() {
        this.browser = null;
        this.page = null;
        this.websocketMessages = [];
        this.chatResponses = [];
        this.connectionEvents = [];
        this.userInteractionIssues = [];
        this.testResults = {
            duplicateResponses: false,
            frontendDisconnects: false,
            chatFocusIssues: false,
            wheelGenerationWorks: false,
            wheelSpinningWorks: false,
            winnerDetectionWorks: false
        };
    }

    async initialize() {
        console.log('🔍 Comprehensive Issue Analyzer - Initializing...');
        
        this.browser = await chromium.launch({ 
            headless: false,
            slowMo: 1000,
            args: ['--disable-web-security', '--disable-features=VizDisplayCompositor']
        });
        
        this.page = await this.browser.newPage();
        
        // Set up comprehensive monitoring
        await this.setupWebSocketMonitoring();
        await this.setupConsoleMonitoring();
        await this.setupNetworkMonitoring();
        
        console.log('✅ Analyzer initialized successfully');
    }

    async setupWebSocketMonitoring() {
        // Monitor WebSocket connections and messages
        this.page.on('websocket', ws => {
            console.log(`🔌 WebSocket connection: ${ws.url()}`);
            this.connectionEvents.push({
                type: 'connection',
                url: ws.url(),
                timestamp: Date.now()
            });

            ws.on('framereceived', event => {
                const message = event.payload;
                console.log(`📨 ← Server: ${message.substring(0, 100)}...`);
                this.websocketMessages.push({
                    direction: 'received',
                    message: message,
                    timestamp: Date.now()
                });
                
                // Check for duplicate responses
                this.checkForDuplicateResponses(message);
            });

            ws.on('framesent', event => {
                const message = event.payload;
                console.log(`📤 → Client: ${message.substring(0, 100)}...`);
                this.websocketMessages.push({
                    direction: 'sent',
                    message: message,
                    timestamp: Date.now()
                });
            });

            ws.on('close', () => {
                console.log('❌ WebSocket connection closed');
                this.connectionEvents.push({
                    type: 'disconnection',
                    timestamp: Date.now()
                });
            });
        });
    }

    async setupConsoleMonitoring() {
        this.page.on('console', msg => {
            const text = msg.text();
            console.log(`🖥️  [${msg.type()}] ${text}`);
            
            // Check for connection issues
            if (text.includes('disconnect') || text.includes('connection lost')) {
                this.testResults.frontendDisconnects = true;
            }
        });
    }

    async setupNetworkMonitoring() {
        this.page.on('response', response => {
            if (response.url().includes('/api/') || response.url().includes('/ws/')) {
                console.log(`🌐 API Response: ${response.status()} ${response.url()}`);
            }
        });
    }

    checkForDuplicateResponses(message) {
        try {
            const parsed = JSON.parse(message);
            if (parsed.type === 'chat_message' || parsed.type === 'ai_response') {
                // Check if we've seen this exact response before
                const isDuplicate = this.chatResponses.some(prev => 
                    prev.content === parsed.content && 
                    (Date.now() - prev.timestamp) < 5000 // Within 5 seconds
                );
                
                if (isDuplicate) {
                    console.log('⚠️  DUPLICATE RESPONSE DETECTED!');
                    this.testResults.duplicateResponses = true;
                }
                
                this.chatResponses.push({
                    content: parsed.content,
                    timestamp: Date.now()
                });
            }
        } catch (e) {
            // Not JSON, ignore
        }
    }

    async testUserRecognitionScenario() {
        console.log('\n🧪 Testing: "hey! do you recognize me?" scenario');
        console.log('════════════════════════════════════════════════════════════');
        
        // Load frontend
        await this.page.goto('http://localhost:3001');
        await this.page.waitForTimeout(5000); // Wait for initialization
        
        // Test chat focus and interaction
        await this.testChatFocusIssues();
        
        // Send the user recognition message
        const chatInput = await this.findChatInput();
        if (chatInput) {
            console.log('📝 Sending user recognition message...');
            await chatInput.fill('hey! do you recognize me?');
            await chatInput.press('Enter');
            
            // Monitor for responses and check for duplicates
            const responseCount = await this.monitorResponsesForDuplicates();
            console.log(`📊 Received ${responseCount} responses`);
            
            // Wait for backend processing and check for disconnections
            await this.monitorForDisconnections();
            
        } else {
            console.log('❌ Could not find chat input - focus issue confirmed');
            this.testResults.chatFocusIssues = true;
        }
    }

    async testChatFocusIssues() {
        console.log('🎯 Testing chat focus and interaction issues...');
        
        try {
            // Try multiple methods to access chat input
            const methods = [
                // Standard selector
                () => this.page.locator('textarea'),
                // Shadow DOM traversal
                () => this.page.locator('app-shell').locator('chat-interface').locator('textarea'),
                // Direct shadow DOM access
                async () => {
                    const appShell = await this.page.locator('app-shell').first();
                    const shadowRoot = await appShell.evaluateHandle(el => el.shadowRoot);
                    const chatInterface = await shadowRoot.$('chat-interface');
                    if (chatInterface) {
                        const chatShadowRoot = await chatInterface.evaluateHandle(el => el.shadowRoot);
                        return await chatShadowRoot.$('textarea');
                    }
                    return null;
                }
            ];
            
            let chatInput = null;
            for (let i = 0; i < methods.length; i++) {
                try {
                    console.log(`🔍 Trying method ${i + 1}...`);
                    chatInput = await methods[i]();
                    if (chatInput) {
                        console.log(`✅ Found chat input with method ${i + 1}`);
                        break;
                    }
                } catch (e) {
                    console.log(`❌ Method ${i + 1} failed: ${e.message}`);
                }
            }
            
            if (!chatInput) {
                console.log('❌ All methods failed - chat focus issue confirmed');
                this.testResults.chatFocusIssues = true;
                return null;
            }
            
            // Test focus and interaction
            await this.testInputInteraction(chatInput);
            return chatInput;
            
        } catch (error) {
            console.log(`❌ Chat focus test failed: ${error.message}`);
            this.testResults.chatFocusIssues = true;
            return null;
        }
    }

    async testInputInteraction(chatInput) {
        console.log('🖱️  Testing mouse click and focus behavior...');
        
        try {
            // Test mouse click
            await chatInput.click();
            console.log('✅ Mouse click successful');
            
            // Test focus
            await chatInput.focus();
            console.log('✅ Focus successful');
            
            // Test typing
            await chatInput.fill('test message');
            const value = await chatInput.inputValue();
            if (value === 'test message') {
                console.log('✅ Typing successful');
            } else {
                console.log('❌ Typing failed - input not working');
                this.testResults.chatFocusIssues = true;
            }
            
            // Clear for actual test
            await chatInput.fill('');
            
        } catch (error) {
            console.log(`❌ Input interaction failed: ${error.message}`);
            this.testResults.chatFocusIssues = true;
        }
    }

    async findChatInput() {
        // Use the most reliable method found in testChatFocusIssues
        try {
            const appShell = await this.page.locator('app-shell').first();
            const shadowRoot = await appShell.evaluateHandle(el => el.shadowRoot);
            const chatInterface = await shadowRoot.$('chat-interface');
            if (chatInterface) {
                const chatShadowRoot = await chatInterface.evaluateHandle(el => el.shadowRoot);
                const textarea = await chatShadowRoot.$('textarea');
                return textarea;
            }
        } catch (e) {
            console.log(`Shadow DOM access failed: ${e.message}`);
        }
        
        // Fallback to standard selectors
        try {
            return await this.page.locator('textarea').first();
        } catch (e) {
            return null;
        }
    }

    async monitorResponsesForDuplicates() {
        console.log('👀 Monitoring for duplicate responses...');
        const initialCount = this.chatResponses.length;
        
        // Wait for responses (up to 30 seconds)
        await this.page.waitForTimeout(30000);
        
        const finalCount = this.chatResponses.length;
        const responseCount = finalCount - initialCount;
        
        if (responseCount > 1) {
            console.log(`⚠️  Multiple responses detected: ${responseCount}`);
            // Check if they're actually duplicates
            const recentResponses = this.chatResponses.slice(-responseCount);
            const uniqueResponses = new Set(recentResponses.map(r => r.content));
            
            if (uniqueResponses.size < responseCount) {
                console.log('❌ DUPLICATE RESPONSES CONFIRMED!');
                this.testResults.duplicateResponses = true;
            }
        }
        
        return responseCount;
    }

    async monitorForDisconnections() {
        console.log('🔌 Monitoring for disconnections during backend processing...');
        
        const initialDisconnections = this.connectionEvents.filter(e => e.type === 'disconnection').length;
        
        // Wait for backend processing
        await this.page.waitForTimeout(15000);
        
        const finalDisconnections = this.connectionEvents.filter(e => e.type === 'disconnection').length;
        
        if (finalDisconnections > initialDisconnections) {
            console.log('❌ DISCONNECTION DETECTED during backend processing!');
            this.testResults.frontendDisconnects = true;
        } else {
            console.log('✅ No disconnections detected');
        }
    }

    async testWheelGeneration() {
        console.log('\n🎡 Testing wheel generation and spinning...');
        console.log('════════════════════════════════════════════════════════════');
        
        const chatInput = await this.findChatInput();
        if (chatInput) {
            console.log('📝 Requesting wheel generation...');
            await chatInput.fill('I want to try something creative');
            await chatInput.press('Enter');
            
            // Wait for wheel generation
            await this.page.waitForTimeout(20000);
            
            // Check for wheel elements
            const wheelFound = await this.checkForWheelElements();
            this.testResults.wheelGenerationWorks = wheelFound;
            
            if (wheelFound) {
                // Test wheel spinning
                const spinningWorks = await this.testWheelSpinning();
                this.testResults.wheelSpinningWorks = spinningWorks;
                
                if (spinningWorks) {
                    // Test winner detection
                    const winnerDetected = await this.testWinnerDetection();
                    this.testResults.winnerDetectionWorks = winnerDetected;
                }
            }
        }
    }

    async checkForWheelElements() {
        console.log('🔍 Checking for wheel elements...');
        
        const selectors = [
            '.wheel-container',
            '.wheel',
            'game-wheel',
            'svg',
            'canvas',
            '[data-wheel]'
        ];
        
        for (const selector of selectors) {
            try {
                const element = await this.page.locator(selector).first();
                if (await element.isVisible()) {
                    console.log(`✅ Found wheel element: ${selector}`);
                    return true;
                }
            } catch (e) {
                // Element not found, continue
            }
        }
        
        console.log('❌ No wheel elements found');
        return false;
    }

    async testWheelSpinning() {
        console.log('🎲 Testing wheel spinning...');
        
        const spinSelectors = [
            'button:has-text("Spin")',
            '.spin-button',
            '.wheel',
            'svg',
            'canvas'
        ];
        
        for (const selector of spinSelectors) {
            try {
                const element = await this.page.locator(selector).first();
                if (await element.isVisible()) {
                    console.log(`🎯 Attempting to spin using: ${selector}`);
                    await element.click();
                    await this.page.waitForTimeout(3000);
                    console.log('✅ Spin attempt completed');
                    return true;
                }
            } catch (e) {
                console.log(`❌ Spin failed with ${selector}: ${e.message}`);
            }
        }
        
        console.log('❌ Could not spin wheel');
        return false;
    }

    async testWinnerDetection() {
        console.log('🏆 Testing winner detection...');
        
        const winnerSelectors = [
            '.winner',
            '.selected',
            '[data-winner]',
            '.highlighted',
            '.active'
        ];
        
        for (const selector of winnerSelectors) {
            try {
                const element = await this.page.locator(selector).first();
                if (await element.isVisible()) {
                    console.log(`✅ Winner detected: ${selector}`);
                    return true;
                }
            } catch (e) {
                // Element not found, continue
            }
        }
        
        console.log('❌ No winner indication found');
        return false;
    }

    async generateReport() {
        console.log('\n📊 COMPREHENSIVE ISSUE ANALYSIS REPORT');
        console.log('════════════════════════════════════════════════════════════');
        
        const report = {
            timestamp: new Date().toISOString(),
            testResults: this.testResults,
            websocketMessages: this.websocketMessages.length,
            chatResponses: this.chatResponses.length,
            connectionEvents: this.connectionEvents.length,
            issues: {
                duplicateResponses: this.testResults.duplicateResponses,
                frontendDisconnects: this.testResults.frontendDisconnects,
                chatFocusIssues: this.testResults.chatFocusIssues
            },
            functionality: {
                wheelGeneration: this.testResults.wheelGenerationWorks,
                wheelSpinning: this.testResults.wheelSpinningWorks,
                winnerDetection: this.testResults.winnerDetectionWorks
            },
            recommendations: this.generateRecommendations()
        };
        
        console.log('🎯 Test Results:');
        console.log(`  Duplicate Responses: ${this.testResults.duplicateResponses ? '❌ DETECTED' : '✅ NOT DETECTED'}`);
        console.log(`  Frontend Disconnects: ${this.testResults.frontendDisconnects ? '❌ DETECTED' : '✅ NOT DETECTED'}`);
        console.log(`  Chat Focus Issues: ${this.testResults.chatFocusIssues ? '❌ DETECTED' : '✅ NOT DETECTED'}`);
        console.log(`  Wheel Generation: ${this.testResults.wheelGenerationWorks ? '✅ WORKING' : '❌ NOT WORKING'}`);
        console.log(`  Wheel Spinning: ${this.testResults.wheelSpinningWorks ? '✅ WORKING' : '❌ NOT WORKING'}`);
        console.log(`  Winner Detection: ${this.testResults.winnerDetectionWorks ? '✅ WORKING' : '❌ NOT WORKING'}`);
        
        console.log('\n📈 Statistics:');
        console.log(`  WebSocket Messages: ${this.websocketMessages.length}`);
        console.log(`  Chat Responses: ${this.chatResponses.length}`);
        console.log(`  Connection Events: ${this.connectionEvents.length}`);
        
        console.log('\n💡 Recommendations:');
        report.recommendations.forEach((rec, i) => {
            console.log(`  ${i + 1}. ${rec}`);
        });
        
        // Save detailed report
        const fs = require('fs');
        const reportPath = `test-results/comprehensive-issue-analysis-${Date.now()}.json`;
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        console.log(`\n📁 Detailed report saved to: ${reportPath}`);
        
        return report;
    }

    generateRecommendations() {
        const recommendations = [];
        
        if (this.testResults.duplicateResponses) {
            recommendations.push('Fix backend duplicate response issue - implement response deduplication');
        }
        
        if (this.testResults.frontendDisconnects) {
            recommendations.push('Improve WebSocket connection stability during backend processing');
        }
        
        if (this.testResults.chatFocusIssues) {
            recommendations.push('Fix chat input focus issues - implement proper Shadow DOM handling');
        }
        
        if (!this.testResults.wheelGenerationWorks) {
            recommendations.push('Fix wheel generation - ensure backend generates wheel data properly');
        }
        
        if (!this.testResults.wheelSpinningWorks) {
            recommendations.push('Implement proper wheel spinning mechanism with clear interaction elements');
        }
        
        if (!this.testResults.winnerDetectionWorks) {
            recommendations.push('Add clear winner indication after wheel spin completion');
        }
        
        if (recommendations.length === 0) {
            recommendations.push('All core functionality appears to be working correctly');
        }
        
        return recommendations;
    }

    async cleanup() {
        if (this.browser) {
            await this.browser.close();
        }
    }

    async run() {
        try {
            await this.initialize();
            await this.testUserRecognitionScenario();
            await this.testWheelGeneration();
            await this.generateReport();
        } catch (error) {
            console.error('❌ Test failed:', error);
        } finally {
            await this.cleanup();
        }
    }
}

// Run the analyzer
if (require.main === module) {
    const analyzer = new ComprehensiveIssueAnalyzer();
    analyzer.run().catch(console.error);
}

module.exports = ComprehensiveIssueAnalyzer;
