#!/usr/bin/env node

/**
 * Simple Playwright WebSocket Test
 * 
 * A minimal test to identify WebSocket connection issues using Playwright
 */

const { chromium } = require('playwright');

async function testWebSocketConnection() {
    console.log('🚀 Starting Simple Playwright WebSocket Test...');
    
    let browser, context, page;
    
    try {
        // Launch browser
        console.log('🌐 Launching browser...');
        browser = await chromium.launch({ 
            headless: false,
            slowMo: 1000
        });
        
        context = await browser.newContext();
        page = await context.newPage();
        
        // Enable console logging
        page.on('console', msg => {
            console.log(`🖥️  Browser: [${msg.type()}] ${msg.text()}`);
        });
        
        // Enable error tracking
        page.on('pageerror', error => {
            console.error('❌ Page Error:', error.message);
        });
        
        // Setup WebSocket monitoring
        console.log('🔌 Setting up WebSocket monitoring...');
        let wsConnected = false;
        let wsMessages = [];
        
        await page.routeWebSocket('**/ws/**', ws => {
            console.log(`🔗 WebSocket intercepted: ${ws.url()}`);
            
            // Connect to server and monitor
            const server = ws.connectToServer();
            
            server.onMessage(message => {
                console.log('📨 WebSocket Message Received:', message);
                wsMessages.push({ direction: 'received', message, timestamp: new Date() });
                wsConnected = true;
            });
            
            ws.onMessage(message => {
                console.log('📤 WebSocket Message Sent:', message);
                wsMessages.push({ direction: 'sent', message, timestamp: new Date() });
                server.send(message);
            });
        });
        
        // Navigate to the application
        console.log('🌐 Navigating to application...');
        await page.goto('http://localhost:3001');
        
        // Wait for page to load
        await page.waitForLoadState('networkidle');
        console.log('✅ Page loaded');
        
        // Wait for WebSocket connection
        console.log('⏳ Waiting for WebSocket connection...');
        await page.waitForTimeout(5000);
        
        // Check WebSocket status
        const wsStatus = await page.evaluate(() => {
            // Check if WebSocket exists in window
            if (window.websocket) {
                return {
                    exists: true,
                    readyState: window.websocket.readyState,
                    url: window.websocket.url
                };
            }
            return { exists: false };
        });
        
        console.log('📊 WebSocket Status:', wsStatus);
        
        if (wsStatus.exists) {
            const states = ['CONNECTING', 'OPEN', 'CLOSING', 'CLOSED'];
            console.log(`🔌 WebSocket State: ${states[wsStatus.readyState]} (${wsStatus.readyState})`);
            console.log(`🌐 WebSocket URL: ${wsStatus.url}`);
        } else {
            console.log('❌ No WebSocket found in window object');
        }
        
        // Try to send a test message if possible
        if (wsStatus.exists && wsStatus.readyState === 1) { // OPEN
            console.log('💬 Attempting to send test message...');
            
            try {
                // Look for chat input
                const chatInput = page.locator('input[type="text"], textarea').first();
                const inputCount = await chatInput.count();
                
                if (inputCount > 0) {
                    await chatInput.fill("Test message from Playwright");
                    await chatInput.press('Enter');
                    console.log('✅ Test message sent');
                    
                    // Wait for response
                    await page.waitForTimeout(5000);
                } else {
                    console.log('⚠️  No chat input found');
                }
            } catch (error) {
                console.log('⚠️  Could not send test message:', error.message);
            }
        }
        
        // Final report
        console.log('\n📊 Final Report:');
        console.log(`🔌 WebSocket Connected: ${wsConnected}`);
        console.log(`📨 Messages Exchanged: ${wsMessages.length}`);
        
        if (wsMessages.length > 0) {
            console.log('📋 Message Log:');
            wsMessages.forEach((msg, i) => {
                console.log(`  ${i + 1}. [${msg.direction}] ${msg.message.substring(0, 100)}...`);
            });
        }
        
        // Keep browser open for manual inspection
        console.log('\n⏳ Keeping browser open for 30 seconds for manual inspection...');
        console.log('💡 You can interact with the page to test WebSocket functionality');
        await page.waitForTimeout(30000);
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        console.error(error.stack);
    } finally {
        // Cleanup
        console.log('🧹 Cleaning up...');
        if (page) await page.close();
        if (context) await context.close();
        if (browser) await browser.close();
        console.log('✅ Test completed');
    }
}

// Run the test
testWebSocketConnection().catch(console.error);
