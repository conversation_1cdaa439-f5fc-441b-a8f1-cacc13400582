const { chromium } = require('playwright');

async function comprehensiveWheelTest() {
    console.log('🎡 Comprehensive Wheel Test: Full User Story with Spin Testing');
    console.log('═══════════════════════════════════════════════════════════════');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 300
    });
    
    const context = await browser.newContext();
    const page = await context.newPage();
    
    // Capture all WebSocket messages
    const messages = [];
    let wheelData = null;
    let spinResult = null;
    let postSpinMentorResponse = null;
    
    page.on('websocket', ws => {
        console.log(`🔌 WebSocket connection: ${ws.url()}`);
        ws.on('framereceived', event => {
            try {
                const data = JSON.parse(event.payload);
                messages.push({ type: 'received', data, timestamp: Date.now() });
                
                if (data.type === 'wheel_data') {
                    wheelData = data;
                    console.log('🎡 Wheel data received!');
                }
                
                if (data.type === 'spin_result') {
                    spinResult = data;
                    console.log('🏆 Spin result received!', JSON.stringify(data, null, 2));
                }
                
                if (data.type === 'chat_message' && !data.is_user && data.timestamp > Date.now() - 30000) {
                    // Recent mentor message (last 30 seconds)
                    if (data.content.toLowerCase().includes('congratulations') || 
                        data.content.toLowerCase().includes('great choice') ||
                        data.content.toLowerCase().includes('activity')) {
                        postSpinMentorResponse = data;
                        console.log('💬 Post-spin mentor response detected!');
                    }
                }
                
                console.log(`📨 ← Server: ${JSON.stringify(data).substring(0, 100)}...`);
            } catch (e) {
                console.log(`📨 ← Server (raw): ${event.payload.substring(0, 100)}...`);
            }
        });
        ws.on('framesent', event => {
            try {
                const data = JSON.parse(event.payload);
                messages.push({ type: 'sent', data, timestamp: Date.now() });
                console.log(`📤 → Client: ${JSON.stringify(data).substring(0, 100)}...`);
            } catch (e) {
                console.log(`📤 → Client (raw): ${event.payload.substring(0, 100)}...`);
            }
        });
    });
    
    try {
        // Load the frontend
        console.log('🌐 Loading frontend...');
        await page.goto('http://localhost:3000/', { waitUntil: 'networkidle' });
        console.log('✅ Frontend loaded');
        
        // Wait for connection
        await page.waitForTimeout(3000);
        
        // Send wheel generation request
        const chatInput = await page.locator('textarea').first();
        await chatInput.waitFor({ state: 'visible', timeout: 10000 });
        await chatInput.click();
        
        const message = "I'm restless and need to do things physical. I have 2 hours. Make me a wheel";
        console.log(`📝 Sending message: "${message}"`);
        await chatInput.fill(message);
        await page.keyboard.press('Enter');
        
        // Wait for wheel data with longer timeout (2 minutes for LLM tailoring)
        console.log('⏳ Waiting for wheel data (up to 2 minutes for LLM tailoring)...');
        let attempts = 0;
        while (!wheelData && attempts < 120) { // 2 minutes
            await page.waitForTimeout(1000);
            attempts++;
            if (attempts % 10 === 0) {
                console.log(`   Still waiting... ${attempts}/120 seconds`);
            }
        }
        
        if (!wheelData) {
            console.log('❌ Wheel data not received within 2 minutes');
            return;
        }
        
        console.log('✅ Wheel data received! Analyzing activities...');
        
        // Analyze wheel activities
        const activities = wheelData.wheel?.items || [];
        console.log(`📊 Found ${activities.length} activities`);
        
        // Check if activities match user request for "physical" activities
        const physicalKeywords = ['physical', 'exercise', 'movement', 'strength', 'cardio', 'yoga', 'dance', 'parkour', 'bodyweight'];
        const physicalActivities = activities.filter(activity => {
            const text = (activity.name + ' ' + activity.description + ' ' + activity.domain).toLowerCase();
            return physicalKeywords.some(keyword => text.includes(keyword));
        });
        
        console.log(`🏃 Physical activities found: ${physicalActivities.length}/${activities.length}`);
        physicalActivities.forEach(activity => {
            console.log(`   - ${activity.name} (${activity.domain})`);
        });
        
        // Wait additional time for wheel component to fully initialize
        console.log('⏳ Waiting for wheel component to fully initialize...');
        await page.waitForTimeout(5000);
        
        // Look for spin button
        console.log('🔍 Looking for spin button...');
        const spinButton = await page.locator('game-wheel button.spin-button').first();
        const spinButtonExists = await spinButton.count() > 0;
        
        if (!spinButtonExists) {
            console.log('❌ Spin button not found, checking for alternative spin triggers...');
            
            // Check for clickable wheel
            const wheelElement = await page.locator('game-wheel canvas, game-wheel svg, .wheel').first();
            if (await wheelElement.count() > 0) {
                console.log('🎯 Found wheel element, attempting to click...');
                await wheelElement.click();
                await page.waitForTimeout(2000);
            }
        } else {
            console.log('✅ Spin button found! Testing spin...');
            
            // Check button state
            const isEnabled = await spinButton.isEnabled();
            const isVisible = await spinButton.isVisible();
            console.log(`   Button enabled: ${isEnabled}, visible: ${isVisible}`);
            
            if (isEnabled && isVisible) {
                console.log('🎲 Clicking spin button...');
                await spinButton.click();
                
                // Wait for spin to complete (up to 15 seconds)
                console.log('⏳ Waiting for spin to complete...');
                let spinCompleted = false;
                let spinAttempts = 0;
                
                while (!spinCompleted && spinAttempts < 15) {
                    await page.waitForTimeout(1000);
                    spinAttempts++;
                    
                    // Check if spin result was received
                    if (spinResult) {
                        spinCompleted = true;
                        console.log('✅ Spin completed with result!');
                        break;
                    }
                    
                    // Check if wheel is still spinning by examining component state
                    const isSpinning = await page.evaluate(() => {
                        const gameWheel = document.querySelector('game-wheel');
                        return gameWheel ? gameWheel.isSpinning : false;
                    });
                    
                    if (!isSpinning && spinAttempts > 5) {
                        console.log('✅ Wheel appears to have stopped spinning');
                        spinCompleted = true;
                        break;
                    }
                    
                    if (spinAttempts % 3 === 0) {
                        console.log(`   Still spinning... ${spinAttempts}/15 seconds`);
                    }
                }
                
                // Wait a bit more for post-spin processing
                console.log('⏳ Waiting for post-spin mentor response...');
                await page.waitForTimeout(5000);
                
                // Check for post-spin mentor response
                const recentMentorMessages = messages.filter(m => 
                    m.data && m.data.type === 'chat_message' && 
                    !m.data.is_user && 
                    m.timestamp > Date.now() - 20000 // Last 20 seconds
                );
                
                if (recentMentorMessages.length > 0) {
                    postSpinMentorResponse = recentMentorMessages[recentMentorMessages.length - 1].data;
                    console.log('💬 Post-spin mentor response found!');
                }
            }
        }
        
        // Final analysis
        console.log('\n📊 COMPREHENSIVE TEST RESULTS');
        console.log('═══════════════════════════════════════════════════════════════');
        console.log(`✅ Frontend loaded: YES`);
        console.log(`✅ Message sent: YES`);
        console.log(`${wheelData ? '✅' : '❌'} Wheel generated: ${wheelData ? 'YES' : 'NO'}`);
        console.log(`${activities.length > 0 ? '✅' : '❌'} Activities found: ${activities.length}`);
        console.log(`${physicalActivities.length > 0 ? '✅' : '❌'} Physical activities: ${physicalActivities.length}/${activities.length}`);
        console.log(`${spinButtonExists ? '✅' : '❌'} Spin button available: ${spinButtonExists ? 'YES' : 'NO'}`);
        console.log(`${spinResult ? '✅' : '❌'} Spin result received: ${spinResult ? 'YES' : 'NO'}`);
        console.log(`${postSpinMentorResponse ? '✅' : '❌'} Post-spin mentor response: ${postSpinMentorResponse ? 'YES' : 'NO'}`);
        
        if (spinResult) {
            console.log('\n🏆 SPIN RESULT DETAILS:');
            console.log(`   Winner: ${spinResult.content?.activity_name || 'Unknown'}`);
            console.log(`   Activity ID: ${spinResult.content?.activity_id || 'Unknown'}`);
        }
        
        if (postSpinMentorResponse) {
            console.log('\n💬 POST-SPIN MENTOR RESPONSE:');
            console.log(`   "${postSpinMentorResponse.content.substring(0, 200)}..."`);
        }
        
        console.log(`\n📨 Total WebSocket messages: ${messages.length}`);
        
        console.log('\n🔍 Browser kept open for manual inspection...');
        console.log('Press Ctrl+C to close when done.');
        
        // Keep browser open for inspection
        await new Promise(() => {});
        
    } catch (error) {
        console.error('❌ Test failed:', error);
    }
}

comprehensiveWheelTest().catch(console.error);
