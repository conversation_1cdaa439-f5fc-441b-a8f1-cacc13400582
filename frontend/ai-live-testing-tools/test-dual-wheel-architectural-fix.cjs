#!/usr/bin/env node

/**
 * Dual-Wheel Architectural Fix Validation Test
 * 
 * This test validates that the architectural fix for dual-wheel issues is working:
 * 1. WebSocket wheel data format consistency
 * 2. API wheel data format consistency  
 * 3. No grey segments after remove operations
 * 4. Consistent wheel data across all operations
 * 5. Modal closing doesn't cause wheel data corruption
 */

const puppeteer = require('puppeteer');

class DualWheelArchitecturalFixTest {
    constructor(port = 3002) {
        this.port = port;
        this.baseUrl = `http://localhost:${port}`;
        this.browser = null;
        this.page = null;
        this.testResults = {
            wheelGeneration: false,
            wheelDataConsistency: false,
            removeOperationColors: false,
            addOperationColors: false,
            modalClosingStability: false,
            overallSuccess: false
        };
        this.wheelDataSnapshots = [];
    }

    async runTest() {
        console.log('🏗️ ===== DUAL-WHEEL ARCHITECTURAL FIX VALIDATION =====');
        console.log(`Testing on ${this.baseUrl}`);
        console.log();

        try {
            await this.setupBrowser();
            await this.setupAuthentication();
            await this.testWheelGeneration();
            await this.testWheelDataConsistency();
            await this.testRemoveOperationColors();
            await this.testAddOperationColors();
            await this.testModalClosingStability();
            await this.generateReport();
        } catch (error) {
            console.error('❌ Test failed:', error);
        } finally {
            if (this.browser) {
                await this.browser.close();
            }
        }
    }

    async setupBrowser() {
        console.log('🚀 Setting up browser...');
        this.browser = await puppeteer.launch({
            headless: false,
            defaultViewport: { width: 1200, height: 800 },
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        this.page = await this.browser.newPage();
        
        // Enable console logging for wheel data debugging
        this.page.on('console', msg => {
            const text = msg.text();
            if (text.includes('wheel') || text.includes('WHEEL') || text.includes('🎡') || text.includes('🗑️') || text.includes('➕')) {
                console.log(`   🔍 Console: ${text}`);
            }
        });
        
        await this.page.goto(this.baseUrl);
        await new Promise(resolve => setTimeout(resolve, 2000));
        console.log('   ✅ Browser setup complete');
    }

    async setupAuthentication() {
        console.log('🔐 Setting up authentication...');
        
        try {
            // Check if login modal is present
            const loginModal = await this.page.$('.modal:has(.login-form), .login-modal, [data-testid="login-modal"]');
            if (loginModal) {
                console.log('   🔑 Login modal detected, performing admin login...');
                
                // Fill in admin credentials
                const usernameInput = await this.page.$('input[name="username"], input[type="text"], input[placeholder*="username" i]');
                if (usernameInput) {
                    await usernameInput.type('admin');
                    console.log('   ✅ Entered username: admin');
                }
                
                const passwordInput = await this.page.$('input[name="password"], input[type="password"], input[placeholder*="password" i]');
                if (passwordInput) {
                    await passwordInput.type('admin123');
                    console.log('   ✅ Entered password');
                }
                
                // Click login button
                let loginButton = await this.page.$('button[type="submit"]');
                if (!loginButton) {
                    loginButton = await this.page.$('.btn-primary');
                }
                if (!loginButton) {
                    loginButton = await this.page.evaluateHandle(() => {
                        const buttons = Array.from(document.querySelectorAll('button'));
                        return buttons.find(btn => 
                            btn.textContent.toLowerCase().includes('login') ||
                            btn.textContent.toLowerCase().includes('sign in')
                        );
                    });
                }
                
                if (loginButton && loginButton.asElement) {
                    await loginButton.asElement().click();
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    console.log('   ✅ Clicked login button');
                } else if (loginButton) {
                    await loginButton.click();
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    console.log('   ✅ Clicked login button');
                }
            }

            // Open debug panel
            await this.page.keyboard.down('Control');
            await this.page.keyboard.down('Shift');
            await this.page.keyboard.press('KeyD');
            await this.page.keyboard.up('Shift');
            await this.page.keyboard.up('Control');
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Select PhiPhi user (ID 2)
            const userSelect = await this.page.$('select[data-testid="user-select"]');
            if (userSelect) {
                await userSelect.selectOption('2');
                console.log('   ✅ Selected PhiPhi user');
            }

            // Select LLM
            const llmSelect = await this.page.$('select[data-testid="llm-select"]');
            if (llmSelect) {
                await llmSelect.selectOption('mistral-small-latest');
                console.log('   ✅ Selected LLM');
            }

            // Apply settings
            const applyButton = await this.page.$('button[data-testid="apply-settings"]');
            if (applyButton) {
                await applyButton.click();
                await new Promise(resolve => setTimeout(resolve, 2000));
                console.log('   ✅ Applied settings');
            }

        } catch (error) {
            console.log('   ⚠️ Authentication setup failed, continuing with default user');
        }
    }

    async testWheelGeneration() {
        console.log('🎡 Testing wheel generation...');
        
        try {
            // Look for generate button
            let generateButton = await this.page.$('button[data-testid="generate-wheel"]');
            if (!generateButton) {
                generateButton = await this.page.$('button.btn-primary');
            }
            if (!generateButton) {
                generateButton = await this.page.evaluateHandle(() => {
                    const buttons = Array.from(document.querySelectorAll('button'));
                    return buttons.find(btn => btn.textContent.toLowerCase().includes('generate'));
                });
            }
            
            if (generateButton && generateButton.asElement) {
                await generateButton.asElement().click();
                console.log('   🔄 Wheel generation started...');
                
                // Wait for wheel generation to complete (up to 60 seconds)
                await this.page.waitForFunction(() => {
                    const appShell = document.querySelector('app-shell');
                    return appShell && appShell.wheelData && appShell.wheelData.segments && appShell.wheelData.segments.length > 0;
                }, { timeout: 60000 });
                
                // Capture initial wheel data
                const initialWheelData = await this.page.evaluate(() => {
                    const appShell = document.querySelector('app-shell');
                    return appShell && appShell.wheelData ? JSON.parse(JSON.stringify(appShell.wheelData)) : null;
                });
                
                if (initialWheelData && initialWheelData.segments.length > 0) {
                    this.wheelDataSnapshots.push({
                        operation: 'initial_generation',
                        timestamp: Date.now(),
                        data: initialWheelData
                    });
                    
                    console.log(`   ✅ Wheel generated with ${initialWheelData.segments.length} segments`);
                    console.log(`   📊 Sample segment:`, initialWheelData.segments[0]);
                    this.testResults.wheelGeneration = true;
                } else {
                    console.log('   ❌ Wheel generation failed or no segments');
                }
                
            } else if (generateButton) {
                await generateButton.click();
                console.log('   🔄 Wheel generation started...');
                // Similar logic as above...
            } else {
                console.log('   ⚠️ Generate button not found');
            }
            
        } catch (error) {
            console.log(`   ❌ Wheel generation test failed: ${error.message}`);
        }
    }

    async testWheelDataConsistency() {
        console.log('🔍 Testing wheel data consistency...');
        
        try {
            // Get wheel data multiple times to check consistency
            const wheelDataChecks = [];
            
            for (let i = 0; i < 3; i++) {
                const wheelData = await this.page.evaluate(() => {
                    const appShell = document.querySelector('app-shell');
                    return appShell && appShell.wheelData ? JSON.parse(JSON.stringify(appShell.wheelData)) : null;
                });
                
                if (wheelData) {
                    wheelDataChecks.push(wheelData);
                }
                
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            // Check if all wheel data snapshots are consistent
            if (wheelDataChecks.length >= 2) {
                const firstCheck = wheelDataChecks[0];
                const allConsistent = wheelDataChecks.every(check => 
                    check.segments.length === firstCheck.segments.length &&
                    check.segments.every((segment, index) => 
                        segment.id === firstCheck.segments[index].id &&
                        segment.name === firstCheck.segments[index].name
                    )
                );
                
                if (allConsistent) {
                    console.log('   ✅ Wheel data is consistent across multiple checks');
                    this.testResults.wheelDataConsistency = true;
                } else {
                    console.log('   ❌ Wheel data inconsistency detected');
                }
            }
            
        } catch (error) {
            console.log(`   ❌ Wheel data consistency test failed: ${error.message}`);
        }
    }

    async testRemoveOperationColors() {
        console.log('🗑️ Testing remove operation color preservation...');
        
        try {
            // Find a remove button
            const removeButton = await this.page.$('.remove-activity-btn, .activity-item .btn-danger, button[title*="Remove"]');
            if (removeButton) {
                // Capture wheel data before removal
                const beforeRemoval = await this.page.evaluate(() => {
                    const appShell = document.querySelector('app-shell');
                    return appShell && appShell.wheelData ? JSON.parse(JSON.stringify(appShell.wheelData)) : null;
                });
                
                await removeButton.click();
                await new Promise(resolve => setTimeout(resolve, 3000)); // Wait for removal to complete
                
                // Capture wheel data after removal
                const afterRemoval = await this.page.evaluate(() => {
                    const appShell = document.querySelector('app-shell');
                    return appShell && appShell.wheelData ? JSON.parse(JSON.stringify(appShell.wheelData)) : null;
                });
                
                if (beforeRemoval && afterRemoval) {
                    this.wheelDataSnapshots.push({
                        operation: 'after_removal',
                        timestamp: Date.now(),
                        data: afterRemoval
                    });
                    
                    // Check if colors are preserved (not grey)
                    const hasProperColors = afterRemoval.segments.every(segment => 
                        segment.color && segment.color !== '#95A5A6' && segment.color !== 'grey'
                    );
                    
                    if (hasProperColors) {
                        console.log('   ✅ Colors preserved after removal operation');
                        this.testResults.removeOperationColors = true;
                    } else {
                        console.log('   ❌ Grey segments detected after removal');
                        console.log('   📊 Segments after removal:', afterRemoval.segments.map(s => ({ name: s.name, color: s.color })));
                    }
                } else {
                    console.log('   ❌ Could not capture wheel data for removal test');
                }
            } else {
                console.log('   ⚠️ No remove button found');
            }
            
        } catch (error) {
            console.log(`   ❌ Remove operation color test failed: ${error.message}`);
        }
    }

    async testAddOperationColors() {
        console.log('➕ Testing add operation color preservation...');
        
        try {
            // Open add activity modal
            let addButton = await this.page.$('button[data-testid="search-activities"], .add-activity-btn');
            if (!addButton) {
                addButton = await this.page.evaluateHandle(() => {
                    const buttons = Array.from(document.querySelectorAll('button'));
                    return buttons.find(btn => 
                        btn.textContent.toLowerCase().includes('add') ||
                        btn.textContent.toLowerCase().includes('search')
                    );
                });
            }
            
            if (addButton && addButton.asElement) {
                await addButton.asElement().click();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Try to add an activity
                const activityItem = await this.page.$('.activity-item:first-child, .catalog-item:first-child');
                if (activityItem) {
                    // Capture wheel data before adding
                    const beforeAdding = await this.page.evaluate(() => {
                        const appShell = document.querySelector('app-shell');
                        return appShell && appShell.wheelData ? JSON.parse(JSON.stringify(appShell.wheelData)) : null;
                    });
                    
                    await activityItem.click();
                    await new Promise(resolve => setTimeout(resolve, 3000)); // Wait for addition to complete
                    
                    // Capture wheel data after adding
                    const afterAdding = await this.page.evaluate(() => {
                        const appShell = document.querySelector('app-shell');
                        return appShell && appShell.wheelData ? JSON.parse(JSON.stringify(appShell.wheelData)) : null;
                    });
                    
                    if (beforeAdding && afterAdding) {
                        this.wheelDataSnapshots.push({
                            operation: 'after_adding',
                            timestamp: Date.now(),
                            data: afterAdding
                        });
                        
                        // Check if colors are preserved (not grey)
                        const hasProperColors = afterAdding.segments.every(segment => 
                            segment.color && segment.color !== '#95A5A6' && segment.color !== 'grey'
                        );
                        
                        if (hasProperColors) {
                            console.log('   ✅ Colors preserved after add operation');
                            this.testResults.addOperationColors = true;
                        } else {
                            console.log('   ❌ Grey segments detected after adding');
                            console.log('   📊 Segments after adding:', afterAdding.segments.map(s => ({ name: s.name, color: s.color })));
                        }
                    }
                } else {
                    console.log('   ⚠️ No activity items found in modal');
                }
            }
            
        } catch (error) {
            console.log(`   ❌ Add operation color test failed: ${error.message}`);
        }
    }

    async testModalClosingStability() {
        console.log('🔄 Testing modal closing stability...');
        
        try {
            // Capture wheel data before any modal operations
            const beforeModal = await this.page.evaluate(() => {
                const appShell = document.querySelector('app-shell');
                return appShell && appShell.wheelData ? JSON.parse(JSON.stringify(appShell.wheelData)) : null;
            });
            
            // Close any open modals
            const closeButtons = await this.page.$$('.modal-close, .close, button[aria-label="Close"]');
            for (const closeButton of closeButtons) {
                try {
                    await closeButton.click();
                    await new Promise(resolve => setTimeout(resolve, 500));
                } catch (e) {
                    // Ignore if button is not clickable
                }
            }
            
            // Wait a bit for any state changes
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // Capture wheel data after modal closing
            const afterModal = await this.page.evaluate(() => {
                const appShell = document.querySelector('app-shell');
                return appShell && appShell.wheelData ? JSON.parse(JSON.stringify(appShell.wheelData)) : null;
            });
            
            if (beforeModal && afterModal) {
                // Check if wheel data is stable after modal operations
                const isStable = beforeModal.segments.length === afterModal.segments.length &&
                    beforeModal.segments.every((segment, index) => 
                        segment.id === afterModal.segments[index].id &&
                        segment.name === afterModal.segments[index].name &&
                        segment.color === afterModal.segments[index].color
                    );
                
                if (isStable) {
                    console.log('   ✅ Wheel data stable after modal closing');
                    this.testResults.modalClosingStability = true;
                } else {
                    console.log('   ❌ Wheel data changed after modal closing');
                }
            }
            
        } catch (error) {
            console.log(`   ❌ Modal closing stability test failed: ${error.message}`);
        }
    }

    async generateReport() {
        console.log();
        console.log('📋 ===== DUAL-WHEEL ARCHITECTURAL FIX VALIDATION RESULTS =====');
        
        const passedTests = Object.values(this.testResults).filter(result => result === true).length;
        const totalTests = Object.keys(this.testResults).length - 1; // Exclude overallSuccess
        
        this.testResults.overallSuccess = passedTests >= totalTests * 0.8; // 80% pass rate
        
        console.log(`✅ Passed: ${passedTests}/${totalTests} tests`);
        console.log();
        
        const testNames = {
            wheelGeneration: 'Wheel Generation',
            wheelDataConsistency: 'Wheel Data Consistency',
            removeOperationColors: 'Remove Operation Color Preservation',
            addOperationColors: 'Add Operation Color Preservation',
            modalClosingStability: 'Modal Closing Stability'
        };
        
        Object.entries(this.testResults).forEach(([test, result]) => {
            if (test !== 'overallSuccess') {
                const status = result ? '✅' : '❌';
                const testName = testNames[test] || test;
                console.log(`${status} ${testName}: ${result ? 'PASS' : 'FAIL'}`);
            }
        });
        
        console.log();
        console.log('📊 Wheel Data Snapshots:');
        this.wheelDataSnapshots.forEach((snapshot, index) => {
            console.log(`   ${index + 1}. ${snapshot.operation}: ${snapshot.data.segments.length} segments`);
            if (snapshot.data.segments.length > 0) {
                const hasColors = snapshot.data.segments.every(s => s.color && s.color !== '#95A5A6');
                console.log(`      Colors: ${hasColors ? '✅ Proper' : '❌ Grey/Missing'}`);
            }
        });
        
        console.log();
        if (this.testResults.overallSuccess) {
            console.log('🎉 DUAL-WHEEL ARCHITECTURAL FIX VALIDATION: SUCCESS');
            console.log('   The architectural fix appears to be working correctly');
            console.log('   Wheel data format is consistent across WebSocket and API operations');
            console.log('   No more grey segments after remove/add operations');
        } else {
            console.log('⚠️ DUAL-WHEEL ARCHITECTURAL FIX VALIDATION: NEEDS ATTENTION');
            console.log('   Some tests failed - architectural issues may still exist');
            console.log('   Review failed tests and check data format consistency');
        }
        
        console.log();
        console.log('🔧 Next Steps:');
        console.log('   1. If tests passed: Monitor for any remaining dual-wheel reports');
        console.log('   2. If tests failed: Review WebSocket vs API data format consistency');
        console.log('   3. Ensure all wheel operations use the same data processing logic');
    }
}

// Run the test
const port = process.argv[2] || 3002;
const test = new DualWheelArchitecturalFixTest(port);
test.runTest().catch(console.error);
