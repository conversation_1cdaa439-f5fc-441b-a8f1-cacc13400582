#!/usr/bin/env node

/**
 * Robust Testing Framework for Frontend Tests
 * 
 * This module provides reliable convenience functions for frontend manipulation
 * that all tests should use to ensure consistency and reliability.
 * 
 * Philosophy: Since we rely heavily on frontend tests for validation, we need
 * excellent, reliable tests with a solid template that all tests respect.
 */

const { chromium } = require('playwright');

/**
 * Robust Testing Framework Class
 */
class TestingFramework {
    constructor(options = {}) {
        this.options = {
            headless: options.headless !== undefined ? options.headless : false,
            slowMo: options.slowMo || 500,
            timeout: options.timeout || 30000,
            ...options
        };
        this.browser = null;
        this.page = null;
    }

    /**
     * Initialize browser and page
     */
    async initialize(url) {
        console.log('🚀 Initializing Testing Framework...');
        
        this.browser = await chromium.launch({
            headless: this.options.headless,
            slowMo: this.options.slowMo
        });
        
        this.page = await this.browser.newPage();
        
        // Set default timeout
        this.page.setDefaultTimeout(this.options.timeout);
        
        // Navigate to application
        await this.page.goto(url);
        await this.page.waitForLoadState('networkidle');
        
        console.log('✓ Browser initialized and page loaded');
        return this.page;
    }

    /**
     * Reliable debug panel setup
     */
    async setupDebugPanel() {
        console.log('🔧 Setting up debug panel...');
        
        try {
            // Multiple strategies to open debug panel
            const strategies = [
                // Strategy 1: Click debug button
                async () => {
                    const debugButton = await this.page.locator('button:has-text("Debug")').first();
                    if (await debugButton.isVisible()) {
                        await debugButton.click();
                        return true;
                    }
                    return false;
                },
                // Strategy 2: Use keyboard shortcut
                async () => {
                    await this.page.keyboard.press('Control+Shift+D');
                    await this.page.waitForTimeout(1000);
                    return true;
                },
                // Strategy 3: Look for debug panel element
                async () => {
                    const debugPanel = await this.page.locator('debug-panel').first();
                    if (await debugPanel.isVisible()) {
                        return true;
                    }
                    return false;
                }
            ];

            for (const strategy of strategies) {
                try {
                    if (await strategy()) {
                        await this.page.waitForTimeout(2000); // Wait for panel to load
                        console.log('✓ Debug panel opened successfully');
                        return true;
                    }
                } catch (e) {
                    console.log(`   Strategy failed: ${e.message}`);
                }
            }
            
            console.log('⚠️  Debug panel setup failed, continuing without it');
            return false;
            
        } catch (error) {
            console.log(`⚠️  Debug panel setup error: ${error.message}`);
            return false;
        }
    }

    /**
     * Reliable user selection with fallbacks
     */
    async selectUser(userId = '191') {
        console.log(`👤 Selecting user ${userId}...`);
        
        try {
            // Multiple selectors for user dropdown
            const dropdownSelectors = [
                'select[id*="user"]',
                'select:has(option:text-matches("User"))',
                'select:has(option[value*="user"])',
                '#user-select',
                '.user-dropdown'
            ];

            let userDropdown = null;
            for (const selector of dropdownSelectors) {
                try {
                    const dropdown = await this.page.locator(selector).first();
                    if (await dropdown.isVisible()) {
                        userDropdown = dropdown;
                        break;
                    }
                } catch (e) {
                    // Continue to next selector
                }
            }

            if (!userDropdown) {
                console.log('⚠️  User dropdown not found, proceeding without user selection');
                return false;
            }

            // Try to select specific user
            const userOption = await userDropdown.locator(`option[value="${userId}"]`).first();
            if (await userOption.isVisible()) {
                await userDropdown.selectOption(userId);
                console.log(`✓ Selected User ${userId}`);
                return true;
            } else {
                // Fallback: select last user
                const userOptions = await userDropdown.locator('option').all();
                if (userOptions.length > 1) {
                    const lastOption = userOptions[userOptions.length - 1];
                    const lastValue = await lastOption.getAttribute('value');
                    await userDropdown.selectOption(lastValue);
                    console.log(`⚠️  User ${userId} not found, selected user ${lastValue} instead`);
                    return true;
                }
            }
            
            console.log('⚠️  No suitable user found for selection');
            return false;
            
        } catch (error) {
            console.log(`⚠️  User selection error: ${error.message}`);
            return false;
        }
    }

    /**
     * Reliable LLM configuration
     */
    async selectLLM(llmName = 'mistral-small-latest') {
        console.log(`🤖 Selecting LLM: ${llmName}...`);
        
        try {
            const llmSelectors = [
                'select[id*="llm"]',
                'select:has(option:text-matches("mistral"))',
                'select:has(option:text-matches("LLM"))',
                '#llm-select',
                '.llm-dropdown'
            ];

            let llmDropdown = null;
            for (const selector of llmSelectors) {
                try {
                    const dropdown = await this.page.locator(selector).first();
                    if (await dropdown.isVisible()) {
                        llmDropdown = dropdown;
                        break;
                    }
                } catch (e) {
                    // Continue to next selector
                }
            }

            if (!llmDropdown) {
                console.log('⚠️  LLM dropdown not found, using default');
                return false;
            }

            // Try to select specific LLM
            const llmOption = await llmDropdown.locator(`option:text-matches("${llmName}")`).first();
            if (await llmOption.isVisible()) {
                await llmDropdown.selectOption(await llmOption.getAttribute('value'));
                console.log(`✓ Selected LLM: ${llmName}`);
                
                // Look for and click apply button
                const applySelectors = [
                    'button:has-text("Apply")',
                    'button[type="submit"]',
                    '.apply-button',
                    '#apply-config'
                ];

                for (const selector of applySelectors) {
                    try {
                        const applyButton = await this.page.locator(selector).first();
                        if (await applyButton.isVisible()) {
                            await applyButton.click();
                            await this.page.waitForTimeout(3000); // Wait for config to apply
                            console.log('✓ Applied LLM configuration');
                            return true;
                        }
                    } catch (e) {
                        // Continue to next selector
                    }
                }
                
                console.log('⚠️  Apply button not found, configuration may not be applied');
                return true;
            }
            
            console.log(`⚠️  LLM ${llmName} not found in dropdown`);
            return false;
            
        } catch (error) {
            console.log(`⚠️  LLM selection error: ${error.message}`);
            return false;
        }
    }

    /**
     * Reliable message sending with validation
     */
    async sendMessage(message) {
        console.log(`💬 Sending message: "${message}"...`);
        
        try {
            // Multiple selectors for chat input
            const chatSelectors = [
                'chat-interface textarea',
                'textarea[placeholder*="message"]',
                'textarea[placeholder*="type"]',
                '.chat-input textarea',
                '#chat-input',
                'input[type="text"][placeholder*="message"]'
            ];

            let chatInput = null;
            for (const selector of chatSelectors) {
                try {
                    const input = await this.page.locator(selector).first();
                    if (await input.isVisible()) {
                        chatInput = input;
                        break;
                    }
                } catch (e) {
                    // Continue to next selector
                }
            }

            if (!chatInput) {
                console.log('❌ Chat input not found');
                return false;
            }

            // Click on chat input to focus
            await chatInput.click();
            await this.page.waitForTimeout(500);
            
            // Clear any existing text and type message
            await chatInput.fill('');
            await chatInput.type(message);
            
            // Send message (try Enter key first, then look for send button)
            await chatInput.press('Enter');
            
            console.log('✓ Message sent successfully');
            return true;
            
        } catch (error) {
            console.log(`❌ Message sending error: ${error.message}`);
            return false;
        }
    }

    /**
     * Smart response waiting with hanging detection
     */
    async waitForResponse(timeout = 30000) {
        console.log(`⏳ Waiting for response (timeout: ${timeout}ms)...`);
        
        const startTime = Date.now();
        
        try {
            // Look for response indicators
            const responseSelectors = [
                '.message:has-text("I")',
                '.assistant-message',
                '.response-message',
                '[role="assistant"]',
                '.chat-message:last-child'
            ];

            // Wait for any response indicator
            for (const selector of responseSelectors) {
                try {
                    await this.page.waitForSelector(selector, { timeout: timeout });
                    const elapsed = Date.now() - startTime;
                    console.log(`✓ Response received in ${elapsed}ms`);
                    return true;
                } catch (e) {
                    // Continue to next selector
                }
            }
            
            // If no response found, check for hanging
            const elapsed = Date.now() - startTime;
            if (elapsed >= timeout) {
                console.log(`❌ HANGING DETECTED: No response after ${elapsed}ms`);
                return false;
            }
            
        } catch (error) {
            console.log(`❌ Response waiting error: ${error.message}`);
            return false;
        }
    }

    /**
     * Clean up resources
     */
    async cleanup() {
        if (this.browser) {
            await this.browser.close();
            console.log('✓ Browser closed');
        }
    }
}

module.exports = { TestingFramework };
