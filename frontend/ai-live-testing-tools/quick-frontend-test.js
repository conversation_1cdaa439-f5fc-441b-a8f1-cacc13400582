#!/usr/bin/env node

/**
 * Quick Frontend Test
 * Rapid validation of frontend changes without full integration testing
 */

import { readFileSync, existsSync } from 'fs';
import { join } from 'path';

class QuickFrontendTest {
  constructor() {
    this.frontendDir = '..';
    this.results = {
      files: { passed: 0, failed: 0, details: [] },
      config: { passed: 0, failed: 0, details: [] },
      types: { passed: 0, failed: 0, details: [] },
      components: { passed: 0, failed: 0, details: [] }
    };
  }

  async runQuickTest() {
    console.log('⚡ Quick Frontend Test Starting...');
    console.log('==================================\n');

    this.testRequiredFiles();
    this.testEnvironmentConfig();
    this.testTypeDefinitions();
    this.testComponentStructure();
    this.testPackageScripts();

    this.printSummary();
    return this.results;
  }

  testRequiredFiles() {
    console.log('📁 Testing Required Files...');
    
    const requiredFiles = [
      'src/services/config-service.ts',
      'src/services/auth-service.ts',
      'src/components/debug/debug-panel.ts',
      'src/components/auth/login-form.ts',
      '.env.development',
      '.env.production'
    ];

    requiredFiles.forEach(file => {
      const filePath = join(this.frontendDir, file);
      if (existsSync(filePath)) {
        this.results.files.passed++;
        this.results.files.details.push(`✅ ${file}`);
      } else {
        this.results.files.failed++;
        this.results.files.details.push(`❌ ${file} - Missing`);
      }
    });
  }

  testEnvironmentConfig() {
    console.log('⚙️ Testing Environment Configuration...');
    
    const envFiles = ['.env.development', '.env.production'];
    
    envFiles.forEach(envFile => {
      const filePath = join(this.frontendDir, envFile);
      
      if (!existsSync(filePath)) {
        this.results.config.failed++;
        this.results.config.details.push(`❌ ${envFile} - Missing`);
        return;
      }

      try {
        const content = readFileSync(filePath, 'utf8');
        const requiredVars = [
          'VITE_APP_MODE',
          'VITE_WS_URL',
          'VITE_DEBUG_ENABLED',
          'VITE_SECURITY_REQUIRE_AUTH'
        ];

        const missingVars = requiredVars.filter(varName => !content.includes(varName));
        
        if (missingVars.length === 0) {
          this.results.config.passed++;
          this.results.config.details.push(`✅ ${envFile} - All variables present`);
        } else {
          this.results.config.failed++;
          this.results.config.details.push(`❌ ${envFile} - Missing: ${missingVars.join(', ')}`);
        }
        
      } catch (error) {
        this.results.config.failed++;
        this.results.config.details.push(`❌ ${envFile} - Read error: ${error.message}`);
      }
    });
  }

  testTypeDefinitions() {
    console.log('🔧 Testing Type Definitions...');
    
    const typesFile = join(this.frontendDir, 'src/types/app-types.ts');
    
    if (!existsSync(typesFile)) {
      this.results.types.failed++;
      this.results.types.details.push('❌ app-types.ts - Missing');
      return;
    }

    try {
      const content = readFileSync(typesFile, 'utf8');
      const requiredTypes = [
        'AppMode',
        'DebugConfig', 
        'SecurityConfig',
        'AuthToken',
        'LLMConfig'
      ];

      requiredTypes.forEach(typeName => {
        if (content.includes(`interface ${typeName}`) || content.includes(`type ${typeName}`)) {
          this.results.types.passed++;
          this.results.types.details.push(`✅ ${typeName} type defined`);
        } else {
          this.results.types.failed++;
          this.results.types.details.push(`❌ ${typeName} type missing`);
        }
      });
      
    } catch (error) {
      this.results.types.failed++;
      this.results.types.details.push(`❌ Type definitions - Read error: ${error.message}`);
    }
  }

  testComponentStructure() {
    console.log('🎨 Testing Component Structure...');
    
    const components = [
      { file: 'src/components/debug/debug-panel.ts', element: 'debug-panel' },
      { file: 'src/components/auth/login-form.ts', element: 'login-form' },
      { file: 'src/services/config-service.ts', class: 'ConfigService' },
      { file: 'src/services/auth-service.ts', class: 'AuthService' }
    ];

    components.forEach(component => {
      const filePath = join(this.frontendDir, component.file);
      
      if (!existsSync(filePath)) {
        this.results.components.failed++;
        this.results.components.details.push(`❌ ${component.file} - Missing`);
        return;
      }

      try {
        const content = readFileSync(filePath, 'utf8');
        
        if (component.element && content.includes(`@customElement('${component.element}')`)) {
          this.results.components.passed++;
          this.results.components.details.push(`✅ ${component.element} component defined`);
        } else if (component.class && content.includes(`class ${component.class}`)) {
          this.results.components.passed++;
          this.results.components.details.push(`✅ ${component.class} service defined`);
        } else {
          this.results.components.failed++;
          this.results.components.details.push(`❌ ${component.file} - Invalid structure`);
        }
        
      } catch (error) {
        this.results.components.failed++;
        this.results.components.details.push(`❌ ${component.file} - Read error: ${error.message}`);
      }
    });
  }

  testPackageScripts() {
    console.log('📦 Testing Package Scripts...');
    
    const packageFile = join(this.frontendDir, 'package.json');
    
    if (!existsSync(packageFile)) {
      console.log('❌ package.json - Missing');
      return;
    }

    try {
      const packageJson = JSON.parse(readFileSync(packageFile, 'utf8'));
      const scripts = packageJson.scripts || {};
      
      const requiredScripts = ['dev:debug', 'dev:prod', 'build:debug', 'build:prod'];
      
      requiredScripts.forEach(script => {
        if (scripts[script]) {
          console.log(`✅ ${script} script defined`);
        } else {
          console.log(`❌ ${script} script missing`);
        }
      });
      
    } catch (error) {
      console.log(`❌ package.json - Parse error: ${error.message}`);
    }
  }

  printSummary() {
    console.log('\n📊 Quick Test Summary:');
    console.log('======================');
    
    Object.entries(this.results).forEach(([category, result]) => {
      const total = result.passed + result.failed;
      const percentage = total > 0 ? Math.round((result.passed / total) * 100) : 0;
      const status = result.failed === 0 ? '✅' : '⚠️';
      
      console.log(`\n${status} ${category.toUpperCase()}: ${result.passed}/${total} (${percentage}%)`);
      
      if (result.details.length > 0) {
        result.details.forEach(detail => console.log(`  ${detail}`));
      }
    });

    // Overall assessment
    const totalPassed = Object.values(this.results).reduce((sum, r) => sum + r.passed, 0);
    const totalFailed = Object.values(this.results).reduce((sum, r) => sum + r.failed, 0);
    const overallPercentage = totalPassed + totalFailed > 0 ? 
      Math.round((totalPassed / (totalPassed + totalFailed)) * 100) : 0;

    console.log('\n🎯 Overall Assessment:');
    if (overallPercentage >= 90) {
      console.log('✅ EXCELLENT - Ready for testing');
    } else if (overallPercentage >= 75) {
      console.log('⚠️ GOOD - Minor issues to address');
    } else if (overallPercentage >= 50) {
      console.log('🟡 FAIR - Several issues need fixing');
    } else {
      console.log('❌ POOR - Major issues need attention');
    }

    console.log(`📈 Success Rate: ${overallPercentage}%`);

    // Next steps
    console.log('\n💡 Next Steps:');
    if (totalFailed === 0) {
      console.log('1. Run backend health check: node backend-health-checker.js');
      console.log('2. Start WebSocket monitor: node websocket-monitor.js');
      console.log('3. Test debug mode: npm run dev:debug');
      console.log('4. Test production mode: npm run dev:prod');
    } else {
      console.log('1. Fix the failed tests above');
      console.log('2. Re-run this quick test');
      console.log('3. Proceed with backend testing');
    }
  }
}

// Run quick test if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const quickTest = new QuickFrontendTest();
  quickTest.runQuickTest().catch(console.error);
}

export { QuickFrontendTest };
