#!/usr/bin/env node

/**
 * Critical Issues Test
 * Reproduces and tests the specific issues identified in console and Celery logs
 */

import WebSocket from 'ws';
import { CONFIG } from './config.js';
import { writeFileSync, mkdirSync } from 'fs';
import { join } from 'path';

class CriticalIssuesTest {
  constructor() {
    this.results = {
      llmConfigError: { reproduced: false, details: [] },
      profileRetrievalError: { reproduced: false, details: [] },
      debugPanelState: { reproduced: false, details: [] },
      messageStructure: { reproduced: false, details: [] },
      summary: { totalIssues: 4, reproduced: 0, fixed: 0 }
    };
    
    // Ensure logs directory exists
    try {
      mkdirSync('./logs', { recursive: true });
    } catch (e) {
      // Directory already exists
    }
  }

  async runAllTests() {
    console.log('🔥 Critical Issues Test Starting...');
    console.log('🎯 Reproducing exact issues from console/Celery logs');
    console.log('================================================\n');

    await this.testLLMConfigError();
    await this.testProfileRetrievalError();
    await this.testDebugPanelStatePersistence();
    await this.testMessageStructureIssues();

    this.generateReport();
    return this.results;
  }

  async testLLMConfigError() {
    console.log('🧪 Testing LLM Config Error...');
    
    try {
      // Reproduce the exact scenario from Celery logs:
      // "No LLMConfig provided, no default found, and no valid environment variables set for default LLMConfig."
      
      const ws = new WebSocket(CONFIG.backend.websocketUrl);
      
      await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => reject(new Error('Connection timeout')), 5000);
        
        ws.on('open', () => {
          clearTimeout(timeout);
          
          // Send message WITHOUT LLM config (reproducing the error)
          const messageWithoutLLMConfig = {
            type: 'chat_message',
            content: {
              message: 'Test message to reproduce LLM config error',
              user_profile_id: '2',
              timestamp: new Date().toISOString()
              // Missing metadata.llm_config_id - this should cause the error
            }
          };
          
          this.results.llmConfigError.details.push('Sending message without LLM config...');
          ws.send(JSON.stringify(messageWithoutLLMConfig));
          
          // Listen for error response
          ws.on('message', (data) => {
            try {
              const message = JSON.parse(data);
              
              if (message.type === 'error' && 
                  message.content && 
                  message.content.includes('No LLMConfig provided')) {
                
                this.results.llmConfigError.reproduced = true;
                this.results.llmConfigError.details.push('✅ Successfully reproduced LLM config error');
                this.results.llmConfigError.details.push(`Error message: ${message.content}`);
                this.results.summary.reproduced++;
              }
              
              if (message.type === 'debug_info') {
                this.results.llmConfigError.details.push(`Debug info: ${JSON.stringify(message.content)}`);
              }
              
            } catch (e) {
              this.results.llmConfigError.details.push(`Parse error: ${e.message}`);
            }
          });
          
          // Wait for response
          setTimeout(() => {
            ws.close();
            resolve();
          }, 3000);
        });
        
        ws.on('error', (error) => {
          clearTimeout(timeout);
          this.results.llmConfigError.details.push(`Connection error: ${error.message}`);
          reject(error);
        });
      });
      
    } catch (error) {
      this.results.llmConfigError.details.push(`Test error: ${error.message}`);
    }
    
    console.log(`   Result: ${this.results.llmConfigError.reproduced ? '✅ Reproduced' : '❌ Not reproduced'}\n`);
  }

  async testProfileRetrievalError() {
    console.log('🧪 Testing Profile Retrieval Error...');
    
    try {
      // Reproduce: "An unexpected error occurred while retrieving your profile"
      
      const ws = new WebSocket(CONFIG.backend.websocketUrl);
      
      await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => reject(new Error('Connection timeout')), 5000);
        
        ws.on('open', () => {
          clearTimeout(timeout);
          
          // Send message with invalid user ID
          const messageWithInvalidUser = {
            type: 'chat_message',
            content: {
              message: 'Test message with invalid user',
              user_profile_id: 'invalid-user-id-999',
              timestamp: new Date().toISOString(),
              metadata: {
                llm_config_id: '5'
              }
            }
          };
          
          this.results.profileRetrievalError.details.push('Sending message with invalid user ID...');
          ws.send(JSON.stringify(messageWithInvalidUser));
          
          ws.on('message', (data) => {
            try {
              const message = JSON.parse(data);
              
              if (message.type === 'error' && 
                  message.content && 
                  message.content.includes('retrieving your profile')) {
                
                this.results.profileRetrievalError.reproduced = true;
                this.results.profileRetrievalError.details.push('✅ Successfully reproduced profile retrieval error');
                this.results.profileRetrievalError.details.push(`Error message: ${message.content}`);
                this.results.summary.reproduced++;
              }
              
            } catch (e) {
              this.results.profileRetrievalError.details.push(`Parse error: ${e.message}`);
            }
          });
          
          setTimeout(() => {
            ws.close();
            resolve();
          }, 3000);
        });
        
        ws.on('error', (error) => {
          clearTimeout(timeout);
          this.results.profileRetrievalError.details.push(`Connection error: ${error.message}`);
          reject(error);
        });
      });
      
    } catch (error) {
      this.results.profileRetrievalError.details.push(`Test error: ${error.message}`);
    }
    
    console.log(`   Result: ${this.results.profileRetrievalError.reproduced ? '✅ Reproduced' : '❌ Not reproduced'}\n`);
  }

  async testDebugPanelStatePersistence() {
    console.log('🧪 Testing Debug Panel State Persistence...');
    
    try {
      // Test localStorage persistence issues
      const testStorage = {
        'debug_selected_user_id': '2',
        'debug_selected_llm_config_id': '5',
        'debug_backend_url': 'ws://localhost:8000/ws/game/'
      };
      
      // Simulate setting values
      this.results.debugPanelState.details.push('Simulating debug panel value storage...');
      
      // Test if values would persist (simulated)
      let persistenceWorking = true;
      
      Object.entries(testStorage).forEach(([key, value]) => {
        // Simulate localStorage operations
        try {
          // This would be: localStorage.setItem(key, value);
          // Then: const retrieved = localStorage.getItem(key);
          const retrieved = value; // Simulated retrieval
          
          if (retrieved !== value) {
            persistenceWorking = false;
            this.results.debugPanelState.details.push(`❌ ${key}: expected ${value}, got ${retrieved}`);
          } else {
            this.results.debugPanelState.details.push(`✅ ${key}: persisted correctly`);
          }
        } catch (error) {
          persistenceWorking = false;
          this.results.debugPanelState.details.push(`❌ ${key}: storage error - ${error.message}`);
        }
      });
      
      // Test the specific issue: debug panel not restoring selections
      if (!testStorage['debug_selected_llm_config_id']) {
        this.results.debugPanelState.reproduced = true;
        this.results.debugPanelState.details.push('✅ Reproduced: LLM config not persisting');
        this.results.summary.reproduced++;
      }
      
      if (persistenceWorking) {
        this.results.debugPanelState.details.push('✅ Storage persistence working correctly');
      } else {
        this.results.debugPanelState.reproduced = true;
        this.results.debugPanelState.details.push('✅ Reproduced: Debug panel state persistence issues');
        this.results.summary.reproduced++;
      }
      
    } catch (error) {
      this.results.debugPanelState.details.push(`Test error: ${error.message}`);
    }
    
    console.log(`   Result: ${this.results.debugPanelState.reproduced ? '✅ Reproduced' : '❌ Not reproduced'}\n`);
  }

  async testMessageStructureIssues() {
    console.log('🧪 Testing Message Structure Issues...');
    
    try {
      // Test various message structure problems
      const testMessages = [
        {
          name: 'Missing content wrapper',
          message: {
            type: 'chat_message',
            message: 'Direct message field', // Wrong - should be in content
            user_profile_id: '2'
          },
          shouldFail: true
        },
        {
          name: 'Missing metadata',
          message: {
            type: 'chat_message',
            content: {
              message: 'Test message',
              user_profile_id: '2',
              timestamp: new Date().toISOString()
              // Missing metadata.llm_config_id
            }
          },
          shouldFail: true
        },
        {
          name: 'Correct structure',
          message: {
            type: 'chat_message',
            content: {
              message: 'Test message',
              user_profile_id: '2',
              timestamp: new Date().toISOString(),
              metadata: {
                llm_config_id: '5'
              }
            }
          },
          shouldFail: false
        }
      ];
      
      testMessages.forEach(test => {
        const messageStr = JSON.stringify(test.message);
        const hasContentWrapper = test.message.content !== undefined;
        const hasMetadata = test.message.content?.metadata !== undefined;
        const hasLLMConfig = test.message.content?.metadata?.llm_config_id !== undefined;
        
        this.results.messageStructure.details.push(`Testing: ${test.name}`);
        this.results.messageStructure.details.push(`  Has content wrapper: ${hasContentWrapper}`);
        this.results.messageStructure.details.push(`  Has metadata: ${hasMetadata}`);
        this.results.messageStructure.details.push(`  Has LLM config: ${hasLLMConfig}`);
        
        if (test.shouldFail && (!hasContentWrapper || !hasMetadata || !hasLLMConfig)) {
          this.results.messageStructure.reproduced = true;
          this.results.messageStructure.details.push(`  ✅ Reproduced issue: ${test.name}`);
        }
      });
      
      if (this.results.messageStructure.reproduced) {
        this.results.summary.reproduced++;
      }
      
    } catch (error) {
      this.results.messageStructure.details.push(`Test error: ${error.message}`);
    }
    
    console.log(`   Result: ${this.results.messageStructure.reproduced ? '✅ Reproduced' : '❌ Not reproduced'}\n`);
  }

  generateReport() {
    console.log('📊 Critical Issues Test Report');
    console.log('==============================');
    
    const issues = [
      { name: 'LLM Config Error', result: this.results.llmConfigError },
      { name: 'Profile Retrieval Error', result: this.results.profileRetrievalError },
      { name: 'Debug Panel State', result: this.results.debugPanelState },
      { name: 'Message Structure', result: this.results.messageStructure }
    ];
    
    issues.forEach(issue => {
      console.log(`\n${issue.result.reproduced ? '🔥' : '✅'} ${issue.name}: ${issue.result.reproduced ? 'REPRODUCED' : 'NOT REPRODUCED'}`);
      issue.result.details.forEach(detail => {
        console.log(`   ${detail}`);
      });
    });
    
    console.log(`\n📈 Summary: ${this.results.summary.reproduced}/${this.results.summary.totalIssues} issues reproduced`);
    
    if (this.results.summary.reproduced > 0) {
      console.log('\n💡 Next Steps:');
      console.log('1. Fix debug panel state persistence');
      console.log('2. Ensure LLM config is always included in messages');
      console.log('3. Validate user IDs before sending messages');
      console.log('4. Use correct WebSocket message structure');
    } else {
      console.log('\n✅ All issues appear to be resolved!');
    }
    
    // Save report
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const reportFile = join('./logs', `critical-issues-test-${timestamp}.json`);
    writeFileSync(reportFile, JSON.stringify(this.results, null, 2));
    console.log(`\n📄 Detailed report saved to: ${reportFile}`);
  }
}

// Run test if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const test = new CriticalIssuesTest();
  test.runAllTests().catch(console.error);
}

export { CriticalIssuesTest };
