/**
 * Frontend Wheel Display Validation Test
 * 
 * This script tests the complete frontend wheel display system:
 * 1. WebSocket message reception and processing
 * 2. Wheel state machine integration
 * 3. Domain color system application
 * 4. Wheel component rendering
 * 5. Quality visualization (domain diversity, energy distribution)
 */

console.log('🎯 Frontend Wheel Display Validation Test');
console.log('==========================================');

// Test configuration
const TEST_CONFIG = {
    wheelGenerationTimeout: 30000, // 30 seconds
    expectedMinDomains: 3,
    expectedPhysicalPercentage: 0.7, // 70% for high energy
    testEnergyLevel: 100,
    testTimeAvailable: 10
};

// Test state
let testResults = {
    websocketConnection: false,
    wheelDataReceived: false,
    wheelStateUpdated: false,
    domainColorsApplied: false,
    wheelRendered: false,
    qualityMetrics: {}
};

/**
 * Main test execution
 */
async function runFrontendWheelDisplayTest() {
    try {
        console.log('🚀 Starting frontend wheel display validation...');
        
        // Step 1: Validate WebSocket connection
        await validateWebSocketConnection();
        
        // Step 2: Set up wheel data monitoring
        setupWheelDataMonitoring();
        
        // Step 3: Trigger wheel generation
        await triggerWheelGeneration();
        
        // Step 4: Wait for and validate wheel data
        await waitForWheelData();
        
        // Step 5: Validate wheel display
        await validateWheelDisplay();
        
        // Step 6: Analyze quality metrics
        analyzeQualityMetrics();
        
        // Step 7: Generate report
        generateTestReport();
        
    } catch (error) {
        console.error('❌ Test failed:', error);
        generateErrorReport(error);
    }
}

/**
 * Validate WebSocket connection
 */
async function validateWebSocketConnection() {
    console.log('\n📡 Step 1: Validating WebSocket Connection');
    
    // Check if app-shell exists
    const appShell = document.querySelector('app-shell');
    if (!appShell) {
        throw new Error('App-shell component not found');
    }
    
    // Check WebSocket manager
    const wsManager = appShell.websocketManager;
    if (!wsManager) {
        throw new Error('WebSocket manager not found');
    }
    
    // Check connection status
    if (!wsManager.isConnected()) {
        console.log('⏳ Waiting for WebSocket connection...');
        await new Promise((resolve, reject) => {
            const timeout = setTimeout(() => reject(new Error('WebSocket connection timeout')), 5000);
            wsManager.onConnectionChange((connected) => {
                if (connected) {
                    clearTimeout(timeout);
                    resolve();
                }
            });
        });
    }
    
    testResults.websocketConnection = true;
    console.log('✅ WebSocket connection validated');
}

/**
 * Set up monitoring for wheel data reception
 */
function setupWheelDataMonitoring() {
    console.log('\n🔍 Step 2: Setting up wheel data monitoring');
    
    const appShell = document.querySelector('app-shell');
    
    // Monitor wheel state machine changes
    if (appShell.wheelStateMachine) {
        const originalSubscribe = appShell.wheelStateMachine.subscribe;
        appShell.wheelStateMachine.subscribe = function(callback) {
            return originalSubscribe.call(this, (context) => {
                console.log('🎡 Wheel state changed:', context.state);
                if (context.state === 'POPULATED' && context.data) {
                    testResults.wheelStateUpdated = true;
                    testResults.wheelData = context.data;
                    console.log('✅ Wheel state updated to POPULATED');
                }
                callback(context);
            });
        };
    }
    
    // Monitor WebSocket wheel_data messages
    const wsManager = appShell.websocketManager;
    const originalOnMessage = wsManager.onMessage;
    wsManager.onMessage = function(type, handler) {
        if (type === 'wheel_data') {
            return originalOnMessage.call(this, type, (data) => {
                console.log('🎡 Wheel data received via WebSocket:', data);
                testResults.wheelDataReceived = true;
                testResults.rawWheelData = data;
                handler(data);
            });
        }
        return originalOnMessage.call(this, type, handler);
    };
    
    console.log('✅ Wheel data monitoring set up');
}

/**
 * Trigger wheel generation
 */
async function triggerWheelGeneration() {
    console.log('\n🎲 Step 3: Triggering wheel generation');
    
    const appShell = document.querySelector('app-shell');
    
    // Set energy and time values
    appShell.energyLevel = TEST_CONFIG.testEnergyLevel;
    appShell.timeAvailable = TEST_CONFIG.testTimeAvailable;
    
    console.log(`⚡ Energy level: ${TEST_CONFIG.testEnergyLevel}%`);
    console.log(`⏰ Time available: ${TEST_CONFIG.testTimeAvailable} minutes`);
    
    // Trigger wheel generation via button click or direct method
    const generateButton = appShell.shadowRoot?.querySelector('.generate-wheel-button');
    if (generateButton) {
        generateButton.click();
        console.log('✅ Wheel generation triggered via button');
    } else {
        // Fallback: trigger via direct method
        if (appShell.generateWheel) {
            await appShell.generateWheel();
            console.log('✅ Wheel generation triggered via direct method');
        } else {
            throw new Error('Could not find wheel generation trigger');
        }
    }
}

/**
 * Wait for wheel data to be received and processed
 */
async function waitForWheelData() {
    console.log('\n⏳ Step 4: Waiting for wheel data...');
    
    const startTime = Date.now();
    const timeout = TEST_CONFIG.wheelGenerationTimeout;
    
    return new Promise((resolve, reject) => {
        const checkInterval = setInterval(() => {
            const elapsed = Date.now() - startTime;
            
            if (testResults.wheelDataReceived && testResults.wheelStateUpdated) {
                clearInterval(checkInterval);
                console.log(`✅ Wheel data received and processed in ${elapsed}ms`);
                resolve();
            } else if (elapsed > timeout) {
                clearInterval(checkInterval);
                reject(new Error(`Wheel generation timeout after ${timeout}ms`));
            } else {
                console.log(`⏳ Waiting... (${elapsed}ms elapsed)`);
            }
        }, 1000);
    });
}

/**
 * Validate wheel display components
 */
async function validateWheelDisplay() {
    console.log('\n🎨 Step 5: Validating wheel display');
    
    const appShell = document.querySelector('app-shell');
    const gameWheel = appShell.shadowRoot?.querySelector('game-wheel');
    
    if (!gameWheel) {
        throw new Error('Game wheel component not found');
    }
    
    // Check if wheel has data
    if (!gameWheel.wheelData || !gameWheel.wheelData.segments) {
        throw new Error('Game wheel has no data');
    }
    
    const segments = gameWheel.wheelData.segments;
    console.log(`🎡 Wheel has ${segments.length} segments`);
    
    // Validate domain colors
    const domainsWithColors = segments.filter(s => s.color && s.color.match(/^#[0-9A-Fa-f]{6}$/));
    testResults.domainColorsApplied = domainsWithColors.length === segments.length;
    
    console.log(`🎨 Segments with valid colors: ${domainsWithColors.length}/${segments.length}`);
    
    // Check if wheel is rendered
    const canvas = gameWheel.shadowRoot?.querySelector('canvas');
    testResults.wheelRendered = !!canvas;
    
    console.log(`🖼️ Wheel canvas rendered: ${testResults.wheelRendered}`);
    
    console.log('✅ Wheel display validation complete');
}

/**
 * Analyze quality metrics
 */
function analyzeQualityMetrics() {
    console.log('\n📊 Step 6: Analyzing quality metrics');
    
    if (!testResults.wheelData || !testResults.wheelData.segments) {
        console.log('❌ No wheel data available for quality analysis');
        return;
    }
    
    const segments = testResults.wheelData.segments;
    
    // Domain diversity analysis
    const domains = new Set(segments.map(s => s.domain || 'unknown'));
    const domainDiversity = domains.size;
    
    // Physical activity analysis
    const physicalSegments = segments.filter(s => (s.domain || '').startsWith('phys'));
    const physicalPercentage = physicalSegments.length / segments.length;
    
    // Store metrics
    testResults.qualityMetrics = {
        totalSegments: segments.length,
        domainDiversity,
        domains: Array.from(domains),
        physicalCount: physicalSegments.length,
        physicalPercentage,
        expectedPhysicalPercentage: TEST_CONFIG.expectedPhysicalPercentage
    };
    
    console.log(`🌈 Domain diversity: ${domainDiversity} domains`);
    console.log(`📋 Domains: ${Array.from(domains).join(', ')}`);
    console.log(`💪 Physical activities: ${physicalSegments.length}/${segments.length} (${(physicalPercentage * 100).toFixed(1)}%)`);
    
    console.log('✅ Quality metrics analyzed');
}

/**
 * Generate comprehensive test report
 */
function generateTestReport() {
    console.log('\n📋 FRONTEND WHEEL DISPLAY TEST REPORT');
    console.log('=====================================');
    
    const results = testResults;
    const metrics = results.qualityMetrics;
    
    // Connection and data flow
    console.log('\n🔌 Connection & Data Flow:');
    console.log(`  WebSocket Connection: ${results.websocketConnection ? '✅' : '❌'}`);
    console.log(`  Wheel Data Received: ${results.wheelDataReceived ? '✅' : '❌'}`);
    console.log(`  Wheel State Updated: ${results.wheelStateUpdated ? '✅' : '❌'}`);
    
    // Display validation
    console.log('\n🎨 Display Validation:');
    console.log(`  Domain Colors Applied: ${results.domainColorsApplied ? '✅' : '❌'}`);
    console.log(`  Wheel Rendered: ${results.wheelRendered ? '✅' : '❌'}`);
    
    // Quality metrics
    if (metrics.totalSegments) {
        console.log('\n📊 Quality Metrics:');
        console.log(`  Total Segments: ${metrics.totalSegments}`);
        console.log(`  Domain Diversity: ${metrics.domainDiversity} (Expected: ≥${TEST_CONFIG.expectedMinDomains})`);
        console.log(`  Physical Activities: ${metrics.physicalCount}/${metrics.totalSegments} (${(metrics.physicalPercentage * 100).toFixed(1)}%)`);
        console.log(`  Expected Physical: ≥${(TEST_CONFIG.expectedPhysicalPercentage * 100).toFixed(0)}% for ${TEST_CONFIG.testEnergyLevel}% energy`);
        
        // Quality assessment
        const domainDiversityOk = metrics.domainDiversity >= TEST_CONFIG.expectedMinDomains;
        const physicalPercentageOk = metrics.physicalPercentage >= TEST_CONFIG.expectedPhysicalPercentage;
        
        console.log('\n✅ Quality Assessment:');
        console.log(`  Domain Diversity: ${domainDiversityOk ? '✅ PASS' : '❌ FAIL'}`);
        console.log(`  Physical Distribution: ${physicalPercentageOk ? '✅ PASS' : '❌ FAIL'}`);
    }
    
    // Overall result
    const overallSuccess = results.websocketConnection && 
                          results.wheelDataReceived && 
                          results.wheelStateUpdated && 
                          results.domainColorsApplied && 
                          results.wheelRendered;
    
    console.log('\n🎯 OVERALL RESULT:');
    console.log(`${overallSuccess ? '✅ FRONTEND WHEEL DISPLAY TEST PASSED' : '❌ FRONTEND WHEEL DISPLAY TEST FAILED'}`);
    
    return overallSuccess;
}

/**
 * Generate error report
 */
function generateErrorReport(error) {
    console.log('\n❌ FRONTEND WHEEL DISPLAY TEST ERROR REPORT');
    console.log('==========================================');
    console.log(`Error: ${error.message}`);
    console.log(`Stack: ${error.stack}`);
    console.log('\nTest Results at Time of Error:');
    console.log(JSON.stringify(testResults, null, 2));
}

// Auto-run test when script is loaded
if (typeof window !== 'undefined') {
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', runFrontendWheelDisplayTest);
    } else {
        // DOM is already ready
        setTimeout(runFrontendWheelDisplayTest, 1000);
    }
}

// Export for manual execution
window.runFrontendWheelDisplayTest = runFrontendWheelDisplayTest;
