#!/usr/bin/env node

/**
 * Playwright UI Fixes Validator
 * 
 * Tests the fixes for the three critical UI issues:
 * 1. Unstable connection (FIXED: removed wsConnected override in demo mode)
 * 2. Chat scrolling impossibility (TESTING: scroll functionality)
 * 3. Long mentor response times (TESTING: actual response times)
 */

const { chromium } = require('playwright');
const fs = require('fs');

class UIFixesValidator {
    constructor() {
        this.browser = null;
        this.context = null;
        this.page = null;
        this.testResults = {
            connectionStability: {
                status: 'unknown',
                wsConnected: false,
                chatInputEnabled: false,
                connectionChanges: 0,
                timeline: []
            },
            chatScrolling: {
                status: 'unknown',
                scrollable: false,
                scrollWorking: false,
                messagesAdded: 0,
                scrollTests: []
            },
            mentorResponseTime: {
                status: 'unknown',
                averageResponseTime: 0,
                responses: [],
                timeouts: 0
            }
        };
        this.startTime = Date.now();
    }

    async initialize() {
        console.log('🔧 Initializing UI Fixes Validator...');
        
        this.browser = await chromium.launch({
            headless: false,
            slowMo: 1000
        });

        this.context = await this.browser.newContext({
            viewport: { width: 1280, height: 720 }
        });

        this.page = await this.context.newPage();
        
        // Monitor console for connection events
        this.page.on('console', msg => {
            const text = msg.text();
            const timestamp = Date.now() - this.startTime;
            
            console.log(`🖥️  [${msg.type()}] ${text}`);
            
            if (text.includes('Connected') || text.includes('WebSocket') || text.includes('connection')) {
                this.testResults.connectionStability.timeline.push({
                    timestamp,
                    message: text
                });
            }
        });

        await this.setupWebSocketMonitoring();
        console.log('✅ Validator initialized');
    }

    async setupWebSocketMonitoring() {
        await this.page.routeWebSocket('**/ws/**', ws => {
            console.log(`🔗 WebSocket intercepted: ${ws.url()}`);
            
            const server = ws.connectToServer();
            
            server.onMessage(message => {
                const timestamp = Date.now() - this.startTime;
                console.log(`📨 WebSocket ← Server (${timestamp}ms):`, message.substring(0, 100) + '...');
                
                try {
                    const parsed = JSON.parse(message);
                    if (parsed.type === 'ai_response' || parsed.type === 'chat_message') {
                        this.testResults.mentorResponseTime.responses.push({
                            timestamp,
                            responseTime: timestamp,
                            type: parsed.type,
                            content: parsed.content?.substring(0, 50) || 'No content'
                        });
                    }
                } catch (e) {
                    // Not JSON, ignore
                }
            });

            ws.onMessage(message => {
                console.log('📤 WebSocket → Server:', message.substring(0, 100) + '...');
                server.send(message);
            });
        });
    }

    async testConnectionStability() {
        console.log('🔌 Testing connection stability fix...');
        
        // Navigate to app
        await this.page.goto('http://localhost:3001');
        await this.page.waitForLoadState('networkidle');
        await this.page.waitForTimeout(5000);
        
        // Check connection status
        const connectionStatus = await this.page.evaluate(() => {
            return {
                wsConnected: !!window.websocket && window.websocket.readyState === 1,
                stateManagerExists: !!window.stateManager,
                chatInputDisabled: document.querySelector('.message-input')?.disabled || false
            };
        });
        
        console.log('📊 Connection Status:', connectionStatus);
        
        this.testResults.connectionStability.wsConnected = connectionStatus.wsConnected;
        this.testResults.connectionStability.chatInputEnabled = !connectionStatus.chatInputDisabled;
        
        // Test chat input enabling
        const chatInput = this.page.locator('.message-input').first();
        const isEnabled = await chatInput.evaluate(el => !el.disabled);
        
        if (isEnabled) {
            this.testResults.connectionStability.status = 'FIXED';
            console.log('✅ Connection stability FIXED - chat input is enabled');
        } else {
            this.testResults.connectionStability.status = 'FAILED';
            console.log('❌ Connection stability FAILED - chat input still disabled');
        }
        
        return this.testResults.connectionStability;
    }

    async testChatScrolling() {
        console.log('📜 Testing chat scrolling functionality...');
        
        // Find chat container
        const chatContainer = this.page.locator('.messages-container').first();
        const containerExists = await chatContainer.count() > 0;
        
        if (!containerExists) {
            this.testResults.chatScrolling.status = 'FAILED';
            console.log('❌ Chat container not found');
            return this.testResults.chatScrolling;
        }
        
        // Add multiple messages to test scrolling
        console.log('📝 Adding test messages to trigger scrolling...');
        
        const chatInput = this.page.locator('.message-input').first();
        const isEnabled = await chatInput.evaluate(el => !el.disabled);
        
        if (isEnabled) {
            // Send multiple messages to create scrollable content
            const testMessages = [
                "Test message 1 - checking scrolling functionality",
                "Test message 2 - adding more content to the chat",
                "Test message 3 - this should make the chat scrollable",
                "Test message 4 - continuing to add messages",
                "Test message 5 - final test message for scrolling"
            ];
            
            for (const message of testMessages) {
                await chatInput.fill(message);
                await chatInput.press('Enter');
                await this.page.waitForTimeout(2000); // Wait for message to appear
                this.testResults.chatScrolling.messagesAdded++;
            }
            
            // Wait for all messages to be processed
            await this.page.waitForTimeout(5000);
        }
        
        // Test scrolling properties
        const scrollInfo = await chatContainer.evaluate(element => {
            return {
                scrollHeight: element.scrollHeight,
                clientHeight: element.clientHeight,
                scrollTop: element.scrollTop,
                isScrollable: element.scrollHeight > element.clientHeight
            };
        });
        
        console.log('📊 Scroll Info:', scrollInfo);
        
        this.testResults.chatScrolling.scrollable = scrollInfo.isScrollable;
        
        if (scrollInfo.isScrollable) {
            // Test actual scrolling
            const initialScrollTop = scrollInfo.scrollTop;
            
            // Scroll to top
            await chatContainer.evaluate(element => {
                element.scrollTop = 0;
            });
            
            await this.page.waitForTimeout(500);
            
            // Scroll to bottom
            await chatContainer.evaluate(element => {
                element.scrollTop = element.scrollHeight;
            });
            
            await this.page.waitForTimeout(500);
            
            const finalScrollInfo = await chatContainer.evaluate(element => ({
                scrollTop: element.scrollTop,
                scrollHeight: element.scrollHeight
            }));
            
            const scrollWorking = finalScrollInfo.scrollTop > 0;
            this.testResults.chatScrolling.scrollWorking = scrollWorking;
            
            if (scrollWorking) {
                this.testResults.chatScrolling.status = 'WORKING';
                console.log('✅ Chat scrolling is WORKING');
            } else {
                this.testResults.chatScrolling.status = 'FAILED';
                console.log('❌ Chat scrolling FAILED - scroll position not changing');
            }
        } else {
            this.testResults.chatScrolling.status = 'NOT_NEEDED';
            console.log('ℹ️  Chat scrolling not needed - content fits in container');
        }
        
        return this.testResults.chatScrolling;
    }

    async testMentorResponseTime() {
        console.log('🤖 Testing mentor response time...');
        
        // Enable debug panel and select user
        await this.page.keyboard.press('Control+Shift+D');
        await this.page.waitForTimeout(1000);
        
        const userSelect = this.page.locator('select[name="user"]').first();
        const selectExists = await userSelect.count() > 0;
        
        if (selectExists) {
            await userSelect.selectOption('2');
            console.log('✅ Selected user 2 (phiphi)');
            await this.page.waitForTimeout(1000);
        }
        
        // Test simple question response time
        const chatInput = this.page.locator('.message-input').first();
        const isEnabled = await chatInput.evaluate(el => !el.disabled);
        
        if (!isEnabled) {
            this.testResults.mentorResponseTime.status = 'FAILED';
            console.log('❌ Cannot test response time - chat input disabled');
            return this.testResults.mentorResponseTime;
        }
        
        const testQuestion = "hey, do you recognize me ?";
        console.log(`💬 Sending test question: "${testQuestion}"`);
        
        const startTime = Date.now();
        
        await chatInput.fill(testQuestion);
        await chatInput.press('Enter');
        
        // Wait for response (up to 30 seconds)
        let responseReceived = false;
        let waitTime = 0;
        const maxWaitTime = 30000;
        
        while (!responseReceived && waitTime < maxWaitTime) {
            await this.page.waitForTimeout(1000);
            waitTime += 1000;
            
            // Check for AI response in messages
            const aiMessages = await this.page.locator('message-bubble[data-type="ai"], .message.ai, .ai-message').count();
            if (aiMessages > 0) {
                responseReceived = true;
                const responseTime = Date.now() - startTime;
                console.log(`✅ Response received in ${responseTime}ms`);
                
                this.testResults.mentorResponseTime.responses.push({
                    question: testQuestion,
                    responseTime,
                    timestamp: Date.now() - this.startTime
                });
                
                if (responseTime < 10000) {
                    this.testResults.mentorResponseTime.status = 'GOOD';
                    console.log('✅ Response time is GOOD (< 10 seconds)');
                } else if (responseTime < 20000) {
                    this.testResults.mentorResponseTime.status = 'ACCEPTABLE';
                    console.log('⚠️  Response time is ACCEPTABLE (10-20 seconds)');
                } else {
                    this.testResults.mentorResponseTime.status = 'SLOW';
                    console.log('⚠️  Response time is SLOW (> 20 seconds)');
                }
                
                this.testResults.mentorResponseTime.averageResponseTime = responseTime;
            }
        }
        
        if (!responseReceived) {
            this.testResults.mentorResponseTime.status = 'TIMEOUT';
            this.testResults.mentorResponseTime.timeouts++;
            console.log('❌ No response received within 30 seconds - TIMEOUT');
        }
        
        return this.testResults.mentorResponseTime;
    }

    async generateValidationReport() {
        const report = {
            timestamp: new Date().toISOString(),
            duration: Date.now() - this.startTime,
            testResults: this.testResults,
            summary: {
                connectionStability: this.testResults.connectionStability.status,
                chatScrolling: this.testResults.chatScrolling.status,
                mentorResponseTime: this.testResults.mentorResponseTime.status,
                overallStatus: this.calculateOverallStatus()
            }
        };

        const reportPath = `./test-results/ui-fixes-validation-${Date.now()}.json`;
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        
        console.log('\n🔧 UI FIXES VALIDATION REPORT');
        console.log('═'.repeat(50));
        console.log(`📁 Report saved to: ${reportPath}`);
        console.log(`⏱️  Test Duration: ${Math.round(report.duration / 1000)}s`);
        
        console.log('\n📊 Test Results:');
        console.log(`🔌 Connection Stability: ${this.getStatusEmoji(this.testResults.connectionStability.status)} ${this.testResults.connectionStability.status}`);
        console.log(`📜 Chat Scrolling: ${this.getStatusEmoji(this.testResults.chatScrolling.status)} ${this.testResults.chatScrolling.status}`);
        console.log(`🤖 Mentor Response Time: ${this.getStatusEmoji(this.testResults.mentorResponseTime.status)} ${this.testResults.mentorResponseTime.status}`);
        
        console.log(`\n🎯 Overall Status: ${this.getStatusEmoji(report.summary.overallStatus)} ${report.summary.overallStatus}`);
        
        return report;
    }

    calculateOverallStatus() {
        const statuses = [
            this.testResults.connectionStability.status,
            this.testResults.chatScrolling.status,
            this.testResults.mentorResponseTime.status
        ];
        
        if (statuses.includes('FAILED') || statuses.includes('TIMEOUT')) {
            return 'FAILED';
        } else if (statuses.includes('SLOW') || statuses.includes('ACCEPTABLE')) {
            return 'NEEDS_IMPROVEMENT';
        } else if (statuses.every(s => s === 'FIXED' || s === 'WORKING' || s === 'GOOD' || s === 'NOT_NEEDED')) {
            return 'ALL_FIXED';
        } else {
            return 'PARTIAL';
        }
    }

    getStatusEmoji(status) {
        const emojis = {
            'FIXED': '✅',
            'WORKING': '✅',
            'GOOD': '✅',
            'NOT_NEEDED': 'ℹ️',
            'ACCEPTABLE': '⚠️',
            'SLOW': '⚠️',
            'FAILED': '❌',
            'TIMEOUT': '❌',
            'ALL_FIXED': '🎉',
            'NEEDS_IMPROVEMENT': '⚠️',
            'PARTIAL': '🔄',
            'unknown': '❓'
        };
        return emojis[status] || '❓';
    }

    async cleanup() {
        console.log('🧹 Cleaning up...');
        if (this.page) await this.page.close();
        if (this.context) await this.context.close();
        if (this.browser) await this.browser.close();
    }
}

// Main execution
async function main() {
    const validator = new UIFixesValidator();
    
    try {
        await validator.initialize();
        
        // Test each fix
        await validator.testConnectionStability();
        await validator.testChatScrolling();
        await validator.testMentorResponseTime();
        
        // Generate validation report
        await validator.generateValidationReport();
        
    } catch (error) {
        console.error('❌ Validation failed:', error.message);
    } finally {
        await validator.cleanup();
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = UIFixesValidator;
