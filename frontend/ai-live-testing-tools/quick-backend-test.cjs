#!/usr/bin/env node

/**
 * Quick Backend Test
 * 
 * Simple test to check if the backend is ready and accessible.
 */

const http = require('http');

function testBackendHealth() {
    console.log('🔍 Testing Backend Health...');
    
    return new Promise((resolve) => {
        const req = http.request({
            hostname: 'localhost',
            port: 8000,
            path: '/admin/',
            method: 'GET',
            timeout: 5000
        }, (res) => {
            console.log(`✅ Backend responding: HTTP ${res.statusCode}`);
            resolve(true);
        });

        req.on('error', (error) => {
            console.log(`❌ Backend not accessible: ${error.message}`);
            resolve(false);
        });

        req.on('timeout', () => {
            console.log('❌ Backend health check timeout');
            req.destroy();
            resolve(false);
        });

        req.end();
    });
}

async function main() {
    const isHealthy = await testBackendHealth();
    
    if (isHealthy) {
        console.log('🎉 Backend is ready for testing!');
        process.exit(0);
    } else {
        console.log('⚠️ Backend is not ready yet. Please wait and try again.');
        process.exit(1);
    }
}

main().catch(console.error);
