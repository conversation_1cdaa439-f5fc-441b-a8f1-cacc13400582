const { chromium } = require('playwright');

async function testRealMode() {
    console.log('🚀 Simple Real Mode Test...');
    
    const browser = await chromium.launch({
        headless: false,  // Visual debugging
        slowMo: 500       // Slow down for observation
    });
    
    const page = await browser.newPage();
    
    // Monitor console messages
    page.on('console', msg => {
        const text = msg.text();
        console.log(`[BROWSER] ${text}`);
    });
    
    try {
        console.log('📱 Loading frontend...');
        await page.goto('http://localhost:3003', { 
            waitUntil: 'networkidle',
            timeout: 60000
        });
        
        console.log('✅ Frontend loaded successfully');
        
        // Wait and check for demo mode indicators
        await page.waitForTimeout(10000);
        
        // Check for demo mode text
        const demoModeElements = await page.locator('text=/demo mode/i').count();
        const realModeIndicators = await page.locator('text=/debug mode/i').count();
        
        console.log(`Demo mode elements found: ${demoModeElements}`);
        console.log(`Real mode indicators found: ${realModeIndicators}`);
        
        if (demoModeElements > 0) {
            console.log('🎭 DEMO MODE detected');
        } else if (realModeIndicators > 0) {
            console.log('🐛 DEBUG MODE detected (real backend connection)');
        } else {
            console.log('🚀 PRODUCTION MODE detected (real backend connection)');
        }
        
        // Try to send a message
        const chatInput = await page.locator('input[placeholder*="message"], textarea[placeholder*="message"], input[type="text"]').first();
        if (await chatInput.isVisible({ timeout: 5000 })) {
            console.log('💬 Chat input found, testing message...');
            await chatInput.fill('hey! do you recognize me?');
            await chatInput.press('Enter');
            
            console.log('📤 Message sent, waiting for response...');
            await page.waitForTimeout(15000);
            
            console.log('✅ Message test completed');
        } else {
            console.log('❌ Chat input not found');
        }
        
        // Keep browser open for manual inspection
        console.log('🔍 Browser will stay open for manual inspection...');
        console.log('Press Ctrl+C to close when done.');
        
        // Wait indefinitely
        await new Promise(() => {});
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
    } finally {
        // Don't close browser automatically for manual inspection
        // await browser.close();
    }
}

testRealMode().catch(console.error);
