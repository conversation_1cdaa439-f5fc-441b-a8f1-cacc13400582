#!/usr/bin/env node

/**
 * Backend Performance Analyzer
 * 
 * This tool analyzes backend performance issues:
 * 1. Monitors Docker container logs for performance issues
 * 2. Identifies cluttered/useless log data
 * 3. Measures response times and bottlenecks
 * 4. Analyzes mentor_agent.py performance specifically
 */

const { spawn } = require('child_process');
const fs = require('fs');

class BackendPerformanceAnalyzer {
    constructor() {
        this.logData = {
            web: [],
            celery: [],
            redis: []
        };
        this.performanceMetrics = {
            slowQueries: [],
            errors: [],
            duplicateMessages: [],
            uselessLogs: [],
            mentorAgentPerformance: []
        };
        this.startTime = Date.now();
    }

    async initialize() {
        console.log('⚡ Backend Performance Analyzer - Initializing...');
        console.log('🔍 This will monitor Docker containers for performance issues');
        console.log('════════════════════════════════════════════════════════════');
    }

    async monitorDockerLogs() {
        console.log('📊 Starting Docker log monitoring...');
        
        // Monitor web container
        this.monitorContainer('backend-web-1', 'web');
        
        // Monitor celery container  
        this.monitorContainer('backend-celery-1', 'celery');
        
        // Monitor redis container
        this.monitorContainer('backend-redis-1', 'redis');
        
        console.log('✅ Log monitoring started for all containers');
    }

    monitorContainer(containerName, type) {
        console.log(`🔍 Monitoring ${containerName}...`);
        
        const dockerLogs = spawn('docker', ['logs', '-f', '--tail', '50', containerName]);
        
        dockerLogs.stdout.on('data', (data) => {
            const logLine = data.toString();
            this.processLogLine(logLine, type, 'stdout');
        });
        
        dockerLogs.stderr.on('data', (data) => {
            const logLine = data.toString();
            this.processLogLine(logLine, type, 'stderr');
        });
        
        dockerLogs.on('error', (error) => {
            console.log(`❌ Error monitoring ${containerName}: ${error.message}`);
        });
    }

    processLogLine(logLine, containerType, stream) {
        const timestamp = Date.now();
        const logEntry = {
            timestamp: timestamp,
            container: containerType,
            stream: stream,
            content: logLine.trim(),
            analysis: this.analyzeLogLine(logLine)
        };
        
        this.logData[containerType].push(logEntry);
        
        // Real-time analysis and alerts
        if (logEntry.analysis.isImportant) {
            console.log(`🚨 [${containerType.toUpperCase()}] ${logEntry.analysis.category}: ${logLine.trim()}`);
        } else if (logEntry.analysis.isUseless) {
            // Count useless logs but don't spam console
            this.performanceMetrics.uselessLogs.push(logEntry);
        } else {
            // Normal important logs
            console.log(`📝 [${containerType.toUpperCase()}] ${logLine.trim()}`);
        }
        
        // Specific performance tracking
        this.trackPerformanceMetrics(logEntry);
    }

    analyzeLogLine(logLine) {
        const line = logLine.toLowerCase();
        
        // Identify important performance issues
        if (line.includes('slow query') || line.includes('timeout') || 
            line.includes('error') || line.includes('exception') ||
            line.includes('failed') || line.includes('warning')) {
            return {
                isImportant: true,
                category: 'PERFORMANCE_ISSUE',
                severity: 'high'
            };
        }
        
        // Identify mentor agent specific issues
        if (line.includes('mentor_agent') || line.includes('mentoragent')) {
            return {
                isImportant: true,
                category: 'MENTOR_AGENT',
                severity: 'medium'
            };
        }
        
        // Identify duplicate response patterns
        if (line.includes('duplicate') || line.includes('already sent') ||
            line.includes('repeated')) {
            return {
                isImportant: true,
                category: 'DUPLICATE_RESPONSE',
                severity: 'high'
            };
        }
        
        // Identify useless/cluttered logs
        if (line.includes('debug') && (
            line.includes('ping') || 
            line.includes('heartbeat') ||
            line.includes('connection check') ||
            line.includes('health check') ||
            line.includes('static file') ||
            line.includes('favicon') ||
            line.includes('css') ||
            line.includes('js')
        )) {
            return {
                isUseless: true,
                category: 'CLUTTER',
                reason: 'Debug/maintenance noise'
            };
        }
        
        // Identify database query patterns
        if (line.includes('select') || line.includes('insert') || 
            line.includes('update') || line.includes('delete')) {
            return {
                isImportant: false,
                category: 'DATABASE',
                severity: 'low'
            };
        }
        
        return {
            isImportant: false,
            category: 'NORMAL',
            severity: 'low'
        };
    }

    trackPerformanceMetrics(logEntry) {
        const content = logEntry.content;
        
        // Track slow queries
        if (content.includes('slow') || content.includes('timeout')) {
            this.performanceMetrics.slowQueries.push({
                timestamp: logEntry.timestamp,
                container: logEntry.container,
                content: content,
                type: 'slow_query'
            });
        }
        
        // Track errors
        if (content.includes('error') || content.includes('exception') || content.includes('failed')) {
            this.performanceMetrics.errors.push({
                timestamp: logEntry.timestamp,
                container: logEntry.container,
                content: content,
                type: 'error'
            });
        }
        
        // Track mentor agent performance
        if (content.includes('mentor_agent') || content.includes('MentorAgent')) {
            this.performanceMetrics.mentorAgentPerformance.push({
                timestamp: logEntry.timestamp,
                container: logEntry.container,
                content: content,
                type: 'mentor_agent'
            });
        }
        
        // Track potential duplicate responses
        if (content.includes('chat_message') && content.includes('response')) {
            // Look for patterns that might indicate duplicates
            const existingResponses = this.performanceMetrics.duplicateMessages.filter(msg => 
                Math.abs(msg.timestamp - logEntry.timestamp) < 5000 // Within 5 seconds
            );
            
            if (existingResponses.length > 0) {
                this.performanceMetrics.duplicateMessages.push({
                    timestamp: logEntry.timestamp,
                    container: logEntry.container,
                    content: content,
                    type: 'potential_duplicate',
                    relatedMessages: existingResponses.length
                });
            } else {
                this.performanceMetrics.duplicateMessages.push({
                    timestamp: logEntry.timestamp,
                    container: logEntry.container,
                    content: content,
                    type: 'response_message'
                });
            }
        }
    }

    async testBackendDirectly() {
        console.log('\n🧪 Testing backend directly...');
        console.log('════════════════════════════════════════════════════════════');
        
        try {
            // Test mentor agent directly
            console.log('🤖 Testing mentor agent performance...');
            
            const testScript = `
import asyncio
import time
from apps.main.services.conversation_dispatcher import ConversationDispatcher

async def test_mentor_performance():
    start_time = time.time()
    print(f"Starting mentor test at {start_time}")
    
    try:
        dispatcher = ConversationDispatcher(user_profile_id='2')
        
        message_start = time.time()
        result = await dispatcher.process_message({
            'text': 'hey! do you recognize me?',
            'timestamp': '2025-01-02T15:00:00.000Z',
            'metadata': {}
        })
        message_end = time.time()
        
        print(f"Message processing took: {message_end - message_start:.2f} seconds")
        print(f"Result type: {type(result)}")
        print(f"Result keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
        
        return {
            'success': True,
            'processing_time': message_end - message_start,
            'result': result
        }
        
    except Exception as e:
        error_time = time.time()
        print(f"Error after {error_time - start_time:.2f} seconds: {str(e)}")
        return {
            'success': False,
            'error': str(e),
            'processing_time': error_time - start_time
        }

# Run the test
result = asyncio.run(test_mentor_performance())
print(f"Final result: {result}")
`;
            
            // Write test script to container
            const testProcess = spawn('docker', [
                'exec', '-i', 'backend-web-1', 
                'python', '-c', testScript
            ]);
            
            let output = '';
            let errorOutput = '';
            
            testProcess.stdout.on('data', (data) => {
                const text = data.toString();
                output += text;
                console.log(`🐍 ${text.trim()}`);
            });
            
            testProcess.stderr.on('data', (data) => {
                const text = data.toString();
                errorOutput += text;
                console.log(`❌ ${text.trim()}`);
            });
            
            return new Promise((resolve) => {
                testProcess.on('close', (code) => {
                    console.log(`✅ Backend test completed with code: ${code}`);
                    resolve({
                        exitCode: code,
                        output: output,
                        error: errorOutput
                    });
                });
            });
            
        } catch (error) {
            console.log(`❌ Backend test failed: ${error.message}`);
            return { error: error.message };
        }
    }

    async generatePerformanceReport() {
        const runtime = Date.now() - this.startTime;
        
        console.log('\n📊 BACKEND PERFORMANCE ANALYSIS REPORT');
        console.log('════════════════════════════════════════════════════════════');
        
        const report = {
            timestamp: new Date().toISOString(),
            analysisRuntime: runtime,
            logAnalysis: {
                totalLogs: Object.values(this.logData).reduce((sum, logs) => sum + logs.length, 0),
                uselessLogs: this.performanceMetrics.uselessLogs.length,
                uselessPercentage: this.performanceMetrics.uselessLogs.length / 
                    Object.values(this.logData).reduce((sum, logs) => sum + logs.length, 0) * 100
            },
            performanceIssues: {
                slowQueries: this.performanceMetrics.slowQueries.length,
                errors: this.performanceMetrics.errors.length,
                potentialDuplicates: this.performanceMetrics.duplicateMessages.filter(m => m.type === 'potential_duplicate').length,
                mentorAgentIssues: this.performanceMetrics.mentorAgentPerformance.length
            },
            detailedMetrics: this.performanceMetrics,
            recommendations: this.generateRecommendations()
        };
        
        console.log('📈 Log Analysis:');
        console.log(`  Total logs processed: ${report.logAnalysis.totalLogs}`);
        console.log(`  Useless/cluttered logs: ${report.logAnalysis.uselessLogs} (${report.logAnalysis.uselessPercentage.toFixed(1)}%)`);
        
        console.log('\n⚡ Performance Issues:');
        console.log(`  Slow queries: ${report.performanceIssues.slowQueries}`);
        console.log(`  Errors: ${report.performanceIssues.errors}`);
        console.log(`  Potential duplicates: ${report.performanceIssues.potentialDuplicates}`);
        console.log(`  Mentor agent issues: ${report.performanceIssues.mentorAgentIssues}`);
        
        console.log('\n💡 Recommendations:');
        report.recommendations.forEach((rec, i) => {
            console.log(`  ${i + 1}. ${rec}`);
        });
        
        // Save detailed report
        const reportPath = `test-results/backend-performance-${Date.now()}.json`;
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        console.log(`\n📁 Detailed report saved to: ${reportPath}`);
        
        return report;
    }

    generateRecommendations() {
        const recommendations = [];
        
        if (this.performanceMetrics.uselessLogs.length > 50) {
            recommendations.push('Reduce debug log verbosity - too much clutter detected');
        }
        
        if (this.performanceMetrics.slowQueries.length > 0) {
            recommendations.push('Investigate slow database queries - performance bottleneck detected');
        }
        
        if (this.performanceMetrics.errors.length > 5) {
            recommendations.push('Address backend errors - multiple error patterns detected');
        }
        
        if (this.performanceMetrics.duplicateMessages.filter(m => m.type === 'potential_duplicate').length > 0) {
            recommendations.push('Investigate duplicate response generation - patterns detected in logs');
        }
        
        if (this.performanceMetrics.mentorAgentPerformance.length > 10) {
            recommendations.push('Optimize mentor_agent.py performance - high activity detected');
        }
        
        return recommendations;
    }

    async run() {
        try {
            await this.initialize();
            
            // Start monitoring logs
            await this.monitorDockerLogs();
            
            // Wait a bit for logs to accumulate
            console.log('⏳ Collecting logs for 30 seconds...');
            await new Promise(resolve => setTimeout(resolve, 30000));
            
            // Test backend directly
            const backendTest = await this.testBackendDirectly();
            
            // Wait a bit more for any additional logs
            console.log('⏳ Collecting additional logs for 15 seconds...');
            await new Promise(resolve => setTimeout(resolve, 15000));
            
            // Generate report
            const report = await this.generatePerformanceReport();
            
            console.log('\n🎯 Backend Performance Analysis completed!');
            
        } catch (error) {
            console.error('❌ Analysis failed:', error);
        }
    }
}

// Run the analyzer
if (require.main === module) {
    const analyzer = new BackendPerformanceAnalyzer();
    analyzer.run().catch(console.error);
}

module.exports = BackendPerformanceAnalyzer;
