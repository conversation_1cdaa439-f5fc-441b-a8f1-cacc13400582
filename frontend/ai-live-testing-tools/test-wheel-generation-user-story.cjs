const { chromium } = require('playwright');
const WebSocket = require('ws');

class WheelGenerationUserStoryTester {
    constructor() {
        this.browser = null;
        this.page = null;
        this.wsMessages = [];
        this.testResults = {
            frontend_loaded: false,
            message_sent: false,
            wheel_generated: false,
            wheel_has_activities: false,
            activities_have_distinct_colors: false,
            wheel_spinning_works: false,
            winner_detection_works: false,
            result_sent_to_mentor: false
        };
    }

    async initialize() {
        console.log('🚀 Initializing Wheel Generation User Story Tester...');
        
        this.browser = await chromium.launch({ 
            headless: false,
            slowMo: 1000 // Slow down for better observation
        });
        
        this.page = await this.browser.newPage();
        
        // Intercept WebSocket messages
        await this.page.route('ws://localhost:8000/ws/game/', route => {
            console.log('🔗 WebSocket connection intercepted');
            route.continue();
        });

        // Listen to console logs
        this.page.on('console', msg => {
            if (msg.type() === 'log' || msg.type() === 'error' || msg.type() === 'warning') {
                console.log(`🖥️  [${msg.type()}] ${msg.text()}`);
            }
        });

        console.log('✅ Tester initialized successfully');
    }

    async loadFrontend() {
        console.log('\n🌐 Loading frontend...');
        
        try {
            await this.page.goto('http://localhost:3000/', {
                waitUntil: 'networkidle',
                timeout: 30000
            });
            
            // Wait for the app to initialize
            await this.page.waitForTimeout(3000);
            
            const title = await this.page.title();
            console.log(`📄 Page title: ${title}`);
            
            this.testResults.frontend_loaded = true;
            console.log('✅ Frontend loaded successfully');
            return true;
        } catch (error) {
            console.error('❌ Failed to load frontend:', error.message);
            return false;
        }
    }

    async sendUserMessage() {
        console.log('\n💬 Sending user message: "hey, would you generate me a wheel please ? I\'m tired but not sleepy"');
        
        try {
            // Find chat input
            const chatInput = await this.page.locator('textarea, input[type="text"]').first();
            await chatInput.waitFor({ timeout: 10000 });
            
            // Send the message
            await chatInput.fill("hey, would you generate me a wheel please ? I'm tired but not sleepy");
            await chatInput.press('Enter');
            
            this.testResults.message_sent = true;
            console.log('✅ Message sent successfully');
            
            // Wait for response
            console.log('⏳ Waiting for wheel generation response...');
            await this.page.waitForTimeout(10000); // Give time for backend processing
            
            return true;
        } catch (error) {
            console.error('❌ Failed to send message:', error.message);
            return false;
        }
    }

    async checkWheelGeneration() {
        console.log('\n🎡 Checking wheel generation...');
        
        try {
            // Look for wheel container
            const wheelContainer = await this.page.locator('.wheel-container, .wheel, [class*="wheel"]').first();
            await wheelContainer.waitFor({ timeout: 30000 });
            
            this.testResults.wheel_generated = true;
            console.log('✅ Wheel container found');
            
            // Check for activities in the wheel
            const activities = await this.page.locator('.wheel-segment, .activity, [class*="segment"], [class*="activity"]').all();
            console.log(`🔍 Found ${activities.length} wheel segments/activities`);
            
            if (activities.length >= 4) {
                this.testResults.wheel_has_activities = true;
                console.log('✅ Wheel has sufficient activities (4+)');
                
                // Check for distinct colors
                const colors = new Set();
                for (const activity of activities) {
                    const style = await activity.getAttribute('style');
                    const computedStyle = await activity.evaluate(el => window.getComputedStyle(el));
                    
                    if (style && (style.includes('background') || style.includes('color'))) {
                        colors.add(style);
                    }
                    if (computedStyle.backgroundColor && computedStyle.backgroundColor !== 'rgba(0, 0, 0, 0)') {
                        colors.add(computedStyle.backgroundColor);
                    }
                }
                
                console.log(`🎨 Found ${colors.size} distinct colors/styles`);
                if (colors.size >= 2) {
                    this.testResults.activities_have_distinct_colors = true;
                    console.log('✅ Activities have distinct colors');
                } else {
                    console.log('⚠️  Activities may not have distinct colors');
                }
            } else {
                console.log('⚠️  Insufficient activities found');
            }
            
            return true;
        } catch (error) {
            console.error('❌ Wheel generation check failed:', error.message);
            return false;
        }
    }

    async testWheelSpinning() {
        console.log('\n🎲 Testing wheel spinning...');
        
        try {
            // Look for spin button or clickable wheel
            const spinButton = await this.page.locator('button[class*="spin"], .spin-button, [data-testid="spin"], .wheel').first();
            await spinButton.waitFor({ timeout: 10000 });
            
            console.log('🎯 Found spin element, attempting to spin...');
            
            // Click to spin
            await spinButton.click();
            
            // Wait for spinning animation
            await this.page.waitForTimeout(5000);
            
            // Check if wheel is still spinning or has stopped
            const isSpinning = await this.page.locator('.spinning, [class*="spinning"]').count() > 0;
            console.log(`🔄 Wheel spinning status: ${isSpinning ? 'spinning' : 'stopped'}`);
            
            // Wait for spin to complete
            if (isSpinning) {
                await this.page.waitForTimeout(10000); // Wait for spin to complete
            }
            
            this.testResults.wheel_spinning_works = true;
            console.log('✅ Wheel spinning mechanism works');
            
            return true;
        } catch (error) {
            console.error('❌ Wheel spinning test failed:', error.message);
            return false;
        }
    }

    async checkWinnerDetection() {
        console.log('\n🏆 Checking winner detection...');
        
        try {
            // Look for winner indication
            const winner = await this.page.locator('.winner, .selected, .result, [class*="winner"], [class*="selected"]').first();
            
            if (await winner.count() > 0) {
                const winnerText = await winner.textContent();
                console.log(`🎉 Winner detected: ${winnerText}`);
                this.testResults.winner_detection_works = true;
                console.log('✅ Winner detection works');
                return true;
            } else {
                console.log('⚠️  No winner indication found');
                return false;
            }
        } catch (error) {
            console.error('❌ Winner detection check failed:', error.message);
            return false;
        }
    }

    async checkMentorResponse() {
        console.log('\n🤖 Checking if result was sent to mentor...');
        
        try {
            // Look for mentor response in chat
            const chatMessages = await this.page.locator('.message, .chat-message, [class*="message"]').all();
            
            let mentorResponseFound = false;
            for (const message of chatMessages) {
                const text = await message.textContent();
                if (text && (text.includes('wheel') || text.includes('activity') || text.includes('result'))) {
                    console.log(`💬 Potential mentor response: ${text.substring(0, 100)}...`);
                    mentorResponseFound = true;
                }
            }
            
            if (mentorResponseFound) {
                this.testResults.result_sent_to_mentor = true;
                console.log('✅ Result appears to have been sent to mentor');
                return true;
            } else {
                console.log('⚠️  No clear mentor response found');
                return false;
            }
        } catch (error) {
            console.error('❌ Mentor response check failed:', error.message);
            return false;
        }
    }

    async runCompleteTest() {
        console.log('\n🎭 Running Complete Wheel Generation User Story Test');
        console.log('════════════════════════════════════════════════════════════');
        
        const steps = [
            { name: 'Load Frontend', method: this.loadFrontend },
            { name: 'Send User Message', method: this.sendUserMessage },
            { name: 'Check Wheel Generation', method: this.checkWheelGeneration },
            { name: 'Test Wheel Spinning', method: this.testWheelSpinning },
            { name: 'Check Winner Detection', method: this.checkWinnerDetection },
            { name: 'Check Mentor Response', method: this.checkMentorResponse }
        ];
        
        for (const step of steps) {
            console.log(`\n🧪 Running: ${step.name}`);
            const success = await step.method.call(this);
            console.log(`${success ? '✅' : '❌'} ${step.name}: ${success ? 'PASSED' : 'FAILED'}`);
        }
        
        this.generateReport();
    }

    generateReport() {
        console.log('\n📊 WHEEL GENERATION USER STORY TEST REPORT');
        console.log('════════════════════════════════════════════════════════════');
        
        const results = this.testResults;
        const totalTests = Object.keys(results).length;
        const passedTests = Object.values(results).filter(Boolean).length;
        
        console.log(`⏱️  Test Completion: ${passedTests}/${totalTests} steps passed`);
        console.log('\n🎯 Detailed Results:');
        
        Object.entries(results).forEach(([test, passed]) => {
            console.log(`  ${passed ? '✅' : '❌'} ${test.replace(/_/g, ' ')}: ${passed ? 'PASSED' : 'FAILED'}`);
        });
        
        console.log('\n💡 Issues Identified:');
        if (!results.activities_have_distinct_colors) {
            console.log('  🎨 Activities may not have distinct colors');
        }
        if (!results.wheel_spinning_works) {
            console.log('  🎲 Wheel spinning mechanism needs improvement');
        }
        if (!results.winner_detection_works) {
            console.log('  🏆 Winner detection after spin is not working properly');
        }
        if (!results.result_sent_to_mentor) {
            console.log('  🤖 Result may not be properly sent to mentor');
        }
        
        console.log('\n🔧 Next Steps:');
        console.log('  1. Fix wheel spinning mechanics');
        console.log('  2. Implement proper winner detection');
        console.log('  3. Ensure distinct colors for activities');
        console.log('  4. Verify mentor receives wheel results');
    }

    async cleanup() {
        if (this.browser) {
            await this.browser.close();
        }
    }
}

// Run the test
async function main() {
    const tester = new WheelGenerationUserStoryTester();
    
    try {
        await tester.initialize();
        await tester.runCompleteTest();
    } catch (error) {
        console.error('❌ Test execution failed:', error);
    } finally {
        await tester.cleanup();
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = WheelGenerationUserStoryTester;
