#!/usr/bin/env node

/**
 * Test Message Display Fix - Verify that backend messages show content instead of "No content"
 * 
 * This tool tests:
 * 1. WebSocket message handling fixes
 * 2. Chat message content extraction
 * 3. Wheel data processing
 */

const WebSocket = require('ws');

const config = {
  gameWebSocketUrl: 'ws://localhost:8000/ws/game/',
  testDuration: 30000, // 30 seconds
  messageDelay: 3000 // 3 seconds between messages
};

class MessageDisplayTest {
  constructor() {
    this.gameWs = null;
    this.messages = [];
    this.chatMessages = [];
    this.wheelMessages = [];
    this.errors = [];
    this.startTime = Date.now();
  }

  async start() {
    console.log('🔍 Message Display Fix Test Starting...');
    console.log('🎯 Testing WebSocket message handling and content extraction');
    console.log('⏱️  Test duration: 30 seconds\n');

    try {
      await this.connectToGame();
      await this.runMessageTest();
      this.generateReport();
    } catch (error) {
      console.error('❌ Test failed:', error.message);
      this.errors.push({
        type: 'TEST_FAILURE',
        message: error.message,
        timestamp: new Date().toISOString()
      });
    } finally {
      this.cleanup();
    }
  }

  async connectToGame() {
    return new Promise((resolve, reject) => {
      console.log('🔌 Connecting to game WebSocket...');
      
      this.gameWs = new WebSocket(config.gameWebSocketUrl);
      
      this.gameWs.on('open', () => {
        console.log('✅ Game WebSocket connected');
        resolve();
      });

      this.gameWs.on('message', (data) => {
        try {
          const message = JSON.parse(data.toString());
          this.handleMessage(message);
        } catch (error) {
          console.error('❌ Failed to parse message:', error.message);
          this.errors.push({
            type: 'MESSAGE_PARSE_ERROR',
            message: error.message,
            rawData: data.toString(),
            timestamp: new Date().toISOString()
          });
        }
      });

      this.gameWs.on('error', (error) => {
        console.error('❌ Game WebSocket error:', error.message);
        this.errors.push({
          type: 'WEBSOCKET_ERROR',
          message: error.message,
          timestamp: new Date().toISOString()
        });
        reject(error);
      });

      this.gameWs.on('close', (code, reason) => {
        console.log(`🔌 Game WebSocket closed: ${code} ${reason}`);
      });

      setTimeout(() => reject(new Error('Connection timeout')), 10000);
    });
  }

  handleMessage(message) {
    this.messages.push({
      message,
      timestamp: new Date().toISOString()
    });

    console.log(`📨 Received: ${message.type}`);

    // Test chat message content extraction
    if (message.type === 'chat_message') {
      this.analyzeChatMessage(message);
    }

    // Test wheel data processing
    if (message.type === 'wheel_data') {
      this.analyzeWheelData(message);
    }

    // Check for errors
    if (message.type === 'error') {
      console.log(`  ❌ Error: ${message.content || message.message}`);
      this.errors.push({
        type: 'BACKEND_ERROR',
        message: message.content || message.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  analyzeChatMessage(message) {
    this.chatMessages.push(message);

    // Test content extraction (simulating frontend logic)
    const extractedContent = message.content || 'No content';
    const hasValidContent = extractedContent !== 'No content' && extractedContent.trim() !== '';

    if (hasValidContent) {
      const preview = extractedContent.substring(0, 50) + (extractedContent.length > 50 ? '...' : '');
      const sender = message.is_user ? 'User' : 'AI';
      console.log(`  ✅ ${sender}: "${preview}"`);
    } else {
      console.log(`  ❌ ISSUE: Message has no content or shows "No content"`);
      this.errors.push({
        type: 'NO_CONTENT_ISSUE',
        message: 'Chat message missing content',
        data: message,
        timestamp: new Date().toISOString()
      });
    }
  }

  analyzeWheelData(message) {
    this.wheelMessages.push(message);

    // Test wheel data structure (simulating frontend logic)
    if (message.wheel && message.wheel.items && Array.isArray(message.wheel.items)) {
      console.log(`  🎡 Wheel with ${message.wheel.items.length} items received`);
      
      // Test item processing
      const processedItems = message.wheel.items.map(item => ({
        id: item.id,
        text: item.title || item.name || 'Activity',
        percentage: item.percentage,
        color: item.color,
        description: item.description || ''
      }));

      const hasProperTitles = processedItems.every(item => 
        item.text !== 'Activity' && 
        !item.text.startsWith('Activity ')
      );

      if (hasProperTitles) {
        console.log(`  ✅ All wheel items have proper titles`);
        processedItems.forEach((item, index) => {
          console.log(`    ${index + 1}. "${item.text}"`);
        });
      } else {
        console.log(`  ❌ ISSUE: Some wheel items have generic names`);
        this.errors.push({
          type: 'GENERIC_WHEEL_NAMES',
          message: 'Wheel items showing generic names',
          data: processedItems,
          timestamp: new Date().toISOString()
        });
      }
    } else {
      console.log(`  ❌ ISSUE: Invalid wheel data structure`);
      this.errors.push({
        type: 'INVALID_WHEEL_DATA',
        message: 'Wheel data missing or malformed',
        data: message,
        timestamp: new Date().toISOString()
      });
    }
  }

  async runMessageTest() {
    const testMessages = [
      "I'm feeling bored and need some inspiration",
      "Can you help me find something fun to do?"
    ];

    let messageIndex = 0;

    const sendMessage = () => {
      if (messageIndex < testMessages.length && this.gameWs && this.gameWs.readyState === WebSocket.OPEN) {
        const messageContent = testMessages[messageIndex];
        
        const message = {
          type: 'chat_message',
          content: {
            message: messageContent,
            user_profile_id: '2',
            timestamp: new Date().toISOString()
          }
        };

        console.log(`👤 Sending: "${messageContent}"`);
        this.gameWs.send(JSON.stringify(message));
        messageIndex++;
      }
    };

    // Send first message immediately
    sendMessage();

    // Send subsequent messages at intervals
    const messageInterval = setInterval(() => {
      sendMessage();
      if (messageIndex >= testMessages.length) {
        clearInterval(messageInterval);
      }
    }, config.messageDelay);

    // Wait for test completion
    return new Promise(resolve => {
      setTimeout(() => {
        clearInterval(messageInterval);
        resolve();
      }, config.testDuration);
    });
  }

  generateReport() {
    const duration = Date.now() - this.startTime;
    
    console.log('\n🔍 Message Display Fix Test Report');
    console.log('=' .repeat(50));
    console.log(`⏱️  Test duration: ${Math.round(duration / 1000)}s`);
    console.log(`📨 Total messages received: ${this.messages.length}`);
    console.log(`💬 Chat messages: ${this.chatMessages.length}`);
    console.log(`🎡 Wheel messages: ${this.wheelMessages.length}`);
    console.log(`❌ Errors: ${this.errors.length}`);

    // Analyze chat message content
    const validChatMessages = this.chatMessages.filter(m => 
      m.content && m.content.trim() !== '' && m.content !== 'No content'
    );
    
    console.log(`\n💬 Chat Message Analysis:`);
    console.log(`  ✅ Valid content messages: ${validChatMessages.length}/${this.chatMessages.length}`);
    
    if (validChatMessages.length === this.chatMessages.length && this.chatMessages.length > 0) {
      console.log(`  🎉 SUCCESS: All chat messages have proper content!`);
    } else if (this.chatMessages.length === 0) {
      console.log(`  ⚠️  No chat messages received to test`);
    } else {
      console.log(`  ❌ ISSUE: ${this.chatMessages.length - validChatMessages.length} messages missing content`);
    }

    // Analyze wheel data
    if (this.wheelMessages.length > 0) {
      console.log(`\n🎡 Wheel Data Analysis:`);
      console.log(`  📊 Wheel messages received: ${this.wheelMessages.length}`);
      
      const validWheelMessages = this.wheelMessages.filter(m => 
        m.wheel && m.wheel.items && Array.isArray(m.wheel.items)
      );
      
      if (validWheelMessages.length === this.wheelMessages.length) {
        console.log(`  🎉 SUCCESS: All wheel data properly structured!`);
      } else {
        console.log(`  ❌ ISSUE: ${this.wheelMessages.length - validWheelMessages.length} wheel messages malformed`);
      }
    }

    // Show errors if any
    if (this.errors.length > 0) {
      console.log(`\n❌ Issues Found:`);
      this.errors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error.type}: ${error.message}`);
      });
    }

    // Overall assessment
    const hasContentIssues = this.errors.some(e => e.type === 'NO_CONTENT_ISSUE');
    const hasWheelIssues = this.errors.some(e => e.type === 'INVALID_WHEEL_DATA' || e.type === 'GENERIC_WHEEL_NAMES');

    console.log(`\n📊 Overall Assessment:`);
    
    if (!hasContentIssues && this.chatMessages.length > 0) {
      console.log(`✅ FIXED: Chat messages display proper content (no more "No content")`);
    } else if (this.chatMessages.length === 0) {
      console.log(`⚠️  No chat messages received to validate fix`);
    } else {
      console.log(`❌ ISSUE: Chat messages still showing "No content"`);
    }

    if (!hasWheelIssues && this.wheelMessages.length > 0) {
      console.log(`✅ FIXED: Wheel data processed correctly`);
    } else if (this.wheelMessages.length === 0) {
      console.log(`⚠️  No wheel data received to validate fix`);
    } else {
      console.log(`❌ ISSUE: Wheel data processing problems remain`);
    }

    // Save detailed report
    const report = {
      testSummary: {
        duration,
        totalMessages: this.messages.length,
        chatMessages: this.chatMessages.length,
        wheelMessages: this.wheelMessages.length,
        errors: this.errors.length,
        hasContentIssues,
        hasWheelIssues
      },
      chatMessages: this.chatMessages,
      wheelMessages: this.wheelMessages,
      errors: this.errors,
      allMessages: this.messages
    };

    const fs = require('fs');
    const filename = `logs/message-display-test-${Date.now()}.json`;
    fs.writeFileSync(filename, JSON.stringify(report, null, 2));
    console.log(`\n📄 Detailed report saved: ${filename}`);
  }

  cleanup() {
    if (this.gameWs) {
      this.gameWs.close();
    }
  }
}

// Run the test
if (require.main === module) {
  const test = new MessageDisplayTest();
  test.start().catch(console.error);
}

module.exports = MessageDisplayTest;
