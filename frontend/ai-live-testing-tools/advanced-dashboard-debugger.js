#!/usr/bin/env node

/**
 * Advanced Dashboard Debugger
 * 
 * Comprehensive debugging tool for the connection dashboard that:
 * - Tests each component individually
 * - Provides detailed error diagnostics
 * - Shows real packet flow
 * - Offers interactive debugging
 */

import WebSocket from 'ws';
import { CONFIG } from './config.js';
import fs from 'fs';
import path from 'path';

class AdvancedDashboardDebugger {
  constructor() {
    this.logFile = path.join(CONFIG.logging.logDirectory, `dashboard-debug-${Date.now()}.log`);
    this.packetLog = [];
    this.clientSockets = [];
    this.adminSocket = null;
    this.isRunning = false;
    
    // Ensure log directory exists
    if (!fs.existsSync(CONFIG.logging.logDirectory)) {
      fs.mkdirSync(CONFIG.logging.logDirectory, { recursive: true });
    }
  }

  log(level, message, data = null) {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level,
      message,
      data
    };
    
    const colorMap = {
      'error': '\x1b[31m',   // Red
      'warn': '\x1b[33m',    // Yellow
      'info': '\x1b[36m',    // Cyan
      'debug': '\x1b[90m',   // Gray
      'success': '\x1b[32m'  // Green
    };
    
    const color = colorMap[level] || '';
    const reset = '\x1b[0m';
    
    console.log(`${color}[${timestamp}] ${level.toUpperCase()}: ${message}${reset}`);
    if (data) {
      console.log(`${color}  Data:${reset}`, JSON.stringify(data, null, 2));
    }
    
    // Save to file
    if (CONFIG.logging.saveToFile) {
      fs.appendFileSync(this.logFile, JSON.stringify(logEntry) + '\n');
    }
  }

  async startDebugging() {
    this.log('info', '🔍 Starting Advanced Dashboard Debugging');
    this.isRunning = true;

    try {
      // Phase 1: Test backend connectivity
      await this.testBackendConnectivity();
      
      // Phase 2: Test Redis connectivity
      await this.testRedisConnectivity();
      
      // Phase 3: Test admin WebSocket connection
      await this.testAdminWebSocketConnection();
      
      // Phase 4: Test client connections and tracking
      await this.testClientConnectionTracking();
      
      // Phase 5: Test real-time data flow
      await this.testRealTimeDataFlow();
      
      // Phase 6: Interactive debugging session
      await this.startInteractiveSession();
      
    } catch (error) {
      this.log('error', 'Debugging session failed', { error: error.message });
    } finally {
      await this.cleanup();
    }
  }

  async testBackendConnectivity() {
    this.log('info', '🏥 Testing Backend Connectivity');
    
    try {
      // Test basic HTTP connectivity
      const response = await fetch('http://localhost:8000/admin/', {
        method: 'HEAD'
      });
      
      this.log('success', `Backend HTTP: ${response.status} ${response.statusText}`);
      
      // Test admin dashboard URL
      const dashboardResponse = await fetch('http://localhost:8000/admin/connection-dashboard/', {
        method: 'HEAD',
        redirect: 'manual'
      });
      
      this.log('success', `Dashboard URL: ${dashboardResponse.status} ${dashboardResponse.statusText}`);
      
    } catch (error) {
      this.log('error', 'Backend connectivity failed', { error: error.message });
      throw error;
    }
  }

  async testRedisConnectivity() {
    this.log('info', '📊 Testing Redis Connectivity');
    
    try {
      // Try to connect to Redis directly
      const redis = await import('redis').catch(() => null);
      
      if (!redis) {
        this.log('warn', 'Redis module not available for direct testing');
        return;
      }
      
      const client = redis.createClient({
        host: 'localhost',
        port: 6379
      });
      
      await client.connect();
      await client.ping();
      this.log('success', 'Redis connection successful');
      
      // Test if there are any existing session keys
      const keys = await client.keys('ws_session:*');
      this.log('info', `Found ${keys.length} existing session keys in Redis`);
      
      await client.disconnect();
      
    } catch (error) {
      this.log('error', 'Redis connectivity failed', { error: error.message });
      this.log('warn', 'This might be why the dashboard shows no data');
    }
  }

  async testAdminWebSocketConnection() {
    this.log('info', '🔌 Testing Admin WebSocket Connection');
    
    return new Promise((resolve, reject) => {
      const adminWsUrl = 'ws://localhost:8000/ws/connection-monitor/';
      this.adminSocket = new WebSocket(adminWsUrl);
      
      const timeout = setTimeout(() => {
        this.log('error', 'Admin WebSocket connection timeout');
        reject(new Error('Admin WebSocket timeout'));
      }, 10000);

      this.adminSocket.on('open', () => {
        clearTimeout(timeout);
        this.log('success', 'Admin WebSocket connected successfully');
        resolve();
      });

      this.adminSocket.on('error', (error) => {
        clearTimeout(timeout);
        this.log('error', 'Admin WebSocket connection failed', { 
          error: error.message,
          code: error.code 
        });
        
        if (error.message.includes('403')) {
          this.log('warn', 'Authentication required - you need to be logged in as staff user');
        }
        
        reject(error);
      });

      this.adminSocket.on('message', (data) => {
        try {
          const message = JSON.parse(data);
          this.logPacket('ADMIN_RECEIVED', message);
        } catch (error) {
          this.log('error', 'Failed to parse admin message', { data: data.toString() });
        }
      });

      this.adminSocket.on('close', (code, reason) => {
        this.log('warn', 'Admin WebSocket closed', { code, reason: reason.toString() });
      });
    });
  }

  async testClientConnectionTracking() {
    this.log('info', '👥 Testing Client Connection Tracking');
    
    const numClients = 3;
    const promises = [];
    
    for (let i = 1; i <= numClients; i++) {
      promises.push(this.createTrackedClient(i));
    }
    
    try {
      await Promise.all(promises);
      this.log('success', `Created ${numClients} tracked client connections`);
      
      // Wait a bit for tracking to register
      await this.sleep(2000);
      
      // Request connection data from admin socket
      if (this.adminSocket && this.adminSocket.readyState === WebSocket.OPEN) {
        this.sendAdminMessage({ type: 'get_connections' });
        this.log('info', 'Requested connection data from admin socket');
      }
      
    } catch (error) {
      this.log('error', 'Client connection tracking failed', { error: error.message });
    }
  }

  async createTrackedClient(clientId) {
    return new Promise((resolve, reject) => {
      const clientWsUrl = CONFIG.backend.websocketUrl;
      const socket = new WebSocket(clientWsUrl);
      
      const timeout = setTimeout(() => {
        reject(new Error(`Client ${clientId} connection timeout`));
      }, 5000);

      socket.on('open', () => {
        clearTimeout(timeout);
        this.log('debug', `Client ${clientId} connected`);
        
        // Send identification message
        const message = {
          type: 'chat_message',
          content: {
            message: `Debug test from client ${clientId}`,
            user_profile_id: `debug-user-${clientId}`,
            timestamp: new Date().toISOString()
          }
        };
        
        socket.send(JSON.stringify(message));
        this.logPacket(`CLIENT_${clientId}_SENT`, message);
        
        this.clientSockets.push({ id: clientId, socket });
        resolve();
      });

      socket.on('error', (error) => {
        clearTimeout(timeout);
        this.log('error', `Client ${clientId} connection failed`, { error: error.message });
        reject(error);
      });

      socket.on('message', (data) => {
        try {
          const message = JSON.parse(data);
          this.logPacket(`CLIENT_${clientId}_RECEIVED`, message);
        } catch (error) {
          this.log('error', `Failed to parse message for client ${clientId}`, { error: error.message });
        }
      });
    });
  }

  async testRealTimeDataFlow() {
    this.log('info', '⚡ Testing Real-time Data Flow');
    
    if (!this.adminSocket || this.adminSocket.readyState !== WebSocket.OPEN) {
      this.log('error', 'Admin socket not available for real-time testing');
      return;
    }
    
    // Test different admin requests
    const requests = [
      { type: 'get_connections' },
      { type: 'get_system_health' },
      { type: 'get_message_stats' }
    ];
    
    for (const request of requests) {
      this.log('info', `Sending admin request: ${request.type}`);
      this.sendAdminMessage(request);
      await this.sleep(1000);
    }
    
    // Send some client messages to generate activity
    for (const client of this.clientSockets) {
      if (client.socket.readyState === WebSocket.OPEN) {
        const message = {
          type: 'chat_message',
          content: {
            message: `Activity test from client ${client.id}`,
            user_profile_id: `debug-user-${client.id}`,
            timestamp: new Date().toISOString()
          }
        };
        
        client.socket.send(JSON.stringify(message));
        this.logPacket(`CLIENT_${client.id}_ACTIVITY`, message);
      }
    }
    
    await this.sleep(3000);
  }

  async startInteractiveSession() {
    this.log('info', '🎮 Starting Interactive Debugging Session');
    this.log('info', 'Available commands:');
    this.log('info', '  - connections: Request connection data');
    this.log('info', '  - health: Request system health');
    this.log('info', '  - stats: Request message stats');
    this.log('info', '  - send <json>: Send custom JSON to admin socket');
    this.log('info', '  - client <id> <json>: Send JSON to specific client');
    this.log('info', '  - packets: Show recent packet log');
    this.log('info', '  - quit: Exit interactive session');
    
    // Note: In a real implementation, you'd use readline for interactive input
    // For now, we'll just show the packet log and exit
    this.showPacketLog();
  }

  sendAdminMessage(message) {
    if (this.adminSocket && this.adminSocket.readyState === WebSocket.OPEN) {
      this.adminSocket.send(JSON.stringify(message));
      this.logPacket('ADMIN_SENT', message);
    } else {
      this.log('error', 'Admin socket not available');
    }
  }

  logPacket(direction, packet) {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      direction,
      packet
    };
    
    this.packetLog.push(logEntry);
    
    // Keep only last 100 packets
    if (this.packetLog.length > 100) {
      this.packetLog.shift();
    }
    
    // Color-coded packet logging
    const colors = {
      'ADMIN_SENT': '\x1b[34m',      // Blue
      'ADMIN_RECEIVED': '\x1b[35m',  // Magenta
      'CLIENT_': '\x1b[32m'          // Green (for any client)
    };
    
    let color = '\x1b[37m'; // White default
    for (const [prefix, c] of Object.entries(colors)) {
      if (direction.startsWith(prefix)) {
        color = c;
        break;
      }
    }
    
    console.log(`${color}📦 ${direction}: ${JSON.stringify(packet, null, 2)}\x1b[0m`);
  }

  showPacketLog() {
    this.log('info', '📦 Recent Packet Log:');
    console.log('='.repeat(80));
    
    for (const entry of this.packetLog.slice(-20)) { // Show last 20 packets
      console.log(`[${entry.timestamp}] ${entry.direction}:`);
      console.log(JSON.stringify(entry.packet, null, 2));
      console.log('-'.repeat(40));
    }
    
    // Save full packet log to file
    const packetLogFile = path.join(CONFIG.logging.logDirectory, `packet-log-${Date.now()}.json`);
    fs.writeFileSync(packetLogFile, JSON.stringify(this.packetLog, null, 2));
    this.log('info', `Full packet log saved to: ${packetLogFile}`);
  }

  async sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async cleanup() {
    this.log('info', '🧹 Cleaning up debugging session');
    
    // Close all client sockets
    for (const client of this.clientSockets) {
      if (client.socket && client.socket.readyState === WebSocket.OPEN) {
        client.socket.close();
      }
    }
    
    // Close admin socket
    if (this.adminSocket && this.adminSocket.readyState === WebSocket.OPEN) {
      this.adminSocket.close();
    }
    
    this.isRunning = false;
    this.log('success', 'Debugging session completed');
  }
}

// CLI interface
if (import.meta.url === `file://${process.argv[1]}`) {
  console.log('🔍 Advanced Dashboard Debugger');
  console.log('This tool will comprehensively test the connection dashboard and show detailed packet flow.');
  console.log('Make sure the backend is running and you have staff access.\n');
  
  const dashboardDebugger = new AdvancedDashboardDebugger();

  // Handle graceful shutdown
  process.on('SIGINT', () => {
    console.log('\n🛑 Received SIGINT, stopping debugger...');
    dashboardDebugger.cleanup().then(() => process.exit(0));
  });

  dashboardDebugger.startDebugging().catch((error) => {
    console.error('❌ Debugging failed:', error.message);
    process.exit(1);
  });
}

export default AdvancedDashboardDebugger;
