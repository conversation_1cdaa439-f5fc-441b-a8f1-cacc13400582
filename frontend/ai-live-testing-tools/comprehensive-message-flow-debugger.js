#!/usr/bin/env node

/**
 * Comprehensive Message Flow Debugger
 * 
 * This tool reproduces exactly what the frontend client does and monitors:
 * 1. WebSocket message flow (both directions)
 * 2. Web container logs in real-time
 * 3. Celery container logs in real-time
 * 4. Dashboard message inspector behavior
 * 
 * Purpose: Investigate why message inspector shows 0 chat messages
 * and session monitoring stays silent despite messages being sent.
 */

import WebSocket from 'ws';
import { spawn } from 'child_process';
import fs from 'fs';
import path from 'path';

// Configuration matching frontend client behavior
const CONFIG = {
  websockets: {
    game: 'ws://localhost:8000/ws/game/',
    admin: 'ws://localhost:8000/ws/connection-monitor/'
  },
  users: {
    real: { id: '1', name: 'Test User', is_real: true },
    fake: { id: '2', name: '<PERSON><PERSON><PERSON>', is_real: false }
  },
  testing: {
    maxDuration: 180000, // 3 minutes
    messageInterval: 5000, // Send message every 5 seconds
    logCheckInterval: 2000 // Check logs every 2 seconds
  }
};

class ComprehensiveMessageFlowDebugger {
  constructor() {
    this.gameSocket = null;
    this.adminSocket = null;
    this.webLogProcess = null;
    this.celeryLogProcess = null;
    this.messageLog = [];
    this.webLogs = [];
    this.celeryLogs = [];
    this.sessionId = null;
    this.userId = CONFIG.users.fake.id; // Start with PhiPhi
    this.messageCount = 0;
    this.startTime = Date.now();
    this.testPhase = 'initialization';
    this.dashboardState = {
      totalMessages: 0,
      chatMessages: 0,
      sessionHistory: [],
      messageFlow: []
    };
  }

  async runComprehensiveDebug() {
    console.log('🔍 COMPREHENSIVE MESSAGE FLOW DEBUGGER');
    console.log('=====================================');
    console.log('Reproducing exact frontend client behavior...\n');

    try {
      // Phase 1: Start monitoring
      await this.startContainerMonitoring();
      
      // Phase 2: Connect like frontend client
      await this.connectLikeFrontendClient();
      
      // Phase 3: Simulate user selection in debug panel
      await this.simulateUserSelection();
      
      // Phase 4: Send messages like frontend
      await this.sendMessagesLikeFrontend();
      
      // Phase 5: Monitor dashboard behavior
      await this.monitorDashboardBehavior();
      
      // Phase 6: Generate test messages
      await this.generateTestMessages();
      
      // Phase 7: Analyze findings
      this.analyzeFindingsAndGenerateReport();
      
    } catch (error) {
      console.error('❌ Debug session failed:', error.message);
    } finally {
      this.cleanup();
    }
  }

  async startContainerMonitoring() {
    console.log('📋 Phase 1: Starting Container Monitoring');
    console.log('------------------------------------------');

    // Monitor web container logs
    console.log('🔍 Starting web container log monitoring...');
    this.webLogProcess = spawn('docker', ['logs', '-f', '--tail', '10', 'backend-web-1'], {
      stdio: ['ignore', 'pipe', 'pipe']
    });

    this.webLogProcess.stdout.on('data', (data) => {
      const logLine = data.toString();
      this.analyzeWebLog(logLine);
    });

    this.webLogProcess.stderr.on('data', (data) => {
      const logLine = data.toString();
      this.analyzeWebLog(logLine);
    });

    // Monitor celery container logs
    console.log('🔍 Starting celery container log monitoring...');
    this.celeryLogProcess = spawn('docker', ['logs', '-f', '--tail', '10', 'backend-celery-1'], {
      stdio: ['ignore', 'pipe', 'pipe']
    });

    this.celeryLogProcess.stdout.on('data', (data) => {
      const logLine = data.toString();
      this.analyzeCeleryLog(logLine);
    });

    this.celeryLogProcess.stderr.on('data', (data) => {
      const logLine = data.toString();
      this.analyzeCeleryLog(logLine);
    });

    console.log('✅ Container monitoring started\n');
    await new Promise(resolve => setTimeout(resolve, 2000));
  }

  analyzeWebLog(logLine) {
    this.webLogs.push({
      timestamp: new Date().toISOString(),
      content: logLine.trim(),
      phase: this.testPhase
    });

    // Look for WebSocket related logs
    if (logLine.includes('WebSocket') || logLine.includes('ws/') || logLine.includes('connection')) {
      console.log(`🌐 WEB: ${logLine.trim()}`);
    }

    // Look for message processing
    if (logLine.includes('message') || logLine.includes('chat_message') || logLine.includes('user_profile_id')) {
      console.log(`📨 WEB MSG: ${logLine.trim()}`);
    }
  }

  analyzeCeleryLog(logLine) {
    this.celeryLogs.push({
      timestamp: new Date().toISOString(),
      content: logLine.trim(),
      phase: this.testPhase
    });

    // Look for workflow and message processing
    if (logLine.includes('workflow') || logLine.includes('user_profile_id') || logLine.includes('chat_message')) {
      console.log(`⚙️ CELERY: ${logLine.trim()}`);
    }

    // Look for errors
    if (logLine.includes('ERROR') || logLine.includes('Exception') || logLine.includes('Traceback')) {
      console.log(`🚨 CELERY ERROR: ${logLine.trim()}`);
    }
  }

  async connectLikeFrontendClient() {
    console.log('📋 Phase 2: Connecting Like Frontend Client');
    console.log('--------------------------------------------');
    this.testPhase = 'connection';

    // Connect to admin dashboard first (like opening dashboard)
    console.log('🌐 Connecting to admin dashboard WebSocket...');
    this.adminSocket = new WebSocket(CONFIG.websockets.admin);

    await new Promise((resolve, reject) => {
      this.adminSocket.on('open', () => {
        console.log('✅ Admin dashboard connected');
        this.logMessage('ADMIN_CONNECTED', { type: 'connection', status: 'connected' });
        resolve();
      });

      this.adminSocket.on('message', (data) => {
        this.handleAdminMessage(data);
      });

      this.adminSocket.on('error', (error) => {
        console.log('❌ Admin connection error:', error.message);
        reject(error);
      });

      setTimeout(() => reject(new Error('Admin connection timeout')), 10000);
    });

    // Connect to game WebSocket (like frontend client)
    console.log('🌐 Connecting to game WebSocket...');
    this.gameSocket = new WebSocket(CONFIG.websockets.game);

    await new Promise((resolve, reject) => {
      this.gameSocket.on('open', () => {
        console.log('✅ Game WebSocket connected');
        this.logMessage('GAME_CONNECTED', { type: 'connection', status: 'connected' });
        resolve();
      });

      this.gameSocket.on('message', (data) => {
        this.handleGameMessage(data);
      });

      this.gameSocket.on('error', (error) => {
        console.log('❌ Game connection error:', error.message);
        reject(error);
      });

      setTimeout(() => reject(new Error('Game connection timeout')), 10000);
    });

    console.log('✅ Both WebSocket connections established\n');
    await new Promise(resolve => setTimeout(resolve, 2000));
  }

  handleAdminMessage(data) {
    try {
      const message = JSON.parse(data);
      this.logMessage('ADMIN_RECEIVED', message);
      
      // Track dashboard state changes
      if (message.type === 'connection_data') {
        console.log(`📊 Dashboard: ${message.data.length} connections`);
        if (message.data.length > 0) {
          const connection = message.data[0];
          console.log(`   Session: ${connection.session_id?.substring(0, 8)}... User: ${connection.user_id}`);
        }
      }

      if (message.type === 'message_stats') {
        this.dashboardState.totalMessages = message.data.total_messages || 0;
        this.dashboardState.chatMessages = message.data.chat_messages || 0;
        console.log(`📊 Dashboard: ${this.dashboardState.totalMessages} total, ${this.dashboardState.chatMessages} chat messages`);
      }

      if (message.type === 'message_flow') {
        this.dashboardState.messageFlow.push(message.data);
        console.log(`📊 Dashboard: New message flow entry (${this.dashboardState.messageFlow.length} total)`);
      }

    } catch (error) {
      console.log('❌ Failed to parse admin message:', error.message);
      this.logMessage('ADMIN_PARSE_ERROR', { error: error.message, raw: data.toString() });
    }
  }

  handleGameMessage(data) {
    try {
      const message = JSON.parse(data);
      this.logMessage('GAME_RECEIVED', message);
      
      console.log(`📨 Game received: ${message.type}`);
      
      // Extract session ID if available
      if (message.session_id && !this.sessionId) {
        this.sessionId = message.session_id;
        console.log(`🔗 Session ID captured: ${this.sessionId.substring(0, 8)}...`);
      }

    } catch (error) {
      console.log('❌ Failed to parse game message:', error.message);
      this.logMessage('GAME_PARSE_ERROR', { error: error.message, raw: data.toString() });
    }
  }

  async simulateUserSelection() {
    console.log('📋 Phase 3: Simulating User Selection in Debug Panel');
    console.log('----------------------------------------------------');
    this.testPhase = 'user_selection';

    // This simulates what happens when user selects a user in debug panel
    console.log(`🎯 Simulating user selection: User ${this.userId} (${CONFIG.users.fake.name})`);
    
    // Request dashboard refresh (like debug panel would do)
    if (this.adminSocket && this.adminSocket.readyState === WebSocket.OPEN) {
      this.adminSocket.send(JSON.stringify({ type: 'get_connections' }));
      this.adminSocket.send(JSON.stringify({ type: 'get_message_stats' }));
    }

    await new Promise(resolve => setTimeout(resolve, 3000));
    console.log('✅ User selection simulation completed\n');
  }

  async sendMessagesLikeFrontend() {
    console.log('📋 Phase 4: Sending Messages Like Frontend');
    console.log('-------------------------------------------');
    this.testPhase = 'message_sending';

    // Send first message (like user typing in frontend)
    const message1 = {
      type: 'chat_message',
      content: {
        message: 'Hello, I need help with something',
        user_profile_id: this.userId,
        timestamp: new Date().toISOString(),
        metadata: {
          source: 'frontend_client_simulation',
          requested_workflow: 'discussion'
        }
      }
    };

    console.log('📤 Sending first message (like frontend client)...');
    this.gameSocket.send(JSON.stringify(message1));
    this.logMessage('GAME_SENT', message1);
    this.messageCount++;

    await new Promise(resolve => setTimeout(resolve, 5000));

    // Send second message
    const message2 = {
      type: 'chat_message',
      content: {
        message: 'I am feeling bored and need some activity suggestions',
        user_profile_id: this.userId,
        timestamp: new Date().toISOString(),
        metadata: {
          source: 'frontend_client_simulation',
          requested_workflow: 'wheel_generation'
        }
      }
    };

    console.log('📤 Sending second message...');
    this.gameSocket.send(JSON.stringify(message2));
    this.logMessage('GAME_SENT', message2);
    this.messageCount++;

    console.log(`✅ Sent ${this.messageCount} messages like frontend client\n`);
    await new Promise(resolve => setTimeout(resolve, 5000));
  }

  async monitorDashboardBehavior() {
    console.log('📋 Phase 5: Monitoring Dashboard Behavior');
    console.log('-----------------------------------------');
    this.testPhase = 'dashboard_monitoring';

    // Activate message inspector (like clicking the button)
    console.log('🔍 Activating message inspector...');
    if (this.adminSocket && this.adminSocket.readyState === WebSocket.OPEN) {
      this.adminSocket.send(JSON.stringify({ type: 'start_message_monitoring' }));
      this.adminSocket.send(JSON.stringify({ type: 'get_message_stats' }));
    }

    await new Promise(resolve => setTimeout(resolve, 3000));

    // Check session monitoring
    console.log('👁️ Checking session monitoring...');
    if (this.sessionId && this.adminSocket && this.adminSocket.readyState === WebSocket.OPEN) {
      this.adminSocket.send(JSON.stringify({ 
        type: 'focus_session', 
        session_id: this.sessionId 
      }));
    }

    await new Promise(resolve => setTimeout(resolve, 3000));
    console.log('✅ Dashboard monitoring phase completed\n');
  }

  async generateTestMessages() {
    console.log('📋 Phase 6: Generating Test Messages');
    console.log('------------------------------------');
    this.testPhase = 'test_message_generation';

    // Simulate clicking "Generate Test Messages" button
    console.log('🧪 Generating test messages (like clicking button)...');
    if (this.adminSocket && this.adminSocket.readyState === WebSocket.OPEN) {
      this.adminSocket.send(JSON.stringify({ type: 'generate_test_messages' }));
    }

    await new Promise(resolve => setTimeout(resolve, 5000));
    console.log('✅ Test message generation completed\n');
  }

  logMessage(direction, message) {
    const logEntry = {
      timestamp: Date.now(),
      phase: this.testPhase,
      direction,
      message,
      messageCount: this.messageCount
    };
    this.messageLog.push(logEntry);
  }

  analyzeFindingsAndGenerateReport() {
    console.log('📋 Phase 7: Analysis and Report Generation');
    console.log('===========================================');

    const duration = Date.now() - this.startTime;
    
    console.log(`⏱️ Total Duration: ${Math.round(duration / 1000)}s`);
    console.log(`📨 Messages Sent: ${this.messageCount}`);
    console.log(`📋 WebSocket Messages Logged: ${this.messageLog.length}`);
    console.log(`🌐 Web Log Entries: ${this.webLogs.length}`);
    console.log(`⚙️ Celery Log Entries: ${this.celeryLogs.length}`);

    console.log('\n🔍 CRITICAL FINDINGS:');
    console.log('=====================');

    // Analyze message flow
    const sentMessages = this.messageLog.filter(log => log.direction === 'GAME_SENT');
    const receivedMessages = this.messageLog.filter(log => log.direction === 'GAME_RECEIVED');
    const adminMessages = this.messageLog.filter(log => log.direction === 'ADMIN_RECEIVED');

    console.log(`📤 Messages Sent to Game: ${sentMessages.length}`);
    console.log(`📨 Messages Received from Game: ${receivedMessages.length}`);
    console.log(`📊 Admin Dashboard Messages: ${adminMessages.length}`);

    // Check for message format issues
    const parseErrors = this.messageLog.filter(log => 
      log.direction.includes('PARSE_ERROR')
    );
    
    if (parseErrors.length > 0) {
      console.log(`🚨 Message Parse Errors: ${parseErrors.length}`);
      parseErrors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error.message.error}`);
      });
    }

    // Check dashboard state
    console.log('\n📊 Dashboard State:');
    console.log(`   Total Messages: ${this.dashboardState.totalMessages}`);
    console.log(`   Chat Messages: ${this.dashboardState.chatMessages}`);
    console.log(`   Message Flow Entries: ${this.dashboardState.messageFlow.length}`);

    // Identify the core issue
    console.log('\n🎯 ISSUE IDENTIFICATION:');
    if (this.dashboardState.totalMessages > 0 && this.dashboardState.chatMessages === 0) {
      console.log('❌ ISSUE FOUND: Dashboard shows total messages but 0 chat messages');
      console.log('   This suggests message type classification or filtering issues');
    }

    if (this.dashboardState.messageFlow.length === 0 && sentMessages.length > 0) {
      console.log('❌ ISSUE FOUND: No message flow entries despite sent messages');
      console.log('   This suggests message inspector is not capturing real chat messages');
    }

    // Save comprehensive report
    this.saveComprehensiveReport(duration);
  }

  saveComprehensiveReport(duration) {
    const reportPath = path.join('logs', `comprehensive-message-flow-debug-${Date.now()}.json`);
    const report = {
      timestamp: new Date().toISOString(),
      duration,
      testConfiguration: CONFIG,
      messagesSent: this.messageCount,
      sessionId: this.sessionId,
      userId: this.userId,
      dashboardState: this.dashboardState,
      messageLog: this.messageLog,
      webLogs: this.webLogs.slice(-50), // Last 50 web logs
      celeryLogs: this.celeryLogs.slice(-50), // Last 50 celery logs
      analysis: {
        totalWebSocketMessages: this.messageLog.length,
        sentMessages: this.messageLog.filter(log => log.direction === 'GAME_SENT').length,
        receivedMessages: this.messageLog.filter(log => log.direction === 'GAME_RECEIVED').length,
        adminMessages: this.messageLog.filter(log => log.direction === 'ADMIN_RECEIVED').length,
        parseErrors: this.messageLog.filter(log => log.direction.includes('PARSE_ERROR')).length
      }
    };

    try {
      if (!fs.existsSync('logs')) {
        fs.mkdirSync('logs');
      }
      fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
      console.log(`\n💾 Comprehensive report saved: ${reportPath}`);
    } catch (error) {
      console.log(`❌ Failed to save report: ${error.message}`);
    }
  }

  cleanup() {
    console.log('\n🧹 Cleaning up...');
    
    if (this.gameSocket) {
      this.gameSocket.close();
    }
    
    if (this.adminSocket) {
      this.adminSocket.close();
    }

    if (this.webLogProcess) {
      this.webLogProcess.kill();
    }

    if (this.celeryLogProcess) {
      this.celeryLogProcess.kill();
    }

    console.log('✅ Cleanup completed');
  }
}

// Run the comprehensive debug
const messageFlowDebugger = new ComprehensiveMessageFlowDebugger();
messageFlowDebugger.runComprehensiveDebug().then(() => {
  process.exit(0);
}).catch((error) => {
  console.error('❌ Debug failed:', error);
  process.exit(1);
});

// Auto-exit after max duration
setTimeout(() => {
  console.log('\n⏰ Maximum debug duration reached - exiting');
  process.exit(1);
}, CONFIG.testing.maxDuration);
