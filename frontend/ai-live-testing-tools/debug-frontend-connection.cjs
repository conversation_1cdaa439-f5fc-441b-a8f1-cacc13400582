#!/usr/bin/env node

/**
 * Debug Frontend Connection - Check why frontend thinks backend is unavailable
 */

const { chromium } = require('playwright');

async function debugFrontendConnection() {
    console.log('🚀 Starting frontend connection debug...');
    
    const browser = await chromium.launch({ headless: false });
    const page = await browser.newPage();
    
    // Capture all console messages
    page.on('console', msg => {
        const type = msg.type();
        const text = msg.text();
        console.log(`🖥️  [${type.toUpperCase()}] ${text}`);
    });
    
    // Capture network requests
    page.on('request', request => {
        console.log(`📤 REQUEST: ${request.method()} ${request.url()}`);
    });
    
    page.on('response', response => {
        const status = response.status();
        const url = response.url();
        if (status >= 400) {
            console.log(`📥 RESPONSE ERROR: ${status} ${url}`);
        } else {
            console.log(`📥 RESPONSE OK: ${status} ${url}`);
        }
    });
    
    // Capture page errors
    page.on('pageerror', error => {
        console.log(`🖥️  [PAGE ERROR] ${error.message}`);
    });
    
    // Load page with detailed monitoring
    console.log('🌐 Loading page with connection monitoring...');
    await page.goto('http://localhost:3000/');
    
    // Wait for initial load
    await page.waitForTimeout(10000);
    
    // Check what the frontend thinks about backend availability
    const connectionStatus = await page.evaluate(() => {
        return {
            // Check for any global state about backend connection
            window: Object.keys(window).filter(key => 
                key.includes('backend') || 
                key.includes('connection') || 
                key.includes('websocket') || 
                key.includes('ws') ||
                key.includes('demo') ||
                key.includes('fallback')
            ),
            
            // Check localStorage
            localStorage: Object.keys(localStorage).map(key => ({
                key: key,
                value: localStorage.getItem(key)
            })),
            
            // Check sessionStorage
            sessionStorage: Object.keys(sessionStorage).map(key => ({
                key: key,
                value: sessionStorage.getItem(key)
            })),
            
            // Check for any error states in DOM
            errorElements: Array.from(document.querySelectorAll('.error, .fallback, [class*="error"], [class*="demo"]')).map(el => ({
                className: el.className,
                textContent: el.textContent,
                innerHTML: el.innerHTML
            }))
        };
    });
    
    console.log('\n📊 CONNECTION STATUS ANALYSIS:');
    console.log('════════════════════════════════════════════════════════════');
    
    console.log(`\n🌐 Window Properties:`);
    connectionStatus.window.forEach(key => {
        console.log(`  ${key}`);
    });
    
    console.log(`\n💾 LocalStorage:`);
    connectionStatus.localStorage.forEach(item => {
        console.log(`  ${item.key}: ${item.value}`);
    });
    
    console.log(`\n💾 SessionStorage:`);
    connectionStatus.sessionStorage.forEach(item => {
        console.log(`  ${item.key}: ${item.value}`);
    });
    
    console.log(`\n❌ Error Elements:`);
    connectionStatus.errorElements.forEach((el, i) => {
        console.log(`  ${i+1}. class="${el.className}"`);
        console.log(`     Text: "${el.textContent}"`);
    });
    
    // Test direct backend connectivity
    console.log('\n🔍 Testing direct backend connectivity...');
    
    try {
        const healthResponse = await page.evaluate(async () => {
            try {
                const response = await fetch('http://localhost:8000/api/health/', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                return {
                    status: response.status,
                    ok: response.ok,
                    text: await response.text()
                };
            } catch (error) {
                return {
                    error: error.message
                };
            }
        });
        
        console.log(`Health check result:`, healthResponse);
        
    } catch (error) {
        console.log(`Health check failed: ${error.message}`);
    }
    
    // Test WebSocket connectivity
    console.log('\n🔌 Testing WebSocket connectivity...');
    
    const wsTest = await page.evaluate(() => {
        return new Promise((resolve) => {
            try {
                const ws = new WebSocket('ws://localhost:8000/ws/game/');
                
                const timeout = setTimeout(() => {
                    ws.close();
                    resolve({ error: 'Connection timeout' });
                }, 5000);
                
                ws.onopen = () => {
                    clearTimeout(timeout);
                    ws.close();
                    resolve({ success: true, message: 'WebSocket connected successfully' });
                };
                
                ws.onerror = (error) => {
                    clearTimeout(timeout);
                    resolve({ error: 'WebSocket connection failed', details: error.message });
                };
                
                ws.onclose = (event) => {
                    if (!timeout._destroyed) {
                        clearTimeout(timeout);
                        resolve({ error: 'WebSocket closed unexpectedly', code: event.code, reason: event.reason });
                    }
                };
                
            } catch (error) {
                resolve({ error: 'WebSocket creation failed', details: error.message });
            }
        });
    });
    
    console.log(`WebSocket test result:`, wsTest);
    
    console.log('\n🔍 DIAGNOSIS:');
    if (wsTest.success) {
        console.log('✅ WebSocket connection works - frontend detection logic issue');
        console.log('💡 Check: Frontend initialization, error boundary, demo mode trigger');
    } else if (wsTest.error) {
        console.log('❌ WebSocket connection failed - network/backend issue');
        console.log(`💡 Error: ${wsTest.error}`);
        if (wsTest.details) console.log(`💡 Details: ${wsTest.details}`);
    }
    
    await browser.close();
}

debugFrontendConnection().catch(console.error);
