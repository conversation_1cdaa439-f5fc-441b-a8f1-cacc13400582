#!/usr/bin/env node

/**
 * Test Wheel ID Fix - Verify that backend sends proper wheel item IDs
 * 
 * This test connects directly to the backend WebSocket and captures
 * the exact wheel data structure to verify the architectural fix.
 */

const WebSocket = require('ws');

const CONFIG = {
  gameWebSocketUrl: 'ws://localhost:8000/ws/game/',
  testUserId: '2',
  testMessage: 'hey, I\'m feeling energetic and I have 2h free ahead of me. It\'s very hot outside though. Generate me the perfect wheel !'
};

class WheelIDFixTest {
  constructor() {
    this.ws = null;
    this.wheelData = null;
    this.testResults = {
      wheelReceived: false,
      properWheelItemIDs: false,
      activityTailoredIDsPresent: false,
      idMappingCorrect: false
    };
  }

  async runTest() {
    console.log('🧪 Testing Wheel ID Fix');
    console.log('========================');
    
    return new Promise((resolve, reject) => {
      this.ws = new WebSocket(CONFIG.gameWebSocketUrl);
      
      this.ws.on('open', () => {
        console.log('✅ WebSocket connected');
        this.sendWheelRequest();
      });
      
      this.ws.on('message', (data) => {
        try {
          const message = JSON.parse(data.toString());
          this.handleMessage(message);
        } catch (e) {
          console.log(`📥 Non-JSON message: ${data}`);
        }
      });
      
      this.ws.on('error', (error) => {
        console.error(`❌ WebSocket error: ${error.message}`);
        reject(error);
      });
      
      this.ws.on('close', () => {
        console.log('🔌 WebSocket closed');
        resolve(this.testResults);
      });
      
      // Timeout after 2 minutes
      setTimeout(() => {
        if (this.ws.readyState === WebSocket.OPEN) {
          this.ws.close();
        }
        resolve(this.testResults);
      }, 120000);
    });
  }

  sendWheelRequest() {
    console.log('📤 Sending wheel generation request...');
    
    const message = {
      type: 'chat_message',
      content: {
        message: CONFIG.testMessage,
        user_profile_id: CONFIG.testUserId,
        timestamp: new Date().toISOString()
      }
    };
    
    this.ws.send(JSON.stringify(message));
    console.log('✅ Request sent');
  }

  handleMessage(message) {
    console.log(`📥 Received: ${message.type}`);
    
    if (message.type === 'wheel_data') {
      console.log('🎡 WHEEL DATA RECEIVED!');
      console.log('======================');
      
      this.wheelData = message;
      this.testResults.wheelReceived = true;
      
      // Analyze wheel data structure
      this.analyzeWheelData(message.wheel);
      
      // Close connection after analysis
      setTimeout(() => {
        this.ws.close();
      }, 1000);
    }
  }

  analyzeWheelData(wheel) {
    if (!wheel || !wheel.items || !Array.isArray(wheel.items)) {
      console.log('❌ Invalid wheel structure');
      return;
    }

    console.log(`📊 Wheel: ${wheel.name}`);
    console.log(`📊 Items: ${wheel.items.length}`);
    console.log('');

    let properWheelItemIDs = true;
    let activityTailoredIDsPresent = true;
    let idMappingCorrect = true;

    wheel.items.forEach((item, index) => {
      console.log(`🔍 Item ${index + 1}:`);
      console.log(`   - ID: ${item.id}`);
      console.log(`   - Name: ${item.name}`);
      console.log(`   - Activity Tailored ID: ${item.activity_tailored_id}`);
      
      // Check if wheel item ID has proper format
      if (!item.id || item.id.startsWith('llm_tailored_') || item.id.startsWith('generic-')) {
        console.log(`   ❌ ISSUE: Improper wheel item ID format: ${item.id}`);
        properWheelItemIDs = false;
      } else if (item.id.startsWith('item_') || item.id.startsWith('item-') || item.id.startsWith('wheel-item-')) {
        console.log(`   ✅ GOOD: Proper wheel item ID format: ${item.id}`);
      } else {
        console.log(`   ⚠️  WARNING: Unexpected wheel item ID format: ${item.id}`);
      }
      
      // Check if activity tailored ID is present and different from wheel item ID
      if (!item.activity_tailored_id) {
        console.log(`   ❌ ISSUE: Missing activity_tailored_id`);
        activityTailoredIDsPresent = false;
      } else if (item.id === item.activity_tailored_id) {
        console.log(`   ❌ ISSUE: Wheel item ID same as activity tailored ID`);
        idMappingCorrect = false;
      } else {
        console.log(`   ✅ GOOD: Proper ID separation`);
      }
      
      console.log('');
    });

    this.testResults.properWheelItemIDs = properWheelItemIDs;
    this.testResults.activityTailoredIDsPresent = activityTailoredIDsPresent;
    this.testResults.idMappingCorrect = idMappingCorrect;

    // Summary
    console.log('📊 ANALYSIS RESULTS:');
    console.log('===================');
    console.log(`✅ Wheel received: ${this.testResults.wheelReceived}`);
    console.log(`${properWheelItemIDs ? '✅' : '❌'} Proper wheel item IDs: ${properWheelItemIDs}`);
    console.log(`${activityTailoredIDsPresent ? '✅' : '❌'} Activity tailored IDs present: ${activityTailoredIDsPresent}`);
    console.log(`${idMappingCorrect ? '✅' : '❌'} ID mapping correct: ${idMappingCorrect}`);
    
    const allGood = properWheelItemIDs && activityTailoredIDsPresent && idMappingCorrect;
    console.log('');
    console.log(`🎯 OVERALL RESULT: ${allGood ? '✅ ARCHITECTURAL FIX SUCCESSFUL' : '❌ ISSUES REMAIN'}`);
  }
}

// Run the test
if (require.main === module) {
  const test = new WheelIDFixTest();
  test.runTest()
    .then(results => {
      console.log('\n🏁 TEST COMPLETED');
      console.log('================');
      
      const success = results.wheelReceived && 
                     results.properWheelItemIDs && 
                     results.activityTailoredIDsPresent && 
                     results.idMappingCorrect;
      
      if (success) {
        console.log('🎉 ALL TESTS PASSED - ARCHITECTURAL FIX SUCCESSFUL!');
        process.exit(0);
      } else {
        console.log('💥 SOME TESTS FAILED - ISSUES REMAIN');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('💥 Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = { WheelIDFixTest };
