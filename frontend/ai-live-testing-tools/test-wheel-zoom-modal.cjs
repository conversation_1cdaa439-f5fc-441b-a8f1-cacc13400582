#!/usr/bin/env node

/**
 * Focused test for wheel zoom and modal positioning fixes
 */

const { chromium } = require('playwright');

const FRONTEND_URL = process.argv[2] || 'http://localhost:3001';

async function testWheelZoomAndModal() {
  console.log('🎡 Testing wheel zoom and modal positioning...');
  console.log(`📍 Testing frontend at: ${FRONTEND_URL}`);

  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 500
  });
  
  const context = await browser.newContext({
    viewport: { width: 1400, height: 900 }
  });
  
  const page = await context.newPage();

  // Listen to console logs for debugging
  page.on('console', msg => {
    if (msg.text().includes('🖱️') || msg.text().includes('📍') || msg.text().includes('zoom') || msg.text().includes('modal')) {
      console.log('BROWSER:', msg.text());
    }
  });

  try {
    // Navigate to the frontend
    console.log('🌐 Navigating to frontend...');
    await page.goto(FRONTEND_URL);
    await page.waitForTimeout(3000);

    // Open debug panel
    console.log('📋 Opening debug panel...');
    await page.keyboard.press('Control+Shift+D');
    await page.waitForTimeout(1000);

    // Select PhiPhi user (ID: 2)
    const userSelect = await page.locator('debug-panel select').first();
    if (await userSelect.isVisible()) {
      await userSelect.selectOption('2');
      console.log('✅ Selected PhiPhi user');
      await page.waitForTimeout(500);
    }

    // Set time to 7% (should be around 26 minutes)
    const timeSlider = await page.locator('input[type="range"]').first();
    if (await timeSlider.isVisible()) {
      await timeSlider.fill('7');
      await page.waitForTimeout(500);
      
      const timeDisplay = await page.locator('.potentiometer-value').first().textContent();
      console.log(`✅ Time set to: ${timeDisplay}`);
    }

    // Set energy to 100%
    const energySlider = await page.locator('input[type="range"]').nth(1);
    if (await energySlider.isVisible()) {
      await energySlider.fill('100');
      console.log('✅ Energy set to 100%');
    }

    // Click generate button
    console.log('🎯 Clicking generate button...');
    const generateButton = await page.locator('.generate-button');
    if (await generateButton.isVisible()) {
      await generateButton.click();
      console.log('✅ Generate button clicked');
      
      // Wait for wheel to appear
      console.log('⏳ Waiting for wheel generation...');
      await page.waitForTimeout(15000); // Wait up to 15 seconds
      
      const wheelComponent = await page.locator('game-wheel');
      const wheelVisible = await wheelComponent.isVisible();
      console.log(`🎡 Wheel generated: ${wheelVisible ? '✅' : '❌'}`);
      
      if (wheelVisible) {
        // Get wheel dimensions and position
        const wheelBox = await wheelComponent.boundingBox();
        console.log(`📏 Wheel position: x=${wheelBox.x}, y=${wheelBox.y}`);
        console.log(`📏 Wheel size: ${wheelBox.width}x${wheelBox.height}`);
        
        // Calculate expected zoom center (bottom edge of wheel)
        const wheelCenterX = wheelBox.x + wheelBox.width / 2;
        const wheelBottomY = wheelBox.y + wheelBox.height;
        console.log(`🎯 Expected zoom center: x=${wheelCenterX}, y=${wheelBottomY} (bottom edge)`);
        
        // Look for spin button
        const spinButton = await page.locator('.spin-button');
        if (await spinButton.isVisible()) {
          console.log('🎲 Spin button found, initiating spin...');
          
          // Monitor zoom during spin
          let maxZoom = 1.0;
          let zoomCenterCorrect = false;
          
          const zoomMonitor = setInterval(async () => {
            try {
              const canvas = await page.locator('game-wheel canvas').first();
              if (await canvas.isVisible()) {
                const transform = await canvas.evaluate(el => el.style.transform);
                const transformOrigin = await canvas.evaluate(el => el.style.transformOrigin);
                
                if (transform) {
                  const scaleMatch = transform.match(/scale\\(([^)]+)\\)/);
                  if (scaleMatch) {
                    const currentZoom = parseFloat(scaleMatch[1]);
                    if (currentZoom > maxZoom) {
                      maxZoom = currentZoom;
                    }
                  }
                }
                
                if (transformOrigin) {
                  console.log(`🔍 Transform origin: ${transformOrigin}`);
                  // Check if the Y coordinate is at the bottom of the wheel
                  const originMatch = transformOrigin.match(/(\\d+)px (\\d+)px/);
                  if (originMatch) {
                    const originY = parseInt(originMatch[2]);
                    const expectedBottomY = wheelBox.height; // Relative to wheel
                    zoomCenterCorrect = Math.abs(originY - expectedBottomY) < 10;
                  }
                }
              }
            } catch (e) {
              // Ignore errors during monitoring
            }
          }, 200);
          
          // Click spin button
          await spinButton.click();
          console.log('🎲 Wheel spin initiated');
          
          // Wait for spin to complete
          await page.waitForTimeout(10000);
          clearInterval(zoomMonitor);
          
          console.log(`🔍 Maximum zoom detected: ${maxZoom.toFixed(2)}x`);
          console.log(`🎯 Zoom center at bottom edge: ${zoomCenterCorrect ? '✅' : '❌'}`);
          console.log(`📈 Progressive zoom working: ${maxZoom > 1.5 ? '✅' : '❌'}`);
          
          // Check for winning modal
          const winningModal = await page.locator('.winning-modal');
          const winningModalVisible = await winningModal.isVisible();
          console.log(`🏆 Winning modal appeared: ${winningModalVisible ? '✅' : '❌'}`);
          
          if (winningModalVisible) {
            const modalBox = await winningModal.boundingBox();
            console.log(`📏 Modal position: x=${modalBox.x}, y=${modalBox.y}`);
            console.log(`📏 Modal size: ${modalBox.width}x${modalBox.height}`);
            
            // Check if modal is positioned relative to wheel (not viewport)
            const modalRelativeToWheel = (
              modalBox.x >= wheelBox.x && 
              modalBox.y >= wheelBox.y &&
              modalBox.x + modalBox.width <= wheelBox.x + wheelBox.width &&
              modalBox.y + modalBox.height <= wheelBox.y + wheelBox.height
            );
            
            console.log(`🎯 Modal positioned relative to wheel: ${modalRelativeToWheel ? '✅' : '❌'}`);
            
            // Check if modal is centered within the wheel
            const wheelCenterX = wheelBox.x + wheelBox.width / 2;
            const wheelCenterY = wheelBox.y + wheelBox.height / 2;
            const modalCenterX = modalBox.x + modalBox.width / 2;
            const modalCenterY = modalBox.y + modalBox.height / 2;
            
            const xDiff = Math.abs(wheelCenterX - modalCenterX);
            const yDiff = Math.abs(wheelCenterY - modalCenterY);
            
            console.log(`📐 Modal centering - X diff: ${xDiff}px, Y diff: ${yDiff}px`);
            console.log(`🎯 Modal properly centered on wheel: ${xDiff < 50 && yDiff < 50 ? '✅' : '❌'}`);
            
            // Test activity modal functionality
            console.log('\\n📝 Testing activity modal...');
            
            // Close winning modal first
            const closeModalBtn = await page.locator('.winning-modal .close-btn, .winning-modal button').first();
            if (await closeModalBtn.isVisible()) {
              await closeModalBtn.click();
              await page.waitForTimeout(500);
            }
            
            // Look for activity items
            const activityItems = await page.locator('.activity-item');
            const activityCount = await activityItems.count();
            console.log(`📝 Activity items found: ${activityCount}`);
            
            if (activityCount > 0) {
              // Expand first activity
              await activityItems.first().click();
              await page.waitForTimeout(500);
              
              // Look for change button
              const changeButton = await page.locator('.activity-change-btn').first();
              if (await changeButton.isVisible()) {
                console.log('✅ Change button found');
                
                // Click change button to open modal
                await changeButton.click();
                await page.waitForTimeout(1000);
                
                const activityModal = await page.locator('.modal');
                const modalVisible = await activityModal.isVisible();
                console.log(`📝 Activity modal opened: ${modalVisible ? '✅' : '❌'}`);
                
                if (modalVisible) {
                  // Test create new activity button
                  const createButton = await page.locator('.create-activity-btn');
                  const createButtonVisible = await createButton.isVisible();
                  console.log(`➕ Create activity button found: ${createButtonVisible ? '✅' : '❌'}`);
                  
                  if (createButtonVisible) {
                    await createButton.click();
                    await page.waitForTimeout(1000);
                    
                    const createModal = await page.locator('.modal').nth(1);
                    const createModalVisible = await createModal.isVisible();
                    console.log(`➕ Create activity modal opened: ${createModalVisible ? '✅' : '❌'}`);
                  }
                }
              }
            }
          }
        } else {
          console.log('❌ Spin button not found');
        }
      }
    } else {
      console.log('❌ Generate button not found');
    }

    console.log('\\n🎉 Test completed! Check the browser window for visual validation.');
    console.log('Press Ctrl+C to close the browser...');
    
    // Keep browser open for manual inspection
    await page.waitForTimeout(60000);

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await browser.close();
  }
}

// Run the test
testWheelZoomAndModal().catch(console.error);
