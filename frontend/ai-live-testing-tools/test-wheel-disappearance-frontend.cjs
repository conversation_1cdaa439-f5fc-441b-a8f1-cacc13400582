#!/usr/bin/env node

/**
 * Frontend-focused test to reproduce the wheel disappearance issue.
 * 
 * This test focuses on the frontend state management and WebSocket communication
 * to identify why the wheel appears to "disappear" from the user's perspective
 * after item removal, even though the backend works correctly.
 */

const puppeteer = require('puppeteer');

const CONFIG = {
  baseUrl: 'http://localhost:3000',
  apiUrl: 'http://localhost:8000',
  timeout: 30000,
  debugMode: true
};

class WheelDisappearanceFrontendTest {
  constructor() {
    this.browser = null;
    this.page = null;
    this.testResults = {
      initialWheelState: null,
      removalApiCall: null,
      frontendStateAfterRemoval: null,
      websocketMessages: [],
      stateTransitions: [],
      potentialIssues: []
    };
  }

  async setup() {
    console.log('🔧 Setting up frontend test environment...');
    
    this.browser = await puppeteer.launch({
      headless: false, // Show browser for debugging
      devtools: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    this.page = await this.browser.newPage();
    
    // Enable console logging
    this.page.on('console', msg => {
      if (CONFIG.debugMode) {
        console.log(`🖥️ [BROWSER] ${msg.text()}`);
      }
    });
    
    // Monitor network requests
    this.page.on('response', response => {
      if (response.url().includes('/api/wheel-items/') && response.request().method() === 'DELETE') {
        console.log(`🌐 [NETWORK] DELETE ${response.url()} - Status: ${response.status()}`);
      }
    });
    
    return true;
  }

  async navigateToApp() {
    console.log('🌐 Navigating to application...');
    
    await this.page.goto(CONFIG.baseUrl, { waitUntil: 'networkidle0' });
    
    // Wait for app to load
    await this.page.waitForSelector('app-shell', { timeout: CONFIG.timeout });
    
    console.log('✅ Application loaded');
    return true;
  }

  async setupFrontendMonitoring() {
    console.log('🔍 Setting up frontend state monitoring...');
    
    await this.page.evaluate(() => {
      // Global test state
      window.wheelDisappearanceTest = {
        wheelStates: [],
        websocketMessages: [],
        stateTransitions: [],
        apiCalls: []
      };
      
      // Monitor wheel state machine
      const appShell = document.querySelector('app-shell');
      if (appShell && appShell.wheelStateMachine) {
        const originalSetWheelData = appShell.wheelStateMachine.setWheelData;
        appShell.wheelStateMachine.setWheelData = function(data) {
          console.log('🔄 [STATE MACHINE] setWheelData called with:', data);
          window.wheelDisappearanceTest.stateTransitions.push({
            timestamp: Date.now(),
            action: 'setWheelData',
            data: data,
            previousState: this.currentState
          });
          return originalSetWheelData.call(this, data);
        };
        
        const originalClearWheel = appShell.wheelStateMachine.clearWheel;
        appShell.wheelStateMachine.clearWheel = function() {
          console.log('🔄 [STATE MACHINE] clearWheel called');
          window.wheelDisappearanceTest.stateTransitions.push({
            timestamp: Date.now(),
            action: 'clearWheel',
            previousState: this.currentState
          });
          return originalClearWheel.call(this);
        };
        
        const originalSetError = appShell.wheelStateMachine.setError;
        appShell.wheelStateMachine.setError = function(error) {
          console.log('🔄 [STATE MACHINE] setError called with:', error);
          window.wheelDisappearanceTest.stateTransitions.push({
            timestamp: Date.now(),
            action: 'setError',
            error: error,
            previousState: this.currentState
          });
          return originalSetError.call(this, error);
        };
      }
      
      // Monitor WebSocket messages
      const originalWebSocket = window.WebSocket;
      window.WebSocket = function(url, protocols) {
        const ws = new originalWebSocket(url, protocols);
        
        const originalOnMessage = ws.onmessage;
        ws.onmessage = function(event) {
          try {
            const data = JSON.parse(event.data);
            console.log('📨 [WEBSOCKET] Message received:', data.type);
            
            window.wheelDisappearanceTest.websocketMessages.push({
              timestamp: Date.now(),
              type: data.type,
              data: data
            });
            
            if (data.type === 'wheel_data') {
              console.log('🎡 [WEBSOCKET] Wheel data message received');
              console.log('🎡 Wheel structure:', {
                hasWheel: !!data.wheel,
                hasItems: !!(data.wheel && data.wheel.items),
                itemCount: data.wheel && data.wheel.items ? data.wheel.items.length : 0
              });
            }
          } catch (e) {
            // Ignore parsing errors
          }
          
          if (originalOnMessage) {
            return originalOnMessage.call(this, event);
          }
        };
        
        return ws;
      };
      
      // Monitor fetch API calls
      const originalFetch = window.fetch;
      window.fetch = function(url, options) {
        if (url.includes('/api/wheel-items/') && options && options.method === 'DELETE') {
          console.log('🌐 [FETCH] DELETE wheel item API call:', url);
          window.wheelDisappearanceTest.apiCalls.push({
            timestamp: Date.now(),
            url: url,
            method: options.method,
            type: 'wheel_item_removal'
          });
        }
        return originalFetch.call(this, url, options);
      };
    });
    
    console.log('✅ Frontend monitoring setup complete');
    return true;
  }

  async captureInitialWheelState() {
    console.log('📊 Capturing initial wheel state...');
    
    const wheelState = await this.page.evaluate(() => {
      const appShell = document.querySelector('app-shell');
      if (!appShell) return null;
      
      return {
        hasWheelData: !!appShell.wheelData,
        wheelDataSegmentCount: appShell.wheelData ? appShell.wheelData.segments.length : 0,
        wheelId: appShell.wheelData ? appShell.wheelData.wheelId : null,
        stateMachineState: appShell.wheelStateMachine ? appShell.wheelStateMachine.currentState : null,
        wheelDataPreview: appShell.wheelData ? appShell.wheelData.segments.slice(0, 3).map(s => ({
          id: s.id,
          name: s.name || s.text,
          color: s.color
        })) : null
      };
    });
    
    this.testResults.initialWheelState = wheelState;
    
    console.log('✅ Initial wheel state captured:');
    console.log(`   - Has wheel data: ${wheelState?.hasWheelData}`);
    console.log(`   - Segment count: ${wheelState?.wheelDataSegmentCount}`);
    console.log(`   - Wheel ID: ${wheelState?.wheelId}`);
    console.log(`   - State machine state: ${wheelState?.stateMachineState}`);
    
    return wheelState && wheelState.hasWheelData;
  }

  async triggerWheelItemRemoval() {
    console.log('🗑️ Triggering wheel item removal...');
    
    // Try to find and click a remove button
    const removalTriggered = await this.page.evaluate(() => {
      const appShell = document.querySelector('app-shell');
      if (!appShell || !appShell.shadowRoot) return false;
      
      // Look for remove button in the shadow DOM
      const removeButton = appShell.shadowRoot.querySelector('.remove-activity-btn');
      if (removeButton) {
        console.log('🗑️ Found remove button, clicking...');
        removeButton.click();
        return true;
      }
      
      // Alternative: try to call removeWheelItem directly if button not found
      if (appShell.wheelData && appShell.wheelData.segments.length > 0) {
        const firstSegment = appShell.wheelData.segments[0];
        const itemId = firstSegment.id;
        console.log('🗑️ Calling removeWheelItem directly with ID:', itemId);
        
        if (appShell.removeWheelItem) {
          appShell.removeWheelItem(itemId);
          return true;
        }
      }
      
      return false;
    });
    
    if (!removalTriggered) {
      console.log('❌ Could not trigger wheel item removal');
      this.testResults.potentialIssues.push('Could not trigger wheel item removal');
      return false;
    }
    
    console.log('✅ Wheel item removal triggered');
    
    // Wait for API call and response
    await this.page.waitForTimeout(2000);
    
    return true;
  }

  async captureFinalState() {
    console.log('📊 Capturing final state after removal...');
    
    const finalState = await this.page.evaluate(() => {
      const appShell = document.querySelector('app-shell');
      const testData = window.wheelDisappearanceTest;
      
      return {
        wheelState: {
          hasWheelData: !!appShell.wheelData,
          wheelDataSegmentCount: appShell.wheelData ? appShell.wheelData.segments.length : 0,
          wheelId: appShell.wheelData ? appShell.wheelData.wheelId : null,
          stateMachineState: appShell.wheelStateMachine ? appShell.wheelStateMachine.currentState : null,
          wheelDataPreview: appShell.wheelData ? appShell.wheelData.segments.slice(0, 3).map(s => ({
            id: s.id,
            name: s.name || s.text,
            color: s.color
          })) : null
        },
        testData: testData
      };
    });
    
    this.testResults.frontendStateAfterRemoval = finalState.wheelState;
    this.testResults.websocketMessages = finalState.testData.websocketMessages;
    this.testResults.stateTransitions = finalState.testData.stateTransitions;
    
    console.log('✅ Final state captured:');
    console.log(`   - Has wheel data: ${finalState.wheelState?.hasWheelData}`);
    console.log(`   - Segment count: ${finalState.wheelState?.wheelDataSegmentCount}`);
    console.log(`   - Wheel ID: ${finalState.wheelState?.wheelId}`);
    console.log(`   - State machine state: ${finalState.wheelState?.stateMachineState}`);
    console.log(`   - WebSocket messages: ${this.testResults.websocketMessages.length}`);
    console.log(`   - State transitions: ${this.testResults.stateTransitions.length}`);
    
    return true;
  }

  analyzeResults() {
    console.log('\n🔍 Analyzing test results...');
    
    const initial = this.testResults.initialWheelState;
    const final = this.testResults.frontendStateAfterRemoval;
    
    // Check if wheel disappeared from frontend
    if (initial?.hasWheelData && !final?.hasWheelData) {
      this.testResults.potentialIssues.push('CRITICAL: Wheel data disappeared from frontend');
    }
    
    // Check if wheel ID changed
    if (initial?.wheelId && final?.wheelId && initial.wheelId !== final.wheelId) {
      this.testResults.potentialIssues.push(`Wheel ID changed: ${initial.wheelId} -> ${final.wheelId}`);
    }
    
    // Check state machine transitions
    const errorTransitions = this.testResults.stateTransitions.filter(t => t.action === 'setError');
    if (errorTransitions.length > 0) {
      this.testResults.potentialIssues.push(`State machine errors: ${errorTransitions.map(t => t.error).join(', ')}`);
    }
    
    const clearTransitions = this.testResults.stateTransitions.filter(t => t.action === 'clearWheel');
    if (clearTransitions.length > 0) {
      this.testResults.potentialIssues.push('Wheel was cleared by state machine');
    }
    
    // Check WebSocket messages
    const wheelDataMessages = this.testResults.websocketMessages.filter(m => m.type === 'wheel_data');
    if (wheelDataMessages.length === 0) {
      this.testResults.potentialIssues.push('No wheel_data WebSocket messages received after removal');
    }
    
    console.log(`📊 Analysis complete. Issues found: ${this.testResults.potentialIssues.length}`);
  }

  generateReport() {
    console.log('\n📋 FRONTEND TEST REPORT');
    console.log('=' * 50);
    
    if (this.testResults.potentialIssues.length > 0) {
      console.log(`\n⚠️ ISSUES IDENTIFIED (${this.testResults.potentialIssues.length}):`);
      this.testResults.potentialIssues.forEach((issue, i) => {
        console.log(`   ${i + 1}. ${issue}`);
      });
    } else {
      console.log('\n✅ NO FRONTEND ISSUES IDENTIFIED');
    }
    
    console.log('\n📊 State Transitions:');
    this.testResults.stateTransitions.forEach((transition, i) => {
      console.log(`   ${i + 1}. ${transition.action} (${new Date(transition.timestamp).toISOString()})`);
    });
    
    console.log('\n📨 WebSocket Messages:');
    this.testResults.websocketMessages.forEach((msg, i) => {
      console.log(`   ${i + 1}. ${msg.type} (${new Date(msg.timestamp).toISOString()})`);
    });
  }

  async runTest() {
    console.log('🧪 Starting Frontend Wheel Disappearance Test');
    console.log('=' * 50);
    
    try {
      if (!await this.setup()) return false;
      if (!await this.navigateToApp()) return false;
      if (!await this.setupFrontendMonitoring()) return false;
      if (!await this.captureInitialWheelState()) return false;
      if (!await this.triggerWheelItemRemoval()) return false;
      if (!await this.captureFinalState()) return false;
      
      this.analyzeResults();
      this.generateReport();
      
      return true;
    } catch (error) {
      console.error('❌ Test failed with error:', error);
      return false;
    } finally {
      if (this.browser) {
        await this.browser.close();
      }
    }
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
    }
  }
}

// Run the test
async function main() {
  const test = new WheelDisappearanceFrontendTest();
  const success = await test.runTest();
  
  if (success) {
    console.log('\n✅ Frontend test completed');
  } else {
    console.log('\n❌ Frontend test failed');
  }
  
  process.exit(success ? 0 : 1);
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = WheelDisappearanceFrontendTest;
