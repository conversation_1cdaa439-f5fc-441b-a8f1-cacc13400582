#!/usr/bin/env node

/**
 * WebSocket Connection Debug
 * 
 * Comprehensive test to debug WebSocket connection issues
 */

import WebSocket from 'ws';

console.log('🔍 WebSocket Connection Debug');
console.log('==============================\n');

// Test different WebSocket endpoints
const endpoints = [
  'ws://localhost:8000/ws/game/',
  'ws://localhost:8000/ws/connection-monitor/',
  'ws://localhost:8000/ws/admin-tester/'
];

async function testEndpoint(url, name) {
  console.log(`🔌 Testing ${name}: ${url}`);
  
  return new Promise((resolve) => {
    const socket = new WebSocket(url);
    const timeout = setTimeout(() => {
      console.log(`⏰ ${name}: Connection timeout`);
      socket.close();
      resolve({ success: false, error: 'timeout' });
    }, 5000);

    socket.on('open', () => {
      clearTimeout(timeout);
      console.log(`✅ ${name}: Connected successfully`);
      
      // Send a test message
      const message = {
        type: 'chat_message',
        content: {
          message: 'Debug test message',
          user_profile_id: '2', // Use real user ID (PhiPhi - fake user for testing)
          timestamp: new Date().toISOString()
        }
      };
      
      try {
        socket.send(JSON.stringify(message));
        console.log(`📤 ${name}: Sent test message`);
      } catch (error) {
        console.log(`❌ ${name}: Failed to send message: ${error.message}`);
      }
      
      // Wait for response
      setTimeout(() => {
        socket.close();
        resolve({ success: true });
      }, 2000);
    });

    socket.on('error', (error) => {
      clearTimeout(timeout);
      console.log(`❌ ${name}: Connection error: ${error.message}`);
      resolve({ success: false, error: error.message });
    });

    socket.on('message', (data) => {
      try {
        const message = JSON.parse(data);
        console.log(`📨 ${name}: Received: ${message.type}`);
      } catch (error) {
        console.log(`📨 ${name}: Received raw: ${data.toString()}`);
      }
    });

    socket.on('close', (code, reason) => {
      console.log(`🔌 ${name}: Closed: ${code} ${reason}`);
    });
  });
}

async function testAllEndpoints() {
  console.log('🧪 Testing all WebSocket endpoints...\n');
  
  for (const url of endpoints) {
    const name = url.split('/').pop() || 'unknown';
    await testEndpoint(url, name);
    console.log(''); // Add spacing
  }
}

async function testBackendConnectivity() {
  console.log('🏥 Testing backend connectivity...\n');
  
  try {
    // Test HTTP connectivity
    const response = await fetch('http://localhost:8000/admin/', {
      method: 'HEAD'
    });
    console.log(`✅ HTTP connectivity: ${response.status} ${response.statusText}`);
  } catch (error) {
    console.log(`❌ HTTP connectivity failed: ${error.message}`);
  }
  
  console.log('');
}

async function testSpecificGameEndpoint() {
  console.log('🎮 Detailed test of /ws/game/ endpoint...\n');
  
  return new Promise((resolve) => {
    const socket = new WebSocket('ws://localhost:8000/ws/game/');
    let messageCount = 0;
    
    const timeout = setTimeout(() => {
      console.log('⏰ Game endpoint test timeout');
      socket.close();
      resolve();
    }, 10000);

    socket.on('open', () => {
      console.log('✅ Game endpoint: Connected');
      
      // Send multiple test messages
      const messages = [
        {
          type: 'chat_message',
          content: {
            message: 'Hello from debug test',
            user_profile_id: '2', // Use real user ID (PhiPhi - fake user for testing)
            timestamp: new Date().toISOString()
          }
        },
        {
          type: 'workflow_status_request',
          content: {
            workflow_id: 'test-workflow-123'
          }
        }
      ];
      
      messages.forEach((message, index) => {
        setTimeout(() => {
          try {
            socket.send(JSON.stringify(message));
            console.log(`📤 Game endpoint: Sent message ${index + 1}: ${message.type}`);
          } catch (error) {
            console.log(`❌ Game endpoint: Failed to send message ${index + 1}: ${error.message}`);
          }
        }, (index + 1) * 1000);
      });
    });

    socket.on('error', (error) => {
      clearTimeout(timeout);
      console.log(`❌ Game endpoint error: ${error.message}`);
      resolve();
    });

    socket.on('message', (data) => {
      messageCount++;
      try {
        const message = JSON.parse(data);
        console.log(`📨 Game endpoint: Message ${messageCount}: ${message.type}`);
        if (message.content) {
          console.log(`   Content: ${JSON.stringify(message.content).substring(0, 100)}...`);
        }
      } catch (error) {
        console.log(`📨 Game endpoint: Raw message ${messageCount}: ${data.toString().substring(0, 100)}...`);
      }
    });

    socket.on('close', (code, reason) => {
      clearTimeout(timeout);
      console.log(`🔌 Game endpoint: Closed: ${code} ${reason}`);
      console.log(`📊 Game endpoint: Received ${messageCount} messages total`);
      resolve();
    });
  });
}

async function main() {
  try {
    await testBackendConnectivity();
    await testAllEndpoints();
    await testSpecificGameEndpoint();
    
    console.log('🎯 Debug Summary:');
    console.log('- If /ws/game/ connects but no backend logs appear, the UserSessionConsumer is not being called');
    console.log('- If /ws/connection-monitor/ connects, the admin endpoint is working');
    console.log('- If messages are received, the WebSocket is working but connection tracking may be broken');
    console.log('- Check backend logs for UserSessionConsumer debug messages');
    
  } catch (error) {
    console.error('❌ Debug test failed:', error.message);
  }
}

main();
