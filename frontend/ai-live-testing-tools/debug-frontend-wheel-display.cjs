#!/usr/bin/env node

/**
 * Debug Frontend Wheel Display
 * 
 * This test focuses on debugging why the frontend is not properly displaying
 * the wheel data that the backend is sending correctly.
 */

const { chromium } = require('playwright');

class DebugFrontendWheelDisplay {
    constructor() {
        this.browser = null;
        this.page = null;
        this.startTime = Date.now();
    }

    log(message) {
        const timestamp = new Date().toISOString();
        const elapsed = ((Date.now() - this.startTime) / 1000).toFixed(1);
        console.log(`[${elapsed}s] ${message}`);
    }

    async initialize() {
        this.log('🚀 Initializing Frontend Wheel Display Debug...');
        
        this.browser = await chromium.launch({ 
            headless: false,
            slowMo: 100,
            args: ['--disable-web-security']
        });
        
        this.page = await this.browser.newPage();
        
        // Set up comprehensive console logging
        this.page.on('console', msg => {
            const type = msg.type();
            const text = msg.text();
            if (type === 'error') {
                this.log(`🖥️  [ERROR] ${text}`);
            } else if (type === 'warn') {
                this.log(`🖥️  [WARN] ${text}`);
            } else if (type === 'log' && (text.includes('wheel') || text.includes('WebSocket') || text.includes('message'))) {
                this.log(`🖥️  [LOG] ${text}`);
            }
        });

        // Set up error handling
        this.page.on('pageerror', error => {
            this.log(`🖥️  [PAGE ERROR] ${error.message}`);
        });

        this.log('✅ Debug environment initialized');
    }

    async setupFrontendDebugging() {
        this.log('🔧 Setting up frontend debugging...');
        
        // Inject debugging code into the page
        await this.page.addInitScript(() => {
            window.wheelDebug = {
                messages: [],
                wheelData: null,
                wheelDisplayed: false,
                errors: []
            };

            // Override console.log to capture wheel-related logs
            const originalLog = console.log;
            console.log = function(...args) {
                const message = args.join(' ');
                if (message.includes('wheel') || message.includes('WebSocket') || message.includes('message')) {
                    window.wheelDebug.messages.push({
                        timestamp: Date.now(),
                        message: message,
                        type: 'log'
                    });
                }
                originalLog.apply(console, args);
            };

            // Override console.error to capture errors
            const originalError = console.error;
            console.error = function(...args) {
                const message = args.join(' ');
                window.wheelDebug.errors.push({
                    timestamp: Date.now(),
                    message: message,
                    type: 'error'
                });
                originalError.apply(console, args);
            };

            // Monitor WebSocket messages
            const originalWebSocket = window.WebSocket;
            window.WebSocket = function(url, protocols) {
                const ws = new originalWebSocket(url, protocols);
                
                ws.addEventListener('message', (event) => {
                    try {
                        const data = JSON.parse(event.data);
                        window.wheelDebug.messages.push({
                            timestamp: Date.now(),
                            message: `WebSocket received: ${data.type}`,
                            data: data,
                            type: 'websocket'
                        });
                        
                        if (data.type === 'wheel_data') {
                            window.wheelDebug.wheelData = data;
                            console.log('🎡 WHEEL DATA CAPTURED:', JSON.stringify(data, null, 2));
                            
                            // Check if wheel is displayed after receiving data
                            setTimeout(() => {
                                const wheelElements = document.querySelectorAll('.wheel, .wheel-container, [class*="wheel"]');
                                const activityElements = document.querySelectorAll('.activity, .wheel-item, [class*="activity"]');
                                
                                window.wheelDebug.wheelDisplayed = wheelElements.length > 0 && activityElements.length > 0;
                                console.log(`🎡 Wheel display check: ${wheelElements.length} wheel elements, ${activityElements.length} activity elements`);
                            }, 1000);
                        }
                    } catch (e) {
                        window.wheelDebug.messages.push({
                            timestamp: Date.now(),
                            message: `WebSocket non-JSON: ${event.data}`,
                            type: 'websocket'
                        });
                    }
                });
                
                return ws;
            };
        });

        this.log('✅ Frontend debugging setup complete');
    }

    async loadAndTestFrontend() {
        this.log('🌐 Loading frontend...');
        
        try {
            await this.page.goto('http://localhost:3000/', { 
                waitUntil: 'networkidle',
                timeout: 30000 
            });
            
            this.log('✅ Frontend loaded');
            
            // Wait for initial setup
            await this.page.waitForTimeout(3000);
            
            // Check if we're in debug mode and configure
            const debugMode = await this.page.evaluate(() => {
                return document.querySelector('.debug-panel, [class*="debug"]') !== null;
            });

            if (debugMode) {
                this.log('🐛 Debug mode detected, configuring...');
                
                const userSelect = await this.page.locator('select[id*="user"], select[name*="user"]').first();
                if (await userSelect.isVisible({ timeout: 5000 })) {
                    await userSelect.selectOption('2');
                    this.log('✅ Selected user ID 2');
                    await this.page.waitForTimeout(1000);
                }
            }

            // Send wheel generation request
            this.log('💬 Sending wheel generation request...');
            
            await this.page.waitForSelector('textarea', { timeout: 10000 });
            
            // Enable textarea if needed
            await this.page.evaluate(() => {
                const textarea = document.querySelector('textarea');
                if (textarea) {
                    textarea.disabled = false;
                    textarea.removeAttribute('disabled');
                }
            });

            const message = "I'm restless and need to do things physical. I have 2 hours. Make me a wheel";
            await this.page.fill('textarea', message);
            await this.page.press('textarea', 'Enter');
            
            this.log('✅ Message sent, waiting for wheel data...');
            
            // Wait for wheel data with extended timeout
            await this.page.waitForFunction(() => {
                return window.wheelDebug && window.wheelDebug.wheelData;
            }, { timeout: 120000 });
            
            this.log('✅ Wheel data received by frontend');
            
            // Wait a bit more for rendering
            await this.page.waitForTimeout(5000);
            
            // Analyze the frontend state
            await this.analyzeFrontendState();
            
        } catch (error) {
            this.log(`❌ Frontend test failed: ${error.message}`);
            await this.analyzeFrontendState();
        }
    }

    async analyzeFrontendState() {
        this.log('\n📊 FRONTEND STATE ANALYSIS');
        this.log('════════════════════════════════════════════════════════════');
        
        try {
            const debugData = await this.page.evaluate(() => {
                return {
                    wheelDebug: window.wheelDebug || {},
                    wheelElements: Array.from(document.querySelectorAll('.wheel, .wheel-container, [class*="wheel"]')).map(el => ({
                        tagName: el.tagName,
                        className: el.className,
                        id: el.id,
                        innerHTML: el.innerHTML.substring(0, 200) + '...'
                    })),
                    activityElements: Array.from(document.querySelectorAll('.activity, .wheel-item, [class*="activity"]')).map(el => ({
                        tagName: el.tagName,
                        className: el.className,
                        textContent: el.textContent.substring(0, 100)
                    })),
                    allElements: Array.from(document.querySelectorAll('*')).filter(el =>
                        el.className && typeof el.className === 'string' &&
                        (el.className.includes('wheel') || el.className.includes('activity'))
                    ).map(el => ({
                        tagName: el.tagName,
                        className: el.className,
                        id: el.id
                    }))
                };
            });

            // WebSocket Messages Analysis
            this.log(`📨 WebSocket Messages: ${debugData.wheelDebug.messages?.length || 0}`);
            if (debugData.wheelDebug.messages) {
                debugData.wheelDebug.messages.slice(-10).forEach((msg, i) => {
                    this.log(`  ${i+1}. [${msg.type}] ${msg.message}`);
                });
            }

            // Wheel Data Analysis
            if (debugData.wheelDebug.wheelData) {
                const wheelData = debugData.wheelDebug.wheelData;
                this.log(`\n🎡 Wheel Data Received:`);
                this.log(`  Type: ${wheelData.type}`);
                if (wheelData.wheel) {
                    this.log(`  Items: ${wheelData.wheel.items?.length || 0}`);
                    this.log(`  Activities: ${wheelData.wheel.activities?.length || 0}`);
                    if (wheelData.wheel.items) {
                        wheelData.wheel.items.slice(0, 3).forEach((item, i) => {
                            this.log(`    ${i+1}. ${item.name} (${item.color})`);
                        });
                    }
                }
            } else {
                this.log(`\n❌ No wheel data received`);
            }

            // DOM Elements Analysis
            this.log(`\n🏗️  DOM Elements:`);
            this.log(`  Wheel elements: ${debugData.wheelElements.length}`);
            debugData.wheelElements.forEach((el, i) => {
                this.log(`    ${i+1}. <${el.tagName}> class="${el.className}" id="${el.id}"`);
            });

            this.log(`  Activity elements: ${debugData.activityElements.length}`);
            debugData.activityElements.forEach((el, i) => {
                this.log(`    ${i+1}. <${el.tagName}> class="${el.className}" text="${el.textContent}"`);
            });

            this.log(`  All wheel-related elements: ${debugData.allElements.length}`);
            debugData.allElements.forEach((el, i) => {
                this.log(`    ${i+1}. <${el.tagName}> class="${el.className}" id="${el.id}"`);
            });

            // Error Analysis
            if (debugData.wheelDebug.errors?.length > 0) {
                this.log(`\n❌ Frontend Errors: ${debugData.wheelDebug.errors.length}`);
                debugData.wheelDebug.errors.forEach((error, i) => {
                    this.log(`  ${i+1}. ${error.message}`);
                });
            }

            // Diagnosis
            this.log(`\n🔍 DIAGNOSIS:`);
            if (!debugData.wheelDebug.wheelData) {
                this.log(`  ❌ Issue: Frontend not receiving wheel data from WebSocket`);
            } else if (debugData.wheelElements.length === 0) {
                this.log(`  ❌ Issue: Wheel data received but no wheel elements in DOM`);
                this.log(`  💡 Likely: Frontend not rendering wheel component`);
            } else if (debugData.activityElements.length === 0) {
                this.log(`  ❌ Issue: Wheel elements exist but no activity elements`);
                this.log(`  💡 Likely: Activities not being rendered within wheel`);
            } else {
                this.log(`  ✅ Wheel data received and elements present`);
                this.log(`  💡 Check: Visual rendering and styling issues`);
            }

        } catch (error) {
            this.log(`❌ Analysis failed: ${error.message}`);
        }
    }

    async cleanup() {
        if (this.browser) {
            await this.browser.close();
        }
    }

    async run() {
        try {
            await this.initialize();
            await this.setupFrontendDebugging();
            await this.loadAndTestFrontend();
            
        } catch (error) {
            this.log(`❌ Debug failed: ${error.message}`);
        } finally {
            await this.cleanup();
        }
    }
}

// Run the debug
if (require.main === module) {
    const debug = new DebugFrontendWheelDisplay();
    debug.run().catch(console.error);
}

module.exports = DebugFrontendWheelDisplay;
