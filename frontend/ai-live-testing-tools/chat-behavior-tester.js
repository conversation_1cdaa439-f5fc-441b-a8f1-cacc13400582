#!/usr/bin/env node

/**
 * Chat Behavior Tester
 * Simulates real frontend chat behavior to identify and fix issues
 * - Proper message structure
 * - Debug info handling
 * - Error message display
 * - Chat area scrolling behavior
 */

import WebSocket from 'ws';
import { CONFIG } from './config.js';
import { writeFileSync, mkdirSync } from 'fs';
import { join } from 'path';

class ChatBehaviorTester {
  constructor() {
    this.ws = null;
    this.isConnected = false;
    this.messages = [];
    this.debugMessages = [];
    this.errors = [];
    this.chatHeight = 400; // Simulated chat container height
    this.messageCount = 0;
    
    // Ensure logs directory exists
    try {
      mkdirSync('./logs', { recursive: true });
    } catch (e) {
      // Directory already exists
    }
  }

  async start() {
    console.log('🧪 Chat Behavior Tester Starting...');
    console.log('🎯 Simulating real frontend chat behavior');
    console.log('📡 Connecting to backend...\n');

    await this.connect();
    this.setupMessageHandlers();
    this.startInteractiveMode();
  }

  async connect() {
    return new Promise((resolve, reject) => {
      this.ws = new WebSocket(CONFIG.backend.websocketUrl);
      
      const timeout = setTimeout(() => {
        reject(new Error('Connection timeout'));
      }, CONFIG.testing.shortTimeout);

      this.ws.on('open', () => {
        clearTimeout(timeout);
        this.isConnected = true;
        console.log('✅ Connected to backend');
        resolve();
      });

      this.ws.on('error', (error) => {
        clearTimeout(timeout);
        console.error('❌ Connection error:', error.message);
        reject(error);
      });

      this.ws.on('close', (code, reason) => {
        this.isConnected = false;
        console.log(`🔌 Connection closed: ${code} ${reason}`);
      });
    });
  }

  setupMessageHandlers() {
    this.ws.on('message', (data) => {
      try {
        const message = JSON.parse(data);
        this.handleIncomingMessage(message);
      } catch (error) {
        console.error('❌ Invalid JSON received:', data.toString());
        this.errors.push({
          type: 'parse_error',
          data: data.toString(),
          timestamp: new Date().toISOString()
        });
      }
    });
  }

  handleIncomingMessage(message) {
    const timestamp = new Date().toISOString();
    
    console.log(`\n📥 [${timestamp}] ${message.type || 'unknown'}`);
    
    switch (message.type) {
      case 'chat_message':
        this.handleChatMessage(message);
        break;
      case 'debug_info':
        this.handleDebugInfo(message);
        break;
      case 'error':
        this.handleError(message);
        break;
      case 'processing_status':
        this.handleProcessingStatus(message);
        break;
      case 'workflow_status':
        this.handleWorkflowStatus(message);
        break;
      case 'system_message':
        this.handleSystemMessage(message);
        break;
      default:
        console.log(`   Unknown message type: ${JSON.stringify(message, null, 2)}`);
    }
    
    this.simulateChatAreaBehavior();
  }

  handleChatMessage(message) {
    if (message.content) {
      const chatMsg = {
        id: `msg_${++this.messageCount}`,
        type: message.is_user ? 'user' : 'ai',
        content: typeof message.content === 'string' ? message.content : message.content.message || JSON.stringify(message.content),
        timestamp: new Date().toISOString()
      };
      
      this.messages.push(chatMsg);
      console.log(`   💬 ${chatMsg.type.toUpperCase()}: ${chatMsg.content.substring(0, 100)}${chatMsg.content.length > 100 ? '...' : ''}`);
      
      // Check if this is the final answer
      if (!message.is_user && chatMsg.content.length > 50) {
        console.log('   ✅ This appears to be a final answer from backend');
      }
    } else {
      console.log('   ⚠️ Chat message missing content field');
      console.log(`   Raw: ${JSON.stringify(message, null, 2)}`);
    }
  }

  handleDebugInfo(message) {
    const debugMsg = {
      timestamp: new Date().toISOString(),
      content: message.content || message,
      source: message.content?.source || 'unknown'
    };
    
    this.debugMessages.push(debugMsg);
    console.log(`   🐛 DEBUG (${debugMsg.source}): ${JSON.stringify(debugMsg.content, null, 2).substring(0, 150)}...`);
    
    // Check for problematic debug patterns
    const debugStr = JSON.stringify(debugMsg.content);
    if (debugStr.includes('error') || debugStr.includes('Error')) {
      console.log('   ⚠️ Debug message contains error information');
    }
  }

  handleError(message) {
    const errorMsg = {
      timestamp: new Date().toISOString(),
      content: message.content,
      type: 'error'
    };
    
    this.errors.push(errorMsg);
    console.log(`   ❌ ERROR: ${typeof message.content === 'string' ? message.content : JSON.stringify(message.content)}`);
    
    // Simulate frontend error display behavior
    console.log('   🎭 SIMULATING: Frontend would show "error UNKNOWN" in chat');
    
    // Check for specific error patterns
    const errorStr = typeof message.content === 'string' ? message.content : JSON.stringify(message.content);
    if (errorStr.includes('No LLMConfig provided')) {
      console.log('   🔥 CRITICAL: LLM Config error detected');
    }
    if (errorStr.includes('retrieving your profile')) {
      console.log('   🔥 CRITICAL: Profile retrieval error detected');
    }
  }

  handleProcessingStatus(message) {
    console.log(`   ⚙️ Processing: ${message.status}`);
    if (message.status === 'processing') {
      console.log('   🎭 SIMULATING: Frontend would show loading indicator');
    } else if (message.status === 'completed') {
      console.log('   🎭 SIMULATING: Frontend would hide loading indicator');
    }
  }

  handleWorkflowStatus(message) {
    console.log(`   🔄 Workflow: ${message.status} (${message.workflow_id || 'no-id'})`);
  }

  handleSystemMessage(message) {
    const systemMsg = {
      id: `sys_${++this.messageCount}`,
      type: 'system',
      content: message.content,
      timestamp: new Date().toISOString()
    };
    
    this.messages.push(systemMsg);
    console.log(`   📢 SYSTEM: ${systemMsg.content}`);
  }

  simulateChatAreaBehavior() {
    const totalMessages = this.messages.length + this.debugMessages.length;
    const estimatedHeight = totalMessages * 60; // Rough estimate: 60px per message
    
    if (estimatedHeight > this.chatHeight) {
      console.log(`   📏 CHAT BEHAVIOR: Container would overflow (${estimatedHeight}px > ${this.chatHeight}px)`);
      console.log('   🎭 SIMULATING: Chat area should scroll, but might be growing in height instead');
    }
    
    // Check for rapid message influx (debug spam)
    if (this.debugMessages.length > 10) {
      console.log('   ⚠️ CHAT BEHAVIOR: Too many debug messages - could cause UI issues');
    }
  }

  sendChatMessage(content, includeMetadata = true) {
    if (!this.isConnected) {
      console.log('❌ Cannot send message - not connected');
      return;
    }

    // Use the correct message structure that frontend should send
    const message = {
      type: 'chat_message',
      content: {
        message: content,
        user_profile_id: '2', // Use existing user ID
        timestamp: new Date().toISOString()
      }
    };

    // Add metadata if requested (simulating debug panel selections)
    if (includeMetadata) {
      message.content.metadata = {
        llm_config_id: '5' // Use existing LLM config ID
      };
    }

    console.log(`\n📤 SENDING: ${content}`);
    console.log(`   Structure: ${JSON.stringify(message, null, 2)}`);

    this.ws.send(JSON.stringify(message));

    // Add user message to our local messages
    this.messages.push({
      id: `user_${++this.messageCount}`,
      type: 'user',
      content: content,
      timestamp: new Date().toISOString()
    });
  }

  startInteractiveMode() {
    console.log('\n🎮 Interactive Chat Tester');
    console.log('Commands:');
    console.log('  1 - Send "I\'m bored" (reproduces your issue)');
    console.log('  2 - Send test message with metadata');
    console.log('  3 - Send test message without metadata');
    console.log('  4 - Show chat analysis');
    console.log('  5 - Show debug messages');
    console.log('  6 - Show errors');
    console.log('  q - Quit');
    console.log('  Or type any message to send it\n');

    process.stdin.setRawMode(false);
    process.stdin.resume();
    process.stdin.setEncoding('utf8');

    process.stdin.on('data', (input) => {
      const command = input.trim();

      switch (command) {
        case '1':
          this.sendChatMessage("I'm bored");
          break;
        case '2':
          this.sendChatMessage("Test message with metadata", true);
          break;
        case '3':
          this.sendChatMessage("Test message without metadata", false);
          break;
        case '4':
          this.showChatAnalysis();
          break;
        case '5':
          this.showDebugMessages();
          break;
        case '6':
          this.showErrors();
          break;
        case 'q':
          this.quit();
          break;
        default:
          if (command.length > 0) {
            this.sendChatMessage(command);
          }
          break;
      }
    });
  }

  showChatAnalysis() {
    console.log('\n📊 CHAT ANALYSIS');
    console.log('================');
    console.log(`Total messages: ${this.messages.length}`);
    console.log(`Debug messages: ${this.debugMessages.length}`);
    console.log(`Errors: ${this.errors.length}`);

    const userMessages = this.messages.filter(m => m.type === 'user');
    const aiMessages = this.messages.filter(m => m.type === 'ai');
    const systemMessages = this.messages.filter(m => m.type === 'system');

    console.log(`\nMessage breakdown:`);
    console.log(`  User: ${userMessages.length}`);
    console.log(`  AI: ${aiMessages.length}`);
    console.log(`  System: ${systemMessages.length}`);

    // Check for issues
    console.log('\n🔍 ISSUE DETECTION:');

    if (this.debugMessages.length > this.messages.length) {
      console.log('❌ Too many debug messages - could overwhelm chat UI');
    }

    if (this.errors.length > 0) {
      console.log('❌ Errors detected - frontend might show "error UNKNOWN"');
    }

    if (aiMessages.length === 0 && userMessages.length > 0) {
      console.log('❌ No AI responses - backend might not be processing messages correctly');
    }

    // Simulate chat height issues
    const estimatedHeight = (this.messages.length + this.debugMessages.length) * 60;
    if (estimatedHeight > this.chatHeight) {
      console.log(`❌ Chat overflow: ${estimatedHeight}px > ${this.chatHeight}px - height growing instead of scrolling`);
    }

    console.log('');
  }

  showDebugMessages() {
    console.log('\n🐛 DEBUG MESSAGES');
    console.log('=================');

    if (this.debugMessages.length === 0) {
      console.log('No debug messages received');
      return;
    }

    this.debugMessages.slice(-10).forEach((msg, index) => {
      console.log(`${index + 1}. [${msg.timestamp}] ${msg.source}`);
      console.log(`   ${JSON.stringify(msg.content, null, 2).substring(0, 200)}...`);
    });

    if (this.debugMessages.length > 10) {
      console.log(`\n... and ${this.debugMessages.length - 10} more debug messages`);
    }
    console.log('');
  }

  showErrors() {
    console.log('\n❌ ERRORS');
    console.log('=========');

    if (this.errors.length === 0) {
      console.log('No errors detected');
      return;
    }

    this.errors.forEach((error, index) => {
      console.log(`${index + 1}. [${error.timestamp}] ${error.type}`);
      console.log(`   ${typeof error.content === 'string' ? error.content : JSON.stringify(error.content)}`);
    });
    console.log('');
  }

  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalMessages: this.messages.length,
        debugMessages: this.debugMessages.length,
        errors: this.errors.length,
        connected: this.isConnected
      },
      messages: this.messages,
      debugMessages: this.debugMessages,
      errors: this.errors,
      issues: {
        debugSpam: this.debugMessages.length > this.messages.length,
        noAIResponses: this.messages.filter(m => m.type === 'ai').length === 0,
        chatOverflow: (this.messages.length + this.debugMessages.length) * 60 > this.chatHeight,
        hasErrors: this.errors.length > 0
      }
    };

    const filename = join('./logs', `chat-behavior-test-${new Date().toISOString().replace(/[:.]/g, '-')}.json`);
    writeFileSync(filename, JSON.stringify(report, null, 2));
    console.log(`📄 Report saved to: ${filename}`);

    return report;
  }

  quit() {
    console.log('\n🛑 Stopping Chat Behavior Tester...');

    this.showChatAnalysis();
    this.generateReport();

    if (this.ws) {
      this.ws.close();
    }

    console.log('👋 Tester stopped');
    process.exit(0);
  }
}

// Run tester if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const tester = new ChatBehaviorTester();
  tester.start().catch(console.error);
}

export { ChatBehaviorTester };
