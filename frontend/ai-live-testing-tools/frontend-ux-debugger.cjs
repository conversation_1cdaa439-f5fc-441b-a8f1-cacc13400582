#!/usr/bin/env node

/**
 * Frontend UX Debugger - Comprehensive frontend debugging and UX analysis tool
 */

const { chromium } = require('playwright');

class FrontendUXDebugger {
    constructor() {
        this.browser = null;
        this.page = null;
        this.issues = [];
        this.uxMetrics = {};
    }

    async initialize() {
        console.log('🚀 Initializing Frontend UX Debugger...');
        this.browser = await chromium.launch({ headless: false });
        this.page = await this.browser.newPage();
        
        // Set up comprehensive monitoring
        await this.setupMonitoring();
        
        console.log('✅ Frontend UX Debugger initialized');
    }

    async setupMonitoring() {
        // Monitor console messages
        this.page.on('console', msg => {
            const type = msg.type();
            const text = msg.text();
            console.log(`🖥️  [${type.toUpperCase()}] ${text}`);
            
            if (type === 'error') {
                this.issues.push({
                    type: 'console_error',
                    message: text,
                    timestamp: Date.now()
                });
            }
        });

        // Monitor page errors
        this.page.on('pageerror', error => {
            console.log(`🖥️  [PAGE ERROR] ${error.message}`);
            this.issues.push({
                type: 'page_error',
                message: error.message,
                timestamp: Date.now()
            });
        });

        // Monitor network failures
        this.page.on('response', response => {
            if (response.status() >= 400) {
                console.log(`📥 NETWORK ERROR: ${response.status()} ${response.url()}`);
                this.issues.push({
                    type: 'network_error',
                    status: response.status(),
                    url: response.url(),
                    timestamp: Date.now()
                });
            }
        });

        // Set up frontend debugging injection
        await this.page.addInitScript(() => {
            window.frontendDebugger = {
                events: [],
                elements: {},
                websocket: null,
                messagesSent: [],
                messagesReceived: [],
                
                // Track all events
                trackEvent: function(type, data) {
                    this.events.push({
                        type: type,
                        data: data,
                        timestamp: Date.now()
                    });
                    console.log(`🔍 Frontend Event: ${type}`, data);
                },
                
                // Monitor WebSocket
                monitorWebSocket: function() {
                    const originalWebSocket = window.WebSocket;
                    const tracker = this;

                    window.WebSocket = function(url, protocols) {
                        const ws = new originalWebSocket(url, protocols);
                        tracker.websocket = ws;

                        const originalSend = ws.send;
                        ws.send = function(data) {
                            try {
                                const parsed = JSON.parse(data);
                                tracker.messagesSent.push({
                                    type: parsed.type || 'unknown',
                                    data: parsed,
                                    timestamp: Date.now()
                                });
                                tracker.trackEvent('websocket_send', parsed);
                            } catch (e) {
                                tracker.messagesSent.push({
                                    type: 'raw',
                                    data: data,
                                    timestamp: Date.now()
                                });
                            }
                            return originalSend.call(this, data);
                        };

                        ws.addEventListener('message', (event) => {
                            try {
                                const data = JSON.parse(event.data);
                                tracker.messagesReceived.push({
                                    type: data.type,
                                    data: data,
                                    timestamp: Date.now()
                                });
                                tracker.trackEvent('websocket_receive', data);
                            } catch (e) {
                                tracker.messagesReceived.push({
                                    type: 'raw',
                                    data: event.data,
                                    timestamp: Date.now()
                                });
                            }
                        });

                        return ws;
                    };
                },
                
                // Monitor form interactions
                monitorForms: function() {
                    const tracker = this;

                    document.addEventListener('DOMContentLoaded', () => {
                        // Monitor all form elements
                        const forms = document.querySelectorAll('form');
                        forms.forEach((form, index) => {
                            tracker.elements[`form_${index}`] = form;

                            form.addEventListener('submit', (e) => {
                                tracker.trackEvent('form_submit', {
                                    formIndex: index,
                                    prevented: e.defaultPrevented,
                                    target: e.target.tagName
                                });
                            });
                        });

                        // Monitor textarea specifically
                        const textareas = document.querySelectorAll('textarea');
                        textareas.forEach((textarea, index) => {
                            tracker.elements[`textarea_${index}`] = textarea;

                            ['keydown', 'keyup', 'input', 'change'].forEach(eventType => {
                                textarea.addEventListener(eventType, (e) => {
                                    if (eventType === 'keydown' && e.key === 'Enter') {
                                        tracker.trackEvent('textarea_enter', {
                                            textareaIndex: index,
                                            value: textarea.value,
                                            disabled: textarea.disabled,
                                            shiftKey: e.shiftKey,
                                            ctrlKey: e.ctrlKey
                                        });
                                    }
                                });
                            });
                        });

                        // Monitor buttons
                        const buttons = document.querySelectorAll('button');
                        buttons.forEach((button, index) => {
                            tracker.elements[`button_${index}`] = button;

                            button.addEventListener('click', (e) => {
                                tracker.trackEvent('button_click', {
                                    buttonIndex: index,
                                    text: button.textContent,
                                    type: button.type,
                                    disabled: button.disabled
                                });
                            });
                        });
                    });
                }
            };
            
            // Auto-start monitoring
            window.frontendDebugger.monitorWebSocket();
            window.frontendDebugger.monitorForms();
        });
    }

    async loadPage() {
        console.log('🌐 Loading page...');
        await this.page.goto('http://localhost:3000/');
        await this.page.waitForTimeout(3000);
        
        // Check initial state
        const initialState = await this.analyzePageState();
        console.log('📊 Initial page state:', initialState);
        
        return initialState;
    }

    async analyzePageState() {
        return await this.page.evaluate(() => {
            return {
                title: document.title,
                url: window.location.href,
                readyState: document.readyState,
                
                // Check for demo mode
                demoMode: document.body.textContent.includes('Demo Mode'),
                
                // Form elements
                forms: Array.from(document.querySelectorAll('form')).length,
                textareas: Array.from(document.querySelectorAll('textarea')).map(ta => ({
                    disabled: ta.disabled,
                    placeholder: ta.placeholder,
                    value: ta.value
                })),
                buttons: Array.from(document.querySelectorAll('button')).map(btn => ({
                    text: btn.textContent,
                    disabled: btn.disabled,
                    type: btn.type
                })),
                
                // Debug elements
                debugSelects: Array.from(document.querySelectorAll('select')).map(sel => ({
                    options: Array.from(sel.options).map(opt => ({
                        value: opt.value,
                        text: opt.text,
                        selected: opt.selected
                    }))
                })),
                
                // Error indicators
                errors: Array.from(document.querySelectorAll('.error, .alert, [class*="error"]')).map(el => el.textContent),
                
                // WebSocket status
                websocketReady: !!window.frontendDebugger?.websocket
            };
        });
    }

    async testChatFlow() {
        console.log('💬 Testing chat flow...');
        
        // Configure debug mode if available
        try {
            const userSelect = await this.page.locator('select').first();
            if (await userSelect.isVisible({ timeout: 2000 })) {
                await userSelect.selectOption('2');
                console.log('✅ Selected user 2');
                await this.page.waitForTimeout(1000);
            }
        } catch (e) {
            console.log('⚠️  No user select found');
        }
        
        // Wait for WebSocket connection
        await this.page.waitForTimeout(2000);
        
        // Try to send a message
        const testMessage = "I'm restless and need to do things physical. I have 2 hours. Make me a wheel";
        
        // Check if textarea is available and enabled
        const textareaState = await this.page.evaluate(() => {
            const textarea = document.querySelector('textarea');
            return {
                exists: !!textarea,
                disabled: textarea ? textarea.disabled : null,
                visible: textarea ? textarea.offsetParent !== null : null
            };
        });
        
        console.log('📝 Textarea state:', textareaState);
        
        if (!textareaState.exists) {
            this.issues.push({
                type: 'missing_textarea',
                message: 'No textarea found on page',
                timestamp: Date.now()
            });
            return false;
        }
        
        if (textareaState.disabled) {
            this.issues.push({
                type: 'disabled_textarea',
                message: 'Textarea is disabled',
                timestamp: Date.now()
            });
        }
        
        // Try to enable and fill textarea
        await this.page.evaluate(() => {
            const textarea = document.querySelector('textarea');
            if (textarea) {
                textarea.disabled = false;
                textarea.removeAttribute('disabled');
            }
        });
        
        await this.page.fill('textarea', testMessage);
        await this.page.press('textarea', 'Enter');
        
        // Wait and analyze results
        await this.page.waitForTimeout(5000);
        
        const results = await this.page.evaluate(() => {
            return {
                messagesSent: window.frontendDebugger?.messagesSent || [],
                messagesReceived: window.frontendDebugger?.messagesReceived || [],
                events: window.frontendDebugger?.events || []
            };
        });
        
        return results;
    }

    async generateReport() {
        console.log('\n📊 FRONTEND UX ANALYSIS REPORT');
        console.log('════════════════════════════════════════════════════════════');
        
        const pageState = await this.analyzePageState();
        const chatResults = await this.testChatFlow();
        
        console.log('\n🔍 PAGE STATE:');
        console.log(`  Demo Mode: ${pageState.demoMode ? '❌ YES' : '✅ NO'}`);
        console.log(`  Forms: ${pageState.forms}`);
        console.log(`  Textareas: ${pageState.textareas.length}`);
        console.log(`  Buttons: ${pageState.buttons.length}`);
        console.log(`  WebSocket Ready: ${pageState.websocketReady ? '✅ YES' : '❌ NO'}`);
        
        if (pageState.errors.length > 0) {
            console.log('\n❌ ERRORS FOUND:');
            pageState.errors.forEach((error, i) => {
                console.log(`  ${i+1}. ${error}`);
            });
        }
        
        console.log('\n💬 CHAT FLOW ANALYSIS:');
        if (chatResults) {
            console.log(`  Messages Sent: ${chatResults.messagesSent.length}`);
            console.log(`  Messages Received: ${chatResults.messagesReceived.length}`);
            console.log(`  Events Tracked: ${chatResults.events.length}`);
            
            const chatMessagesSent = chatResults.messagesSent.filter(m => m.type === 'chat_message');
            console.log(`  Chat Messages Sent: ${chatMessagesSent.length} ${chatMessagesSent.length === 0 ? '❌' : '✅'}`);
        }
        
        console.log('\n🐛 ISSUES FOUND:');
        if (this.issues.length === 0) {
            console.log('  ✅ No issues detected');
        } else {
            this.issues.forEach((issue, i) => {
                console.log(`  ${i+1}. [${issue.type}] ${issue.message}`);
            });
        }
        
        console.log('\n💡 RECOMMENDATIONS:');
        if (pageState.demoMode) {
            console.log('  🔧 Fix backend connectivity to exit demo mode');
        }
        if (chatResults && chatResults.messagesSent.filter(m => m.type === 'chat_message').length === 0) {
            console.log('  🔧 Fix chat message sending logic in frontend');
        }
        if (pageState.textareas.some(ta => ta.disabled)) {
            console.log('  🔧 Enable textarea for user input');
        }
    }

    async cleanup() {
        if (this.browser) {
            await this.browser.close();
        }
    }
}

async function main() {
    const uxDebugger = new FrontendUXDebugger();

    try {
        await uxDebugger.initialize();
        await uxDebugger.loadPage();
        await uxDebugger.generateReport();
    } catch (error) {
        console.error('❌ Error during debugging:', error);
    } finally {
        await uxDebugger.cleanup();
    }
}

main().catch(console.error);
