#!/usr/bin/env node

/**
 * Test script for main app UI elements
 * Tests the new button bar and activity list functionality
 */

const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

async function testMainAppUI(port = 3002) {
  console.log('🎡 Testing main app UI elements...');

  const browser = await chromium.launch({
    headless: false,
    slowMo: 500
  });

  try {
    const page = await browser.newPage();
    
    // Enable console logging
    page.on('console', msg => {
      console.log(`[BROWSER] ${msg.text()}`);
    });

    // Navigate to main app
    console.log(`📱 Navigating to http://localhost:${port}`);
    await page.goto(`http://localhost:${port}`, { waitUntil: 'networkidle0' });

    // Wait for app to load
    await page.waitForSelector('app-shell', { timeout: 10000 });
    console.log('✅ App shell loaded');

    // Take initial screenshot
    await page.screenshot({ path: 'test-main-app-initial.png', fullPage: true });
    console.log('📸 Initial screenshot taken');

    // Check if we're in debug mode and can see the wheel
    const hasWheel = await page.$('game-wheel');
    if (!hasWheel) {
      console.log('🔄 No wheel visible, trying to load sample data...');
      
      // Try to find and click a load data button or similar
      const loadButton = await page.$('button[data-test="load-sample"]');
      if (loadButton) {
        await loadButton.click();
        await page.waitForTimeout(2000);
      }
    }

    // Look for the button bar
    console.log('🔍 Looking for button bar...');
    const buttonBar = await page.$('.button-bar');
    if (buttonBar) {
      console.log('✅ Button bar found');
      
      // Check for potentiometer controls
      const timeControl = await page.$('.potentiometer-control:first-child');
      const energyControl = await page.$('.potentiometer-control:last-child');
      
      if (timeControl && energyControl) {
        console.log('✅ Both potentiometer controls found');
        
        // Test slider interaction
        const timeSlider = await timeControl.$('input[type="range"]');
        const energySlider = await energyControl.$('input[type="range"]');
        
        if (timeSlider && energySlider) {
          console.log('🎛️ Testing slider interactions...');
          
          // Move time slider
          await timeSlider.click();
          await page.keyboard.press('ArrowRight');
          await page.keyboard.press('ArrowRight');
          await page.keyboard.press('ArrowRight');
          
          // Move energy slider
          await energySlider.click();
          await page.keyboard.press('ArrowLeft');
          await page.keyboard.press('ArrowLeft');
          
          console.log('✅ Slider interactions tested');
        }
      }
    } else {
      console.log('❌ Button bar not found');
    }

    // Look for activity list
    console.log('🔍 Looking for activity list...');
    const activityList = await page.$('.activity-list');
    if (activityList) {
      console.log('✅ Activity list found');
      
      // Check for activity items
      const activityItems = await page.$$('.activity-item');
      console.log(`📋 Found ${activityItems.length} activity items`);
      
      if (activityItems.length > 0) {
        // Test expanding an activity
        console.log('🔄 Testing activity expansion...');
        await activityItems[0].click();
        await page.waitForTimeout(500);
        
        // Check if it expanded
        const expandedItem = await page.$('.activity-item.expanded');
        if (expandedItem) {
          console.log('✅ Activity item expanded successfully');
          
          // Look for change button
          const changeButton = await expandedItem.$('.activity-change-btn');
          if (changeButton) {
            console.log('🔄 Testing change button...');
            await changeButton.click();
            await page.waitForTimeout(500);
            
            // Check if modal opened
            const modal = await page.$('.modal-overlay');
            if (modal) {
              console.log('✅ Activity change modal opened');
              
              // Test search functionality
              const searchInput = await modal.$('.search-input');
              if (searchInput) {
                console.log('🔍 Testing search functionality...');
                await searchInput.type('yoga');
                await page.waitForTimeout(500);
                
                // Check for filtered results
                const catalogItems = await modal.$$('.catalog-item');
                console.log(`📋 Found ${catalogItems.length} catalog items after search`);
              }
              
              // Close modal
              const closeButton = await modal.$('.modal-close');
              if (closeButton) {
                await closeButton.click();
                await page.waitForTimeout(500);
                console.log('✅ Modal closed');
              }
            } else {
              console.log('❌ Activity change modal not found');
            }
          } else {
            console.log('❌ Change button not found');
          }
        } else {
          console.log('❌ Activity item did not expand');
        }
      }
    } else {
      console.log('❌ Activity list not found');
    }

    // Take final screenshot
    await page.screenshot({ path: 'test-main-app-final.png', fullPage: true });
    console.log('📸 Final screenshot taken');

    console.log('✅ Main app UI test completed');

  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

// Run the test
const port = process.argv[2] || 3002;
testMainAppUI(port).catch(console.error);
