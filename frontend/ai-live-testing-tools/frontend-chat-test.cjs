#!/usr/bin/env node

/**
 * Frontend Chat UI Test - Test the actual frontend chat interface
 * 
 * This tool tests:
 * 1. WebSocket connection to /ws/game/ endpoint
 * 2. Sending chat messages and receiving responses
 * 3. Proper message display in frontend
 */

const WebSocket = require('ws');

const config = {
  gameWebSocketUrl: 'ws://localhost:8000/ws/game/',
  testDuration: 60000, // 60 seconds
  messageDelay: 5000 // 5 seconds between messages
};

class FrontendChatTest {
  constructor() {
    this.gameWs = null;
    this.messages = [];
    this.chatMessages = [];
    this.errors = [];
    this.startTime = Date.now();
  }

  async start() {
    console.log('🔍 Frontend Chat UI Test Starting...');
    console.log('💬 Testing chat message flow and display');
    console.log('⏱️  Test duration: 60 seconds\n');

    try {
      await this.connectToGame();
      await this.runChatTest();
      this.generateReport();
    } catch (error) {
      console.error('❌ Test failed:', error.message);
      this.errors.push({
        type: 'TEST_FAILURE',
        message: error.message,
        timestamp: new Date().toISOString()
      });
    } finally {
      this.cleanup();
    }
  }

  async connectToGame() {
    return new Promise((resolve, reject) => {
      console.log('🔌 Connecting to game WebSocket...');
      
      this.gameWs = new WebSocket(config.gameWebSocketUrl);
      
      this.gameWs.on('open', () => {
        console.log('✅ Game WebSocket connected');
        resolve();
      });

      this.gameWs.on('message', (data) => {
        try {
          const message = JSON.parse(data.toString());
          this.handleMessage(message);
        } catch (error) {
          console.error('❌ Failed to parse message:', error.message);
          this.errors.push({
            type: 'MESSAGE_PARSE_ERROR',
            message: error.message,
            rawData: data.toString(),
            timestamp: new Date().toISOString()
          });
        }
      });

      this.gameWs.on('error', (error) => {
        console.error('❌ Game WebSocket error:', error.message);
        this.errors.push({
          type: 'WEBSOCKET_ERROR',
          message: error.message,
          timestamp: new Date().toISOString()
        });
        reject(error);
      });

      this.gameWs.on('close', (code, reason) => {
        console.log(`🔌 Game WebSocket closed: ${code} ${reason}`);
      });

      setTimeout(() => reject(new Error('Connection timeout')), 10000);
    });
  }

  handleMessage(message) {
    this.messages.push({
      message,
      timestamp: new Date().toISOString()
    });

    console.log(`📨 Received: ${message.type}`);

    // Analyze chat messages specifically
    if (message.type === 'chat_message') {
      this.analyzeChatMessage(message);
    }

    // Check for wheel data
    if (message.type === 'wheel_data') {
      console.log(`  🎡 Wheel generated with ${message.wheel?.items?.length || 0} items`);
    }

    // Check for errors
    if (message.type === 'error') {
      console.log(`  ❌ Error: ${message.content || message.message}`);
      this.errors.push({
        type: 'BACKEND_ERROR',
        message: message.content || message.message,
        timestamp: new Date().toISOString()
      });
    }

    // Check for debug info
    if (message.type === 'debug_info') {
      console.log(`  🔧 Debug: ${message.content || 'No content'}`);
    }
  }

  analyzeChatMessage(message) {
    this.chatMessages.push(message);

    // Check message structure
    const issues = [];

    if (!message.content) {
      issues.push('Missing content field');
    } else if (typeof message.content !== 'string') {
      issues.push(`Content is not string: ${typeof message.content}`);
    } else if (message.content.trim() === '') {
      issues.push('Content is empty string');
    }

    if (message.is_user === undefined) {
      issues.push('Missing is_user field');
    }

    if (issues.length > 0) {
      console.log(`  ⚠️  Chat message issues: ${issues.join(', ')}`);
      this.errors.push({
        type: 'CHAT_MESSAGE_ISSUES',
        message: issues.join(', '),
        data: message,
        timestamp: new Date().toISOString()
      });
    } else {
      const sender = message.is_user ? 'User' : 'AI';
      const preview = message.content.substring(0, 50) + (message.content.length > 50 ? '...' : '');
      console.log(`  💬 ${sender}: "${preview}"`);
    }
  }

  async runChatTest() {
    const testMessages = [
      "Hello, I'm testing the chat interface",
      "I'm feeling bored today",
      "I want to do some exercise",
      "Can you generate a wheel for me?",
      "What activities do you recommend?"
    ];

    let messageIndex = 0;

    const sendMessage = () => {
      if (messageIndex < testMessages.length && this.gameWs && this.gameWs.readyState === WebSocket.OPEN) {
        const messageContent = testMessages[messageIndex];
        
        const message = {
          type: 'chat_message',
          content: {
            message: messageContent,
            user_profile_id: '2',
            timestamp: new Date().toISOString()
          }
        };

        console.log(`👤 Sending: "${messageContent}"`);
        this.gameWs.send(JSON.stringify(message));
        messageIndex++;
      }
    };

    // Send first message immediately
    sendMessage();

    // Send subsequent messages at intervals
    const messageInterval = setInterval(() => {
      sendMessage();
      if (messageIndex >= testMessages.length) {
        clearInterval(messageInterval);
      }
    }, config.messageDelay);

    // Wait for test completion
    return new Promise(resolve => {
      setTimeout(() => {
        clearInterval(messageInterval);
        resolve();
      }, config.testDuration);
    });
  }

  generateReport() {
    const duration = Date.now() - this.startTime;
    
    console.log('\n🔍 Frontend Chat UI Test Report');
    console.log('=' .repeat(50));
    console.log(`⏱️  Test duration: ${Math.round(duration / 1000)}s`);
    console.log(`📨 Total messages received: ${this.messages.length}`);
    console.log(`💬 Chat messages: ${this.chatMessages.length}`);
    console.log(`❌ Errors: ${this.errors.length}`);

    // Analyze chat message quality
    const userMessages = this.chatMessages.filter(m => m.is_user);
    const aiMessages = this.chatMessages.filter(m => !m.is_user);
    
    console.log(`\n💬 Chat Message Analysis:`);
    console.log(`  👤 User messages: ${userMessages.length}`);
    console.log(`  🤖 AI messages: ${aiMessages.length}`);

    // Check for empty or problematic messages
    const emptyMessages = this.chatMessages.filter(m => !m.content || m.content.trim() === '');
    const undefinedMessages = this.chatMessages.filter(m => m.content === undefined || m.content === null);
    
    if (emptyMessages.length > 0) {
      console.log(`  ⚠️  Empty messages: ${emptyMessages.length}`);
    }
    
    if (undefinedMessages.length > 0) {
      console.log(`  ⚠️  Undefined content messages: ${undefinedMessages.length}`);
    }

    // Show sample AI responses
    if (aiMessages.length > 0) {
      console.log(`\n🤖 Sample AI Responses:`);
      aiMessages.slice(0, 3).forEach((msg, index) => {
        const preview = msg.content ? msg.content.substring(0, 100) + '...' : 'No content';
        console.log(`  ${index + 1}. "${preview}"`);
      });
    }

    // Show errors if any
    if (this.errors.length > 0) {
      console.log(`\n❌ Errors Encountered:`);
      this.errors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error.type}: ${error.message}`);
      });
    }

    // Overall assessment
    const hasEmptyMessages = emptyMessages.length > 0 || undefinedMessages.length > 0;
    const hasErrors = this.errors.length > 0;
    const hasAIResponses = aiMessages.length > 0;

    console.log(`\n📊 Overall Assessment:`);
    
    if (hasEmptyMessages) {
      console.log(`❌ ISSUE: Chat UI showing empty message bubbles (${emptyMessages.length + undefinedMessages.length} problematic messages)`);
    } else {
      console.log(`✅ Chat messages have proper content`);
    }

    if (hasErrors) {
      console.log(`❌ ISSUE: ${this.errors.length} errors encountered during test`);
    } else {
      console.log(`✅ No errors encountered`);
    }

    if (hasAIResponses) {
      console.log(`✅ AI responses are being received`);
    } else {
      console.log(`❌ ISSUE: No AI responses received`);
    }

    // Save detailed report
    const report = {
      testSummary: {
        duration,
        totalMessages: this.messages.length,
        chatMessages: this.chatMessages.length,
        errors: this.errors.length,
        hasEmptyMessages,
        hasErrors,
        hasAIResponses
      },
      chatMessages: this.chatMessages,
      errors: this.errors,
      allMessages: this.messages
    };

    const fs = require('fs');
    const filename = `logs/frontend-chat-test-${Date.now()}.json`;
    fs.writeFileSync(filename, JSON.stringify(report, null, 2));
    console.log(`\n📄 Detailed report saved: ${filename}`);
  }

  cleanup() {
    if (this.gameWs) {
      this.gameWs.close();
    }
  }
}

// Run the test
if (require.main === module) {
  const test = new FrontendChatTest();
  test.start().catch(console.error);
}

module.exports = FrontendChatTest;
