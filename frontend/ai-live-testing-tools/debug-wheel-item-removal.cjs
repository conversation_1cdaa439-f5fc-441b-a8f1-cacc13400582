/**
 * Debug Wheel Item Removal Issue
 * Deep investigation of the wheel data management issue during item removal
 */

const puppeteer = require('puppeteer');

async function debugWheelItemRemoval() {
  console.log('🔍 Debugging wheel item removal issue...');
  
  const browser = await puppeteer.launch({ 
    headless: false,
    defaultViewport: { width: 1200, height: 800 }
  });
  
  try {
    const page = await browser.newPage();
    
    // Enable console logging
    page.on('console', msg => {
      if (msg.text().includes('REMOVING WHEEL ITEM DEBUG') || 
          msg.text().includes('wheel data') ||
          msg.text().includes('wheelData')) {
        console.log('🔍 BROWSER:', msg.text());
      }
    });
    
    // Navigate to the app
    console.log('📱 Navigating to http://localhost:3000...');
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle0' });
    
    // Wait for app to load
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('\n🎯 Step 1: Check initial state...');
    
    // Check initial wheel state
    const initialState = await page.evaluate(() => {
      const appShell = document.querySelector('app-shell');
      if (appShell && appShell.shadowRoot) {
        const wheel = appShell.shadowRoot.querySelector('game-wheel');
        return {
          hasAppShell: !!appShell,
          hasWheelData: !!(appShell.wheelData && appShell.wheelData.segments),
          wheelDataSegmentCount: appShell.wheelData ? appShell.wheelData.segments.length : 0,
          wheelDataPreview: appShell.wheelData ? appShell.wheelData.segments.slice(0, 2).map(s => ({ id: s.id, text: s.text || s.name, color: s.color })) : null,
          hasWheel: !!wheel,
          wheelClass: wheel ? wheel.className : 'no wheel',
          isUnpopulated: wheel ? wheel.classList.contains('wheel-unpopulated') : false
        };
      }
      return { error: 'No app-shell or shadow root' };
    });
    
    console.log('Initial state:', JSON.stringify(initialState, null, 2));
    
    if (!initialState.hasWheelData) {
      console.log('\n🎡 No wheel data found, generating wheel first...');
      
      // Try to generate a wheel
      const generateButton = await page.evaluate(() => {
        const appShell = document.querySelector('app-shell');
        if (appShell && appShell.shadowRoot) {
          const button = appShell.shadowRoot.querySelector('.generate-button, button[data-testid="generate-wheel"]');
          if (button) {
            button.click();
            return true;
          }
        }
        return false;
      });
      
      if (generateButton) {
        console.log('🔄 Clicked generate button, waiting for wheel generation...');
        
        // Wait for wheel generation to complete
        await page.waitForFunction(() => {
          const appShell = document.querySelector('app-shell');
          return appShell && appShell.wheelData && appShell.wheelData.segments && appShell.wheelData.segments.length > 0;
        }, { timeout: 60000 });
        
        console.log('✅ Wheel generation completed');
      } else {
        console.log('❌ Could not find generate button');
        return;
      }
    }
    
    console.log('\n🎯 Step 2: Check wheel state after generation...');
    
    // Check wheel state after generation
    const afterGenerationState = await page.evaluate(() => {
      const appShell = document.querySelector('app-shell');
      if (appShell && appShell.shadowRoot) {
        const wheel = appShell.shadowRoot.querySelector('game-wheel');
        return {
          hasWheelData: !!(appShell.wheelData && appShell.wheelData.segments),
          wheelDataSegmentCount: appShell.wheelData ? appShell.wheelData.segments.length : 0,
          wheelDataPreview: appShell.wheelData ? appShell.wheelData.segments.slice(0, 3).map(s => ({ 
            id: s.id, 
            text: s.text || s.name, 
            color: s.color,
            wheel_item_id: s.wheel_item_id,
            activity_tailored_id: s.activity_tailored_id
          })) : null,
          wheelId: appShell.wheelData ? appShell.wheelData.wheelId : null,
          isUnpopulated: wheel ? wheel.classList.contains('wheel-unpopulated') : false
        };
      }
      return { error: 'No app-shell or shadow root' };
    });
    
    console.log('After generation state:', JSON.stringify(afterGenerationState, null, 2));
    
    if (!afterGenerationState.hasWheelData || afterGenerationState.wheelDataSegmentCount === 0) {
      console.log('❌ No wheel data after generation, cannot proceed with removal test');
      return;
    }
    
    console.log('\n🎯 Step 3: Find and click remove button...');
    
    // Find and click a remove button
    const removeButtonClicked = await page.evaluate(() => {
      const appShell = document.querySelector('app-shell');
      if (appShell && appShell.shadowRoot) {
        const removeButton = appShell.shadowRoot.querySelector('.remove-activity-btn, .activity-item .remove-btn');
        if (removeButton) {
          console.log('Found remove button, clicking...');
          removeButton.click();
          return true;
        }
      }
      return false;
    });
    
    if (!removeButtonClicked) {
      console.log('❌ Could not find remove button');
      return;
    }
    
    console.log('🔄 Clicked remove button, waiting for removal to complete...');
    
    // Wait a bit for the removal to process
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('\n🎯 Step 4: Check wheel state after removal...');
    
    // Check wheel state after removal
    const afterRemovalState = await page.evaluate(() => {
      const appShell = document.querySelector('app-shell');
      if (appShell && appShell.shadowRoot) {
        const wheel = appShell.shadowRoot.querySelector('game-wheel');
        return {
          hasWheelData: !!(appShell.wheelData && appShell.wheelData.segments),
          wheelDataSegmentCount: appShell.wheelData ? appShell.wheelData.segments.length : 0,
          wheelDataPreview: appShell.wheelData ? appShell.wheelData.segments.slice(0, 3).map(s => ({ 
            id: s.id, 
            text: s.text || s.name, 
            color: s.color,
            wheel_item_id: s.wheel_item_id,
            activity_tailored_id: s.activity_tailored_id
          })) : null,
          wheelId: appShell.wheelData ? appShell.wheelData.wheelId : null,
          isUnpopulated: wheel ? wheel.classList.contains('wheel-unpopulated') : false,
          wheelDataType: appShell.wheelData ? typeof appShell.wheelData : 'undefined',
          wheelDataTruthy: !!appShell.wheelData
        };
      }
      return { error: 'No app-shell or shadow root' };
    });
    
    console.log('After removal state:', JSON.stringify(afterRemovalState, null, 2));
    
    console.log('\n🎯 Step 5: Analysis...');
    
    // Compare states
    const segmentCountBefore = afterGenerationState.wheelDataSegmentCount;
    const segmentCountAfter = afterRemovalState.wheelDataSegmentCount;
    const wheelIdBefore = afterGenerationState.wheelId;
    const wheelIdAfter = afterRemovalState.wheelId;
    
    console.log(`Segment count: ${segmentCountBefore} → ${segmentCountAfter}`);
    console.log(`Wheel ID: ${wheelIdBefore} → ${wheelIdAfter}`);
    console.log(`Unpopulated before: ${afterGenerationState.isUnpopulated}`);
    console.log(`Unpopulated after: ${afterRemovalState.isUnpopulated}`);
    
    if (afterRemovalState.isUnpopulated && !afterGenerationState.isUnpopulated) {
      console.log('🚨 ISSUE DETECTED: Wheel became unpopulated after removal!');
      console.log('This suggests wheelData became falsy, triggering the background wheel display');
    }
    
    if (wheelIdBefore !== wheelIdAfter) {
      console.log('🚨 ISSUE DETECTED: Wheel ID changed after removal!');
      console.log('This suggests a completely different wheel was loaded');
    }
    
    if (segmentCountAfter === 5 && afterRemovalState.wheelDataPreview) {
      const hasBackgroundSegments = afterRemovalState.wheelDataPreview.some(s => 
        s.text.includes('Exercise') || s.text.includes('Learning') || s.text.includes('Creative')
      );
      if (hasBackgroundSegments) {
        console.log('🚨 ISSUE DETECTED: Background wheel data is being displayed!');
        console.log('The getBackgroundWheelData() method is being used instead of the actual wheel data');
      }
    }
    
    // Wait for user to see the browser
    console.log('\n⏳ Browser will stay open for 15 seconds for manual inspection...');
    await new Promise(resolve => setTimeout(resolve, 15000));
    
  } catch (error) {
    console.error('❌ Debug failed:', error);
  } finally {
    await browser.close();
  }
}

// Run the debug
debugWheelItemRemoval().catch(console.error);
