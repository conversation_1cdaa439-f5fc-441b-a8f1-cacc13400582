# Goali Frontend Debug Mode Configuration
# This configuration enables full debugging capabilities for development

# Application Mode
VITE_APP_MODE=debug

# WebSocket URL for backend connection
VITE_WS_URL=ws://localhost:8000/ws/game/

# API Base URL for HTTP requests
VITE_API_BASE_URL=http://localhost:8000

# Default user profile ID (can be changed in debug interface)
VITE_USER_PROFILE_ID=2

# Debug Configuration
VITE_DEBUG_ENABLED=true
VITE_DEBUG_SHOW_PERFORMANCE=true
VITE_DEBUG_SHOW_NETWORK_LOGS=true
VITE_DEBUG_SHOW_STATE_INSPECTOR=true
VITE_DEBUG_ALLOW_USER_SELECTION=true
VITE_DEBUG_ALLOW_LLM_CONFIG_SELECTION=true
VITE_DEBUG_ALLOW_BACKEND_URL_CHANGE=true
VITE_DEBUG_MOCK_DATA_ENABLED=false

# Security (authentication required even in development for backend communication)
VITE_SECURITY_REQUIRE_AUTH=true
VITE_SECURITY_TOKEN_VALIDATION=true
VITE_SECURITY_SESSION_TIMEOUT=3600000

# Wheel Configuration
VITE_WHEEL_CACHE_ENABLED=false
