<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Conversation State Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .pass { background-color: #d4edda; color: #155724; }
        .fail { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .state-display {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🔄 Conversation State Management Test</h1>
    
    <div class="test-section">
        <h2>Current State</h2>
        <div id="currentState" class="state-display">Loading...</div>
        <button onclick="refreshState()">Refresh State</button>
        <button onclick="clearState()">Clear State</button>
    </div>
    
    <div class="test-section">
        <h2>Basic Operations</h2>
        <button onclick="testBasicOperations()">Test Basic Operations</button>
        <div id="basicResults"></div>
    </div>
    
    <div class="test-section">
        <h2>State Validation</h2>
        <button onclick="testStateValidation()">Test State Validation</button>
        <div id="validationResults"></div>
    </div>
    
    <div class="test-section">
        <h2>Phase Transitions</h2>
        <button onclick="testPhaseTransitions()">Test Phase Transitions</button>
        <div id="transitionResults"></div>
    </div>
    
    <div class="test-section">
        <h2>Conversation Scenarios</h2>
        <button onclick="simulateOnboardingFlow()">Simulate Onboarding</button>
        <button onclick="simulateDiscussionFlow()">Simulate Discussion</button>
        <button onclick="simulateWheelFlow()">Simulate Wheel Generation</button>
        <div id="scenarioResults"></div>
    </div>

    <script type="module">
        import { ConversationState } from './src/utils/conversationState.js';
        
        // Make ConversationState available globally for testing
        window.ConversationState = ConversationState;
        
        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = message;
            container.appendChild(div);
        }
        
        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }
        
        window.refreshState = function() {
            const state = ConversationState.get();
            document.getElementById('currentState').textContent = JSON.stringify(state, null, 2);
        };
        
        window.clearState = function() {
            ConversationState.clear();
            refreshState();
            addResult('basicResults', 'State cleared', 'info');
        };
        
        window.testBasicOperations = function() {
            clearResults('basicResults');
            
            try {
                // Test set and get
                const testState = {
                    phase: 'test_phase',
                    awaiting_response_type: 'test_response',
                    context: { test: 'data' }
                };
                
                ConversationState.set(testState);
                const retrieved = ConversationState.get();
                
                if (JSON.stringify(retrieved) === JSON.stringify(testState)) {
                    addResult('basicResults', '✅ Set/Get operations work correctly', 'pass');
                } else {
                    addResult('basicResults', '❌ Set/Get operations failed', 'fail');
                }
                
                // Test update
                ConversationState.update({ phase: 'updated_phase' });
                const updated = ConversationState.get();
                
                if (updated.phase === 'updated_phase' && updated.context.test === 'data') {
                    addResult('basicResults', '✅ Update operation works correctly', 'pass');
                } else {
                    addResult('basicResults', '❌ Update operation failed', 'fail');
                }
                
                // Test field operations
                ConversationState.setField('test_field', 'test_value');
                const fieldValue = ConversationState.getField('test_field');
                
                if (fieldValue === 'test_value') {
                    addResult('basicResults', '✅ Field operations work correctly', 'pass');
                } else {
                    addResult('basicResults', '❌ Field operations failed', 'fail');
                }
                
                // Test helper methods
                ConversationState.setField('phase', 'discussion_active');
                if (ConversationState.isInPhase('discussion_active')) {
                    addResult('basicResults', '✅ Phase checking works correctly', 'pass');
                } else {
                    addResult('basicResults', '❌ Phase checking failed', 'fail');
                }
                
            } catch (error) {
                addResult('basicResults', `❌ Error in basic operations: ${error.message}`, 'fail');
            }
            
            refreshState();
        };
        
        window.testStateValidation = function() {
            clearResults('validationResults');
            
            try {
                // Test invalid phase validation
                const invalidState = {
                    phase: 'invalid_phase',
                    awaiting_response_type: 'invalid_response',
                    context: 'not_an_object'
                };
                
                const validated = ConversationState.validateState(invalidState);
                
                if (validated.phase === 'initial') {
                    addResult('validationResults', '✅ Invalid phase corrected to initial', 'pass');
                } else {
                    addResult('validationResults', '❌ Invalid phase not corrected', 'fail');
                }
                
                if (!validated.awaiting_response_type) {
                    addResult('validationResults', '✅ Invalid awaiting_response_type removed', 'pass');
                } else {
                    addResult('validationResults', '❌ Invalid awaiting_response_type not removed', 'fail');
                }
                
                if (typeof validated.context === 'object' && validated.context !== null) {
                    addResult('validationResults', '✅ Invalid context corrected to object', 'pass');
                } else {
                    addResult('validationResults', '❌ Invalid context not corrected', 'fail');
                }
                
                // Test initialization with validation
                ConversationState.initialize(invalidState);
                const initialized = ConversationState.get();
                
                if (initialized.phase === 'initial') {
                    addResult('validationResults', '✅ Initialize with validation works', 'pass');
                } else {
                    addResult('validationResults', '❌ Initialize with validation failed', 'fail');
                }
                
            } catch (error) {
                addResult('validationResults', `❌ Error in validation test: ${error.message}`, 'fail');
            }
            
            refreshState();
        };
        
        window.testPhaseTransitions = function() {
            clearResults('transitionResults');
            
            try {
                const transitions = [
                    { from: 'initial', to: 'onboarding_in_progress' },
                    { from: 'onboarding_in_progress', to: 'onboarding_complete' },
                    { from: 'onboarding_complete', to: 'initial' },
                    { from: 'initial', to: 'discussion_active' },
                    { from: 'discussion_active', to: 'awaiting_reflection' },
                    { from: 'awaiting_reflection', to: 'discussion_active' },
                    { from: 'discussion_active', to: 'activity_selection' },
                    { from: 'activity_selection', to: 'initial' }
                ];
                
                let allTransitionsValid = true;
                
                for (const transition of transitions) {
                    ConversationState.set({ phase: transition.from });
                    ConversationState.update({ phase: transition.to });
                    const currentPhase = ConversationState.getField('phase');
                    
                    if (currentPhase === transition.to) {
                        addResult('transitionResults', `✅ ${transition.from} → ${transition.to}`, 'pass');
                    } else {
                        addResult('transitionResults', `❌ ${transition.from} → ${transition.to} failed`, 'fail');
                        allTransitionsValid = false;
                    }
                }
                
                if (allTransitionsValid) {
                    addResult('transitionResults', '🎉 All phase transitions work correctly', 'pass');
                }
                
            } catch (error) {
                addResult('transitionResults', `❌ Error in transition test: ${error.message}`, 'fail');
            }
            
            refreshState();
        };
        
        window.simulateOnboardingFlow = function() {
            clearResults('scenarioResults');
            
            try {
                // Simulate onboarding flow
                ConversationState.initialize({
                    phase: 'onboarding_in_progress',
                    awaiting_response_type: 'onboarding_response',
                    context: {
                        completion_percentage: 0.3,
                        next_question_category: 'profile_enrichment'
                    }
                });
                
                addResult('scenarioResults', '📝 Onboarding flow initialized', 'info');
                
                // Simulate progression
                ConversationState.update({
                    context: {
                        ...ConversationState.getContext(),
                        completion_percentage: 0.8
                    }
                });
                
                addResult('scenarioResults', '📈 Onboarding progress updated', 'info');
                
                // Complete onboarding
                ConversationState.update({
                    phase: 'onboarding_complete',
                    awaiting_response_type: null,
                    context: { profile_completion: 1.0 }
                });
                
                addResult('scenarioResults', '✅ Onboarding completed', 'pass');
                
            } catch (error) {
                addResult('scenarioResults', `❌ Error in onboarding simulation: ${error.message}`, 'fail');
            }
            
            refreshState();
        };
        
        window.simulateDiscussionFlow = function() {
            clearResults('scenarioResults');
            
            try {
                // Start discussion
                ConversationState.initialize({
                    phase: 'discussion_active',
                    last_workflow: 'discussion',
                    awaiting_response_type: null
                });
                
                addResult('scenarioResults', '💬 Discussion flow started', 'info');
                
                // Move to reflection
                ConversationState.update({
                    phase: 'awaiting_reflection',
                    awaiting_response_type: 'reflection_answer',
                    context: {
                        guidance_provided: true,
                        last_question: 'What patterns do you notice?'
                    }
                });
                
                addResult('scenarioResults', '🤔 Moved to reflection phase', 'info');
                
                // Return to discussion
                ConversationState.update({
                    phase: 'discussion_active',
                    awaiting_response_type: null
                });
                
                addResult('scenarioResults', '✅ Discussion flow completed', 'pass');
                
            } catch (error) {
                addResult('scenarioResults', `❌ Error in discussion simulation: ${error.message}`, 'fail');
            }
            
            refreshState();
        };
        
        window.simulateWheelFlow = function() {
            clearResults('scenarioResults');
            
            try {
                // Generate wheel
                ConversationState.initialize({
                    phase: 'activity_selection',
                    awaiting_response_type: 'activity_selection',
                    last_workflow: 'wheel_generation',
                    context: {
                        wheel_generated: true,
                        activity_count: 4
                    }
                });
                
                addResult('scenarioResults', '🎯 Wheel generated, awaiting selection', 'info');
                
                // Activity selected
                ConversationState.update({
                    phase: 'initial',
                    awaiting_response_type: null,
                    last_workflow: 'post_activity'
                });
                
                addResult('scenarioResults', '✅ Wheel flow completed', 'pass');
                
            } catch (error) {
                addResult('scenarioResults', `❌ Error in wheel simulation: ${error.message}`, 'fail');
            }
            
            refreshState();
        };
        
        // Initialize display
        refreshState();
    </script>
</body>
</html>
