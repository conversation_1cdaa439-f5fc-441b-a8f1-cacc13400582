<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Wheel Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .wheel-container {
            width: 500px;
            height: 500px;
            border: 2px solid #4ecdc4;
            margin: 20px auto;
            position: relative;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        button {
            background: #4ecdc4;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .log {
            background: #333;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎡 Quick Wheel Component Test</h1>
        
        <div class="wheel-container">
            <wheel-viewport id="viewport">
                <game-wheel id="wheel"></game-wheel>
            </wheel-viewport>
        </div>
        
        <div class="controls">
            <button id="loadBtn">Load Mock Data</button>
            <button id="spinBtn" disabled>Spin Wheel</button>
            <button id="resetBtn" disabled>Reset</button>
        </div>
        
        <div class="log" id="log">Loading components...</div>
    </div>

    <script type="module">
        let wheel = null;
        let viewport = null;
        const log = document.getElementById('log');
        
        function logMessage(msg) {
            const timestamp = new Date().toLocaleTimeString();
            log.textContent += `[${timestamp}] ${msg}\n`;
            log.scrollTop = log.scrollHeight;
            console.log(msg);
        }
        
        async function loadComponents() {
            try {
                logMessage('🔄 Loading wheel component...');
                await import('../src/components/game-wheel/game-wheel.ts');
                logMessage('✅ Wheel component loaded');
                
                logMessage('🔄 Loading viewport component...');
                await import('../src/components/game-wheel/wheel-viewport.ts');
                logMessage('✅ Viewport component loaded');
                
                // Wait for custom elements to be defined
                await customElements.whenDefined('game-wheel');
                await customElements.whenDefined('wheel-viewport');
                logMessage('✅ Custom elements defined');
                
                // Get references
                wheel = document.getElementById('wheel');
                viewport = document.getElementById('viewport');
                
                // Wait a bit for initialization
                setTimeout(() => {
                    logMessage('🔍 Checking component methods...');
                    logMessage(`Wheel setWheelItems: ${typeof wheel.setWheelItems}`);
                    logMessage(`Wheel spin: ${typeof wheel.spin}`);
                    logMessage(`Wheel getWheelState: ${typeof wheel.getWheelState}`);
                    logMessage(`Viewport zoomIn: ${typeof viewport.zoomIn}`);
                    
                    if (typeof wheel.setWheelItems === 'function') {
                        document.getElementById('loadBtn').disabled = false;
                        logMessage('✅ Components ready!');
                    } else {
                        logMessage('❌ Wheel component not properly initialized');
                    }
                }, 1000);
                
            } catch (error) {
                logMessage(`❌ Error loading components: ${error.message}`);
                console.error('Component loading error:', error);
            }
        }
        
        function loadMockData() {
            if (!wheel || typeof wheel.setWheelItems !== 'function') {
                logMessage('❌ Wheel not ready');
                return;
            }
            
            const mockItems = [
                { id: '1', text: '🏃‍♂️ Morning Run', percentage: 25, color: '#FF6B6B' },
                { id: '2', text: '📚 Study Session', percentage: 25, color: '#4ECDC4' },
                { id: '3', text: '🎨 Creative Time', percentage: 25, color: '#45B7D1' },
                { id: '4', text: '🧘‍♀️ Meditation', percentage: 25, color: '#96CEB4' }
            ];
            
            try {
                wheel.setWheelItems(mockItems);
                logMessage('✅ Mock data loaded');
                document.getElementById('spinBtn').disabled = false;
                document.getElementById('resetBtn').disabled = false;
            } catch (error) {
                logMessage(`❌ Error loading mock data: ${error.message}`);
            }
        }
        
        function spinWheel() {
            if (!wheel || typeof wheel.spin !== 'function') {
                logMessage('❌ Cannot spin wheel');
                return;
            }
            
            try {
                wheel.spin();
                logMessage('🎡 Wheel spinning...');
            } catch (error) {
                logMessage(`❌ Error spinning wheel: ${error.message}`);
            }
        }
        
        function resetWheel() {
            if (!wheel || typeof wheel.reset !== 'function') {
                logMessage('❌ Cannot reset wheel');
                return;
            }
            
            try {
                wheel.reset();
                logMessage('🔄 Wheel reset');
            } catch (error) {
                logMessage(`❌ Error resetting wheel: ${error.message}`);
            }
        }
        
        // Event listeners
        document.getElementById('loadBtn').addEventListener('click', loadMockData);
        document.getElementById('spinBtn').addEventListener('click', spinWheel);
        document.getElementById('resetBtn').addEventListener('click', resetWheel);
        
        // Start loading
        loadComponents();
    </script>
</body>
</html>
