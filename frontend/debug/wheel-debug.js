/**
 * Debug page functionality for the wheel component
 * Provides testing interface and mock data generation
 */

import { generateMockWheelItems, getAvailableScenarios, validateWheelItems } from './mock-data-generator.js';

// Current mock wheel items (will be generated dynamically)
let CURRENT_MOCK_ITEMS = [];

class WheelDebugger {
    constructor() {
        this.wheel = null;
        this.viewport = null;
        this.statusDisplay = null;
        this.debugInfo = null;
        this.winnerDisplay = null;
        this.winnerText = null;
        this.isWheelLoaded = false;
        this.currentScenario = 'balanced';
        this.ballTrackingActive = false;
        this.ballTrackingInfo = null;

        this.init();
    }

    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupElements());
        } else {
            this.setupElements();
        }
    }

    setupElements() {
        // Get DOM elements
        this.wheel = document.getElementById('debugWheel');
        this.viewport = document.getElementById('debugViewport');
        this.statusDisplay = document.getElementById('statusDisplay');
        this.debugInfo = document.getElementById('debugInfo');
        this.winnerDisplay = document.getElementById('winnerDisplay');
        this.winnerText = document.getElementById('winnerText');
        this.ballTrackingInfo = document.getElementById('ballTrackingInfo');

        // Verify wheel component is available
        if (!this.wheel) {
            this.log('❌ Wheel element not found in DOM');
            return;
        }

        // Verify viewport component is available
        if (!this.viewport) {
            this.log('❌ Viewport element not found in DOM');
            return;
        }

        // Wait for wheel component to be defined
        this.waitForWheelComponent();

        // Get buttons
        const loadWheelBtn = document.getElementById('loadWheelBtn');
        const spinWheelBtn = document.getElementById('spinWheelBtn');
        const resetWheelBtn = document.getElementById('resetWheelBtn');
        const debugWinnerBtn = document.getElementById('debugWinnerBtn');
        const trackBallBtn = document.getElementById('trackBallBtn');
        const showSegmentsBtn = document.getElementById('showSegmentsBtn');
        const zoomInBtn = document.getElementById('zoomInBtn');
        const zoomOutBtn = document.getElementById('zoomOutBtn');
        const resetViewBtn = document.getElementById('resetViewBtn');
        const runTestsBtn = document.getElementById('runTestsBtn');
        const takeScreenshotBtn = document.getElementById('takeScreenshotBtn');
        const runScreenshotSequenceBtn = document.getElementById('runScreenshotSequenceBtn');
        const scenarioSelect = document.getElementById('scenarioSelect');

        // Setup event listeners
        loadWheelBtn.addEventListener('click', () => this.loadMockWheel());
        spinWheelBtn.addEventListener('click', () => this.spinWheel());
        resetWheelBtn.addEventListener('click', () => this.resetWheel());
        debugWinnerBtn.addEventListener('click', () => this.debugWinnerDetection());
        trackBallBtn.addEventListener('click', () => this.toggleBallTracking());
        showSegmentsBtn.addEventListener('click', () => this.showSegmentInfo());
        zoomInBtn.addEventListener('click', () => this.zoomIn());
        zoomOutBtn.addEventListener('click', () => this.zoomOut());
        resetViewBtn.addEventListener('click', () => this.resetView());
        runTestsBtn.addEventListener('click', () => this.runTests());
        takeScreenshotBtn.addEventListener('click', () => this.takeScreenshot());
        runScreenshotSequenceBtn.addEventListener('click', () => this.runScreenshotSequence());
        scenarioSelect.addEventListener('change', (e) => this.changeScenario(e.target.value));

        // Setup wheel event listeners
        this.setupWheelEventListeners();

        this.log('Debug page initialized. Ready to test wheel component.');
        this.updateDebugInfo();
    }

    async waitForWheelComponent() {
        this.log('⏳ Waiting for wheel component to be defined...');

        // Wait for custom element to be defined
        try {
            await customElements.whenDefined('game-wheel');
            this.log('✅ Wheel component is now defined');

            // Wait a bit more for the component to fully initialize
            setTimeout(() => {
                this.verifyWheelMethods();
            }, 1000);

        } catch (error) {
            this.log(`❌ Error waiting for wheel component: ${error.message}`);
        }
    }

    verifyWheelMethods() {
        if (!this.wheel) {
            this.log('❌ Wheel element not available');
            return;
        }

        this.log('🔍 Verifying wheel component methods...');

        // Check available methods
        const methods = ['setWheelItems', 'spin', 'reset', 'getWheelState'];
        const availableMethods = [];
        const missingMethods = [];

        methods.forEach(method => {
            if (typeof this.wheel[method] === 'function') {
                availableMethods.push(method);
            } else {
                missingMethods.push(method);
            }
        });

        this.log(`✅ Available methods: ${availableMethods.join(', ')}`);
        if (missingMethods.length > 0) {
            this.log(`❌ Missing methods: ${missingMethods.join(', ')}`);
        }

        // Check if component is properly initialized
        if (typeof this.wheel.setWheelItems === 'function') {
            this.log('✅ Wheel component ready for testing!');
        } else {
            this.log('❌ Wheel component not properly initialized');
            this.log('🔧 Attempting to access component properties...');
            console.log('Wheel element:', this.wheel);
            console.log('Wheel prototype:', Object.getPrototypeOf(this.wheel));
            console.log('Wheel constructor:', this.wheel.constructor.name);
        }
    }

    setupWheelEventListeners() {
        if (!this.wheel) return;

        // Listen for wheel events
        this.wheel.addEventListener('wheel-spin-start', (event) => {
            this.log(`🎡 Wheel spin started with force: ${event.detail.force}`);
            this.updateButtons(false, true, true, false);
        });

        this.wheel.addEventListener('wheel-spinning', (event) => {
            const { velocity, wheelRotation, ballAngle } = event.detail;
            this.updateDebugInfo({
                velocity: velocity.toFixed(4),
                wheelRotation: (wheelRotation * 180 / Math.PI).toFixed(1) + '°',
                ballAngle: (ballAngle * 180 / Math.PI).toFixed(1) + '°'
            });
        });

        this.wheel.addEventListener('wheel-spin-complete', (event) => {
            const { winningSegment, finalAngle, duration } = event.detail;
            this.log(`🎉 Wheel spin complete! Winner: ${winningSegment?.text || 'None'}`);
            this.log(`   Final angle: ${(finalAngle * 180 / Math.PI).toFixed(1)}°`);
            this.log(`   Duration: ${(duration / 1000).toFixed(1)}s`);
            
            this.showWinner(winningSegment);
            this.updateButtons(true, false, true, true);
        });

        this.wheel.addEventListener('wheel-result', (event) => {
            const { segment } = event.detail;
            this.log(`🎯 Final result: ${segment.text} (${segment.percentage}%)`);
        });
    }

    loadMockWheel() {
        this.log(`Loading mock wheel items (${this.currentScenario} scenario)...`);

        // Check if wheel component is ready
        if (!this.wheel) {
            this.log('❌ Wheel element not found');
            return;
        }

        if (typeof this.wheel.setWheelItems !== 'function') {
            this.log('❌ Wheel component not ready. setWheelItems method not available.');
            this.log('🔧 Trying alternative approach...');

            // Try using wheelData property directly
            try {
                CURRENT_MOCK_ITEMS = generateMockWheelItems(this.currentScenario);
                validateWheelItems(CURRENT_MOCK_ITEMS);

                // Create wheelData object
                const wheelData = {
                    segments: CURRENT_MOCK_ITEMS.map(item => ({
                        id: item.id,
                        text: item.text,
                        percentage: item.percentage,
                        color: item.color,
                        activityId: item.activityId
                    })),
                    wheelId: `wheel-${Date.now()}`,
                    createdAt: new Date().toISOString()
                };

                // Set wheelData property directly
                this.wheel.wheelData = wheelData;
                // Don't set invisible to false - let the component handle visibility
                this.wheel.requestUpdate();

                this.isWheelLoaded = true;
                this.log('✅ Mock wheel items loaded via wheelData property!');
                this.log(`   Scenario: ${this.currentScenario}`);
                this.log(`   Items: ${CURRENT_MOCK_ITEMS.map(item => item.text).join(', ')}`);

                this.updateButtons(true, true, true, true);
                this.updateDebugInfo();

            } catch (error) {
                this.log(`❌ Alternative approach failed: ${error.message}`);
                console.error('Wheel loading error:', error);
            }
            return;
        }

        try {
            // Generate mock items using the current scenario
            CURRENT_MOCK_ITEMS = generateMockWheelItems(this.currentScenario);

            // Validate the generated items
            validateWheelItems(CURRENT_MOCK_ITEMS);

            // Use the new public API
            this.wheel.setWheelItems(CURRENT_MOCK_ITEMS);
            this.isWheelLoaded = true;

            this.log('✅ Mock wheel items loaded successfully!');
            this.log(`   Scenario: ${this.currentScenario}`);
            this.log(`   Items: ${CURRENT_MOCK_ITEMS.map(item => item.text).join(', ')}`);

            // Wait a moment for wheel initialization to complete
            setTimeout(() => {
                this.updateButtons(true, true, true, true);
                this.updateDebugInfo();
                this.log('🎮 Wheel ready for spinning!');
            }, 500);

        } catch (error) {
            this.log(`❌ Error loading wheel items: ${error.message}`);
            console.error('Wheel loading error:', error);
        }
    }

    spinWheel() {
        if (!this.isWheelLoaded) {
            this.log('❌ Cannot spin: No wheel items loaded');
            return;
        }

        this.log('🎡 Starting wheel spin...');
        this.hideWinner();
        
        try {
            this.wheel.spin();
        } catch (error) {
            this.log(`❌ Error spinning wheel: ${error.message}`);
            console.error('Wheel spin error:', error);
        }
    }

    resetWheel() {
        this.log('🔄 Resetting wheel...');
        
        try {
            this.wheel.reset();
            this.hideWinner();
            this.updateButtons(true, false, true, false);
            this.log('✅ Wheel reset complete');
            
        } catch (error) {
            this.log(`❌ Error resetting wheel: ${error.message}`);
            console.error('Wheel reset error:', error);
        }
    }

    debugWinnerDetection() {
        this.log('🔍 Debugging winner detection logic...');
        
        try {
            const wheelState = this.wheel.getWheelState();
            this.log('📊 Current wheel state:');
            this.log(`   Spinning: ${wheelState.isSpinning}`);
            this.log(`   Segments: ${wheelState.segments.length}`);
            this.log(`   Has Physics: ${wheelState.hasPhysicsEngine}`);
            this.log(`   Has Renderer: ${wheelState.hasRenderer}`);
            
            if (wheelState.segments.length > 0) {
                this.log('🎯 Segment breakdown:');
                wheelState.segments.forEach((segment, index) => {
                    this.log(`   ${index + 1}. ${segment.text} (${segment.percentage}%) - ${segment.color}`);
                });
            }
            
        } catch (error) {
            this.log(`❌ Error debugging winner detection: ${error.message}`);
            console.error('Debug error:', error);
        }
    }

    showWinner(winningSegment) {
        if (winningSegment) {
            this.winnerText.textContent = winningSegment.text;
            this.winnerDisplay.classList.add('visible');
        }
    }

    hideWinner() {
        this.winnerDisplay.classList.remove('visible');
    }

    updateButtons(loadEnabled, spinEnabled, resetEnabled, debugEnabled) {
        document.getElementById('loadWheelBtn').disabled = !loadEnabled;
        document.getElementById('spinWheelBtn').disabled = !spinEnabled;
        document.getElementById('resetWheelBtn').disabled = !resetEnabled;
        document.getElementById('debugWinnerBtn').disabled = !debugEnabled;
        document.getElementById('trackBallBtn').disabled = !debugEnabled;
        document.getElementById('showSegmentsBtn').disabled = !debugEnabled;
    }

    changeScenario(scenario) {
        this.currentScenario = scenario;
        this.log(`🎨 Scenario changed to: ${scenario}`);

        if (this.isWheelLoaded) {
            this.log('🔄 Reloading wheel with new scenario...');
            this.loadMockWheel();
        }
    }

    toggleBallTracking() {
        if (!this.isWheelLoaded) {
            this.log('❌ Cannot track ball: No wheel loaded');
            return;
        }

        this.ballTrackingActive = !this.ballTrackingActive;

        if (this.ballTrackingActive) {
            this.log('🎯 Starting enhanced ball tracking...');
            this.wheel.classList.add('ball-tracking-active');
            this.startEnhancedBallTracking();
        } else {
            this.log('⏹️ Stopping ball tracking');
            this.wheel.classList.remove('ball-tracking-active');
            this.stopEnhancedBallTracking();
        }
    }

    startEnhancedBallTracking() {
        if (this.ballTrackingInterval) {
            clearInterval(this.ballTrackingInterval);
        }

        this.ballTrackingInterval = setInterval(() => {
            if (!this.wheel || !this.isWheelLoaded) return;

            try {
                const wheelState = this.wheel.getWheelState();
                if (wheelState.hasPhysicsEngine) {
                    // Get ball position and other data
                    const ballBody = this.wheel.physicsEngine?.getBallBody();
                    if (ballBody) {
                        const pos = ballBody.position;
                        const velocity = this.wheel.physicsEngine.getBallVelocity();
                        const distance = Math.sqrt(
                            Math.pow(pos.x - this.wheel.wheelConfig.centerX, 2) +
                            Math.pow(pos.y - this.wheel.wheelConfig.centerY, 2)
                        );

                        let trackingInfo = `Ball Tracking:\n`;
                        trackingInfo += `  Position: (${pos.x.toFixed(1)}, ${pos.y.toFixed(1)})\n`;
                        trackingInfo += `  Velocity: ${velocity.magnitude.toFixed(4)}\n`;
                        trackingInfo += `  Distance from center: ${distance.toFixed(1)}\n`;
                        trackingInfo += `  Spinning: ${wheelState.isSpinning ? '✅' : '❌'}\n`;

                        this.ballTrackingInfo.textContent = trackingInfo;
                    }
                }
            } catch (error) {
                this.ballTrackingInfo.textContent = `Tracking error: ${error.message}`;
            }
        }, 100); // Update every 100ms
    }

    stopEnhancedBallTracking() {
        if (this.ballTrackingInterval) {
            clearInterval(this.ballTrackingInterval);
            this.ballTrackingInterval = null;
        }
        this.ballTrackingInfo.textContent = 'Ball tracking stopped';
    }

    showSegmentInfo() {
        if (!this.isWheelLoaded || !CURRENT_MOCK_ITEMS.length) {
            this.log('❌ Cannot show segments: No wheel loaded');
            return;
        }

        this.log('📊 Segment Information:');
        this.log(`   Total segments: ${CURRENT_MOCK_ITEMS.length}`);

        CURRENT_MOCK_ITEMS.forEach((item, index) => {
            this.log(`   ${index + 1}. ${item.text}`);
            this.log(`      Percentage: ${item.percentage}%`);
            this.log(`      Color: ${item.color}`);
            this.log(`      Domain: ${item.domain || 'unknown'}`);
            this.log(`      Energy: ${item.energyLevel ? (item.energyLevel * 100).toFixed(0) + '%' : 'N/A'}`);
            this.log(`      Challenge: ${item.challengeLevel ? (item.challengeLevel * 100).toFixed(0) + '%' : 'N/A'}`);
        });

        // Also show wheel state segments if available
        try {
            const wheelState = this.wheel.getWheelState();
            if (wheelState.segments && wheelState.segments.length > 0) {
                this.log(`📐 Wheel segments (${wheelState.segments.length} total):`);
                wheelState.segments.slice(0, 5).forEach((segment, index) => {
                    this.log(`   ${index + 1}. ${segment.text} - Angles: ${(segment.startAngle * 180 / Math.PI).toFixed(1)}° to ${(segment.endAngle * 180 / Math.PI).toFixed(1)}°`);
                });
                if (wheelState.segments.length > 5) {
                    this.log(`   ... and ${wheelState.segments.length - 5} more segments`);
                }
            }
        } catch (error) {
            this.log(`❌ Error getting wheel segments: ${error.message}`);
        }
    }

    updateDebugInfo(spinData = null) {
        if (!this.wheel) return;

        try {
            const wheelState = this.wheel.getWheelState();
            let info = `Wheel State:\n`;
            info += `  Loaded: ${this.isWheelLoaded}\n`;
            info += `  Spinning: ${wheelState.isSpinning}\n`;
            info += `  Segments: ${wheelState.segments.length}\n`;
            info += `  Physics: ${wheelState.hasPhysicsEngine ? '✅' : '❌'}\n`;
            info += `  Renderer: ${wheelState.hasRenderer ? '✅' : '❌'}\n`;

            // Add viewport state if available
            if (this.viewport && typeof this.viewport.getViewportState === 'function') {
                const viewportState = this.viewport.getViewportState();
                info += `\nViewport State:\n`;
                info += `  Zoom: ${(viewportState.zoom * 100).toFixed(0)}%\n`;
                info += `  Pan: ${viewportState.panX.toFixed(0)}, ${viewportState.panY.toFixed(0)}\n`;
                info += `  Dragging: ${viewportState.isDragging ? '✅' : '❌'}\n`;
            }

            if (spinData) {
                info += `\nSpin Data:\n`;
                info += `  Velocity: ${spinData.velocity}\n`;
                info += `  Wheel Rotation: ${spinData.wheelRotation}\n`;
                info += `  Ball Angle: ${spinData.ballAngle}\n`;
            }

            this.debugInfo.textContent = info;

        } catch (error) {
            this.debugInfo.textContent = `Error getting debug info: ${error.message}`;
        }
    }

    zoomIn() {
        if (this.viewport && typeof this.viewport.zoomIn === 'function') {
            this.viewport.zoomIn();
            this.log('🔍 Zoomed in');
            this.updateDebugInfo();
        } else {
            this.log('❌ Viewport not available for zoom in');
        }
    }

    zoomOut() {
        if (this.viewport && typeof this.viewport.zoomOut === 'function') {
            this.viewport.zoomOut();
            this.log('🔍 Zoomed out');
            this.updateDebugInfo();
        } else {
            this.log('❌ Viewport not available for zoom out');
        }
    }

    resetView() {
        if (this.viewport && typeof this.viewport.resetView === 'function') {
            this.viewport.resetView();
            this.log('🔄 Viewport reset to default view');
            this.updateDebugInfo();
        } else {
            this.log('❌ Viewport not available for reset');
        }
    }

    runTests() {
        this.log('🧪 Running comprehensive test suite...');
        this.log('📝 Check browser console for detailed test results');

        // Run the test suite if available
        if (window.wheelTestSuite && typeof window.wheelTestSuite.runAllTests === 'function') {
            window.wheelTestSuite.runAllTests().then(results => {
                const passed = Object.values(results).filter(r => r).length;
                const total = Object.keys(results).length;
                this.log(`🎯 Test Results: ${passed}/${total} tests passed (${((passed/total)*100).toFixed(1)}%)`);

                if (passed === total) {
                    this.log('🎉 All tests passed! Wheel component is working correctly.');
                } else {
                    this.log('🔧 Some tests failed. Check browser console for details.');
                }
            }).catch(error => {
                this.log(`❌ Test execution failed: ${error.message}`);
            });
        } else {
            this.log('❌ Test suite not available. Make sure test-wheel-fixes.js is loaded.');
        }
    }

    takeScreenshot() {
        this.log('📸 Taking screenshot of current wheel state...');

        if (window.screenshotHelper && typeof window.screenshotHelper.validateAndScreenshot === 'function') {
            window.screenshotHelper.validateAndScreenshot();
            this.log('📸 Screenshot captured! Check Downloads folder.');
        } else {
            this.log('❌ Screenshot helper not available. Make sure screenshot-helper.js is loaded.');
        }
    }

    runScreenshotSequence() {
        this.log('📸 Starting automated screenshot sequence...');
        this.log('📝 Follow the prompts to capture screenshots at each stage');

        if (window.screenshotHelper && typeof window.screenshotHelper.runScreenshotSequence === 'function') {
            window.screenshotHelper.runScreenshotSequence().then(() => {
                this.log('✅ Screenshot sequence completed successfully!');
            }).catch(error => {
                this.log(`❌ Screenshot sequence failed: ${error.message}`);
            });
        } else {
            this.log('❌ Screenshot helper not available. Make sure screenshot-helper.js is loaded.');
        }
    }

    log(message) {
        const timestamp = new Date().toLocaleTimeString();
        const logMessage = `[${timestamp}] ${message}\n`;
        this.statusDisplay.textContent += logMessage;
        this.statusDisplay.scrollTop = this.statusDisplay.scrollHeight;
        console.log(message);
    }
}

// Initialize the debugger when the page loads
new WheelDebugger();
