/**
 * Screenshot Helper for Wheel Component Testing
 * 
 * This script helps capture screenshots at different stages of wheel testing
 * using the browser's built-in screenshot capabilities.
 */

class WheelScreenshotHelper {
    constructor() {
        this.screenshots = [];
        this.currentStage = 0;
        this.stages = [
            { name: 'before_loading', description: 'Before loading activities (greyed out)' },
            { name: 'after_loading', description: 'After loading activities (colorful with ball)' },
            { name: 'during_spin', description: 'During spin (ball falling)' },
            { name: 'winner_detected', description: 'Winner detected (final state)' }
        ];
    }

    /**
     * Captures a screenshot using html2canvas (if available) or prompts user
     */
    async captureScreenshot(stageName, description) {
        console.log(`📸 Capturing screenshot: ${stageName} - ${description}`);
        
        try {
            // Try to use html2canvas if available
            if (typeof html2canvas !== 'undefined') {
                const canvas = await html2canvas(document.body, {
                    width: window.innerWidth,
                    height: window.innerHeight,
                    useCORS: true
                });
                
                // Convert to blob and create download link
                canvas.toBlob((blob) => {
                    const url = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = `wheel_${stageName}_${Date.now()}.png`;
                    link.click();
                    URL.revokeObjectURL(url);
                });
                
                console.log(`✅ Screenshot saved: wheel_${stageName}_${Date.now()}.png`);
            } else {
                // Fallback: Prompt user to take manual screenshot
                this.promptManualScreenshot(stageName, description);
            }
        } catch (error) {
            console.error('❌ Screenshot capture failed:', error);
            this.promptManualScreenshot(stageName, description);
        }
        
        this.screenshots.push({
            stage: stageName,
            description,
            timestamp: new Date().toISOString()
        });
    }

    /**
     * Prompts user to take manual screenshot
     */
    promptManualScreenshot(stageName, description) {
        const message = `📸 MANUAL SCREENSHOT NEEDED\n\nStage: ${stageName}\nDescription: ${description}\n\nPlease take a screenshot now using:\n- Mac: Cmd+Shift+4\n- Windows: Win+Shift+S\n- Browser: Right-click → "Save image as"`;
        
        alert(message);
        console.log(`📸 Manual screenshot prompt for: ${stageName}`);
    }

    /**
     * Runs automated screenshot sequence
     */
    async runScreenshotSequence() {
        console.log('🎡 Starting automated screenshot sequence...');
        
        // Stage 1: Before loading
        await this.captureScreenshot('01_before_loading', 'Greyed out wheel before activities loaded');
        
        // Wait for user to proceed
        await this.waitForUserAction('Click "Load 8 Mock Items" and then press OK');
        
        // Stage 2: After loading
        await this.captureScreenshot('02_after_loading', 'Colorful wheel with activities and visible ball');
        
        // Wait for user to proceed
        await this.waitForUserAction('Click "Spin!" and then press OK immediately');
        
        // Stage 3: During spin
        await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
        await this.captureScreenshot('03_during_spin', 'Wheel spinning with ball movement');
        
        // Wait for spin to complete
        await this.waitForSpinCompletion();
        
        // Stage 4: Winner detected
        await this.captureScreenshot('04_winner_detected', 'Winner detected and highlighted');
        
        console.log('✅ Screenshot sequence completed!');
        this.printSummary();
    }

    /**
     * Waits for user action with prompt
     */
    async waitForUserAction(instruction) {
        return new Promise((resolve) => {
            const proceed = confirm(`${instruction}\n\nPress OK when ready to continue.`);
            if (proceed) {
                resolve();
            } else {
                console.log('Screenshot sequence cancelled by user');
                throw new Error('User cancelled');
            }
        });
    }

    /**
     * Waits for spin to complete
     */
    async waitForSpinCompletion() {
        console.log('⏳ Waiting for spin to complete...');
        
        const wheel = document.getElementById('debugWheel');
        if (!wheel) {
            console.log('❌ Wheel element not found');
            return;
        }
        
        // Wait up to 20 seconds for spin to complete
        for (let i = 0; i < 200; i++) {
            await new Promise(resolve => setTimeout(resolve, 100));
            
            try {
                const state = wheel.getWheelState();
                if (!state.isSpinning) {
                    console.log('✅ Spin completed!');
                    // Wait additional 2 seconds for winner animation
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    return;
                }
            } catch (error) {
                // Continue waiting
            }
        }
        
        console.log('⚠️ Spin completion timeout - proceeding anyway');
    }

    /**
     * Prints summary of captured screenshots
     */
    printSummary() {
        console.log('\n📊 Screenshot Summary:');
        console.log('======================');
        
        this.screenshots.forEach((screenshot, index) => {
            console.log(`${index + 1}. ${screenshot.stage}: ${screenshot.description}`);
            console.log(`   Captured at: ${screenshot.timestamp}`);
        });
        
        console.log(`\nTotal screenshots: ${this.screenshots.length}`);
        console.log('\n📁 Screenshots should be saved to your Downloads folder');
        console.log('📝 Use these screenshots to validate wheel component functionality');
    }

    /**
     * Quick screenshot of current state
     */
    async quickScreenshot(customName = null) {
        const name = customName || `quick_${Date.now()}`;
        await this.captureScreenshot(name, 'Quick screenshot of current state');
    }

    /**
     * Validates current wheel state and suggests screenshot
     */
    validateAndScreenshot() {
        const wheel = document.getElementById('debugWheel');
        if (!wheel) {
            console.log('❌ Wheel not found');
            return;
        }

        try {
            const state = wheel.getWheelState();
            
            if (state.segments.length === 0) {
                console.log('📸 Suggested: Take screenshot of empty wheel state');
                this.quickScreenshot('empty_wheel');
            } else if (state.isSpinning) {
                console.log('📸 Suggested: Take screenshot of spinning wheel');
                this.quickScreenshot('spinning_wheel');
            } else {
                console.log('📸 Suggested: Take screenshot of loaded wheel');
                this.quickScreenshot('loaded_wheel');
            }
        } catch (error) {
            console.log('❌ Could not validate wheel state:', error);
        }
    }
}

// Create global instance
window.screenshotHelper = new WheelScreenshotHelper();

// Add convenient global functions
window.takeScreenshot = (name) => window.screenshotHelper.quickScreenshot(name);
window.runScreenshotSequence = () => window.screenshotHelper.runScreenshotSequence();
window.validateAndScreenshot = () => window.screenshotHelper.validateAndScreenshot();

console.log('📸 Screenshot Helper loaded!');
console.log('Available commands:');
console.log('  - takeScreenshot("name") - Quick screenshot');
console.log('  - runScreenshotSequence() - Full automated sequence');
console.log('  - validateAndScreenshot() - Smart screenshot based on state');

// Auto-detect if html2canvas is available
if (typeof html2canvas === 'undefined') {
    console.log('💡 For automatic screenshots, include html2canvas:');
    console.log('   <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>');
}
