# 🎡 Wheel Component Testing Guide

## Quick Visual Validation Steps

### 1. **Before Loading Activities** 📸
- Navigate to: `http://localhost:3005/wheel-debug.html`
- **Expected**: Greyed out wheel, no ball visible
- **Screenshot**: Take screenshot showing waiting state

### 2. **After Loading Activities** 📸
- Click "Load 8 Mock Items" button
- **Expected**: 
  - Colorful wheel segments appear
  - **🎱 RED BALL should be visible at TOP of wheel**
  - Ball should be bright red with white highlight
- **Screenshot**: Take screenshot showing colorful wheel with visible ball

### 3. **During Spin** 📸
- Click "Spin!" button
- **Expected**:
  - Ball should immediately start falling due to gravity
  - Wheel should start rotating
  - Console should show physics logs
- **Screenshot**: Take screenshot during spin showing ball movement

### 4. **Winner Detection** 📸
- Wait for spin to complete (up to 15 seconds)
- **Expected**:
  - Ball settles in a segment
  - 1-second delay after settling
  - Winner announcement appears
  - Winning segment highlighted in gold
- **Screenshot**: Take screenshot showing final winner

## Console Testing Commands

Open browser console and run these commands:

### Quick Test Suite
```javascript
// Run all automated tests
window.wheelTestSuite.runAllTests()
```

### Individual Tests
```javascript
// Test component loading
window.wheelTestSuite.testComponentLoading()

// Test ball movement specifically
window.wheelTestSuite.testBallMovement()

// Test viewport controls
window.wheelTestSuite.testViewportControls()
```

### Manual Ball Position Check
```javascript
const wheel = document.getElementById('debugWheel');
const ballBody = wheel.physicsEngine.getBallBody();
console.log('Ball position:', ballBody.position);
console.log('Ball velocity:', wheel.physicsEngine.getBallVelocity());
```

## Expected Console Output

### After Loading Mock Data
```
[WHEEL] 🎱 Rendering initial ball position at: (250, 130)
[RENDERER] 🎱 Rendering ball at: (250.0, 130.0)
```

### During Spin
```
[WHEEL] Starting spin with force: 0.15
[PHYSICS] GRAVITY ENABLED - ball will now fall as wheel spins
[PHYSICS] Ball position before release: (250.0, 130.0)
[PHYSICS] Ball released to fall STRAIGHT DOWN
```

### Animation Loop
```
[PHYSICS] Ball: (250.0, 145.2) Speed: 0.156 Distance: 105.2 WheelVel: 0.142
[RENDERER] 🎱 Rendering ball at: (250.0, 145.2)
```

## Viewport Testing

### Zoom Controls
- Click "Zoom In (+)" - wheel should get larger
- Click "Zoom Out (-)" - wheel should get smaller  
- Click "Reset View" - wheel should return to normal size

### Mouse Controls
- **Mouse wheel**: Zoom in/out towards cursor position
- **Drag**: Pan the wheel around
- **Debug info**: Should show current zoom and pan values

## Troubleshooting

### Ball Not Visible
1. Check console for ball rendering logs
2. Verify physics engine is running
3. Check if ball is rendered outside viewport

### Ball Not Moving
1. Verify gravity is enabled in console
2. Check physics animation loop is running
3. Look for physics update logs

### Winner Detection Issues
1. Wait full 1 second after settling
2. Check collision detection logs
3. Verify nail positions are correct

## Performance Expectations

- **Ball visibility**: Immediate after loading
- **Spin start**: Instant response to button click
- **Ball movement**: Visible within 100ms of spin start
- **Spin duration**: 8-12 seconds typical
- **Winner detection**: 1 second after settling
- **Total time**: ~15 seconds max per spin

## Success Criteria

✅ **Ball Visibility**: Bright red ball visible at top of wheel after loading
✅ **Ball Movement**: Ball falls immediately when spin starts
✅ **Physics**: Realistic gravity and collision behavior
✅ **Winner Detection**: Accurate detection with 1-second delay
✅ **Viewport**: Smooth zoom and pan functionality
✅ **Visual Effects**: Winner highlighting and animations

## Advanced Testing

### Stress Test
```javascript
// Run multiple spins in sequence
for (let i = 0; i < 5; i++) {
  setTimeout(() => {
    document.getElementById('spinWheelBtn').click();
  }, i * 20000); // 20 seconds apart
}
```

### Ball Tracking
```javascript
// Enable detailed ball tracking
const wheel = document.getElementById('debugWheel');
wheel.startBallTracking();
```

### Debug Information
```javascript
// Get comprehensive wheel state
const wheel = document.getElementById('debugWheel');
console.log('Wheel State:', wheel.getWheelState());

// Get viewport state
const viewport = document.getElementById('debugViewport');
console.log('Viewport State:', viewport.getViewportState());
```

## Known Issues Fixed

- ✅ Import path resolution for wheel components
- ✅ Ball visibility and rendering
- ✅ Physics animation loop integration
- ✅ Winner detection timing (1-second delay)
- ✅ Viewport zoom and pan functionality

## Next Steps

After validating these fixes:
1. Implement 4x zoom on ball during final settling
2. Add compelling winner animation with color effects
3. Enhance visual feedback during spin
4. Add sound effects for better UX
