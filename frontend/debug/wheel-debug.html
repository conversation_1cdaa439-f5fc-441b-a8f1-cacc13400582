<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wheel Component Debug Page</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            color: white;
        }

        .header {
            padding: 20px;
            text-align: center;
            background: rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .main-container {
            flex: 1;
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 20px;
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
            width: 100%;
        }

        .wheel-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            flex-direction: column;
        }

        .wheel-container {
            flex: 1;
            min-height: 600px;
            height: 600px; /* Fixed height for consistency */
            position: relative;
            border-radius: 15px;
            overflow: hidden;
            background: rgba(0, 0, 0, 0.3);
            /* Ensure proper aspect ratio for wheel */
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid rgba(255, 255, 255, 0.2); /* Add border for visibility */
        }

        .wheel-container wheel-viewport {
            width: 100%;
            height: 100%;
            border-radius: 15px;
        }

        .wheel-container wheel-viewport game-wheel {
            width: 500px;
            height: 500px;
            aspect-ratio: 1;
            border-radius: 50%;
        }

        .controls-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .control-group {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 12px;
            padding: 16px;
        }

        .control-group h3 {
            margin-bottom: 12px;
            color: #4ecdc4;
            font-size: 1.1rem;
        }

        .button {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-bottom: 8px;
        }

        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        .button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .button.secondary {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .status-display {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 12px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        .debug-info {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 12px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 150px;
            overflow-y: auto;
        }

        .winner-display {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            font-weight: bold;
            font-size: 1.2rem;
            margin-top: 10px;
            display: none;
        }

        .winner-display.visible {
            display: block;
            animation: winnerPulse 0.5s ease-in-out;
        }

        @keyframes winnerPulse {
            0% { transform: scale(0.9); opacity: 0; }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); opacity: 1; }
        }

        .scenario-select {
            width: 100%;
            padding: 8px 12px;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            background: rgba(0, 0, 0, 0.3);
            color: white;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .scenario-select:focus {
            outline: none;
            border-color: #4ecdc4;
            box-shadow: 0 0 0 2px rgba(78, 205, 196, 0.3);
        }

        .ball-tracking-active {
            border: 2px solid #4ecdc4 !important;
            box-shadow: 0 0 10px rgba(78, 205, 196, 0.5) !important;
        }

        @media (max-width: 1024px) {
            .main-container {
                grid-template-columns: 1fr;
                grid-template-rows: 1fr auto;
            }

            .controls-section {
                max-height: 300px;
                overflow-y: auto;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎡 Wheel Component Debugger</h1>
        <p>Test and debug the game wheel component with mocked data</p>
    </div>

    <div class="main-container">
        <div class="wheel-section">
            <h2 style="margin-bottom: 16px; color: #4ecdc4;">Wheel Display (with Viewport)</h2>
            <div class="wheel-container">
                <wheel-viewport id="debugViewport" constrain-to-wheel-size wheel-radius="200">
                    <game-wheel id="debugWheel"></game-wheel>
                </wheel-viewport>
            </div>
            <div class="winner-display" id="winnerDisplay">
                🎉 Winner: <span id="winnerText">None</span>
            </div>
        </div>

        <div class="controls-section">
            <div class="control-group">
                <h3>🎮 Wheel Controls</h3>
                <button class="button" id="loadWheelBtn">Load 8 Mock Items</button>
                <button class="button" id="spinWheelBtn" disabled>Spin Wheel!</button>
                <button class="button secondary" id="resetWheelBtn">Reset Wheel</button>
            </div>

            <div class="control-group">
                <h3>📊 Status</h3>
                <div class="status-display" id="statusDisplay">Ready to load wheel items...</div>
            </div>

            <div class="control-group">
                <h3>🔍 Debug Info</h3>
                <div class="debug-info" id="debugInfo">Wheel state will appear here...</div>
            </div>

            <div class="control-group">
                <h3>🎯 Winner Detection</h3>
                <button class="button secondary" id="debugWinnerBtn" disabled>Debug Winner Logic</button>
                <button class="button secondary" id="trackBallBtn" disabled>Track Ball Movement</button>
                <button class="button secondary" id="showSegmentsBtn" disabled>Show Segment Info</button>
                <div style="font-size: 12px; margin-top: 8px; opacity: 0.8;">
                    Advanced debugging tools for winner detection analysis
                </div>
            </div>

            <div class="control-group">
                <h3>🔍 Viewport Controls</h3>
                <button class="button secondary" id="zoomInBtn">Zoom In (+)</button>
                <button class="button secondary" id="zoomOutBtn">Zoom Out (-)</button>
                <button class="button secondary" id="resetViewBtn">Reset View</button>
                <div style="font-size: 12px; margin-top: 8px; opacity: 0.8;">
                    Use mouse wheel to zoom, drag to pan, or use these buttons
                </div>
            </div>

            <div class="control-group">
                <h3>🧪 Testing</h3>
                <button class="button secondary" id="runTestsBtn">Run All Tests</button>
                <button class="button secondary" id="takeScreenshotBtn">Take Screenshot</button>
                <button class="button secondary" id="runScreenshotSequenceBtn">Screenshot Sequence</button>
                <div style="font-size: 12px; margin-top: 8px; opacity: 0.8;">
                    Validate all fixes and functionality (check console for results)
                </div>
            </div>

            <div class="control-group">
                <h3>📊 Ball Tracking</h3>
                <div class="debug-info" id="ballTrackingInfo">Ball tracking data will appear here...</div>
            </div>

            <div class="control-group">
                <h3>🎨 Scenario Selection</h3>
                <select id="scenarioSelect" class="scenario-select">
                    <option value="balanced">Balanced Life Activities</option>
                    <option value="high_energy">High Energy Activities</option>
                    <option value="relaxed">Relaxed & Mindful Activities</option>
                    <option value="student_focused">Student Life Activities</option>
                </select>
                <div style="font-size: 12px; margin-top: 8px; opacity: 0.8;">
                    Choose different activity scenarios for testing
                </div>
            </div>
        </div>
    </div>

    <!-- Load the wheel component and viewport -->
    <script type="module">
        try {
            // Import and register the wheel component using relative path from debug directory
            await import('../src/components/game-wheel/game-wheel.ts');
            console.log('✅ Wheel component imported successfully');
            console.log('✅ Custom element defined:', !!customElements.get('game-wheel'));

            // Import and register the viewport component
            await import('../src/components/game-wheel/wheel-viewport.ts');
            console.log('✅ Viewport component imported successfully');
            console.log('✅ Custom element defined:', !!customElements.get('wheel-viewport'));
        } catch (error) {
            console.error('❌ Failed to import wheel components:', error);
            console.error('Trying alternative import paths...');
            try {
                // Try absolute path from root
                await import('/src/components/game-wheel/game-wheel.ts');
                await import('/src/components/game-wheel/wheel-viewport.ts');
                console.log('✅ Components imported with absolute path');
            } catch (error2) {
                console.error('❌ Absolute path also failed:', error2);
                try {
                    // Try alias path
                    await import('@/components/game-wheel/game-wheel.ts');
                    await import('@/components/game-wheel/wheel-viewport.ts');
                    console.log('✅ Components imported with alias path');
                } catch (error3) {
                    console.error('❌ All import attempts failed:', error3);
                    // Show error message to user
                    document.body.innerHTML = `
                        <div style="padding: 20px; background: #ff4444; color: white; text-align: center;">
                            <h2>❌ Failed to load wheel components</h2>
                            <p>Please ensure the development server is running with proper module resolution.</p>
                            <p>Try running: <code>npm run dev</code> from the frontend directory</p>
                        </div>
                    `;
                }
            }
        }
    </script>
    <script type="module" src="./wheel-debug.js"></script>
    <script type="module" src="./test-wheel-fixes.js"></script>
    <script type="module" src="./screenshot-helper.js"></script>
</body>
</html>
