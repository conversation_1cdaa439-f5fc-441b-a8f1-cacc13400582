import { defineConfig } from 'vite';
import { resolve } from 'path';

export default defineConfig({
  // Base configuration for debug server
  base: './',
  
  // Root directory for debug server
  root: resolve(__dirname, '.'),
  
  // Build configuration (if needed)
  build: {
    target: 'es2022',
    outDir: 'dist-debug',
    sourcemap: true,
    minify: false, // Keep unminified for debugging
    rollupOptions: {
      input: {
        debug: resolve(__dirname, 'wheel-debug.html'),
      },
    },
  },

  // Development server configuration
  server: {
    port: 3004, // Different port from main app (3000) and preview (3001)
    host: true,
    open: '/wheel-debug.html',
    cors: true,
    strictPort: false, // Try next available port if this one is in use
  },

  // Preview server (for built version)
  preview: {
    port: 3003,
    host: true,
    strictPort: true,
  },

  // Path resolution - point to parent src directory
  resolve: {
    alias: {
      '@': resolve(__dirname, '../src'),
      '@/components': resolve(__dirname, '../src/components'),
      '@/services': resolve(__dirname, '../src/services'),
      '@/types': resolve(__dirname, '../src/types'),
      '@/utils': resolve(__dirname, '../src/utils'),
      '@/styles': resolve(__dirname, '../src/styles'),
      // Add explicit path for wheel component
      'game-wheel': resolve(__dirname, '../src/components/game-wheel/game-wheel.ts'),
      // Add src alias for absolute imports
      '/src': resolve(__dirname, '../src'),
    },
  },

  // Optimizations - include the same dependencies as main app
  optimizeDeps: {
    include: ['lit', 'pixi.js', 'matter-js'],
    exclude: [],
  },

  // CSS configuration
  css: {
    devSourcemap: true,
  },

  // Define global constants
  define: {
    __DEV__: JSON.stringify(true),
    __DEBUG__: JSON.stringify(true),
    __VERSION__: JSON.stringify('debug-1.0.0'),
  },

  // Plugin configuration (minimal for debug)
  plugins: [],

  // Worker configuration
  worker: {
    format: 'es',
  },

  // Enable detailed logging for debugging
  logLevel: 'info',
  
  // Clear screen on rebuild
  clearScreen: true,
});
