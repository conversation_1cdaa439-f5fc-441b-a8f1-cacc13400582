<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Wheel Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .wheel-container {
            width: 500px;
            height: 500px;
            border: 2px solid #4ecdc4;
            margin: 20px auto;
            position: relative;
            background: #333;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        button {
            background: #4ecdc4;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .log {
            background: #333;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎡 Simple Wheel Test</h1>
        
        <div class="wheel-container">
            <game-wheel id="wheel"></game-wheel>
        </div>
        
        <div class="controls">
            <button id="loadBtn">Load Simple Data</button>
            <button id="spinBtn" disabled>Spin Wheel</button>
            <button id="resetBtn" disabled>Reset</button>
            <button id="debugBtn">Debug State</button>
        </div>
        
        <div class="log" id="log">Loading components...</div>
    </div>

    <script type="module">
        let wheel = null;
        const log = document.getElementById('log');
        
        function logMessage(msg) {
            const timestamp = new Date().toLocaleTimeString();
            log.textContent += `[${timestamp}] ${msg}\n`;
            log.scrollTop = log.scrollHeight;
            console.log(msg);
        }
        
        async function loadComponents() {
            try {
                logMessage('🔄 Loading wheel component...');
                await import('../src/components/game-wheel/game-wheel.ts');
                logMessage('✅ Wheel component loaded');
                
                // Wait for custom elements to be defined
                await customElements.whenDefined('game-wheel');
                logMessage('✅ Custom element defined');
                
                // Get references
                wheel = document.getElementById('wheel');
                
                // Wait a bit for initialization
                setTimeout(() => {
                    logMessage('🔍 Checking component methods...');
                    logMessage(`Wheel setWheelItems: ${typeof wheel.setWheelItems}`);
                    logMessage(`Wheel spin: ${typeof wheel.spin}`);
                    logMessage(`Wheel getWheelState: ${typeof wheel.getWheelState}`);
                    
                    if (typeof wheel.setWheelItems === 'function') {
                        document.getElementById('loadBtn').disabled = false;
                        logMessage('✅ Components ready!');
                    } else {
                        logMessage('❌ Wheel component not properly initialized');
                    }
                }, 1000);
                
            } catch (error) {
                logMessage(`❌ Error loading components: ${error.message}`);
                console.error('Component loading error:', error);
            }
        }
        
        function loadSimpleData() {
            if (!wheel || typeof wheel.setWheelItems !== 'function') {
                logMessage('❌ Wheel not ready');
                return;
            }
            
            const simpleItems = [
                { id: '1', text: 'Red', percentage: 25, color: '#FF0000' },
                { id: '2', text: 'Green', percentage: 25, color: '#00FF00' },
                { id: '3', text: 'Blue', percentage: 25, color: '#0000FF' },
                { id: '4', text: 'Yellow', percentage: 25, color: '#FFFF00' }
            ];
            
            try {
                logMessage('🔄 Loading simple data...');
                logMessage(`Data: ${JSON.stringify(simpleItems, null, 2)}`);
                
                wheel.setWheelItems(simpleItems);
                logMessage('✅ Simple data loaded');
                
                // Check wheel state
                setTimeout(() => {
                    const state = wheel.getWheelState();
                    logMessage(`Wheel state: segments=${state.segments.length}, physics=${state.hasPhysicsEngine}, renderer=${state.hasRenderer}`);
                    
                    if (state.segments.length > 0) {
                        logMessage('✅ Segments created successfully');
                        document.getElementById('spinBtn').disabled = false;
                        document.getElementById('resetBtn').disabled = false;
                        
                        // Log segment details
                        state.segments.forEach((seg, i) => {
                            logMessage(`Segment ${i}: ${seg.text}, color=${seg.color}, angles=${(seg.startAngle * 180 / Math.PI).toFixed(1)}° to ${(seg.endAngle * 180 / Math.PI).toFixed(1)}°`);
                        });
                    } else {
                        logMessage('❌ No segments created');
                    }
                }, 500);
                
            } catch (error) {
                logMessage(`❌ Error loading simple data: ${error.message}`);
                console.error('Data loading error:', error);
            }
        }
        
        function spinWheel() {
            if (!wheel || typeof wheel.spin !== 'function') {
                logMessage('❌ Cannot spin wheel');
                return;
            }
            
            try {
                logMessage('🎡 Spinning wheel...');
                wheel.spin();
            } catch (error) {
                logMessage(`❌ Error spinning wheel: ${error.message}`);
                console.error('Spin error:', error);
            }
        }
        
        function resetWheel() {
            if (!wheel || typeof wheel.reset !== 'function') {
                logMessage('❌ Cannot reset wheel');
                return;
            }
            
            try {
                logMessage('🔄 Resetting wheel...');
                wheel.reset();
            } catch (error) {
                logMessage(`❌ Error resetting wheel: ${error.message}`);
                console.error('Reset error:', error);
            }
        }
        
        function debugState() {
            if (!wheel) {
                logMessage('❌ No wheel available');
                return;
            }
            
            try {
                const state = wheel.getWheelState();
                logMessage('🔍 Debug State:');
                logMessage(`  Segments: ${state.segments.length}`);
                logMessage(`  Physics Engine: ${state.hasPhysicsEngine}`);
                logMessage(`  Renderer: ${state.hasRenderer}`);
                logMessage(`  Is Spinning: ${state.isSpinning}`);
                logMessage(`  Error: ${state.errorMessage || 'None'}`);
                
                // Check if wheel has renderer and physics
                if (wheel.renderer) {
                    const rendererState = wheel.renderer.getRendererState();
                    logMessage(`  Renderer initialized: ${rendererState.isInitialized}`);
                }
                
                if (wheel.physicsEngine) {
                    logMessage(`  Physics running: ${wheel.physicsEngine.isRunning || 'Unknown'}`);
                }
                
            } catch (error) {
                logMessage(`❌ Error getting debug state: ${error.message}`);
                console.error('Debug error:', error);
            }
        }
        
        // Event listeners
        document.getElementById('loadBtn').addEventListener('click', loadSimpleData);
        document.getElementById('spinBtn').addEventListener('click', spinWheel);
        document.getElementById('resetBtn').addEventListener('click', resetWheel);
        document.getElementById('debugBtn').addEventListener('click', debugState);
        
        // Start loading
        loadComponents();
    </script>
</body>
</html>
