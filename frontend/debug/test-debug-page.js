/**
 * Test script for the wheel debug page
 * Uses <PERSON><PERSON> to validate functionality
 */

import { chromium } from 'playwright';

async function testWheelDebugPage() {
    console.log('🧪 Starting Wheel Debug Page Test...');
    
    const browser = await chromium.launch({ 
        headless: false, // Show browser for visual debugging
        slowMo: 1000 // Slow down for observation
    });
    
    const context = await browser.newContext();
    const page = await context.newPage();
    
    try {
        // Navigate to debug page
        console.log('📍 Navigating to debug page...');
        await page.goto('http://localhost:3004/wheel-debug.html');
        
        // Wait for page to load
        await page.waitForLoadState('networkidle');
        console.log('✅ Page loaded successfully');
        
        // Check if wheel component is present
        const wheelElement = await page.locator('#debugWheel').first();
        await wheelElement.waitFor({ state: 'attached' });
        console.log('✅ Wheel component found');
        
        // Check if it starts invisible (waiting state)
        const isInvisible = await wheelElement.getAttribute('invisible');
        console.log(`✅ Wheel invisible state: ${isInvisible !== null}`);
        
        // Test loading mock data
        console.log('🎮 Testing mock data loading...');
        const loadButton = page.locator('#loadWheelBtn');
        await loadButton.click();
        
        // Wait for wheel to become visible
        await page.waitForTimeout(2000);
        
        // Check if wheel is now visible
        const isStillInvisible = await wheelElement.getAttribute('invisible');
        console.log(`✅ Wheel visible after loading: ${isStillInvisible === null}`);
        
        // Check if spin button is enabled
        const spinButton = page.locator('#spinWheelBtn');
        const isSpinEnabled = !(await spinButton.isDisabled());
        console.log(`✅ Spin button enabled: ${isSpinEnabled}`);
        
        // Test spinning the wheel
        if (isSpinEnabled) {
            console.log('🎡 Testing wheel spin...');
            await spinButton.click();
            
            // Wait for spin to complete (max 15 seconds)
            await page.waitForTimeout(15000);
            
            // Check for winner display
            const winnerDisplay = page.locator('#winnerDisplay');
            const isWinnerVisible = await winnerDisplay.isVisible();
            console.log(`✅ Winner displayed: ${isWinnerVisible}`);
            
            if (isWinnerVisible) {
                const winnerText = await page.locator('#winnerText').textContent();
                console.log(`🎉 Winner: ${winnerText}`);
            }
        }
        
        // Test debug info
        console.log('🔍 Testing debug information...');
        const debugInfo = await page.locator('#debugInfo').textContent();
        console.log(`✅ Debug info populated: ${debugInfo.length > 0}`);
        
        // Test status log
        const statusDisplay = await page.locator('#statusDisplay').textContent();
        console.log(`✅ Status log populated: ${statusDisplay.length > 0}`);
        
        // Test reset functionality
        console.log('🔄 Testing reset functionality...');
        const resetButton = page.locator('#resetWheelBtn');
        await resetButton.click();
        await page.waitForTimeout(1000);
        
        console.log('✅ All tests completed successfully!');
        
        // Keep browser open for manual inspection
        console.log('🔍 Browser will remain open for manual inspection...');
        console.log('   - Check that the wheel displays correctly');
        console.log('   - Verify that spinning works smoothly');
        console.log('   - Test winner detection accuracy');
        console.log('   - Close browser when done');
        
        // Wait for manual inspection
        await page.waitForTimeout(60000); // Wait 1 minute
        
    } catch (error) {
        console.error('❌ Test failed:', error);
        
        // Take screenshot for debugging
        await page.screenshot({ path: 'debug-page-error.png' });
        console.log('📸 Screenshot saved as debug-page-error.png');
        
    } finally {
        await browser.close();
    }
}

// Run the test if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    testWheelDebugPage().catch(console.error);
}

export { testWheelDebugPage };
