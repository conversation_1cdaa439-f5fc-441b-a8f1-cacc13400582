/**
 * Simple test script to verify wheel component fixes
 * Run this in the browser console on the wheel-debug.html page
 */

console.log('🎡 Testing Wheel Component Fixes...');

// Test 1: Check if components are loaded
function testComponentLoading() {
    console.log('\n🔍 Test 1: Component Loading');
    
    const gameWheelDefined = !!customElements.get('game-wheel');
    const viewportDefined = !!customElements.get('wheel-viewport');
    const wheelElement = document.getElementById('debugWheel');
    const viewportElement = document.getElementById('debugViewport');
    
    console.log(`✅ game-wheel component defined: ${gameWheelDefined}`);
    console.log(`✅ wheel-viewport component defined: ${viewportDefined}`);
    console.log(`✅ Wheel element in DOM: ${!!wheelElement}`);
    console.log(`✅ Viewport element in DOM: ${!!viewportElement}`);
    
    return gameWheelDefined && viewportDefined && wheelElement && viewportElement;
}

// Test 2: Check viewport functionality
function testViewportFunctionality() {
    console.log('\n🔍 Test 2: Viewport Functionality');
    
    const viewport = document.getElementById('debugViewport');
    if (!viewport) {
        console.log('❌ Viewport element not found');
        return false;
    }
    
    const hasZoomIn = typeof viewport.zoomIn === 'function';
    const hasZoomOut = typeof viewport.zoomOut === 'function';
    const hasResetView = typeof viewport.resetView === 'function';
    const hasGetViewportState = typeof viewport.getViewportState === 'function';
    
    console.log(`✅ zoomIn method: ${hasZoomIn}`);
    console.log(`✅ zoomOut method: ${hasZoomOut}`);
    console.log(`✅ resetView method: ${hasResetView}`);
    console.log(`✅ getViewportState method: ${hasGetViewportState}`);
    
    if (hasGetViewportState) {
        const state = viewport.getViewportState();
        console.log(`📊 Current viewport state:`, state);
    }
    
    return hasZoomIn && hasZoomOut && hasResetView && hasGetViewportState;
}

// Test 3: Check wheel functionality
function testWheelFunctionality() {
    console.log('\n🔍 Test 3: Wheel Functionality');
    
    const wheel = document.getElementById('debugWheel');
    if (!wheel) {
        console.log('❌ Wheel element not found');
        return false;
    }
    
    const hasSetWheelItems = typeof wheel.setWheelItems === 'function';
    const hasSpin = typeof wheel.spin === 'function';
    const hasReset = typeof wheel.reset === 'function';
    const hasGetWheelState = typeof wheel.getWheelState === 'function';
    
    console.log(`✅ setWheelItems method: ${hasSetWheelItems}`);
    console.log(`✅ spin method: ${hasSpin}`);
    console.log(`✅ reset method: ${hasReset}`);
    console.log(`✅ getWheelState method: ${hasGetWheelState}`);
    
    if (hasGetWheelState) {
        const state = wheel.getWheelState();
        console.log(`📊 Current wheel state:`, {
            isSpinning: state.isSpinning,
            segments: state.segments.length,
            hasPhysicsEngine: state.hasPhysicsEngine,
            hasRenderer: state.hasRenderer
        });
    }
    
    return hasSetWheelItems && hasSpin && hasReset && hasGetWheelState;
}

// Test 4: Test mock data loading and ball visibility
async function testMockDataLoading() {
    console.log('\n🔍 Test 4: Mock Data Loading and Ball Visibility');

    const loadButton = document.getElementById('loadWheelBtn');
    if (!loadButton) {
        console.log('❌ Load button not found');
        return false;
    }

    console.log('📝 Clicking load button...');
    loadButton.click();

    // Wait a moment for loading
    await new Promise(resolve => setTimeout(resolve, 1000));

    const wheel = document.getElementById('debugWheel');
    const state = wheel.getWheelState();

    console.log(`✅ Segments loaded: ${state.segments.length}`);
    console.log(`✅ Physics engine: ${state.hasPhysicsEngine}`);
    console.log(`✅ Renderer: ${state.hasRenderer}`);

    // Test ball visibility
    if (wheel.physicsEngine) {
        const ballBody = wheel.physicsEngine.getBallBody();
        if (ballBody) {
            console.log(`🎱 Ball position: (${ballBody.position.x.toFixed(1)}, ${ballBody.position.y.toFixed(1)})`);
            console.log(`🎱 Ball should be visible at top of wheel`);
        } else {
            console.log('❌ Ball body not found');
        }
    }

    return state.segments.length > 0 && state.hasPhysicsEngine && state.hasRenderer;
}

// Test 5: Test viewport controls
async function testViewportControls() {
    console.log('\n🔍 Test 5: Viewport Controls');
    
    const viewport = document.getElementById('debugViewport');
    const zoomInBtn = document.getElementById('zoomInBtn');
    const zoomOutBtn = document.getElementById('zoomOutBtn');
    const resetViewBtn = document.getElementById('resetViewBtn');
    
    if (!viewport || !zoomInBtn || !zoomOutBtn || !resetViewBtn) {
        console.log('❌ Viewport or control buttons not found');
        return false;
    }
    
    const initialState = viewport.getViewportState();
    console.log('📊 Initial viewport state:', initialState);
    
    // Test zoom in
    console.log('📝 Testing zoom in...');
    zoomInBtn.click();
    await new Promise(resolve => setTimeout(resolve, 200));
    const zoomedInState = viewport.getViewportState();
    console.log('📊 After zoom in:', zoomedInState);
    
    // Test zoom out
    console.log('📝 Testing zoom out...');
    zoomOutBtn.click();
    await new Promise(resolve => setTimeout(resolve, 200));
    const zoomedOutState = viewport.getViewportState();
    console.log('📊 After zoom out:', zoomedOutState);
    
    // Test reset
    console.log('📝 Testing reset view...');
    resetViewBtn.click();
    await new Promise(resolve => setTimeout(resolve, 200));
    const resetState = viewport.getViewportState();
    console.log('📊 After reset:', resetState);
    
    return zoomedInState.zoom !== initialState.zoom && 
           zoomedOutState.zoom !== zoomedInState.zoom &&
           resetState.zoom === 1.0;
}

// Test 6: Test ball movement during spin
async function testBallMovement() {
    console.log('\n🔍 Test 6: Ball Movement During Spin');

    const wheel = document.getElementById('debugWheel');
    if (!wheel || !wheel.physicsEngine) {
        console.log('❌ Wheel or physics engine not available');
        return false;
    }

    const spinButton = document.getElementById('spinWheelBtn');
    if (!spinButton || spinButton.disabled) {
        console.log('❌ Spin button not available or disabled');
        return false;
    }

    // Get initial ball position
    const ballBody = wheel.physicsEngine.getBallBody();
    if (!ballBody) {
        console.log('❌ Ball body not found');
        return false;
    }

    const initialPos = { x: ballBody.position.x, y: ballBody.position.y };
    console.log(`🎱 Initial ball position: (${initialPos.x.toFixed(1)}, ${initialPos.y.toFixed(1)})`);

    // Start spin
    console.log('📝 Starting spin...');
    spinButton.click();

    // Monitor ball movement for 3 seconds
    let movementDetected = false;
    let maxMovement = 0;

    for (let i = 0; i < 30; i++) { // 3 seconds at 100ms intervals
        await new Promise(resolve => setTimeout(resolve, 100));

        const currentPos = ballBody.position;
        const movement = Math.sqrt(
            Math.pow(currentPos.x - initialPos.x, 2) +
            Math.pow(currentPos.y - initialPos.y, 2)
        );

        if (movement > maxMovement) {
            maxMovement = movement;
        }

        if (movement > 5) { // 5 pixels of movement
            movementDetected = true;
            console.log(`🎱 Ball movement detected! Distance: ${movement.toFixed(1)} pixels`);
            break;
        }
    }

    console.log(`🎱 Maximum ball movement: ${maxMovement.toFixed(1)} pixels`);
    console.log(`🎱 Movement detected: ${movementDetected ? '✅' : '❌'}`);

    return movementDetected;
}

// Run all tests
async function runAllTests() {
    console.log('🎡 Starting Wheel Component Fix Validation...');

    const results = {
        componentLoading: testComponentLoading(),
        viewportFunctionality: testViewportFunctionality(),
        wheelFunctionality: testWheelFunctionality(),
        mockDataLoading: await testMockDataLoading(),
        viewportControls: await testViewportControls(),
        ballMovement: await testBallMovement()
    };
    
    console.log('\n📊 Test Results Summary:');
    console.log('========================');
    
    let passed = 0;
    let total = 0;
    
    Object.entries(results).forEach(([test, result]) => {
        total++;
        if (result) {
            passed++;
            console.log(`✅ ${test}: PASSED`);
        } else {
            console.log(`❌ ${test}: FAILED`);
        }
    });
    
    console.log(`\n🎯 Overall Result: ${passed}/${total} tests passed (${((passed/total)*100).toFixed(1)}%)`);
    
    if (passed === total) {
        console.log('🎉 All fixes are working correctly!');
        console.log('\n🎮 You can now:');
        console.log('  • Load mock wheel items');
        console.log('  • Spin the wheel and see winner detection with 1-second delay');
        console.log('  • Use viewport controls to zoom and pan');
        console.log('  • Debug wheel functionality with detailed logging');
    } else {
        console.log('🔧 Some tests failed. Check the console for details.');
    }
    
    return results;
}

// Auto-run tests if this script is executed directly
if (typeof window !== 'undefined') {
    // Wait for page to be fully loaded
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(runAllTests, 1000);
        });
    } else {
        setTimeout(runAllTests, 1000);
    }
}

// Export for manual testing
window.wheelTestSuite = {
    runAllTests,
    testComponentLoading,
    testViewportFunctionality,
    testWheelFunctionality,
    testMockDataLoading,
    testViewportControls,
    testBallMovement
};
