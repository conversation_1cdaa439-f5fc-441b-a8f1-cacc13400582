# Wheel Component Debug Environment

This directory contains a dedicated debugging environment for the game wheel component, allowing isolated testing and development.

## 🎯 Purpose

- **Isolated Testing**: Test the wheel component without the full application context
- **Mock Data**: Use predefined mock wheel items for consistent testing
- **Debug Tools**: Visual debugging interface with real-time state monitoring
- **Winner Detection**: Debug and validate the winner detection algorithm

## 🚀 Quick Start

### Option 1: Using Vite Debug Server (Recommended)

```bash
# From the frontend directory
npm run debug:wheel

# Or from this debug directory
npx vite --config vite.debug.config.ts
```

This will start a development server on `http://localhost:3002` with the debug page.

### Option 2: Using Main Vite Server

```bash
# From the frontend directory
npm run dev

# Then navigate to:
# http://localhost:3000/debug/wheel-debug.html
```

## 🎮 How to Use

1. **Load Mock Data**: Click "Load 8 Mock Items" to populate the wheel
2. **Spin the Wheel**: Click "Spin Wheel!" to test the physics and winner detection
3. **Monitor Status**: Watch the status log for real-time feedback
4. **Debug Info**: View wheel state and physics data in the debug panel
5. **Winner Detection**: Use "Debug Winner Logic" to inspect the detection algorithm

## 🔧 Features

### Mock Wheel Items
The debug page includes 8 predefined activities:
- 🏃‍♂️ Morning Run (15%)
- 📚 Study Session (20%)
- 🎨 Creative Time (12%)
- 🧘‍♀️ Meditation (10%)
- 🍳 Cooking (13%)
- 📞 Social Call (8%)
- 🌱 Garden Work (12%)
- 🎵 Music Practice (10%)

### Debug Controls
- **Load Mock Items**: Populates wheel with test data
- **Spin Wheel**: Triggers the physics simulation
- **Reset Wheel**: Returns wheel to initial state
- **Debug Winner Logic**: Shows detailed winner detection info

### Real-time Monitoring
- **Status Log**: Timestamped event logging
- **Debug Info**: Live wheel state and physics data
- **Winner Display**: Visual winner announcement
- **Event Tracking**: All wheel events are logged

## 🎨 Component Interface

The debug page demonstrates the clean Lit component interface:

```javascript
// Set wheel items (clean API)
wheel.setWheelItems([
  {
    id: 'activity-1',
    text: '🏃‍♂️ Morning Run',
    percentage: 15,
    color: '#FF6B6B',
    activityId: 'run-morning'
  },
  // ... more items
]);

// Spin the wheel
wheel.spin();

// Reset to initial state
wheel.reset();

// Get current state for debugging
const state = wheel.getWheelState();
```

## 🔍 Debugging Winner Detection

The debug page helps validate the winner detection algorithm:

1. **Ball Position Tracking**: Monitor ball movement during spin
2. **Angle Calculations**: See real-time angle calculations
3. **Segment Mapping**: Verify which segment the ball lands in
4. **Physics State**: Check ball and wheel settling detection

## 📊 Event System

The component dispatches these events that are monitored:

- `wheel-spin-start`: When spin begins
- `wheel-spinning`: During spin (real-time updates)
- `wheel-spin-complete`: When spin ends with winner
- `wheel-result`: Final result with winning segment

## 🛠️ Development Tips

### Testing Winner Detection
1. Load mock items
2. Spin multiple times
3. Check that winners match expected percentages over time
4. Use debug info to verify ball position calculations

### Physics Debugging
1. Monitor velocity values during spin
2. Check settling detection thresholds
3. Verify ball and wheel rotation synchronization
4. Test edge cases (very fast/slow spins)

### Visual Debugging
1. Watch ball movement on the wheel
2. Verify segment highlighting on winner
3. Check responsive behavior at different sizes
4. Test touch/mouse interactions

## 🔧 Configuration

The debug server uses port 3002 by default. You can modify this in `vite.debug.config.ts`:

```typescript
server: {
  port: 3002, // Change this port if needed
  host: true,
  open: '/wheel-debug.html',
}
```

## 📝 Notes

- The wheel component starts invisible until items are loaded
- UI elements (spin button, legend) are hidden in debug mode
- All console logs are preserved for detailed debugging
- The debug page is responsive and works on mobile devices

## 🚨 Troubleshooting

### Component Not Loading
- Check that the wheel component is properly built
- Verify import paths in the HTML file
- Check browser console for module loading errors

### Physics Issues
- Monitor ball tracking in the debug info
- Check that physics engine initializes properly
- Verify Matter.js and PixiJS are loaded correctly

### Winner Detection Problems
- Use the debug winner button to inspect segment data
- Check ball angle calculations in real-time
- Verify segment angle ranges are correct
