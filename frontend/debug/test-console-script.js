/**
 * Console test script for the wheel debug page
 * Copy and paste this into the browser console to test functionality
 */

console.log('🧪 Starting Wheel Debug Page Console Test...');

// Test 1: Check if wheel component is loaded
const wheel = document.getElementById('debugWheel');
if (wheel) {
    console.log('✅ Wheel element found');
    console.log('   Tag name:', wheel.tagName);
    console.log('   Constructor:', wheel.constructor.name);
    
    // Check if it's a custom element
    if (wheel.tagName === 'GAME-WHEEL') {
        console.log('✅ Wheel is a custom element');
        
        // Check available methods
        const methods = ['setWheelItems', 'spin', 'reset', 'getWheelState'];
        methods.forEach(method => {
            if (typeof wheel[method] === 'function') {
                console.log(`✅ Method ${method} available`);
            } else {
                console.log(`❌ Method ${method} missing`);
            }
        });
        
        // Test getWheelState if available
        if (typeof wheel.getWheelState === 'function') {
            try {
                const state = wheel.getWheelState();
                console.log('✅ Wheel state:', state);
            } catch (error) {
                console.log('❌ Error getting wheel state:', error);
            }
        }
    } else {
        console.log('❌ Wheel is not a custom element');
    }
} else {
    console.log('❌ Wheel element not found');
}

// Test 2: Check if debug controls are working
const loadBtn = document.getElementById('loadWheelBtn');
const spinBtn = document.getElementById('spinWheelBtn');
const debugBtn = document.getElementById('debugWinnerBtn');

console.log('🎮 Testing debug controls...');
console.log('   Load button:', loadBtn ? '✅' : '❌');
console.log('   Spin button:', spinBtn ? '✅' : '❌');
console.log('   Debug button:', debugBtn ? '✅' : '❌');

// Test 3: Check if mock data generator is working
try {
    if (typeof generateMockWheelItems === 'function') {
        const mockItems = generateMockWheelItems('balanced');
        console.log('✅ Mock data generator working');
        console.log('   Generated items:', mockItems.length);
        console.log('   Sample item:', mockItems[0]);
    } else {
        console.log('❌ Mock data generator not available');
    }
} catch (error) {
    console.log('❌ Error testing mock data generator:', error);
}

// Test 4: Simulate loading wheel items
console.log('🔄 Testing wheel loading...');
if (loadBtn && !loadBtn.disabled) {
    console.log('   Clicking load button...');
    loadBtn.click();
    
    // Check result after a delay
    setTimeout(() => {
        if (wheel && typeof wheel.getWheelState === 'function') {
            const state = wheel.getWheelState();
            console.log('   Wheel state after loading:', {
                segments: state.segments?.length || 0,
                hasPhysics: state.hasPhysicsEngine,
                hasRenderer: state.hasRenderer
            });
        }
    }, 2000);
} else {
    console.log('   Load button not available or disabled');
}

// Test 5: Check enhanced winner detection
console.log('🎯 Testing enhanced winner detection...');
try {
    // Test the enhanced detection function if available
    if (typeof getWinningSegmentAdvanced === 'function') {
        console.log('✅ Enhanced winner detection function available');
        
        // Create test segments
        const testSegments = [
            { id: '1', text: 'Test 1', startAngle: 0, endAngle: Math.PI/2, centerAngle: Math.PI/4 },
            { id: '2', text: 'Test 2', startAngle: Math.PI/2, endAngle: Math.PI, centerAngle: 3*Math.PI/4 }
        ];
        
        // Test detection
        const result = getWinningSegmentAdvanced(100, 100, 50, 50, testSegments);
        console.log('   Test result:', result);
    } else {
        console.log('❌ Enhanced winner detection not available');
    }
} catch (error) {
    console.log('❌ Error testing winner detection:', error);
}

console.log('🏁 Console test completed!');
console.log('');
console.log('📋 Manual Test Checklist:');
console.log('   1. Click "Load 8 Mock Items" - wheel should become visible');
console.log('   2. Click "Spin Wheel!" - wheel should spin and show winner');
console.log('   3. Check status log for detailed information');
console.log('   4. Try "Debug Winner Logic" for detailed analysis');
console.log('   5. Test different scenarios from dropdown');
console.log('   6. Use "Track Ball Movement" during spin');
console.log('   7. Check "Show Segment Info" for segment details');
console.log('');
console.log('🔍 Expected Results:');
console.log('   - Wheel loads with 8 colorful segments');
console.log('   - Spinning takes 8-12 seconds');
console.log('   - Winner detection shows confidence percentage');
console.log('   - Ball tracking shows real-time position data');
console.log('   - Debug info shows physics engine status');
