/**
 * Mock data generator for wheel component testing
 * Provides various sets of test data for different scenarios
 */

// Activity domains with culturally relevant colors
const ACTIVITY_DOMAINS = {
    physical: {
        baseColor: '#FF6B6B', // Red for energy/physical
        activities: ['🏃‍♂️ Morning Run', '🚴‍♀️ Bike Ride', '🏋️‍♂️ Gym Workout', '🧘‍♀️ Yoga Session', '🏊‍♀️ Swimming']
    },
    mental: {
        baseColor: '#4ECDC4', // Teal for focus/mental
        activities: ['📚 Study Session', '🧠 Brain Training', '📝 Writing Time', '🔬 Research', '💭 Reflection']
    },
    creative: {
        baseColor: '#45B7D1', // Blue for creativity
        activities: ['🎨 Creative Time', '🎵 Music Practice', '📸 Photography', '✍️ Journaling', '🎭 Acting Practice']
    },
    social: {
        baseColor: '#96CEB4', // Green for growth/social
        activities: ['📞 Social Call', '👥 Team Meeting', '🎉 Social Event', '🤝 Networking', '👨‍👩‍👧‍👦 Family Time']
    },
    practical: {
        baseColor: '#FFEAA7', // Yellow for practical/daily
        activities: ['🍳 Cooking', '🧹 Cleaning', '📋 Planning', '💰 Budgeting', '🛒 Shopping']
    },
    nature: {
        baseColor: '#98D8C8', // Light green for nature
        activities: ['🌱 Garden Work', '🌳 Nature Walk', '🏕️ Outdoor Time', '🌺 Plant Care', '🦋 Wildlife Watching']
    },
    learning: {
        baseColor: '#DDA0DD', // Purple for learning/wisdom
        activities: ['📖 Reading', '🎓 Online Course', '🗣️ Language Practice', '🔍 Skill Building', '📺 Educational Video']
    },
    wellness: {
        baseColor: '#F7DC6F', // Light yellow for wellness
        activities: ['🧘‍♀️ Meditation', '🛁 Self Care', '😴 Rest Time', '🌅 Mindfulness', '💆‍♀️ Relaxation']
    }
};

/**
 * Generates color variations based on energy and challenge levels
 */
function generateColorVariation(baseColor, energyLevel = 0.5, challengeLevel = 0.5) {
    // Convert hex to HSL for manipulation
    const hex = baseColor.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16) / 255;
    const g = parseInt(hex.substr(2, 2), 16) / 255;
    const b = parseInt(hex.substr(4, 2), 16) / 255;

    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    let h, s, l = (max + min) / 2;

    if (max === min) {
        h = s = 0; // achromatic
    } else {
        const d = max - min;
        s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
        switch (max) {
            case r: h = (g - b) / d + (g < b ? 6 : 0); break;
            case g: h = (b - r) / d + 2; break;
            case b: h = (r - g) / d + 4; break;
        }
        h /= 6;
    }

    // Adjust brightness based on energy level (0.3 to 0.8)
    l = 0.3 + (energyLevel * 0.5);
    
    // Adjust saturation based on challenge level (0.4 to 0.9)
    s = 0.4 + (challengeLevel * 0.5);

    // Convert back to hex
    const hslToHex = (h, s, l) => {
        const hue2rgb = (p, q, t) => {
            if (t < 0) t += 1;
            if (t > 1) t -= 1;
            if (t < 1/6) return p + (q - p) * 6 * t;
            if (t < 1/2) return q;
            if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
            return p;
        };

        let r, g, b;
        if (s === 0) {
            r = g = b = l; // achromatic
        } else {
            const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
            const p = 2 * l - q;
            r = hue2rgb(p, q, h + 1/3);
            g = hue2rgb(p, q, h);
            b = hue2rgb(p, q, h - 1/3);
        }

        const toHex = (c) => {
            const hex = Math.round(c * 255).toString(16);
            return hex.length === 1 ? '0' + hex : hex;
        };

        return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
    };

    return hslToHex(h, s, l);
}

/**
 * Generates a balanced set of 8 wheel items
 */
export function generateMockWheelItems(scenario = 'balanced') {
    const scenarios = {
        balanced: {
            name: 'Balanced Life Activities',
            items: [
                { domain: 'physical', activity: '🏃‍♂️ Morning Run', percentage: 15, energy: 0.8, challenge: 0.6 },
                { domain: 'mental', activity: '📚 Study Session', percentage: 20, energy: 0.4, challenge: 0.8 },
                { domain: 'creative', activity: '🎨 Creative Time', percentage: 12, energy: 0.6, challenge: 0.7 },
                { domain: 'wellness', activity: '🧘‍♀️ Meditation', percentage: 10, energy: 0.3, challenge: 0.4 },
                { domain: 'practical', activity: '🍳 Cooking', percentage: 13, energy: 0.5, challenge: 0.5 },
                { domain: 'social', activity: '📞 Social Call', percentage: 8, energy: 0.7, challenge: 0.3 },
                { domain: 'nature', activity: '🌱 Garden Work', percentage: 12, energy: 0.4, challenge: 0.6 },
                { domain: 'learning', activity: '🎵 Music Practice', percentage: 10, energy: 0.6, challenge: 0.8 }
            ]
        },
        
        high_energy: {
            name: 'High Energy Activities',
            items: [
                { domain: 'physical', activity: '🏋️‍♂️ Gym Workout', percentage: 18, energy: 0.9, challenge: 0.8 },
                { domain: 'physical', activity: '🚴‍♀️ Bike Ride', percentage: 15, energy: 0.8, challenge: 0.6 },
                { domain: 'social', activity: '🎉 Social Event', percentage: 12, energy: 0.9, challenge: 0.4 },
                { domain: 'creative', activity: '🎭 Acting Practice', percentage: 10, energy: 0.8, challenge: 0.9 },
                { domain: 'nature', activity: '🏕️ Outdoor Time', percentage: 14, energy: 0.7, challenge: 0.7 },
                { domain: 'learning', activity: '🗣️ Language Practice', percentage: 11, energy: 0.6, challenge: 0.8 },
                { domain: 'practical', activity: '🛒 Shopping', percentage: 8, energy: 0.6, challenge: 0.3 },
                { domain: 'mental', activity: '🧠 Brain Training', percentage: 12, energy: 0.5, challenge: 0.9 }
            ]
        },

        relaxed: {
            name: 'Relaxed & Mindful Activities',
            items: [
                { domain: 'wellness', activity: '🧘‍♀️ Meditation', percentage: 20, energy: 0.2, challenge: 0.4 },
                { domain: 'learning', activity: '📖 Reading', percentage: 18, energy: 0.3, challenge: 0.5 },
                { domain: 'nature', activity: '🌳 Nature Walk', percentage: 15, energy: 0.4, challenge: 0.3 },
                { domain: 'creative', activity: '✍️ Journaling', percentage: 12, energy: 0.3, challenge: 0.6 },
                { domain: 'wellness', activity: '🛁 Self Care', percentage: 10, energy: 0.2, challenge: 0.2 },
                { domain: 'practical', activity: '🌺 Plant Care', percentage: 8, energy: 0.3, challenge: 0.4 },
                { domain: 'learning', activity: '📺 Educational Video', percentage: 9, energy: 0.2, challenge: 0.3 },
                { domain: 'social', activity: '👨‍👩‍👧‍👦 Family Time', percentage: 8, energy: 0.5, challenge: 0.2 }
            ]
        },

        student_focused: {
            name: 'Student Life Activities',
            items: [
                { domain: 'mental', activity: '📚 Study Session', percentage: 25, energy: 0.4, challenge: 0.9 },
                { domain: 'learning', activity: '🎓 Online Course', percentage: 15, energy: 0.3, challenge: 0.8 },
                { domain: 'mental', activity: '📝 Writing Time', percentage: 12, energy: 0.4, challenge: 0.7 },
                { domain: 'physical', activity: '🧘‍♀️ Yoga Session', percentage: 10, energy: 0.4, challenge: 0.5 },
                { domain: 'social', activity: '👥 Team Meeting', percentage: 8, energy: 0.6, challenge: 0.6 },
                { domain: 'practical', activity: '📋 Planning', percentage: 10, energy: 0.3, challenge: 0.5 },
                { domain: 'wellness', activity: '😴 Rest Time', percentage: 12, energy: 0.1, challenge: 0.2 },
                { domain: 'creative', activity: '🎵 Music Practice', percentage: 8, energy: 0.6, challenge: 0.8 }
            ]
        }
    };

    const selectedScenario = scenarios[scenario] || scenarios.balanced;
    
    return selectedScenario.items.map((item, index) => {
        const domain = ACTIVITY_DOMAINS[item.domain];
        const color = generateColorVariation(domain.baseColor, item.energy, item.challenge);
        
        return {
            id: `activity-${index + 1}`,
            text: item.activity,
            percentage: item.percentage,
            color: color,
            activityId: `${item.domain}-${index + 1}`,
            domain: item.domain,
            energyLevel: item.energy,
            challengeLevel: item.challenge
        };
    });
}

/**
 * Gets available scenarios
 */
export function getAvailableScenarios() {
    return [
        { id: 'balanced', name: 'Balanced Life Activities', description: 'Well-rounded mix of different activity types' },
        { id: 'high_energy', name: 'High Energy Activities', description: 'Active and energetic activities for motivation' },
        { id: 'relaxed', name: 'Relaxed & Mindful Activities', description: 'Calm and peaceful activities for stress relief' },
        { id: 'student_focused', name: 'Student Life Activities', description: 'Academic and study-focused activities' }
    ];
}

/**
 * Validates wheel items for proper structure
 */
export function validateWheelItems(items) {
    if (!Array.isArray(items)) {
        throw new Error('Wheel items must be an array');
    }

    if (items.length === 0) {
        throw new Error('Wheel items array cannot be empty');
    }

    const totalPercentage = items.reduce((sum, item) => sum + item.percentage, 0);
    if (Math.abs(totalPercentage - 100) > 1) {
        console.warn(`Total percentage is ${totalPercentage}%, not 100%`);
    }

    items.forEach((item, index) => {
        if (!item.id || typeof item.id !== 'string') {
            throw new Error(`Item ${index} missing valid id`);
        }
        if (!item.text || typeof item.text !== 'string') {
            throw new Error(`Item ${index} missing valid text`);
        }
        if (typeof item.percentage !== 'number' || item.percentage <= 0) {
            throw new Error(`Item ${index} missing valid percentage`);
        }
        if (!item.color || typeof item.color !== 'string') {
            throw new Error(`Item ${index} missing valid color`);
        }
    });

    return true;
}

// Default export for convenience
export default {
    generateMockWheelItems,
    getAvailableScenarios,
    validateWheelItems,
    ACTIVITY_DOMAINS
};
