/**
 * Production-test configuration for Goali frontend
 * This configuration simulates production environment for local testing
 * with proper authentication and security measures enforced
 */

export const ProductionTestConfig = {
  // Environment settings
  environment: 'production-test',
  debug: false,
  
  // Backend connection
  backend: {
    baseUrl: 'http://localhost:8001',
    websocketUrl: 'ws://localhost:8001/ws/game/',
    apiTimeout: 30000,
    retryAttempts: 3,
    retryDelay: 1000
  },
  
  // Authentication settings
  auth: {
    required: true,
    sessionTimeout: 3600000, // 1 hour in milliseconds
    refreshThreshold: 300000, // 5 minutes before expiry
    loginUrl: '/api/auth/login/',
    verifyUrl: '/api/auth/verify/',
    logoutUrl: '/api/auth/logout/',
    // No demo mode in production-test
    demoMode: false,
    allowGuestAccess: false
  },
  
  // Security settings
  security: {
    enforceHttps: false, // Set to true in real production
    secureCookies: false, // Set to true in real production
    csrfProtection: true,
    xssProtection: true,
    contentSecurityPolicy: true,
    frameOptions: 'DENY',
    contentTypeOptions: 'nosniff'
  },
  
  // Feature flags
  features: {
    debugPanel: false, // Disabled in production-test
    mockData: false,
    offlineMode: false,
    analytics: true,
    errorReporting: true,
    performanceMonitoring: true
  },
  
  // UI settings
  ui: {
    showDebugInfo: false,
    enableDevTools: false,
    logLevel: 'warn', // Only warnings and errors
    showPerformanceMetrics: false
  },
  
  // WebSocket settings
  websocket: {
    reconnectAttempts: 5,
    reconnectDelay: 2000,
    heartbeatInterval: 30000,
    connectionTimeout: 10000,
    messageQueueSize: 100
  },
  
  // Error handling
  errorHandling: {
    showDetailedErrors: false,
    reportErrors: true,
    fallbackToOffline: false,
    maxRetries: 3
  },
  
  // Performance settings
  performance: {
    enableCaching: true,
    cacheTimeout: 300000, // 5 minutes
    lazyLoading: true,
    imageOptimization: true,
    bundleOptimization: true
  },
  
  // Logging settings
  logging: {
    level: 'warn',
    remoteLogging: true,
    logEndpoint: '/api/logs/',
    maxLogSize: 1000,
    logRetention: 86400000 // 24 hours
  },
  
  // API settings
  api: {
    rateLimit: {
      enabled: true,
      maxRequests: 100,
      windowMs: 60000 // 1 minute
    },
    timeout: 30000,
    retries: 3,
    caching: true
  }
};

// Validation function to ensure required settings are present
export function validateProductionTestConfig() {
  const required = [
    'backend.baseUrl',
    'backend.websocketUrl',
    'auth.loginUrl',
    'auth.verifyUrl',
    'auth.logoutUrl'
  ];
  
  const missing = [];
  
  for (const path of required) {
    const keys = path.split('.');
    let current = ProductionTestConfig;
    
    for (const key of keys) {
      if (current[key] === undefined) {
        missing.push(path);
        break;
      }
      current = current[key];
    }
  }
  
  if (missing.length > 0) {
    throw new Error(`Missing required production-test configuration: ${missing.join(', ')}`);
  }
  
  return true;
}

// Export default configuration
export default ProductionTestConfig;
