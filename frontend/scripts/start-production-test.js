#!/usr/bin/env node

/**
 * Production-Test Environment Startup Script
 *
 * This script sets up and starts the Goali frontend in production-test mode,
 * which simulates production environment settings for local testing.
 *
 * Features:
 * - Authentication required (like production)
 * - Debug panel disabled (like production)
 * - Production-like security settings
 * - Local backend connection
 * - Comprehensive error reporting
 */

import { execSync, spawn } from 'child_process';
import { existsSync, readFileSync, writeFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logHeader(message) {
  log(`\n${colors.bright}${colors.blue}=== ${message} ===${colors.reset}`);
}

function logSuccess(message) {
  log(`${colors.green}✅ ${message}${colors.reset}`);
}

function logWarning(message) {
  log(`${colors.yellow}⚠️  ${message}${colors.reset}`);
}

function logError(message) {
  log(`${colors.red}❌ ${message}${colors.reset}`);
}

// Check if backend is running
function checkBackendStatus() {
  logHeader('Checking Backend Status');

  try {
    const response = execSync('curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/health/', {
      encoding: 'utf8',
      timeout: 5000
    }).trim();

    if (response === '200') {
      logSuccess('Backend is running and healthy');
      return true;
    } else {
      logWarning(`Backend responded with status: ${response}`);
      return false;
    }
  } catch (error) {
    logError('Backend is not running or not accessible');
    log('Please start the backend first:', colors.cyan);
    log('  cd backend && docker-compose up', colors.cyan);
    return false;
  }
}

// Validate environment configuration
function validateEnvironment() {
  logHeader('Validating Environment Configuration');

  const envFile = join(projectRoot, '.env.production-test');

  if (!existsSync(envFile)) {
    logError('.env.production-test file not found');
    return false;
  }

  const envContent = readFileSync(envFile, 'utf8');
  const requiredVars = [
    'VITE_APP_MODE=production-test',
    'VITE_SECURITY_REQUIRE_AUTH=true',
    'VITE_API_BASE_URL=http://localhost:8000',
    'VITE_WS_URL=ws://localhost:8000/ws/game/'
  ];

  const missing = requiredVars.filter(varDef => !envContent.includes(varDef));

  if (missing.length > 0) {
    logError('Missing or incorrect environment variables:');
    missing.forEach(varDef => log(`  - ${varDef}`, colors.red));
    return false;
  }

  logSuccess('Environment configuration is valid');
  return true;
}

// Start the development server with production-test environment
function startProductionTest() {
  logHeader('Starting Production-Test Environment');

  // Set environment to use production-test config
  process.env.NODE_ENV = 'production-test';

  log('Starting Vite development server with production-test configuration...', colors.cyan);
  log('This will simulate production environment with local backend', colors.cyan);

  const viteProcess = spawn('npx', ['vite', '--mode', 'production-test'], {
    cwd: projectRoot,
    stdio: 'inherit',
    env: { ...process.env, NODE_ENV: 'production-test' }
  });

  viteProcess.on('error', (error) => {
    logError(`Failed to start development server: ${error.message}`);
    process.exit(1);
  });

  viteProcess.on('close', (code) => {
    if (code !== 0) {
      logError(`Development server exited with code ${code}`);
      process.exit(code);
    }
  });

  // Handle graceful shutdown
  process.on('SIGINT', () => {
    log('\nShutting down production-test environment...', colors.yellow);
    viteProcess.kill('SIGINT');
    process.exit(0);
  });
}

// Main execution
function main() {
  log(`${colors.bright}${colors.magenta}🎯 Goali Frontend - Production-Test Environment${colors.reset}`);
  log('This environment simulates production settings for local testing\n');

  // Check prerequisites
  if (!checkBackendStatus()) {
    process.exit(1);
  }

  if (!validateEnvironment()) {
    process.exit(1);
  }

  // Start the server
  startProductionTest();
}

// Run the script
main();