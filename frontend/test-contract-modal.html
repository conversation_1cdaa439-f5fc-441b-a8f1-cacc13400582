<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contract Modal Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-button {
            background: linear-gradient(135deg, #FF6B6B, #e55a5a);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            background: linear-gradient(135deg, #e55a5a, #d94545);
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(255, 107, 107, 0.4);
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-weight: 500;
        }
        
        .test-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .test-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .signature-preview {
            margin-top: 15px;
            text-align: center;
        }
        
        .signature-preview img {
            max-width: 300px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            background: white;
            padding: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🎯 Contract Disclaimer Modal Test</h1>
            <p>Test the complete contract signing flow for the wheel spinning feature</p>
        </div>
        
        <div class="test-section">
            <h3>📋 Test Flow</h3>
            <ol>
                <li>Click "Show Contract Modal" to display the disclaimer</li>
                <li>Read the contract text and draw your signature</li>
                <li>Click "Accept Contract & Spin" to proceed</li>
                <li>Observe the signature storage and retrieval</li>
                <li>Test the cleanup functionality</li>
            </ol>
        </div>
        
        <div class="test-section">
            <h3>🧪 Test Actions</h3>
            <button class="test-button" onclick="showContractModal()">Show Contract Modal</button>
            <button class="test-button" onclick="testSignatureStorage()">Test Signature Storage</button>
            <button class="test-button" onclick="clearSignature()">Clear Signature</button>
            <button class="test-button" onclick="simulateWinningModal()">Simulate Winning Modal</button>
            
            <div id="test-results"></div>
        </div>
        
        <div class="test-section">
            <h3>📊 Current State</h3>
            <div id="state-display">
                <p><strong>Stored Signature:</strong> <span id="signature-status">None</span></p>
                <div id="signature-preview" class="signature-preview"></div>
            </div>
        </div>
    </div>

    <!-- Contract Modal -->
    <contract-disclaimer-modal id="contract-modal"></contract-disclaimer-modal>

    <!-- Load the components -->
    <script type="module" src="./dist/assets/main-QGAha7eQ.js"></script>
    
    <script>
        let contractModal;
        
        // Wait for components to be defined
        customElements.whenDefined('contract-disclaimer-modal').then(() => {
            contractModal = document.getElementById('contract-modal');
            setupEventListeners();
            updateStateDisplay();
        });
        
        function setupEventListeners() {
            contractModal.addEventListener('contract-accepted', handleContractAccepted);
            contractModal.addEventListener('contract-cancelled', handleContractCancelled);
        }
        
        function showContractModal() {
            contractModal.visible = true;
            logResult('Contract modal displayed', 'success');
        }
        
        function handleContractAccepted(event) {
            const signature = event.detail.signature;
            logResult(`Contract accepted! Signature captured: ${signature ? 'Yes' : 'No'}`, 'success');
            contractModal.visible = false;
            updateStateDisplay();
        }
        
        function handleContractCancelled() {
            logResult('Contract cancelled by user', 'error');
            contractModal.visible = false;
        }
        
        function testSignatureStorage() {
            // Access the static method through the constructor
            const stored = contractModal.constructor.getStoredSignature();
            if (stored) {
                logResult(`Signature found in storage. Timestamp: ${new Date(stored.timestamp).toLocaleString()}`, 'success');
            } else {
                logResult('No signature found in storage', 'error');
            }
            updateStateDisplay();
        }
        
        function clearSignature() {
            contractModal.constructor.clearStoredSignature();
            logResult('Signature cleared from storage', 'success');
            updateStateDisplay();
        }
        
        function simulateWinningModal() {
            const stored = contractModal.constructor.getStoredSignature();
            if (stored) {
                logResult('Winning modal would display signature: ✓', 'success');
                // Simulate cleanup after winning modal closes
                setTimeout(() => {
                    contractModal.constructor.clearStoredSignature();
                    logResult('Signature cleaned up after winning modal closed', 'success');
                    updateStateDisplay();
                }, 2000);
            } else {
                logResult('No signature to display in winning modal', 'error');
            }
        }
        
        function updateStateDisplay() {
            const stored = contractModal?.constructor.getStoredSignature();
            const statusElement = document.getElementById('signature-status');
            const previewElement = document.getElementById('signature-preview');
            
            if (stored) {
                statusElement.textContent = `Present (${new Date(stored.timestamp).toLocaleString()})`;
                statusElement.style.color = '#28a745';
                
                // Show signature preview
                previewElement.innerHTML = `
                    <p><strong>Signature Preview:</strong></p>
                    <img src="${stored.imageData}" alt="Stored signature" />
                `;
            } else {
                statusElement.textContent = 'None';
                statusElement.style.color = '#dc3545';
                previewElement.innerHTML = '';
            }
        }
        
        function logResult(message, type) {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result test-${type}`;
            resultDiv.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            resultsDiv.appendChild(resultDiv);
            
            // Keep only last 5 results
            while (resultsDiv.children.length > 5) {
                resultsDiv.removeChild(resultsDiv.firstChild);
            }
        }
        
        // Update state display every 2 seconds
        setInterval(updateStateDisplay, 2000);
    </script>
</body>
</html>
