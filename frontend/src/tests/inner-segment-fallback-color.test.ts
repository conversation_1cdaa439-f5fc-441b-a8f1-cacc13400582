/**
 * Test to reproduce the inner segment fallback color issue
 * 
 * This test reproduces the exact issue where renderInnerSegment always falls back
 * to #000000 because centerColor is not available and getDomainColor fails.
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { getDomainColor } from '../services/domainColorService.js';

// Mock segment structure that matches what the renderer receives
interface TestSegment {
  id: string;
  name: string;
  domain: string;
  percentage: number;
  startAngle: number;
  endAngle: number;
  color?: string;
  centerColor?: string;
  extremityColor?: string;
}

describe('Inner Segment Fallback Color Issue', () => {
  let testSegments: TestSegment[];

  beforeEach(() => {
    // Create test segments that match the real wheel data structure
    testSegments = [
      {
        id: 'item_252_1_86',
        name: 'Pomodoro Focus Session (Tailored for 125min)',
        domain: 'productive_practical',
        percentage: 16.666666666666668,
        startAngle: 0,
        endAngle: Math.PI / 3,
        color: '#27AE60', // This is the outer color
        // Note: centerColor is NOT provided - this is the issue
      },
      {
        id: 'item_252_2_155',
        name: 'Advanced Urban Parkour (Tailored for 125min)',
        domain: 'general',
        percentage: 16.666666666666668,
        startAngle: Math.PI / 3,
        endAngle: 2 * Math.PI / 3,
        color: '#52C41A',
        // Note: centerColor is NOT provided - this is the issue
      },
      {
        id: 'item_252_5_64',
        name: 'Emotional Anchoring Technique (Tailored for 125min)',
        domain: 'emotional',
        percentage: 16.666666666666668,
        startAngle: 2 * Math.PI / 3,
        endAngle: Math.PI,
        color: '#9B59B6',
        // Note: centerColor is NOT provided - this is the issue
      }
    ];
  });

  it('should reproduce the fallback color issue in renderInnerSegment', () => {
    console.log('=== REPRODUCING INNER SEGMENT FALLBACK COLOR ISSUE ===');

    // Simulate the exact logic from renderInnerSegment function
    const innerSegmentColors = testSegments.map((segment, index) => {
      // Use centerColor for inner wheel (domain color)
      let domainColor = (segment as any).centerColor;

      // Fallback to domain-based color if centerColor not available
      if (!domainColor && (segment as any).domain) {
        try {
          domainColor = getDomainColor((segment as any).domain);
        } catch (error) {
          console.warn('Could not get domain color for inner segment:', error);
          domainColor = '#000000'; // Fallback to physical color
        }
      }

      // Final fallback
      if (!domainColor) {
        domainColor = '#000000'; // Default physical color
      }

      // Validate color
      const validDomainColor = domainColor?.match(/^#[0-9A-Fa-f]{6}$/) ? domainColor : '#E74C3C';

      console.log(`Inner segment ${index}: ${segment.name}`);
      console.log(`  - centerColor: ${(segment as any).centerColor || 'undefined'}`);
      console.log(`  - domain: ${segment.domain}`);
      console.log(`  - getDomainColor result: ${getDomainColor(segment.domain)}`);
      console.log(`  - final domainColor: ${domainColor}`);
      console.log(`  - validDomainColor: ${validDomainColor}`);

      return {
        segmentIndex: index,
        segmentName: segment.name,
        domain: segment.domain,
        centerColor: (segment as any).centerColor,
        resolvedDomainColor: domainColor,
        finalColor: validDomainColor
      };
    });

    // The test should FAIL initially because we expect proper domain colors, not fallback
    const hasProperDomainColors = innerSegmentColors.every(result =>
      result.finalColor !== '#000000' && result.finalColor !== '#E74C3C'
    );

    console.log('=== RESULTS ===');
    console.log('Inner segment colors:', innerSegmentColors.map(r => r.finalColor));
    console.log('Has proper domain colors:', hasProperDomainColors);

    // This test should FAIL initially to detect the issue
    expect(hasProperDomainColors).toBe(true);

    // Verify that getDomainColor works correctly for known domains
    expect(getDomainColor('productive_practical')).toBe('#27AE60');
    expect(getDomainColor('general')).toBe('#52C41A');
    expect(getDomainColor('emotional')).toBe('#9B59B6');
  });

  it('should reproduce the exact console log issue where centerColor is missing', () => {
    console.log('=== REPRODUCING EXACT CONSOLE LOG ISSUE ===');

    // Create segments that match the console logs exactly - with centerColor missing
    const segmentsFromConsoleLog = [
      {
        id: 'item_252_1_86',
        name: 'Pomodoro Focus Session (Tailored for 125min)',
        domain: 'productive_practical',
        percentage: 16.666666666666668,
        startAngle: 0,
        endAngle: Math.PI / 3,
        color: '#27AE60',
        extremityColor: '#f58231',
        // centerColor is missing - this is the issue from console logs
      },
      {
        id: 'item_252_5_64',
        name: 'Emotional Anchoring Technique (Tailored for 125min)',
        domain: 'emotional',
        percentage: 16.666666666666668,
        startAngle: 2 * Math.PI / 3,
        endAngle: Math.PI,
        color: '#9B59B6',
        extremityColor: '#f58231',
        // centerColor is missing - this is the issue from console logs
      }
    ];

    // Simulate the renderInnerSegment logic exactly
    const results = segmentsFromConsoleLog.map((segment, index) => {
      let domainColor = (segment as any).centerColor;

      if (!domainColor && (segment as any).domain) {
        try {
          domainColor = getDomainColor((segment as any).domain);
        } catch (error) {
          console.warn('Could not get domain color for inner segment:', error);
          domainColor = '#000000'; // This is what causes the fallback in console logs
        }
      }

      if (!domainColor) {
        domainColor = '#000000';
      }

      const validDomainColor = domainColor?.match(/^#[0-9A-Fa-f]{6}$/) ? domainColor : '#E74C3C';

      console.log(`[RENDERER] 🎨 Inner segment ${index}: domain color=${validDomainColor}`);

      return validDomainColor;
    });

    console.log('Results from console log reproduction:', results);

    // If centerColor is missing but getDomainColor works, we should get proper colors
    // But if there's an issue with getDomainColor import/execution, we get #000000
    const allBlack = results.every(color => color === '#000000');

    // This should be false - we should NOT get all black colors
    expect(allBlack).toBe(false);
  });

  it('should verify that getDomainColor function works correctly', () => {
    // Test that the getDomainColor function itself works
    const productiveColor = getDomainColor('productive_practical');
    const generalColor = getDomainColor('general');
    const emotionalColor = getDomainColor('emotional');

    console.log('Domain color test results:');
    console.log(`  productive_practical: ${productiveColor}`);
    console.log(`  general: ${generalColor}`);
    console.log(`  emotional: ${emotionalColor}`);

    expect(productiveColor).toBe('#27AE60');
    expect(generalColor).toBe('#52C41A');
    expect(emotionalColor).toBe('#9B59B6');
  });

  it('should detect when centerColor is missing from segments', () => {
    // This test verifies that the issue is centerColor not being provided
    const segmentsWithoutCenterColor = testSegments.filter(segment =>
      !(segment as any).centerColor
    );

    console.log('Segments without centerColor:', segmentsWithoutCenterColor.length);
    console.log('Total segments:', testSegments.length);

    // All test segments should be missing centerColor (this is the issue)
    expect(segmentsWithoutCenterColor.length).toBe(testSegments.length);
  });

  it('should verify the fix preserves centerColor in enhanced segments', () => {
    console.log('=== TESTING CENTERCOLOR PRESERVATION FIX ===');

    // Create segments with centerColor and extremityColor (as enhanced by app-shell.ts)
    const enhancedSegments = [
      {
        id: 'item_252_1_86',
        name: 'Pomodoro Focus Session (Tailored for 125min)',
        domain: 'productive_practical',
        percentage: 16.666666666666668,
        startAngle: 0,
        endAngle: Math.PI / 3,
        color: '#27AE60',
        centerColor: '#27AE60',  // FIXED: This should be preserved
        extremityColor: '#f58231',
      },
      {
        id: 'item_252_5_64',
        name: 'Emotional Anchoring Technique (Tailored for 125min)',
        domain: 'emotional',
        percentage: 16.666666666666668,
        startAngle: 2 * Math.PI / 3,
        endAngle: Math.PI,
        color: '#9B59B6',
        centerColor: '#9B59B6',  // FIXED: This should be preserved
        extremityColor: '#f58231',
      }
    ];

    // Simulate the FIXED renderInnerSegment logic
    const results = enhancedSegments.map((segment, index) => {
      let domainColor = (segment as any).centerColor;

      if (!domainColor && (segment as any).domain) {
        try {
          domainColor = getDomainColor((segment as any).domain);
        } catch (error) {
          console.warn('Could not get domain color for inner segment:', error);
          domainColor = '#000000';
        }
      }

      if (!domainColor) {
        domainColor = '#000000';
      }

      const validDomainColor = domainColor?.match(/^#[0-9A-Fa-f]{6}$/) ? domainColor : '#E74C3C';

      console.log(`[RENDERER] 🎨 Inner segment ${index}: domain color=${validDomainColor}`);

      return {
        segmentIndex: index,
        segmentName: segment.name,
        domain: segment.domain,
        centerColor: (segment as any).centerColor,
        finalColor: validDomainColor
      };
    });

    console.log('Results from FIXED segments:', results);

    // With the fix, we should get proper domain colors, not fallback colors
    const hasProperColors = results.every(result =>
      result.finalColor !== '#000000' && result.finalColor !== '#E74C3C'
    );

    const allColorsMatch = results.every(result =>
      result.finalColor === result.centerColor
    );

    console.log('Has proper colors (not fallback):', hasProperColors);
    console.log('All colors match centerColor:', allColorsMatch);

    // This should PASS with the fix
    expect(hasProperColors).toBe(true);
    expect(allColorsMatch).toBe(true);
  });
});
