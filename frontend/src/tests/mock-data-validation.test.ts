/**
 * Test to reproduce the mock data validation failure issue
 * 
 * This test reproduces the exact issue where the debug panel "load mock data"
 * fails because the mock data structure doesn't match the expected format.
 */

import { describe, it, expect } from 'vitest';

// Mock the wheel state machine validation logic
interface WheelSegment {
  id: string;
  name: string;
  text: string;
  percentage: number;
  domain: string;
  color?: string;
  activity_tailored_id?: string;
  description?: string;
}

interface WheelData {
  segments?: WheelSegment[];
  items?: WheelSegment[];
  wheelId: string;
  createdAt: string;
}

// Simulate the validation logic from wheel-state-machine.ts
function isValidSegment(segment: any): boolean {
  // Check required properties
  if (!segment.id || !segment.name || !segment.percentage) {
    console.log('🚨 Invalid segment: missing required properties', segment);
    return false;
  }

  // Check for color property (this is what's failing)
  if (!segment.color) {
    console.log('🚨 Invalid segment: missing color', segment);
    return false;
  }

  return true;
}

function isValidWheelData(wheelData: WheelData): boolean {
  // Check if we have segments or items
  const segments = wheelData.segments || wheelData.items || [];
  
  if (segments.length === 0) {
    console.log('🚨 Invalid wheel data: no segments');
    return false;
  }

  // Validate each segment
  for (const segment of segments) {
    if (!isValidSegment(segment)) {
      return false;
    }
  }

  return true;
}

describe('Mock Data Validation Issue', () => {
  it('should reproduce the mock data validation failure (BEFORE FIX)', () => {
    console.log('=== REPRODUCING MOCK DATA VALIDATION FAILURE (BEFORE FIX) ===');

    // This is the OLD mock data structure from app-shell.ts handleLoadMockedWheel (BEFORE FIX)
    const mockWheelDataBeforeFix: WheelData = {
      segments: [
        {
          id: 'item_mock_1_86',
          name: '🏃‍♂️ Pomodoro Focus Session (Tailored for 125min)',
          text: '🏃‍♂️ Pomodoro Focus Session (Tailored for 125min)',
          percentage: 16.67,
          domain: 'productive_practical',
          activity_tailored_id: '86',
          description: 'Start your day with an energizing 20-minute jog around the neighborhood'
          // Note: color property is MISSING - this was the issue
        },
        {
          id: 'item_mock_2_155',
          name: '📚 Advanced Urban Parkour (Tailored for 125min)',
          text: '📚 Advanced Urban Parkour (Tailored for 125min)',
          percentage: 16.67,
          domain: 'general',
          activity_tailored_id: '155',
          description: 'Focus on your most challenging subject for 45 minutes with breaks'
          // Note: color property is MISSING - this is the issue
        },
        {
          id: 'item_mock_3_124',
          name: '🎨 App-Guided Movement Session (Tailored for 125min)',
          text: '🎨 App-Guided Movement Session (Tailored for 125min)',
          percentage: 16.67,
          domain: 'productive_practical',
          activity_tailored_id: '124',
          description: 'Express yourself through art - sketch, paint, or digital drawing'
          // Note: color property is MISSING - this is the issue
        },
        {
          id: 'item_mock_4_84',
          name: '🧘‍♀️ Deliberate Practice Session (Tailored for 125min)',
          text: '🧘‍♀️ Deliberate Practice Session (Tailored for 125min)',
          percentage: 16.67,
          domain: 'productive_practical',
          activity_tailored_id: '84',
          description: 'Find inner peace with a 15-minute mindfulness session'
          // Note: color property is MISSING - this is the issue
        },
        {
          id: 'item_mock_5_64',
          name: '🍳 Emotional Anchoring Technique (Tailored for 125min)',
          text: '🍳 Emotional Anchoring Technique (Tailored for 125min)',
          percentage: 16.67,
          domain: 'emotional',
          activity_tailored_id: '64',
          description: 'Try a new healthy recipe and enjoy the process of cooking'
          // Note: color property is MISSING - this is the issue
        },
        {
          id: 'item_mock_6_121',
          name: '🎵 Intentional Break Transition (Tailored for 125min)',
          text: '🎵 Intentional Break Transition (Tailored for 125min)',
          percentage: 16.66,
          domain: 'productive_practical',
          activity_tailored_id: '121',
          description: 'Practice your instrument or learn a new song for 30 minutes'
          // Note: color property is MISSING - this is the issue
        }
      ],
      wheelId: 'mock-wheel-123',
      createdAt: new Date().toISOString()
    };

    console.log('Mock wheel data structure (BEFORE FIX):', {
      hasSegments: !!mockWheelDataBeforeFix.segments,
      segmentCount: mockWheelDataBeforeFix.segments?.length || 0,
      wheelId: mockWheelDataBeforeFix.wheelId
    });

    // Test individual segment validation
    const segments = mockWheelDataBeforeFix.segments || [];
    console.log('=== INDIVIDUAL SEGMENT VALIDATION (BEFORE FIX) ===');

    segments.forEach((segment, index) => {
      const isValid = isValidSegment(segment);
      console.log(`Segment ${index + 1}: ${segment.name}`);
      console.log(`  - id: ${segment.id}`);
      console.log(`  - name: ${segment.name}`);
      console.log(`  - percentage: ${segment.percentage}`);
      console.log(`  - domain: ${segment.domain}`);
      console.log(`  - color: ${segment.color || 'MISSING'}`);
      console.log(`  - valid: ${isValid}`);
    });

    // Test overall wheel data validation
    const isValidWheelBeforeFix = isValidWheelData(mockWheelDataBeforeFix);
    console.log('=== OVERALL VALIDATION (BEFORE FIX) ===');
    console.log(`Wheel data valid: ${isValidWheelBeforeFix}`);

    // This test should FAIL because the old mock data was invalid
    expect(isValidWheelBeforeFix).toBe(false);
  });

  it('should validate the FIXED mock data structure', () => {
    console.log('=== TESTING FIXED MOCK DATA STRUCTURE ===');

    // This is the FIXED mock data structure from app-shell.ts handleLoadMockedWheel (AFTER FIX)
    const fixedMockWheelData: WheelData = {
      segments: [
        {
          id: 'item_mock_1_86',
          name: '🏃‍♂️ Pomodoro Focus Session (Tailored for 125min)',
          text: '🏃‍♂️ Pomodoro Focus Session (Tailored for 125min)',
          percentage: 16.67,
          domain: 'productive_practical',
          activity_tailored_id: '86',
          description: 'Start your day with an energizing 20-minute jog around the neighborhood',
          color: '#27AE60'  // FIXED: Added color property
        },
        {
          id: 'item_mock_2_155',
          name: '📚 Advanced Urban Parkour (Tailored for 125min)',
          text: '📚 Advanced Urban Parkour (Tailored for 125min)',
          percentage: 16.67,
          domain: 'general',
          activity_tailored_id: '155',
          description: 'Focus on your most challenging subject for 45 minutes with breaks',
          color: '#52C41A'  // FIXED: Added color property
        },
        {
          id: 'item_mock_3_124',
          name: '🎨 App-Guided Movement Session (Tailored for 125min)',
          text: '🎨 App-Guided Movement Session (Tailored for 125min)',
          percentage: 16.67,
          domain: 'productive_practical',
          activity_tailored_id: '124',
          description: 'Express yourself through art - sketch, paint, or digital drawing',
          color: '#27AE60'  // FIXED: Added color property
        }
      ],
      wheelId: 'mock-wheel-123',
      createdAt: new Date().toISOString()
    };

    const isValidWheelFixed = isValidWheelData(fixedMockWheelData);
    console.log(`Fixed mock wheel data valid: ${isValidWheelFixed}`);

    // This should PASS because the fixed mock data is valid
    expect(isValidWheelFixed).toBe(true);
  });

  it('should show what valid mock data should look like', () => {
    console.log('=== VALID MOCK DATA STRUCTURE ===');
    
    // This is what the mock data SHOULD look like with colors
    const validMockWheelData: WheelData = {
      segments: [
        {
          id: 'item_mock_1_86',
          name: '🏃‍♂️ Pomodoro Focus Session (Tailored for 125min)',
          text: '🏃‍♂️ Pomodoro Focus Session (Tailored for 125min)',
          percentage: 16.67,
          domain: 'productive_practical',
          activity_tailored_id: '86',
          description: 'Start your day with an energizing 20-minute jog around the neighborhood',
          color: '#27AE60' // FIXED: Added color property
        },
        {
          id: 'item_mock_2_155',
          name: '📚 Advanced Urban Parkour (Tailored for 125min)',
          text: '📚 Advanced Urban Parkour (Tailored for 125min)',
          percentage: 16.67,
          domain: 'general',
          activity_tailored_id: '155',
          description: 'Focus on your most challenging subject for 45 minutes with breaks',
          color: '#52C41A' // FIXED: Added color property
        },
        {
          id: 'item_mock_3_124',
          name: '🎨 App-Guided Movement Session (Tailored for 125min)',
          text: '🎨 App-Guided Movement Session (Tailored for 125min)',
          percentage: 16.67,
          domain: 'productive_practical',
          activity_tailored_id: '124',
          description: 'Express yourself through art - sketch, paint, or digital drawing',
          color: '#27AE60' // FIXED: Added color property
        }
      ],
      wheelId: 'mock-wheel-123',
      createdAt: new Date().toISOString()
    };

    const isValidWheel = isValidWheelData(validMockWheelData);
    console.log(`Valid mock wheel data: ${isValidWheel}`);

    expect(isValidWheel).toBe(true);
  });
});
