/**
 * Tests for the frontend error handling system
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { <PERSON><PERSON>r<PERSON><PERSON><PERSON> } from '../services/error-handler.js';
import { ErrorClassificationService } from '../services/error-classification.js';
import { ErrorReportingService } from '../services/error-reporting.js';
import { ErrorNotificationManager } from '../services/error-notification-manager.js';
import { ErrorType, ErrorLevel, NotificationType } from '../types/error-types.js';

// Mock fetch for testing
global.fetch = vi.fn();

describe('Error Classification Service', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should classify connection errors correctly', () => {
    const error = new Error('WebSocket connection failed');
    const context = { component: 'websocket-manager' };

    const classified = ErrorClassificationService.classifyError(error, context);

    // The error classification prioritizes "connection" over "websocket" in the message
    // Since the message contains "connection", it gets classified as CONNECTION_ERROR
    expect(classified.type).toBe(ErrorType.CONNECTION_ERROR);
    expect(classified.level).toBe(ErrorLevel.TEMPORARY);
    expect(classified.component).toBe('websocket-manager');
    expect(classified.message).toBe('WebSocket connection failed');
  });

  it('should classify WebSocket errors correctly', () => {
    const error = new Error('WebSocket error occurred');
    const context = { component: 'websocket-manager' };

    const classified = ErrorClassificationService.classifyError(error, context);

    expect(classified.type).toBe(ErrorType.WEBSOCKET_ERROR);
    expect(classified.level).toBe(ErrorLevel.TEMPORARY);
    expect(classified.component).toBe('websocket-manager');
  });

  it('should classify validation errors correctly', () => {
    const error = new Error('Invalid input format');
    const context = { component: 'input-validator' };

    const classified = ErrorClassificationService.classifyError(error, context);

    expect(classified.type).toBe(ErrorType.VALIDATION_ERROR);
    expect(classified.level).toBe(ErrorLevel.NON_CRITICAL);
    expect(classified.component).toBe('input-validator');
  });

  it('should classify wheel errors as critical', () => {
    const error = new Error('Wheel rendering failed');
    const context = { component: 'wheel-component' };
    
    const classified = ErrorClassificationService.classifyError(error, context);
    
    expect(classified.type).toBe(ErrorType.WHEEL_ERROR);
    expect(classified.level).toBe(ErrorLevel.CRITICAL);
    expect(classified.component).toBe('wheel-component');
  });

  it('should get correct notification type for error levels', () => {
    const temporaryError = { level: ErrorLevel.TEMPORARY, type: ErrorType.CONNECTION_ERROR } as any;
    const nonCriticalError = { level: ErrorLevel.NON_CRITICAL, type: ErrorType.VALIDATION_ERROR } as any;
    const criticalError = { level: ErrorLevel.CRITICAL, type: ErrorType.WHEEL_ERROR } as any;
    
    expect(ErrorClassificationService.getNotificationType(temporaryError)).toBe(NotificationType.BANNER);
    expect(ErrorClassificationService.getNotificationType(nonCriticalError)).toBe(NotificationType.POPUP);
    expect(ErrorClassificationService.getNotificationType(criticalError)).toBe(NotificationType.PERSISTENT);
  });

  it('should determine auto-retry capability correctly', () => {
    const connectionError = { 
      type: ErrorType.CONNECTION_ERROR, 
      retryCount: 1 
    } as any;
    const wheelError = { 
      type: ErrorType.WHEEL_ERROR, 
      retryCount: 0 
    } as any;
    
    expect(ErrorClassificationService.canAutoRetry(connectionError)).toBe(true);
    expect(ErrorClassificationService.canAutoRetry(wheelError)).toBe(false);
  });

  it('should check if errors are similar for deduplication', () => {
    const error1 = {
      type: ErrorType.CONNECTION_ERROR,
      component: 'websocket-manager',
      message: 'Connection failed'
    } as any;
    
    const error2 = {
      type: ErrorType.CONNECTION_ERROR,
      component: 'websocket-manager',
      message: 'Connection failed'
    } as any;
    
    const error3 = {
      type: ErrorType.VALIDATION_ERROR,
      component: 'input-validator',
      message: 'Invalid input'
    } as any;
    
    expect(ErrorClassificationService.areErrorsSimilar(error1, error2)).toBe(true);
    expect(ErrorClassificationService.areErrorsSimilar(error1, error3)).toBe(false);
  });
});

describe('Error Reporting Service', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Mock successful response
    (global.fetch as any).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ success: true, event_id: 'test-event-123' })
    });
  });

  it('should report error to backend successfully', async () => {
    const error = {
      id: 'test-error-1',
      type: ErrorType.CONNECTION_ERROR,
      level: ErrorLevel.TEMPORARY,
      message: 'Connection failed',
      timestamp: new Date(),
      component: 'websocket-manager'
    } as any;
    
    const success = await ErrorReportingService.reportError(error);
    
    expect(success).toBe(true);
    expect(global.fetch).toHaveBeenCalledWith('/api/track-event/', expect.objectContaining({
      method: 'POST',
      headers: expect.objectContaining({
        'Content-Type': 'application/json'
      }),
      body: expect.stringContaining('frontend_error_temporary')
    }));
  });

  it('should handle reporting failures gracefully', async () => {
    // Mock failed response
    (global.fetch as any).mockResolvedValue({
      ok: false,
      status: 500,
      statusText: 'Internal Server Error'
    });
    
    const error = {
      id: 'test-error-2',
      type: ErrorType.VALIDATION_ERROR,
      level: ErrorLevel.NON_CRITICAL,
      message: 'Validation failed',
      timestamp: new Date(),
      component: 'form-validator'
    } as any;
    
    const success = await ErrorReportingService.reportError(error);
    
    expect(success).toBe(false);
  });

  it('should retry failed requests', async () => {
    // Mock first call to fail, second to succeed
    (global.fetch as any)
      .mockRejectedValueOnce(new Error('Network error'))
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ success: true })
      });
    
    const error = {
      id: 'test-error-3',
      type: ErrorType.CONNECTION_ERROR,
      level: ErrorLevel.TEMPORARY,
      message: 'Network error',
      timestamp: new Date(),
      component: 'api-client'
    } as any;
    
    const success = await ErrorReportingService.reportError(error);
    
    expect(success).toBe(true);
    expect(global.fetch).toHaveBeenCalledTimes(2);
  });

  it('should queue errors for later reporting when offline', () => {
    const error = {
      id: 'test-error-4',
      type: ErrorType.CONNECTION_ERROR,
      level: ErrorLevel.TEMPORARY,
      message: 'Offline error',
      timestamp: new Date(),
      component: 'offline-handler'
    } as any;
    
    // Mock localStorage
    const localStorageMock = {
      getItem: vi.fn(() => '[]'),
      setItem: vi.fn(),
      removeItem: vi.fn()
    };
    Object.defineProperty(window, 'localStorage', { value: localStorageMock });
    
    ErrorReportingService.queueErrorForLaterReporting(error);
    
    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      'error_report_queue',
      expect.stringContaining('test-error-4')
    );
  });
});

describe('Error Handler', () => {
  let errorHandler: ErrorHandler;

  beforeEach(() => {
    vi.clearAllMocks();
    errorHandler = ErrorHandler.getInstance();
    
    // Mock DOM events
    Object.defineProperty(window, 'dispatchEvent', {
      value: vi.fn(),
      writable: true
    });
  });

  afterEach(() => {
    errorHandler.clearErrors();
  });

  it('should handle and classify errors', async () => {
    const rawError = new Error('Test error');
    const context = { component: 'test-component' };
    
    await errorHandler.handleError(rawError, context);
    
    const activeErrors = errorHandler.getActiveErrors();
    expect(activeErrors).toHaveLength(1);
    expect(activeErrors[0].message).toBe('Test error');
    expect(activeErrors[0].component).toBe('test-component');
  });

  it('should deduplicate similar errors', async () => {
    const error1 = new Error('Duplicate error');
    const error2 = new Error('Duplicate error');
    const context = { component: 'test-component' };
    
    await errorHandler.handleError(error1, context);
    await errorHandler.handleError(error2, context);
    
    const activeErrors = errorHandler.getActiveErrors();
    expect(activeErrors).toHaveLength(1); // Should be deduplicated
  });

  it('should emit error events', async () => {
    const error = new Error('Event test error');
    const context = { component: 'event-test' };
    
    await errorHandler.handleError(error, context);
    
    expect(window.dispatchEvent).toHaveBeenCalledWith(
      expect.objectContaining({
        type: 'app-error'
      })
    );
  });

  it('should clear all errors', async () => {
    const error1 = new Error('Error 1');
    const error2 = new Error('Error 2');
    
    await errorHandler.handleError(error1);
    await errorHandler.handleError(error2);
    
    expect(errorHandler.getActiveErrors()).toHaveLength(2);
    
    errorHandler.clearErrors();
    
    expect(errorHandler.getActiveErrors()).toHaveLength(0);
  });
});

describe('Error Notification Manager', () => {
  let notificationManager: ErrorNotificationManager;

  beforeEach(() => {
    vi.clearAllMocks();
    notificationManager = ErrorNotificationManager.getInstance();
    
    // Mock DOM methods
    Object.defineProperty(document, 'querySelector', {
      value: vi.fn(() => null),
      writable: true
    });
    
    Object.defineProperty(document, 'createElement', {
      value: vi.fn(() => ({
        style: {},
        innerHTML: '',
        remove: vi.fn()
      })),
      writable: true
    });
    
    Object.defineProperty(document.body, 'appendChild', {
      value: vi.fn(),
      writable: true
    });
  });

  it('should show and dismiss notifications', () => {
    const error = {
      id: 'test-error',
      type: ErrorType.CONNECTION_ERROR,
      level: ErrorLevel.TEMPORARY,
      message: 'Test notification',
      timestamp: new Date()
    } as any;
    
    const notification = {
      id: 'notification-1',
      error,
      notificationType: NotificationType.BANNER,
      dismissible: true,
      showInProduction: true,
      showInDebug: true
    } as any;
    
    notificationManager.show(notification);
    expect(notificationManager.getActive()).toHaveLength(1);
    
    notificationManager.dismiss('notification-1');
    expect(notificationManager.getActive()).toHaveLength(0);
  });

  it('should dismiss all notifications', () => {
    const error1 = { id: 'error-1', level: ErrorLevel.TEMPORARY } as any;
    const error2 = { id: 'error-2', level: ErrorLevel.NON_CRITICAL } as any;
    
    const notification1 = {
      id: 'notification-1',
      error: error1,
      notificationType: NotificationType.BANNER,
      dismissible: true,
      showInProduction: true,
      showInDebug: true
    } as any;
    
    const notification2 = {
      id: 'notification-2',
      error: error2,
      notificationType: NotificationType.POPUP,
      dismissible: true,
      showInProduction: true,
      showInDebug: true
    } as any;
    
    notificationManager.show(notification1);
    notificationManager.show(notification2);
    expect(notificationManager.getActive()).toHaveLength(2);
    
    notificationManager.dismissAll();
    expect(notificationManager.getActive()).toHaveLength(0);
  });

  it('should provide notification statistics', () => {
    const bannerError = { id: 'banner-error', level: ErrorLevel.TEMPORARY } as any;
    const popupError = { id: 'popup-error', level: ErrorLevel.NON_CRITICAL } as any;
    const persistentError = { id: 'persistent-error', level: ErrorLevel.CRITICAL } as any;
    
    const bannerNotification = {
      id: 'banner-notification',
      error: bannerError,
      notificationType: NotificationType.BANNER,
      dismissible: true,
      showInProduction: true,
      showInDebug: true
    } as any;
    
    const popupNotification = {
      id: 'popup-notification',
      error: popupError,
      notificationType: NotificationType.POPUP,
      dismissible: true,
      showInProduction: true,
      showInDebug: true
    } as any;
    
    const persistentNotification = {
      id: 'persistent-notification',
      error: persistentError,
      notificationType: NotificationType.PERSISTENT,
      dismissible: false,
      showInProduction: true,
      showInDebug: true
    } as any;
    
    notificationManager.show(bannerNotification);
    notificationManager.show(popupNotification);
    notificationManager.show(persistentNotification);
    
    const stats = notificationManager.getStats();
    
    expect(stats.total).toBe(3);
    expect(stats.byType[NotificationType.BANNER]).toBe(1);
    expect(stats.byType[NotificationType.POPUP]).toBe(1);
    expect(stats.byType[NotificationType.PERSISTENT]).toBe(1);
    expect(stats.byLevel[ErrorLevel.TEMPORARY]).toBe(1);
    expect(stats.byLevel[ErrorLevel.NON_CRITICAL]).toBe(1);
    expect(stats.byLevel[ErrorLevel.CRITICAL]).toBe(1);
  });
});
