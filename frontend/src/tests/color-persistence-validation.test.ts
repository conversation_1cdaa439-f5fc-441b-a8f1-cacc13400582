/**
 * Color Persistence Validation Tests
 * These tests verify that colors remain consistent when wheel items are added/removed
 */

import { describe, it, expect } from 'vitest';
import { generateWheelColors } from '../services/domainColorService.js';

describe('Color Persistence Validation', () => {
  
  it('should maintain consistent colors when items are removed from wheel', () => {
    const originalActivities = [
      { id: 'act1', name: 'Running', domain: 'physical' },
      { id: 'act2', name: 'Reading', domain: 'intellectual' },
      { id: 'act3', name: 'Painting', domain: 'creative' },
      { id: 'act4', name: 'Socializing', domain: 'social' }
    ];

    const activitiesAfterRemoval = [
      { id: 'act1', name: 'Running', domain: 'physical' },
      { id: 'act3', name: 'Painting', domain: 'creative' }
    ];

    console.log('=== COLOR PERSISTENCE TEST ===');
    
    // Generate colors for original set
    const originalColors = generateWheelColors(originalActivities, { dualColorMode: true });
    console.log('Original colors:');
    originalColors.forEach(c => {
      console.log(`  ${c.activityId}: center=${c.centerColor}, extremity=${c.extremityColor}`);
    });

    // Generate colors after removal
    const colorsAfterRemoval = generateWheelColors(activitiesAfterRemoval, { dualColorMode: true });
    console.log('Colors after removal:');
    colorsAfterRemoval.forEach(c => {
      console.log(`  ${c.activityId}: center=${c.centerColor}, extremity=${c.extremityColor}`);
    });

    // Find colors for activities that remain
    const act1Original = originalColors.find(c => c.activityId === 'act1');
    const act1AfterRemoval = colorsAfterRemoval.find(c => c.activityId === 'act1');
    const act3Original = originalColors.find(c => c.activityId === 'act3');
    const act3AfterRemoval = colorsAfterRemoval.find(c => c.activityId === 'act3');

    console.log('Persistence check:');
    console.log(`  act1: ${act1Original?.extremityColor} -> ${act1AfterRemoval?.extremityColor} (${act1Original?.extremityColor === act1AfterRemoval?.extremityColor ? 'SAME' : 'DIFFERENT'})`);
    console.log(`  act3: ${act3Original?.extremityColor} -> ${act3AfterRemoval?.extremityColor} (${act3Original?.extremityColor === act3AfterRemoval?.extremityColor ? 'SAME' : 'DIFFERENT'})`);

    // Colors should remain the same for activities that weren't removed
    expect(act1Original?.centerColor).toBe(act1AfterRemoval?.centerColor);
    expect(act1Original?.extremityColor).toBe(act1AfterRemoval?.extremityColor);
    expect(act3Original?.centerColor).toBe(act3AfterRemoval?.centerColor);
    expect(act3Original?.extremityColor).toBe(act3AfterRemoval?.extremityColor);
  });

  it('should maintain consistent colors when items are added to wheel', () => {
    const initialActivities = [
      { id: 'act1', name: 'Running', domain: 'physical' },
      { id: 'act2', name: 'Reading', domain: 'intellectual' }
    ];

    const activitiesAfterAddition = [
      { id: 'act1', name: 'Running', domain: 'physical' },
      { id: 'act2', name: 'Reading', domain: 'intellectual' },
      { id: 'act3', name: 'Painting', domain: 'creative' }
    ];

    console.log('=== COLOR ADDITION TEST ===');
    
    // Generate colors for initial set
    const initialColors = generateWheelColors(initialActivities, { dualColorMode: true });
    console.log('Initial colors:');
    initialColors.forEach(c => {
      console.log(`  ${c.activityId}: center=${c.centerColor}, extremity=${c.extremityColor}`);
    });

    // Generate colors after addition
    const colorsAfterAddition = generateWheelColors(activitiesAfterAddition, { dualColorMode: true });
    console.log('Colors after addition:');
    colorsAfterAddition.forEach(c => {
      console.log(`  ${c.activityId}: center=${c.centerColor}, extremity=${c.extremityColor}`);
    });

    // Find colors for activities that were already there
    const act1Initial = initialColors.find(c => c.activityId === 'act1');
    const act1AfterAddition = colorsAfterAddition.find(c => c.activityId === 'act1');
    const act2Initial = initialColors.find(c => c.activityId === 'act2');
    const act2AfterAddition = colorsAfterAddition.find(c => c.activityId === 'act2');

    console.log('Addition persistence check:');
    console.log(`  act1: ${act1Initial?.extremityColor} -> ${act1AfterAddition?.extremityColor} (${act1Initial?.extremityColor === act1AfterAddition?.extremityColor ? 'SAME' : 'DIFFERENT'})`);
    console.log(`  act2: ${act2Initial?.extremityColor} -> ${act2AfterAddition?.extremityColor} (${act2Initial?.extremityColor === act2AfterAddition?.extremityColor ? 'SAME' : 'DIFFERENT'})`);

    // Colors should remain the same for existing activities when new ones are added
    expect(act1Initial?.centerColor).toBe(act1AfterAddition?.centerColor);
    expect(act1Initial?.extremityColor).toBe(act1AfterAddition?.extremityColor);
    expect(act2Initial?.centerColor).toBe(act2AfterAddition?.centerColor);
    expect(act2Initial?.extremityColor).toBe(act2AfterAddition?.extremityColor);
  });

  it('should maintain consistent colors with percentage changes', () => {
    const activitiesOriginal = [
      { id: 'act1', name: 'Running', domain: 'physical', percentage: 40 },
      { id: 'act2', name: 'Reading', domain: 'intellectual', percentage: 60 }
    ];

    const activitiesWithChangedPercentages = [
      { id: 'act1', name: 'Running', domain: 'physical', percentage: 70 },
      { id: 'act2', name: 'Reading', domain: 'intellectual', percentage: 30 }
    ];

    console.log('=== PERCENTAGE CHANGE TEST ===');
    
    // Generate colors with original percentages
    const originalColors = generateWheelColors(activitiesOriginal, { dualColorMode: true });
    console.log('Original colors with percentages:');
    originalColors.forEach(c => {
      console.log(`  ${c.activityId}: ${c.percentage}% - center=${c.centerColor}, extremity=${c.extremityColor}`);
    });

    // Generate colors with changed percentages
    const colorsWithChangedPercentages = generateWheelColors(activitiesWithChangedPercentages, { dualColorMode: true });
    console.log('Colors with changed percentages:');
    colorsWithChangedPercentages.forEach(c => {
      console.log(`  ${c.activityId}: ${c.percentage}% - center=${c.centerColor}, extremity=${c.extremityColor}`);
    });

    // Find colors for comparison
    const act1Original = originalColors.find(c => c.activityId === 'act1');
    const act1Changed = colorsWithChangedPercentages.find(c => c.activityId === 'act1');
    const act2Original = originalColors.find(c => c.activityId === 'act2');
    const act2Changed = colorsWithChangedPercentages.find(c => c.activityId === 'act2');

    console.log('Percentage change persistence check:');
    console.log(`  act1: ${act1Original?.extremityColor} -> ${act1Changed?.extremityColor} (${act1Original?.extremityColor === act1Changed?.extremityColor ? 'SAME' : 'DIFFERENT'})`);
    console.log(`  act2: ${act2Original?.extremityColor} -> ${act2Changed?.extremityColor} (${act2Original?.extremityColor === act2Changed?.extremityColor ? 'SAME' : 'DIFFERENT'})`);

    // Colors should remain the same even when percentages change (colors are based on ID, not percentage)
    expect(act1Original?.centerColor).toBe(act1Changed?.centerColor);
    expect(act1Original?.extremityColor).toBe(act1Changed?.extremityColor);
    expect(act2Original?.centerColor).toBe(act2Changed?.centerColor);
    expect(act2Original?.extremityColor).toBe(act2Changed?.extremityColor);

    // But percentages should be updated
    expect(act1Changed?.percentage).toBe(70);
    expect(act2Changed?.percentage).toBe(30);
  });

  it('should generate same colors regardless of input order', () => {
    const activitiesOrder1 = [
      { id: 'act1', name: 'Running', domain: 'physical' },
      { id: 'act2', name: 'Reading', domain: 'intellectual' },
      { id: 'act3', name: 'Painting', domain: 'creative' }
    ];

    const activitiesOrder2 = [
      { id: 'act3', name: 'Painting', domain: 'creative' },
      { id: 'act1', name: 'Running', domain: 'physical' },
      { id: 'act2', name: 'Reading', domain: 'intellectual' }
    ];

    console.log('=== INPUT ORDER TEST ===');
    
    // Generate colors with first order
    const colorsOrder1 = generateWheelColors(activitiesOrder1, { dualColorMode: true });
    console.log('Colors with order 1:');
    colorsOrder1.forEach(c => {
      console.log(`  ${c.activityId}: center=${c.centerColor}, extremity=${c.extremityColor}`);
    });

    // Generate colors with second order
    const colorsOrder2 = generateWheelColors(activitiesOrder2, { dualColorMode: true });
    console.log('Colors with order 2:');
    colorsOrder2.forEach(c => {
      console.log(`  ${c.activityId}: center=${c.centerColor}, extremity=${c.extremityColor}`);
    });

    // Find colors for each activity in both orders
    const act1Order1 = colorsOrder1.find(c => c.activityId === 'act1');
    const act1Order2 = colorsOrder2.find(c => c.activityId === 'act1');
    const act2Order1 = colorsOrder1.find(c => c.activityId === 'act2');
    const act2Order2 = colorsOrder2.find(c => c.activityId === 'act2');
    const act3Order1 = colorsOrder1.find(c => c.activityId === 'act3');
    const act3Order2 = colorsOrder2.find(c => c.activityId === 'act3');

    console.log('Order independence check:');
    console.log(`  act1: ${act1Order1?.extremityColor} vs ${act1Order2?.extremityColor} (${act1Order1?.extremityColor === act1Order2?.extremityColor ? 'SAME' : 'DIFFERENT'})`);
    console.log(`  act2: ${act2Order1?.extremityColor} vs ${act2Order2?.extremityColor} (${act2Order1?.extremityColor === act2Order2?.extremityColor ? 'SAME' : 'DIFFERENT'})`);
    console.log(`  act3: ${act3Order1?.extremityColor} vs ${act3Order2?.extremityColor} (${act3Order1?.extremityColor === act3Order2?.extremityColor ? 'SAME' : 'DIFFERENT'})`);

    // Colors should be the same regardless of input order
    expect(act1Order1?.centerColor).toBe(act1Order2?.centerColor);
    expect(act1Order1?.extremityColor).toBe(act1Order2?.extremityColor);
    expect(act2Order1?.centerColor).toBe(act2Order2?.centerColor);
    expect(act2Order1?.extremityColor).toBe(act2Order2?.extremityColor);
    expect(act3Order1?.centerColor).toBe(act3Order2?.centerColor);
    expect(act3Order1?.extremityColor).toBe(act3Order2?.extremityColor);
  });
});
