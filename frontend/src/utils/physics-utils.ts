/**
 * Physics utility functions for the spinning wheel component
 * Provides mathematical calculations and physics helpers
 */

import type { WheelSegment, WheelConfig } from '../components/game-wheel/wheel-types.js';

/**
 * Converts degrees to radians
 */
export function degreesToRadians(degrees: number): number {
  return (degrees * Math.PI) / 180;
}

/**
 * Converts radians to degrees
 */
export function radiansToDegrees(radians: number): number {
  return (radians * 180) / Math.PI;
}

/**
 * Normalizes an angle to be between 0 and 2π
 */
export function normalizeAngle(angle: number): number {
  while (angle < 0) {
    angle += 2 * Math.PI;
  }
  while (angle >= 2 * Math.PI) {
    angle -= 2 * Math.PI;
  }
  return angle;
}

/**
 * Calculates the distance between two points
 */
export function distance(x1: number, y1: number, x2: number, y2: number): number {
  const dx = x2 - x1;
  const dy = y2 - y1;
  return Math.sqrt(dx * dx + dy * dy);
}

/**
 * Calculates the angle between two points
 */
export function angleBetweenPoints(x1: number, y1: number, x2: number, y2: number): number {
  return Math.atan2(y2 - y1, x2 - x1);
}

/**
 * Converts cartesian coordinates to polar coordinates
 */
export function cartesianToPolar(x: number, y: number, centerX: number, centerY: number): { r: number; theta: number } {
  const dx = x - centerX;
  const dy = y - centerY;
  const r = Math.sqrt(dx * dx + dy * dy);
  const theta = Math.atan2(dy, dx);
  return { r, theta: normalizeAngle(theta) };
}

/**
 * Converts polar coordinates to cartesian coordinates
 */
export function polarToCartesian(r: number, theta: number, centerX: number, centerY: number): { x: number; y: number } {
  const x = centerX + r * Math.cos(theta);
  const y = centerY + r * Math.sin(theta);
  return { x, y };
}

/**
 * Calculates wheel segments with proper angles based on percentages
 */
export function calculateSegmentAngles(segments: Omit<WheelSegment, 'startAngle' | 'endAngle' | 'centerAngle'>[]): WheelSegment[] {
  // Normalize percentages to ensure they sum to 100
  const totalPercentage = segments.reduce((sum, segment) => sum + segment.percentage, 0);
  const normalizedSegments = segments.map(segment => ({
    ...segment,
    percentage: (segment.percentage / totalPercentage) * 100
  }));

  let currentAngle = -Math.PI / 2; // Start at top (12 o'clock)

  return normalizedSegments.map(segment => {
    const angleSize = (segment.percentage / 100) * 2 * Math.PI;
    const startAngle = currentAngle;
    const endAngle = currentAngle + angleSize;
    const centerAngle = currentAngle + angleSize / 2;

    currentAngle = endAngle;

    return {
      ...segment,
      startAngle: normalizeAngle(startAngle),
      endAngle: normalizeAngle(endAngle),
      centerAngle: normalizeAngle(centerAngle)
    };
  });
}

/**
 * Subdivides activities into multiple segments and calculates total nail count
 */
export function calculateSubdividedSegments(
  activities: Omit<WheelSegment, 'startAngle' | 'endAngle' | 'centerAngle'>[],
  segmentsPerActivity: number = 8
): { segments: WheelSegment[], totalNailCount: number } {
  console.log(`[PHYSICS] Subdividing ${activities.length} activities into ${segmentsPerActivity} segments each`);

  const totalSegments = activities.length * segmentsPerActivity;
  const totalNailCount = totalSegments; // One nail per segment

  // Normalize percentages to ensure they sum to 100
  const totalPercentage = activities.reduce((sum, activity) => sum + activity.percentage, 0);
  const normalizedActivities = activities.map(activity => ({
    ...activity,
    percentage: (activity.percentage / totalPercentage) * 100
  }));

  let currentAngle = -Math.PI / 2; // Start at top (12 o'clock)
  const segments: WheelSegment[] = [];

  normalizedActivities.forEach((activity, activityIndex) => {
    const activityPercentage = activity.percentage;
    const segmentPercentage = activityPercentage / segmentsPerActivity;
    const segmentAngleSize = (segmentPercentage / 100) * 2 * Math.PI;

    // Create multiple segments for this activity
    for (let segIndex = 0; segIndex < segmentsPerActivity; segIndex++) {
      const startAngle = currentAngle;
      const endAngle = currentAngle + segmentAngleSize;
      const centerAngle = currentAngle + segmentAngleSize / 2;

      segments.push({
        id: `${activity.id}-seg-${segIndex}`,
        text: activity.text,
        percentage: segmentPercentage,
        color: activity.color,
        textColor: activity.textColor,
        startAngle: normalizeAngle(startAngle),
        endAngle: normalizeAngle(endAngle),
        centerAngle: normalizeAngle(centerAngle),
        activityId: activity.id,
        activityIndex: activityIndex,
        segmentIndex: segIndex
      });

      currentAngle = endAngle;
    }
  });

  console.log(`[PHYSICS] Created ${segments.length} segments with ${totalNailCount} nails`);
  return { segments, totalNailCount };
}

/**
 * FIXED 100-SEGMENT SYSTEM: Creates exactly 100 equal segments with proportional activity distribution
 */
export function calculateFixed100Segments(
  activities: Omit<WheelSegment, 'startAngle' | 'endAngle' | 'centerAngle'>[]
): { segments: WheelSegment[], totalNailCount: number } {
  console.log(`[PHYSICS] Creating FIXED 100-segment system for ${activities.length} activities`);

  const TOTAL_SEGMENTS = 100;
  const SEGMENT_ANGLE = (2 * Math.PI) / TOTAL_SEGMENTS; // Each segment is exactly 3.6 degrees

  // Calculate how many segments each activity should get based on percentage
  const totalPercentage = activities.reduce((sum, activity) => sum + activity.percentage, 0);
  const normalizedActivities = activities.map(activity => ({
    ...activity,
    percentage: (activity.percentage / totalPercentage) * 100,
    segmentCount: Math.round((activity.percentage / totalPercentage) * TOTAL_SEGMENTS)
  }));

  // Adjust segment counts to ensure exactly 100 segments total
  let totalAssigned = normalizedActivities.reduce((sum, activity) => sum + activity.segmentCount, 0);
  let adjustment = TOTAL_SEGMENTS - totalAssigned;

  // Distribute remaining segments to largest activities first
  const sortedBySize = [...normalizedActivities].sort((a, b) => b.segmentCount - a.segmentCount);
  for (let i = 0; i < Math.abs(adjustment); i++) {
    const activity = sortedBySize[i % sortedBySize.length];
    activity.segmentCount += adjustment > 0 ? 1 : -1;
  }

  console.log(`[PHYSICS] Segment distribution:`, normalizedActivities.map(a => `${a.text}: ${a.segmentCount} segments`));

  // Create segments with color distribution to avoid adjacent same colors
  const segments: WheelSegment[] = [];
  const segmentPlan = createSegmentDistributionPlan(normalizedActivities);

  let currentAngle = -Math.PI / 2; // Start at top (12 o'clock)

  segmentPlan.forEach((activityIndex, segmentIndex) => {
    const activity = normalizedActivities[activityIndex];
    const startAngle = currentAngle;
    const endAngle = currentAngle + SEGMENT_ANGLE;
    const centerAngle = currentAngle + SEGMENT_ANGLE / 2;

    segments.push({
      id: `${activity.id}-seg-${segmentIndex}`,
      text: activity.text,
      percentage: 1, // Each segment is exactly 1% of the wheel
      color: activity.color,
      textColor: activity.textColor,
      startAngle: normalizeAngle(startAngle),
      endAngle: normalizeAngle(endAngle),
      centerAngle: normalizeAngle(centerAngle),
      activityId: activity.id,
      activityIndex: activityIndex,
      segmentIndex: segmentIndex,
      // CRITICAL FIX: Preserve domain information for inner wheel rendering
      domain: (activity as any).domain

    });

    currentAngle = endAngle;
  });

  console.log(`[PHYSICS] Created exactly ${segments.length} equal segments with ${TOTAL_SEGMENTS} nails`);
  return { segments, totalNailCount: TOTAL_SEGMENTS };
}

/**
 * Creates a distribution plan that avoids adjacent segments of the same color
 */
function createSegmentDistributionPlan(activities: any[]): number[] {
  const plan: number[] = [];
  const remaining = activities.map((activity, index) => ({
    index,
    count: activity.segmentCount,
    color: activity.color
  }));

  // Simple distribution algorithm to minimize adjacent same colors
  while (plan.length < 100) {
    // Find activities that still have segments to place
    const available = remaining.filter(r => r.count > 0);
    if (available.length === 0) break;

    // Try to avoid placing same color as previous segment
    const lastColor = plan.length > 0 ? remaining[plan[plan.length - 1]].color : null;
    const preferred = available.filter(r => r.color !== lastColor);

    // Choose from preferred (different color) or fallback to any available
    const candidates = preferred.length > 0 ? preferred : available;

    // Select the activity with the most remaining segments (greedy approach)
    const selected = candidates.reduce((max, current) =>
      current.count > max.count ? current : max
    );

    plan.push(selected.index);
    selected.count--;
  }

  return plan;
}

/**
 * Calculates nail positions around the wheel perimeter
 */
export function calculateNailPositions(config: WheelConfig): Array<{ x: number; y: number; angle: number }> {
  const positions: Array<{ x: number; y: number; angle: number }> = [];
  const angleStep = (2 * Math.PI) / config.nailCount;
  
  for (let i = 0; i < config.nailCount; i++) {
    const angle = i * angleStep;
    const x = config.centerX + (config.radius + config.nailRadius) * Math.cos(angle);
    const y = config.centerY + (config.radius + config.nailRadius) * Math.sin(angle);
    positions.push({ x, y, angle });
  }
  
  return positions;
}

/**
 * Determines which segment contains a given angle
 */
export function getSegmentAtAngle(angle: number, segments: WheelSegment[]): WheelSegment | null {
  const normalizedAngle = normalizeAngle(angle);

  for (const segment of segments) {
    // Handle segments that cross the 0/2π boundary
    if (segment.startAngle > segment.endAngle) {
      if (normalizedAngle >= segment.startAngle || normalizedAngle <= segment.endAngle) {
        return segment;
      }
    } else {
      if (normalizedAngle >= segment.startAngle && normalizedAngle <= segment.endAngle) {
        return segment;
      }
    }
  }

  return null;
}

/**
 * MECHANISM 1: Enhanced Multi-Point Angle Detection
 * DOES NOT WORK
 * Samples multiple points around ball circumference for robust angle detection
 */
function detectWinnerByMultiPointAngle(
  ballX: number,
  ballY: number,
  centerX: number,
  centerY: number,
  segments: WheelSegment[],
  ballRadius: number = 8
): { segment: WheelSegment | null; confidence: number; debugInfo: any } {
  const samplePoints = 8; // Sample 8 points around ball circumference
  const segmentVotes: Map<string, number> = new Map();
  const angleStep = (2 * Math.PI) / samplePoints;

  const debugInfo: any = {
    method: 'multi-point-angle',
    samplePoints: [],
    votes: {}
  };

  // Sample points around ball circumference
  for (let i = 0; i < samplePoints; i++) {
    const angle = i * angleStep;
    const sampleX = ballX + ballRadius * Math.cos(angle);
    const sampleY = ballY + ballRadius * Math.sin(angle);

    const sampleAngle = getBallAngle(sampleX, sampleY, centerX, centerY);
    const segment = getSegmentAtAngle(sampleAngle, segments);

    debugInfo.samplePoints.push({
      x: sampleX,
      y: sampleY,
      angle: sampleAngle,
      segment: segment?.text || 'None'
    });

    if (segment) {
      const votes = segmentVotes.get(segment.id) || 0;
      segmentVotes.set(segment.id, votes + 1);
    }
  }

  // Find segment with most votes
  let winningSegment: WheelSegment | null = null;
  let maxVotes = 0;

  for (const [segmentId, votes] of segmentVotes.entries()) {
    debugInfo.votes[segmentId] = votes;
    if (votes > maxVotes) {
      maxVotes = votes;
      winningSegment = segments.find(s => s.id === segmentId) || null;
    }
  }

  // Calculate confidence based on vote consensus
  const confidence = maxVotes / samplePoints;

  return {
    segment: winningSegment,
    confidence: confidence,
    debugInfo: debugInfo
  };
}

/**
 * MECHANISM 2: Physics Collision History Analysis
 * Analyzes actual collision events during ball movement
 */
function detectWinnerByCollisionHistory(
  ballX: number,
  ballY: number,
  centerX: number,
  centerY: number,
  segments: WheelSegment[],
  nailPositions: Array<{ x: number; y: number; angle: number }>,
  ballRadius: number = 8,
  collisionHistory?: Array<{ nailIndex: number; timestamp: number }>
): { segment: WheelSegment | null; confidence: number; debugInfo: any } {
  const debugInfo: any = {
    method: 'collision-history',
    collisions: collisionHistory || [],
    nailDistances: []
  };

  // If we have collision history, use it
  if (collisionHistory && collisionHistory.length > 0) {
    const recentCollisions = collisionHistory.slice(-3); // Last 3 collisions
    const segmentVotes: Map<string, number> = new Map();

    for (const collision of recentCollisions) {
      const nail = nailPositions[collision.nailIndex];
      if (nail) {
        const segment = getSegmentAtAngle(nail.angle, segments);
        if (segment) {
          const votes = segmentVotes.get(segment.id) || 0;
          segmentVotes.set(segment.id, votes + 1);
        }
      }
    }

    let winningSegment: WheelSegment | null = null;
    let maxVotes = 0;

    for (const [segmentId, votes] of segmentVotes.entries()) {
      if (votes > maxVotes) {
        maxVotes = votes;
        winningSegment = segments.find(s => s.id === segmentId) || null;
      }
    }

    return {
      segment: winningSegment,
      confidence: maxVotes / recentCollisions.length,
      debugInfo: debugInfo
    };
  }

  // Fallback: Analyze current proximity to nails with enhanced logic
  if (nailPositions.length === 0) {
    return {
      segment: null,
      confidence: 0,
      debugInfo: debugInfo
    };
  }

  let closestNails: Array<{ nail: any; distance: number; segment: WheelSegment | null }> = [];

  for (let i = 0; i < nailPositions.length; i++) {
    const nail = nailPositions[i];
    const dist = distance(ballX, ballY, nail.x, nail.y);
    const segment = getSegmentAtAngle(nail.angle, segments);

    closestNails.push({ nail, distance: dist, segment });
    debugInfo.nailDistances.push({
      nailIndex: i,
      distance: dist,
      segment: segment?.text || 'None'
    });
  }

  // Sort by distance and take closest 3 nails
  closestNails.sort((a, b) => a.distance - b.distance);
  const topNails = closestNails.slice(0, 3);

  // Check if ball is actually touching any nails (more generous threshold)
  const touchingNails = topNails.filter(n => n.distance < ballRadius + 10);

  if (touchingNails.length > 0) {
    // Use the closest touching nail
    const closestTouching = touchingNails[0];
    return {
      segment: closestTouching.segment,
      confidence: 0.85, // Good confidence for proximity
      debugInfo: debugInfo
    };
  }

  // Even if not touching, use the closest nail if it's reasonably close
  const closestNail = topNails[0];
  if (closestNail && closestNail.distance < ballRadius + 30) {
    return {
      segment: closestNail.segment,
      confidence: 0.6, // Lower confidence for distant nails
      debugInfo: debugInfo
    };
  }

  return {
    segment: null,
    confidence: 0,
    debugInfo: debugInfo
  };
}

/**
 * MECHANISM 3: Radial Sector Geometric Analysis
 * Uses robust angle calculation with proper boundary handling
 */
function detectWinnerByRadialSector(
  ballX: number,
  ballY: number,
  centerX: number,
  centerY: number,
  segments: WheelSegment[]
): { segment: WheelSegment | null; confidence: number; debugInfo: any } {
  const debugInfo: any = {
    method: 'radial-sector',
    ballPosition: { x: ballX, y: ballY },
    wheelCenter: { x: centerX, y: centerY },
    sectorTests: []
  };

  // Calculate ball angle using atan2 for proper quadrant handling
  const ballAngle = Math.atan2(ballY - centerY, ballX - centerX);
  const normalizedBallAngle = normalizeAngle(ballAngle);

  debugInfo.ballAngle = normalizedBallAngle;
  debugInfo.ballAngleDegrees = (normalizedBallAngle * 180 / Math.PI);

  // Find the segment containing this angle using a different approach than mechanism 1
  for (const segment of segments) {
    const normalizedStartAngle = normalizeAngle(segment.startAngle);
    const normalizedEndAngle = normalizeAngle(segment.endAngle);

    let inSector = false;

    // Handle angle wrapping around 0/2π boundary
    if (normalizedStartAngle > normalizedEndAngle) {
      // Segment crosses 0 boundary (e.g., from 350° to 10°)
      inSector = (normalizedBallAngle >= normalizedStartAngle) || (normalizedBallAngle <= normalizedEndAngle);
    } else {
      // Normal segment (e.g., from 10° to 50°)
      inSector = (normalizedBallAngle >= normalizedStartAngle) && (normalizedBallAngle <= normalizedEndAngle);
    }

    // Additional check: use segment center angle for tie-breaking
    const distanceFromCenter = Math.abs(normalizedBallAngle - normalizeAngle(segment.centerAngle));
    const adjustedDistance = Math.min(distanceFromCenter, 2 * Math.PI - distanceFromCenter);

    debugInfo.sectorTests.push({
      segmentId: segment.id,
      segmentText: segment.text,
      startAngle: normalizedStartAngle,
      endAngle: normalizedEndAngle,
      centerAngle: normalizeAngle(segment.centerAngle),
      startAngleDegrees: (normalizedStartAngle * 180 / Math.PI),
      endAngleDegrees: (normalizedEndAngle * 180 / Math.PI),
      centerAngleDegrees: (normalizeAngle(segment.centerAngle) * 180 / Math.PI),
      inSector: inSector,
      distanceFromCenter: adjustedDistance,
      distanceFromCenterDegrees: (adjustedDistance * 180 / Math.PI)
    });

    if (inSector) {
      return {
        segment: segment,
        confidence: 0.95, // High confidence for geometric method
        debugInfo: debugInfo
      };
    }
  }

  // Fallback: find closest segment by center angle
  let closestSegment: WheelSegment | null = null;
  let minDistance = Infinity;

  for (const segment of segments) {
    const centerAngle = normalizeAngle(segment.centerAngle);
    const distance = Math.abs(normalizedBallAngle - centerAngle);
    const adjustedDistance = Math.min(distance, 2 * Math.PI - distance);

    if (adjustedDistance < minDistance) {
      minDistance = adjustedDistance;
      closestSegment = segment;
    }
  }

  return {
    segment: closestSegment,
    confidence: closestSegment ? 0.8 : 0, // Lower confidence for fallback
    debugInfo: debugInfo
  };
}

/**
 * MECHANISM 4: Statistical Sampling Consensus
 * Placeholder for temporal sampling (requires integration with physics engine)
 */
function detectWinnerByStatisticalSampling(
  ballX: number,
  ballY: number,
  centerX: number,
  centerY: number,
  segments: WheelSegment[],
  historicalSamples?: Array<{ x: number; y: number; timestamp: number }>
): { segment: WheelSegment | null; confidence: number; debugInfo: any } {
  const debugInfo: any = {
    method: 'statistical-sampling',
    samples: historicalSamples || [],
    segmentFrequency: {}
  };

  if (!historicalSamples || historicalSamples.length < 3) {
    // Fallback: Use current position with multiple angle calculations
    const angles = [];
    const numSamples = 5;

    for (let i = 0; i < numSamples; i++) {
      // Add small random offset to simulate sampling variation
      const offsetX = ballX + (Math.random() - 0.5) * 2;
      const offsetY = ballY + (Math.random() - 0.5) * 2;
      const angle = getBallAngle(offsetX, offsetY, centerX, centerY);
      angles.push(angle);
    }

    const segmentVotes: Map<string, number> = new Map();

    for (const angle of angles) {
      const segment = getSegmentAtAngle(angle, segments);
      if (segment) {
        const votes = segmentVotes.get(segment.id) || 0;
        segmentVotes.set(segment.id, votes + 1);
        debugInfo.segmentFrequency[segment.id] = votes + 1;
      }
    }

    let winningSegment: WheelSegment | null = null;
    let maxVotes = 0;

    for (const [segmentId, votes] of segmentVotes.entries()) {
      if (votes > maxVotes) {
        maxVotes = votes;
        winningSegment = segments.find(s => s.id === segmentId) || null;
      }
    }

    return {
      segment: winningSegment,
      confidence: maxVotes / numSamples,
      debugInfo: debugInfo
    };
  }

  // Use historical samples
  const segmentVotes: Map<string, number> = new Map();

  for (const sample of historicalSamples) {
    const angle = getBallAngle(sample.x, sample.y, centerX, centerY);
    const segment = getSegmentAtAngle(angle, segments);
    if (segment) {
      const votes = segmentVotes.get(segment.id) || 0;
      segmentVotes.set(segment.id, votes + 1);
      debugInfo.segmentFrequency[segment.id] = votes + 1;
    }
  }

  let winningSegment: WheelSegment | null = null;
  let maxVotes = 0;

  for (const [segmentId, votes] of segmentVotes.entries()) {
    if (votes > maxVotes) {
      maxVotes = votes;
      winningSegment = segments.find(s => s.id === segmentId) || null;
    }
  }

  return {
    segment: winningSegment,
    confidence: maxVotes / historicalSamples.length,
    debugInfo: debugInfo
  };
}

/**
 * MECHANISM 5: Distance-Based Segment Center Detection
 * Finds the segment whose center point is closest to the ball
 */
function detectWinnerByDistanceToCenter(
  ballX: number,
  ballY: number,
  centerX: number,
  centerY: number,
  segments: WheelSegment[]
): { segment: WheelSegment | null; confidence: number; debugInfo: any } {
  const debugInfo: any = {
    method: 'distance-to-center',
    ballPosition: { x: ballX, y: ballY },
    wheelCenter: { x: centerX, y: centerY },
    segmentDistances: []
  };

  let closestSegment: WheelSegment | null = null;
  let minDistance = Infinity;
  let maxDistance = 0;

  // Calculate distance from ball to each segment's center point
  for (const segment of segments) {
    // Calculate the center point of the segment on the wheel rim
    const segmentRadius = 150; // Approximate radius to segment centers
    const segmentCenterX = centerX + segmentRadius * Math.cos(segment.centerAngle);
    const segmentCenterY = centerY + segmentRadius * Math.sin(segment.centerAngle);

    // Distance from ball to segment center
    const distance = Math.sqrt(
      Math.pow(ballX - segmentCenterX, 2) + Math.pow(ballY - segmentCenterY, 2)
    );

    debugInfo.segmentDistances.push({
      segmentId: segment.id,
      segmentText: segment.text,
      centerX: segmentCenterX,
      centerY: segmentCenterY,
      distance: distance
    });

    if (distance < minDistance) {
      minDistance = distance;
      closestSegment = segment;
    }

    if (distance > maxDistance) {
      maxDistance = distance;
    }
  }

  // Calculate confidence based on how much closer the winning segment is compared to others
  const confidence = maxDistance > 0 ? Math.max(0, 1 - (minDistance / maxDistance)) : 0;

  debugInfo.minDistance = minDistance;
  debugInfo.maxDistance = maxDistance;
  debugInfo.winningSegment = closestSegment?.text || null;

  return {
    segment: closestSegment,
    confidence: confidence,
    debugInfo: debugInfo
  };
}

/**
 * MECHANISM 6: Geometric Center-of-Mass Detection
 * Uses the geometric center of mass of the segment to determine winner
 */
function detectWinnerByGeometricCenterOfMass(
  ballX: number,
  ballY: number,
  centerX: number,
  centerY: number,
  segments: WheelSegment[]
): { segment: WheelSegment | null; confidence: number; debugInfo: any } {
  const debugInfo: any = {
    method: 'geometric-center-of-mass',
    ballPosition: { x: ballX, y: ballY },
    wheelCenter: { x: centerX, y: centerY },
    segmentAnalysis: []
  };

  let bestSegment: WheelSegment | null = null;
  let bestScore = -Infinity;
  let maxScore = 0;

  for (const segment of segments) {
    // Calculate the geometric center of mass for this segment
    const segmentRadius = 150;
    const angleSpan = segment.endAngle - segment.startAngle;

    // Use multiple points along the segment to calculate center of mass
    const numPoints = 10;
    let totalX = 0;
    let totalY = 0;

    for (let i = 0; i < numPoints; i++) {
      const angle = segment.startAngle + (angleSpan * i / (numPoints - 1));
      const pointX = centerX + segmentRadius * Math.cos(angle);
      const pointY = centerY + segmentRadius * Math.sin(angle);
      totalX += pointX;
      totalY += pointY;
    }

    const centerOfMassX = totalX / numPoints;
    const centerOfMassY = totalY / numPoints;

    // Calculate distance from ball to center of mass
    const distance = Math.sqrt(
      Math.pow(ballX - centerOfMassX, 2) + Math.pow(ballY - centerOfMassY, 2)
    );

    // Score is inverse of distance (closer = higher score)
    const score = 1000 / (distance + 1);

    debugInfo.segmentAnalysis.push({
      segmentId: segment.id,
      segmentText: segment.text,
      centerOfMassX: centerOfMassX,
      centerOfMassY: centerOfMassY,
      distance: distance,
      score: score
    });

    if (score > bestScore) {
      bestScore = score;
      bestSegment = segment;
    }

    if (score > maxScore) {
      maxScore = score;
    }
  }

  // Calculate confidence based on score separation
  const confidence = maxScore > 0 ? Math.min(0.99, bestScore / maxScore) : 0;

  debugInfo.bestScore = bestScore;
  debugInfo.maxScore = maxScore;
  debugInfo.winningSegment = bestSegment?.text || null;

  return {
    segment: bestSegment,
    confidence: confidence,
    debugInfo: debugInfo
  };
}

/**
 * ULTRA-RELIABLE CONSENSUS-BASED WINNER DETECTION
 * Now uses 6 mechanisms for maximum reliability - requires at least 3 out of 6 to agree
 * Includes user feedback system for continuous improvement
 */
export function getWinningSegmentUltraReliable(
  ballX: number,
  ballY: number,
  centerX: number,
  centerY: number,
  segments: WheelSegment[],
  nailPositions?: Array<{ x: number; y: number; angle: number }>,
  ballRadius: number = 8,
  collisionHistory?: Array<{ nailIndex: number; timestamp: number }>,
  historicalSamples?: Array<{ x: number; y: number; timestamp: number }>
): {
  segment: WheelSegment | null;
  confidence: number;
  consensusLevel: number;
  agreementCount: number;
  methods: Array<{ name: string; segment: string | null; confidence: number }>;
  debugInfo: any;
} {
  console.log('[ULTRA-RELIABLE] Starting 6-mechanism consensus detection...');

  // Run all 6 mechanisms independently
  //const mechanism1 = detectWinnerByMultiPointAngle(ballX, ballY, centerX, centerY, segments, ballRadius);
  //const mechanism2 = detectWinnerByCollisionHistory(ballX, ballY, centerX, centerY, segments,nailPositions || [], ballRadius, collisionHistory);
  const mechanism3 = detectWinnerByRadialSector(ballX, ballY, centerX, centerY, segments);
  //const mechanism4 = detectWinnerByStatisticalSampling(ballX, ballY, centerX, centerY, segments, historicalSamples);
  const mechanism5 = detectWinnerByDistanceToCenter(ballX, ballY, centerX, centerY, segments);
  const mechanism6 = detectWinnerByGeometricCenterOfMass(ballX, ballY, centerX, centerY, segments);

  const mechanisms = [
    //{ name: 'Multi-Point Angle', result: mechanism1 },
    //{ name: 'Collision History', result: mechanism2 },
    { name: 'Radial Sector', result: mechanism3 },
    //{ name: 'Statistical Sampling', result: mechanism4 },
    { name: 'Distance to Center', result: mechanism5 },
    { name: 'Geometric Center of Mass', result: mechanism6 }
  ];

  // Count votes for each segment
  const segmentVotes: Map<string, { count: number; totalConfidence: number; mechanisms: string[] }> = new Map();
  const methodResults: Array<{ name: string; segment: string | null; confidence: number }> = [];

  for (const mechanism of mechanisms) {
    const segmentId = mechanism.result.segment?.id || null;
    const segmentText = mechanism.result.segment?.text || null;

    methodResults.push({
      name: mechanism.name,
      segment: segmentText,
      confidence: mechanism.result.confidence
    });

    if (segmentId && mechanism.result.confidence > 0.5) { // Only count high-confidence results
      const existing = segmentVotes.get(segmentId) || { count: 0, totalConfidence: 0, mechanisms: [] };
      existing.count += 1;
      existing.totalConfidence += mechanism.result.confidence;
      existing.mechanisms.push(mechanism.name);
      segmentVotes.set(segmentId, existing);
    }
  }

  // Find segment with most votes
  let winningSegment: WheelSegment | null = null;
  let maxVotes = 0;
  let maxConfidence = 0;
  let agreementCount = 0;

  for (const [segmentId, votes] of segmentVotes.entries()) {
    if (votes.count > maxVotes || (votes.count === maxVotes && votes.totalConfidence > maxConfidence)) {
      maxVotes = votes.count;
      maxConfidence = votes.totalConfidence;
      agreementCount = votes.count;
      winningSegment = segments.find(s => s.id === segmentId) || null;
    }
  }

  // Calculate consensus level (percentage of mechanisms that agree)
  const consensusLevel = agreementCount / mechanisms.length;

  // Calculate final confidence based on consensus and individual confidences
  let finalConfidence = 0;
  if (winningSegment && agreementCount >= mechanisms.length-1) {
    // Ultra-high confidence
    const avgConfidence = maxConfidence / agreementCount;
    finalConfidence = Math.min(0.999, 0.85 + (consensusLevel * 0.12) + (avgConfidence * 0.039));
  } else if (winningSegment && agreementCount === mechanisms.length-2) {
    // high confidence
    const avgConfidence = maxConfidence / agreementCount;
    finalConfidence = Math.min(0.995, 0.8 + (consensusLevel * 0.15) + (avgConfidence * 0.045));
  } else if (winningSegment && agreementCount === 1) {
    // Low confidence if only 1 mechanism agrees
    finalConfidence = 0.3;
  }

  const debugInfo = {
    ballPosition: { x: ballX, y: ballY },
    wheelCenter: { x: centerX, y: centerY },
    segmentVotes: Object.fromEntries(segmentVotes),
    mechanismResults: {
      //multiPointAngle: mechanism1.debugInfo,
      //collisionHistory: mechanism2.debugInfo,
      radialSector: mechanism3.debugInfo,
      //statisticalSampling: mechanism4.debugInfo
      distanceToCenter: mechanism5.debugInfo,
      geometricCenterOfMass: mechanism6.debugInfo
    }
  };

  console.log(`[ULTRA-RELIABLE] Consensus Results:`);
  console.log(`  Winner: ${winningSegment?.text || 'None'}`);
  console.log(`  Agreement: ${agreementCount}/6 mechanisms (${(consensusLevel * 100).toFixed(1)}%)`);
  console.log(`  Final Confidence: ${(finalConfidence * 100).toFixed(1)}%`);
  console.log(`  Method Results:`, methodResults);

  // ENHANCED: Detect disagreements and trigger user feedback system
  const hasDisagreement = agreementCount < mechanisms.length-1; // Less than 5/6 consensus indicates disagreement
  const disagreementMethods: string[] = [];
  const minorityWinners: string[] = [];

  if (hasDisagreement) {
    // Identify which methods disagreed and what they predicted
    mechanisms.forEach(mechanism => {
      const mechanismWinner = mechanism.result.segment?.text || null;
      if (mechanismWinner !== winningSegment?.text) {
        disagreementMethods.push(mechanism.name);
        if (mechanismWinner && !minorityWinners.includes(mechanismWinner)) {
          minorityWinners.push(mechanismWinner);
        }
      }
    });

    // Create disagreement event for learning
    const disagreementData = {
      ballPosition: { x: ballX, y: ballY },
      wheelCenter: { x: centerX, y: centerY },
      detectionResults: Object.fromEntries(
        mechanisms.map(m => [
          m.name.toLowerCase().replace(/\s+/g, '_'),
          { segment: m.result.segment, confidence: m.result.confidence }
        ])
      ),
      consensusLevel: consensusLevel,
      agreementCount: agreementCount,
      disagreementMethods: disagreementMethods,
      majorityWinner: winningSegment?.text || null,
      minorityWinners: minorityWinners,
      userFeedbackRequested: true,
      spinTimestamp: Date.now()
    };

    // Trigger user feedback modal if disagreement is significant
    if (agreementCount <= 4) {
      console.warn(`[ULTRA-RELIABLE] DISAGREEMENT DETECTED! Only ${agreementCount}/6 mechanisms agree.`);
      console.warn(`[ULTRA-RELIABLE] Disagreeing methods:`, disagreementMethods);
      console.warn(`[ULTRA-RELIABLE] Minority predictions:`, minorityWinners);

      // Trigger user feedback system
      triggerUserFeedbackForDisagreement(disagreementData, methodResults);
    }
  }

  return {
    segment: winningSegment,
    confidence: finalConfidence,
    consensusLevel: consensusLevel,
    agreementCount: agreementCount,
    methods: methodResults,
    debugInfo: debugInfo
  };
}

/**
 * Legacy function for backward compatibility
 * @deprecated Use getWinningSegmentUltraReliable instead
 */
export function getWinningSegmentAdvanced(
  ballX: number,
  ballY: number,
  centerX: number,
  centerY: number,
  segments: WheelSegment[],
  nailPositions?: Array<{ x: number; y: number; angle: number }>,
  ballRadius: number = 8
): {
  segment: WheelSegment | null;
  confidence: number;
  method: 'angle' | 'collision' | 'proximity' | 'precise';
  debugInfo: any;
} {
  // Use the new ultra-reliable method and convert the result format
  const result = getWinningSegmentUltraReliable(
    ballX, ballY, centerX, centerY, segments, nailPositions, ballRadius
  );

  return {
    segment: result.segment,
    confidence: result.confidence,
    method: result.agreementCount >= 3 ? 'precise' : 'angle',
    debugInfo: result.debugInfo
  };
}

/**
 * Helper function to check if an angle is within a segment's angular range
 */
function isAngleInSegment(angle: number, startAngle: number, endAngle: number): boolean {
  const normalizedAngle = normalizeAngle(angle);
  const normalizedStart = normalizeAngle(startAngle);
  const normalizedEnd = normalizeAngle(endAngle);

  if (normalizedStart > normalizedEnd) {
    // Segment crosses 0 boundary
    return normalizedAngle >= normalizedStart || normalizedAngle <= normalizedEnd;
  } else {
    // Normal segment
    return normalizedAngle >= normalizedStart && normalizedAngle <= normalizedEnd;
  }
}

/**
 * Calculates the ball's position angle relative to wheel center
 */
export function getBallAngle(ballX: number, ballY: number, centerX: number, centerY: number): number {
  return normalizeAngle(Math.atan2(ballY - centerY, ballX - centerX));
}

/**
 * Applies easing function to a value (ease-out cubic)
 */
export function easeOutCubic(t: number): number {
  return 1 - Math.pow(1 - t, 3);
}

/**
 * Applies easing function to a value (ease-in-out cubic)
 */
export function easeInOutCubic(t: number): number {
  return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
}

/**
 * Calculates spin velocity based on interaction data
 */
export function calculateSpinVelocity(
  startX: number,
  startY: number,
  endX: number,
  endY: number,
  deltaTime: number,
  centerX: number,
  centerY: number
): number {
  if (deltaTime <= 0) return 0;
  
  const startAngle = Math.atan2(startY - centerY, startX - centerX);
  const endAngle = Math.atan2(endY - centerY, endX - centerX);
  
  let angleDiff = endAngle - startAngle;
  
  // Handle angle wrapping
  if (angleDiff > Math.PI) {
    angleDiff -= 2 * Math.PI;
  } else if (angleDiff < -Math.PI) {
    angleDiff += 2 * Math.PI;
  }
  
  return angleDiff / deltaTime;
}

/**
 * Calculates the final resting position after a spin with given initial velocity
 */
export function calculateFinalPosition(
  initialVelocity: number,
  friction: number,
  airResistance: number
): { finalAngle: number; duration: number } {
  // Simple physics simulation to predict where the ball will stop
  let velocity = Math.abs(initialVelocity);
  let angle = 0;
  let time = 0;
  const timeStep = 16; // 60fps
  
  while (velocity > 0.01) { // Stop when velocity is very low
    velocity *= airResistance; // Apply air resistance
    velocity -= friction; // Apply friction
    if (velocity < 0) velocity = 0;
    
    angle += velocity * (timeStep / 1000);
    time += timeStep;
    
    // Safety check to prevent infinite loops
    if (time > 10000) break;
  }
  
  return {
    finalAngle: normalizeAngle(angle),
    duration: time
  };
}

/**
 * Checks if a point is inside a circle
 */
export function isPointInCircle(
  pointX: number,
  pointY: number,
  circleX: number,
  circleY: number,
  radius: number
): boolean {
  const dist = distance(pointX, pointY, circleX, circleY);
  return dist <= radius;
}

/**
 * Clamps a value between min and max
 */
export function clamp(value: number, min: number, max: number): number {
  return Math.min(Math.max(value, min), max);
}

/**
 * Linear interpolation between two values
 */
export function lerp(start: number, end: number, t: number): number {
  return start + (end - start) * clamp(t, 0, 1);
}

/**
 * Generates a random number between min and max
 */
export function random(min: number, max: number): number {
  return Math.random() * (max - min) + min;
}

/**
 * Calculates the momentum needed for a specific spin duration
 */
export function calculateMomentumForDuration(
  targetDuration: number,
  friction: number,
  airResistance: number
): number {
  // Work backwards from desired duration to find initial velocity
  let velocity = 1.0;
  let bestVelocity = 1.0;
  let bestDifference = Infinity;
  
  // Try different velocities to find the one that gives closest to target duration
  for (let testVelocity = 0.1; testVelocity <= 5.0; testVelocity += 0.1) {
    const result = calculateFinalPosition(testVelocity, friction, airResistance);
    const difference = Math.abs(result.duration - targetDuration);
    
    if (difference < bestDifference) {
      bestDifference = difference;
      bestVelocity = testVelocity;
    }
  }
  
  return bestVelocity;
}

/**
 * USER FEEDBACK SYSTEM FOR WINNER DETECTION IMPROVEMENT
 * Triggers when detection mechanisms disagree to collect user feedback
 */
function triggerUserFeedbackForDisagreement(disagreementData: any, methodResults: any[]): void {
  console.log('[USER-FEEDBACK] Winner detection disagreement detected - requesting user feedback');

  // Create a modal or notification asking user which result is correct
  const modal = document.createElement('div');
  modal.className = 'winner-detection-feedback-modal';
  modal.style.cssText = `
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border: 2px solid #007bff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    z-index: 10000;
    max-width: 500px;
    font-family: Arial, sans-serif;
  `;

  const majorityWinner = disagreementData.majorityWinner;
  const minorityWinners = disagreementData.minorityWinners;
  const allOptions = [majorityWinner, ...minorityWinners].filter(Boolean);

  modal.innerHTML = `
    <h3 style="margin-top: 0; color: #007bff;">🎯 Help Improve Winner Detection</h3>
    <p>Our detection system had some disagreement. Which segment did the ball actually land on?</p>
    <p><strong>System predicted:</strong> ${majorityWinner} (${disagreementData.agreementCount}/6 mechanisms agreed)</p>
    <p><strong>Alternative predictions:</strong> ${minorityWinners.join(', ')}</p>

    <div style="margin: 15px 0;">
      <strong>Which result is correct?</strong>
    </div>

    ${allOptions.map(option => `
      <button class="feedback-option" data-winner="${option}" style="
        display: block;
        width: 100%;
        margin: 5px 0;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        background: #f8f9fa;
        cursor: pointer;
        font-size: 14px;
      ">${option}</button>
    `).join('')}

    <div style="margin-top: 15px; text-align: right;">
      <button id="skip-feedback" style="
        padding: 8px 16px;
        border: 1px solid #6c757d;
        border-radius: 4px;
        background: #6c757d;
        color: white;
        cursor: pointer;
        margin-right: 10px;
      ">Skip</button>
    </div>
  `;

  // Add event listeners
  modal.querySelectorAll('.feedback-option').forEach(button => {
    button.addEventListener('click', (e) => {
      const target = e.target as HTMLButtonElement;
      const userChoice = target.dataset.winner;
      handleUserFeedback(userChoice!, disagreementData, methodResults);
      document.body.removeChild(modal);
    });
  });

  modal.querySelector('#skip-feedback')?.addEventListener('click', () => {
    console.log('[USER-FEEDBACK] User skipped feedback');
    document.body.removeChild(modal);
  });

  // Add to page
  document.body.appendChild(modal);

  // Auto-remove after 30 seconds if no response
  setTimeout(() => {
    if (document.body.contains(modal)) {
      console.log('[USER-FEEDBACK] Auto-removing feedback modal after timeout');
      document.body.removeChild(modal);
    }
  }, 30000);
}

/**
 * Handle user feedback and create HistoryEvent for learning
 */
function handleUserFeedback(userChoice: string, disagreementData: any, methodResults: any[]): void {
  console.log(`[USER-FEEDBACK] User confirmed winner: ${userChoice}`);

  // Determine which methods were correct/incorrect
  const correctMethods: string[] = [];
  const incorrectMethods: string[] = [];

  methodResults.forEach(method => {
    const methodWinner = method.segment;
    if (methodWinner === userChoice) {
      correctMethods.push(method.name);
    } else {
      incorrectMethods.push(method.name);
    }
  });

  // Create feedback event data
  const feedbackData = {
    originalDisagreementData: disagreementData,
    userConfirmedWinner: userChoice,
    systemPredictedWinner: disagreementData.majorityWinner,
    predictionWasCorrect: userChoice === disagreementData.majorityWinner,
    userFeedbackTimestamp: Date.now(),
    userConfidence: 'confident', // Could be enhanced with confidence selection
    feedbackMethod: 'modal_selection',
    correctMethods: correctMethods,
    incorrectMethods: incorrectMethods,
    learningImpact: {
      trainingDataPointCreated: true,
      modelWeightsNeedUpdate: true,
      consensusThresholdNeedsReview: correctMethods.length < 3
    }
  };

  // Send to backend for HistoryEvent creation and learning
  sendFeedbackToBackend(feedbackData);

  // Show thank you message
  showFeedbackThankYou(userChoice, feedbackData.predictionWasCorrect);
}

/**
 * Send feedback to backend for HistoryEvent creation
 */
async function sendFeedbackToBackend(feedbackData: any): Promise<void> {
  try {
    const response = await fetch('/api/wheel-winner-feedback/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': getCsrfToken()
      },
      body: JSON.stringify({
        event_type: 'wheel_winner_detection_feedback',
        feedback_data: feedbackData
      })
    });

    if (response.ok) {
      console.log('[USER-FEEDBACK] Feedback sent to backend successfully');
    } else {
      console.error('[USER-FEEDBACK] Failed to send feedback to backend:', response.status);
    }
  } catch (error) {
    console.error('[USER-FEEDBACK] Error sending feedback to backend:', error);
  }
}

/**
 * Show thank you message to user
 */
function showFeedbackThankYou(userChoice: string, wasCorrect: boolean): void {
  const message = wasCorrect
    ? `✅ Thank you! Our system correctly predicted "${userChoice}".`
    : `🎯 Thank you! We'll learn from this - the correct answer was "${userChoice}".`;

  // Create temporary notification
  const notification = document.createElement('div');
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: ${wasCorrect ? '#d4edda' : '#fff3cd'};
    border: 1px solid ${wasCorrect ? '#c3e6cb' : '#ffeaa7'};
    color: ${wasCorrect ? '#155724' : '#856404'};
    padding: 12px 16px;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 10001;
    max-width: 300px;
    font-size: 14px;
  `;
  notification.textContent = message;

  document.body.appendChild(notification);

  // Auto-remove after 5 seconds
  setTimeout(() => {
    if (document.body.contains(notification)) {
      document.body.removeChild(notification);
    }
  }, 5000);
}

/**
 * Get CSRF token for Django requests
 */
function getCsrfToken(): string {
  const cookies = document.cookie.split(';');
  for (let cookie of cookies) {
    const [name, value] = cookie.trim().split('=');
    if (name === 'csrftoken') {
      return value;
    }
  }
  return '';
}
