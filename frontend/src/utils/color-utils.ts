/**
 * Color utility functions for the spinning wheel component
 * Provides color manipulation, conversion, and palette generation
 */

/**
 * RGB color representation
 */
export interface RGB {
  r: number;
  g: number;
  b: number;
}

/**
 * HSL color representation
 */
export interface HSL {
  h: number;
  s: number;
  l: number;
}

/**
 * Converts a hex color string to RGB values
 */
export function hexToRgb(hex: string): RGB | null {
  // Remove # if present
  hex = hex.replace('#', '');
  
  // Handle 3-digit hex
  if (hex.length === 3) {
    hex = hex.split('').map(char => char + char).join('');
  }
  
  if (hex.length !== 6) {
    return null;
  }
  
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);
  
  if (isNaN(r) || isNaN(g) || isNaN(b)) {
    return null;
  }
  
  return { r, g, b };
}

/**
 * Converts RGB values to a hex color string
 */
export function rgbToHex(r: number, g: number, b: number): string {
  const toHex = (n: number) => {
    const hex = Math.round(Math.max(0, Math.min(255, n))).toString(16);
    return hex.length === 1 ? '0' + hex : hex;
  };
  
  return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
}

/**
 * Converts RGB to HSL
 */
export function rgbToHsl(r: number, g: number, b: number): HSL {
  r /= 255;
  g /= 255;
  b /= 255;
  
  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h = 0;
  let s = 0;
  const l = (max + min) / 2;
  
  if (max !== min) {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
    
    switch (max) {
      case r:
        h = (g - b) / d + (g < b ? 6 : 0);
        break;
      case g:
        h = (b - r) / d + 2;
        break;
      case b:
        h = (r - g) / d + 4;
        break;
    }
    h /= 6;
  }
  
  return { h: h * 360, s: s * 100, l: l * 100 };
}

/**
 * Converts HSL to RGB
 */
export function hslToRgb(h: number, s: number, l: number): RGB {
  h /= 360;
  s /= 100;
  l /= 100;
  
  const hue2rgb = (p: number, q: number, t: number) => {
    if (t < 0) t += 1;
    if (t > 1) t -= 1;
    if (t < 1/6) return p + (q - p) * 6 * t;
    if (t < 1/2) return q;
    if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
    return p;
  };
  
  let r: number, g: number, b: number;
  
  if (s === 0) {
    r = g = b = l; // achromatic
  } else {
    const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
    const p = 2 * l - q;
    r = hue2rgb(p, q, h + 1/3);
    g = hue2rgb(p, q, h);
    b = hue2rgb(p, q, h - 1/3);
  }
  
  return {
    r: Math.round(r * 255),
    g: Math.round(g * 255),
    b: Math.round(b * 255)
  };
}

/**
 * Lightens a color by a given percentage
 */
export function lightenColor(hex: string, percent: number): string {
  const rgb = hexToRgb(hex);
  if (!rgb) return hex;
  
  const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b);
  hsl.l = Math.min(100, hsl.l + percent);
  
  const newRgb = hslToRgb(hsl.h, hsl.s, hsl.l);
  return rgbToHex(newRgb.r, newRgb.g, newRgb.b);
}

/**
 * Darkens a color by a given percentage
 */
export function darkenColor(hex: string, percent: number): string {
  const rgb = hexToRgb(hex);
  if (!rgb) return hex;
  
  const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b);
  hsl.l = Math.max(0, hsl.l - percent);
  
  const newRgb = hslToRgb(hsl.h, hsl.s, hsl.l);
  return rgbToHex(newRgb.r, newRgb.g, newRgb.b);
}

/**
 * Adjusts the saturation of a color
 */
export function adjustSaturation(hex: string, percent: number): string {
  const rgb = hexToRgb(hex);
  if (!rgb) return hex;
  
  const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b);
  hsl.s = Math.max(0, Math.min(100, hsl.s + percent));
  
  const newRgb = hslToRgb(hsl.h, hsl.s, hsl.l);
  return rgbToHex(newRgb.r, newRgb.g, newRgb.b);
}

/**
 * Gets the contrast color (black or white) for a given background color
 */
export function getContrastColor(hex: string): string {
  const rgb = hexToRgb(hex);
  if (!rgb) return '#000000';
  
  // Calculate relative luminance
  const luminance = (0.299 * rgb.r + 0.587 * rgb.g + 0.114 * rgb.b) / 255;
  
  // Return black for light backgrounds, white for dark backgrounds
  return luminance > 0.5 ? '#000000' : '#FFFFFF';
}

/**
 * Generates a color palette with good contrast and visual appeal
 */
export function generateColorPalette(count: number, baseHue: number = 0): string[] {
  const colors: string[] = [];
  const goldenRatio = 137.508; // Golden angle in degrees
  
  for (let i = 0; i < count; i++) {
    const hue = (baseHue + i * goldenRatio) % 360;
    const saturation = 65 + (i % 3) * 10; // Vary saturation slightly
    const lightness = 50 + (i % 2) * 15; // Alternate lightness
    
    const rgb = hslToRgb(hue, saturation, lightness);
    colors.push(rgbToHex(rgb.r, rgb.g, rgb.b));
  }
  
  return colors;
}

/**
 * Ensures colors have sufficient contrast between adjacent segments
 * ENHANCED: Improved color differentiation with higher threshold and better adjustment logic
 */
export function optimizeSegmentColors(colors: string[]): string[] {
  if (colors.length <= 1) return colors;

  const optimized = [...colors];
  const minDistance = 80; // ENHANCED: Increased from 50 to 80 for better differentiation

  for (let i = 0; i < optimized.length; i++) {
    const currentColor = optimized[i];
    const nextIndex = (i + 1) % optimized.length;
    const nextColor = optimized[nextIndex];

    // Check if colors are too similar
    if (getColorDistance(currentColor, nextColor) < minDistance) {
      // ENHANCED: Try multiple adjustment strategies for better results
      const rgb = hexToRgb(nextColor);
      if (rgb) {
        const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b);

        // Strategy 1: Shift hue significantly
        let adjustedHsl = { ...hsl };
        adjustedHsl.h = (hsl.h + 90) % 360; // ENHANCED: Increased from 60 to 90 degrees

        // Strategy 2: If still too similar, also adjust saturation and lightness
        let newRgb = hslToRgb(adjustedHsl.h, adjustedHsl.s, adjustedHsl.l);
        let newColor = rgbToHex(newRgb.r, newRgb.g, newRgb.b);

        if (getColorDistance(currentColor, newColor) < minDistance) {
          // Further adjust saturation and lightness
          adjustedHsl.s = Math.max(30, Math.min(90, adjustedHsl.s + (adjustedHsl.s > 50 ? -30 : 30)));
          adjustedHsl.l = Math.max(25, Math.min(75, adjustedHsl.l + (adjustedHsl.l > 50 ? -25 : 25)));
          newRgb = hslToRgb(adjustedHsl.h, adjustedHsl.s, adjustedHsl.l);
          newColor = rgbToHex(newRgb.r, newRgb.g, newRgb.b);
        }

        optimized[nextIndex] = newColor;
      }
    }
  }

  return optimized;
}

/**
 * Calculates the perceptual distance between two colors
 */
export function getColorDistance(color1: string, color2: string): number {
  const rgb1 = hexToRgb(color1);
  const rgb2 = hexToRgb(color2);
  
  if (!rgb1 || !rgb2) return 0;
  
  // Use weighted Euclidean distance for better perceptual accuracy
  const rMean = (rgb1.r + rgb2.r) / 2;
  const deltaR = rgb1.r - rgb2.r;
  const deltaG = rgb1.g - rgb2.g;
  const deltaB = rgb1.b - rgb2.b;
  
  const weightR = 2 + rMean / 256;
  const weightG = 4;
  const weightB = 2 + (255 - rMean) / 256;
  
  return Math.sqrt(
    weightR * deltaR * deltaR +
    weightG * deltaG * deltaG +
    weightB * deltaB * deltaB
  );
}

/**
 * Converts a color to PixiJS-compatible format (0xRRGGBB)
 */
export function hexToPixiColor(hex: string): number {
  // Remove # if present
  hex = hex.replace('#', '');
  
  // Handle 3-digit hex
  if (hex.length === 3) {
    hex = hex.split('').map(char => char + char).join('');
  }
  
  return parseInt(hex, 16);
}

/**
 * Converts PixiJS color format to hex string
 */
export function pixiColorToHex(color: number): string {
  return `#${color.toString(16).padStart(6, '0')}`;
}

/**
 * Creates a gradient color array for smooth transitions
 */
export function createGradient(startColor: string, endColor: string, steps: number): string[] {
  const startRgb = hexToRgb(startColor);
  const endRgb = hexToRgb(endColor);
  
  if (!startRgb || !endRgb) return [startColor];
  
  const gradient: string[] = [];
  
  for (let i = 0; i < steps; i++) {
    const ratio = i / (steps - 1);
    const r = Math.round(startRgb.r + (endRgb.r - startRgb.r) * ratio);
    const g = Math.round(startRgb.g + (endRgb.g - startRgb.g) * ratio);
    const b = Math.round(startRgb.b + (endRgb.b - startRgb.b) * ratio);
    
    gradient.push(rgbToHex(r, g, b));
  }
  
  return gradient;
}

/**
 * Predefined color palettes for different themes
 */
export const COLOR_PALETTES = {
  vibrant: [
    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
    '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
  ],
  pastel: [
    '#FFB3BA', '#BAFFC9', '#BAE1FF', '#FFFFBA', '#FFD1DC',
    '#E0BBE4', '#C7CEEA', '#FEC89A', '#B4F8C8', '#A0E7E5'
  ],
  earth: [
    '#8B4513', '#CD853F', '#DEB887', '#F4A460', '#D2691E',
    '#BC8F8F', '#F5DEB3', '#DDD3C0', '#C19A6B', '#A0522D'
  ],
  ocean: [
    '#006994', '#0085C3', '#00A6FB', '#7209B7', '#560BAD',
    '#480CA8', '#3A0CA3', '#3F37C9', '#4361EE', '#4895EF'
  ]
} as const;
