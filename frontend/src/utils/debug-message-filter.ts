/**
 * Debug Message Filter Utility
 * Prevents debug message spam in production and filters appropriately in debug mode
 */

export class DebugMessageFilter {
  private static instance: DebugMessageFilter;
  private debugMode = false;
  private maxDebugMessages = 10;
  private debugMessageCount = 0;
  private lastResetTime = Date.now();
  private resetInterval = 30000; // Reset count every 30 seconds

  private constructor() {}

  public static getInstance(): DebugMessageFilter {
    if (!DebugMessageFilter.instance) {
      DebugMessageFilter.instance = new DebugMessageFilter();
    }
    return DebugMessageFilter.instance;
  }

  public setDebugMode(enabled: boolean): void {
    this.debugMode = enabled;
  }

  public shouldShowDebugMessage(message: any): boolean {
    if (!this.debugMode) {
      return false; // Never show debug messages in production
    }

    // Auto-reset count periodically
    const now = Date.now();
    if (now - this.lastResetTime > this.resetInterval) {
      this.resetDebugMessageCount();
      this.lastResetTime = now;
    }

    // Limit debug message spam even in debug mode
    if (this.debugMessageCount >= this.maxDebugMessages) {
      if (this.debugMessageCount === this.maxDebugMessages) {
        console.warn('🚫 Debug message limit reached. Further debug messages will be suppressed for 30 seconds.');
        this.debugMessageCount++;
      }
      return false;
    }

    this.debugMessageCount++;
    return true;
  }

  public resetDebugMessageCount(): void {
    this.debugMessageCount = 0;
  }

  public shouldAddToChat(messageType: string): boolean {
    // Never add debug messages to chat
    if (messageType === 'debug_info') {
      return false;
    }
    
    // Add all other message types to chat
    return true;
  }

  public getDebugMessageCount(): number {
    return this.debugMessageCount;
  }
}
