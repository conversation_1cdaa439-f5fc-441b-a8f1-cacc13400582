/**
 * Conversation State Management Utility
 * 
 * Manages conversation state using browser session storage to maintain context
 * across user interactions and workflow transitions.
 */

export class ConversationState {
    static STORAGE_KEY = 'conversationState';
    
    /**
     * Get current conversation state from session storage
     * @returns {Object} Current conversation state
     */
    static get() {
        try {
            const stored = sessionStorage.getItem(this.STORAGE_KEY);
            return stored ? JSON.parse(stored) : {};
        } catch (error) {
            console.warn('Failed to parse conversation state from session storage:', error);
            return {};
        }
    }
    
    /**
     * Set complete conversation state in session storage
     * @param {Object} state - Complete state object to store
     */
    static set(state) {
        try {
            sessionStorage.setItem(this.STORAGE_KEY, JSON.stringify(state));
        } catch (error) {
            console.error('Failed to store conversation state:', error);
        }
    }
    
    /**
     * Update conversation state with partial updates
     * @param {Object} updates - Partial state updates to merge
     */
    static update(updates) {
        const current = this.get();
        this.set({...current, ...updates});
    }
    
    /**
     * Clear conversation state from session storage
     */
    static clear() {
        try {
            sessionStorage.removeItem(this.STORAGE_KEY);
        } catch (error) {
            console.error('Failed to clear conversation state:', error);
        }
    }
    
    /**
     * Get specific field from conversation state
     * @param {string} key - State field key
     * @param {*} defaultValue - Default value if key doesn't exist
     * @returns {*} State field value or default
     */
    static getField(key, defaultValue = null) {
        const state = this.get();
        return state[key] !== undefined ? state[key] : defaultValue;
    }
    
    /**
     * Set specific field in conversation state
     * @param {string} key - State field key
     * @param {*} value - Value to set
     */
    static setField(key, value) {
        this.update({ [key]: value });
    }
    
    /**
     * Check if conversation is in a specific phase
     * @param {string} phase - Phase to check for
     * @returns {boolean} True if in specified phase
     */
    static isInPhase(phase) {
        return this.getField('phase') === phase;
    }
    
    /**
     * Check if conversation is awaiting a specific response type
     * @param {string} responseType - Response type to check for
     * @returns {boolean} True if awaiting specified response type
     */
    static isAwaitingResponse(responseType) {
        return this.getField('awaiting_response_type') === responseType;
    }
    
    /**
     * Get conversation context data
     * @returns {Object} Context data object
     */
    static getContext() {
        return this.getField('context', {});
    }
    
    /**
     * Update conversation context with partial updates
     * @param {Object} contextUpdates - Partial context updates
     */
    static updateContext(contextUpdates) {
        const currentContext = this.getContext();
        this.setField('context', {...currentContext, ...contextUpdates});
    }
    
    /**
     * Validate state format and fix common issues
     * @param {Object} state - State to validate
     * @returns {Object} Validated and corrected state
     */
    static validateState(state) {
        const validPhases = [
            'initial',
            'awaiting_profile_info', 
            'awaiting_situation_info',
            'awaiting_activity_feedback',
        ];
        
        const validResponseTypes = [
            'profile_info',
            'situation_info',
            'activity_selection',
            'activity_feedback'
        ];
        
        // Ensure valid phase
        if (!state.phase || !validPhases.includes(state.phase)) {
            state.phase = 'initial';
        }
        
        // Ensure valid awaiting_response_type if present
        if (state.awaiting_response_type && !validResponseTypes.includes(state.awaiting_response_type)) {
            delete state.awaiting_response_type;
        }
        
        // Ensure context is an object
        if (!state.context || typeof state.context !== 'object') {
            state.context = {};
        }
        
        return state;
    }
    
    /**
     * Initialize conversation state with validation
     * @param {Object} initialState - Initial state to set
     */
    static initialize(initialState = {}) {
        const validatedState = this.validateState(initialState);
        this.set(validatedState);
    }
    
    /**
     * Debug helper to log current state
     */
    static debug() {
        console.log('🔄 Conversation State:', this.get());
    }
}

// Export default for convenience
export default ConversationState;
