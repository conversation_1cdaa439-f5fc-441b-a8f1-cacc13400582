/**
 * Comprehensive error handling types for the frontend application
 * Following traditional best practices with error levels and classification
 */

/**
 * Error severity levels following traditional patterns
 */
export enum ErrorLevel {
  TEMPORARY = 'temporary',      // Connection issues, transient failures
  NON_CRITICAL = 'non_critical', // Validation errors, minor issues  
  CRITICAL = 'critical'         // System failures, data corruption
}

/**
 * Error types for classification and handling
 */
export enum ErrorType {
  // Connection and Network Errors
  CONNECTION_ERROR = 'CONNECTION_ERROR',
  WEBSOCKET_ERROR = 'WEBSOCKET_ERROR',
  API_ERROR = 'API_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  
  // Application Errors
  WHEEL_ERROR = 'WHEEL_ERROR',
  PHYSICS_ERROR = 'PHYSICS_ERROR',
  RENDER_ERROR = 'RENDER_ERROR',
  STATE_ERROR = 'STATE_ERROR',
  
  // User Input Errors
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
  
  // System Errors
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  CONFIGURATION_ERROR = 'CONFIGURATION_ERROR',
  DEPENDENCY_ERROR = 'DEPENDENCY_ERROR'
}

/**
 * Error notification display types
 */
export enum NotificationType {
  BANNER = 'banner',           // Top of app banner (temporary errors)
  POPUP = 'popup',            // Modal popup (non-critical in debug)
  PERSISTENT = 'persistent',   // Always visible (critical errors)
  TOAST = 'toast'             // Brief notification
}

/**
 * Core error interface
 */
export interface AppError {
  id: string;
  type: ErrorType;
  level: ErrorLevel;
  message: string;
  timestamp: Date;
  component?: string;
  metadata?: Record<string, any>;
  stack?: string;
  userAgent?: string;
  url?: string;
  retryCount?: number;
  autoResolved?: boolean;
}

/**
 * Error notification configuration
 */
export interface ErrorNotification {
  id: string;
  error: AppError;
  notificationType: NotificationType;
  displayDuration?: number;  // milliseconds, null for persistent
  dismissible: boolean;
  showInProduction: boolean;
  showInDebug: boolean;
  requiresSupport?: boolean;
  onDismiss?: () => void;
  onRetry?: () => void;
}

/**
 * Error context for detailed reporting
 */
export interface ErrorContext {
  component: string;
  method?: string;
  userProfile?: {
    id: string;
    isStaff: boolean;
  };
  sessionId?: string;
  wheelData?: any;
  inputData?: any;
  expectedFormat?: any;
  metadata?: Record<string, any>;

}

/**
 * Error reporting payload for backend
 */
export interface ErrorReportPayload {
  event_type: string;
  content_type: string;
  object_id: string;
  details: {
    error_type: ErrorType;
    error_message: string;
    error_level: ErrorLevel;
    component: string;
    timestamp: string;
    user_agent: string;
    url: string;
    stack_trace?: string;
    metadata?: Record<string, any>;
    retry_count?: number;
    auto_resolved?: boolean;
    requires_support?: boolean;
    debug_mode?: boolean;
  };
}

/**
 * Error handler configuration
 */
export interface ErrorHandlerConfig {
  enableReporting: boolean;
  enableRetry: boolean;
  maxRetryAttempts: number;
  retryDelay: number;
  debugMode: boolean;
  productionMode: boolean;
  reportingEndpoint: string;
}

/**
 * Error recovery strategy
 */
export interface ErrorRecoveryStrategy {
  canRecover: (error: AppError) => boolean;
  recover: (error: AppError) => Promise<boolean>;
  maxAttempts: number;
  backoffMultiplier: number;
}

/**
 * Error handler interface
 */
export interface IErrorHandler {
  handleError(error: Error | AppError, context?: ErrorContext): Promise<void>;
  reportError(error: AppError): Promise<void>;
  showNotification(notification: ErrorNotification): void;
  dismissNotification(notificationId: string): void;
  retryOperation(error: AppError): Promise<boolean>;
  clearErrors(): void;
  getActiveErrors(): AppError[];
}

/**
 * Error notification manager interface
 */
export interface IErrorNotificationManager {
  show(notification: ErrorNotification): void;
  dismiss(notificationId: string): void;
  dismissAll(): void;
  getActive(): ErrorNotification[];
  configure(config: Partial<ErrorHandlerConfig>): void;
}

/**
 * Error classification rules
 */
export const ERROR_CLASSIFICATION_RULES: Record<ErrorType, {
  defaultLevel: ErrorLevel;
  notificationType: NotificationType;
  showInProduction: boolean;
  showInDebug: boolean;
  autoRetry: boolean;
  maxRetries: number;
}> = {
  [ErrorType.CONNECTION_ERROR]: {
    defaultLevel: ErrorLevel.TEMPORARY,
    notificationType: NotificationType.BANNER,
    showInProduction: true,
    showInDebug: true,
    autoRetry: true,
    maxRetries: 3
  },
  [ErrorType.WEBSOCKET_ERROR]: {
    defaultLevel: ErrorLevel.TEMPORARY,
    notificationType: NotificationType.BANNER,
    showInProduction: true,
    showInDebug: true,
    autoRetry: true,
    maxRetries: 5
  },
  [ErrorType.API_ERROR]: {
    defaultLevel: ErrorLevel.NON_CRITICAL,
    notificationType: NotificationType.POPUP,
    showInProduction: false,
    showInDebug: true,
    autoRetry: false,
    maxRetries: 0
  },
  [ErrorType.TIMEOUT_ERROR]: {
    defaultLevel: ErrorLevel.TEMPORARY,
    notificationType: NotificationType.BANNER,
    showInProduction: true,
    showInDebug: true,
    autoRetry: true,
    maxRetries: 2
  },
  [ErrorType.WHEEL_ERROR]: {
    defaultLevel: ErrorLevel.CRITICAL,
    notificationType: NotificationType.PERSISTENT,
    showInProduction: true,
    showInDebug: true,
    autoRetry: false,
    maxRetries: 0
  },
  [ErrorType.PHYSICS_ERROR]: {
    defaultLevel: ErrorLevel.CRITICAL,
    notificationType: NotificationType.PERSISTENT,
    showInProduction: true,
    showInDebug: true,
    autoRetry: false,
    maxRetries: 0
  },
  [ErrorType.RENDER_ERROR]: {
    defaultLevel: ErrorLevel.CRITICAL,
    notificationType: NotificationType.PERSISTENT,
    showInProduction: true,
    showInDebug: true,
    autoRetry: false,
    maxRetries: 0
  },
  [ErrorType.STATE_ERROR]: {
    defaultLevel: ErrorLevel.CRITICAL,
    notificationType: NotificationType.PERSISTENT,
    showInProduction: true,
    showInDebug: true,
    autoRetry: false,
    maxRetries: 0
  },
  [ErrorType.VALIDATION_ERROR]: {
    defaultLevel: ErrorLevel.NON_CRITICAL,
    notificationType: NotificationType.POPUP,
    showInProduction: false,
    showInDebug: true,
    autoRetry: false,
    maxRetries: 0
  },
  [ErrorType.AUTHENTICATION_ERROR]: {
    defaultLevel: ErrorLevel.CRITICAL,
    notificationType: NotificationType.PERSISTENT,
    showInProduction: true,
    showInDebug: true,
    autoRetry: false,
    maxRetries: 0
  },
  [ErrorType.AUTHORIZATION_ERROR]: {
    defaultLevel: ErrorLevel.CRITICAL,
    notificationType: NotificationType.PERSISTENT,
    showInProduction: true,
    showInDebug: true,
    autoRetry: false,
    maxRetries: 0
  },
  [ErrorType.UNKNOWN_ERROR]: {
    defaultLevel: ErrorLevel.CRITICAL,
    notificationType: NotificationType.PERSISTENT,
    showInProduction: true,
    showInDebug: true,
    autoRetry: false,
    maxRetries: 0
  },
  [ErrorType.CONFIGURATION_ERROR]: {
    defaultLevel: ErrorLevel.CRITICAL,
    notificationType: NotificationType.PERSISTENT,
    showInProduction: true,
    showInDebug: true,
    autoRetry: false,
    maxRetries: 0
  },
  [ErrorType.DEPENDENCY_ERROR]: {
    defaultLevel: ErrorLevel.CRITICAL,
    notificationType: NotificationType.PERSISTENT,
    showInProduction: true,
    showInDebug: true,
    autoRetry: false,
    maxRetries: 0
  }
};

/**
 * Error message templates
 */
export const ERROR_MESSAGES: Record<ErrorType, {
  userMessage: string;
  debugMessage: string;
}> = {
  [ErrorType.CONNECTION_ERROR]: {
    userMessage: 'Connection to server lost. Attempting to reconnect...',
    debugMessage: 'Network connection error occurred'
  },
  [ErrorType.WEBSOCKET_ERROR]: {
    userMessage: 'Real-time connection interrupted. Reconnecting...',
    debugMessage: 'WebSocket connection error'
  },
  [ErrorType.API_ERROR]: {
    userMessage: 'Request failed. Please try again.',
    debugMessage: 'API request failed'
  },
  [ErrorType.TIMEOUT_ERROR]: {
    userMessage: 'Request timed out. Please try again.',
    debugMessage: 'Operation timed out'
  },
  [ErrorType.WHEEL_ERROR]: {
    userMessage: 'Wheel system error. Please refresh the page.',
    debugMessage: 'Wheel component error'
  },
  [ErrorType.PHYSICS_ERROR]: {
    userMessage: 'Physics simulation error. Please refresh the page.',
    debugMessage: 'Physics engine error'
  },
  [ErrorType.RENDER_ERROR]: {
    userMessage: 'Display error. Please refresh the page.',
    debugMessage: 'Rendering error occurred'
  },
  [ErrorType.STATE_ERROR]: {
    userMessage: 'Application state error. Please refresh the page.',
    debugMessage: 'State management error'
  },
  [ErrorType.VALIDATION_ERROR]: {
    userMessage: 'Invalid input. Please check your data.',
    debugMessage: 'Input validation failed'
  },
  [ErrorType.AUTHENTICATION_ERROR]: {
    userMessage: 'Authentication required. Please log in.',
    debugMessage: 'Authentication failed'
  },
  [ErrorType.AUTHORIZATION_ERROR]: {
    userMessage: 'Access denied. Insufficient permissions.',
    debugMessage: 'Authorization failed'
  },
  [ErrorType.UNKNOWN_ERROR]: {
    userMessage: 'An unexpected error occurred. Please try again.',
    debugMessage: 'Unknown error occurred'
  },
  [ErrorType.CONFIGURATION_ERROR]: {
    userMessage: 'Configuration error. Please contact support.',
    debugMessage: 'Configuration error'
  },
  [ErrorType.DEPENDENCY_ERROR]: {
    userMessage: 'System dependency error. Please contact support.',
    debugMessage: 'Dependency error'
  }
};
