/**
 * WebSocket message type definitions based on MESSAGE_SPECIFICATIONS.md
 */

/**
 * Base message structure for all WebSocket communications
 */
export interface BaseMessage {
  type: string;
  content?: any;
}

/**
 * Client to Server message types
 */
export type ClientMessageType = 
  | 'chat_message'
  | 'spin_result'
  | 'workflow_status_request';

/**
 * Server to Client message types
 */
export type ServerMessageType =
  | 'system_message'
  | 'chat_message'
  | 'processing_status'
  | 'wheel_data'
  | 'error'
  | 'workflow_status'
  | 'activity_details'
  | 'debug_info'
  | 'conversation_state_update'
  | 'progress_update'
  | 'progress_monitoring_update'
  | 'performance_metrics'
  | 'stage_timing'
  | 'workflow_progress';

/**
 * Chat message from client to server
 */
export interface ChatMessageRequest extends BaseMessage {
  type: 'chat_message';
  content: {
    message: string;
    user_profile_id: string;
    timestamp?: string;
    metadata?: {
      requested_workflow?: WorkflowType;
      [key: string]: any; // Allow additional metadata fields
    };
  };
}

/**
 * Spin result from client to server
 */
export interface SpinResultRequest extends BaseMessage {
  type: 'spin_result';
  content: {
    activity_tailored_id: string;
    name: string;
    description?: string;
    user_profile_id: string;
    metadata?: {
      conversation_phase?: any;
      awaiting_response_type?: any;
      last_workflow?: any;
      session_context?: any;
      [key: string]: any;
    };
  };
}

/**
 * Workflow status request from client to server
 */
export interface WorkflowStatusRequest extends BaseMessage {
  type: 'workflow_status_request';
  content: {
    workflow_id: string;
  };
}

/**
 * System message from server to client
 */
export interface SystemMessageResponse extends BaseMessage {
  type: 'system_message';
  content: string;
}

/**
 * Chat message from server to client
 */
export interface ChatMessageResponse extends BaseMessage {
  type: 'chat_message';
  content: string;
  is_user: boolean;
}

/**
 * Processing status from server to client
 */
export interface ProcessingStatusResponse extends BaseMessage {
  type: 'processing_status';
  status: ProcessingStatus;
}

/**
 * Wheel data from server to client
 */
export interface WheelDataResponse extends BaseMessage {
  type: 'wheel_data';
  wheel: {
    name: string;
    items: WheelItemData[];
  };
  mentor_context?: MentorContext;
  workflow_insights?: WorkflowInsights;
}

/**
 * Error message from server to client
 */
export interface ErrorResponse extends BaseMessage {
  type: 'error';
  content: string | {
    content: string;
    code?: string;
    details?: Record<string, any>;
  };
}

/**
 * Workflow status from server to client
 */
export interface WorkflowStatusResponse extends BaseMessage {
  type: 'workflow_status';
  workflow_id: string;
  status: WorkflowStatus;
  stage?: string;
  data?: Record<string, any>;
}

/**
 * Activity details from server to client
 */
export interface ActivityDetailsResponse extends BaseMessage {
  type: 'activity_details';
  details: {
    id: string;
    name: string;
    detailed_description: string;
    preparation_steps: string[];
    tips_for_success: string[];
    reflection_questions: string[];
  };
}

/**
 * Debug info from server to client
 */
export interface DebugInfoResponse extends BaseMessage {
  type: 'debug_info';
  content: Record<string, any>;
}

/**
 * Conversation state update from server to client
 */
export interface ConversationStateUpdateResponse extends BaseMessage {
  type: 'conversation_state_update';
  content: Record<string, any>;
}

/**
 * Progress update from server to client with detailed performance metrics
 */
export interface ProgressUpdateResponse extends BaseMessage {
  type: 'progress_update';
  data: {
    tracker_id: string;
    stage_id: string;
    stage_name: string;
    stage: 'initializing' | 'processing' | 'executing' | 'completing' | 'completed' | 'error';
    progress_percent: number;
    message: string;
    timestamp: string;
    priority: 'low' | 'normal' | 'high' | 'critical';
    workflow_type?: string;
    metrics?: {
      duration_ms?: number;
      tokens_used?: number;
      cost_estimate?: number;
      memory_usage_mb?: number;
      cpu_usage_percent?: number;
      custom_metrics?: Record<string, any>;
    };
    substages?: ProgressStageData[];
  };
}

/**
 * Performance metrics from server to client
 */
export interface PerformanceMetricsResponse extends BaseMessage {
  type: 'performance_metrics';
  data: {
    tracker_id: string;
    workflow_type: string;
    total_duration_ms: number;
    stage_breakdown: {
      stage_id: string;
      stage_name: string;
      duration_ms: number;
      percentage_of_total: number;
      tokens_used?: number;
      cost_estimate?: number;
      bottleneck_score?: number;
    }[];
    performance_score: number;
    bottlenecks: {
      stage_id: string;
      severity: 'low' | 'medium' | 'high' | 'critical';
      description: string;
      impact_ms: number;
    }[];
    recommendations: string[];
  };
}

/**
 * Stage timing data from server to client
 */
export interface StageTimingResponse extends BaseMessage {
  type: 'stage_timing';
  data: {
    tracker_id: string;
    stage_id: string;
    stage_name: string;
    start_time: string;
    end_time?: string;
    duration_ms?: number;
    expected_duration_ms?: number;
    performance_ratio?: number;
    timing_category: 'fast' | 'normal' | 'slow' | 'critical';
    substage_timings?: {
      name: string;
      duration_ms: number;
      percentage: number;
    }[];
  };
}

/**
 * Workflow progress from server to client
 */
export interface WorkflowProgressResponse extends BaseMessage {
  type: 'workflow_progress';
  data: {
    workflow_id: string;
    workflow_type: string;
    overall_progress: number;
    current_stage: string;
    stages_completed: string[];
    stages_remaining: string[];
    estimated_completion_time_ms?: number;
    performance_indicators: {
      speed: 'fast' | 'normal' | 'slow';
      efficiency: number;
      resource_usage: 'low' | 'medium' | 'high';
    };
    real_time_metrics: {
      tokens_per_second?: number;
      stages_per_minute?: number;
      average_stage_duration_ms?: number;
    };
  };
}

/**
 * Progress monitoring update for admin dashboard
 */
export interface ProgressMonitoringUpdateResponse extends BaseMessage {
  type: 'progress_monitoring_update';
  data: {
    tracker_summary?: {
      tracker_id: string;
      name: string;
      workflow_type?: string;
      user_id?: string;
      total_progress: number;
      is_completed: boolean;
      has_error: boolean;
      total_duration_ms?: number;
      stage_summaries: Record<string, {
        duration_ms?: number;
        tokens_used?: number;
        cost_estimate?: number;
      }>;
    };
    latest_update: {
      stage_id: string;
      stage_name: string;
      stage: string;
      progress_percent: number;
      message: string;
      timestamp: string;
      priority: string;
    };
    system_performance?: {
      active_workflows: number;
      average_completion_time_ms: number;
      success_rate: number;
      current_load: number;
    };
  };
}

/**
 * Union type for all client messages
 */
export type ClientMessage = 
  | ChatMessageRequest
  | SpinResultRequest
  | WorkflowStatusRequest;

/**
 * Union type for all server messages
 */
export type ServerMessage =
  | SystemMessageResponse
  | ChatMessageResponse
  | ProcessingStatusResponse
  | WheelDataResponse
  | ErrorResponse
  | WorkflowStatusResponse
  | ActivityDetailsResponse
  | DebugInfoResponse
  | ConversationStateUpdateResponse
  | ProgressUpdateResponse
  | PerformanceMetricsResponse
  | StageTimingResponse
  | WorkflowProgressResponse
  | ProgressMonitoringUpdateResponse;

/**
 * Workflow types
 */
export type WorkflowType =
  | 'wheel_generation'
  | 'pre_spin_feedback'
  | 'activity_feedback'
  | 'discussion'
  | 'user_onboarding'
  | 'post_spin'
  | 'post_activity';

/**
 * Processing status values
 */
export type ProcessingStatus = 
  | 'processing'
  | 'completed'
  | 'error';

/**
 * Workflow status values
 */
export type WorkflowStatus = 
  | 'initial'
  | 'wheel_generated'
  | 'activity_selected'
  | 'activity_in_progress'
  | 'activity_completed';

/**
 * Wheel item data structure
 */
export interface WheelItemData {
  id: string;
  name: string;
  description: string;
  percentage: number;
  color: string;
  domain: string;
  base_challenge_rating: number;
  activity_tailored_id: string;
}

/**
 * Mentor context from enhanced workflow
 */
export interface MentorContext {
  workflow_summary: {
    completed_successfully: boolean;
    agents_participated: string[];
    execution_quality: string;
  };
  user_insights: {
    trust_level: number;
    trust_phase: string;
    dominant_traits: string[];
    current_mood: string;
    energy_level: string;
  };
  wheel_characteristics: {
    activity_count: number;
    domains_covered: string[];
    wheel_name: string;
    personalization_level: string;
    challenge_level: string;
  };
  communication_guidance: {
    style: string;
    tone: string;
    detail_level: string;
    encouragement_level: string;
  };
}

/**
 * Workflow insights
 */
export interface WorkflowInsights {
  execution_summary: {
    workflow_completed: boolean;
    error_occurred: boolean;
    stages_completed: string[];
  };
  personalization_quality: string;
  next_interaction_suggestions: string[];
}

/**
 * Progress stage data for detailed tracking
 */
export interface ProgressStageData {
  stage_id: string;
  stage_name: string;
  stage_type: 'sequential' | 'parallel' | 'conditional';
  status: 'pending' | 'active' | 'completed' | 'error' | 'skipped';
  progress_percent: number;
  start_time?: string;
  end_time?: string;
  duration_ms?: number;
  expected_duration_ms?: number;
  performance_metrics?: {
    tokens_used?: number;
    cost_estimate?: number;
    memory_peak_mb?: number;
    cpu_avg_percent?: number;
    io_operations?: number;
    network_requests?: number;
    cache_hits?: number;
    cache_misses?: number;
  };
  substages?: ProgressStageData[];
  error_details?: {
    error_type: string;
    error_message: string;
    stack_trace?: string;
    recovery_attempted: boolean;
  };
}

/**
 * Real-time performance indicators
 */
export interface RealTimePerformanceIndicators {
  current_throughput: {
    tokens_per_second: number;
    requests_per_minute: number;
    stages_per_minute: number;
  };
  resource_utilization: {
    cpu_percent: number;
    memory_percent: number;
    disk_io_percent: number;
    network_io_percent: number;
  };
  quality_metrics: {
    success_rate: number;
    average_response_time_ms: number;
    error_rate: number;
    user_satisfaction_score?: number;
  };
  predictive_indicators: {
    estimated_completion_time_ms: number;
    bottleneck_probability: number;
    resource_exhaustion_risk: 'low' | 'medium' | 'high';
    performance_trend: 'improving' | 'stable' | 'degrading';
  };
}

/**
 * WebSocket connection state
 */
export interface WebSocketState {
  readyState: number;
  url: string;
  protocol: string;
  isConnected: boolean;
  lastPingTime: number;
  reconnectAttempts: number;
  lastError?: string | null;
}

/**
 * WebSocket event types
 */
export interface WebSocketEventMap {
  'open': Event;
  'close': CloseEvent;
  'error': Event;
  'message': MessageEvent<string>;
  'ping': CustomEvent<void>;
  'pong': CustomEvent<void>;
}

/**
 * Message queue item
 */
export interface QueuedMessage {
  id: string;
  message: ClientMessage;
  timestamp: number;
  retryCount: number;
  maxRetries: number;
}

/**
 * WebSocket manager configuration
 */
export interface WebSocketManagerConfig {
  url: string;
  reconnectAttempts: number;
  reconnectDelay: number;
  heartbeatInterval: number;
  messageTimeout: number;
  maxQueueSize: number;
}

/**
 * WebSocket configuration alias for compatibility
 */
export type WebSocketConfig = WebSocketManagerConfig;

/**
 * Message validation result
 */
export interface MessageValidationResult {
  isValid: boolean;
  errors: string[];
  sanitizedMessage?: any;
}
