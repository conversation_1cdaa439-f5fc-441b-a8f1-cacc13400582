/**
 * Global type declarations for the frontend application
 */

// Extend Window interface for custom properties
declare global {
  interface Window {
    __GOALI_WS__?: WebSocket;
  }

  // Custom event types for WebSocket
  interface WindowEventMap {
    'websocket-connected': CustomEvent<any>;
  }

  // Vite environment variables
  interface ImportMeta {
    env: {
      MODE: string;
      VITE_APP_MODE?: string;
      VITE_BACKEND_URL?: string;
      VITE_WS_URL?: string;
      [key: string]: any;
    };
  }

  // Global variables for testing
  var global: typeof globalThis;
  var afterAll: (fn: () => void) => void;
}

// Export empty object to make this a module
export {};
