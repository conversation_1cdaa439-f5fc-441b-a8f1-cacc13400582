/**
 * Business Objects - Single Source of Truth
 * 
 * This file contains the authoritative type definitions for all business objects
 * used throughout the frontend application. This ensures consistency and enables
 * better error handling and debugging for beta release.
 * 
 * RULES:
 * 1. All business object types MUST be defined here
 * 2. Other files MUST import from this file, not define their own types
 * 3. Any changes to business objects MUST be made here first
 * 4. All types include debug-friendly properties for beta error reporting
 */

// =============================================================================
// WHEEL BUSINESS OBJECTS
// =============================================================================

/**
 * Individual wheel item/segment - AUTHORITATIVE DEFINITION
 * This represents a single activity in the wheel
 */
export interface WheelItem {
  // Core identification
  id: string;                           // Wheel item ID (e.g., "item_170_1_142")
  activity_tailored_id: string;        // Activity tailored ID for backend operations
  
  // Display properties
  name: string;                         // Activity name (e.g., "Brief Breathing Exercise")
  description: string;                  // Activity description
  percentage: number;                   // Percentage of wheel (0-100)
  color: string;                        // Hex color code (e.g., "#E74C3C")
  
  // Business properties
  domain: string;                       // Domain code (e.g., "physical", "creative")
  base_challenge_rating: number;        // Challenge rating (0-100)
  duration_minutes?: number;            // Expected duration in minutes
  
  // Optional properties for compatibility
  text?: string;                        // Alternative name field for compatibility
  wheel_item_id?: string;              // Alternative ID field for removal operations
  activityId?: string;                 // Alternative activity ID for compatibility
  type?: 'tailored' | 'generic';      // Activity type
  resources_required?: string[];       // Required resources
  
  // Debug properties for beta error reporting
  _debug?: {
    source: 'backend' | 'mock' | 'cache';
    created_at: string;
    last_modified: string;
    validation_errors?: string[];
  };
}

/**
 * Complete wheel data structure - AUTHORITATIVE DEFINITION
 * This represents the entire wheel with all its items
 */
export interface WheelData {
  // Core identification
  id?: string;                          // Wheel database ID
  wheelId: string;                      // Frontend wheel ID for tracking
  
  // Display properties
  name: string;                         // Wheel name (e.g., "Foundation Wheel - 22:25")
  
  // Business data
  items: WheelItem[];                   // Array of wheel items (AUTHORITATIVE: use 'items', not 'segments')
  
  // Metadata
  createdAt: string;                    // ISO timestamp when wheel was created
  user_id?: string;                     // User who owns this wheel
  
  // Optional metadata
  metadata?: {
    trust_phase?: string;
    strategy_id?: string;
    total_duration_minutes?: number;
    domain_distribution?: Record<string, number>;
  };
  
  // Debug properties for beta error reporting
  _debug?: {
    source: 'backend' | 'mock' | 'cache';
    transformation_history: string[];   // Track data transformations
    validation_errors?: string[];
    performance_metrics?: {
      generation_time_ms: number;
      item_count: number;
      domain_count: number;
    };
  };
}

// =============================================================================
// WEBSOCKET MESSAGE TYPES
// =============================================================================

/**
 * Wheel data from backend WebSocket - AUTHORITATIVE DEFINITION
 */
export interface WheelDataMessage {
  type: 'wheel_data';
  wheel: {
    id?: string;
    name: string;
    items: WheelItem[];
    metadata?: {
      created_at: string;
      trust_phase?: string;
      strategy_id?: string;
    };
    timestamp?: string;
    user_id?: string;
  };
  mentor_context?: any;
  workflow_insights?: any;
}

// =============================================================================
// USER BUSINESS OBJECTS
// =============================================================================

/**
 * User profile - AUTHORITATIVE DEFINITION
 */
export interface UserProfile {
  id: string;
  name: string;
  username?: string;
  email?: string;
  is_staff?: boolean;
  trustLevel: number;
  preferences: UserPreferences;
  is_fake?: boolean;
  
  // Debug properties for beta error reporting
  _debug?: {
    session_id: string;
    last_activity: string;
    permissions: string[];
  };
}

/**
 * User preferences - AUTHORITATIVE DEFINITION
 */
export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  animations: boolean;
  soundEnabled: boolean;
  language: string;
}

// =============================================================================
// APPLICATION STATE
// =============================================================================

/**
 * Application state - AUTHORITATIVE DEFINITION
 */
export interface AppState {
  isConnected: boolean;
  isLoading: boolean;
  currentUser: UserProfile | null;
  currentWheel: WheelData | null;
  chatMessages: ChatMessage[];
  error: string | null;
  lastActivity: number;
  
  // Debug properties for beta error reporting
  _debug?: {
    state_version: number;
    last_state_change: string;
    error_history: Array<{
      timestamp: string;
      error: string;
      context: string;
    }>;
  };
}

/**
 * Chat message - AUTHORITATIVE DEFINITION
 */
export interface ChatMessage {
  id: string;
  type: 'user' | 'ai' | 'system';
  content: string;
  timestamp: Date;
  
  // Debug properties for beta error reporting
  _debug?: {
    source: 'websocket' | 'local' | 'cache';
    processing_time_ms?: number;
  };
}

// =============================================================================
// TYPE GUARDS AND VALIDATION
// =============================================================================

/**
 * Type guard to validate WheelData structure
 */
export function isValidWheelData(obj: any): obj is WheelData {
  if (!obj || typeof obj !== 'object') {
    return false;
  }
  
  // Required fields
  if (!obj.wheelId || typeof obj.wheelId !== 'string') {
    return false;
  }
  
  if (!obj.name || typeof obj.name !== 'string') {
    return false;
  }
  
  if (!Array.isArray(obj.items) || obj.items.length === 0) {
    return false;
  }
  
  // Validate each item
  return obj.items.every((item: any) => isValidWheelItem(item));
}

/**
 * Type guard to validate WheelItem structure
 */
export function isValidWheelItem(obj: any): obj is WheelItem {
  if (!obj || typeof obj !== 'object') {
    return false;
  }
  
  // Required fields
  const requiredFields = ['id', 'name', 'percentage', 'color', 'domain'];
  for (const field of requiredFields) {
    if (!obj[field] || (typeof obj[field] !== 'string' && typeof obj[field] !== 'number')) {
      return false;
    }
  }
  
  // Validate percentage range
  if (obj.percentage < 0 || obj.percentage > 100) {
    return false;
  }
  
  return true;
}

// =============================================================================
// ERROR HANDLING FOR BETA RELEASE
// =============================================================================

/**
 * Beta error context for user reporting
 */
export interface BetaErrorContext {
  timestamp: string;
  user_id?: string;
  session_id: string;
  error_type: 'validation' | 'transformation' | 'network' | 'state' | 'unknown';
  error_message: string;
  context_data: {
    current_wheel_id?: string;
    current_state: string;
    last_action: string;
    browser_info: string;
  };
  stack_trace?: string;
  reproduction_steps?: string[];
}

/**
 * Create beta error context for user reporting
 */
export function createBetaErrorContext(
  error: Error | string,
  errorType: BetaErrorContext['error_type'],
  additionalContext: Partial<BetaErrorContext['context_data']> = {}
): BetaErrorContext {
  const errorMessage = typeof error === 'string' ? error : error.message;
  const stackTrace = typeof error === 'string' ? undefined : error.stack;
  
  return {
    timestamp: new Date().toISOString(),
    session_id: `session_${Date.now()}`,
    error_type: errorType,
    error_message: errorMessage,
    context_data: {
      current_state: 'unknown',
      last_action: 'unknown',
      browser_info: navigator.userAgent,
      ...additionalContext
    },
    stack_trace: stackTrace
  };
}
