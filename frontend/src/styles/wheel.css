/**
 * Wheel-specific styles for the spinning wheel component
 * Provides animations, responsive design, and visual enhancements
 */

/* Wheel container animations */
@keyframes wheelGlow {
  0% {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  }
  50% {
    box-shadow: 0 4px 30px rgba(255, 215, 0, 0.4), 0 0 40px rgba(255, 215, 0, 0.2);
  }
  100% {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  }
}

@keyframes spinPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes buttone {
  0%, 20%, 60%, 100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-10px);
  }
  80% {
    transform: translateX(-50%) translateY(-5px);
  }
}

@keyframes statusSlideIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

@keyframes errorShake {
  0%, 100% {
    transform: translate(-50%, -50%);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translate(-52%, -50%);
  }
  20%, 40%, 60%, 80% {
    transform: translate(-48%, -50%);
  }
}

/* Wheel component styles */
game-wheel {
  --wheel-primary-color: #ff6b6b;
  --wheel-secondary-color: #4ecdc4;
  --wheel-accent-color: #ffd700;
  --wheel-text-color: #ffffff;
  --wheel-background-color: #1a1a1a;
  --wheel-border-color: #333333;
  --wheel-shadow-color: rgba(0, 0, 0, 0.3);
  --wheel-glow-color: rgba(255, 215, 0, 0.4);
}

/* Spinning state styles */
game-wheel[spinning] .wheel-container {
  animation: wheelGlow 2s ease-in-out infinite, spinPulse 0.5s ease-in-out infinite;
}

game-wheel[spinning] .spin-button {
  animation: buttonBounce 1s ease-in-out infinite;
}

/* Status message animations */
game-wheel .wheel-status.visible {
  animation: statusSlideIn 0.3s ease-out;
}

/* Error message animations */
game-wheel .error-message {
  animation: errorShake 0.5s ease-in-out;
}

/* Responsive wheel sizing */
@media (max-width: 1200px) {
  game-wheel {
    --wheel-size: min(90vw, 90vh);
  }
}

@media (max-width: 768px) {
  game-wheel {
    --wheel-size: min(95vw, 95vh);
  }
  
  game-wheel .wheel-container {
    box-shadow: 0 2px 15px var(--wheel-shadow-color);
  }
  
  game-wheel .spin-button {
    padding: 8px 16px;
    font-size: 14px;
    bottom: 5px;
  }
  
  game-wheel .wheel-status {
    top: 10px;
    font-size: 12px;
    padding: 6px 12px;
  }
}

@media (max-width: 480px) {
  game-wheel {
    --wheel-size: min(98vw, 98vh);
  }
  
  game-wheel .spin-button {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 20px;
  }
  
  game-wheel .wheel-status {
    font-size: 11px;
    padding: 4px 8px;
  }
  
  game-wheel .error-message {
    max-width: 250px;
    padding: 12px;
    font-size: 13px;
  }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  game-wheel .wheel-canvas {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  game-wheel .wheel-container,
  game-wheel .spin-button,
  game-wheel .wheel-status,
  game-wheel .error-message {
    animation: none !important;
  }
  
  game-wheel .wheel-container {
    transition: none;
  }
}

/* Focus styles for accessibility */
game-wheel .spin-button:focus {
  outline: 3px solid var(--wheel-accent-color);
  outline-offset: 2px;
}

game-wheel .wheel-canvas:focus {
  outline: 2px solid var(--wheel-accent-color);
  outline-offset: -2px;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  game-wheel {
    --wheel-background-color: #0a0a0a;
    --wheel-border-color: #444444;
    --wheel-shadow-color: rgba(0, 0, 0, 0.5);
  }
}

/* Light mode support */
@media (prefers-color-scheme: light) {
  game-wheel {
    --wheel-background-color: #f5f5f5;
    --wheel-border-color: #cccccc;
    --wheel-shadow-color: rgba(0, 0, 0, 0.2);
    --wheel-text-color: #333333;
  }
  
  game-wheel .wheel-status {
    background: rgba(255, 255, 255, 0.9);
    color: var(--wheel-text-color);
  }
}

/* Print styles */
@media print {
  game-wheel {
    break-inside: avoid;
  }
  
  game-wheel .spin-button,
  game-wheel .wheel-status {
    display: none;
  }
  
  game-wheel .wheel-container {
    box-shadow: none;
    border: 2px solid #000;
  }
}

/* Loading state styles */
game-wheel[loading] .wheel-container {
  opacity: 0.7;
  pointer-events: none;
}

game-wheel[loading] .wheel-container::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 40px;
  height: 40px;
  margin: -20px 0 0 -20px;
  border: 4px solid var(--wheel-border-color);
  border-top-color: var(--wheel-primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Winner highlight styles */
game-wheel .winner-highlight {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: linear-gradient(45deg, var(--wheel-accent-color), #ffed4e);
  color: #333;
  padding: 12px 24px;
  border-radius: 25px;
  font-weight: bold;
  font-size: 18px;
  box-shadow: 0 4px 20px rgba(255, 215, 0, 0.5);
  animation: winnerPulse 1s ease-in-out infinite;
  z-index: 10;
}

@keyframes winnerPulse {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    transform: translate(-50%, -50%) scale(1.05);
  }
}

/* Touch-friendly interaction areas */
@media (pointer: coarse) {
  game-wheel .spin-button {
    min-height: 44px;
    min-width: 88px;
  }
  
  game-wheel .wheel-canvas {
    touch-action: manipulation;
  }
}

/* Landscape orientation adjustments */
@media (orientation: landscape) and (max-height: 600px) {
  game-wheel .spin-button {
    bottom: 5px;
    padding: 6px 12px;
    font-size: 12px;
  }
  
  game-wheel .wheel-status {
    top: 5px;
    font-size: 11px;
    padding: 4px 8px;
  }
}

/* Container query support (future-proofing) */
@container (max-width: 400px) {
  game-wheel .spin-button {
    font-size: 11px;
    padding: 5px 10px;
  }
}

/* Custom scrollbar for overflow areas */
game-wheel ::-webkit-scrollbar {
  width: 8px;
}

game-wheel ::-webkit-scrollbar-track {
  background: var(--wheel-background-color);
}

game-wheel ::-webkit-scrollbar-thumb {
  background: var(--wheel-border-color);
  border-radius: 4px;
}

game-wheel ::-webkit-scrollbar-thumb:hover {
  background: var(--wheel-primary-color);
}
