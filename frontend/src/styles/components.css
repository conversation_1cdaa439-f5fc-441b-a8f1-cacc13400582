/**
 * Component-specific styles for the Goali frontend
 * Provides shared styles and component integration
 */

/* Import wheel-specific styles */
@import './wheel.css';

/* App Shell Component Styles */
app-shell {
  display: block;
  width: 100%;
  min-height: 100vh;
  opacity: 0;
  transition: opacity 0.5s ease-in-out;
}

app-shell.loaded {
  opacity: 1;
}

/* Game Wheel Component Integration */
game-wheel {
  display: block;
  width: 100%;
  height: 100%;
  min-height: 400px;
  max-height: 600px;
  aspect-ratio: 1;
  margin: 0 auto;
}

/* Chat Interface Component Integration */
chat-interface {
  display: block;
  width: 100%;
  height: 400px;
  min-height: 300px;
  max-height: 600px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* Message Bubble Component Integration */
message-bubble {
  display: block;
  width: 100%;
  margin: 4px 0;
}

/* Responsive Layout Adjustments */
@media (min-width: 1024px) {
  .app-container {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 2rem;
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
  }
  
  .wheel-section {
    grid-column: 1;
  }
  
  .chat-section {
    grid-column: 2;
    height: 100vh;
    max-height: 800px;
    position: sticky;
    top: 2rem;
  }
  
  chat-interface {
    height: 100%;
    max-height: none;
  }
  
  game-wheel {
    max-height: 800px;
  }
}

@media (max-width: 1023px) {
  .app-container {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    padding: 1rem;
  }
  
  .wheel-section {
    order: 1;
  }
  
  .chat-section {
    order: 2;
  }
  
  chat-interface {
    height: 50vh;
    min-height: 300px;
  }
}

@media (max-width: 768px) {
  .app-container {
    gap: 1rem;
    padding: 0.5rem;
  }
  
  game-wheel {
    min-height: 300px;
    max-height: 400px;
  }
  
  chat-interface {
    height: 40vh;
    min-height: 250px;
  }
}

/* Component Loading States */
.component-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.component-loading::after {
  content: '';
  width: 32px;
  height: 32px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top-color: #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Component Error States */
.component-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  padding: 2rem;
  background: rgba(255, 0, 0, 0.1);
  border: 2px dashed rgba(255, 0, 0, 0.3);
  border-radius: 12px;
  color: #ff4757;
  text-align: center;
}

.component-error-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.component-error-message {
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.component-error-details {
  font-size: 0.875rem;
  opacity: 0.8;
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  app-shell,
  game-wheel,
  chat-interface,
  message-bubble {
    transition: none !important;
    animation: none !important;
  }
  
  .component-loading::after {
    animation: none;
    border-top-color: transparent;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  game-wheel {
    border: 2px solid currentColor;
  }
  
  chat-interface {
    border: 1px solid currentColor;
  }
  
  message-bubble {
    outline: 1px solid currentColor;
    outline-offset: 2px;
  }
}

/* Print Styles */
@media print {
  app-shell {
    background: white !important;
    color: black !important;
  }
  
  .chat-section {
    display: none;
  }
  
  game-wheel {
    max-height: none;
    page-break-inside: avoid;
  }
}

/* Focus Management */
app-shell:focus-within game-wheel {
  outline: 2px solid #667eea;
  outline-offset: 4px;
  border-radius: 50%;
}

chat-interface:focus-within {
  outline: 2px solid #667eea;
  outline-offset: 2px;
  border-radius: 12px;
}

/* Component Interaction States */
game-wheel:hover {
  transform: scale(1.02);
  transition: transform 0.2s ease;
}

game-wheel[spinning] {
  transform: scale(1.05);
  filter: drop-shadow(0 8px 32px rgba(102, 126, 234, 0.3));
}

chat-interface[processing] {
  opacity: 0.8;
  pointer-events: none;
}

/* Theme Integration */
:root {
  --component-border-radius: 12px;
  --component-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  --component-shadow-hover: 0 8px 32px rgba(0, 0, 0, 0.15);
  --component-transition: all 0.2s ease;
}

@media (prefers-color-scheme: dark) {
  :root {
    --component-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    --component-shadow-hover: 0 8px 32px rgba(0, 0, 0, 0.4);
  }
  
  .component-loading {
    background: rgba(255, 255, 255, 0.02);
  }
  
  .component-error {
    background: rgba(255, 0, 0, 0.05);
    border-color: rgba(255, 0, 0, 0.2);
  }
}

/* Animation Utilities */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Component Entry Animations */
game-wheel {
  animation: fadeIn 0.6s ease-out;
}

chat-interface {
  animation: slideInFromRight 0.5s ease-out;
}

message-bubble {
  animation: fadeIn 0.3s ease-out;
}

/* Utility Classes */
.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.component-container {
  position: relative;
  border-radius: var(--component-border-radius);
  box-shadow: var(--component-shadow);
  transition: var(--component-transition);
}

.component-container:hover {
  box-shadow: var(--component-shadow-hover);
}
