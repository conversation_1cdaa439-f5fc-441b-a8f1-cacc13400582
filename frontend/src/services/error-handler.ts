/**
 * Main error handler service that coordinates error classification, reporting, and notifications
 * Implements the IErrorHandler interface with comprehensive error management
 */

import {
  AppError,
  ErrorNotification,
  ErrorContext,
  ErrorHandlerConfig,
  ErrorRecoveryStrategy,
  IErrorHandler,
  NotificationType,
  ErrorLevel,
  ErrorType
} from '../types/error-types.js';
import { ErrorClassificationService } from './error-classification.js';
import { ErrorReportingService } from './error-reporting.js';

/**
 * Main error handler service
 */
export class ErrorHandler implements IErrorHandler {
  private static instance: ErrorHandler | null = null;
  private activeErrors: Map<string, AppError> = new Map();
  private activeNotifications: Map<string, ErrorNotification> = new Map();
  private config: ErrorHandlerConfig;
  private recoveryStrategies: Map<ErrorType, ErrorRecoveryStrategy> = new Map();

  constructor(config: Partial<ErrorHandlerConfig> = {}) {
    this.config = {
      enableReporting: true,
      enableRetry: true,
      maxRetryAttempts: 3,
      retryDelay: 1000,
      debugMode: !window.location.hostname.includes('production'),
      productionMode: window.location.hostname.includes('production'),
      reportingEndpoint: '/api/track-event/',
      ...config
    };

    this.setupRecoveryStrategies();
    this.setupGlobalErrorHandlers();
  }

  /**
   * Get singleton instance
   */
  static getInstance(config?: Partial<ErrorHandlerConfig>): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler(config);
    }
    return ErrorHandler.instance;
  }

  /**
   * Handle an error with full processing pipeline
   */
  async handleError(error: Error | AppError, context?: ErrorContext): Promise<void> {
    try {
      // Classify the error if it's a raw Error
      const appError = error instanceof Error 
        ? ErrorClassificationService.classifyError(error, context)
        : error;

      // Check for duplicate errors
      if (this.isDuplicateError(appError)) {
        console.log(`Duplicate error ignored: ${appError.id}`);
        return;
      }

      // Store the error
      this.activeErrors.set(appError.id, appError);

      // Log the error
      this.logError(appError);

      // Attempt recovery if possible
      const recovered = await this.attemptRecovery(appError);
      if (recovered) {
        appError.autoResolved = true;
        console.log(`✅ Error auto-recovered: ${appError.id}`);
      }

      // Report to backend if enabled
      if (this.config.enableReporting) {
        await this.reportError(appError);
      }

      // Show notification if appropriate
      if (this.shouldShowNotification(appError)) {
        this.showNotification(this.createNotification(appError));
      }

      // Emit error event for other components
      this.emitErrorEvent(appError);

    } catch (handlingError) {
      console.error('❌ Error in error handler:', handlingError);
      // Prevent infinite loops by not handling errors in the error handler
    }
  }

  /**
   * Report error to backend
   */
  async reportError(error: AppError): Promise<void> {
    try {
      await ErrorReportingService.reportError(error);
    } catch (reportingError) {
      console.warn('Failed to report error:', reportingError);
      // Queue for later if reporting fails
      ErrorReportingService.queueErrorForLaterReporting(error);
    }
  }

  /**
   * Show error notification
   */
  showNotification(notification: ErrorNotification): void {
    this.activeNotifications.set(notification.id, notification);

    // Dispatch event to notification manager
    window.dispatchEvent(new CustomEvent('show-error-notification', {
      detail: notification
    }));

    // Auto-dismiss if configured
    if (notification.displayDuration && notification.displayDuration > 0) {
      setTimeout(() => {
        this.dismissNotification(notification.id);
      }, notification.displayDuration);
    }
  }

  /**
   * Dismiss error notification
   */
  dismissNotification(notificationId: string): void {
    const notification = this.activeNotifications.get(notificationId);
    if (notification) {
      this.activeNotifications.delete(notificationId);
      
      // Call dismiss callback if provided
      if (notification.onDismiss) {
        notification.onDismiss();
      }

      // Dispatch event to notification manager
      window.dispatchEvent(new CustomEvent('dismiss-error-notification', {
        detail: { notificationId }
      }));
    }
  }

  /**
   * Retry operation for an error
   */
  async retryOperation(error: AppError): Promise<boolean> {
    if (!ErrorClassificationService.canAutoRetry(error)) {
      return false;
    }

    try {
      // Increment retry count
      error.retryCount = (error.retryCount || 0) + 1;

      // Dispatch retry event
      window.dispatchEvent(new CustomEvent('error-retry', {
        detail: { error }
      }));

      return true;
    } catch (retryError) {
      console.error('Failed to retry operation:', retryError);
      return false;
    }
  }

  /**
   * Clear all errors
   */
  clearErrors(): void {
    this.activeErrors.clear();
    this.activeNotifications.clear();
    
    window.dispatchEvent(new CustomEvent('clear-all-errors'));
  }

  /**
   * Get active errors
   */
  getActiveErrors(): AppError[] {
    return Array.from(this.activeErrors.values());
  }

  /**
   * Setup recovery strategies for different error types
   */
  private setupRecoveryStrategies(): void {
    // Connection error recovery
    this.recoveryStrategies.set(ErrorType.CONNECTION_ERROR, {
      canRecover: (error) => (error.retryCount || 0) < 3,
      recover: async (error) => {
        await this.delay(1000 * Math.pow(2, error.retryCount || 0));
        return this.testConnection();
      },
      maxAttempts: 3,
      backoffMultiplier: 2
    });

    // WebSocket error recovery
    this.recoveryStrategies.set(ErrorType.WEBSOCKET_ERROR, {
      canRecover: (error) => (error.retryCount || 0) < 5,
      recover: async (error) => {
        await this.delay(2000 * Math.pow(1.5, error.retryCount || 0));
        return this.reconnectWebSocket();
      },
      maxAttempts: 5,
      backoffMultiplier: 1.5
    });
  }

  /**
   * Setup global error handlers
   */
  private setupGlobalErrorHandlers(): void {
    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.handleError(new Error(event.reason), {
        component: 'global-promise-handler',
        method: 'unhandledrejection'
      });
    });

    // Handle global JavaScript errors
    window.addEventListener('error', (event) => {
      this.handleError(event.error || new Error(event.message), {
        component: 'global-error-handler',
        method: 'error',
        metadata: {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno
        }
      });
    });
  }

  /**
   * Check if error is duplicate
   */
  private isDuplicateError(error: AppError): boolean {
    const existingErrors = Array.from(this.activeErrors.values());
    return existingErrors.some(existing => 
      ErrorClassificationService.areErrorsSimilar(existing, error) &&
      (Date.now() - existing.timestamp.getTime()) < 5000 // Within 5 seconds
    );
  }

  /**
   * Attempt error recovery
   */
  private async attemptRecovery(error: AppError): Promise<boolean> {
    const strategy = this.recoveryStrategies.get(error.type);
    if (!strategy || !strategy.canRecover(error)) {
      return false;
    }

    try {
      return await strategy.recover(error);
    } catch (recoveryError) {
      console.warn(`Recovery failed for ${error.type}:`, recoveryError);
      return false;
    }
  }

  /**
   * Check if notification should be shown
   */
  private shouldShowNotification(error: AppError): boolean {
    return ErrorClassificationService.shouldShowError(
      error, 
      this.config.debugMode, 
      this.config.productionMode
    );
  }

  /**
   * Create notification from error
   */
  private createNotification(error: AppError): ErrorNotification {
    const notificationType = ErrorClassificationService.getNotificationType(error);
    
    return {
      id: `notification_${error.id}`,
      error,
      notificationType,
      displayDuration: this.getDisplayDuration(error),
      dismissible: error.level !== ErrorLevel.CRITICAL,
      showInProduction: !this.config.debugMode,
      showInDebug: this.config.debugMode,
      requiresSupport: ErrorClassificationService.requiresSupport(error),
      onDismiss: () => {
        this.activeErrors.delete(error.id);
      },
      onRetry: ErrorClassificationService.canAutoRetry(error) 
        ? () => this.retryOperation(error)
        : undefined
    };
  }

  /**
   * Get display duration for error type
   */
  private getDisplayDuration(error: AppError): number | undefined {
    switch (error.level) {
      case ErrorLevel.TEMPORARY:
        return 5000; // 5 seconds
      case ErrorLevel.NON_CRITICAL:
        return 8000; // 8 seconds
      case ErrorLevel.CRITICAL:
        return undefined; // Persistent
      default:
        return 5000;
    }
  }

  /**
   * Log error appropriately
   */
  private logError(error: AppError): void {
    const logMessage = ErrorClassificationService.formatForLogging(error);
    
    switch (error.level) {
      case ErrorLevel.CRITICAL:
        console.error(logMessage, error);
        break;
      case ErrorLevel.NON_CRITICAL:
        console.warn(logMessage, error);
        break;
      case ErrorLevel.TEMPORARY:
        console.log(logMessage, error);
        break;
    }
  }

  /**
   * Emit error event for other components
   */
  private emitErrorEvent(error: AppError): void {
    window.dispatchEvent(new CustomEvent('app-error', {
      detail: { error }
    }));
  }

  /**
   * Test connection (recovery helper)
   */
  private async testConnection(): Promise<boolean> {
    try {
      const response = await fetch('/api/health/', { 
        method: 'GET',
        cache: 'no-cache'
      });
      return response.ok;
    } catch {
      return false;
    }
  }

  /**
   * Reconnect WebSocket (recovery helper)
   */
  private async reconnectWebSocket(): Promise<boolean> {
    try {
      // This would trigger WebSocket reconnection
      window.dispatchEvent(new CustomEvent('websocket-reconnect'));
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Utility delay function
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<ErrorHandlerConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Get current configuration
   */
  getConfig(): ErrorHandlerConfig {
    return { ...this.config };
  }
}
