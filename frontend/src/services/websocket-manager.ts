/**
 * WebSocket Manager Service
 * Handles WebSocket connections, reconnection logic, message queuing, and heartbeat
 */

import type {
  WebSocketManagerConfig,
  WebSocketState,
  QueuedMessage,
  ClientMessage,
  ServerMessage,
  MessageValidationResult,
} from '../types/websocket-types.js';
import { ErrorHandler } from './error-handler.js';
import { ErrorType, ErrorLevel } from '../types/error-types.js';

export class WebSocketManager extends EventTarget {
  private static instance: WebSocketManager;
  private ws: WebSocket | null = null;
  private config: WebSocketManagerConfig | null = null;
  private state: WebSocketState;
  private messageQueue: QueuedMessage[] = [];
  private heartbeatInterval: number | null = null;
  private reconnectTimeout: number | null = null;
  private isReconnecting = false;
  private messageId = 0;
  private errorHandler: ErrorHandler;

  private constructor() {
    super();
    this.state = {
      readyState: WebSocket.CLOSED,
      url: '',
      protocol: '',
      isConnected: false,
      lastPingTime: 0,
      reconnectAttempts: 0,
    };
    this.errorHandler = ErrorHandler.getInstance();
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): WebSocketManager {
    if (!WebSocketManager.instance) {
      WebSocketManager.instance = new WebSocketManager();
    }
    return WebSocketManager.instance;
  }

  /**
   * Initialize WebSocket connection
   */
  public async initialize(config: WebSocketManagerConfig): Promise<void> {
    this.config = config;
    this.state.url = config.url;
    
    return new Promise((resolve, reject) => {
      try {
        this.connect();
        
        // Set up one-time listeners for initial connection
        const onOpen = () => {
          this.removeEventListener('open', onOpen);
          this.removeEventListener('error', onError);
          resolve();
        };
        
        const onError = (event: Event) => {
          this.removeEventListener('open', onOpen);
          this.removeEventListener('error', onError);

          const error = new Error('Failed to establish WebSocket connection');
          this.errorHandler.handleError(error, {
            component: 'websocket-manager',
            method: 'initialize',
            metadata: {
              url: config.url,
              event: event
            }
          });

          reject(error);
        };
        
        this.addEventListener('open', onOpen);
        this.addEventListener('error', onError);
        
        // Timeout for initial connection
        setTimeout(() => {
          this.removeEventListener('open', onOpen);
          this.removeEventListener('error', onError);

          const timeoutError = new Error('WebSocket connection timeout');
          this.errorHandler.handleError(timeoutError, {
            component: 'websocket-manager',
            method: 'initialize',
            metadata: {
              url: config.url,
              timeout: 40000
            }
          });

          reject(timeoutError);
        }, 40000);
        
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Establish WebSocket connection
   */
  private connect(): void {
    if (!this.config) {
      throw new Error('WebSocket manager not initialized');
    }

    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      return;
    }

    try {
      this.ws = new WebSocket(this.config.url);
      this.setupEventListeners();
      this.updateState();
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      this.scheduleReconnect();
    }
  }

  /**
   * Setup WebSocket event listeners
   */
  private setupEventListeners(): void {
    if (!this.ws) return;

    this.ws.onopen = (event) => {
      console.log('✅ WebSocket connected');
      this.state.isConnected = true;
      this.state.reconnectAttempts = 0;
      this.isReconnecting = false;
      this.updateState();
      this.startHeartbeat();
      this.processMessageQueue();

      // Expose WebSocket globally for debug panel
      (window as any).__GOALI_WS__ = this.ws;

      this.dispatchEvent(new CustomEvent('open', { detail: event }));
      this.dispatchEvent(new CustomEvent('connection-change', { detail: true }));

      // Emit global event for late connection recovery
      window.dispatchEvent(new CustomEvent('websocket-connected', { detail: true }));
    };

    this.ws.onclose = (event) => {
      const isCleanClose = event.code === 1000;
      const reason = event.reason || this.getCloseReasonText(event.code);

      console.log(`🔌 WebSocket disconnected: ${event.code} (${reason})`);

      this.state.isConnected = false;
      this.state.lastError = isCleanClose ? null : `Connection closed: ${reason}`;
      this.updateState();
      this.stopHeartbeat();

      // Handle unexpected disconnections through error handler
      if (!isCleanClose) {
        this.errorHandler.handleError(new Error(`WebSocket connection closed unexpectedly: ${reason}`), {
          component: 'websocket-manager',
          method: 'onclose',
          metadata: {
            code: event.code,
            reason: reason,
            wasClean: isCleanClose,
            reconnectAttempts: this.state.reconnectAttempts,
            url: this.config?.url
          }
        });
      }

      this.dispatchEvent(new CustomEvent('close', {
        detail: {
          event,
          code: event.code,
          reason,
          wasClean: isCleanClose
        }
      }));
      this.dispatchEvent(new CustomEvent('connection-change', { detail: false }));

      if (!isCleanClose && this.config) {
        console.log('🔄 Scheduling reconnection due to unexpected close');
        this.scheduleReconnect();
      }
    };

    this.ws.onerror = (event) => {
      const errorMessage = 'WebSocket connection error occurred';
      console.error('❌ WebSocket error:', event);

      this.state.lastError = errorMessage;
      this.updateState();

      // Handle error through error handler
      this.errorHandler.handleError(new Error(errorMessage), {
        component: 'websocket-manager',
        method: 'onerror',
        metadata: {
          event: event,
          readyState: this.ws?.readyState,
          url: this.config?.url,
          reconnectAttempts: this.state.reconnectAttempts
        }
      });

      this.dispatchEvent(new CustomEvent('error', {
        detail: {
          event,
          message: errorMessage,
          timestamp: Date.now()
        }
      }));
    };

    this.ws.onmessage = (event) => {
      this.handleMessage(event);
    };
  }

  /**
   * Handle incoming WebSocket messages
   */
  private handleMessage(event: MessageEvent): void {
    try {
      const data = JSON.parse(event.data) as ServerMessage;
      
      // Validate message structure
      const validation = this.validateServerMessage(data);
      if (!validation.isValid) {
        console.warn('Invalid message received:', validation.errors);

        // Handle validation error through error handler
        this.errorHandler.handleError(new Error(`Invalid server message: ${validation.errors.join(', ')}`), {
          component: 'websocket-manager',
          method: 'handleMessage',
          metadata: {
            rawData: event.data,
            validationErrors: validation.errors,
            messageType: data?.type || 'unknown'
          }
        });
        return;
      }

      // Handle heartbeat responses
      if (data.type === 'system_message' && data.content === 'pong') {
        this.dispatchEvent(new CustomEvent('pong'));
        return;
      }

      // Dispatch message event
      this.dispatchEvent(new CustomEvent('message', {
        detail: { data, originalEvent: event }
      }));

      // Dispatch specific message type events
      this.dispatchEvent(new CustomEvent(`message-${data.type}`, {
        detail: data  // Pass full message object so handlers can access all properties
      }));
      
    } catch (error) {
      console.error('Failed to parse WebSocket message:', error);

      // Handle parsing error through error handler
      this.errorHandler.handleError(error as Error, {
        component: 'websocket-manager',
        method: 'handleMessage',
        metadata: {
          rawData: event.data,
          dataLength: event.data?.length || 0
        }
      });
    }
  }

  /**
   * Send message through WebSocket
   */
  public send(message: ClientMessage): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.isConnected()) {
        // Queue message for later sending
        this.queueMessage(message);
        resolve();
        return;
      }

      try {
        const validation = this.validateClientMessage(message);
        if (!validation.isValid) {
          const validationError = new Error(`Invalid message: ${validation.errors.join(', ')}`);
          this.errorHandler.handleError(validationError, {
            component: 'websocket-manager',
            method: 'send',
            metadata: {
              message: message,
              validationErrors: validation.errors
            }
          });
          reject(validationError);
          return;
        }

        const messageString = JSON.stringify(message);
        this.ws!.send(messageString);
        resolve();

      } catch (error) {
        console.error('Failed to send message:', error);

        // Handle send error through error handler
        this.errorHandler.handleError(error as Error, {
          component: 'websocket-manager',
          method: 'send',
          metadata: {
            message: message,
            messageType: message.type,
            isConnected: this.isConnected(),
            readyState: this.ws?.readyState
          }
        });

        this.queueMessage(message);
        reject(error);
      }
    });
  }

  /**
   * Queue message for later sending
   */
  private queueMessage(message: ClientMessage): void {
    const queuedMessage: QueuedMessage = {
      id: `msg_${++this.messageId}`,
      message,
      timestamp: Date.now(),
      retryCount: 0,
      maxRetries: 3,
    };

    this.messageQueue.push(queuedMessage);
    
    // Limit queue size
    if (this.messageQueue.length > 100) {
      this.messageQueue.shift();
    }
  }

  /**
   * Process queued messages
   */
  private async processMessageQueue(): Promise<void> {
    if (!this.isConnected() || this.messageQueue.length === 0) {
      return;
    }

    const messagesToProcess = [...this.messageQueue];
    this.messageQueue = [];

    for (const queuedMessage of messagesToProcess) {
      try {
        await this.send(queuedMessage.message);
      } catch (error) {
        queuedMessage.retryCount++;
        if (queuedMessage.retryCount < queuedMessage.maxRetries) {
          this.messageQueue.push(queuedMessage);
        } else {
          console.error('Failed to send queued message after retries:', error);
        }
      }
    }
  }

  /**
   * Start heartbeat mechanism
   */
  private startHeartbeat(): void {
    if (!this.config || this.heartbeatInterval) return;

    this.heartbeatInterval = window.setInterval(() => {
      if (this.isConnected()) {
        this.ping();
      }
    }, this.config.heartbeatInterval);
  }

  /**
   * Stop heartbeat mechanism
   */
  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  /**
   * Send ping message
   */
  private ping(): void {
    this.state.lastPingTime = Date.now();
    // Skip ping for now to avoid backend errors
    // TODO: Implement proper ping/pong mechanism
    console.debug('Ping skipped - using connection state check instead');
  }

  /**
   * Schedule reconnection attempt
   */
  private scheduleReconnect(): void {
    if (!this.config || this.isReconnecting) return;
    
    if (this.state.reconnectAttempts >= this.config.reconnectAttempts) {
      console.error('Max reconnection attempts reached');
      this.dispatchEvent(new CustomEvent('max-reconnects-reached'));
      return;
    }

    this.isReconnecting = true;
    this.state.reconnectAttempts++;
    
    const delay = this.config.reconnectDelay * Math.pow(2, this.state.reconnectAttempts - 1);
    
    console.log(`Reconnecting in ${delay}ms (attempt ${this.state.reconnectAttempts})`);
    
    this.reconnectTimeout = window.setTimeout(() => {
      this.connect();
    }, delay);
  }

  /**
   * Get human-readable close reason text
   */
  private getCloseReasonText(code: number): string {
    const reasons: Record<number, string> = {
      1000: 'Normal closure',
      1001: 'Going away',
      1002: 'Protocol error',
      1003: 'Unsupported data',
      1004: 'Reserved',
      1005: 'No status received',
      1006: 'Abnormal closure',
      1007: 'Invalid frame payload data',
      1008: 'Policy violation',
      1009: 'Message too big',
      1010: 'Mandatory extension',
      1011: 'Internal server error',
      1012: 'Service restart',
      1013: 'Try again later',
      1014: 'Bad gateway',
      1015: 'TLS handshake'
    };

    return reasons[code] || `Unknown close code: ${code}`;
  }

  /**
   * Update internal state
   */
  private updateState(): void {
    if (this.ws) {
      this.state.readyState = this.ws.readyState;
      this.state.protocol = this.ws.protocol;
    }
  }

  /**
   * Check if WebSocket is connected
   */
  public isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN && this.state.isConnected;
  }

  /**
   * Get current connection state
   */
  public getState(): WebSocketState {
    return { ...this.state };
  }

  /**
   * Validate client message
   */
  private validateClientMessage(message: ClientMessage): MessageValidationResult {
    const errors: string[] = [];
    
    if (!message.type) {
      errors.push('Message type is required');
    }
    
    if (!message.content) {
      errors.push('Message content is required');
    }
    
    // Type-specific validation
    if (message.type === 'chat_message') {
      if (!message.content.message) {
        errors.push('Chat message content is required');
      }
      if (!message.content.user_profile_id) {
        errors.push('User profile ID is required');
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Validate server message
   */
  private validateServerMessage(message: ServerMessage): MessageValidationResult {
    const errors: string[] = [];
    
    if (!message.type) {
      errors.push('Message type is required');
    }
    
    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Close WebSocket connection
   */
  public close(): void {
    this.stopHeartbeat();
    
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }
    
    if (this.ws) {
      this.ws.close(1000, 'Client closing');
      this.ws = null;
    }
    
    this.state.isConnected = false;
    this.updateState();
  }

  /**
   * Add message event listener
   */
  public onMessage(type: string, callback: (data: any) => void): void {
    this.addEventListener(`message-${type}`, ((event: CustomEvent) => {
      callback(event.detail);
    }) as EventListener);
  }

  /**
   * Add connection change listener
   */
  public onConnectionChange(callback: (connected: boolean) => void): void {
    this.addEventListener('connection-change', ((event: CustomEvent) => {
      callback(event.detail);
    }) as EventListener);
  }

  /**
   * Send message to server
   */
  public sendMessage(type: string, data: any): void {
    const message: ClientMessage = {
      type: type as any,
      content: data
    };
    this.send(message);
  }

  /**
   * Disconnect from server
   */
  public disconnect(): void {
    this.close();
  }

  /**
   * Destroy the WebSocket manager
   */
  public destroy(): void {
    this.close();
    this.messageQueue = [];
    this.config = null;
  }
}
