/**
 * Error reporting service for sending errors to backend and creating HistoryEvents
 * Integrates with the backend HistoryEvent system for comprehensive error tracking
 */

import {
  AppError,
  ErrorReportPayload,
  ErrorContext,
  ErrorLevel,
  ErrorType
} from '../types/error-types.js';

/**
 * Error reporting service
 */
export class ErrorReportingService {
  private static readonly REPORTING_ENDPOINT = '/api/track-event/';
  private static readonly MAX_RETRY_ATTEMPTS = 3;
  private static readonly RETRY_DELAY = 1000; // 1 second

  /**
   * Report error to backend for HistoryEvent creation
   */
  static async reportError(error: AppError, context?: ErrorContext): Promise<boolean> {
    try {
      const payload = this.createReportPayload(error, context);
      
      // Attempt to send the error report
      const success = await this.sendErrorReport(payload);
      
      if (success) {
        console.log(`✅ Error reported successfully: ${error.id}`);
        return true;
      } else {
        console.warn(`⚠️ Failed to report error: ${error.id}`);
        return false;
      }
    } catch (reportingError) {
      console.error('❌ Error reporting service failed:', reportingError);
      return false;
    }
  }

  /**
   * Create error report payload for backend
   */
  private static createReportPayload(error: AppError, context?: ErrorContext): ErrorReportPayload {
    const eventType = `frontend_error_${error.level}`;
    
    return {
      event_type: eventType,
      content_type: 'UserProfile',
      object_id: this.getCurrentUserId() || 'anonymous',
      details: {
        error_type: error.type,
        error_message: error.message,
        error_level: error.level,
        component: error.component || 'unknown',
        timestamp: error.timestamp.toISOString(),
        user_agent: error.userAgent || navigator.userAgent,
        url: error.url || window.location.href,
        stack_trace: error.stack,
        metadata: {
          ...error.metadata,
          context: context
        },
        retry_count: error.retryCount,
        auto_resolved: error.autoResolved,
        requires_support: error.level === ErrorLevel.CRITICAL,
        debug_mode: this.isDebugMode()
      }
    };
  }

  /**
   * Send error report to backend with retry logic
   */
  private static async sendErrorReport(payload: ErrorReportPayload): Promise<boolean> {
    let attempts = 0;
    
    while (attempts < this.MAX_RETRY_ATTEMPTS) {
      try {
        const headers: Record<string, string> = {
          'Content-Type': 'application/json',
          'X-CSRFToken': this.getCSRFToken()
        };

        // Add debug user ID header for staff impersonation
        if (this.isDebugMode()) {
          const debugUserId = localStorage.getItem('debug_user_id');
          if (debugUserId) {
            headers['X-Debug-User-ID'] = debugUserId;
          }
        }

        const response = await fetch(this.REPORTING_ENDPOINT, {
          method: 'POST',
          headers: headers,
          body: JSON.stringify(payload)
        });

        if (response.ok) {
          const result = await response.json();
          if (result.success) {
            return true;
          } else {
            console.warn('Backend rejected error report:', result.error);
            return false;
          }
        } else {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      } catch (error) {
        attempts++;
        console.warn(`Error reporting attempt ${attempts} failed:`, error);
        
        if (attempts < this.MAX_RETRY_ATTEMPTS) {
          // Wait before retrying with exponential backoff
          await this.delay(this.RETRY_DELAY * Math.pow(2, attempts - 1));
        }
      }
    }
    
    return false;
  }

  /**
   * Batch report multiple errors
   */
  static async reportErrors(errors: AppError[], context?: ErrorContext): Promise<number> {
    let successCount = 0;
    
    // Report errors in parallel but limit concurrency
    const batchSize = 5;
    for (let i = 0; i < errors.length; i += batchSize) {
      const batch = errors.slice(i, i + batchSize);
      const promises = batch.map(error => this.reportError(error, context));
      const results = await Promise.allSettled(promises);
      
      results.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value) {
          successCount++;
        } else {
          console.warn(`Failed to report error in batch:`, batch[index]);
        }
      });
    }
    
    return successCount;
  }

  /**
   * Report critical error immediately (high priority)
   */
  static async reportCriticalError(error: AppError, context?: ErrorContext): Promise<void> {
    // For critical errors, try to report immediately and don't give up easily
    const maxAttempts = 5;
    let attempts = 0;
    
    while (attempts < maxAttempts) {
      const success = await this.reportError(error, context);
      if (success) {
        break;
      }
      
      attempts++;
      if (attempts < maxAttempts) {
        await this.delay(500 * attempts); // Shorter delay for critical errors
      }
    }
    
    if (attempts >= maxAttempts) {
      console.error('❌ Failed to report critical error after maximum attempts:', error);
      // Could implement fallback reporting mechanism here (e.g., localStorage queue)
    }
  }

  /**
   * Queue error for later reporting (when offline)
   */
  static queueErrorForLaterReporting(error: AppError, context?: ErrorContext): void {
    try {
      const queue = this.getErrorQueue();
      queue.push({
        error,
        context,
        timestamp: Date.now()
      });
      
      // Limit queue size to prevent memory issues
      if (queue.length > 100) {
        queue.splice(0, queue.length - 100);
      }
      
      localStorage.setItem('error_report_queue', JSON.stringify(queue));
    } catch (storageError) {
      console.warn('Failed to queue error for later reporting:', storageError);
    }
  }

  /**
   * Process queued errors when connection is restored
   */
  static async processQueuedErrors(): Promise<void> {
    try {
      const queue = this.getErrorQueue();
      if (queue.length === 0) {
        return;
      }
      
      console.log(`📤 Processing ${queue.length} queued error reports...`);
      
      let processedCount = 0;
      for (const queuedItem of queue) {
        const success = await this.reportError(queuedItem.error, queuedItem.context);
        if (success) {
          processedCount++;
        }
      }
      
      // Clear the queue
      localStorage.removeItem('error_report_queue');
      
      console.log(`✅ Processed ${processedCount}/${queue.length} queued error reports`);
    } catch (error) {
      console.error('Failed to process queued errors:', error);
    }
  }

  /**
   * Get error queue from localStorage
   */
  private static getErrorQueue(): Array<{
    error: AppError;
    context?: ErrorContext;
    timestamp: number;
  }> {
    try {
      const queueData = localStorage.getItem('error_report_queue');
      return queueData ? JSON.parse(queueData) : [];
    } catch (error) {
      console.warn('Failed to parse error queue:', error);
      return [];
    }
  }

  /**
   * Get current user ID from authentication system
   */
  private static getCurrentUserId(): string | null {
    try {
      // Try to get user ID from various sources

      // 1. Check for user data in window object (if set by backend)
      if ((window as any).currentUser?.id) {
        return (window as any).currentUser.id;
      }

      // 2. Check for user ID in localStorage (if stored by auth system)
      const storedUserId = localStorage.getItem('user_id');
      if (storedUserId) {
        return storedUserId;
      }

      // 3. Check for user ID in sessionStorage
      const sessionUserId = sessionStorage.getItem('user_id');
      if (sessionUserId) {
        return sessionUserId;
      }

      // 4. Try to extract from meta tags (if set by Django template)
      const userIdMeta = document.querySelector('meta[name="user-id"]');
      if (userIdMeta) {
        return userIdMeta.getAttribute('content');
      }

      // 5. For debug mode, check for debug user ID
      if (this.isDebugMode()) {
        const debugUserId = localStorage.getItem('debug_user_id') || '1'; // Default to user 1 for debug
        return debugUserId;
      }

      return null;
    } catch (error) {
      console.warn('Failed to get current user ID:', error);
      return null;
    }
  }

  /**
   * Check if debug mode is enabled
   */
  private static isDebugMode(): boolean {
    // This should be implemented based on your configuration system
    return !window.location.hostname.includes('production');
  }

  /**
   * Get CSRF token for Django
   */
  private static getCSRFToken(): string {
    // Try multiple sources for CSRF token

    // 1. Form input (most common)
    const tokenInput = document.querySelector('[name=csrfmiddlewaretoken]') as HTMLInputElement;
    if (tokenInput?.value) {
      return tokenInput.value;
    }

    // 2. Meta tag
    const tokenMeta = document.querySelector('meta[name=csrf-token]') as HTMLMetaElement;
    if (tokenMeta?.content) {
      return tokenMeta.content;
    }

    // 3. Cookie (if using CSRF_USE_SESSIONS = False)
    const csrfCookie = document.cookie
      .split('; ')
      .find(row => row.startsWith('csrftoken='));
    if (csrfCookie) {
      return csrfCookie.split('=')[1];
    }

    // 4. Try to get from window object
    if ((window as any).csrfToken) {
      return (window as any).csrfToken;
    }

    return '';
  }

  /**
   * Utility function for delays
   */
  private static delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Check if error reporting is enabled
   */
  static isReportingEnabled(): boolean {
    // Could be controlled by configuration or user preferences
    return true;
  }

  /**
   * Get error reporting statistics
   */
  static getReportingStats(): {
    queuedErrors: number;
    lastReportTime: Date | null;
  } {
    const queue = this.getErrorQueue();
    const lastReportTime = localStorage.getItem('last_error_report_time');
    
    return {
      queuedErrors: queue.length,
      lastReportTime: lastReportTime ? new Date(lastReportTime) : null
    };
  }

  /**
   * Clear error reporting queue
   */
  static clearErrorQueue(): void {
    localStorage.removeItem('error_report_queue');
  }
}
