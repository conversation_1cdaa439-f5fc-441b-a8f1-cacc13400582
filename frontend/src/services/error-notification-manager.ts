/**
 * Error notification manager for coordinating different notification components
 * Manages the display of error banners, popups, and persistent notifications
 */

import {
  ErrorNotification,
  ErrorHandlerConfig,
  IErrorNotificationManager,
  NotificationType,
  ErrorLevel
} from '../types/error-types.js';

/**
 * Error notification manager service
 */
export class ErrorNotificationManager implements IErrorNotificationManager {
  private static instance: ErrorNotificationManager | null = null;
  private activeNotifications: Map<string, ErrorNotification> = new Map();
  private config: Partial<ErrorHandlerConfig> = {};
  
  // Component references
  private bannerComponent: HTMLElement | null = null;
  private popupComponent: HTMLElement | null = null;
  private persistentComponent: HTMLElement | null = null;

  constructor() {
    this.setupEventListeners();
    this.initializeComponents();
  }

  /**
   * Get singleton instance
   */
  static getInstance(): ErrorNotificationManager {
    if (!ErrorNotificationManager.instance) {
      ErrorNotificationManager.instance = new ErrorNotificationManager();
    }
    return ErrorNotificationManager.instance;
  }

  /**
   * Show error notification
   */
  show(notification: ErrorNotification): void {
    // Store the notification
    this.activeNotifications.set(notification.id, notification);

    // Route to appropriate component based on notification type
    switch (notification.notificationType) {
      case NotificationType.BANNER:
        this.showBanner(notification);
        break;
      case NotificationType.POPUP:
        this.showPopup(notification);
        break;
      case NotificationType.PERSISTENT:
        this.showPersistent(notification);
        break;
      case NotificationType.TOAST:
        this.showToast(notification);
        break;
    }

    console.log(`📢 Showing ${notification.notificationType} notification:`, notification.error.message);
  }

  /**
   * Dismiss notification
   */
  dismiss(notificationId: string): void {
    const notification = this.activeNotifications.get(notificationId);
    if (!notification) return;

    // Remove from active notifications
    this.activeNotifications.delete(notificationId);

    // Hide from appropriate component
    switch (notification.notificationType) {
      case NotificationType.BANNER:
        this.hideBanner();
        break;
      case NotificationType.POPUP:
        this.hidePopup();
        break;
      case NotificationType.PERSISTENT:
        this.hidePersistent();
        break;
      case NotificationType.TOAST:
        this.hideToast(notificationId);
        break;
    }

    console.log(`🔕 Dismissed ${notification.notificationType} notification:`, notificationId);
  }

  /**
   * Dismiss all notifications
   */
  dismissAll(): void {
    const notificationIds = Array.from(this.activeNotifications.keys());
    notificationIds.forEach(id => this.dismiss(id));
  }

  /**
   * Get active notifications
   */
  getActive(): ErrorNotification[] {
    return Array.from(this.activeNotifications.values());
  }

  /**
   * Configure the notification manager
   */
  configure(config: Partial<ErrorHandlerConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * Setup event listeners for global error events
   */
  private setupEventListeners(): void {
    // Listen for show notification events
    window.addEventListener('show-error-notification', (event: Event) => {
      const customEvent = event as CustomEvent;
      this.show(customEvent.detail);
    });

    // Listen for dismiss notification events
    window.addEventListener('dismiss-error-notification', (event: Event) => {
      const customEvent = event as CustomEvent;
      this.dismiss(customEvent.detail.notificationId);

    });

    // Listen for clear all errors events
    window.addEventListener('clear-all-errors', () => {
      this.dismissAll();
    });

    // Listen for component events

    document.addEventListener('error-dismissed', (event: Event) => {
      const customEvent = event as CustomEvent;
      const errorId = customEvent.detail.error?.id;

      if (errorId) {
        // Find notification by error ID
        for (const [notificationId, notification] of this.activeNotifications) {
          if (notification.error.id === errorId) {
            this.dismiss(notificationId);
            break;
          }
        }
      }
    });

    document.addEventListener('error-retry', (event: Event) => {
      const customEvent = event as CustomEvent;
      const error = customEvent.detail.error;
      console.log(`🔄 Retrying operation for error: ${error.id}`);
      // The actual retry logic is handled by the error handler
    });
  }

  /**
   * Initialize notification components
   */
  private initializeComponents(): void {
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        this.findComponents();
      });
    } else {
      this.findComponents();
    }
  }

  /**
   * Find and cache component references
   */
  private findComponents(): void {
    // These components should be created by the app-shell
    this.bannerComponent = document.querySelector('error-banner');
    this.popupComponent = document.querySelector('error-popup');
    this.persistentComponent = document.querySelector('error-persistent');

    // Create components if they don't exist
    if (!this.bannerComponent) {
      this.bannerComponent = this.createBannerComponent();
    }
    if (!this.popupComponent) {
      this.popupComponent = this.createPopupComponent();
    }
    if (!this.persistentComponent) {
      this.persistentComponent = this.createPersistentComponent();
    }
  }

  /**
   * Show banner notification
   */
  private showBanner(notification: ErrorNotification): void {
    if (!this.bannerComponent) return;

    // Set properties
    (this.bannerComponent as any).error = notification.error;
    (this.bannerComponent as any).isDebugMode = this.config.debugMode || false;
    (this.bannerComponent as any).dismissible = notification.dismissible;
    (this.bannerComponent as any).showRetry = !!notification.onRetry;
  }

  /**
   * Show popup notification
   */
  private showPopup(notification: ErrorNotification): void {
    if (!this.popupComponent) return;

    // Only show in debug mode for non-critical errors
    if (notification.error.level === ErrorLevel.NON_CRITICAL && !this.config.debugMode) {
      return;
    }

    // Set properties
    (this.popupComponent as any).error = notification.error;
    (this.popupComponent as any).visible = true;
    (this.popupComponent as any).isDebugMode = this.config.debugMode || false;
    (this.popupComponent as any).displayDuration = notification.displayDuration || 5000;
  }

  /**
   * Show persistent notification
   */
  private showPersistent(notification: ErrorNotification): void {
    if (!this.persistentComponent) return;

    // Set properties
    (this.persistentComponent as any).error = notification.error;
    (this.persistentComponent as any).visible = true;
    (this.persistentComponent as any).isDebugMode = this.config.debugMode || false;
    (this.persistentComponent as any).allowDismiss = notification.dismissible;
  }

  /**
   * Show toast notification (simple implementation)
   */
  private showToast(notification: ErrorNotification): void {
    // Create a simple toast element
    const toast = document.createElement('div');
    toast.id = `toast-${notification.id}`;
    toast.style.cssText = `
      position: fixed;
      bottom: 20px;
      right: 20px;
      background: #333;
      color: white;
      padding: 12px 16px;
      border-radius: 4px;
      z-index: 10003;
      max-width: 300px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.3);
      animation: slideIn 0.3s ease;
    `;
    
    toast.innerHTML = `
      <div style="display: flex; align-items: center; gap: 8px;">
        <span>⚠️</span>
        <span>${notification.error.message}</span>
        ${notification.dismissible ? '<button onclick="this.parentElement.parentElement.remove()" style="background:none;border:none;color:white;cursor:pointer;margin-left:auto;">✕</button>' : ''}
      </div>
    `;

    document.body.appendChild(toast);

    // Auto-remove after duration
    if (notification.displayDuration) {
      setTimeout(() => {
        this.hideToast(notification.id);
      }, notification.displayDuration);
    }
  }

  /**
   * Hide banner notification
   */
  private hideBanner(): void {
    if (this.bannerComponent) {
      (this.bannerComponent as any).error = null;
    }
  }

  /**
   * Hide popup notification
   */
  private hidePopup(): void {
    if (this.popupComponent) {
      (this.popupComponent as any).visible = false;
    }
  }

  /**
   * Hide persistent notification
   */
  private hidePersistent(): void {
    if (this.persistentComponent) {
      (this.persistentComponent as any).visible = false;
    }
  }

  /**
   * Hide toast notification
   */
  private hideToast(notificationId: string): void {
    const toast = document.getElementById(`toast-${notificationId}`);
    if (toast) {
      toast.remove();
    }
  }

  /**
   * Create banner component
   */
  private createBannerComponent(): HTMLElement {
    const banner = document.createElement('error-banner');
    document.body.appendChild(banner);
    return banner;
  }

  /**
   * Create popup component
   */
  private createPopupComponent(): HTMLElement {
    const popup = document.createElement('error-popup');
    document.body.appendChild(popup);
    return popup;
  }

  /**
   * Create persistent component
   */
  private createPersistentComponent(): HTMLElement {
    const persistent = document.createElement('error-persistent');
    document.body.appendChild(persistent);
    return persistent;
  }

  /**
   * Get notification statistics
   */
  getStats(): {
    total: number;
    byType: Record<NotificationType, number>;
    byLevel: Record<ErrorLevel, number>;
  } {
    const notifications = Array.from(this.activeNotifications.values());
    
    const stats = {
      total: notifications.length,
      byType: {
        [NotificationType.BANNER]: 0,
        [NotificationType.POPUP]: 0,
        [NotificationType.PERSISTENT]: 0,
        [NotificationType.TOAST]: 0
      },
      byLevel: {
        [ErrorLevel.TEMPORARY]: 0,
        [ErrorLevel.NON_CRITICAL]: 0,
        [ErrorLevel.CRITICAL]: 0
      }
    };

    notifications.forEach(notification => {
      stats.byType[notification.notificationType]++;
      stats.byLevel[notification.error.level]++;
    });

    return stats;
  }
}
