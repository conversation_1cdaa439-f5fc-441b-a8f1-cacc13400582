/**
 * State Manager Service
 * Centralized application state management with reactive updates
 */

import type {
  AppConfig,
  AppState,
  UserProfile,
  WheelData,
  ChatMessage,
  LoadingState,
  ConnectionState,
} from '@/types/app-types';

export class StateManager extends EventTarget {
  private static instance: StateManager;
  private state: AppState;
  private config: AppConfig | null = null;
  private subscribers = new Map<string, Set<(state: AppState) => void>>();

  private constructor() {
    super();
    this.state = this.getInitialState();
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): StateManager {
    if (!StateManager.instance) {
      StateManager.instance = new StateManager();
    }
    return StateManager.instance;
  }

  /**
   * Initialize state manager with configuration
   */
  public initialize(config: AppConfig): void {
    this.config = config;
    
    // Load persisted state from localStorage
    this.loadPersistedState();
    
    // Set up periodic state persistence
    setInterval(() => {
      this.persistState();
    }, 30000); // Save every 30 seconds
    
    // Save state on page unload
    window.addEventListener('beforeunload', () => {
      this.persistState();
    });
    
    console.log('✅ State manager initialized');
  }

  /**
   * Get initial state
   */
  private getInitialState(): AppState {
    return {
      isConnected: false,
      isLoading: false,
      currentUser: null,
      currentWheel: null,
      chatMessages: [],
      error: null,
      lastActivity: Date.now(),
    };
  }

  /**
   * Get current state (read-only)
   */
  public getState(): Readonly<AppState> {
    return { ...this.state };
  }

  /**
   * Update state with partial changes
   */
  public updateState(updates: Partial<AppState>): void {
    const previousState = { ...this.state };
    this.state = { ...this.state, ...updates };
    
    // Update last activity timestamp
    this.state.lastActivity = Date.now();
    
    // Notify subscribers
    this.notifySubscribers(previousState);
    
    // Dispatch global state change event
    this.dispatchEvent(new CustomEvent('state-updated', {
      detail: { 
        previousState, 
        currentState: this.state, 
        updates 
      }
    }));
    
    if (this.config?.debug) {
      console.log('🔄 State updated:', updates);
    }
  }

  /**
   * Set connection state
   */
  public setConnectionState(isConnected: boolean): void {
    this.updateState({ isConnected });
  }

  /**
   * Set loading state
   */
  public setLoadingState(isLoading: boolean): void {
    this.updateState({ isLoading });
  }

  /**
   * Set current user
   */
  public setCurrentUser(user: UserProfile | null): void {
    this.updateState({ currentUser: user });
  }

  /**
   * Set current wheel data
   */
  public setCurrentWheel(wheel: WheelData | null): void {
    this.updateState({ currentWheel: wheel });
  }

  /**
   * Add chat message
   */
  public addChatMessage(message: ChatMessage): void {
    const chatMessages = [...this.state.chatMessages, message];
    
    // Limit chat history to prevent memory issues
    if (chatMessages.length > 100) {
      chatMessages.splice(0, chatMessages.length - 100);
    }
    
    this.updateState({ chatMessages });
  }

  /**
   * Clear chat messages
   */
  public clearChatMessages(): void {
    this.updateState({ chatMessages: [] });
  }

  /**
   * Set error state
   */
  public setError(error: string | null): void {
    this.updateState({ error });
    
    if (error) {
      console.error('❌ Application error:', error);
    }
  }

  /**
   * Clear error state
   */
  public clearError(): void {
    this.updateState({ error: null });
  }

  /**
   * Subscribe to state changes
   */
  public subscribe(
    key: string, 
    callback: (state: AppState) => void
  ): () => void {
    if (!this.subscribers.has(key)) {
      this.subscribers.set(key, new Set());
    }
    
    this.subscribers.get(key)!.add(callback);
    
    // Return unsubscribe function
    return () => {
      const keySubscribers = this.subscribers.get(key);
      if (keySubscribers) {
        keySubscribers.delete(callback);
        if (keySubscribers.size === 0) {
          this.subscribers.delete(key);
        }
      }
    };
  }

  /**
   * Subscribe to specific state property changes
   */
  public subscribeToProperty<K extends keyof AppState>(
    property: K,
    callback: (value: AppState[K], previousValue: AppState[K]) => void
  ): () => void {
    let previousValue = this.state[property];
    
    return this.subscribe(`property:${property}`, (state) => {
      const currentValue = state[property];
      if (currentValue !== previousValue) {
        callback(currentValue, previousValue);
        previousValue = currentValue;
      }
    });
  }

  /**
   * Notify all subscribers of state changes
   */
  private notifySubscribers(previousState: AppState): void {
    for (const [key, callbacks] of this.subscribers) {
      for (const callback of callbacks) {
        try {
          callback(this.state);
        } catch (error) {
          console.error(`Error in state subscriber ${key}:`, error);
        }
      }
    }
  }

  /**
   * Persist state to localStorage
   */
  private persistState(): void {
    try {
      const persistableState = {
        currentUser: this.state.currentUser,
        chatMessages: this.state.chatMessages.slice(-20), // Only keep last 20 messages
        lastActivity: this.state.lastActivity,
      };
      
      localStorage.setItem('goali-state', JSON.stringify(persistableState));
    } catch (error) {
      console.warn('Failed to persist state:', error);
    }
  }

  /**
   * Load persisted state from localStorage
   */
  private loadPersistedState(): void {
    try {
      const persistedData = localStorage.getItem('goali-state');
      if (persistedData) {
        const parsedState = JSON.parse(persistedData);
        
        // Only restore certain properties
        this.state = {
          ...this.state,
          currentUser: parsedState.currentUser || null,
          chatMessages: Array.isArray(parsedState.chatMessages) 
            ? parsedState.chatMessages 
            : [],
          lastActivity: parsedState.lastActivity || Date.now(),
        };
        
        console.log('✅ Persisted state loaded');
      }
    } catch (error) {
      console.warn('Failed to load persisted state:', error);
    }
  }

  /**
   * Reset state to initial values
   */
  public reset(): void {
    const initialState = this.getInitialState();
    this.state = initialState;
    this.notifySubscribers(initialState);
    
    // Clear persisted state
    try {
      localStorage.removeItem('goali-state');
    } catch (error) {
      console.warn('Failed to clear persisted state:', error);
    }
    
    console.log('🔄 State reset to initial values');
  }

  /**
   * Get state statistics for debugging
   */
  public getStateStats(): {
    messageCount: number;
    subscriberCount: number;
    lastActivity: Date;
    memoryUsage: number;
  } {
    return {
      messageCount: this.state.chatMessages.length,
      subscriberCount: Array.from(this.subscribers.values())
        .reduce((total, set) => total + set.size, 0),
      lastActivity: new Date(this.state.lastActivity),
      memoryUsage: JSON.stringify(this.state).length,
    };
  }

  /**
   * Validate state integrity
   */
  public validateState(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    // Validate chat messages
    if (!Array.isArray(this.state.chatMessages)) {
      errors.push('Chat messages must be an array');
    } else {
      for (const [index, message] of this.state.chatMessages.entries()) {
        if (!message.id || !message.content || typeof message.isUser !== 'boolean') {
          errors.push(`Invalid chat message at index ${index}`);
        }
      }
    }
    
    // Validate wheel data
    if (this.state.currentWheel) {
      if (!this.state.currentWheel.name || !Array.isArray(this.state.currentWheel.items)) {
        errors.push('Invalid wheel data structure');
      }
    }
    
    // Validate user profile
    if (this.state.currentUser) {
      if (!this.state.currentUser.id || !this.state.currentUser.name) {
        errors.push('Invalid user profile structure');
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Destroy state manager
   */
  public destroy(): void {
    this.persistState();
    this.subscribers.clear();
    this.config = null;
  }
}
