/**
 * Authentication Service
 * Handles user authentication and token management for production mode
 */

import type { AuthToken, UserProfile } from '../types/app-types';
import { ConfigService } from './config-service.js';

export class AuthService {
  private static instance: AuthService;
  private token: AuthToken | null = null;
  private user: UserProfile | null = null;
  private configService = ConfigService.getInstance();

  private constructor() {
    this.loadStoredAuth();
  }

  public static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  /**
   * Check if user is authenticated
   */
  public isAuthenticated(): boolean {
    // PRODUCTION AUTHENTICATION FIX: Force authentication on production domain
    const isProductionDomain = window.location.hostname.includes('ondigitalocean.app');
    const requireAuth = this.configService.getConfig().security.requireAuthentication || isProductionDomain;

    if (!requireAuth) {
      return true; // Authentication not required in debug mode
    }

    return this.token !== null && this.isTokenValid();
  }

  /**
   * Get current user
   */
  public getCurrentUser(): UserProfile | null {
    return this.user;
  }

  /**
   * Get current token
   */
  public getToken(): string | null {
    return this.token?.token || null;
  }

  /**
   * Authenticate with username/password
   */
  public async authenticate(username: string, password: string): Promise<boolean> {
    try {
      const config = this.configService.getConfig();
      const response = await fetch(`${config.api.baseUrl}/api/auth/login/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Include cookies for session-based auth
        body: JSON.stringify({ username, password }),
      });

      if (response.ok) {
        const data = await response.json();

        // Validate response structure
        if (!data.user || !data.user.id || !data.token) {
          console.error('Invalid authentication response structure:', data);
          return false;
        }

        this.token = {
          token: data.token,
          userId: data.user.id,
          expiresAt: Date.now() + (data.expires_in * 1000),
          permissions: data.permissions || [],
        };
        this.user = data.user;
        this.storeAuth();
        this.notifyAuthChange();
        return true;
      }

      return false;
    } catch (error) {
      console.error('Authentication failed:', error);
      return false;
    }
  }

  /**
   * Authenticate with token (session-based)
   */
  public async authenticateWithToken(token: string): Promise<boolean> {
    try {
      const config = this.configService.getConfig();
      const response = await fetch(`${config.api.baseUrl}/api/auth/verify/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Include cookies for session-based auth
      });

      if (response.ok) {
        const data = await response.json();
        this.token = {
          token: token,
          userId: data.user.id,
          expiresAt: Date.now() + (data.expires_in * 1000),
          permissions: data.permissions || [],
        };
        this.user = data.user;
        this.storeAuth();
        this.notifyAuthChange();
        return true;
      }

      return false;
    } catch (error) {
      console.error('Token authentication failed:', error);
      return false;
    }
  }

  /**
   * Logout user
   */
  public async logout(): Promise<void> {
    try {
      if (this.token) {
        const config = this.configService.getConfig();
        await fetch(`${config.api.baseUrl}/api/auth/logout/`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include', // Include cookies for session-based auth
        });
      }
    } catch (error) {
      console.error('Logout request failed:', error);
    } finally {
      this.token = null;
      this.user = null;
      this.clearStoredAuth();
      this.notifyAuthChange();
    }
  }

  /**
   * Refresh authentication token (session-based)
   */
  public async refreshToken(): Promise<boolean> {
    if (!this.token) return false;

    try {
      const config = this.configService.getConfig();
      //<<<<<<< refactor/arch
      // For session-based auth, we verify the session instead of refreshing a token
      //const response = await fetch(`${config.websocket.url.replace('ws://', 'http://').replace('wss://', 'https://').replace('/ws/game/', '')}/api/auth/verify/`, {
      //=======
      const response = await fetch(`${config.api.baseUrl}/api/auth/refresh/`, {

        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Include cookies for session-based auth
      });

      if (response.ok) {
        const data = await response.json();
        this.token = {
          ...this.token,
          expiresAt: Date.now() + (data.expires_in * 1000),
        };
        this.storeAuth();
        return true;
      }

      return false;
    } catch (error) {
      console.error('Token refresh failed:', error);
      return false;
    }
  }

  /**
   * Check if current token is valid
   */
  private isTokenValid(): boolean {
    if (!this.token) return false;
    
    // Check if token is expired (with 5 minute buffer)
    const bufferTime = 5 * 60 * 1000; // 5 minutes
    return Date.now() < (this.token.expiresAt - bufferTime);
  }

  /**
   * Load authentication from storage
   */
  private loadStoredAuth(): void {
    try {
      const storedToken = localStorage.getItem('goali_auth_token');
      const storedUser = localStorage.getItem('goali_auth_user');
      
      if (storedToken && storedUser) {
        this.token = JSON.parse(storedToken);
        this.user = JSON.parse(storedUser);
        
        // Validate stored token
        if (!this.isTokenValid()) {
          this.clearStoredAuth();
        }
      }
    } catch (error) {
      console.error('Failed to load stored auth:', error);
      this.clearStoredAuth();
    }
  }

  /**
   * Store authentication in storage
   */
  private storeAuth(): void {
    if (this.token && this.user) {
      localStorage.setItem('goali_auth_token', JSON.stringify(this.token));
      localStorage.setItem('goali_auth_user', JSON.stringify(this.user));
    }
  }

  /**
   * Clear stored authentication
   */
  private clearStoredAuth(): void {
    localStorage.removeItem('goali_auth_token');
    localStorage.removeItem('goali_auth_user');
    this.token = null;
    this.user = null;
  }

  /**
   * Notify listeners of authentication changes
   */
  private notifyAuthChange(): void {
    window.dispatchEvent(new CustomEvent('auth-changed', {
      detail: { 
        isAuthenticated: this.isAuthenticated(),
        user: this.user 
      }
    }));
  }

  /**
   * Setup automatic token refresh
   */
  public setupAutoRefresh(): void {
    setInterval(async () => {
      if (this.isAuthenticated() && this.token) {
        // Refresh token if it expires in the next 10 minutes
        const refreshTime = 10 * 60 * 1000; // 10 minutes
        if (Date.now() > (this.token.expiresAt - refreshTime)) {
          await this.refreshToken();
        }
      }
    }, 60000); // Check every minute
  }
}

export default AuthService;
