/**
 * Error classification service for categorizing and handling different types of errors
 * Following traditional error handling patterns with proper classification
 */

import {
  AppError,
  ErrorType,
  ErrorLevel,
  NotificationType,
  ErrorContext,
  ERROR_CLASSIFICATION_RULES,
  ERROR_MESSAGES
} from '../types/error-types.js';

/**
 * Error classification service
 */
export class ErrorClassificationService {
  /**
   * Classify a raw error into an AppError with proper categorization
   */
  static classifyError(
    error: Error | string,
    context?: ErrorContext,
    overrides?: Partial<AppError>
  ): AppError {
    const errorMessage = typeof error === 'string' ? error : error.message;
    const errorStack = typeof error === 'string' ? undefined : error.stack;
    
    // Determine error type based on error message and context
    const errorType = this.determineErrorType(errorMessage, context);
    
    // Get classification rules for this error type
    const rules = ERROR_CLASSIFICATION_RULES[errorType];
    
    // Create the classified error
    const classifiedError: AppError = {
      id: this.generateErrorId(),
      type: errorType,
      level: rules.defaultLevel,
      message: errorMessage,
      timestamp: new Date(),
      component: context?.component,
      metadata: {
        originalError: typeof error === 'string' ? error : error.constructor.name,
        context: context,
        rules: rules
      },
      stack: errorStack,
      userAgent: navigator.userAgent,
      url: window.location.href,
      retryCount: 0,
      autoResolved: false,
      ...overrides
    };

    return classifiedError;
  }

  /**
   * Determine error type based on error message and context
   */
  private static determineErrorType(message: string, context?: ErrorContext): ErrorType {
    const lowerMessage = message.toLowerCase();
    
    // Connection-related errors
    if (lowerMessage.includes('connection') || 
        lowerMessage.includes('network') ||
        lowerMessage.includes('fetch')) {
      return ErrorType.CONNECTION_ERROR;
    }
    
    // WebSocket errors
    if (lowerMessage.includes('websocket') || 
        lowerMessage.includes('ws') ||
        context?.component?.includes('websocket')) {
      return ErrorType.WEBSOCKET_ERROR;
    }
    
    // API errors
    if (lowerMessage.includes('api') || 
        lowerMessage.includes('http') ||
        lowerMessage.includes('request')) {
      return ErrorType.API_ERROR;
    }
    
    // Timeout errors
    if (lowerMessage.includes('timeout') || 
        lowerMessage.includes('timed out')) {
      return ErrorType.TIMEOUT_ERROR;
    }
    
    // Wheel-related errors
    if (lowerMessage.includes('wheel') || 
        context?.component?.includes('wheel')) {
      return ErrorType.WHEEL_ERROR;
    }
    
    // Physics errors
    if (lowerMessage.includes('physics') || 
        lowerMessage.includes('collision') ||
        context?.component?.includes('physics')) {
      return ErrorType.PHYSICS_ERROR;
    }
    
    // Render errors
    if (lowerMessage.includes('render') || 
        lowerMessage.includes('display') ||
        lowerMessage.includes('canvas')) {
      return ErrorType.RENDER_ERROR;
    }
    
    // State errors
    if (lowerMessage.includes('state') || 
        lowerMessage.includes('invalid state')) {
      return ErrorType.STATE_ERROR;
    }
    
    // Validation errors
    if (lowerMessage.includes('validation') || 
        lowerMessage.includes('invalid') ||
        lowerMessage.includes('required')) {
      return ErrorType.VALIDATION_ERROR;
    }
    
    // Authentication errors
    if (lowerMessage.includes('auth') || 
        lowerMessage.includes('login') ||
        lowerMessage.includes('unauthorized')) {
      return ErrorType.AUTHENTICATION_ERROR;
    }
    
    // Authorization errors
    if (lowerMessage.includes('permission') || 
        lowerMessage.includes('forbidden') ||
        lowerMessage.includes('access denied')) {
      return ErrorType.AUTHORIZATION_ERROR;
    }
    
    // Configuration errors
    if (lowerMessage.includes('config') || 
        lowerMessage.includes('configuration')) {
      return ErrorType.CONFIGURATION_ERROR;
    }
    
    // Dependency errors
    if (lowerMessage.includes('dependency') || 
        lowerMessage.includes('module') ||
        lowerMessage.includes('import')) {
      return ErrorType.DEPENDENCY_ERROR;
    }
    
    // Default to unknown error
    return ErrorType.UNKNOWN_ERROR;
  }

  /**
   * Get user-friendly error message
   */
  static getUserMessage(error: AppError, isDebugMode: boolean = false): string {
    const messages = ERROR_MESSAGES[error.type];
    return isDebugMode ? messages.debugMessage : messages.userMessage;
  }

  /**
   * Get notification type for an error
   */
  static getNotificationType(error: AppError): NotificationType {
    const rules = ERROR_CLASSIFICATION_RULES[error.type];
    return rules.notificationType;
  }

  /**
   * Check if error should be shown in current mode
   */
  static shouldShowError(error: AppError, isDebugMode: boolean, isProduction: boolean): boolean {
    const rules = ERROR_CLASSIFICATION_RULES[error.type];
    
    if (isProduction) {
      return rules.showInProduction;
    } else {
      return rules.showInDebug;
    }
  }

  /**
   * Check if error supports auto-retry
   */
  static canAutoRetry(error: AppError): boolean {
    const rules = ERROR_CLASSIFICATION_RULES[error.type];
    return rules.autoRetry && (error.retryCount || 0) < rules.maxRetries;
  }

  /**
   * Get maximum retry attempts for error type
   */
  static getMaxRetries(error: AppError): number {
    const rules = ERROR_CLASSIFICATION_RULES[error.type];
    return rules.maxRetries;
  }

  /**
   * Check if error is critical and requires support
   */
  static requiresSupport(error: AppError): boolean {
    return error.level === ErrorLevel.CRITICAL;
  }

  /**
   * Check if error is temporary
   */
  static isTemporary(error: AppError): boolean {
    return error.level === ErrorLevel.TEMPORARY;
  }

  /**
   * Check if error is non-critical
   */
  static isNonCritical(error: AppError): boolean {
    return error.level === ErrorLevel.NON_CRITICAL;
  }

  /**
   * Generate unique error ID
   */
  private static generateErrorId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Create error context from current application state
   */
  static createContext(
    component: string,
    method?: string,
    additionalData?: Record<string, any>
  ): ErrorContext {
    return {
      component,
      method,
      userProfile: this.getCurrentUserProfile(),
      sessionId: this.getSessionId(),
      ...additionalData
    };
  }

  /**
   * Get current user profile (if available)
   */
  private static getCurrentUserProfile(): { id: string; isStaff: boolean } | undefined {
    // This would be implemented based on your authentication system
    // For now, return undefined
    return undefined;
  }

  /**
   * Get current session ID
   */
  private static getSessionId(): string | undefined {
    // This would be implemented based on your session management
    // For now, return undefined
    return undefined;
  }

  /**
   * Enhance error with additional metadata
   */
  static enhanceError(error: AppError, metadata: Record<string, any>): AppError {
    return {
      ...error,
      metadata: {
        ...error.metadata,
        ...metadata
      }
    };
  }

  /**
   * Check if two errors are similar (for deduplication)
   */
  static areErrorsSimilar(error1: AppError, error2: AppError): boolean {
    return (
      error1.type === error2.type &&
      error1.component === error2.component &&
      error1.message === error2.message
    );
  }

  /**
   * Get error severity score (for prioritization)
   */
  static getErrorSeverity(error: AppError): number {
    switch (error.level) {
      case ErrorLevel.CRITICAL:
        return 3;
      case ErrorLevel.NON_CRITICAL:
        return 2;
      case ErrorLevel.TEMPORARY:
        return 1;
      default:
        return 0;
    }
  }

  /**
   * Format error for logging
   */
  static formatForLogging(error: AppError): string {
    return `[${error.level.toUpperCase()}] ${error.type}: ${error.message} (Component: ${error.component || 'unknown'})`;
  }

  /**
   * Create error summary for reporting
   */
  static createErrorSummary(errors: AppError[]): {
    total: number;
    byLevel: Record<ErrorLevel, number>;
    byType: Record<ErrorType, number>;
    critical: AppError[];
  } {
    const summary = {
      total: errors.length,
      byLevel: {
        [ErrorLevel.CRITICAL]: 0,
        [ErrorLevel.NON_CRITICAL]: 0,
        [ErrorLevel.TEMPORARY]: 0
      },
      byType: {} as Record<ErrorType, number>,
      critical: errors.filter(e => e.level === ErrorLevel.CRITICAL)
    };

    errors.forEach(error => {
      summary.byLevel[error.level]++;
      summary.byType[error.type] = (summary.byType[error.type] || 0) + 1;
    });

    return summary;
  }
}
