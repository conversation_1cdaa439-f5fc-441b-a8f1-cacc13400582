/**
 * TypeScript declarations for domainColorService.js
 */

export interface DomainColorMap {
  [key: string]: string;
}

export interface WheelValidationResult {
  issues: string[];
  suggestions: string[];
  isValid: boolean;
}

export interface WheelData {
  items?: Array<{
    domain?: string;
    color?: string;
    [key: string]: any;
  }>;
  [key: string]: any;
}


export interface Activity {
  id: string;
  name?: string;
  domain?: string;
  percentage?: number;
  [key: string]: any;
}

export interface WheelColorInfo {
  activityId: string;
  domain: string;
  centerColor?: string;
  extremityColor?: string;
  listColor?: string;
  backgroundColor?: string;
  color: string;
  percentage: number;
  segmentSize: string;
}

export interface GenerateWheelColorsOptions {
  dualColorMode?: boolean;
}


/**
 * Get the color for a specific domain code
 */
export function getDomainColor(domainCode: string): string;

/**
 * Get all domain colors as a map
 */
export function getAllDomainColors(): DomainColorMap;

/**
 * Get color with opacity for backgrounds, overlays, etc.
 */
export function getDomainColorWithOpacity(domainCode: string, opacity?: number): string;

/**
 * Get contrasting text color (black or white) for a domain
 */
export function getContrastingTextColor(domainCode: string): string;

/**
 * Validate wheel domains and provide suggestions
 */
export function validateWheelDomains(wheelData: WheelData): WheelValidationResult;

/**
 * Apply colors to wheel data
 */
export function applyColorsToWheel(wheelData: WheelData): WheelData;

/**
 * Generate wheel colors for activities
 */
export function generateWheelColors(activities: Activity[], options?: GenerateWheelColorsOptions): WheelColorInfo[];

/**
 * Get distinct colors for same domain activities
 */
export function getDistinctColorsForSameDomain(domain: string, count: number): string[];

