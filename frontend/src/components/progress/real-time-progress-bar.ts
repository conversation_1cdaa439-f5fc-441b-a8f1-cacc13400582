/**
 * Real-Time Progress Bar Component
 * 
 * A sophisticated progress bar component that displays real-time workflow execution progress
 * with detailed performance metrics, stage-by-stage tracking, and visual performance indicators.
 * 
 * Features:
 * - Real-time progress updates via WebSocket
 * - Stage-by-stage progress visualization
 * - Performance metrics display (timing, tokens, cost)
 * - Bottleneck detection and warnings
 * - Smooth animations and transitions
 * - Responsive design for all screen sizes
 * - Accessibility support
 */

import { LitElement, html, css, PropertyValues } from 'lit';
import { customElement, property, state } from 'lit/decorators.js';
import { classMap } from 'lit/directives/class-map.js';
import { styleMap } from 'lit/directives/style-map.js';
import { 
  ProgressUpdateResponse, 
  PerformanceMetricsResponse, 
  StageTimingResponse,
  WorkflowProgressResponse,
  ProgressStageData 
} from '../../types/websocket-types.js';

interface ProgressStage {
  id: string;
  name: string;
  status: 'pending' | 'active' | 'completed' | 'error' | 'skipped';
  progress: number;
  duration?: number;
  expectedDuration?: number;
  performanceRatio?: number;
  message?: string;
  metrics?: {
    tokensUsed?: number;
    costEstimate?: number;
    memoryUsage?: number;
    cpuUsage?: number;
  };
}

interface PerformanceIndicators {
  speed: 'fast' | 'normal' | 'slow' | 'critical';
  efficiency: number;
  resourceUsage: 'low' | 'medium' | 'high';
  bottleneckRisk: number;
}

@customElement('real-time-progress-bar')
export class RealTimeProgressBar extends LitElement {
  static styles = css`
    :host {
      display: block;
      width: 100%;
      font-family: var(--font-family-base, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
    }

    .progress-container {
      background: var(--color-gray-50, #f9fafb);
      border: 1px solid var(--color-gray-200, #e5e7eb);
      border-radius: var(--radius-lg, 12px);
      padding: var(--spacing-4, 16px);
      box-shadow: var(--shadow-sm, 0 1px 2px 0 rgba(0, 0, 0, 0.05));
    }

    .progress-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-3, 12px);
    }

    .progress-title {
      font-size: var(--font-size-lg, 18px);
      font-weight: var(--font-weight-semibold, 600);
      color: var(--color-gray-900, #111827);
      margin: 0;
    }

    .progress-percentage {
      font-size: var(--font-size-xl, 20px);
      font-weight: var(--font-weight-bold, 700);
      color: var(--color-primary, #3b82f6);
    }

    .progress-bar-container {
      position: relative;
      height: 12px;
      background: var(--color-gray-200, #e5e7eb);
      border-radius: 6px;
      overflow: hidden;
      margin-bottom: var(--spacing-4, 16px);
    }

    .progress-bar {
      height: 100%;
      background: linear-gradient(90deg, var(--color-primary, #3b82f6), var(--color-primary-light, #60a5fa));
      border-radius: 6px;
      transition: width 0.3s ease-in-out;
      position: relative;
    }

    .progress-bar.fast {
      background: linear-gradient(90deg, #10b981, #34d399);
    }

    .progress-bar.slow {
      background: linear-gradient(90deg, #f59e0b, #fbbf24);
    }

    .progress-bar.critical {
      background: linear-gradient(90deg, #ef4444, #f87171);
    }

    .progress-bar::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
      animation: shimmer 2s infinite;
    }

    @keyframes shimmer {
      0% { transform: translateX(-100%); }
      100% { transform: translateX(100%); }
    }

    .current-stage {
      display: flex;
      align-items: center;
      gap: var(--spacing-2, 8px);
      margin-bottom: var(--spacing-3, 12px);
      padding: var(--spacing-2, 8px);
      background: var(--color-blue-50, #eff6ff);
      border-radius: var(--radius-md, 8px);
      border-left: 4px solid var(--color-primary, #3b82f6);
    }

    .stage-icon {
      width: 16px;
      height: 16px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 10px;
      font-weight: bold;
      color: white;
    }

    .stage-icon.active {
      background: var(--color-primary, #3b82f6);
      animation: pulse 2s infinite;
    }

    .stage-icon.completed {
      background: var(--color-success, #10b981);
    }

    .stage-icon.error {
      background: var(--color-error, #ef4444);
    }

    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.5; }
    }

    .stage-info {
      flex: 1;
    }

    .stage-name {
      font-weight: var(--font-weight-medium, 500);
      color: var(--color-gray-900, #111827);
      margin: 0 0 2px 0;
      font-size: var(--font-size-sm, 14px);
    }

    .stage-message {
      font-size: var(--font-size-xs, 12px);
      color: var(--color-gray-600, #4b5563);
      margin: 0;
    }

    .performance-metrics {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: var(--spacing-3, 12px);
      margin-top: var(--spacing-4, 16px);
    }

    .metric-card {
      background: white;
      padding: var(--spacing-3, 12px);
      border-radius: var(--radius-md, 8px);
      border: 1px solid var(--color-gray-200, #e5e7eb);
      text-align: center;
    }

    .metric-value {
      font-size: var(--font-size-lg, 18px);
      font-weight: var(--font-weight-bold, 700);
      color: var(--color-gray-900, #111827);
      margin: 0 0 4px 0;
    }

    .metric-label {
      font-size: var(--font-size-xs, 12px);
      color: var(--color-gray-500, #6b7280);
      margin: 0;
    }

    .stages-timeline {
      margin-top: var(--spacing-4, 16px);
      padding-top: var(--spacing-4, 16px);
      border-top: 1px solid var(--color-gray-200, #e5e7eb);
    }

    .timeline-stage {
      display: flex;
      align-items: center;
      gap: var(--spacing-3, 12px);
      padding: var(--spacing-2, 8px) 0;
      position: relative;
    }

    .timeline-stage:not(:last-child)::after {
      content: '';
      position: absolute;
      left: 8px;
      top: 32px;
      width: 2px;
      height: calc(100% - 16px);
      background: var(--color-gray-200, #e5e7eb);
    }

    .timeline-stage.completed::after {
      background: var(--color-success, #10b981);
    }

    .timeline-icon {
      width: 16px;
      height: 16px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 10px;
      font-weight: bold;
      color: white;
      flex-shrink: 0;
    }

    .timeline-content {
      flex: 1;
    }

    .timeline-name {
      font-weight: var(--font-weight-medium, 500);
      color: var(--color-gray-900, #111827);
      margin: 0 0 2px 0;
      font-size: var(--font-size-sm, 14px);
    }

    .timeline-duration {
      font-size: var(--font-size-xs, 12px);
      color: var(--color-gray-500, #6b7280);
      margin: 0;
    }

    .warning-indicator {
      background: var(--color-warning-light, #fef3c7);
      border: 1px solid var(--color-warning, #f59e0b);
      border-radius: var(--radius-md, 8px);
      padding: var(--spacing-3, 12px);
      margin-top: var(--spacing-3, 12px);
      display: flex;
      align-items: center;
      gap: var(--spacing-2, 8px);
    }

    .warning-icon {
      color: var(--color-warning, #f59e0b);
      font-size: var(--font-size-lg, 18px);
    }

    .warning-text {
      color: var(--color-warning-dark, #92400e);
      font-size: var(--font-size-sm, 14px);
      margin: 0;
    }

    .hidden {
      display: none;
    }

    @media (max-width: 640px) {
      .performance-metrics {
        grid-template-columns: repeat(2, 1fr);
      }
      
      .progress-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-2, 8px);
      }
    }
  `;

  @property({ type: String }) trackerId = '';
  @property({ type: String }) workflowType = '';
  @property({ type: String }) title = 'Processing...';
  @property({ type: Boolean }) showMetrics = true;
  @property({ type: Boolean }) showTimeline = true;
  @property({ type: Boolean }) compact = false;

  @state() private progress = 0;
  @state() private currentStage: ProgressStage | null = null;
  @state() private stages: ProgressStage[] = [];
  @state() private performanceIndicators: PerformanceIndicators = {
    speed: 'normal',
    efficiency: 1.0,
    resourceUsage: 'low',
    bottleneckRisk: 0
  };
  @state() private metrics = {
    totalDuration: 0,
    tokensUsed: 0,
    costEstimate: 0,
    averageStageTime: 0
  };
  @state() private isCompleted = false;
  @state() private hasError = false;
  @state() private warningMessage = '';

  connectedCallback() {
    super.connectedCallback();
    this.addEventListener('progress-update', this.handleProgressUpdate as EventListener);
    this.addEventListener('performance-metrics', this.handlePerformanceMetrics as EventListener);
    this.addEventListener('stage-timing', this.handleStageTiming as EventListener);
    this.addEventListener('workflow-progress', this.handleWorkflowProgress as EventListener);
  }

  disconnectedCallback() {
    super.disconnectedCallback();
    this.removeEventListener('progress-update', this.handleProgressUpdate as EventListener);
    this.removeEventListener('performance-metrics', this.handlePerformanceMetrics as EventListener);
    this.removeEventListener('stage-timing', this.handleStageTiming as EventListener);
    this.removeEventListener('workflow-progress', this.handleWorkflowProgress as EventListener);
  }

  private handleProgressUpdate = (event: Event) => {
    const customEvent = event as CustomEvent<ProgressUpdateResponse>;
    const { data } = customEvent.detail;
    
    if (data.tracker_id !== this.trackerId) return;

    this.progress = data.progress_percent;
    
    // Update current stage
    this.currentStage = {
      id: data.stage_id,
      name: data.stage_name,
      status: data.stage === 'error' ? 'error' : 
              data.stage === 'completed' ? 'completed' : 'active',
      progress: data.progress_percent,
      message: data.message,
      metrics: data.metrics ? {
        tokensUsed: data.metrics.tokens_used,
        costEstimate: data.metrics.cost_estimate,
        memoryUsage: data.metrics.memory_usage_mb,
        cpuUsage: data.metrics.cpu_usage_percent
      } : undefined
    };

    // Update stages list
    const existingStageIndex = this.stages.findIndex(s => s.id === data.stage_id);
    if (existingStageIndex >= 0) {
      this.stages[existingStageIndex] = this.currentStage;
    } else {
      this.stages = [...this.stages, this.currentStage];
    }

    // Check for completion or error
    this.isCompleted = data.stage === 'completed' && data.progress_percent >= 100;
    this.hasError = data.stage === 'error';

    // Update performance indicators based on priority
    if (data.priority === 'critical') {
      this.performanceIndicators.speed = 'critical';
      this.warningMessage = 'Critical performance issue detected';
    } else if (data.priority === 'high') {
      this.performanceIndicators.speed = 'slow';
      this.warningMessage = 'Performance degradation detected';
    }

    this.requestUpdate();
  };

  private handlePerformanceMetrics = (event: Event) => {
    const customEvent = event as CustomEvent<PerformanceMetricsResponse>;
    const { data } = customEvent.detail;
    
    if (data.tracker_id !== this.trackerId) return;

    this.metrics = {
      totalDuration: data.total_duration_ms,
      tokensUsed: data.stage_breakdown.reduce((sum, stage) => sum + (stage.tokens_used || 0), 0),
      costEstimate: data.stage_breakdown.reduce((sum, stage) => sum + (stage.cost_estimate || 0), 0),
      averageStageTime: data.total_duration_ms / data.stage_breakdown.length
    };

    // Update performance indicators
    this.performanceIndicators.efficiency = data.performance_score;
    
    // Check for bottlenecks
    if (data.bottlenecks.length > 0) {
      const criticalBottlenecks = data.bottlenecks.filter(b => b.severity === 'critical');
      if (criticalBottlenecks.length > 0) {
        this.performanceIndicators.speed = 'critical';
        this.warningMessage = `Critical bottleneck: ${criticalBottlenecks[0].description}`;
      }
    }

    this.requestUpdate();
  };

  private handleStageTiming = (event: Event) => {
    const customEvent = event as CustomEvent<StageTimingResponse>;
    const { data } = customEvent.detail;
    
    if (data.tracker_id !== this.trackerId) return;

    // Update stage with timing information
    const stageIndex = this.stages.findIndex(s => s.id === data.stage_id);
    if (stageIndex >= 0) {
      this.stages[stageIndex] = {
        ...this.stages[stageIndex],
        duration: data.duration_ms,
        expectedDuration: data.expected_duration_ms,
        performanceRatio: data.performance_ratio
      };
      this.requestUpdate();
    }
  };

  private handleWorkflowProgress = (event: Event) => {
    const customEvent = event as CustomEvent<WorkflowProgressResponse>;
    const { data } = customEvent.detail;
    
    this.progress = data.overall_progress;
    this.performanceIndicators = {
      speed: data.performance_indicators.speed,
      efficiency: data.performance_indicators.efficiency,
      resourceUsage: data.performance_indicators.resource_usage,
      bottleneckRisk: 0 // Calculate based on data
    };

    this.requestUpdate();
  };

  private formatDuration(ms: number): string {
    if (ms < 1000) return `${Math.round(ms)}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${Math.floor(ms / 60000)}m ${Math.floor((ms % 60000) / 1000)}s`;
  }

  private formatNumber(num: number): string {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  }

  render() {
    const progressBarClasses = {
      'progress-bar': true,
      'fast': this.performanceIndicators.speed === 'fast',
      'slow': this.performanceIndicators.speed === 'slow',
      'critical': this.performanceIndicators.speed === 'critical'
    };

    const progressBarStyles = {
      width: `${this.progress}%`
    };

    return html`
      <div class="progress-container">
        <div class="progress-header">
          <h3 class="progress-title">${this.title}</h3>
          <div class="progress-percentage">${Math.round(this.progress)}%</div>
        </div>

        <div class="progress-bar-container">
          <div class=${classMap(progressBarClasses)} style=${styleMap(progressBarStyles)}></div>
        </div>

        ${this.currentStage ? html`
          <div class="current-stage">
            <div class="stage-icon ${this.currentStage.status}">
              ${this.currentStage.status === 'completed' ? '✓' : 
                this.currentStage.status === 'error' ? '✗' : '●'}
            </div>
            <div class="stage-info">
              <p class="stage-name">${this.currentStage.name}</p>
              ${this.currentStage.message ? html`
                <p class="stage-message">${this.currentStage.message}</p>
              ` : ''}
            </div>
          </div>
        ` : ''}

        ${this.warningMessage ? html`
          <div class="warning-indicator">
            <span class="warning-icon">⚠️</span>
            <p class="warning-text">${this.warningMessage}</p>
          </div>
        ` : ''}

        ${this.showMetrics && !this.compact ? html`
          <div class="performance-metrics">
            <div class="metric-card">
              <p class="metric-value">${this.formatDuration(this.metrics.totalDuration)}</p>
              <p class="metric-label">Duration</p>
            </div>
            <div class="metric-card">
              <p class="metric-value">${this.formatNumber(this.metrics.tokensUsed)}</p>
              <p class="metric-label">Tokens</p>
            </div>
            <div class="metric-card">
              <p class="metric-value">$${this.metrics.costEstimate.toFixed(4)}</p>
              <p class="metric-label">Cost</p>
            </div>
            <div class="metric-card">
              <p class="metric-value">${Math.round(this.performanceIndicators.efficiency * 100)}%</p>
              <p class="metric-label">Efficiency</p>
            </div>
          </div>
        ` : ''}

        ${this.showTimeline && !this.compact && this.stages.length > 1 ? html`
          <div class="stages-timeline">
            ${this.stages.map(stage => html`
              <div class="timeline-stage ${stage.status}">
                <div class="timeline-icon ${stage.status}">
                  ${stage.status === 'completed' ? '✓' : 
                    stage.status === 'error' ? '✗' : 
                    stage.status === 'active' ? '●' : '○'}
                </div>
                <div class="timeline-content">
                  <p class="timeline-name">${stage.name}</p>
                  ${stage.duration ? html`
                    <p class="timeline-duration">${this.formatDuration(stage.duration)}</p>
                  ` : ''}
                </div>
              </div>
            `)}
          </div>
        ` : ''}
      </div>
    `;
  }
}
