/**
 * Persistent error component for displaying critical errors that require immediate attention
 * Always visible in both production and debug modes
 */

import { LitElement, html, css } from 'lit';
import { customElement, property, state } from 'lit/decorators.js';
import { AppError, ErrorLevel, ErrorType } from '../types/error-types.js';
import { ErrorClassificationService } from '../services/error-classification.js';

@customElement('error-persistent')
export class ErrorPersistent extends LitElement {
  @property({ type: Object })
  error: AppError | null = null;

  @property({ type: Boolean })
  visible = false;

  @property({ type: Boolean })
  isDebugMode = false;

  @property({ type: Boolean })
  allowDismiss = false;

  @state()
  private isExpanded = false;

  static styles = css`
    :host {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 10002;
      opacity: 0;
      visibility: hidden;
      transition: opacity 0.3s ease, visibility 0.3s ease;
    }

    :host([visible]) {
      opacity: 1;
      visibility: visible;
    }

    .overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.7);
      z-index: -1;
    }

    .error-container {
      background: white;
      border-radius: 8px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
      max-width: 600px;
      width: 90vw;
      max-height: 80vh;
      overflow: hidden;
      transform: scale(0.9);
      transition: transform 0.3s ease;
    }

    :host([visible]) .error-container {
      transform: scale(1);
    }

    .error-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20px 24px;
      background: linear-gradient(135deg, #dc3545, #c82333);
      color: white;
    }

    .error-header.wheel-error {
      background: linear-gradient(135deg, #fd7e14, #e55a00);
    }

    .error-header.auth-error {
      background: linear-gradient(135deg, #6f42c1, #5a2d91);
    }

    .header-content {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .error-icon {
      font-size: 24px;
      animation: pulse 2s infinite;
    }

    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.7; }
    }

    .error-title {
      font-size: 18px;
      font-weight: 600;
      margin: 0;
    }

    .error-subtitle {
      font-size: 14px;
      opacity: 0.9;
      margin: 4px 0 0 0;
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .expand-button {
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: white;
      padding: 6px 12px;
      border-radius: 4px;
      font-size: 12px;
      cursor: pointer;
      transition: background-color 0.2s;
    }

    .expand-button:hover {
      background: rgba(255, 255, 255, 0.3);
    }

    .close-button {
      background: none;
      border: none;
      color: white;
      font-size: 20px;
      cursor: pointer;
      padding: 4px;
      opacity: 0.8;
      transition: opacity 0.2s;
    }

    .close-button:hover {
      opacity: 1;
    }

    .error-body {
      padding: 24px;
    }

    .error-message {
      font-size: 16px;
      line-height: 1.5;
      color: #333;
      margin-bottom: 20px;
    }

    .error-actions {
      display: flex;
      gap: 12px;
      margin-bottom: 20px;
    }

    .action-button {
      padding: 10px 20px;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;
    }

    .action-button.primary {
      background-color: #007bff;
      color: white;
    }

    .action-button.primary:hover {
      background-color: #0056b3;
      transform: translateY(-1px);
    }

    .action-button.secondary {
      background-color: #6c757d;
      color: white;
    }

    .action-button.secondary:hover {
      background-color: #545b62;
      transform: translateY(-1px);
    }

    .action-button.danger {
      background-color: #dc3545;
      color: white;
    }

    .action-button.danger:hover {
      background-color: #c82333;
      transform: translateY(-1px);
    }

    .error-details {
      background-color: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 6px;
      padding: 16px;
      margin-top: 16px;
      max-height: 300px;
      overflow-y: auto;
    }

    .details-grid {
      display: grid;
      grid-template-columns: auto 1fr;
      gap: 8px 16px;
      font-family: monospace;
      font-size: 12px;
    }

    .detail-label {
      font-weight: bold;
      color: #666;
    }

    .detail-value {
      color: #333;
      word-break: break-all;
    }

    .stack-trace {
      background-color: #f1f3f4;
      border: 1px solid #dadce0;
      border-radius: 4px;
      padding: 12px;
      font-family: monospace;
      font-size: 11px;
      white-space: pre-wrap;
      max-height: 200px;
      overflow-y: auto;
      margin-top: 12px;
    }

    .support-info {
      background-color: #e7f3ff;
      border: 1px solid #b3d9ff;
      border-radius: 6px;
      padding: 16px;
      margin-top: 16px;
    }

    .support-title {
      font-weight: 600;
      color: #0066cc;
      margin-bottom: 8px;
    }

    .support-text {
      font-size: 14px;
      color: #333;
      line-height: 1.4;
    }

    .error-id {
      font-family: monospace;
      background-color: #f8f9fa;
      padding: 2px 6px;
      border-radius: 3px;
      font-size: 12px;
    }
  `;

  private getHeaderClass(): string {
    if (!this.error) return '';

    switch (this.error.type) {
      case ErrorType.WHEEL_ERROR:
      case ErrorType.PHYSICS_ERROR:
      case ErrorType.RENDER_ERROR:
        return 'wheel-error';
      case ErrorType.AUTHENTICATION_ERROR:
      case ErrorType.AUTHORIZATION_ERROR:
        return 'auth-error';
      default:
        return '';
    }
  }

  private getErrorIcon(): string {
    if (!this.error) return '🚨';

    switch (this.error.type) {
      case ErrorType.WHEEL_ERROR:
        return '🎯';
      case ErrorType.PHYSICS_ERROR:
        return '⚡';
      case ErrorType.RENDER_ERROR:
        return '🖥️';
      case ErrorType.AUTHENTICATION_ERROR:
        return '🔐';
      case ErrorType.AUTHORIZATION_ERROR:
        return '🚫';
      case ErrorType.CONFIGURATION_ERROR:
        return '⚙️';
      default:
        return '🚨';
    }
  }

  private getErrorTitle(): string {
    if (!this.error) return 'Critical Error';

    switch (this.error.type) {
      case ErrorType.WHEEL_ERROR:
        return 'Wheel System Error';
      case ErrorType.PHYSICS_ERROR:
        return 'Physics Engine Error';
      case ErrorType.RENDER_ERROR:
        return 'Display Error';
      case ErrorType.AUTHENTICATION_ERROR:
        return 'Authentication Required';
      case ErrorType.AUTHORIZATION_ERROR:
        return 'Access Denied';
      case ErrorType.CONFIGURATION_ERROR:
        return 'Configuration Error';
      default:
        return 'Critical System Error';
    }
  }

  private getErrorSubtitle(): string {
    return 'Immediate attention required';
  }

  private handleClose() {
    if (this.allowDismiss) {
      this.dispatchEvent(new CustomEvent('error-dismissed', {
        detail: { error: this.error },
        bubbles: true
      }));
    }
  }

  private handleRefresh() {
    window.location.reload();
  }

  private handleContactSupport() {
    // This would typically open a support form or email client
    this.dispatchEvent(new CustomEvent('contact-support', {
      detail: { error: this.error },
      bubbles: true
    }));
  }

  private handleToggleDetails() {
    this.isExpanded = !this.isExpanded;
  }

  private handleCopyError() {
    if (!this.error) return;

    const errorInfo = {
      id: this.error.id,
      type: this.error.type,
      level: this.error.level,
      message: this.error.message,
      component: this.error.component,
      timestamp: this.error.timestamp.toISOString(),
      stack: this.error.stack,
      metadata: this.error.metadata
    };

    navigator.clipboard.writeText(JSON.stringify(errorInfo, null, 2));
  }

  render() {
    if (!this.error) return html``;

    const headerClass = this.getHeaderClass();
    const icon = this.getErrorIcon();
    const title = this.getErrorTitle();
    const subtitle = this.getErrorSubtitle();
    const message = ErrorClassificationService.getUserMessage(this.error, this.isDebugMode);

    return html`
      <div class="overlay"></div>
      <div class="error-container">
        <div class="error-header ${headerClass}">
          <div class="header-content">
            <span class="error-icon">${icon}</span>
            <div>
              <h2 class="error-title">${title}</h2>
              <p class="error-subtitle">${subtitle}</p>
            </div>
          </div>
          
          <div class="header-actions">
            ${this.isDebugMode ? html`
              <button class="expand-button" @click=${this.handleToggleDetails}>
                ${this.isExpanded ? 'Hide Details' : 'Show Details'}
              </button>
            ` : ''}
            
            ${this.allowDismiss ? html`
              <button class="close-button" @click=${this.handleClose}>✕</button>
            ` : ''}
          </div>
        </div>

        <div class="error-body">
          <div class="error-message">${message}</div>

          <div class="error-actions">
            <button class="action-button primary" @click=${this.handleRefresh}>
              Refresh Page
            </button>
            
            ${ErrorClassificationService.requiresSupport(this.error) ? html`
              <button class="action-button secondary" @click=${this.handleContactSupport}>
                Contact Support
              </button>
            ` : ''}
            
            ${this.isDebugMode ? html`
              <button class="action-button secondary" @click=${this.handleCopyError}>
                Copy Error Details
              </button>
            ` : ''}
          </div>

          ${ErrorClassificationService.requiresSupport(this.error) ? html`
            <div class="support-info">
              <div class="support-title">Need Help?</div>
              <div class="support-text">
                This error has been automatically reported. Please include this error ID when contacting support: 
                <span class="error-id">${this.error.id}</span>
              </div>
            </div>
          ` : ''}

          ${this.isExpanded && this.isDebugMode ? html`
            <div class="error-details">
              <div class="details-grid">
                <span class="detail-label">Type:</span>
                <span class="detail-value">${this.error.type}</span>
                
                <span class="detail-label">Level:</span>
                <span class="detail-value">${this.error.level}</span>
                
                <span class="detail-label">Component:</span>
                <span class="detail-value">${this.error.component || 'unknown'}</span>
                
                <span class="detail-label">ID:</span>
                <span class="detail-value">${this.error.id}</span>
                
                <span class="detail-label">Timestamp:</span>
                <span class="detail-value">${this.error.timestamp.toLocaleString()}</span>
                
                <span class="detail-label">URL:</span>
                <span class="detail-value">${this.error.url || window.location.href}</span>
              </div>

              ${this.error.stack ? html`
                <div class="stack-trace">${this.error.stack}</div>
              ` : ''}
            </div>
          ` : ''}
        </div>
      </div>
    `;
  }
}
