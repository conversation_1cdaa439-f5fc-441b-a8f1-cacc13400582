/**
 * Physics engine for the spinning wheel using Matter.js
 * Handles ball physics, collision detection, and wheel mechanics
 */

import {
  Engine,
  World,
  Bodies,
  Body,
  Events,
  Vector,
  Constraint,
  Runner,
  Render
} from 'matter-js';

import type {
  WheelPhysics,
  WheelConfig,
  WheelSegment,
  AnimationFrame
} from './wheel-types.js';

import {
  calculateNailPositions,
  getBallAngle,
  normalizeAngle
} from '../../utils/physics-utils.js';

/**
 * Physics engine class for the spinning wheel
 */
export class WheelPhysicsEngine {
  private engine: Engine;
  private world: World;
  private runner: Runner | null = null;
  private ballBody: Body | null = null;
  private nailBodies: Body[] = [];
  private innerNailBodies: Body[] = [];
  private boundaryBodies: Body[] = [];
  private config: WheelConfig;
  private isRunning = false;
  private animationCallbacks: Array<(frame: AnimationFrame) => void> = [];
  private lastFrameTime = 0;

  // Wheel rotation state (separate from Matter.js physics)
  private wheelRotation = 0;
  private wheelVelocity = 0;
  private frameCount = 0;

  // Store initial nail positions for rotation calculations
  private initialNailPositions: Array<{ x: number; y: number; angle: number }> = [];
  private initialInnerNailPositions: Array<{ x: number; y: number; angle: number }> = [];

  constructor(config: WheelConfig) {
    this.config = config;
    
    // Create Matter.js engine
    this.engine = Engine.create();
    this.world = this.engine.world;
    
    // Configure engine settings for VERTICAL wheel - GRAVITY DISABLED INITIALLY
    this.engine.gravity.y = 0; // NO gravity until wheel starts spinning
    this.engine.gravity.x = 0;
    this.engine.gravity.scale = 0.001; // Scale gravity for better control when enabled
    
    // Set up collision detection
    this.setupCollisionEvents();
  }

  /**
   * Initializes the physics world with wheel components
   */
  public initialize(): void {
    this.createNails();
    this.createInnerNails();
    this.createBall();
    this.setupConstraints();
  }



  /**
   * Creates nail bodies around the wheel perimeter (for vertical spinning wheel)
   */
  private createNails(): void {
    const nailPositions = calculateNailPositions(this.config);
    this.initialNailPositions = [...nailPositions]; // Store initial positions

    this.nailBodies = nailPositions.map((pos, index) => {
      const nail = Bodies.circle(pos.x, pos.y, this.config.nailRadius, {
        isStatic: true,
        restitution: 0.98, // SUPER BOUNCY nails for maximum fun!
        friction: 1, // Very low friction for more bouncing
        render: {
          visible: false // We'll render this with PixiJS
        },
        collisionFilter: {
          category: 0x0002,
          mask: 0x0004 // Only collides with ball
        },
        label: `nail-${index}`
      });

      return nail;
    });

    World.add(this.world, this.nailBodies);
  }

  /**
   * Creates inner nail bodies around the inner wheel perimeter for ball collision
   */
  private createInnerNails(): void {
    const ballDiameter = this.config.ballRadius * 2;
    const nailSpacing = ballDiameter * 1.5;
    const circumference = 2 * Math.PI * this.config.innerWheelRadius;
    const actualNailCount = Math.floor(circumference / nailSpacing);

    // Ensure we don't exceed the configured inner nail count
    const nailCount = Math.min(actualNailCount, this.config.innerNailCount);

    this.innerNailBodies = [];
    this.initialInnerNailPositions = [];

    for (let i = 0; i < nailCount; i++) {
      const angle = (i / nailCount) * 2 * Math.PI;
      const x = this.config.centerX + Math.cos(angle) * this.config.innerWheelRadius;
      const y = this.config.centerY + Math.sin(angle) * this.config.innerWheelRadius;

      // Store initial position for rotation updates
      this.initialInnerNailPositions.push({ x, y, angle });

      const nail = Bodies.circle(x, y, this.config.nailRadius*2, {
        isStatic: true,
        restitution: 0.8, // SUPER BOUNCY nails for maximum fun!
        friction: 1, // Very low friction for more bouncing
        render: {
          visible: false // We'll render this with PixiJS
        },
        collisionFilter: {
          category: 0x0002,
          mask: 0x0004 // Only collides with ball
        },
        label: `inner-nail-${i}`
      });

      this.innerNailBodies.push(nail);
    }

    World.add(this.world, this.innerNailBodies);
    console.log(`[PHYSICS] 🔩 Created ${nailCount} inner nails for ball collision (spacing: ${nailSpacing.toFixed(1)}px)`);
  }

  /**
   * Creates the ball body with randomized initial position
   */
  private createBall(): void {
    // Add randomization to initial ball position for variety
    const randomAngle = Math.random() * 2 * Math.PI; // Random angle around the wheel
    const randomRadius = 20 + Math.random() * 30; // Random distance from center (20-50px)

    const ballStartX = this.config.centerX + Math.cos(randomAngle) * randomRadius;
    const ballStartY = this.config.centerY + Math.sin(randomAngle) * randomRadius;

    console.log(`[PHYSICS] Creating ball at randomized position: (${ballStartX.toFixed(1)}, ${ballStartY.toFixed(1)})`);

    this.ballBody = Bodies.circle(ballStartX, ballStartY, this.config.ballRadius, {
      restitution: 0.99, // SUPER BOUNCY for fun physics!
      friction: 0.01, // Low friction for more bouncing
      frictionAir: 0.005, // Very low air resistance for more dynamic movement
      density: 0.01, // Lower density for more responsive physics
      render: {
        visible: false // We'll render this with PixiJS
      },
      collisionFilter: {
        category: 0x0004,
        mask: 0x0002 | 0x0008 // Collides with nails and boundaries
      },
      label: 'ball'
    });

    // CRITICAL: Make ball completely static until spin starts
    Body.setStatic(this.ballBody, true);
    console.log(`[PHYSICS] Ball created as STATIC - will not move until spin starts`);

    World.add(this.world, this.ballBody);
  }

  /**
   * Sets up constraints to keep the ball within the wheel
   */
  private setupConstraints(): void {
    if (!this.ballBody) return;

    // Create invisible boundary walls around the wheel
    const wallThickness = 20; // FIXED: Thicker walls
    const boundaryRadius = this.config.radius + this.config.ballRadius + 5; // FIXED: Closer boundary

    // Create circular boundary using multiple small static bodies
    const boundarySegments = 32; // FIXED: Fewer segments, larger bodies
    const angleStep = (2 * Math.PI) / boundarySegments;

    this.boundaryBodies = [];
    for (let i = 0; i < boundarySegments; i++) {
      const angle = i * angleStep;
      const x = this.config.centerX + boundaryRadius * Math.cos(angle);
      const y = this.config.centerY + boundaryRadius * Math.sin(angle);

      const boundary = Bodies.rectangle(x, y, wallThickness, wallThickness, {
        isStatic: true,
        restitution: 0.9, // Super bouncy boundaries
        friction: 0.1, // Low friction for more bouncing
        render: { visible: false },
        collisionFilter: {
          category: 0x0008,
          mask: 0x0004 // Only collides with ball
        },
        label: `boundary-${i}`
      });

      this.boundaryBodies.push(boundary);
    }

    World.add(this.world, this.boundaryBodies);
  }

  /**
   * Sets up collision event handlers
   */
  private setupCollisionEvents(): void {
    Events.on(this.engine, 'collisionStart', (event) => {
      event.pairs.forEach((pair) => {
        const { bodyA, bodyB } = pair;
        
        // Check if ball collided with nail (outer or inner)
        if (
          (bodyA.label === 'ball' && (bodyB.label?.startsWith('nail-') || bodyB.label?.startsWith('inner-nail-'))) ||
          (bodyB.label === 'ball' && (bodyA.label?.startsWith('nail-') || bodyA.label?.startsWith('inner-nail-')))
        ) {
          this.handleBallNailCollision(bodyA, bodyB);
        }
      });
    });
    
    Events.on(this.engine, 'beforeUpdate', () => {
      this.updatePhysics();
    });
  }

  /**
   * Handles collision between ball and nail - ENHANCED for super bouncy fun!
   */
  private handleBallNailCollision(bodyA: Body, bodyB: Body): void {
    const ball = bodyA.label === 'ball' ? bodyA : bodyB;
    const nail = bodyA.label === 'ball' ? bodyB : bodyA;

    // Add dynamic forces based on wheel rotation for more realistic physics
    const wheelSpeed = Math.abs(this.wheelVelocity);
    const forceMultiplier = 0.002 + (wheelSpeed * 0.001); // Stronger force when wheel spins faster

    // Calculate direction from nail to ball for realistic bounce
    const dx = ball.position.x - nail.position.x;
    const dy = ball.position.y - nail.position.y;
    const distance = Math.sqrt(dx * dx + dy * dy);

    if (distance > 0) {
      const normalizedX = dx / distance;
      const normalizedY = dy / distance;

      // Apply force in direction away from nail + some randomness
      const bounceForce = {
        x: normalizedX * forceMultiplier + (Math.random() - 0.5) * 0.001,
        y: normalizedY * forceMultiplier + (Math.random() - 0.5) * 0.001
      };

      Body.applyForce(ball, ball.position, bounceForce);

      // Add some spin to the ball for more realistic rolling
      const spinForce = (Math.random() - 0.5) * 0.05;
      Body.setAngularVelocity(ball, ball.angularVelocity + spinForce);
    }

    // Emit collision event for sound effects, etc.
    this.emitCollisionEvent(ball, nail);
  }

  /**
   * Emits collision event for external handling
   */
  private emitCollisionEvent(ball: Body, nail: Body): void {
    // This would be handled by the main wheel component
    // For now, we'll just log it
    console.log('Ball-nail collision detected');
  }

  /**
   * Updates nail positions to rotate with the wheel (for vertical spinning wheel)
   */
  private updateNailPositions(): void {
    if (this.nailBodies.length === 0 || this.initialNailPositions.length === 0) return;

    // Only update if wheel is rotating significantly
    if (Math.abs(this.wheelVelocity) < 0.001) return;

    // Update outer nails
    this.nailBodies.forEach((nail, index) => {
      const initialPos = this.initialNailPositions[index];
      if (!initialPos) return;

      // Calculate new position based on wheel rotation
      const rotatedAngle = initialPos.angle + this.wheelRotation;
      const newX = this.config.centerX + (this.config.radius + this.config.nailRadius) * Math.cos(rotatedAngle);
      const newY = this.config.centerY + (this.config.radius + this.config.nailRadius) * Math.sin(rotatedAngle);

      // Update nail position
      Body.setPosition(nail, { x: newX, y: newY });
    });

    // Update inner nails
    this.innerNailBodies.forEach((nail, index) => {
      const initialPos = this.initialInnerNailPositions[index];
      if (!initialPos) return;

      // Calculate new position based on wheel rotation
      const rotatedAngle = initialPos.angle + this.wheelRotation;
      const newX = this.config.centerX + this.config.innerWheelRadius * Math.cos(rotatedAngle);
      const newY = this.config.centerY + this.config.innerWheelRadius * Math.sin(rotatedAngle);

      // Update nail position
      Body.setPosition(nail, { x: newX, y: newY });
    });
  }

  /**
   * Updates physics simulation - ENHANCED for realistic physics
   */
  private updatePhysics(): void {
    if (!this.ballBody) return;

    // Let Matter.js handle friction naturally for more realistic physics
    // No custom friction forces - this was interfering with bouncing!

    // Update wheel rotation (separate from ball physics) - FASTER dynamics!
    const deltaTime = 16; // Assume 60fps
    this.wheelRotation += this.wheelVelocity * (deltaTime / 1000);

    // Apply wheel rotation friction - FASTER slowdown for more dynamism
    if (Math.abs(this.wheelVelocity) > 0.001) {
      this.wheelVelocity *= (1 - this.config.wheelRotationFriction);
    } else {
      this.wheelVelocity = 0;
    }

    // Update nail positions to rotate with the wheel (for vertical wheel)
    this.updateNailPositions();

    // Create animation frame data
    const now = performance.now();
    const frameTime = now - this.lastFrameTime;
    this.lastFrameTime = now;

    const ballVelocity = this.getBallVelocity();
    const speed = ballVelocity.magnitude; // Get speed for logging
    const frame: AnimationFrame = {
      timestamp: now,
      deltaTime: frameTime,
      ballPosition: { ...this.ballBody.position },
      ballVelocity,
      ballAngle: this.getBallAngle(),
      wheelRotation: this.wheelRotation,
      wheelVelocity: this.wheelVelocity
    };

    // Log every 30 frames (0.5 seconds) for debugging
    if (this.frameCount % 30 === 0 && (speed > 0.01 || Math.abs(this.wheelVelocity) > 0.01)) {
      const distanceFromCenter = Math.sqrt(
        Math.pow(this.ballBody.position.x - this.config.centerX, 2) +
        Math.pow(this.ballBody.position.y - this.config.centerY, 2)
      );
      console.log(`[PHYSICS] Ball: (${this.ballBody.position.x.toFixed(1)}, ${this.ballBody.position.y.toFixed(1)}) Speed: ${speed.toFixed(3)} Distance: ${distanceFromCenter.toFixed(1)} WheelVel: ${this.wheelVelocity.toFixed(3)}`);

      // Check if ball is outside expected bounds
      if (distanceFromCenter > this.config.radius + 50) {
        console.warn(`[PHYSICS] Ball is outside expected bounds! Distance: ${distanceFromCenter.toFixed(1)}, Max expected: ${this.config.radius + 50}`);
      }
    }
    this.frameCount = (this.frameCount || 0) + 1;

    // Notify animation callbacks
    this.animationCallbacks.forEach(callback => callback(frame));
  }

  /**
   * Starts the physics simulation
   */
  public start(): void {
    if (this.isRunning) return;

    this.runner = Runner.create();

    // Note: beforeUpdate event is already set up in setupCollisionEvents()
    // which calls updatePhysics() - no need to duplicate here

    Runner.run(this.runner, this.engine);
    this.isRunning = true;
    this.lastFrameTime = performance.now();

    console.log('[PHYSICS] Physics simulation started with animation loop');
  }

  /**
   * Stops the physics simulation
   */
  public stop(): void {
    if (!this.isRunning || !this.runner) return;

    // Event listeners are managed in setupCollisionEvents() and will be
    // cleaned up when the engine is destroyed

    Runner.stop(this.runner);
    this.runner = null;
    this.isRunning = false;

    console.log('[PHYSICS] Physics simulation stopped');
  }

  /**
   * Spins the wheel with given force (VERTICAL WHEEL: ball falls when wheel turns)
   */
  public spinWheel(force: number): void {
    console.log(`[PHYSICS] Spinning VERTICAL wheel with force: ${force} for ~10 second duration`);

    // Set initial wheel velocity based on force for 10-second spinning
    this.wheelVelocity = force * this.config.initialWheelVelocity;
    console.log(`[PHYSICS] Wheel velocity set to: ${this.wheelVelocity} (target: 10s duration)`);

    // ENABLE GRAVITY when wheel starts spinning - ball falls only when wheel turns
    this.engine.gravity.y = 0.7; // Enable realistic gravity
    console.log(`[PHYSICS] GRAVITY ENABLED - ball will now fall as wheel spins`);

    // For vertical wheel: ball falls STRAIGHT DOWN due to gravity only
    if (this.ballBody) {
      // CRITICAL: Make ball dynamic (non-static) so it can move
      Body.setStatic(this.ballBody, false);
      console.log(`[PHYSICS] Ball set to DYNAMIC - can now move and fall`);

      // NO horizontal forces - ball falls purely vertically
      console.log(`[PHYSICS] Ball position before release: (${this.ballBody.position.x.toFixed(1)}, ${this.ballBody.position.y.toFixed(1)})`);
      console.log(`[PHYSICS] Ball released to fall STRAIGHT DOWN - no horizontal forces applied`);

      // Reset any existing velocity to ensure pure vertical fall
      Body.setVelocity(this.ballBody, { x: 0, y: 0 });
      Body.setAngularVelocity(this.ballBody, 0);

      console.log(`[PHYSICS] Ball will fall vertically due to gravity only when wheel spins. Wheel spins for ~10 seconds.`);
    }
  }

  /**
   * Applies a spin force to the ball (legacy method for compatibility)
   */
  public spinBall(force: number, angle: number = 0): void {
    this.spinWheel(force);
  }

  /**
   * Gets the current ball angle relative to wheel center
   */
  public getBallAngle(): number {
    if (!this.ballBody) return 0;
    
    return getBallAngle(
      this.ballBody.position.x,
      this.ballBody.position.y,
      this.config.centerX,
      this.config.centerY
    );
  }

  /**
   * Gets the current ball position
   */
  public getBallPosition(): { x: number; y: number } {
    if (!this.ballBody) return { x: this.config.centerX, y: this.config.centerY - this.config.ballStartOffset };

    return {
      x: this.ballBody.position.x,
      y: this.ballBody.position.y
    };
  }

  /**
   * Gets the current ball velocity
   */
  public getBallVelocity(): { x: number; y: number; magnitude: number } {
    if (!this.ballBody) return { x: 0, y: 0, magnitude: 0 };

    const velocity = this.ballBody.velocity;
    return {
      x: velocity.x,
      y: velocity.y,
      magnitude: Vector.magnitude(velocity)
    };
  }

  /**
   * Checks if the ball has settled (low velocity)
   */
  public isBallSettled(threshold: number = 0.01): boolean {
    const velocity = this.getBallVelocity();
    return velocity.magnitude < threshold;
  }

  /**
   * Checks if the wheel has settled (low rotation velocity)
   */
  public isWheelSettled(threshold: number = 0.01): boolean {
    return Math.abs(this.wheelVelocity) < threshold;
  }

  /**
   * Gets the current wheel rotation
   */
  public getWheelRotation(): number {
    return this.wheelRotation;
  }

  /**
   * Gets the current wheel velocity
   */
  public getWheelVelocity(): number {
    return this.wheelVelocity;
  }

  /**
   * Gets the ball body for external access
   */
  public getBallBody(): Body | null {
    return this.ballBody;
  }

  /**
   * Gets nail positions for winner detection
   */
  public getNailPositions(): Array<{ x: number; y: number; angle: number }> {
    return this.initialNailPositions.map(nail => {
      // Apply wheel rotation to nail positions
      const rotatedAngle = nail.angle + this.wheelRotation;
      const x = this.config.centerX + (this.config.radius + this.config.nailRadius) * Math.cos(rotatedAngle);
      const y = this.config.centerY + (this.config.radius + this.config.nailRadius) * Math.sin(rotatedAngle);
      return { x, y, angle: rotatedAngle };
    });
  }

  /**
   * Checks if both wheel and ball have settled
   */
  public isSettled(threshold: number = 0.01): boolean {
    const ballSettled = this.isBallSettled(threshold);
    const wheelSettled = Math.abs(this.wheelVelocity) < threshold;
    return ballSettled && wheelSettled;
  }

  /**
   * Resets the ball to randomized starting position and makes it static
   */
  public resetBall(): void {
    if (!this.ballBody) return;

    // Add randomization to ball reset position for variety
    const randomAngle = Math.random() * 2 * Math.PI; // Random angle around the wheel
    const randomRadius = 20 + Math.random() * 30; // Random distance from center (20-50px)

    const startX = this.config.centerX + Math.cos(randomAngle) * randomRadius;
    const startY = this.config.centerY + Math.sin(randomAngle) * randomRadius;

    console.log(`[PHYSICS] Resetting ball to randomized position: (${startX.toFixed(1)}, ${startY.toFixed(1)})`);

    Body.setPosition(this.ballBody, { x: startX, y: startY });
    Body.setVelocity(this.ballBody, { x: 0, y: 0 });
    Body.setAngularVelocity(this.ballBody, 0);

    // CRITICAL: Make ball completely static to prevent any sliding
    Body.setStatic(this.ballBody, true);
    console.log(`[PHYSICS] Ball set to STATIC - completely immobile until spin starts`);

    // DISABLE GRAVITY - ball should not fall until wheel spins
    this.engine.gravity.y = 0;
    console.log(`[PHYSICS] GRAVITY DISABLED - ball will not fall until wheel spins`);

    // Also reset wheel rotation
    this.wheelRotation = 0;
    this.wheelVelocity = 0;
    this.frameCount = 0;

    console.log(`[PHYSICS] Ball reset complete. Ball is STATIC and suspended until wheel spins. Position: (${this.ballBody.position.x.toFixed(1)}, ${this.ballBody.position.y.toFixed(1)})`);
  }

  /**
   * Adds an animation frame callback
   */
  public onAnimationFrame(callback: (frame: AnimationFrame) => void): void {
    this.animationCallbacks.push(callback);
  }

  /**
   * Removes an animation frame callback
   */
  public removeAnimationFrame(callback: (frame: AnimationFrame) => void): void {
    const index = this.animationCallbacks.indexOf(callback);
    if (index > -1) {
      this.animationCallbacks.splice(index, 1);
    }
  }

  /**
   * Gets the physics state for external use
   */
  public getPhysicsState(): WheelPhysics {
    return {
      engine: this.engine,
      world: this.world,
      wheelBody: null, // No longer using wheel body
      ballBody: this.ballBody!,
      nailBodies: this.nailBodies,
      isRunning: this.isRunning,
      ballAngularVelocity: this.ballBody?.angularVelocity || 0
    };
  }

  /**
   * Destroys the physics engine and cleans up resources
   */
  public destroy(): void {
    this.stop();

    // Clear all bodies from world
    World.clear(this.world, false);

    // Clear engine
    Engine.clear(this.engine);

    // Clear callbacks
    this.animationCallbacks = [];

    // Reset references
    this.ballBody = null;
    this.nailBodies = [];
    this.innerNailBodies = [];
    this.boundaryBodies = [];

    // Reset wheel state
    this.wheelRotation = 0;
    this.wheelVelocity = 0;
  }
}
