/**
 * Viewport component for the wheel - provides zoom and pan capabilities
 * Wraps the game-wheel component to enable interactive viewing
 */

import { LitElement, html, css, PropertyValues } from 'lit';
import { customElement, property, state, query } from 'lit/decorators.js';
import { ref, createRef, Ref } from 'lit/directives/ref.js';

/**
 * Viewport component that wraps the game wheel with zoom/pan functionality
 */
@customElement('wheel-viewport')
export class WheelViewport extends LitElement {
  static styles = css`
    :host {
      display: block;
      width: 100%;
      height: 100%;
      position: relative;
      overflow: hidden;
      background: #1a1a1a;
      border-radius: 8px;
      cursor: grab;
    }

    :host([constrain-to-wheel-size]) {
      width: var(--wheel-diameter, 400px);
      height: var(--wheel-diameter, 400px);
      max-width: 100%;
      max-height: 100%;
      margin: 0 auto;
    }

    :host(:active) {
      cursor: grabbing;
    }

    .viewport-container {
      width: 100%;
      height: 100%;
      position: relative;
      overflow: hidden;
    }

    .viewport-content {
      position: absolute;
      top: 0;
      left: 50%;
      transform-origin: center top;
      transition: transform 0.1s ease-out;
      will-change: transform;
    }

    .viewport-content.dragging {
      transition: none;
    }

    .zoom-controls {
      position: absolute;
      top: 10px;
      right: 10px;
      display: flex;
      flex-direction: column;
      gap: 5px;
      z-index: 10;
    }

    .zoom-button {
      width: 40px;
      height: 40px;
      border: none;
      border-radius: 50%;
      background: rgba(0, 0, 0, 0.7);
      color: white;
      font-size: 18px;
      font-weight: bold;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
      backdrop-filter: blur(5px);
    }

    .zoom-button:hover {
      background: rgba(0, 0, 0, 0.9);
      transform: scale(1.1);
    }

    .zoom-button:active {
      transform: scale(0.95);
    }

    .zoom-info {
      position: absolute;
      bottom: 10px;
      left: 10px;
      background: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 8px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-family: monospace;
      backdrop-filter: blur(5px);
      z-index: 10;
    }

    .reset-button {
      position: absolute;
      bottom: 10px;
      right: 10px;
      padding: 8px 16px;
      border: none;
      border-radius: 20px;
      background: rgba(78, 205, 196, 0.8);
      color: white;
      font-size: 12px;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.2s ease;
      backdrop-filter: blur(5px);
      z-index: 10;
    }

    .reset-button:hover {
      background: rgba(78, 205, 196, 1);
      transform: translateY(-2px);
    }

    /* Mobile-friendly touch controls */
    @media (max-width: 768px) {
      .zoom-controls {
        top: 5px;
        right: 5px;
      }

      .zoom-button {
        width: 35px;
        height: 35px;
        font-size: 16px;
      }

      .zoom-info {
        bottom: 5px;
        left: 5px;
        font-size: 11px;
        padding: 6px 10px;
      }

      .reset-button {
        bottom: 5px;
        right: 5px;
        padding: 6px 12px;
        font-size: 11px;
      }
    }
  `;

  // Properties
  @property({ type: Number })
  minZoom = 0.5;

  @property({ type: Number })
  maxZoom = 3.0;

  @property({ type: Number })
  zoomStep = 0.2;

  @property({ type: Boolean })
  showControls = true;

  @property({ type: Boolean })
  constrainToWheelSize = false;

  @property({ type: Number })
  wheelRadius = 200;

  // Internal state
  @state()
  private zoom = 1.0;

  @state()
  private panX = 0;

  @state()
  private panY = 0;

  @state()
  private isDragging = false;

  // Component references
  private containerRef: Ref<HTMLDivElement> = createRef();
  private contentRef: Ref<HTMLDivElement> = createRef();
  
  // Interaction state
  private lastPointerX = 0;
  private lastPointerY = 0;
  private startPanX = 0;
  private startPanY = 0;

  /**
   * Lifecycle: First update completed
   */
  protected firstUpdated(): void {
    this.setupEventListeners();
    this.updateTransform();
    this.updateWheelSize();
  }

  /**
   * Lifecycle: Property changed
   */
  protected updated(changedProperties: Map<string, any>): void {
    if (changedProperties.has('wheelRadius') || changedProperties.has('constrainToWheelSize')) {
      this.updateWheelSize();
    }
  }

  /**
   * Updates the wheel size constraint
   */
  private updateWheelSize(): void {
    if (this.constrainToWheelSize) {
      const diameter = this.wheelRadius * 2;
      this.style.setProperty('--wheel-diameter', `${diameter}px`);
    }
  }

  /**
   * Sets up event listeners for pan and zoom
   */
  private setupEventListeners(): void {
    if (!this.containerRef.value) return;

    const container = this.containerRef.value;

    // Mouse/touch events for panning
    container.addEventListener('pointerdown', this.handlePointerDown.bind(this));
    container.addEventListener('pointermove', this.handlePointerMove.bind(this));
    container.addEventListener('pointerup', this.handlePointerUp.bind(this));
    container.addEventListener('pointercancel', this.handlePointerUp.bind(this));

    // Wheel event for zooming
    container.addEventListener('wheel', this.handleWheel.bind(this), { passive: false });

    // Prevent context menu on right click
    container.addEventListener('contextmenu', (e) => e.preventDefault());
  }

  /**
   * Handles pointer down events (start panning)
   */
  private handlePointerDown(event: PointerEvent): void {
    // Only handle primary pointer (left mouse button or first touch)
    if (!event.isPrimary) return;

    this.isDragging = true;
    this.lastPointerX = event.clientX;
    this.lastPointerY = event.clientY;
    this.startPanX = this.panX;
    this.startPanY = this.panY;

    // Capture pointer for consistent tracking
    (event.target as Element).setPointerCapture(event.pointerId);
    
    // Add dragging class for styling
    this.contentRef.value?.classList.add('dragging');
  }

  /**
   * Handles pointer move events (panning)
   */
  private handlePointerMove(event: PointerEvent): void {
    if (!this.isDragging || !event.isPrimary) return;

    const deltaX = event.clientX - this.lastPointerX;
    const deltaY = event.clientY - this.lastPointerY;

    this.panX = this.startPanX + deltaX;
    this.panY = this.startPanY + deltaY;

    this.updateTransform();
  }

  /**
   * Handles pointer up events (stop panning)
   */
  private handlePointerUp(event: PointerEvent): void {
    if (!event.isPrimary) return;

    this.isDragging = false;
    
    // Remove dragging class
    this.contentRef.value?.classList.remove('dragging');
    
    // Release pointer capture
    (event.target as Element).releasePointerCapture(event.pointerId);
  }

  /**
   * Handles wheel events (zooming)
   */
  private handleWheel(event: WheelEvent): void {
    event.preventDefault();

    const rect = this.containerRef.value!.getBoundingClientRect();
    const centerX = rect.width / 2;
    const centerY = rect.height / 2;
    
    // Calculate zoom point relative to container center
    const zoomPointX = event.clientX - rect.left - centerX;
    const zoomPointY = event.clientY - rect.top - centerY;

    // Determine zoom direction and amount
    const zoomDelta = event.deltaY > 0 ? -this.zoomStep : this.zoomStep;
    const newZoom = Math.max(this.minZoom, Math.min(this.maxZoom, this.zoom + zoomDelta));

    if (newZoom !== this.zoom) {
      // Adjust pan to zoom towards cursor position
      const zoomRatio = newZoom / this.zoom;
      this.panX = zoomPointX + (this.panX - zoomPointX) * zoomRatio;
      this.panY = zoomPointY + (this.panY - zoomPointY) * zoomRatio;
      
      this.zoom = newZoom;
      this.updateTransform();
    }
  }

  /**
   * Updates the transform of the content
   */
  private updateTransform(): void {
    if (!this.contentRef.value) return;

    const transform = `translate(-50%, 0) translate(${this.panX}px, ${this.panY}px) scale(${this.zoom})`;
    this.contentRef.value.style.transform = transform;
  }

  /**
   * Zooms in
   */
  public zoomIn(): void {
    const newZoom = Math.min(this.maxZoom, this.zoom + this.zoomStep);
    if (newZoom !== this.zoom) {
      this.zoom = newZoom;
      this.updateTransform();
    }
  }

  /**
   * Zooms out
   */
  public zoomOut(): void {
    const newZoom = Math.max(this.minZoom, this.zoom - this.zoomStep);
    if (newZoom !== this.zoom) {
      this.zoom = newZoom;
      this.updateTransform();
    }
  }

  /**
   * Resets zoom and pan to default
   */
  public resetView(): void {
    this.zoom = 1.0;
    this.panX = 0;
    this.panY = 0;
    this.updateTransform();
  }

  /**
   * Gets current viewport state
   */
  public getViewportState() {
    return {
      zoom: this.zoom,
      panX: this.panX,
      panY: this.panY,
      isDragging: this.isDragging
    };
  }

  /**
   * Renders the component
   */
  render() {
    return html`
      <div class="viewport-container" ${ref(this.containerRef)}>
        <div class="viewport-content" ${ref(this.contentRef)}>
          <slot></slot>
        </div>

        ${this.showControls ? html`
          <div class="zoom-controls">
            <button class="zoom-button" @click=${this.zoomIn} title="Zoom In">+</button>
            <button class="zoom-button" @click=${this.zoomOut} title="Zoom Out">−</button>
          </div>

          <div class="zoom-info">
            Zoom: ${(this.zoom * 100).toFixed(0)}%<br>
            Pan: ${this.panX.toFixed(0)}, ${this.panY.toFixed(0)}
          </div>

          <button class="reset-button" @click=${this.resetView} title="Reset View">
            Reset View
          </button>
        ` : ''}
      </div>
    `;
  }
}
