/**
 * PixiJS renderer for the spinning wheel component
 * Handles all visual rendering including wheel segments, text, nails, and ball
 */

import {
  Application,
  Container,
  Graphics,
  Text,
  TextStyle,
  Assets
} from 'pixi.js';

import type {
  WheelRenderer,
  WheelConfig,
  WheelSegment,
  AnimationFrame
} from './wheel-types.js';

import {
  calculateNailPositions,
  polarToCartesian
} from '../../utils/physics-utils.js';

import {
  hexToPixiColor,
  getContrastColor
} from '../../utils/color-utils.js';

import { getDomainColor } from '../../services/domainColorService.js';

/**
 * PixiJS renderer class for the spinning wheel
 */
export class WheelPixiRenderer {
  private app: Application;
  private container: Container;
  private wheelContainer: Container; // Container for rotating wheel parts
  private wheelGraphics: Graphics;
  private segmentsContainer: Container;
  private innerWheelContainer: Container; // Container for inner wheel
  private innerSegmentsContainer: Container;
  private innerNailsContainer: Container;
  private textContainer: Container;
  private nailsContainer: Container;
  private ballGraphics: Graphics;
  private upperHalfMask: Graphics;
  private config: WheelConfig;
  private segments: WheelSegment[] = [];
  private isInitialized = false;
  private upperHalfOpacity = 1.0; // Controls upper half visibility
  private lastBallPosition: { x: number; y: number } | null = null; // Track last ball position to reduce logging

  constructor(config: WheelConfig) {
    this.config = config;
    this.app = new Application();
    this.container = new Container();
    this.wheelContainer = new Container(); // Container for rotating parts
    this.wheelGraphics = new Graphics();
    this.segmentsContainer = new Container();
    this.innerWheelContainer = new Container(); // Container for inner wheel
    this.innerSegmentsContainer = new Container();
    this.innerNailsContainer = new Container();
    this.textContainer = new Container();
    this.nailsContainer = new Container();
    this.ballGraphics = new Graphics();
    this.upperHalfMask = new Graphics();
  }

  /**
   * Initializes the PixiJS application and renderer
   */
  public async initialize(canvas: HTMLCanvasElement): Promise<void> {
    // Enhanced browser compatibility settings
    const isFirefox = navigator.userAgent.toLowerCase().includes('firefox');
    const isSafari = navigator.userAgent.toLowerCase().includes('safari') && !navigator.userAgent.toLowerCase().includes('chrome');

    console.log(`[RENDERER] Browser detection: Firefox=${isFirefox}, Safari=${isSafari}`);

    await this.app.init({
      canvas,
      width: this.config.centerX * 2,
      height: this.config.centerY * 2,
      backgroundColor: 0x1a1a1a,
      antialias: true,
      resolution: window.devicePixelRatio || 1,
      autoDensity: true,
      // Enhanced compatibility settings for Firefox/Safari
      preference: (isFirefox || isSafari ? 'webgl' : 'webgl') as 'webgl', // Force WebGL1 for better compatibility
      powerPreference: 'default' as any, // Use default power preference for better compatibility
      premultipliedAlpha: false, // Disable premultiplied alpha for better compatibility
      preserveDrawingBuffer: false, // Disable for better performance
      failIfMajorPerformanceCaveat: false // Don't fail on performance issues
    });

    // Set up container hierarchy
    this.setupContainers();
    
    this.isInitialized = true;
  }

  /**
   * Sets up the container hierarchy
   */
  private setupContainers(): void {
    // Set up wheel container pivot point for rotation
    this.wheelContainer.pivot.set(this.config.centerX, this.config.centerY);
    this.wheelContainer.position.set(this.config.centerX, this.config.centerY);

    // Add rotating parts to wheel container in correct order (back to front)
    this.wheelContainer.addChild(this.wheelGraphics);     // Wheel rim first (background)
    this.wheelContainer.addChild(this.segmentsContainer); // Outer segments on top of rim
    this.wheelContainer.addChild(this.nailsContainer);    // Outer nails on top

    // Add inner wheel components
    this.innerWheelContainer.addChild(this.innerSegmentsContainer); // Inner segments
    this.innerWheelContainer.addChild(this.innerNailsContainer);    // Inner nails
    this.wheelContainer.addChild(this.innerWheelContainer);         // Inner wheel on top of outer

    // Set up upper half mask
    this.setupUpperHalfMask();

    // Add containers in rendering order (back to front)
    this.container.addChild(this.wheelContainer);
    this.container.addChild(this.textContainer); // Text doesn't rotate
    this.container.addChild(this.ballGraphics); // Ball is on top
    this.container.addChild(this.upperHalfMask); // Upper half overlay

    this.app.stage.addChild(this.container);
  }

  /**
   * Renders the wheel segments
   */
  public renderSegments(segments: WheelSegment[]): void {
    this.segments = segments;
    this.segmentsContainer.removeChildren();
    this.innerSegmentsContainer.removeChildren();

    segments.forEach((segment, index) => {
      // Render outer wheel segment
      this.renderSegment(segment, index);
      // Render corresponding inner wheel segment
      this.renderInnerSegment(segment, index);
    });
  }

  /**
   * Renders a single wheel segment (outer wheel with distinct colors)
   */
  private renderSegment(segment: WheelSegment, index: number): void {
    const graphics = new Graphics();

    // Use extremityColor for outer wheel if available, otherwise use color
    let segmentColor = (segment as any).extremityColor || segment.color;

    // Validate and fallback color if needed
    if (!segmentColor || !segmentColor.match(/^#[0-9A-Fa-f]{6}$/)) {
      // Fallback to a default color if segment color is invalid
      segmentColor = '#95A5A6'; // Default neutral gray
      console.warn(`[RENDERER] ⚠️ Invalid color for outer segment ${index}: ${(segment as any).extremityColor || segment.color}, using fallback: ${segmentColor}`);
    }

    // Convert color and log for debugging
    const pixiColor = hexToPixiColor(segmentColor);

    // Draw segment arc
    graphics.moveTo(this.config.centerX, this.config.centerY);
    graphics.arc(
      this.config.centerX,
      this.config.centerY,
      this.config.radius,
      segment.startAngle,
      segment.endAngle
    );
    graphics.closePath();
    graphics.fill(pixiColor);

    // Add visible border between segments for better distinction
    graphics.stroke({
      color: 0x000000, // Black border for better visibility
      width: 2
    });

    console.log(`[RENDERER] 🎨 Outer segment ${index}: color=${segmentColor}`);
    this.segmentsContainer.addChild(graphics);
  }




  /**
   * Renders a segment for the inner wheel using pure domain color
   */
  private renderInnerSegment(segment: WheelSegment, index: number): void {
    const graphics = new Graphics();

    // Use centerColor for inner wheel (domain color)
    let domainColor = (segment as any).centerColor;

    // Fallback to domain-based color if centerColor not available
    if (!domainColor && (segment as any).domain) {
      try {
        domainColor = getDomainColor((segment as any).domain);
      } catch (error) {
        console.warn('Could not get domain color for inner segment:', error);
        domainColor = '#000000'; // Fallback to physical color
      }
    }

    // Final fallback
    if (!domainColor) {
      domainColor = '#000000'; // Default physical color
    }

    // Validate color
    const validDomainColor = domainColor?.match(/^#[0-9A-Fa-f]{6}$/) ? domainColor : '#E74C3C';
    const domainPixiColor = hexToPixiColor(validDomainColor);

    // Draw inner segment arc
    graphics.moveTo(this.config.centerX, this.config.centerY);
    graphics.arc(
      this.config.centerX,
      this.config.centerY,
      this.config.innerWheelRadius,
      segment.startAngle,
      segment.endAngle
    );
    graphics.closePath();
    graphics.fill(domainPixiColor);

    // No border - domain colors should be in direct contact for clean look

    this.innerSegmentsContainer.addChild(graphics);
    console.log(`[RENDERER] 🎨 Inner segment ${index}: domain color=${validDomainColor}`);
  }

  /**
   * Renders text labels for segments (disabled as requested)
   */
  public renderSegmentText(_segments: WheelSegment[]): void {
    // Text rendering disabled per user request
    this.textContainer.removeChildren();
  }

  /**
   * Renders the nails around the wheel perimeter
   */
  public renderNails(): void {
    this.nailsContainer.removeChildren();
    this.innerNailsContainer.removeChildren();

    // Render outer wheel nails
    const nailPositions = calculateNailPositions(this.config);

    nailPositions.forEach((pos) => {
      const nail = new Graphics();
      nail.circle(0, 0, this.config.nailRadius/2);
      nail.fill(0xC0C0C0); // Silver color
      /*nail.stroke({
        color: 0x808080,
        width: 1
      });*/

      nail.x = pos.x;
      nail.y = pos.y;

      this.nailsContainer.addChild(nail);
    });

    // Render inner wheel nails (fewer nails, spaced by 1.5x ball diameter)
    this.renderInnerNails();
  }

  /**
   * Renders nails for the inner wheel with proper spacing
   */
  private renderInnerNails(): void {
    const ballDiameter = this.config.ballRadius * 2;
    const nailSpacing = ballDiameter * 1.5;
    const circumference = 2 * Math.PI * this.config.innerWheelRadius;
    const actualNailCount = Math.floor(circumference / nailSpacing);

    // Ensure we don't exceed the configured inner nail count
    const nailCount = Math.min(actualNailCount, this.config.innerNailCount);

    for (let i = 0; i < nailCount; i++) {
      const angle = (i / nailCount) * 2 * Math.PI;
      const x = this.config.centerX + Math.cos(angle) * this.config.innerWheelRadius;
      const y = this.config.centerY + Math.sin(angle) * this.config.innerWheelRadius;

      const nail = new Graphics();
      nail.circle(0, 0, this.config.nailRadius);
      nail.fill(0xC0C0C0); // Silver color
      /*nail.stroke({
        color: 0x808080,
        width: 1
      });*/

      nail.x = x;
      nail.y = y;

      this.innerNailsContainer.addChild(nail);
    }

    console.log(`[RENDERER] 🔩 Rendered ${nailCount} inner nails (spacing: ${nailSpacing.toFixed(1)}px)`);
  }

  /**
   * Renders the ball
   */
  public renderBall(x: number, y: number): void {
    // Only log ball position when it changes significantly (reduce spam)
    const lastPos = this.lastBallPosition || { x: 0, y: 0 };
    const distance = Math.sqrt((x - lastPos.x) ** 2 + (y - lastPos.y) ** 2);

    if (distance > 5 || !this.lastBallPosition) { // Only log when ball moves >5 pixels
      console.log(`[RENDERER] 🎱 Rendering ball at: (${x.toFixed(1)}, ${y.toFixed(1)})`);
      this.lastBallPosition = { x, y };
    }

    this.ballGraphics.clear();

    // Make ball more visible with larger size and brighter colors
    const ballRadius = this.config.ballRadius * 1.2; // 20% larger

    // Main ball body - bright red
    this.ballGraphics.circle(x, y, ballRadius);
    this.ballGraphics.fill(0xFF0000); // Bright red ball

    // Ball highlight for 3D effect - white highlight
    this.ballGraphics.circle(
      x - ballRadius * 0.3,
      y - ballRadius * 0.3,
      ballRadius * 0.4
    );
    this.ballGraphics.fill(0xFFFFFF); // White highlight

    // Ball shadow/outline - black outline
    this.ballGraphics.circle(x, y, ballRadius);
    this.ballGraphics.stroke({
      color: 0x000000,
      width: 1
    });

    // Add a glowing effect
    this.ballGraphics.circle(x, y, ballRadius + 2);
    this.ballGraphics.stroke({
      color: 0xFF4444,
      width: 1,
      alpha: 0.5
    });
  }

  /**
   * Updates the ball position from physics
   */
  public updateBall(frame: AnimationFrame): void {
    this.renderBall(frame.ballPosition.x, frame.ballPosition.y);
  }

  /**
   * Updates the wheel rotation
   */
  public updateWheelRotation(rotation: number): void {
    this.wheelContainer.rotation = rotation;
  }

  /**
   * Updates both ball and wheel from animation frame
   */
  public updateFromFrame(frame: AnimationFrame): void {
    this.updateBall(frame);
    this.updateWheelRotation(frame.wheelRotation);
  }

  /**
   * Renders the wheel background (rim and center hub only, not covering segments)
   */
  public renderWheelBackground(): void {
    this.wheelGraphics.clear();

    // Only render the outer rim (donut shape) without covering segments
    // Outer rim circle
    this.wheelGraphics.circle(
      this.config.centerX,
      this.config.centerY,
      this.config.radius + 10
    );
    this.wheelGraphics.stroke({
      color: 0x444444,
      width: 12 // Thick rim
    });

    // Center hub (small circle in the middle)
    this.wheelGraphics.circle(
      this.config.centerX,
      this.config.centerY,
      20
    );
    this.wheelGraphics.fill(0x333333);
    this.wheelGraphics.stroke({
      color: 0x555555,
      width: 2
    });
  }

  /**
   * Highlights the winning segment
   */
  public highlightSegment(segment: WheelSegment): void {
    const graphics = new Graphics();
    
    // Draw highlighted segment with glow effect
    graphics.moveTo(this.config.centerX, this.config.centerY);
    graphics.arc(
      this.config.centerX,
      this.config.centerY,
      this.config.radius + 5,
      segment.startAngle,
      segment.endAngle
    );
    graphics.closePath();
    graphics.fill(0xFFD700); // Gold highlight
    graphics.stroke({
      color: 0xFFA500,
      width: 4
    });
    
    // Add to segments container temporarily
    this.segmentsContainer.addChild(graphics);
    
    // Remove highlight after animation
    setTimeout(() => {
      if (graphics.parent) {
        graphics.parent.removeChild(graphics);
      }
    }, 2000);
  }

  /**
   * Adds a spinning animation effect
   */
  public addSpinEffect(): void {
    // Spin effects are now handled by the physics engine
    // This method is kept for compatibility
  }

  /**
   * Removes spin effects
   */
  public removeSpinEffect(): void {
    // Spin effects are now handled by the physics engine
    // This method is kept for compatibility
  }

  /**
   * Resizes the renderer
   */
  public resize(width: number, height: number): void {
    if (!this.app || !this.app.renderer || !this.isInitialized) {
      console.warn('Cannot resize: PixiJS app not initialized');
      return;
    }

    try {
      this.app.renderer.resize(width, height);

      // Update config if needed
      this.config.centerX = width / 2;
      this.config.centerY = height / 2;
      this.config.radius = (width / 2) * 0.8; // 80% of available space

      // Always re-render wheel background and nails
      this.renderWheelBackground();
      this.renderNails();

      // Re-render segments if we have them
      if (this.segments.length > 0) {
        this.renderSegments(this.segments);
      }
    } catch (error) {
      console.error('Error resizing renderer:', error);
    }
  }

  /**
   * Gets the PixiJS application instance
   */
  public getApp(): Application {
    return this.app;
  }

  /**
   * Gets the main container
   */
  public getContainer(): Container {
    return this.container;
  }

  /**
   * Gets the renderer state
   */
  public getRendererState(): WheelRenderer {
    return {
      app: this.app,
      container: this.container,
      wheelGraphics: this.wheelGraphics,
      segmentsContainer: this.segmentsContainer,
      textContainer: this.textContainer,
      nailsContainer: this.nailsContainer,
      ballGraphics: this.ballGraphics,
      isInitialized: this.isInitialized
    };
  }

  /**
   * Sets up the upper half mask for gradual hiding
   */
  private setupUpperHalfMask(): void {
    this.updateUpperHalfMask();
  }

  /**
   * Updates the upper half mask opacity
   */
  private updateUpperHalfMask(): void {
    this.upperHalfMask.clear();

    // Create a semi-transparent overlay for the upper half
    this.upperHalfMask.rect(
      0,
      0,
      this.config.centerX * 2,
      this.config.centerY
    );
    this.upperHalfMask.fill({ color: 0x000000, alpha: 1 - this.upperHalfOpacity });
  }

  /**
   * Gradually hides the upper half of the wheel
   */
  public hideUpperHalf(progress: number): void {
    // progress: 0 = fully visible, 1 = fully hidden
    this.upperHalfOpacity = Math.max(0, 1 - progress);
    this.updateUpperHalfMask();
  }

  /**
   * Shows the upper half of the wheel
   */
  public showUpperHalf(): void {
    this.upperHalfOpacity = 1.0;
    this.updateUpperHalfMask();
  }

  /**
   * Destroys the renderer and cleans up resources
   */
  public destroy(): void {
    // Remove all children
    this.container.removeChildren();

    // Stop ticker
    this.app.ticker.stop();

    // Destroy application
    this.app.destroy({
      removeView: true
    }, {
      children: true,
      texture: true,
      textureSource: true
    });

    this.isInitialized = false;
  }
}
