/**
 * Main spinning wheel Lit component
 * Integrates physics engine, renderer, and user interaction
 */

import { LitElement, html, css, PropertyValues } from 'lit';
import { customElement, property, state, query } from 'lit/decorators.js';
import { ref, createRef, Ref } from 'lit/directives/ref.js';

import { WheelPhysicsEngine } from './wheel-physics.js';
import { WheelPixiRenderer } from './wheel-renderer.js';

import type {
  WheelData,
  WheelSegment,
  WheelConfig,
  SpinState,
  InteractionData,
  AnimationFrame,
  WheelEvents
} from './wheel-types.js';

import {
  DEFAULT_WHEEL_CONFIG,
  WHEEL_COLORS,
  isWheelData
} from './wheel-types.js';

import {
  calculateSegmentAngles,
  calculateSubdividedSegments,
  calculateFixed100Segments,
  getSegmentAtAngle,
  getWinningSegmentAdvanced,
  getWinningSegmentUltraReliable,
  calculateSpinVelocity,
  isPointInCircle,
  normalizeAngle
} from '../../utils/physics-utils.js';

import {
  generateColorPalette,
  optimizeSegmentColors
} from '../../utils/color-utils.js';

/**
 * Spinning wheel web component
 */
@customElement('game-wheel')
export class GameWheel extends LitElement {
  static styles = css`
    :host {
      display: block;
      width: 100%;
      height: 100%;
      position: relative;
      user-select: none;
      touch-action: none;
    }

    .wheel-container {
      width: 100%;
      height: 100%;
      position: relative;
      overflow: hidden;
      border-radius: 50%;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }

    .wheel-canvas {
      width: 100%;
      height: 100%;
      display: block;
      cursor: pointer;
      transition: transform 0.1s ease-out;
      transform-origin: center bottom;
    }

    .wheel-canvas:active {
      cursor: grabbing;
    }

    .wheel-canvas.zooming {
      transition: transform 0.1s ease-out;
    }

    .spin-button {
      position: absolute;
      bottom: 20px;
      left: 50%;
      transform: translateX(-50%);
      padding: 12px 24px;
      background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
      color: white;
      border: none;
      border-radius: 25px;
      font-size: 16px;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    .spin-button:hover {
      transform: translateX(-50%) translateY(-2px);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    }

    .spin-button:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: translateX(-50%);
    }

    .wheel-status {
      position: absolute;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 14px;
      font-weight: bold;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    .wheel-status.visible {
      opacity: 1;
    }

    .error-message {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(255, 0, 0, 0.9);
      color: white;
      padding: 16px;
      border-radius: 8px;
      text-align: center;
      max-width: 300px;
    }



    .waiting {
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(0, 0, 0, 0.8);
    }

    .waiting-message {
      text-align: center;
      color: white;
    }

    .waiting-message p {
      margin: 16px 0 0 0;
      font-size: 16px;
      font-weight: 500;
    }

    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid rgba(255, 255, 255, 0.3);
      border-top: 4px solid #4ecdc4;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* Winning Modal Styles - FIXED: Positioned relative to wheel */
    .winning-modal {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
      opacity: 0;
      animation: modalFadeIn 0.5s ease-out forwards;
      border-radius: inherit; /* Match wheel container border radius */
    }

    .winning-modal-content {
      background: white;
      border-radius: 20px;
      padding: 40px;
      max-width: 600px;
      width: 90%;
      max-height: 80vh;
      overflow-y: auto;
      text-align: center;
      position: relative;
      transform: scale(0.7) translateY(50px);
      animation: modalSlideIn 0.6s ease-out 0.2s forwards;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    }

    .winning-header {
      margin-bottom: 30px;
    }

    .winning-title {
      font-size: 2.5rem;
      font-weight: bold;
      margin-bottom: 10px;
      background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .winning-subtitle {
      font-size: 1.2rem;
      color: #666;
      margin-bottom: 20px;
    }

    .winning-activity {
      background: var(--winning-color, #4ecdc4);
      color: white;
      padding: 30px;
      border-radius: 15px;
      margin: 20px 0;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    }

    .winning-activity-name {
      font-size: 2rem;
      font-weight: bold;
      margin-bottom: 15px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .winning-activity-details {
      font-size: 1.1rem;
      line-height: 1.6;
      opacity: 0.95;
      text-align: left;
    }

    .activity-description {
      margin-bottom: 20px;
    }

    .activity-description p {
      margin: 0;
      font-size: 1rem;
      line-height: 1.5;
    }

    .activity-metadata {
      display: flex;
      flex-direction: column;
      gap: 10px;
      margin-top: 15px;
    }

    .metadata-item {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 0.95rem;
    }

    .metadata-icon {
      font-size: 1.1rem;
    }

    .metadata-label {
      font-weight: 600;
      opacity: 0.9;
    }

    .metadata-value {
      font-weight: 500;
      opacity: 0.95;
    }

    .fallback-message {
      font-style: italic;
      opacity: 0.8;
    }

    .fallback-message p {
      margin: 0;
    }

    .winning-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 20px;
      margin: 30px 0;
    }

    .winning-stat {
      background: #f8f9fa;
      padding: 20px;
      border-radius: 10px;
      border-left: 4px solid var(--winning-color, #4ecdc4);
    }

    .winning-stat-label {
      font-size: 0.9rem;
      color: #666;
      margin-bottom: 5px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .winning-stat-value {
      font-size: 1.5rem;
      font-weight: bold;
      color: #333;
    }

    .winning-actions {
      margin-top: 30px;
      display: flex;
      gap: 15px;
      justify-content: center;
      flex-wrap: wrap;
    }

    .winning-action-btn {
      padding: 12px 24px;
      border: none;
      border-radius: 25px;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .winning-action-primary {
      background: var(--winning-color, #4ecdc4);
      color: white;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    .winning-action-primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    }

    .winning-action-secondary {
      background: #f8f9fa;
      color: #666;
      border: 2px solid #e9ecef;
    }

    .winning-action-secondary:hover {
      background: #e9ecef;
      color: #333;
    }

    .winning-close {
      position: absolute;
      top: 15px;
      right: 20px;
      background: none;
      border: none;
      font-size: 2rem;
      color: #999;
      cursor: pointer;
      transition: color 0.3s ease;
    }

    .winning-close:hover {
      color: #333;
    }

    .winning-confetti {
      position: absolute;
      top: -10px;
      left: 50%;
      transform: translateX(-50%);
      font-size: 3rem;
      animation: confettiFall 2s ease-out infinite;
    }

    @keyframes modalFadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }

    @keyframes modalSlideIn {
      from {
        transform: scale(0.7) translateY(50px);
        opacity: 0;
      }
      to {
        transform: scale(1) translateY(0);
        opacity: 1;
      }
    }

    @keyframes confettiFall {
      0% { transform: translateX(-50%) translateY(-20px) rotate(0deg); opacity: 1; }
      100% { transform: translateX(-50%) translateY(20px) rotate(360deg); opacity: 0; }
    }

    @media (max-width: 768px) {
      .spin-button {
        bottom: 10px;
        padding: 10px 20px;
        font-size: 14px;
      }

      .winning-modal-content {
        padding: 20px;
        margin: 20px;
      }

      .winning-title {
        font-size: 2rem;
      }

      .winning-activity-name {
        font-size: 1.5rem;
      }

      .winning-stats {
        grid-template-columns: 1fr;
      }
    }
  `;

  // Public properties
  @property({ type: Object })
  wheelData: WheelData | null = null;

  @property({ type: Object })
  config: Partial<WheelConfig> = {};

  @property({ type: Boolean })
  autoSpin = false;

  @property({ type: Number })
  spinDelay = 1000;

  @property({ type: Boolean })
  hideUI = false; // Hide spin button and legend for clean reuse

  @property({ type: Boolean, attribute: 'disable-interaction' })
  disableInteraction = false; // Disable all user interactions

  @property({ type: Boolean })
  invisible = false; // Start invisible until wheel items are set

  // Internal state
  @state()
  private isSpinning = false;

  @state()
  private statusMessage = '';

  @state()
  private errorMessage = '';

  @state()
  private segments: WheelSegment[] = [];

  @state()
  private currentZoom = 1.0; // Current zoom level (1x to 4x)

  @state()
  private isZooming = false; // Whether progressive zoom is active

  @state()
  private showWinningModal = false; // Whether to show winning modal

  @state()
  private winningSegment: WheelSegment | null = null; // The winning segment for modal

  @state()
  private showWinningAnimation = false; // Whether to show winning animation

  // Component references
  private canvasRef: Ref<HTMLCanvasElement> = createRef();
  private physicsEngine: WheelPhysicsEngine | null = null;
  private renderer: WheelPixiRenderer | null = null;
  private wheelConfig: WheelConfig = DEFAULT_WHEEL_CONFIG;
  private spinState: SpinState | null = null;
  private interactionData: InteractionData | null = null;
  private resizeObserver: ResizeObserver | null = null;

  // Ultra-reliable winner detection data
  private collisionHistory: Array<{ nailIndex: number; timestamp: number }> = [];
  private ballPositionSamples: Array<{ x: number; y: number; timestamp: number }> = [];
  private samplingInterval: number | null = null;

  /**
   * Lifecycle: Component connected to DOM
   */
  connectedCallback(): void {
    super.connectedCallback();
    this.setupResizeObserver();
  }

  /**
   * Lifecycle: Component disconnected from DOM
   */
  disconnectedCallback(): void {
    super.disconnectedCallback();
    this.cleanup();
  }

  /**
   * Lifecycle: Properties changed
   */
  protected willUpdate(changedProperties: PropertyValues<this>): void {
    if (changedProperties.has('wheelData') && this.wheelData) {
      // ARCHITECTURAL FIX: Only process wheel data if it has actually changed
      const previousWheelData = changedProperties.get('wheelData');
      if (this.hasWheelDataChanged(previousWheelData, this.wheelData)) {
        console.log('[WHEEL] 🔄 Wheel data changed, processing...');
        this.processWheelData();
        // Make wheel visible when wheelData is set (for production compatibility)
        this.invisible = false;
      } else {
        // Only log caching message if caching is enabled
        if (this.wheelConfig.cacheEnabled) {
          console.log('[WHEEL] ⏭️ Wheel data unchanged, skipping processing');
        }
      }
    }

    if (changedProperties.has('config')) {
      this.updateConfig();
    }

    // Initialize wheel when becoming visible
    if (changedProperties.has('invisible') && !this.invisible && !this.physicsEngine) {
      // Defer initialization to next frame to ensure DOM is updated
      setTimeout(() => this.initializeWheel(), 0);
    }
  }

  /**
   * Lifecycle: First update completed
   */
  protected firstUpdated(): void {
    // Only initialize if not invisible, otherwise wait for visibility
    if (!this.invisible) {
      this.initializeWheel();
    }
  }

  /**
   * Checks if wheel data has actually changed to prevent unnecessary processing
   */
  private hasWheelDataChanged(previousData: WheelData | null | undefined, currentData: WheelData): boolean {
    // If no previous data, this is a new wheel
    if (!previousData) {
      return true;
    }

    // Quick checks for obvious differences
    if (previousData.wheelId !== currentData.wheelId) {
      return true;
    }

    if (previousData.segments.length !== currentData.segments.length) {
      return true;
    }

    // Deep comparison of segments (only check essential properties that affect rendering)
    for (let i = 0; i < currentData.segments.length; i++) {
      const prev = previousData.segments[i];
      const curr = currentData.segments[i];

      if (!prev || !curr) {
        return true;
      }

      // Check properties that affect wheel rendering
      if (prev.id !== curr.id ||
          prev.percentage !== curr.percentage ||
          prev.color !== curr.color ||
          (prev.text || prev.name) !== (curr.text || curr.name)) {
        return true;
      }
    }

    // No significant changes detected
    return false;
  }

  /**
   * Processes wheel data with FIXED 100-segment system
   */
  private processWheelData(): void {
    if (!this.wheelData || !isWheelData(this.wheelData)) {
      this.errorMessage = 'Invalid wheel data provided';
      console.log('[WHEEL] Invalid wheel data:', this.wheelData);
      return;
    }

    try {
      console.log('[WHEEL] Processing wheel data with FIXED 100-segment system...');
      console.log('[WHEEL] Input segments:', this.wheelData.segments);

      // Normalize segments to handle both simple and full WheelItem formats
      // CRITICAL FIX: Preserve centerColor and extremityColor for dual-wheel rendering
      const normalizedSegments = this.wheelData.segments.map(segment => ({
        id: segment.id,
        text: segment.text || segment.name || 'Unknown Activity',
        percentage: segment.percentage,
        color: segment.color,
        activityId: segment.activityId || segment.activity_tailored_id || segment.id,
        // CRITICAL FIX: Preserve dual-color properties for inner/outer wheel rendering
        centerColor: (segment as any).centerColor,
        extremityColor: (segment as any).extremityColor,
        domain: (segment as any).domain // Also preserve domain for fallback color logic
      }));

      console.log('[WHEEL] Normalized segments:', normalizedSegments);

      // Create exactly 100 equal segments with proportional activity distribution
      const { segments, totalNailCount } = calculateFixed100Segments(normalizedSegments);
      this.segments = segments;

      console.log('[WHEEL] Generated segments count:', segments.length);
      console.log('[WHEEL] First few segments:', segments.slice(0, 5));

      // Nail count is always 100 (fixed)
      this.wheelConfig.nailCount = totalNailCount;
      console.log(`[WHEEL] FIXED system: ${totalNailCount} nails for ${segments.length} equal segments`);

      // Assign colors if not provided (but avoid adjacent same colors)
      this.assignSegmentColors();

      // Re-render wheel with new segments if renderer is available
      if (this.renderer) {
        console.log('[WHEEL] Re-rendering wheel with new segments...');
        this.renderWheel();
      }

      // Update physics engine with new segments if available
      if (this.physicsEngine) {
        console.log('[WHEEL] Updating physics engine with new segments...');
        // The physics engine should already have the correct nail count from wheelConfig
        // but we may need to recreate it if the segment count changed significantly
      }

      this.errorMessage = '';
    } catch (error) {
      this.errorMessage = `Error processing wheel data: ${error}`;
      console.error('Wheel data processing error:', error);
    }
  }

  /**
   * Assigns colors to segments if not provided
   */
  private assignSegmentColors(): void {
    const segmentsNeedingColors = this.segments.filter(s => !s.color);
    
    if (segmentsNeedingColors.length > 0) {
      const colors = generateColorPalette(this.segments.length);
      const optimizedColors = optimizeSegmentColors(colors);
      
      let colorIndex = 0;
      this.segments.forEach(segment => {
        if (!segment.color) {
          segment.color = optimizedColors[colorIndex % optimizedColors.length];
          colorIndex++;
        }
      });
    }
  }

  /**
   * Updates wheel configuration
   */
  private updateConfig(): void {
    this.wheelConfig = { ...DEFAULT_WHEEL_CONFIG, ...this.config };
  }

  /**
   * Sets up resize observer for responsive behavior
   */
  private setupResizeObserver(): void {
    if (!window.ResizeObserver) return;

    this.resizeObserver = new ResizeObserver(() => {
      this.handleResize();
    });

    this.resizeObserver.observe(this);
  }

  /**
   * Handles component resize
   */
  private handleResize(): void {
    try {
      const rect = this.getBoundingClientRect();
      console.log(`[WHEEL] Component rect: ${rect.width}x${rect.height}`);

      if (rect.width === 0 || rect.height === 0) {
        // Component not yet rendered or hidden
        console.log('[WHEEL] Component not yet rendered or hidden');
        return;
      }

      const size = Math.min(rect.width, rect.height);
      console.log(`[WHEEL] Calculated size: ${size}`);

      this.wheelConfig.centerX = size / 2;
      this.wheelConfig.centerY = size / 2;
      this.wheelConfig.radius = (size / 2) * 0.8; // 80% of available space

      console.log(`[WHEEL] Updated config: center(${this.wheelConfig.centerX}, ${this.wheelConfig.centerY}), radius=${this.wheelConfig.radius}`);

      if (this.renderer) {
        this.renderer.resize(size, size);
        console.log(`[WHEEL] Renderer resized to ${size}x${size}`);
      }

      // Update physics engine with new configuration
      if (this.physicsEngine) {
        console.log('[WHEEL] Updating physics engine configuration...');
        // We need to recreate the physics engine with new config
        this.physicsEngine.destroy();
        this.physicsEngine = new WheelPhysicsEngine(this.wheelConfig);
        this.physicsEngine.initialize();
        this.physicsEngine.start();
        this.setupPhysicsRendererIntegration();
        console.log('[WHEEL] Physics engine updated');
      }
    } catch (error) {
      console.error('Error handling resize:', error);
    }
  }

  /**
   * Initializes the wheel components
   */
  private async initializeWheel(): Promise<void> {
    console.log('[WHEEL] Starting wheel initialization...');

    // Prevent multiple initializations
    if (this.physicsEngine && this.renderer) {
      console.log('[WHEEL] Already initialized, skipping...');
      return;
    }

    if (!this.canvasRef.value) {
      this.errorMessage = 'Canvas element not found';
      console.error('[WHEEL] Canvas element not found!');
      return;
    }

    try {
      // First, handle initial sizing
      this.handleResize();
      console.log(`[WHEEL] Canvas sized to: ${this.wheelConfig.centerX * 2}x${this.wheelConfig.centerY * 2}`);
      console.log(`[WHEEL] Wheel config: center(${this.wheelConfig.centerX}, ${this.wheelConfig.centerY}), radius=${this.wheelConfig.radius}`);

      console.log('[WHEEL] Initializing physics engine...');
      // Initialize physics engine
      this.physicsEngine = new WheelPhysicsEngine(this.wheelConfig);
      this.physicsEngine.initialize();
      console.log('[WHEEL] Physics engine initialized');

      // Reset ball to initial position (top of wheel, gravity disabled)
      this.physicsEngine.resetBall();
      console.log('[WHEEL] Ball reset to initial position with gravity disabled');

      console.log('[WHEEL] Initializing renderer...');
      // Initialize renderer
      this.renderer = new WheelPixiRenderer(this.wheelConfig);
      await this.renderer.initialize(this.canvasRef.value);
      console.log('[WHEEL] Renderer initialized');

      // Set up physics-renderer integration
      this.setupPhysicsRendererIntegration();
      console.log('[WHEEL] Physics-renderer integration set up');

      // Render initial state
      this.renderWheel();
      console.log('[WHEEL] Initial wheel rendered');

      // Start physics simulation
      this.physicsEngine.start();
      console.log('[WHEEL] Physics simulation started');

      // Auto-spin if enabled
      if (this.autoSpin) {
        setTimeout(() => this.spin(), this.spinDelay);
      }

      console.log('[WHEEL] Wheel initialization complete!');

    } catch (error) {
      this.errorMessage = `Failed to initialize wheel: ${error}`;
      console.error('Wheel initialization error:', error);
    }
  }

  /**
   * Sets up integration between physics and renderer
   */
  private setupPhysicsRendererIntegration(): void {
    if (!this.physicsEngine || !this.renderer) return;

    this.physicsEngine.onAnimationFrame((frame: AnimationFrame) => {
      this.renderer!.updateFromFrame(frame);
      this.updateSpinState(frame);
      this.updateProgressiveZoom(frame);
    });
  }

  /**
   * Renders the wheel components
   */
  private renderWheel(): void {
    if (!this.renderer) return;

    // Always render wheel background and nails, even without segments
    this.renderer.renderWheelBackground();
    this.renderer.renderNails();

    // Only render segments if they exist
    if (this.segments.length > 0) {
      this.renderer.renderSegments(this.segments);
    }

    // Render initial ball position using actual physics position
    const ballPosition = this.physicsEngine?.getBallPosition();
    if (ballPosition) {
      console.log(`[WHEEL] 🎱 Rendering initial ball position at: (${ballPosition.x}, ${ballPosition.y})`);
      this.renderer.renderBall(ballPosition.x, ballPosition.y);
    }
  }

  /**
   * Renders the component template
   */
  render() {
    // If invisible, show waiting screen
    if (this.invisible) {
      return html`
        <div class="wheel-container waiting">
          <div class="waiting-message">
            <div class="spinner"></div>
            <p>Waiting for wheel data...</p>
          </div>
        </div>
      `;
    }

    return html`
      <div class="wheel-container">
        <canvas ${ref(this.canvasRef)} class="wheel-canvas"
                @pointerdown=${this.handlePointerDown}
                @pointermove=${this.handlePointerMove}
                @pointerup=${this.handlePointerUp}
                @pointercancel=${this.handlePointerUp}>
        </canvas>

        <div class="wheel-status ${this.statusMessage ? 'visible' : ''}">
          ${this.statusMessage}
        </div>

        <!-- Spin button removed - will be replaced with dynamic generate/spin button in parent component -->



        ${this.errorMessage ? html`
          <div class="error-message">
            ${this.errorMessage}
          </div>
        ` : ''}
      </div>

      ${this.showWinningModal && this.winningSegment ? this.renderWinningModal() : ''}
    `;
  }

  /**
   * Renders the winning modal with activity details
   */
  private renderWinningModal() {
    if (!this.winningSegment) return '';

    const segment = this.winningSegment;
    const winningColor = segment.color || '#4ecdc4';

    return html`
      <div class="winning-modal" @click=${this.handleModalBackdropClick}>
        <div class="winning-modal-content" style="--winning-color: ${winningColor}">
          <button class="winning-close" @click=${this.closeWinningModal}>×</button>

          <div class="winning-confetti">🎉</div>

          <div class="winning-header">
            <h1 class="winning-title">Congratulations!</h1>
            <p class="winning-subtitle">Your wheel has chosen an activity for you</p>
          </div>

          <div class="winning-activity" style="background: ${winningColor}">
            <h2 class="winning-activity-name">${segment.text || (segment as any).name}</h2>
            <div class="winning-activity-details">
              ${(segment as any).description ? html`
                <div class="activity-description">
                  <p>${(segment as any).description}</p>
                </div>
              ` : ''}

              <div class="activity-metadata">
                ${(segment as any).domain ? html`
                  <div class="metadata-item">
                    <span class="metadata-icon">🎯</span>
                    <span class="metadata-label">Domain:</span>
                    <span class="metadata-value">${(segment as any).domain}</span>
                  </div>
                ` : ''}

                ${(segment as any).base_challenge_rating ? html`
                  <div class="metadata-item">
                    <span class="metadata-icon">⚡</span>
                    <span class="metadata-label">Challenge:</span>
                    <span class="metadata-value">${(segment as any).base_challenge_rating}/100</span>
                  </div>
                ` : ''}

                ${segment.percentage ? html`
                  <div class="metadata-item">
                    <span class="metadata-icon">📊</span>
                    <span class="metadata-label">Weight:</span>
                    <span class="metadata-value">${segment.percentage.toFixed(1)}%</span>
                  </div>
                ` : ''}
              </div>

              ${!(segment as any).description ? html`
                <div class="fallback-message">
                  <p>Get ready for an exciting activity tailored just for you!</p>
                </div>
              ` : ''}
            </div>
          </div>

          <div class="winning-stats">
            <div class="winning-stat">
              <div class="winning-stat-label">Activity ID</div>
              <div class="winning-stat-value">${segment.activityId || segment.id}</div>
            </div>
            <div class="winning-stat">
              <div class="winning-stat-label">Probability</div>
              <div class="winning-stat-value">${segment.percentage}%</div>
            </div>
            <div class="winning-stat">
              <div class="winning-stat-label">Category</div>
              <div class="winning-stat-value">Activity</div>
            </div>
          </div>

          <div class="winning-actions">
            <button class="winning-action-btn winning-action-primary" @click=${this.startActivity}>
              Start Activity
            </button>
            <button class="winning-action-btn winning-action-secondary" @click=${this.spinAgain}>
              Spin Again
            </button>
            <button class="winning-action-btn winning-action-secondary" @click=${this.closeWinningModal}>
              Close
            </button>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Closes the winning modal
   */
  private closeWinningModal = () => {
    this.showWinningModal = false;
    this.winningSegment = null;
  };

  /**
   * Handles modal backdrop click (close on backdrop click)
   */
  private handleModalBackdropClick = (event: Event) => {
    if (event.target === event.currentTarget) {
      this.closeWinningModal();
    }
  };

  /**
   * Handles start activity button click
   */
  private startActivity = () => {
    if (this.winningSegment) {
      this.dispatchEvent(new CustomEvent('activity-start', {
        detail: { segment: this.winningSegment }
      }));
    }
    this.closeWinningModal();
  };

  /**
   * Handles spin again button click
   */
  private spinAgain = () => {
    this.closeWinningModal();
    // Small delay to allow modal to close before spinning
    setTimeout(() => {
      this.spin();
    }, 300);
  };

  /**
   * Handles pointer down events (touch/mouse)
   */
  private handlePointerDown(event: PointerEvent): void {
    if (this.isSpinning) return;

    const rect = this.canvasRef.value!.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    // Check if click is within wheel area
    if (!isPointInCircle(x, y, this.wheelConfig.centerX, this.wheelConfig.centerY, this.wheelConfig.radius)) {
      return;
    }

    this.interactionData = {
      type: event.pointerType === 'touch' ? 'touch' : 'mouse',
      startPosition: { x, y },
      currentPosition: { x, y },
      startTime: performance.now(),
      isActive: true,
      velocity: { x: 0, y: 0 }
    };

    // Capture pointer for consistent tracking
    (event.target as Element).setPointerCapture(event.pointerId);

    this.dispatchEvent(new CustomEvent('wheel-interaction', {
      detail: { type: this.interactionData.type, position: { x, y } }
    }));
  }

  /**
   * Handles pointer move events
   */
  private handlePointerMove(event: PointerEvent): void {
    if (!this.interactionData?.isActive) return;

    const rect = this.canvasRef.value!.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    const now = performance.now();
    const deltaTime = now - this.interactionData.startTime;

    // Calculate velocity
    if (deltaTime > 0) {
      this.interactionData.velocity = {
        x: (x - this.interactionData.currentPosition.x) / deltaTime,
        y: (y - this.interactionData.currentPosition.y) / deltaTime
      };
    }

    this.interactionData.currentPosition = { x, y };
  }

  /**
   * Handles pointer up events
   */
  private handlePointerUp(event: PointerEvent): void {
    if (!this.interactionData?.isActive) return;

    const deltaTime = performance.now() - this.interactionData.startTime;

    // Calculate spin velocity based on interaction
    if (deltaTime > 100) { // Minimum interaction time
      const spinVelocity = calculateSpinVelocity(
        this.interactionData.startPosition.x,
        this.interactionData.startPosition.y,
        this.interactionData.currentPosition.x,
        this.interactionData.currentPosition.y,
        deltaTime,
        this.wheelConfig.centerX,
        this.wheelConfig.centerY
      );

      if (Math.abs(spinVelocity) > 0.001) {
        this.spinWithVelocity(spinVelocity);
      }
    }

    this.interactionData = null;
    (event.target as Element).releasePointerCapture(event.pointerId);
  }

  /**
   * Public method to set wheel items (clean API for reuse)
   */
  public setWheelItems(items: Array<{
    id: string;
    text: string;
    percentage: number;
    color?: string;
    activityId?: string;
  }>): void {
    console.log('[WHEEL] Setting wheel items via public API:', items);

    // Convert items to WheelData format
    const wheelData: WheelData = {
      segments: items.map(item => ({
        id: item.id,
        text: item.text,
        percentage: item.percentage,
        color: item.color || '',
        activityId: item.activityId || item.id
      })),
      wheelId: `wheel-${Date.now()}`,
      createdAt: new Date().toISOString()
    };

    // Set the wheel data and make visible
    this.wheelData = wheelData;
    this.invisible = false;

    // Trigger re-render
    this.requestUpdate();
  }

  /**
   * Spins the wheel with default force
   */
  public spin(): void {
    console.log('[WHEEL] Spin button clicked!');
    console.log(`[WHEEL] Current state - isSpinning: ${this.isSpinning}, segments: ${this.segments.length}, disableInteraction: ${this.disableInteraction}`);
    console.log(`[WHEEL] Physics engine: ${!!this.physicsEngine}, Renderer: ${!!this.renderer}`);
    console.log('[WHEEL] Segments array:', this.segments);
    console.log('[WHEEL] WheelData:', this.wheelData);

    if (this.isSpinning || this.segments.length === 0 || this.disableInteraction || !this.physicsEngine || !this.renderer) {
      console.log('[WHEEL] Spin blocked - checking conditions:');
      console.log(`[WHEEL] - isSpinning: ${this.isSpinning}`);
      console.log(`[WHEEL] - segments.length: ${this.segments.length}`);
      console.log(`[WHEEL] - disableInteraction: ${this.disableInteraction}`);
      console.log(`[WHEEL] - physicsEngine: ${!!this.physicsEngine}`);
      console.log(`[WHEEL] - renderer: ${!!this.renderer}`);

      // If physics engine is not ready, try to initialize it
      if (!this.physicsEngine || !this.renderer) {
        console.log('[WHEEL] Physics engine or renderer not ready, attempting initialization...');
        this.initializeWheel().then(() => {
          console.log('[WHEEL] Initialization complete, retrying spin...');
          // Retry spin after initialization
          setTimeout(() => this.spin(), 100);
        }).catch(error => {
          console.error('[WHEEL] Failed to initialize wheel:', error);
        });
      }
      return;
    }

    // Generate random spin force
    const minForce = this.wheelConfig.spinForce * 0.5;
    const maxForce = this.wheelConfig.spinForce * 1.5;
    const force = minForce + Math.random() * (maxForce - minForce);

    console.log(`[WHEEL] Generated spin force: ${force} (min: ${minForce}, max: ${maxForce})`);
    this.spinWithForce(force);
  }

  /**
   * Spins the wheel with specific force
   */
  public spinWithForce(force: number): void {
    if (this.isSpinning || !this.physicsEngine) return;

    this.startSpin(force);
  }

  /**
   * Spins the wheel with calculated velocity
   */
  private spinWithVelocity(velocity: number): void {
    if (this.isSpinning || !this.physicsEngine) return;

    const force = Math.abs(velocity) * this.wheelConfig.spinForce;
    this.startSpin(force, velocity > 0 ? 0 : Math.PI);
  }

  /**
   * Starts the spin animation
   */
  private startSpin(force: number, angle: number = 0): void {
    console.log(`[WHEEL] Starting spin with force: ${force}, angle: ${angle}`);

    if (!this.physicsEngine || !this.renderer) {
      console.error('[WHEEL] Cannot start spin - missing physics engine or renderer');
      console.log(`[WHEEL] Physics engine: ${!!this.physicsEngine}, Renderer: ${!!this.renderer}`);
      return;
    }

    this.isSpinning = true;
    this.statusMessage = 'Spinning...';

    this.spinState = {
      isSpinning: true,
      startTime: performance.now(),
      duration: 0,
      initialVelocity: force,
      currentVelocity: force
    };

    console.log('[WHEEL] Applying spin force to wheel...');
    // Apply spin force (now spins the wheel) - this will enable gravity and start the ball falling
    this.physicsEngine.spinWheel(force);

    // Add visual effects
    this.renderer.addSpinEffect();

    // Dispatch spin start event
    this.dispatchEvent(new CustomEvent('wheel-spin-start', {
      detail: { force }
    }));

    console.log('[WHEEL] Starting spin monitoring...');

    // Clear previous detection data
    this.collisionHistory = [];
    this.ballPositionSamples = [];

    // Start ball position sampling for ultra-reliable detection
    this.startBallPositionSampling();

    // Monitor spin completion
    this.monitorSpinCompletion();

    // Start ball tracking for debugging
    setTimeout(() => this.startBallTracking(), 100);
  }

  /**
   * Monitors spin completion - waits for BOTH wheel and ball to stop, then waits 1 second before determining winner
   */
  private monitorSpinCompletion(): void {
    let settledTime: number | null = null; // Track when both settled

    const checkCompletion = () => {
      if (!this.physicsEngine || !this.spinState) return;

      // Check if both ball AND wheel have settled with stricter thresholds
      const ballSettled = this.physicsEngine.isBallSettled(0.005); // Stricter threshold
      const wheelSettled = this.physicsEngine.isWheelSettled(0.005); // Stricter threshold
      const bothSettled = ballSettled && wheelSettled;

      const timeElapsed = performance.now() - this.spinState.startTime;
      const timeoutReached = timeElapsed > this.wheelConfig.maxSpinDuration;

      // Enhanced logging with velocity details
      const ballVelocity = this.physicsEngine.getBallVelocity();
      const wheelVelocity = this.physicsEngine.getWheelVelocity();

      // Track when both first settled
      if (bothSettled && settledTime === null) {
        settledTime = performance.now();
        console.log(`[WHEEL] Both ball and wheel settled! Starting 1-second wait before winner detection...`);
      } else if (!bothSettled && settledTime !== null) {
        // If they were settled but now moving again, reset the timer
        settledTime = null;
        console.log(`[WHEEL] Movement detected after settling, resetting wait timer...`);
      }

      // Calculate time since settled
      const timeSinceSettled = settledTime ? performance.now() - settledTime : 0;
      const waitComplete = timeSinceSettled >= 1000; // 1 second wait

      console.log(`[WHEEL] Monitoring: Ball settled: ${ballSettled} (vel: ${ballVelocity.magnitude.toFixed(4)}), Wheel settled: ${wheelSettled} (vel: ${Math.abs(wheelVelocity).toFixed(4)}), Time: ${(timeElapsed/1000).toFixed(1)}s, Wait: ${settledTime ? (timeSinceSettled/1000).toFixed(1) : 'N/A'}s`);

      if ((bothSettled && waitComplete) || timeoutReached) {
        console.log(`[WHEEL] Spin complete! Both settled: ${bothSettled}, Wait complete: ${waitComplete}, Timeout: ${timeoutReached}`);
        this.completeSpin();
      } else {
        requestAnimationFrame(checkCompletion);
      }
    };

    requestAnimationFrame(checkCompletion);
  }

  /**
   * Completes the spin and determines winner
   */
  private completeSpin(): void {
    if (!this.physicsEngine || !this.renderer || !this.spinState) return;

    // Enhanced winner detection using multiple methods
    const ballBody = this.physicsEngine.getBallBody();
    const ballPosition = ballBody?.position;

    if (!ballPosition) {
      console.error('[WHEEL] Ball position not available for winner detection');
      return;
    }

    console.log(`[WHEEL] Ball final position: (${ballPosition.x.toFixed(1)}, ${ballPosition.y.toFixed(1)})`);

    // Get current wheel rotation to adjust segment angles
    const wheelRotation = this.physicsEngine.getWheelRotation();
    console.log(`[WHEEL] Current wheel rotation: ${(wheelRotation * 180 / Math.PI).toFixed(1)}°`);

    // Create rotated segments that account for wheel rotation
    const rotatedSegments = this.segments.map(segment => ({
      ...segment,
      startAngle: segment.startAngle + wheelRotation,
      endAngle: segment.endAngle + wheelRotation,
      centerAngle: segment.centerAngle + wheelRotation
    }));

    // Use ULTRA-RELIABLE winner detection with 4-mechanism consensus
    const nailPositions = this.physicsEngine.getNailPositions();
    const winnerResult = getWinningSegmentUltraReliable(
      ballPosition.x,
      ballPosition.y,
      this.wheelConfig.centerX,
      this.wheelConfig.centerY,
      rotatedSegments,
      nailPositions,
      this.wheelConfig.ballRadius,
      this.collisionHistory,
      this.ballPositionSamples
    );

    const winningSegment = winnerResult.segment;
    const duration = performance.now() - this.spinState.startTime;

    console.log(`[WHEEL] ULTRA-RELIABLE winner detection:`);
    console.log(`   Winner: ${winningSegment?.text || 'None'}`);
    console.log(`   Consensus: ${winnerResult.agreementCount}/4 mechanisms agree (${(winnerResult.consensusLevel * 100).toFixed(1)}%)`);
    console.log(`   Final Confidence: ${(winnerResult.confidence * 100).toFixed(1)}%`);
    console.log(`   Method Results:`, winnerResult.methods);
    console.log(`   Debug info:`, winnerResult.debugInfo);

    // CRITICAL: Check consensus level
    if (winnerResult.agreementCount < 3) {
      console.error(`[WHEEL] 🚨 CRITICAL: Only ${winnerResult.agreementCount}/4 mechanisms agree! This indicates a reliability issue.`);
      console.error(`[WHEEL] Method disagreements:`, winnerResult.methods);
      // In production, this should trigger an alert or retry mechanism
    }

    // Also log simple angle-based detection for comparison
    const ballAngle = this.physicsEngine.getBallAngle();
    const simpleWinner = getSegmentAtAngle(ballAngle, rotatedSegments);
    console.log(`[WHEEL] Simple angle-based winner: ${simpleWinner?.text || 'None'}`);
    console.log(`[WHEEL] Ball angle: ${(ballAngle * 180 / Math.PI).toFixed(1)}°`);

    this.isSpinning = false;
    this.statusMessage = winningSegment ? `Winner: ${winningSegment.text} (${(winnerResult.confidence * 100).toFixed(0)}% confidence)` : 'No winner determined';

    // Remove visual effects
    this.renderer.removeSpinEffect();

    // Highlight winning segment with correct rotation
    if (winningSegment) {
      // Find the corresponding rotated segment for highlighting
      const rotatedWinningSegment = rotatedSegments.find(rs =>
        rs.id === winningSegment.id ||
        (rs.text === winningSegment.text && rs.color === winningSegment.color)
      );

      if (rotatedWinningSegment) {
        console.log(`[WHEEL] Highlighting rotated segment: ${rotatedWinningSegment.text} at angles ${(rotatedWinningSegment.startAngle * 180 / Math.PI).toFixed(1)}° to ${(rotatedWinningSegment.endAngle * 180 / Math.PI).toFixed(1)}°`);
        this.renderer.highlightSegment(rotatedWinningSegment);
      } else {
        console.warn(`[WHEEL] Could not find rotated segment for highlighting: ${winningSegment.text}`);
        this.renderer.highlightSegment(winningSegment);
      }
    }

    // Dispatch completion events
    console.log('🎡 [WHEEL] Dispatching wheel-spin-complete event with winning segment:', winningSegment);
    this.dispatchEvent(new CustomEvent('wheel-spin-complete', {
      detail: {
        winningSegment,
        finalAngle: ballAngle,
        duration
      }
    }));

    if (winningSegment) {
      console.log('🎡 [WHEEL] Dispatching wheel-result event');
      this.dispatchEvent(new CustomEvent('wheel-result', {
        detail: { segment: winningSegment }
      }));
    }

    // ARCHITECTURAL FIX: Don't show winning modal in wheel component
    // Let the parent app-shell handle the modal with complete activity data
    // The wheel component should only handle the spinning mechanics

    // Clear status message after delay
    setTimeout(() => {
      this.statusMessage = '';
    }, 3000);

    this.spinState = null;

    // Stop ball position sampling
    this.stopBallPositionSampling();
  }

  /**
   * Starts ball position sampling for ultra-reliable winner detection
   */
  private startBallPositionSampling(): void {
    if (this.samplingInterval) {
      clearInterval(this.samplingInterval);
    }

    console.log('[WHEEL] Starting ball position sampling for ultra-reliable detection...');

    this.samplingInterval = window.setInterval(() => {
      if (!this.physicsEngine || !this.isSpinning) {
        return;
      }

      const ballPosition = this.physicsEngine.getBallPosition();
      const timestamp = performance.now();

      this.ballPositionSamples.push({
        x: ballPosition.x,
        y: ballPosition.y,
        timestamp: timestamp
      });

      // Keep only last 20 samples (1 second of data at 50ms intervals)
      if (this.ballPositionSamples.length > 20) {
        this.ballPositionSamples.shift();
      }

    }, 50); // Sample every 50ms
  }

  /**
   * Stops ball position sampling
   */
  private stopBallPositionSampling(): void {
    if (this.samplingInterval) {
      clearInterval(this.samplingInterval);
      this.samplingInterval = null;
    }
  }

  /**
   * Records a collision event for ultra-reliable detection
   */
  public recordCollision(nailIndex: number): void {
    const timestamp = performance.now();
    this.collisionHistory.push({ nailIndex, timestamp });

    // Keep only last 10 collisions
    if (this.collisionHistory.length > 10) {
      this.collisionHistory.shift();
    }

    console.log(`[WHEEL] Collision recorded: nail ${nailIndex} at ${timestamp}`);
  }

  /**
   * Updates spin state during animation
   */
  private updateSpinState(frame: AnimationFrame): void {
    if (!this.spinState) return;

    this.spinState.currentVelocity = Math.max(frame.ballVelocity.magnitude, Math.abs(frame.wheelVelocity));
    this.spinState.duration = frame.timestamp - this.spinState.startTime;

    // Dispatch spinning event
    this.dispatchEvent(new CustomEvent('wheel-spinning', {
      detail: {
        velocity: this.spinState.currentVelocity,
        wheelRotation: frame.wheelRotation,
        ballAngle: frame.ballAngle
      }
    }));
  }

  /**
   * Updates progressive zoom based on velocities
   * ENHANCED: Centers zoom on ball position for better visual tracking
   */
  private updateProgressiveZoom(frame: AnimationFrame): void {
    if (!this.isSpinning) {
      // Reset zoom when not spinning
      if (this.currentZoom !== 1.0) {
        this.currentZoom = 1.0;
        this.isZooming = false;
        this.updateZoomTransform();
      }
      return;
    }

    // Calculate combined velocity (ball + wheel)
    const ballVelocity = frame.ballVelocity.magnitude;
    const wheelVelocity = Math.abs(frame.wheelVelocity);
    const combinedVelocity = ballVelocity + wheelVelocity;

    // ENHANCED: Define velocity thresholds - zoom only activates when velocity is very low
    const zoomActivationThreshold = 0.5; // Only start zooming when velocity drops below this
    const maxZoomVelocity = 0.5; // Velocity at which zoom starts (1x)
    const minZoomVelocity = 0.05; // Velocity at which zoom reaches maximum (3x)

    // Calculate zoom level (1x to 3x max) - only when velocity is very low
    let targetZoom = 1.0;

    // ENHANCED: Only activate zoom when velocity is very low
    if (combinedVelocity <= zoomActivationThreshold) {
      if (combinedVelocity <= minZoomVelocity) {
        targetZoom = 2.5;
      } else if (combinedVelocity <= maxZoomVelocity) {
        // Linear interpolation between min and max zoom velocity
        const velocityRatio = (maxZoomVelocity - combinedVelocity) / (maxZoomVelocity - minZoomVelocity);
        targetZoom = 1.0 + (2.0 * velocityRatio); // 1x to 3x zoom (300% max)
      }
    }

    // Smooth zoom transition
    const zoomSpeed = 0.06; // Slightly faster zoom for better UX
    this.currentZoom += (targetZoom - this.currentZoom) * zoomSpeed;

    // Update zoom state
    this.isZooming = this.currentZoom > 1.1;

    // Apply zoom transformation centered on ball
    this.updateZoomTransformCenteredOnBall();
  }

  /**
   * Applies zoom transformation centered on ball position for better visual tracking
   */
  private updateZoomTransformCenteredOnBall(): void {
    if (!this.canvasRef.value || !this.physicsEngine) return;

    const canvas = this.canvasRef.value;
    const ballPosition = this.physicsEngine.getBallPosition();

    // Center zoom on ball position
    const zoomOriginX = ballPosition.x;
    const zoomOriginY = ballPosition.y;

    // Apply CSS transform for zoom centered on ball
    canvas.style.transform = `scale(${this.currentZoom})`;
    canvas.style.transformOrigin = `${zoomOriginX}px ${zoomOriginY}px`;

    // Add/remove zooming class for CSS transition control
    if (this.isZooming) {
      canvas.classList.add('zooming');
    } else {
      canvas.classList.remove('zooming');
    }
  }

  /**
   * Legacy zoom transform method (fallback)
   */
  private updateZoomTransform(): void {
    // Use the new ball-centered method
    this.updateZoomTransformCenteredOnBall();
  }



  /**
   * Gets the current wheel state for debugging
   */
  public getWheelState() {
    return {
      isSpinning: this.isSpinning,
      segments: this.segments,
      config: this.wheelConfig,
      hasPhysicsEngine: !!this.physicsEngine,
      hasRenderer: !!this.renderer,
      spinState: this.spinState,
      errorMessage: this.errorMessage
    };
  }

  /**
   * Starts ball movement tracking for debugging
   */
  public startBallTracking(): void {
    if (!this.physicsEngine) {
      console.error('[BALL TRACKER] No physics engine available');
      return;
    }

    // Capture physicsEngine to ensure its non-null type within the interval callback
    const physicsEngine = this.physicsEngine;

    console.log('[BALL TRACKER] Starting ball movement tracking...');
    let trackingCount = 0;
    let lastPosition: { x: number; y: number } | null = null;
    let movementDetected = false;

    const trackingInterval = setInterval(() => {
      const ballBody = physicsEngine.getBallBody();
      if (!ballBody) {
        console.log('[BALL TRACKER] No ball body, stopping tracking');
        clearInterval(trackingInterval);
        return;
      }

      const pos = ballBody.position;
      const vel = physicsEngine.getBallVelocity();
      const distance = Math.sqrt(
        Math.pow(pos.x - this.wheelConfig.centerX, 2) +
        Math.pow(pos.y - this.wheelConfig.centerY, 2)
      );

      // Check for movement
      if (lastPosition) {
        const movement = Math.sqrt(
          Math.pow(pos.x - lastPosition.x, 2) +
          Math.pow(pos.y - lastPosition.y, 2)
        );
        if (movement > 0.1) {
          movementDetected = true;
        }
      }

      trackingCount++;
      console.log(`[BALL TRACKER ${trackingCount}] Pos: (${pos.x.toFixed(1)}, ${pos.y.toFixed(1)}) Vel: ${vel.magnitude.toFixed(3)} Dist: ${distance.toFixed(1)} Movement: ${movementDetected ? '✅' : '❌'}`);

      lastPosition = { x: pos.x, y: pos.y };

      // Stop tracking after 10 seconds or when settled
      if (trackingCount > 100 || (!this.isSpinning && vel.magnitude < 0.01)) {
        console.log(`[BALL TRACKER] Stopping tracking. Movement detected: ${movementDetected}`);
        clearInterval(trackingInterval);
      }
    }, 100);
  }

  /**
   * Resets the wheel to initial state
   */
  public reset(): void {
    if (this.physicsEngine) {
      this.physicsEngine.resetBall();
    }
    this.isSpinning = false;
    this.statusMessage = '';
    this.spinState = null;
  }

  /**
   * Cleans up resources
   */
  private cleanup(): void {
    if (this.physicsEngine) {
      this.physicsEngine.destroy();
      this.physicsEngine = null;
    }

    if (this.renderer) {
      this.renderer.destroy();
      this.renderer = null;
    }

    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
      this.resizeObserver = null;
    }
  }


}
