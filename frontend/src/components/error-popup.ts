/**
 * Error popup component for displaying non-critical errors in debug mode
 * Modal-style popup with detailed error information
 */

import { LitElement, html, css } from 'lit';
import { customElement, property, state } from 'lit/decorators.js';
import { AppError, ErrorLevel, ErrorType } from '../types/error-types.js';
import { ErrorClassificationService } from '../services/error-classification.js';

@customElement('error-popup')
export class ErrorPopup extends LitElement {
  @property({ type: Object })
  error: AppError | null = null;

  @property({ type: Boolean })
  visible = false;

  @property({ type: Boolean })
  isDebugMode = false;

  @property({ type: Number })
  displayDuration = 5000; // 5 seconds default

  @state()
  private timeRemaining = 0;

  @state()
  private autoCloseTimer: number | null = null;

  static styles = css`
    :host {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 10001;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: rgba(0, 0, 0, 0.5);
      opacity: 0;
      visibility: hidden;
      transition: opacity 0.3s ease, visibility 0.3s ease;
    }

    :host([visible]) {
      opacity: 1;
      visibility: visible;
    }

    .popup {
      background: white;
      border-radius: 8px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
      max-width: 500px;
      width: 90%;
      max-height: 80vh;
      overflow: hidden;
      transform: scale(0.9);
      transition: transform 0.3s ease;
    }

    :host([visible]) .popup {
      transform: scale(1);
    }

    .popup-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 20px;
      border-bottom: 1px solid #e0e0e0;
    }

    .popup-header.non-critical {
      background-color: #fff3cd;
      border-bottom-color: #ffc107;
    }

    .popup-header.validation {
      background-color: #f8d7da;
      border-bottom-color: #dc3545;
    }

    .header-content {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .error-icon {
      font-size: 20px;
    }

    .error-title {
      font-size: 16px;
      font-weight: 600;
      margin: 0;
    }

    .close-button {
      background: none;
      border: none;
      font-size: 18px;
      cursor: pointer;
      padding: 4px;
      opacity: 0.7;
      transition: opacity 0.2s;
    }

    .close-button:hover {
      opacity: 1;
    }

    .popup-body {
      padding: 20px;
      max-height: 400px;
      overflow-y: auto;
    }

    .error-message {
      font-size: 14px;
      line-height: 1.5;
      margin-bottom: 16px;
      color: #333;
    }

    .error-details {
      background-color: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      padding: 12px;
      font-family: monospace;
      font-size: 12px;
      margin-bottom: 16px;
    }

    .detail-row {
      display: flex;
      margin-bottom: 4px;
    }

    .detail-label {
      font-weight: bold;
      min-width: 80px;
      color: #666;
    }

    .detail-value {
      color: #333;
      word-break: break-all;
    }

    .stack-trace {
      background-color: #f1f3f4;
      border: 1px solid #dadce0;
      border-radius: 4px;
      padding: 12px;
      font-family: monospace;
      font-size: 11px;
      white-space: pre-wrap;
      max-height: 200px;
      overflow-y: auto;
      margin-top: 12px;
    }

    .popup-footer {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 20px;
      border-top: 1px solid #e0e0e0;
      background-color: #f8f9fa;
    }

    .auto-close-info {
      font-size: 12px;
      color: #666;
    }

    .action-buttons {
      display: flex;
      gap: 8px;
    }

    .button {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      font-size: 14px;
      cursor: pointer;
      transition: background-color 0.2s;
    }

    .button-primary {
      background-color: #007bff;
      color: white;
    }

    .button-primary:hover {
      background-color: #0056b3;
    }

    .button-secondary {
      background-color: #6c757d;
      color: white;
    }

    .button-secondary:hover {
      background-color: #545b62;
    }

    .progress-bar {
      width: 100%;
      height: 2px;
      background-color: #e9ecef;
      border-radius: 1px;
      overflow: hidden;
      margin-top: 8px;
    }

    .progress-fill {
      height: 100%;
      background-color: #007bff;
      transition: width 0.1s linear;
    }
  `;

  connectedCallback() {
    super.connectedCallback();
    this.startAutoCloseTimer();
  }

  disconnectedCallback() {
    super.disconnectedCallback();
    this.clearAutoCloseTimer();
  }

  updated(changedProperties: Map<string | number | symbol, unknown>) {
    if (changedProperties.has('visible') && this.visible) {
      this.startAutoCloseTimer();
    }
    if (changedProperties.has('displayDuration')) {
      this.startAutoCloseTimer();
    }
  }

  private startAutoCloseTimer() {
    this.clearAutoCloseTimer();
    
    if (this.displayDuration > 0 && this.visible) {
      this.timeRemaining = this.displayDuration;
      
      const updateInterval = 100; // Update every 100ms
      this.autoCloseTimer = window.setInterval(() => {
        this.timeRemaining -= updateInterval;
        
        if (this.timeRemaining <= 0) {
          this.handleClose();
        }
      }, updateInterval);
    }
  }

  private clearAutoCloseTimer() {
    if (this.autoCloseTimer) {
      clearInterval(this.autoCloseTimer);
      this.autoCloseTimer = null;
    }
  }

  private getHeaderClass(): string {
    if (!this.error) return 'non-critical';

    switch (this.error.type) {
      case ErrorType.VALIDATION_ERROR:
        return 'validation';
      default:
        return 'non-critical';
    }
  }

  private getErrorIcon(): string {
    if (!this.error) return '⚠️';

    switch (this.error.type) {
      case ErrorType.VALIDATION_ERROR:
        return '❌';
      case ErrorType.API_ERROR:
        return '🔗';
      default:
        return '⚠️';
    }
  }

  private getErrorTitle(): string {
    if (!this.error) return 'Error';

    switch (this.error.type) {
      case ErrorType.VALIDATION_ERROR:
        return 'Validation Error';
      case ErrorType.API_ERROR:
        return 'API Error';
      default:
        return 'Application Error';
    }
  }

  private handleClose() {
    this.clearAutoCloseTimer();
    this.dispatchEvent(new CustomEvent('error-dismissed', {
      detail: { error: this.error },
      bubbles: true
    }));
  }

  private handleBackdropClick(event: Event) {
    if (event.target === event.currentTarget) {
      this.handleClose();
    }
  }

  private handleCopyError() {
    if (!this.error) return;

    const errorInfo = {
      id: this.error.id,
      type: this.error.type,
      level: this.error.level,
      message: this.error.message,
      component: this.error.component,
      timestamp: this.error.timestamp.toISOString(),
      stack: this.error.stack,
      metadata: this.error.metadata
    };

    navigator.clipboard.writeText(JSON.stringify(errorInfo, null, 2)).then(() => {
      // Could show a brief "Copied!" message
    }).catch(() => {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = JSON.stringify(errorInfo, null, 2);
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
    });
  }

  private getProgressPercentage(): number {
    if (this.displayDuration <= 0) return 0;
    return Math.max(0, (this.timeRemaining / this.displayDuration) * 100);
  }

  render() {
    if (!this.error) return html``;

    const headerClass = this.getHeaderClass();
    const icon = this.getErrorIcon();
    const title = this.getErrorTitle();
    const message = ErrorClassificationService.getUserMessage(this.error, this.isDebugMode);
    const progressPercentage = this.getProgressPercentage();

    return html`
      <div @click=${this.handleBackdropClick}>
        <div class="popup">
          <div class="popup-header ${headerClass}">
            <div class="header-content">
              <span class="error-icon">${icon}</span>
              <h3 class="error-title">${title}</h3>
            </div>
            <button class="close-button" @click=${this.handleClose}>✕</button>
          </div>

          <div class="popup-body">
            <div class="error-message">${message}</div>

            ${this.isDebugMode ? html`
              <div class="error-details">
                <div class="detail-row">
                  <span class="detail-label">Type:</span>
                  <span class="detail-value">${this.error.type}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">Level:</span>
                  <span class="detail-value">${this.error.level}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">Component:</span>
                  <span class="detail-value">${this.error.component || 'unknown'}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">ID:</span>
                  <span class="detail-value">${this.error.id}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">Time:</span>
                  <span class="detail-value">${this.error.timestamp.toLocaleString()}</span>
                </div>
              </div>

              ${this.error.stack ? html`
                <div class="stack-trace">${this.error.stack}</div>
              ` : ''}
            ` : ''}
          </div>

          <div class="popup-footer">
            <div class="auto-close-info">
              ${this.displayDuration > 0 ? html`
                Auto-close in ${Math.ceil(this.timeRemaining / 1000)}s
                <div class="progress-bar">
                  <div class="progress-fill" style="width: ${progressPercentage}%"></div>
                </div>
              ` : ''}
            </div>

            <div class="action-buttons">
              ${this.isDebugMode ? html`
                <button class="button button-secondary" @click=${this.handleCopyError}>
                  Copy Details
                </button>
              ` : ''}
              <button class="button button-primary" @click=${this.handleClose}>
                OK
              </button>
            </div>
          </div>
        </div>
      </div>
    `;
  }
}
