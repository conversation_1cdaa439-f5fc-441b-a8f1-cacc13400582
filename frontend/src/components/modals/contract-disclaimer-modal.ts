/**
 * Contract Disclaimer Modal Component
 * Displays a powerful UX modal requiring user signature before wheel spin
 * Creates a meaningful moment of authority transfer and user commitment
 */

import { LitElement, html, css } from 'lit';
import { customElement, property, state, query } from 'lit/decorators.js';

export interface SignatureData {
  imageData: string;
  timestamp: number;
  isValid: boolean;
}

@customElement('contract-disclaimer-modal')
export class ContractDisclaimerModal extends LitElement {
  @property({ type: Boolean })
  visible = false;

  @state()
  private signatureData: SignatureData | null = null;

  @state()
  private isDrawing = false;

  @state()
  private hasSignature = false;

  @query('#signature-canvas')
  private signatureCanvas!: HTMLCanvasElement;

  private canvasContext: CanvasRenderingContext2D | null = null;
  private lastPoint: { x: number; y: number } | null = null;

  static styles = css`
    :host {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 2000;
      background: rgba(0, 0, 0, 0.7);
      backdrop-filter: blur(4px);
      animation: fadeIn 0.3s ease-out;
    }

    :host([visible]) {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
      }
      to {
        opacity: 1;
      }
    }

    .modal-container {
      background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
      border-radius: 20px;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
      max-width: 600px;
      width: 100%;
      max-height: 90vh;
      overflow-y: auto;
      animation: slideIn 0.4s ease-out;
      position: relative;
    }

    @keyframes slideIn {
      from {
        opacity: 0;
        transform: translateY(-30px) scale(0.95);
      }
      to {
        opacity: 1;
        transform: translateY(0) scale(1);
      }
    }

    .modal-header {
      text-align: center;
      padding: 30px 30px 20px;
      border-bottom: 2px solid #e9ecef;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-radius: 20px 20px 0 0;
    }

    .modal-icon {
      font-size: 3rem;
      margin-bottom: 15px;
      display: block;
    }

    .modal-title {
      font-size: 1.8rem;
      font-weight: 700;
      margin: 0 0 10px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    .modal-subtitle {
      font-size: 1rem;
      opacity: 0.9;
      margin: 0;
      font-weight: 300;
    }

    .modal-body {
      padding: 30px;
    }

    .contract-text {
      background: rgba(102, 126, 234, 0.05);
      border-left: 4px solid #667eea;
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 25px;
      font-size: 1rem;
      line-height: 1.6;
      color: #2c3e50;
    }

    .authority-moment {
      background: linear-gradient(135deg, #ff6b6b, #ee5a52);
      color: white;
      padding: 15px 20px;
      border-radius: 10px;
      margin: 20px 0;
      text-align: center;
      font-weight: 600;
      box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
    }

    .signature-section {
      margin-top: 25px;
    }

    .signature-label {
      font-size: 1.1rem;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 15px;
      text-align: center;
    }

    .signature-canvas-container {
      border: 3px dashed #667eea;
      border-radius: 12px;
      padding: 15px;
      background: #f8f9fa;
      text-align: center;
      transition: all 0.3s ease;
    }

    .signature-canvas-container.has-signature {
      border-color: #28a745;
      background: rgba(40, 167, 69, 0.05);
      animation: signatureSuccess 0.5s ease-out;
    }

    @keyframes signatureSuccess {
      0% {
        border-color: #667eea;
        background: #f8f9fa;
      }
      50% {
        border-color: #28a745;
        background: rgba(40, 167, 69, 0.1);
        transform: scale(1.02);
      }
      100% {
        border-color: #28a745;
        background: rgba(40, 167, 69, 0.05);
        transform: scale(1);
      }
    }

    #signature-canvas {
      border: 2px solid #dee2e6;
      border-radius: 8px;
      background: white;
      cursor: crosshair;
      touch-action: none;
      max-width: 100%;
    }

    .signature-instructions {
      font-size: 0.9rem;
      color: #6c757d;
      margin-top: 10px;
      font-style: italic;
    }

    .signature-actions {
      display: flex;
      gap: 10px;
      justify-content: center;
      margin-top: 15px;
    }

    .clear-signature-btn {
      background: #6c757d;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 0.9rem;
      transition: all 0.2s ease;
    }

    .clear-signature-btn:hover {
      background: #5a6268;
      transform: translateY(-1px);
    }

    .modal-actions {
      display: flex;
      gap: 15px;
      justify-content: center;
      padding: 0 30px 30px;
    }

    .action-btn {
      padding: 12px 30px;
      border: none;
      border-radius: 8px;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      min-width: 120px;
    }

    .cancel-btn {
      background: #6c757d;
      color: white;
    }

    .cancel-btn:hover {
      background: #5a6268;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
    }

    .accept-btn {
      background: linear-gradient(135deg, #28a745, #20c997);
      color: white;
    }

    .accept-btn:hover:not(:disabled) {
      background: linear-gradient(135deg, #218838, #1ea080);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
    }

    .accept-btn:disabled {
      background: #dee2e6;
      color: #6c757d;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    @media (max-width: 768px) {
      .modal-container {
        margin: 10px;
        max-height: 95vh;
      }

      .modal-header {
        padding: 20px 20px 15px;
      }

      .modal-title {
        font-size: 1.5rem;
      }

      .modal-body {
        padding: 20px;
      }

      #signature-canvas {
        width: 100%;
        height: 120px;
      }

      .modal-actions {
        flex-direction: column;
        padding: 0 20px 20px;
      }
    }
  `;

  firstUpdated() {
    this.setupSignatureCanvas();
  }

  private setupSignatureCanvas() {
    if (!this.signatureCanvas) return;

    this.canvasContext = this.signatureCanvas.getContext('2d');
    if (!this.canvasContext) return;

    // Set canvas size
    this.signatureCanvas.width = 400;
    this.signatureCanvas.height = 150;

    // Configure drawing context
    this.canvasContext.strokeStyle = '#2c3e50';
    this.canvasContext.lineWidth = 2;
    this.canvasContext.lineCap = 'round';
    this.canvasContext.lineJoin = 'round';

    // Add event listeners
    this.addCanvasEventListeners();
  }

  private addCanvasEventListeners() {
    // Mouse events
    this.signatureCanvas.addEventListener('mousedown', this.startDrawing.bind(this));
    this.signatureCanvas.addEventListener('mousemove', this.draw.bind(this));
    this.signatureCanvas.addEventListener('mouseup', this.stopDrawing.bind(this));
    this.signatureCanvas.addEventListener('mouseout', this.stopDrawing.bind(this));

    // Touch events
    this.signatureCanvas.addEventListener('touchstart', this.handleTouch.bind(this));
    this.signatureCanvas.addEventListener('touchmove', this.handleTouch.bind(this));
    this.signatureCanvas.addEventListener('touchend', this.stopDrawing.bind(this));
  }

  private getEventPoint(event: MouseEvent | TouchEvent): { x: number; y: number } {
    const rect = this.signatureCanvas.getBoundingClientRect();
    const scaleX = this.signatureCanvas.width / rect.width;
    const scaleY = this.signatureCanvas.height / rect.height;

    if (event instanceof MouseEvent) {
      return {
        x: (event.clientX - rect.left) * scaleX,
        y: (event.clientY - rect.top) * scaleY
      };
    } else {
      const touch = event.touches[0] || event.changedTouches[0];
      return {
        x: (touch.clientX - rect.left) * scaleX,
        y: (touch.clientY - rect.top) * scaleY
      };
    }
  }

  private startDrawing(event: MouseEvent) {
    this.isDrawing = true;
    this.lastPoint = this.getEventPoint(event);
  }

  private draw(event: MouseEvent) {
    if (!this.isDrawing || !this.canvasContext || !this.lastPoint) return;

    const currentPoint = this.getEventPoint(event);
    
    this.canvasContext.beginPath();
    this.canvasContext.moveTo(this.lastPoint.x, this.lastPoint.y);
    this.canvasContext.lineTo(currentPoint.x, currentPoint.y);
    this.canvasContext.stroke();

    this.lastPoint = currentPoint;
    this.hasSignature = true;
    this.requestUpdate();
  }

  private handleTouch(event: TouchEvent) {
    event.preventDefault();
    
    if (event.type === 'touchstart') {
      this.isDrawing = true;
      this.lastPoint = this.getEventPoint(event);
    } else if (event.type === 'touchmove' && this.isDrawing) {
      this.draw(event as any);
    }
  }

  private stopDrawing() {
    this.isDrawing = false;
    this.lastPoint = null;
  }

  private clearSignature() {
    if (!this.canvasContext) return;
    
    this.canvasContext.clearRect(0, 0, this.signatureCanvas.width, this.signatureCanvas.height);
    this.hasSignature = false;
    this.signatureData = null;
    this.requestUpdate();
  }

  private captureSignature(): SignatureData | null {
    if (!this.hasSignature) return null;

    const imageData = this.signatureCanvas.toDataURL('image/png');
    const signature: SignatureData = {
      imageData,
      timestamp: Date.now(),
      isValid: this.validateSignature(imageData)
    };

    // Store signature temporarily in sessionStorage
    this.storeSignatureTemporarily(signature);

    return signature;
  }

  private validateSignature(imageData: string): boolean {
    // Basic validation - check if signature has meaningful content
    // This is a simple check - in production you might want more sophisticated validation
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) return false;

    const img = new Image();
    img.onload = () => {
      canvas.width = img.width;
      canvas.height = img.height;
      ctx.drawImage(img, 0, 0);

      const imageDataObj = ctx.getImageData(0, 0, canvas.width, canvas.height);
      const data = imageDataObj.data;

      // Count non-white pixels (simple signature detection)
      let nonWhitePixels = 0;
      for (let i = 0; i < data.length; i += 4) {
        const r = data[i];
        const g = data[i + 1];
        const b = data[i + 2];
        const alpha = data[i + 3];

        // If pixel is not white/transparent, count it
        if (alpha > 0 && (r < 250 || g < 250 || b < 250)) {
          nonWhitePixels++;
        }
      }

      // Signature is valid if it has at least 50 non-white pixels
      return nonWhitePixels > 50;
    };

    img.src = imageData;
    return true; // Default to true for immediate validation
  }

  private storeSignatureTemporarily(signature: SignatureData) {
    try {
      sessionStorage.setItem('wheel-contract-signature', JSON.stringify(signature));
      console.log('📝 Contract signature stored temporarily');
    } catch (error) {
      console.warn('Failed to store signature temporarily:', error);
    }
  }

  public static getStoredSignature(): SignatureData | null {
    try {
      const stored = sessionStorage.getItem('wheel-contract-signature');
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (error) {
      console.warn('Failed to retrieve stored signature:', error);
    }
    return null;
  }

  public static clearStoredSignature() {
    try {
      sessionStorage.removeItem('wheel-contract-signature');
      console.log('🗑️ Contract signature cleared from storage');
    } catch (error) {
      console.warn('Failed to clear stored signature:', error);
    }
  }

  private handleCancel() {
    this.dispatchEvent(new CustomEvent('contract-cancelled'));
  }

  private handleAccept() {
    if (!this.hasSignature) return;

    const signature = this.captureSignature();
    if (signature) {
      this.signatureData = signature;
      this.dispatchEvent(new CustomEvent('contract-accepted', {
        detail: { signature }
      }));
    }
  }

  render() {
    return html`
      <div class="modal-container" @click=${(e: Event) => e.stopPropagation()}>
        <div class="modal-header">
          <span class="modal-icon">⚖️</span>
          <h2 class="modal-title">Activity Contract</h2>
          <p class="modal-subtitle">A moment of conscious commitment</p>
        </div>

        <div class="modal-body">
          <div class="contract-text">
            <p><strong>By spinning this wheel, you acknowledge:</strong></p>
            <ul>
              <li>You are consciously choosing to let the system guide your next activity</li>
              <li>You accept the wheel's decision as a fair and meaningful choice</li>
              <li>You commit to engaging with the selected activity with an open mind</li>
              <li>This is a moment where you temporarily transfer decision authority to the wheel</li>
            </ul>
          </div>

          <div class="authority-moment">
            🎯 <strong>This is your moment of conscious choice</strong> 🎯<br>
            <small>Here lies the power: accepting guidance while maintaining your agency</small>
          </div>

          <div class="signature-section">
            <div class="signature-label">
              Please sign below to accept this contract:
            </div>
            
            <div class="signature-canvas-container ${this.hasSignature ? 'has-signature' : ''}">
              <canvas 
                id="signature-canvas"
                width="400" 
                height="150"
              ></canvas>
              <div class="signature-instructions">
                ${this.hasSignature 
                  ? '✓ Signature captured - you may now accept the contract'
                  : 'Draw your signature above using mouse or touch'
                }
              </div>
              
              ${this.hasSignature ? html`
                <div class="signature-actions">
                  <button class="clear-signature-btn" @click=${this.clearSignature}>
                    Clear Signature
                  </button>
                </div>
              ` : ''}
            </div>
          </div>
        </div>

        <div class="modal-actions">
          <button class="action-btn cancel-btn" @click=${this.handleCancel}>
            Cancel
          </button>
          <button 
            class="action-btn accept-btn" 
            ?disabled=${!this.hasSignature}
            @click=${this.handleAccept}
          >
            Accept Contract & Spin
          </button>
        </div>
      </div>
    `;
  }
}

declare global {
  interface HTMLElementTagNameMap {
    'contract-disclaimer-modal': ContractDisclaimerModal;
  }
}
