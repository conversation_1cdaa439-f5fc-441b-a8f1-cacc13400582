/**
 * Real-Time Observability Dashboard
 * 
 * Advanced monitoring dashboard for LangGraph + Celery + Django stack.
 * Provides real-time insights into:
 * - Workflow execution traces
 * - Performance metrics and bottlenecks
 * - Cost tracking for LLM calls
 * - Error rates and anomaly detection
 * - System health indicators
 * 
 * Features:
 * - Live event streaming via WebSocket
 * - Interactive trace visualization
 * - Performance heatmaps
 * - Cost analysis charts
 * - Smart alerting system
 */

import { LitElement, html, css } from 'lit';
import { customElement, property, state } from 'lit/decorators.js';
import { classMap } from 'lit/directives/class-map.js';

interface ObservabilityEvent {
  event_id: string;
  event_type: string;
  timestamp: number;
  trace_id: string;
  span_id: string;
  parent_span_id?: string;
  component: string;
  operation: string;
  duration_ms?: number;
  severity: string;
  metadata: Record<string, any>;
  tags: Record<string, string>;
  metrics: Record<string, number>;
}

interface PerformanceMetrics {
  operation_stats: Record<string, {
    count: number;
    total_time: number;
    avg_time: number;
  }>;
  recent_samples: Array<{
    operation: string;
    duration_ms: number;
    timestamp: number;
  }>;
  total_operations: number;
}

interface DashboardData {
  performance_summary: PerformanceMetrics;
  active_traces: number;
  total_events: number;
  cost_summary: Record<string, number>;
  sampling_rate: number;
  recent_events: ObservabilityEvent[];
}

@customElement('observability-dashboard')
export class ObservabilityDashboard extends LitElement {
  static styles = css`
    :host {
      display: block;
      width: 100%;
      height: 100vh;
      background: var(--color-gray-50, #f9fafb);
      font-family: var(--font-family-base, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
      overflow: hidden;
    }

    .dashboard-container {
      display: grid;
      grid-template-columns: 300px 1fr;
      grid-template-rows: 60px 1fr;
      height: 100vh;
      gap: 1px;
      background: var(--color-gray-200, #e5e7eb);
    }

    .dashboard-header {
      grid-column: 1 / -1;
      background: white;
      padding: var(--spacing-4, 16px);
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid var(--color-gray-200, #e5e7eb);
    }

    .dashboard-title {
      font-size: var(--font-size-xl, 20px);
      font-weight: var(--font-weight-bold, 700);
      color: var(--color-gray-900, #111827);
      margin: 0;
    }

    .dashboard-status {
      display: flex;
      align-items: center;
      gap: var(--spacing-3, 12px);
    }

    .status-indicator {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: var(--color-success, #10b981);
      animation: pulse 2s infinite;
    }

    .status-indicator.warning {
      background: var(--color-warning, #f59e0b);
    }

    .status-indicator.error {
      background: var(--color-error, #ef4444);
    }

    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.5; }
    }

    .sidebar {
      background: white;
      padding: var(--spacing-4, 16px);
      overflow-y: auto;
    }

    .main-content {
      background: white;
      padding: var(--spacing-4, 16px);
      overflow-y: auto;
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-template-rows: auto auto 1fr;
      gap: var(--spacing-4, 16px);
    }

    .metric-card {
      background: white;
      border: 1px solid var(--color-gray-200, #e5e7eb);
      border-radius: var(--radius-lg, 12px);
      padding: var(--spacing-4, 16px);
      box-shadow: var(--shadow-sm, 0 1px 2px 0 rgba(0, 0, 0, 0.05));
    }

    .metric-card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: var(--spacing-3, 12px);
    }

    .metric-card-title {
      font-size: var(--font-size-lg, 18px);
      font-weight: var(--font-weight-semibold, 600);
      color: var(--color-gray-900, #111827);
      margin: 0;
    }

    .metric-card-value {
      font-size: var(--font-size-2xl, 24px);
      font-weight: var(--font-weight-bold, 700);
      color: var(--color-primary, #3b82f6);
      margin: 0;
    }

    .metric-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: var(--spacing-3, 12px);
      margin-bottom: var(--spacing-4, 16px);
    }

    .performance-chart {
      grid-column: 1 / -1;
      height: 300px;
      background: var(--color-gray-50, #f9fafb);
      border-radius: var(--radius-md, 8px);
      padding: var(--spacing-4, 16px);
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--color-gray-500, #6b7280);
    }

    .events-list {
      grid-column: 1 / -1;
      max-height: 400px;
      overflow-y: auto;
    }

    .event-item {
      display: flex;
      align-items: center;
      gap: var(--spacing-3, 12px);
      padding: var(--spacing-2, 8px);
      border-bottom: 1px solid var(--color-gray-100, #f3f4f6);
    }

    .event-item:hover {
      background: var(--color-gray-50, #f9fafb);
    }

    .event-timestamp {
      font-size: var(--font-size-xs, 12px);
      color: var(--color-gray-500, #6b7280);
      min-width: 80px;
    }

    .event-component {
      font-size: var(--font-size-xs, 12px);
      padding: 2px 6px;
      border-radius: 4px;
      font-weight: var(--font-weight-medium, 500);
      min-width: 80px;
      text-align: center;
    }

    .event-component.langgraph {
      background: var(--color-blue-100, #dbeafe);
      color: var(--color-blue-800, #1e40af);
    }

    .event-component.celery {
      background: var(--color-green-100, #dcfce7);
      color: var(--color-green-800, #166534);
    }

    .event-component.django {
      background: var(--color-purple-100, #e9d5ff);
      color: var(--color-purple-800, #6b21a8);
    }

    .event-component.llm {
      background: var(--color-orange-100, #fed7aa);
      color: var(--color-orange-800, #9a3412);
    }

    .event-operation {
      flex: 1;
      font-size: var(--font-size-sm, 14px);
      color: var(--color-gray-900, #111827);
    }

    .event-duration {
      font-size: var(--font-size-xs, 12px);
      color: var(--color-gray-600, #4b5563);
      min-width: 60px;
      text-align: right;
    }

    .event-severity {
      width: 8px;
      height: 8px;
      border-radius: 50%;
    }

    .event-severity.info {
      background: var(--color-blue-500, #3b82f6);
    }

    .event-severity.warning {
      background: var(--color-warning, #f59e0b);
    }

    .event-severity.error {
      background: var(--color-error, #ef4444);
    }

    .sidebar-section {
      margin-bottom: var(--spacing-6, 24px);
    }

    .sidebar-section-title {
      font-size: var(--font-size-sm, 14px);
      font-weight: var(--font-weight-semibold, 600);
      color: var(--color-gray-900, #111827);
      margin: 0 0 var(--spacing-3, 12px) 0;
    }

    .filter-group {
      margin-bottom: var(--spacing-4, 16px);
    }

    .filter-label {
      font-size: var(--font-size-xs, 12px);
      color: var(--color-gray-600, #4b5563);
      margin-bottom: var(--spacing-1, 4px);
      display: block;
    }

    .filter-select {
      width: 100%;
      padding: var(--spacing-2, 8px);
      border: 1px solid var(--color-gray-300, #d1d5db);
      border-radius: var(--radius-md, 8px);
      font-size: var(--font-size-sm, 14px);
    }

    .trace-tree {
      font-family: monospace;
      font-size: var(--font-size-xs, 12px);
      line-height: 1.4;
    }

    .trace-node {
      padding: 2px 0;
      cursor: pointer;
    }

    .trace-node:hover {
      background: var(--color-gray-100, #f3f4f6);
    }

    .trace-indent {
      display: inline-block;
      width: 16px;
    }

    .loading-spinner {
      width: 20px;
      height: 20px;
      border: 2px solid var(--color-gray-200, #e5e7eb);
      border-top: 2px solid var(--color-primary, #3b82f6);
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  `;

  @property({ type: Boolean }) visible = false;
  @property({ type: String }) websocketUrl = '';

  @state() private dashboardData: DashboardData | null = null;
  @state() private recentEvents: ObservabilityEvent[] = [];
  @state() private selectedComponent = 'all';
  @state() private selectedSeverity = 'all';
  @state() private wsConnected = false;
  @state() private connectionError = '';

  private websocket: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  connectedCallback() {
    super.connectedCallback();
    if (this.visible) {
      this.connectWebSocket();
    }
  }

  disconnectedCallback() {
    super.disconnectedCallback();
    this.disconnectWebSocket();
  }

  updated(changedProperties: Map<string, any>) {
    if (changedProperties.has('visible')) {
      if (this.visible) {
        this.connectWebSocket();
      } else {
        this.disconnectWebSocket();
      }
    }
  }

  private connectWebSocket() {
    if (this.websocket) return;

    try {
      const wsUrl = this.websocketUrl || 'ws://localhost:8000/ws/observability/';
      this.websocket = new WebSocket(wsUrl);

      this.websocket.onopen = () => {
        this.wsConnected = true;
        this.connectionError = '';
        this.reconnectAttempts = 0;
        console.log('🔍 Observability dashboard connected');
      };

      this.websocket.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          this.handleWebSocketMessage(data);
        } catch (error) {
          console.error('Error parsing observability message:', error);
        }
      };

      this.websocket.onclose = () => {
        this.wsConnected = false;
        this.websocket = null;
        
        if (this.visible && this.reconnectAttempts < this.maxReconnectAttempts) {
          this.reconnectAttempts++;
          setTimeout(() => this.connectWebSocket(), 2000 * this.reconnectAttempts);
        }
      };

      this.websocket.onerror = (error) => {
        this.connectionError = 'WebSocket connection failed';
        console.error('Observability WebSocket error:', error);
      };

    } catch (error) {
      this.connectionError = 'Failed to connect to observability service';
      console.error('Error connecting to observability WebSocket:', error);
    }
  }

  private disconnectWebSocket() {
    if (this.websocket) {
      this.websocket.close();
      this.websocket = null;
    }
    this.wsConnected = false;
  }

  private handleWebSocketMessage(data: any) {
    switch (data.type) {
      case 'observability_events':
        this.handleObservabilityEvents(data.events);
        break;
      case 'dashboard_data':
        this.dashboardData = data.data;
        break;
      default:
        console.log('Unknown observability message type:', data.type);
    }
  }

  private handleObservabilityEvents(events: ObservabilityEvent[]) {
    // Add new events to the beginning of the list
    this.recentEvents = [...events, ...this.recentEvents].slice(0, 1000);
    this.requestUpdate();
  }

  private formatTimestamp(timestamp: number): string {
    return new Date(timestamp * 1000).toLocaleTimeString();
  }

  private formatDuration(duration?: number): string {
    if (!duration) return '-';
    if (duration < 1000) return `${Math.round(duration)}ms`;
    return `${(duration / 1000).toFixed(1)}s`;
  }

  private getFilteredEvents(): ObservabilityEvent[] {
    return this.recentEvents.filter(event => {
      if (this.selectedComponent !== 'all' && event.component !== this.selectedComponent) {
        return false;
      }
      if (this.selectedSeverity !== 'all' && event.severity !== this.selectedSeverity) {
        return false;
      }
      return true;
    });
  }

  render() {
    if (!this.visible) return html``;

    const filteredEvents = this.getFilteredEvents();
    const systemStatus = this.wsConnected ? 'connected' : 'disconnected';

    return html`
      <div class="dashboard-container">
        <div class="dashboard-header">
          <h1 class="dashboard-title">🔍 Observability Dashboard</h1>
          <div class="dashboard-status">
            <div class="status-indicator ${systemStatus}"></div>
            <span>${this.wsConnected ? 'Connected' : 'Disconnected'}</span>
            ${this.connectionError ? html`<span style="color: var(--color-error);">${this.connectionError}</span>` : ''}
          </div>
        </div>

        <div class="sidebar">
          <div class="sidebar-section">
            <h3 class="sidebar-section-title">Filters</h3>
            
            <div class="filter-group">
              <label class="filter-label">Component</label>
              <select 
                class="filter-select" 
                .value=${this.selectedComponent}
                @change=${(e: Event) => this.selectedComponent = (e.target as HTMLSelectElement).value}
              >
                <option value="all">All Components</option>
                <option value="langgraph">LangGraph</option>
                <option value="celery">Celery</option>
                <option value="django">Django</option>
                <option value="llm">LLM</option>
              </select>
            </div>

            <div class="filter-group">
              <label class="filter-label">Severity</label>
              <select 
                class="filter-select"
                .value=${this.selectedSeverity}
                @change=${(e: Event) => this.selectedSeverity = (e.target as HTMLSelectElement).value}
              >
                <option value="all">All Severities</option>
                <option value="info">Info</option>
                <option value="warning">Warning</option>
                <option value="error">Error</option>
              </select>
            </div>
          </div>

          ${this.dashboardData ? html`
            <div class="sidebar-section">
              <h3 class="sidebar-section-title">System Metrics</h3>
              <div class="metric-card">
                <div class="metric-card-header">
                  <span class="metric-card-title">Active Traces</span>
                </div>
                <p class="metric-card-value">${this.dashboardData.active_traces}</p>
              </div>
              
              <div class="metric-card">
                <div class="metric-card-header">
                  <span class="metric-card-title">Total Events</span>
                </div>
                <p class="metric-card-value">${this.dashboardData.total_events}</p>
              </div>

              <div class="metric-card">
                <div class="metric-card-header">
                  <span class="metric-card-title">Total Cost</span>
                </div>
                <p class="metric-card-value">
                  $${Object.values(this.dashboardData.cost_summary).reduce((a, b) => a + b, 0).toFixed(4)}
                </p>
              </div>
            </div>
          ` : ''}
        </div>

        <div class="main-content">
          <div class="performance-chart">
            📊 Performance Chart (Coming Soon)
          </div>

          <div class="events-list">
            <h3 style="margin-top: 0;">Recent Events (${filteredEvents.length})</h3>
            ${filteredEvents.slice(0, 50).map(event => html`
              <div class="event-item">
                <div class="event-severity ${event.severity}"></div>
                <div class="event-timestamp">${this.formatTimestamp(event.timestamp)}</div>
                <div class="event-component ${event.component}">${event.component}</div>
                <div class="event-operation">${event.operation}</div>
                <div class="event-duration">${this.formatDuration(event.duration_ms)}</div>
              </div>
            `)}
          </div>
        </div>
      </div>
    `;
  }
}
