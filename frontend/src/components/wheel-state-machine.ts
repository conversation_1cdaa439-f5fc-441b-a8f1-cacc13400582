/**
 * Wheel State Machine
 * 
 * Implements a robust State Machine pattern for wheel data management
 * to eliminate confusion and ensure reliability and maintainability.
 * 
 * States:
 * - EMPTY: No wheel data, show unpopulated wheel
 * - LOADING: Wheel generation in progress, show progress bar
 * - POPULATED: Valid wheel data with segments, show interactive wheel
 * - ERROR: Error state, show error message and fallback
 * 
 * This replaces the confusing template logic with clear state transitions.
 */

export enum WheelState {
  EMPTY = 'EMPTY',
  LOADING = 'LOADING', 
  POPULATED = 'POPULATED',
  ERROR = 'ERROR'
}


// Import the authoritative WheelData from business-objects
import type { WheelData as BusinessWheelData } from '../types/business-objects.js';


export interface WheelData {
  segments: WheelSegment[];
  wheelId: string | number;
  createdAt: string;
}


// Use the business WheelData as the primary interface
export type AuthoritativeWheelData = BusinessWheelData;


export interface WheelSegment {
  id: string;
  name: string;
  text?: string;
  description?: string;
  percentage: number;
  color: string;
  centerColor?: string;     // Domain-based color for inner wheel
  extremityColor?: string;  // Distinct color for outer wheel
  listColor?: string;       // List color for UI elements
  backgroundColor?: string; // Background color for UI elements
  activity_tailored_id: string | number;
  domain?: string;
  base_challenge_rating?: number;
  wheel_item_id?: string;
}

export interface WheelStateContext {
  state: WheelState;
  data: WheelData | null;
  error: string | null;
  isLoading: boolean;
  progressPercentage: number;
}

export class WheelStateMachine {
  private context: WheelStateContext;
  private listeners: Set<(context: WheelStateContext) => void> = new Set();

  constructor() {
    this.context = {
      state: WheelState.EMPTY,
      data: null,
      error: null,
      isLoading: false,
      progressPercentage: 0
    };
  }

  // State getters
  get currentState(): WheelState {
    return this.context.state;
  }

  get wheelData(): WheelData | null {
    return this.context.data;
  }

  get isLoading(): boolean {
    return this.context.isLoading;
  }

  get hasError(): boolean {
    return this.context.state === WheelState.ERROR;
  }

  get error(): string | null {
    return this.context.error;
  }

  get isEmpty(): boolean {
    return this.context.state === WheelState.EMPTY;
  }

  get isPopulated(): boolean {
    return this.context.state === WheelState.POPULATED;
  }

  // Template helpers - replace confusing template logic
  get shouldShowPopulatedWheel(): boolean {
    return this.context.state === WheelState.POPULATED &&
           this.context.data !== null &&
           this.context.data.segments.length > 0;
  }

  get shouldShowUnpopulatedWheel(): boolean {
    return this.context.state === WheelState.EMPTY ||
           (this.context.state === WheelState.POPULATED &&
            (!this.context.data || this.context.data.segments.length === 0));
  }

  get shouldShowProgressBar(): boolean {
    return this.context.state === WheelState.LOADING;
  }

  get shouldShowSpinButton(): boolean {
    return this.shouldShowPopulatedWheel;
  }

  get shouldShowGenerateButton(): boolean {
    return this.shouldShowUnpopulatedWheel;
  }

  // State transitions
  startLoading(): void {
    this.transition(WheelState.LOADING, {
      isLoading: true,
      progressPercentage: 0,
      error: null
    });
  }

  updateProgress(percentage: number): void {
    if (this.context.state === WheelState.LOADING) {
      this.updateContext({ progressPercentage: Math.min(100, Math.max(0, percentage)) });
    }
  }

  setWheelData(data: WheelData): void {
    console.log('🔄 State Machine: setWheelData called with:', data);
    console.log('🔄 State Machine: Current state:', this.context.state);
    console.log('🔄 State Machine: Current data:', this.context.data);

    // Validate data structure
    if (!this.isValidWheelData(data)) {
      console.error('🚨 State Machine: Invalid wheel data structure:', data);
      this.setError('Invalid wheel data structure received');
      return;
    }

    console.log('✅ State Machine: Wheel data validation passed');

    // Normalize data to ensure consistency
    const normalizedData = this.normalizeWheelData(data);
    console.log('🔄 State Machine: Normalized data:', normalizedData);

    // ARCHITECTURAL FIX: Only transition if data has actually changed
    if (this.hasWheelDataChanged(this.context.data, normalizedData)) {
      console.log('🔄 State Machine: Wheel data changed, transitioning to POPULATED');
      console.log('🔄 State Machine: Previous data:', this.context.data);
      console.log('🔄 State Machine: New data:', normalizedData);

      this.transition(WheelState.POPULATED, {
        data: normalizedData,
        isLoading: false,
        progressPercentage: 100,
        error: null
      });

      console.log('✅ State Machine: Transition to POPULATED completed');
    } else {
      console.log('⏭️ State Machine: Wheel data unchanged, skipping transition');
    }
  }

  clearWheel(): void {
    console.log('🗑️ State Machine: clearWheel called');
    console.log('🗑️ State Machine: Current state before clear:', this.context.state);
    console.log('🗑️ State Machine: Current data before clear:', this.context.data);

    this.transition(WheelState.EMPTY, {
      data: null,
      isLoading: false,
      progressPercentage: 0,
      error: null
    });

    console.log('✅ State Machine: Wheel cleared, new state:', this.context.state);
  }

  setError(error: string): void {
    console.error('🚨 State Machine: setError called with:', error);
    console.error('🚨 State Machine: Current state before error:', this.context.state);
    console.error('🚨 State Machine: Current data before error:', this.context.data);

    this.transition(WheelState.ERROR, {
      error,
      isLoading: false,
      progressPercentage: 0
    });

    console.error('🚨 State Machine: Error state set, new state:', this.context.state);
  }

  // Data validation and normalization
  private hasWheelDataChanged(previousData: WheelData | null, currentData: WheelData): boolean {
    // If no previous data, this is a new wheel
    if (!previousData) {
      return true;
    }

    // Quick checks for obvious differences
    if (previousData.wheelId !== currentData.wheelId) {
      return true;
    }

    if (previousData.segments.length !== currentData.segments.length) {
      return true;
    }

    // Deep comparison of segments (only check essential properties)
    for (let i = 0; i < currentData.segments.length; i++) {
      const prev = previousData.segments[i];
      const curr = currentData.segments[i];

      if (!prev || !curr) {
        return true;
      }

      // Check properties that affect wheel state
      if (prev.id !== curr.id ||
          prev.percentage !== curr.percentage ||
          prev.color !== curr.color ||
          prev.name !== curr.name) {
        return true;
      }
    }

    // No significant changes detected
    return false;
  }

  private isValidWheelData(data: any): data is WheelData {
    if (!data || typeof data !== 'object') {
      console.error('🚨 Invalid wheel data: not an object', data);
      return false;
    }

    if (!Array.isArray(data.segments)) {
      console.error('🚨 Invalid wheel data: segments is not an array', data);
      return false;
    }

    if (!data.wheelId) {
      console.error('🚨 Invalid wheel data: missing wheelId', data);
      return false;
    }

    // Validate each segment
    for (const segment of data.segments) {
      if (!this.isValidSegment(segment)) {
        return false;
      }
    }

    return true;
  }

  private isValidSegment(segment: any): segment is WheelSegment {
    if (!segment || typeof segment !== 'object') {
      console.error('🚨 Invalid segment: not an object', segment);
      return false;
    }

    const required = ['id', 'name', 'percentage', 'color'];
    for (const field of required) {
      if (!(field in segment)) {
        console.error(`🚨 Invalid segment: missing ${field}`, segment);
        return false;
      }
    }

    if (typeof segment.percentage !== 'number' || segment.percentage <= 0) {
      console.error('🚨 Invalid segment: invalid percentage', segment);
      return false;
    }

    return true;
  }

  private normalizeWheelData(data: WheelData): WheelData {
    return {
      ...data,
      wheelId: String(data.wheelId), // Ensure string format
      segments: data.segments.map(segment => ({
        ...segment, // CRITICAL FIX: Preserve all properties including centerColor, extremityColor
        text: segment.text || segment.name, // Ensure text field
        wheel_item_id: segment.wheel_item_id || segment.id, // Ensure wheel_item_id
        activity_tailored_id: String(segment.activity_tailored_id) // Ensure string format
      }))
    };
  }

  // Event system
  subscribe(listener: (context: WheelStateContext) => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  private transition(newState: WheelState, updates: Partial<WheelStateContext> = {}): void {
    const previousState = this.context.state;
    
    this.context = {
      ...this.context,
      state: newState,
      ...updates
    };

    console.log(`🔄 Wheel State: ${previousState} → ${newState}`, this.context);
    
    // Notify listeners
    this.listeners.forEach(listener => {
      try {
        listener(this.context);
      } catch (error) {
        console.error('🚨 Error in wheel state listener:', error);
      }
    });
  }

  private updateContext(updates: Partial<WheelStateContext>): void {
    this.context = { ...this.context, ...updates };
    
    // Notify listeners
    this.listeners.forEach(listener => {
      try {
        listener(this.context);
      } catch (error) {
        console.error('🚨 Error in wheel state listener:', error);
      }
    });
  }

  // Debug helpers
  getDebugInfo(): string {
    return JSON.stringify({
      state: this.context.state,
      hasData: !!this.context.data,
      segmentCount: this.context.data?.segments.length || 0,
      isLoading: this.context.isLoading,
      error: this.context.error,
      progressPercentage: this.context.progressPercentage
    }, null, 2);
  }
}
