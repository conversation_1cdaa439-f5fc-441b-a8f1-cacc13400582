/**
 * Debug Panel Component
 * Provides debugging tools and configuration options for development
 */

import { LitElement, html, css } from 'lit';
import { customElement, state, property } from 'lit/decorators.js';
import { ConfigService } from '../../services/config-service.js';
import type { UserProfile, LLMConfig } from '../../types/app-types.js';

@customElement('debug-panel')
export class DebugPanel extends LitElement {
  static styles = css`
    :host {
      position: fixed;
      /* FIXED: Remove initial positioning to allow drag positioning */
      width: 320px;
      background: rgba(0, 0, 0, 0.9);
      color: white;
      border-radius: 8px;
      padding: 16px;
      font-family: 'Monaco', 'Menlo', monospace;
      font-size: 12px;
      z-index: 10000;
      max-height: 80vh;
      overflow-y: auto;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      user-select: none;
    }

    :host(.hidden) {
      display: none !important;
    }

    :host(.dragging) {
      opacity: 0.8;
      transform: scale(0.98);
      transition: none;
    }

    .debug-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.2);
      cursor: move; /* Make header draggable */
    }

    .debug-title {
      font-weight: bold;
      color: #00ff00;
    }

    .close-btn {
      background: none;
      border: none;
      color: white;
      cursor: pointer;
      font-size: 16px;
      padding: 4px;
    }

    .debug-section {
      margin-bottom: 16px;
    }

    .section-title {
      font-weight: bold;
      color: #ffff00;
      margin-bottom: 8px;
      font-size: 11px;
      text-transform: uppercase;
    }

    .form-group {
      margin-bottom: 12px;
    }

    .form-label {
      display: block;
      margin-bottom: 4px;
      color: #ccc;
      font-size: 10px;
    }

    .form-input, .form-select {
      width: 100%;
      padding: 6px;
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 4px;
      color: white;
      font-size: 11px;
    }

    .form-input:focus, .form-select:focus {
      outline: none;
      border-color: #00ff00;
    }

    .checkbox-group {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 8px;
    }

    .checkbox {
      width: 14px;
      height: 14px;
    }

    .btn {
      background: #007acc;
      border: none;
      color: white;
      padding: 6px 12px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 10px;
      margin-right: 8px;
      margin-bottom: 4px;
    }

    .btn:hover {
      background: #005a9e;
    }

    .btn-danger {
      background: #dc3545;
    }

    .btn-danger:hover {
      background: #c82333;
    }

    .status-indicator {
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 8px;
    }

    .status-connected {
      background: #00ff00;
    }

    .status-disconnected {
      background: #ff0000;
    }

    .status-demo {
      background: #ffaa00;
    }

    .metrics {
      font-size: 10px;
      line-height: 1.4;
    }

    .hidden {
      display: none;
    }
  `;

  @property({ type: Boolean }) visible = false;
  @state() private users: UserProfile[] = [];
  @state() private llmConfigs: LLMConfig[] = [];
  @state() private selectedUserId = '';
  @state() private selectedLLMConfigId = '';
  @state() private backendUrl = '';
  @state() private connectionStatus = 'disconnected';
  @state() private errors: string[] = [];
  @state() private lastError = '';
  @state() private isLoading = false;
  @state() private isCreatingUser = false;
  @state() private currentUserDetails: any = null;
  @state() private websocketMessages: Array<{timestamp: string, direction: 'in' | 'out', type: string, data: any}> = [];
  @state() private connectionDetails: any = {};
  @state() private performanceMetrics: any = {};
  @state() private showRawMessages = false;
  @state() private connectionHealth: any = {};
  @state() private lastPingTime: number = 0;
  @state() private responseTimeHistory: number[] = [];
  @state() private showPerformanceMetrics = false;
  @state() private maxMessageHistory = 50;
  @state() private showOnboardingDebug = false;
  @state() private onboardingMetrics: any = {};
  @state() private conversationState: any = {};
  @state() private profileCompletionHistory: Array<{timestamp: string, completion: number}> = [];
  @state() private responseTimeThreshold = 10000; // 10 seconds
  @state() private slowResponseAlerts: Array<{timestamp: string, duration: number, message: string}> = [];
  @state() private backendLogs: Array<{timestamp: string, level: string, message: string, source: string}> = [];
  @state() private showBackendLogs = false;
  @state() private maxLogHistory = 100;

  // Drag functionality
  @state() private isDragging = false;
  @state() private dragOffset = { x: 0, y: 0 };
  @state() private position = { x: 10, y: 50 }; // Initial position (50px from top, 10px from left)

  private configService = ConfigService.getInstance();
  private messageLogInterval: number | null = null;
  private onboardingMonitorInterval: number | null = null;

  connectedCallback() {
    super.connectedCallback();
    this.loadInitialData();
    this.setupEventListeners();
    this.setupDragListeners();
    this.startPerformanceMonitoring();
    this.startOnboardingMonitoring();
    this.loadPosition();
  }

  disconnectedCallback() {
    super.disconnectedCallback();
    this.removeEventListeners();
    this.removeDragListeners();
    this.stopPerformanceMonitoring();
    this.stopOnboardingMonitoring();
  }

  private removeDragListeners() {
    // The mousedown listener is attached to the header in the template
    document.removeEventListener('mousemove', this.handleMouseMove);
    document.removeEventListener('mouseup', this.handleMouseUp);
  }

  private async loadInitialData() {
    const config = this.configService.getConfig();
    this.backendUrl = config.websocket.url;

    // Load saved selections from localStorage
    this.selectedUserId = this.loadFromStorage('debug_selected_user_id') || (import.meta as any).env?.VITE_USER_PROFILE_ID || '2';
    this.selectedLLMConfigId = this.loadFromStorage('debug_selected_llm_config_id') || '';
    this.backendUrl = this.loadFromStorage('debug_backend_url') || config.websocket.url;

    if (config.debug.allowUserSelection || this.isStaffUser()) {
      await this.loadUsers();
      // Load user details if a user is already selected
      if (this.selectedUserId) {
        await this.loadUserDetails();
      }
    }

    if (config.debug.allowLLMConfigSelection) {
      await this.loadLLMConfigs();
    }
  }

  private async loadUsers() {
    this.isLoading = true;
    this.clearError();

    try {
      const baseUrl = this.getApiBaseUrl();
      console.log('Loading users from:', `${baseUrl}/api/debug/users/`);

      const response = await fetch(`${baseUrl}/api/debug/users/`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        credentials: 'include', // Include cookies for CORS
      });

      console.log('Users API response:', response.status, response.statusText);

      if (response.ok) {
        const data = await response.json();
        this.users = data;
        console.log('Loaded users:', this.users.length);
      } else {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const errorMsg = `Failed to load users: ${errorMessage}`;
      console.error(errorMsg, error);
      this.addError(errorMsg);

      // Try to get more detailed error via WebSocket
      this.requestDetailedError('load_users_failed', errorMessage);
    } finally {
      this.isLoading = false;
    }
  }

  private async loadLLMConfigs() {
    this.isLoading = true;
    this.clearError();

    try {
      const baseUrl = this.getApiBaseUrl();
      console.log('Loading LLM configs from:', `${baseUrl}/api/debug/llm-configs/`);

      const response = await fetch(`${baseUrl}/api/debug/llm-configs/`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        credentials: 'include', // Include cookies for CORS
      });

      console.log('LLM configs API response:', response.status, response.statusText);

      if (response.ok) {
        const data = await response.json();
        this.llmConfigs = data;
        console.log('Loaded LLM configs:', this.llmConfigs.length);
      } else {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const errorMsg = `Failed to load LLM configs: ${errorMessage}`;
      console.error(errorMsg, error);
      this.addError(errorMsg);

      // Try to get more detailed error via WebSocket
      this.requestDetailedError('load_llm_configs_failed', errorMessage);
    } finally {
      this.isLoading = false;
    }
  }

  private setupEventListeners() {
    window.addEventListener('keydown', this.handleKeydown);
    window.addEventListener('connection-status-changed', this.handleConnectionChange);
    window.addEventListener('websocket-error', this.handleWebSocketError);
    window.addEventListener('websocket-message', this.handleWebSocketMessage);
    window.addEventListener('websocket-send', this.handleWebSocketSend);
    window.addEventListener('websocket-state-change', this.handleWebSocketStateChange);
    window.addEventListener('performance-update', this.handlePerformanceUpdate);
  }

  private removeEventListeners() {
    window.removeEventListener('keydown', this.handleKeydown);
    window.removeEventListener('connection-status-changed', this.handleConnectionChange);
    window.removeEventListener('websocket-error', this.handleWebSocketError);
    window.removeEventListener('websocket-message', this.handleWebSocketMessage);
    window.removeEventListener('websocket-send', this.handleWebSocketSend);
    window.removeEventListener('websocket-state-change', this.handleWebSocketStateChange);
    window.removeEventListener('performance-update', this.handlePerformanceUpdate);
  }

  private handleKeydown = (event: KeyboardEvent) => {
    // Toggle debug panel with Ctrl+Shift+D
    if (event.ctrlKey && event.shiftKey && event.key === 'D') {
      event.preventDefault();
      this.visible = !this.visible;
    }
  };

  private handleConnectionChange = (event: any) => {
    this.connectionStatus = event.detail.isConnected ? 'connected' : 'disconnected';

    // Update connection health metrics
    this.connectionHealth = {
      ...this.connectionHealth,
      isConnected: event.detail.isConnected,
      lastConnectionChange: new Date().toISOString(),
      connectionQuality: this.calculateConnectionQuality()
    };

    // Reset ping time if disconnected
    if (!event.detail.isConnected) {
      this.lastPingTime = 0;
      this.responseTimeHistory = [];
    }
  };

  private handleWebSocketError = (event: any) => {
    const errorMsg = `WebSocket Error: ${event.detail?.message || 'Unknown error'}`;
    this.addError(errorMsg);
  };

  private handleWebSocketMessage = (event: any) => {
    try {
      const message = event.detail;

      // Log the message for debugging
      this.logWebSocketMessage('in', message);

      // Track response times for ping/pong messages
      if (message.type === 'pong' && this.lastPingTime > 0) {
        const responseTime = Date.now() - this.lastPingTime;
        this.responseTimeHistory.push(responseTime);
        if (this.responseTimeHistory.length > 10) {
          this.responseTimeHistory = this.responseTimeHistory.slice(-10);
        }
        this.lastPingTime = 0;
      }

      // Monitor onboarding-specific messages
      this.monitorOnboardingMessages(message);

      // Check for error messages from backend
      if (message.type === 'error' || message.type === 'debug_error_response') {
        const errorContent = message.content || message.error || 'Unknown error';
        this.addError(`Backend Error: ${errorContent}`);
      }

      // Check for detailed error responses to our debug requests
      if (message.type === 'debug_error_response' && message.error_type) {
        const detailedError = `${message.error_type}: ${message.details || message.error || 'No details available'}`;
        this.addError(detailedError);
      }

      // Monitor backend logs
      if (message.type === 'debug_info' || message.type === 'backend_log') {
        this.logBackendMessage(message);
      }
    } catch (error) {
      console.warn('Error handling WebSocket message in debug panel:', error);
    }
  };

  private handleWebSocketSend = (event: any) => {
    // Log outgoing WebSocket messages
    console.log('Debug Panel: WebSocket message sent', event.detail);
    this.logWebSocketMessage('out', event.detail);
  };

  private handleWebSocketStateChange = (event: any) => {
    // Update connection details
    this.connectionDetails = {
      ...this.connectionDetails,
      ...event.detail,
      lastStateChange: new Date().toISOString()
    };
    this.requestUpdate();
  };

  private handlePerformanceUpdate = (event: any) => {
    // Update performance metrics
    this.performanceMetrics = {
      ...this.performanceMetrics,
      ...event.detail,
      lastUpdate: new Date().toISOString()
    };
    this.requestUpdate();
  };

  private handleClose() {
    this.visible = false;
  }

  private handleUserChange(event: Event) {
    const target = event.target as HTMLSelectElement;
    this.selectedUserId = target.value;

    // Save to localStorage
    this.saveToStorage('debug_selected_user_id', this.selectedUserId);

    // Load user details
    this.loadUserDetails();

    this.dispatchEvent(new CustomEvent('user-changed', {
      detail: { userId: this.selectedUserId }
    }));
  }

  private handleLLMConfigChange(event: Event) {
    const target = event.target as HTMLSelectElement;
    this.selectedLLMConfigId = target.value;

    // Save to localStorage
    this.saveToStorage('debug_selected_llm_config_id', this.selectedLLMConfigId);

    this.dispatchEvent(new CustomEvent('llm-config-changed', {
      detail: { configId: this.selectedLLMConfigId }
    }));
  }

  private handleBackendUrlChange(event: Event) {
    const target = event.target as HTMLInputElement;
    this.backendUrl = target.value;

    // Save to localStorage
    this.saveToStorage('debug_backend_url', this.backendUrl);
  }

  private applyBackendUrl() {
    this.configService.updateWebSocketUrl(this.backendUrl);
    this.dispatchEvent(new CustomEvent('backend-url-changed', {
      detail: { url: this.backendUrl }
    }));
  }

  private clearStorage() {
    localStorage.clear();
    sessionStorage.clear();
    location.reload();
  }

  private loadMockedWheelItems() {
    // Dispatch event to app-shell to load mocked wheel items
    this.dispatchEvent(new CustomEvent('load-mocked-wheel', {
      detail: {
        action: 'load_mocked_items',
        timestamp: new Date().toISOString()
      },
      bubbles: true,
      composed: true
    }));
  }

  private async createNewUser() {
    this.isCreatingUser = true;
    this.clearError();

    try {
      const baseUrl = this.getApiBaseUrl();
      console.log('Creating new user at:', `${baseUrl}/api/debug/users/`);

      const response = await fetch(`${baseUrl}/api/debug/users/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          profile_type: 'german_student'
        })
      });

      console.log('Create user API response:', response.status, response.statusText);

      if (response.ok) {
        const data = await response.json();
        console.log('Created new user:', data);

        // Add the new user to the list
        this.users = [...this.users, data.user];

        // Select the new user
        this.selectedUserId = data.user.id;
        this.saveToStorage('debug_selected_user_id', this.selectedUserId);

        // Load user details
        await this.loadUserDetails();

        // Dispatch user change event
        this.dispatchEvent(new CustomEvent('user-changed', {
          detail: { userId: this.selectedUserId }
        }));

        console.log(`✅ Created and selected new user: ${data.user.name}`);
      } else {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }
    } catch (error) {
      console.error('Failed to create new user:', error);
      this.addError(`Failed to create new user: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      this.isCreatingUser = false;
    }
  }

  private async loadUserDetails() {
    if (!this.selectedUserId) {
      this.currentUserDetails = null;
      return;
    }

    try {
      const baseUrl = this.getApiBaseUrl();
      const response = await fetch(`${baseUrl}/api/debug/users/${this.selectedUserId}/`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        this.currentUserDetails = data;
        console.log('Loaded user details:', data);
      } else {
        console.warn('Failed to load user details:', response.status);
        this.currentUserDetails = null;
      }
    } catch (error) {
      console.warn('Error loading user details:', error);
      this.currentUserDetails = null;
    }
  }

  private getApiBaseUrl(): string {
    // Get the base URL for API calls, handling different configurations
    const config = this.configService.getConfig();
    let baseUrl = config.websocket.url;

    // Convert WebSocket URL to HTTP URL
    if (baseUrl.startsWith('ws://')) {
      baseUrl = baseUrl.replace('ws://', 'http://');
    } else if (baseUrl.startsWith('wss://')) {
      baseUrl = baseUrl.replace('wss://', 'https://');
    }

    // Remove WebSocket path
    baseUrl = baseUrl.replace('/ws/game/', '');

    return baseUrl;
  }

  private addError(error: string) {
    this.errors.push(error);
    this.lastError = error;

    // Keep only last 5 errors
    if (this.errors.length > 5) {
      this.errors = this.errors.slice(-5);
    }
  }

  private clearError() {
    this.lastError = '';
  }

  private clearAllErrors() {
    this.errors = [];
    this.lastError = '';
  }

  private requestDetailedError(errorType: string, basicError: string) {
    // Request detailed error information via WebSocket
    try {
      const message = {
        type: 'debug_error_request',
        error_type: errorType,
        basic_error: basicError,
        timestamp: new Date().toISOString()
      };

      // Try to send via WebSocket if available
      if (window.WebSocket && (window as any).__GOALI_WS__) {
        (window as any).__GOALI_WS__.send(JSON.stringify(message));
      }
    } catch (error) {
      console.warn('Could not request detailed error via WebSocket:', error);
    }
  }

  private saveToStorage(key: string, value: string) {
    try {
      localStorage.setItem(key, value);
    } catch (error) {
      console.warn(`Failed to save ${key} to localStorage:`, error);
    }
  }

  private isStaffUser(): boolean {
    try {
      // Check if current user is staff by looking at the app shell
      const appShell = document.querySelector('app-shell') as any;
      return appShell?.currentUser?.isStaff || false;
    } catch (e) {
      return false;
    }
  }

  private loadFromStorage(key: string): string | null {
    try {
      return localStorage.getItem(key);
    } catch (error) {
      console.warn(`Failed to load ${key} from localStorage:`, error);
      return null;
    }
  }

  private logWebSocketMessage(direction: 'in' | 'out', data: any) {
    const timestamp = new Date().toISOString();
    const messageType = data?.type || 'unknown';

    // Add to message history
    this.websocketMessages.unshift({
      timestamp,
      direction,
      type: messageType,
      data: data
    });

    // Keep only the last N messages
    if (this.websocketMessages.length > this.maxMessageHistory) {
      this.websocketMessages = this.websocketMessages.slice(0, this.maxMessageHistory);
    }

    this.requestUpdate();
  }

  private startPerformanceMonitoring() {
    // Start monitoring performance metrics
    this.messageLogInterval = window.setInterval(() => {
      this.updatePerformanceMetrics();
    }, 2000); // Update every 2 seconds
  }

  private stopPerformanceMonitoring() {
    if (this.messageLogInterval) {
      clearInterval(this.messageLogInterval);
      this.messageLogInterval = null;
    }
  }

  private updatePerformanceMetrics() {
    const now = performance.now();
    const memory = (performance as any).memory;

    this.performanceMetrics = {
      timestamp: new Date().toISOString(),
      uptime: Math.round(now / 1000),
      memory: memory ? {
        used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
        total: Math.round(memory.totalJSHeapSize / 1024 / 1024),
        limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024)
      } : null,
      messageCount: this.websocketMessages.length,
      connectionStatus: this.connectionStatus,
      lastUpdate: new Date().toISOString()
    };
  }

  private toggleRawMessages() {
    this.showRawMessages = !this.showRawMessages;
  }

  private togglePerformanceMetrics() {
    this.showPerformanceMetrics = !this.showPerformanceMetrics;
  }

  private calculateConnectionQuality(): string {
    if (this.connectionStatus !== 'connected') return 'poor';

    if (this.responseTimeHistory.length === 0) return 'unknown';

    const avgResponseTime = this.responseTimeHistory.reduce((a, b) => a + b, 0) / this.responseTimeHistory.length;

    if (avgResponseTime < 100) return 'excellent';
    if (avgResponseTime < 300) return 'good';
    if (avgResponseTime < 1000) return 'fair';
    return 'poor';
  }

  private sendPing() {
    if (this.connectionStatus === 'connected' && (window as any).__GOALI_WS__) {
      this.lastPingTime = Date.now();
      try {
        (window as any).__GOALI_WS__.send(JSON.stringify({ type: 'ping', timestamp: this.lastPingTime }));
      } catch (error) {
        console.warn('Failed to send ping:', error);
        this.lastPingTime = 0;
      }
    }
  }

  private getConnectionQualityColor(quality: string): string {
    switch (quality) {
      case 'excellent': return '#28a745';
      case 'good': return '#17a2b8';
      case 'fair': return '#ffc107';
      case 'poor': return '#dc3545';
      default: return '#6c757d';
    }
  }

  private clearMessageHistory() {
    this.websocketMessages = [];
    this.requestUpdate();
  }

  private startOnboardingMonitoring() {
    this.onboardingMonitorInterval = window.setInterval(() => {
      this.checkOnboardingHealth();
    }, 2000); // Check every 2 seconds
  }

  private stopOnboardingMonitoring() {
    if (this.onboardingMonitorInterval) {
      clearInterval(this.onboardingMonitorInterval);
      this.onboardingMonitorInterval = null;
    }
  }

  private checkOnboardingHealth() {
    // Check for hanging responses
    const now = Date.now();
    const recentMessages = this.websocketMessages.filter(msg =>
      now - new Date(msg.timestamp).getTime() < 30000 // Last 30 seconds
    );

    // Look for user messages without responses
    const userMessages = recentMessages.filter(msg =>
      msg.direction === 'out' && msg.type === 'chat_message'
    );

    const assistantResponses = recentMessages.filter(msg =>
      msg.direction === 'in' && (msg.type === 'chat_message' || msg.type === 'processing_status')
    );

    if (userMessages.length > assistantResponses.length) {
      const lastUserMessage = userMessages[userMessages.length - 1];
      const timeSinceLastMessage = now - new Date(lastUserMessage.timestamp).getTime();

      if (timeSinceLastMessage > this.responseTimeThreshold) {
        this.addSlowResponseAlert(timeSinceLastMessage, 'Potential hanging detected - no response to user message');
      }
    }
  }

  private monitorOnboardingMessages(message: any) {
    // Track conversation state updates
    if (message.type === 'conversation_state_update') {
      this.conversationState = {
        ...this.conversationState,
        ...message.updates,
        lastUpdate: new Date().toISOString()
      };
    }

    // Track profile completion changes
    if (message.type === 'profile_completion_update' ||
        (message.type === 'debug_info' && message.details?.profile_completion !== undefined)) {
      const completion = message.details?.profile_completion || message.completion || 0;
      this.profileCompletionHistory.push({
        timestamp: new Date().toISOString(),
        completion: completion
      });

      // Keep only last 20 entries
      if (this.profileCompletionHistory.length > 20) {
        this.profileCompletionHistory = this.profileCompletionHistory.slice(-20);
      }
    }

    // Track workflow execution times
    if (message.type === 'workflow_started') {
      this.onboardingMetrics = {
        ...this.onboardingMetrics,
        lastWorkflowStart: new Date().toISOString(),
        workflowType: message.workflow_type
      };
    }

    if (message.type === 'workflow_completed') {
      const startTime = this.onboardingMetrics.lastWorkflowStart;
      if (startTime) {
        const duration = Date.now() - new Date(startTime).getTime();
        this.onboardingMetrics = {
          ...this.onboardingMetrics,
          lastWorkflowDuration: duration,
          lastWorkflowCompleted: new Date().toISOString()
        };
      }
    }
  }

  private logBackendMessage(message: any) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      level: message.level || 'info',
      message: message.message || message.content || JSON.stringify(message),
      source: message.source || 'backend'
    };

    this.backendLogs.push(logEntry);

    // Keep only recent logs
    if (this.backendLogs.length > this.maxLogHistory) {
      this.backendLogs = this.backendLogs.slice(-this.maxLogHistory);
    }
  }

  private addSlowResponseAlert(duration: number, message: string) {
    this.slowResponseAlerts.push({
      timestamp: new Date().toISOString(),
      duration: duration,
      message: message
    });

    // Keep only last 10 alerts
    if (this.slowResponseAlerts.length > 10) {
      this.slowResponseAlerts = this.slowResponseAlerts.slice(-10);
    }
  }

  private toggleOnboardingDebug() {
    this.showOnboardingDebug = !this.showOnboardingDebug;
  }

  private toggleBackendLogs() {
    this.showBackendLogs = !this.showBackendLogs;
  }

  // Drag functionality methods
  private setupDragListeners() {
    // Only listen for mousedown on the header for dragging
    document.addEventListener('mousemove', this.handleMouseMove);
    document.addEventListener('mouseup', this.handleMouseUp);
  }

  private handleMouseDown = (e: MouseEvent) => {
    console.log('🖱️ Debug panel drag started');
    this.isDragging = true;
    this.classList.add('dragging');

    const rect = this.getBoundingClientRect();
    this.dragOffset = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    };

    console.log('📍 Initial drag offset:', this.dragOffset);
    e.preventDefault();
  };

  private handleMouseMove = (e: MouseEvent) => {
    if (!this.isDragging) return;

    const newX = e.clientX - this.dragOffset.x;
    const newY = e.clientY - this.dragOffset.y;

    // Keep panel within viewport bounds
    const maxX = window.innerWidth - this.offsetWidth;
    const maxY = window.innerHeight - this.offsetHeight;

    this.position = {
      x: Math.max(0, Math.min(newX, maxX)),
      y: Math.max(0, Math.min(newY, maxY))
    };

    console.log('🖱️ Moving to:', this.position);

    this.style.left = `${this.position.x}px`;
    this.style.top = `${this.position.y}px`;
    this.style.right = 'auto'; // Override the CSS right positioning

    this.savePosition();
  };

  private handleMouseUp = () => {
    this.isDragging = false;
    this.classList.remove('dragging');
  };

  private loadPosition() {
    const savedPosition = this.loadFromStorage('debug_panel_position');
    if (savedPosition) {
      try {
        this.position = JSON.parse(savedPosition);
      } catch (e) {
        console.warn('Failed to load debug panel position:', e);
        // Use default position if parsing fails
        this.position = { x: 10, y: 50 };
      }
    }

    // Always set the position styles
    this.style.left = `${this.position.x}px`;
    this.style.top = `${this.position.y}px`;
    this.style.right = 'auto';
    this.style.bottom = 'auto';
  }

  private savePosition() {
    this.saveToStorage('debug_panel_position', JSON.stringify(this.position));
  }

  render() {
    // Allow debug panel for debug mode OR staff users
    const allowDebugPanel = this.configService.isDebugMode() || this.isStaffUser();

    // Add/remove hidden class based on visibility
    if (!this.visible || !allowDebugPanel) {
      this.classList.add('hidden');
      return html``;
    } else {
      this.classList.remove('hidden');
    }

    const config = this.configService.getConfig();

    return html`
      <div class="debug-panel">
        <div class="debug-header" @mousedown=${this.handleMouseDown}>
          <span class="debug-title">🐛 DEBUG PANEL</span>
          <button class="close-btn" @click=${this.handleClose} @mousedown=${(e: Event) => e.stopPropagation()}>×</button>
        </div>

        <!-- Enhanced Connection Status -->
        <div class="debug-section">
          <div class="section-title">
            🔗 Connection Status
            ${this.connectionStatus === 'connected' ? html`
              <button class="btn" style="font-size: 9px; margin-left: 8px;" @click=${this.sendPing}>
                📡 Ping
              </button>
            ` : ''}
          </div>
          <div style="font-size: 11px;">
            <div style="margin-bottom: 4px;">
              <span class="status-indicator status-${this.connectionStatus}"></span>
              <strong>${this.connectionStatus.toUpperCase()}</strong>
              ${this.isLoading ? html`<span style="margin-left: 8px;">⏳ Loading...</span>` : ''}
              ${this.connectionHealth.connectionQuality ? html`
                <span style="margin-left: 8px; color: ${this.getConnectionQualityColor(this.connectionHealth.connectionQuality)};">
                  ● ${this.connectionHealth.connectionQuality}
                </span>
              ` : ''}
            </div>
            ${this.connectionDetails.readyState !== undefined ? html`
              <div><strong>Ready State:</strong> ${this.connectionDetails.readyState}</div>
            ` : ''}
            ${this.connectionDetails.url ? html`
              <div><strong>URL:</strong> ${this.connectionDetails.url}</div>
            ` : ''}
            ${this.connectionDetails.lastStateChange ? html`
              <div><strong>Last Change:</strong> ${new Date(this.connectionDetails.lastStateChange).toLocaleTimeString()}</div>
            ` : ''}
            ${this.responseTimeHistory.length > 0 ? html`
              <div><strong>Avg Response:</strong> ${Math.round(this.responseTimeHistory.reduce((a, b) => a + b, 0) / this.responseTimeHistory.length)}ms</div>
            ` : ''}
            ${this.websocketMessages.length > 0 ? html`
              <div><strong>Messages:</strong> ${this.websocketMessages.length} logged</div>
            ` : ''}
          </div>
        </div>

        <!-- Error Display -->
        ${this.lastError ? html`
          <div class="debug-section">
            <div class="section-title" style="color: #ff6b6b;">Latest Error</div>
            <div style="background: rgba(255, 107, 107, 0.1); padding: 8px; border-radius: 4px; font-size: 10px; line-height: 1.3; color: #ff6b6b; margin-bottom: 8px;">
              ${this.lastError}
            </div>
            <button class="btn" style="font-size: 9px; padding: 3px 6px;" @click=${this.clearAllErrors}>Clear Errors</button>
          </div>
        ` : ''}

        <!-- All Errors (if multiple) -->
        ${this.errors.length > 1 ? html`
          <div class="debug-section">
            <div class="section-title" style="color: #ffaa00;">Error History (${this.errors.length})</div>
            <div style="max-height: 100px; overflow-y: auto; font-size: 9px;">
              ${this.errors.map((error, index) => html`
                <div style="padding: 2px 0; border-bottom: 1px solid rgba(255,255,255,0.1);">
                  ${index + 1}. ${error}
                </div>
              `)}
            </div>
          </div>
        ` : ''}

        <!-- User Selection -->
        ${(config.debug.allowUserSelection || this.isStaffUser()) ? html`
          <div class="debug-section">
            <div class="section-title">User Selection</div>
            <div class="form-group">
              <label class="form-label">Select User:</label>
              <select class="form-select" .value=${this.selectedUserId} @change=${this.handleUserChange} ?disabled=${this.isLoading || this.isCreatingUser}>
                <option value="">Select a user...</option>
                ${this.users.map(user => html`
                  <option value=${user.id}>
                    ${user.name} (ID: ${user.id}) ${user.is_fake ? '🤖' : '👤'}
                  </option>
                `)}
              </select>
              <div style="display: flex; gap: 4px; margin-top: 4px;">
                ${this.users.length === 0 && !this.isLoading ? html`
                  <button class="btn" style="font-size: 10px;" @click=${this.loadUsers}>
                    🔄 Reload Users
                  </button>
                ` : ''}
                <button
                  class="btn"
                  style="font-size: 10px; background: #4CAF50; color: white;"
                  @click=${this.createNewUser}
                  ?disabled=${this.isCreatingUser}
                >
                  ${this.isCreatingUser ? '⏳ Creating...' : '👤 New German Student'}
                </button>
              </div>
            </div>

            <!-- Current User Details -->
            ${this.selectedUserId && this.currentUserDetails ? html`
              <div class="user-details" style="margin-top: 8px; padding: 8px; background: #f5f5f5; border-radius: 4px; font-size: 11px;">
                <div style="font-weight: bold; margin-bottom: 4px;">📋 Current User Details</div>
                <div><strong>Name:</strong> ${this.currentUserDetails.profile_name || 'N/A'}</div>
                <div><strong>ID:</strong> ${this.currentUserDetails.id}</div>
                <div><strong>Profile Completion:</strong>
                  <span style="color: ${this.currentUserDetails.profile_completion_percentage >= 50 ? '#28a745' : this.currentUserDetails.profile_completion_percentage >= 25 ? '#ffc107' : '#dc3545'};">
                    ${this.currentUserDetails.profile_completion_percentage || 0}%
                  </span>
                </div>
                <div><strong>Is Real User:</strong> ${this.currentUserDetails.is_real ? '👤 Yes' : '🤖 Test'}</div>
                ${this.currentUserDetails.demographics ? html`
                  <div style="margin-top: 4px; border-top: 1px solid #ddd; padding-top: 4px;">
                    <div style="font-weight: bold; margin-bottom: 2px;">👤 Demographics</div>
                    <div><strong>Age:</strong> ${this.currentUserDetails.demographics.age || 'N/A'}</div>
                    <div><strong>Gender:</strong> ${this.currentUserDetails.demographics.gender || 'N/A'}</div>
                    <div><strong>Location:</strong> ${this.currentUserDetails.demographics.location || 'N/A'}</div>
                    <div><strong>Occupation:</strong> ${this.currentUserDetails.demographics.occupation || 'N/A'}</div>
                    ${this.currentUserDetails.demographics.language ? html`
                      <div><strong>Language:</strong> ${this.currentUserDetails.demographics.language}</div>
                    ` : ''}
                  </div>
                ` : html`
                  <div style="margin-top: 4px; color: #dc3545;">⚠️ No demographics data</div>
                `}
                ${this.currentUserDetails.preferences && this.currentUserDetails.preferences.length > 0 ? html`
                  <div style="margin-top: 4px; border-top: 1px solid #ddd; padding-top: 4px;">
                    <div style="font-weight: bold; margin-bottom: 2px;">⚙️ Preferences (${this.currentUserDetails.preferences.length})</div>
                    ${this.currentUserDetails.preferences.slice(0, 3).map((pref: any) => html`
                      <div style="font-size: 10px;">• ${pref.preference_type}: ${pref.content}</div>
                    `)}
                    ${this.currentUserDetails.preferences.length > 3 ? html`
                      <div style="font-size: 10px; color: #666;">... and ${this.currentUserDetails.preferences.length - 3} more</div>
                    ` : ''}
                  </div>
                ` : html`
                  <div style="margin-top: 4px; color: #ffc107;">⚠️ No preferences set</div>
                `}
                ${this.currentUserDetails.goals && this.currentUserDetails.goals.length > 0 ? html`
                  <div style="margin-top: 4px; border-top: 1px solid #ddd; padding-top: 4px;">
                    <div style="font-weight: bold; margin-bottom: 2px;">🎯 Goals (${this.currentUserDetails.goals.length})</div>
                    ${this.currentUserDetails.goals.slice(0, 2).map((goal: any) => html`
                      <div style="font-size: 10px;">• ${goal.goal_type}: ${goal.content}</div>
                    `)}
                  </div>
                ` : html`
                  <div style="margin-top: 4px; color: #ffc107;">⚠️ No goals set</div>
                `}
              </div>
            ` : this.selectedUserId ? html`
              <div style="margin-top: 8px; padding: 8px; background: #fff3cd; border-radius: 4px; font-size: 11px;">
                ⏳ Loading user details...
              </div>
            ` : ''}
          </div>
        ` : ''}

        <!-- LLM Configuration -->
        ${config.debug.allowLLMConfigSelection ? html`
          <div class="debug-section">
            <div class="section-title">LLM Configuration</div>
            <div class="form-group">
              <label class="form-label">Select LLM Config:</label>
              <select class="form-select" .value=${this.selectedLLMConfigId} @change=${this.handleLLMConfigChange} ?disabled=${this.isLoading}>
                <option value="">Select config...</option>
                ${this.llmConfigs.map(config => html`
                  <option value=${config.id}>
                    ${config.name} (${config.model_name}) ${config.is_default ? '⭐' : ''}
                  </option>
                `)}
              </select>
              ${this.llmConfigs.length === 0 && !this.isLoading ? html`
                <button class="btn" style="margin-top: 4px; font-size: 10px;" @click=${this.loadLLMConfigs}>
                  🔄 Reload LLM Configs
                </button>
              ` : ''}
            </div>
          </div>
        ` : ''}

        <!-- Backend URL -->
        ${config.debug.allowBackendUrlChange ? html`
          <div class="debug-section">
            <div class="section-title">Backend Configuration</div>
            <div class="form-group">
              <label class="form-label">WebSocket URL:</label>
              <input 
                type="text" 
                class="form-input" 
                .value=${this.backendUrl}
                @input=${this.handleBackendUrlChange}
                placeholder="ws://localhost:8000/ws/game/"
              />
              <button class="btn" @click=${this.applyBackendUrl}>Apply</button>
            </div>
          </div>
        ` : ''}

        <!-- WebSocket Message Log -->
        <div class="debug-section">
          <div class="section-title">
            📡 WebSocket Messages
            <button class="btn" style="font-size: 9px; margin-left: 8px;" @click=${this.toggleRawMessages}>
              ${this.showRawMessages ? 'Hide' : 'Show'} Raw
            </button>
            <button class="btn" style="font-size: 9px; margin-left: 4px;" @click=${this.clearMessageHistory}>
              Clear
            </button>
          </div>
          ${this.showRawMessages && this.websocketMessages.length > 0 ? html`
            <div style="max-height: 200px; overflow-y: auto; font-size: 9px; background: #f8f9fa; border-radius: 4px; padding: 8px;">
              ${this.websocketMessages.map(msg => html`
                <div style="margin-bottom: 8px; padding: 4px; border-left: 3px solid ${msg.direction === 'in' ? '#28a745' : '#007bff'}; background: white;">
                  <div style="font-weight: bold; color: ${msg.direction === 'in' ? '#28a745' : '#007bff'};">
                    ${msg.direction === 'in' ? '⬇️ IN' : '⬆️ OUT'} ${msg.type}
                    <span style="color: #666; font-weight: normal;">${new Date(msg.timestamp).toLocaleTimeString()}</span>
                  </div>
                  <div style="margin-top: 2px; font-family: monospace; white-space: pre-wrap; max-height: 100px; overflow-y: auto;">
                    ${JSON.stringify(msg.data, null, 2)}
                  </div>
                </div>
              `)}
            </div>
          ` : html`
            <div style="font-size: 11px; color: #666;">
              ${this.websocketMessages.length === 0 ? 'No messages logged yet' : `${this.websocketMessages.length} messages logged (click Show Raw to view)`}
            </div>
          `}
        </div>

        <!-- Onboarding Debug -->
        <div class="debug-section">
          <div class="section-title">
            🎯 Onboarding Monitor
            <button class="btn" style="font-size: 9px; margin-left: 8px;" @click=${this.toggleOnboardingDebug}>
              ${this.showOnboardingDebug ? 'Hide' : 'Show'} Details
            </button>
          </div>
          ${this.showOnboardingDebug ? html`
            <div style="font-size: 11px;">
              <!-- Conversation State -->
              ${this.conversationState.phase ? html`
                <div style="margin-bottom: 8px; padding: 6px; background: rgba(0,255,0,0.1); border-radius: 4px;">
                  <div><strong>Phase:</strong> ${this.conversationState.phase}</div>
                  <div><strong>Awaiting:</strong> ${this.conversationState.awaiting_response_type || 'None'}</div>
                  ${this.conversationState.lastUpdate ? html`
                    <div><strong>Last Update:</strong> ${new Date(this.conversationState.lastUpdate).toLocaleTimeString()}</div>
                  ` : ''}
                </div>
              ` : ''}

              <!-- Profile Completion History -->
              ${this.profileCompletionHistory.length > 0 ? html`
                <div style="margin-bottom: 8px;">
                  <div style="font-weight: bold; margin-bottom: 4px;">📊 Profile Completion</div>
                  <div style="max-height: 80px; overflow-y: auto;">
                    ${this.profileCompletionHistory.slice(-5).map(entry => html`
                      <div style="font-size: 10px; padding: 2px 0;">
                        ${new Date(entry.timestamp).toLocaleTimeString()}: ${(entry.completion * 100).toFixed(1)}%
                      </div>
                    `)}
                  </div>
                </div>
              ` : ''}

              <!-- Slow Response Alerts -->
              ${this.slowResponseAlerts.length > 0 ? html`
                <div style="margin-bottom: 8px;">
                  <div style="font-weight: bold; margin-bottom: 4px; color: #ff6b6b;">⚠️ Slow Response Alerts</div>
                  <div style="max-height: 80px; overflow-y: auto;">
                    ${this.slowResponseAlerts.slice(-3).map(alert => html`
                      <div style="font-size: 10px; padding: 2px 0; color: #ff6b6b;">
                        ${new Date(alert.timestamp).toLocaleTimeString()}: ${(alert.duration/1000).toFixed(1)}s - ${alert.message}
                      </div>
                    `)}
                  </div>
                </div>
              ` : ''}

              <!-- Workflow Metrics -->
              ${this.onboardingMetrics.workflowType ? html`
                <div style="margin-bottom: 8px;">
                  <div style="font-weight: bold; margin-bottom: 4px;">🔄 Workflow Status</div>
                  <div><strong>Type:</strong> ${this.onboardingMetrics.workflowType}</div>
                  ${this.onboardingMetrics.lastWorkflowDuration ? html`
                    <div><strong>Duration:</strong> ${(this.onboardingMetrics.lastWorkflowDuration/1000).toFixed(1)}s</div>
                  ` : ''}
                  ${this.onboardingMetrics.lastWorkflowCompleted ? html`
                    <div><strong>Completed:</strong> ${new Date(this.onboardingMetrics.lastWorkflowCompleted).toLocaleTimeString()}</div>
                  ` : ''}
                </div>
              ` : ''}
            </div>
          ` : html`
            <div style="font-size: 11px; color: #666;">
              Monitoring onboarding flow for hanging issues...
              ${this.slowResponseAlerts.length > 0 ? html`
                <span style="color: #ff6b6b; margin-left: 8px;">⚠️ ${this.slowResponseAlerts.length} alerts</span>
              ` : ''}
            </div>
          `}
        </div>

        <!-- Backend Logs -->
        <div class="debug-section">
          <div class="section-title">
            📋 Backend Logs
            <button class="btn" style="font-size: 9px; margin-left: 8px;" @click=${this.toggleBackendLogs}>
              ${this.showBackendLogs ? 'Hide' : 'Show'} Logs
            </button>
          </div>
          ${this.showBackendLogs && this.backendLogs.length > 0 ? html`
            <div style="max-height: 150px; overflow-y: auto; font-size: 9px; background: #f8f9fa; border-radius: 4px; padding: 8px;">
              ${this.backendLogs.slice(-20).map(log => html`
                <div style="margin-bottom: 4px; padding: 2px; border-left: 3px solid ${log.level === 'error' ? '#dc3545' : log.level === 'warning' ? '#ffc107' : '#28a745'};">
                  <span style="color: #666;">${new Date(log.timestamp).toLocaleTimeString()}</span>
                  <span style="color: ${log.level === 'error' ? '#dc3545' : log.level === 'warning' ? '#ffc107' : '#666'}; font-weight: bold; margin-left: 4px;">[${log.level.toUpperCase()}]</span>
                  <span style="margin-left: 4px;">${log.message}</span>
                </div>
              `)}
            </div>
          ` : html`
            <div style="font-size: 11px; color: #666;">
              ${this.backendLogs.length === 0 ? 'No backend logs received' : `${this.backendLogs.length} logs (click Show Logs to view)`}
            </div>
          `}
        </div>

        <!-- Performance Metrics -->
        <div class="debug-section">
          <div class="section-title">
            ⚡ Performance
            <button class="btn" style="font-size: 9px; margin-left: 8px;" @click=${this.togglePerformanceMetrics}>
              ${this.showPerformanceMetrics ? 'Hide' : 'Show'} Details
            </button>
          </div>
          ${this.showPerformanceMetrics && this.performanceMetrics.timestamp ? html`
            <div style="font-size: 11px;">
              <div><strong>Uptime:</strong> ${this.performanceMetrics.uptime}s</div>
              ${this.performanceMetrics.memory ? html`
                <div><strong>Memory:</strong> ${this.performanceMetrics.memory.used}MB / ${this.performanceMetrics.memory.total}MB</div>
                <div><strong>Memory Limit:</strong> ${this.performanceMetrics.memory.limit}MB</div>
              ` : ''}
              <div><strong>Messages Logged:</strong> ${this.performanceMetrics.messageCount}</div>
              <div><strong>Last Update:</strong> ${new Date(this.performanceMetrics.lastUpdate).toLocaleTimeString()}</div>
            </div>
          ` : html`
            <div style="font-size: 11px; color: #666;">
              Click "Show Details" to view performance metrics
            </div>
          `}
        </div>

        <!-- Actions -->
        <div class="debug-section">
          <div class="section-title">🔧 Actions</div>
          <div style="display: flex; flex-wrap: wrap; gap: 4px;">
            <button class="btn" style="background: #28a745;" @click=${this.loadMockedWheelItems}>🎡 Load Mocked Items</button>
            <button class="btn btn-danger" @click=${this.clearStorage}>Clear Storage</button>
            <button class="btn" @click=${this.clearMessageHistory}>Clear Messages</button>
            <button class="btn" @click=${this.clearAllErrors}>Clear Errors</button>
          </div>
        </div>
      </div>
    `;
  }
}

declare global {
  interface HTMLElementTagNameMap {
    'debug-panel': DebugPanel;
  }
}
