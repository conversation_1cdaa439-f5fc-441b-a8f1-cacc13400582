/**
 * Error banner component for displaying temporary errors at the top of the application
 * Used for connection issues and other transient failures
 */

import { LitElement, html, css } from 'lit';
import { customElement, property, state } from 'lit/decorators.js';
import { AppError, ErrorLevel, ErrorType } from '../types/error-types.js';
import { ErrorClassificationService } from '../services/error-classification.js';

@customElement('error-banner')
export class ErrorBanner extends LitElement {
  @property({ type: Object })
  error: AppError | null = null;

  @property({ type: Boolean })
  isDebugMode = false;

  @property({ type: Boolean })
  dismissible = true;

  @property({ type: Boolean })
  showRetry = false;

  @state()
  private isVisible = false;

  @state()
  private isRetrying = false;

  static styles = css`
    :host {
      display: block;
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 10000;
      transform: translateY(-100%);
      transition: transform 0.3s ease-in-out;
    }

    :host([visible]) {
      transform: translateY(0);
    }

    .banner {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 16px;
      font-size: 14px;
      font-weight: 500;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .banner.temporary {
      background-color: #fff3cd;
      border-bottom: 2px solid #ffc107;
      color: #856404;
    }

    .banner.connection-error {
      background-color: #f8d7da;
      border-bottom: 2px solid #dc3545;
      color: #721c24;
    }

    .banner.websocket-error {
      background-color: #d1ecf1;
      border-bottom: 2px solid #17a2b8;
      color: #0c5460;
    }

    .banner-content {
      display: flex;
      align-items: center;
      gap: 8px;
      flex: 1;
    }

    .banner-icon {
      font-size: 16px;
    }

    .banner-message {
      flex: 1;
    }

    .banner-actions {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .retry-button {
      background: none;
      border: 1px solid currentColor;
      color: inherit;
      padding: 4px 12px;
      border-radius: 4px;
      font-size: 12px;
      cursor: pointer;
      transition: background-color 0.2s;
    }

    .retry-button:hover:not(:disabled) {
      background-color: rgba(0, 0, 0, 0.1);
    }

    .retry-button:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    .dismiss-button {
      background: none;
      border: none;
      color: inherit;
      padding: 4px;
      cursor: pointer;
      font-size: 16px;
      line-height: 1;
      opacity: 0.7;
      transition: opacity 0.2s;
    }

    .dismiss-button:hover {
      opacity: 1;
    }

    .spinner {
      display: inline-block;
      width: 12px;
      height: 12px;
      border: 2px solid currentColor;
      border-radius: 50%;
      border-top-color: transparent;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      to {
        transform: rotate(360deg);
      }
    }

    .debug-info {
      font-size: 11px;
      opacity: 0.8;
      margin-top: 4px;
      font-family: monospace;
    }
  `;

  connectedCallback() {
    super.connectedCallback();
    this.updateVisibility();
  }

  updated(changedProperties: Map<string | number | symbol, unknown>) {
    if (changedProperties.has('error')) {
      this.updateVisibility();
    }
  }

  private updateVisibility() {
    this.isVisible = this.error !== null;
    this.toggleAttribute('visible', this.isVisible);
  }

  private getBannerClass(): string {
    if (!this.error) return 'temporary';

    switch (this.error.type) {
      case ErrorType.CONNECTION_ERROR:
        return 'connection-error';
      case ErrorType.WEBSOCKET_ERROR:
        return 'websocket-error';
      default:
        return 'temporary';
    }
  }

  private getBannerIcon(): string {
    if (!this.error) return '⚠️';

    switch (this.error.type) {
      case ErrorType.CONNECTION_ERROR:
        return '🔌';
      case ErrorType.WEBSOCKET_ERROR:
        return '📡';
      case ErrorType.TIMEOUT_ERROR:
        return '⏱️';
      default:
        return '⚠️';
    }
  }

  private getMessage(): string {
    if (!this.error) return '';
    return ErrorClassificationService.getUserMessage(this.error, this.isDebugMode);
  }

  private handleDismiss() {
    this.dispatchEvent(new CustomEvent('error-dismissed', {
      detail: { error: this.error },
      bubbles: true
    }));
  }

  private async handleRetry() {
    if (!this.error || this.isRetrying) return;

    this.isRetrying = true;
    
    try {
      this.dispatchEvent(new CustomEvent('error-retry', {
        detail: { error: this.error },
        bubbles: true
      }));
    } finally {
      // Reset retry state after a delay
      setTimeout(() => {
        this.isRetrying = false;
      }, 2000);
    }
  }

  private shouldShowRetry(): boolean {
    if (!this.error || !this.showRetry) return false;
    return ErrorClassificationService.canAutoRetry(this.error);
  }

  render() {
    if (!this.error) return html``;

    const bannerClass = this.getBannerClass();
    const icon = this.getBannerIcon();
    const message = this.getMessage();
    const showRetry = this.shouldShowRetry();

    return html`
      <div class="banner ${bannerClass}">
        <div class="banner-content">
          <span class="banner-icon">${icon}</span>
          <div class="banner-message">
            ${message}
            ${this.isDebugMode ? html`
              <div class="debug-info">
                ${this.error.type} | ${this.error.component || 'unknown'} | ${this.error.id}
              </div>
            ` : ''}
          </div>
        </div>
        
        <div class="banner-actions">
          ${showRetry ? html`
            <button 
              class="retry-button" 
              @click=${this.handleRetry}
              ?disabled=${this.isRetrying}
            >
              ${this.isRetrying ? html`
                <span class="spinner"></span> Retrying...
              ` : 'Retry'}
            </button>
          ` : ''}
          
          ${this.dismissible ? html`
            <button 
              class="dismiss-button" 
              @click=${this.handleDismiss}
              title="Dismiss"
            >
              ✕
            </button>
          ` : ''}
        </div>
      </div>
    `;
  }
}
