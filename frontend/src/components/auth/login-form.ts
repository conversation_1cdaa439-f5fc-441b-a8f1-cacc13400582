/**
 * Login Form Component
 * Handles user authentication for production mode
 */

import { LitElement, html, css } from 'lit';
import { customElement, state } from 'lit/decorators.js';
import { AuthService } from '../../services/auth-service.js';

@customElement('login-form')
export class LoginForm extends LitElement {
  static styles = css`
    :host {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      background: var(--gradient-primary);
      padding: var(--spacing-4);
    }

    .login-container {
      background: rgba(255, 255, 255, 0.95);
      border-radius: var(--radius-2xl);
      padding: var(--spacing-8);
      box-shadow: var(--shadow-xl);
      backdrop-filter: blur(10px);
      width: 100%;
      max-width: 400px;
    }

    .login-header {
      text-align: center;
      margin-bottom: var(--spacing-8);
    }

    .logo {
      font-size: 3rem;
      margin-bottom: var(--spacing-4);
    }

    .title {
      font-size: var(--font-size-2xl);
      font-weight: var(--font-weight-bold);
      color: var(--color-gray-900);
      margin-bottom: var(--spacing-2);
    }

    .subtitle {
      color: var(--color-gray-600);
      font-size: var(--font-size-sm);
    }

    .form-group {
      margin-bottom: var(--spacing-6);
    }

    .form-label {
      display: block;
      font-weight: var(--font-weight-medium);
      color: var(--color-gray-700);
      margin-bottom: var(--spacing-2);
      font-size: var(--font-size-sm);
    }

    .form-input {
      width: 100%;
      padding: var(--spacing-3);
      border: 2px solid var(--color-gray-300);
      border-radius: var(--radius-lg);
      font-size: var(--font-size-base);
      transition: border-color var(--transition-fast);
      box-sizing: border-box;
    }

    .form-input:focus {
      outline: none;
      border-color: var(--color-primary);
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .form-input.error {
      border-color: var(--color-error);
    }

    .login-button {
      width: 100%;
      background: var(--color-primary);
      color: white;
      border: none;
      padding: var(--spacing-4);
      border-radius: var(--radius-lg);
      font-size: var(--font-size-base);
      font-weight: var(--font-weight-medium);
      cursor: pointer;
      transition: background-color var(--transition-fast);
      margin-bottom: var(--spacing-4);
    }

    .login-button:hover:not(:disabled) {
      background: var(--color-primary-dark);
    }

    .login-button:disabled {
      background: var(--color-gray-400);
      cursor: not-allowed;
    }

    .error-message {
      background: var(--color-error-light);
      color: var(--color-error-dark);
      padding: var(--spacing-3);
      border-radius: var(--radius-lg);
      font-size: var(--font-size-sm);
      margin-bottom: var(--spacing-4);
      border: 1px solid var(--color-error);
    }

    .loading-spinner {
      display: inline-block;
      width: 16px;
      height: 16px;
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-top: 2px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: var(--spacing-2);
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .demo-notice {
      background: var(--color-warning-light);
      color: var(--color-warning-dark);
      padding: var(--spacing-3);
      border-radius: var(--radius-lg);
      font-size: var(--font-size-sm);
      text-align: center;
      margin-top: var(--spacing-4);
      border: 1px solid var(--color-warning);
    }

    .demo-button {
      background: var(--color-warning);
      color: white;
      border: none;
      padding: var(--spacing-2) var(--spacing-4);
      border-radius: var(--radius-md);
      font-size: var(--font-size-sm);
      cursor: pointer;
      margin-top: var(--spacing-2);
    }

    .demo-button:hover {
      background: var(--color-warning-dark);
    }

    .beta-notice {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: var(--spacing-4);
      border-radius: var(--radius-lg);
      text-align: center;
      margin-top: var(--spacing-4);
    }

    .beta-notice h3 {
      margin: 0 0 var(--spacing-2) 0;
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-bold);
    }

    .beta-notice p {
      margin: 0 0 var(--spacing-3) 0;
      opacity: 0.9;
      line-height: 1.5;
    }

    .beta-signup-button {
      background: rgba(255, 255, 255, 0.2);
      color: white;
      border: 1px solid rgba(255, 255, 255, 0.3);
      padding: var(--spacing-2) var(--spacing-4);
      border-radius: var(--radius-md);
      font-size: var(--font-size-sm);
      cursor: pointer;
      transition: all 0.2s ease;
      margin-top: var(--spacing-2);
    }

    .beta-signup-button:hover {
      background: rgba(255, 255, 255, 0.3);
      border-color: rgba(255, 255, 255, 0.5);
    }

    .beta-form {
      margin-top: var(--spacing-4);
      padding: var(--spacing-4);
      background: rgba(255, 255, 255, 0.1);
      border-radius: var(--radius-lg);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .beta-form .form-group {
      margin-bottom: var(--spacing-4);
    }

    .beta-form .form-label {
      color: white;
      font-weight: var(--font-weight-medium);
    }

    .beta-form .form-input,
    .beta-form .form-textarea {
      background: rgba(255, 255, 255, 0.9);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: var(--color-gray-900);
    }

    .beta-form .form-input:focus,
    .beta-form .form-textarea:focus {
      background: white;
      border-color: var(--color-primary);
    }

    .form-textarea {
      width: 100%;
      padding: var(--spacing-3);
      border: 2px solid var(--color-gray-300);
      border-radius: var(--radius-lg);
      font-size: var(--font-size-base);
      transition: border-color var(--transition-fast);
      box-sizing: border-box;
      resize: vertical;
      min-height: 80px;
      font-family: inherit;
    }

    .beta-actions {
      display: flex;
      gap: var(--spacing-3);
      justify-content: flex-end;
    }

    .beta-submit-button {
      background: rgba(255, 255, 255, 0.9);
      color: #667eea;
      border: none;
      padding: var(--spacing-3) var(--spacing-4);
      border-radius: var(--radius-lg);
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .beta-submit-button:hover:not(:disabled) {
      background: white;
      transform: translateY(-1px);
    }

    .beta-submit-button:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    .beta-cancel-button {
      background: transparent;
      color: rgba(255, 255, 255, 0.8);
      border: 1px solid rgba(255, 255, 255, 0.3);
      padding: var(--spacing-3) var(--spacing-4);
      border-radius: var(--radius-lg);
      font-size: var(--font-size-sm);
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .beta-cancel-button:hover {
      background: rgba(255, 255, 255, 0.1);
      border-color: rgba(255, 255, 255, 0.5);
    }

    .success-message {
      background: var(--color-success-light);
      color: var(--color-success-dark);
      padding: var(--spacing-3);
      border-radius: var(--radius-lg);
      font-size: var(--font-size-sm);
      margin-bottom: var(--spacing-4);
      border: 1px solid var(--color-success);
      text-align: center;
    }

    .beta-success {
      background: rgba(76, 175, 80, 0.2);
      color: white;
      padding: var(--spacing-4);
      border-radius: var(--radius-lg);
      text-align: center;
      border: 1px solid rgba(76, 175, 80, 0.4);
    }

    .beta-success h3 {
      margin: 0 0 var(--spacing-2) 0;
      color: #4CAF50;
    }

    /* Mobile responsive */
    @media (max-width: 480px) {
      .login-container {
        padding: var(--spacing-6);
        margin: var(--spacing-4);
      }

      .beta-actions {
        flex-direction: column;
      }
    }
  `;

  @state() private username = '';
  @state() private password = '';
  @state() private isLoading = false;
  @state() private error = '';
  @state() private showBetaSignup = false;
  @state() private betaEmail = '';
  @state() private betaMessage = '';
  @state() private betaLoading = false;
  @state() private betaError = '';
  @state() private betaSuccess = false;

  // Property to hide demo mode when needed
  hideDemoMode = false;

  private authService = AuthService.getInstance();

  private handleUsernameChange(event: Event) {
    const target = event.target as HTMLInputElement;
    this.username = target.value;
    this.error = ''; // Clear error when user types
  }

  private handlePasswordChange(event: Event) {
    const target = event.target as HTMLInputElement;
    this.password = target.value;
    this.error = ''; // Clear error when user types
  }

  private async handleSubmit(event: Event) {
    event.preventDefault();
    
    if (!this.username.trim() || !this.password.trim()) {
      this.error = 'Please enter both username and password';
      return;
    }

    this.isLoading = true;
    this.error = '';

    try {
      const success = await this.authService.authenticate(this.username.trim(), this.password);
      
      if (success) {
        // Authentication successful - the auth service will emit an event
        // that the app shell will listen to
        this.dispatchEvent(new CustomEvent('login-success', {
          bubbles: true,
          composed: true
        }));
      } else {
        this.error = 'Invalid username or password. Please try again.';
      }
    } catch (error) {
      console.error('Login error:', error);
      this.error = 'Login failed. Please check your connection and try again.';
    } finally {
      this.isLoading = false;
    }
  }

  private handleDemoMode() {
    // Switch to demo mode by dispatching an event
    this.dispatchEvent(new CustomEvent('demo-mode-requested', {
      bubbles: true,
      composed: true
    }));
  }

  private handleKeyPress(event: KeyboardEvent) {
    if (event.key === 'Enter' && !this.isLoading) {
      this.handleSubmit(event);
    }
  }

  private handleBetaEmailChange(event: Event) {
    const target = event.target as HTMLInputElement;
    this.betaEmail = target.value;
    this.betaError = ''; // Clear error when user types
  }

  private handleBetaMessageChange(event: Event) {
    const target = event.target as HTMLTextAreaElement;
    this.betaMessage = target.value;
  }

  private showBetaSignupForm() {
    this.showBetaSignup = true;
    this.betaError = '';
    this.betaSuccess = false;
  }

  private hideBetaSignupForm() {
    this.showBetaSignup = false;
    this.betaEmail = '';
    this.betaMessage = '';
    this.betaError = '';
    this.betaSuccess = false;
  }

  private async handleBetaSubmit(event: Event) {
    event.preventDefault();

    if (!this.betaEmail.trim()) {
      this.betaError = 'Please enter your email address';
      return;
    }

    // Basic email validation
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailPattern.test(this.betaEmail.trim())) {
      this.betaError = 'Please enter a valid email address';
      return;
    }

    this.betaLoading = true;
    this.betaError = '';

    try {
      const response = await fetch('/api/auth/beta-signup/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: this.betaEmail.trim(),
          message: this.betaMessage.trim()
        }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        this.betaSuccess = true;
        this.betaEmail = '';
        this.betaMessage = '';
      } else {
        this.betaError = data.error || 'Failed to submit beta signup. Please try again.';
      }
    } catch (error) {
      console.error('Beta signup error:', error);
      this.betaError = 'Network error. Please check your connection and try again.';
    } finally {
      this.betaLoading = false;
    }
  }

  render() {
    return html`
      <div class="login-container">
        <div class="login-header">
          <div class="logo">🎯</div>
          <h1 class="title">Welcome to Goali</h1>
          <p class="subtitle">Sign in to access your personalized activity wheel</p>
        </div>

        <form @submit=${this.handleSubmit}>
          ${this.error ? html`
            <div class="error-message">
              ${this.error}
            </div>
          ` : ''}

          <div class="form-group">
            <label class="form-label" for="username">Username</label>
            <input
              type="text"
              id="username"
              class="form-input ${this.error ? 'error' : ''}"
              .value=${this.username}
              @input=${this.handleUsernameChange}
              @keypress=${this.handleKeyPress}
              ?disabled=${this.isLoading}
              autocomplete="username"
              required
            />
          </div>

          <div class="form-group">
            <label class="form-label" for="password">Password</label>
            <input
              type="password"
              id="password"
              class="form-input ${this.error ? 'error' : ''}"
              .value=${this.password}
              @input=${this.handlePasswordChange}
              @keypress=${this.handleKeyPress}
              ?disabled=${this.isLoading}
              autocomplete="current-password"
              required
            />
          </div>

          <button
            type="submit"
            class="login-button"
            ?disabled=${this.isLoading}
          >
            ${this.isLoading ? html`
              <span class="loading-spinner"></span>
              Signing in...
            ` : 'Sign In'}
          </button>
        </form>

        ${!this.hideDemoMode ? html`
          <div class="demo-notice">
            <p>Want to try Goali without signing up?</p>
            <button class="demo-button" @click=${this.handleDemoMode}>
              Try Demo Mode
            </button>
          </div>
        ` : ''}

        <!-- Beta Signup Section -->
        <div class="beta-notice">
          <h3>🚀 Closed Beta</h3>
          <p>Goali is currently in closed beta. We're not accepting new registrations at this time, but we'd love to hear from you!</p>

          ${this.betaSuccess ? html`
            <div class="beta-success">
              <h3>✅ Thank You!</h3>
              <p>We've received your interest and will contact you when beta access becomes available.</p>
            </div>
          ` : html`
            ${!this.showBetaSignup ? html`
              <button class="beta-signup-button" @click=${this.showBetaSignupForm}>
                Join the Waitlist
              </button>
            ` : html`
              <form class="beta-form" @submit=${this.handleBetaSubmit}>
                ${this.betaError ? html`
                  <div class="error-message">
                    ${this.betaError}
                  </div>
                ` : ''}

                <div class="form-group">
                  <label class="form-label" for="beta-email">Email Address *</label>
                  <input
                    type="email"
                    id="beta-email"
                    class="form-input"
                    .value=${this.betaEmail}
                    @input=${this.handleBetaEmailChange}
                    ?disabled=${this.betaLoading}
                    placeholder="<EMAIL>"
                    required
                  />
                </div>

                <div class="form-group">
                  <label class="form-label" for="beta-message">Tell us about your interest (optional)</label>
                  <textarea
                    id="beta-message"
                    class="form-textarea"
                    .value=${this.betaMessage}
                    @input=${this.handleBetaMessageChange}
                    ?disabled=${this.betaLoading}
                    placeholder="What interests you about Goali? What would you like to achieve?"
                    rows="3"
                  ></textarea>
                </div>

                <div class="beta-actions">
                  <button
                    type="button"
                    class="beta-cancel-button"
                    @click=${this.hideBetaSignupForm}
                    ?disabled=${this.betaLoading}
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    class="beta-submit-button"
                    ?disabled=${this.betaLoading}
                  >
                    ${this.betaLoading ? html`
                      <span class="loading-spinner"></span>
                      Submitting...
                    ` : 'Join Waitlist'}
                  </button>
                </div>
              </form>
            `}
          `}
        </div>
      </div>
    `;
  }
}

declare global {
  interface HTMLElementTagNameMap {
    'login-form': LoginForm;
  }
}
