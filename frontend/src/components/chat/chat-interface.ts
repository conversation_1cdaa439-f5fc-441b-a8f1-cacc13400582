/**
 * Chat interface component for user-AI communication
 * Handles message display, input, and WebSocket integration
 */

import { LitElement, html, css, PropertyValues } from 'lit';
import { customElement, property, state, query } from 'lit/decorators.js';
import { repeat } from 'lit/directives/repeat.js';

import './message-bubble.js';
import type { ChatMessage, MessageType } from './message-bubble.js';

/**
 * Chat interface web component
 */
@customElement('chat-interface')
export class ChatInterface extends LitElement {
  static styles = css`
    :host {
      display: flex;
      flex-direction: column;
      height: 100%;
      background: #f8f9fa;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .chat-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 16px;
      text-align: center;
      font-weight: bold;
      font-size: 18px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .chat-status {
      background: rgba(255, 255, 255, 0.1);
      padding: 4px 12px;
      font-size: 12px;
      text-align: center;
      opacity: 0.9;
    }

    .chat-status.connected {
      background: rgba(76, 175, 80, 0.2);
    }

    .chat-status.disconnected {
      background: rgba(244, 67, 54, 0.2);
    }

    .chat-status.connecting {
      background: rgba(255, 193, 7, 0.2);
    }

        .messages-container {
      flex: 1;
      overflow-y: auto;
      padding: 16px;
      scroll-behavior: smooth;
      max-height: 60vh; /* Prevent container from growing indefinitely */
      min-height: 300px; /* Ensure minimum usable height */
    }

    .messages-list {
      display: flex;
      flex-direction: column;
      gap: 8px;
      min-height: 100%;
      /* Ensure messages don't cause container to grow */
      overflow-wrap: break-word;
      word-wrap: break-word;
    }

    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      color: #666;
      text-align: center;
      padding: 32px;
    }

    .empty-state-icon {
      font-size: 48px;
      margin-bottom: 16px;
      opacity: 0.5;
    }

    .empty-state-text {
      font-size: 16px;
      margin-bottom: 8px;
    }

    .empty-state-subtext {
      font-size: 14px;
      opacity: 0.7;
    }

    .input-container {
      background: white;
      border-top: 1px solid #e0e0e0;
      padding: 16px;
      display: flex;
      gap: 12px;
      align-items: flex-end;
    }

    .input-wrapper {
      flex: 1;
      position: relative;
    }

    .message-input {
      width: 100%;
      min-height: 40px;
      max-height: 120px;
      padding: 12px 16px;
      border: 2px solid #e0e0e0;
      border-radius: 20px;
      font-size: 14px;
      font-family: inherit;
      resize: none;
      outline: none;
      transition: border-color 0.2s ease;
      line-height: 1.4;
    }

    .message-input:focus {
      border-color: #667eea;
    }

    .message-input:disabled {
      background: #f5f5f5;
      color: #999;
      cursor: not-allowed;
    }

    .input-placeholder {
      position: absolute;
      top: 12px;
      left: 16px;
      color: #999;
      pointer-events: none;
      transition: all 0.2s ease;
      font-size: 14px;
    }

    .message-input:focus + .input-placeholder,
    .message-input:not(:placeholder-shown) + .input-placeholder {
      transform: translateY(-24px) scale(0.8);
      color: #667eea;
    }

    .send-button {
      width: 40px;
      height: 40px;
      border: none;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
      flex-shrink: 0;
    }

    .send-button:hover:not(:disabled) {
      transform: scale(1.05);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }

    .send-button:disabled {
      background: #ccc;
      cursor: not-allowed;
      transform: none;
    }

    .send-button svg {
      width: 18px;
      height: 18px;
    }

    .send-button .dots-animation {
      justify-content: center;
    }

    .send-button .dots-animation span {
      background: white;
      width: 4px;
      height: 4px;
    }

    .typing-indicator-container {
      padding: 8px 16px;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    .typing-indicator-container.visible {
      opacity: 1;
    }

    .processing-indicator {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 8px 16px;
      background: rgba(102, 126, 234, 0.1);
      border-radius: 20px;
      margin: 8px 16px;
      opacity: 0;
      transition: opacity 0.3s ease;
      pointer-events: none;
    }

    .processing-indicator.visible {
      opacity: 1;
    }

    .dots-animation {
      display: flex;
      gap: 4px;
      align-items: center;
    }

    .dots-animation span {
      width: 6px;
      height: 6px;
      background: #667eea;
      border-radius: 50%;
      animation: dots-bounce 1.4s infinite ease-in-out;
    }

    .dots-animation span:nth-child(1) {
      animation-delay: -0.32s;
    }

    .dots-animation span:nth-child(2) {
      animation-delay: -0.16s;
    }

    .dots-animation span:nth-child(3) {
      animation-delay: 0s;
    }

    @keyframes dots-bounce {
      0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
      }
      40% {
        transform: scale(1);
        opacity: 1;
      }
    }

    /* Scrollbar styling */
    .messages-container::-webkit-scrollbar {
      width: 6px;
    }

    .messages-container::-webkit-scrollbar-track {
      background: #f1f1f1;
    }

    .messages-container::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
    }

    .messages-container::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }

    /* Dark mode support */
    @media (prefers-color-scheme: dark) {
      :host {
        background: #1a1a1a;
        color: #e0e0e0;
      }

      .input-container {
        background: #2d2d2d;
        border-top-color: #404040;
      }

      .message-input {
        background: #3d3d3d;
        color: #e0e0e0;
        border-color: #555;
      }

      .message-input:focus {
        border-color: #667eea;
      }

      .empty-state {
        color: #999;
      }

      .processing-overlay {
        background: rgba(26, 26, 26, 0.8);
      }
    }

    /* Mobile responsiveness */
    @media (max-width: 768px) {
      .chat-header {
        padding: 12px;
        font-size: 16px;
      }

      .messages-container {
        padding: 12px;
      }

      .input-container {
        padding: 12px;
      }

      .message-input {
        font-size: 16px; /* Prevent zoom on iOS */
      }
    }

    @media (max-width: 480px) {
      .input-container {
        padding: 8px;
        gap: 8px;
      }

      .send-button {
        width: 36px;
        height: 36px;
      }
    }
  `;

  @property({ type: Array })
  messages: ChatMessage[] = [];

  @property({ type: String })
  connectionStatus: 'connected' | 'disconnected' | 'connecting' = 'disconnected';

  @property({ type: Boolean })
  isProcessing = false;

  @property({ type: Boolean })
  isTyping = false;

  @property({ type: String })
  placeholder = 'Type your message...';

  @property({ type: String })
  title = 'Life Coach Assistant';

  @state()
  private inputValue = '';

  @state()
  private isInputFocused = false;

  @query('.messages-container')
  private messagesContainer!: HTMLElement;

  @query('.message-input')
  private messageInput!: HTMLTextAreaElement;

  /**
   * Lifecycle: Component updated
   */
  protected updated(changedProperties: PropertyValues): void {
    if (changedProperties.has('messages')) {
      this.scrollToBottom();
    }

    if (changedProperties.has('isTyping') && this.isTyping) {
      this.scrollToBottom();
    }
  }

    /**
   * Scrolls to the bottom of the messages container
   */
  private scrollToBottom(): void {
    if (this.messagesContainer) {
      // Use requestAnimationFrame for smoother scrolling
      requestAnimationFrame(() => {
        this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
      });
    }
  }

  /**
   * Handles input changes
   */
  private handleInputChange(event: Event): void {
    const target = event.target as HTMLTextAreaElement;
    this.inputValue = target.value;
    
    // Auto-resize textarea
    target.style.height = 'auto';
    target.style.height = `${Math.min(target.scrollHeight, 120)}px`;
  }

  /**
   * Handles input focus
   */
  private handleInputFocus(): void {
    this.isInputFocused = true;
  }

  /**
   * Handles input blur
   */
  private handleInputBlur(): void {
    this.isInputFocused = false;
  }

  /**
   * Handles key press events
   */
  private handleKeyPress(event: KeyboardEvent): void {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.sendMessage();
    }
  }

  /**
   * Sends a message
   */
  private sendMessage(): void {
    const content = this.inputValue.trim();
    if (!content || this.isProcessing || this.connectionStatus !== 'connected') return;

    // Create user message
    const userMessage: ChatMessage = {
      id: `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type: 'user',
      content,
      timestamp: new Date()
    };

    // Dispatch message event
    this.dispatchEvent(new CustomEvent('message-send', {
      detail: { message: userMessage },
      bubbles: true
    }));

    // Clear input
    this.inputValue = '';
    this.messageInput.style.height = 'auto';
  }

  /**
   * Adds a message to the chat
   */
  public addMessage(message: ChatMessage): void {
    this.messages = [...this.messages, message];
  }

  /**
   * Clears all messages
   */
  public clearMessages(): void {
    this.messages = [];
  }

  /**
   * Gets connection status display text
   */
  private getStatusText(): string {
    switch (this.connectionStatus) {
      case 'connected':
        return '🟢 Connected';
      case 'connecting':
        return '🟡 Connecting...';
      case 'disconnected':
        return '🔴 Disconnected';
      default:
        return '';
    }
  }

  /**
   * Gets input tooltip based on connection status
   */
  private getInputTooltip(): string {
    if (this.isProcessing) {
      return 'Please wait while your message is being processed...';
    }

    switch (this.connectionStatus) {
      case 'connected':
        return 'Type your message and press Enter to send';
      case 'connecting':
        return 'Connecting to server... Please wait';
      case 'disconnected':
        return 'Not connected to server. Please check your connection and refresh the page.';
      default:
        return 'Chat unavailable';
    }
  }

  /**
   * Gets placeholder text based on connection status
   */
  private getPlaceholderText(): string {
    if (this.isProcessing) {
      return 'Creating your personalized wheel...';
    }

    switch (this.connectionStatus) {
      case 'connected':
        return this.placeholder || 'Tell me what you\'d like to do and I\'ll create a personalized activity wheel for you!';
      case 'connecting':
        return 'Connecting to your life coach...';
      case 'disconnected':
        return 'Connection lost - please refresh the page';
      default:
        return 'Chat unavailable';
    }
  }

  /**
   * Renders empty state
   */
  private renderEmptyState() {
    return html`
      <div class="empty-state">
        <div class="empty-state-icon">💬</div>
        <div class="empty-state-text">Start a conversation</div>
        <div class="empty-state-subtext">Ask me anything about your goals and activities!</div>
      </div>
    `;
  }

  /**
   * Renders typing indicator
   */
  private renderTypingIndicator() {
    if (!this.isTyping) return '';

    return html`
      <div class="typing-indicator-container visible">
        <message-bubble
          .message=${{
            id: 'typing',
            type: 'ai' as MessageType,
            content: 'AI is typing...',
            timestamp: new Date()
          }}
          .isTyping=${true}
          .showTimestamp=${false}>
        </message-bubble>
      </div>
    `;
  }

  render() {
    const hasMessages = this.messages.length > 0;
    const canSend = this.inputValue.trim().length > 0 && this.connectionStatus === 'connected';

    return html`
      <div class="chat-header">
        ${this.title}
        <div class="chat-status ${this.connectionStatus}">
          ${this.getStatusText()}
        </div>
      </div>

      <div class="messages-container">
        <div class="messages-list">
          ${hasMessages ? repeat(
            this.messages,
            (message) => message.id,
            (message) => html`
              <message-bubble .message=${message}></message-bubble>
            `
          ) : this.renderEmptyState()}
        </div>

        ${this.renderTypingIndicator()}

        <!-- Non-blocking processing indicator -->
        <div class="processing-indicator ${this.isProcessing ? 'visible' : ''}">
          <div class="dots-animation">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </div>

      <div class="input-container">
        <div class="input-wrapper">
          <textarea
            class="message-input"
            .value=${this.inputValue}
            ?disabled=${this.connectionStatus !== 'connected'}
            placeholder=" "
            @input=${this.handleInputChange}
            @focus=${this.handleInputFocus}
            @blur=${this.handleInputBlur}
            @keypress=${this.handleKeyPress}
            rows="1"
            title=${this.getInputTooltip()}>
          </textarea>
          <div class="input-placeholder">${this.getPlaceholderText()}</div>
        </div>
        
        <button
          class="send-button"
          ?disabled=${!canSend || this.isProcessing}
          @click=${this.sendMessage}
          title=${this.isProcessing ? 'Processing...' : 'Send message'}>
          ${this.isProcessing ? html`
            <div class="dots-animation">
              <span></span>
              <span></span>
              <span></span>
            </div>
          ` : html`
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
            </svg>
          `}
        </button>
      </div>


    `;
  }
}
