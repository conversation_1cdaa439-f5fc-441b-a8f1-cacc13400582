/**
 * Message bubble component for chat interface
 * Displays individual messages with proper styling and metadata
 */

import { LitElement, html, css, PropertyValues } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { classMap } from 'lit/directives/class-map.js';

/**
 * Message types supported by the chat interface
 */
export type MessageType = 'user' | 'ai' | 'system' | 'error' | 'wheel-result';

/**
 * Message data structure
 */
export interface ChatMessage {
  id: string;
  type: MessageType;
  content: string;
  timestamp: Date;
  metadata?: {
    wheelResult?: {
      segmentId: string;
      segmentText: string;
      spinDuration: number;
    };
    processingTime?: number;
    error?: {
      code: string;
      details: string;
    };
  };
}

/**
 * Message bubble web component
 */
@customElement('message-bubble')
export class MessageBubble extends LitElement {
  static styles = css`
    :host {
      display: block;
      margin: 8px 0;
      animation: slideIn 0.3s ease-out;
    }

    @keyframes slideIn {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .message-container {
      display: flex;
      align-items: flex-end;
      gap: 8px;
    }

    .message-container.user {
      flex-direction: row-reverse;
    }

    .avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: linear-gradient(45deg, #4ecdc4, #45b7d1);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      font-weight: bold;
      color: white;
      flex-shrink: 0;
    }

    .avatar.user {
      background: linear-gradient(45deg, #ff6b6b, #ffa500);
    }

    .avatar.system {
      background: linear-gradient(45deg, #96ceb4, #85c1e9);
    }

    .avatar.error {
      background: linear-gradient(45deg, #ff4757, #ff3838);
    }

    .message-bubble {
      max-width: 70%;
      padding: 12px 16px;
      border-radius: 18px;
      position: relative;
      word-wrap: break-word;
      line-height: 1.4;
    }

    .message-bubble.user {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-bottom-right-radius: 4px;
    }

    .message-bubble.ai {
      background: #f1f3f4;
      color: #333;
      border-bottom-left-radius: 4px;
      border: 1px solid #e0e0e0;
    }

    .message-bubble.system {
      background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
      color: #333;
      border-radius: 12px;
      font-style: italic;
      text-align: center;
      max-width: 90%;
      margin: 0 auto;
    }

    .message-bubble.error {
      background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
      color: #721c24;
      border: 1px solid #f5c6cb;
    }

    .message-bubble.wheel-result {
      background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
      color: #8b4513;
      border: 2px solid #ffd700;
      box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
    }

    .message-content {
      margin: 0;
    }

    .message-metadata {
      margin-top: 8px;
      padding-top: 8px;
      border-top: 1px solid rgba(255, 255, 255, 0.2);
      font-size: 12px;
      opacity: 0.8;
    }

    .message-bubble.ai .message-metadata {
      border-top-color: rgba(0, 0, 0, 0.1);
    }

    .timestamp {
      font-size: 11px;
      opacity: 0.6;
      margin-top: 4px;
      text-align: right;
    }

    .message-container.user .timestamp {
      text-align: left;
    }

    .wheel-result-details {
      display: flex;
      flex-direction: column;
      gap: 4px;
      margin-top: 8px;
      padding: 8px;
      background: rgba(255, 255, 255, 0.3);
      border-radius: 8px;
    }

    .wheel-result-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 12px;
    }

    .wheel-result-label {
      font-weight: bold;
    }

    .wheel-result-value {
      color: #654321;
    }

    .error-details {
      margin-top: 8px;
      padding: 8px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 6px;
      font-size: 12px;
    }

    .error-code {
      font-weight: bold;
      color: #5a1a1a;
    }

    .processing-indicator {
      display: inline-flex;
      align-items: center;
      gap: 4px;
      font-size: 11px;
      opacity: 0.7;
    }

    .typing-indicator {
      display: flex;
      gap: 2px;
      margin: 4px 0;
    }

    .typing-dot {
      width: 4px;
      height: 4px;
      border-radius: 50%;
      background: currentColor;
      animation: typingPulse 1.4s ease-in-out infinite both;
    }

    .typing-dot:nth-child(1) { animation-delay: -0.32s; }
    .typing-dot:nth-child(2) { animation-delay: -0.16s; }
    .typing-dot:nth-child(3) { animation-delay: 0s; }

    @keyframes typingPulse {
      0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
      }
      40% {
        transform: scale(1);
        opacity: 1;
      }
    }

    /* Dark mode support */
    @media (prefers-color-scheme: dark) {
      .message-bubble.ai {
        background: #2d2d2d;
        color: #e0e0e0;
        border-color: #404040;
      }

      .message-bubble.system {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        color: #ecf0f1;
      }
    }

    /* Mobile responsiveness */
    @media (max-width: 768px) {
      .message-bubble {
        max-width: 85%;
        padding: 10px 14px;
        font-size: 14px;
      }

      .avatar {
        width: 28px;
        height: 28px;
        font-size: 12px;
      }
    }

    @media (max-width: 480px) {
      .message-bubble {
        max-width: 90%;
        padding: 8px 12px;
        font-size: 13px;
      }

      .avatar {
        width: 24px;
        height: 24px;
        font-size: 10px;
      }
    }
  `;

  @property({ type: Object })
  message!: ChatMessage;

  @property({ type: Boolean })
  showAvatar = true;

  @property({ type: Boolean })
  showTimestamp = true;

  @property({ type: Boolean })
  isTyping = false;

  /**
   * Formats timestamp for display
   */
  private formatTimestamp(date: Date): string {
    const now = new Date();
    const diff = now.getTime() - date.getTime();

    if (diff < 60000) { // Less than 1 minute
      return 'Just now';
    } else if (diff < 3600000) { // Less than 1 hour
      const minutes = Math.floor(diff / 60000);
      return `${minutes}m ago`;
    } else if (diff < 86400000) { // Less than 1 day
      const hours = Math.floor(diff / 3600000);
      return `${hours}h ago`;
    } else {
      return date.toLocaleDateString();
    }
  }

  /**
   * Renders message content with markdown auto-detection and line break preservation
   */
  private renderMessageContent() {
    let content = this.message.content;

    // Auto-detect and convert markdown patterns
    content = this.processMarkdown(content);

    // Preserve line breaks
    content = this.preserveLineBreaks(content);

    // Return as unsafe HTML to allow markdown rendering
    return html`<div .innerHTML=${content}></div>`;
  }

  /**
   * Process basic markdown patterns
   */
  private processMarkdown(text: string): string {
    // Bold text: **text** or __text__
    text = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    text = text.replace(/__(.*?)__/g, '<strong>$1</strong>');

    // Italic text: *text* or _text_
    text = text.replace(/\*(.*?)\*/g, '<em>$1</em>');
    text = text.replace(/_(.*?)_/g, '<em>$1</em>');

    // Code: `text`
    text = text.replace(/`(.*?)`/g, '<code>$1</code>');

    // Lists: - item or * item
    text = text.replace(/^[\s]*[-*]\s+(.+)$/gm, '<li>$1</li>');

    // Wrap consecutive list items in ul tags
    text = text.replace(/(<li>.*<\/li>)/gs, (match) => {
      return `<ul>${match}</ul>`;
    });

    return text;
  }

  /**
   * Preserve line breaks by converting them to HTML
   */
  private preserveLineBreaks(text: string): string {
    // Convert double line breaks to paragraphs
    text = text.replace(/\n\n/g, '</p><p>');

    // Convert single line breaks to <br>
    text = text.replace(/\n/g, '<br>');

    // Wrap in paragraph tags if not already wrapped
    if (!text.includes('<p>') && !text.includes('<ul>')) {
      text = `<p>${text}</p>`;
    }

    return text;
  }

  /**
   * Gets avatar text based on message type
   */
  private getAvatarText(): string {
    switch (this.message.type) {
      case 'user':
        return 'U';
      case 'ai':
        return 'AI';
      case 'system':
        return 'S';
      case 'error':
        return '!';
      case 'wheel-result':
        return '🎯';
      default:
        return '?';
    }
  }

  /**
   * Renders wheel result metadata
   */
  private renderWheelResult() {
    const wheelResult = this.message.metadata?.wheelResult;
    if (!wheelResult) return '';

    return html`
      <div class="wheel-result-details">
        <div class="wheel-result-item">
          <span class="wheel-result-label">Selected:</span>
          <span class="wheel-result-value">${wheelResult.segmentText}</span>
        </div>
        <div class="wheel-result-item">
          <span class="wheel-result-label">Duration:</span>
          <span class="wheel-result-value">${(wheelResult.spinDuration / 1000).toFixed(1)}s</span>
        </div>
      </div>
    `;
  }

  /**
   * Renders error details
   */
  private renderErrorDetails() {
    const error = this.message.metadata?.error;
    if (!error) return '';

    return html`
      <div class="error-details">
        <div class="error-code">Error ${error.code}</div>
        <div>${error.details}</div>
      </div>
    `;
  }

  /**
   * Renders processing time indicator
   */
  private renderProcessingTime() {
    const processingTime = this.message.metadata?.processingTime;
    if (!processingTime) return '';

    return html`
      <div class="processing-indicator">
        ⚡ Processed in ${processingTime}ms
      </div>
    `;
  }

  /**
   * Renders typing indicator
   */
  private renderTypingIndicator() {
    if (!this.isTyping) return '';

    return html`
      <div class="typing-indicator">
        <div class="typing-dot"></div>
        <div class="typing-dot"></div>
        <div class="typing-dot"></div>
      </div>
    `;
  }

  render() {
    const containerClasses = {
      'message-container': true,
      [this.message.type]: true
    };

    const bubbleClasses = {
      'message-bubble': true,
      [this.message.type]: true
    };

    const avatarClasses = {
      'avatar': true,
      [this.message.type]: true
    };

    return html`
      <div class=${classMap(containerClasses)}>
        ${this.showAvatar ? html`
          <div class=${classMap(avatarClasses)}>
            ${this.getAvatarText()}
          </div>
        ` : ''}
        
        <div class=${classMap(bubbleClasses)}>
          <div class="message-content">
            ${this.renderMessageContent()}
            ${this.renderTypingIndicator()}
          </div>
          
          ${this.message.type === 'wheel-result' ? this.renderWheelResult() : ''}
          ${this.message.type === 'error' ? this.renderErrorDetails() : ''}
          
          <div class="message-metadata">
            ${this.renderProcessingTime()}
          </div>
        </div>
      </div>
      
      ${this.showTimestamp ? html`
        <div class="timestamp">
          ${this.formatTimestamp(this.message.timestamp)}
        </div>
      ` : ''}
    `;
  }
}
