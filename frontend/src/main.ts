/**
 * Main entry point for the Goali frontend application
 * Initializes the application, registers web components, and handles global setup
 */

import './styles/global.css';

// Import and register all web components
import './components/app-shell';
import './components/game-wheel/game-wheel';
import './components/chat/chat-interface';
import './components/chat/message-bubble';
import './components/debug/debug-panel';
import './components/auth/login-form';
import './components/progress/real-time-progress-bar';
import './components/observability/observability-dashboard';

// Import core services
import { WebSocketManager } from './services/websocket-manager';
import { StateManager } from './services/state-manager';
import { MessageHandler } from './services/message-handler';
import { ConfigService } from './services/config-service';
import { AuthService } from './services/auth-service';

// Import types
import type { AppConfig } from './types/app-types';

// Get configuration from ConfigService
const configService = ConfigService.getInstance();
const APP_CONFIG = configService.getConfig();

/**
 * Global error handler
 */
function setupGlobalErrorHandling(): void {
  window.addEventListener('error', (event) => {
    console.error('Global error:', event.error);
    showErrorFallback('An unexpected error occurred');
  });

  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
    showErrorFallback('A network or loading error occurred');
  });
}

/**
 * Show error fallback UI
 */
function showErrorFallback(message: string): void {
  const errorFallback = document.getElementById('error-fallback');
  const loadingScreen = document.getElementById('loading-screen');

  if (errorFallback) {
    errorFallback.style.display = 'block';
    const messageElement = errorFallback.querySelector('p');
    if (messageElement) {
      // Provide more helpful error messages
      if (message.includes('WebSocket') || message.includes('connection')) {
        messageElement.textContent = 'Unable to connect to server. The app will work in demo mode with sample data.';
      } else if (message.includes('WebGL')) {
        messageElement.textContent = 'Your browser doesn\'t support WebGL. Please try a different browser.';
      } else {
        messageElement.textContent = message;
      }
    }
  }

  if (loadingScreen) {
    loadingScreen.classList.add('hidden');
  }
}

/**
 * Hide loading screen and show app
 */
function hideLoadingScreen(): void {
  const loadingScreen = document.getElementById('loading-screen');
  const appShell = document.querySelector('app-shell');
  
  if (loadingScreen) {
    loadingScreen.classList.add('hidden');
    setTimeout(() => {
      loadingScreen.style.display = 'none';
    }, 500);
  }
  
  if (appShell) {
    appShell.classList.add('loaded');
  }
}

/**
 * Initialize core services
 */
async function initializeServices(): Promise<void> {
  try {
    // Initialize state manager
    const stateManager = StateManager.getInstance();
    stateManager.initialize(APP_CONFIG);

    // Initialize WebSocket manager (but don't connect yet - let app-shell handle it)
    const wsManager = WebSocketManager.getInstance();

    // Initialize message handler
    const messageHandler = MessageHandler.getInstance();
    messageHandler.initialize(wsManager, stateManager);

    // Initialize authentication service for production mode
    const authService = AuthService.getInstance();
    if (configService.isProductionMode()) {
      authService.setupAutoRefresh();
    }

    // Store global references for debugging
    if (APP_CONFIG.debug.enabled) {
      (window as any).__GOALI_DEBUG__ = {
        stateManager,
        wsManager,
        messageHandler,
        configService,
        authService,
        config: APP_CONFIG,
      };
    }

    console.log(`✅ Core services initialized successfully (${APP_CONFIG.mode} mode)`);
  } catch (error) {
    console.error('❌ Failed to initialize services:', error);
    // Don't throw - let the app continue with demo mode
    console.warn('⚠️ Continuing with limited functionality');
  }
}

/**
 * Check browser compatibility
 */
function checkBrowserCompatibility(): boolean {
  const requiredFeatures = [
    'customElements',
    'WebSocket',
    'Promise',
    'fetch',
    'requestAnimationFrame',
  ];
  
  for (const feature of requiredFeatures) {
    if (!(feature in window)) {
      console.error(`❌ Browser missing required feature: ${feature}`);
      return false;
    }
  }
  
  // Check for WebGL support (required for PixiJS)
  const canvas = document.createElement('canvas');
  const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
  if (!gl) {
    console.error('❌ WebGL not supported');
    return false;
  }
  
  return true;
}

/**
 * Main application initialization
 */
async function initializeApp(): Promise<void> {
  console.log('🚀 Initializing Goali frontend...');
  
  try {
    // Check browser compatibility
    if (!checkBrowserCompatibility()) {
      throw new Error('Browser not compatible');
    }
    
    // Setup global error handling
    setupGlobalErrorHandling();
    
    // Initialize services
    await initializeServices();
    
    // Wait for web components to be defined
    await Promise.all([
      customElements.whenDefined('app-shell'),
      customElements.whenDefined('game-wheel'),
      customElements.whenDefined('chat-interface'),
      customElements.whenDefined('debug-panel'),
      customElements.whenDefined('login-form'),
      customElements.whenDefined('real-time-progress-bar'),
      customElements.whenDefined('observability-dashboard'),
    ]);
    
    // Hide loading screen
    hideLoadingScreen();
    
    console.log('✅ Goali frontend initialized successfully');
    
  } catch (error) {
    console.error('❌ Failed to initialize app:', error);
    showErrorFallback(
      error instanceof Error ? error.message : 'Failed to load application'
    );
  }
}

/**
 * Start the application when DOM is ready
 */
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeApp);
} else {
  initializeApp();
}

// Export for potential external access
export { APP_CONFIG, initializeApp };
