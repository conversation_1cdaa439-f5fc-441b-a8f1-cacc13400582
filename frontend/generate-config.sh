#!/usr/bin/env bash
set -eE

echo "🔧 Generating frontend configuration..."

# Default values for development
DEFAULT_REQUIRE_AUTH="false"
DEFAULT_TOKEN_VALIDATION="false"
DEFAULT_SESSION_TIMEOUT="1800000"

# Use environment variables or defaults
REQUIRE_AUTH="${VITE_SECURITY_REQUIRE_AUTH:-$DEFAULT_REQUIRE_AUTH}"
TOKEN_VALIDATION="${VITE_SECURITY_TOKEN_VALIDATION:-$DEFAULT_TOKEN_VALIDATION}"
SESSION_TIMEOUT="${VITE_SECURITY_SESSION_TIMEOUT:-$DEFAULT_SESSION_TIMEOUT}"

echo "📋 Configuration values:"
echo "  VITE_SECURITY_REQUIRE_AUTH: $REQUIRE_AUTH"
echo "  VITE_SECURITY_TOKEN_VALIDATION: $TOKEN_VALIDATION"
echo "  VITE_SECURITY_SESSION_TIMEOUT: $SESSION_TIMEOUT"

# Ensure the target directory exists
mkdir -p src/config

# Create the environment configuration file
cat > src/config/generated-env.ts << EOF
// Auto-generated configuration file
// DO NOT EDIT MANUALLY - This file is generated during build

export const GENERATED_ENV = {
  VITE_SECURITY_REQUIRE_AUTH: '$REQUIRE_AUTH',
  VITE_SECURITY_TOKEN_VALIDATION: '$TOKEN_VALIDATION',
  VITE_SECURITY_SESSION_TIMEOUT: '$SESSION_TIMEOUT',
  GENERATED_AT: '$(date -u +"%Y-%m-%dT%H:%M:%SZ")'
} as const;
EOF

echo "✅ Configuration file generated at src/config/generated-env.ts"
