#!/usr/bin/env node

/**
 * Test script to verify debug vs production mode functionality
 * Run with: node test-debug-mode.js
 */

import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.log('🧪 Testing Debug vs Production Mode Configuration\n');

// Test environment file parsing
function testEnvironmentFiles() {
  console.log('📁 Testing Environment Files:');
  
  try {
    const devEnv = readFileSync(join(__dirname, '.env.development'), 'utf8');
    const prodEnv = readFileSync(join(__dirname, '.env.production'), 'utf8');
    
    console.log('✅ .env.development exists');
    console.log('✅ .env.production exists');
    
    // Check for required variables
    const requiredVars = [
      'VITE_APP_MODE',
      'VITE_WS_URL',
      'VITE_DEBUG_ENABLED',
      'VITE_SECURITY_REQUIRE_AUTH'
    ];
    
    requiredVars.forEach(varName => {
      if (devEnv.includes(varName) && prodEnv.includes(varName)) {
        console.log(`✅ ${varName} defined in both environments`);
      } else {
        console.log(`❌ ${varName} missing in one or both environments`);
      }
    });
    
  } catch (error) {
    console.log('❌ Error reading environment files:', error.message);
  }
  
  console.log('');
}

// Test TypeScript type definitions
function testTypeDefinitions() {
  console.log('🔧 Testing TypeScript Definitions:');
  
  try {
    const typesContent = readFileSync(join(__dirname, 'src/types/app-types.ts'), 'utf8');
    
    const requiredTypes = [
      'AppMode',
      'DebugConfig',
      'SecurityConfig',
      'AuthToken',
      'LLMConfig'
    ];
    
    requiredTypes.forEach(typeName => {
      if (typesContent.includes(`interface ${typeName}`) || typesContent.includes(`type ${typeName}`)) {
        console.log(`✅ ${typeName} type defined`);
      } else {
        console.log(`❌ ${typeName} type missing`);
      }
    });
    
  } catch (error) {
    console.log('❌ Error reading type definitions:', error.message);
  }
  
  console.log('');
}

// Test component files
function testComponents() {
  console.log('🎨 Testing Component Files:');
  
  const components = [
    'src/components/debug/debug-panel.ts',
    'src/components/auth/login-form.ts',
    'src/services/config-service.ts',
    'src/services/auth-service.ts'
  ];
  
  components.forEach(componentPath => {
    try {
      const content = readFileSync(join(__dirname, componentPath), 'utf8');
      if (content.includes('@customElement') || content.includes('class')) {
        console.log(`✅ ${componentPath} exists and has class definition`);
      } else {
        console.log(`⚠️ ${componentPath} exists but may be incomplete`);
      }
    } catch (error) {
      console.log(`❌ ${componentPath} missing or unreadable`);
    }
  });
  
  console.log('');
}

// Test package.json scripts
function testPackageScripts() {
  console.log('📦 Testing Package Scripts:');
  
  try {
    const packageJson = JSON.parse(readFileSync(join(__dirname, 'package.json'), 'utf8'));
    const scripts = packageJson.scripts;
    
    const requiredScripts = [
      'dev:debug',
      'dev:prod',
      'build:debug',
      'build:prod'
    ];
    
    requiredScripts.forEach(scriptName => {
      if (scripts[scriptName]) {
        console.log(`✅ ${scriptName} script defined`);
      } else {
        console.log(`❌ ${scriptName} script missing`);
      }
    });
    
  } catch (error) {
    console.log('❌ Error reading package.json:', error.message);
  }
  
  console.log('');
}

// Test configuration logic
function testConfigurationLogic() {
  console.log('⚙️ Testing Configuration Logic:');
  
  // Simulate different environment configurations
  const testConfigs = [
    {
      name: 'Debug Mode',
      env: {
        VITE_APP_MODE: 'debug',
        VITE_DEBUG_ENABLED: 'true',
        VITE_DEBUG_ALLOW_USER_SELECTION: 'true',
        VITE_SECURITY_REQUIRE_AUTH: 'false'
      }
    },
    {
      name: 'Production Mode',
      env: {
        VITE_APP_MODE: 'production',
        VITE_DEBUG_ENABLED: 'false',
        VITE_DEBUG_ALLOW_USER_SELECTION: 'false',
        VITE_SECURITY_REQUIRE_AUTH: 'true'
      }
    }
  ];
  
  testConfigs.forEach(config => {
    console.log(`Testing ${config.name}:`);
    
    // Simulate config service logic
    const mode = config.env.VITE_APP_MODE;
    const debugEnabled = mode === 'debug' && config.env.VITE_DEBUG_ENABLED === 'true';
    const authRequired = config.env.VITE_SECURITY_REQUIRE_AUTH === 'true';
    
    console.log(`  Mode: ${mode}`);
    console.log(`  Debug: ${debugEnabled ? 'enabled' : 'disabled'}`);
    console.log(`  Auth: ${authRequired ? 'required' : 'optional'}`);
    
    if (mode === 'debug' && debugEnabled) {
      console.log('  ✅ Debug mode configured correctly');
    } else if (mode === 'production' && !debugEnabled && authRequired) {
      console.log('  ✅ Production mode configured correctly');
    } else {
      console.log('  ⚠️ Configuration may have issues');
    }
    
    console.log('');
  });
}

// Run all tests
function runTests() {
  testEnvironmentFiles();
  testTypeDefinitions();
  testComponents();
  testPackageScripts();
  testConfigurationLogic();
  
  console.log('🎯 Test Summary:');
  console.log('');
  console.log('To test the implementation:');
  console.log('1. Run debug mode: npm run dev:debug');
  console.log('2. Press Ctrl+Shift+D to open debug panel');
  console.log('3. Test user selection and configuration changes');
  console.log('4. Run production mode: npm run dev:prod');
  console.log('5. Verify login form appears');
  console.log('6. Test authentication flow');
  console.log('');
  console.log('Backend endpoints needed:');
  console.log('- GET /api/debug/users/ (debug mode)');
  console.log('- GET /api/debug/llm-configs/ (debug mode)');
  console.log('- POST /api/auth/login/ (production mode)');
  console.log('- POST /api/auth/verify/ (production mode)');
}

runTests();
