# Goali Frontend Production-Test Environment Configuration
# This configuration simulates production environment for local testing
# with proper authentication and security measures enforced

# Application Mode (production-like but with local backend)
VITE_APP_MODE=production-test

# Local Backend URLs (simulating production setup)
VITE_API_BASE_URL=http://localhost:8000
VITE_WS_URL=ws://localhost:8000/ws/game/

# Security Configuration (production-like requirements)
VITE_SECURITY_REQUIRE_AUTH=true
VITE_SECURITY_TOKEN_VALIDATION=true
VITE_SECURITY_SESSION_TIMEOUT=1800000
VITE_SECURITY_ALLOWED_ORIGINS=http://localhost:3001,http://localhost:8000

# Debug Configuration (minimal debug features)
VITE_DEBUG_ENABLED=false
VITE_DEBUG_SHOW_PERFORMANCE=false
VITE_DEBUG_SHOW_NETWORK_LOGS=false
VITE_DEBUG_SHOW_STATE_INSPECTOR=false
VITE_DEBUG_ALLOW_USER_SELECTION=false
VITE_DEBUG_ALLOW_LLM_CONFIG_SELECTION=false
VITE_DEBUG_ALLOW_BACKEND_URL_CHANGE=false
VITE_DEBUG_MOCK_DATA_ENABLED=false

# Wheel Configuration (production-like caching)
VITE_WHEEL_CACHE_ENABLED=true

# Logging Configuration (production-like logging)
VITE_LOGGING_ENABLED=true
VITE_LOGGING_LEVEL=warn
VITE_LOGGING_ENDPOINT=http://localhost:8000/api/logs

# Performance optimizations (production-like)
VITE_BUILD_SOURCEMAP=false
VITE_BUILD_MINIFY=false
