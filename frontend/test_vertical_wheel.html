<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vertical Wheel Physics Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-controls {
            margin-bottom: 20px;
            padding: 20px;
            background: #2a2a2a;
            border-radius: 8px;
        }
        .test-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .test-button:hover {
            background: #45a049;
        }
        .test-results {
            margin-top: 20px;
            padding: 20px;
            background: #2a2a2a;
            border-radius: 8px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .wheel-container {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }
        .info {
            background: #333;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .info h3 {
            margin-top: 0;
            color: #4CAF50;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎡 Vertical Wheel Physics Test</h1>
        
        <div class="info">
            <h3>🎡 FIXED 100-Segment Vertical Wheel Model</h3>
            <p><strong>Segments:</strong> Exactly 100 equal segments, activities get proportional distribution</p>
            <p><strong>Physics:</strong> Ball suspended until wheel spins, then falls straight down due to gravity</p>
            <p><strong>Nails:</strong> 100 nails precisely delimiting segments, ball twice the size of nail spacing</p>
            <p><strong>Colors:</strong> Adjacent segments avoid same colors when possible</p>
            <p><strong>Winner:</strong> Determined only after BOTH wheel and ball completely stop</p>
            <p><strong>Visual:</strong> Upper half gradually hidden, activity legend with color mapping</p>
        </div>

        <div class="test-controls">
            <h3>Test Controls</h3>
            <button class="test-button" onclick="runVerticalWheelTest()">🧪 Test Vertical Wheel Physics</button>
            <button class="test-button" onclick="spinWheelManual()">🎡 Manual Spin Test</button>
            <button class="test-button" onclick="clearResults()">🗑️ Clear Results</button>
        </div>

        <div class="wheel-container">
            <game-wheel id="test-wheel"></game-wheel>
        </div>

        <div class="test-results" id="test-results">
Ready to test vertical wheel physics...
Click "Test Vertical Wheel Physics" to start automated test.
        </div>
    </div>

    <script type="module">
        import './src/main.js';

        let testResults = '';

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            testResults += `[${timestamp}] ${message}\n`;
            document.getElementById('test-results').textContent = testResults;
            console.log(message);
        }

        window.runVerticalWheelTest = async function() {
            log('🧪 STARTING FIXED 100-SEGMENT WHEEL TEST');
            log('=========================================');
            
            const wheel = document.querySelector('#test-wheel');
            if (!wheel) {
                log('❌ FAIL: Wheel component not found');
                return;
            }
            
            log('✅ Wheel component found');
            
            // Test 1: Initial State
            log('\n1️⃣ Testing Initial State...');
            const initialState = wheel.getWheelState();
            log(`Initial ball position: (${initialState.ballPosition.x.toFixed(1)}, ${initialState.ballPosition.y.toFixed(1)})`);
            log(`Center position: (${initialState.config.centerX}, ${initialState.config.centerY})`);
            
            // Check if ball starts at TOP (Y < center)
            if (initialState.ballPosition.y < initialState.config.centerY) {
                log('✅ Ball correctly starts at TOP of vertical wheel');
            } else {
                log('❌ Ball should start at TOP (Y < centerY)');
            }
            
            // Test 2: Fixed Segment System Check
            log('\n2️⃣ Testing Fixed 100-Segment System...');
            log('Expected: Exactly 100 segments, proportional activity distribution');
            
            // Test 3: Ball Suspension and Release
            log('\n3️⃣ Testing Ball Suspension and Release...');
            wheel.startBallTracking();

            // Record initial position
            const startY = initialState.ballPosition.y;
            log(`Starting Y position: ${startY.toFixed(1)}`);
            log('Ball should be SUSPENDED (not falling) until wheel spins');

            // Spin the wheel (ball should be released to fall)
            wheel.spin();
            log('✅ Wheel spin initiated - ball should be RELEASED to fall straight down!');

            // Monitor for falling movement and bouncing
            let checkCount = 0;
            const maxChecks = 80; // 8 seconds for more bouncing time
            let maxSpeed = 0;
            let bounceCount = 0;
            let lastY = startY;

            const monitorFalling = setInterval(() => {
                checkCount++;
                const currentState = wheel.getWheelState();
                const currentY = currentState.ballPosition.y;
                const deltaY = currentY - startY;
                const velocity = currentState.ballVelocity || { magnitude: 0 };
                const speed = velocity.magnitude || 0;

                // Track maximum speed (should increase due to gravity acceleration)
                if (speed > maxSpeed) {
                    maxSpeed = speed;
                }

                // Detect bounces (sudden velocity changes)
                if (speed > 0.1 && Math.abs(currentY - lastY) > 2) {
                    bounceCount++;
                }
                lastY = currentY;

                log(`Check ${checkCount}: Y=${currentY.toFixed(1)}, ΔY=${deltaY.toFixed(1)} ${deltaY > 0 ? '⬇️ FALLING' : '⬆️ RISING'}, Speed=${speed.toFixed(3)}, Bounces=${bounceCount}`);

                if (deltaY > 15) {
                    log('✅ SUCCESS: Ball is FALLING DOWN with realistic acceleration!');
                    log(`✅ Max speed reached: ${maxSpeed.toFixed(3)} (shows gravity acceleration)`);
                    log(`✅ Bounces detected: ${bounceCount} (shows super bouncy physics)`);
                    clearInterval(monitorFalling);
                    log('\n🏁 SUPER BOUNCY VERTICAL WHEEL PHYSICS TEST COMPLETED');
                    log('Expected behavior: Ball accelerates down, bounces dynamically on fast-spinning nails');
                    return;
                }

                if (checkCount >= maxChecks) {
                    log('⚠️ Test timeout - check console for detailed physics logs');
                    log(`Final stats: Max speed=${maxSpeed.toFixed(3)}, Bounces=${bounceCount}`);
                    clearInterval(monitorFalling);
                }
            }, 100);
        };

        window.spinWheelManual = function() {
            log('\n🎡 MANUAL SPIN TEST');
            log('==================');
            
            const wheel = document.querySelector('#test-wheel');
            if (!wheel) {
                log('❌ Wheel not found');
                return;
            }
            
            log('Spinning wheel manually...');
            log('Watch the wheel - ball should fall from top and bounce on nails');
            wheel.spin();
        };

        window.clearResults = function() {
            testResults = 'Results cleared. Ready for new test...\n';
            document.getElementById('test-results').textContent = testResults;
        };

        // Auto-start test when page loads
        window.addEventListener('load', () => {
            setTimeout(() => {
                log('🎡 Vertical Wheel Physics Test Page Loaded');
                log('Click "Test Vertical Wheel Physics" to run automated test');
                log('Or click "Manual Spin Test" to spin and observe visually');
            }, 1000);
        });
    </script>
</body>
</html>
