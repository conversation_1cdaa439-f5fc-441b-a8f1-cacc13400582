# Goali Frontend Developer Guide

## Overview

The Goali frontend is a modern web application built with **Lit 3.x**, **TypeScript**, **PixiJS 8.x**, and **Matter.js**. It provides an interactive spinning wheel interface for activity selection, integrated with a real-time chat system for AI-powered life coaching.

## Architecture

### Core Technologies

- **Lit 3.x**: Web Components framework for building the UI
- **TypeScript**: Type-safe JavaScript development
- **PixiJS 8.x**: High-performance 2D graphics rendering
- **Matter.js**: 2D physics engine for realistic wheel mechanics
- **Vite**: Build tool and development server
- **WebSockets**: Real-time communication with backend

### Project Structure

```
frontend/
├── src/
│   ├── components/           # Lit web components
│   │   ├── app-shell.ts     # Main application container
│   │   ├── game-wheel/      # Spinning wheel component
│   │   │   ├── game-wheel.ts        # Main wheel component
│   │   │   ├── wheel-physics.ts     # Matter.js physics engine
│   │   │   ├── wheel-renderer.ts    # PixiJS rendering engine
│   │   │   └── wheel-types.ts       # TypeScript interfaces
│   │   └── chat/            # Chat interface components
│   │       ├── chat-interface.ts    # Main chat component
│   │       └── message-bubble.ts    # Individual message display
│   ├── services/            # Core application services
│   │   ├── websocket-manager.ts     # WebSocket communication
│   │   ├── state-manager.ts         # Application state management
│   │   └── message-handler.ts       # Message processing
│   ├── utils/               # Utility functions
│   │   ├── physics-utils.ts         # Physics calculations
│   │   └── color-utils.ts           # Color manipulation
│   ├── types/               # TypeScript type definitions
│   ├── styles/              # CSS stylesheets
│   └── main.ts              # Application entry point
├── public/                  # Static assets
├── package.json             # Dependencies and scripts
├── tsconfig.json           # TypeScript configuration
├── vite.config.ts          # Vite build configuration
└── index.html              # HTML entry point
```

## Key Components

### 1. App Shell (`app-shell.ts`)

The main application container that orchestrates all other components.

**Responsibilities:**
- WebSocket connection management
- Global state coordination
- Error handling and demo mode fallback
- Component integration and communication

**Key Features:**
- Automatic fallback to demo mode when backend is unavailable
- Real-time connection status display
- Message routing between chat and wheel components

### 2. Game Wheel (`game-wheel/`)

A sophisticated spinning wheel implementation with realistic physics.

#### Main Component (`game-wheel.ts`)
- Lit web component that integrates physics and rendering
- Handles user interactions (touch/mouse)
- Manages wheel state and animations
- Dispatches events for spin results

#### Physics Engine (`wheel-physics.ts`)
- Matter.js-based physics simulation
- Realistic ball movement with gravity and friction
- Collision detection with nails around wheel perimeter
- Configurable physics parameters

#### Renderer (`wheel-renderer.ts`)
- PixiJS-powered graphics rendering
- Dynamic segment visualization
- Smooth animations and visual effects
- Responsive design for all screen sizes

#### Types (`wheel-types.ts`)
- Comprehensive TypeScript interfaces
- Configuration objects for wheel behavior
- Event data structures
- Default configurations and color palettes

### 3. Chat Interface (`chat/`)

Real-time messaging system for AI interaction.

#### Chat Interface (`chat-interface.ts`)
- Message display with auto-scrolling
- Input handling with keyboard shortcuts
- Connection status indicators
- Processing state management

#### Message Bubble (`message-bubble.ts`)
- Individual message rendering
- Support for different message types (user, AI, system, error, wheel-result)
- Timestamp formatting
- Metadata display (processing time, wheel results)

## Services

### WebSocket Manager (`websocket-manager.ts`)

Handles all real-time communication with the backend.

**Features:**
- Automatic reconnection with exponential backoff
- Message queuing during disconnections
- Heartbeat mechanism for connection health
- Event-driven message handling
- Type-safe message validation

**Usage:**
```typescript
const wsManager = WebSocketManager.getInstance();
await wsManager.initialize(config);
wsManager.onMessage('wheel_generated', (data) => {
  // Handle wheel data
});
```

### State Manager (`state-manager.ts`)

Centralized application state management.

**Features:**
- Reactive state updates
- Component subscription system
- Immutable state updates
- Type-safe state access

### Message Handler (`message-handler.ts`)

Processes and routes messages between components.

**Features:**
- Message validation and parsing
- Error handling and logging
- Message transformation and enrichment

## Development Workflow

### Getting Started

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Start Development Server**
   ```bash
   npm run dev
   ```

3. **Build for Production**
   ```bash
   npm run build
   ```

4. **Type Checking**
   ```bash
   npm run type-check
   ```

### Demo Mode

When the backend is unavailable, the application automatically enters demo mode:

- Displays sample wheel with predefined activities
- Simulates AI responses to user messages
- Provides full wheel spinning functionality
- Shows "Demo Mode" in connection status

### Adding New Components

1. Create component file in appropriate directory
2. Extend `LitElement` and use `@customElement` decorator
3. Define properties with `@property` and `@state` decorators
4. Implement `render()` method with `html` template
5. Add styles using `static styles = css` property
6. Export component and import in `main.ts`

### Styling Guidelines

- Use CSS custom properties for theming
- Follow mobile-first responsive design
- Support dark/light mode preferences
- Ensure accessibility compliance
- Use consistent spacing and typography scales

### Physics Configuration

The wheel physics can be customized through the `WheelConfig` interface:

```typescript
const config: WheelConfig = {
  radius: 200,           // Wheel radius in pixels
  nailCount: 24,         // Number of nails around perimeter
  spinForce: 0.02,       // Base spin force multiplier
  wheelFriction: 0.001,  // Friction coefficient
  airResistance: 0.999,  // Air resistance factor
  // ... more options
};
```

### Rendering Optimization

- PixiJS handles 60fps animations efficiently
- Physics simulation runs independently
- Automatic canvas resizing for responsive design
- Memory management for graphics objects

## Testing

### Manual Testing

1. **Wheel Functionality**
   - Spin wheel with mouse/touch
   - Verify physics behavior
   - Check winner selection accuracy
   - Test responsive design

2. **Chat Interface**
   - Send messages in demo mode
   - Verify message display
   - Test auto-scrolling
   - Check connection status

3. **Error Handling**
   - Test with backend unavailable
   - Verify graceful degradation
   - Check error message display

### Browser Compatibility

- Modern browsers with ES2020 support
- WebGL support for PixiJS
- WebSocket support for real-time features
- Touch events for mobile devices

## Performance Considerations

### Optimization Strategies

1. **Lazy Loading**: Components load on demand
2. **Asset Optimization**: Compressed images and fonts
3. **Code Splitting**: Separate bundles for different features
4. **Memory Management**: Proper cleanup of physics/graphics objects
5. **Efficient Rendering**: PixiJS object pooling and culling

### Mobile Optimization

- Touch-friendly interaction areas (44px minimum)
- Optimized physics calculations for mobile CPUs
- Reduced particle effects on low-end devices
- Efficient WebSocket message handling

## Deployment

### Build Process

1. TypeScript compilation with strict type checking
2. Vite bundling with tree shaking
3. Asset optimization and compression
4. Source map generation for debugging

### Environment Configuration

Use environment variables for configuration:

```bash
VITE_WS_URL=ws://your-backend-url/ws
VITE_API_URL=https://your-api-url
```

### Production Considerations

- Enable gzip compression
- Set appropriate cache headers
- Use CDN for static assets
- Monitor WebSocket connection health
- Implement error tracking and analytics

## Troubleshooting

### Common Issues

1. **WebSocket Connection Fails**
   - Check backend server status
   - Verify WebSocket URL configuration
   - Check network connectivity
   - Application falls back to demo mode automatically

2. **Wheel Physics Issues**
   - Verify Matter.js initialization
   - Check physics configuration parameters
   - Ensure proper cleanup on component unmount

3. **Rendering Problems**
   - Check WebGL support in browser
   - Verify PixiJS canvas initialization
   - Check for memory leaks in graphics objects

4. **TypeScript Errors**
   - Run `npm run type-check` for detailed errors
   - Ensure all imports use `.js` extensions
   - Check interface compatibility

### Debug Tools

- Browser DevTools for WebSocket inspection
- Matter.js debug renderer for physics visualization
- PixiJS DevTools extension for graphics debugging
- TypeScript compiler for type checking

## Contributing

### Code Style

- Use TypeScript strict mode
- Follow ESLint configuration
- Use Prettier for code formatting
- Write descriptive commit messages
- Add JSDoc comments for public APIs

### Pull Request Process

1. Create feature branch from main
2. Implement changes with tests
3. Update documentation as needed
4. Ensure all checks pass
5. Request code review

This guide provides a comprehensive overview of the Goali frontend architecture and development practices. For specific implementation details, refer to the inline code documentation and TypeScript interfaces.
