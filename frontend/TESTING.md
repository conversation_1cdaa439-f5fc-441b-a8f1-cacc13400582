# Modern Frontend Testing Framework

This document describes the comprehensive testing setup for the Goali frontend, including unit tests, integration tests, and VSCode integration.

## 🏗️ Architecture

### Testing Stack
- **Vitest** - Modern, fast test runner with excellent TypeScript support
- **@open-wc/testing** - Web Components testing utilities for Lit components
- **jsdom** - DOM environment for testing
- **@vitest/ui** - Visual test interface
- **@vitest/coverage-v8** - Code coverage reporting

### Test Types
1. **Unit Tests** (`tests/*.test.ts`) - Component logic, template conditions, API interactions
2. **Integration Tests** (`ai-live-testing-tools/*.cjs`) - End-to-end browser testing with Puppeteer

## 🚀 Quick Start

### Using the Test Runner
```bash
# Run all unit tests
node test-runner.js unit

# Watch mode for development
node test-runner.js unit:watch

# Visual test interface
node test-runner.js unit:ui

# Coverage report
node test-runner.js unit:coverage

# Wheel-specific tests
node test-runner.js wheel

# Integration tests
node test-runner.js integration

# All tests
node test-runner.js all
```

### Direct npm Commands
```bash
# Unit tests
npm test                    # Run all tests once
npm run test:watch         # Watch mode
npm run test:ui            # Visual interface
npm run test:coverage      # With coverage

# Specific test files
npm test wheel-item-management
```

## 🧪 Test Examples

### Unit Test Structure
```typescript
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { fixture, html } from '@open-wc/testing';
import '../src/components/app-shell.ts';
import type { AppShell } from '../src/components/app-shell.ts';

describe('Component Name', () => {
  let component: AppShell;
  
  beforeEach(async () => {
    component = await fixture(html`<app-shell></app-shell>`);
    await component.updateComplete;
  });

  it('should test something', () => {
    expect(component.someProperty).toBe(expectedValue);
  });
});
```

### Mocking API Calls
```typescript
const mockFetch = vi.fn();
global.fetch = mockFetch;

beforeEach(() => {
  mockFetch.mockResolvedValue({
    ok: true,
    json: () => Promise.resolve({ success: true, data: {} })
  });
});
```

### Testing Template Logic
```typescript
it('should show correct template based on wheel data', async () => {
  component.wheelData = { segments: [] };
  await component.updateComplete;
  
  const templateCondition = !!(
    component.wheelData && 
    component.wheelData.segments && 
    component.wheelData.segments.length > 0
  );
  
  expect(templateCondition).toBe(false);
});
```

## 🔧 VSCode Integration

### Recommended Extensions
1. **Vitest** (`vitest.explorer`) - Inline test results and debugging
2. **Test Explorer UI** - Visual test management
3. **Coverage Gutters** - Inline coverage indicators

### Configuration
The workspace is pre-configured with:
- Test discovery for `tests/*.test.ts`
- TypeScript support
- Source maps for debugging
- Watch mode integration

### Usage
- **Cmd+Shift+P** → "Vitest: Run All Tests"
- **Cmd+Shift+P** → "Vitest: Run Current File"
- Click the play button next to individual tests
- View pass/fail indicators in the editor gutter

## 📊 Coverage Reports

### Generating Coverage
```bash
node test-runner.js unit:coverage
```

### Viewing Reports
- **HTML Report**: Open `coverage/index.html` in browser
- **Terminal**: Summary displayed after test run
- **VSCode**: Install Coverage Gutters extension for inline indicators

### Coverage Thresholds
```javascript
// vitest.config.ts
coverage: {
  thresholds: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  }
}
```

## 🌐 Integration Testing

### Browser Testing with Puppeteer
Integration tests use Puppeteer to test real browser behavior:

```javascript
// ai-live-testing-tools/test-example.cjs
const puppeteer = require('puppeteer');

async function testFeature() {
  const browser = await puppeteer.launch({ headless: false });
  const page = await browser.newPage();
  
  await page.goto('http://localhost:3001');
  
  // Test real user interactions
  await page.click('.some-button');
  await page.waitForSelector('.expected-result');
  
  await browser.close();
}
```

### Available Integration Tests
- `test-dual-wheel-fix.cjs` - Validates wheel state management
- `test-template-logic-fix.cjs` - Tests template condition fixes
- `debug-removal-state-changes.cjs` - Debug wheel item removal

## 🚦 CI/CD Integration

### GitHub Actions
```yaml
- name: Run Tests
  run: |
    npm ci
    node test-runner.js ci
    
- name: Upload Coverage
  uses: codecov/codecov-action@v3
  with:
    file: ./coverage/lcov.info
```

### Test Output Formats
- **JUnit XML**: For CI systems (`--reporter=junit`)
- **JSON**: For programmatic analysis (`--reporter=json`)
- **HTML**: For human review (coverage reports)

## 🐛 Debugging Tests

### VSCode Debugging
1. Set breakpoints in test files
2. Use "Debug Test" button in Vitest extension
3. Step through code with full TypeScript support

### Console Debugging
```typescript
it('should debug something', () => {
  console.log('Debug info:', component.someProperty);
  // Test continues...
});
```

### Browser Debugging (Integration Tests)
```javascript
const browser = await puppeteer.launch({ 
  headless: false,  // See browser
  devtools: true    // Open DevTools
});
```

## 📝 Writing New Tests

### Unit Test Checklist
- [ ] Test component initialization
- [ ] Test property changes and reactivity
- [ ] Test event handling
- [ ] Test API interactions (mocked)
- [ ] Test error conditions
- [ ] Test template rendering logic

### Integration Test Checklist
- [ ] Test real user workflows
- [ ] Test cross-component interactions
- [ ] Test API integration (with real/mock backend)
- [ ] Test browser-specific behavior
- [ ] Test responsive design

### Best Practices
1. **Descriptive test names** - Clearly state what is being tested
2. **Arrange-Act-Assert** - Structure tests clearly
3. **Mock external dependencies** - Keep tests isolated
4. **Test edge cases** - Empty data, error conditions, etc.
5. **Keep tests focused** - One concept per test
6. **Use beforeEach/afterEach** - Clean setup and teardown

## 🔍 Troubleshooting

### Common Issues

#### "Cannot find module" errors
```bash
npm install  # Ensure all dependencies are installed
```

#### Physics engine errors in tests
These are expected in the test environment and don't affect test results.

#### Tests timing out
Increase timeout in test configuration:
```typescript
it('slow test', async () => {
  // test code
}, 10000); // 10 second timeout
```

#### VSCode not detecting tests
1. Reload window (Cmd+Shift+P → "Developer: Reload Window")
2. Check Vitest extension is enabled
3. Verify test file naming convention (`*.test.ts`)

### Getting Help
1. Check test output for specific error messages
2. Use `console.log` for debugging
3. Run tests with `--reporter=verbose` for detailed output
4. Check browser console in integration tests

## 📚 Resources

- [Vitest Documentation](https://vitest.dev/)
- [Open WC Testing](https://open-wc.org/docs/testing/testing-package/)
- [Lit Testing Guide](https://lit.dev/docs/tools/testing/)
- [Puppeteer Documentation](https://pptr.dev/)

## 🎯 Current Test Coverage

### Wheel Item Management (`tests/wheel-item-management.test.ts`)
✅ **19 tests passing**

**Test Categories:**
- **Initial State** (3 tests) - Component setup and initial rendering
- **Adding Wheel Items** (3 tests) - Adding activities to populated wheels
- **Removing Wheel Items** (7 tests) - Normal and complete removal scenarios
- **Error Handling** (3 tests) - API failures and edge cases
- **Template Logic Validation** (3 tests) - Critical template condition fixes

**Key Validations:**
- Template condition fix: `wheelData && wheelData.segments && wheelData.segments.length > 0`
- Proper state transitions between populated/unpopulated wheel states
- API error handling and graceful degradation
- Background wheel fallback when segments are empty

This comprehensive test suite validates the critical wheel item removal fix that was implemented to resolve the grey wheel fallback issue.
