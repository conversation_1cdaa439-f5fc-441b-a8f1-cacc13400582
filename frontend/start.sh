#!/bin/bash

# Goali Frontend Startup Script
# This script helps you start the frontend with proper configuration

set -e

echo "🎯 Goali Frontend Startup Script"
echo "================================="

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ and try again."
    exit 1
fi

# Check Node.js version
NODE_MAJOR_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
REQUIRED_MAJOR_VERSION=18

if (( NODE_MAJOR_VERSION < REQUIRED_MAJOR_VERSION )); then
    echo "❌ Node.js version $NODE_MAJOR_VERSION is too old. Please install Node.js 18+ and try again."
    exit 1
fi

echo "✅ Node.js major version: $NODE_MAJOR_VERSION"

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm and try again."
    exit 1
fi

echo "✅ npm version: $(npm --version)"

# Check if package.json exists
if [ ! -f "package.json" ]; then
    echo "❌ package.json not found. Please run this script from the frontend directory."
    exit 1
fi

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
else
    echo "✅ Dependencies already installed"
fi

# Check if .env.development exists, create if not
if [ ! -f ".env.development" ]; then
    echo "📝 Creating .env.development file..."
    cat > .env.development << EOF
# Goali Frontend Development Environment Configuration

# WebSocket URL for backend connection
VITE_WS_URL=ws://localhost:8000/ws/game/

# User profile ID for development (should be a real user ID from the database)
VITE_USER_PROFILE_ID=2

# Debug mode
VITE_DEBUG=true

# API Base URL (if needed for HTTP requests)
VITE_API_BASE_URL=http://localhost:8000

# Development mode flag
VITE_DEV_MODE=true
EOF
    echo "✅ Created .env.development with default configuration"
else
    echo "✅ .env.development already exists"
fi

# Test backend connection
echo ""
echo "🔍 Testing backend connection..."
if npm run test:connection; then
    echo "✅ Backend connection test passed!"
else
    echo "⚠️  Backend connection test failed. The frontend will run in demo mode."
    echo "   Make sure the backend server is running on localhost:8000"
    echo "   and Redis is available for WebSocket channels."
fi

echo ""
echo "🚀 Starting development server..."
echo "   Frontend will be available at: http://localhost:5173"
echo "   Press Ctrl+C to stop the server"
echo ""

# Start the development server
npm run dev
