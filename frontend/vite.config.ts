import { defineConfig } from 'vite';
import { resolve } from 'path';

export default defineConfig({
  // Base configuration
  base: './',
  
  // Build configuration
  build: {
    target: 'es2022',
    outDir: 'dist',
    sourcemap: true,
    minify: 'esbuild',
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'index.html'),
      },
      output: {
        manualChunks: {
          // Separate vendor chunks for better caching
          'lit': ['lit'],
          'pixi': ['pixi.js'],
          'matter': ['matter-js'],
        },
      },
    },
    // Optimize for mobile WebView
    chunkSizeWarningLimit: 1000,
  },

  // Development server
  server: {
    port: 3000,
    host: true,
    open: true,
    cors: true,
  },

  // Preview server
  preview: {
    port: 3001,
    host: true,
  },

  // Path resolution
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@/components': resolve(__dirname, 'src/components'),
      '@/services': resolve(__dirname, 'src/services'),
      '@/types': resolve(__dirname, 'src/types'),
      '@/utils': resolve(__dirname, 'src/utils'),
      '@/styles': resolve(__dirname, 'src/styles'),
    },
  },

  // Optimizations
  optimizeDeps: {
    include: ['lit', 'pixi.js', 'matter-js'],
    exclude: [],
  },

  // CSS configuration
  css: {
    devSourcemap: true,
  },

  // Define global constants
  define: {
    __DEV__: JSON.stringify(process.env.NODE_ENV === 'development'),
    __VERSION__: JSON.stringify(process.env.npm_package_version || '1.0.0'),
  },

  // Test configuration
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./tests/setup.ts'],
  },

  // Plugin configuration
  plugins: [],

  // Worker configuration for potential future use
  worker: {
    format: 'es',
  },

  // Experimental features disabled for stable build
  // experimental: {
  //   renderBuiltUrl(filename: string) {
  //     return { runtime: `window.__assetsPath(${JSON.stringify(filename)})` };
  //   },
  // },
});
