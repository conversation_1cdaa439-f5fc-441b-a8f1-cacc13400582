# Debug vs Production Modes

This document explains the clear separation between debug and production modes in the Goali frontend application.

## Overview

The frontend now supports two distinct modes:

- **Debug Mode**: Full development capabilities with debugging tools
- **Production Mode**: Secure, streamlined user experience

## Mode Configuration

### Environment Files

- `.env.development` - Debug mode configuration
- `.env.production` - Production mode configuration

### Running Different Modes

```bash
# Debug mode (default)
npm run dev:debug

# Production mode
npm run dev:prod

# Build for different modes
npm run build:debug
npm run build:prod
```

## Debug Mode Features

### 🐛 Debug Panel
- **Access**: Press `Ctrl+Shift+D` or click the "🐛 Debug" button in the header
- **User Selection**: Choose any user from the database
- **LLM Configuration**: Select different LLM configs from the database
- **Backend URL**: Change the backend URL on the fly
- **Storage Management**: Clear localStorage/sessionStorage

### 🔧 Development Tools
- Performance metrics display
- Network request logging
- State inspector
- Mock data toggle
- Real-time configuration updates

### 🎯 Clean Start Experience
- No mocked wheel on startup (unless explicitly enabled)
- Clear instructions to start chatting
- Debug-specific welcome messages

### 🛠️ Configuration Options
```env
VITE_DEBUG_ENABLED=true
VITE_DEBUG_SHOW_PERFORMANCE=true
VITE_DEBUG_SHOW_NETWORK_LOGS=true
VITE_DEBUG_SHOW_STATE_INSPECTOR=true
VITE_DEBUG_ALLOW_USER_SELECTION=true
VITE_DEBUG_ALLOW_LLM_CONFIG_SELECTION=true
VITE_DEBUG_ALLOW_BACKEND_URL_CHANGE=true
VITE_DEBUG_MOCK_DATA_ENABLED=false
```

## Production Mode Features

### 🔐 Authentication System
- Secure login form
- Token-based authentication
- Automatic token refresh
- Session timeout management

### 🛡️ Security Features
- Strict token validation
- Allowed origins configuration
- Session timeout enforcement
- Secure WebSocket connections (WSS)

### 📊 Logging Interface
- Error logging to remote endpoint
- User activity tracking
- Performance monitoring
- Security event logging

### 🎨 Streamlined UX
- Clean, professional interface
- No debug information visible
- Optimized for end users
- Demo mode option for trial users

### 🔒 Security Configuration
```env
VITE_SECURITY_REQUIRE_AUTH=true
VITE_SECURITY_TOKEN_VALIDATION=true
VITE_SECURITY_SESSION_TIMEOUT=1800000
VITE_SECURITY_ALLOWED_ORIGINS=https://goali.app,https://www.goali.app
```

## Key Differences

| Feature | Debug Mode | Production Mode |
|---------|------------|-----------------|
| Authentication | Optional | Required |
| Debug Panel | Available | Hidden |
| User Selection | Allowed | Fixed |
| Backend URL | Configurable | Fixed |
| Mock Data | Optional | Disabled |
| Error Display | Detailed | User-friendly |
| Performance Tools | Visible | Hidden |
| Security | Relaxed | Strict |

## Usage Examples

### Debug Mode Workflow

1. Start the application: `npm run dev:debug`
2. Open debug panel: `Ctrl+Shift+D`
3. Select a test user from the database
4. Configure LLM settings if needed
5. Change backend URL if testing different environments
6. Start chatting to generate wheels

### Production Mode Workflow

1. Start the application: `npm run dev:prod`
2. User sees login form
3. Enter credentials or try demo mode
4. Authenticated users get full experience
5. Demo users get limited functionality
6. All actions are logged and monitored

## API Endpoints for Debug Mode

The debug panel expects these endpoints to be available:

```
GET /api/debug/users/          # List all users
GET /api/debug/llm-configs/    # List all LLM configurations
```

## API Endpoints for Production Mode

Authentication endpoints:

```
POST /api/auth/login/          # User login
POST /api/auth/verify/         # Token verification
POST /api/auth/refresh/        # Token refresh
POST /api/auth/logout/         # User logout
POST /logs                     # Error logging
```

## Configuration Service

The `ConfigService` manages all configuration:

```typescript
import { ConfigService } from './services/config-service';

const config = ConfigService.getInstance();

// Check current mode
if (config.isDebugMode()) {
  // Debug-specific logic
}

if (config.isProductionMode()) {
  // Production-specific logic
}

// Update configuration (debug mode only)
config.updateWebSocketUrl('ws://localhost:8001/ws/game/');
```

## Authentication Service

The `AuthService` handles production authentication:

```typescript
import { AuthService } from './services/auth-service';

const auth = AuthService.getInstance();

// Check authentication
if (auth.isAuthenticated()) {
  // User is logged in
}

// Login
const success = await auth.authenticate(username, password);

// Get current user
const user = auth.getCurrentUser();
```

## Troubleshooting

### Debug Mode Issues

1. **Debug panel not showing**: Check if `VITE_DEBUG_ENABLED=true`
2. **User selection not working**: Verify backend `/api/debug/users/` endpoint
3. **LLM config not loading**: Check `/api/debug/llm-configs/` endpoint

### Production Mode Issues

1. **Login not working**: Verify authentication endpoints
2. **Token issues**: Check token validation configuration
3. **Connection problems**: Verify WSS URLs and certificates

## Security Considerations

### Debug Mode
- Never use in production environments
- Contains sensitive debugging information
- Allows configuration changes that could affect security

### Production Mode
- All communications should use HTTPS/WSS
- Implement proper CORS policies
- Regular security audits recommended
- Monitor authentication logs

## Future Enhancements

- Role-based access control
- Advanced debugging tools
- Performance profiling
- A/B testing framework
- Advanced logging and analytics
