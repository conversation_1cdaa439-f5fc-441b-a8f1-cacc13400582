# Goali Frontend - AI Technical Reference

## Tech Stack & Dependencies

**Core**: Lit 3.x + TypeScript + PixiJS 8.x + Matter.js + Vite
**Communication**: WebSocket with auto-reconnect + fallback demo mode
**Build**: Vite with TypeScript strict mode, ES2020 target

## Architecture Overview

```
App Shell (app-shell.ts)
├── WebSocket Manager (singleton, event-driven)
├── Game Wheel Component (physics + rendering)
│   ├── Matter.js Physics Engine
│   ├── PixiJS Renderer
│   └── Lit Component Integration
└── Chat Interface (real-time messaging)
    ├── Message Bubbles (typed messages)
    └── Input Handler (WebSocket/demo routing)
```

## Critical Implementation Details

### 1. Component System (Lit 3.x)

```typescript
@customElement('component-name')
export class Component extends LitElement {
  @property({ type: Object }) data: Type;
  @state() private internal: Type;
  
  static styles = css`/* styles */`;
  
  render() {
    return html`<template>`;
  }
}
```

**Key Patterns:**
- Use `@property` for external data, `@state` for internal
- Import paths MUST use `.js` extensions (not `.ts`)
- CSS custom properties for theming
- Event dispatching: `this.dispatchEvent(new CustomEvent('name', {detail}))`

### 2. Physics Engine (Matter.js)

**File**: `wheel-physics.ts`
**Key Classes**: `WheelPhysicsEngine`

```typescript
// Engine setup for VERTICAL wheel
this.engine = Engine.create();
this.world = this.engine.world;
this.engine.world.gravity.y = 1; // VERTICAL wheel: gravity pulls ball down

// Wheel rotation (separate from Matter.js)
private wheelRotation = 0;
private wheelVelocity = 0;

// Bodies (ball only - wheel is visual)
this.ballBody = Bodies.circle(x, y, radius, options);
this.nailBodies = nailPositions.map(pos =>
  Bodies.circle(pos.x, pos.y, nailRadius, {isStatic: true})
);

// Collision filtering
collisionFilter: {
  category: 0x0004, // Ball
  mask: 0x0002 | 0x0008 // Nails + boundaries
}
```

**Critical Changes for VERTICAL Wheel**:
- **Gravity enabled** (y = 1) for ball to fall downward
- **Wheel rotation managed separately** from Matter.js physics
- **Spinning nails** that rotate with wheel while ball falls
- **Ball falls from top** due to gravity and bounces on spinning nails
- **Combined settling detection** for wheel + ball
- Use `spinWheel(force)` to spin wheel and release ball

### 3. Rendering Engine (PixiJS 8.x)

**File**: `wheel-renderer.ts`
**Key Classes**: `WheelPixiRenderer`

```typescript
// App initialization
await this.app.init({
  canvas,
  width, height,
  backgroundColor: 0x1a1a1a,
  antialias: true
});

// Rotating wheel container
this.wheelContainer = new Container();
this.wheelContainer.pivot.set(centerX, centerY);
this.wheelContainer.position.set(centerX, centerY);

// Graphics
const graphics = new Graphics();
graphics.circle(x, y, radius);
graphics.fill(0xFF0000);

// Update wheel rotation
this.wheelContainer.rotation = wheelRotation;
```

**Critical Changes**:
- **Rotating wheel container** for proper wheel spinning
- **No text rendering** on wheel (per requirements)
- **360 nails** rendered with smaller radius
- Container hierarchy: app.stage > container > wheelContainer > graphics
- Use `updateFromFrame(frame)` for synchronized updates

### 4. WebSocket Management

**File**: `websocket-manager.ts`
**Pattern**: Singleton with EventTarget inheritance

```typescript
// Initialization
await wsManager.initialize(config);

// Message handling
wsManager.onMessage('type', (data) => {});
wsManager.sendMessage('type', data);

// Connection events
wsManager.onConnectionChange((connected) => {});
```

**Auto-reconnect**: Exponential backoff, message queuing, heartbeat

### 5. Type System

**Key Interfaces**:
- `WheelData`: Backend wheel structure
- `WheelSegment`: Individual wheel segment with angles
- `ChatMessage`: Typed message system
- `WheelConfig`: Physics/rendering configuration
- `AnimationFrame`: Physics-renderer bridge

**Type Guards**: Use `isWheelData()`, `isWheelSegment()` for runtime validation

### 6. Demo Mode Fallback

**Trigger**: WebSocket connection failure
**Implementation**: 
- Fake wheel data with 8 predefined segments
- Simulated AI responses with realistic delays
- Full wheel functionality maintained
- Connection status shows "Demo Mode"

## File Structure & Imports

```
src/
├── components/
│   ├── app-shell.ts                 # Main orchestrator
│   ├── game-wheel/
│   │   ├── game-wheel.ts           # Lit component
│   │   ├── wheel-physics.ts        # Matter.js engine
│   │   ├── wheel-renderer.ts       # PixiJS renderer
│   │   └── wheel-types.ts          # TypeScript interfaces
│   └── chat/
│       ├── chat-interface.ts       # Main chat UI
│       └── message-bubble.ts       # Message display
├── services/
│   ├── websocket-manager.ts        # WebSocket singleton
│   ├── state-manager.ts           # App state
│   └── message-handler.ts         # Message processing
├── utils/
│   ├── physics-utils.ts           # Math/physics helpers
│   └── color-utils.ts             # Color manipulation
├── types/                         # Global type definitions
└── styles/                        # CSS files
```

## Critical Configuration

### TypeScript (tsconfig.json)
```json
{
  "target": "ES2020",
  "module": "ESNext",
  "moduleResolution": "bundler",
  "strict": true,
  "experimentalDecorators": true,
  "useDefineForClassFields": false
}
```

### Vite (vite.config.ts)
```typescript
export default defineConfig({
  plugins: [resolve()],
  build: {
    target: 'es2020',
    lib: {
      entry: 'src/main.ts',
      formats: ['es']
    }
  }
});
```

## Event System

### Wheel Events
- `wheel-spin-start`: `{force: number}`
- `wheel-spin-complete`: `{winningSegment, finalAngle, duration}`
- `wheel-result`: `{segment: WheelSegment}`

### Chat Events
- `message-send`: `{message: ChatMessage}`

### WebSocket Events
- `message-{type}`: Specific message type handlers
- `connection-change`: `boolean` connection status

## Physics Configuration

```typescript
const DEFAULT_WHEEL_CONFIG: WheelConfig = {
  radius: 200,
  nailCount: 360,              // 360 nails for fine segments
  nailRadius: 2,               // Smaller nails
  ballRadius: 6,               // Optimized ball size
  spinForce: 0.05,             // Increased spin force
  wheelFriction: 0.002,        // Ball friction
  wheelRotationFriction: 0.001, // Wheel rotation friction
  airResistance: 0.995,        // More air resistance
  maxSpinDuration: 8000,       // Longer max duration
  initialWheelVelocity: 0.1    // Wheel rotation multiplier
};
```

## Color System

**Utilities**: `color-utils.ts`
- `hexToRgb()`, `rgbToHex()`, `hslToRgb()`
- `generateColorPalette()`, `optimizeSegmentColors()`
- `getContrastColor()`, `hexToPixiColor()`

**Palettes**: Predefined color sets (vibrant, pastel, earth, ocean)

## Error Handling

1. **WebSocket Failures**: Auto-fallback to demo mode
2. **Physics Errors**: Component-level error boundaries
3. **Rendering Errors**: Graceful degradation with error messages
4. **Type Errors**: Runtime type guards and validation

## Performance Optimizations

1. **Physics**: 60fps with efficient collision detection
2. **Rendering**: PixiJS object pooling, container hierarchy
3. **Memory**: Proper cleanup in `disconnectedCallback()`
4. **Network**: Message queuing, connection pooling
5. **Mobile**: Touch-optimized interactions, reduced effects

## Build & Development

**Commands**:
- `npm run dev`: Development server with HMR
- `npm run build`: Production build with optimization
- `npm run type-check`: TypeScript validation

**Environment Variables**:
- `VITE_WS_URL`: WebSocket endpoint
- `VITE_API_URL`: REST API endpoint

## Common Patterns

### Component Lifecycle
```typescript
connectedCallback() {
  super.connectedCallback();
  // Setup
}

disconnectedCallback() {
  super.disconnectedCallback();
  // Cleanup
}

updated(changedProperties) {
  if (changedProperties.has('data')) {
    // React to changes
  }
}
```

### Service Integration
```typescript
// Singleton pattern
const service = ServiceClass.getInstance();

// Event-driven communication
service.addEventListener('event', handler);
service.dispatchEvent(new CustomEvent('event', {detail}));
```

### Async Operations
```typescript
// Proper error handling
try {
  await operation();
} catch (error) {
  console.error('Operation failed:', error);
  // Fallback behavior
}
```

## Testing Strategy

1. **Manual**: Wheel physics, chat functionality, responsive design
2. **Browser**: Chrome/Firefox/Safari, mobile devices
3. **Network**: Connection failures, message handling
4. **Performance**: 60fps animations, memory usage

## Deployment Checklist

1. TypeScript compilation passes
2. Vite build succeeds
3. WebSocket URL configured
4. Demo mode functional
5. Mobile responsiveness verified
6. Error handling tested

## VERTICAL Wheel Physics Model

### **Critical Understanding: The Wheel is VERTICAL**

The wheel operates as a **vertical spinning wheel** where:

1. **Ball starts at TOP** of the wheel (centerY - ballStartOffset)
2. **Gravity pulls ball DOWN** (gravity.y = 1) when "Spin!" is pressed
3. **Wheel spins** with nails rotating around the center
4. **Ball falls and BOUNCES** on the spinning nails during descent
5. **Winner determined** by where ball settles after bouncing

```typescript
// FIXED 100-SEGMENT vertical wheel physics setup
this.engine.gravity.y = 0; // NO gravity until wheel spins
this.engine.gravity.x = 0;
this.engine.gravity.scale = 0.001; // Scale gravity for control when enabled

// FIXED segment system
const TOTAL_SEGMENTS = 100;
const SEGMENT_ANGLE = (2 * Math.PI) / TOTAL_SEGMENTS; // 3.6° each

// Ball suspension and release
public resetBall(): void {
  this.engine.gravity.y = 0; // Disable gravity - ball suspended
}

public spinWheel(force: number): void {
  this.engine.gravity.y = 0.8; // Enable gravity when wheel spins
}

// PRECISE nail configuration
nailCount: 100, // Always 100 nails
ballRadius: 8, // Twice the space between nails
nailRadius: 1.5, // Smaller for precise delimiting

// Color distribution algorithm
function createSegmentDistributionPlan(activities): number[] {
  // Avoids adjacent same colors when possible
}
```

## Recent Critical Fixes (Wheel Physics Overhaul)

### 1. **FIXED 100-SEGMENT Vertical Wheel System - PRECISION & CONTROL**
- **FIXED SEGMENTS**: Exactly 100 equal segments (3.6° each) regardless of activity count
- **PROPORTIONAL DISTRIBUTION**: Activities get segments based on percentage (rounded to whole segments)
- **SUSPENDED BALL**: Ball doesn't fall until wheel spins (`gravity.y = 0` initially)
- **PRECISE NAILS**: 100 nails exactly delimiting segments, ball twice the nail spacing
- **COLOR OPTIMIZATION**: Adjacent segments avoid same colors when possible
- **UPPER HALF HIDING**: Gradual masking of upper wheel area for focus on bottom
- **Removed wheel body** from Matter.js physics (wheel is now purely visual)
- **Added separate wheel rotation** managed independently from ball physics
- **Increased nail count** from 24 to 360 for fine-grained segments
- **Fixed ball starting position** (was outside wheel radius: 160 → 120)
- **Improved boundary constraints** with thicker walls (8 → 20px) and better positioning
- **Enhanced ball physics**: restitution (0.6 → 0.8), friction (0.1 → 0.3), density (0.001 → 0.01)
- **Fixed air resistance** calculation (was inverted: 0.995 → 0.01)
- **Implemented combined settling detection** for both wheel and ball

### 2. **Renderer Architecture Fixes**
- **Created rotating wheel container** (`wheelContainer`) for proper wheel spinning
- **Fixed container hierarchy**: `app.stage > container > wheelContainer > graphics`
- **Added wheel rotation updates** synchronized with physics (`updateFromFrame()`)
- **Removed text rendering** as requested (clean wheel display)
- **Fixed nail rendering** for 360 nails with appropriate sizing (radius: 3 → 2)
- **Added ball position logging** for debugging

### 3. **Configuration Optimizations**
```typescript
const FIXED_CONFIG = {
  nailCount: 360,           // Was: 24
  ballStartOffset: 120,     // Was: 160 (outside wheel)
  spinForce: 0.1,          // Was: 0.02
  wheelFriction: 0.005,    // Was: 0.001
  airResistance: 0.99,     // Was: 0.995 (inverted)
  wheelRotationFriction: 0.002,
  initialWheelVelocity: 2.0
};
```

### 4. **Debugging Tools Added**
- **Console test script** (`test_console_script.js`) for real-time debugging
- **Comprehensive logging** throughout physics and rendering
- **getWheelState() method** for state inspection
- **Ball position monitoring** and bounds checking
- **Physics state validation** and error reporting

### 5. **Critical Issues Discovered and Resolved**
- ✅ **Ball not moving** - CONFIRMED: Ball force was too weak (0.025), increased to 5x stronger
- ✅ **Wrong winner selection** - FIXED: Now uses ball position instead of wheel rotation
- ✅ **Ball separation/duplication** - Fixed physics body management
- ✅ **Wheel sliding** - Proper container anchoring and rotation
- ✅ **No spinning** - Implemented wheel rotation separate from ball physics
- ✅ **Ball disappearing** - Fixed starting position and boundary constraints
- ✅ **Nail spacing** - Optimized to 72 nails with proper gaps for ball settling

### 6. **VERTICAL Wheel Testing Strategy - UPDATED**
```typescript
// Automatic ball tracking during spins
wheel.startBallTracking(); // Monitors ball position every 100ms
wheel.spin(); // Ball falls due to gravity, bounces on spinning nails

// Dedicated test page for vertical wheel physics
// http://localhost:3001/test_vertical_wheel.html
// Provides automated testing with visual feedback

// Console test script for manual verification
// Copy/paste from TESTING_VERIFICATION.md into browser console
// Monitors ball FALLING movement (Y position increasing)
```

### 7. **Winner Selection Logic - CORRECTED**
```typescript
// WRONG (previous implementation)
const wheelRotation = this.physicsEngine.getWheelRotation();
const finalAngle = normalizeAngle(-wheelRotation);

// CORRECT (current implementation)
const ballAngle = this.physicsEngine.getBallAngle();
const winningSegment = getSegmentAtAngle(ballAngle, this.segments);
// Winner = segment most in contact with ball's final position
```

This reference provides all critical technical details for AI agents to understand, modify, and extend the Goali frontend codebase effectively.
