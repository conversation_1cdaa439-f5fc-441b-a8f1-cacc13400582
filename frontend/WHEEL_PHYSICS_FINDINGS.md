# Wheel Physics Investigation and Fixes

## Investigation Summary

During the wheel physics debugging session, several critical issues were discovered and resolved. This document details the findings, root causes, and solutions implemented.

## Critical Issues Discovered

### 1. **Ball Not Moving - CONFIRMED**

**Problem**: <PERSON> was completely stationary during spins despite wheel rotation working correctly.

**Root Cause**: 
- Ball force calculation: `force * 0.5` with base `spinForce: 0.1`
- Random force range: 0.05 to 0.15 (50% to 150% of base)
- Final ball force: As low as 0.025 (0.05 * 0.5)
- This was insufficient to overcome physics friction and create visible movement

**Solution**:
```typescript
// BEFORE
const ballForce = force * 0.5; // Too weak

// AFTER  
const ballForce = force * 5.0; // 10x stronger for visible movement
```

**Testing Strategy**: Implemented real-time ball tracking that monitors position every 100ms and detects movement > 0.1 pixels.

### 2. **Incorrect Winner Selection - FIXED**

**Problem**: Winner was determined by wheel rotation angle instead of ball's final position.

**Root Cause**: 
```typescript
// WRONG LOGIC
const wheelRotation = this.physicsEngine.getWheelRotation();
const finalAngle = normalizeAngle(-wheelRotation);
const winningSegment = getSegmentAtAngle(finalAngle, this.segments);
```

This approach assumes the ball follows the wheel rotation, but the ball has independent physics.

**Solution**:
```typescript
// CORRECT LOGIC
const ballAngle = this.physicsEngine.getBallAngle();
const winningSegment = getSegmentAtAngle(ballAngle, this.segments);
```

**Rationale**: Winner should be the segment that the ball is most in contact with, not where the wheel stopped rotating.

### 3. **Nail Configuration Optimization**

**Problem**: Too many nails (360) created no space for ball to settle.

**Solution**: 
- Reduced from 360 to 72 nails
- Creates proper gaps between nails
- Allows ball to find stable resting positions
- Maintains sufficient granularity for segment detection

**Configuration**:
```typescript
nailCount: 72, // Optimal spacing for ball settling
nailRadius: 2, // Appropriate size for 72 nails
```

## Testing Infrastructure Implemented

### 1. **Ball Movement Tracking**
```typescript
// Automatic tracking during spins
public startBallTracking(): void {
  // Monitors ball position every 100ms
  // Detects movement > 0.1 pixels
  // Logs position, velocity, distance from center
  // Reports if no movement detected
}
```

### 2. **Console Test Script**
Located in `test_console_script.js`:
- Real-time ball position monitoring
- Movement detection alerts
- Physics state validation
- Comprehensive debugging output

### 3. **Enhanced Logging**
```typescript
// Physics engine logging
console.log(`[PHYSICS] Ball force: ${ballForce}, vector: (${x}, ${y})`);
console.log(`[PHYSICS] Ball velocity after force: (${vx}, ${vy})`);

// Winner selection logging  
console.log(`[WHEEL] Ball final angle: ${ballAngle} (${degrees}°)`);
console.log(`[WHEEL] Winner by ball position: ${winner}`);
console.log(`[WHEEL] Wheel-based winner would be: ${wheelWinner}`);
```

## Configuration Changes

### Physics Parameters
```typescript
const OPTIMIZED_CONFIG = {
  nailCount: 72,              // Was: 360 (too dense)
  ballRadius: 7,              // Was: 6 (slightly larger)
  ballStartOffset: 120,       // Was: 160 (was outside wheel)
  spinForce: 0.1,            // Base force maintained
  wheelFriction: 0.005,      // Ball friction
  airResistance: 0.99,       // Was: 0.995 (was too high)
  wheelRotationFriction: 0.002,
  initialWheelVelocity: 2.0
};
```

### Ball Physics
```typescript
// Enhanced ball body properties
{
  restitution: 0.8,          // More bouncy
  friction: 0.3,             // Better control
  frictionAir: 0.01,         // Proper air resistance
  density: 0.01,             // Realistic mass
}
```

## Verification Methods

### 1. **Visual Inspection**
- Wheel rotates smoothly ✅
- Ball moves visibly during spin ✅
- Ball settles in gaps between nails ✅

### 2. **Console Monitoring**
```javascript
// Run in browser console
const wheel = document.querySelector('game-wheel');
wheel.startBallTracking();
wheel.spin();
// Watch for movement detection logs
```

### 3. **Winner Accuracy**
- Ball position determines winner ✅
- Logging shows both ball-based and wheel-based results for comparison ✅
- Ball-based selection is more accurate ✅

## Key Learnings

1. **Force Scaling**: Physics forces need careful calibration - too small and nothing moves, too large and objects fly off screen.

2. **Winner Logic**: In physics-based wheels, the ball's final position is the authoritative source for winner selection, not the wheel's rotation.

3. **Nail Density**: Balance between granularity and functionality - too many nails prevent settling, too few reduce precision.

4. **Testing Strategy**: Real-time monitoring is essential for physics debugging - static analysis isn't sufficient.

5. **Logging Importance**: Comprehensive logging at each step helps identify exactly where physics break down.

## Future Improvements

1. **Dynamic Force Adjustment**: Auto-calibrate forces based on wheel size and configuration.

2. **Settling Detection**: Improve detection of when ball has truly settled vs. just moving slowly.

3. **Visual Debugging**: Add optional visual indicators showing ball trail, forces, and collision points.

4. **Performance Optimization**: Monitor frame rates during complex physics interactions.

## Testing Commands

```bash
# Test in browser console
const wheel = document.querySelector('game-wheel');
wheel.getWheelState(); // Check configuration
wheel.startBallTracking(); // Start monitoring
wheel.spin(); // Test with tracking
```

This investigation confirmed that systematic testing and logging are essential for physics debugging, and that assumptions about physics behavior must be verified through observation.
