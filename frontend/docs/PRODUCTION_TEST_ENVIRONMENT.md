# Production-Test Environment Setup

This document provides comprehensive instructions for setting up and using the production-test environment, which simulates production settings locally for testing authentication, security, and production-like behavior.

## Overview

The production-test environment bridges the gap between development and production by:

- **Requiring authentication** (like production)
- **Disabling debug features** (like production)  
- **Using production-like security settings**
- **Connecting to local backend** (for testing)
- **Providing comprehensive error reporting**

## Environment Modes Comparison

| Feature | Development | Debug | Production-Test | Production |
|---------|-------------|-------|-----------------|------------|
| Authentication Required | ✅ | ✅ | ✅ | ✅ |
| Debug Panel | ❌ | ✅ | ❌ | ❌ |
| Debug Badge | ❌ | ✅ | ❌ | ❌ |
| User Selection | ❌ | ✅ | ❌ | ❌ |
| LLM Config Selection | ❌ | ✅ | ❌ | ❌ |
| Backend URL Change | ❌ | ✅ | ❌ | ❌ |
| Performance Metrics | ❌ | ✅ | ❌ | ❌ |
| Network Logs | ❌ | ✅ | ❌ | ❌ |
| State Inspector | ❌ | ✅ | ❌ | ❌ |
| Mock Data | ❌ | ❌ | ❌ | ❌ |
| Caching | ❌ | ❌ | ✅ | ✅ |
| Error Reporting | Basic | Detailed | Production | Production |

## Prerequisites

### 1. Backend Running
The backend must be running and accessible at `http://localhost:8000`:

```bash
cd backend
docker-compose up
```

Verify backend is running:
```bash
curl http://localhost:8000/health/
```

### 2. Environment Configuration
Ensure `.env.production-test` exists with correct settings:

```bash
# Check if file exists
ls -la frontend/.env.production-test

# Verify content
cat frontend/.env.production-test
```

## Quick Start

### Method 1: Using NPM Script (Recommended)
```bash
cd frontend
npm run dev:production-test
```

### Method 2: Manual Vite Command
```bash
cd frontend
vite --mode production-test
```

### Method 3: Direct Script Execution
```bash
cd frontend
node scripts/start-production-test.js
```

## Configuration Files

### `.env.production-test`
Main configuration file for production-test mode:

```env
# Application Mode
VITE_APP_MODE=production-test

# Local Backend URLs
VITE_API_BASE_URL=http://localhost:8000
VITE_WS_URL=ws://localhost:8000/ws/game/

# Security (production-like)
VITE_SECURITY_REQUIRE_AUTH=true
VITE_SECURITY_TOKEN_VALIDATION=true
VITE_SECURITY_SESSION_TIMEOUT=1800000

# Debug (disabled)
VITE_DEBUG_ENABLED=false
VITE_DEBUG_SHOW_PERFORMANCE=false
# ... all debug features disabled

# Performance (production-like)
VITE_WHEEL_CACHE_ENABLED=true
VITE_LOGGING_ENABLED=true
VITE_LOGGING_LEVEL=warn
```

### `config/production-test.js`
JavaScript configuration object for advanced settings:

```javascript
export const ProductionTestConfig = {
  environment: 'production-test',
  debug: false,
  auth: {
    required: true,
    sessionTimeout: 3600000,
    demoMode: false,
    allowGuestAccess: false
  },
  // ... additional settings
};
```

## Authentication Testing

### Login Flow
1. Start production-test environment
2. Navigate to `http://localhost:3001`
3. Should see login form (not main app)
4. Login with valid credentials
5. Should see main app with user info in header

### Expected Behavior
- **Login Required**: App shows login form on first visit
- **No Demo Mode**: Demo mode button should not appear
- **User Info**: Header shows user name and logout button
- **Session Management**: Proper session timeout handling
- **Logout**: Logout button works and returns to login

## Troubleshooting

### Backend Connection Issues
```bash
# Check backend status
curl -I http://localhost:8000/health/

# Check WebSocket endpoint
curl -I http://localhost:8000/ws/game/

# View backend logs
docker-compose logs -f backend
```

### Authentication Issues
```bash
# Check session cookies
# Open browser dev tools > Application > Cookies

# Check authentication API
curl -X POST http://localhost:8000/api/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'
```

### Environment Issues
```bash
# Verify environment file
cat frontend/.env.production-test

# Check Vite mode detection
npm run dev:production-test -- --debug

# Clear Vite cache
rm -rf frontend/node_modules/.vite
```

## Comparison with Production

### Similarities to Production
- Authentication required
- Debug features disabled
- Production-like security settings
- Caching enabled
- Error reporting enabled
- Session management

### Differences from Production
- Uses local backend (not Digital Ocean)
- HTTP instead of HTTPS
- Less strict CORS settings
- Development SSL certificates
- Local logging endpoint

## Testing Scenarios

### 1. Authentication Flow
- [ ] Login form appears on first visit
- [ ] Valid credentials allow access
- [ ] Invalid credentials show error
- [ ] Session timeout works
- [ ] Logout returns to login

### 2. Security Features
- [ ] CSRF protection active
- [ ] XSS protection enabled
- [ ] Secure headers present
- [ ] Session cookies secure

### 3. Production-like Behavior
- [ ] No debug panel visible
- [ ] No debug badge in header
- [ ] User selection disabled
- [ ] LLM config selection disabled
- [ ] Backend URL change disabled

### 4. Error Handling
- [ ] Production-level error messages
- [ ] Error reporting to backend
- [ ] Graceful fallbacks
- [ ] No sensitive data in errors

## Integration with CI/CD

### GitHub Actions
```yaml
- name: Test Production-Test Environment
  run: |
    cd frontend
    npm run dev:production-test &
    sleep 10
    npm run test:integration
```

### Local Testing Script
```bash
#!/bin/bash
# test-production-environment.sh

echo "Starting backend..."
cd backend && docker-compose up -d

echo "Waiting for backend..."
sleep 30

echo "Starting frontend in production-test mode..."
cd ../frontend && npm run dev:production-test &

echo "Running integration tests..."
sleep 10
npm run test:integration

echo "Cleanup..."
pkill -f "vite.*production-test"
cd ../backend && docker-compose down
```

## Monitoring and Debugging

### Log Levels
- **Error**: Critical issues only
- **Warn**: Warnings and errors
- **Info**: Disabled in production-test
- **Debug**: Disabled in production-test

### Performance Monitoring
- Caching enabled for optimal performance
- Bundle optimization active
- Image optimization enabled
- Lazy loading implemented

### Error Reporting
- Errors sent to local backend endpoint
- No sensitive data in error reports
- Structured error format
- Automatic retry mechanisms

## Best Practices

1. **Always test authentication flow** before deploying
2. **Verify all debug features are disabled**
3. **Test with production-like data volumes**
4. **Validate error handling scenarios**
5. **Check performance under load**
6. **Ensure security headers are present**
7. **Test session timeout behavior**
8. **Verify logout functionality**

## Support

For issues with production-test environment:

1. Check this documentation
2. Verify prerequisites are met
3. Review troubleshooting section
4. Check backend logs
5. Examine browser console
6. Test with clean browser session
